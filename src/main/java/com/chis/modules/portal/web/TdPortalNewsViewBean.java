package com.chis.modules.portal.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalNews;
import com.chis.modules.portal.entity.TdPortalNewsAnnex;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.web.FacesBean;

/**
 * 更多信息查看
 * 
 * <AUTHOR>
 * @history 2014-08-18
 */
@ManagedBean(name = "tdPortalNewsViewBean")
@ViewScoped
public class TdPortalNewsViewBean extends FacesBean {

	private static final long serialVersionUID = 2695401462098651426L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl portalBaseImpl = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
	/** 消息类型 */
	private String newsType;
	/** 模块名称 */
	private String pageTitle;
	/** rid */
	private String rid;
	/** 消息实体 */
	private TdPortalNews tdPortalNews;
	/** 附件实体 */
	private TdPortalNewsAnnex tdPortalNewsAnnex;

	/** 文件名称 */
	private String officeName;
	/** 文件类型 */
	private String officeType;

	/**
	 * 总页数
	 */
	private int totalPdfPage;
	/**
	 * 当前页
	 */
	private int currPdfPage;
	/**
	 * pdfList
	 */
	private List<String> pdfList;
	/** pdfHTML字符串 */
	private String pdfhtml;
	/** 字符串前 */
	private String PDFMODULE_FRONT = "<object type='application/pdf' height='1000px' width='100%' data='";
	/** 字符串后 */
	private String PDFMODULE_BACK = "' ></object>";

	@PostConstruct
	public void init() {
		// 文本信息类型
		this.newsType = JsfUtil.getRequest().getParameter("newsType");
		// 信息表rid
		this.rid = JsfUtil.getRequest().getParameter("rid");
		this.officeName = "";
		this.officeType = "";

		if (StringUtils.isNotBlank(this.rid)) {
			this.tdPortalNews = this.portalBaseImpl.findTdPortalNews(Integer.valueOf(rid), sessionData.getUser().getRid());
			this.pageTitle = this.tdPortalNews.getNewsTitle();
			// office文件路径
			String annex = this.tdPortalNews.getNewsAnnex();
			if (StringUtils.isNotBlank(annex)) {
				if (tdPortalNews.getNewsType() == 5) {
					this.pdfList = new LinkedList<String>();
					String[] pdfAddrArr = annex.split(",");
					for (String addr : pdfAddrArr) {
						if(addr.contains("webFile")){
							pdfList.add(addr);
						}else{
							pdfList.add(Constants.XNPATH_DIR+addr);
						}
					}
					totalPdfPage = this.pdfList.size();
					currPdfPage = 1;
					pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(this.pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK).toString();
				} else {
					// 分割office路径
					String name = annex.substring(annex.lastIndexOf("/") + 1);
					String[] office = name.split("\\.");
					if (office.length == 2) {
						this.officeName = office[0];
						this.officeType = "." + office[1];
					}
				}
			}

		} else {
			this.tdPortalNews = new TdPortalNews();
			this.newsType = "-1";
		}
	}

	/**
	 * 下载文件
	 */
	public void downLoadDiskFile() {
		String path = JsfUtil.getAbsolutePath() + this.tdPortalNewsAnnex.getAnnexAddr();
		FileInputStream fis = null;
		try {
			File file = new File(path);
			fis = new FileInputStream(file);
			String fileString = DownLoadUtil.uploadFile2Database(fis);
			DownLoadUtil.downFile(this.tdPortalNewsAnnex.getAnnexName(), fileString);
		} catch (Exception e1) {
			e1.printStackTrace();
			JsfUtil.addErrorMessage("文件下载失败！");
		} finally {// 关闭输入输出流
			try {
				if (null != fis) {
					fis.close();
				}
			} catch (IOException e2) {
				e2.printStackTrace();
			}
		}
	}

	/**
	 * 上一个
	 */
	public void upPdf() {
		if (pdfList.size() > 0 && currPdfPage > 0) {
			currPdfPage = currPdfPage - 1;
			pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK).toString();
		}
	}

	/**
	 * 下一个
	 */
	public void downPdf() {
		if (pdfList.size() > 0 && currPdfPage < pdfList.size()) {
			currPdfPage = currPdfPage + 1;
			pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK).toString();
		}
	}

	public String getNewsType() {
		return newsType;
	}

	public void setNewsType(String newsType) {
		this.newsType = newsType;
	}

	public String getPageTitle() {
		return pageTitle;
	}

	public void setPageTitle(String pageTitle) {
		this.pageTitle = pageTitle;
	}

	public String getRid() {
		return rid;
	}

	public void setRid(String rid) {
		this.rid = rid;
	}

	public TdPortalNews getTdPortalNews() {
		return tdPortalNews;
	}

	public void setTdPortalNews(TdPortalNews tdPortalNews) {
		this.tdPortalNews = tdPortalNews;
	}

	public TdPortalNewsAnnex getTdPortalNewsAnnex() {
		return tdPortalNewsAnnex;
	}

	public void setTdPortalNewsAnnex(TdPortalNewsAnnex tdPortalNewsAnnex) {
		this.tdPortalNewsAnnex = tdPortalNewsAnnex;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	public int getTotalPdfPage() {
		return totalPdfPage;
	}

	public void setTotalPdfPage(int totalPdfPage) {
		this.totalPdfPage = totalPdfPage;
	}

	public int getCurrPdfPage() {
		return currPdfPage;
	}

	public void setCurrPdfPage(int currPdfPage) {
		this.currPdfPage = currPdfPage;
	}

	public List<String> getPdfList() {
		return pdfList;
	}

	public void setPdfList(List<String> pdfList) {
		this.pdfList = pdfList;
	}

	public String getPdfhtml() {
		return pdfhtml;
	}

	public void setPdfhtml(String pdfhtml) {
		this.pdfhtml = pdfhtml;
	}

}
