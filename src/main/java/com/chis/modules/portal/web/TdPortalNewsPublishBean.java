package com.chis.modules.portal.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.imageio.stream.FileImageOutputStream;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CroppedImage;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;

import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.ImageUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.web.FlowFacesBean;
import com.chis.modules.portal.entity.TdPortalNews;
import com.chis.modules.portal.entity.TdPortalNewsAnnex;
import com.chis.modules.portal.entity.TdPortalNewsOffice;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.portal.entity.TdPortalNewsUnit;
import com.chis.modules.portal.enumn.NewsType;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.UnitPO;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;

/**
 * 信息发布页面
 * 
 * <AUTHOR>
 * @history 文件存储到虚拟路径修改 图片剪辑功能修改
 * @LastModify xt
 * @ModifyDate 2014年9月4日
 * @history 发布范围不选，置“是否全部可见”值为1
 * @LastModify xt
 * @ModifyDate 2014年9月4日
 * @history 新增PDF类型
 * @LastModify xt
 * @ModifyDate 2014年10月13日
 * 
 * 
 * 
 * <p>修订内容：</p>
 * 新增系统参数控制授权范围
 * @ClassReviser xq,2018年1月16日,TdPortalNewsPublishBean
 */
@ManagedBean(name = "tdPortalNewsPublishBean")
@ViewScoped
public class TdPortalNewsPublishBean extends FlowFacesBean {

	private static final long serialVersionUID = -4938026789397850401L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl portalBaseImpl = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);	
	private SystemModuleServiceImpl service = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	/** 主键ID */
	private Integer rid;
	/** 信息实体 */
	private TdPortalNews tdPortalNews;
	/** 图片信息数据 */
	private List<TdPortalNewsType> TdPortalNewsTypeList;
	/** 栏目下拉 */
	private List<SelectItem> colNews = new ArrayList<SelectItem>();
	/** 是否传入类别 */
	private boolean ifHasNewsType;
	/** 栏目的个数 */
	private int colNewsSize = 0;

	/** 信息分类树 */
	private TreeNode portalTypeTree;
	/** 信息类型 */
	private List<SelectItem> newsType;
	/** 文件类型 */
	private String fileType;
	/** 文件名 */
	private String fileName;
	/** 选中的科室ID */
	private String selectOfficeIds;
	/** 选中的科室名称 */
	private String selectOfficeNames;

	/** 附件list */
	private List<TdPortalNewsAnnex> annexList = new LinkedList<TdPortalNewsAnnex>();
	/** 附件表 */
	private TdPortalNewsAnnex tdPortalNewsAnnex;

	/** 图片树 */
	private TreeNode photoTree;
	/** 截取图片对象 */
	private CroppedImage croppedImage;
	/** 是否选中树，默认为否 */
	private boolean ifSelTree;
	/** 信息类型，临时字段 */
	private int tempNewsType;
	/** 总页数 */
	private int totalPdfPage;
	/** 当前页 */
	private int currPdfPage;
	/** pdfList */
	private List<String> pdfList;
	/** pdfHTML字符串 */
	private String pdfhtml;
	/** 字符串前 */
	private String PDFMODULE_FRONT = "<object type='application/pdf' height='1000px' width='100%' data='";
	/** 字符串后 */
	private String PDFMODULE_BACK = "' ></object>";
	/** 是否修改照片 */
	private boolean ifModPhoto = false;
    /*+是否展示信息类型栏*/
    private Integer showNewsType;
    /*+图片类型*/
    private TdPortalNewsType pictureType;
    
	/** ++是否显示截取组件 */
	private boolean ifShowCrop = false;
	/**发布范围是否可以按单位选择**/
	private Boolean ifCanGrantAllUnit = Boolean.FALSE;
	public TdPortalNewsPublishBean() {
		String systemParam = commService.findParamValue("PORTAL_SHOW_All_USER");
		if("1".equals(systemParam)){
			ifCanGrantAllUnit = Boolean.TRUE;
		}
		this.init(BusinessIdUtil.parsePK(this.businessId));
	}

	/**
	 * 业务表单初始化
	 * 
	 * @param obj
	 *            表单id
	 */
	@Override
	public void init(Object obj) {
		pdfhtml = null;
		this.pdfList = new ArrayList<String>();
		// 流程节点中，如果传入ID有值，说明是ID,则初始化实体
		if (null != obj && !"".equals(obj.toString())) {
			this.tdPortalNews = this.portalBaseImpl.findTdPortalNews(Integer.valueOf(obj.toString()),null);
			this.pdfList = new ArrayList<String>();
			// office路径 或者 PDF 路径
			String newsAnnex = tdPortalNews.getNewsAnnex();
			if (StringUtils.isNotBlank(newsAnnex)) {
				if (tdPortalNews.getNewsType() == 1 || tdPortalNews.getNewsType() == 2
						|| tdPortalNews.getNewsType() == 3) {
					String tempFileName = newsAnnex.substring(newsAnnex.lastIndexOf("/") + 1, newsAnnex.length());
					String[] split = tempFileName.split("[.]");
					if (split.length == 2) {
						fileName = split[0];
						fileType = "." + split[1];
					}
				} else if (tdPortalNews.getNewsType() == 5) {
					String[] pdfAddrArr = newsAnnex.split(",");
					for (String addr : pdfAddrArr) {
						pdfList.add(Constants.XNPATH_DIR+addr);
					}
					totalPdfPage = pdfList.size();
					currPdfPage = 1;
					pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1))
							.append(PDFMODULE_BACK).toString();
				}
			}
			tempNewsType = tdPortalNews.getNewsType();
			selectOfficeIds = null;
			selectOfficeNames = null;
			// 科室Id与名称
			StringBuilder officeIds = new StringBuilder();
			StringBuilder officeNames = new StringBuilder();
			if(ifCanGrantAllUnit){
				List<TdPortalNewsUnit> tdPortalNewsUnits = this.tdPortalNews.getTdPortalNewsUnits();
				if (null != tdPortalNewsUnits && tdPortalNewsUnits.size() > 0) {
					for (TdPortalNewsUnit tdPortalNewsUnit : tdPortalNewsUnits) {
						TsUnit tsUnit = tdPortalNewsUnit.getFkByUnitId();
						officeIds.append(",").append(tsUnit.getRid());
						officeNames.append(",").append(tsUnit.getUnitname());
					}
					selectOfficeIds = officeIds.substring(1);
					selectOfficeNames = officeNames.substring(1);
				}
				
			}else{
			
				List<TdPortalNewsOffice> tdPortalNewsOffices = this.tdPortalNews.getTdPortalNewsOffices();
				if ( null != tdPortalNewsOffices && tdPortalNewsOffices.size() > 0 ) {
					for( TdPortalNewsOffice tdPortalNewsOffice : tdPortalNewsOffices)	{
						TsOffice tsOffice = tdPortalNewsOffice.getTsOffice();
						officeIds.append(",").append(tsOffice.getRid());
						officeNames.append(",").append(tsOffice.getOfficename());
					}
					selectOfficeIds = officeIds.substring(1);
					selectOfficeNames = officeNames.substring(1);
				}
			}
            //信息类型为图片，不显示栏目
            if(tdPortalNews.getNewsType().equals(4)){
                showNewsType = 0;
            }else{
                showNewsType = 1;
            }

			// 获取附件集合
			annexList = this.tdPortalNews.getTdPortalNewsAnnexes();
			// 图片需要初始化
			RequestContext currentInstance = RequestContext.getCurrentInstance();
			currentInstance.execute("showOffBtn();");

		} else {
			// 发起流程数据为空
			this.tdPortalNews = new TdPortalNews();
			// 默认三颗星
			this.tdPortalNews.setNewsLevel(3);
			// 默认不添加公告栏
			this.tdPortalNews.setIfNotice(0);
			// 默认加载超文本
			this.tdPortalNews.setNewsType(0);
			tempNewsType = 0;
			fileType = ".doc";
			// 文件名默认为uuid
			fileName = UUID.randomUUID().toString().replaceAll("-", "");
			// 初始化附件
			annexList = new ArrayList<TdPortalNewsAnnex>();
			// 初始化附件信息内容
			this.tdPortalNews.setTdPortalNewsAnnexes(annexList);
			// 初始化科室选中科室ID为空
			if(ifCanGrantAllUnit){
				selectOfficeIds= sessionData.getUser().getTsUnit().getRid().toString();
				selectOfficeNames=sessionData.getUser().getTsUnit().getUnitname();
			}else{
				selectOfficeIds = "";
			}
			// 是否置顶
			this.tdPortalNews.setIfZd(0);

			totalPdfPage = 0;
			currPdfPage = 0;

            showNewsType = 1;
		}
		// 传入信息类别
		String colType = JsfUtil.getRequest().getParameter("colType");
		if (StringUtils.isNotBlank(colType)) {// 流程传入信息类型不为空
			ifHasNewsType = true;
			// 初始化栏目Radio
			colNews = new ArrayList<SelectItem>();
			colNewsSize = 0;
			TdPortalNewsType t = this.portalBaseImpl.findTdPortalNewsType(Integer.valueOf(colType));
			this.tdPortalNews.setTdPortalNewsType(t);
		} else {
			ifHasNewsType = false;
			initColNews();
			if (null == this.tdPortalNews.getTdPortalNewsType() && null != this.colNews && this.colNews.size() > 0) {
				// 栏目Id
				String rid = String.valueOf(colNews.get(0).getValue());
				TdPortalNewsType t = new TdPortalNewsType();
				t.setRid(Integer.valueOf(rid));
				this.tdPortalNews.setTdPortalNewsType(t);
			}
		}
		// 如果信息分类为空，则初始化
		if (null == tdPortalNews.getTdPortalNewsType()) {
			TdPortalNewsType newType = new TdPortalNewsType();
			tdPortalNews.setTdPortalNewsType(newType);
		}
		// 初始化页面数据
		initPage();
        //初始化图片类型
        pictureType = portalBaseImpl.findPictureType();
	}

	/**
	 * 添加图片初始化
	 */
	public void addPhotoAction() {
		tdPortalNewsAnnex = new TdPortalNewsAnnex();
		tdPortalNewsAnnex.setAnnexAddr("1");
		croppedImage = null;
		// 添加初始化时，将是否修改照片标识为false
		this.ifModPhoto = false;
	}

	/**
	 * 初始化栏目类型
	 */
	private void initColNews() {
		// 初始化栏目Radio
		colNews = new ArrayList<SelectItem>();
		colNewsSize = 0;
		// 用户ID
		Integer userId = null;
		// 用户管理员，则不过滤条件
		boolean userAdmin = sessionData.getUser().getUseradmin();
		if (!userAdmin) {
			userId = sessionData.getUser().getRid();
		}
		List<Object[]> list = this.portalBaseImpl.fetchColNews(userId);
		if (null != list && list.size() > 0) {
			for (Object[] objArr : list) {
				// 栏目ID
				String rid = String.valueOf(objArr[0]);
				// 栏目名称
				String name = String.valueOf(objArr[1]);
				colNews.add(new SelectItem(rid, name));
			}
			colNewsSize = colNews.size();
		}
	}

	/**
	 * 截图，获取截取的内容
	 */
	public void crop() {
		FileImageOutputStream imageOutput = null;
		try {
			if (StringUtils.isBlank(this.tdPortalNewsAnnex.getAnnexName())) {
				JsfUtil.addErrorMessage("图片标题不能为空！");
				return;
			}
			if (StringUtils.isBlank(this.tdPortalNewsAnnex.getAnnexDesc())) {
				JsfUtil.addErrorMessage("图片描述不能为空！");
				return;
			}
			// 如果为新增图片，则剪切图片
			if (!ifModPhoto) {
				if (croppedImage == null) {
					JsfUtil.addErrorMessage("剪辑图片不能为空！");
					return;
				}
				String fileNamepath = tdPortalNewsAnnex.getAnnexAddr();// 文件名称

				// 配置的虚拟路径
				String pzPath = JsfUtil.getAbsolutePath();
				// 实际文件路径
				String factFilePath = pzPath + fileNamepath;

				if (new File(factFilePath).exists()) {
					new File(factFilePath).delete();
				}
				imageOutput = new FileImageOutputStream(new File(factFilePath));
				imageOutput.write(croppedImage.getBytes(), 0, croppedImage.getBytes().length);
				this.annexList.add(tdPortalNewsAnnex);
				initPhotoTree();
			}
			RequestContext cI = RequestContext.getCurrentInstance();
			this.ifShowCrop = false;
			cI.update("photoDes");
			cI.execute("PF('photoDescript').hide();");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("剪辑图片失败，请重新剪辑！");
		} finally {
			if (null != imageOutput) {
				try {
					imageOutput.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 初始化图片树
	 */
	private void initPhotoTree() {
		photoTree = new DefaultTreeNode("root", null);
		photoTree.setExpanded(true);
		if (null != annexList && annexList.size() > 0) {
			for (TdPortalNewsAnnex tdPortalNewsAnnex : annexList) {
				new DefaultTreeNode(tdPortalNewsAnnex, photoTree);
			}
		}
	}

	@Override
	public String saveAction() {
		if (verifyInfo()) {// 验证数据合法性
			if (null == this.tdPortalNews.getRid()) {// 新增
				// 发布时间
				this.tdPortalNews.setNewsDate(new Date());
				// 发布部门
				TsOffice tsOffice = null;
				TbSysEmp tbSysEmp = sessionData.getUser().getTbSysEmp();
				if (null != tbSysEmp) {
					tsOffice = tbSysEmp.getTsOffice();
				}
				this.tdPortalNews.setTsOffice(tsOffice);
				// 发布人
				Integer fbRid = sessionData.getUser().getRid();
				this.tdPortalNews.setTsUserInfo(new TsUserInfo(fbRid));
				//发布单位
				this.tdPortalNews.setTsUnit(sessionData.getUser().getTsUnit());
				// 保存状态
				this.tdPortalNews.setStateMark(0);
			}
			// 信息关系
			// 选中的人员Id
			this.tdPortalNews.setIfAll((short) 0);
			//发布人员Ids
			StringBuilder perRangeIds = new StringBuilder(); 
			if (StringUtils.isNotBlank(selectOfficeIds)) {
				perRangeIds = new StringBuilder(selectOfficeIds);
			} else {//如果发布范围人数为空，则默认发给所有人员
			// this.tdPortalNews.setIfAll((short) 1);
				List<TsOffice> list= service.selAllOfficeList(null,sessionData.getUser().getTsUnit().getRid());
				for (TsOffice tsOffice : list) {
					perRangeIds.append(",").append(tsOffice.getRid());
				}
				if (perRangeIds.length() > 1) {
					perRangeIds = perRangeIds.deleteCharAt(0);
				}
			}
			if(ifCanGrantAllUnit){
				List<TdPortalNewsUnit> tdPortalNewsUnits = new ArrayList<TdPortalNewsUnit>(0);
				this.tdPortalNews.setTdPortalNewsUnits(tdPortalNewsUnits);
				//将人员Id处理到信息查看权限实体中，进行级联保存
				if( StringUtils.isNotBlank(perRangeIds.toString()))	{
					String[] idsArr = perRangeIds.toString().split(",");
					for (String id : idsArr) {
						TdPortalNewsUnit tdPortalNewsUnit = new TdPortalNewsUnit();
						tdPortalNewsUnit.setFkByNewsId(this.tdPortalNews);
						tdPortalNewsUnit.setFkByUnitId(new TsUnit(Integer.valueOf(id)));
						tdPortalNewsUnits.add(tdPortalNewsUnit);
					}
				}
			}else{
			
				List<TdPortalNewsOffice> tdPortalNewsOffices = new ArrayList<TdPortalNewsOffice>(0);
				this.tdPortalNews.setTdPortalNewsOffices(tdPortalNewsOffices);
				//将人员Id处理到信息查看权限实体中，进行级联保存
				if( StringUtils.isNotBlank(perRangeIds.toString()))	{
					String[] idsArr = perRangeIds.toString().split(",");
					for (String id : idsArr) {
						TdPortalNewsOffice tdPortalNewsOffice = new TdPortalNewsOffice();
						tdPortalNewsOffice.setTdPortalNews(this.tdPortalNews);
						TsOffice tsOffice = new TsOffice(Integer.valueOf(id));
						tdPortalNewsOffice.setTsOffice(tsOffice);
						tdPortalNewsOffices.add(tdPortalNewsOffice);
					}
				}
			}
			//如果不是发布到公告栏，则清空滚动天数
			if (this.tdPortalNews.getIfNotice().intValue() == 0) {
				this.tdPortalNews.setRollDays(null);
			}
			
			tdPortalNews.setTdPortalNewsAnnexes(annexList);
            //如果是图片类型
            if(tdPortalNews.getNewsType().equals(4) && pictureType != null){
                tdPortalNews.setTdPortalNewsType(pictureType);
            }
			tdPortalNews = this.portalBaseImpl.saveNews(this.tdPortalNews, false, 0);
			annexList = tdPortalNews.getTdPortalNewsAnnexes();
			RequestContext.getCurrentInstance().execute("showOffBtn();hideOffice();");
			return BusinessIdUtil.toJsonString(tdPortalNews.getRid().toString());
		} else {
			RequestContext.getCurrentInstance().execute("showOffBtn();");
			return Constants.FLOW_FAILURE;
		}
	}

	@Override
	public String submitAction() {
		return Constants.FLOW_SUCCESS;
	}

	@Override
	public String backAction() {
		return Constants.FLOW_SUCCESS;
	}

	@Override
	public String deleteAction() {
		if (null != this.tdPortalNews && null != this.tdPortalNews.getRid()) {
			String deleteNews = this.portalBaseImpl.deleteNews(this.tdPortalNews.getRid());
			return deleteNews;
		}
		return null;
	}

	/**
	 * 授权初始化
	 */
	public void sqInitAction() {
		Map<String, Object> options = new HashMap<String, Object>();
		if(ifCanGrantAllUnit){
		    options.put("modal", true);
	        options.put("draggable", true);
	        options.put("resizable", false);
	        options.put("height", 500);
	        options.put("width", 920);
	        options.put("contentWidth", 870);
	        //对话框参数
	        Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
	        List<String> paramList = new ArrayList<String>(1);
	        //对话框标题
	        paramList.add("发布范围");
	        paramsMap.put("title", paramList);
	        
	        //选中的单位ids
	        paramList = new ArrayList<String>(1);
			paramList.add(selectOfficeIds);
	        paramsMap.put("selectIds", paramList);
	        RequestContext requestContext = RequestContext.getCurrentInstance();
	        requestContext.openDialog("/webapp/system/selectMutilUnitList", options, paramsMap);
		}else{
			options.put("modal", true);
			options.put("draggable", true);
			options.put("resizable", false);
			options.put("width", 350);
			options.put("height", 400);
			options.put("contentWidth", 320);
			options.put("contentHeight", 380);
	//		options.put("contentWidth", 870);
	
			Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
			/**
			 * 传入标题
			 */
			List<String> paramList = new ArrayList<String>(1);
			paramList.add("发布范围");
			paramsMap.put("officeTitle", paramList);
			/**
			 * 选中科室Id
			 */
			paramList = new ArrayList<String>(1);
			paramList.add(selectOfficeIds);
			paramsMap.put("selectOfficeIds", paramList);
	
			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.openDialog("/webapp/system/selectOfficesList", options, paramsMap);
		}
	}

	/**
	 * 选择人
	 * 
	 * @param event
	 *            弹出框关闭事件
	 */
	@SuppressWarnings("unchecked")
	public void onUserSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			selectOfficeIds = "";
		    selectOfficeNames = "";
		    // 单次选中的ID
			StringBuffer selBuffer = new StringBuffer();
			// 单次选中的名称
			StringBuffer selNameBuffer = new StringBuffer();
		    if(ifCanGrantAllUnit){
				Collection<UnitPO> coll = (Collection<UnitPO>) selectedMap.get("selectUnitList");
				for(UnitPO po:coll){
					selBuffer.append(",").append(po.getUnitId());
					selNameBuffer.append(",").append(po.getUnitName());
				}
				
			}else{
			    Collection<TsOffice> coll = (Collection<TsOffice>) selectedMap.get("selectOffices");
				
				Set<String> nameSet = new LinkedHashSet<String>();
				for (TsOffice t : coll) {
					selBuffer.append(",").append(t.getRid());
					nameSet.add(t.getOfficename());
				}
				Iterator<String> iterator = nameSet.iterator();
				while (iterator.hasNext()) {
					String next = iterator.next();
					selNameBuffer.append(",").append(next);
				}
			}
			if (selBuffer.length() > 1) {
				selBuffer = selBuffer.deleteCharAt(0);
			}
			selectOfficeIds = selBuffer.toString();
			if (selNameBuffer.length() > 1) {
				selNameBuffer = selNameBuffer.deleteCharAt(0);
			}
			selectOfficeNames = selNameBuffer.toString();
		}
	}

	/**
	 * 信息分类选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onportalTypeNodeSelect(NodeSelectEvent event) {
		this.tdPortalNews.setTdPortalNewsType(null);
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			TdPortalNewsType t = (TdPortalNewsType) selectNode.getData();
			this.tdPortalNews.setTdPortalNewsType(t);
		}
	}

	/**
	 * 图片列表
	 * 
	 * @param event
	 */
	public void onPhotoNodeSelect(NodeSelectEvent event) {
		this.tdPortalNewsAnnex = null;
		ifSelTree = false;
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			tdPortalNewsAnnex = (TdPortalNewsAnnex) selectNode.getData();
			ifSelTree = true;
		}
	}

	/**
	 * 删除图片
	 */
	public void delPhoto() {
		if (null == tdPortalNewsAnnex || !ifSelTree) {
			JsfUtil.addErrorMessage("请选择图片名称，再进行删除！");
			return;
		} else {
			this.annexList.remove(tdPortalNewsAnnex);
			initPhotoTree();
			this.ifSelTree = false;
		}
	}

	/**
	 * 修改图片
	 */
	public void modPhoto() {
		if (null == this.tdPortalNewsAnnex || !ifSelTree) {
			JsfUtil.addErrorMessage("请选择图片名称，再进行修改！");
		} else {
			RequestContext.getCurrentInstance().execute("PF('photoDescript').show();");
			this.ifShowCrop = true;
			this.ifSelTree = false;
			this.ifModPhoto = true;
		}
	}

	/**
	 * 选中不同的信息类别，初始化不同类型的信息内容
	 */
	public void selectNewsType() {
		if (0 == this.tdPortalNews.getNewsType()) {// 超文本
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (1 == this.tdPortalNews.getNewsType()) {// word
			fileType = ".doc";
			fileName = UUID.randomUUID().toString().replace("-", "");
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (2 == this.tdPortalNews.getNewsType()) {// excel
			fileType = ".xls";
			fileName = UUID.randomUUID().toString().replace("-", "");
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (3 == this.tdPortalNews.getNewsType()) {// ppt
			fileType = ".ppt";
			fileName = UUID.randomUUID().toString().replace("-", "");
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (4 == this.tdPortalNews.getNewsType()) {// 图片
			annexList = new ArrayList<TdPortalNewsAnnex>();
			tdPortalNewsAnnex = new TdPortalNewsAnnex();
			tdPortalNewsAnnex.setAnnexAddr("1");
			initPhotoTree();
		} else if (5 == this.tdPortalNews.getNewsType()) {// PDF
			currPdfPage = 0;
			totalPdfPage = 0;
			pdfList = new ArrayList<String>();
			tdPortalNews.setNewsAnnex(null);
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		}
		// 确认后修改属性
		tempNewsType = this.tdPortalNews.getNewsType();
        if(tdPortalNews.getNewsType() == 4){
            showNewsType = 0;
        }else{
            showNewsType = 1;
        }
	}

	/**
	 * 还原信息类型数据
	 */
	public void resetNewsType() {
		this.tdPortalNews.setNewsType(tempNewsType);
	}

	/**
	 * 页面加载，初始化信息
	 */
	private void initPage() {
		initNewsType();
		initPhotoTree();
		ifSelTree = false;
		tdPortalNewsAnnex = new TdPortalNewsAnnex();
		tdPortalNewsAnnex.setAnnexAddr("1");
		//初始化滚动天数
		if (this.tdPortalNews.getIfNotice().intValue() == 0) {
			this.tdPortalNews.setRollDays(Integer.parseInt(this.commService
					.findParamValue("PORTAL_SHOW_GGL_DAYS")));
		}
	}

	/**
	 * 初始化信息类型
	 */
	private void initNewsType() {
		newsType = new ArrayList<SelectItem>();
		for (NewsType type : NewsType.values()) {
			int typeNo = type.getTypeNo();
			if(typeNo == 0 || typeNo == 4 || typeNo == 5){
				String typeCn = type.getTypeCN();
				newsType.add(new SelectItem(typeNo, typeCn));
			}
		}
	}

	/**
	 * 验证数据正确性
	 * 
	 * @return
	 */
	private boolean verifyInfo() {
		// 发布部门
		TbSysEmp tbSysEmp = sessionData.getUser().getTbSysEmp();
		if (!ifCanGrantAllUnit && null == tbSysEmp) {
			JsfUtil.addErrorMessage("请维护你的所属科室！");
			return false;
		}

		if (null == this.tdPortalNews.getTdPortalNewsType() || null == this.tdPortalNews.getTdPortalNewsType().getRid()) {
			JsfUtil.addErrorMessage("tabView:editForm:typeName", "栏目不能为空！");
			return false;
		}

		if(ifCanGrantAllUnit && (StringUtils.isBlank(selectOfficeIds) || StringUtils.isBlank(selectOfficeNames))){
			JsfUtil.addErrorMessage("tabView:editForm:selectNames", "发布范围不能为空！");
			return false;
		}
		if (StringUtils.isBlank(this.tdPortalNews.getNewsTitle())) {
			JsfUtil.addErrorMessage("tabView:editForm:newsTitle", "信息标题不能为空！");
			return false;
		}
		
		if (this.tdPortalNews.getIfNotice().intValue() == 1 && null == this.tdPortalNews.getRollDays()) {
			JsfUtil.addErrorMessage("tabView:editForm:rollDays", "滚动天数不能为空！");
			return false;
		}

		if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.HYPERTEXT.getTypeNo()) {// 超文本
			if (StringUtils.isBlank(this.tdPortalNews.getNewsCont())) {
				JsfUtil.addErrorMessage("editForm:myTextarea", "超文本内容不能为空！");
				return false;
			}
			this.tdPortalNews.setNewsAnnex(null);
		} else if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.WORD.getTypeNo()
				|| Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.EXCEL.getTypeNo()
				|| Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.PPT.getTypeNo()) {// office
			StringBuilder path = new StringBuilder(JsfUtil.getAbsolutePath());
			path.append("/officeFiles/").append(fileName).append(fileType);
			File tempFile = new File(path.toString());
			if (!tempFile.exists()) {
				JsfUtil.addErrorMessage("Office信息不能为空！");
				return false;
			}
			// 设置office路径
			this.tdPortalNews.setNewsAnnex(path.toString());
			// 设置FCK内容
			this.tdPortalNews.setNewsCont(null);
		} else if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.PHOTOS.getTypeNo()) {// photos
			// 清空FCK与Office
			this.tdPortalNews.setNewsCont(null);
			this.tdPortalNews.setNewsAnnex(null);
			if (this.annexList.size() == 0) {
				JsfUtil.addErrorMessage("图片信息不能为空！");
				return false;
			}
		} else if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.PDF.getTypeNo()) {// PDF格式
			this.tdPortalNews.setNewsCont(null);
			// PDF地址
			StringBuffer addrPdfs = new StringBuffer();
			if (null != pdfList && pdfList.size() > 0) {
				for (String s : pdfList) {
					addrPdfs.append(",").append(s);
				}
				this.tdPortalNews.setNewsAnnex(addrPdfs.substring(1));
			} else {
				JsfUtil.addErrorMessage("PDF文件不能为空！");
				return false;
			}
		}
		return true;
	}

	/**
	 * 下载文件
	 */
	public void downLoadDiskFile() {
		// 虚拟路径
		String xnPath = JsfUtil.getAbsolutePath();
		// 虚拟路径加上文件的相对路径
		String path = xnPath + this.tdPortalNewsAnnex.getAnnexAddr();
		FileInputStream fis = null;
		try {
			File file = new File(path);
			
			if(file.exists()) {
				fis = new FileInputStream(file);
				String fileString = DownLoadUtil.uploadFile2Database(fis);
				DownLoadUtil.downFile(this.tdPortalNewsAnnex.getAnnexName(), fileString);
			}else {
				JsfUtil.addErrorMessage("该文件已不存在，不能下载！");
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		} finally {// 关闭输入输出流
			try {
				fis.close();
			} catch (IOException e2) {
				e2.printStackTrace();
			}
		}
	}

	/**
	 * 删除上传的文件
	 */
	public void deleteDiskFile() {
		// 配置的虚拟路径
		String xnPath = JsfUtil.getAbsolutePath();
		this.annexList.remove(this.tdPortalNewsAnnex);
		// 附件文件如果存在，则删除
		File tempFile = new File(xnPath + this.tdPortalNewsAnnex.getAnnexAddr());
		if (tempFile.exists()) {
			tempFile.delete();
		}
	}

	/**
	 * 文件上传
	 */
	public void handleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			TdPortalNewsAnnex annex = new TdPortalNewsAnnex();
			try {
				String fileName = file.getFileName();// 文件名称
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				String relativePath = new StringBuffer("/files/").append(uuid)
						.append(fileName.substring(fileName.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(relativePath).toString();
				// 将文件信息保存至list中
				annex.setAnnexName(fileName);// 文件名称
				annex.setAnnexAddr(relativePath);// 文件路径
				annex.setTdPortalNews(this.tdPortalNews);// 主表信息
				annex.setXh(this.annexList.size());// 序号
				this.annexList.add(annex);
				FileUtils.copyFile(filePath, file.getInputstream());
			} catch (Exception e) {
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}
	}

	/**
	 * PDF文件上传
	 */
	public void handlePDFUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			try {
				String fileName = file.getFileName();// 文件名称
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				String relativePath = new StringBuffer("/files/").append(uuid)
						.append(fileName.substring(fileName.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(relativePath).toString();
				pdfList.add(Constants.XNPATH_DIR+relativePath);
				totalPdfPage = pdfList.size();
				currPdfPage = totalPdfPage;
				pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
						.toString();
				FileUtils.copyFile(filePath, file.getInputstream());
			} catch (Exception e) {
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}
	}

	/**
	 * 文件上传
	 */
	public void handlePhotoUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			tdPortalNewsAnnex = new TdPortalNewsAnnex();
			try {
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String fileName = file.getFileName();// 文件名称

				// 配置的虚拟路径
				String pzPath = JsfUtil.getAbsolutePath();
				// 实际文件路径
				String factFilePath = pzPath + "resources/files/" + uuid
						+ fileName.substring(fileName.lastIndexOf("."));
				// 页面显示路径
				String xdpath = "/resources/files/" + uuid + fileName.substring(fileName.lastIndexOf("."));

				// 将文件信息保存至list中
				tdPortalNewsAnnex.setAnnexName(fileName.substring(0,fileName.lastIndexOf(".")));// 文件名称
				tdPortalNewsAnnex.setAnnexAddr(xdpath);// 文件路径
				tdPortalNewsAnnex.setTdPortalNews(this.tdPortalNews);// 主表信息
				tdPortalNewsAnnex.setXh(this.annexList.size());// 序号
				tdPortalNewsAnnex.setAnnexDesc("");

				// 虚拟路径存储一个，项目下临时存取一个
				FileUtils.copyFile(factFilePath, file.getInputstream());
				ImageUtil.compressImage(factFilePath, factFilePath, 520, 390);
				this.ifShowCrop = true;
			} catch (Exception e) {
				this.ifShowCrop = false;
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}
	}

	/**
	 * 删除正文
	 */
	public void delLoadPdf() {
		if (pdfList.size() >= currPdfPage && pdfList.size() > 0) {
			totalPdfPage--;
			pdfList.remove(currPdfPage - 1);
			if (currPdfPage > totalPdfPage) {
				currPdfPage = totalPdfPage;
			}
			if (currPdfPage >= 1) {
				pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
						.toString();
			} else {
				pdfhtml = null;
			}
		}
	}

	/**
	 * 上一个
	 */
	public void upPdf() {
		if (pdfList.size() > 0 && currPdfPage > 0) {
			currPdfPage = currPdfPage - 1;
			pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
					.toString();
		}
	}

	/**
	 * 下一个
	 */
	public void downPdf() {
		if (pdfList.size() > 0 && currPdfPage < pdfList.size()) {
			currPdfPage = currPdfPage + 1;
			pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
					.toString();
		}
	}

	public TreeNode getPortalTypeTree() {
		return portalTypeTree;
	}

	public void setPortalTypeTree(TreeNode portalTypeTree) {
		this.portalTypeTree = portalTypeTree;
	}

	public List<SelectItem> getNewsType() {
		return newsType;
	}

	public void setNewsType(List<SelectItem> newsType) {
		this.newsType = newsType;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TdPortalNews getTdPortalNews() {
		return tdPortalNews;
	}

	public void setTdPortalNews(TdPortalNews tdPortalNews) {
		this.tdPortalNews = tdPortalNews;
	}

	public List<TdPortalNewsType> getTdPortalNewsTypeList() {
		return TdPortalNewsTypeList;
	}

	public void setTdPortalNewsTypeList(List<TdPortalNewsType> tdPortalNewsTypeList) {
		TdPortalNewsTypeList = tdPortalNewsTypeList;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public List<TdPortalNewsAnnex> getAnnexList() {
		return annexList;
	}

	public void setAnnexList(List<TdPortalNewsAnnex> annexList) {
		this.annexList = annexList;
	}

	public TdPortalNewsAnnex getTdPortalNewsAnnex() {
		return tdPortalNewsAnnex;
	}

	public void setTdPortalNewsAnnex(TdPortalNewsAnnex tdPortalNewsAnnex) {
		this.tdPortalNewsAnnex = tdPortalNewsAnnex;
	}

	public TreeNode getPhotoTree() {
		return photoTree;
	}

	public void setPhotoTree(TreeNode photoTree) {
		this.photoTree = photoTree;
	}

	public CroppedImage getCroppedImage() {
		return croppedImage;
	}

	public void setCroppedImage(CroppedImage croppedImage) {
		this.croppedImage = croppedImage;
	}

	public boolean isIfSelTree() {
		return ifSelTree;
	}

	public void setIfSelTree(boolean ifSelTree) {
		this.ifSelTree = ifSelTree;
	}

	public int getTempNewsType() {
		return tempNewsType;
	}

	public void setTempNewsType(int tempNewsType) {
		this.tempNewsType = tempNewsType;
	}

	public List<SelectItem> getColNews() {
		return colNews;
	}

	public void setColNews(List<SelectItem> colNews) {
		this.colNews = colNews;
	}

	public boolean isIfHasNewsType() {
		return ifHasNewsType;
	}

	public void setIfHasNewsType(boolean ifHasNewsType) {
		this.ifHasNewsType = ifHasNewsType;
	}

	public int getColNewsSize() {
		return colNewsSize;
	}

	public void setColNewsSize(int colNewsSize) {
		this.colNewsSize = colNewsSize;
	}

	public int getTotalPdfPage() {
		return totalPdfPage;
	}

	public void setTotalPdfPage(int totalPdfPage) {
		this.totalPdfPage = totalPdfPage;
	}

	public int getCurrPdfPage() {
		return currPdfPage;
	}

	public void setCurrPdfPage(int currPdfPage) {
		this.currPdfPage = currPdfPage;
	}

	public List<String> getPdfList() {
		return pdfList;
	}

	public void setPdfList(List<String> pdfList) {
		this.pdfList = pdfList;
	}

	public String getPdfhtml() {
		return pdfhtml;
	}

	public void setPdfhtml(String pdfhtml) {
		this.pdfhtml = pdfhtml;
	}

	public boolean isIfModPhoto() {
		return ifModPhoto;
	}

	public void setIfModPhoto(boolean ifModPhoto) {
		this.ifModPhoto = ifModPhoto;
	}

    public Integer getShowNewsType() {
        return showNewsType;
    }

    public void setShowNewsType(Integer showNewsType) {
        this.showNewsType = showNewsType;
    }

    @Override
	public void afterBackAction() {

	}

	@Override
	public void afterSaveAction() {

	}

	@Override
	public void afterSubmitAction() {

	}

	@Override
	public Map<Integer, String> supplyPhoneNum(String arg0) {
		return null;
	}

	public boolean isIfShowCrop() {
		return ifShowCrop;
	}

	public void setIfShowCrop(boolean ifShowCrop) {
		this.ifShowCrop = ifShowCrop;
	}

	public String getSelectOfficeIds() {
		return selectOfficeIds;
	}

	public void setSelectOfficeIds(String selectOfficeIds) {
		this.selectOfficeIds = selectOfficeIds;
	}

	public String getSelectOfficeNames() {
		return selectOfficeNames;
	}

	public void setSelectOfficeNames(String selectOfficeNames) {
		this.selectOfficeNames = selectOfficeNames;
	}

	@Override
	public boolean supportPrint() {
		// TODO Auto-generated method stub
		return false;
	}

	public Boolean getIfCanGrantAllUnit() {
		return ifCanGrantAllUnit;
	}

	public void setIfCanGrantAllUnit(Boolean ifCanGrantAllUnit) {
		this.ifCanGrantAllUnit = ifCanGrantAllUnit;
	}

}
