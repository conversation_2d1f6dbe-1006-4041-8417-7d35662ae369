package com.chis.modules.portal.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.el.ELContext;
import javax.el.ExpressionFactory;
import javax.el.MethodExpression;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.event.MethodExpressionActionListener;

import org.primefaces.component.behavior.ajax.AjaxBehavior;
import org.primefaces.component.behavior.ajax.AjaxBehaviorListenerImpl;
import org.primefaces.component.commandlink.CommandLink;
import org.primefaces.component.dashboard.Dashboard;
import org.primefaces.component.panel.Panel;
import org.primefaces.context.RequestContext;
import org.primefaces.event.CloseEvent;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.entity.TdPortalLayout;
import com.chis.modules.portal.entity.TdProtal;
import com.chis.modules.portal.enumn.TemplType;
import com.chis.modules.portal.logic.PortalColumnSetBean;
import com.chis.modules.portal.logic.PortalLayOutBean;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * 门户管理 个人工作站
 * 
 * <AUTHOR>
 * 
 * @history 2014年9月19日
 */
@ManagedBean(name = "portalMgr4PersonBean")
@ViewScoped
public class PortalMgr4PersonBean extends FacesBean {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
	/** 门户对象 */
	private TdProtal tdProtal = new TdProtal();
	/** 查询条件：门户名称 */
	private String searchPortalName;
	/** 门户布局的版式 */
	private PortalLayOutBean layOutBean = PortalLayOutBean.getInstance();
	/** 门户布局的选中的版式 */
	private String layout;
	/** 布局容器 */
	private Dashboard dashboard = (Dashboard) JsfUtil.getApplication().createComponent(Dashboard.COMPONENT_TYPE);
	/** 布局模型 */
	private DashboardModel dashboardModel = new DefaultDashboardModel();
	/** 布局样式 */
	private String columnStyle;
	/** 自己拥有权限的栏目 */
	private List<TdPortalColumn> columnList = new ArrayList<TdPortalColumn>(0);
	/** 缓存自己拥有权限的栏目 */
	private List<TdPortalColumn> allColumnList = new ArrayList<TdPortalColumn>(0);
	/** 选中的栏目 */
	private TdPortalColumn portalColumn;
	/** 换算宽度百分比 */
	private static final BigDecimal TEN = new BigDecimal(10);
	/** 每一个布局容器中的栏目的panel的ID的前缀 */
	private static final String WIGET_FIX = "portal_col_";
	/** 缓存栏目设置 */
	private Map<String, PortalColumnSetBean> columnSetMap = new HashMap<String, PortalColumnSetBean>();
	/** 栏目设置Bean */
	private PortalColumnSetBean columnSetBean = new PortalColumnSetBean();
	/** 当前设置的Panel的id */
	private String columnSetId;

	/**
	 * 布局初始化
	 */
	@PostConstruct
	public void init() {
		/**
		 * 需要初始化每个已经布局的栏目的属性，如显示行数、显示日期
		 */
		this.columnSetId = null;
		this.columnSetBean = new PortalColumnSetBean();
		this.columnSetMap = new HashMap<String, PortalColumnSetBean>();
		Integer userId = this.sessionData.getUser().getRid();
		this.tdProtal = this.service.findPortalByUserId(userId);
		/**
		 * 之前已经布局过了的布局记录
		 */
		List<TdPortalLayout> oldList = this.tdProtal.getTdPortalLayouts();
		// 初始化栏目
		initColumnInfo(userId, oldList);
		// 初始化版式
		TemplType nowType = this.tdProtal.getTemplType();
		if (null == nowType) {
			nowType = (TemplType) EnumUtils.findEnum(TemplType.class, 0);
		}
		initLayoutInfo(nowType);
		String[] splits = nowType.getTypeCN().split(":");
		// 初始化panle
		initPanleInfo(oldList, nowType, splits);
		// 初始化样式
		initStyleInfo(splits);
	}

	/**
	 * 版式更改事件
	 */
	public void layoutChgAction() {
		if (StringUtils.isNotBlank(this.layout)) {
			this.columnList = (List<TdPortalColumn>) ((ArrayList<TdPortalColumn>) this.allColumnList).clone();
			this.dashboard.getChildren().clear();
			this.dashboardModel.getColumns().clear();

			TemplType nowType = (TemplType) EnumUtils.findEnum(TemplType.class, this.layout);

			String[] splits = nowType.getTypeCN().split(":");
			for (int i = 0; i < splits.length; i++) {
				DashboardColumn col = new DefaultDashboardColumn();
				this.dashboardModel.addColumn(col);
			}
			this.dashboard.setModel(this.dashboardModel);

			// 初始化样式
			initStyleInfo(splits);

		} else {
			JsfUtil.addErrorMessage("请刷新页面！");
		}
	}

	/**
	 * Panel关闭事件，将栏目还原
	 * 
	 * @param event
	 *            Panel关闭事件
	 */
	public void panelClose(CloseEvent event) {
		Panel panel = (Panel) event.getSource();

		// 可选栏目栏中添加关掉的栏目
		String panelId = panel.getId();
		TdPortalColumn column = this.service.findColumn(Integer.valueOf(panelId.replace(WIGET_FIX, "")));
		this.columnList.add(column);

		// 去除布局列中的栏目
		List<DashboardColumn> columns = this.dashboardModel.getColumns();
		if (null != columns && columns.size() > 0) {
			for (DashboardColumn dc : columns) {
				dc.removeWidget(panelId);
			}
		}

		// 去除布局容器中的栏目
		this.dashboard.getChildren().remove(panel);
	}

	/**
	 * 保存布局
	 */
	public void saveLayoutAction() {
		List<TdPortalLayout> list = new ArrayList<TdPortalLayout>();
		List<DashboardColumn> columns = this.dashboardModel.getColumns();

		if (null != columns && columns.size() > 0) {
			for (int i = 0; i < columns.size(); i++) {
				DashboardColumn col = columns.get(i);
				List<String> widgets = col.getWidgets();
				if (null != widgets && widgets.size() > 0) {
					for (int j = 0; j < widgets.size(); j++) {
						PortalColumnSetBean columnSetting = this.columnSetMap.get(widgets.get(j));

						String w = widgets.get(j).replace(WIGET_FIX, "");
						TdPortalLayout tpl = new TdPortalLayout();
						tpl.setTdProtal(this.tdProtal);
						tpl.setTdPortalColumn(new TdPortalColumn(Integer.valueOf(w)));
						tpl.setPos(i);
						tpl.setNums(j);
						if (null != columnSetting) {
							tpl.setLines(columnSetting.getLines());
							tpl.setDispDate(columnSetting.getDispDate());
							tpl.setWords(columnSetting.getWords());
							tpl.setHeighth(columnSetting.getHeighth());
						}
						list.add(tpl);
					}
				}
			}
		}

		this.service.saveOrUpdatePortalLayout(list, this.tdProtal.getRid(), this.layout);
		JsfUtil.addSuccessMessage("保存成功！");
	}

	/**
	 * 添加栏目
	 */
	public void addColumn() {
		String columnClientId = WIGET_FIX + this.portalColumn.getRid();
		DashboardColumn column = this.dashboardModel.getColumn(0);
		column.addWidget(columnClientId);

		/**
		 * 为Panel初始化关闭行为
		 */
		ExpressionFactory ef = JsfUtil.getApplication().getExpressionFactory();
		FacesContext fc = FacesContext.getCurrentInstance();
		ELContext elContext = fc.getELContext();

		MethodExpression closeMtd = ef.createMethodExpression(elContext, "#{portalMgr4PersonBean.panelClose}", null, new Class[] { AjaxBehaviorEvent.class });
		AjaxBehavior closeBehavior = new AjaxBehavior();
		AjaxBehaviorListenerImpl closeBehaviorImpl = new AjaxBehaviorListenerImpl(closeMtd, closeMtd);
		closeBehavior.addAjaxBehaviorListener(closeBehaviorImpl);
		closeBehavior.setUpdate("columnPanel,layoutBoard");

		// 添加设置链接
		final CommandLink commandLink = (CommandLink) JsfUtil.getApplication().createComponent(CommandLink.COMPONENT_TYPE);
		commandLink.setStyleClass("ui-panel-titlebar-icon ui-corner-all ui-state-default");
		final HtmlOutputText output = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
		output.setStyleClass("ui-icon ui-icon-gear");
		commandLink.getChildren().add(output);
		commandLink.addActionListener(new MethodExpressionActionListener(ef.createMethodExpression(elContext, "#{portalMgr4PersonBean.panelSettingInit('" + columnClientId + "')}",
				Void.class, new Class[] { String.class })));
		commandLink.setResetValues(Boolean.TRUE);
		commandLink.setUpdate("colSetDialog");
		commandLink.setProcess("@this");
		// 新增的栏目初始化对应的栏目类型
		if (!this.columnSetMap.containsKey(columnClientId)) {
			this.columnSetBean = new PortalColumnSetBean();
			this.columnSetBean.setPortalColType(this.portalColumn.getColType());
			this.columnSetMap.put(columnClientId, columnSetBean);
		}

		/**
		 * 添加Panel
		 */
		final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
		panel.setHeader(this.portalColumn.getColName());
		panel.setId(columnClientId);
		panel.setClosable(Boolean.TRUE);
		panel.addClientBehavior("close", closeBehavior);
		panel.getFacets().put("actions", commandLink);

		this.dashboard.getChildren().add(panel);
		this.columnList.remove(this.portalColumn);
	}

	/**
	 * panel设置初始化
	 * 
	 * @param event
	 */
	public void panelSettingInit(String panelId) {
		this.columnSetId = panelId;
		if (StringUtils.isNotBlank(this.columnSetId)) {
			this.columnSetBean = this.columnSetMap.get(this.columnSetId);
			if (null == this.columnSetBean) {
				this.columnSetBean = new PortalColumnSetBean();
			}

			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.update("colSetDialog");
			requestContext.execute("ColSetDialog.show()");
		} else {
			JsfUtil.addErrorMessage("请刷新页面！");
		}
	}

	/**
	 * panel设置保存
	 */
	public void panelSettingSave() {
		this.columnSetMap.put(this.columnSetId, this.columnSetBean);
		RequestContext.getCurrentInstance().execute("ColSetDialog.hide()");
	}

	/**
	 * 栏目移动事件，必须要有，让PF自动保存一下当前的布局
	 */
	public void handleReorder(DashboardReorderEvent event) {
	}

	/**
	 * 初始化布局中的栏目
	 * 
	 * @param userId
	 *            用户id
	 * @param oldList
	 *            之前已经布局过了的布局记录
	 */
	private void initColumnInfo(Integer userId, List<TdPortalLayout> oldList) {
		// 根据登陆人id 查询所有可以看到的栏目
		this.allColumnList = this.service.findPortalColumn(userId);
		this.columnList = (List<TdPortalColumn>) ((ArrayList<TdPortalColumn>) this.allColumnList).clone();

		if (null != oldList && oldList.size() > 0) {
			List<TdPortalColumn> removeColList = new ArrayList<TdPortalColumn>();
			// 去掉已经在布局中的栏目
			for (TdPortalLayout t : oldList) {
				TdPortalColumn removeCol = t.getTdPortalColumn();
				for (TdPortalColumn col : this.columnList) {
					if (removeCol.getRid().equals(col.getRid())) {
						removeColList.add(col);
						break;
					}
				}

				/**
				 * 需要初始化每个已经布局的栏目的属性，如显示行数、显示日期
				 */
				String columnClientId = WIGET_FIX + t.getTdPortalColumn().getRid();
				PortalColumnSetBean columnSetting = new PortalColumnSetBean();
				columnSetting.setDispDate(t.getDispDate());
				columnSetting.setLines(t.getLines());
				columnSetting.setWords(t.getWords());
				columnSetting.setHeighth(t.getHeighth());
				// 新增栏目类型
				columnSetting.setPortalColType(t.getTdPortalColumn().getColType());
				this.columnSetMap.put(columnClientId, columnSetting);
			}
			if (null != removeColList && removeColList.size() > 0) {
				this.columnList.removeAll(removeColList);
			}
		}
	}

	/**
	 * 初始化版式
	 * 
	 * @param nowType
	 *            版式
	 */
	private void initLayoutInfo(TemplType nowType) {
		if (null == nowType) {
			nowType = TemplType.STT;
		}
		this.layout = nowType.getTypeNo().toString();
	}

	/**
	 * 初始化panle
	 * 
	 * @param oldList
	 *            之前已经布局过了的布局记录
	 * @param nowType
	 *            版式
	 * @param splits
	 *            版式数组
	 */
	private void initPanleInfo(List<TdPortalLayout> oldList, TemplType nowType, String[] splits) {
		/**
		 * 为Panel初始化关闭行为
		 */
		ExpressionFactory ef = JsfUtil.getApplication().getExpressionFactory();
		FacesContext fc = FacesContext.getCurrentInstance();
		ELContext elContext = fc.getELContext();
		MethodExpression closeMtd = ef.createMethodExpression(elContext, "#{portalMgr4PersonBean.panelClose}", null, new Class[] { AjaxBehaviorEvent.class });

		AjaxBehavior closeBehavior = new AjaxBehavior();
		AjaxBehaviorListenerImpl closeBehaviorImpl = new AjaxBehaviorListenerImpl(closeMtd, closeMtd);
		closeBehavior.addAjaxBehaviorListener(closeBehaviorImpl);
		closeBehavior.setUpdate("columnPanel,layoutBoard");

		// 初始化每一列
		this.dashboardModel.getColumns().clear();
		this.dashboard.getChildren().clear();

		for (int i = 0; i < splits.length; i++) {
			DashboardColumn col = new DefaultDashboardColumn();

			if (null != oldList && oldList.size() > 0) {
				// 给每一列添加设置过的栏目
				for (TdPortalLayout t : oldList) {
					// 位置相同
					if (t.getPos().intValue() == i) {
						String columnClientId = WIGET_FIX + t.getTdPortalColumn().getRid();
						col.addWidget(columnClientId);

						// 添加设置链接
						final CommandLink commandLink = (CommandLink) JsfUtil.getApplication().createComponent(CommandLink.COMPONENT_TYPE);
						commandLink.setStyleClass("ui-panel-titlebar-icon ui-corner-all ui-state-default");
						final HtmlOutputText output = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
						output.setStyleClass("ui-icon ui-icon-gear");
						commandLink.getChildren().add(output);
						commandLink.addActionListener(new MethodExpressionActionListener(ef.createMethodExpression(elContext, "#{portalMgr4PersonBean.panelSettingInit('"
								+ columnClientId + "')}", Void.class, new Class[] { String.class })));
						commandLink.setResetValues(Boolean.TRUE);
						commandLink.setUpdate("colSetDialog");
						commandLink.setProcess("@this");

						final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
						panel.setHeader(t.getTdPortalColumn().getColName());
						panel.setId(columnClientId);
						panel.setClosable(Boolean.TRUE);
						panel.addClientBehavior("close", closeBehavior);
						panel.getFacets().put("actions", commandLink);

						this.dashboard.getChildren().add(panel);
					}
				}
			}

			this.dashboardModel.addColumn(col);
		}
		this.dashboard.setModel(this.dashboardModel);
	}

	/**
	 * 初始化样式
	 * 
	 * @param splits
	 *            版式
	 */
	private void initStyleInfo(String[] splits) {
		// 初始化样式
		StringBuilder sb = new StringBuilder();
		sb.append("<style type=\"text/css\">");
		sb.append("		.ui-panel, .ui-dashboard div.ui-state-hover {");
		sb.append("				margin: 10px;");
		sb.append("		}");
		for (int i = 0; i < splits.length; i++) {
			sb.append("		 .ui-dashboard    .ui-dashboard-column:nth-child(").append(i + 1).append(")  { ");
			sb.append("		 		width: ").append(new BigDecimal(splits[i]).multiply(TEN)).append("%;");
			sb.append("		 }");
		}
		sb.append("</style>");
		this.columnStyle = sb.toString();
	}

	public TdProtal getTdProtal() {
		return tdProtal;
	}

	public void setTdProtal(TdProtal tdProtal) {
		this.tdProtal = tdProtal;
	}

	public String getSearchPortalName() {
		return searchPortalName;
	}

	public void setSearchPortalName(String searchPortalName) {
		this.searchPortalName = searchPortalName;
	}

	public PortalLayOutBean getLayOutBean() {
		return layOutBean;
	}

	public void setLayOutBean(PortalLayOutBean layOutBean) {
		this.layOutBean = layOutBean;
	}

	public String getLayout() {
		return layout;
	}

	public void setLayout(String layout) {
		this.layout = layout;
	}

	public Dashboard getDashboard() {
		return dashboard;
	}

	public void setDashboard(Dashboard dashboard) {
		this.dashboard = dashboard;
	}

	public List<TdPortalColumn> getColumnList() {
		return columnList;
	}

	public void setColumnList(List<TdPortalColumn> columnList) {
		this.columnList = columnList;
	}

	public TdPortalColumn getPortalColumn() {
		return portalColumn;
	}

	public void setPortalColumn(TdPortalColumn portalColumn) {
		this.portalColumn = portalColumn;
	}

	public DashboardModel getDashboardModel() {
		return dashboardModel;
	}

	public void setDashboardModel(DashboardModel dashboardModel) {
		this.dashboardModel = dashboardModel;
	}

	public String getColumnStyle() {
		return columnStyle;
	}

	public void setColumnStyle(String columnStyle) {
		this.columnStyle = columnStyle;
	}

	public PortalColumnSetBean getColumnSetBean() {
		return columnSetBean;
	}

	public void setColumnSetBean(PortalColumnSetBean columnSetBean) {
		this.columnSetBean = columnSetBean;
	}

	public String getColumnSetId() {
		return columnSetId;
	}

	public void setColumnSetId(String columnSetId) {
		this.columnSetId = columnSetId;
	}

}
