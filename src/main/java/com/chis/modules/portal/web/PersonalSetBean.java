package com.chis.modules.portal.web;

import java.util.ArrayList;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.pdfbox.pdfwriter.ContentStreamWriter;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TsPersonalSetting;
import com.chis.modules.portal.enumn.PersonSetType;
import com.chis.modules.portal.logic.Theme;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 个人设置受管Bean
 *
 * <AUTHOR>
 * @createDate 2014年10月8日
 */
@ManagedBean(name = "personalSetBean")
@ViewScoped
public class PersonalSetBean extends FacesBean {

	private static final long serialVersionUID = 2350275983339647923L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
    /** 手机号码 */
    private String mobilePhone;
    /** 新密码 */
    private String password;
    /** 确认密码 */
    private String password2;
    /** 主页设置集合 */
    private List<String[]> portalList;

    /** 首页地址 */
    private String url;
    /** 首页名称 */
    private String openName;
    /** 个人设置实体 */
    private TsPersonalSetting tsPersonalSetting;
    /** 选中的页面 */
    private int selectedPage;
    /** 主题集合 */
    private List<Theme> themes;
    /** 选中的皮肤 */
    private String selectedSkin;
    private boolean ifUseQickDesk;
    private String passwordLevel;
    private CommServiceImpl commService = (CommServiceImpl)SpringContextHolder.getBean(CommServiceImpl.class);

    public PersonalSetBean() {
    }

    /**
     * 进入个人设置初始化
     */
    private void pageInit() {
        //默认不选中
        selectedPage = -1;
        portalList = new ArrayList<String[]>();
        //门户Id与名称集合
        String ports = this.service.findMyPortal(this.sessionData.getUser().getRid());
        if(StringUtils.isNotBlank(ports))    {
            String[] split = ports.split(";");
            for(String s : split)   {
                if(StringUtils.isNotBlank(s))    {
                    String[] split1 = s.split(",");
                    //地址
                    String url = new StringBuffer("/webapp/portal/portalCenter.faces?pid=").append(split1[1]).toString();
                    portalList.add(new String[]{"PORTAL","set-desktop1.png",split1[0],url});
                }
            }
        }
        portalList.add(new String[]{"PERSONALPORTAL","set-desktop2.png","工作台","/webapp/portal/personalPortalCenter.faces"});
        portalList.add(new String[]{"PERSONMENU","set-desktop3.png","个人桌面","content.faces"});
        portalList.add(new String[]{"MENUDESK","set-desktop3.png","菜单桌面","content_menu.faces"});

        //个人设置实体
        TsPersonalSetting personSetting = this.service.findPersonSetting(this.sessionData.getUser().getRid());
        if( null != personSetting)    {
            openName =  personSetting.getDefaultUrlType().name();
            url = personSetting.getDefaultUrl();
            selectedSkin = personSetting.getDefaultSkin();
        }
        //如果皮肤为空，则设置默认皮肤
        if(StringUtils.isBlank(selectedSkin))    {
            selectedSkin = Constants.DEFALUTSKINNAME;
        }
        if(StringUtils.isBlank(openName)){
            openName = PersonSetType.MENUDESK.name();
            url = "content_menu.faces";
        }
        for(int i = 0 ;i <portalList.size() ; i++)   {
            String[] strArr = portalList.get(i);
            if(openName.equals(strArr[0]) && url.equals(strArr[3]) )    {
                selectedPage = i;
                break;
            }
        }

        //初始化手机号码，号码不从session中直接取
        mobilePhone = null;
        
//      TbSysEmp tbSysEmp = sessionData.getUser().getTbSysEmp();
        TsPersonalSetting tps=service.findPersonSetting(sessionData.getUser().getRid());
        TbSysEmp tbSysEmp = (tps==null?sessionData.getUser().getTbSysEmp():tps.getTsUserInfo().getTbSysEmp());
       
        if( null != tbSysEmp)    {
            mobilePhone = tbSysEmp.getMbNum();
        }else{
        	mobilePhone=tps==null?sessionData.getUser().getMbNum():tps.getTsUserInfo().getMbNum();
        }

        //初始化密码
        password = null;
        password2 = null;
        TsUserInfo user=this.service.findTsUserInfo(this.sessionData.getUser().getRid());
        if(null != user.getDispKjmenu() && user.getDispKjmenu()==1){
        	this.ifUseQickDesk=true;
        }else{
        	this.ifUseQickDesk=false;
        }
        

        //初始化主题集合
        initThemeList();
    }


    /**
     * 保存方法
     */
    public void saveAction()    {
        if(StringUtils.isBlank(openName))    {
            JsfUtil.addErrorMessage("请选择主页设置！");
            return;
        }
        if(StringUtils.isNotBlank(password) || StringUtils.isNotBlank(password2))    {
            if(!String.valueOf(password).equals(password2) ){
                JsfUtil.addErrorMessage("password","新密码与确认密码不相同！");
                return;
            }
            if(password.length()<6  )    {
                JsfUtil.addErrorMessage("password","输入密码长度最少不低于6位数！");
                return;
            }
        }
      //密码级别过低时是否保存
    	String value = commService.findParamValue("STRONG_PASSWORD");
    	if (value.equals("1")&& StringUtils.isNotBlank(passwordLevel)&&passwordLevel.equals("弱")) {
    		JsfUtil.addErrorMessage("您输入的密码不符合安全强度要求，密码要求为长度不少于6位且为数字加字符！");
    		return;
    	}
        Integer empId = null;
        TbSysEmp tbSysEmp = sessionData.getUser().getTbSysEmp();
        if( null != tbSysEmp)    {
            empId = tbSysEmp.getRid();
        }

        service.saveOrUpdatePersonSet(sessionData.getUser().getRid(),empId,openName,password,url,
                mobilePhone,selectedSkin,this.ifUseQickDesk);
        //更新Session中的皮肤
        sessionData.setSkinName(selectedSkin);
        //手机号码不为空，则更新人员号码
        if(StringUtils.isNotBlank(mobilePhone) && null != sessionData.getUser().getTbSysEmp() )    {
        	if(!mobilePhone.matches(Constants.MOBILE_REGEX)){
        		JsfUtil.addErrorMessage("您输入的手机号码格式不正确！");
        		return;
        	}
        	sessionData.getUser().getTbSysEmp().setMbNum(mobilePhone);
        }
        if(StringUtils.isNotBlank(openName) && StringUtils.isNotBlank(url))    {
            for(int i = 0 ;i <portalList.size() ; i++)   {
                String[] strArr = portalList.get(i);
                if(openName.equals(strArr[0]) && url.equals(strArr[3]) )    {
                    selectedPage = i;
                    break;
                }
            }
        }

        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.execute("PF('PersonalSet').hide();");
        requestContext.update("dialogDeskForm:personForm:selectedPage");
    }

    /**
     * 显示个性化弹出框
     */
    public void showPersonalDiag()  {
        pageInit();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.execute("PersonalSet.show();");
        requestContext.update("dialogDeskForm:personForm");
    }

    /**
     * 初始化主题集合
     */
    private void initThemeList()    {
        themes = new ArrayList<Theme>();
        themes.add(new Theme( "浅蓝天空", "redmond",""));
        themes.add(new Theme( "神秘蓝灰", "hot-sneaks",""));
        themes.add(new Theme( "黑色深沉", "black-tie",""));
        themes.add(new Theme( "银色酷炫", "aristo",""));
        themes.add(new Theme( "蓝色经典", "bluesky",""));
        themes.add(new Theme( "激情岁月", "blitzer",""));
        themes.add(new Theme( "清澈玻璃", "glass-x",""));



//        themes.add(new Theme("Afterdark", "afterdark",""));
//        themes.add(new Theme( "Afternoon", "afternoon",""));
//        themes.add(new Theme("Afterwork", "afterwork",""));
//        themes.add(new Theme( "Aristo", "aristo",""));
////        themes.add(new Theme( "Black-Tie", "black-tie",""));
//
//        themes.add(new Theme( "Blitzer", "blitzer",""));
//        themes.add(new Theme( "Bluesky", "bluesky",""));
//        themes.add(new Theme( "Bootstrap", "bootstrap",""));
//        themes.add(new Theme( "Casablanca", "casablanca",""));//粉色
//        themes.add(new Theme( "Cupertino", "cupertino",""));
//        themes.add(new Theme( "Cruze", "cruze",""));
//        themes.add(new Theme( "Dark-Hive", "dark-hive",""));
//        themes.add(new Theme( "Delta", "delta",""));
//        themes.add(new Theme( "Dot-Luv", "dot-luv",""));
//        themes.add(new Theme( "Eggplant", "eggplant",""));
//        themes.add(new Theme( "Excite-Bike", "excite-bike",""));
//        themes.add(new Theme( "Flick", "flick",""));
//        themes.add(new Theme( "Glass-X", "glass-x",""));
//        themes.add(new Theme( "Home", "home",""));
//        themes.add(new Theme( "Hot-Sneaks", "hot-sneaks",""));
//        themes.add(new Theme( "Humanity", "humanity",""));
//        themes.add(new Theme( "Le-Frog", "le-frog",""));
//        themes.add(new Theme( "Midnight", "midnight",""));
//        themes.add(new Theme( "Mint-Choc", "mint-choc",""));
//        themes.add(new Theme( "Overcast", "overcast",""));
//        themes.add(new Theme( "Pepper-Grinder", "pepper-grinder",""));
//
//        themes.add(new Theme( "Rocket", "rocket",""));
//        themes.add(new Theme( "Sam", "sam",""));
//        themes.add(new Theme( "Smoothness", "smoothness",""));
//        themes.add(new Theme( "South-Street", "south-street",""));
//        themes.add(new Theme( "Start", "start",""));
//        themes.add(new Theme( "Sunny", "sunny",""));
//        themes.add(new Theme( "Swanky-Purse", "swanky-purse",""));
//        themes.add(new Theme( "Trontastic", "trontastic",""));
//        themes.add(new Theme( "UI-Darkness", "ui-darkness",""));
//        themes.add(new Theme( "UI-Lightness", "ui-lightness",""));
//        themes.add(new Theme( "Vader", "vader",""));


    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword2() {
        return password2;
    }

    public void setPassword2(String password2) {
        this.password2 = password2;
    }

    public List<String[]> getPortalList() {
        return portalList;
    }

    public void setPortalList(List<String[]> portalList) {
        this.portalList = portalList;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getOpenName() {
        return openName;
    }

    public void setOpenName(String openName) {
        this.openName = openName;
    }

    public TsPersonalSetting getTsPersonalSetting() {
        return tsPersonalSetting;
    }

    public void setTsPersonalSetting(TsPersonalSetting tsPersonalSetting) {
        this.tsPersonalSetting = tsPersonalSetting;
    }

    public int getSelectedPage() {
        return selectedPage;
    }

    public void setSelectedPage(int selectedPage) {
        this.selectedPage = selectedPage;
    }


    public String getSelectedSkin() {
        return selectedSkin;
    }

    public void setSelectedSkin(String selectedSkin) {
        this.selectedSkin = selectedSkin;
    }

    public List<Theme> getThemes() {
        return themes;
    }

    public void setThemes(List<Theme> themes) {
        this.themes = themes;
    }

	public boolean isIfUseQickDesk() {
		return ifUseQickDesk;
	}

	public void setIfUseQickDesk(boolean ifUseQickDesk) {
		this.ifUseQickDesk = ifUseQickDesk;
	}

	public String getPasswordLevel() {
		return passwordLevel;
	}

	public void setPasswordLevel(String passwordLevel) {
		this.passwordLevel = passwordLevel;
	}
    
}
