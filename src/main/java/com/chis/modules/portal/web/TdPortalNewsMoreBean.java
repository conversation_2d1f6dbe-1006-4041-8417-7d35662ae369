package com.chis.modules.portal.web;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesSimpleBean;

/**
 * 更多信息查看
 * 
 * <AUTHOR>
 * @history 2014-08-18
 * 
 * 
 * <p>修订内容：</p>
 * 信息查看的时候多关联信息查看单位
 * @ClassReviser xq,2018年1月16日,TdPortalNewsMoreBean
 */
@ManagedBean(name = "tdPortalNewsMoreBean")
@ViewScoped
public class TdPortalNewsMoreBean extends FacesSimpleBean {

	private static final long serialVersionUID = 2695401462098651426L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl portalBaseImpl = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 查询标题 */
	private String searchTitle;
	/** 查询科室id */
	private Integer searchOfficeId;
	/** 科室名称 */
	private String searchOfficeName;
	/** 查询发布人 */
	private String searchMan;
	/** 查询开始日期 */
	private Date searchStartDate;
	/** 查询结束日期 */
	private Date searchEndDate;
	/** 模块标题 */
	private String pageTitle;
	/** 消息类型 */
	private String newsType;
	/** 添加页面地区树 */
	private TreeNode treeNode;
	/** 选中的树 */
	private TreeNode selectedNode;

	/** +是否普通栏目带多标签的跳转，如带多标签，则需要加载其对应的子项类别 */
	private String special;
	/** +当前登陆人所在科室Ids */
	private String officeIds;

	@PostConstruct
	public void init() {
		this.searchTitle = "";
		this.searchOfficeId = null;
		this.searchMan = "";
		this.searchStartDate = null;
		this.searchEndDate = null;
		officeIds = commService.findUserAllOfficeIds(sessionData.getUser().getRid());
		// 初始化科室信息
		initTree();

		this.newsType = JsfUtil.getRequest().getParameter("newsType");
		this.pageTitle = JsfUtil.getRequest().getParameter("pageTitle");
		this.special = JsfUtil.getRequest().getParameter("special");

		// 是否多tab的普通栏目跳转，如是，则需要加载子类别
		if ("1".equals(this.special)) {
			TdPortalColumn findColumn = this.portalBaseImpl.findColumn(Integer.valueOf(this.newsType));
			if (null != findColumn && null != findColumn.getTdPortalNewsType()) {
				this.newsType = findColumn.getTdPortalNewsType().getRid().toString();
				TdPortalNewsType findTdPortalNewsType = this.portalBaseImpl.findTdPortalNewsType(Integer.valueOf(this.newsType));
				if (null != findTdPortalNewsType.getTdPortalNewsTypes() && findTdPortalNewsType.getTdPortalNewsTypes().size() > 0) {
					for (TdPortalNewsType type : findTdPortalNewsType.getTdPortalNewsTypes()) {
						this.newsType = new StringBuilder(this.newsType).append(",").append(type.getRid()).toString();
					}
				}
			}
		}
		this.searchAction();

	}

	/**
	 * 发布信息的查看范围为 ：信息查看权限中的人+科室
	 */
	@Override
	public String[] buildHqls() {
		StringBuilder countsql = new StringBuilder("");
		countsql.append(" select count(distinct t.rid) from TdPortalNews t left outer join t.tdPortalNewsOffices ofs");
		countsql.append(" left outer join t.tdPortalNewsAuthes aus");
		countsql.append(" left outer join t.tdPortalNewsUnits units");
		
		StringBuffer sql = new StringBuffer("");
		sql.append(" select distinct new TdPortalNews(t.rid,t.tdPortalNewsType, t.tsOffice, t.newsTitle,t.newsType, t.newsAnnex,");
		sql.append(" t.newsLevel, t.ifNotice, t.stateMark,t.newsDate,t.tsUserInfo, t.ifZd,  t.newAdr,t.ifAll) ");
		sql.append(" from TdPortalNews t left outer join t.tdPortalNewsOffices ofs");
		sql.append(" left outer join t.tdPortalNewsAuthes aus");
		sql.append(" left outer join t.tdPortalNewsUnits units");
		
		sql.append(" where t.stateMark = 1 and (ofs.tsOffice.rid in (").append(officeIds).append(")");
		countsql.append(" where t.stateMark = 1 and (ofs.tsOffice.rid in (").append(officeIds).append(")");

		sql.append(" or units.fkByUnitId.rid = ").append(sessionData.getUser().getTsUnit().getRid()).append("");
		countsql.append(" or units.fkByUnitId.rid = ").append(sessionData.getUser().getTsUnit().getRid()).append("");
		
		sql.append(" or aus.tsUserInfo.rid = ").append(sessionData.getUser().getRid()).append(")");
		countsql.append(" or aus.tsUserInfo.rid = ").append(sessionData.getUser().getRid()).append(")");

		sql.append(" and t.tdPortalNewsType.rid in (").append(this.newsType).append(")");
		countsql.append(" and t.tdPortalNewsType.rid in (").append(this.newsType).append(")");

		if (StringUtils.isNotBlank(this.searchTitle)) {
			sql.append(" and t.newsTitle like :newsTitle ESCAPE '\\\' ");
			countsql.append(" and t.newsTitle like :newsTitle ESCAPE '\\\' ");
			this.paramMap.put("newsTitle", "%" + this.convertBFH(this.searchTitle.trim()) + "%");
		}

		if (null != this.searchOfficeId) {
			sql.append(" and t.tsOffice.rid = ").append(this.searchOfficeId);
			countsql.append(" and t.tsOffice.rid = ").append(this.searchOfficeId);
		}

		if (StringUtils.isNotBlank(this.searchMan)) {
			sql.append(" and t.tsUserInfo.username like :username ESCAPE '\\\' ");
			countsql.append(" and t.tsUserInfo.username like :username ESCAPE '\\\' ");
			this.paramMap.put("username", "%" + this.convertBFH(this.searchMan.trim()) + "%");
		}

		if (null != this.searchStartDate) {
			sql.append(" and t.newsDate >= :startDate ");
			countsql.append(" and t.newsDate >= :startDate ");
			paramMap.put("startDate", this.searchStartDate);
		}

		if (null != this.searchEndDate) {
			sql.append(" and t.newsDate < :endDate ");
			countsql.append(" and t.newsDate < :endDate ");
			paramMap.put("endDate", DateUtils.addDays(this.searchEndDate, 1));
		}
		sql.append(" order by t.newsDate desc");
		return new String[] { sql.toString(), countsql.toString() };
	}

	/**
	 * 所属分类选择
	 * 
	 * @param node
	 *            选择的物资分类
	 */
	public void searchSelectTreeNode(TreeNode node) {
		this.searchOfficeId = null;
		this.searchOfficeName = "";
		if (null != node) {
			// 遍历选择的物资分类，记录名称与id
			TsOffice t = (TsOffice) node.getData();
			this.searchOfficeId = t.getRid();
			this.searchOfficeName = t.getOfficename();
		}
	}

	/**
	 * 解决页面模糊查询时，输入% 或 ％查询数据有误问题
	 * 
	 * @param str
	 *            页面传入的值
	 * @return
	 */
	private String convertBFH(String str) {
		if (str.indexOf("%") != -1 || str.indexOf("％") != -1) {
			str = str.replaceAll("%", "\\\\%").replaceAll("％", "\\\\％");
		}
		return str;
	}

	/**
	 * 初始化查询条件的树
	 */
	private void initTree() {
		this.treeNode = new DefaultTreeNode("root", null);

		TsOffice ts = new TsOffice();
		ts.setOfficename("所有科室");
		TreeNode allNode = new DefaultTreeNode(ts, this.treeNode);
		allNode.setExpanded(true);

		TsUserInfo tsUserInfo = this.sessionData.getUser();
		Integer unitRid = tsUserInfo.getTsUnit().getRid();
		List<TsOffice> list = this.portalBaseImpl.findOfficeByUnitId(unitRid);
		if (null != list && list.size() > 0) {
			Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
			Map<String, TsOffice> allMap = new HashMap<String, TsOffice>(); // 所有物资分类

			for (TsOffice t : list) {
				allMap.put(String.valueOf(t.getRid()), t);
				if (StringUtils.isNotBlank(String.valueOf(t.getRid()))) {
					// 只有第一层
					if (StringUtils.containsNone(String.valueOf(t.getRid()), ".")) {
						firstLevelNoSet.add(String.valueOf(t.getRid()));
					} else {
						levelNoSet.add(String.valueOf(t.getRid()));
					}
				}
			}

			for (String ln : firstLevelNoSet) {
				TreeNode node = new DefaultTreeNode(allMap.get(ln), allNode);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
	}

	/**
	 * 构建菜单树
	 * 
	 * @param levelNo
	 *            菜单层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的层级编码集合
	 * @param allMap
	 *            所有数据级map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsOffice> allMap, TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				// 根据编码，获取树，组成新的树节点
				TreeNode node = new DefaultTreeNode(allMap.get(ln), parentNode);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
	}

	public String getSearchTitle() {
		return searchTitle;
	}

	public void setSearchTitle(String searchTitle) {
		this.searchTitle = searchTitle;
	}

	public Integer getSearchOfficeId() {
		return searchOfficeId;
	}

	public void setSearchOfficeId(Integer searchOfficeId) {
		this.searchOfficeId = searchOfficeId;
	}

	public String getSearchMan() {
		return searchMan;
	}

	public void setSearchMan(String searchMan) {
		this.searchMan = searchMan;
	}

	public Date getSearchStartDate() {
		return searchStartDate;
	}

	public void setSearchStartDate(Date searchStartDate) {
		this.searchStartDate = searchStartDate;
	}

	public Date getSearchEndDate() {
		return searchEndDate;
	}

	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}

	public String getPageTitle() {
		return pageTitle;
	}

	public void setPageTitle(String pageTitle) {
		this.pageTitle = pageTitle;
	}

	public String getNewsType() {
		return newsType;
	}

	public void setNewsType(String newsType) {
		this.newsType = newsType;
	}

	public String getSearchOfficeName() {
		return searchOfficeName;
	}

	public void setSearchOfficeName(String searchOfficeName) {
		this.searchOfficeName = searchOfficeName;
	}

	public TreeNode getTreeNode() {
		return treeNode;
	}

	public void setTreeNode(TreeNode treeNode) {
		this.treeNode = treeNode;
	}

	public TreeNode getSelectedNode() {
		return selectedNode;
	}

	public void setSelectedNode(TreeNode selectedNode) {
		this.selectedNode = selectedNode;
	}

}
