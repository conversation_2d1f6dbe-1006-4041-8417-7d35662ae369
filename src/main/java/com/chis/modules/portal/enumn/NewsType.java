package com.chis.modules.portal.enumn;

/**
 * 门户信息类型
 * <AUTHOR>
 * @createDate 2014-8-16
 */
public enum NewsType {

	HYPERTEXT((short)0) {
        public String getTypeCN() { return "超文本";}
	},

    WORD((short)1) {
        public String getTypeCN() { return "Word";}
	},

    EXCEL((short)2) {
        public String getTypeCN() { return "Excel";}
    },

    PPT((short)3) {
        public String getTypeCN() { return "PPT";}
    },

    PHOTOS((short)4) {
        public String getTypeCN() { return "图片";}
    },

    PDF((short)5) {
        public String getTypeCN() { return "PDF";}
    }
    ;

	private final Short typeNo;

	NewsType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
