package com.chis.modules.portal.enumn;

/**
 *  默认首页类型
 * <AUTHOR>
 * @createDate 2014-10-9
 */
public enum PersonSetType {

	PORTAL((short)0) {
        public String getTypeCN() { return "门户";}
	},

    PERSONALPORTAL((short)1) {
        public String getTypeCN() { return "工作台";}
	},

    PERSONMENU((short)2) {
        public String getTypeCN() { return "个人桌面";}
    },

    MENUDESK((short)3) {
        public String getTypeCN() { return "菜单桌面";}
    }
    ;
	private final Short typeNo;

	PersonSetType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

    public static void  main(String args[]){
        System.out.print(PersonSetType.MENUDESK.name());
    }

}
