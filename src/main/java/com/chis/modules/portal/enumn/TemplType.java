package com.chis.modules.portal.enumn;

/**
 * 模板类型，门户的布局版式
 * <AUTHOR>
 */
public enum TemplType {
	
	STT((short)0) {
        public String getTypeCN() { return "3:4:3";}
	},

	TST((short)1) {
        public String getTypeCN() { return "3:3:4";}
	},

    TTS((short)2) {
        public String getTypeCN() { return "3:7";}
    },

    FTT((short)3) {
        public String getTypeCN() { return "4:3:3";}
    },
    
    ST((short)4) {
        public String getTypeCN() { return "7:3";}
    }

    ;

	private final Short typeNo;

	TemplType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
