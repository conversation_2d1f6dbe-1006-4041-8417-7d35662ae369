package com.chis.modules.portal.enumn;

/**
 * 栏目类型
 * <AUTHOR>
 */
public enum PortalColType {
	
	PT((short)0) {
        public String getTypeCN() { return "普通栏目";}
	},

	IMG((short)1) {
        public String getTypeCN() { return "图片栏目";}
	},

    GGL((short)2) {
        public String getTypeCN() { return "公告栏";}
    },

    LINK((short)3) {
        public String getTypeCN() { return "链接栏目";}
    },

    OTHER((short)4) {
        @Override
        public String getTypeCN() {
            return "其它栏目";
        }
    };

	private final Short typeNo;

	PortalColType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}

	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
