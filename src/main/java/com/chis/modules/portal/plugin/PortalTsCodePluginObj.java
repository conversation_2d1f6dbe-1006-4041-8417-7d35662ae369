package com.chis.modules.portal.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 门户码表的插件
 * 码表编码是写死的
 * <AUTHOR>
 * @createDate 2014-8-14
 */
public class PortalTsCodePluginObj {

	public static Set<TsCodeType> codeTypeSet;
	public static Set<TsSimpleCode> simpleCodeSet;

	static {
		codeTypeSet = new HashSet<TsCodeType>();
		simpleCodeSet = new HashSet<TsSimpleCode>();
	}

}
