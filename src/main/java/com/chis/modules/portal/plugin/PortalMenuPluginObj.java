package com.chis.modules.portal.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenu;

/**
 * 门户菜单的插件
 * <AUTHOR>
 * @createDate 2014-8-14
 */
public class PortalMenuPluginObj {

	public static Set<TsMenu> menuSet;
	
	static {
		menuSet = new HashSet<TsMenu>();
//		menuSet.add(new TsMenu("013","门户管理","portal","门户管理",Short.valueOf("0"),null,"portal2.png", new Date(), 1, 1));
//	    menuSet.add(new TsMenu("013.001","信息类型维护","xxlxwh","信息类型维护",Short.valueOf("1"),"/webapp/portal/portalNewsTypeList.faces","systems-analysis.png",new Date(),1,2));
//        menuSet.add(new TsMenu("013.002","栏目管理","lmgl","栏目管理",Short.valueOf("1"),"/webapp/portal/portalColumnList.faces","column-set.png",new Date(),1,3));
//        menuSet.add(new TsMenu("013.003","常用链接管理","cyljgl","常用链接管理",Short.valueOf("1"),"/webapp/portal/portalLinksList.faces","jh-wiki.png",new Date(),1,4));
//        menuSet.add(new TsMenu("013.004","门户管理","portal_mhgl","门户管理",Short.valueOf("1"),"/webapp/portal/portalMgrList.faces","portal.png",new Date(),1,5));
//        menuSet.add(new TsMenu("013.005","信息管理","xxgl","信息管理",Short.valueOf("1"),"/webapp/portal/tdPortalNewsManagList.faces","integrated-manage.png","integrated-manage.png",new Date(),1,6));
    }
	
}
