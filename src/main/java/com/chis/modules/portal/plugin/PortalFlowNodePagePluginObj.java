package com.chis.modules.portal.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.modules.system.enumn.SystemType;

/**
 * 流程节点页面插件
 * <AUTHOR> 2014-01-08
 */
public class PortalFlowNodePagePluginObj {

	public static Set<TdFlowNodePage> tdFlowNodePageSet;

	static {
		tdFlowNodePageSet = new HashSet<TdFlowNodePage>();

		tdFlowNodePageSet.add(new TdFlowNodePage(SystemType.PORTAL,"mhgl_lccs_qjym","门户管理-流程测试-请假页面","/webapp/test/vaccEdit.xhtml",1));

		tdFlowNodePageSet.add(new TdFlowNodePage(SystemType.PORTAL,"mhgl_xxfb_xxfb","门户管理-信息发布-信息发布","/webapp/portal/tdPortalNewsPublishList.xhtml",2));
		tdFlowNodePageSet.add(new TdFlowNodePage(SystemType.PORTAL,"mhgl_xxfb_xxsh","门户管理-信息发布-信息审核","/webapp/portal/tdPortalNewsCheckList.xhtml",3));

	}
}
