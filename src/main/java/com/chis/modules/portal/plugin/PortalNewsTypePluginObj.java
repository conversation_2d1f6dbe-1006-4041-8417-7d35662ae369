package com.chis.modules.portal.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.portal.entity.TdPortalNewsType;

/**
 * <AUTHOR>
 * @createDate 2015-3-5.
 */
public class PortalNewsTypePluginObj {
    public static Set<TdPortalNewsType> newsTypeSet;

    static {
        newsTypeSet = new HashSet<TdPortalNewsType>();

        TdPortalNewsType tdPortalNewsType = new TdPortalNewsType();
        tdPortalNewsType.setTypeName("图片类型");
        tdPortalNewsType.setTypeCode("PICTUERTYPE");
        tdPortalNewsType.setTdPortalNewsType(null);
        tdPortalNewsType.setCreateDate(new Date());
        tdPortalNewsType.setCreateManid(1);
        tdPortalNewsType.setXh(1);
        tdPortalNewsType.setZdCounts(5);
        newsTypeSet.add(tdPortalNewsType);

    }
}
