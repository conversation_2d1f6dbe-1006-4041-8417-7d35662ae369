package com.chis.modules.portal.plugin;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.init.PluginInit;

@Component(value = "PluginObjInit_PORTAL_506")
@Transactional(readOnly = false)
public class PluginObjInit extends PluginInit {

	@Override
	public void run() {
		super.initDataStructureByXml();
		super.run();
		this.initPortalColumns();
		this.initFlowNodePage();
		this.initNewsType();
	}

	private void initPortalColumns() {
		Set<TdPortalColumn> set = PortalColumnPluginObj.columnSet;
		if (null != set && set.size() > 0) {
			for (TdPortalColumn t : set) {
				List<TdPortalColumn> list = em.createNamedQuery("TdPortalColumn.findByCode")
						.setParameter("colCode", t.getColCode()).getResultList();
				if (null == list || list.size() == 0) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	private void initNewsType() {
		Set<TdPortalNewsType> set = PortalNewsTypePluginObj.newsTypeSet;
		if (null != set && set.size() > 0) {
			for (TdPortalNewsType t : set) {
				List<TdPortalNewsType> list = super.em.createNamedQuery("TdPortalNewsType.findByTypeCode")
						.setParameter("typecode", t.getTypeCode()).getResultList();
				if (null == list || list.size() == 0) {
					t = (TdPortalNewsType) pluginUpdateService.saveObj(t);
					// 如果图片类型,更新图片栏目
					if (t.getTypeCode().equals("PICTUERTYPE")) {
						StringBuilder sb = new StringBuilder();
						sb.append("update TD_PORTAL_COLUMN a set a.NEWS_TYPE_ID = ").append(t.getRid());
						sb.append(" , a.MORE_URL = '/webapp/portal/tdPortalNewsMoreList.faces?newsType=")
								.append(t.getRid()).append("&pageTitle=图片新闻'");
						sb.append(" where a.COL_TYPE = 1");
						em.createNativeQuery(sb.toString()).executeUpdate();
					}
				}
			}
		}
	}

	private void initFlowNodePage() {
		Set<TdFlowNodePage> set = PortalFlowNodePagePluginObj.tdFlowNodePageSet;
		if (null != set && set.size() > 0) {
			for (TdFlowNodePage t : set) {
				List<TdFlowNodePage> list = super.em.createNamedQuery("TdFlowNodePage.findByPageCode")
						.setParameter("pagecode", t.getPageCode()).getResultList();
				if (null == list || list.size() == 0) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	@Override
	public List<String> buildDataStructurePlugin() {
		return null;
	}

	@Override
	public Set<TsMenu> buildMenuPlugin() {
		return PortalMenuPluginObj.menuSet;
	}

	@Override
	public Set<TsCodeRule> buildCodeRulePlugin() {
		return PortalCodeRulePluginObj.ruleSet;
	}

	@Override
	public Set<TsSystemParam> buildSystemParamPlugin() {
		return PortalSystemParamPluginObj.paramSet;
	}

	@Override
	public Set<TsSimpleCode> buildCodePlugin() {
		return PortalTsCodePluginObj.simpleCodeSet;
	}

	@Override
	public Set<TsCodeType> buildCodeTypePlugin() {
		return PortalTsCodePluginObj.codeTypeSet;
	}

	@Override
	public SystemType buildSystemType() {
		return SystemType.PORTAL;
	}
}
