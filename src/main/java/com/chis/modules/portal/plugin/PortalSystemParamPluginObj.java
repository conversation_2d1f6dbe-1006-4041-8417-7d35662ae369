package com.chis.modules.portal.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.DictType;
import com.chis.modules.system.enumn.FieldType;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;

/**
 * 门户系统参数的插件
 * <AUTHOR>
 * @createDate 2014-8-14
 */
public class PortalSystemParamPluginObj {

	public static Set<TsSystemParam> paramSet;
	
	static {
		paramSet = new HashSet<TsSystemParam>();
		
//        /**
//         * 创建2014-8-23 zww
//         */
//        paramSet.add(new TsSystemParam(SystemType.PORTAL, "PORTAL_NEWS_DAYS",
//                "7", "门户信息几天内显示'new'，单位：天", Short.valueOf("1"),"mhxxjtnxsnew", new Date(), 1,
//                FieldType.STRINGS, DictType.NO_NEED, null));
//        /**
//         * 创建2014-12-19 xt
//         */
//        paramSet.add(new TsSystemParam(SystemType.PORTAL, "PORTAL_SHOW_GGL_DAYS",
//        		"30", "门户信息公告栏上发布的信息显示几天，单位：天", Short.valueOf("1"),"mhxxshowggldays", new Date(), 1,
//        		FieldType.STRINGS, DictType.NO_NEED, null));
//        /***/
//        paramSet.add(new TsSystemParam(SystemType.PORTAL, "PORTAL_SHOW_All_USER", "0",
//                "门户管理中授权是否可以选中所有用户", Short.valueOf("1"),"mhglshowallusers", new Date(), 1, FieldType.SELECT_ONE_MENU,
//                DictType.SELF_DEFINE, "否@@0##是@@1"));
    }

}
