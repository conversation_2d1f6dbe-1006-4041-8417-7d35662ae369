package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * 门户布局实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_LAYOUT")
@SequenceGenerator(name = "TdPortalLayout_Seq", sequenceName = "TD_PORTAL_LAYOUT_SEQ", allocationSize = 1)
public class TdPortalLayout implements java.io.Serializable {
	
	private static final long serialVersionUID = -2952862775202343706L;
	private Integer rid;
	private TdProtal tdProtal;
	private TdPortalColumn tdPortalColumn;
	private Integer pos;
	private Integer nums;
	private Integer lines = 5;
	private Integer dispDate = 1;
	private Integer ifScroll = 0;
	private Integer heighth;
	private Integer words;
	
	// Constructors
	
	/** default constructor */
	public TdPortalLayout() {
	}
	
	/** minimal constructor */
	public TdPortalLayout(Integer rid, TdProtal tdProtal, TdPortalColumn tdPortalColumn, Integer dispDate) {
		this.rid = rid;
		this.tdProtal = tdProtal;
		this.tdPortalColumn = tdPortalColumn;
		this.dispDate = dispDate;
	}
	
	/** full constructor */
	public TdPortalLayout(Integer rid, TdProtal tdProtal, TdPortalColumn tdPortalColumn, Integer pos, Integer nums,
			Integer lines, Integer dispDate, Integer ifScroll) {
		this.rid = rid;
		this.tdProtal = tdProtal;
		this.tdPortalColumn = tdPortalColumn;
		this.pos = pos;
		this.nums = nums;
		this.lines = lines;
		this.dispDate = dispDate;
		this.ifScroll = ifScroll;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalLayout_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "PORTAL_ID" )
	public TdProtal getTdProtal() {
		return this.tdProtal;
	}
	
	public void setTdProtal(TdProtal tdProtal) {
		this.tdProtal = tdProtal;
	}
	
	@ManyToOne
	@JoinColumn(name = "COL_ID" )
	public TdPortalColumn getTdPortalColumn() {
		return this.tdPortalColumn;
	}
	
	public void setTdPortalColumn(TdPortalColumn tdPortalColumn) {
		this.tdPortalColumn = tdPortalColumn;
	}
	
	@Column(name = "POS", precision = 1, scale = 0)
	public Integer getPos() {
		return this.pos;
	}
	
	public void setPos(Integer pos) {
		this.pos = pos;
	}
	
	@Column(name = "NUMS", precision = 1, scale = 0)
	public Integer getNums() {
		return this.nums;
	}
	
	public void setNums(Integer nums) {
		this.nums = nums;
	}
	
	@Column(name = "LINES", precision = 2, scale = 0)
	public Integer getLines() {
		return this.lines;
	}
	
	public void setLines(Integer lines) {
		this.lines = lines;
	}
	
	@Column(name = "DISP_DATE" , precision = 1, scale = 0)
	public Integer getDispDate() {
		return this.dispDate;
	}
	
	public void setDispDate(Integer dispDate) {
		this.dispDate = dispDate;
	}
	
	@Column(name = "IF_SCROLL", precision = 1, scale = 0)
	public Integer getIfScroll() {
		return this.ifScroll;
	}
	
	public void setIfScroll(Integer ifScroll) {
		this.ifScroll = ifScroll;
	}

	@Column(name = "HEIGHTH")
	public Integer getHeighth() {
		return heighth;
	}

	public void setHeighth(Integer heighth) {
		this.heighth = heighth;
	}

	@Column(name = "WORDS")
	public Integer getWords() {
		return words;
	}

	public void setWords(Integer words) {
		this.words = words;
	}
	
	
	
}