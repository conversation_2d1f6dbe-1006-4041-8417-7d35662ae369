package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsOffice;

/**
 * 门户科室关系表实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_OFFICE", uniqueConstraints = @UniqueConstraint(columnNames = { "PORTAL_ID",
		"OFFICE_ID" }))
@SequenceGenerator(name = "TdPortalOffice_Seq", sequenceName = "TD_PORTAL_OFFICE_SEQ", allocationSize = 1)
public class TdPortalOffice implements java.io.Serializable {
	
	private static final long serialVersionUID = 589645205580109012L;
	private Integer rid;
	private TdProtal tdProtal;
	private TsOffice tsOffice;
	
	// Constructors
	
	/** default constructor */
	public TdPortalOffice() {
	}
	
	/** full constructor */
	public TdPortalOffice(Integer rid, TdProtal tdProtal, TsOffice tsOffice) {
		this.rid = rid;
		this.tdProtal = tdProtal;
		this.tsOffice = tsOffice;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalOffice_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "PORTAL_ID" )
	public TdProtal getTdProtal() {
		return this.tdProtal;
	}
	
	public void setTdProtal(TdProtal tdProtal) {
		this.tdProtal = tdProtal;
	}
	
	@ManyToOne
	@JoinColumn(name = "OFFICE_ID" )
	public TsOffice getTsOffice() {
		return this.tsOffice;
	}
	
	public void setTsOffice(TsOffice tsOffice) {
		this.tsOffice = tsOffice;
	}
	
}