package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * 栏目权限表实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_COLAUTH", uniqueConstraints = @UniqueConstraint(columnNames = { "COL_ID",
		"USER_ID" }))
@SequenceGenerator(name = "TdPortalColauth_Seq", sequenceName = "TD_PORTAL_COLAUTH_SEQ", allocationSize = 1)
public class TdPortalColauth implements java.io.Serializable {
	
	private static final long serialVersionUID = -1853410412890865021L;
	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TdPortalColumn tdPortalColumn;
	
	// Constructors
	
	/** default constructor */
	public TdPortalColauth() {
	}
	
	/** full constructor */
	public TdPortalColauth(Integer rid, TsUserInfo tsUserInfo, TdPortalColumn tdPortalColumn) {
		this.rid = rid;
		this.tsUserInfo = tsUserInfo;
		this.tdPortalColumn = tdPortalColumn;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalColauth_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "USER_ID" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}
	
	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}
	
	@ManyToOne
	@JoinColumn(name = "COL_ID" )
	public TdPortalColumn getTdPortalColumn() {
		return this.tdPortalColumn;
	}
	
	public void setTdPortalColumn(TdPortalColumn tdPortalColumn) {
		this.tdPortalColumn = tdPortalColumn;
	}
	
}