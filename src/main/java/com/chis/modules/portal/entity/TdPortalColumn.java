package com.chis.modules.portal.entity;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.chis.modules.portal.enumn.PortalColType;

/**
 * 栏目实体
 * 
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_COLUMN")
@SequenceGenerator(name = "TdPortalColumn_Seq", sequenceName = "TD_PORTAL_COLUMN_SEQ", allocationSize = 1)
@NamedQueries({ @NamedQuery(name = "TdPortalColumn.findByCode", query = "SELECT t FROM TdPortalColumn t WHERE t.colCode=:colCode") })
public class TdPortalColumn implements java.io.Serializable {

	private static final long serialVersionUID = -7927240584116944438L;
	private Integer rid;
	private TdPortalNewsType tdPortalNewsType;
	private String colName;
	private PortalColType colType = PortalColType.PT;
	private String moreUrl;
	private List<TdPortalLayout> tdPortalLayouts = new ArrayList<TdPortalLayout>(
			0);
	private List<TdPortalColauth> tdPortalColauths = new ArrayList<TdPortalColauth>(
			0);
	/** 栏目编码 */
	private String colCode;
	/** 是否隐藏标题栏 */
	private Integer ifHideHeader;
	/**是否需要websocket刷新*/
	private Integer needSocketUpdate;
	// Constructors

	/** default constructor */
	public TdPortalColumn() {
	}

	public TdPortalColumn(Integer rid) {
		this.rid = rid;
	}

	/**
	 * 不允许删
	 * 
	 * @param rid
	 *            主键
	 * @param colName
	 *            栏目名称
	 */
	public TdPortalColumn(Integer rid, String colName) {
		this.rid = rid;
		this.colName = colName;
	}

	/**
	 * 不允许删
	 * 
	 * @param rid
	 *            主键
	 * @param colName
	 *            栏目名称
	 */
	public TdPortalColumn(Integer rid, String colName, PortalColType colType) {
		this.rid = rid;
		this.colName = colName;
		this.colType = colType;
	}

	/**
	 * @param colName
	 *            栏目名称
	 * @param colType
	 *            栏目类型
	 * @param moreUrl
	 *            地址
	 * @param colCode
	 *            栏目编码
	 */
	public TdPortalColumn(String colName, PortalColType colType,
			String moreUrl, String colCode) {
		this.colName = colName;
		this.colType = colType;
		this.moreUrl = moreUrl;
		this.colCode = colCode;
	}

	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalColumn_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "NEWS_TYPE_ID")
	public TdPortalNewsType getTdPortalNewsType() {
		return this.tdPortalNewsType;
	}

	public void setTdPortalNewsType(TdPortalNewsType tdPortalNewsType) {
		this.tdPortalNewsType = tdPortalNewsType;
	}

	@Column(name = "COL_NAME" , length = 100)
	public String getColName() {
		return this.colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	@Enumerated
	@Column(name = "COL_TYPE" )
	public PortalColType getColType() {
		return this.colType;
	}

	public void setColType(PortalColType colType) {
		this.colType = colType;
	}

	@Column(name = "MORE_URL", length = 200)
	public String getMoreUrl() {
		return this.moreUrl;
	}

	public void setMoreUrl(String moreUrl) {
		this.moreUrl = moreUrl;
	}

	@Column(name = "COL_CODE")
	public String getColCode() {
		return colCode;
	}

	public void setColCode(String colCode) {
		this.colCode = colCode;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdPortalColumn")
	public List<TdPortalLayout> getTdPortalLayouts() {
		return this.tdPortalLayouts;
	}

	public void setTdPortalLayouts(List<TdPortalLayout> tdPortalLayouts) {
		this.tdPortalLayouts = tdPortalLayouts;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdPortalColumn")
	public List<TdPortalColauth> getTdPortalColauths() {
		return this.tdPortalColauths;
	}

	public void setTdPortalColauths(List<TdPortalColauth> tdPortalColauths) {
		this.tdPortalColauths = tdPortalColauths;
	}

	@Column(name = "IF_HIDE_HEADER")
	public Integer getIfHideHeader() {
		return ifHideHeader;
	}

	public void setIfHideHeader(Integer ifHideHeader) {
		this.ifHideHeader = ifHideHeader;
	}
	@Column(name = "NEED_SOCKET_UPDATE")
	public Integer getNeedSocketUpdate() {
		return needSocketUpdate;
	}

	public void setNeedSocketUpdate(Integer needSocketUpdate) {
		this.needSocketUpdate = needSocketUpdate;
	}

}