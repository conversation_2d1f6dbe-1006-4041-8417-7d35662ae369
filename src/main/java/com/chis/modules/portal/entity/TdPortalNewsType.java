package com.chis.modules.portal.entity;

import org.hibernate.annotations.Cascade;
import org.hibernate.validator.constraints.NotBlank;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 信息类型实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_NEWS_TYPE")
@SequenceGenerator(name = "TdPortalNewsType_Seq", sequenceName = "TD_PORTAL_NEWS_TYPE_SEQ", allocationSize = 1)
@NamedQueries({
        @NamedQuery(name = "TdPortalNewsType.findAll", query = "SELECT t FROM TdPortalNewsType t order by t.xh"),
        @NamedQuery(name = "TdPortalNewsType.findByTypeCode", query = "SELECT t FROM TdPortalNewsType t WHERE t.typeCode=:typecode")
})
public class TdPortalNewsType implements java.io.Serializable,Comparable<TdPortalNewsType> {

	private static final long serialVersionUID = 588863601933762772L;
	private Integer rid;
	private TdPortalNewsType tdPortalNewsType;
	private String typeName;
	private String typeDesc;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
    private Integer xh;
    private Integer zdCounts;//置顶条数
    private String typeCode;//类型编码
    private List<TdPortalColumn> tdPortalColumns = new ArrayList<TdPortalColumn>(0);
    private List<TdPortalNews> tdPortalNewses = new ArrayList<TdPortalNews>(0);
    private List<TdPortalNewsType> tdPortalNewsTypes = new ArrayList<TdPortalNewsType>(0);
    private List<TdPortalTypAuth> tdPortalTypAuths = new ArrayList<TdPortalTypAuth>(0);

	// Constructors
	/** default constructor */
	public TdPortalNewsType() {
	}

    public TdPortalNewsType(Integer rid) {
        this.rid = rid;
    }
	
	/** minimal constructor */
	public TdPortalNewsType(Integer rid, String typeName, Date createDate, Integer createManid) {
		this.rid = rid;
		this.typeName = typeName;
		this.createDate = createDate;
		this.createManid = createManid;
	}
	
	/** full constructor */
	public TdPortalNewsType(Integer rid, TdPortalNewsType tdPortalNewsType, String typeName, String typeDesc,
			Date createDate, Integer createManid, Date modifyDate, Integer modifyManid,
			List<TdPortalColumn> tdPortalColumns, List<TdPortalNews> tdPortalNewses,
			List<TdPortalNewsType> tdPortalNewsTypes) {
		this.rid = rid;
		this.tdPortalNewsType = tdPortalNewsType;
		this.typeName = typeName;
		this.typeDesc = typeDesc;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.tdPortalColumns = tdPortalColumns;
		this.tdPortalNewses = tdPortalNewses;
		this.tdPortalNewsTypes = tdPortalNewsTypes;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalNewsType_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "PARENT_ID")
	public TdPortalNewsType getTdPortalNewsType() {
		return this.tdPortalNewsType;
	}
	
	public void setTdPortalNewsType(TdPortalNewsType tdPortalNewsType) {
		this.tdPortalNewsType = tdPortalNewsType;
	}
	
	@Column(name = "TYPE_NAME" , unique = true , length = 100)
    @NotBlank(message = "类型名称不允许为空！")
	public String getTypeName() {
		return this.typeName;
	}
	
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	
	@Column(name = "TYPE_DESC", length = 200)
	public String getTypeDesc() {
		return this.typeDesc;
	}
	
	public void setTypeDesc(String typeDesc) {
		this.typeDesc = typeDesc;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}
	
	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}
	
	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}
	
	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}
	
	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

    @Column(name = "XH")
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    @Column(name = "ZD_CONTS")
    public Integer getZdCounts() {
        return zdCounts;
    }

    public void setZdCounts(Integer zdCounts) {
        this.zdCounts = zdCounts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY,mappedBy = "tdPortalNewsType")
    public List<TdPortalTypAuth> getTdPortalTypAuths() {
        return tdPortalTypAuths;
    }

    public void setTdPortalTypAuths(List<TdPortalTypAuth> tdPortalTypAuths) {
        this.tdPortalTypAuths = tdPortalTypAuths;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdPortalNewsType")
	public List<TdPortalColumn> getTdPortalColumns() {
		return this.tdPortalColumns;
	}
	
	public void setTdPortalColumns(List<TdPortalColumn> tdPortalColumns) {
		this.tdPortalColumns = tdPortalColumns;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdPortalNewsType")
	public List<TdPortalNews> getTdPortalNewses() {
		return this.tdPortalNewses;
	}
	
	public void setTdPortalNewses(List<TdPortalNews> tdPortalNewses) {
		this.tdPortalNewses = tdPortalNewses;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY,mappedBy = "tdPortalNewsType")
	public List<TdPortalNewsType> getTdPortalNewsTypes() {
		return this.tdPortalNewsTypes;
	}
	
	public void setTdPortalNewsTypes(List<TdPortalNewsType> tdPortalNewsTypes) {
		this.tdPortalNewsTypes = tdPortalNewsTypes;
	}

    @Override
    public int compareTo(TdPortalNewsType o) {
        if(null != o.getXh() && null != this.xh){
            return xh.compareTo(o.getXh());
        }else{
            return 0;
        }
    }

    @Column(name = "TYPE_CODE")
    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }
}