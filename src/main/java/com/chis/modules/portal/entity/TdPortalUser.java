package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * 门户用户关系表实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_USER", uniqueConstraints = @UniqueConstraint(columnNames = { "PORTAL_ID",
		"USER_ID" }))
@SequenceGenerator(name = "TdPortalUser_Seq", sequenceName = "TD_PORTAL_USER_SEQ", allocationSize = 1)
public class TdPortalUser implements java.io.Serializable {
	
	private static final long serialVersionUID = 1115657409845052836L;
	private Integer rid;
	private TdProtal tdProtal;
	private TsUserInfo tsUserInfo;
	
	// Constructors
	
	/** default constructor */
	public TdPortalUser() {
	}
	
	/** full constructor */
	public TdPortalUser(Integer rid, TdProtal tdProtal, TsUserInfo tsUserInfo) {
		this.rid = rid;
		this.tdProtal = tdProtal;
		this.tsUserInfo = tsUserInfo;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalUser_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "PORTAL_ID" )
	public TdProtal getTdProtal() {
		return this.tdProtal;
	}
	
	public void setTdProtal(TdProtal tdProtal) {
		this.tdProtal = tdProtal;
	}
	
	@ManyToOne
	@JoinColumn(name = "USER_ID" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}
	
	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}
	
}