package com.chis.modules.portal.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.portal.enumn.TemplType;

/**
 * 门户实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PROTAL")
@SequenceGenerator(name = "TdProtal_Seq", sequenceName = "TD_PROTAL_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdProtal.findPersonalPortal", query = "SELECT t FROM TdProtal t WHERE t.portalType=1 AND t.userId=:userId")
})
public class TdProtal implements java.io.Serializable {
	
	private static final long serialVersionUID = 3115933783142613403L;
	private Integer rid;
	private String portalName;
	/**0-非个人 1-个人*/
	private Integer portalType = 0;
	private TemplType templType;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Short xh;
	private List<TdPortalUser> tdPortalUsers = new ArrayList<TdPortalUser>(0);
	private List<TdPortalOffice> tdPortalOffices = new ArrayList<TdPortalOffice>(0);
	private List<TdPortalLayout> tdPortalLayouts = new ArrayList<TdPortalLayout>(0);
	private List<TdPortalGroup> tdPortalGroups = new ArrayList<TdPortalGroup>(0);
	
	private Integer userId;	//个人门户的用户ID
	
	// Constructors
	
	/** default constructor */
	public TdProtal() {
	}
	
	/** minimal constructor */
	public TdProtal(Integer rid) {
		this.rid = rid;
	}
	

	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdProtal_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "PORTAL_NAME" , length = 100)
	public String getPortalName() {
		return this.portalName;
	}
	
	public void setPortalName(String portalName) {
		this.portalName = portalName;
	}
	
	@Column(name = "PORTAL_TYPE" , precision = 1, scale = 0)
	public Integer getPortalType() {
		return this.portalType;
	}
	
	public void setPortalType(Integer portalType) {
		this.portalType = portalType;
	}
	
	@Enumerated
	@Column(name = "TEMPL_TYPE")
	public TemplType getTemplType() {
		return this.templType;
	}
	
	public void setTemplType(TemplType templType) {
		this.templType = templType;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}
	
	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}
	
	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}
	
	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}
	
	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	
	@Column(name = "XH")
	public Short getXh() {
		return xh;
	}

	public void setXh(Short xh) {
		this.xh = xh;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdProtal")
	public List<TdPortalUser> getTdPortalUsers() {
		return this.tdPortalUsers;
	}
	
	public void setTdPortalUsers(List<TdPortalUser> tdPortalUsers) {
		this.tdPortalUsers = tdPortalUsers;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdProtal")
	public List<TdPortalOffice> getTdPortalOffices() {
		return this.tdPortalOffices;
	}
	
	public void setTdPortalOffices(List<TdPortalOffice> tdPortalOffices) {
		this.tdPortalOffices = tdPortalOffices;
	}
	
	@OrderBy(value = "nums")
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdProtal")
	public List<TdPortalLayout> getTdPortalLayouts() {
		return this.tdPortalLayouts;
	}
	
	public void setTdPortalLayouts(List<TdPortalLayout> tdPortalLayouts) {
		this.tdPortalLayouts = tdPortalLayouts;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdProtal")
	public List<TdPortalGroup> getTdPortalGroups() {
		return this.tdPortalGroups;
	}
	
	public void setTdPortalGroups(List<TdPortalGroup> tdPortalGroups) {
		this.tdPortalGroups = tdPortalGroups;
	}
	
	@Column(name = "USER_ID")
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	
}