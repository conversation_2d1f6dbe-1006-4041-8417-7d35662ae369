package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsGroup;

/**
 * 门户组关系表实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_GROUP", uniqueConstraints = @UniqueConstraint(columnNames = { "PORTAL_ID",
		"GROUP_ID" }))
@SequenceGenerator(name = "TdPortalGroup_Seq", sequenceName = "TD_PORTAL_GROUP_SEQ", allocationSize = 1)
public class TdPortalGroup implements java.io.Serializable {
	
	private static final long serialVersionUID = 6551384740390410644L;
	private Integer rid;
	private TdProtal tdProtal;
	private TsGroup tsGroup;
	
	// Constructors
	
	/** default constructor */
	public TdPortalGroup() {
	}
	
	/** full constructor */
	public TdPortalGroup(Integer rid, TdProtal tdProtal, TsGroup tsGroup) {
		this.rid = rid;
		this.tdProtal = tdProtal;
		this.tsGroup = tsGroup;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalGroup_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "PORTAL_ID" )
	public TdProtal getTdProtal() {
		return this.tdProtal;
	}
	
	public void setTdProtal(TdProtal tdProtal) {
		this.tdProtal = tdProtal;
	}
	
	@ManyToOne
	@JoinColumn(name = "GROUP_ID" )
	public TsGroup getTsGroup() {
		return this.tsGroup;
	}

	public void setTsGroup(TsGroup tsGroup) {
		this.tsGroup = tsGroup;
	}
	
}