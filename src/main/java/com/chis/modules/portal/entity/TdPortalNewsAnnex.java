package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * 信息附件实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_NEWS_ANNEX")
@SequenceGenerator(name = "TdPortalNewsAnnex_Seq", sequenceName = "TD_PORTAL_NEWS_ANNEX_SEQ", allocationSize = 1)
public class TdPortalNewsAnnex implements java.io.Serializable {
	
	private static final long serialVersionUID = 6484886187934185296L;
	private Integer rid;
	private TdPortalNews tdPortalNews;
	private String annexName;
	private String annexAddr;
	private Integer xh;
    private String annexDesc;
	
	// Constructors
	
	/** default constructor */
	public TdPortalNewsAnnex() {
	}
	
	/** full constructor */
	public TdPortalNewsAnnex(Integer rid, TdPortalNews tdPortalNews, String annexName, String annexAddr, Integer xh) {
		this.rid = rid;
		this.tdPortalNews = tdPortalNews;
		this.annexName = annexName;
		this.annexAddr = annexAddr;
		this.xh = xh;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalNewsAnnex_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID" )
	public TdPortalNews getTdPortalNews() {
		return this.tdPortalNews;
	}
	
	public void setTdPortalNews(TdPortalNews tdPortalNews) {
		this.tdPortalNews = tdPortalNews;
	}
	
	@Column(name = "ANNEX_NAME" , length = 200)
	public String getAnnexName() {
		return this.annexName;
	}
	
	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}
	
	@Column(name = "ANNEX_ADDR" , length = 200)
	public String getAnnexAddr() {
		return this.annexAddr;
	}
	
	public void setAnnexAddr(String annexAddr) {
		this.annexAddr = annexAddr;
	}

	@Column(name = "XH")
	public Integer getXh() {
		return this.xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}

    @Column(name = "ANNEX_DESC" , length = 400)
    public String getAnnexDesc() {
        return annexDesc;
    }

    public void setAnnexDesc(String annexDesc) {
        this.annexDesc = annexDesc;
    }
}