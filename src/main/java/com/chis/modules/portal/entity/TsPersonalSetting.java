package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import com.chis.modules.portal.enumn.PersonSetType;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 个人设置表
 *
 * <AUTHOR>
 * @创建时间 2014-10-9
 */
@Entity
@Table(name = "TS_PERSONAL_SETTING", uniqueConstraints = @UniqueConstraint(columnNames = "USER_INFO_ID"))
@SequenceGenerator(name = "TsPersonalSetting_Seq", sequenceName = "TS_PERSONAL_SETTING_SEQ", allocationSize = 1)
public class TsPersonalSetting implements java.io.Serializable {

    // Fields

    private Integer rid;
    private TsUserInfo tsUserInfo;
    private String defaultUrl;
    private String defaultSkin;
    private PersonSetType defaultUrlType = PersonSetType.PORTAL;
    private String displayName;

    // Constructors

    /**
     * default constructor
     */
    public TsPersonalSetting() {
    }

    // Property accessors
    @Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsPersonalSetting_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "USER_INFO_ID", unique = true )
    public TsUserInfo getTsUserInfo() {
        return this.tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }

    @Column(name = "DEFAULT_URL", length = 100)
    public String getDefaultUrl() {
        return this.defaultUrl;
    }

    public void setDefaultUrl(String defaultUrl) {
        this.defaultUrl = defaultUrl;
    }

    @Column(name = "DEFAULT_SKIN", length = 50)
    public String getDefaultSkin() {
        return this.defaultSkin;
    }

    public void setDefaultSkin(String defaultSkin) {
        this.defaultSkin = defaultSkin;
    }

    @Enumerated
    @Column(name = "DEFAULT_URL_TYPE" , precision = 1, scale = 0)
    public PersonSetType getDefaultUrlType() {
        return this.defaultUrlType;
    }

    public void setDefaultUrlType(PersonSetType defaultUrlType) {
        this.defaultUrlType = defaultUrlType;
    }

    @Transient
    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}