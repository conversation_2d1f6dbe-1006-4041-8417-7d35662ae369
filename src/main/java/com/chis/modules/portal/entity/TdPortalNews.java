package com.chis.modules.portal.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;

import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 信息实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 * 
 * 
 * <p>修订内容：</p>
 * 添加发布单位关联
 * @ClassReviser xq,2018年1月16日,TdPortalNews
 * 
 * 
 * <p>修订内容：</p>
 * 新增发布单位字段
 * @ClassReviser xq,2018年1月17日,TdPortalNews
 */
@Entity
@Table(name = "TD_PORTAL_NEWS")
@SequenceGenerator(name = "TdPortalNews_Seq", sequenceName = "TD_PORTAL_NEWS_SEQ", allocationSize = 1)
public class TdPortalNews implements java.io.Serializable {
	
	private static final long serialVersionUID = -6770615181212789179L;
	private Integer rid;
	private TdPortalNewsType tdPortalNewsType;
	private TsOffice tsOffice;//科室
	private String newsTitle;//信息标题
	private Integer newsType;//信息类型
	private String newsAnnex;//信息附件
	private String newsCont;//信息内容
	private Integer newsLevel;//重要程度
	private Integer ifNotice;//是否添加到公告栏
	private Integer stateMark;//状态
	private Date newsDate;//发布时间
	private TsUserInfo tsUserInfo;// 发布人
	private List<TdPortalNewsAnnex> tdPortalNewsAnnexes = new ArrayList<TdPortalNewsAnnex>(0);
	private List<TdPortalNewsAuth> tdPortalNewsAuthes = new ArrayList<TdPortalNewsAuth>(0);
	
    private Integer ifZd;//是否置顶
    private String newAdr;//链接地址
    private short ifAll;
    
    /**用于图片类新闻，做为首页显示的那一条*/
    private TdPortalNewsAnnex tdPortalNewsAnnex;
    /**发布日期字符串*/
    private String newsDateStr;
    /**是否是新消息*/
    private boolean ifNew = Boolean.FALSE;
    /**滚动天数*/
    private Integer rollDays;
    
    /** +是否已经阅读过此消息 */
    private boolean ifHasRead;
	
    /** +信息与科室关系 */
    private List<TdPortalNewsOffice> tdPortalNewsOffices = new ArrayList<TdPortalNewsOffice>(0);
    /** +信息与单位关系 */
    private List<TdPortalNewsUnit> tdPortalNewsUnits = new ArrayList<TdPortalNewsUnit>(0);
    private TsUnit tsUnit;//单位
    
    /** default constructor */
	public TdPortalNews() {
	}
	
	public TdPortalNews(Integer rid) {
		this.rid = rid;
	}
	
	/** minimal constructor */
	public TdPortalNews(Integer rid, TdPortalNewsType tdPortalNewsType, TsOffice tsOffice, String newsTitle,
			Integer newsLevel, Integer ifNotice, TsUserInfo tsUserInfo) {
		this.rid = rid;
		this.tdPortalNewsType = tdPortalNewsType;
		this.tsOffice = tsOffice;
		this.newsTitle = newsTitle;
		this.newsLevel = newsLevel;
		this.ifNotice = ifNotice;
		this.tsUserInfo = tsUserInfo;
	}
	
	/** full constructor */
	public TdPortalNews(Integer rid, TdPortalNewsType tdPortalNewsType, TsOffice tsOffice, String newsTitle,
			Integer newsType, String newsAnnex, String newsCont, Integer newsLevel, Integer ifNotice,
			Integer stateMark, Date newsDate, TsUserInfo tsUserInfo, List<TdPortalNewsAnnex> tdPortalNewsAnnexes) {
		this.rid = rid;
		this.tdPortalNewsType = tdPortalNewsType;
		this.tsOffice = tsOffice;
		this.newsTitle = newsTitle;
		this.newsType = newsType;
		this.newsAnnex = newsAnnex;
		this.newsCont = newsCont;
		this.newsLevel = newsLevel;
		this.ifNotice = ifNotice;
		this.stateMark = stateMark;
		this.newsDate = newsDate;
		this.tsUserInfo = tsUserInfo;
		this.tdPortalNewsAnnexes = tdPortalNewsAnnexes;
	}
	
	public TdPortalNews(Integer rid, TdPortalNewsType tdPortalNewsType, TsOffice tsOffice, String newsTitle,
			Integer newsType, String newsAnnex, Integer newsLevel, Integer ifNotice, Integer stateMark, Date newsDate,
			TsUserInfo tsUserInfo, Integer ifZd, String newAdr,
			short ifAll) {
		this.rid = rid;
		this.tdPortalNewsType = tdPortalNewsType;
		this.tsOffice = tsOffice;
		this.newsTitle = newsTitle;
		this.newsType = newsType;
		this.newsAnnex = newsAnnex;
		this.newsLevel = newsLevel;
		this.ifNotice = ifNotice;
		this.stateMark = stateMark;
		this.newsDate = newsDate;
		this.tsUserInfo = tsUserInfo;
		this.ifZd = ifZd;
		this.newAdr = newAdr;
		this.ifAll = ifAll;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalNews_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "NEWS_TYPE_ID" )
	public TdPortalNewsType getTdPortalNewsType() {
		return this.tdPortalNewsType;
	}
	
	public void setTdPortalNewsType(TdPortalNewsType tdPortalNewsType) {
		this.tdPortalNewsType = tdPortalNewsType;
	}
	
	@ManyToOne
	@JoinColumn(name = "NEWS_DEPT" )
	public TsOffice getTsOffice() {
		return this.tsOffice;
	}
	
	public void setTsOffice(TsOffice tsOffice) {
		this.tsOffice = tsOffice;
	}

    @Column(name = "NEWS_TITLE" , length = 100)
    public String getNewsTitle() {
        return this.newsTitle;
    }

	public void setNewsTitle(String newsTitle) {
		this.newsTitle = newsTitle;
	}

	@Column(name = "NEWS_TYPE", precision = 1, scale = 0)
    @NotNull(message = "信息类型不能为空！")
	public Integer getNewsType() {
		return this.newsType;
	}
	
	public void setNewsType(Integer newsType) {
		this.newsType = newsType;
	}
	
	@Column(name = "NEWS_ANNEX", length = 200)
	public String getNewsAnnex() {
		return this.newsAnnex;
	}
	
	public void setNewsAnnex(String newsAnnex) {
		this.newsAnnex = newsAnnex;
	}
	
	@Column(name = "NEWS_CONT")
	public String getNewsCont() {
		return this.newsCont;
	}
	
	public void setNewsCont(String newsCont) {
		this.newsCont = newsCont;
	}
	
	@Column(name = "NEWS_LEVEL" , precision = 1, scale = 0)
	public Integer getNewsLevel() {
		return this.newsLevel;
	}
	
	public void setNewsLevel(Integer newsLevel) {
		this.newsLevel = newsLevel;
	}

    @NotNull(message = "添加到公告栏不能为空！")
	@Column(name = "IF_NOTICE" , precision = 1, scale = 0)
	public Integer getIfNotice() {
		return this.ifNotice;
	}
	
	public void setIfNotice(Integer ifNotice) {
		this.ifNotice = ifNotice;
	}
	
	@Column(name = "STATE_MARK", precision = 1, scale = 0)
	public Integer getStateMark() {
		return this.stateMark;
	}
	
	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "NEWS_DATE", length = 7)
	public Date getNewsDate() {
		return this.newsDate;
	}
	
	public void setNewsDate(Date newsDate) {
		this.newsDate = newsDate;
	}
	
	@ManyToOne
	@JoinColumn(name = "NEWS_MAN" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, mappedBy = "tdPortalNews",orphanRemoval = true)
	public List<TdPortalNewsAnnex> getTdPortalNewsAnnexes() {
		return this.tdPortalNewsAnnexes;
	}
	
	public void setTdPortalNewsAnnexes(List<TdPortalNewsAnnex> tdPortalNewsAnnexes) {
		this.tdPortalNewsAnnexes = tdPortalNewsAnnexes;
	}

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdPortalNews")
    public List<TdPortalNewsAuth> getTdPortalNewsAuthes() {
        return tdPortalNewsAuthes;
    }

    public void setTdPortalNewsAuthes(List<TdPortalNewsAuth> tdPortalNewsAuthes) {
        this.tdPortalNewsAuthes = tdPortalNewsAuthes;
    }

    @NotNull(message = "是否置顶不能为空！")
    @Column(name = "IF_ZD" , precision = 1, scale = 0)
    public Integer getIfZd() {
        return ifZd;
    }

    public void setIfZd(Integer ifZd) {
        this.ifZd = ifZd;
    }

    @Column(name = "NEW_ADR", length = 200)
    public String getNewAdr() {
        return newAdr;
    }

    public void setNewAdr(String newAdr) {
        this.newAdr = newAdr;
    }

    @Transient
	public TdPortalNewsAnnex getTdPortalNewsAnnex() {
		return tdPortalNewsAnnex;
	}

	public void setTdPortalNewsAnnex(TdPortalNewsAnnex tdPortalNewsAnnex) {
		this.tdPortalNewsAnnex = tdPortalNewsAnnex;
	}

	@Transient
	public String getNewsDateStr() {
		return newsDateStr;
	}

	public void setNewsDateStr(String newsDateStr) {
		this.newsDateStr = newsDateStr;
	}

	@Transient
	public boolean isIfNew() {
		return ifNew;
	}

	public void setIfNew(boolean ifNew) {
		this.ifNew = ifNew;
	}

    @Column(name = "IF_ALL" , precision = 1, scale = 0)
    public short getIfAll() {
        return ifAll;
    }

    public void setIfAll(short ifAll) {
        this.ifAll = ifAll;
    }

    @Column(name = "ROLL_DAYS")
	public Integer getRollDays() {
		return rollDays;
	}

	public void setRollDays(Integer rollDays) {
		this.rollDays = rollDays;
	}

	@Transient
	public boolean isIfHasRead() {
		return ifHasRead;
	}

	public void setIfHasRead(boolean ifHasRead) {
		this.ifHasRead = ifHasRead;
	}

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdPortalNews")
	public List<TdPortalNewsOffice> getTdPortalNewsOffices() {
		return tdPortalNewsOffices;
	}

	public void setTdPortalNewsOffices(List<TdPortalNewsOffice> tdPortalNewsOffices) {
		this.tdPortalNewsOffices = tdPortalNewsOffices;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByNewsId")
	public List<TdPortalNewsUnit> getTdPortalNewsUnits() {
		return tdPortalNewsUnits;
	}

	public void setTdPortalNewsUnits(List<TdPortalNewsUnit> tdPortalNewsUnits) {
		this.tdPortalNewsUnits = tdPortalNewsUnits;
	}
	
	@ManyToOne
	@JoinColumn(name = "NEWS_UNIT" )
	public TsUnit getTsUnit() {
		return tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}
	
	
}