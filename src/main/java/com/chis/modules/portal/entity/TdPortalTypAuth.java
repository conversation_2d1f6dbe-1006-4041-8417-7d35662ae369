package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * 信息类型权限实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-21
 */
@Entity
@Table(name = "TD_PORTAL_TYP_AUTH", uniqueConstraints = @UniqueConstraint(columnNames = { "NEWS_ID",
		"USER_ID" }))
@SequenceGenerator(name = "TdPortalTypAuth_Seq", sequenceName = "TD_PORTAL_TYP_AUTH_SEQ", allocationSize = 1)
public class TdPortalTypAuth implements java.io.Serializable {
	
	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TdPortalNewsType tdPortalNewsType;
	private Integer ifAdmin;
	
	// Constructors
	
	/** default constructor */
	public TdPortalTypAuth() {
	}

    public TdPortalTypAuth(TdPortalNewsType tdPortalNewsType, TsUserInfo tsUserInfo, Integer ifAdmin) {
        this.tdPortalNewsType = tdPortalNewsType;
        this.tsUserInfo = tsUserInfo;
        this.ifAdmin = ifAdmin;
    }

	/** full constructor */
	public TdPortalTypAuth(Integer rid, TsUserInfo tsUserInfo, TdPortalNewsType tdPortalNewsType, Integer ifAdmin) {
		this.rid = rid;
		this.tsUserInfo = tsUserInfo;
		this.tdPortalNewsType = tdPortalNewsType;
		this.ifAdmin = ifAdmin;
	}
	
	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalTypAuth_Seq")
    public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "USER_ID" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}
	
	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}
	
	@ManyToOne
	@JoinColumn(name = "NEWS_ID" )
	public TdPortalNewsType getTdPortalNewsType() {
		return this.tdPortalNewsType;
	}
	
	public void setTdPortalNewsType(TdPortalNewsType tdPortalNewsType) {
		this.tdPortalNewsType = tdPortalNewsType;
	}
	
	@Column(name = "IF_ADMIN" , precision = 1, scale = 0)
	public Integer getIfAdmin() {
		return this.ifAdmin;
	}
	
	public void setIfAdmin(Integer ifAdmin) {
		this.ifAdmin = ifAdmin;
	}
	
}