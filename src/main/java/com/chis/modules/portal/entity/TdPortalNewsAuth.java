package com.chis.modules.portal.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * 信息权限表实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_NEWS_AUTH", uniqueConstraints = @UniqueConstraint(columnNames = { "NEWS_ID",
		"USER_ID" }))
@SequenceGenerator(name = "TdPortalNewsAuth_Seq", sequenceName = "TD_PORTAL_NEWS_AUTH_SEQ", allocationSize = 1)
public class TdPortalNewsAuth implements java.io.Serializable {

	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TdPortalNews tdPortalNews;
	private Short readState;
	private Date readTime;
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalNewsAuth_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @ManyToOne
    @JoinColumn(name = "NEWS_ID" )
    public TdPortalNews getTdPortalNews() {
        return tdPortalNews;
    }

    public void setTdPortalNews(TdPortalNews tdPortalNews) {
        this.tdPortalNews = tdPortalNews;
    }

    @ManyToOne
    @JoinColumn(name = "USER_ID" )
    public TsUserInfo getTsUserInfo() {
        return tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }

    @Column(name="READ_STATE")
	public Short getReadState() {
		return readState;
	}

	public void setReadState(Short readState) {
		this.readState = readState;
	}

	@Column(name="READ_TIME")
	@Temporal(TemporalType.TIMESTAMP)
	public Date getReadTime() {
		return readTime;
	}

	public void setReadTime(Date readTime) {
		this.readTime = readTime;
	}
}