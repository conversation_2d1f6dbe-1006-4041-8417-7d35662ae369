package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.chis.modules.system.entity.TsOffice;

/**
 * 信息查看科室实体
 * 
 * <AUTHOR>
 * @创建时间 2015-4-15
 */
@Entity
@Table(name = "TD_PORTAL_NEWS_OFFICE")
@SequenceGenerator(name = "TdPortalNewsOffice_Seq", sequenceName = "TD_PORTAL_NEWS_OFFICE_SEQ", allocationSize = 1)
public class TdPortalNewsOffice implements java.io.Serializable {

	private static final long serialVersionUID = -5397665184189191414L;
	private Integer rid;
	private TdPortalNews tdPortalNews;
	private TsOffice tsOffice;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalNewsOffice_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "NEWS_ID" )
	public TdPortalNews getTdPortalNews() {
		return tdPortalNews;
	}

	public void setTdPortalNews(TdPortalNews tdPortalNews) {
		this.tdPortalNews = tdPortalNews;
	}

	@ManyToOne
	@JoinColumn(name = "OFFICE_ID" )
	public TsOffice getTsOffice() {
		return tsOffice;
	}

	public void setTsOffice(TsOffice tsOffice) {
		this.tsOffice = tsOffice;
	}

}