package com.chis.modules.portal.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.chis.modules.system.entity.TsUnit;

/**
 * 
 * <AUTHOR>
 * @createTime 2018-1-15
 */
@Entity
@Table(name = "TD_PORTAL_NEWS_UNIT")
@SequenceGenerator(name = "TdPortalNewsUnit", sequenceName = "TD_PORTAL_NEWS_UNIT_SEQ", allocationSize = 1)
public class TdPortalNewsUnit implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdPortalNews fkByNewsId;
	private TsUnit fkByUnitId;
	
	public TdPortalNewsUnit() {
	}

	public TdPortalNewsUnit(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalNewsUnit")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "NEWS_ID")			
	public TdPortalNews getFkByNewsId() {
		return fkByNewsId;
	}

	public void setFkByNewsId(TdPortalNews fkByNewsId) {
		this.fkByNewsId = fkByNewsId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
}