package com.chis.modules.portal.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 常用链接实体
 *
 * <AUTHOR>
 * @创建时间 2014-8-14
 */
@Entity
@Table(name = "TD_PORTAL_LINKS")
@SequenceGenerator(name = "TdPortalLinks_Seq", sequenceName = "TD_PORTAL_LINKS_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdPortalLinks.findAll", query = "SELECT t FROM TdPortalLinks t ORDER BY t.nums")
})
public class TdPortalLinks implements java.io.Serializable {

	private static final long serialVersionUID = -1487858827873178008L;
	private Integer rid;
	private Integer linkType;
	private String linkName;
	private String linkIcon;
	private String linkUrl;
	private Integer nums;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	/** default constructor */
	public TdPortalLinks() {
	}
	
	/** minimal constructor */
	public TdPortalLinks(Integer rid, Integer linkType, String linkName, String linkUrl, Integer nums,
			Date createDate, Integer createManid) {
		this.rid = rid;
		this.linkType = linkType;
		this.linkName = linkName;
		this.linkUrl = linkUrl;
		this.nums = nums;
		this.createDate = createDate;
		this.createManid = createManid;
	}
	
	/** full constructor */
	public TdPortalLinks(Integer rid, Integer linkType, String linkName, String linkIcon, String linkUrl, Integer nums,
			Date createDate, Integer createManid, Date modifyDate, Integer modifyManid) {
		this.rid = rid;
		this.linkType = linkType;
		this.linkName = linkName;
		this.linkIcon = linkIcon;
		this.linkUrl = linkUrl;
		this.nums = nums;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
	}
	
	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPortalLinks_Seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "LINK_TYPE" , precision = 1, scale = 0)
	public Integer getLinkType() {
		return this.linkType;
	}
	
	public void setLinkType(Integer linkType) {
		this.linkType = linkType;
	}
	
	@Column(name = "LINK_NAME" , length = 200)
	public String getLinkName() {
		return this.linkName;
	}
	
	public void setLinkName(String linkName) {
		this.linkName = linkName;
	}
	
	@Column(name = "LINK_ICON", length = 200)
	public String getLinkIcon() {
		return this.linkIcon;
	}
	
	public void setLinkIcon(String linkIcon) {
		this.linkIcon = linkIcon;
	}
	
	@Column(name = "LINK_URL" , length = 200)
	public String getLinkUrl() {
		return this.linkUrl;
	}
	
	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}
	
	@Column(name = "NUMS" , precision = 2, scale = 0)
	public Integer getNums() {
		return this.nums;
	}
	
	public void setNums(Integer nums) {
		this.nums = nums;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}
	
	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}
	
	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}
	
	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}
	
	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	
}