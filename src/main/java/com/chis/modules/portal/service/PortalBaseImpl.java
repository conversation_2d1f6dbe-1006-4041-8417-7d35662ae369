package com.chis.modules.portal.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.*;
import com.chis.modules.portal.enumn.NewsType;
import com.chis.modules.portal.enumn.PersonSetType;
import com.chis.modules.portal.enumn.PortalColType;
import com.chis.modules.portal.enumn.TemplType;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.service.CommServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.persistence.Query;
import java.util.*;

/**
 * 门户会话Bean
 * <AUTHOR>
 * @createDate 2014-08-14
 * @修改人 xt
 * @修改时间 2014-10-9 新增门户个人设置方法
 */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class PortalBaseImpl extends AbstractTemplate {

	@Autowired
    private CommServiceImpl commService;

    //============================信息类型维护===========================
    /**
     * 查询所有的信息类型，按序号排序
     * @return
     *      返回查询到的类型集合
     * <AUTHOR>
     */
    
    public List<TdPortalNewsType> findPortalNewsTypes() {
        String hql = "select t from TdPortalNewsType t order by t.xh";
        List<TdPortalNewsType> list = em.createQuery(hql).getResultList();
        if(null != list){
            for(TdPortalNewsType t : list){
                t.getTdPortalNewsTypes().size();
                t.getTdPortalColumns().size();
            }
        }
        return list;
    }

    /**
     * 保存或更新信息类型
     * @param tdPortalNewsType
     *          保存对象
     * @return
     *      操作结果返回值，类型名称重复，则保存失败，返回提示信息
     *      保存成功，则返回null
     * <AUTHOR>
     */
    
    public String saveOrUpdateNewsType(TdPortalNewsType tdPortalNewsType) {
        String msg;
        StringBuilder sb = null;
        if(selectedPortalNewsTypeByName(tdPortalNewsType.getRid(), tdPortalNewsType.getTypeName())){
            msg = "类型名称不允许重复！";
            return msg;
        }else{
            if(tdPortalNewsType.getRid() != null) {
                this.update(tdPortalNewsType);
                Integer typeId = tdPortalNewsType.getRid();
                String typeName = tdPortalNewsType.getTypeName();
                sb = new StringBuilder();
                sb.append("UPDATE TD_PORTAL_COLUMN T SET T.MORE_URL = '");
                sb.append("/webapp/portal/tdPortalNewsMoreList.faces?newsType=").append(typeId.toString());
                sb.append("&pageTitle=").append(typeName);
                sb.append("' WHERE T.NEWS_TYPE_ID = '").append(typeId).append("'");
                em.createNativeQuery(sb.toString()).executeUpdate();
            }else {
                this.save(tdPortalNewsType);
                //在栏目表中写入该类型的栏目的更多信息内容，不允许修改
                String typeName = tdPortalNewsType.getTypeName();
                sb = new StringBuilder("SELECT T.RID FROM TD_PORTAL_NEWS_TYPE T WHERE T.TYPE_NAME = '");
                sb.append(typeName).append("'");
                List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
                String typeId = list.get(0).toString();
                sb = new StringBuilder("");
                sb.append("UPDATE TD_PORTAL_COLUMN T SET T.MORE_URL = '");
                sb.append("/webapp/portal/tdPortalNewsMoreList.faces?newsType=").append(typeId);
                sb.append("&pageTitle=").append(typeName);
                sb.append("' WHERE T.NEWS_TYPE_ID = '").append(typeId).append("'");
                em.createNativeQuery(sb.toString()).executeUpdate();
            }
            return null;
        }
    }

    public boolean selectedPortalNewsTypeByName(Integer rid,String name) {
        StringBuilder sb = new StringBuilder("select t from TdPortalNewsType t where t.typeName = '");
        sb.append(name).append("'");
        if(rid != null){
            sb.append(" and t.rid <> ").append(rid);
        }
        List list = this.em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 删除指定RID的信息类型记录
     * @param rid
     *          该记录的RID
     * @return
     *      删除失败，则返回提示信息，删除成功，返回null
     * <AUTHOR>
     */
    
    public String deleteNewsType(Integer rid) {
        String msg = null;
        StringBuilder sb = null;
        TdPortalNewsType tdPortalNewsType = (TdPortalNewsType) this.find(TdPortalNewsType.class,rid);
        if(null != tdPortalNewsType) {
            sb = new StringBuilder();
            sb.append("SELECT T.RID FROM TD_PORTAL_COLUMN T WHERE T.NEWS_TYPE_ID IS NOT NULL");
            sb.append(" AND T.COL_NAME = '").append(tdPortalNewsType.getTypeName()).append("'");
            List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
            if(null != list && list.size() > 0){
                for(Object o : list){
                    msg = deleteColumn(Integer.valueOf(o.toString()));
                }
            }
            if(null == msg){
                try{
                    sb = new StringBuilder("DELETE TD_PORTAL_NEWS_TYPE WHERE RID = ");
                    sb.append(rid);
                    em.createNativeQuery(sb.toString()).executeUpdate();
                }catch(Exception e){
                    msg = "该类型已被引用，不允许删除！";
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            }else{
                msg = "该类型的栏目已被引用，不允许删除！";
            }
        }else {
            msg = "该类型已经被删除";
        }
        return msg;
    }

    /**
     * 根据信息类型id找到该类型已授权的用户
     * @param rid 信息类型rid
     * @param ifAdmin 是否管理员
     * @return 所有用户的rid的字符串，以逗号隔开
     * <AUTHOR>
     */
    
    public String findPortalNewsTypeSqIds(Integer rid,boolean ifAdmin){
        StringBuilder sb = new StringBuilder(" SELECT LISTAGG(T.USER_ID, ',') WITHIN GROUP(ORDER BY T.RID) FROM TD_PORTAL_TYP_AUTH T WHERE T.NEWS_ID = '");
        sb.append(rid).append("'");
        if(ifAdmin){
            sb.append(" and t.IF_ADMIN = 1");
        }
        List<String> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 给用户授权
     * @param authList 用户集合
     * @param rid 类型rid
     * @return 授权成功返回null，失败返回“授权失败”信息
     * <AUTHOR>
     */
    
    public String sqNewsType(List<TdPortalTypAuth> authList,Integer rid){
        //获取管理员记录
        StringBuilder sb = new StringBuilder("SELECT T.USER_ID FROM TD_PORTAL_TYP_AUTH T WHERE T.NEWS_ID = ");
        sb.append(rid).append(" and t.if_admin = 1");
        List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
        //删除全部普通用户
        deletePortalNewsTypeAuth(rid);
        //添加新用户
        for (TdPortalTypAuth t : authList) {
            boolean b = false;
            for (Object obj : list) {
                if (obj.toString().equals(t.getTsUserInfo().getRid().toString())) {
                    b = true;break;
                }
            }
            if (!b) {
                this.save(t);
            }
        }
        //删除旧管理员用户
        for (Object obj : list) {
            boolean b = false;
            for (TdPortalTypAuth t : authList) {
                if(obj.toString().equals(t.getTsUserInfo().getRid().toString())){
                    b = true;break;
                }
            }
            if(!b){
                sb = new StringBuilder("DELETE TD_PORTAL_TYP_AUTH WHERE USER_ID = ");
                sb.append(Integer.valueOf(obj.toString()));
                sb.append(" and NEWS_ID = ").append(rid);
                em.createNativeQuery(sb.toString()).executeUpdate();
            }
        }
        return null;
    }

    //删除权限表记录
    public String deletePortalNewsTypeAuth(Integer rid){
        String msg = null;
        try{
            StringBuilder sb = new StringBuilder("DELETE TD_PORTAL_TYP_AUTH t WHERE t.NEWS_ID = ");
            sb.append(rid);
            sb.append(" and t.IF_ADMIN = 0");
            em.createNativeQuery(sb.toString()).executeUpdate();
        }catch(Exception e){
            msg = "删除失败！";
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return msg;
    }

    /**
     * 给管理员授权
     * @param list 管理员集合
     * @param rid 类型rid
     * @return 授权结果
     * <AUTHOR>
     */
    
    public String adminSqNewsType(List<TdPortalTypAuth> list,Integer rid){
        //设置类型权限表中所有类型id为rid的记录的管理员字段为0
        StringBuilder sb = new StringBuilder("");
        sb.append("UPDATE TD_PORTAL_TYP_AUTH T SET T.IF_ADMIN = 0 where t.news_id = ").append(rid);
        em.createNativeQuery(sb.toString()).executeUpdate();
        //找到权限表中类型id为rid的所有记录
        sb = new StringBuilder("");
        sb.append("select t from TdPortalTypAuth t where t.tdPortalNewsType.rid = ").append(rid);
        List<TdPortalTypAuth> typAuthList = em.createQuery(sb.toString()).getResultList();
        if(null != typAuthList && typAuthList.size() > 0){
            //遍历所有记录，如果该记录的用户在list中，在删除此记录
            for(TdPortalTypAuth t : typAuthList){
                boolean b = false;
                for(TdPortalTypAuth s : list){
                    if(s.getTsUserInfo().getRid().equals(t.getTsUserInfo().getRid())){
                        b = true;
                        break;
                    }
                }
                if(b){
                    sb = new StringBuilder("DELETE TD_PORTAL_TYP_AUTH WHERE USER_ID = ");
                    sb.append(t.getTsUserInfo().getRid());
                    sb.append(" and NEWS_ID = ").append(rid);
                    em.createNativeQuery(sb.toString()).executeUpdate();
                }
            }
        }
        //list数据持久化数据库中
        for(TdPortalTypAuth t : list){
            this.save(t);
        }
        return null;
    }
    //=================================================================================

    //=====================================栏目管理====================================
    /**
     * 更新栏目内容
     * @param tdPortalColumn
     *                  栏目实例
     * <AUTHOR>
     */
    
    public void updateColumn(TdPortalColumn tdPortalColumn){
        if(null == tdPortalColumn.getRid()){
            this.save(tdPortalColumn);
        }else{
            this.update(tdPortalColumn);
        }
    }

    /**
     * 删除栏目
     * @param rid
     *          栏目RID
     * @return
     *      删除成功返回null,删除失败返回错误信息
     * <AUTHOR>
     */
    
    public String deleteColumn(Integer rid){
        String msg = null;
        StringBuilder sb = null;
        TdPortalColumn tdPortalColumn = (TdPortalColumn) this.find(TdPortalColumn.class,rid);
        if(null != tdPortalColumn) {
            try{
                sb = new StringBuilder("DELETE TD_PORTAL_COLUMN WHERE RID = ");
                sb.append(rid);
                em.createNativeQuery(sb.toString()).executeUpdate();
            }catch(Exception e){
                msg = "该栏目已被引用，不允许删除！";
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }else{
            msg = "该栏目已被删除！";
        }
        return msg;
    }

    /**
     * 得到该栏目所有已分配用户
     * @param rid
     *          栏目RID
     * @return
     *          该栏目已分配用户集合
     * <AUTHOR>
     */
    
    public List<TsUserInfo> findCloumnUserList(Integer rid , Boolean tag){
        List<TsUserInfo> targetList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder("SELECT B.RID,B.USERNAME, B.OFFICENAMES");
		sb.append(" FROM (SELECT A.RID,A.USERNAME, LISTAGG(A.OFFICENAME, ',') WITHIN GROUP(ORDER BY DECODE(A.ZKS, 1, -1, A.NUM)) AS OFFICENAMES,");
		sb.append(" ',' || LISTAGG(A.OFFICEID, ',') WITHIN GROUP(ORDER BY A.OFFICEID) || ',' AS OFFICEIDS");
		sb.append(" ,SUM(DECODE(A.ZKS, 1, A.NUM, NULL)) AS ZKSNUM,A.EMPNUM");
		sb.append(" FROM (SELECT T1.RID,T1.USERNAME,C.OFFICENAME,C.OFFICEID,C.EMPNUM,C.ZKS,C.NUM FROM TS_USER_INFO T1 ");
		// 2015-4-3 xt 新增过滤兼职科室
		sb.append(tag?" LEFT":" INNER").append(" JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID ,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append("  SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID  ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		sb.append(" WHERE T1.IF_REVEAL = 1 AND T1.RID IN ");
		sb.append("(SELECT T.USER_ID FROM TD_PORTAL_COLAUTH T WHERE T.COL_ID = ");
		sb.append(rid);
		sb.append(")) A GROUP BY A.RID,A.USERNAME ,A.EMPNUM ) B ");
		sb.append(" ORDER BY B.ZKSNUM,B.EMPNUM");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(list != null && list.size() > 0){
            for(Object[] obj : list){
                TsUserInfo tsUserInfo = new TsUserInfo(Integer.valueOf(obj[0].toString()));
                tsUserInfo.setUsername(obj[1] == null ? "" : obj[1].toString());
                tsUserInfo.setOfficeName(obj[2] == null ? "" : obj[2].toString());
                targetList.add(tsUserInfo);
            }
        }
        return targetList;
    }

    /**
     * 得到本单位本科室的所有用户
     * @param unitId
     *          单位ID
     * @param officeId
     *          科室ID
     * @return
     *       用户集合
     * <AUTHOR>
     */
    
    public List<TsUserInfo> findAllUsers(Integer unitId,Integer officeId){
        List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
        StringBuilder sb = new StringBuilder("");
        sb.append("SELECT * FROM (SELECT T1.RID,T1.USERNAME");
        sb.append(" ,LISTAGG(C.OFFICENAME, ',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES,");
        sb.append(" ',' || LISTAGG(C.OFFICEID, ',') WITHIN GROUP(ORDER BY C.OFFICEID) || ',' AS OFFICEIDS");
        sb.append(" ,SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) AS ZKSNUM,C.EMPNUM FROM TS_USER_INFO T1 ");

		sb.append(" INNER JOIN (SELECT A.RID, B.OFFICENAME, B.RID AS OFFICEID ,B.NUM, A.NUM EMPNUM, 1 AS ZKS");
		sb.append(" FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append(" SELECT * FROM ( SELECT A.RID, C.OFFICENAME, C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS");
		sb.append(" FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM) ) C");
		sb.append(" ON C.RID = T1.EMP_ID");
        
        sb.append(" WHERE T1.IF_REVEAL = 1 AND T1.UNIT_RID = ");
        sb.append(unitId);
        sb.append(" GROUP BY T1.RID, T1.USERNAME,C.EMPNUM");
        sb.append(" ) D  ");
        if(officeId != null){
        	sb.append(" WHERE D.OFFICEIDS LIKE '%,").append(officeId).append(",%'");
        }
        sb.append(" ORDER BY D.ZKSNUM,D.EMPNUM");
        List<Object[]> resultList = em.createNativeQuery(sb.toString()).getResultList();
        if(resultList != null && resultList.size() > 0){
            for(Object[] obj : resultList){
                TsUserInfo tsUserInfo = new TsUserInfo(Integer.valueOf(obj[0].toString()));
                tsUserInfo.setUsername(obj[1] == null ? "" : obj[1].toString());
                tsUserInfo.setOfficeName(obj[2] == null ? "" : obj[2].toString());
                userList.add(tsUserInfo);
            }
        }
        return userList;
    }

    /**
     * 得到本单位科室列表
     * @param unitId
     *          单位ID
     * @return
     *      科室列表
     * <AUTHOR>
     */
    
    public List<TsOffice> findOfficeList(Integer unitId) {
        List<TsOffice> rtnList = new ArrayList<TsOffice>(0);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT A.OFFICENAME, A.RID, A.UP_ID  ");
        sb.append(" FROM TS_OFFICE A  ");
        sb.append(" WHERE A.IF_REVEAL = '1' ");
        if(null != unitId) {
            sb.append(" AND A.UNIT_RID = '").append(unitId).append("' ");
        }
        sb.append(" START WITH A.UP_ID IS NULL ");
        sb.append(" CONNECT BY PRIOR A.RID = A.UP_ID ");
        sb.append(" ORDER SIBLINGS BY A.RID ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            for(Object[] o: list) {
                TsOffice t = new TsOffice();
                t.setOfficename(String.valueOf(o[0]));
                t.setRid(Integer.valueOf(o[1].toString()));
                if(null != o[2]) {
                    t.setParentOffice(new TsOffice(Integer.valueOf(o[2].toString())));
                }
                rtnList.add(t);
            }
        }
        return rtnList;
    }

    /**
     * 更新栏目权限表
     * @param tdPortalColauthList
     *              更新的栏目信息集合
     * @param columnId
     *              该栏目的ID
     * @return
     *      保存结果，成功返回null，失败返回提示信息
     * <AUTHOR>
     */
    
    public String updateTdPortalColauth(List<TdPortalColauth> tdPortalColauthList,Integer columnId){
        String msg = deleteTdPortalColauth(columnId);
        if(msg == null){
            for(TdPortalColauth t : tdPortalColauthList){
                this.save(t);
            }
            return null;
        }else{
            return msg;
        }
    }

    public String deleteTdPortalColauth(Integer columnId){
        String msg = null;
        try{
            StringBuilder sb = new StringBuilder("DELETE TD_PORTAL_COLAUTH WHERE COL_ID = ");
            sb.append(columnId);
            em.createNativeQuery(sb.toString()).executeUpdate();
        }catch(Exception e){
            msg = "删除失败！";
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return msg;
    }
    //=============================================================================

    //=================================常用链接管理================================
    /**
     * 保存或更新链接
     * @param tdPortalLinks
     *              链接实例
     * @return
     *      如果保存更新成功，返回null，链接名称重复，保存更新失败，返回提示信息
     * <AUTHOR>
     */
    
    public String saveOrUpdatePortalLinks(TdPortalLinks tdPortalLinks){
        String msg;
        if(selectedPortalLinksByName(tdPortalLinks.getRid(), tdPortalLinks.getLinkName())){
            msg = "链接名称不允许重复！";
            return msg;
        }else{
            if(tdPortalLinks.getRid() != null) {
                this.update(tdPortalLinks);
            }else {
                this.save(tdPortalLinks);
            }
            return null;
        }
    }

    public boolean selectedPortalLinksByName(Integer rid,String name) {
        StringBuilder sb = new StringBuilder("select t from TdPortalLinks t where t.linkName = '");
        sb.append(name).append("'");
        if(rid != null){
            sb.append(" and t.rid <> ").append(rid);
        }
        List list = this.em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0){
            return true;
        }else{
            return false;
        }
    }

    /***
     * 删除链接
     * @param rid
     *          链接rid
     * @return
     *      删除结果，如果被引用则无法删除，删除成功，返回null
     * <AUTHOR>
     */
    
    public String deletePortalLinks(Integer rid){
        String msg = null;
        TdPortalLinks tdPortalLinks = (TdPortalLinks) this.find(TdPortalLinks.class,rid);
        if(null != tdPortalLinks) {
            try{
                StringBuilder sb = new StringBuilder("DELETE TD_PORTAL_LINKS WHERE RID = ");
                sb.append(rid);
                em.createNativeQuery(sb.toString()).executeUpdate();
            }catch(Exception e){
                msg = "该链接已被引用，不允许删除！";
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }else {
            msg = "该链接已经被删除";
        }
        return msg;
    }
    //==================================================================================


    /************************信息发布***************************/
    /**
     * 判断该用户是否拥有该信息类型的管理员权限
     * @param newTypeId 信息类型
     * @param userId 用户类型
     * @return
     * <AUTHOR>
     * @createDate 2014-8-15
     */
    public boolean ifHasAdminInNews(Integer newTypeId,Integer userId){
    	if( null != newTypeId && null != userId ){
    		StringBuilder sql = new  StringBuilder();
    		sql.append("SELECT T.RID FROM TD_PORTAL_TYP_AUTH T WHERE T.NEWS_ID = ?1");
    		sql.append(" AND T.USER_ID = ?2 AND T.IF_ADMIN = 1");
    		Query query = this.em.createNativeQuery(sql.toString());
    		query.setParameter(1, newTypeId);
    		query.setParameter(2, userId);
    		List resultList = query.getResultList();
    		if( null != resultList && resultList.size() > 0)	{
    			return true;
    		}
    	}
    	return false;
    }
    
    /**
     * 根据传入的信息ID获取信息实体
     * @param rid 信息Id
     * @param userId 用户Id
     * @return
     * <AUTHOR>
     * @createDate 2014-8-15
     */
    public TdPortalNews findTdPortalNews(Integer rid,Integer userId)  {
        if( null != rid)    {
            TdPortalNews tdPortalNews = (TdPortalNews)this.find(TdPortalNews.class,rid);
            if( null != tdPortalNews.getTdPortalNewsAnnexes()){
                tdPortalNews.getTdPortalNewsAnnexes().size();
            }
            if( null != tdPortalNews.getTdPortalNewsOffices()){
            	tdPortalNews.getTdPortalNewsOffices().size();
            }
            if( null != tdPortalNews.getTdPortalNewsUnits()){
            	tdPortalNews.getTdPortalNewsUnits().size();
            }
            if( null != tdPortalNews.getTdPortalNewsAuthes()){
            	tdPortalNews.getTdPortalNewsAuthes().size();
            	List<TdPortalNewsAuth> tdPortalNewsAuthes = tdPortalNews.getTdPortalNewsAuthes();
				Collections.sort(tdPortalNewsAuthes, new Comparator<TdPortalNewsAuth>() {
					
					public int compare(TdPortalNewsAuth o1, TdPortalNewsAuth o2) {
						String data1 = o1.getTsUserInfo().getUsername();
						String data2 = o2.getTsUserInfo().getUsername();
						int i = data2.compareTo(data1);
						return i;
					}
				});
            }
            
            // 如传入人员Id,则判断该人员是否查看过信息发布的信息，如看过，则更新状态
			if (null != userId && tdPortalNews.getStateMark().intValue() == 1) {
				StringBuilder IfSeeSql = new StringBuilder();
				IfSeeSql.append("SELECT  T.RID FROM TD_PORTAL_NEWS_AUTH T WHERE T.NEWS_ID = ?1 AND T.USER_ID = ?2 ");
				Query query = this.em.createNativeQuery(IfSeeSql.toString());
				query.setParameter(1, rid);
				query.setParameter(2, userId);
				List resultList = query.getResultList();
				if (null == resultList || resultList.size() == 0) {
					TdPortalNewsAuth tdPortalNewsAuth = new TdPortalNewsAuth();
					tdPortalNewsAuth.setReadState((short)1);
					tdPortalNewsAuth.setReadTime(new Date());
					tdPortalNewsAuth.setTdPortalNews(new TdPortalNews(rid));
					tdPortalNewsAuth.setTsUserInfo(new TsUserInfo(userId));
					this.save(tdPortalNewsAuth);
				}
          }
            return tdPortalNews;
        }
        return null;
    }

    /**
     * 查询信息的所有分类
     * @param  userId 用户ID
     * @return
     * <AUTHOR>
     * @createDate 2014-8-15
     */
    public List<TdPortalNewsType> findNewsType(Integer userId)    {
        StringBuffer tSearchHql = new StringBuffer();

        if(null != userId)    {
            tSearchHql.append("select distinct t1 from TdPortalTypAuth t inner join t.tdPortalNewsType t1");
            tSearchHql.append(" where t.tsUserInfo.rid = ").append(userId);
        }else{
            tSearchHql.append("select t1 from TdPortalNewsType t1 ");
        }
        tSearchHql.append(" order by t1.xh");
        List<TdPortalNewsType> resultList = this.em.createQuery(tSearchHql.toString()).getResultList();
        return resultList;
    }

    
    public String findNewsUserIds(Integer newsId) {
        StringBuilder sb = new StringBuilder(" SELECT LISTAGG(T.USER_ID, ',') WITHIN GROUP(ORDER BY T.RID) AS RIDS,LISTAGG(T1.USERNAME, ',') WITHIN GROUP(ORDER BY T.RID) AS NAMES ");
        sb.append(" FROM TD_PORTAL_NEWS_AUTH T INNER JOIN TS_USER_INFO T1 ON T.USER_ID=T1.RID WHERE T.NEWS_ID = '");
        sb.append(newsId).append("' ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            Object[] objArr = list.get(0);
            String rids = objArr[0]==null?"":objArr[0].toString();
            String names = objArr[1]==null?"":objArr[1].toString();
            if(StringUtils.isNotBlank(rids)){
                return rids+"#@#"+names;
            }
        }
        return null;
    }

    /**
     * 保存信息实体
     * @return
     * <AUTHOR>
     * @createDate 2014-8-16
     */
    public TdPortalNews saveNews(TdPortalNews tdPortalNews,boolean ifCheck,Integer counts)  {
        if( null != tdPortalNews.getRid())    {//修改
            StringBuffer tdelSql = new StringBuffer();
//            tdelSql.append("DELETE FROM TD_PORTAL_NEWS_ANNEX T WHERE T.MAIN_ID = '").append(tdPortalNews.getRid()).append("'");
            tdelSql.append("BEGIN ");
            tdelSql.append("DELETE FROM TD_PORTAL_NEWS_ANNEX T WHERE T.MAIN_ID = '").append(tdPortalNews.getRid()).append("';");
            tdelSql.append("DELETE FROM TD_PORTAL_NEWS_OFFICE T WHERE T.NEWS_ID = '").append(tdPortalNews.getRid()).append("';");
            tdelSql.append("DELETE FROM TD_PORTAL_NEWS_UNIT T WHERE T.NEWS_ID = '").append(tdPortalNews.getRid()).append("';");
            tdelSql.append(" END;");
            em.createNativeQuery(tdelSql.toString()).executeUpdate();
        }else{
            tdPortalNews.setNewAdr("1");
            tdPortalNews = (TdPortalNews)this.saveObj(tdPortalNews);
        }
        //单个标题连接
        String url = "/webapp/portal/tdPortalNewsViewList.faces?newsType="+tdPortalNews.getNewsType();
        url+= "&rid="+tdPortalNews.getRid();
        tdPortalNews.setNewAdr(url);
        tdPortalNews = (TdPortalNews)this.updateObj(tdPortalNews);
        if(ifCheck && null != counts && counts > 0 && tdPortalNews.getIfZd() == 1)    {
            StringBuffer upHql = new StringBuffer();
            upHql.append("select t from TdPortalNews t where t.tdPortalNewsType.rid=").append(tdPortalNews.getNewsType());
            upHql.append(" order by t.newsDate ");
            List<TdPortalNews> resultList = em.createQuery(upHql.toString()).getResultList();
            //如果不存在置顶或者置顶条数小于置顶数，则不需要操作
            //只有置顶数不够，当前置顶，则更新置顶数
            if( null != resultList && resultList.size() > 0 &&  resultList.size() > counts )    {
                //需要更新的ids集合
                StringBuffer upIds = new StringBuffer();
                for( int i = counts ; i < resultList.size(); i ++)   {
                    upIds.append(",").append(resultList.get(i).getRid());
                }
                if(upIds.length() > 1)    {
                    //置顶多余的信息
                    StringBuffer tupdateSql = new StringBuffer();
                    tupdateSql.append("UPDATE TD_PORTAL_NEWS T SET T.IF_ZD IN(").append(upIds.deleteCharAt(0)).append(")");
                    this.em.createNativeQuery(tupdateSql.toString()).executeUpdate();
                }
            }
        }
        return tdPortalNews;
    }

    /**
     * 获取信息类型的末级节点
     * @param userId
     * @return
     * @his
     */
    public List<Object[]> fetchColNews(Integer userId)   {
        StringBuffer tSearchSql = new StringBuffer();
        tSearchSql.append("SELECT DISTINCT T.RID,T.TYPE_NAME,T.XH FROM TD_PORTAL_NEWS_TYPE T");
        if( null != userId)    {
            tSearchSql.append(" INNER JOIN TD_PORTAL_TYP_AUTH T1 ON T.RID = T1.NEWS_ID");
        }
        tSearchSql.append(" WHERE NOT EXISTS (SELECT 1 FROM TD_PORTAL_NEWS_TYPE A WHERE A.PARENT_ID = T.RID)");
        if( null != userId)    {
            tSearchSql.append(" AND T1.USER_ID =").append(userId);
        }
        tSearchSql.append(" START WITH T.PARENT_ID IS NULL");
        tSearchSql.append(" CONNECT BY T.PARENT_ID = PRIOR T.RID ORDER SIBLINGS BY T.XH");
        List<Object[]> resultList = em.createNativeQuery(tSearchSql.toString()).getResultList();
        return resultList;
    }

    /**
     * 根据栏目Id,获取栏目实体
     * @return
     */
    public TdPortalNewsType findTdPortalNewsType(Integer id)  {
        if(null != id)    {
            TdPortalNewsType tdPortalNewsType = (TdPortalNewsType)this.find(TdPortalNewsType.class, id);
            if( null != tdPortalNewsType.getTdPortalNewsTypes())	{
            	tdPortalNewsType.getTdPortalNewsTypes().size();
            }
            return tdPortalNewsType;
        }
        return null;
    }


    //===========================门户管理===========================
    
    public TdProtal findPortal(Integer rid) {
        return (TdProtal) this.find(TdProtal.class, rid);
    }

    
    public TdProtal findPortalWithLayout(Integer rid) {
        TdProtal protal = em.find(TdProtal.class, rid);
        protal.getTdPortalLayouts().size();
        return protal;
    }

    
    public String saveOrUpdatePortal(TdProtal portal) {
        StringBuilder sb = new StringBuilder(" select t from TdProtal t where t.portalName ='");
        sb.append(portal.getPortalName()).append("' ");
        if(null != portal.getRid()) {
            sb.append(" and t.rid <> ").append(portal.getRid());
        }
        List<TdProtal> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            return "门户名称不能重复！";
        }

        if(null != portal.getRid()) {
            this.update(portal);
        }else {
            this.save(portal);
        }
        return null;
    }

    
    public void sqPortal(List<String> selectedIds, Integer portalId) {
        StringBuilder sb = null;
        if(null != selectedIds && selectedIds.size() > 0) {
            sb = new StringBuilder(" select t from TdPortalUser t where t.tdProtal.rid =");
            sb.append(portalId);
            List<TdPortalUser> list = em.createQuery(sb.toString()).getResultList();

            List<String> oldList = new ArrayList<String>();
            if(null != list && list.size() > 0) {
                for(TdPortalUser t:list) {
                    oldList.add(t.getTsUserInfo().getRid().toString());
                }
            }

            List<String> allList = new ArrayList<String>(selectedIds);
            allList.addAll(oldList);

            /**
             * 要删的集合
             */
            List<String> delList = CollectionUtil.getDiffentNoDuplicate(selectedIds, allList);
            /**
             * 要加的集合
             */
            List<String> addList = CollectionUtil.getDiffentNoDuplicate(oldList, allList);

            if(null != delList && delList.size() > 0) {
                StringBuilder del = new StringBuilder();
                if(null != delList && delList.size() > 0) {
                    for(String s: delList) {
                        del.append(",").append(s);
                    }

                    sb = new StringBuilder(" DELETE FROM TD_PORTAL_USER WHERE PORTAL_ID = '");
                    sb.append(portalId).append("' AND USER_ID IN (").append(del.toString().substring(1)).append(") ") ;

                    em.createNativeQuery(sb.toString()).executeUpdate();
                }
            }

            if(null != addList && addList.size() > 0) {
                for(String userId: addList) {
                    TdPortalUser t = new TdPortalUser();
                    t.setTdProtal(new TdProtal(portalId));
                    t.setTsUserInfo(new TsUserInfo(Integer.valueOf(userId)));

                    this.save(t);
                }
            }

        }else {
            //删除中间表
            sb = new StringBuilder(" DELETE FROM TD_PORTAL_USER WHERE PORTAL_ID = '");
            sb.append(portalId).append("' ");
            em.createNativeQuery(sb.toString()).executeUpdate();
        }
    }

    
    public String findPortalUserIds(Integer portalId) {
        StringBuilder sb = new StringBuilder(" SELECT LISTAGG(T.USER_ID, ',') WITHIN GROUP(ORDER BY T.RID) FROM TD_PORTAL_USER T WHERE T.PORTAL_ID = '");
        sb.append(portalId).append("' ");
        List<String> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    
    public List<TdPortalColumn> findPortalColumn(Integer userId) {
        List<TdPortalColumn> rtnList = new ArrayList<TdPortalColumn>();
        StringBuilder sb = new StringBuilder(" SELECT DISTINCT T2.RID, T2.COL_NAME,T2.COL_TYPE ");
        if(null != userId) {
            sb.append(" FROM TD_PORTAL_COLAUTH T1 ");
            sb.append(" INNER JOIN TD_PORTAL_COLUMN T2 ON T1.COL_ID = T2.RID ");
        }else {
            sb.append(" FROM TD_PORTAL_COLUMN T2 ");
        }
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_TYPE T3 ON T2.NEWS_TYPE_ID = T3.RID ");
        sb.append(" WHERE 1=1 AND T3.PARENT_ID IS NULL ");
        if(null != userId) {
            sb.append(" AND T1.USER_ID = '").append(userId).append("' ");
        }
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            for(Object[] o:list) {
            	//栏目类型
            	String colType = null == o[2] ? "":String.valueOf(o[2]);
                TdPortalColumn col = new TdPortalColumn(Integer.valueOf(o[0].toString()), o[1].toString(),(PortalColType)EnumUtils.findEnum(PortalColType.class, colType));
                rtnList.add(col);
            }
        }
        return rtnList;
    }

    /**
     * 根据单位id查询科室
     *
     * @param unitId
     *            单位id
     * @return 科室list
     * <AUTHOR>
     */
    public List<TsOffice> findOfficeByUnitId(Integer unitId) {
        if (null != unitId) {
            StringBuffer sql = new StringBuffer("");
            sql.append(" select new TsOffice(t.rid,t.officename) from TsOffice t ");
            sql.append(" where t.tsUnit.rid = ").append(unitId);
            sql.append(" and t.ifReveal = 1 order by t.num");
            return em.createQuery(sql.toString()).getResultList();
        }
        return null;
    }

    
    public void saveOrUpdatePortalLayout(List<TdPortalLayout> newList, Integer portalId, String layout) {
        StringBuilder sb = null;
        if(null != newList && newList.size() > 0) {
            TdProtal oldPortal = em.find(TdProtal.class, portalId);
            List<TdPortalLayout> oldList = oldPortal.getTdPortalLayouts();

            /**
             * 1. 找出要删掉的记录：oldList中的元素在newList中找不到
             * 2. 找出要更新的记录：oldList中的元素在newList中能找到
             * 3. 找出要添加的记录：newList中移除要更新的记录，就是要添加的
             */
            List<TdPortalLayout> deleteList = new ArrayList<TdPortalLayout>();
            //需要更新的集合
            List<TdPortalLayout> updateList = new ArrayList<TdPortalLayout>();
            //需要从newList移除的集合
            List<TdPortalLayout> updateList2 = new ArrayList<TdPortalLayout>();

            int len = newList.size();
            for(TdPortalLayout oldT:oldList) {
                for(int i=0; i<len; i++) {
                    TdPortalLayout newT = newList.get(i);
                    if(oldT.getTdPortalColumn().getRid().equals(newT.getTdPortalColumn().getRid())) {
                        //栏目相同，就要更新
                        oldT.setPos(newT.getPos());
                        oldT.setNums(newT.getNums());
                        oldT.setLines(newT.getLines());
                        oldT.setWords(newT.getWords());
                        oldT.setDispDate(newT.getDispDate());
                        oldT.setHeighth(newT.getHeighth());

                        updateList.add(oldT);
                        updateList2.add(newT);

                        break;
                    }else {
                        if(i == (len-1)) {
                            deleteList.add(oldT);
                        }
                    }
                }
            }

            //删除要删除的记录
            if(null != deleteList && deleteList.size() > 0) {
                StringBuilder del = new StringBuilder();
                for(TdPortalLayout t:deleteList) {
                    del.append(",").append(t.getTdPortalColumn().getRid());
                }
                sb = new StringBuilder(" DELETE FROM TD_PORTAL_LAYOUT WHERE PORTAL_ID = '");
                sb.append(portalId).append("' AND COL_ID IN(").append(del.toString().substring(1)).append(") ");
                em.createNativeQuery(sb.toString()).executeUpdate();
            }

            //更新要更新的记录
            if(null != updateList && updateList.size() > 0) {
                for(TdPortalLayout t:updateList) {
                    this.update(t);
                }
            }

            //添加要添加的记录
            newList.removeAll(updateList2);
            if(null != newList && newList.size() > 0) {
                for(TdPortalLayout t:newList) {
                    this.save(t);
                }
            }

            //更新门户对象，设置版式
            oldPortal.setTdPortalLayouts(null);
            oldPortal.setTemplType((TemplType) EnumUtils.findEnum(TemplType.class, layout));
            this.update(oldPortal);

        }else {
            //为空的话，删掉
            sb = new StringBuilder(" DELETE FROM TD_PORTAL_LAYOUT WHERE PORTAL_ID = '");
            sb.append(portalId).append("' ");
            em.createNativeQuery(sb.toString()).executeUpdate();
        }
    }

    public TdPortalColumn findColumn(Integer columnId) {
        return em.find(TdPortalColumn.class, columnId);
    }

    
    public void deletePortal(Integer portalId) {
        StringBuilder sb = new StringBuilder();
        sb.append(" DELETE FROM TD_PORTAL_LAYOUT WHERE PORTAL_ID = '").append(portalId).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();

        sb = new StringBuilder();
        sb.append("  DELETE FROM TD_PORTAL_USER WHERE PORTAL_ID = '").append(portalId).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();

        sb = new StringBuilder();
        sb.append("  DELETE FROM TD_PROTAL WHERE RID = '").append(portalId).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();
    }

    //==============门户展示==========================

    
    public List findPortalData(Integer portalId,Integer userId) {
        TdProtal protal = em.find(TdProtal.class, portalId);
        return this.initPortalData(protal,userId);
    }

    
    public List findPersonalPortalData(Integer userId) {
        //查询个人门户
        List<TdProtal> personalList = em.createNamedQuery("TdProtal.findPersonalPortal").setParameter("userId", userId).getResultList();
        if(null != personalList && personalList.size() > 0) {
            TdProtal personalPortal = personalList.get(0);
            return this.initPortalData(personalPortal,userId);
        }else {
            return null;
        }
    }

    /**
     * 处理要查询的门户数据
     * @param protal 门户对象
     * @param userId 用户id
     * @return 需要的数据
     */
    private List initPortalData(TdProtal protal,Integer userId) {
        /**
         * 0- 门户对象
         * 1- 栏目对应的信息 map<栏目ID， 信息>
         */
        List rtnList = new ArrayList();
        List<TdPortalLayout> layoutList = protal.getTdPortalLayouts();

        //门户信息几天内显示'new'，单位：天
        int newsDays;
        //公告栏显示天数
        int showGglDays;
        try {
            newsDays = Integer.parseInt(this.commService.findParamValue("PORTAL_NEWS_DAYS"));
            showGglDays = Integer.parseInt(this.commService.findParamValue("PORTAL_SHOW_GGL_DAYS"));
        }catch(Exception e) {
            throw new RuntimeException("参数设置错误！");
        }

        Map newsMap = new HashMap();
        if(null != layoutList && layoutList.size() > 0) {
            for(TdPortalLayout layout: layoutList) {
                TdPortalColumn column = layout.getTdPortalColumn();
                PortalColType colType = column.getColType();
                // TODO
                //需要加载信息类型
                TdPortalNewsType newsType = column.getTdPortalNewsType();
                if(PortalColType.IMG.equals(colType)) {
                    //图片栏目
                    List<TdPortalNews> imgNewsList = this.loadImgInf(layout.getLines(),userId);
                    newsMap.put(column.getRid().toString(), imgNewsList);
                } else if(PortalColType.GGL.equals(colType)) {
                    //公告栏
                    List<TdPortalNews> gglNewsList = this.loadGglInf(layout.getLines(), newsDays,userId,showGglDays);
                    newsMap.put(column.getRid().toString(), gglNewsList);
                } else if(PortalColType.LINK.equals(colType)) {
                    //常用链接
                    List<TdPortalLinks> linkList = this.loadLinksInf(layout.getLines());
                    newsMap.put(column.getRid().toString(), linkList);
                }	else if(PortalColType.PT.equals(colType)) {
                    //普通信息栏，如果有上下级关系，需要加载下级类型的信息
                    List<TdPortalNewsType> newsTypeList = newsType.getTdPortalNewsTypes();
                    if(null != newsTypeList && newsTypeList.size() > 0) {
                        Map<TdPortalNewsType, List<TdPortalNews>> comNewsMap = new LinkedHashMap<TdPortalNewsType, List<TdPortalNews>>();
                        for(TdPortalNewsType tnt : newsTypeList) {
                            //System.out.println("------");
                            List<TdPortalNews> newsList = this.loadWordNewsInf(tnt.getRid(), layout.getLines(), newsDays,userId);
                            comNewsMap.put(tnt, newsList);
                        }
                        newsMap.put(column.getRid().toString(), comNewsMap);
                    }else {
                        //普通信息栏
                        List<TdPortalNews> newsList = this.loadWordNewsInf(column.getTdPortalNewsType().getRid(), layout.getLines(), newsDays,userId);
                        newsMap.put(column.getRid().toString(), newsList);
                    }
                }
            }
        }
        rtnList.add(protal);
        rtnList.add(newsMap);
        return rtnList;
    }


    /**
     * 加载常用链接信息
     * @param limits
     * @return
     */
    private List<TdPortalLinks> loadLinksInf(int limits) {
        return em.createNamedQuery("TdPortalLinks.findAll").setMaxResults(limits).getResultList();
    }

    /**
     * 加载公告栏信息
     * @param limits 查询前几条
     * @param newsDays  门户信息几天内显示'new'，单位：天
     * @param userRid  人员id
     * @param showGglDays  公告栏信息显示天数
     * @return 信息集合
     */
    private List<TdPortalNews> loadGglInf(int limits, int newsDays,Integer userRid,int showGglDays) {
        List<TdPortalNews> rtnList = new ArrayList<TdPortalNews>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM ( ");
        sb.append(" SELECT DISTINCT T1.RID, T1.NEWS_TITLE, T1.IF_ZD, TO_CHAR(T1.NEWS_DATE,'YYYY-MM-DD') AS  NEWS_DATE, ");
        sb.append(" CASE WHEN SYSDATE-T1.NEWS_DATE <=").append(newsDays).append(" THEN 1 ELSE 0 END AS DAYS, T1.NEW_ADR,T1.NEWS_TYPE ");
        sb.append(",DECODE(T2.READ_STATE,1,1,NULL) AS IFREAD FROM TD_PORTAL_NEWS T1 ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_AUTH T2 ON T2.NEWS_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_OFFICE T4 ON T4.NEWS_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_UNIT T5 ON T5.NEWS_ID = T1.RID ");
        sb.append(" WHERE T1.IF_NOTICE = '1' AND T1.STATE_MARK = '1' AND T1.NEWS_TYPE<> '").append(NewsType.PHOTOS).append("' ");
//        sb.append(" AND (T1.IF_ALL = 1 OR T2.USER_ID = ").append(userRid).append(") ");
        sb.append(" AND (T1.IF_ALL = 1 OR T2.USER_ID = ").append(userRid).append(" OR T4.OFFICE_ID IN (");
        
        sb.append(" SELECT C.OFFICEID FROM TS_USER_INFO T1 INNER JOIN (");
        sb.append(" SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
        sb.append(" UNION ALL");
        sb.append(" SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
        sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ) C ON C.RID = T1.EMP_ID");
        sb.append(" WHERE 1=1 AND T1.IF_REVEAL='1' AND T1.RID = '").append(userRid).append("' )");
        
        sb.append(" OR T5.UNIT_ID IN ( SELECT T2.RID FROM TS_USER_INFO T1 INNER JOIN TS_UNIT T2 ON T1.UNIT_RID = T2.RID WHERE T1.RID =").append(userRid).append("))");
        
        sb.append(" AND SYSDATE-T1.NEWS_DATE <= T1.ROLL_DAYS");
        sb.append(" )  ");
        sb.append(" ORDER BY IF_ZD DESC, NEWS_DATE DESC ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            for(Object[] o: list) {
                TdPortalNews news = new TdPortalNews();
                news.setRid(Integer.valueOf(o[0].toString()));
                news.setNewsTitle(o[1]==null?"":o[1].toString());
                news.setIfZd(Integer.valueOf(o[2].toString()));
                news.setNewsDateStr(o[3]==null?"":o[3].toString());
                if(null != o[4] && "1".equals(o[4].toString())) {
                    news.setIfNew(Boolean.TRUE);
                }
                news.setNewAdr(o[5]==null?"":o[5].toString());
                news.setNewsType(Integer.valueOf(o[6].toString()));
               //TODO
                news.setIfHasRead(true);
                rtnList.add(news);
            }
        }
        return rtnList;
    }

    /**
     * 加载普通信息
     * @param typeId 信息类型ID
     * @param limits 查询前几条
     * @param newsDays  门户信息几天内显示'new'，单位：天
     * @param userRid  人员id
     * @return 信息集合
     */
    private List<TdPortalNews> loadWordNewsInf(int typeId, int limits, int newsDays,Integer userRid) {
        List<TdPortalNews> rtnList = new ArrayList<TdPortalNews>();

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM ( ");
        sb.append(" SELECT ROWNUM RN, U.*  FROM ( ");
        sb.append(" SELECT * FROM ( ");
        sb.append(" SELECT DISTINCT T1.RID, T1.NEWS_TITLE, T1.IF_ZD, TO_CHAR(T1.NEWS_DATE,'YYYY-MM-DD') AS  NEWS_DATE, ");
        sb.append(" CASE WHEN SYSDATE-T1.NEWS_DATE <=").append(newsDays).append(" THEN 1 ELSE 0 END AS DAYS, T1.NEW_ADR,T1.NEWS_TYPE ");
        sb.append(" FROM TD_PORTAL_NEWS T1 ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_AUTH T2 ON T2.NEWS_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_OFFICE T4 ON T4.NEWS_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_UNIT T5 ON T5.NEWS_ID = T1.RID ");
        sb.append(" WHERE T1.NEWS_TYPE_ID = '").append(typeId).append("' AND T1.STATE_MARK = '1' AND T1.NEWS_TYPE<> '").append(NewsType.PHOTOS).append("' ");
//        sb.append(" AND (T1.IF_ALL = 1 OR T2.USER_ID = ").append(userRid).append(") ");
        sb.append(" AND (T1.IF_ALL = 1 OR T2.USER_ID = ").append(userRid).append(" OR T4.OFFICE_ID IN (");
        
        sb.append(" SELECT C.OFFICEID FROM TS_USER_INFO T1 INNER JOIN (");
        sb.append(" SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
        sb.append(" UNION ALL");
        sb.append(" SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
        sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ) C ON C.RID = T1.EMP_ID");
        sb.append(" WHERE 1=1 AND T1.IF_REVEAL='1' AND T1.RID = '").append(userRid).append("' )");
        
        sb.append(" OR T5.UNIT_ID IN ( SELECT T2.RID FROM TS_USER_INFO T1 INNER JOIN TS_UNIT T2 ON T1.UNIT_RID = T2.RID WHERE T1.RID =").append(userRid).append("))");
        
        sb.append(" ) ");
        sb.append(" ORDER BY IF_ZD DESC, NEWS_DATE DESC ");
        sb.append(" ) U ");
        sb.append(" ) WHERE RN <=").append(limits);

        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            for(Object[] o: list) {
                TdPortalNews news = new TdPortalNews();
                news.setRid(Integer.valueOf(o[1].toString()));
                news.setNewsTitle(o[2]==null?"":o[2].toString());
                news.setIfZd(Integer.valueOf(o[3].toString()));
                news.setNewsDateStr(o[4]==null?"":o[4].toString());
                if(null != o[5] && "1".equals(o[5].toString())) {
                    news.setIfNew(Boolean.TRUE);
                }
                news.setNewAdr(o[6]==null?"":o[6].toString());
                news.setNewsType(Integer.valueOf(o[7].toString()));

                rtnList.add(news);
            }
        }
        return rtnList;
    }

    /**
     * 加载图片信息
     * @param userRid  人员id
     */
    private List<TdPortalNews> loadImgInf(int limits,Integer userRid) {
        List<TdPortalNews> rtnList = new ArrayList<TdPortalNews>();

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM ( ");
        sb.append(" SELECT U1.*, ROWNUM RN FROM ( ");
        sb.append(" SELECT RANK() OVER(PARTITION BY A.RID ORDER BY A.XH DESC) R, ");
        sb.append(" A.RID, A.ANNEX_ADDR, A.ANNEX_DESC , A.NEW_ADR FROM (SELECT");
        sb.append(" DISTINCT T1.RID, T2.ANNEX_ADDR, T2.ANNEX_DESC , T1.NEW_ADR,T2.XH,T1.IF_ZD , T1.NEWS_DATE");
        sb.append(" FROM TD_PORTAL_NEWS T1 ");
        sb.append(" INNER JOIN TD_PORTAL_NEWS_ANNEX T2 ON T2.MAIN_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_AUTH T3 ON T3.NEWS_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_OFFICE T4 ON T4.NEWS_ID = T1.RID ");
        sb.append(" LEFT JOIN TD_PORTAL_NEWS_UNIT T5 ON T5.NEWS_ID = T1.RID ");
        sb.append(" WHERE T1.NEWS_TYPE = '").append(NewsType.PHOTOS).append("' AND T1.STATE_MARK = '1'  ");
        sb.append(" AND (T1.IF_ALL = 1 OR T3.USER_ID = ").append(userRid).append(" OR T4.OFFICE_ID IN (");
        
        sb.append(" SELECT C.OFFICEID FROM TS_USER_INFO T1 INNER JOIN (");
        sb.append(" SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
        sb.append(" UNION ALL");
        sb.append(" SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
        sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ) C ON C.RID = T1.EMP_ID");
        sb.append(" WHERE 1=1 AND T1.IF_REVEAL='1' AND T1.RID = '").append(userRid).append("' )");
        
        sb.append(" OR T5.UNIT_ID IN ( SELECT T2.RID FROM TS_USER_INFO T1 INNER JOIN TS_UNIT T2 ON T1.UNIT_RID = T2.RID WHERE T1.RID =").append(userRid).append("))");
        
        sb.append(" ) A  ORDER BY A.IF_ZD DESC, A.NEWS_DATE DESC ");
        sb.append("  ) U1 WHERE U1.R = 1 ");
        sb.append(" ) WHERE RN <= ").append(limits);
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            for(Object[] o: list) {
                TdPortalNews news = new TdPortalNews();
                news.setNewsType(NewsType.PHOTOS.getTypeNo().intValue());
                news.setRid(Integer.valueOf(o[1].toString()));

                TdPortalNewsAnnex annex = new TdPortalNewsAnnex();
                annex.setAnnexAddr(o[2]==null?"":o[2].toString());
                annex.setAnnexDesc(o[3]==null?"":o[3].toString());

                news.setNewAdr(o[4]==null?"":o[4].toString());

                news.setTdPortalNewsAnnex(annex);

                rtnList.add(news);
            }
        }

        return rtnList;
    }

    
    public String findMyPortal(Integer userId) {
        String rtnStr = null;

        StringBuilder sb = new StringBuilder(" SELECT T1.PORTAL_NAME, T1.RID ");
        sb.append(" FROM TD_PROTAL T1 ");
        sb.append(" INNER JOIN TD_PORTAL_USER T2 ON T2.PORTAL_ID = T1.RID ");
        sb.append(" WHERE T2.USER_ID = '").append(userId).append("'");
        sb.append(" ORDER BY T1.XH ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            sb = new StringBuilder();
            for(Object[] o: list) {
                sb.append(";").append(o[0]).append(",").append(o[1]);
            }
            rtnStr = sb.toString().substring(1);
        }
        return rtnStr;
    }




    /********************************信息管理***********************************/
    /**
     * 删除信息主表数据
     * @param newsId 信息主表Id
     * <AUTHOR>
     * @createDate 2014-8-22
     */
    public String deleteNews(Integer newsId)    {
        if( null != newsId)    {
        	try{
        		StringBuffer tdelSql = new StringBuffer();
        		tdelSql.append("BEGIN ");
        		tdelSql.append("DELETE TD_PORTAL_NEWS_ANNEX T WHERE T.MAIN_ID = '").append(newsId).append("';");
        		tdelSql.append("DELETE TD_PORTAL_NEWS_OFFICE T WHERE T.NEWS_ID = '").append(newsId).append("';");
        		tdelSql.append("DELETE TD_PORTAL_NEWS_UNIT T WHERE T.NEWS_ID = '").append(newsId).append("';");
        		tdelSql.append("DELETE TD_PORTAL_NEWS_AUTH T WHERE T.NEWS_ID = '").append(newsId).append("';");
        		tdelSql.append("DELETE TD_PORTAL_NEWS T WHERE T.RID = '").append(newsId).append("';");
        		tdelSql.append("END;");
        		em.createNativeQuery(tdelSql.toString()).executeUpdate();
        	}catch(Exception e){
        		e.printStackTrace();
        		 TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        		return "该数据已被引用，无法删除！";
        	}
        }
        return null;
    }

    /**
     * 根据当前登录人id查询门户对象，附带布局对象
     * @param rid 主键
     * @return 门户对象
     */
    public TdProtal findPortalByUserId(Integer rid){
        if(null != rid){
            StringBuffer sql = new StringBuffer();
            sql.append("SELECT RID FROM TD_PROTAL T WHERE T.USER_ID = ").append(rid);
            List<Object> list = em.createNativeQuery(sql.toString()).getResultList();
            if(null != list && list.size() > 0){//已存在该用户的门户对象
                Integer protalId = Integer.valueOf(String.valueOf(list.get(0)));
                return findPortalWithLayout(protalId);
            }else{//没有，则新增该用户的门户对象
                TdProtal tdProtal = new TdProtal();
                tdProtal.setUserId(rid);
                tdProtal.setCreateDate(new Date());
                tdProtal.setCreateManid(rid);
                tdProtal.setPortalName("个人工作站");
                tdProtal.setPortalType(1);
                tdProtal.setTdPortalLayouts(new LinkedList<TdPortalLayout>());
                return (TdProtal) this.saveObj(tdProtal);
            }
        }
        return null;
    }

    /************************门户个人设置***************************/

    /**
     * 保存门户个人设置
     * @param userId 登录人Id
     * @param empId 职员ID
     * @param name 名称
     * @param password 密码
     * @param url 地址
     * @param mobilePhone 电话号码
     * @param skinName 皮肤
     * <AUTHOR>
     * @createDate 2014-8-22
     */
    public void saveOrUpdatePersonSet(Integer userId,Integer empId,String name,String password,
                                      String url,String mobilePhone,String skinName,boolean ifUserQickDesk) {
        if( null != userId && StringUtils.isNotBlank(name) && StringUtils.isNotBlank(url))    {
            TsPersonalSetting tempSet = null;
            StringBuffer tHql = new StringBuffer();
            tHql.append("select t from TsPersonalSetting t where t.tsUserInfo.rid = ").append(userId);
            List<TsPersonalSetting> resultList = this.em.createQuery(tHql.toString()).getResultList();
            if( null != resultList && resultList.size() > 0)    {
                tempSet = resultList.get(0);
                tempSet.setDefaultUrl(url);
                tempSet.setDefaultUrlType(PersonSetType.valueOf(name));
                tempSet.setDefaultSkin(skinName);
                this.update(tempSet);
            }else{
                tempSet = new TsPersonalSetting();
                tempSet.setDefaultUrl(url);
                tempSet.setDefaultUrlType(PersonSetType.valueOf(name));
                tempSet.setTsUserInfo(new TsUserInfo(userId));
                tempSet.setDefaultSkin(skinName);
                this.save(tempSet);
            }
            StringBuffer tUpSql = null;
            if(null != empId)    {
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TB_SYS_EMP T SET T.MB_NUM = '").append(mobilePhone);
                tUpSql.append("' WHERE T.RID = ").append(empId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
            if(null != userId)    {
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.MB_NUM = '").append(mobilePhone);
                tUpSql.append("' WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
            if(StringUtils.isNotBlank(password))    {
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.PASSWORD ='").append(new MD5Util().getMD5ofStr(password));
                tUpSql.append("' WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
            if(ifUserQickDesk){
            	tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.DISP_KJMENU =1");
                tUpSql.append(" WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }else{
            	tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.DISP_KJMENU =0");
                tUpSql.append(" WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
        }
    }
    /**
     * 保存门户个人设置
     * @param userId 登录人Id
     * @param empId 职员ID
     * @param name 名称
     * @param password 密码
     * @param url 地址
     * @param mobilePhone 电话号码
     * @param skinName 皮肤
     * <AUTHOR>
     * <AUTHOR> 是否需要加密密码
     * @createDate 2014-8-22
     */
    public void saveOrUpdatePersonSet(Integer userId,Integer empId,String name,String password,
                                      String url,String mobilePhone,String skinName,boolean ifUserQickDesk,boolean passwordEncrypt,Integer themesRid,Integer ifUpPsw) {
        if( null != userId && StringUtils.isNotBlank(name) && StringUtils.isNotBlank(url))    {
            TsPersonalSetting tempSet = null;
            StringBuffer tHql = new StringBuffer();
            tHql.append("select t from TsPersonalSetting t where t.tsUserInfo.rid = ").append(userId);
            List<TsPersonalSetting> resultList = this.em.createQuery(tHql.toString()).getResultList();
            if( null != resultList && resultList.size() > 0)    {
                tempSet = resultList.get(0);
                tempSet.setDefaultUrl(url);
                tempSet.setDefaultUrlType(PersonSetType.valueOf(name));
                tempSet.setDefaultSkin(skinName);
                this.update(tempSet);
            }else{
                tempSet = new TsPersonalSetting();
                tempSet.setDefaultUrl(url);
                tempSet.setDefaultUrlType(PersonSetType.valueOf(name));
                tempSet.setTsUserInfo(new TsUserInfo(userId));
                tempSet.setDefaultSkin(skinName);
                this.save(tempSet);
            }
            StringBuffer tUpSql = null;
            if(null != empId) {
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TB_SYS_EMP T SET T.MB_NUM = '").append(mobilePhone);
                tUpSql.append("' WHERE T.RID = ").append(empId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
            if(null != userId) {
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.MB_NUM = '").append(mobilePhone);
                tUpSql.append("' WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
            if(ifUpPsw==1 && StringUtils.isNotBlank(password))    {
                password=password.toUpperCase();
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.PASSWORD ='");
                tUpSql.append(passwordEncrypt ? new MD5Util().getMD5ofStr(password) : password);
                tUpSql.append("' WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
            if(ifUserQickDesk){
            	tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.DISP_KJMENU =1");
                tUpSql.append(" WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }else{
            	tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.DISP_KJMENU =0");
                tUpSql.append(" WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }

            if(themesRid!=null){
                tUpSql = new StringBuffer();
                tUpSql.append(" UPDATE TS_USER_INFO T SET T.THEME_ID =").append(themesRid);
                tUpSql.append(" WHERE T.RID = ").append(userId);
                em.createNativeQuery(tUpSql.toString()).executeUpdate();
            }
        }
    }

   /**
   * <p>Description：个人设置中添加密码有效期修改调整 </p>
   * <p>Author： yzz 2024-01-19 </p>
   */
   public void saveOrUpdatePersonSet(Integer userId,Integer empId,String name,String password,
                                     String url,String mobilePhone,String skinName,boolean ifUserQickDesk,
                                     boolean passwordEncrypt,Integer themesRid,Integer ifUpPsw,String pwsLiveDay) {
        if( null != userId && StringUtils.isNotBlank(name) && StringUtils.isNotBlank(url))    {
            if( null != userId && StringUtils.isNotBlank(name) && StringUtils.isNotBlank(url))    {
                TsPersonalSetting tempSet = null;
                StringBuffer tHql = new StringBuffer();
                tHql.append("select t from TsPersonalSetting t where t.tsUserInfo.rid = ").append(userId);
                List<TsPersonalSetting> resultList = this.em.createQuery(tHql.toString()).getResultList();
                if( null != resultList && resultList.size() > 0)    {
                    tempSet = resultList.get(0);
                    tempSet.setDefaultUrl(url);
                    tempSet.setDefaultUrlType(PersonSetType.valueOf(name));
                    tempSet.setDefaultSkin(skinName);
                    this.update(tempSet);
                }else{
                    tempSet = new TsPersonalSetting();
                    tempSet.setDefaultUrl(url);
                    tempSet.setDefaultUrlType(PersonSetType.valueOf(name));
                    tempSet.setTsUserInfo(new TsUserInfo(userId));
                    tempSet.setDefaultSkin(skinName);
                    this.save(tempSet);
                }
                StringBuffer tUpSql = null;
                if(null != empId) {
                    tUpSql = new StringBuffer();
                    tUpSql.append(" UPDATE TB_SYS_EMP T SET T.MB_NUM = '").append(mobilePhone);
                    tUpSql.append("' WHERE T.RID = ").append(empId);
                    em.createNativeQuery(tUpSql.toString()).executeUpdate();
                }
                if(null != userId) {
                    tUpSql = new StringBuffer();
                    tUpSql.append(" UPDATE TS_USER_INFO T SET T.MB_NUM = '").append(mobilePhone);
                    tUpSql.append("' WHERE T.RID = ").append(userId);
                    em.createNativeQuery(tUpSql.toString()).executeUpdate();
                }
                if(ifUpPsw==1 && StringUtils.isNotBlank(password))    {
                    password=password.toUpperCase();
                    tUpSql = new StringBuffer();
                    tUpSql.append(" UPDATE TS_USER_INFO T SET T.PASSWORD ='");
                    tUpSql.append(passwordEncrypt ? new MD5Util().getMD5ofStr(password) : password).append("'");
                    tUpSql.append(",PWD_BEG_DATE=sysdate,PWD_END_DATE=SYSDATE + INTERVAL '").append(pwsLiveDay).append("' DAY ");
                    tUpSql.append(" WHERE T.RID = ").append(userId);
                    em.createNativeQuery(tUpSql.toString()).executeUpdate();
                }
                if(ifUserQickDesk){
                    tUpSql = new StringBuffer();
                    tUpSql.append(" UPDATE TS_USER_INFO T SET T.DISP_KJMENU =1");
                    tUpSql.append(" WHERE T.RID = ").append(userId);
                    em.createNativeQuery(tUpSql.toString()).executeUpdate();
                }else{
                    tUpSql = new StringBuffer();
                    tUpSql.append(" UPDATE TS_USER_INFO T SET T.DISP_KJMENU =0");
                    tUpSql.append(" WHERE T.RID = ").append(userId);
                    em.createNativeQuery(tUpSql.toString()).executeUpdate();
                }

                if(themesRid!=null){
                    tUpSql = new StringBuffer();
                    tUpSql.append(" UPDATE TS_USER_INFO T SET T.THEME_ID =").append(themesRid);
                    tUpSql.append(" WHERE T.RID = ").append(userId);
                    em.createNativeQuery(tUpSql.toString()).executeUpdate();
                }
            }
        }
    }



    /**
     * 根据用户ID获取个人设置
     * @param userId 用户Id
     * @return
     * <AUTHOR>
     * @createDate 2014-8-22
     */
    public TsPersonalSetting findPersonSetting(Integer userId){
        if( null != userId)    {
            StringBuffer tHql = new StringBuffer();
            tHql.append("select t from TsPersonalSetting t where t.tsUserInfo.rid = ").append(userId);
            List<TsPersonalSetting> resultList = this.em.createQuery(tHql.toString()).getResultList();
            if( null != resultList && resultList.size() > 0)    {
                return resultList.get(0);
            }
        }
        return null;
    }

    
    public TdPortalNewsType findPictureType(){
        String hql = "select t from TdPortalNewsType t where t.typeCode = 'PICTUERTYPE'";
        List<TdPortalNewsType> list = em.createQuery(hql).getResultList();
        if(list != null && list.size() > 0){
            return list.get(0);
        }else{
            return null;
        }
    }

    
    public List<TdPortalNews> findPortalNews(Integer newsType , Integer unitId,String title ,Integer officeId,String man , Date beginDate,Date endDate){
        StringBuilder sb = new StringBuilder();
        sb.append("select t from TdPortalNews t where t.newsType = ").append(newsType);
        sb.append(" and t.tsOffice.tsUnit.rid = ").append(unitId);
        if(StringUtils.isNotBlank(title)){
            sb.append(" and t.newsTitle like '%").append(title).append("%'");
        }
        if(officeId != null){
            sb.append(" and t.tsOffice.rid = ").append(officeId);
        }
        if(StringUtils.isNotBlank(man)){
            sb.append(" and t.tsUserInfo.username like '%").append(man).append("%'");
        }
        if(beginDate != null ){
            sb.append(" and t.newsDate >= :beDate");
        }
        if(endDate != null){
            sb.append(" and t.newsDate <= :enDate");
        }
        sb.append(" order by t.newsDate desc");
        Query query = em.createQuery(sb.toString());
        if(beginDate != null){
            query.setParameter("beDate",beginDate);
        }
        if(endDate != null){
            query.setParameter("enDate",endDate);
        }
        List<TdPortalNews> list = query.getResultList();
        if(list != null && list.size() > 0){
            for(TdPortalNews t : list){
                t.getTdPortalNewsAnnexes().size();
            }
        }
        return list;
    }

    
    /**
     * 获取发布信息的阅读情况
     * @param newsId 发布信息的信息Id
     * 
     * @return
     * <AUTHOR>
     * @createDate 2015-4-16
     * 
     *  <p>修订内容：</p>
	 * 新增参数flag true:查询发布单位中人员阅读情况 ，false查询发布科室中人员阅读情况
 	 * @MethodReviser xq,2018年1月16日,findPortalNewsReadState
     * 
     */
    public List<Object[]> findPortalNewsReadState(Integer newsId,Boolean flag)	{
    	if( null != newsId){
    		StringBuilder sb = new StringBuilder();
    		//科室Id
    		String officeIds = null;
    		if(flag){
    			sb.append(" SELECT T2.UNITNAME,T1.USERNAME,T3.READ_STATE,T3.READ_TIME FROM TS_USER_INFO T1");
				sb.append(" LEFT JOIN TS_UNIT T2 ON T1.UNIT_RID = T2.RID");
				sb.append(" LEFT JOIN TD_PORTAL_NEWS_AUTH T3 ON T3.USER_ID = T1.RID");
				sb.append(" WHERE T2.IF_REVEAL= '1' AND T1.IF_REVEAL = '1' AND T3.NEWS_ID = ").append(newsId).append(" AND T2.RID IN (");
				sb.append(" SELECT T.UNIT_ID FROM TD_PORTAL_NEWS_UNIT T WHERE T.NEWS_ID = ").append(newsId);
				sb.append(" UNION SELECT T2.UNIT_RID FROM TD_PORTAL_NEWS_OFFICE T1 ");
				sb.append(" INNER JOIN TS_OFFICE T2 ON T2.RID = T1.OFFICE_ID WHERE T1.NEWS_ID = ").append(newsId);
				sb.append(") ORDER BY T2.UNITNAME");
				List<Object[]> resultList2 = em.createNativeQuery(sb.toString()).getResultList();
				return resultList2;
    		}else{
    			sb.append("SELECT LISTAGG( T.OFFICE_ID,',') WITHIN GROUP (ORDER BY T.OFFICE_ID) AS RIDS");
    			sb.append(" FROM TD_PORTAL_NEWS_OFFICE T WHERE T.NEWS_ID = '").append(newsId).append("' ");
    		}
			List resultList = this.em.createNativeQuery(sb.toString()).getResultList();
			if( null != resultList && resultList.size() > 0)	{
				officeIds = resultList.get(0)==null?null:resultList.get(0).toString();
			}
			//信息发布的科室
			if(StringUtils.isNotBlank(officeIds))	{
				sb = new StringBuilder();
				sb.append("SELECT E.OFFICENAMES,E.USERNAME,T.READ_STATE,T.READ_TIME FROM (");
				sb.append(" SELECT D.RID, D.USERNAME, D.OFFICENAMES");
				sb.append(" FROM (SELECT T1.RID, T1.USERNAME, LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
				sb.append(" , ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ");
				sb.append(" ,SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) AS ZKSNUM,C.EMPNUM FROM TS_USER_INFO T1 ");
				//2015-3-31 xt 新增过滤兼职科室
				sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
				sb.append(" UNION ALL");
				sb.append(" SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
				sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
				sb.append(" WHERE 1=1 AND T1.IF_REVEAL='1' ");
				sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ");
				sb.append(" ) D ");
				
				if (StringUtils.isNotBlank(officeIds)) {
					StringBuilder officePiece = new StringBuilder();
					String[] idArr = officeIds.split(",");
					for( String offid : idArr)	{
						officePiece.append(" OR D.OFFICEIDS LIKE '%,").append(offid).append(",%'");
					}
					sb.append(" WHERE (").append(officePiece.substring(3)).append(")");
				}
				sb.append(" ORDER BY D.ZKSNUM, D.EMPNUM ");
				sb.append(") E LEFT JOIN TD_PORTAL_NEWS_AUTH T ON T.USER_ID = E.RID");
				sb.append(" AND T.NEWS_ID = '").append(newsId).append("'");
				List<Object[]> resultList2 = em.createNativeQuery(sb.toString()).getResultList();
				return resultList2;
			}
    	}
    	return null;
    }
    
    public TdMsgMain saveMsgEntity(TdMsgMain entity) {
    	entity = (TdMsgMain) this.saveObj(entity);
    	return entity;
    }
    
    public TsUserInfo findTsUserInfo(Integer userId){
        if( null != userId)    {
            StringBuffer tHql = new StringBuffer();
            tHql.append("select t from TsUserInfo t where t.rid = ").append(userId);
            List<TsUserInfo> resultList = this.em.createQuery(tHql.toString()).getResultList();
            if( null != resultList && resultList.size() > 0)    {
                return resultList.get(0);
            }
        }
        return null;
    }
    
    public List<Object[]> findQickDeskList(Integer userId){
    	StringBuilder sb=new StringBuilder();
    	sb.append("select b.menu_cn,b.menu_uri,concat('/resources/component/quickDesktop/image/64px/',nvl(b.big_icon,b.menu_icon))");
    	sb.append(" from TS_USER_DESK a inner join ts_menu b on a.menu_id=b.rid");
    	sb.append(" where user_id=").append(userId);
    	sb.append(" order by a.order_no");
    	return em.createNativeQuery(sb.toString()).getResultList();
    }
    /************************服务方法***************************/
    
    public String getDesc() {
        return "门户基础服务";
    }

    
    public String getVersion() {
        return "1.0.0";
    }

}
