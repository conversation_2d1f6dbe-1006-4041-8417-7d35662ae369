package com.chis.modules.portal.logic;

import java.io.Serializable;

import com.chis.modules.portal.enumn.PortalColType;

/**
 * 布局的时候栏目设置Bean
 * <AUTHOR>
 */
public class PortalColumnSetBean implements Serializable{
	private static final long serialVersionUID = -8880212125491897785L;
	/**显示行数*/
	private Integer lines = 0;
	/**是否显示日期*/
	private Integer dispDate = 0;
	/**标题长度，超过以...显示*/
	private Integer words;
	/**栏目高度*/
	private Integer heighth;
	/** 门户类型 */
	private PortalColType portalColType;
	
	public PortalColumnSetBean() {
	}
	
	public Integer getLines() {
		return lines;
	}
	public void setLines(Integer lines) {
		this.lines = lines;
	}
	public Integer getDispDate() {
		return dispDate;
	}
	public void setDispDate(Integer dispDate) {
		this.dispDate = dispDate;
	}

	public Integer getWords() {
		return words;
	}

	public void setWords(Integer words) {
		this.words = words;
	}

	public Integer getHeighth() {
		return heighth;
	}

	public void setHeighth(Integer heighth) {
		this.heighth = heighth;
	}

	public PortalColType getPortalColType() {
		return portalColType;
	}

	public void setPortalColType(PortalColType portalColType) {
		this.portalColType = portalColType;
	}
	
	
	
}
