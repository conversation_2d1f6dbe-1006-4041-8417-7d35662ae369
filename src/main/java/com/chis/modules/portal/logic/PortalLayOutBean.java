package com.chis.modules.portal.logic;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

import com.chis.modules.portal.enumn.TemplType;

/**
 *  门户的几款布局
 * <AUTHOR>
 */
public class PortalLayOutBean implements Serializable{

	private static final long serialVersionUID = 7657527496718538230L;
	private Map<String, String> layoutMap;
	private Map<String, String> htmlMap;
	
	private static final String TABLE_S = "<table border=\"0\" style=\"width: 160px;background-color: #696666;\" cellpadding=\"0\" cellspacing=\"1\">";
	private static final String TABLE_E = "</table>";
	private static final String TR_S = "<tr style=\"height: 20px;\" bgcolor=\"#ffffff\">";
	private static final String TR_E = "</tr>";
	private static final String TD_S = "<td style=\"width: ";
	private static final String TD_E = "%\"></td>";
	private static final BigDecimal TEN = new BigDecimal(10);
	
	private static PortalLayOutBean instance = new PortalLayOutBean();
	
	private PortalLayOutBean() {
		this.init();
	}
	
	public static PortalLayOutBean getInstance() {
		return instance;
	}
	
	private void init() {
		TemplType[] types = TemplType.values();
		this.layoutMap = new LinkedHashMap<String, String>(types.length);
		this.htmlMap = new LinkedHashMap<String, String>(types.length);
		
		StringBuilder sb = null;
		for(TemplType t:types) {
			String cn = t.getTypeCN();
			this.layoutMap.put(cn, t.getTypeNo().toString());
			String[] splits = cn.split(":");
			sb =  new StringBuilder(TABLE_S);
			sb.append(TR_S);
			for(String s: splits) {
				sb.append(TD_S).append(new BigDecimal(s).multiply(TEN)).append(TD_E);
			}
			sb.append(TR_E);
			sb.append(TABLE_E);
			
			this.htmlMap.put(cn, sb.toString());
		}
	}

	public Map<String, String> getLayoutMap() {
		return layoutMap;
	}

	public void setLayoutMap(Map<String, String> layoutMap) {
		this.layoutMap = layoutMap;
	}

	public Map<String, String> getHtmlMap() {
		return htmlMap;
	}

	public void setHtmlMap(Map<String, String> htmlMap) {
		this.htmlMap = htmlMap;
	}



}
