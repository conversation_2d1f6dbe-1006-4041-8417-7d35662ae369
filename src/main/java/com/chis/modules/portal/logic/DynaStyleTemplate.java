package com.chis.modules.portal.logic;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * 动态表单模版
 *
 * <AUTHOR>
 * @createDate 2016年1月5日
 */
@XmlRootElement(name = "template")
public class DynaStyleTemplate implements Serializable {

	private static final long serialVersionUID = -8161822963801130400L;
	private String name; // 名称
	private String content; // 内容

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

}
