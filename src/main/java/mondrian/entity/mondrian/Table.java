package mondrian.entity.mondrian;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * 表
 * <AUTHOR>
 *name="TD_TJ_JKZ_KETTLE" schema="SAW" alias="JKZ_DES"
 */
public class Table {
	
	private String name;
	private String schema;
	private String alias;
	
	@XmlAttribute(name="name")
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	@XmlAttribute(name="schema")
	public String getSchema() {
		return schema;
	}
	public void setSchema(String schema) {
		this.schema = schema;
	}
	@XmlAttribute(name="alias")
	public String getAlias() {
		return alias;
	}
	public void setAlias(String alias) {
		this.alias = alias;
	}
	
	
	
	
}
