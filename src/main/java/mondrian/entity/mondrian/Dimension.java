package mondrian.entity.mondrian;

import java.util.List;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * 层次
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
public class Dimension {
	
	private String type;
	private Boolean visible;
	private String foreignKey;
	private Boolean highCardinality;
	private String name;
	private String description;
	
	private List<Hierarchy> hierarchies;

	@XmlAttribute(name="type")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	@XmlAttribute(name="visible")
	public boolean getVisible() {
		return visible;
	}

	public void setVisible(Boolean visible) {
		this.visible = visible;
	}

	@XmlAttribute(name="foreignKey")
	public String getForeignKey() {
		return foreignKey;
	}

	public void setForeignKey(String foreignKey) {
		this.foreignKey = foreignKey;
	}
	@XmlAttribute(name="highCardinality")
	public boolean getHighCardinality() {
		return highCardinality;
	}

	public void setHighCardinality(Boolean highCardinality) {
		this.highCardinality = highCardinality;
	}
	@XmlAttribute(name="name")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	@XmlAttribute(name="description")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	@XmlElement(name="Hierarchy")
	public List<Hierarchy> getHierarchies() {
		return hierarchies;
	}

	public void setHierarchies(List<Hierarchy> hierarchies) {
		this.hierarchies = hierarchies;
	}
	
	
	
	  
}
