package mondrian.entity.mondrian;

import java.util.List;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * 关系
 * <AUTHOR>
 *
 */
public class Hierarchy {
	
	private String name;
	private Boolean visible;
	private Boolean hasAll;
	private String allMemberName;
	private String primaryKey;
	private Table table;
	private List<Level> levels;
	
	@XmlAttribute(name="name")
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	@XmlAttribute(name="visible")
	public Boolean getVisible() {
		return visible;
	}
	public void setVisible(Boolean visible) {
		this.visible = visible;
	}
	@XmlAttribute(name="hasAll")
	public Boolean getHasAll() {
		return hasAll;
	}
	public void setHasAll(Boolean hasAll) {
		this.hasAll = hasAll;
	}
	@XmlAttribute(name="allMemberName")
	public String getAllMemberName() {
		return allMemberName;
	}
	public void setAllMemberName(String allMemberName) {
		this.allMemberName = allMemberName;
	}
	@XmlAttribute(name="primaryKey")
	public String getPrimaryKey() {
		return primaryKey;
	}
	public void setPrimaryKey(String primaryKey) {
		this.primaryKey = primaryKey;
	}
	@XmlElement(name="Table")
	public Table getTable() {
		return table;
	}
	public void setTable(Table table) {
		this.table = table;
	}
	@XmlElement(name="Level")
	public List<Level> getLevels() {
		return levels;
	}
	public void setLevels(List<Level> levels) {
		this.levels = levels;
	}
	
	
}
