package mondrian.entity.mondrian;

import java.util.Map;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * 级别
 * <AUTHOR>
 *
 */
public class Level {
	
	private String name;
	private Boolean visible;
	private String table;
	private String column;
	private String type;
	private String internalType;
	private Boolean uniqueMembers;
	private String levelType;
	private String hideMemberIf;
	private String escription;
	
	@XmlAttribute
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	@XmlAttribute
	public Boolean getVisible() {
		return visible;
	}
	public void setVisible(Boolean visible) {
		this.visible = visible;
	}
	@XmlAttribute
	public String getTable() {
		return table;
	}
	public void setTable(String table) {
		this.table = table;
	}
	@XmlAttribute
	public String getColumn() {
		return column;
	}
	public void setColumn(String column) {
		this.column = column;
	}
	@XmlAttribute
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	@XmlAttribute
	public String getInternalType() {
		return internalType;
	}
	public void setInternalType(String internalType) {
		this.internalType = internalType;
	}
	@XmlAttribute
	public Boolean getUniqueMembers() {
		return uniqueMembers;
	}
	public void setUniqueMembers(Boolean uniqueMembers) {
		this.uniqueMembers = uniqueMembers;
	}
	@XmlAttribute
	public String getLevelType() {
		return levelType;
	}
	public void setLevelType(String levelType) {
		this.levelType = levelType;
	}
	@XmlAttribute
	public String getHideMemberIf() {
		return hideMemberIf;
	}
	public void setHideMemberIf(String hideMemberIf) {
		this.hideMemberIf = hideMemberIf;
	}
	@XmlAttribute
	public String getEscription() {
		return escription;
	}
	public void setEscription(String escription) {
		this.escription = escription;
	}
	
	
	
}
