package mondrian.entity.mondrian;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * 指标
 * <AUTHOR>
 *
 */
public class Measure{
	
	private String name;
	private String column;
	private String datatype;
	private String aggregator;
	private String caption;
	private Boolean visible;
	@XmlAttribute
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	@XmlAttribute
	public String getColumn() {
		return column;
	}
	public void setColumn(String column) {
		this.column = column;
	}
	@XmlAttribute
	public String getDatatype() {
		return datatype;
	}
	public void setDatatype(String datatype) {
		this.datatype = datatype;
	}
	@XmlAttribute
	public String getAggregator() {
		return aggregator;
	}
	public void setAggregator(String aggregator) {
		this.aggregator = aggregator;
	}
	@XmlAttribute
	public String getCaption() {
		return caption;
	}
	public void setCaption(String caption) {
		this.caption = caption;
	}
	@XmlAttribute
	public Boolean getVisible() {
		return visible;
	}
	public void setVisible(Boolean visible) {
		this.visible = visible;
	}
	
	

}
