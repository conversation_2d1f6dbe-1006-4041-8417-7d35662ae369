package mondrian.entity;

/**
 * 页面展示表格单元对象
 * <AUTHOR>
 *
 */
public class ViewCell {
	//列合并数
	private Integer colSpan;
	//行合并数
	private Integer rowSpan;
	//单元值
	private String value;
	//将层级拼接在一起的展示字段
	private String showValue;
	
	private String realValue;

	public ViewCell() {
		this.colSpan = 1;
		this.rowSpan = 1;
	}

	public ViewCell(String value,String realValue) {
		this.colSpan = 1;
		this.rowSpan = 1;
		this.value = value;
		this.realValue = realValue;
	}

	public ViewCell(Integer colSpan, Integer rowSpan, String value) {
		this.colSpan = colSpan;
		this.rowSpan = rowSpan;
		this.value = value;
	}

	public Integer getColSpan() {
		return colSpan;
	}

	public void setColSpan(Integer colSpan) {
		this.colSpan = colSpan;
	}

	public Integer getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(Integer rowSpan) {
		this.rowSpan = rowSpan;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getShowValue() {
		return showValue;
	}

	public void setShowValue(String showValue) {
		this.showValue = showValue;
	}

	public String getRealValue() {
		return realValue;
	}

	public void setRealValue(String realValue) {
		this.realValue = realValue;
	}
}
