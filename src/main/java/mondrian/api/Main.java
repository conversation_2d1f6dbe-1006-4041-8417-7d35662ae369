package mondrian.api;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;




import org.olap4j.Cell;
import org.olap4j.CellSet;
import org.olap4j.OlapConnection;
import org.olap4j.OlapException;
import org.olap4j.OlapStatement;
import org.olap4j.Position;
import org.olap4j.metadata.Member;

public class Main {

	
	/** 
     * 获取连接Olap的连接 
     * @param url  连接Olap的URL 
     * @return 
     * @throws ClassNotFoundException 
     * @throws SQLException 
     */  
    public static OlapConnection getConnection(String url) throws ClassNotFoundException, SQLException{  
        Class.forName("mondrian.olap4j.MondrianOlap4jDriver");  
        Connection connection = DriverManager.getConnection(url);  
        return connection.unwrap(OlapConnection.class);  
    }  
    
    /** 
     * 获取查询的结构结果集 
     * @param mdx  mdx查询语句 
     * @param conn Olap连接 
     * @return 
     * @throws OlapException 
     */  
    public static CellSet getResultSet(String mdx,OlapConnection conn) throws OlapException{  
        OlapStatement statement = conn.createStatement();  
        CellSet cellSet = statement.executeOlapQuery(mdx);  
        return cellSet;  
    }  
    
    public static void main(String[] args) throws ClassNotFoundException, SQLException {  
        // TODO Auto-generated method stub  
        //callschema();  
        //获取连接  
     OlapConnection conn= getConnection(  
                   //URL协议  
                  "jdbc:mondrian:"  
                   //连接数据源的JDBC连接  
                + "Jdbc=****************************************;"  
                +"JdbcUser=saw;"
                +"JdbcPassword=cybercdc;"
                   //数据模型文件  
                + "Catalog=file://D:/rebuild/workspaces/web-system/src/main/webapp/WEB-INF/schema/Schema1.xml;"  
                  //连接数据源用到的驱动  
                + "JdbcDrivers=oracle.jdbc.driver.OracleDriver;");  
     //查询语句  
     String mdx="WITH "
     		+ "SET [~COLUMNS] AS     {[DIM_DQ].[DQ].[SING_DQ].Members} "
     		+ "SET [~ROWS_DIM_LB_LB] AS     {[DIM_LB].[LB].[SIMPLE_LB].Members} "
     		+ "SELECT NON EMPTY [~COLUMNS] ON COLUMNS, "
     		+ "NON EMPTY [~ROWS_DIM_LB_LB] ON ROWS FROM [JKZ_CUBE]";  
     //获取查询结果  
     CellSet cs=getResultSet(mdx, conn);  
       
     //处理返回数据  
        if(cs.getAxes().size()>1){  
            for (Position row : cs.getAxes().get(1)) {  
                for (Position column : cs.getAxes().get(0)) {  
                    for (Member member : row.getMembers()) {  
//                        System.out.println("rows:"+member.getUniqueName());
                        System.out.println("rows:"+member.getName());
                    }  
                    for (Member member : column.getMembers()) {  
//                        System.out.println("columns:"+member.getUniqueName());  
                    	System.out.println("columns:"+member.getName());
                    }  
                    final Cell cell = cs.getCell(column, row);  
                    System.out.println("values:"+cell.getValue());  
                    System.out.println();  
                }  
                }  
        }else{  
            for(Position column:cs.getAxes().get(0)){  
                for(Member member:column.getMembers()){  
                    System.out.println("columns:"+member.getUniqueName());  
                }  
                Cell cell=cs.getCell(column);  
                System.out.print("values:"+cell.getValue());  
                System.out.println();  
            }  
        }  
          
      
    }     
}
