package mondrian.api;

import java.util.List;

import mondrian.api.excel.ExcelBuilderOptions;
import mondrian.api.excel.ExcelWorksheetBuilder;

import org.olap4j.CellSet;
import org.saiku.olap.dto.resultset.CellDataSet;
import org.saiku.olap.query2.ThinHierarchy;
import org.saiku.olap.util.OlapResultSetUtil;
import org.saiku.olap.util.formatter.FlattenedCellSetFormatter;
import org.saiku.olap.util.formatter.HierarchicalCellSetFormatter;
import org.saiku.olap.util.formatter.ICellSetFormatter;

public class MondrianExportExcelUtil {

	 public static byte[] exportExcel(CellSet cellSet, List<ThinHierarchy> filters) {
	        return exportExcel(cellSet, new HierarchicalCellSetFormatter(), filters);
	    }

    public static byte[] exportExcel(CellSet cellSet,
                                     ICellSetFormatter formatter,
                                     List<ThinHierarchy> filters) {
        CellDataSet table = OlapResultSetUtil.cellSet2Matrix(cellSet, formatter);
        return exportExcel(table, formatter, filters);
    }

    public static byte[] exportExcel(CellDataSet table,
                                     ICellSetFormatter formatter,
                                     List<ThinHierarchy> filters) {
        ExcelBuilderOptions exb = new ExcelBuilderOptions();
        exb.repeatValues = (formatter instanceof FlattenedCellSetFormatter);
        return getExcel(table, filters, exb);
    }

    private static byte[] getExcel(CellDataSet table, List<ThinHierarchy> filters, ExcelBuilderOptions options) {
        // TBD Sheet name is parametric. Useful for future ideas or improvements
        ExcelWorksheetBuilder worksheetBuilder = new ExcelWorksheetBuilder(table, filters, options);
        return worksheetBuilder.build();
    }
}
