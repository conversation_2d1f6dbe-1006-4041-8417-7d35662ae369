package mondrian.api;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import mondrian.entity.NameMember;
import mondrian.rolap.RolapConnection;

import org.apache.log4j.Logger;
import org.olap4j.Axis;
import org.olap4j.Cell;
import org.olap4j.CellSet;
import org.olap4j.OlapConnection;
import org.olap4j.OlapException;
import org.olap4j.OlapStatement;
import org.olap4j.Position;
import org.olap4j.metadata.Cube;
import org.olap4j.metadata.Dimension;
import org.olap4j.metadata.Hierarchy;
import org.olap4j.metadata.Level;
import org.olap4j.metadata.Measure;
import org.olap4j.metadata.Member;
import org.olap4j.metadata.Schema;
import org.saiku.olap.dto.resultset.AbstractBaseCell;
import org.saiku.olap.dto.resultset.CellDataSet;
import org.saiku.olap.dto.resultset.DataCell;
import org.saiku.olap.dto.resultset.MemberCell;
import org.saiku.olap.query2.ThinQuery;
import org.saiku.olap.query2.util.Fat;
import org.saiku.olap.util.formatter.FlattenedCellSetFormatter;
import org.saiku.olap.util.formatter.ICellSetFormatter;
import org.saiku.query.Query;
import org.saiku.query.QueryDetails;
import org.saiku.service.olap.totals.AxisInfo;
import org.saiku.service.olap.totals.TotalNode;
import org.saiku.service.olap.totals.TotalsListsBuilder;
import org.saiku.service.olap.totals.aggregators.TotalAggregator;
















import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.github.abel533.echarts.DataZoom;
import com.github.abel533.echarts.axis.AxisLabel;
import com.github.abel533.echarts.axis.CategoryAxis;
import com.github.abel533.echarts.axis.ValueAxis;
import com.github.abel533.echarts.code.AxisType;
import com.github.abel533.echarts.code.Magic;
import com.github.abel533.echarts.code.Orient;
import com.github.abel533.echarts.code.Tool;
import com.github.abel533.echarts.code.Trigger;
import com.github.abel533.echarts.code.X;
import com.github.abel533.echarts.code.Y;
import com.github.abel533.echarts.feature.MagicType;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class MondrianUtile {
	
	public static final Logger logger = Logger.getLogger(MondrianUtile.class);

	/** 
     * 获取查询的结构结果集 
     * @param mdx  mdx查询语句 
     * @param path
     * @return 
     * @throws OlapException 
     */  
    public static CellSet getResultSet(String mdx,String path) throws OlapException{  
    	if(!StringUtils.isNotBlank(mdx))
    		return null;
    	OlapConnection conn = getConnection(path);
    	if(conn==null)
    		return null;
        CellSet cellSet = getResultSet(mdx,conn);
        return cellSet;  
    }
    
    /**
     * 显示结果
     * @param cs
     */
    public static void showResult(CellSet cs){
    	if(cs.getAxes().size()>1){  
            for (Position row : cs.getAxes().get(1)) {  
                for (Member member : row.getMembers()) {  
                    System.out.println("rows:"+member.getUniqueName());  
                }  
            }
    	
	    	for (Position column : cs.getAxes().get(0)) {  
	    		 for (Member member : column.getMembers()) {  
	                 System.out.println("columns:"+member.getUniqueName());  
	             }  
	    	}
        }else{  
            for(Position column:cs.getAxes().get(0))  
            {  
                for(Member member:column.getMembers()){  
                    System.out.println("columns:"+member.getUniqueName());  
                }  
                Cell cell=cs.getCell(column);  
                System.out.print("values:"+cell.getValue());  
                System.out.println();  
            }  
        }  
    }
    
	/**
     * 正则匹配
     * @param uniqueName
     * @return
     */
    private static int getMemberLevel(String uniqueName) {
    	Pattern p = Pattern.compile("\\].\\[");
        Matcher m = p.matcher(uniqueName);
        int count=0;  
        while(m.find()){  
            count++;  
        }  
		return count;
	}

	

	

	/** 
     * 获取查询的结构结果集 
     * @param mdx  mdx查询语句 
     * @param conn Olap连接 
     * @return 
     * @throws OlapException 
     */  
    public static CellSet getResultSet(String mdx,OlapConnection conn) throws OlapException{  
        OlapStatement statement = conn.createStatement();  
        long start  = System.currentTimeMillis();
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>start:"+start);
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>mdx:"+mdx);
        CellSet cellSet = statement.executeOlapQuery(mdx); 
        long end  = System.currentTimeMillis();
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>end:"+end);
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>use:"+(end-start));
        return cellSet;  
    }
    
    /**
     * 获取olap链接
     * @param path
     * @return
     */
    public static OlapConnection getConnection(String path){
    	MondrianConnection mondrianConnection = SpringContextHolder.getBean(MondrianConnection.class);
    	String url = packConnectionUrl(path);
    	if(!StringUtils.isNotBlank(url))
    		return null;
    	try{
    		OlapConnection olapConnection = mondrianConnection.getConnection(url);
    		return olapConnection;
    	}catch(Exception e){
    		e.printStackTrace();
    	}
    	return null;
    }

    /**
     * 构建url
     * 
     * mondrian.jkz.url.jdbc=****************************************
	 * mondrian.jkz.url.jdbcUser=saw
	 * mondrian.jkz.url.jdbcPassword=cybercdc
	 * mondrian.jkz.url.jdbcDrivers=oracle.jdbc.driver.OracleDriver
	 * mondrian.jkz.url.catalog=/WEB-INF/schema/Schema1.xml
	 * 
	 * jdbc:mondrian:Jdbc='****************************************';
	 * JdbcUser='saw'JdbcPassword='cybercdc';
	 * Catalog='file://D:/rebuild/workspaces/web-system/src/main/webapp/WEB-INF/schema/Schema1.xml';
	 * JdbcDrivers=oracle.jdbc.driver.OracleDriver;
     * @param path
     * @return
     */
	public static String packConnectionUrl(String path) {
		String jdbc = PropertyUtils.getValue(path+".url.jdbc");
		String jdbcUser = PropertyUtils.getValue(path+".url.jdbcUser");
		String jdbcPassword = PropertyUtils.getValue(path+".url.jdbcPassword");
		String jdbcDrivers = PropertyUtils.getValue(path+".url.jdbcDrivers");
		String catalog = PropertyUtils.getValue(path+".url.catalog");
		if(!StringUtils.isNotBlank(jdbc)
			||!StringUtils.isNotBlank(jdbcUser)
			||!StringUtils.isNotBlank(jdbcPassword) 
			||!StringUtils.isNotBlank(jdbcDrivers) 
			||!StringUtils.isNotBlank(catalog))
		return null;
		StringBuilder sb = new StringBuilder("jdbc:mondrian:");
		sb.append("Jdbc=").append(jdbc);
		sb.append(";JdbcUser=").append(jdbcUser);
		sb.append(";JdbcPassword=").append(jdbcPassword);
		sb.append(";JdbcDrivers=").append(jdbcDrivers);
		catalog = "file:/"+MondrianUtile.class.getClassLoader().getResource("").getPath().replace("classes/", "")+catalog;
		sb.append(";Catalog=").append(catalog).append(";");
		return sb.toString();
	}
	
	
	
	/**
	 * 封装指标，和维度数据，包含页面显示名称和后台使用的参数名称 （只封装维度）
	 * 例：showname=SIMPLE_DQ realName=[JKZ].[DQ].[DIM_DQ].[SIMPLE_DQ]
	 * [CUBE].[Dimension].[Hierarchy].[Level]
	 * [立方体].[层].[关系].[级别]
	 * @param path
	 * @return
	 * @throws OlapException 
	 */
	public static Map<NameMember,List<NameMember>> packNameMembersLevelsForMap(String path) throws OlapException{
		if(!StringUtils.isNotBlank(path))
			return null;
		OlapConnection conn = getConnection(path);
		if(conn==null)
			return null;
		return packNameMembersLevelsForMap(conn);
	}
	
	/**
	 * 封装指标，和维度数据，包含页面显示名称和后台使用的参数名称（只封装维度）
	 * 例：showname=SIMPLE_DQ realName=[JKZ].[DQ].[DIM_DQ].[SIMPLE_DQ]
	 * [CUBE].[Dimension].[Hierarchy].[Level]
	 * [立方体].[层].[关系].[级别]
	 * @param conn
	 * @return
	 * @throws OlapException 
	 */
	public static Map<NameMember,List<NameMember>> packNameMembersLevelsForMap(OlapConnection conn) throws OlapException{
		if(conn==null)
			return null;
		Map<NameMember,List<NameMember>> map = Maps.newHashMap();
		
		Schema schema = conn.getOlapSchema();
		List<Cube> cubes = schema.getCubes();
		if(cubes==null || cubes.size()==0)
			return null;
		for(Cube cubeItem:cubes){
			//处理层
			List<Dimension> dimensions = cubeItem.getDimensions();
			if(dimensions==null||dimensions.size()==0)
				continue;
			for(Dimension dimensionItem:dimensions){
				NameMember dimensionMember = new NameMember();
				String dimensionName = dimensionItem.getName();
				String dimensionRealName = contactStr("",dimensionName);
				dimensionMember.setShowName(dimensionName);
				dimensionMember.setRealName(dimensionRealName);
				List<NameMember> list = Lists.newArrayList();
				//处理关系
				List<Hierarchy> hierarchys = dimensionItem.getHierarchies();
				if(hierarchys==null||hierarchys.size()==0)
					continue;
				for(Hierarchy hierarchyItem:hierarchys){
					String hierarchyName = hierarchyItem.getName();
					String hierarchyRealName = contactStr(dimensionRealName,hierarchyName);
					//处理级别
					List<Level> levels = hierarchyItem.getLevels();
					if(levels==null||levels.size()==0)
						continue;
					for(Level levelItem:levels){
						NameMember levelMember = new NameMember();
						String levelName = levelItem.getName();
						levelMember.setShowName(levelName);
						levelMember.setRealName(contactStr(hierarchyRealName,levelName));
						levelMember.setParentName(dimensionRealName);
						list.add(levelMember);
					}
				}
				map.put(dimensionMember,list);
			}
		}
		return map;
	}
	
	/**
	 * 封装指标，和维度数据，包含页面显示名称和后台使用的参数名称 （只封装维度）
	 * 例：showname=SIMPLE_DQ realName=[JKZ].[DQ].[DIM_DQ].[SIMPLE_DQ]
	 * [CUBE].[Dimension].[Hierarchy].[Level]
	 * [立方体].[层].[关系].[级别]
	 * @param path
	 * @return
	 * @throws OlapException 
	 */
	public static List<NameMember> packNameMembersForLevels(String path) throws OlapException{
		if(!StringUtils.isNotBlank(path))
			return null;
		OlapConnection conn = getConnection(path);
		if(conn==null)
			return null;
		return packNameMembersForLevels(conn);
	}
	
	/**
	 * 封装指标，和维度数据，包含页面显示名称和后台使用的参数名称（只封装维度）
	 * 例：showname=SIMPLE_DQ realName=[JKZ].[DQ].[DIM_DQ].[SIMPLE_DQ]
	 * [CUBE].[Dimension].[Hierarchy].[Level]
	 * [立方体].[层].[关系].[级别]
	 * @param conn
	 * @return
	 * @throws OlapException 
	 */
	public static List<NameMember> packNameMembersForLevels(OlapConnection conn) throws OlapException{
		if(conn==null)
			return null;
		List<NameMember> list = Lists.newArrayList();
		Schema schema = conn.getOlapSchema();
		List<Cube> cubes = schema.getCubes();
		if(cubes==null || cubes.size()==0)
			return null;
		for(Cube cubeItem:cubes){
			//处理层
			List<Dimension> dimensions = cubeItem.getDimensions();
			if(dimensions==null||dimensions.size()==0)
				continue;
			for(Dimension dimensionItem:dimensions){
				String dimensionName = dimensionItem.getName();
				String dimensionRealName = contactStr("",dimensionName);
				//处理关系
				List<Hierarchy> hierarchys = dimensionItem.getHierarchies();
				if(hierarchys==null||hierarchys.size()==0)
					continue;
				for(Hierarchy hierarchyItem:hierarchys){
					String hierarchyName = hierarchyItem.getName();
					String hierarchyRealName = contactStr(dimensionRealName,hierarchyName);
					//处理级别
					List<Level> levels = hierarchyItem.getLevels();
					if(levels==null||levels.size()==0)
						continue;
					for(Level levelItem:levels){
						NameMember levelMember = new NameMember();
						String levelName = levelItem.getName();
						levelMember.setShowName(levelName);
						levelMember.setRealName(contactStr(hierarchyRealName,levelName));
						levelMember.setParentName(dimensionRealName);
						list.add(levelMember);
					}
				}
			}
		}
		return list;
	}
	
	/**
	 * 封装指标，和维度数据，包含页面显示名称和后台使用的参数名称
	 * 例：showname=SIMPLE_DQ realName=[JKZ].[DQ].[DIM_DQ].[SIMPLE_DQ]
	 * [CUBE].[Dimension].[Hierarchy].[Level]
	 * [立方体].[层].[关系].[级别]
	 * @param path
	 * @return
	 * @throws OlapException 
	 */
	public static List<NameMember> packNameMembers(String path) throws OlapException{
		if(!StringUtils.isNotBlank(path))
			return null;
		OlapConnection conn = getConnection(path);
		if(conn==null)
			return null;
		return packNameMembers(conn);
	}
	
	/**
	 * 封装指标，和维度数据，包含页面显示名称和后台使用的参数名称
	 * 例：showname=SIMPLE_DQ realName=[JKZ].[DQ].[DIM_DQ].[SIMPLE_DQ]
	 * [CUBE].[Dimension].[Hierarchy].[Level]
	 * [立方体].[层].[关系].[级别]
	 * @param conn
	 * @return
	 * @throws OlapException 
	 */
	public static List<NameMember> packNameMembers(OlapConnection conn) throws OlapException{
		if(conn==null)
			return null;
		List<NameMember> list = Lists.newArrayList();
		Schema schema = conn.getOlapSchema();
		List<Cube> cubes = schema.getCubes();
		if(cubes==null || cubes.size()==0)
			return null;
		for(Cube cubeItem:cubes){
			//处理立方体
			NameMember cubeMember = new NameMember();
			String cubeName = cubeItem.getName();
			cubeMember.setShowName(cubeName);
			cubeMember.setRealName(contactStr("",cubeName));
			List<NameMember> dimensionMembers = Lists.newArrayList();
			//处理指标
			List<Measure> measures = cubeItem.getMeasures();
			if(measures==null||measures.size()==0)
				continue;
			for(Measure measureItem:measures){
				NameMember measureMember = new NameMember();
				String measureName = measureItem.getName();
				measureMember.setShowName(measureName);
				measureMember.setRealName(contactStr("[Measures]",measureName));
				//2:指标
				measureMember.setFlag(2);
				dimensionMembers.add(measureMember);
			}
			
			//处理层
			List<Dimension> dimensions = cubeItem.getDimensions();
			if(dimensions==null||dimensions.size()==0)
				continue;
			for(Dimension dimensionItem:dimensions){
				NameMember dimensionMember = new NameMember();
				String dimensionName = dimensionItem.getName();
				dimensionMember.setShowName(dimensionName);
				dimensionMember.setRealName(contactStr("",dimensionName));
				List<NameMember> hierarchyMembers = Lists.newArrayList();
				//处理关系
				List<Hierarchy> hierarchys = dimensionItem.getHierarchies();
				if(hierarchys==null||hierarchys.size()==0)
					continue;
				for(Hierarchy hierarchyItem:hierarchys){
					NameMember hierarchyMember = new NameMember();
					String hierarchyName = hierarchyItem.getName();
					hierarchyMember.setShowName(hierarchyName);
					hierarchyMember.setRealName(contactStr(dimensionMember.getRealName(),hierarchyName));
					List<NameMember> levelMembers = Lists.newArrayList();
					//处理级别
					List<Level> levels = hierarchyItem.getLevels();
					if(levels==null||levels.size()==0)
						continue;
					for(Level levelItem:levels){
						NameMember levelMember = new NameMember();
						String levelName = levelItem.getName();
						levelMember.setShowName(levelName);
						levelMember.setRealName(contactStr(hierarchyMember.getRealName(),levelName));
						levelMembers.add(levelMember);
					}
					hierarchyMember.setNameMembers(levelMembers);
					hierarchyMembers.add(hierarchyMember);
				}
				dimensionMember.setNameMembers(hierarchyMembers);
				dimensionMembers.add(dimensionMember);
			}
			cubeMember.setNameMembers(dimensionMembers);
			list.add(cubeMember);
		}
		return list;
	}
	
	/**
	 * 拼接字符串
	 * @param a
	 * @param b
	 * @return
	 */
	private static String contactStr(String a,String b){
		StringBuilder sb = new StringBuilder();
		if(StringUtils.isNotBlank(a)){
			sb.append(a).append(".[").append(b).append("]");
		}else{
			sb.append("[").append(b).append("]");
		}
		return sb.toString();
	}
	
	/**
	 * 计算行合计
	 * @param result
	 */
	public static void calculateRowTotal(CellDataSet result) {
		if(result.getCellSetHeaders()==null || result.getCellSetHeaders().length==0
				||result.getCellSetBody()==null || result.getCellSetBody().length==0)
			return;
		//头部处理
		AbstractBaseCell[][] header = result.getCellSetHeaders();
		int headerLength = header.length;
		AbstractBaseCell[][] newHeader = new AbstractBaseCell[headerLength][];
		for(int x=0;x<headerLength;x++){
			AbstractBaseCell[] tempArr = header[x];
			int tempArrLength = tempArr.length;
			AbstractBaseCell[] newArr = new AbstractBaseCell[tempArrLength+1];
			for(int y=0;y<tempArrLength;y++){
				newArr[y] = tempArr[y];
			}
			MemberCell title = new MemberCell();
			title.setFormattedValue("合计");
			newArr[tempArrLength] = title;
			newHeader[x] = newArr;
		}
		result.setCellSetHeaders(newHeader);
		//body处理
		AbstractBaseCell[][] body = result.getCellSetBody();
		int bodyLength = body.length;
		AbstractBaseCell[][] newBody = new AbstractBaseCell[bodyLength][];
		//合计列的index
		int totalIndex =body[0].length;
		for(int x=0;x<bodyLength;x++){
			AbstractBaseCell[] tempArr = body[x];
			AbstractBaseCell[] totalArr = new AbstractBaseCell[totalIndex+1];
			for(int y=0;y<tempArr.length;y++){
				AbstractBaseCell temp = tempArr[y];
				
				if(temp instanceof MemberCell){
					totalArr[y] = temp;
					continue;
				}else if(temp instanceof DataCell){
					DataCell data = (DataCell) temp;
					DataCell totalData =  (DataCell)totalArr[totalIndex];
					if(totalData==null){
						totalData = new DataCell();
						totalData.setRawNumber((Double) data.getRawNumber());
					}else{
						totalData.setRawNumber(add((Double)totalData.getRawNumber(),(Double) data.getRawNumber()));
					}
					totalData.setFormattedValue(formaterValue(totalData.getRawNumber()==null?"0":totalData.getRawNumber().toString()));
					totalArr[totalIndex] = totalData;
					totalArr[y] = temp;
				}
			}
			newBody[x] = totalArr;
		}
		result.setCellSetBody(newBody);
		
	}
	
	/**
	 * 进行分类合计
	 * @param result
	 * @param i 指定合计行数
	 * @param j 指定聚合列，根据低J列分类合计
	 */
	public static void calculateTotalForIndex(CellDataSet result, int i, int j) {
		if(result.getCellSetHeaders()==null || result.getCellSetHeaders().length==0
				||result.getCellSetBody()==null || result.getCellSetBody().length==0)
			return;
		AbstractBaseCell[][] body = result.getCellSetBody();
		int bodyLength = body.length;
		//新建BODY
		AbstractBaseCell[][] newBody = new AbstractBaseCell[bodyLength+i][];
		//新建并且初始化聚合数组
		AbstractBaseCell[][] totalArrs = new AbstractBaseCell[i][];
		for(int x=0;x<i;x++){
			AbstractBaseCell[] totalArr = new AbstractBaseCell[body[0].length];
			MemberCell title = null;
			//合计只填充第一行第一列
			if(x==0){
				title=new MemberCell();
				title.setFormattedValue("合计");
				totalArr[0] = title;
			}else{
				title=new MemberCell();
				totalArr[0] = title;
			}
			totalArr[j] = body[x][j];
			totalArrs[x] = totalArr;
		}
		
		//数据汇总
		for(int x=0;x<bodyLength;x++){
			AbstractBaseCell[] tempArr = result.getCellSetBody()[x];
			for(int y=0;y<tempArr.length;y++){
				AbstractBaseCell temp = tempArr[y];
				if(temp instanceof MemberCell){
					MemberCell title = (MemberCell) totalArrs[x%i][y];
					if(title!=null)
						continue;
					title=new MemberCell();
					totalArrs[x%i][y]=title;
				}else if(temp instanceof DataCell){
					DataCell data = (DataCell) totalArrs[x%i][y];
					if(data==null){
						data = new DataCell();
						data.setRawNumber((Double) ((DataCell) temp).getRawNumber());
					}else{
						data.setRawNumber(add((Double) ((DataCell) temp).getRawNumber(),(Double) ((DataCell) data).getRawNumber()));
					}
					data.setFormattedValue(formaterValue(data.getRawNumber()==null?"0":data.getRawNumber().toString()));
					totalArrs[x%i][y] = data;
				}
			}
		}
		
		for(int  z=0;z<bodyLength;z++){
			newBody[z] = body[z];
		}
		for(int x=1;x<=i;x++)
			newBody[bodyLength+i-x]=totalArrs[i-x];
		result.setCellSetBody(newBody);
		
	}

	/**
	 * 计算列合计
	 * @param result
	 */
	public static void calculateTotal(CellDataSet result) {
		if(result.getCellSetHeaders()==null || result.getCellSetHeaders().length==0
				||result.getCellSetBody()==null || result.getCellSetBody().length==0)
			return;
		AbstractBaseCell[][] body = result.getCellSetBody();
		int bodyLength = body.length;
		AbstractBaseCell[][] newBody = new AbstractBaseCell[bodyLength+1][];
		AbstractBaseCell[] totalArr = new AbstractBaseCell[body[0].length];
		for(int x=0;x<bodyLength;x++){
			AbstractBaseCell[] tempArr = result.getCellSetBody()[x];
			for(int y=0;y<tempArr.length;y++){
				AbstractBaseCell temp = tempArr[y];
				if(temp instanceof MemberCell){
					MemberCell title = (MemberCell) totalArr[y];
					if(title!=null)
						continue;
					title=new MemberCell();
					title.setFormattedValue("合计");
					totalArr[y]=title;
				}else if(temp instanceof DataCell){
					DataCell data = (DataCell) totalArr[y];
					if(data==null){
						data = new DataCell();
						data.setRawNumber((Double) ((DataCell) temp).getRawNumber());
					}else{
						data.setRawNumber(add((Double) ((DataCell) temp).getRawNumber(),(Double) ((DataCell) data).getRawNumber()));
					}
					data.setFormattedValue(formaterValue(data.getRawNumber()==null?"0":data.getRawNumber().toString()));
					totalArr[y] = data;
				}
			}
		}
		
		for(int i =0;i<bodyLength;i++){
			newBody[i] = body[i];
		}
		newBody[bodyLength]=totalArr;
		result.setCellSetBody(newBody);
		
	}
	
	/**
	 * 拼接结果集result1拼接到result2上，result2标识index标识首列宽度
	 * @param result1
	 * @param result2
	 * @param i
	 * @return
	 */
	public static CellDataSet contactDataTable(CellDataSet result1,
			CellDataSet result2, int i) {
		if(result1.getCellSetHeaders()==null || result1.getCellSetHeaders().length==0
				||result1.getCellSetBody()==null || result1.getCellSetBody().length==0||
				result2.getCellSetHeaders()==null || result2.getCellSetHeaders().length==0
				||result2.getCellSetBody()==null || result2.getCellSetBody().length==0)
			return result1;
		//头部处理
		//获取头部
		AbstractBaseCell[][] header1 = result1.getCellSetHeaders();
		AbstractBaseCell[][] header2 = result2.getCellSetHeaders();
		//获取数据
		AbstractBaseCell[][] body1 = result1.getCellSetBody();
		AbstractBaseCell[][] body2 = result2.getCellSetBody();
		//新头部长度
		int headerx = header1.length;
		//第一结果集列数
		int header1l = header1[0].length;
		int headery = header1l+header2[0].length-i;
		AbstractBaseCell[][] newHeader = new AbstractBaseCell[headerx][];
		
		for(int x=0;x<headerx;x++){
			AbstractBaseCell[] newArr = new AbstractBaseCell[headery];
			AbstractBaseCell[] arr1 = header1[x];
			AbstractBaseCell[] arr2 = header2[x];
			
			for(int y=0;y<headery;y++){
				if(y<header1l){
					newArr[y] = arr1[y];
				}else{
					newArr[y] = arr2[y-header1l+i];
				}
				
			}
			newHeader[x] = newArr;
		}
		result1.setCellSetHeaders(newHeader);
		
		int bodyx = body1.length;
		AbstractBaseCell[][] newBody = new AbstractBaseCell[bodyx][];
		for(int x=0;x<bodyx;x++){
			
			AbstractBaseCell[] newBrr = new AbstractBaseCell[headery];
			AbstractBaseCell[] brr1 = body1[x];
			AbstractBaseCell[] brr2 = body2[x];
			for(int y=0;y<headery;y++){
				if(y<header1l){
					newBrr[y] = brr1[y];
				}else{
					newBrr[y] = brr2[y-header1l+i];
				}
			}
			newBody[x] = newBrr;
		}
		result1.setCellSetBody(newBody);
		return result1;
	}
	
	/**
	 * 格式化数据
	 * @param string
	 * @return
	 */
	public static String formaterValue(String str) {
		if(!StringUtils.isNotBlank(str))
			return "";
		String[] arr = str.split("\\.");
		if(arr.length!=2)
			return str;
		if("0".equals(arr[1]) || "00".equals(arr[1]))
			return arr[0];
		return str;
	}

	/** 
	* * 两个Double数相加 * 
	*  
	* @param v1 * 
	* @param v2 * 
	* @return Double 
	*/  
	public static Double add(Double v1, Double v2) {  
	   BigDecimal b1 = new BigDecimal(v1==null?"0":v1.toString());  
	   BigDecimal b2 = new BigDecimal(v2==null?"0":v2.toString());  
	   return new Double(b1.add(b2).doubleValue());  
	}

	/**
	 * 清除数据体中的null值
	 * @param result
	 */
	public static void clearDataBodyNull(CellDataSet result) {
		if(result.getCellSetHeaders()==null || result.getCellSetHeaders().length==0)
			return;
		AbstractBaseCell[][] body = result.getCellSetBody();
		int bodyLength = body.length;
		for(int x=0;x<bodyLength;x++){
			AbstractBaseCell[] tempArr = result.getCellSetBody()[x];
			for(int y=0;y<tempArr.length;y++){
				AbstractBaseCell temp = tempArr[y];
				if(temp instanceof DataCell){
					if(((DataCell) temp).getRawNumber()==null)
						((DataCell) temp).setRawNumber(0.0);
					if(!StringUtils.isNotBlank(temp.getFormattedValue()))
						temp.setFormattedValue("0");
				}else if(temp==null){
					temp = new DataCell();
					((DataCell) temp).setRawNumber(0.0);
					temp.setFormattedValue("0");
					tempArr[y] = temp;
				}
			}
		}
	}  
	
	/**
	 * 清除指定行数据
	 * @param result
	 */
	public static void clearData(CellDataSet result,String headerOrBody,Integer index) {
		AbstractBaseCell[][] data = null;
		if("header".equals(headerOrBody)){
			data = result.getCellSetHeaders();
		}
		if("body".equals(headerOrBody)){
			data = result.getCellSetBody();
		}
		if(data==null || (data.length-1)<index)
			return;
		int dataLength = data.length;
		AbstractBaseCell[][] newdata = new AbstractBaseCell[dataLength-1][];
		for(int i=0;i<dataLength;i++){
			if(i==index)
				continue;
			else if(i<index)
				newdata[i] = data[i];
			else if(i>index)
				newdata[i-1] = data[i];
		}
		if("header".equals(headerOrBody)){
			result.setCellSetHeaders(newdata);
		}
		if("body".equals(headerOrBody)){
			result.setCellSetBody(newdata);
		}
	}  
	
	/**
	 * 
	 * @param result
	 * @return
	 */
	public static String buildBarChartJson(CellDataSet result,String title,String xAxisTitle,String yAxisTitle){
		AbstractBaseCell[][] body = result.getCellSetBody();
		AbstractBaseCell[][] header = result.getCellSetHeaders();
		if(header==null || header.length==0 || body==null || body.length==0)
			return "";
		GsonOption op = new GsonOption();
		op.tooltip().trigger(Trigger.axis);
		//定义工具
		op.toolbox().show(true).feature(
				new MagicType(Magic.line, Magic.bar), Tool.restore,
					Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
				.orient(Orient.horizontal);
    	
		Object[] obj = new Object[body.length];
		int i = 0;
		for (AbstractBaseCell[] items : body) {
			StringBuilder objStr = new StringBuilder(items[0].getFormattedValue());
			int strLength = objStr.length();
			if(strLength>6)
				objStr.insert(strLength/2, "\n");
			obj[i] =objStr.toString();
			i++;
		}
		op.legend().data(obj);
		op.legend().y(Y.top);
		op.legend().x(X.left);
		op.grid().y(55);
		op.grid().y2("30%");
		op.title().x(X.center);
		op.title(title);
		
		Integer dev = 0;
		for(AbstractBaseCell item:body[0]){
			if(item instanceof MemberCell)
				dev++;
			else
				break;
		}
		String[] titles = new String[header[0].length-dev];
		for (AbstractBaseCell[] item : header) {
			for(int x=dev;x<item.length;x++){
				String nowStr = titles[x-dev];
				if(StringUtils.isNotBlank(nowStr))
					nowStr = nowStr+"~"+item[x].getFormattedValue();
				else
					nowStr = item[x].getFormattedValue();
				titles[x-dev] = nowStr;
			}
		}
		//横坐标
		CategoryAxis xAxis = new CategoryAxis();
		xAxis.setData(Arrays.asList(titles));
		xAxis.setType(AxisType.category);
		xAxis.setShow(true);
		xAxis.setName(xAxisTitle);
		op.xAxis().add(xAxis);
		
		if(titles.length>12){
			DataZoom dz = new DataZoom();
			dz.setShow(true);
			dz.setStart(0);
			dz.setEnd(10);
			op.setDataZoom(dz);
		}
		
		// 纵坐标
		ValueAxis valueAxis = new ValueAxis();
		valueAxis.setType(AxisType.value);
		valueAxis.setName(yAxisTitle);
		valueAxis.axisLabel().formatter("{value}");
		op.yAxis().add(valueAxis);
		
		List<Bar> bars = Lists.newArrayList();
		for (int x=0;x<body.length;x++) {
			AbstractBaseCell[] items = body[x];
			Bar bar = new Bar();
			bar.name(obj[x].toString());
			List<String> datas = new ArrayList<String>();
			for (int j = 0; j < items.length; j++) {
				AbstractBaseCell item = items[j];
				if(item instanceof DataCell){
				    String value = item.getFormattedValue();
					datas.add(value==null?"-":value);
				}
			}
			bar.setData(datas);
			bar.setStack("bar");
			bars.add(bar);
		}
		op.series().addAll(bars);
		return op.toString();
	}

	    
	/**
	 * 
	 * @param result
	 * @return
	 */
	public static String buildExchangeBarChartJson(CellDataSet result,String title,String xAxisTitle,String yAxisTitle){
		AbstractBaseCell[][] body = result.getCellSetBody();
		AbstractBaseCell[][] header = result.getCellSetHeaders();
		if(header==null || header.length==0 || body==null || body.length==0)
			return "";
		GsonOption op = new GsonOption();
		op.tooltip().trigger(Trigger.axis);
		//定义工具
		op.toolbox().show(true).feature(
				new MagicType(Magic.line, Magic.bar), Tool.restore,
					Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
				.orient(Orient.horizontal);
    	
		Object[] obj = new Object[body.length];
		int i = 0;
		for (AbstractBaseCell[] items : body) {
			StringBuilder objStr = new StringBuilder(items[0].getFormattedValue());
			int strLength = objStr.length();
			if(strLength>6)
				objStr.insert(strLength/2, "\n");
			obj[i] =objStr.toString();
			i++;
		}
		
		Integer dev = 0;
		for(AbstractBaseCell item:body[0]){
			if(item instanceof MemberCell)
				dev++;
			else
				break;
		}
		String[] titles = new String[header[0].length-dev];
		for (AbstractBaseCell[] item : header) {
			for(int x=dev;x<item.length;x++){
				String nowStr = titles[x-dev];
				if(StringUtils.isNotBlank(nowStr))
					nowStr = nowStr+"~"+item[x].getFormattedValue();
				else
					nowStr = item[x].getFormattedValue();
				titles[x-dev] = nowStr;
			}
		}
		
		op.legend().data(titles);
		op.legend().y(Y.top);
		op.legend().x(X.left);
		op.grid().y(65);
		op.grid().y2("30%");
		op.title().x(X.center);
		op.title(title);
		
		
		//横坐标
		CategoryAxis xAxis = new CategoryAxis();
		xAxis.setData(Arrays.asList(obj));
		xAxis.setType(AxisType.category);
		xAxis.setShow(true);
		xAxis.setName(xAxisTitle);
		AxisLabel xAxisLabel = new AxisLabel();
		xAxisLabel.setRotate(45);
		xAxis.setAxisLabel(xAxisLabel);
		op.xAxis().add(xAxis);
		
//		if(obj.length>12){
//			DataZoom dz = new DataZoom();
//			dz.setShow(true);
//			dz.setStart(0);
//			dz.setEnd(10);
//			op.setDataZoom(dz);
//		}
		
		// 纵坐标
		ValueAxis valueAxis = new ValueAxis();
		valueAxis.setType(AxisType.value);
		valueAxis.setName(yAxisTitle);
		valueAxis.axisLabel().formatter("{value}");
		op.yAxis().add(valueAxis);
		
		List<Bar> bars = Lists.newArrayList();
		Map<Integer,List<String>> map = Maps.newHashMap();
		for (int x=0;x<body.length;x++) {
			AbstractBaseCell[] items = body[x];
			for (int j = dev; j < items.length; j++) {
				List<String> datas = map.get(j);
				if(datas == null)
					datas = new ArrayList<String>();
				AbstractBaseCell item = items[j];
				if(item instanceof DataCell){
				    String value = item.getFormattedValue();
					datas.add(value==null?"-":value);
					map.put(j, datas);
				}
			}
			
		}
		
		for(int x=0;x<map.keySet().size();x++){
			Bar bar = new Bar();
			bar.name(titles[x]);
			bar.setData(map.get(x+dev));
			bar.setStack("bar");
			bars.add(bar);
		}
		op.series().addAll(bars);
		return op.toString();
	}
	
	/**
	 * 清理缓存
	 * @param conn
	 * @return
	 * @throws Exception
	 */
	public static boolean clearCache(OlapConnection conn) throws Exception {
	    if ( conn.isWrapperFor(RolapConnection.class) ) {
	    	logger.info("Clearing cache");
	    	RolapConnection rcon = conn.unwrap(RolapConnection.class);
	    	rcon.getCacheControl( null ).flushSchemaCache();
	    }
	    return true;
	  }


}
