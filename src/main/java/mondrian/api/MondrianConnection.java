package mondrian.api;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.olap4j.OlapConnection;

import com.chis.common.utils.PropertyUtils;
import com.google.common.collect.Maps;

public class MondrianConnection {
	
	public static final Logger logger = Logger.getLogger(MondrianConnection.class);
	
	private Map<String,OlapConnection> oplaoConnectionMap;
	private Map<String,Connection> connectionMap;
	/** 
     * 获取连接Olap的连接 
     * @param url  连接Olap的URL 
     * @return 
     * @throws ClassNotFoundException 
     * @throws SQLException 
     */  
	public MondrianConnection(){
		oplaoConnectionMap = Maps.newHashMap();
		connectionMap = Maps.newHashMap();
		List<String> values = PropertyUtils.getValuesByStartWith("mondrian.name.");
		if(values==null || values.size()==0)
			return;
		for(String value:values){
			try {
				this.getConnection(MondrianUtile.packConnectionUrl(value));
			} catch (ClassNotFoundException | SQLException e) {
				logger.error(value+"链接生成异常！");
			}
		}
	}
	
    public OlapConnection getConnection(String url) throws ClassNotFoundException, SQLException{  
    	OlapConnection olapConnection = oplaoConnectionMap.get(url);
    	if(olapConnection!=null && !olapConnection.isClosed())
    		return olapConnection;
    	Connection connection = connectionMap.get(url);
    	if(connection!=null && !connection.isClosed()){
    		olapConnection = connection.unwrap(OlapConnection.class);
    		oplaoConnectionMap.put(url, olapConnection);
    		return olapConnection;
    	}
        Class.forName("mondrian.olap4j.MondrianOlap4jDriver");  
	    connection = DriverManager.getConnection(url); 
	    olapConnection = connection.unwrap(OlapConnection.class);  
	    connectionMap.put(url, connection);
	    oplaoConnectionMap.put(url, olapConnection);
        return olapConnection;  
    }  
}
