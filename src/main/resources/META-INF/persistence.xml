<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.0" xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd">
	<persistence-unit name="OracleDS" transaction-type="RESOURCE_LOCAL">
		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<properties>
			<property name="hibernate.transaction.flush_before_completion" value="true" />
			<property name="hibernate.dialect" value="com.chis.common.db.Oracle10gDialectPower" />
			<property name="hibernate.cache.provider_class" value="org.hibernate.cache.NoCacheProvider" />
			<property name="hibernate.ejb.interceptor"
					  value="com.chis.modules.system.encrypt.interceptor.EncryptInterceptor" />
			<property name="hibernate.ejb.event.post-insert"
					  value="com.chis.modules.system.encrypt.interceptor.EncryptListener" />
		</properties>
	</persistence-unit>

</persistence>
