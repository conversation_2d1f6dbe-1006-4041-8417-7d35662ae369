log4j.rootLogger=ERROR,logfile

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern= >>>>>>>LOG4J<<<<<<< %r %d [%t] %-p %c %x - #%l# - %m%n

log4j.appender.logfile=org.apache.log4j.RollingFileAppender
log4j.appender.logfile.File=risk.log
log4j.appender.logfile.MaxFileSize=1024KB
log4j.appender.logfile.layout=org.apache.log4j.PatternLayout
log4j.appender.logfile.layout.ConversionPattern= >>>>>>>LOG4J<<<<<<< %r %d %p [%c] - %m%n



