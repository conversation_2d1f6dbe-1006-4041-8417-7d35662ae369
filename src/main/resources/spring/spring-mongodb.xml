<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mongo="http://www.springframework.org/schema/data/mongo"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/data/mongo http://www.springframework.org/schema/data/mongo/spring-mongo-1.8.xsd
        http://www.springframework.org/schema/data/repository
        http://www.springframework.org/schema/data/repository/spring-repository-1.5.xsd">

	<mongo:mongo-client id="mongoClient" host="${mongo.host}" port="${mongo.port}"  credentials="${mongo.credentials}" />
	<!--<mongo:mongo replica-set="${mongo.host.port}" />-->
	<mongo:db-factory dbname="${mongo.dbname}" mongo-ref="mongoClient"/>
	<mongo:template id="mongoTemplate" db-factory-ref="mongoDbFactory"/>
</beans>
