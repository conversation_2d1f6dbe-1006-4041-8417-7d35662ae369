<?xml version="1.0" encoding="UTF-8"?>
<ehcache updateCheck="false" name="defaultCache">

    <diskStore path="../ehcache"/>

    <!-- 默认缓存配置. -->
    <defaultCache maxEntriesLocalHeap="100" eternal="false" timeToIdleSeconds="300" timeToLiveSeconds="600"
                  overflowToDisk="true" maxEntriesLocalDisk="100000"/>

    <!-- 验证字典的缓存， -->
    <cache name="dictCache" maxEntriesLocalHeap="10000" eternal="true" overflowToDisk="true"/>
    <!-- 行政处罚违法事实的缓存 -->
    <cache name="xzcfFactCache" maxEntriesLocalHeap="10000" eternal="true" overflowToDisk="true"/>
    <!-- 行政处罚违法事实对照的缓存 -->
    <cache name="factCodeTransDictCache" maxEntriesLocalHeap="10000" eternal="true" overflowToDisk="true"/>
    <!-- 对照字典的缓存， -->
    <cache name="transCache" maxEntriesLocalHeap="10000" eternal="true" overflowToDisk="true"/>

    <!-- 系统缓存 -->
    <cache name="sysCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 地区缓存 -->
    <cache name="zoneDictCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 地区缓存 -->
    <cache name="zoneGbDictCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 码表缓存，验证对照框架里面使用；码表缓存key:SIM_230001,value:Map<String-字典编码,String-字典中文>;
        普通基础库key:大写表名，value:Map<String-字典编码,String-字典中文> -->
    <cache name="validDictCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 单位缓存-->
    <cache name="unitCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 平台单位缓存-->
    <cache name="orgUnitCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 单位缓存TokenId eternal 数据是否过期是否有效。true：无效，即永不过期，false:有效，可以通过设置timeToLiveSeconds的有效期
        memoryStoreEvictionPolicy:缓存中对象达到最大数量限制操作，默认为LRU[最后使用的]，:FIFO[先进先出]，LFU[使用频率低]
        overflowToDisk：超过maxEntriesLocalHeap最大数量限制是否保存到磁盘 -->
    <cache name="unitTokenIdCache" maxEntriesLocalHeap="1000" timeToLiveSeconds="86400" eternal="false"
           overflowToDisk="true"
           memoryStoreEvictionPolicy="FIFO"/>

    <!-- tokenId计数器，默认24小时，情况计数重来-->
    <cache name="limitTokenIdCache" maxEntriesLocalHeap="1000" timeToLiveSeconds="86400" eternal="false"
           overflowToDisk="true"
           memoryStoreEvictionPolicy="FIFO"/>

    <!-- 用户缓存 -->
    <cache name="userInfoCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 登录用户缓存 -->
    <cache name="loginUserTokenIdCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>


    <!-- ~~~~~~~~~~~~~~~ 学生体检 start ~~~~~~~~~~~~~~~~ -->
    <!-- 体检机构缓存 -->
    <cache name="shBhkOrgCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 学校缓存 -->
    <cache name="shSchoolCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 体检项目 -->
    <cache name="shBhkItmCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 常见病 -->
    <cache name="shDiseaseCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 课桌椅标准 -->
    <cache name="shDeskStd" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 学生体检 end ~~~~~~~~~~~~~~~~~~ -->

    <!-- ~~~~~~~~~~~~~~~ 监测系统 start ~~~~~~~~~~~~~~~~ -->
    <cache name="jcItemCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 监测系统 end ~~~~~~~~~~~~~~~~ -->

    <!-- ~~~~~~~~~~~~~~~ 计免门诊记录上传 start ~~~~~~~~~~~~~~~~ -->
    <cache name="inoUnitCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="ymSortCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="inoDoctorCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 计免门诊记录上传 end ~~~~~~~~~~~~~~~~ -->

    <!-- ~~~~~~~~~~~~~~~ 健康证上传 start ~~~~~~~~~~~~~~~~ -->
    <cache name="jkzUnitCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="jkzHyTypeCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="jkzTjItemsCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 健康证上传 end ~~~~~~~~~~~~~~~~ -->

    <!-- ~~~~~~~~~~~~~~~ 爱国卫生技术评估 start ~~~~~~~~~~~~~~~~ -->
    <cache name="agwsJspgUserCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 爱国卫生技术评估 end ~~~~~~~~~~~~~~~~ -->

    <!-- ~~~~~~~~~~~~~~~ 问卷用户缓存 start ~~~~~~~~~~~~~~~~ -->
    <cache name="slowQueUserInfoCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 问卷用户缓存 end ~~~~~~~~~~~~~~~~ -->

    <!-- ~~~~~~~~~~~~~~~ 卫生监督 start ~~~~~~~~~~~~~~~~ -->
    <cache name="wsjdSupervItem" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="wsjdSupervOptItem" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="wsjdIllfacts" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <cache name="wsjdPnsDecide" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 发送编号缓存 start 有效期一个月 超过内容缓存到磁盘 ~~~~~~~~~~~~~~~~ -->
    <cache name="wsjdSendCodeCache" maxEntriesLocalHeap="10000" timeToLiveSeconds="2592000" eternal="false"
           overflowToDisk="true"/>
    <!-- ~~~~~~~~~~~~~~~ 发送编号缓存 end ~~~~~~~~~~~~~~~~ -->
    <!-- ~~~~~~~~~~~~~~~ 卫生监督 end ~~~~~~~~~~~~~~~~ -->


    <!-- ~~~~~~~~~~~~~~~ 自助检测二维码用户信息存储 ~~~~~~~~~~~~~~~~ -->
    <cache name="zzjcQrcodeTemp" maxEntriesLocalHeap="10000" timeToLiveSeconds="300" eternal="false"
           overflowToDisk="true"/>


    <!-- 省慢病地区缓存 -->
    <cache name="sslowZoneCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

    <!-- 监控缓存内容 -->
    <cache name="monitorCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!--重点职业病全国 重点职业病专项统计项类别 -->
    <cache name="zdzybAnalyItmTypeCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>
    <!-- 江苏放射卫生放射数据接口token缓存 -->
    <cache name="loginTokenIdCache" maxEntriesLocalHeap="1000" eternal="false" overflowToDisk="true"
           timeToLiveSeconds="86400"/>
    <!-- 微信体检预约token缓存 -->
    <cache name="bhkPushCache" maxEntriesLocalHeap="1000" eternal="false" overflowToDisk="true"
           timeToLiveSeconds="86400"/>

    <!-- 武汉万达数据对接转换缓存 -->
    <cache name="contraCache" maxEntriesLocalHeap="10000" eternal="true" overflowToDisk="true"/>

    <!-- 上海体检数据上传工种缓存 -->
    <cache name="tbTjWrknamCache" maxEntriesLocalHeap="1000" eternal="true" overflowToDisk="true"/>

</ehcache>