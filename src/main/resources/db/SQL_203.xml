<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职业卫生平台-质控考核</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_BADRSN_STAND';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_BADRSN_STAND (
                RID                  INTEGER              not null,
                CHECK_TYPE           NUMBER(1),
			    CHECK_NAME           NVARCHAR2(200),
			    STATE_MARK           NUMBER(1),
			    XH                   NUMBER(3),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_BADRSN_STAND primary key (RID) 
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_BADRSN_STAND_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_BADRSN_STAND_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_SCORES';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_SCORES (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    SCORE                NUMBER(5,2)          not null,
			    SPECIAL_FLAG         NUMBER(2),
			    XH                   NUMBER(3),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_SCORES primary key (RID),
                constraint FK_TB_ZW_ZK_SCORES1 foreign key (MAIN_ID)
    					references TB_ZW_ZK_BADRSN_STAND (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_SCORES_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_SCORES_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_SCORE_INDEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_SCORE_INDEX (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    INDEX_ID             INTEGER              not null,
			    INDEX_XH             NVARCHAR2(10),
			    XH                   NUMBER(3),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_SCORE_INDEX primary key (RID),
                constraint FK_TB_ZW_ZK_SCORE_ITEM1 foreign key (MAIN_ID)
    					references TB_ZW_ZK_SCORES (RID),
    			constraint FK_TB_ZW_ZK_SCORE_ITEM2 foreign key (INDEX_ID)
    					references TS_SIMPLE_CODE (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_SCORE_INDEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_SCORE_INDEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_MAIN (
                RID                  INTEGER              not null,
                CHECK_TYPE           NUMBER(1),
			    ORG_ID               INTEGER,
			    CHECK_DATE           DATE,
			    TOTAL_CHECK_VAL      NUMBER(5,2),
			    TOTAL_SCORE_VAL      NUMBER(5,2),
			    STATE_MARK           NUMBER(1),
			    RECORD_ORG_ID        INTEGER,
			    IF_NEED_IMPROVE      NUMBER(1),
			    IF_IMPROVE_END       NUMBER(1),
			    IMPROVE_FILE_NAME    NVARCHAR2(100),
			    IMPROVE_FILE_ADDR    NVARCHAR2(100),
			    WRITE_PATH           NVARCHAR2(100),
			    DEL_MARK             NUMBER(1),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_MAIN primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_MAIN1 foreign key (ORG_ID)
    					references TS_UNIT (RID),
    			constraint FK_TD_ZW_ZK_CHECK_MAIN2 foreign key (RECORD_ORG_ID)
    					references TS_UNIT (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_MAIN_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_TABLE (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    CHECK_TABLE_ID       INTEGER,
			    CHECK_PSN            NVARCHAR2(100),
			    STATE_MARK           NUMBER(1),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_TABLE primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_TABLE1 foreign key (MAIN_ID)
    					references TD_ZW_ZK_CHECK_MAIN (RID),
    			constraint FK_TD_ZW_ZK_CHECK_TABLE2 foreign key (CHECK_TABLE_ID)
    					references TB_ZW_ZK_BADRSN_STAND (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_TABLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_TABLE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_ITEM (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    ITEM_ID              INTEGER,
			    CHECK_VAL            NUMBER(5,2),
			    SCORE_VAL            NUMBER(5,2),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_ITEM primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_ITEM1 foreign key (MAIN_ID)
    					references TD_ZW_ZK_CHECK_TABLE (RID),
    			constraint FK_TD_ZW_ZK_CHECK_ITEM2 foreign key (ITEM_ID)
    					references TS_SIMPLE_CODE (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_ITEM_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_SUB (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    SCORE_ID             INTEGER,
			    SCORE_VAL            NUMBER(5,2),
			    RMK                  NVARCHAR2(2000),
			    TECH_PSN             NUMBER(5),
			    MEDIUM_PSN           NUMBER(5),
			    MEDIUM_PSN_RATE      NUMBER(5,2),
			    EXTERNAL_PSN         NUMBER(5),
			    EXTERNAL_PSN_RATE    NUMBER(5,2),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_SUB primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_SUB1 foreign key (MAIN_ID)
    					references TD_ZW_ZK_CHECK_ITEM (RID),
    			constraint FK_TD_ZW_ZK_CHECK_SUB2 foreign key (SCORE_ID)
    					references TB_ZW_ZK_SCORES (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_SUB_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_SCORE_DEDUCT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_SCORE_DEDUCT (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
   				DEDUCT_ID            INTEGER,
   				XH                   NUMBER(3),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_SCORE_DEDUCT primary key (RID),
                constraint FK_TB_ZW_ZK_SCORE_DEDUCT1 foreign key (MAIN_ID)
    					references TB_ZW_ZK_SCORES (RID),
    			constraint FK_TB_ZW_ZK_SCORE_DEDUCT2 foreign key (DEDUCT_ID)
    					references TS_SIMPLE_CODE (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_SCORE_DEDUCT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_SCORE_DEDUCT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_DEDUCT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_DEDUCT (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
   				DEDUCT_ID            INTEGER,
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_DEDUCT primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_DEDUCT1 foreign key (MAIN_ID)
    					references TD_ZW_ZK_CHECK_SUB (RID),
    			constraint FK_TD_ZW_ZK_CHECK_DEDUCT2 foreign key (DEDUCT_ID)
    					references TB_ZW_ZK_SCORE_DEDUCT (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_DEDUCT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_DEDUCT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_PROVE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_PROVE (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
   				ANNEX_NAME           NVARCHAR2(100),
   				ANNEX_ADDR           NVARCHAR2(100),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_PROVE primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_PROVE1 foreign key (MAIN_ID)
    					references TD_ZW_ZK_CHECK_MAIN (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_PROVE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_PROVE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORES')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES MODIFY SCORE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN3'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_MAIN3 FOREIGN KEY (CHECK_RST_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IF_HG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IF_HG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
     <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORES')
                AND COLUMN_NAME = UPPER('ITEM_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES ADD ITEM_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_SCORES2'
                AND TABLE_NAME = 'TB_ZW_ZK_SCORES';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES ADD CONSTRAINT FK_TB_ZW_ZK_SCORES2 FOREIGN KEY (ITEM_TYPE_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_RST_RULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_RST_RULE (
                RID                  INTEGER              not null,
                CHECK_TYPE           NUMBER(1),
			    CHECK_GRAGE_ID       INTEGER,
			    XH                   NUMBER(3),
			    BAK                  NVARCHAR2(200),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_RST_RULE primary key (RID),
                constraint FK_TB_ZW_ZK_RST_RULE1 foreign key (CHECK_GRAGE_ID)
      				references TS_SIMPLE_CODE (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_RST_RULE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_RST_RULE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_RST_RULE_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_RST_RULE_SUB (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    ITEM_TYPE_ID         INTEGER,
			    SCORE_RST_ID         INTEGER,
			    SCORE_RST2_ID        INTEGER,
			    CAL_TAG              NUMBER(1),
			    CAL_NUM              NUMBER(3),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_RST_RULE_SUB primary key (RID),
                constraint FK_TB_ZW_ZK_RST_RULE_SUB1 foreign key (MAIN_ID)
      				references TB_ZW_ZK_RST_RULE (RID),
      			constraint FK_TB_ZW_ZK_RST_RULE_SUB2 foreign key (ITEM_TYPE_ID)
      				references TS_SIMPLE_CODE (RID),
      			constraint FK_TB_ZW_ZK_RST_RULE_SUB3 foreign key (SCORE_RST2_ID)
      				references TS_SIMPLE_CODE (RID),
      			constraint FK_TB_ZW_ZK_RST_RULE_SUB4 foreign key (SCORE_RST_ID)
      				references TS_SIMPLE_CODE (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_RST_RULE_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_RST_RULE_SUB_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_SUMMARY (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
			    ITEM_TYPE_ID         INTEGER,
			    SCORE_RST_ID         INTEGER,
			    ITEM_NUM             NUMBER(3),
			    CREATE_DATE          TIMESTAMP            not null,
			    CREATE_MANID         INTEGER              not null,
			    MODIFY_DATE          TIMESTAMP,
			    MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_SUMMARY primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_SUMMARY1 foreign key (MAIN_ID)
      				references TD_ZW_ZK_CHECK_MAIN (RID),
      			constraint FK_TD_ZW_ZK_CHECK_SUMMARY2 foreign key (ITEM_TYPE_ID)
      				references TS_SIMPLE_CODE (RID),
      			constraint FK_TD_ZW_ZK_CHECK_SUMMARY3 foreign key (SCORE_RST_ID)
      				references TS_SIMPLE_CODE (RID)
           		)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_SUMMARY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_SUMMARY_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('SCORE_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD SCORE_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_SUB3'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_SUB3 FOREIGN KEY (SCORE_RST_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_BADRSN_STAND')
                AND COLUMN_NAME = UPPER('CHECK_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_BADRSN_STAND ADD CHECK_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_RST_RULE')
                AND COLUMN_NAME = UPPER('CHECK_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_RST_RULE ADD CHECK_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN4'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_MAIN add constraint FK_TD_ZW_ZK_CHECK_MAIN4 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_RST_RULE2'
                AND TABLE_NAME = 'TB_ZW_ZK_RST_RULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_RST_RULE add constraint FK_TB_ZW_ZK_RST_RULE2 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_BADRSN_STAND1'
                AND TABLE_NAME = 'TB_ZW_ZK_BADRSN_STAND';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_BADRSN_STAND add constraint FK_TB_ZW_ZK_BADRSN_STAND1 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORES')
                AND COLUMN_NAME = UPPER('BUS_EXTENDS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES ADD BUS_EXTENDS NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_CHECK_SUMMARY
                (
                   RID                  INTEGER              not null,
                   CHECK_TYPE_ID        INTEGER,
                   UNIT_ID              INTEGER,
                   CHECK_DATE           DATE,
                   CHECK_ADV            NVARCHAR2(1000),
                   IF_CONFIRM           NUMBER(1),
                   FILE_PATH            NVARCHAR2(100),
                   CHECK_EXPERTS        NVARCHAR2(100),
                   CHECK_LEADERS        NVARCHAR2(100),
                   CHECK_UNIT_ID        INTEGER,
                   STATE                NUMBER(1),
                   DEL_MARK             NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_SUMMARY primary key (RID),
                   constraint FK_TD_ZW_CHECK_SUMMARY1 foreign key (CHECK_TYPE_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_CHECK_SUMMARY2 foreign key (UNIT_ID)
                    references TS_UNIT (RID),
                   constraint FK_TD_ZW_CHECK_SUMMARY3 foreign key (CHECK_UNIT_ID)
                    references TS_UNIT (RID)

                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_SUMMARY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_CHECK_SUMMARY_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_TECH_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_CHECK_TECH_MAIN
                (
                   RID                  INTEGER              not null,
                   CHECK_TYPE_ID        INTEGER,
                   UNIT_ID              INTEGER,
                   CHECK_ADDR           NVARCHAR2(100),
                   CHECK_DATE           DATE,
                   CHECK_CONTENT        NVARCHAR2(1000),
                   CHECK_DESC           NVARCHAR2(1000),
                   CHECK_RST            NUMBER(1),
                   CHECK_EXPERTS        NVARCHAR2(100),
                   IF_CONFIRM           NUMBER(1),
                   FILE_PATH            NVARCHAR2(100),
                   EXPERT_ADV           NVARCHAR2(1000),
                   LEADER_ADV           NVARCHAR2(1000),
                   CHECK_UNIT_ID        INTEGER,
                   STATE                NUMBER(1),
                   DEL_MARK             NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_TECH_MAIN primary key (RID),
                   constraint FK_TD_ZW_CHECK_TECH_MAIN1 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_CHECK_TECH_MAIN2 foreign key (UNIT_ID) references TS_UNIT (RID),
                   constraint FK_TD_ZW_CHECK_TECH_MAIN3 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID)
                )';
              END IF;
            END;
        ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TECH_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TECH_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
        ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_TECH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_TECH_PSN
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   PSN_NAME             NVARCHAR2(50),
                   TITLE_ID             INTEGER,
                   PSN_TYPE_ID          INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_TECH_PSN primary key (RID),
                   constraint FK_TD_ZW_CHECK_TECH_PSN1 foreign key (MAIN_ID)
                    references TD_ZW_CHECK_TECH_MAIN (RID),
                   constraint FK_TD_ZW_CHECK_TECH_PSN2 foreign key (TITLE_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_CHECK_TECH_PSN3 foreign key (PSN_TYPE_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
        ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TECH_PSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TECH_PSN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
        ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_TECH_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_TECH_ITEM
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_TECH_ITEM primary key (RID),
                   constraint FK_TD_ZW_CHECK_TECH_ITEM1 foreign key (MAIN_ID)
                    references TD_ZW_CHECK_TECH_MAIN (RID),
                   constraint FK_TD_ZW_CHECK_TECH_ITEM2 foreign key (ITEM_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
        ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TECH_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TECH_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
        ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_CONCLUSION';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_CONCLUSION
                    (
                       RID                  INTEGER              not null,
                       CHECK_TYPE_ID        INTEGER,
                       UNIT_ID              INTEGER,
                       CHECK_DATE           DATE,
                       CHECK_RST            NUMBER(1) default 1,
                       OTHERS               NVARCHAR2(1000),
                       SUMMARY              NVARCHAR2(1000),
                       CONCLUSION_ID        INTEGER,
                       IF_CONFIRM           NUMBER(1) default 1,
                       FILE_PATH            NVARCHAR2(100),
                       EXPERT_ADV           NVARCHAR2(1000),
                       LEADER_ADV           NVARCHAR2(1000),
                       CHECK_UNIT_ID        INTEGER,
                       REL_ZK_CHECK_ID      INTEGER,
                       STATE                NUMBER(1),
                       DEL_MARK             NUMBER(1),
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_CHECK_CONCLUSION primary key (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION1 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION2 foreign key (UNIT_ID) references TS_UNIT (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION3 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION4 foreign key (REL_ZK_CHECK_ID) references TD_ZW_ZK_CHECK_MAIN (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION5 foreign key (CONCLUSION_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_CONCLUSION_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_CONCLUSION_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_ITEM_RST';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_ITEM_RST
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_ID              INTEGER,
                       RST_ID               INTEGER,
                       NUMS                 NUMBER(3),
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_CHECK_ITEM_RST primary key (RID),
                       constraint FK_TD_ZW_CHECK_ITEM_RST1 foreign key (MAIN_ID) references TD_ZW_CHECK_CONCLUSION (RID),
                       constraint FK_TD_ZW_CHECK_ITEM_RST2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZW_CHECK_ITEM_RST3 foreign key (RST_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_ITEM_RST_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_ITEM_RST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_CLS_ITEM';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_CLS_ITEM
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_ID              INTEGER,
                       IF_PASS              NUMBER(1),
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_CHECK_CLS_ITEM primary key (RID),
                       constraint FK_TD_ZW_CHECK_CLS_ITEM1 foreign key (MAIN_ID) references TD_ZW_CHECK_CONCLUSION (RID),
                       constraint FK_TD_ZW_CHECK_CLS_ITEM2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_CLS_ITEM_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_CLS_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_RPT';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_RPT
                        (
                           RID                  INTEGER              not null,
                           CRPT_ID              INTEGER,
                           CRPT_NAME            NVARCHAR2(50),
                           ZONE_ID              INTEGER,
                           CREDIT_CODE          NVARCHAR2(50),
                           ADDRESS              NVARCHAR2(100),
                           INDUS_TYPE_ID        INTEGER,
                           ECONOMY_ID           INTEGER,
                           CRPT_SIZE_ID         INTEGER,
                           LINK_MAN             NVARCHAR2(20),
                           LINK_PHONE           NVARCHAR2(20),
                           WORK_NAME            NVARCHAR2(500),
                           RPT_DATE             DATE,
                           RPT_NO               NVARCHAR2(50),
                           FILE_PATH            NVARCHAR2(100),
                           STATE                NUMBER(1),
                           UNIT_ID              INTEGER,
                           CREATE_MANID         INTEGER              not null,
                           CREATE_DATE          TIMESTAMP            not null,
                           MODIFY_DATE          TIMESTAMP,
                           MODIFY_MANID         INTEGER,
                           constraint PK_TD_ZW_CHECK_RPT primary key (RID),
                           constraint FK_TD_ZW_CHECK_RPT1 foreign key (CRPT_ID)
                             references TB_TJ_CRPT (RID),
                           constraint FK_TD_ZW_CHECK_RPT2 foreign key (ZONE_ID)
                             references TS_ZONE (RID),
                           constraint FK_TD_ZW_CHECK_RPT3 foreign key (INDUS_TYPE_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZW_CHECK_RPT4 foreign key (ECONOMY_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZW_CHECK_RPT5 foreign key (CRPT_SIZE_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZW_CHECK_RPT6 foreign key (UNIT_ID)
                             references TS_UNIT (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_RPT_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_RPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('ORG_FZ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD ORG_FZ NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('ORG_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD ORG_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('JC_RPT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD JC_RPT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN5'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_MAIN add constraint FK_TD_ZW_ZK_CHECK_MAIN5 foreign key (JC_RPT_ID) references TD_ZW_CHECK_RPT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_TABLE';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_TABLE
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               CHECK_TABLE_ID       INTEGER,
               CHECK_PSN            NVARCHAR2(100),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_CHECK_TABLE primary key (RID),
             constraint FK_TD_ZW_CHECK_TABLE1 foreign key (MAIN_ID)
                  references TD_ZW_CHECK_RPT (RID),
            constraint FK_TD_ZW_CHECK_TABLE2 foreign key (CHECK_TABLE_ID)
                  references TB_ZW_ZK_BADRSN_STAND (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TABLE_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TABLE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_ITEM';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_ITEM
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ITEM_ID              INTEGER,
               CHECK_VAL            NUMBER(5,2),
               SCORE_VAL            NUMBER(5,2),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_CHECK_ITEM primary key (RID),
            constraint FK_TD_ZW_CHECK_ITEM1 foreign key (MAIN_ID)
                  references TD_ZW_CHECK_TABLE (RID),
             constraint FK_TD_ZW_CHECK_ITEM2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_ITEM_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_SUB';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_SUB
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SCORE_ID             INTEGER,
                   SCORE_VAL            NUMBER(5,2),
                   RMK                  NVARCHAR2(2000),
                   SCORE_RST_ID         INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_SUB primary key (RID),
                 constraint FK_TD_ZW_CHECK_SUB3 foreign key (SCORE_RST_ID)
                      references TS_SIMPLE_CODE (RID),
                constraint FK_TD_ZW_CHECK_SUB2 foreign key (SCORE_ID)
                      references TB_ZW_ZK_SCORES (RID),
                      constraint FK_TD_ZW_CHECK_SUB1 foreign key (MAIN_ID)
                      references TD_ZW_CHECK_ITEM (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_SUB_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_DEDUCT';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_DEDUCT
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   DEDUCT_ID            INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_DEDUCT primary key (RID),
                 constraint FK_TD_ZW_CHECK_DEDUCT1 foreign key (MAIN_ID)
                      references TD_ZW_CHECK_SUB (RID),
                 constraint FK_TD_ZW_CHECK_DEDUCT2 foreign key (DEDUCT_ID)
                      references TB_ZW_ZK_SCORE_DEDUCT (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_DEDUCT_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_DEDUCT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_CHECK_RPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_RAD_CHECK_RPT
                (
                   RID                  INTEGER              not null,
                   UNIT_ID              INTEGER,
                   CRPT_NAME            NVARCHAR2(50),
                   ZONE_ID              INTEGER,
                   CREDIT_CODE          NVARCHAR2(50),
                   ADDRESS              NVARCHAR2(100),
                   LINK_MAN             NVARCHAR2(20),
                   LINK_PHONE           NVARCHAR2(20),
                   RPT_DATE             DATE,
                   RPT_NO               NVARCHAR2(50),
                   RPT_NAME             NVARCHAR2(100),
                   FILE_PATH            NVARCHAR2(100),
                   SOURCE_FILE_PATH     NVARCHAR2(100),
                   STATE                NUMBER(1),
                   CHECK_UNIT_ID        INTEGER,
                   CHECK_PSN_ID         INTEGER,
                   CHECK_DATE           DATE,
                   BACK_RAN             NVARCHAR2(100),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_RAD_CHECK_RPT primary key (RID),
                   constraint FK_TD_RAD_CHECK_RPT1 foreign key (UNIT_ID) references TS_UNIT (RID),
                   constraint FK_TD_RAD_CHECK_RPT2 foreign key (ZONE_ID) references TS_ZONE (RID),
                   constraint FK_TD_RAD_CHECK_RPT3 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID),
                   constraint FK_TD_RAD_CHECK_RPT4 foreign key (CHECK_PSN_ID) references TS_USER_INFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_CHECK_RPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_RAD_CHECK_RPT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('RPT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD RPT_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('SOURCE_FILE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD SOURCE_FILE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('BACK_RAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD BACK_RAN NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('CHECK_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD CHECK_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('CHECK_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD CHECK_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_CHECK_RPT7'
                AND TABLE_NAME = 'TD_ZW_CHECK_RPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_CHECK_RPT add constraint FK_TD_ZW_CHECK_RPT7 foreign key (CHECK_PSN_ID) references TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
</sqlsentences>