<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
	<description>门户</description>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_LINKS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_LINKS (   RID                  INTEGER              NOT NULL,   LINK_TYPE            NUMBER(1)            NOT NULL,   LINK_NAME            VARCHAR2(200)        NOT NULL,   LINK_ICON            VARCHAR2(200),   LINK_URL             VARCHAR2(200)        NOT NULL,   NUMS                 NUMBER(2)            NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CONSTRAINT PK_TD_PORTAL_LINKS PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>1</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_NEWS_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_NEWS_TYPE (   RID                  INTEGER              NOT NULL,   TYPE_NAME            VARCHAR2(100)        NOT NULL,   PARENT_ID            INTEGER,   TYPE_DESC            VARCHAR2(200),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   XH         NUMBER(3),   CONSTRAINT PK_TD_PORTAL_NEWS_TYPE PRIMARY KEY (RID),   CONSTRAINT FK_TD_PORTAL_NEWS_TYPE FOREIGN KEY (PARENT_ID)      REFERENCES TD_PORTAL_NEWS_TYPE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>2</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_NEWS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_NEWS (   RID                  INTEGER              NOT NULL,   NEWS_TYPE_ID         INTEGER              NOT NULL,   NEWS_DEPT            INTEGER              NOT NULL,   NEWS_TITLE           VARCHAR2(100)        NOT NULL,   NEWS_TYPE            NUMBER(1),   NEWS_ANNEX           VARCHAR2(200),   NEWS_CONT            CLOB,   NEWS_LEVEL           NUMBER(1)            NOT NULL,   IF_NOTICE            NUMBER(1)            NOT NULL,   STATE_MARK           NUMBER(1),   NEWS_DATE            DATE,   NEWS_MAN             INTEGER              NOT NULL,   IF_ZD                NUMBER(1)            not null,   IF_ALL               NUMBER(1)            not null,   NEW_ADR              VARCHAR2(200)        not null,   CONSTRAINT PK_TD_PORTAL_NEWS PRIMARY KEY (RID),   CONSTRAINT FK_TD_PORTAL_NEWS1 FOREIGN KEY (NEWS_TYPE_ID)      REFERENCES TD_PORTAL_NEWS_TYPE (RID),   CONSTRAINT FK_TD_PORTAL_NEWS2 FOREIGN KEY (NEWS_DEPT)      REFERENCES TS_OFFICE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>3</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_NEWS_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_NEWS_ANNEX (   RID                  INTEGER              NOT NULL,   MAIN_ID              INTEGER              NOT NULL,   ANNEX_NAME           VARCHAR2(200)        NOT NULL,   ANNEX_ADDR           VARCHAR2(200)        NOT NULL,   XH                   NUMBER(1)            NOT NULL,   ANNEX_DESC            VARCHAR2(400),   CONSTRAINT PK_TD_PORTAL_NEWS_ANNEX PRIMARY KEY (RID),   CONSTRAINT FK_TD_PORTA_REFERENCE_TD_PORTA FOREIGN KEY (MAIN_ID)      REFERENCES TD_PORTAL_NEWS (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>4</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_COLUMN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_COLUMN (   RID                  INTEGER              NOT NULL,   NEWS_TYPE_ID         INTEGER,   COL_NAME             VARCHAR2(100)        NOT NULL,   COL_TYPE             NUMBER(1)            NOT NULL,   MORE_URL             VARCHAR2(200),   CONSTRAINT PK_TD_PORTAL_COLUMN PRIMARY KEY (RID),   CONSTRAINT FK_TD_PORTAL_COLUMN FOREIGN KEY (NEWS_TYPE_ID)      REFERENCES TD_PORTAL_NEWS_TYPE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>5</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_COLAUTH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_COLAUTH (   RID                  INTEGER              NOT NULL,   COL_ID               INTEGER              NOT NULL,   USER_ID              INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_COLAUTH PRIMARY KEY (RID),   CONSTRAINT AK_AK_TD_PORTAL_COLAU_TD_PORTA UNIQUE (COL_ID, USER_ID),   CONSTRAINT FK_TD_PORTAL_COLAUTH1 FOREIGN KEY (COL_ID)      REFERENCES TD_PORTAL_COLUMN (RID),   CONSTRAINT FK_TD_PORTAL_COLAUTH2 FOREIGN KEY (USER_ID)      REFERENCES TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>6</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROTAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PROTAL (   RID                  INTEGER              NOT NULL,   PORTAL_NAME          VARCHAR2(100)        NOT NULL,   PORTAL_TYPE          NUMBER(1)     NOT NULL,   TEMPL_TYPE           NUMBER(2),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   XH NUMBER(2),   CONSTRAINT PK_TD_PROTAL PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>7</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_LAYOUT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_LAYOUT (   RID                  INTEGER              NOT NULL,   COL_ID               INTEGER              NOT NULL,   PORTAL_ID            INTEGER              NOT NULL,   POS                  NUMBER(1),   NUMS                 NUMBER(1),   LINES                NUMBER(2),   DISP_DATE            NUMBER(1)            NOT NULL,   IF_SCROLL            NUMBER(1),   HEIGHTH              NUMBER(4),   WORDS                NUMBER(3),   CONSTRAINT PK_TD_PORTAL_LAYOUT PRIMARY KEY (RID),   CONSTRAINT FK_TD_PORTAL_LAYOUT2 FOREIGN KEY (COL_ID)      REFERENCES TD_PORTAL_COLUMN (RID),   CONSTRAINT FK_TD_PORTAL_LAYOUT1 FOREIGN KEY (PORTAL_ID)      REFERENCES TD_PROTAL (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>8</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_OFFICE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_OFFICE (   RID                  INTEGER              NOT NULL,   PORTAL_ID            INTEGER              NOT NULL,   OFFICE_ID            INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_OFFICE PRIMARY KEY (RID),   CONSTRAINT AK_AK_TD_PORTAL_OFFIC_TD_PORTA UNIQUE (PORTAL_ID, OFFICE_ID),   CONSTRAINT FK_TD_PORTAL_OFFICE1 FOREIGN KEY (PORTAL_ID)      REFERENCES TD_PROTAL (RID),    CONSTRAINT FK_TD_PORTAL_OFFICE2 FOREIGN KEY (OFFICE_ID)      REFERENCES TS_OFFICE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>9</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_USER';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_USER (   RID                  INTEGER              NOT NULL,   PORTAL_ID            INTEGER              NOT NULL,   USER_ID              INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_USER PRIMARY KEY (RID),   CONSTRAINT AK_AK_TD_PORTAL_USER_TD_PORTA UNIQUE (PORTAL_ID, USER_ID),   CONSTRAINT FK_TD_PORTAL_USER1 FOREIGN KEY (PORTAL_ID)      REFERENCES TD_PROTAL (RID),   CONSTRAINT FK_TD_PORTAL_USER2 FOREIGN KEY (USER_ID)      REFERENCES TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>10</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_GROUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_GROUP (   RID                  INTEGER              NOT NULL,   PORTAL_ID            INTEGER              NOT NULL,   GROUP_ID             INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_GROUP PRIMARY KEY (RID),   CONSTRAINT AK_AK_TD_PORTAL_GROUP_TD_PORTA UNIQUE (PORTAL_ID, GROUP_ID),   CONSTRAINT FK_TD_PORTAL_GROUP1 FOREIGN KEY (PORTAL_ID)      REFERENCES TD_PROTAL (RID),   CONSTRAINT FK_TD_PORTAL_GROUP2 FOREIGN KEY (GROUP_ID)      REFERENCES TS_GROUP (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>11</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_LINKS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_LINKS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>12</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_NEWS_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_NEWS_TYPE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>13</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_NEWS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_NEWS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>14</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_NEWS_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_NEWS_ANNEX_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>15</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_COLUMN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_COLUMN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>16</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_COLAUTH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_COLAUTH_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>17</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROTAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PROTAL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>18</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_LAYOUT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_LAYOUT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>19</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_OFFICE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_OFFICE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>20</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_USER_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_USER_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>21</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_GROUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_GROUP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>22</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_TYP_AUTH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_TYP_AUTH (   RID                  INTEGER              NOT NULL,   NEWS_ID              INTEGER              NOT NULL,   USER_ID              INTEGER              NOT NULL,   IF_ADMIN             NUMBER(1)            NOT NULL,   CONSTRAINT PK_TD_PORTAL_TYP_AUTH PRIMARY KEY (RID),   CONSTRAINT AK_TD_PORTAL_TYP_AUTH UNIQUE (NEWS_ID, USER_ID),   CONSTRAINT FK_TD_PORTAL_TYP_AUTH1 FOREIGN KEY (NEWS_ID) REFERENCES TD_PORTAL_NEWS_TYPE(RID),   CONSTRAINT FK_TD_PORTAL_TYP_AUTH2 FOREIGN KEY (USER_ID) REFERENCES TS_USER_INFO(RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>23</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_NEWS_AUTH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_NEWS_AUTH (   RID                  INTEGER              NOT NULL,   NEWS_ID              INTEGER              NOT NULL,   USER_ID              INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_NEWS_AUTH PRIMARY KEY (RID),   CONSTRAINT AK_TD_PORTAL_NEWS_AUTH UNIQUE (NEWS_ID, USER_ID),   CONSTRAINT FK_TD_PORTAL_NEWS_AUTH1 FOREIGN KEY (NEWS_ID) REFERENCES TD_PORTAL_NEWS(RID),   CONSTRAINT FK_TD_PORTAL_NEWS_AUTH2 FOREIGN KEY (USER_ID) REFERENCES TS_USER_INFO(RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>24</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_TYP_AUTH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_TYP_AUTH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>25</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_NEWS_AUTH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_NEWS_AUTH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>26</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            MERGE INTO TD_PROTAL
            USING (
              SELECT 1 AS PORTAL_TYPE
              FROM DUAL
            ) DUALTAB ON (TD_PROTAL.PORTAL_TYPE = DUALTAB.PORTAL_TYPE) 
            WHEN NOT MATCHED THEN INSERT (RID, PORTAL_NAME, PORTAL_TYPE, CREATE_DATE, CREATE_MANID) VALUES (TD_PROTAL_SEQ.NEXTVAL, '个人工作站', 1, SYSDATE, 1)
          ]]>
		</sql>
		<ver>27</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            MERGE INTO TD_PORTAL_COLUMN
            USING (
              SELECT 1 AS COL_TYPE
              FROM DUAL
            ) DUALTAB ON (TD_PORTAL_COLUMN.COL_TYPE = DUALTAB.COL_TYPE) 
            WHEN NOT MATCHED THEN INSERT (RID, COL_NAME, COL_TYPE) VALUES (TD_PORTAL_COLUMN_SEQ.NEXTVAL, '图片新闻', 1)
          ]]>
		</sql>
		<ver>28</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            MERGE INTO TD_PORTAL_COLUMN
            USING (
              SELECT 2 AS COL_TYPE
              FROM DUAL
            ) DUALTAB ON (TD_PORTAL_COLUMN.COL_TYPE = DUALTAB.COL_TYPE) 
            WHEN NOT MATCHED THEN INSERT (RID, COL_NAME, COL_TYPE) VALUES (TD_PORTAL_COLUMN_SEQ.NEXTVAL, '公告栏', 2)
          ]]>
		</sql>
		<ver>29</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            MERGE INTO TD_PORTAL_COLUMN
            USING (
              SELECT 3 AS COL_TYPE
              FROM DUAL
            ) DUALTAB ON (TD_PORTAL_COLUMN.COL_TYPE = DUALTAB.COL_TYPE) 
            WHEN NOT MATCHED THEN INSERT (RID, COL_NAME, COL_TYPE) VALUES (TD_PORTAL_COLUMN_SEQ.NEXTVAL, '常用链接', 3)
          ]]>
		</sql>
		<ver>30</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PROTAL')
                AND COLUMN_NAME = UPPER('USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROTAL ADD USER_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>31</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PERSONAL_SETTING';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '       CREATE TABLE TS_PERSONAL_SETTING (   RID                  INTEGER              NOT NULL,   USER_INFO_ID         INTEGER              NOT NULL,   DEFAULT_URL          VARCHAR2(100),   DEFAULT_SKIN         VARCHAR2(50),   DEFAULT_URL_TYPE NUMBER(1) NOT NULL,   CONSTRAINT PK_TS_PERSONAL_SETTING PRIMARY KEY (RID),   CONSTRAINT AK_AK_TS_PERSONAL_SET_TS_PERSO UNIQUE (USER_INFO_ID),   CONSTRAINT FK_TS_PERSONAL_SETTING FOREIGN KEY (USER_INFO_ID)      REFERENCES TS_USER_INFO (RID))        ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>32</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PERSONAL_SETTING_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PERSONAL_SETTING_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>33</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_COLUMN')
                AND COLUMN_NAME = UPPER('COL_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_COLUMN ADD COL_CODE VARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>34</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS')
                AND COLUMN_NAME = UPPER('ROLL_DAYS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS ADD ROLL_DAYS INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>35</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS_ANNEX')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS_ANNEX MODIFY (XH NUMBER(3))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>36</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS_TYPE')
                AND COLUMN_NAME = UPPER('TYPE_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS_TYPE ADD TYPE_CODE VARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>37</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS_TYPE')
                AND COLUMN_NAME = UPPER('ZD_CONTS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS_TYPE ADD ZD_CONTS NUMBER(2) NOT NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>38</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS_AUTH')
                AND COLUMN_NAME = UPPER('READ_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS_AUTH ADD READ_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>39</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS_AUTH')
                AND COLUMN_NAME = UPPER('READ_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS_AUTH ADD READ_TIME DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>40</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_LAYOUT')
                AND COLUMN_NAME = UPPER('NUMS');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_LAYOUT MODIFY NUMS NUMBER(3)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>41</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_NEWS_OFFICE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_NEWS_OFFICE (   RID                  INTEGER              NOT NULL,   NEWS_ID              INTEGER              NOT NULL,   OFFICE_ID            INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_NEWS_OFFICE PRIMARY KEY (RID),   CONSTRAINT FK_TD_PORTAL_NEWS_OFFICE1 FOREIGN KEY (NEWS_ID)      REFERENCES TD_PORTAL_NEWS (RID),   CONSTRAINT FK_TD_PORTAL_NEWS_OFFICE2 FOREIGN KEY (OFFICE_ID)      REFERENCES TS_OFFICE (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>42</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_NEWS_OFFICE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_NEWS_OFFICE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>43</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_COLUMN')
                AND COLUMN_NAME = UPPER('IF_HIDE_HEADER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_COLUMN ADD IF_HIDE_HEADER NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>44</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_COLUMN')
                AND COLUMN_NAME = UPPER('NEED_SOCKET_UPDATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_COLUMN ADD NEED_SOCKET_UPDATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>45</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PORTAL_NEWS_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_PORTAL_NEWS_UNIT (   RID                  INTEGER              NOT NULL,   NEWS_ID              INTEGER              NOT NULL,   UNIT_ID            INTEGER              NOT NULL,   CONSTRAINT PK_TD_PORTAL_NEWS_UNIT PRIMARY KEY (RID),   CONSTRAINT PK_TD_PORTAL_NEWS_UNIT1 FOREIGN KEY (NEWS_ID)      REFERENCES TD_PORTAL_NEWS (RID),   CONSTRAINT PK_TD_PORTAL_NEWS_UNIT2 FOREIGN KEY (UNIT_ID)      REFERENCES TS_UNIT (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>46</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PORTAL_NEWS_UNIT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PORTAL_NEWS_UNIT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>47</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS')
                AND COLUMN_NAME = UPPER('NEWS_DEPT')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS MODIFY NEWS_DEPT INTEGER NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>48</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS')
                AND COLUMN_NAME = UPPER('NEWS_UNIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS ADD NEWS_UNIT INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>49</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PORTAL_NEWS3'
                AND TABLE_NAME = 'TD_PORTAL_NEWS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS ADD CONSTRAINT FK_TD_PORTAL_NEWS3 FOREIGN KEY (NEWS_UNIT)      REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>50</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_PORTAL_NEWS')
                AND COLUMN_NAME = UPPER('NEWS_ANNEX');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PORTAL_NEWS MODIFY NEWS_ANNEX VARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>51</ver>
	</sqlsentence>
</sqlsentences>
<!-- web-system 系统门户相关升级 -->