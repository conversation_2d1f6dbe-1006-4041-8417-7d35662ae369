<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
	<description>通用</description>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('USERADMIN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD USERADMIN NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>1</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            UPDATE TS_USER_INFO
            SET USERADMIN = 0
            WHERE USERADMIN IS NULL
          ]]>
		</sql>
		<ver>2</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDAT<PERSON>[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_GROUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_GROUP (   RID                  INTEGER              NOT NULL,   UNIT_RID             INTEGER              NOT NULL,   GROUP_NAME           VARCHAR2(200)        NOT NULL,   XH                   NUMBER(2)            NOT NULL,   IF_REVEAL            NUMBER(1)            DEFAULT 1 NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CONSTRAINT PK_TS_GROUP PRIMARY KEY (RID),   CONSTRAINT FK_TS_GROUP FOREIGN KEY (UNIT_RID) REFERENCES TS_UNIT(RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>3</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_USER_GROUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_USER_GROUP (   RID                  INTEGER              NOT NULL,   GROUP_ID             INTEGER              NOT NULL,   USER_INFO_ID         INTEGER              NOT NULL,   CONSTRAINT PK_TS_USER_GROUP PRIMARY KEY (RID),   CONSTRAINT FK_TS_USER_GROUP1 FOREIGN KEY (GROUP_ID) REFERENCES TS_GROUP(RID),   CONSTRAINT FK_TS_USER_GROUP2 FOREIGN KEY (USER_INFO_ID) REFERENCES TS_USER_INFO(RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>4</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_GROUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_GROUP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>5</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_USER_GROUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_USER_GROUP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>6</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CODE_TYPE')
                AND COLUMN_NAME = UPPER('NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CODE_TYPE ADD NUM INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>7</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_ROLE_CODEAUTH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '  CREATE TABLE TS_ROLE_CODEAUTH  (   RID                  INTEGER              NOT NULL,   ROLE_ID              INTEGER              NOT NULL,   CODE_TYPEID          INTEGER              NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_ROLE_CODEAUTH PRIMARY KEY (RID),   CONSTRAINT AK_ROLE_CODEAUTH UNIQUE (ROLE_ID, CODE_TYPEID),   CONSTRAINT FK_TS_ROLE_CODEAUTH1 FOREIGN KEY (ROLE_ID) REFERENCES TS_ROLE(RID),   CONSTRAINT FK_TS_ROLE_CODEAUTH2 FOREIGN KEY (CODE_TYPEID) REFERENCES TS_CODE_TYPE(RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>8</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_ROLE_CODEAUTH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_ROLE_CODEAUTH_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>9</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('CODE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD CODE_PATH VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>10</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('PUBLISH_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD PUBLISH_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>11</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PARTTIME_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_PARTTIME_INFO (RID                  INTEGER              NOT NULL,EMP_ID               INTEGER              NOT NULL,OFFICE_ID            INTEGER              NOT NULL,DUTY_ID              INTEGER              NOT NULL,IS_LEADER            NUMBER(1)            NOT NULL,CONSTRAINT PK_TS_PARTTIME_INFO PRIMARY KEY (RID),CONSTRAINT FK_TS_PARTTIME_INFO1 FOREIGN KEY (EMP_ID) REFERENCES TB_SYS_EMP (RID),CONSTRAINT FK_TS_PARTTIME_INFO2 FOREIGN KEY (OFFICE_ID) REFERENCES TS_OFFICE (RID),CONSTRAINT FK_TS_PARTTIME_INFO3 FOREIGN KEY (DUTY_ID) REFERENCES TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>12</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PARTTIME_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE ' CREATE SEQUENCE TS_PARTTIME_INFO_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>13</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_TXTYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_TXTYPE (   RID                  INTEGER              NOT NULL,   TYPE_CODE            VARCHAR2(20)         NOT NULL,   TYPES                NUMBER(1)            NOT NULL,   IMPL_CLASS           VARCHAR2(200),   XH          NUMBER(2),   STATUS               NUMBER(1)            NOT NULL,   CONSTRAINT PK_TS_TXTYPE PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>14</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_TXRPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_TXRPT (   RID                  INTEGER              NOT NULL,   TX_ID                INTEGER              NOT NULL,   MSG_ID               VARCHAR2(100),   SEND_STATUS          NUMBER(1),   RECEIVE_STATUS       NUMBER(1),   RECEIVE_TIME         DATE    ,   MSG_CONT             VARCHAR2(2000),   CONSTRAINT PK_TS_TXRPT PRIMARY KEY (RID),   CONSTRAINT FK_TS_TXRPT FOREIGN KEY (TX_ID)      REFERENCES TS_TXTYPE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>15</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_TXRPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_TXRPT_SEQ          MINVALUE 0          MAXVALUE 9999999999999999999999999          START WITH 1          INCREMENT BY 1          CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>16</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_TXTYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_TXTYPE_SEQ          MINVALUE 0          MAXVALUE 9999999999999999999999999          START WITH 1          INCREMENT BY 1          CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>17</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_OFFICE')
                AND COLUMN_NAME = UPPER('IS_YJOFFICE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_OFFICE ADD IS_YJOFFICE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>18</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_SYS_EMP')
                AND COLUMN_NAME = UPPER('PSN_PROP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD PSN_PROP INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>19</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_SYS_EMP2'
                AND TABLE_NAME = 'TB_SYS_EMP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD CONSTRAINT FK_TB_SYS_EMP2 FOREIGN KEY (PSN_PROP)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>20</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_SYS_EMP')
                AND COLUMN_NAME = UPPER('PSN_SIGN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD PSN_SIGN VARCHAR2(500) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>21</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXRPT')
                AND COLUMN_NAME = UPPER('MSG_ERR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXRPT ADD MSG_ERR VARCHAR2(100) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>22</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXRPT')
                AND COLUMN_NAME = UPPER('MSG_SECONDS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXRPT ADD MSG_SECONDS INTEGER ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>23</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_QUARTZ_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_QUARTZ_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>24</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_QUARTZ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_QUARTZ (   RID                  INTEGER              NOT NULL,   PARAM_TYPE           NUMBER(2)            DEFAULT 0 NOT NULL,   TASK_CODE            VARCHAR2(50)         NOT NULL,   TASK_CLASS           VARCHAR2(200)        NOT NULL,   TASK_DESCR           VARCHAR2(200)        NOT NULL,   PERIOD_KIND          NUMBER(1)            NOT NULL,   PERIOD_TYPE          NUMBER(1),   DAY_OF_MON           NUMBER(2),   DAY_OF_WEEK          NUMBER(1),   HOUR_OF_DAY          NUMBER(2),   MIN_OF_DAY           NUMBER(2),   INTERVAL_HOUR        INTEGER,   INTERVAL_MIN         INTEGER,   EXPRESSIONS          VARCHAR2(50),   IF_REVEAL            NUMBER(1)            DEFAULT 1 NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TS_QUARTZ PRIMARY KEY (RID),   CONSTRAINT AK_TS_QUARTZ UNIQUE (TASK_CODE))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>25</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXRPT')
                AND COLUMN_NAME = UPPER('TAR_USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXRPT ADD TAR_USER_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>26</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_RPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_RPT (   RID                  INTEGER              NOT NULL,   PARAM_TYPE           NUMBER(2),   RPTCOD               VARCHAR2(50)         NOT NULL,   RPTNAM               VARCHAR2(100)         NOT NULL,   RPTPATH              VARCHAR2(500),   RPTVRE               INTEGER              DEFAULT 1 NOT NULL,   RMK                  VARCHAR2(200)        NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TS_RPT PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>27</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_RPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_RPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>28</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TEMPMETA_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TEMPMETA_TYPE (   RID                  INTEGER              NOT NULL,   PARAM_TYPE           NUMBER(2)            default 0 not null,   TEMP_CODE            VARCHAR2(50)         not null,   RMK                  VARCHAR2(200)        not null,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   constraint PK_TD_TEMPMETA_TYPE primary key (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>29</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TEMPMETA_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TEMPMETA_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>30</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TEMPMETA_DEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TEMPMETA_DEFINE (   RID                  INTEGER              NOT NULL,   TYPE_ID              INTEGER              not null,   TEMP_CONT            VARCHAR2(2000)       not null,   IF_DEFAULT           NUMBER(1)            not null,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_TEMPMETA_DEFINE primary key (RID),   constraint FK_PK_TD_TEMPMETA_DEFINE foreign key (TYPE_ID) references TD_TEMPMETA_TYPE (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>31</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TEMPMETA_DEFINE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TEMPMETA_DEFINE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>32</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_RPT')
                AND COLUMN_NAME = UPPER('RPTPATH')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_RPT MODIFY RPTPATH VARCHAR2(500) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>33</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_MS_TEMPLATE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_MS_TEMPLATE (   RID                  INTEGER              not null,   FILE_NAME            VARCHAR2(200)        not null,   FILE_TYPE            NUMBER(1)            not null,   FILE_SIZE            INTEGER,   FILE_PATH            VARCHAR2(500),   FILE_DESC            VARCHAR2(500),   XH                   INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   RECORDID             VARCHAR2(50)         not null,   constraint PK_TD_MS_TEMPLATE primary key (RID),   constraint AK_AK_TD_MS_TEMPLATE_TD_MS_TE unique (RECORDID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>34</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_MS_TEMPLATE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_MS_TEMPLATE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>35</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_MS_BOOKMARK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_MS_BOOKMARK (   RID                  INTEGER              not null,   TEMPLATE_ID          INTEGER              not null,   BOOKMARK_NAME        VARCHAR2(200)        not null,   BOOKMARK_DESC        VARCHAR2(200)        not null,   BOOKMARK_KEY         VARCHAR2(100)        not null,   XH                   INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   RECORDID             VARCHAR2(50)         not null,   constraint PK_TD_MS_BOOKMARK primary key (RID),   constraint AK_AK_TD_MS_BOOKMARK_TD_MS_BO2  unique (BOOKMARK_NAME,TEMPLATE_ID),   constraint FK_TD_MS_BOOKMARK foreign key (TEMPLATE_ID)  references TD_MS_TEMPLATE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>36</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_MS_BOOKMARK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_MS_BOOKMARK_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>37</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_AK_TD_MS_BOOKMARK_TD_MS_BO'
                AND TABLE_NAME = 'TD_MS_BOOKMARK';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MS_BOOKMARK DROP CONSTRAINT  AK_AK_TD_MS_BOOKMARK_TD_MS_BO';
              END IF;
            END;
          ]]>
		</sql>
		<ver>38</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TD_MS_BOOKMARK'
                AND TABLE_NAME = 'TD_MS_BOOKMARK';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MS_BOOKMARK DROP CONSTRAINT  AK_TD_MS_BOOKMARK';
              END IF;
            END;
          ]]>
		</sql>
		<ver>39</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_AK_TD_MS_BOOKMARK_TD_MS_BO2'
                AND TABLE_NAME = 'TD_MS_BOOKMARK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MS_BOOKMARK ADD CONSTRAINT  AK_AK_TD_MS_BOOKMARK_TD_MS_BO2  unique (BOOKMARK_NAME,TEMPLATE_ID) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>40</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              num number;
            BEGIN
              SELECT count(1)
              INTO num
              FROM user_tables
              WHERE table_name = 'TS_MENU_BTN';
              IF num = 0 THEN
                EXECUTE IMMEDIATE 'create table TS_MENU_BTN (   RID                  INTEGER              not null,   MENU_TEMPLATE_ID     INTEGER              not null,   BTN_CODE             VARCHAR2(100)        not null,   BTN_NAME             VARCHAR2(50)         not null,   IF_REVEAL            NUMBER(1)            default 1 not null,   constraint PK_TS_MENU_BTN primary key (RID),   constraint AK_TS_MENU_BTN unique (BTN_CODE),   constraint FK_TS_MENU_BTN foreign key (MENU_TEMPLATE_ID) references TS_MENU(RID)    )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>41</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TS_MENU_BTN_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TS_MENU_BTN_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 3000 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>42</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              num number;
            BEGIN
              SELECT count(1)
              INTO num
              FROM user_tables
              WHERE table_name = 'TS_USER_BTN';
              IF num = 0 THEN
                EXECUTE IMMEDIATE 'create table TS_USER_BTN (   RID                  INTEGER              not null,   USER_INFO_ID         INTEGER              not null,   BTN_ID               INTEGER              not null,   constraint PK_TS_USER_BTN primary key (RID),   constraint FK_TS_USER_BTN1 foreign key (USER_INFO_ID) references TS_USER_INFO(RID),   constraint FK_TS_USER_BTN2 foreign key (BTN_ID) references TS_MENU_BTN(RID)   )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>43</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TS_USER_BTN_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TS_USER_BTN_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 3000 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>44</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_MENU_BTN')
                AND COLUMN_NAME = UPPER('LEVEL_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_MENU_BTN ADD LEVEL_NO VARCHAR2(30)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>45</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXRPT')
                AND COLUMN_NAME = UPPER('MOBILE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXRPT ADD MOBILE VARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>46</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD NUM INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>47</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('MB_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD MB_NUM VARCHAR2(13)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>48</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_OA_SCHEDULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_OA_SCHEDULE (   RID                  INTEGER              not null,   SCHEDULE_TITLE       VARCHAR2(200)        not null,   SCHEDULE_TXT         CLOB,   BEGIN_TIME           DATE,   END_TIME             DATE,   PUBLIC_TYPE          NUMBER(1)            not null,   PUBLIC_SCOPE         CLOB,   SCHEDULE_TYPE        INTEGER              not null,   IS_REMIND            NUMBER(1)            not null,   SEASONAL             NUMBER(1)            not null,   REMIND_TIME          INTEGER,   REMIND_DATETIME      DATE,   PERIOD_TYPE          NUMBER(1),   DAY_OF_MON           NUMBER(2),   DAY_OF_WEEK          NUMBER(1),   HOUR_OF_DAY          NUMBER(2),   MIN_OF_DAY           NUMBER(2),   EXPRESSIONS          VARCHAR2(50),   REMIND_MTD           VARCHAR2(200),   EXECUTE_MAN_ID       INTEGER              not null,   ARRANGE_MAN_ID       INTEGER,   LINK_URL             VARCHAR2(500),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_OA_SCHEDULE primary key (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>49</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_OA_SCHEDULE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_OA_SCHEDULE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>50</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('ZONE_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD ZONE_CODE VARCHAR2(12)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>51</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_ROLE_BTN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_ROLE_BTN (   RID                  INTEGER              NOT NULL,   ROLE_ID              INTEGER              not null,   BTN_ID               INTEGER              not null,   constraint PK_TS_ROLE_BTN primary key (RID),   constraint FK_TS_ROLE_BTN1 foreign key (BTN_ID) references TS_MENU_BTN (RID),    constraint FK_TS_ROLE_BTN2 foreign key (ROLE_ID) references TS_ROLE (RID) )    ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>52</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_ROLE_BTN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE ' CREATE SEQUENCE TS_ROLE_BTN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>53</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('REG_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD REG_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>54</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('CODE_LEVEL_NO');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY CODE_LEVEL_NO VARCHAR2(200) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>55</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('FULL_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD FULL_NAME VARCHAR2(200) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>56</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FORM_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FORM_DEF (  RID  NUMBER(18)    NOT NULL,  TYPE_ID  NUMBER(18)    NOT NULL,  FORM_CODE  VARCHAR2(50)    NOT NULL,  TABLE_ID  NUMBER(18)    NOT NULL,  FORM_NAME  VARCHAR2(200)    NOT NULL,  STATE  NUMBER(1)    NOT NULL,  CREATE_DATE  TIMESTAMP(6)    NOT NULL,  CREATE_MANID  NUMBER(18)    NOT NULL,  PRT_TPL_CODE  VARCHAR2(50),  HTML  CLOB,  CONSTRAINT PK_TD_FORM_DEF PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>57</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FORM_FIELD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FORM_FIELD (  RID  NUMBER(18)    NOT NULL,  TABLE_ID  NUMBER(18)    NOT NULL,  FD_CNNAME  VARCHAR2(100)    NOT NULL,  FD_ENNAME  VARCHAR2(20)    NOT NULL,  FD_DBTYPE  VARCHAR2(50)    NOT NULL,  LEN_CHAR  NUMBER(18)    NOT NULL,  LEN_INT  NUMBER(18)    NOT NULL,  LEN_DEMI  NUMBER(18)    NOT NULL,  IS_REQ  NUMBER(1)    NOT NULL,  IS_LIST  NUMBER(1)    NOT NULL,  IS_SHOW  NUMBER(1)    NOT NULL,  ROW_NUM  NUMBER(18),  COL_NUM  NUMBER(18),  DATA_SRC  VARCHAR2(100),  CODE_TYPE_NO  VARCHAR2(50),  DATA_SCRIPT  VARCHAR2(2000),  IS_PRO_VAL  NUMBER(1)    NOT NULL,  COL_SPAN  NUMBER(1)    NOT NULL,  CONSTRAINT PK_TD_FORM_FIELD PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>58</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FORM_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FORM_TABLE (  RID  NUMBER(18)    NOT NULL,  CN_NAME  VARCHAR2(100)    NOT NULL,  EN_NAME  VARCHAR2(20)    NOT NULL,  FORM_PROP  NUMBER(1)    NOT NULL,  MAIN_TAB_ID  NUMBER(18),  FK_FIELD  VARCHAR2(20),  STATE  NUMBER(1)    NOT NULL,  CONSTRAINT PK_TD_FORM_TABLE PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>59</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FORM_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FORM_TYPE (  RID  NUMBER(18)    NOT NULL,  TYPE_CODE  VARCHAR2(50)    NOT NULL,  TYPE_NAME  VARCHAR2(100)    NOT NULL,  CREATE_DATE  TIMESTAMP(6)    NOT NULL,  CREATE_MANID  NUMBER(18)    NOT NULL,  CONSTRAINT PK_TD_FORM_TYPE PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>60</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TD_FORM_TYPE'
                AND TABLE_NAME = 'TD_FORM_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_TYPE ADD CONSTRAINT AK_TD_FORM_TYPE UNIQUE (TYPE_CODE)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>61</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FORM_TABLE1'
                AND TABLE_NAME = 'TD_FORM_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_TABLE ADD CONSTRAINT FK_TD_FORM_TABLE1 FOREIGN KEY (MAIN_TAB_ID) REFERENCES TD_FORM_TABLE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>62</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FORM_FIELD1'
                AND TABLE_NAME = 'TD_FORM_FIELD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD CONSTRAINT FK_TD_FORM_FIELD1 FOREIGN KEY (TABLE_ID) REFERENCES TD_FORM_TABLE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>63</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FORM_DEF2'
                AND TABLE_NAME = 'TD_FORM_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_DEF ADD CONSTRAINT FK_TD_FORM_DEF2 FOREIGN KEY (TABLE_ID) REFERENCES TD_FORM_TABLE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>64</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FORM_DEF1'
                AND TABLE_NAME = 'TD_FORM_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_DEF ADD CONSTRAINT FK_TD_FORM_DEF1 FOREIGN KEY (TYPE_ID) REFERENCES TD_FORM_TYPE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>65</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FORM_DEF_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FORM_DEF_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>66</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FORM_FIELD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FORM_FIELD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>67</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FORM_TABLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FORM_TABLE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>68</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FORM_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FORM_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>69</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('QUERY_SQL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD QUERY_SQL VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>70</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('SCRIPT_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD SCRIPT_TYPE NUMBER(1) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>71</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_SYS_EMP')
                AND COLUMN_NAME = UPPER('PROF_LEVELID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD PROF_LEVELID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>72</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_SYS_EMP')
                AND COLUMN_NAME = UPPER('WORK_YEARS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD WORK_YEARS NUMBER(2)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>73</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_SYS_EMP')
                AND COLUMN_NAME = UPPER('NEWCHIL_INOC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD NEWCHIL_INOC NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>74</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('LNG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD LNG VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>75</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('LAT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD LAT VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>76</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('DSF_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD DSF_CODE  VARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>77</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PROB_LIB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_PROB_LIB (  RID  NUMBER(18)    NOT NULL,  UNIT_ID  NUMBER(18),  QUEST_SORTID  NUMBER(18)    NOT NULL,  QUEST_NAME  VARCHAR2(100)    NOT NULL,  RMK  VARCHAR2(2000),  NUM  NUMBER(18),  BACK_IMAGE  VARCHAR2(200),  STATE  NUMBER(1)    NOT NULL,  HTML_NAME  VARCHAR2(50),  CREATE_DATE  TIMESTAMP(6)    NOT NULL,  CREATE_MANID  NUMBER(18)    NOT NULL,  CONSTRAINT PK_TS_PROB_LIB PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>78</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PROB_SUBJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_PROB_SUBJECT (  RID  NUMBER(18)    NOT NULL,  QUESTLIB_ID  NUMBER(18)    NOT NULL,  SHOW_CODE  VARCHAR2(20)    NOT NULL,  QES_CODE  VARCHAR2(20)    NOT NULL,  QES_LEVEL_CODE  VARCHAR2(200)    NOT NULL,  NUM  NUMBER(18)    NOT NULL,  TITLE_DESC  VARCHAR2(200)    NOT NULL,  QUEST_TYPE  NUMBER(1)    NOT NULL,  MUST_ASK  NUMBER(1)    NOT NULL,  MIN_SELECT_NUM  NUMBER(18),  OPT_LAYOUT  NUMBER(1),  COLS  NUMBER(2),  SHOW_SCRIPT  VARCHAR2(500),  QUEST_UNIT  VARCHAR2(20),  STATE  NUMBER(1)    NOT NULL,  OTHER_DESC  VARCHAR2(2000),  OTHER_IMG  VARCHAR2(500),  JUMP_TYPE  NUMBER(1),  JUMP_QUEST_CODE  VARCHAR2(200),  CREATE_DATE  TIMESTAMP(6)    NOT NULL,  CREATE_MANID  NUMBER(18)    NOT NULL,  CONSTRAINT PK_TS_PROB_SUBJECT PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>79</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_SUBJECT1'
                AND TABLE_NAME = 'TS_PROB_SUBJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD CONSTRAINT FK_TS_PROB_SUBJECT1 FOREIGN KEY (QUESTLIB_ID) REFERENCES TS_PROB_LIB (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>80</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_LIB2'
                AND TABLE_NAME = 'TS_PROB_LIB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_LIB ADD CONSTRAINT FK_TS_PROB_LIB2 FOREIGN KEY (QUEST_SORTID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>81</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_LIB1'
                AND TABLE_NAME = 'TS_PROB_LIB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_LIB ADD CONSTRAINT FK_TS_PROB_LIB1 FOREIGN KEY (UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>82</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PROB_LIB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PROB_LIB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>83</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PROB_SUBJECT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PROB_SUBJECT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>84</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PRO_OPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_PRO_OPT (  RID  NUMBER(18)    NOT NULL,  QUEST_ID  NUMBER(18)    NOT NULL,  NUM  NUMBER(18),  OPTION_DESC  VARCHAR2(500),  OPTION_IMG  VARCHAR2(500),  OPTION_VALUE  VARCHAR2(50),  OPTION_SCORE  NUMBER(5),  NEED_FILL  NUMBER(1),  RMK  VARCHAR2(500),  STATE  NUMBER(1)    NOT NULL,  OTHER_DESC  VARCHAR2(2000),  OTHER_IMG  VARCHAR2(500),  JUMP_TYPE  NUMBER(1),  JUMP_QUEST_CODE  VARCHAR2(200),  CREATE_DATE  TIMESTAMP(6)    NOT NULL,  CREATE_MANID  NUMBER(18)    NOT NULL,  CONSTRAINT PK_TS_PRO_OPT PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>85</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PRO_OPT1'
                AND TABLE_NAME = 'TS_PRO_OPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_OPT ADD CONSTRAINT FK_TS_PRO_OPT1 FOREIGN KEY (QUEST_ID) REFERENCES TS_PROB_SUBJECT (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>86</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PRO_OPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PRO_OPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>87</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_DSF_SYS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_DSF_SYS  (   RID  NUMBER(18)    NOT NULL,   SYS_NAME  VARCHAR2(100)    NOT NULL,   SYS_JC  VARCHAR2(50),   SYS_ICON  VARCHAR2(200),   XT_TYPE  NUMBER(1)    NOT NULL,   SYS_URL  VARCHAR2(200),   CREATE_DATE  DATE    NOT NULL,   CREATE_MANID  NUMBER(18)    NOT NULL,   MODIFY_DATE  DATE,   MODIFY_MANID  NUMBER(18),   CONSTRAINT PK_TS_DSF_SYS PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>88</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_DSF_SYS_PARAM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_DSF_SYS_PARAM  (   RID  NUMBER(18)    NOT NULL,   DSF_SYS_ID  NUMBER(18)    NOT NULL,   PARAM_CN  VARCHAR2(50)    NOT NULL,   PARAM_EN  VARCHAR2(50)    NOT NULL,   PARAM_DESC  VARCHAR2(100),   CREATE_DATE  DATE    NOT NULL,   CREATE_MANID  NUMBER(18)    NOT NULL,   MODIFY_DATE  DATE,   MODIFY_MANID  NUMBER(18),   CONSTRAINT PK_TS_DSF_SYS_PARAM PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>89</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_DSF_SYS_PARAM'
                AND TABLE_NAME = 'TS_DSF_SYS_PARAM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_DSF_SYS_PARAM ADD CONSTRAINT FK_TS_DSF_SYS_PARAM FOREIGN KEY (DSF_SYS_ID) REFERENCES TS_DSF_SYS (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>90</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_DSF_SYS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_DSF_SYS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>91</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_DSF_SYS_PARAM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_DSF_SYS_PARAM_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>92</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_DSF_LOGINF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_DSF_LOGINF  (   RID  NUMBER(18)    NOT NULL,   USER_ID  NUMBER(18)    NOT NULL,   DSF_SYS_ID  NUMBER(18)    NOT NULL,   SYS_URL  VARCHAR2(200)    NOT NULL,   CREATE_DATE  DATE    NOT NULL,   CREATE_MANID  NUMBER(18)    NOT NULL,   MODIFY_DATE  DATE,   MODIFY_MANID  NUMBER(18),   CONSTRAINT PK_TS_DSF_LOGINF PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>93</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
          ]]>
		</sql>
		<ver>94</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
          ]]>
		</sql>
		<ver>95</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
          ]]>
		</sql>
		<ver>96</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
          ]]>
		</sql>
		<ver>97</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_DSF_LOGINF_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_DSF_LOGINF_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>98</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_DSF_LOGINF_PARAM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_DSF_LOGINF_PARAM_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>99</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_DSF_LOGINF_PARAM')
                AND COLUMN_NAME = UPPER('PARAM_VALUE')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_DSF_LOGINF_PARAM MODIFY PARAM_VALUE VARCHAR2(100) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>100</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'ADDWORK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD ADDWORK DATE ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>101</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'FIRST_EDU';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD FIRST_EDU INTEGER ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>102</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'FIRST_ACADEDU';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD FIRST_ACADEDU INTEGER ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>103</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'FIRST_PROF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD FIRST_PROF VARCHAR2(50) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>104</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'FIRST_SCHL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD FIRST_SCHL VARCHAR2(100) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>105</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'FIRSTGRD_TIME';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD FIRSTGRD_TIME DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>106</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'ACAD_DEGREE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD ACAD_DEGREE INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>107</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'LAST_PROF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD LAST_PROF VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>108</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'LAST_SCHL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD LAST_SCHL VARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>109</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'LASTGRD_TIME';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD LASTGRD_TIME DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>110</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'CREDITS_CARDNO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD CREDITS_CARDNO VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>111</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SLIDE_MAXVAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD SLIDE_MAXVAL INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>112</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SLIDE_MINVAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD SLIDE_MINVAL INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>113</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SLIDE_MAX_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD SLIDE_MAX_DESC VARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>114</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SLIDE_MIN_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD SLIDE_MIN_DESC VARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>115</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PRO_TEMPL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TS_PRO_TEMPL  (    RID                  INTEGER              not null,    TMPL_NAME            varchar2(100)        not null,    TMPL_CODE            varchar2(50)         not null,    TMPL_OPTS            Nvarchar2(1000),    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    constraint PK_TS_PRO_TEMPL primary key (RID),    constraint AK_AK_TS_PRO_TEMPL_TS_PRO_T unique (TMPL_CODE) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>116</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PRO_TEMPL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PRO_TEMPL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>117</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_LIB')
                AND COLUMN_NAME = UPPER('LIB_SCRIPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_LIB ADD LIB_SCRIPT CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>118</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PRO_OPT')
                AND COLUMN_NAME = UPPER('OPTION_VALUE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_OPT MODIFY OPTION_VALUE VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>119</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PROB_EXAMTYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_PROB_EXAMTYPE  ( RID                  INTEGER              not null, PARAM_TYPE           NUMBER(2)            default 0 not null, TYPE_CODE            VARCHAR2(50)         not null, LEVEL_NP             VARCHAR2(200)        not null, TYPE_NAME            VARCHAR2(50)         not null, RMK                  VARCHAR2(200), CREATE_DATE          TIMESTAMP            not null, CREATE_MANID         INTEGER              not null, constraint PK_TS_PROB_EXAMTYPE primary key (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>120</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_PROB_EXAMPOOL  ( RID                  INTEGER              NOT NULL, TYPE_ID              INTEGER, QES_CODE             VARCHAR2(20)         NOT NULL, QES_LEVEL_CODE       VARCHAR2(200)        NOT NULL, TITLE_DESC           VARCHAR2(200)        NOT NULL, QUEST_TYPE           NUMBER(1)            NOT NULL, MUST_ASK             NUMBER(1)            NOT NULL, MIN_SELECT_NUM       INTEGER, OPT_LAYOUT           NUMBER(1), COLS                 NUMBER(2), STATE                NUMBER(1)            NOT NULL, OTHER_DESC           VARCHAR2(2000), OTHER_IMG            VARCHAR2(500), JUMP_TYPE            NUMBER(1), JUMP_QUEST_CODE      VARCHAR2(200), SLIDE_MINVAL         INTEGER, SLIDE_MAXVAL         INTEGER, SLIDE_MAX_DESC       VARCHAR2(20), SLIDE_MIN_DESC       VARCHAR2(20), CREATE_DATE          TIMESTAMP            NOT NULL, CREATE_MANID         INTEGER              NOT NULL, INVOKE_SCRT          CLOB, constraint PK_TS_PROB_EXAMPOOL primary key (RID), constraint FK_TS_PROB_EXAMPOOL1 foreign key (TYPE_ID) references TS_SIMPLE_CODE (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>121</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PROB_EXAMPOOL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PROB_EXAMPOOL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>122</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PRO_POOL_OPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_PRO_POOL_OPT  ( RID                  INTEGER              not null, QUEST_ID             INTEGER              not null, NUM                  INTEGER, OPTION_DESC          VARCHAR2(500), OPTION_IMG           VARCHAR2(500), OPTION_VALUE         VARCHAR2(50), OPTION_SCORE         NUMBER(5), NEED_FILL            NUMBER(1), RMK                  VARCHAR2(500), STATE                NUMBER(1)            not null, OTHER_DESC           VARCHAR2(2000), OTHER_IMG            VARCHAR2(500), JUMP_TYPE            NUMBER(1), JUMP_QUEST_CODE      VARCHAR2(200), CREATE_DATE          TIMESTAMP            not null, CREATE_MANID         INTEGER              not null, constraint PK_TS_PRO_POOL_OPT primary key (RID), constraint FK_TS_PRO_POOL_OPT1 foreign key (QUEST_ID) references TS_PROB_EXAMPOOL (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>123</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PRO_POOL_OPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PRO_POOL_OPT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>124</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PROB_EXAMTYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PROB_EXAMTYPE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>125</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_SUBJECT'
                AND COLUMN_NAME = 'POOL_ID';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD POOL_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>126</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_SUBJECT'
                AND COLUMN_NAME = 'INVOKE_SCRT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD INVOKE_SCRT CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>127</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_SUBJECT2'
                AND TABLE_NAME = 'TS_PROB_SUBJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT add constraint FK_TS_PROB_SUBJECT2 foreign key (POOL_ID)   references TS_PROB_EXAMPOOL (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>128</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('UUID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD UUID VARCHAR(36)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>129</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('QES_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT MODIFY QES_CODE VARCHAR(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>130</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PRO_POOL_OPT')
                AND COLUMN_NAME = UPPER('OPTION_VALUE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_POOL_OPT MODIFY OPTION_VALUE VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>131</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXTYPE')
                AND COLUMN_NAME = UPPER('CREATE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXTYPE ADD CREATE_DATE TIMESTAMP';
              END IF;
            END;
          ]]>
		</sql>
		<ver>132</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXTYPE')
                AND COLUMN_NAME = UPPER('CREATE_MANID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXTYPE ADD CREATE_MANID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>133</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXTYPE')
                AND COLUMN_NAME = UPPER('MODIFY_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXTYPE ADD MODIFY_DATE TIMESTAMP';
              END IF;
            END;
          ]]>
		</sql>
		<ver>134</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_TXTYPE')
                AND COLUMN_NAME = UPPER('MODIFY_MANID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_TXTYPE ADD MODIFY_MANID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>135</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            UPDATE TS_TXTYPE T
            SET T.CREATE_DATE = SYSDATE, T.CREATE_MANID = 1, T.MODIFY_DATE = SYSDATE, T.MODIFY_MANID = 1
            WHERE T.CREATE_DATE IS NULL
          ]]>
		</sql>
		<ver>136</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FLOW_DEF')
                AND COLUMN_NAME = UPPER('IS_DISPAPP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_DEF ADD IS_DISPAPP NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>137</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'BIRTH_PLACE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD BIRTH_PLACE VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>138</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TB_SYS_EMP'
                AND COLUMN_NAME = 'ADDRESS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_SYS_EMP ADD ADDRESS VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>139</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_MSG_MAIN'
                AND COLUMN_NAME = 'APPEND_KEYS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN ADD APPEND_KEYS VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>140</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_MSG_MAIN'
                AND COLUMN_NAME = 'SUB_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN ADD SUB_TYPE INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>141</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PRO_POOL_OPT'
                AND COLUMN_NAME = 'IS_ALTER';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_POOL_OPT ADD IS_ALTER NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>142</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PRO_OPT'
                AND COLUMN_NAME = 'IS_ALTER';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_OPT ADD IS_ALTER NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>143</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PRO_OPT'
                AND COLUMN_NAME = 'IS_CORRECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_OPT ADD IS_CORRECT NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>144</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PRO_POOL_OPT'
                AND COLUMN_NAME = 'IS_CORRECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_POOL_OPT ADD IS_CORRECT NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>145</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_SUBJECT'
                AND COLUMN_NAME = 'OPTION_SCORE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD OPTION_SCORE NUMBER(5)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>146</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_SUBJECT'
                AND COLUMN_NAME = 'IS_MULTI';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD IS_MULTI NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>147</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'IS_MULTI';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD IS_MULTI NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>148</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('QUEST_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT MODIFY QUEST_TYPE NUMBER(2) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>149</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('QUEST_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL MODIFY QUEST_TYPE NUMBER(2) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>150</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PRO_POOL_OPT'
                AND COLUMN_NAME = 'IS_MULTI';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_POOL_OPT ADD IS_MULTI NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>151</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PRO_OPT'
                AND COLUMN_NAME = 'IS_MULTI';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PRO_OPT ADD IS_MULTI NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>152</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SHOW_CODE')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT MODIFY SHOW_CODE VARCHAR2(20) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>153</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PROB_TABDEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_PROB_TABDEFINE (   RID                  INTEGER              NOT NULL,   TAB_NAME             VARCHAR2(50)         NOT NULL,   RMK                  VARCHAR2(100),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TB_PROB_TABDEFINE PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>154</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PROB_COLSDEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_PROB_COLSDEFINE (   RID                  INTEGER              NOT NULL,   TABLE_ID             INTEGER              NOT NULL,   NUM                  INTEGER,   COL_NAME             VARCHAR2(50)         NOT NULL,   COL_DESC             VARCHAR2(100)        NOT NULL,   COL_TYPE             NUMBER(1)            NOT NULL,   COLS                 NUMBER(3),   COL_EXPR             VARCHAR2(200),   COL_LENTH            INTEGER,   COL_PREC             NUMBER(2),   COL_MUST             NUMBER(1)            NOT NULL,   COL_DEFVALUE         VARCHAR2(50),   SCOPE_CONS           NUMBER(1)            NOT NULL,   MIN_VALUE            VARCHAR2(50),   MAX_VALUE            VARCHAR2(50),   DS_TYPE              NUMBER(1)            NOT NULL,   DS_CDCODE            VARCHAR2(50),   DS_SQL               VARCHAR2(200),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TB_PROB_COLSDEFINE PRIMARY KEY (RID),   CONSTRAINT FK_TB_PROB_COLSDEFINE1 FOREIGN KEY (TABLE_ID) REFERENCES TB_PROB_TABDEFINE(RID)     )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>155</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('TABLE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD TABLE_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>156</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_SUBJECT3'
                AND TABLE_NAME = 'TS_PROB_SUBJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT   ADD CONSTRAINT FK_TS_PROB_SUBJECT3 FOREIGN KEY (TABLE_ID)      REFERENCES TB_PROB_TABDEFINE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>157</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_PROB_TABDEFINE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_PROB_TABDEFINE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>158</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_PROB_COLSDEFINE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_PROB_COLSDEFINE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>159</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'FILL_MAX_RANGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD FILL_MAX_RANGE VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>160</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_SUBJECT'
                AND COLUMN_NAME = 'FILL_MAX_RANGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD FILL_MAX_RANGE VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>161</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_PLOT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TS_PLOT (   RID                  INTEGER              not null,   ZONE_ID              INTEGER              not null,   PLOT_CODE            VARCHAR2(50),   PLOT_NAME            VARCHAR2(100),   PLOT_ADDR            VARCHAR2(200),   IF_REVEAL            NUMBER(1)            default 1 not null,   XH                   INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TS_PLOT primary key (RID),   constraint FK_TS_PLOT1 foreign key (ZONE_ID)      references TS_ZONE (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>162</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_PLOT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_PLOT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>163</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_LIB')
                AND COLUMN_NAME = UPPER('LIB_INIT_SRC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_LIB ADD LIB_INIT_SRC CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>164</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_LIB')
                AND COLUMN_NAME = UPPER('VERIFY_SCRIPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_LIB ADD VERIFY_SCRIPT CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>165</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_ROLE_SIMPAUTH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TS_ROLE_SIMPAUTH (    RID                  INTEGER              not null,    ROLE_ID              INTEGER              not null,    SIMPCODE_ID          INTEGER              not null,    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    constraint PK_TS_ROLE_SIMPAUTH primary key (RID),    constraint AK_TS_ROLE_SIMPAUTH unique (ROLE_ID, SIMPCODE_ID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>166</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_ROLE_SIMPAUTH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_ROLE_SIMPAUTH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>167</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_MSG_MAIN')
                AND COLUMN_NAME = UPPER('MENU_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN ADD MENU_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>168</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_MSG_MAIN')
                AND COLUMN_NAME = UPPER('IS_TODO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN ADD IS_TODO NUMERIC(1,0)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>169</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_MSG_MAIN')
                AND COLUMN_NAME = UPPER('TODO_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN ADD TODO_STATE NUMERIC(1,0)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>170</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_CONTRA_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_CONTRA_MAIN  (   RID  NUMBER(18)    NOT NULL,   CONTRA_CODE  VARCHAR2(50)    NOT NULL,   DESCR  VARCHAR2(100)    NOT NULL,   CONSTRAINT PK_TS_CONTRA_MAIN PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>171</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TS_CONTRA_MAIN'
                AND TABLE_NAME = 'TS_CONTRA_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_MAIN ADD CONSTRAINT AK_TS_CONTRA_MAIN UNIQUE  (CONTRA_CODE)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>172</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_CONTRA_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_CONTRA_MAIN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>173</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_CONTRA_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_CONTRA_SUB  (   RID  NUMBER(18)    NOT NULL,   MAIN_ID  NUMBER(18)    NOT NULL,   BUSI_TYPE  NUMBER(18)    NOT NULL,   LEFT_CODE  VARCHAR2(100)    NOT NULL,   RIGHT_CODE  VARCHAR2(100)    NOT NULL,   DESCR  VARCHAR2(200),   CONSTRAINT PK_TS_CONTRA_SUB PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>174</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_CONTRA_SUB1'
                AND TABLE_NAME = 'TS_CONTRA_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_SUB ADD CONSTRAINT FK_TS_CONTRA_SUB1 FOREIGN KEY (MAIN_ID) REFERENCES  TS_CONTRA_MAIN (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>175</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_CONTRA_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_CONTRA_SUB_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>176</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_INDEXES
              WHERE INDEX_NAME = 'AK_XT_ZONE1';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE INDEX AK_XT_ZONE1 ON TS_ZONE (ZONE_CODE )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>177</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_RECORD_LOGS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_RECORD_LOGS (  RID  NUMBER(18)    NOT NULL,  UP_TYPE  NUMBER(18)    NOT NULL,  UP_TAG  NUMBER(18)    NOT NULL,  RECORD_ID  NUMBER(18)    NOT NULL,  ERR_MESS  VARCHAR2(2000),  CREATE_DATE  TIMESTAMP(6)    NOT NULL,  CREATE_MANID  NUMBER(18)    NOT NULL,  CONSTRAINT PK_TS_RECORD_LOGS PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>178</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_RECORD_LOGS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_RECORD_LOGS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>179</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_INDEXES
              WHERE INDEX_NAME = 'IND_TS_RECORD_LOGS1';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE INDEX IND_TS_RECORD_LOGS1 ON TS_RECORD_LOGS (RECORD_ID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>180</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_INDEXES
              WHERE INDEX_NAME = 'IND_TS_RECORD_LOGS2';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE INDEX IND_TS_RECORD_LOGS2 ON TS_RECORD_LOGS (UP_TYPE)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>181</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_ZONE_OUTLINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TS_ZONE_OUTLINE  (    RID                  INTEGER              not null,    ZONE_ID              INTEGER              not null,    LNG                  VARCHAR2(20)         not null,    LAT                  VARCHAR2(20)         not null,    XY                   CLOB                 not null,    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    MODIFY_DATE          TIMESTAMP,    MODIFY_MANID         INTEGER,    constraint PK_TS_ZONE_OUTLINE primary key (RID),    constraint FK_TS_ZONE_OUTLINE1 foreign key (ZONE_ID) references TS_ZONE (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>182</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('DISP_KJMENU');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD DISP_KJMENU NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>183</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_DEF')
                AND COLUMN_NAME = UPPER('SEARCH_HTML');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_DEF ADD SEARCH_HTML CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>184</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('IS_SEARCH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD IS_SEARCH NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>185</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('SEARCH_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD SEARCH_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>186</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('IS_ORDER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD IS_ORDER NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>187</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('ORDER_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD ORDER_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>188</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_FIELD')
                AND COLUMN_NAME = UPPER('ORDER_INDEX');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_FIELD ADD ORDER_INDEX NUMBER(3)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>189</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_FORM_DEF')
                AND COLUMN_NAME = UPPER('SEARCH_SQL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_DEF ADD SEARCH_SQL CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>190</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FORM_STATISTICS_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_FORM_STATISTICS_DEF  (    RID                  INTEGER              not null,    TYPE_ID              INTEGER              not null,    FORM_CODE            VARCHAR2(50)         not null,    TABLE_ID             INTEGER              not null,    FORM_NAME            VARCHAR2(200)        not null,    STATE                NUMBER(1)            not null,    FORM_VERSION         INTEGER,    IS_DEFAULT           NUMBER(1),    FORM_MODEL           NUMBER(1),    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    STATISTICS_HTML      CLOB,    STATISTICS_SQL       CLOB,    DATA_SHELL           CLOB,    constraint PK_TD_FORM_STATISTICS_DEF primary key (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>191</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FORM_STATISTICS_DEF_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FORM_STATISTICS_DEF_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>192</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FORM_STATISTICS_DEF1'
                AND TABLE_NAME = 'TD_FORM_STATISTICS_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_STATISTICS_DEF ADD CONSTRAINT FK_TD_FORM_STATISTICS_DEF1 FOREIGN KEY (TABLE_ID) REFERENCES TD_FORM_TABLE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>193</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FORM_STATISTICS_DEF2'
                AND TABLE_NAME = 'TD_FORM_STATISTICS_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FORM_STATISTICS_DEF ADD CONSTRAINT FK_TD_FORM_STATISTICS_DEF2 FOREIGN KEY (TYPE_ID) REFERENCES TD_FORM_TYPE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>194</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_KLETYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_KLETYPE  (   RID  NUMBER(18)    NOT NULL,   TYPE_NAME  VARCHAR2(100)    NOT NULL,   TYPE_CODE  VARCHAR2(50)    NOT NULL,   LVL_CODE  VARCHAR2(50)    NOT NULL,   PARENT_ID  NUMBER(18)      ,   TYPES  NUMBER(1)    NOT NULL,   CREATE_DATE  TIMESTAMP(6)    NOT NULL,   CREATE_MANID  NUMBER(18)    NOT NULL,   MODIFY_DATE  TIMESTAMP(6),   MODIFY_MANID  NUMBER(18),   CONSTRAINT PK_TS_KLETYPE PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>195</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_KLETYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_KLETYPE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>196</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_KLEMAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_KLEMAIN  (   RID  NUMBER(18)    NOT NULL,   KLE_NAME  VARCHAR2(100),   KLE_KEYS  VARCHAR2(100),   KLE_TYPE_ID  NUMBER(18),   FB_DATE  DATE,   FB_MANID  NUMBER(18),   STATE  NUMBER(1),   CREATE_DATE  TIMESTAMP(6)    NOT NULL,   CREATE_MANID  NUMBER(18)    NOT NULL,   CONSTRAINT PK_TS_KLEMAIN PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>197</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_KLEMAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_KLEMAIN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>198</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_KLEANX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_KLEANX  (   RID  NUMBER(18)    NOT NULL,   MAIN_ID  NUMBER(18)    NOT NULL,   FILE_NAME  VARCHAR2(200)    NOT NULL,   FILE_PATH  VARCHAR2(200)    NOT NULL,   CREATE_DATE  TIMESTAMP(6)    NOT NULL,   CREATE_MANID  NUMBER(18)    NOT NULL,   CONSTRAINT PK_TS_KLEANX PRIMARY KEY (RID) ) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>199</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_KLEANX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_KLEANX_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 3000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>200</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_HOLIDAY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_HOLIDAY (   RID                  INTEGER              NOT NULL,   HOL_YEAR             INTEGER              NOT NULL,   HOL_MON              INTEGER              NOT NULL,   HOL_DATE             DATE                 NOT NULL,   DATE_TYPE            INTEGER ,               CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CONSTRAINT PK_TS_HOLIDAY PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>201</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_HOLIDAY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_HOLIDAY_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>202</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('LINK_MAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD LINK_MAN VARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>203</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('EMAIL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD EMAIL VARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>204</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS1');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS1 NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>205</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS2 INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>206</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS3');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS3 varchar2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>207</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_UNIT_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TS_UNIT_REL  (    RID                  INTEGER              not null,    UNIT_ID              INTEGER              not null,    REL_UNIT_ID              INTEGER         not null,    REL_TYPE             NUMBER(1)            not null,    BEGIN_TIME           DATE                 not null,    END_TIME             DATE                 not null,    constraint PK_TS_UNIT_REL primary key (RID),    constraint FK_TS_UNIT_REL1 foreign key (UNIT_ID)       references TS_UNIT (RID),    constraint FK_TS_UNIT_REL2 foreign key (REL_UNIT_ID)       references TS_UNIT (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>208</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_UNIT_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_UNIT_REL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>209</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('MEDI_LIC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD MEDI_LIC VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>210</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('RAD_LIC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD RAD_LIC VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>211</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('ORG_FZ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD ORG_FZ VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>212</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('ORG_FZZW');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD ORG_FZZW VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>213</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('ORG_TEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD ORG_TEL VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>214</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_LOGIN_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TS_LOGIN_INFO  (    RID                  INTEGER              not null,    HAPPEN_DATE          TIMESTAMP            not null,    USER_NO              VARCHAR2(20),    CLIENT_IP            VARCHAR2(50),    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    MODIFY_DATE          TIMESTAMP,    MODIFY_MANID         INTEGER,    LAST_LOGIN_TIME      TIMESTAMP            not null,    constraint PK_TS_LOGIN_INFO primary key (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>215</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TS_LOGIN_INFO_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TS_LOGIN_INFO_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 1 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>216</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PRO_POOL_TABDEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_PRO_POOL_TABDEFINE (   RID                  INTEGER              not null,   TAB_NAME             VARCHAR2(50)         not null,   RMK                  VARCHAR2(100),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   ROW_FIXED            NUMBER(1),   DEFAULT_LINE_NUM     NUMBER(1),   constraint PK_TB_PRO_POOL_TABDEFINE primary key (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>217</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TB_PRO_POOL_TABDEFINE_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TB_PRO_POOL_TABDEFINE_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 1 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>218</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PRO_POOL_ROWTITLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_PRO_POOL_ROWTITLE (   RID                  INTEGER              not null,   TABLE_ID             INTEGER              not null,   TITLE                VARCHAR2(50)         not null,   ROW_INDEX            NUMBER(2)            not null,   COL_INDEX            NUMBER(2)            not null,   COLSPAN              NUMBER(2),   ROWSPAN              NUMBER(2),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TB_PRO_POOL_ROWTITLE primary key (RID),   constraint AK_TB_PRO_POOL_ROWTITLE unique (TABLE_ID, TITLE),   constraint FK_TB_PRO_POOL_ROWTITLE1 foreign key (TABLE_ID)      references TB_PRO_POOL_TABDEFINE (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>219</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TB_PRO_POOL_ROWTITLE_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TB_PRO_POOL_ROWTITLE_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 1 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>220</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PRO_POOL_COLSDEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_PRO_POOL_COLSDEFINE (   RID                  INTEGER              not null,   TABLE_ID             INTEGER              not null,   NUM                  INTEGER,   ROW_INDEX            NUMBER(2)            not null,   COL_INDEX            NUMBER(2)            not null,   COL_NAME             VARCHAR2(50),   COL_DESC             VARCHAR2(100)        not null,   COL_TYPE             NUMBER(1)            not null,   COLS                 NUMBER(3),   ROWSPAN              NUMBER(3),   COL_EXPR             VARCHAR2(200),   COL_LENTH            INTEGER,   COL_PREC             NUMBER(2),   COL_MUST             NUMBER(1),   COL_DEFVALUE         VARCHAR2(50),   SCOPE_CONS           NUMBER(1),   MIN_VALUE            VARCHAR2(50),   MAX_VALUE            VARCHAR2(50),   DS_TYPE              NUMBER(1),   DS_CDCODE            VARCHAR2(50),   DS_SQL               VARCHAR2(200),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TB_PRO_POOL_COLSDEFINE primary key (RID),   constraint FK_TB_PRO_POOL_COLSDEFINE1 foreign key (TABLE_ID)      references TB_PRO_POOL_TABDEFINE (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>221</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TB_PRO_POOL_COLSDEFINE_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TB_PRO_POOL_COLSDEFINE_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 1 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>222</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('TABLE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD TABLE_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>223</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_EXAMPOOL2'
                AND TABLE_NAME = 'TS_PROB_EXAMPOOL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD CONSTRAINT FK_TS_PROB_EXAMPOOL2 FOREIGN KEY (TABLE_ID) REFERENCES TB_PRO_POOL_TABDEFINE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>224</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_TABDEFINE')
                AND COLUMN_NAME = UPPER('ROW_FIXED');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_TABDEFINE ADD ROW_FIXED NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>225</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_TABDEFINE')
                AND COLUMN_NAME = UPPER('DEFAULT_LINE_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_TABDEFINE ADD DEFAULT_LINE_NUM NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>226</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PROB_ROWTITLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '  create table TB_PROB_ROWTITLE (   RID                  INTEGER              not null,   TABLE_ID             INTEGER              not null,   TITLE                VARCHAR2(50),   ROW_INDEX            NUMBER(2),   COL_INDEX            NUMBER(2),   COLSPAN              NUMBER(2),   ROWSPAN              NUMBER(2),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TB_PROB_ROWTITLE primary key (RID),   constraint FK_TB_PROB_ROWTITLE1 foreign key (TABLE_ID)      references TB_PROB_TABDEFINE (RID) )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>227</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              v1 number;
            BEGIN
              SELECT count(1)
              INTO v1
              FROM user_sequences
              WHERE sequence_name = 'TB_PROB_ROWTITLE_SEQ';
              IF v1 = 0 THEN
                EXECUTE IMMEDIATE 'create sequence TB_PROB_ROWTITLE_SEQ minvalue 0 maxvalue 9999999999999999999999999 start with 1 increment by 1 nocache';
              END IF;
            END;
          ]]>
		</sql>
		<ver>228</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('ROW_INDEX');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE ADD ROW_INDEX NUMBER(2) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>229</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('COL_INDEX');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE ADD COL_INDEX NUMBER(2) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>230</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('ROWSPAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE ADD ROWSPAN NUMBER(3)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>231</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PRO_POOL_ROWTITLE')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PRO_POOL_ROWTITLE ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>232</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PRO_POOL_COLSDEFINE')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PRO_POOL_COLSDEFINE ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>233</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>234</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('COL_MUST')
                AND NULLABLE = UPPER('N');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE MODIFY COL_MUST NUMBER(1) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>235</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('SCOPE_CONS')
                AND NULLABLE = UPPER('N');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE MODIFY SCOPE_CONS NUMBER(1) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>236</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('DS_TYPE')
                AND NULLABLE = UPPER('N');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE MODIFY DS_TYPE NUMBER(1) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>237</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_LIB')
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_LIB ADD PARAM_TYPE NUMBER(2) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>238</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_ROWTITLE')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_ROWTITLE ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>239</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TB_PROB_COLSDEFINE'
                AND TABLE_NAME = 'TB_PROB_COLSDEFINE';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE DROP CONSTRAINT AK_TB_PROB_COLSDEFINE CASCADE DROP INDEX';
              END IF;
            END;
          ]]>
		</sql>
		<ver>240</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('COL_NAME')
                AND NULLABLE = UPPER('N');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE MODIFY COL_NAME VARCHAR2(50) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>241</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TB_PROB_ROWTITLE'
                AND TABLE_NAME = 'TB_PROB_ROWTITLE';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_ROWTITLE DROP CONSTRAINT AK_TB_PROB_ROWTITLE CASCADE DROP INDEX';
              END IF;
            END;
          ]]>
		</sql>
		<ver>242</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TB_PRO_POOL_ROWTITLE'
                AND TABLE_NAME = 'TB_PRO_POOL_ROWTITLE';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PRO_POOL_ROWTITLE DROP CONSTRAINT AK_TB_PRO_POOL_ROWTITLE CASCADE DROP INDEX';
              END IF;
            END;
          ]]>
		</sql>
		<ver>243</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('CREDIT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CREDIT_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>244</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('SAFE_UNITNAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD SAFE_UNITNAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>245</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('JD_UNITNAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD JD_UNITNAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>246</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_WRITSORT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_ZW_WRITSORT (   RID                  INTEGER              not null,   WRIT_SORT            INTEGER              default 1,   RPT_TEMPL_ID         INTEGER,   WRIT_CODE            VARCHAR2(20),   WRIT_SHORTNAME       VARCHAR2(50),   WRIT_NAME            VARCHAR2(100)        not null,   IF_HUMAN             NUMBER(1),   MULTIPLE             NUMBER(1),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TB_ZW_WRITSORT primary key (RID),   constraint FK_TB_ZW_WRITSORT1 foreign key (RPT_TEMPL_ID) references TS_RPT (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>247</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_MSG_BOARD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_MSG_BOARD (   RID                  INTEGER              not null,   LINK_MAN             VARCHAR2(100),   LINK_TEL             VARCHAR2(50),   FEEDBACK_MSG         VARCHAR2(2000),   MSG_DATE             DATE,   SUBMIT_PSN           INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,    CONSTRAINT PK_TS_MSG_BOARD PRIMARY KEY (RID),    CONSTRAINT TS_MSG_BOARD FOREIGN KEY (SUBMIT_PSN) REFERENCES TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>248</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_MSG_BOARD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_MSG_BOARD_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>249</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('PROVE_BAK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD PROVE_BAK VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>250</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('IF_COMPLETE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD IF_COMPLETE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>251</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_MENU')
                AND COLUMN_NAME = UPPER('IF_POP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_MENU ADD IF_POP char(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>252</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS4');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS4 NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>253</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('IS_ADD');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD IS_ADD NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>254</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('UPLOAD_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD UPLOAD_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>255</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('ERR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD ERR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>256</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('UPDATETAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD UPDATETAG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>257</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>258</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('REAL_ZONE_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD REAL_ZONE_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>259</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('CODE_DESC');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY CODE_DESC VARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>260</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('GPY_VERSION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD GPY_VERSION NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>261</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('IF_CITY_DIRECT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD IF_CITY_DIRECT NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>262</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_MSG_MAIN')
                AND COLUMN_NAME = UPPER('BUS_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN ADD BUS_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>263</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_MSG_MAIN')
                AND COLUMN_NAME = UPPER('PUBLISH_MAN')
                AND NULLABLE = UPPER('N');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_MSG_MAIN MODIFY PUBLISH_MAN INTEGER NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>264</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_ROLE_POWER';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TS_ROLE_POWER 
                (   
                	RID                  INTEGER              not null,
   					ROLE_ID              INTEGER,
   					ROLE_TYPE_ID         INTEGER              not null,
   					CREATE_DATE          TIMESTAMP            not null,
   					CREATE_MANID         INTEGER              not null,
				    MODIFY_DATE          TIMESTAMP,
				    MODIFY_MANID         INTEGER,
                	CONSTRAINT PK_TS_ROLE_POWER PRIMARY KEY (RID),    
                	CONSTRAINT FK_TS_ROLE_POWER1 FOREIGN KEY (ROLE_ID) REFERENCES TS_ROLE(RID),
                	CONSTRAINT FK_TS_ROLE_POWER2 FOREIGN KEY (ROLE_TYPE_ID) REFERENCES TS_SIMPLE_CODE(RID)
                )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>265</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('MANAGE_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD MANAGE_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>266</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_UNIT4'
                AND TABLE_NAME = 'TS_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CONSTRAINT FK_TS_UNIT4 FOREIGN KEY (MANAGE_ZONE_ID) REFERENCES TS_ZONE(RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>267</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ROLE')
                AND COLUMN_NAME = UPPER('IF_SUPER_MANAGE_ROLE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ROLE ADD IF_SUPER_MANAGE_ROLE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>268</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('IDC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD IDC VARCHAR2(18)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>269</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('VALID_BEG_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD VALID_BEG_DATE DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>270</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('VALID_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD VALID_END_DATE DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>271</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_ROLE_POWER_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_ROLE_POWER_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>272</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_GEN_SERINO_RULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
				create table TS_GEN_SERINO_RULE 
				(
				   RID                  INTEGER              not null,
				   BUS_TYPE             NUMBER(2),
				   PREFIX               VARCHAR2(50),
				   CURR_SERINO          INTEGER,
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TS_GEN_SERINO_RULE primary key (RID)
				)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>273</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_GEN_SERINO_RULE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_GEN_SERINO_RULE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>274</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_NOTICE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TS_NOTICE (      RID                  INTEGER              not null,
			   NOTICE_TITLE         VARCHAR2(200),
			   NOTICE_DATE          DATE,
			   FILE_PATH            VARCHAR2(200),
			   STATE_MARK           NUMBER(1),
			   CREATE_DATE          TIMESTAMP            not null,
			   CREATE_MANID         INTEGER              not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TS_NOTICE primary key (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>275</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_USER_SECURITY_LOG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
				create table TS_USER_SECURITY_LOG 
				(
				    RID                  INTEGER              not null,
					   LOGIN_IP             VARCHAR2(20)         not null,
					   LOGIN_DATE           DATE,
					   LOCK_DATE            DATE,
					   CTU_FAIL_TIMES       NUMBER(2),
					   LOGIN_INFO           VARCHAR2(500),
					   CREATE_DATE          TIMESTAMP            not null,
					   CREATE_MANID         INTEGER              not null,
					   MODIFY_DATE          TIMESTAMP,
					   MODIFY_MANID         INTEGER,
					   constraint PK_TS_USER_SECURITY_LOG primary key (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>276</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_USER_SECURITY_LOG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_USER_SECURITY_LOG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>277</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_NOTICE')
                AND COLUMN_NAME = UPPER('IF_PUBLISH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_NOTICE ADD IF_PUBLISH NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>278</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_NOTICE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_NOTICE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>279</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_RPT')
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_RPT MODIFY PARAM_TYPE NUMBER(4)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>280</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CODE_TYPE')
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CODE_TYPE MODIFY PARAM_TYPE NUMBER(4)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>281</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_QUARTZ')
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_QUARTZ MODIFY PARAM_TYPE NUMBER(4)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>282</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CODE_RULE')
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CODE_RULE MODIFY PARAM_TYPE NUMBER(4)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>283</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SYSTEM_PARAM')
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SYSTEM_PARAM MODIFY PARAM_TYPE NUMBER(4)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>284</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CONTRA_SUB')
                AND COLUMN_NAME = UPPER('DSF_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_SUB ADD DSF_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>285</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZYWS_UP_COUNTRY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
				create table TD_ZYWS_UP_COUNTRY
				(
				    RID                  INTEGER              not null,
					BUS_TYPE             NUMBER(2),
					BUS_ID               INTEGER,
					UP_FILENAME          VARCHAR2(200),
					OP_TYPE              NUMBER(1),
					STATE                NUMBER(1),
					IF_ADD_TO_GJ         NUMBER(1),
					UPLOAD_DATE          DATE,
					ERR_MSG              VARCHAR2(1000),
					MODIFY_DATE          TIMESTAMP,
					MODIFY_MANID         INTEGER,
					CREATE_DATE          TIMESTAMP            not null,
					CREATE_MANID         INTEGER              not null,
					   constraint PK_TD_ZYWS_UP_COUNTRY primary key (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>286</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZYWS_UP_COUNTRY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZYWS_UP_COUNTRY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>287</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_SYS_APPLY_ACCOUNT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TB_SYS_APPLY_ACCOUNT
                (
                    RID                  INTEGER              not null,
                   SORT_ID              INTEGER              not null,
                   NUM                  NUMBER(4),
                   RMK                  VARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TB_SYS_APPLY_ACCOUNT primary key (RID),
                   constraint FK_TB_SYS_APPLY_ACCOUNT1 foreign key (SORT_ID)
                   references TS_BS_SORT (RID)
                                )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>288</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_SYS_APPLY_ACCOUNT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_SYS_APPLY_ACCOUNT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>289</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_SYS_APPLY_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TB_SYS_APPLY_ANNEX
				(
				   RID                  INTEGER              not null,
				   MAIN_ID              INTEGER              not null,
				   ANNEX_NAME           VARCHAR2(100),
				   NUM                  NUMBER(4),
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
                   constraint PK_TB_SYS_APPLY_ANNEX primary key (RID),
                   constraint FK_TB_SYS_APPLY_ANNEX1 foreign key (MAIN_ID)
                   references TB_SYS_APPLY_ACCOUNT (RID)
                                )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>290</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_SYS_APPLY_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_SYS_APPLY_ANNEX_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>291</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_SYS_APPLY_ROLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TB_SYS_APPLY_ROLE
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ROLE_ID              INTEGER              not null,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_SYS_APPLY_ROLE primary key (RID),
               constraint FK_TB_SYS_APPLY_ROLE1 foreign key (MAIN_ID)
               references TB_SYS_APPLY_ACCOUNT (RID),
               constraint FK_TB_SYS_APPLY_ROLE2 foreign key (ROLE_ID)
               references TS_ROLE (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>292</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_SYS_APPLY_ROLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_SYS_APPLY_ROLE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>293</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_SYS_APPLY_ACCOUNT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_SYS_APPLY_ACCOUNT
            (
                RID                  INTEGER              not null,
                ZONE_ID              INTEGER              not null,
                UNITNAME             VARCHAR2(100),
                CREDIT_CODE          VARCHAR2(50),
                UNIT_TEL             VARCHAR2(50),
                USERNAME             VARCHAR2(30),
                MOBILE_NUM           VARCHAR2(50),
                IDC                  VARCHAR2(50),
                USER_NO              VARCHAR2(30),
                PASSWORD             VARCHAR2(50),
                STATE_MARK           NUMBER(1),
                CHECK_USER_ID        INTEGER,
                BACK_RSN             VARCHAR2(100),
                CREATE_DATE          TIMESTAMP            not null,
                CREATE_MANID         INTEGER              not null,
                MODIFY_DATE          TIMESTAMP,
                MODIFY_MANID         INTEGER,
                constraint PK_TD_SYS_APPLY_ACCOUNT primary key (RID),
               constraint FK_TD_SYS_APPLY_ACCOUNT1 foreign key (ZONE_ID)
                 references TS_ZONE (RID),
               constraint FK_TD_SYS_APPLY_ACCOUNT2 foreign key (CHECK_USER_ID)
                 references TS_USER_INFO (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>294</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_SYS_APPLY_ACCOUNT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_SYS_APPLY_ACCOUNT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>295</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_SYS_APPLY_SORT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_SYS_APPLY_SORT
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER              not null,
               SORT_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_SYS_APPLY_SORT primary key (RID),
               constraint FK_TD_SYS_APPLY_SORT1 foreign key (MAIN_ID)
                references TD_SYS_APPLY_ACCOUNT (RID),
               constraint FK_TD_SYS_APPLY_SORT2 foreign key (SORT_ID)
                references TB_SYS_APPLY_ACCOUNT (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>296</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_SYS_APPLY_SORT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_SYS_APPLY_SORT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>297</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_SYS_APPLY_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_SYS_APPLY_ANNEX
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER              not null,
               ANNEX_ID             INTEGER,
               ANNEX_NAME           VARCHAR2(200),
               ANNEX_PATH           VARCHAR2(200),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_SYS_APPLY_ANNEX primary key (RID),
               constraint FK_TD_SYS_APPLY_ANNEX1 foreign key (MAIN_ID)
                 references TD_SYS_APPLY_ACCOUNT (RID),
               constraint FK_TD_SYS_APPLY_ANNEX2 foreign key (ANNEX_ID)
                  references TB_SYS_APPLY_ANNEX (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>298</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_SYS_APPLY_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_SYS_APPLY_ANNEX_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>299</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SYSTEM_UPDATE')
              	AND DATA_PRECISION < 4
                AND COLUMN_NAME = UPPER('PARAM_TYPE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SYSTEM_UPDATE MODIFY PARAM_TYPE NUMBER(4)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>300</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZYWS_UP_COUNTRY')
                AND COLUMN_NAME = UPPER('BHKORG_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_UP_COUNTRY Add BHKORG_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>301</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZYWS_UP_COUNTRY')
                AND COLUMN_NAME = UPPER('BHK_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_UP_COUNTRY Add BHK_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>302</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZYWS_UP_COUNTRY')
                AND COLUMN_NAME = UPPER('OCC_DISEID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_UP_COUNTRY Add OCC_DISEID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>303</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('IF_SUB_ORG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT Add IF_SUB_ORG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>304</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_SYS_APPLY_ACCOUNT')
                AND COLUMN_NAME = UPPER('IF_SUB_ORG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_SYS_APPLY_ACCOUNT Add IF_SUB_ORG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>305</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SYSTEM_PARAM')
              	AND DATA_LENGTH = 1000
                AND COLUMN_NAME = UPPER('PARAM_VALUE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SYSTEM_PARAM MODIFY PARAM_VALUE VARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>306</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_SYS_HOLIDAY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TS_SYS_HOLIDAY
				(
				   RID                  INTEGER              not null,
				   HOLI_DESC            VARCHAR2(50),
				   HOLI_TYPE            NUMBER(1),
				   START_DATE           DATE,
				   END_DATE             DATE,
				   STATE                NUMBER(1),
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TS_SYS_HOLIDAY primary key (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>307</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_SYS_HOLIDAY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_SYS_HOLIDAY_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>308</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS5');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE Add EXTENDS5 NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>309</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('IF_PROV_DIRECT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE Add IF_PROV_DIRECT NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>310</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('CMA_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CMA_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>311</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('CNAS_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CNAS_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>312</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('ORG_ICON');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD ORG_ICON NVARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>313</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CONTRA_SUB')
                AND COLUMN_NAME = UPPER('DSF_SPECIAL_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_SUB ADD DSF_SPECIAL_DESC VARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>314</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROB_ANS_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_PROB_ANS_DETAIL
            (
               RID                  INTEGER              not null,
               MAIN_TYPE            NUMBER(1),
			   MAIN_ID              INTEGER,
			   QUEST_ID             INTEGER              not null,
			   OPTION_VALUE         INTEGER,
			   SCORE_VALUE          VARCHAR2(200),
			   FILL_VALUE           VARCHAR2(200),
			   MULTI_NUM            INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               constraint PK_TD_PROB_ANS_DETAIL primary key (RID),
               constraint FK_TD_PROB_ANS_DETAIL1 foreign key (QUEST_ID)
                 references TS_PROB_SUBJECT (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>315</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROB_ANS_DETAIL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PROB_ANS_DETAIL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>316</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROB_ANS_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_PROB_ANS_TABLE
            (
               RID                  INTEGER              not null,
               SUR_SUBJECTID        INTEGER              not null,
			   COL_ID               INTEGER,
			   NUM                  INTEGER,
			   COL_VALUE            VARCHAR2(200),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               constraint PK_TD_PROB_ANS_TABLE primary key (RID),
               constraint FK_TD_PROB_ANS_TABLE1 foreign key (SUR_SUBJECTID)
                 references TD_PROB_ANS_DETAIL (RID),
               constraint FK_TD_PROB_ANS_TABLE2 foreign key (COL_ID)
                 references TB_PROB_COLSDEFINE (RID)
               )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>317</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROB_ANS_TABLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_PROB_ANS_TABLE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>318</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_MENU_BTN')
              	AND DATA_LENGTH = 50
                AND COLUMN_NAME = UPPER('BTN_NAME');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_MENU_BTN MODIFY BTN_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>319</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROB_ANS_DETAIL')
                AND COLUMN_NAME = UPPER('QUE_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROB_ANS_DETAIL ADD QUE_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>320</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PROB_ANS_DETAIL2'
                AND TABLE_NAME = 'TD_PROB_ANS_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_PROB_ANS_DETAIL add constraint FK_TD_PROB_ANS_DETAIL2 foreign key (QUE_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>321</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'SCORE_SCRIPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD SCORE_SCRIPT CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>322</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'OPTION_SCORE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD OPTION_SCORE NUMBER(5,2)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>323</ver>
	</sqlsentence>

	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'ANNEX_ADDR';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD ANNEX_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>324</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('USER_TYPE_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD USER_TYPE_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>325</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROB_ANS_DETAIL')
                AND COLUMN_NAME = UPPER('EXAMPOOL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROB_ANS_DETAIL ADD EXAMPOOL_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>326</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PROB_ANS_DETAIL3'
                AND TABLE_NAME = 'TD_PROB_ANS_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_PROB_ANS_DETAIL add constraint FK_TD_PROB_ANS_DETAIL3 foreign key (EXAMPOOL_ID) references TS_PROB_EXAMPOOL (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>327</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROB_ANS_DETAIL')
                AND COLUMN_NAME = UPPER('QUEST_ID')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROB_ANS_DETAIL MODIFY QUEST_ID INTEGER NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>328</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TS_UNIT2'
                AND TABLE_NAME = 'TS_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CONSTRAINT AK_TS_UNIT2 UNIQUE (CREDIT_CODE,UNITNAME,IF_SUB_ORG)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>329</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
           DECLARE
             NUM INT;
           BEGIN
             SELECT COUNT(1)
             INTO NUM
             FROM USER_TAB_COLUMNS
             WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
               AND COLUMN_NAME = UPPER('EXTENDS6');
             IF NUM = 0 THEN
               EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS6 NVARCHAR2(50)';
             END IF;
           END;
         ]]>
		</sql>
		<ver>330</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
           DECLARE
             NUM INT;
           BEGIN
             SELECT COUNT(1)
             INTO NUM
             FROM USER_TAB_COLUMNS
             WHERE TABLE_NAME = UPPER('TB_PRO_POOL_TABDEFINE')
               AND COLUMN_NAME = UPPER('DEFAULT_LINE_NUM')
               AND DATA_PRECISION=1;
             IF NUM = 1 THEN
               EXECUTE IMMEDIATE 'ALTER TABLE TB_PRO_POOL_TABDEFINE MODIFY DEFAULT_LINE_NUM NUMBER(2) ';
             END IF;
           END;
         ]]>
		</sql>
		<ver>331</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
           DECLARE
             NUM INT;
           BEGIN
             SELECT COUNT(1)
             INTO NUM
             FROM USER_TAB_COLUMNS
             WHERE TABLE_NAME = UPPER('TB_PROB_TABDEFINE')
               AND COLUMN_NAME = UPPER('DEFAULT_LINE_NUM')
               AND DATA_PRECISION=1;
             IF NUM = 1 THEN
               EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_TABDEFINE MODIFY DEFAULT_LINE_NUM NUMBER(2) ';
             END IF;
           END;
         ]]>
		</sql>
		<ver>332</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
           DECLARE
             NUM INT;
           BEGIN
             SELECT COUNT(1)
             INTO NUM
             FROM USER_TAB_COLUMNS
             WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
               AND COLUMN_NAME = UPPER('EXTENDS1')
               AND DATA_PRECISION=1;
             IF NUM = 1 THEN
               EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS1 NUMBER(2)';
             END IF;
           END;
         ]]>
		</sql>
		<ver>333</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
           DECLARE
             NUM INT;
           BEGIN
             SELECT COUNT(1)
             INTO NUM
             FROM USER_TAB_COLUMNS
             WHERE TABLE_NAME = UPPER('TS_MENU')
               AND COLUMN_NAME = UPPER('IP_ADDR');
             IF NUM = 0 THEN
               EXECUTE IMMEDIATE 'ALTER TABLE TS_MENU ADD IP_ADDR NVARCHAR2(20)';
             END IF;
           END;
         ]]>
		</sql>
		<ver>334</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PUBLISH_NOTICE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PUBLISH_NOTICE (
	               RID                  INTEGER              not null,
	               PUBLISH_TYPE_ID      INTEGER              not null,
				   OTHER_TYPE           NVARCHAR2(10),
				   TITLE                NVARCHAR2(50),
				   CONTENT              CLOB,
				   READS                NUMBER(4),
				   TOTALS               NUMBER(4),
				   STATE                NUMBER(1),
				   PUBLISH_DATE         DATE,
				   PUBLISH_PSN_ID       INTEGER,
				   PUBLISH_UNIT_ID      INTEGER,
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_PUBLISH_NOTICE primary key (RID),
				   constraint FK_TD_PUBLISH_NOTICE1 foreign key (PUBLISH_TYPE_ID)
      					references TS_SIMPLE_CODE (RID),
      			   constraint FK_TD_PUBLISH_NOTICE2 foreign key (PUBLISH_UNIT_ID)
      					references TS_UNIT (RID),
      		       constraint FK_TD_PUBLISH_NOTICE3 foreign key (PUBLISH_PSN_ID)
      					references TS_USER_INFO (RID)
           		)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>335</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PUBLISH_NOTICE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PUBLISH_NOTICE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>336</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PUBLISH_NOTICE_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PUBLISH_NOTICE_ANNEX (
	               RID                  INTEGER              not null,
	               MAIN_ID              INTEGER,
				   ANNEX_NAME           NVARCHAR2(200),
				   ANNEX_ADDR           NVARCHAR2(200),
				   XH                   NUMBER(3),
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_PUBLISH_NOTICE_ANNEX primary key (RID),
				   constraint FK_TD_PUBLISH_NOTICE_ANNEX1 foreign key (MAIN_ID)
      					references TD_PUBLISH_NOTICE (RID)
           		)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>337</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PUBLISH_NOTICE_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PUBLISH_NOTICE_ANNEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>338</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PUBLISH_NOTICE_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PUBLISH_NOTICE_UNIT (
	               RID                  INTEGER              not null,
	               MAIN_ID              INTEGER,
				   UNIT_ID              INTEGER,
				   STATE                NUMBER(1),
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_PUBLISH_NOTICE_UNIT primary key (RID),
				   constraint FK_TD_PUBLISH_NOTICE_UNIT1 foreign key (MAIN_ID)
      					references TD_PUBLISH_NOTICE (RID),
      			   constraint FK_TD_PUBLISH_NOTICE_UNIT2 foreign key (UNIT_ID)
      					references TS_UNIT (RID)
           		)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>339</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PUBLISH_NOTICE_UNIT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PUBLISH_NOTICE_UNIT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>340</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PUBLISH_NOTICE_UNIT_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PUBLISH_NOTICE_UNIT_ANNEX (
	               RID                  INTEGER              not null,
	               MAIN_ID              INTEGER,
				   ANNEX_NAME           NVARCHAR2(200),
				   ANNEX_ADDR           NVARCHAR2(200),
				   XH                   NUMBER(3),
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_PUBLISH_NOTICE_UNIT_ANNE primary key (RID),
				   constraint FK_TD_PUB_NTC_UNIT_ANNEX1 foreign key (MAIN_ID)
      					references TD_PUBLISH_NOTICE_UNIT (RID)
           		)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>341</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PUB_NTC_UNIT_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PUB_NTC_UNIT_ANNEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>342</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CONTRA_SUB')
                AND COLUMN_NAME = UPPER('BUS_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_SUB ADD BUS_DESC VARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>343</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CONTRA_SUB')
                AND COLUMN_NAME = UPPER('LEFT_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_SUB ADD LEFT_DESC VARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>344</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_EXPORT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_EXPORT
                    (
                       RID                  INTEGER              not null,
                       BUS_TYPE_ID          INTEGER,
                       EXPORT_CONDITION     CLOB,
                       EXPORT_CONDITION_SHOW CLOB,
                       EXPORT_DATE          DATE,
                       STATE                NUMBER(1),
                       OPER_UNIT_ID         INTEGER,
                       OPER_PSN_ID          INTEGER,
                       EXPORT_FILE_NAME     NVARCHAR2(200),
                       EXPORT_FILE_PATH     NVARCHAR2(200),
                       EXPORT_FILE_DATE     DATE,
                       ERROR_MSG            NVARCHAR2(1000),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_EXPORT primary key (RID),
                       constraint FK_TD_TJ_EXPORT3 foreign key (BUS_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                        constraint FK_TD_TJ_EXPORT1 foreign key (OPER_UNIT_ID)
                        references TS_UNIT (RID),
                        constraint FK_TD_TJ_EXPORT2 foreign key (OPER_PSN_ID)
                        references TS_USER_INFO (RID)
                    )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>345</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_EXPORT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_EXPORT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1000  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>346</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TS_USER_LOGIN_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
				create table TS_USER_LOGIN_RCD 
				(
				   RID                  INTEGER              not null,
				   USER_ID              INTEGER              not null,
				   MOBILE_NO            NVARCHAR2(20),
				   CHECK_CODE           NVARCHAR2(10),
				   SEND_STATE           NUMBER(1),
				   NOTICE_CONT          NVARCHAR2(1000),
				   REQUEST_IDENTIFIER   NVARCHAR2(50),
				   ERR_MSG              NVARCHAR2(1000),
				   VALID_DATE           DATE,
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TS_USER_LOGIN_RCD primary key (RID),
				   constraint FK_TS_USER_LOGIN_RCD1 foreign key (USER_ID)
      					references TS_USER_INFO (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
		<ver>347</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TS_USER_LOGIN_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TS_USER_LOGIN_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>348</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>

		</sql>
		<ver>349</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE NUM NUMBER ;
			BEGIN
				SELECT
					COUNT (1) INTO NUM
				FROM
					USER_TAB_COLUMNS
				WHERE
						TABLE_NAME = 'TD_ZYWS_UP_COUNTRY'
				  AND COLUMN_NAME = 'STATE' AND DATA_PRECISION=1 ;
				IF NUM = 1 THEN
					EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_UP_COUNTRY MODIFY STATE INTEGER' ;
				END IF ;
			END ;
          ]]>
		</sql>
		<ver>350</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_TRAS_PLAT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_TJ_TRAS_PLAT (
					RID                  INTEGER              not null,
					UNIT_NAME            VARCHAR2(100)        not null,
					UNIT_CODE            VARCHAR2(50)         not null,
					REG_CODE             VARCHAR2(50)         not null,
					CRPT_ZONE_CODES      NVARCHAR2(100),
					STOP_TAG             NUMBER(1)            default 1 not null,
					CREATE_DATE          TIMESTAMP            not null,
					CREATE_MANID         INTEGER              not null,
					constraint PK_TB_TJ_TRAS_PLAT primary key (RID))';
              END IF;
            END;
          ]]>
		</sql>
		<ver>351</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_TRAS_PLAT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_TRAS_PLAT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>352</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE NUM NUMBER ;
			BEGIN
				SELECT
					COUNT (1) INTO NUM
				FROM
					USER_TAB_COLUMNS
				WHERE
						TABLE_NAME = 'TB_TJ_TRAS_PLAT'
				  AND COLUMN_NAME = 'BUS_TYPE' ;
				IF NUM = 0 THEN
					EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_TRAS_PLAT ADD BUS_TYPE NUMBER(2)' ;
				END
					IF ;
			END ;
          ]]>
		</sql>
		<ver>353</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ROLE')
                AND COLUMN_NAME = UPPER('ROLE_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ROLE ADD ROLE_CODE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>354</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('WRITE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD WRITE_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>355</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SYSTEM_PARAM')
                AND COLUMN_NAME = UPPER('PARAM_VALUE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SYSTEM_PARAM MODIFY PARAM_VALUE NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>356</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('TITLE_DESC')
                AND CHAR_LENGTH < 1000;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL MODIFY TITLE_DESC VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>357</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('ANS_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD ANS_DESC NVARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>358</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROB_ANS_DETAIL')
                AND COLUMN_NAME = UPPER('SCORE_VALUE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROB_ANS_DETAIL MODIFY SCORE_VALUE VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>359</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROB_ANS_DETAIL')
                AND COLUMN_NAME = UPPER('FILL_VALUE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROB_ANS_DETAIL MODIFY FILL_VALUE VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>360</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('ZONE_SHORT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD ZONE_SHORT_NAME NVARCHAR2(30)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>361</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS5')
                AND CHAR_LENGTH < 500;
              IF NUM > 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS5 NVARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>362</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SYSTEM_PARAM')
                AND COLUMN_NAME = UPPER('PARAM_NAME')
                AND CHAR_LENGTH != 50;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SYSTEM_PARAM MODIFY PARAM_NAME VARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>363</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CONTRA_SUB')
                AND COLUMN_NAME = UPPER('RIGHT_CODE')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_SUB MODIFY RIGHT_CODE VARCHAR2(100) NULL';
              END IF;
            END;
          ]]>
		</sql>
		<ver>364</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS7');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS7 NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>365</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('TRUST_WRITE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD TRUST_WRITE_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>366</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('PROVE_WRITE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD PROVE_WRITE_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>367</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('RED_UNIT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD RED_UNIT_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>368</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_COLSDEFINE')
                AND COLUMN_NAME = UPPER('EXEC_SCRIPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_COLSDEFINE ADD EXEC_SCRIPT CLOB ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>369</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PRO_POOL_COLSDEFINE')
                AND COLUMN_NAME = UPPER('EXEC_SCRIPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PRO_POOL_COLSDEFINE ADD EXEC_SCRIPT CLOB ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>370</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'OPTION_SCORE'
                AND DATA_TYPE = 'NUMBER'
                AND DATA_PRECISION = '8';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL MODIFY OPTION_SCORE NUMBER(8,3)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>371</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TS_PROB_EXAMPOOL'
                AND COLUMN_NAME = 'NUM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD NUM INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>372</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS7')
  				AND CHAR_LENGTH = 50;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS7 NVARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>373</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('RIGHT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT ADD RIGHT_CODE NVARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>374</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SLIDE_MIN_DESC') AND DATA_LENGTH=20;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT MODIFY SLIDE_MIN_DESC VARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>375</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_SUBJECT')
                AND COLUMN_NAME = UPPER('SLIDE_MAX_DESC') AND DATA_LENGTH=20;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_SUBJECT MODIFY SLIDE_MAX_DESC VARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>376</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('SLIDE_MAX_DESC') AND DATA_LENGTH=20;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL MODIFY SLIDE_MAX_DESC VARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>377</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('SLIDE_MIN_DESC') AND DATA_LENGTH=20;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL MODIFY SLIDE_MIN_DESC VARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>378</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PRO_POOL_TABDEFINE')
                AND COLUMN_NAME = UPPER('TAB_NAME') AND DATA_LENGTH=50;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PRO_POOL_TABDEFINE MODIFY TAB_NAME VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>379</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_PROB_TABDEFINE')
                AND COLUMN_NAME = UPPER('TAB_NAME') AND DATA_LENGTH=50;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_PROB_TABDEFINE MODIFY TAB_NAME VARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>380</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('HARD_LEVEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD HARD_LEVEL NUMBER(2)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>381</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('THEME_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD THEME_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>382</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_USER_INFO5'
                AND TABLE_NAME = 'TS_USER_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TS_USER_INFO add constraint FK_TS_USER_INFO5 foreign key (THEME_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>383</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS7') AND CHAR_LENGTH=500;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS7 NVARCHAR2(2000) ';
              END IF;
            END;
          ]]>
		</sql>
		<ver>384</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('CEN_COORD_XAXIS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CEN_COORD_XAXIS NVARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>385</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('CEN_COORD_YAXIS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD CEN_COORD_YAXIS NVARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>386</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('TIP_COORD_XAXIS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD TIP_COORD_XAXIS NVARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>387</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('TIP_COORD_YAXIS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD TIP_COORD_YAXIS NVARCHAR2(20)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>388</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('HARD_LEVEL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD HARD_LEVEL_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>389</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('UNIT_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD UNIT_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>390</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('UNIT_GRADE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD UNIT_GRADE_ID INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>391</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_UNIT5'
                AND TABLE_NAME = 'TS_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TS_UNIT add constraint FK_TS_UNIT5 foreign key (UNIT_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>392</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_UNIT6'
                AND TABLE_NAME = 'TS_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TS_UNIT add constraint FK_TS_UNIT6 foreign key (UNIT_GRADE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>393</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TS_PROB_EXAMPOOL3'
                AND TABLE_NAME = 'TS_PROB_EXAMPOOL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TS_PROB_EXAMPOOL add constraint FK_TS_PROB_EXAMPOOL3 foreign key (HARD_LEVEL_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>394</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS8');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE ADD EXTENDS8 NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>395</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PUBLISH_NOTICE')
                AND COLUMN_NAME = UPPER('IF_NEED_FEEDBACK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PUBLISH_NOTICE ADD IF_NEED_FEEDBACK NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>396</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_PROB_EXAMPOOL')
                AND COLUMN_NAME = UPPER('EXEC_SCRIPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_PROB_EXAMPOOL ADD EXEC_SCRIPT CLOB';
              END IF;
            END;
          ]]>
		</sql>
		<ver>397</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('PWD_BEG_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD PWD_BEG_DATE DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>398</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('PWD_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD PWD_END_DATE DATE';
              END IF;
            END;
          ]]>
		</sql>
		<ver>399</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_MENU')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_MENU ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>400</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_MENU')
                AND COLUMN_NAME = UPPER('ERR_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_MENU ADD ERR_RSN NVARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>401</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CODE_TYPE')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CODE_TYPE ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>402</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CODE_TYPE')
                AND COLUMN_NAME = UPPER('ERR_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CODE_TYPE ADD ERR_RSN NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>403</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_CODE_TYPE')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_CODE_TYPE ADD RMK NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>404</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>405</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_ZONE')
                AND COLUMN_NAME = UPPER('ERR_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_ZONE ADD ERR_RSN NVARCHAR2(200)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>406</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('WRITE_NO_RULE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD WRITE_NO_RULE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>407</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS5')
  				AND CHAR_LENGTH < 1000;
              IF NUM > 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS5 NVARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>408</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS6')
  				AND CHAR_LENGTH < 1000;
              IF NUM > 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS6 NVARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>409</ver>
	</sqlsentence>
  <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('EXTENDS3')
                AND DATA_TYPE = UPPER('VARCHAR2')
                AND CHAR_LENGTH = 100;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY EXTENDS3 NVARCHAR2(1000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>410</ver>
    </sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('JD_PROVE_BAK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD JD_PROVE_BAK NVARCHAR2(500)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>411</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_EXPORT')
                AND COLUMN_NAME = UPPER('MODULE_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_EXPORT ADD MODULE_TYPE NUMBER(2)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>412</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TS_CONTRA_MAIN')
                    AND COLUMN_NAME = UPPER('RMK');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TS_CONTRA_MAIN ADD RMK NVARCHAR2(1000)';
                END IF;
            END;
            ]]>
		</sql>
		<ver>413</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              BEGIN
                EXECUTE IMMEDIATE 'ALTER SEQUENCE TS_CONTRA_SUB_SEQ CACHE 1000';
              END;
          ]]>
		</sql>
		<ver>414</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_EXPORT')
                AND COLUMN_NAME = UPPER('DEL_FILE_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_EXPORT ADD DEL_FILE_STATE INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>415</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_EXPORT')
                AND COLUMN_NAME = UPPER('DEL_FILE_ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_EXPORT ADD DEL_FILE_ERROR_MSG NVARCHAR2(1000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>416</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_SIMPLE_CODE')
                AND COLUMN_NAME = UPPER('CODE_NAME') AND CHAR_LENGTH<2000;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_SIMPLE_CODE MODIFY CODE_NAME VARCHAR2(2000)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>417</ver>
	</sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_UNIT')
                AND COLUMN_NAME = UPPER('MASTER_UNIT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_UNIT ADD MASTER_UNIT_CODE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>418</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-system 系统基础模块升级 -->