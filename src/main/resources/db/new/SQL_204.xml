<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职业卫生平台-专业技术人员考核</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PROCHK_EXTRACT_PSN (
                   RID                  INTEGER              not null,
                   UNIT_ID              INTEGER,
                   ZONE_ID              INTEGER,
                   UNIT_NAME            NVARCHAR2(200),
                   PRO_PSN_ID           INTEGER,
                   PSN_NAME             NVARCHAR2(50),
                   CHECK_TYPE           NUMBER(1),
                   EXTRACT_DATE         DATE,
                   ANS_TIME             DATE,
                   TOTAL_SCORE          NUMBER(5,2),
                   SCORE                NUMBER(5,2),
                   NOTICE_CONTENT       NVARCHAR2(200),
                   USER_NO              NVARCHAR2(50),
                   <PERSON><PERSON><PERSON>N_PWD            NVARCHAR2(50),
                   USER_ID              INTEGER,
                   STATE                NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXTRACT_PSN primary key (RID),
                   constraint FK_TD_PROCHK_EXTRACT_PSN1 foreign key (UNIT_ID)
                        references TS_UNIT (RID),
                   constraint FK_TD_PROCHK_EXTRACT_PSN2 foreign key (ZONE_ID)
                        references TS_ZONE (RID),
                    constraint FK_TD_PROCHK_EXTRACT_PSN3 foreign key (PRO_PSN_ID)
                        references TD_ZW_PSNINFO (RID),
                    constraint FK_TD_PROCHK_EXTRACT_PSN4 foreign key (USER_ID)
                        references TS_USER_INFO (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXTRACT_PSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXTRACT_PSN_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_QUE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PROCHK_EXTRACT_QUE (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SUBJECT_ID           INTEGER,
                   SHOW_CODE            VARCHAR2(20),
                   NUM                  INTEGER              not null,
                   TOTAL_SCORE          NUMBER(5,2),
                   SCORE                NUMBER(5,2),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXTRACT_QUE primary key (RID),
                   constraint FK_TD_PROCHK_EXTRACT_QUE1 foreign key (MAIN_ID)
                        references TD_PROCHK_EXTRACT_PSN (RID),
                    constraint FK_TD_PROCHK_EXTRACT_QUE2 foreign key (SUBJECT_ID)
                        references TS_PROB_EXAMPOOL (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXTRACT_QUE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXTRACT_QUE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_PROCHK_EXTRACT_SUBJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_PROCHK_EXTRACT_SUBJECT (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   QUE_TYPE_ID          INTEGER,
                   EXTRACT_NUM          NUMBER(3),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TB_PROCHK_EXTRACT_SUBJECT primary key (RID),
                   constraint FK_TB_PROCHK_EXTRACT_SUBJECT1 foreign key (MAIN_ID)
                        references TD_PROCHK_EXTRACT_PSN (RID),
                   constraint FK_TB_PROCHK_EXTRACT_SUBJECT2 foreign key (QUE_TYPE_ID)
                        references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_PROCHK_EXTRACT_SUBJECT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_PROCHK_EXTRACT_SUBJECT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TS_USER_INFO')
                AND COLUMN_NAME = UPPER('USER_TYPE_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TS_USER_INFO ADD USER_TYPE_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('ANS_LIMIT_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD ANS_LIMIT_TIME NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_QUE')
                AND COLUMN_NAME = UPPER('SHOW_CODE')
                AND NULLABLE = UPPER('N');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_QUE MODIFY SHOW_CODE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('QUE_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD QUE_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('SCORE_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD SCORE_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('SCORE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD SCORE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXT_ARCH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PROCHK_EXT_ARCH
                (
                   RID                  INTEGER              not null,
                   BHK_ID               INTEGER,
                   PERSON_NAME          NVARCHAR2(200),
                   SEX                  NVARCHAR2(10),
                   BRTH                 NVARCHAR2(20),
                   ISXMRD               NVARCHAR2(10),
                   WORK_NAME            NVARCHAR2(200),
                   WRKLNT               NVARCHAR2(10),
                   TCHBADRSNTIM         NVARCHAR2(10),
                   ONGUARD_STATEID      INTEGER,
                   BHK_DATE             DATE ,
                   BHKRST               CLOB ,
                   BHK_RST_ID           INTEGER,
                   OCP_BHKRSTDES        CLOB,
                   MHKADV               CLOB,
                   QUE_TYPE_ID          INTEGER,
                   EXT_DATE             DATE,
                   EXT_UNIT_ID          INTEGER,
                   EXT_PSN_ID           INTEGER,
                   IF_PUBLISH           NUMBER(1),
                   PUBLISH_DATE         DATE,
                   DEL_MARK             NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXT_ARCH primary key (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH1 foreign key (BHK_RST_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH2 foreign key (ONGUARD_STATEID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH3 foreign key (QUE_TYPE_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH4 foreign key (EXT_UNIT_ID)
                    references TS_UNIT (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH5 foreign key (EXT_PSN_ID)
                    references TS_USER_INFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXT_ARCH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXT_ARCH_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXT_ARCH_QUE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_PROCHK_EXT_ARCH_QUE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SUBJECT_ID           INTEGER,
                   NUM                  INTEGER,
                   RST_TOTAL_SCORE      NUMBER(5,2),
                   OCP_TOTAL_SCORE      NUMBER(5,2),
                   MHKADV_TOTAL_SCORE   NUMBER(5,2),
                   BHKRST               CLOB,
                   OCP_BHKRSTDES        CLOB,
                   MHKADV               CLOB,
                   RST_SCORE            NUMBER(5,2),
                   OCP_SCORE            NUMBER(5,2),
                   MHKADV_SCORE         NUMBER(5,2),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXT_ARCH_QUE primary key (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH_QUE1 foreign key (MAIN_ID)
                    references TD_PROCHK_EXTRACT_PSN (RID),
                   constraint FK_TD_PROCHK_EXT_ARCH_QUE2 foreign key (SUBJECT_ID)
                    references TD_PROCHK_EXT_ARCH (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXT_ARCH_QUE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXT_ARCH_QUE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXT_BADRSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_PROCHK_EXT_BADRSN
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER              not null,
                   BADRSN_ID            INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXT_BADRSN primary key (RID),
                   constraint FK_TD_PROCHK_EXT_BADRSN1 foreign key (MAIN_ID)
                    references TD_PROCHK_EXT_ARCH (RID),
                   constraint FK_TD_PROCHK_EXT_BADRSN2 foreign key (BADRSN_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXT_BADRSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXT_BADRSN_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EMHISTORY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_PROCHK_EMHISTORY
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER              not null,
                   HIS_TYPE             NUMBER(1),
                   NUM                  INTEGER,
                   STASTP_DATE          NVARCHAR2(100),
                   UNIT_NAME            NVARCHAR2(100),
                   DEPARTMENT           NVARCHAR2(100),
                   WORK_TYPE            NVARCHAR2(50),
                   PRFRAYSRT            NVARCHAR2(1000),
                   DEFEND_STEP          NVARCHAR2(50),
                   PRFWRKLOD            NVARCHAR2(100),
                   PRFSHNVLU            NVARCHAR2(100),
                   PRFEXCSHN            NVARCHAR2(100),
                   PRFRAYSRT2           NVARCHAR2(500),
                   PRFRAYSRTCODS        NVARCHAR2(200),
                   FSSZL                NVARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EMHISTORY primary key (RID),
                   constraint FK_TD_PROCHK_EMHISTORY1 foreign key (MAIN_ID)
                      references TD_PROCHK_EXT_ARCH (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EMHISTORY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EMHISTORY_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXT_BHKSUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_PROCHK_EXT_BHKSUB
                (
                   RID                  INTEGER              not null,
                   BHK_ID               INTEGER,
                   ITEM_ID              INTEGER              not null,
                   ITEM_RST             CLOB,
                   ITEM_STDVALUE        NVARCHAR2(100),
                   MSRUNT               NVARCHAR2(50),
                   JDGPTN               NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXT_BHKSUB primary key (RID),
                   constraint FK_TD_PROCHK_EXT_BHKSUB1 foreign key (BHK_ID)
                    references TD_PROCHK_EXT_ARCH (RID),
                   constraint FK_TD_PROCHK_EXT_BHKSUB2 foreign key (ITEM_ID)
                    references TB_TJ_ITEMS (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXT_BHKSUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXT_BHKSUB_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_EXMSDATA';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_PROCHK_EXMSDATA
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER              not null,
                   MNRAGE               INTEGER,
                   MNS                  NVARCHAR2(10),
                   CYC                  NVARCHAR2(10),
                   MNLAGE               NVARCHAR2(10),
                   ISXMNS               NVARCHAR2(10),
                   CHLDQTY              NVARCHAR2(10),
                   ABRQTY               NVARCHAR2(10),
                   SLNKQTY              NVARCHAR2(10),
                   STLQTY               NVARCHAR2(10),
                   TRSQTY               NVARCHAR2(10),
                   CHLDHTHCND           NVARCHAR2(250),
                   MRYDAT               NVARCHAR2(50),
                   CPLRDTCND            NVARCHAR2(100),
                   CPLPRFHTHCND         NVARCHAR2(100),
                   SMKSTA               NVARCHAR2(10),
                   SMKDAYBLE            NVARCHAR2(50),
                   SMKYERQTY            NVARCHAR2(50),
                   WINSTA               NVARCHAR2(10),
                   WINDAYMLX            NVARCHAR2(50),
                   WINYERQTY            NVARCHAR2(50),
                   JZS                  NVARCHAR2(200),
                   GRS                  NVARCHAR2(200),
                   OTH                  NVARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_EXMSDATA primary key (RID),
                   constraint FK_TD_PROCHK_EXMSDATA1 foreign key (MAIN_ID)
                    references TD_PROCHK_EXT_ARCH (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_EXMSDATA_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_EXMSDATA_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_ANAMNESIS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_PROCHK_ANAMNESIS
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER              not null,
                   HSTNAM               NVARCHAR2(100),
                   HSTDAT               NVARCHAR2(100),
                   HSTUNT               NVARCHAR2(100),
                   HSTCRUPRC            NVARCHAR2(200),
                   HSTLPS               NVARCHAR2(50),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_ANAMNESIS primary key (RID),
                   constraint FK_TD_PROCHK_ANAMNESIS1 foreign key (MAIN_ID)
                    references TD_PROCHK_EXT_ARCH (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_ANAMNESIS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_ANAMNESIS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_PROCHK_SYMPTOM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_PROCHK_SYMPTOM
                (
                   RID                  INTEGER              not null,
                   BHK_ID               INTEGER,
                   SYM_NAME             NVARCHAR2(50),
                   OTHSYM               NVARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_PROCHK_SYMPTOM primary key (RID),
                   constraint FK_TD_PROCHK_SYMPTOM1 foreign key (BHK_ID)
                    references TD_PROCHK_EXT_ARCH (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_PROCHK_SYMPTOM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_PROCHK_SYMPTOM_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXT_ARCH')
                AND COLUMN_NAME = UPPER('BHK_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXT_ARCH ADD BHK_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PROCHK_EXTRACT_PSN5'
                AND TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN   ADD CONSTRAINT FK_TD_PROCHK_EXTRACT_PSN5 FOREIGN KEY (SCORE_UNIT_ID)      REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PROCHK_EXTRACT_PSN6'
                AND TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN   ADD CONSTRAINT FK_TD_PROCHK_EXTRACT_PSN6 FOREIGN KEY (SCORE_PSN_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PROCHK_EXTRACT_PSN7'
                AND TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN   ADD CONSTRAINT FK_TD_PROCHK_EXTRACT_PSN7  FOREIGN KEY (QUE_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('IF_NEED_SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD IF_NEED_SCORE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('CUT_SCREEN_TIMES');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD CUT_SCREEN_TIMES NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
               INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('IF_START_EXAM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD IF_START_EXAM NUMBER(1) DEFAULT 0';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN'
                AND COLUMN_NAME = 'TOTAL_SCORE'
                AND DATA_TYPE = 'NUMBER'
                AND DATA_PRECISION = '8';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN MODIFY TOTAL_SCORE NUMBER(8,3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN'
                AND COLUMN_NAME = 'SCORE'
                AND DATA_TYPE = 'NUMBER'
                AND DATA_PRECISION = '8';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN MODIFY SCORE NUMBER(8,3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_QUE'
                AND COLUMN_NAME = 'TOTAL_SCORE'
                AND DATA_TYPE = 'NUMBER'
                AND DATA_PRECISION = '8';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_QUE MODIFY TOTAL_SCORE NUMBER(8,3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_QUE'
                AND COLUMN_NAME = 'SCORE'
                AND DATA_TYPE = 'NUMBER'
                AND DATA_PRECISION = '8';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_QUE MODIFY SCORE NUMBER(8,3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN'
                AND COLUMN_NAME = 'EXAM_DESC';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD EXAM_DESC NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_CHECK_PLAN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_CHECK_PLAN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_CHECK_PLAN (
              RID INTEGER NOT NULL,
              TYPE_ID INTEGER NOT NULL,
              CHECK_NAME NVARCHAR2 (50) NOT NULL,
              CHECK_OBJS NVARCHAR2 (50),
              CHECK_CONTENT NVARCHAR2 (1000),
              OTHER_ITEMS NVARCHAR2 (1000),
              FILE_PATH NVARCHAR2 (200),
              SIGN_END_DATE DATE,
              PUBISH_UNIT_ID INTEGER,
              PUBLISH_DATE DATE,
              APPOINT_END_DATE DATE,
              STATE NUMBER(2),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_CHECK_PLAN PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_CHECK_PLAN1 FOREIGN KEY (TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZK_CHECK_PLAN2 FOREIGN KEY (PUBISH_UNIT_ID) REFERENCES TS_UNIT (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_CHECK_TIMES';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_CHECK_TIMES';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_CHECK_TIMES (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              CHECK_TIME NVARCHAR2 (20) NOT NULL,
              CHECK_BEG_DATE DATE,
              CHECK_END_DATE DATE,
              CHECK_PLACE NVARCHAR2 (100),
              REG_PSNS NUMBER(3),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_CHECK_TIMES PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_CHECK_TIMES1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_CHECK_PLAN (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_CHECK_SIGN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_CHECK_SIGN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_CHECK_SIGN (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              UNIT_ID INTEGER,
              PSN_ID INTEGER,
              ORG_TYPE NUMBER(1),
              BACK_RSN NVARCHAR2 (100),
              STATE NUMBER(2),
              TIMES_ID INTEGER,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_CHECK_SIGN PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_CHECK_SIGN1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_CHECK_PLAN (RID),
              CONSTRAINT FK_TD_ZK_CHECK_SIGN2 FOREIGN KEY (UNIT_ID) REFERENCES TS_UNIT (RID),
              CONSTRAINT FK_TD_ZK_CHECK_SIGN3 FOREIGN KEY (PSN_ID) REFERENCES TD_ZW_PSNINFO (RID),
              CONSTRAINT FK_TD_ZK_CHECK_SIGN4 FOREIGN KEY (TIMES_ID) REFERENCES TD_ZK_CHECK_TIMES (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('DATA_SOURCE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD DATA_SOURCE NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('ITEM_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD ITEM_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('BUS_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD BUS_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_PROCHK_EXTRACT_PSN8'
                AND TABLE_NAME = 'TD_PROCHK_EXTRACT_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN   ADD CONSTRAINT FK_TD_PROCHK_EXTRACT_PSN8  FOREIGN KEY (ITEM_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_PROCHK_EXTRACT_PSN')
                AND COLUMN_NAME = UPPER('SCENE_PHOTO_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_PROCHK_EXTRACT_PSN ADD SCENE_PHOTO_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZYWS_CHECK_PLAN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZYWS_CHECK_PLAN (
              RID INTEGER NOT NULL,
              CHECK_NAME NVARCHAR2 (50) NOT NULL,
              CHECK_OBJS NVARCHAR2 (200),
              CHECK_CONTENT NVARCHAR2 (1000),
              OTHER_ITEMS NVARCHAR2 (1000),
              FILE_PATH NVARCHAR2 (200),
              SIGN_END_DATE DATE,
              PUBISH_UNIT_ID INTEGER,
              PUBLISH_DATE DATE,
              STATE NUMBER(2),
              DEL_MARK NUMBER(1),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZYWS_CHECK_PLAN PRIMARY KEY (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZYWS_CHECK_PLAN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZYWS_CHECK_SIGN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZYWS_CHECK_SIGN (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              UNIT_ID INTEGER,
              PSN_ID INTEGER,
              MAJOR_ID INTEGER,
              IF_RETIRE NUMBER(1),
              BACK_RSN NVARCHAR2 (100),
              STATE NUMBER(2),
              DEL_MARK NUMBER(1),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZYWS_CHECK_SIGN PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_SIGN1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZYWS_CHECK_PLAN (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_SIGN2 FOREIGN KEY (UNIT_ID) REFERENCES TS_UNIT (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_SIGN3 FOREIGN KEY (PSN_ID) REFERENCES TD_ZW_PSNINFO (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_SIGN4 FOREIGN KEY (MAJOR_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZYWS_CHECK_SIGN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZYWS_CHECK_SIGN_FILE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZYWS_CHECK_SIGN_FILE (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              FILE_TYPE_ID INTEGER,
              FILE_ADDR NVARCHAR2 (200),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZYWS_CHECK_SIGN_FILE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_SIGN_FILE2 FOREIGN KEY (FILE_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_SIGN_FILE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZYWS_CHECK_SIGN (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZYWS_CHECK_SIGN_FILE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZYWS_CHECK_UNIT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZYWS_CHECK_UNIT (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              UNIT_ID INTEGER,
              APPLY_TYPE_ID INTEGER,
              APPLY_DATE DATE,
              FILL_RSN NVARCHAR2 (50),
              FILL_PHONE NVARCHAR2 (20),
              FILE_ADDR NVARCHAR2 (200),
              FILE_DEL NUMBER(1),
              STATE NUMBER(2),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZYWS_CHECK_UNIT PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_UNIT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZYWS_CHECK_PLAN (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_UNIT2 FOREIGN KEY (UNIT_ID) REFERENCES TD_ZW_OCCHETH_INFO (RID),
              CONSTRAINT FK_TD_ZYWS_CHECK_UNIT3 FOREIGN KEY (APPLY_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZYWS_CHECK_UNIT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_CARD_NO');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD EXAM_CARD_NO NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_FIELD_NO');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD EXAM_FIELD_NO NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_ROOM_NO');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD EXAM_ROOM_NO NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_SEAT_NO');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD EXAM_SEAT_NO NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_ADDRESS');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD EXAM_ADDRESS NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_TIME');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD EXAM_TIME NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_SIGN')
                    AND COLUMN_NAME = UPPER('EXAM_RESULT');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_SIGN ADD  EXAM_RESULT	INTEGER';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_PLAN')
                    AND COLUMN_NAME = UPPER('SUBMIT_DATE');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_PLAN ADD  SUBMIT_DATE	TIMESTAMP';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZYWS_CHECK_UNIT')
                    AND COLUMN_NAME = UPPER('FILE_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZYWS_CHECK_UNIT ADD FILE_PATH NVARCHAR2(200)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-check-que 专业技术人员考核相关模块升级 -->