<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职业卫生平台-职业病重点危害因素监测</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_INSTINFO (RID                  INTEGER              not null,NUM                  NUMBER(4),INST_NAME            VARCHAR2(100)        not null,INST_MODEL           VARCHAR2(50),CUST_NO              VARCHAR2(50),PRDU_NO              VARCHAR2(50),OUTTER               NUMBER(1),OUT_UNIT             VARCHAR2(200),ORG_ID               INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,INST_TYPE            NUMBER(1),INST_KIND_ID         INTEGER,INST_KIND_DETAL_ID   INTEGER,INST_FACTORY         VARCHAR2(200),INST_PRO_DATE        DATE,WHERE_STORE          VARCHAR2(100),INST_CODE            VARCHAR2(50),ACPT_DATE            DATE,ACPT_CONCLU          VARCHAR2(200),LAST_ACPT_DATE       DATE,LAST_ACPT_CIRCLE     INTEGER,ACPT_UNIT            VARCHAR2(100),ACPT_END_DATE        DATE,IF_NEED_ACPT         NUMBER(1),IF_SUPT_OUT_CHK      NUMBER(1),PURCHASE_DATE        DATE,ACPT_REQ_ID          INTEGER,INST_STATE_ID        INTEGER,constraint PK_TD_ZW_INSTINFO primary key (RID),constraint FK_TD_ZW_INSTINFO4 foreign key (ACPT_REQ_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_ZW_INSTINFO5 foreign key (INST_STATE_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_ZW_INSTINFO3 foreign key (INST_KIND_DETAL_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_ZW_INSTINFO2 foreign key (INST_KIND_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_ZW_INSTINFO1 foreign key (ORG_ID) references TS_UNIT (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_YSJC_JC_STD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_YSJC_JC_STD (RID                  INTEGER              not null,STD_NO               NVARCHAR2(50),STD_NAME             NVARCHAR2(100),PUB_DATE             DATE,STOP_DATE            DATE,IMPL_DATE            DATE,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,CONSTRAINT PK_TB_YSJC_JC_STD PRIMARY KEY (RID))   ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_JC_STD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TB_YSJC_JC_STD_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_YSJC_LIMIT_VAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_YSJC_LIMIT_VAL (RID                  INTEGER              not null,STD_ID               INTEGER,BADRSN_ID            INTEGER,RSN_CN_NAME          NVARCHAR2(50),PYM                  NVARCHAR2(50),RSN_EN_NAME          NVARCHAR2(100),CAS_NO               NVARCHAR2(50),NUM                  NUMBER(4),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TB_YSJC_LIMIT_VAL primary key (RID),constraint FK_TB_YSJC_LIMIT_VAL1 foreign key (STD_ID) references TB_YSJC_JC_STD (RID),constraint FK_TB_YSJC_LIMIT_VAL2 foreign key (BADRSN_ID) references TS_SIMPLE_CODE (RID))  ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_LIMIT_VAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_YSJC_LIMIT_VAL_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_YSJC_RSN_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_YSJC_RSN_REL_ITEM (RID                  INTEGER              not null,MAIN_ID              INTEGER,ITEM_ID              INTEGER,ITEM_DESC            NVARCHAR2(50),MSRUNT_ID            INTEGER,NUM                  NUMBER(4),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TB_YSJC_RSN_REL_ITEM primary key (RID),constraint FK_TB_YSJC_RSN_REL_ITEM1 foreign key (MAIN_ID) references TB_YSJC_LIMIT_VAL (RID),constraint FK_TB_YSJC_RSN_REL_ITEM2 foreign key (MSRUNT_ID) references TS_SIMPLE_CODE (RID),constraint FK_TB_YSJC_RSN_REL_ITEM3 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_RSN_REL_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_YSJC_RSN_REL_ITEM_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_YSJC_LIMIT_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_YSJC_LIMIT_REL_ITEM (RID                  INTEGER              not null,MAIN_ID              INTEGER,LIMIT_ID             INTEGER,LIMIT_VAL            NUMBER(18,6),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TB_YSJC_LIMIT_REL_ITEM primary key (RID),constraint FK_TB_YSJC__FK_TB_YSJ_TB_YSJC_ foreign key (MAIN_ID) references TB_YSJC_RSN_REL_ITEM (RID),constraint FK_TB_YSJC_LIMIT_REL_ITEM2 foreign key (LIMIT_ID) references TS_SIMPLE_CODE (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_LIMIT_REL_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_YSJC_LIMIT_REL_ITEM_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_YSJC_CHK_ABILITY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_YSJC_CHK_ABILITY (RID                  INTEGER              not null,ORG_ID               INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TB_YSJC_CHK_ABILITY primary key (RID),constraint FK_TB_YSJC_CHK_ABILITY1 foreign key (ORG_ID) references TS_UNIT (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_CHK_ABILITY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_YSJC_CHK_ABILITY_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_LAW';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_TJ_LAW (RID                  INTEGER              not null,LAW_TYPE_ID          INTEGER,LAW_CODE             VARCHAR2(40),LAW_NAME             VARCHAR2(100),BRIEF_EXPLAIN        VARCHAR2(1000),DECREE_DATE          DATE,DECREE_ORGAN         VARCHAR2(200),FILE_NAME            VARCHAR2(200),FILE_PATH            VARCHAR2(200),STATE_MARK           NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TB_TJ_LAW primary key (RID),constraint FK_TB_TJ_LAW1 foreign key (LAW_TYPE_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_LAW_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_TJ_LAW_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_YSJC_CHK_ABILITY_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_YSJC_CHK_ABILITY_REL (RID                  INTEGER              not null,MAIN_ID              INTEGER,ITEM_ID              INTEGER,JC_WAY               NVARCHAR2(50),JC_BASE_ID           INTEGER,LAB_APPROVAL_TAG     NUMBER(1),QUAL_APPROVAL_TAG    NUMBER(1),OUT_TAG              NUMBER(1),STATE                NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TB_YSJC_CHK_ABILITY_REL primary key (RID),constraint FK_TB_YSJC_CHK_ABILITY_REL1 foreign key (MAIN_ID) references TB_YSJC_CHK_ABILITY (RID),constraint FK_TB_YSJC_CHK_ABILITY_REL2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID),constraint FK_TB_YSJC_CHK_ABILITY_REL3 foreign key (JC_BASE_ID) references TB_TJ_LAW (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_CHK_ABILITY_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_YSJC_CHK_ABILITY_REL_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_CHK_CONTRACT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_CHK_CONTRACT (RID                  INTEGER              not null,CRPT_ID              INTEGER,CRPT_NAME            NVARCHAR2(100)       not null,INSTITUTION_CODE     NVARCHAR2(50)        not null,ZONE_ID              INTEGER,ADDRESS              NVARCHAR2(200),INDUS_TYPE_ID        INTEGER,ECONOMY_ID           INTEGER,CRPT_SIZE_ID         INTEGER,LINKMAN2             NVARCHAR2(50),LINKPHONE2           NVARCHAR2(30),WORK_FORCE           INTEGER,OUTSOURCE_NUM        NUMBER(8),TASK_NO              NVARCHAR2(50),PRO_NAME             NVARCHAR2(50),JC_TYPE_ID           INTEGER,OTHER_JC_TYPE        NVARCHAR2(20),ORDER_DATE           DATE,PRE_COMPLETION_DATE  DATE,ANNEX_PATH           NVARCHAR2(200),CHK_ORG_ID           INTEGER,INVEST_DATE          DATE,SAMP_START_DATE      DATE,SAMP_END_DATE        DATE,JC_START_DATE        DATE,JC_END_DATE          DATE,STATE                NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_CHK_CONTRACT primary key (RID),constraint FK_TD_YSJC_CHK_CONTRACT1 foreign key (CRPT_ID) references TB_TJ_CRPT (RID),constraint FK_TD_YSJC_CHK_CONTRACT2 foreign key (ZONE_ID) references TS_ZONE (RID),constraint FK_TD_YSJC_CHK_CONTRACT3 foreign key (INDUS_TYPE_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_YSJC_CHK_CONTRACT4 foreign key (ECONOMY_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_YSJC_CHK_CONTRACT5 foreign key (CRPT_SIZE_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_YSJC_CHK_CONTRACT6 foreign key (JC_TYPE_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_YSJC_CHK_CONTRACT7 foreign key (CHK_ORG_ID) references TS_UNIT (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_CHK_CONTRACT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_CHK_CONTRACT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_USE_STD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_USE_STD (RID                  INTEGER              not null,MAIN_ID              INTEGER,STD_ID               INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_USE_STD primary key (RID),constraint FK_TD_YSJC_USE_STD1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID),constraint FK_TD_YSJC_USE_STD2 foreign key (STD_ID) references TB_YSJC_JC_STD (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_USE_STD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_USE_STD_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_LABOR_WORK_PLACE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_LABOR_WORK_PLACE (RID                  INTEGER              not null,MAIN_ID              INTEGER,WORK_PLACE           NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_LABOR_WORK_PLACE primary key (RID),constraint FK_TD_YSJC_LABOR_WORK_PLACE1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_LABOR_WORK_PLACE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_LABOR_WORK_PLACE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_LABOR_WORK_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_LABOR_WORK_POST (RID                  INTEGER              not null,MAIN_ID              INTEGER,POST                 NVARCHAR2(50),TOTAL_NUM            NUMBER(5),TIME_NUM             NUMBER(5),WORK_PLACE           NVARCHAR2(50),WORK_CONTENT         NVARCHAR2(50),WORK_WAY             NVARCHAR2(20),WORK_RULE            NVARCHAR2(20),PER_HOUR_DAY         NUMBER(3,1),PER_DAY_WEEK         NUMBER(2,1),PER_HOUR_WEEK        NUMBER(4,1),RMK                  NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_LABOR_WORK_POST primary key (RID),constraint FK_TD_YSJC_LABOR_WORK_POST1 foreign key (MAIN_ID) references TD_YSJC_LABOR_WORK_PLACE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_LABOR_WORK_POST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_LABOR_WORK_POST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_LABOR_WORK_BADRSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_LABOR_WORK_BADRSN (RID                  INTEGER              not null,MAIN_ID              INTEGER,BADRNS_ID            INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_LABOR_WORK_BADRSN primary key (RID),constraint FK_TD_YSJC_LABOR_WORK_BADRSN1 foreign key (MAIN_ID) references TD_YSJC_LABOR_WORK_POST (RID),constraint FK_TD_YSJC_LABOR_WORK_BADRSN2 foreign key (BADRNS_ID) references TB_YSJC_LIMIT_VAL (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_LABOR_WORK_BADRSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_LABOR_WORK_BADRSN_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_MATERIAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_MATERIAL (RID                  INTEGER              not null,MAIN_ID              INTEGER,MATERIAL             NVARCHAR2(50),BASIS                NVARCHAR2(200),USE_SECTION          NVARCHAR2(100),USE_AMOUNT           NVARCHAR2(100),SHAPE_PACKAGE        NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_MATERIAL primary key (RID),constraint FK_TD_YSJC_MATERIAL1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_MATERIAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_MATERIAL_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_PRODUCT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_PRODUCT (RID                  INTEGER              not null,MAIN_ID              INTEGER,PRO_NAME             NVARCHAR2(50),PRO_AMOUNT           NVARCHAR2(50),MEASURE_UNIT         NVARCHAR2(20),SHAPE                NVARCHAR2(50),PACKAGE              NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_PRODUCT primary key (RID),constraint FK_TD_YSJC_PRODUCT1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_PRODUCT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_PRODUCT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_PROCESS_EQU';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_PROCESS_EQU (RID                  INTEGER              not null,MAIN_ID              INTEGER,PROCESS_FLOW         NVARCHAR2(500),PRO_AMOUNT           NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_PROCESS_EQU primary key (RID),constraint FK_TD_YSJC_PROCESS_EQU1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_PROCESS_EQU_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_PROCESS_EQU_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_SAMP_RSN_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_SAMP_RSN_ITEM (RID                  INTEGER              not null,MAIN_ID              INTEGER,BADRSN_ID            INTEGER,ITEM_ID              INTEGER,JC_DATE              DATE,JC_WAY               NVARCHAR2(20),JC_BASE_ID           INTEGER,SAMP_SOURCE_ID       INTEGER,SAMP_HG_NUM   NUMBER(3),SAMP_NHG_NUM   NUMBER(3),STATE                NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_SAMP_RSN_ITEM primary key (RID),constraint FK_TD_YSJC_SAMP_RSN_ITEM1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID),constraint FK_TD_YSJC_SAMP_RSN_ITEM2 foreign key (BADRSN_ID) references TB_YSJC_LIMIT_VAL (RID),constraint FK_TD_YSJC_SAMP_RSN_ITEM3 foreign key (ITEM_ID) references TB_YSJC_RSN_REL_ITEM (RID),constraint FK_TD_YSJC_SAMP_RSN_ITEM4 foreign key (JC_BASE_ID) references TB_YSJC_CHK_ABILITY_REL (RID),constraint FK_TD_YSJC_SAMP_RSN_ITEM5 foreign key (SAMP_SOURCE_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_SAMP_RSN_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_SAMP_RSN_ITEM_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_SAMP_EQU';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_SAMP_EQU (RID                  INTEGER              not null,MAIN_ID              INTEGER,INST_ID              INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_SAMP_EQU primary key (RID),constraint FK_TD_YSJC_SAMP_EQU1 foreign key (MAIN_ID) references TD_YSJC_SAMP_RSN_ITEM (RID),constraint FK_TD_YSJC_SAMP_EQU2 foreign key (INST_ID) references TD_ZW_INSTINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_SAMP_EQU_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_SAMP_EQU_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_SAMP_POST (RID                  INTEGER              not null,MAIN_ID              INTEGER,POST                 NVARCHAR2(50),HG_TAG               NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_SAMP_POST primary key (RID),constraint FK_TD_YSJC_SAMP_POST1 foreign key (MAIN_ID) references TD_YSJC_SAMP_RSN_ITEM (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_SAMP_POST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_SAMP_POST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_SAMP_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_SAMP_INFO (RID                  INTEGER              not null,MAIN_ID              INTEGER,SAMP_NO              NVARCHAR2(50),SAMP_TYPE            NUMBER(1),SAMP_ADDR            NVARCHAR2(50),SAMP_DATE            DATE,SAMP_TIMES           NVARCHAR2(50),PER_HOUR_DAY         NUMBER(3,1),SHORT_TCH_MIN        NUMBER(4,1),CHK_VAL  NVARCHAR2(50),SAMP_TAG             NUMBER(1),RMK                  NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_SAMP_INFO primary key (RID),constraint FK_TD_YSJC_SAMP_INFO1 foreign key (MAIN_ID) references TD_YSJC_SAMP_POST (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_SAMP_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_SAMP_INFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_JC_EQU';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_JC_EQU (RID                  INTEGER              not null,MAIN_ID              INTEGER,INST_ID              INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_JC_EQU primary key (RID),constraint FK_TD_YSJC_JC_EQU1 foreign key (MAIN_ID) references TD_YSJC_SAMP_RSN_ITEM (RID),constraint FK_TD_YSJC_JC_EQU2 foreign key (INST_ID) references TD_ZW_INSTINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_JC_EQU_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_JC_EQU_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_JC_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_JC_RST (RID                  INTEGER              not null,SAMP_POST_ID         INTEGER,SAMP_ID              INTEGER,LIMIT_TYPE_ID        INTEGER,LIMIT_VAL            NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_YSJC_JC_RST primary key (RID),constraint FK_TD_YSJC_JC_RST1 foreign key (SAMP_POST_ID) references TD_YSJC_SAMP_POST (RID),constraint FK_TD_YSJC_JC_RST2 foreign key (LIMIT_TYPE_ID) references TB_YSJC_LIMIT_REL_ITEM (RID),constraint FK_TD_YSJC_JC_RST3 foreign key (SAMP_ID) references TD_YSJC_SAMP_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_JC_RST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_JC_RST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('ENTRUST_UNIT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD ENTRUST_UNIT_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_INFO')
                AND COLUMN_NAME = UPPER('PSN_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_INFO ADD PSN_NAME NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('LIMIT_NAME_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD LIMIT_NAME_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND CONSTRAINT_NAME = UPPER('FK_TD_YSJC_JC_RST4');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD CONSTRAINT FK_TD_YSJC_JC_RST4 FOREIGN KEY (LIMIT_NAME_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_INFO')
                AND COLUMN_NAME = UPPER('CHK_VAL_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_INFO ADD CHK_VAL_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('LIMIT_VAL_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD LIMIT_VAL_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('RPT_ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD RPT_ANNEX_PATH NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('MANAGE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD MANAGE_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('JC_TASK_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD JC_TASK_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('SIN_PRE_OPERTION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST ADD SIN_PRE_OPERTION NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('OCCP_PRE_OPERTION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST ADD OCCP_PRE_OPERTION NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('PER_HOUR_DAY')
                AND (DATA_PRECISION <4 OR DATA_SCALE < 2);
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST MODIFY PER_HOUR_DAY NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('PER_DAY_WEEK')
                AND (DATA_PRECISION <3 OR DATA_SCALE < 2);
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST MODIFY PER_DAY_WEEK NUMBER(3,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('PER_HOUR_WEEK')
                AND (DATA_PRECISION <5 OR DATA_SCALE < 2);
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST MODIFY PER_HOUR_WEEK NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_VAL')
                AND COLUMN_NAME = UPPER('SAMP_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_VAL ADD SAMP_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('OTHER_JC_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD OTHER_JC_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('REL_TASK_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD REL_TASK_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('CONTRACT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD CONTRACT_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('CONTRACT_CONT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD CONTRACT_CONT NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_MATERIAL')
                AND COLUMN_NAME = UPPER('AMT_MSRUNT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_MATERIAL ADD AMT_MSRUNT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_PRO_EQU_IMAGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_PRO_EQU_IMAGE (RID                  INTEGER              not null,MAIN_ID              INTEGER,FILE_PATH            NVARCHAR2(200),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER, constraint PK_TD_YSJC_PRO_EQU_IMAGE primary key (RID),constraint FK_TD_YSJC_PRO_EQU_IMAGE1 foreign key (MAIN_ID)
      references TD_YSJC_PROCESS_EQU (RID)
)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_PRO_EQU_IMAGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_YSJC_PRO_EQU_IMAGE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_RSN_ITEM')
                AND COLUMN_NAME = UPPER('JC_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_RSN_ITEM ADD JC_END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PER_HOUR_DAY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PER_HOUR_DAY NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PER_DAY_WEEK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PER_DAY_WEEK NUMBER(3,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PER_HOUR_WEEK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PER_HOUR_WEEK NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_INFO')
                AND COLUMN_NAME = UPPER('IF_BLANK_SAMP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_INFO ADD IF_BLANK_SAMP NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('IF_CONVER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD IF_CONVER NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('CONVER_LIMIT_VAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD CONVER_LIMIT_VAL NUMBER(18,6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONS_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_MATERIAL')
               AND COLUMN_NAME = UPPER('AMT_MSRUNT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_MATERIAL add CONSTRAINT FK_TD_YSJC_MATERIAL2 FOREIGN KEY (AMT_MSRUNT_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_INFO')
                AND COLUMN_NAME = UPPER('PER_HOUR_DAY')
                AND (DATA_PRECISION <4 OR DATA_SCALE < 2);
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_INFO MODIFY PER_HOUR_DAY NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_PROCESS_EQU')
                AND COLUMN_NAME = UPPER('PRO_AMOUNT') AND CHAR_LENGTH<200;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_PROCESS_EQU MODIFY PRO_AMOUNT NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('CHK_TIMES');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CHK_TIMES NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('SOUND_STD_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD SOUND_STD_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND CONSTRAINT_NAME = UPPER('FK_TD_YSJC_SAMP_POST2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST2 FOREIGN KEY (SOUND_STD_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PER_TCH_TIMES');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PER_TCH_TIMES NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_GT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD TCH_GT NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_GE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD TCH_GE NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_LT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD TCH_LT NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_LE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD TCH_LE NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('VAL_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD VAL_TYPE NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD NUM NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('PER_TCH_HOUR_DAY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST ADD PER_TCH_HOUR_DAY NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_CONTRACT_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_CONTRACT_ANNEX
                                    (
                                       RID                  INTEGER              not null,
                                       MAIN_ID              INTEGER,
                                       ANNEX_PATH           NVARCHAR2(200),
                                       ANNEX_NAME           NVARCHAR2(200),
                                       CREATE_DATE          TIMESTAMP            not null,
                                       CREATE_MANID         INTEGER              not null,
                                       MODIFY_DATE          TIMESTAMP,
                                       MODIFY_MANID         INTEGER,
                                       constraint PK_TD_YSJC_CONTRACT_ANNEX primary key (RID),
                                       constraint FK_TD_YSJC_CONTRACT_ANNEX1 foreign key (MAIN_ID) references TD_YSJC_CHK_CONTRACT (RID)
                                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_CONTRACT_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_YSJC_CONTRACT_ANNEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_INSTINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_INSTINFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_KIND_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_KIND_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_KIND_DETAL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_KIND_DETAL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_FACTORY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_FACTORY VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_PRO_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_PRO_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('WHERE_STORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD WHERE_STORE VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('ACPT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD ACPT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('ACPT_CONCLU');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD ACPT_CONCLU VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_INSTINFO2'
                AND TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO   ADD CONSTRAINT FK_TD_ZW_INSTINFO2 FOREIGN KEY (INST_KIND_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_INSTINFO3'
                AND TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO   ADD CONSTRAINT FK_TD_ZW_INSTINFO3 FOREIGN KEY (INST_KIND_DETAL_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('LAST_ACPT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD LAST_ACPT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('LAST_ACPT_CIRCLE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD LAST_ACPT_CIRCLE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('IF_NEED_ACPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD IF_NEED_ACPT NUMBER(1) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('IF_SUPT_OUT_CHK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD IF_SUPT_OUT_CHK NUMBER(1) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_LAW')
                AND COLUMN_NAME = UPPER('LAW_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_LAW ADD LAW_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TJ_LAW_REF_SIMPLECODE'
                AND TABLE_NAME = 'TB_TJ_LAW';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_LAW   ADD CONSTRAINT FK_TJ_LAW_REF_SIMPLECODE FOREIGN KEY (LAW_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_LAW')
                AND COLUMN_NAME = UPPER('LAW_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_LAW MODIFY LAW_CODE VARCHAR2(40)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_RSN_ITEM')
                AND COLUMN_NAME = UPPER('JC_ITEM_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_RSN_ITEM ADD JC_ITEM_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_RSN_ITEM6'
                AND TABLE_NAME = 'TD_YSJC_SAMP_RSN_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_RSN_ITEM ADD CONSTRAINT FK_TD_YSJC_SAMP_RSN_ITEM6 FOREIGN KEY (JC_ITEM_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_RSN_ITEM')
                AND COLUMN_NAME = UPPER('IF_OUTTEMP_GT30');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_RSN_ITEM ADD IF_OUTTEMP_GT30 NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('SPECTRAL_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD SPECTRAL_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST3'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST3 FOREIGN KEY (SPECTRAL_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('MEASUER_POS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD MEASUER_POS NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('FREQ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD FREQ NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('TCH_RATE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD TCH_RATE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>115</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PHYSICAL_INTENSITY_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PHYSICAL_INTENSITY_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>116</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST4'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST4 FOREIGN KEY (TCH_RATE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>117</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST5'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST5 FOREIGN KEY (PHYSICAL_INTENSITY_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>118</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_GT');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM MODIFY TCH_GT NUMBER(18,6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>119</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_GE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM MODIFY TCH_GE NUMBER(18,6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>120</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_LT');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM MODIFY TCH_LT NUMBER(18,6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>121</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_LE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM MODIFY TCH_LE NUMBER(18,6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>122</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_RSN_REL_ITEM')
                AND COLUMN_NAME = UPPER('ITEM2_MSRUNT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_RSN_REL_ITEM ADD ITEM2_MSRUNT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>123</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('JC_PART');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD JC_PART NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>124</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('SPECTRAL_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD SPECTRAL_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>125</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_JC_RST5'
                AND TABLE_NAME = 'TD_YSJC_JC_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD CONSTRAINT FK_TD_YSJC_JC_RST5 FOREIGN KEY (SPECTRAL_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>126</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD RMK NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>127</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_YSJC_RSN_REL_ITEM4'
                AND TABLE_NAME = 'TB_YSJC_RSN_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_RSN_REL_ITEM ADD CONSTRAINT FK_TB_YSJC_RSN_REL_ITEM4 FOREIGN KEY (ITEM2_MSRUNT_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>128</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('TCH_RATE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD TCH_RATE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>129</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_YSJC_LIMIT_REL_ITEM3'
                AND TABLE_NAME = 'TB_YSJC_LIMIT_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD CONSTRAINT FK_TB_YSJC_LIMIT_REL_ITEM3 FOREIGN KEY (TCH_RATE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>130</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('PHYSICAL_INTENSITY_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD PHYSICAL_INTENSITY_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>131</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_YSJC_LIMIT_REL_ITEM4'
                AND TABLE_NAME = 'TB_YSJC_LIMIT_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD CONSTRAINT FK_TB_YSJC_LIMIT_REL_ITEM4 FOREIGN KEY (PHYSICAL_INTENSITY_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>132</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD NUM INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>133</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('RAD_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD RAD_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>134</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('TCH_TIME_LIMIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD TCH_TIME_LIMIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>135</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PLACE_SOURCE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PLACE_SOURCE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>136</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('PLACE_SOURCE_HZ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD PLACE_SOURCE_HZ NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>137</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('JC_ITEMS_IDS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD JC_ITEMS_IDS NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>138</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD RMK NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>139</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('HG_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD HG_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>140</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('IF_BACK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD IF_BACK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>141</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_RSN_ITEM')
                AND COLUMN_NAME = UPPER('IF_RECAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_RSN_ITEM ADD IF_RECAL NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>142</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_YSJC_SAMP_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_YSJC_SAMP_PSN
                        (
                           RID                  INTEGER              not null,
                           MAIN_ID              INTEGER,
                           PSN_ID               INTEGER,
                           CREATE_DATE          TIMESTAMP            not null,
                           CREATE_MANID         INTEGER              not null,
                           MODIFY_DATE          TIMESTAMP,
                           MODIFY_MANID         INTEGER,
                           constraint PK_TD_YSJC_SAMP_PSN primary key (RID),
                           constraint FK_TD_YSJC_SAMP_PSN1 foreign key (MAIN_ID) references TD_YSJC_SAMP_RSN_ITEM (RID),
                           constraint FK_TD_YSJC_SAMP_PSN2 foreign key (PSN_ID) references TD_ZW_PSNINFO (RID)
                        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>143</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_YSJC_SAMP_PSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_YSJC_SAMP_PSN_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>144</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_PSN')
                AND COLUMN_NAME = UPPER('POST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_PSN ADD POST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>145</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_PSN3'
                AND TABLE_NAME = 'TD_YSJC_SAMP_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_PSN ADD CONSTRAINT FK_TD_YSJC_SAMP_PSN3 FOREIGN KEY (POST_ID) REFERENCES TD_YSJC_SAMP_POST (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>146</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_EQU')
                AND COLUMN_NAME = UPPER('POST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_EQU ADD POST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>147</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_EQU3'
                AND TABLE_NAME = 'TD_YSJC_SAMP_EQU';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_EQU ADD CONSTRAINT FK_TD_YSJC_SAMP_EQU3 FOREIGN KEY (POST_ID) REFERENCES TD_YSJC_SAMP_POST (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>148</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_LABOR_WORK_POST')
                AND COLUMN_NAME = UPPER('WORK_PLACE') AND CHAR_LENGTH<500;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_LABOR_WORK_POST MODIFY WORK_PLACE NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>149</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_INFO')
                AND COLUMN_NAME = UPPER('SAMP_ADDR') AND CHAR_LENGTH<500;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_INFO MODIFY SAMP_ADDR NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>150</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('SAMP_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD SAMP_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>151</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('POST_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD POST_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>152</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('MATCH_RULE1');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD MATCH_RULE1 NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>153</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_LIMIT_REL_ITEM')
                AND COLUMN_NAME = UPPER('MATCH_RULE2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_LIMIT_REL_ITEM ADD MATCH_RULE2 NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>154</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('RAD_TYPE_MCW_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD RAD_TYPE_MCW_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>155</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('MICRO_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD MICRO_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>156</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('RAD_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD RAD_TIME NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>157</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('SPEC_RANGE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD SPEC_RANGE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>158</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('WAVE_LEGTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD WAVE_LEGTH NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>159</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('SHINE_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD SHINE_TIME NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>160</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('MEASUER_POS_IDS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD MEASUER_POS_IDS NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>161</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('LIMIT_VAL_TXT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD LIMIT_VAL_TXT NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>162</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_JC_RST')
                AND COLUMN_NAME = UPPER('JC_PART_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD JC_PART_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>163</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST9'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST9 FOREIGN KEY (RAD_TYPE_MCW_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>164</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST10'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST10 FOREIGN KEY (MICRO_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>165</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST11'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD CONSTRAINT FK_TD_YSJC_SAMP_POST11 FOREIGN KEY (SPEC_RANGE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>166</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_JC_RST6'
                AND TABLE_NAME = 'TD_YSJC_JC_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_JC_RST ADD CONSTRAINT FK_TD_YSJC_JC_RST6 FOREIGN KEY (JC_PART_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>167</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_RSN_REL_ITEM')
                AND COLUMN_NAME = UPPER('SPECIAL_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_RSN_REL_ITEM ADD SPECIAL_TAG NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>168</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('RAD_TYPE_MCW_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD RAD_TYPE_MCW_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>169</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('MICRO_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD MICRO_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>170</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_SAMP_POST')
                AND COLUMN_NAME = UPPER('RAD_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_SAMP_POST ADD RAD_TIME NUMBER(4,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>171</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST9'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_YSJC_SAMP_POST add constraint FK_TD_YSJC_SAMP_POST9 foreign key (RAD_TYPE_MCW_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>172</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_YSJC_SAMP_POST10'
                AND TABLE_NAME = 'TD_YSJC_SAMP_POST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_YSJC_SAMP_POST add constraint FK_TD_YSJC_SAMP_POST10 foreign key (MICRO_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>173</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('SOURCE_CRPT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD SOURCE_CRPT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>174</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_YSJC_CHK_CONTRACT'
                AND CONSTRAINT_NAME = 'FK_TD_YSJC_CHK_CONTRACT9';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD constraint FK_TD_YSJC_CHK_CONTRACT9 foreign key (SOURCE_CRPT_ID) references TB_TJ_CRPT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>175</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_YSJC_CHK_ABILITY_REL')
                AND COLUMN_NAME = UPPER('LIMIT_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_YSJC_CHK_ABILITY_REL ADD LIMIT_DESC NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>176</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TB_YSJC_CHK_METHOD';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TB_YSJC_CHK_METHOD
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   METHOD_ID            INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TB_YSJC_CHK_METHOD primary key (RID),
                   constraint FK_TB_YSJC_CHK_METHOD1 foreign key (MAIN_ID)
                        references TB_YSJC_CHK_ABILITY_REL (RID),
                   constraint FK_TB_YSJC_CHK_METHOD2 foreign key (METHOD_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>177</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_YSJC_CHK_METHOD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_YSJC_CHK_METHOD_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>178</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_YSJC_CHK_CONTRACT')
                AND COLUMN_NAME = UPPER('UUID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_YSJC_CHK_CONTRACT ADD UUID VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>179</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-badrsn-check/web-heth-badrsn-check-cq 工作场所危害因素检测相关模块升级 -->