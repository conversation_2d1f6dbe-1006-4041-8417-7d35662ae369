<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职卫卫生-职业病诊断</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('RMK');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD RMK VARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('TCH_WORK_DAY');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD TCH_WORK_DAY NUMBER(2)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('WORK_YEAR');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD WORK_YEAR NUMBER(2)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('WORK_MONTH');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD WORK_MONTH NUMBER(2)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('WORK_DAY');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD WORK_DAY NUMBER(2)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('BEG_TCH_DUST');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD BEG_TCH_DUST DATE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('DATA_SUBMIT_ID');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD DATA_SUBMIT_ID INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('OTHER_DATA_SUBMIT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD OTHER_DATA_SUBMIT NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('PHOTO_PATH');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD PHOTO_PATH NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('SITE_PHOTO_PATH');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD SITE_PHOTO_PATH NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('IF_FIRST_DIAG');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD IF_FIRST_DIAG NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('IF_FIRST_JD');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD IF_FIRST_JD NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('IF_ALL_DATA');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD IF_ALL_DATA NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('XP_NO');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD XP_NO NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('XP_DATE');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD XP_DATE DATE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('RCD_STATE');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD RCD_STATE NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_BADRSN_HIS')
                  AND COLUMN_NAME = UPPER('WORK_TIME');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_BADRSN_HIS ADD WORK_TIME NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_BADRSN_HIS')
                  AND COLUMN_NAME = UPPER('PROTECTS');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_BADRSN_HIS ADD PROTECTS NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_DIAG_HIS';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                    create table TD_ZW_DIAG_HIS
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER              not null,
                       DIAG_UNIT            NVARCHAR2(100)       not null,
                       DIAG_DATE            DATE,
                       DIAG_RESULT          CLOB,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_DIAG_HIS primary key (RID),
                       constraint FK_TD_ZW_DIAG_HIS1 foreign key (MAIN_ID)
                         references TD_ZW_OCCDISCASE (RID)
                    )';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_DIAG_HIS_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_DIAG_HIS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_JD_HIS';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                    create table TD_ZW_JD_HIS
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER              not null,
                       JD_UNIT              NVARCHAR2(100)       not null,
                       JD_DATE              DATE,
                       JD_RESULT            CLOB,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_JD_HIS primary key (RID),
                       constraint FK_TD_ZW_JD_HIS1 foreign key (MAIN_ID)
                        references TD_ZW_OCCDISCASE (RID)
                    )';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_JD_HIS_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_JD_HIS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZW_DIAG_PROTOCOL';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_DIAG_PROTOCOL ( RID INTEGER NOT NULL, MAIN_ID INTEGER NOT NULL, WRIT_SORTID INTEGER NOT NULL, FIRST_MAN NVARCHAR2(50), FIRST_PHONE NVARCHAR2(20), SECOND_MAN NVARCHAR2(50), SECOND_PHONE NVARCHAR2(20), TAKE_WAY_ID INTEGER, FEE NUMBER(8,1), CREATE_DATE TIMESTAMP NOT NULL, CREATE_MANID INTEGER NOT NULL, MODIFY_DATE TIMESTAMP, MODIFY_MANID INTEGER, CONSTRAINT PK_TD_ZW_DIAG_PROTOCOL PRIMARY KEY (RID) )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TD_ZW_DIAG_PROTOCOL1'
                  AND TABLE_NAME = 'TD_ZW_DIAG_PROTOCOL';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_PROTOCOL ADD CONSTRAINT FK_TD_ZW_DIAG_PROTOCOL1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZW_OCCDISCASE (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TD_ZW_DIAG_PROTOCOL2'
                  AND TABLE_NAME = 'TD_ZW_DIAG_PROTOCOL';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_PROTOCOL ADD CONSTRAINT FK_TD_ZW_DIAG_PROTOCOL2 FOREIGN KEY (WRIT_SORTID) REFERENCES TB_ZW_WRITSORT (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAG_PROTOCOL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_DIAG_PROTOCOL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDISCASE')
                  AND COLUMN_NAME = UPPER('XP_RMK');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE ADD XP_RMK NVARCHAR2(200)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_PROTOCOL')
                  AND COLUMN_NAME = UPPER('ADDRESS');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_PROTOCOL ADD ADDRESS NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DEATH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_DEATH_RCD
                    (
                       RID                  INTEGER              not null,
                       PSN_NAME             NVARCHAR2(50),
                       IDC                  NVARCHAR2(50),
                       HYZK                 NUMBER(2),
                       DEATH_DATE           DATE,
                       LINK_FAMILY          NVARCHAR2(50),
                       LINK_PHONE           NVARCHAR2(20),
                       SWYY_A               NVARCHAR2(100),
                       SWYY_B               NVARCHAR2(100),
                       SWYY_C               NVARCHAR2(100),
                       SWYY_D               NVARCHAR2(100),
                       SWYY_E               NVARCHAR2(100),
                       GBSWYY               NVARCHAR2(100),
                       ICDBM                NVARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_DEATH_RCD primary key (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DEATH_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_DEATH_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_MANAGE_ORG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_MANAGE_ORG
                    (
                       RID                  INTEGER              not null,
                       ZONE_ID              INTEGER,
                       ORG_NAME             NVARCHAR2(50),
                       ORG_ADDR             NVARCHAR2(100),
                       LINK_TEL             NVARCHAR2(20),
                       ITEMS                NVARCHAR2(100),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_MANAGE_ORG primary key (RID),
                       constraint FK_TD_ZW_MANAGE_ORG1 foreign key (ZONE_ID) references TS_ZONE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_MANAGE_ORG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_MANAGE_ORG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDIS_CARD_NEW')
                  AND COLUMN_NAME = UPPER('STATE');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDIS_CARD_NEW ADD STATE NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_OCCDIS_CARD_NEW')
                  AND COLUMN_NAME = UPPER('ERR_MSG');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDIS_CARD_NEW ADD ERR_MSG NVARCHAR2(1000)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT
                    COUNT(1) INTO NUM
                FROM
                    USER_TABLES
                WHERE
                    TABLE_NAME = 'TD_ZW_ONETHING';
                IF NUM = 0 THEN EXECUTE IMMEDIATE
                    'CREATE TABLE TD_ZW_ONETHING (
                        RID INTEGER NOT NULL,
                        SBLSH_SHORT NVARCHAR2 (100) NOT NULL,
                        SXBM NVARCHAR2 (100),
                        XZQHDM NVARCHAR2 (100),
                        EXPRESS_TYPE NUMBER(1),
                        EXCHANGE_NO NVARCHAR2 (100),
                        CREATE_DATE TIMESTAMP NOT NULL,
                        CREATE_MANID INTEGER NOT NULL,
                        MODIFY_DATE TIMESTAMP,
                        MODIFY_MANID INTEGER,
                        CONSTRAINT PK_TD_ZW_ONETHING PRIMARY KEY (RID)
                    )';
                END IF;
            END;
             ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
            V1 NUMBER;
            V2 VARCHAR2(200);
            V3 VARCHAR2(200);
            BEGIN
            V2 := 'TD_ZW_ONETHING';
            V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
            SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
             ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = 'TD_ZW_OCCDISCASE'
                AND COLUMN_NAME = 'RMK'
                AND DATA_LENGTH = '100';
                IF NUM = 1 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCDISCASE MODIFY RMK NVARCHAR2(1000)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_ONETHING')
                  AND COLUMN_NAME = UPPER('STATE');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ONETHING ADD STATE NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_ZW_ONETHING')
                  AND COLUMN_NAME = UPPER('ERR_MSG');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ONETHING ADD ERR_MSG NVARCHAR2(200)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT
                    COUNT(1) INTO NUM
                FROM
                    USER_TABLES
                WHERE
                    TABLE_NAME = 'TD_ZW_ONETHING_MAIN';
                IF NUM = 0 THEN EXECUTE IMMEDIATE
                    'create table TD_ZW_ONETHING_MAIN
                (
                   RID                  INTEGER              not null,
                   SBLSH_SHORT          NVARCHAR2(100)       not null,
                   SXBM                 NVARCHAR2(100),
                   EXPRESS_TYPE         NUMBER(1),
                   PSN_NAME             NVARCHAR2(50),
                   SEX                  NUMBER(1),
                   LINK_TEL             NVARCHAR2(20),
                   IDC                  NVARCHAR2(50),
                   CREDIT_CODE          NVARCHAR2(50),
                   UNIT_NAME            NVARCHAR2(100),
                   TYPE_ID              INTEGER,
                   MANAGER_NAME         NVARCHAR2(50),
                   MANAGER_PHONE        NVARCHAR2(20),
                   BHK_DATE             DATE,
                   POST_NAME            NVARCHAR2(50),
                   ORG_ID               INTEGER,
                   STATE                NUMBER(2),
                   FILL_DATE            DATE,
                   ERROR_MSG            NVARCHAR2(1000),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_ONETHING_MAIN primary key (RID),
                   constraint FK_TD_ZW_ONETHING_MAIN2 foreign key (ORG_ID)
                    references TB_TJ_SRVORG (RID),
                   constraint FK_TD_ZW_ONETHING_MAIN1 foreign key (TYPE_ID)
                    references TS_SIMPLE_CODE (RID)
                    )';
                END IF;
            END;
             ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
            V1 NUMBER;
            V2 VARCHAR2(200);
            V3 VARCHAR2(200);
            BEGIN
            V2 := 'TD_ZW_ONETHING_MAIN';
            V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
            SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
             ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT
                    COUNT(1) INTO NUM
                FROM
                    USER_TABLES
                WHERE
                    TABLE_NAME = 'TD_ZW_ONETHING_SUB';
                IF NUM = 0 THEN EXECUTE IMMEDIATE
                    'create table TD_ZW_ONETHING_SUB
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER              not null,
               HAZARD_ID            INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_ONETHING_SUB primary key (RID),
               constraint FK_TD_ZW_ONETHING_SUB1 foreign key (MAIN_ID)
                  references TD_ZW_ONETHING_MAIN (RID),
               constraint FK_TD_ZW_ONETHING_SUB2 foreign key (HAZARD_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
                END IF;
            END;
             ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
            V1 NUMBER;
            V2 VARCHAR2(200);
            V3 VARCHAR2(200);
            BEGIN
            V2 := 'TD_ZW_ONETHING_SUB';
            V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
            SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
             ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
</sqlsentences>
