<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>重点职业病</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_ANALY_TYPE (   RID                  INTEGER              not null,   ANALY_TYPE           NUMBER(1),   RMK                  VARCHAR2(100),   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZDZYB_ANALY_TYPE primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_ANALY_DETAIL (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   XH                   NUMBER(5),   ANALY_ITEM_ID        INTEGER,   GE                   NUMBER(3),   GT                   NUMBER(3),   LE                   NUMBER(3),   LT                   NUMBER(3),   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZDZYB_ANALY_DETAIL primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_DETAIL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_DETAIL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_SPECIAL_ANALY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_SPECIAL_ANALY (   RID                  INTEGER              not null,   ANALY_TYPE           NUMBER(1),   BADRSN_ID            INTEGER,   ANALY_NAME           VARCHAR2(50),   ITEM_STDVALUE        VARCHAR2(100),   XH                   NUMBER(4),   RMK                  VARCHAR2(100),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_SPECIAL_ANALY primary key (RID),constraint FK_TD_ZDZYB_SPECIAL_ANALY1 foreign key (BADRSN_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_SPECIAL_ANALY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_SPECIAL_ANALY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_ITM_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_ANALY_ITM_TYPE (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   ITEM_NAME            VARCHAR2(50),   SHOW_NAME            VARCHAR2(50),   XH                   NUMBER(1),   RULE_LEVEL           NUMBER(2),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_ANALY_ITM_TYPE primary key (RID),constraint FK_TD_ZDZYB_ANALY_ITM_TYPE1 foreign key (MAIN_ID)      references TD_ZDZYB_SPECIAL_ANALY (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_ITM_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_ITM_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_PG_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_ANALY_PG_ITEM (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   ITEM_ID              INTEGER,   JDGPTN               NUMBER(1)            default 1 not null   constraint CKC_JDGPTN_TD_ZDZYB check (JDGPTN in (2,1)),   HG_FLAG              NUMBER(1),   GE                   NUMBER(18,6),   GT                   NUMBER(18,6),   LE                   NUMBER(18,6),   LT                   NUMBER(18,6),   XH                   NUMBER(2),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_ANALY_PG_ITEM primary key (RID),constraint FK_TD_ZDZYB_ANALY_PG_ITEM1 foreign key (MAIN_ID)      references TD_ZDZYB_ANALY_ITM_TYPE (RID),constraint FK_TD_ZDZYB_ANALY_PG_ITEM2 foreign key (ITEM_ID)      references TB_TJ_ITEMS (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_PG_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_PG_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_SPE_CRPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_SPE_CRPT (   RID                  INTEGER              not null,   BHK_YEAR             NUMBER(4),   BHK_MONTH            NUMBER(2),   ZONE_ID              INTEGER,   INDUS_ID             INTEGER,   ECONOMY_ID           INTEGER,   SIZE_ID              INTEGER,   ITEM_ID              INTEGER,   BHK_PSNS             NUMBER(10),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_SPE_CRPT primary key (RID),constraint FK_TD_ZDZYB_SPE_CRPT1 foreign key (ITEM_ID)      references TD_ZDZYB_ANALY_ITM_TYPE (RID),constraint FK_TD_ZDZYB_SPE_CRPT3 foreign key (ZONE_ID)      references TS_ZONE (RID),constraint FK_TD_ZDZYB_SPE_CRPT4 foreign key (SIZE_ID)      references TS_SIMPLE_CODE (RID),constraint FK_TD_ZDZYB_SPE_CRPT5 foreign key (ECONOMY_ID)      references TS_SIMPLE_CODE (RID),constraint FK_TD_ZDZYB_SPE_CRPT6 foreign key (INDUS_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_SPE_CRPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_SPE_CRPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_SPE_SEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_SPE_SEX (   RID                  INTEGER              not null,   BHK_YEAR             NUMBER(4),   BHK_MONTH            NUMBER(2),   ZONE_ID              INTEGER,   ITEM_ID              INTEGER,   SEX                  NUMBER(1),   BHK_PSNS             NUMBER(10),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_SPE_SEX primary key (RID),constraint FK_TD_ZDZYB_SPE_SEX1 foreign key (ITEM_ID)      references TD_ZDZYB_ANALY_ITM_TYPE (RID),constraint FK_TD_ZDZYB_SPE_SEX3 foreign key (ZONE_ID)      references TS_ZONE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_SPE_SEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_SPE_SEX_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_SPE_AGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_SPE_AGE (   RID                  INTEGER              not null,   BHK_YEAR             NUMBER(4),   BHK_MONTH            NUMBER(2),   ZONE_ID              INTEGER,   ITEM_ID              INTEGER,   AGE_ID               INTEGER,   BHK_PSNS             NUMBER(10),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_SPE_AGE primary key (RID),constraint FK_TD_ZDZYB_SPE_AGE1 foreign key (ITEM_ID)      references TD_ZDZYB_ANALY_ITM_TYPE (RID),constraint FK_TD_ZDZYB_SPE_AGE3 foreign key (ZONE_ID)      references TS_ZONE (RID),constraint FK_TD_ZDZYB_SPE_AGE4 foreign key (AGE_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_SPE_AGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_SPE_AGE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_SPE_WROK_YEAR';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_SPE_WROK_YEAR (   RID                  INTEGER              not null,   BHK_YEAR             NUMBER(4),   BHK_MONTH            NUMBER(2),   ZONE_ID              INTEGER,   ITEM_ID              INTEGER,   WORK_YEAR_ID         INTEGER,   BHK_PSNS             NUMBER(10),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_SPE_WROK_YEAR primary key (RID),constraint FK_TD_ZDZYB_SPE_WROK_YEAR1 foreign key (ITEM_ID)      references TD_ZDZYB_ANALY_ITM_TYPE (RID),constraint FK_TD_ZDZYB_SPE_WROK_YEAR3 foreign key (ZONE_ID)      references TS_ZONE (RID),constraint FK_TD_ZDZYB_SPE_WROK_YEAR4 foreign key (WORK_YEAR_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_SPE_WROK_YEAR_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_SPE_WROK_YEAR_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_DATA_LOG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_DATA_LOG (   RID                  INTEGER              not null,   TABLE_TYPE           NUMBER(1),   BHK_ID               INTEGER,   STATE                NUMBER,   ERR_MSG              VARCHAR2(2000),   CREATE_MANID         INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZDZYB_DATA_LOG primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_DATA_LOG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_DATA_LOG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_INDEXES
              WHERE INDEX_NAME = 'INDEX_TD_ZDZYB_DATA_LOG1';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE INDEX INDEX_TD_ZDZYB_DATA_LOG1 ON TD_ZDZYB_DATA_LOG (BHK_ID ASC)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_EXPORT_SETTING';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_ZDZYB_EXPORT_SETTING
        (
           RID                  INTEGER              not null,
           EXPORT_TYPE          NUMBER(1),
           BADRSN_ID            INTEGER,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           constraint PK_TD_ZDZYB_EXPORT_SETTING primary key (RID),
           constraint FK_TD_ZDZYB_EXPORT_SETTING1 foreign key (BADRSN_ID)
              references TS_SIMPLE_CODE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_EXPORT_SETTING_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_EXPORT_SETTING_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_BADRSN_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_ZDZYB_BADRSN_REL_ITEM
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           ITEM_ID              INTEGER,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           constraint PK_TD_ZDZYB_BADRSN_REL_ITEM primary key (RID),
           constraint FK_TD_ZDZYB_BADRSN_REL_ITEM1 foreign key (MAIN_ID)
              references TD_ZDZYB_EXPORT_SETTING (RID),
            constraint FK_TD_ZDZYB_BADRSN_REL_ITEM2 foreign key (ITEM_ID)
              references TS_SIMPLE_CODE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_BADRSN_REL_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_BADRSN_REL_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_TYPE')
                AND COLUMN_NAME = UPPER('BUS_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_TYPE ADD BUS_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('ITEM_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD ITEM_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_PG_ITEM')
                AND COLUMN_NAME = UPPER('RST_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_PG_ITEM ADD RST_DESC NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_PG_ITEM')
                AND COLUMN_NAME = UPPER('RST_MATCH_VAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_PG_ITEM ADD RST_MATCH_VAL VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_CRPT')
                AND COLUMN_NAME = UPPER('UPLOAD_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_CRPT ADD UPLOAD_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_CRPT')
                AND COLUMN_NAME = UPPER('UPLOAD_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_CRPT ADD UPLOAD_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_CRPT')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_CRPT ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_SEX')
                AND COLUMN_NAME = UPPER('UPLOAD_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_SEX ADD UPLOAD_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_SEX')
                AND COLUMN_NAME = UPPER('UPLOAD_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_SEX ADD UPLOAD_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_SEX')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_SEX ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_AGE')
                AND COLUMN_NAME = UPPER('UPLOAD_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_AGE ADD UPLOAD_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_AGE')
                AND COLUMN_NAME = UPPER('UPLOAD_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_AGE ADD UPLOAD_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_AGE')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_AGE ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_WROK_YEAR')
                AND COLUMN_NAME = UPPER('UPLOAD_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_WROK_YEAR ADD UPLOAD_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_WROK_YEAR')
                AND COLUMN_NAME = UPPER('UPLOAD_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_WROK_YEAR ADD UPLOAD_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPE_WROK_YEAR')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPE_WROK_YEAR ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RESULT_JUDGE_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_TJ_RESULT_JUDGE_ITEM
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           JUDGE_TYPE           NUMBER(1),
           ITEM_ID              INTEGER,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           constraint PK_TD_TJ_RESULT_JUDGE_ITEM primary key (RID),
           constraint FK_TD_TJ_RESULT_JUDGE_ITEM1 foreign key (MAIN_ID)
              references TD_TJ_BHK (RID),
                   constraint FK_TD_TJ_RESULT_JUDGE_ITEM2 foreign key (ITEM_ID)
              references TD_ZDZYB_ANALY_ITM_TYPE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RESULT_JUDGE_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RESULT_JUDGE_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_BHK_TRANS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_ZDZYB_BHK_TRANS
        (
           RID                  INTEGER              not null,
           BHK_ID               INTEGER,
           PERSON_ID            INTEGER,
           ZONE_ID              INTEGER,
           INDUS_ID             INTEGER,
           ECONOMY_ID           INTEGER,
                   SIZE_ID              INTEGER,
                   SEX                  NUMBER(1),
                   AGE_ID               INTEGER,
           WORK_YEAR_ID         INTEGER,
                   BHK_DATE             DATE,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZDZYB_BHK_TRANS primary key (RID),
           constraint FK_TD_ZDZYB_BHK_TRANS1 foreign key (BHK_ID)
              references TD_TJ_BHK (RID),
                   constraint FK_TD_ZDZYB_BHK_TRANS2 foreign key (PERSON_ID)
              references TD_TJ_PERSON (RID),
           constraint FK_TD_ZDZYB_BHK_TRANS3 foreign key (ZONE_ID)
              references TS_ZONE (RID),
           constraint FK_TD_ZDZYB_BHK_TRANS4 foreign key (INDUS_ID)
              references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZDZYB_BHK_TRANS5 foreign key (ECONOMY_ID)
              references TS_SIMPLE_CODE (RID),
           constraint FK_TD_ZDZYB_BHK_TRANS6 foreign key (SIZE_ID)
              references TS_SIMPLE_CODE (RID),
           constraint FK_TD_ZDZYB_BHK_TRANS7 foreign key (AGE_ID)
              references TS_SIMPLE_CODE (RID),
           constraint FK_TD_ZDZYB_BHK_TRANS8 foreign key (WORK_YEAR_ID)
              references TS_SIMPLE_CODE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_BHK_TRANS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_BHK_TRANS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RST_JUDGE_TRANS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_TJ_RST_JUDGE_TRANS
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           JUDGE_TYPE           NUMBER(1),
           ITEM_ID              INTEGER,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_TJ_RST_JUDGE_TRANS primary key (RID),
           constraint FK_TD_TJ_RST_JUDGE_TRANS1 foreign key (MAIN_ID)
              references TD_ZDZYB_BHK_TRANS (RID),
                   constraint FK_TD_TJ_RST_JUDGE_TRANS2 foreign key (ITEM_ID)
              references TD_ZDZYB_ANALY_ITM_TYPE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RST_JUDGE_TRANS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RST_JUDGE_TRANS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_SPECIAL_ANALY')
                AND COLUMN_NAME = UPPER('IF_OCC_CARCINOGENS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_SPECIAL_ANALY ADD IF_OCC_CARCINOGENS NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_BADRSN_NORMAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_ZDZYB_BADRSN_NORMAL
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           BADRSN_ID              INTEGER,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           constraint PK_TD_ZDZYB_BADRSN_NORMAL primary key (RID),
           constraint FK_TD_ZDZYB_BADRSN_NORMAL1 foreign key (MAIN_ID)
              references TD_ZDZYB_SPECIAL_ANALY (RID),
            constraint FK_TD_ZDZYB_BADRSN_NORMAL2 foreign key (BADRSN_ID)
              references TS_SIMPLE_CODE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_BADRSN_NORMAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_BADRSN_NORMAL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('IF_JUDGE_RESULT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD IF_JUDGE_RESULT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_PG_ITEM')
                AND COLUMN_NAME = UPPER('MSRUNT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_PG_ITEM ADD MSRUNT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_PG_ITEM')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('JDGPTN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_PG_ITEM MODIFY JDGPTN NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('EXPT_RATE_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD EXPT_RATE_NAME VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('ONGUARD_STATEID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD ONGUARD_STATEID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('JC_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD JC_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('KEY_INDUS_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD KEY_INDUS_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS10'
                AND TABLE_NAME = 'TD_ZDZYB_BHK_TRANS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZDZYB_BHK_TRANS add constraint FK_TD_ZDZYB_BHK_TRANS10 foreign key (KEY_INDUS_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_PG_ITEM')
                AND COLUMN_NAME = UPPER('RST_FLAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_PG_ITEM ADD RST_FLAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_DETAIL_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
        create table TD_ZDZYB_ANALY_DETAIL_SUB
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER,
                    ANALY_ITEM_ID        INTEGER,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    constraint PK_TD_ZDZYB_ANALY_DETAIL_SUB primary key (RID),
                    constraint FK_TD_ZDZYB_ANALY_DETAIL_SUB1 foreign key (MAIN_ID)
                    references TD_ZDZYB_ANALY_DETAIL (RID),
                    constraint FK_TD_ZDZYB_ANALY_DETAIL_SUB2 foreign key (ANALY_ITEM_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_DETAIL_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_DETAIL_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('RPT_PRINT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD RPT_PRINT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('CRPT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD CRPT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS11'
                AND TABLE_NAME = 'TD_ZDZYB_BHK_TRANS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZDZYB_BHK_TRANS add constraint FK_TD_ZDZYB_BHK_TRANS11 foreign key (CRPT_ID) references TB_TJ_CRPT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('YP_GT_25');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD YP_GT_25 NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('RST_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD RST_TAG NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('JC_TYPES');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD JC_TYPES NVARCHAR2(10)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('SHOW_NAME')
                AND CHAR_LENGTH = 100;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE MODIFY SHOW_NAME VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('RPT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD RPT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('SOURCE_CRPT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD SOURCE_CRPT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZDZYB_BHK_TRANS'
                AND CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS12';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD constraint FK_TD_ZDZYB_BHK_TRANS12 foreign key (SOURCE_CRPT_ID) references TB_TJ_CRPT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RESULT_JUDGE_ITEM')
                AND COLUMN_NAME = UPPER('YP_GT_25');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RESULT_JUDGE_ITEM ADD YP_GT_25 NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('BHKORG_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD BHKORG_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZDZYB_BHK_TRANS'
                AND CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS13';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD constraint FK_TD_ZDZYB_BHK_TRANS13 foreign key (BHKORG_ID) references TB_TJ_SRVORG (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('XH')
                AND DATA_PRECISION = 1;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE MODIFY XH NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_DETAIL')
                AND COLUMN_NAME = UPPER('IF_TOTAL_ANALY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_DETAIL ADD IF_TOTAL_ANALY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZDZYB_ANALY_BADRSN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZDZYB_ANALY_BADRSN (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              BADRSN_ID INTEGER,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              CONSTRAINT TD_ZDZYB_ANALY_BADRSN PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZDZYB_ANALY_BADRSN1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZDZYB_ANALY_TYPE (RID),
              CONSTRAINT FK_TD_ZDZYB_ANALY_BADRSN2 FOREIGN KEY (BADRSN_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZDZYB_ANALY_BADRSN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('INDUS_2_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD INDUS_2_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZDZYB_BHK_TRANS'
                AND CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS14';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD constraint FK_TD_ZDZYB_BHK_TRANS14 foreign key (INDUS_2_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('WORK_YEAR_ID2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD WORK_YEAR_ID2 INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZDZYB_BHK_TRANS'
                AND CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS15';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD constraint FK_TD_ZDZYB_BHK_TRANS15 foreign key (WORK_YEAR_ID2) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('WORK_YEAR_ID3');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD WORK_YEAR_ID3 INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZDZYB_BHK_TRANS'
                AND CONSTRAINT_NAME = 'FK_TD_ZDZYB_BHK_TRANS16';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD constraint FK_TD_ZDZYB_BHK_TRANS16 foreign key (WORK_YEAR_ID3) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_BHK_TRANS')
                AND COLUMN_NAME = UPPER('TCHBADRSNTIM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_BHK_TRANS ADD TCHBADRSNTIM NUMBER(9,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RESULT_JUDGE_ITEM')
                AND COLUMN_NAME = UPPER('BADRSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RESULT_JUDGE_ITEM ADD BADRSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_TJ_RESULT_JUDGE_ITEM'
                AND CONSTRAINT_NAME = 'FK_TD_TJ_RESULT_JUDGE_ITEM3';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RESULT_JUDGE_ITEM ADD constraint FK_TD_TJ_RESULT_JUDGE_ITEM3 foreign key (BADRSN_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RST_JUDGE_TRANS')
                AND COLUMN_NAME = UPPER('BADRSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RST_JUDGE_TRANS ADD BADRSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_TJ_RST_JUDGE_TRANS'
                AND CONSTRAINT_NAME = 'FK_TD_TJ_RST_JUDGE_TRANS3';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RST_JUDGE_TRANS ADD constraint FK_TD_TJ_RST_JUDGE_TRANS3 foreign key (BADRSN_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_ITM_TYPE')
                AND COLUMN_NAME = UPPER('ITEM_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD ITEM_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_ITM_TYPE'
                AND CONSTRAINT_NAME = 'FK_TD_ZDZYB_ANALY_ITM_TYPE2';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_ITM_TYPE ADD constraint FK_TD_ZDZYB_ANALY_ITM_TYPE2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-zdzyb 重点职业病相关模块升级 -->