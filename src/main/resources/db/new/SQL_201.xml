<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>实验室比对（盲样考核）</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ITEM_LEVEL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_ITEM_LEVEL (   RID                  INTEGER              not null,   ITEM_ID              INTEGER              not null,   LEVEL_ID             INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWMY_ITEM_LEVEL primary key (RID),constraint FK_TD_ZWMY_ITEM_LEVEL1 foreign key (ITEM_ID)      references TS_SIMPLE_CODE (RID),constraint FK_TD_ZWMY_ITEM_LEVEL2 foreign key (LEVEL_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_PLAN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_PLAN (RID                  INTEGER              not null,   PLAN_CLS_NAME        VARCHAR2(100)        not null,   END_DATE             DATE,   CHECK_OBJS           VARCHAR2(100),   CHECK_CONTENT        VARCHAR2(2000),   OTHER_ITEMS          VARCHAR2(2000),   FILE_PATH            VARCHAR2(200),   FILE_NAME            VARCHAR2(200),   PUBISH_UNIT_ID       INTEGER,   PUBLISH_DATE         DATE,   STATE                NUMBER(1),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWMY_PLAN primary key (RID),constraint FK_TD_ZWMY_PLAN1 foreign key (PUBISH_UNIT_ID)      references TS_UNIT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_SIGN_UP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_SIGN_UP (RID                  INTEGER              not null,   MAIN_ID              INTEGER,   UNIT_ID              INTEGER,   ORG_NAME             VARCHAR2(100)        not null,   ORG_ADDR             VARCHAR2(200)        not null,   LINK_MAN             VARCHAR2(50),   LINK_SEX             NUMBER(1),   LINK_MB              VARCHAR2(50),   STATE                NUMBER(1),   BACK_RSN             VARCHAR2(200),   IF_MADE_CERT         NUMBER(1),   SEND_CERT_DATE       DATE,   ANNEX_NAME           VARCHAR2(200),   ANNEX_PATH           VARCHAR2(200),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWMY_SIGN_UP primary key (RID),constraint FK_TD_ZWMY_SIGN_UP1 foreign key (MAIN_ID)      references TD_ZWMY_PLAN (RID),constraint FK_TD_ZWMY_SIGN_UP2 foreign key (UNIT_ID)      references TS_UNIT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_FETCH_SAMP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_FETCH_SAMP (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   FETCH_DATE           DATE,   RCD_END_DATE         DATE,   ANNEX_NAME           VARCHAR2(200),   ANNEX_PATH           VARCHAR2(200),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWMY_FETCH_SAMP primary key (RID),constraint FK_TD_ZWMY_FETCH_SAMP1 foreign key (MAIN_ID)      references TD_ZWMY_SIGN_UP (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_SAMP_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_SAMP_SUB (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   ITEM_ID              INTEGER,   SAMP_NO              VARCHAR2(50),   LEVEL_ID             INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWMY_SAMP_SUB primary key (RID),constraint FK_TD_ZWMY_SAMP_SUB1 foreign key (MAIN_ID)      references TD_ZWMY_FETCH_SAMP (RID),constraint FK_TD_ZWMY_SAMP_SUB2 foreign key (ITEM_ID)      references TS_SIMPLE_CODE (RID),constraint FK_TD_ZWMY_SAMP_SUB3 foreign key (LEVEL_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_RCD_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_RCD_RST (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   ITEM_ID              INTEGER,   RST                  VARCHAR2(50),   IF_HG                NUMBER(1),   IF_AUTO_SMT          NUMBER(1),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWMY_RCD_RST primary key (RID),constraint FK_TD_ZWMY_RCD_RST1 foreign key (MAIN_ID)      references TD_ZWMY_SAMP_SUB (RID),constraint FK_TD_ZWMY_RCD_RST2 foreign key (ITEM_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ITEM_LEVEL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ITEM_LEVEL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_PLAN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_PLAN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_SIGN_UP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_SIGN_UP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_FETCH_SAMP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_FETCH_SAMP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_SAMP_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_SAMP_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_RCD_RST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_RCD_RST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_PLAN')
                AND COLUMN_NAME = UPPER('CHECK_OBJS');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_PLAN MODIFY CHECK_OBJS VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('FETCH_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD FETCH_END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SAMP_SUB')
                AND COLUMN_NAME = UPPER('RCD_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SAMP_SUB ADD RCD_END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_FETCH_SAMP')
                AND COLUMN_NAME = UPPER('SMT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_FETCH_SAMP ADD SMT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_RST_ANNEX_PATH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWMY_RST_ANNEX_PATH (
                  RID                  INTEGER              not null,
                  MAIN_ID              INTEGER,
                  ANNEX_NAME           VARCHAR2(200),
            ANNEX_PATH           VARCHAR2(200),
                  MODIFY_DATE          TIMESTAMP,
                  MODIFY_MANID         INTEGER,
                  CREATE_DATE          TIMESTAMP            not null,
                  CREATE_MANID         INTEGER              not null,
                  constraint PK_TD_ZWMY_RST_ANNEX_PATH primary key (RID),
                  constraint FK_TD_ZWMY_RST_ANNEX_PATH1 foreign key (MAIN_ID)
                  references TD_ZWMY_FETCH_SAMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_RST_ANNEX_PATH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_RST_ANNEX_PATH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ITEM_SMPNO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWMY_ITEM_SMPNO (
                  RID                  INTEGER              not null,
                  MAIN_ID              INTEGER              not null,
                  PRE_NO               VARCHAR2(20),
            MIN_NO               NUMBER(5),
            MAX_NO               NUMBER(5),
                  MODIFY_DATE          TIMESTAMP,
                  MODIFY_MANID         INTEGER,
                  CREATE_DATE          TIMESTAMP            not null,
                  CREATE_MANID         INTEGER              not null,
                  constraint PK_TD_ZWMY_ITEM_SMPNO primary key (RID),
                  constraint FK_TD_ZWMY_ITEM_SMPNO1 foreign key (MAIN_ID)
                  references TD_ZWMY_ITEM_LEVEL (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ITEM_SMPNO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ITEM_SMPNO_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_RCD_RST')
                AND COLUMN_NAME = UPPER('RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_RCD_RST ADD RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_RCD_RST')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_RCD_RST3');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_RCD_RST ADD CONSTRAINT FK_TD_ZWMY_RCD_RST3 FOREIGN KEY (RST_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_SIGN_PROJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWMY_SIGN_PROJECT (
                  RID                  INTEGER              not null,
                  MAIN_ID              INTEGER,
            CHECK_PROJECT_ID     INTEGER,
            IF_HG                NUMBER(1),
                  MODIFY_DATE          TIMESTAMP,
                  MODIFY_MANID         INTEGER,
                  CREATE_DATE          TIMESTAMP            not null,
                  CREATE_MANID         INTEGER              not null,
                  constraint PK_TD_ZWMY_SIGN_PROJECT primary key (RID),
                  constraint FK_TD_ZWMY_SIGN_PROJECT1 foreign key (MAIN_ID)
                  references TD_ZWMY_SIGN_UP (RID),
                  constraint FK_TD_ZWMY_SIGN_PROJECT2 foreign key (CHECK_PROJECT_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_SIGN_PROJECT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_SIGN_PROJECT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_TYPE_CONFIG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWMY_TYPE_CONFIG (
                  RID                  INTEGER              not null,
                  TYPE_ID              INTEGER              not null,
            RMK                  NVARCHAR2(100),
                  MODIFY_DATE          TIMESTAMP,
                  MODIFY_MANID         INTEGER,
                  CREATE_DATE          TIMESTAMP            not null,
                  CREATE_MANID         INTEGER              not null,
                  constraint PK_TD_ZWMY_TYPE_CONFIG primary key (RID),
                  constraint FK_TD_ZWMY_TYPE_CONFIG1 foreign key (TYPE_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_TYPE_CONFIG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_TYPE_CONFIG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_TYPE_REL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWMY_TYPE_REL_ITEM (
                  RID                  INTEGER              not null,
                  MAIN_ID              INTEGER              not null,
            ITEM_ID              INTEGER,
                  MODIFY_DATE          TIMESTAMP,
                  MODIFY_MANID         INTEGER,
                  CREATE_DATE          TIMESTAMP            not null,
                  CREATE_MANID         INTEGER              not null,
                  constraint PK_TD_ZWMY_TYPE_REL_ITEM primary key (RID),
                  constraint FK_TD_ZWMY_TYPE_REL_ITEM1 foreign key (MAIN_ID)
                  references TD_ZWMY_TYPE_CONFIG (RID),
                  constraint FK_TD_ZWMY_TYPE_REL_ITEM2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_TYPE_REL_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_TYPE_REL_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ITEM_REL_JC';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWMY_ITEM_REL_JC (
                  RID                  INTEGER              not null,
                  MAIN_ID              INTEGER              not null,
            ITEM_ID              INTEGER,
                  MODIFY_DATE          TIMESTAMP,
                  MODIFY_MANID         INTEGER,
                  CREATE_DATE          TIMESTAMP            not null,
                  CREATE_MANID         INTEGER              not null,
                  constraint PK_TD_ZWMY_ITEM_REL_JC primary key (RID),
                  constraint FK_TD_ZWMY_ITEM_REL_JC1 foreign key (MAIN_ID)
                  references TD_ZWMY_TYPE_REL_ITEM (RID),
                  constraint FK_TD_ZWMY_ITEM_REL_JC2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ITEM_REL_JC_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ITEM_REL_JC_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SAMP_SUB')
                AND COLUMN_NAME = UPPER('JC_INST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SAMP_SUB ADD JC_INST NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_PLAN')
                AND COLUMN_NAME = UPPER('END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_PLAN ADD END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('LINK_EMAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD LINK_EMAL NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('IF_ENTRUST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD IF_ENTRUST NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('ENTRUST_ORG_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD ENTRUST_ORG_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('CONTRACT_ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD CONTRACT_ANNEX_PATH NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORGINFO (RID                  INTEGER              not null,ORG_ID               INTEGER              not null,ORG_NAME             VARCHAR2(100)        not null,ORG_ADDR             VARCHAR2(200)        not null,ORG_FZ               VARCHAR2(50),ORG_FZZW             VARCHAR2(50),LINK_MAN             VARCHAR2(50),LINK_MB              VARCHAR2(50),LINK_TEL             VARCHAR2(50),FAX                  VARCHAR2(50),ZIPCODE              VARCHAR2(10),EMAIL                VARCHAR2(50),CERT_NO              VARCHAR2(50),FIRST_GETDAY         DATE,VALID_DATE           DATE,STATE                NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,CANCEL_STATE         NUMBER(1),CANCEL_DATE          DATE,ZONE_ID              INTEGER              not null,CREDIT_CODE          VARCHAR2(50),OUT_WORK_POWER       NUMBER(1),LAST_SMT_DATE        DATE,constraint PK_TD_ZW_TJORGINFO primary key (RID),constraint FK_TD_ZW_TJORGINFO1 foreign key (ORG_ID) references TS_UNIT (RID) )    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORGINFO_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGGITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORGGITEMS (RID                  INTEGER              not null,ORG_ID               INTEGER              not null,ITEM_CODE            NUMBER(4)            not null,IF_JOIN_PG           NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,constraint PK_TD_ZW_TJORGGITEMS primary key (RID),constraint FK_TD_ZW_TJORGGITEMS1 foreign key (ORG_ID) references TD_ZW_TJORGINFO (RID))  ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGGITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORGGITEMS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_JC_ITEM_HG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
               create table TD_ZWMY_JC_ITEM_HG(
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   SUB_ITEM_ID          INTEGER,
                   IF_HG                NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_JC_ITEM_HG primary key (RID),
                   constraint FK_TD_ZWMY_JC_ITEM_HG1 foreign key (MAIN_ID) references TD_ZWMY_FETCH_SAMP (RID),
                   constraint FK_TD_ZWMY_JC_ITEM_HG2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWMY_JC_ITEM_HG3 foreign key (SUB_ITEM_ID)references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_JC_ITEM_HG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_JC_ITEM_HG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_SIGN_ITEM_HG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
              create table TD_ZWMY_SIGN_ITEM_HG
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   IF_HG                NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_SIGN_ITEM_HG primary key (RID),
                   constraint FK_TD_ZWMY_SIGN_ITEM_HG1 foreign key (MAIN_ID)
                   references TD_ZWMY_SIGN_PROJECT (RID),
                   constraint FK_TD_ZWMY_SIGN_ITEM_HG2 foreign key (ITEM_ID)
                   references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_SIGN_ITEM_HG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_SIGN_ITEM_HG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
               DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ITEM_REL_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
             create table TD_ZWMY_ITEM_REL_RST
                (
                   RID                  INTEGER              not null,
                   ITEM_ID              INTEGER              not null,
                   RST_ID               INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_ITEM_REL_RST primary key (RID),
                   constraint FK_TD_ZWMY_ITEM_REL_RST1 foreign key (ITEM_ID)
                   references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWMY_ITEM_REL_RST2 foreign key (RST_ID)
                   references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ITEM_REL_RST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ITEM_REL_RST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_ITEM_LEVEL')
                AND COLUMN_NAME = UPPER('FILE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_ITEM_LEVEL ADD FILE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
               DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ENTRUST_FILE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_ENTRUST_FILE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ENTRUST_ORG_NAME     NVARCHAR2(100),
                   CONTRACT_ANNEX_PATH  NVARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_ENTRUST_FILE primary key (RID),
                   constraint FK_TD_ZWMY_ENTRUST_FILE1 foreign key (MAIN_ID) references TD_ZWMY_SIGN_UP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ENTRUST_FILE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ENTRUST_FILE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SAMP_SUB')
                AND COLUMN_NAME = UPPER('IF_HAVE_CHECK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SAMP_SUB ADD IF_HAVE_CHECK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
               DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_LAB_CHECK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_LAB_CHECK
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ORG_ID               INTEGER,
               JOIN_DATE            DATE,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_LAB_CHECK primary key (RID),
               constraint FK_TD_ZWMY_LAB_CHECK1 foreign key (MAIN_ID)
              references TD_ZWMY_FETCH_SAMP (RID),
               constraint FK_TD_ZWMY_LAB_CHECK2 foreign key (ORG_ID)
              references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_LAB_CHECK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_LAB_CHECK_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
               DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_LAB_PROVE_FILE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_LAB_PROVE_FILE
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ANNEX_PATH           NVARCHAR2(100),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_LAB_PROVE_FILE primary key (RID),
                constraint FK_TD_ZWMY_LAB_PROVE_FILE1 foreign key (MAIN_ID)
                  references TD_ZWMY_LAB_CHECK (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_LAB_PROVE_FILE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_LAB_PROVE_FILE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
               DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ITEM_RST_RANGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_ITEM_RST_RANGE
        (
           RID                  INTEGER              not null,
           ITEM_ID              INTEGER              not null,
           LEVEL_ID             INTEGER,
           MINVAL               NUMBER(18,6),
           MAXVAL               NUMBER(18,6),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_ZWMY_ITEM_RST_RANGE primary key (RID),
            constraint FK_TD_ZWMY_ITEM_RST_RANGE1 foreign key (ITEM_ID)
              references TS_SIMPLE_CODE (RID),
               constraint FK_TD_ZWMY_ITEM_RST_RANGE2 foreign key (LEVEL_ID)
            references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ITEM_RST_RANGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ITEM_RST_RANGE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SAMP_SUB')
                AND COLUMN_NAME = UPPER('IF_HG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SAMP_SUB ADD IF_HG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_OCC_PLAN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
               create table TD_ZWMY_OCC_PLAN
                (
                   RID                  INTEGER              not null,
                   PLAN_CLS_NAME        VARCHAR2(100)        not null,
                   CHECK_OBJS           VARCHAR2(500),
                   CHECK_CONTENT        VARCHAR2(2000),
                   OTHER_ITEMS          VARCHAR2(2000),
                   FILE_PATH            VARCHAR2(200),
                   FILE_NAME            VARCHAR2(200),
                   PUBISH_UNIT_ID       INTEGER,
                   PUBLISH_DATE         DATE,
                   STATE                NUMBER(1),
                   END_DATE             DATE,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_OCC_PLAN primary key (RID),
                    constraint FK_TD_ZWMY_OCC_PLAN1 foreign key (PUBISH_UNIT_ID)
                      references TS_UNIT (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_OCC_PLAN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_OCC_PLAN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_PUBLISH_FETCH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
              create table TD_ZWMY_PUBLISH_FETCH
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               FETCH_ID             INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_PUBLISH_FETCH primary key (RID),
               constraint FK_TD_ZWMY_PUBLISH_FETCH1 foreign key (MAIN_ID)
               references TD_ZWMY_OCC_PLAN (RID),
               constraint FK_TD_ZWMY_PUBLISH_FETCH2 foreign key (FETCH_ID)
               references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_PUBLISH_FETCH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_PUBLISH_FETCH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_PUBLISH_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
              create table TD_ZWMY_PUBLISH_ITEM
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ITEM_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_PUBLISH_ITEM primary key (RID),
              constraint FK_TD_ZWMY_PUBLISH_ITEM1 foreign key (MAIN_ID)
              references TD_ZWMY_OCC_PLAN (RID),
              constraint FK_TD_ZWMY_PUBLISH_ITEM2 foreign key (ITEM_ID)
              references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_PUBLISH_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_PUBLISH_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_OCC_SIGN_UP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
             create table TD_ZWMY_OCC_SIGN_UP
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               UNIT_ID              INTEGER,
               ORG_NAME             VARCHAR2(100),
               ORG_ADDR             VARCHAR2(200),
               LINK_MAN             VARCHAR2(50),
               LINK_SEX             NUMBER(1),
               LINK_MB              VARCHAR2(50),
               LEVEL_ID             INTEGER,
               STATE                NUMBER(1),
               BACK_RSN             VARCHAR2(200),
               IF_MADE_CERT         NUMBER(1),
               FETCH_END_DATE       DATE,
               FETCH_DATE           DATE,
               SEND_CERT_DATE       DATE,
               ANNEX_NAME           VARCHAR2(200),
               ANNEX_PATH           VARCHAR2(200),
               JUDGE_RST_ID         INTEGER,
               IF_SIGN              NUMBER(1),
               FETCH_ID             INTEGER,
               DROP_RSN             NVARCHAR2(200),
               FETCH_START_DATE     DATE,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_OCC_SIGN_UP primary key (RID),
             constraint FK_TD_ZWMY_OCC_SIGN_UP1 foreign key (MAIN_ID)
                  references TD_ZWMY_OCC_PLAN (RID),
            constraint FK_TD_ZWMY_OCC_SIGN_UP2 foreign key (UNIT_ID)
                  references TS_UNIT (RID),
             constraint FK_TD_ZWMY_OCC_SIGN_UP3 foreign key (LEVEL_ID)
                  references TS_SIMPLE_CODE (RID),
            constraint FK_TD_ZWMY_OCC_SIGN_UP4 foreign key (JUDGE_RST_ID)
                  references TS_SIMPLE_CODE (RID),
             constraint FK_TD_ZWMY_OCC_SIGN_UP5 foreign key (FETCH_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_OCC_SIGN_UP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_OCC_SIGN_UP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_SIGN_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
            create table TD_ZWMY_SIGN_ITEM
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ITEM_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_SIGN_ITEM primary key (RID),
            constraint FK_TD_ZWMY_SIGN_ITEM1 foreign key (MAIN_ID)
                  references TD_ZWMY_OCC_SIGN_UP (RID),
             constraint FK_TD_ZWMY_SIGN_ITEM2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_SIGN_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_SIGN_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_OCC_RST_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
               create table TD_ZWMY_OCC_RST_ANNEX
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ANNEX_NAME           VARCHAR2(200),
                   ANNEX_PATH           VARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_OCC_RST_ANNEX primary key (RID),
                constraint FK_TD_ZWMY_OCC_RST_ANNEX1 foreign key (MAIN_ID)
                  references TD_ZWMY_OCC_SIGN_UP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_OCC_RST_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_OCC_RST_ANNEX_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_OCC_SAMP_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
               create table TD_ZWMY_OCC_SAMP_SUB
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ITEM_ID              INTEGER,
               SAMP_NO              VARCHAR2(50),
               LEVEL_ID             INTEGER,
               RCD_END_DATE         DATE,
               RST                  VARCHAR2(50),
               JUDGE_RST_ID         INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_OCC_SAMP_SUB primary key (RID),
             constraint FK_TD_ZWMY_OCC_SAMP_SUB1 foreign key (MAIN_ID)
                  references TD_ZWMY_OCC_SIGN_UP (RID),
             constraint FK_TD_ZWMY_OCC_SAMP_SUB2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID),
             constraint FK_TD_ZWMY_OCC_SAMP_SUB3 foreign key (LEVEL_ID)
                  references TS_SIMPLE_CODE (RID),
            constraint FK_TD_ZWMY_OCC_SAMP_SUB4 foreign key (JUDGE_RST_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_OCC_SAMP_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_OCC_SAMP_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ITEM_RANGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_ITEM_RANGE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   TYPE                 NUMBER(1),
                   ANALY_ITEM_ID        INTEGER,
                   GE                   NUMBER(6,3),
                   GT                   NUMBER(6,3),
                   LE                   NUMBER(6,3),
                   LT                   NUMBER(6,3),
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZWMY_ITEM_RANGE primary key (RID),
                   constraint FK_TD_ZWMY_ITEM_RANGE1 foreign key (MAIN_ID) references TD_ZWMY_ITEM_LEVEL (RID),
                   constraint FK_TD_ZWMY_ITEM_RANGE2 foreign key (ANALY_ITEM_ID) references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ITEM_RANGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ITEM_RANGE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_OCC_SIGN_UP')
                AND COLUMN_NAME = UPPER('ORG_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_OCC_SIGN_UP ADD ORG_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWMY_OCC_SIGN_UP6'
                AND TABLE_NAME = 'TD_ZWMY_OCC_SIGN_UP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_OCC_SIGN_UP ADD CONSTRAINT FK_TD_ZWMY_OCC_SIGN_UP6 FOREIGN KEY (ORG_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_ITEM_LEVEL')
                AND COLUMN_NAME = UPPER('CODE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_ITEM_LEVEL ADD CODE_NO NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_ITEM_LEVEL')
                AND COLUMN_NAME = UPPER('STAND_VAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_ITEM_LEVEL ADD STAND_VAL NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('IF_ENTRUST_CELL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD IF_ENTRUST_CELL NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('CELL_ORG_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD CELL_ORG_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_EXTRACT_FILE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_EXTRACT_FILE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   CODE_NO              NVARCHAR2(20),
                   STAND_VAL            NUMBER(6),
                   FILE_PATH            NVARCHAR2(100),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_EXTRACT_FILE primary key (RID),
                   constraint FK_TD_ZWMY_EXTRACT_FILE1 foreign key (MAIN_ID) references TD_ZWMY_SAMP_SUB (RID),
                   constraint FK_TD_ZWMY_EXTRACT_FILE2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_EXTRACT_FILE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_EXTRACT_FILE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 10000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_QUAL_EVAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_QUAL_EVAL
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ORG_ID               INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_QUAL_EVAL primary key (RID),
                   constraint FK_TD_ZWMY_QUAL_EVAL1 foreign key (MAIN_ID) references TD_ZWMY_SIGN_UP (RID),
                   constraint FK_TD_ZWMY_QUAL_EVAL2 foreign key (ORG_ID) references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_QUAL_EVAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_QUAL_EVAL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 10000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_EVAL_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_EVAL_ITEM
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_EVAL_ITEM primary key (RID),
                   constraint FK_TD_ZWMY_EVAL_ITEM1 foreign key (MAIN_ID) references TD_ZWMY_SIGN_UP (RID),
                   constraint FK_TD_ZWMY_EVAL_ITEM2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_EVAL_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_EVAL_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 10000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('IF_JOIN_EQA');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD IF_JOIN_EQA NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_ENTRUST_FILE')
                AND COLUMN_NAME = UPPER('TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_ENTRUST_FILE ADD TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_ITEM_LEVEL')
                AND COLUMN_NAME = UPPER('FILE_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_ITEM_LEVEL ADD FILE_NAME NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_CHECK_RESULT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_CHECK_RESULT
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               RESULT_ID            INTEGER,
               IF_HG                NUMBER(1),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWMY_CHECK_RESULT primary key (RID),
               constraint FK_TD_ZWMY_CHECK_RESULT1 foreign key (MAIN_ID)
                  references TD_ZWMY_SIGN_UP (RID),
                constraint FK_TD_ZWMY_CHECK_RESULT2 foreign key (RESULT_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_CHECK_RESULT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_CHECK_RESULT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 10000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_RCD_RST')
                AND COLUMN_NAME = UPPER('FILE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_RCD_RST ADD FILE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_RCD_RST')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_RCD_RST4');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_RCD_RST add constraint FK_TD_ZWMY_RCD_RST4 foreign key (FILE_ID) references TD_ZWMY_EXTRACT_FILE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_TYPE_REL_ITEM')
                AND COLUMN_NAME = UPPER('RESULT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_TYPE_REL_ITEM ADD RESULT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_TYPE_REL_ITEM')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_TYPE_REL_ITEM3');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_TYPE_REL_ITEM add constraint FK_TD_ZWMY_TYPE_REL_ITEM3 foreign key (RESULT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_FETCH_SAMP')
                AND COLUMN_NAME = UPPER('SEND_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_FETCH_SAMP ADD SEND_TIME TIMESTAMP';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('DATA_RESULT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD DATA_RESULT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP3');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP3 foreign key (DATA_RESULT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('INSPECT_METHOD_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD INSPECT_METHOD_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP4');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP4 foreign key (INSPECT_METHOD_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('INST_MEASURE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD INST_MEASURE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP5');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP5 foreign key (INST_MEASURE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('ORIGIN_DATA_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD ORIGIN_DATA_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP6');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP6 foreign key (ORIGIN_DATA_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('FINAL_JUDGR_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD FINAL_JUDGR_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP7');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP7 foreign key (FINAL_JUDGR_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('CERT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD CERT_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_SIGN_REL_ITEM_HG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_SIGN_REL_ITEM_HG
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   IF_HG                NUMBER(1),
                   IF_ENTRUST           NUMBER(1),
                   ENTRUST_ORG_NAME     NVARCHAR2(100),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_SIGN_REL_ITEM_HG primary key (RID),
                   constraint FK_TD_ZWMY_SIGN_REL_ITEM_HG1 foreign key (MAIN_ID)
                    references TD_ZWMY_SIGN_UP (RID),
                   constraint FK_TD_ZWMY_SIGN_REL_ITEM_HG2 foreign key (ITEM_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_SIGN_REL_ITEM_HG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_SIGN_REL_ITEM_HG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 10000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWMY_ENTRUST_ITEM_FILE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWMY_ENTRUST_ITEM_FILE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   CONTRACT_ANNEX_PATH  NVARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWMY_ENTRUST_ITEM_FILE primary key (RID),
                   constraint FK_TD_ZWMY_ENTRUST_ITEM_FILE1 foreign key (MAIN_ID)
                    references TD_ZWMY_SIGN_REL_ITEM_HG (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWMY_ENTRUST_ITEM_FILE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWMY_ENTRUST_ITEM_FILE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 10000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('ENTRUST_ABILITY_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD ENTRUST_ABILITY_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP8');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP8 foreign key (ENTRUST_ABILITY_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND COLUMN_NAME = UPPER('ENTRUST_ORG_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWMY_SIGN_UP ADD ENTRUST_ORG_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = UPPER('TD_ZWMY_SIGN_UP')
                AND CONSTRAINT_NAME = UPPER('FK_TD_ZWMY_SIGN_UP9');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWMY_SIGN_UP add constraint FK_TD_ZWMY_SIGN_UP9 foreign key (ENTRUST_ORG_ID) references TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-lab-compare 实验室对比相关模块升级 -->