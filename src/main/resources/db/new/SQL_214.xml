<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>尘肺病临床诊断</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_DISCASE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_DISCASE (
              RID INTEGER NOT NULL,
              ARCH_CODE NVARCHAR2 (50) NOT NULL,
              PERSONNEL_NAME NVARCHAR2 (50) NOT NULL,
              IDC NVARCHAR2 (20) NOT NULL,
              SEX NUMBER(1),
              BIRTHDAY DATE,
              AGE NUMBER(3),
              LINKTEL NVARCHAR2 (50),
              ADDRESS NVARCHAR2 (100),
              POSTCODE NVARCHAR2 (6),
              PHOTO_PATH NVARCHAR2 (100),
              HU_ZONE_ID INTEGER,
              HU_ADDRESS NVARCHAR2 (200),
              CRPT_NAME NVARCHAR2 (100),
              CRPT_LINKMAN NVARCHAR2 (50),
              CRPT_PHONE NVARCHAR2 (20),
              CRPT_ADDR NVARCHAR2 (200),
              CRPT_ZIP_CODE NVARCHAR2 (6),
              WORK_TYPE_ID INTEGER,
              HIS_DISEASES NVARCHAR2 (1000),
              OCCDISE_APPLYID INTEGER NOT NULL,
              TCH_WORK NUMBER(3, 1),
              DUST_HISTORY NVARCHAR2 (1000),
              APPLY_DATE DATE,
              OCCDISE_ID INTEGER,
              PERIOD_ID INTEGER,
              IS_DIS NUMBER(1),
              IF_STOP NUMBER(1),
              STOP_RSN NVARCHAR2 (1000),
              STATE_MARK NUMBER(1) NOT NULL,
              ACPTORG_ID INTEGER NOT NULL,
              TL_DATE DATE,
              ZD_DATE DATE,
              DEL_MARK NUMBER(1) NOT NULL,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_DISCASE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_DISCASE1 FOREIGN KEY (HU_ZONE_ID) REFERENCES TS_ZONE (RID),
              CONSTRAINT FK_TD_ZWCF_DISCASE2 FOREIGN KEY (WORK_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_DISCASE3 FOREIGN KEY (OCCDISE_APPLYID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_DISCASE4 FOREIGN KEY (OCCDISE_ID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_DISCASE6 FOREIGN KEY (ACPTORG_ID) REFERENCES TS_UNIT (RID),
              CONSTRAINT FK_TD_ZWCF_DISCASE5 FOREIGN KEY (PERIOD_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_DISCASE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_ANNEXTYPE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_ANNEXTYPE (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              TYPE_ID INTEGER,
              TYPE_OTHER NVARCHAR2(50),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_ANNEXTYPE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_ANNEXTYPE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_DISCASE (RID),
              CONSTRAINT FK_TD_ZWCF_ANNEXTYPE2 FOREIGN KEY (TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_ANNEXTYPE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_JZANNEX';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_JZANNEX (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              TYPE_ID INTEGER,
              ANNEX_NAME NVARCHAR2(50),
              ANNEX_PATH NVARCHAR2(100),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_JZANNEX PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_JZANNEX1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_DISCASE (RID),
              CONSTRAINT FK_TD_ZWCF_JZANNEX2 FOREIGN KEY (TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_JZANNEX';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_DIAG_ANNEXS';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_DIAG_ANNEXS (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              DEAL_STEP INTEGER,
              ANNEX_TYPE INTEGER,
              ANNEX_NAME NVARCHAR2(50) NOT NULL,
              ANNEX_PATH NVARCHAR2(100) NOT NULL,
              ANNEX_DESCR NVARCHAR2(100),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_DIAG_ANNEXS PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_DIAG_ANNEXS1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_DISCASE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_DIAG_ANNEXS';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_MADEDWRIT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_MADEDWRIT (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              WRIT_SORTID INTEGER,
              WRIT_ID INTEGER,
              WRIT_ANNEX_ID INTEGER,
              WRIT_DATE DATE,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_MADEDWRIT PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_MADEDWRIT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_DISCASE (RID),
              CONSTRAINT FK_TD_ZWCF_MADEDWRIT2 FOREIGN KEY (WRIT_SORTID) REFERENCES TB_ZW_WRITSORT (RID),
              CONSTRAINT FK_TD_ZWCF_MADEDWRIT3 FOREIGN KEY (WRIT_ANNEX_ID) REFERENCES TD_ZWCF_DIAG_ANNEXS (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_MADEDWRIT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_RECORD';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_RECORD (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              DIAG_CONTENT NVARCHAR2(1000),
              DIS_PSNS NVARCHAR2(100),
              DIAG_PLACE NVARCHAR2(100),
              RECORD_PSN NVARCHAR2(50),
              MULTI_ANALY CLOB,
              DIAG_STANDARD CLOB,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_RECORD PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_RECORD1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_DISCASE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_RECORD';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_CHEST';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_CHEST (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              XP_NO NVARCHAR2(50),
              TOP_LEFT_DENSITY INTEGER,
              TOP_RIGHT_DENSITY INTEGER,
              MIDDLE_LEFT_DENSITY INTEGER,
              MIDDLE_RIGHT_DENSITY INTEGER,
              BOTTOM_LEFT_DENSITY INTEGER,
              BOTTOM_RIGHT_DENSITY INTEGER,
              TOP_FORM INTEGER,
              BOTTOM_FORM INTEGER,
              XP_QUALITY_ID INTEGER,
              TOTAL_DENSITY_ID INTEGER,
              SML_SHADOW_GATHER NUMBER(1),
              BIG_SHADOW NUMBER(1),
              LOCAL_THICK NVARCHAR2(50),
              DIFF_THICK NVARCHAR2(50),
              PLEURA_CALCIFI NVARCHAR2(50),
              UNBOUNDED_HEART NVARCHAR2(50),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_CHEST PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST2 FOREIGN KEY (TOP_LEFT_DENSITY) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST3 FOREIGN KEY (TOP_RIGHT_DENSITY) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST4 FOREIGN KEY (MIDDLE_LEFT_DENSITY) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST5 FOREIGN KEY (MIDDLE_RIGHT_DENSITY) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST6 FOREIGN KEY (BOTTOM_LEFT_DENSITY) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST7 FOREIGN KEY (BOTTOM_RIGHT_DENSITY) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST8 FOREIGN KEY (TOP_FORM) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST9 FOREIGN KEY (BOTTOM_FORM) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST10 FOREIGN KEY (XP_QUALITY_ID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST11 FOREIGN KEY (TOTAL_DENSITY_ID) REFERENCES TS_SIMPLE_CODE (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_RECORD (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_CHEST';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_CHEST_SIGN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_CHEST_SIGN (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              APD_SIGN_ID INTEGER,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_CHEST_SIGN PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST_SIGN1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_CHEST (RID),
              CONSTRAINT FK_TD_ZWCF_CHEST_SIGN2 FOREIGN KEY (APD_SIGN_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_CHEST_SIGN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWCF_PROVE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWCF_PROVE (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER NOT NULL,
              ZD_DOCTORS NVARCHAR2(100),
              DIAG_IDEAS CLOB,
              PROVE_BAK NVARCHAR2(1000),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWCF_PROVE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZWCF_PROVE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWCF_DISCASE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWCF_PROVE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZWCF_DISCASE')
                      AND COLUMN_NAME = UPPER('APPLY_OCC_OTHER');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_DISCASE ADD APPLY_OCC_OTHER NVARCHAR2(50)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZWCF_DISCASE')
                      AND COLUMN_NAME = UPPER('OCC_OTHER');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_DISCASE ADD OCC_OTHER NVARCHAR2(50)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-cfb-diag 字段升级 -->