<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>放射卫生-通用</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_STD')
                AND COLUMN_NAME = UPPER('JC_COND_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_STD ADD JC_COND_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_REL')
                AND COLUMN_NAME = UPPER('IF_NEED_BASE_VAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_REL ADD IF_NEED_BASE_VAL NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('JC_COND_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD JC_COND_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('IF_BUILD_BASE_VALUE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD IF_BUILD_BASE_VALUE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('BASE_VALUE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD BASE_VALUE VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('MAIN_HARN_ORIENT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD MAIN_HARN_ORIENT VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('BASE_VAL_UNIT_MODEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD BASE_VAL_UNIT_MODEL NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('BASE_VAL_UNIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD BASE_VAL_UNIT VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('TEST_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD TEST_CASE VARCHAR2(400)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('IF_REMOVE_BACKGRD_VALUE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD IF_REMOVE_BACKGRD_VALUE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('BACKGRD_VALUE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD BACKGRD_VALUE VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('STATE_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD STATE_MARK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('ITEM_REL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD ITEM_REL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_RESULT5'
                AND TABLE_NAME = 'TD_TJ_RAD_RESULT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD CONSTRAINT FK_TD_TJ_RAD_RESULT5 FOREIGN KEY (ITEM_REL_ID)      REFERENCES TD_TJ_RAD_ITEM_REL (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('RAD_LICENSE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD RAD_LICENSE_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('INST_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST MODIFY INST_NO NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('INST_FACT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD INST_FACT_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('TRAIN_HG_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD TRAIN_HG_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_ANNEXS')
                AND COLUMN_NAME = UPPER('IF_VALID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_ANNEXS ADD IF_VALID NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_STD')
                AND COLUMN_NAME = UPPER('CODION_MODEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_STD ADD CODION_MODEL NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('BELONG_AREA');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD BELONG_AREA VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('BELONG_AREA');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD BELONG_AREA VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('ON_OFFICE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD ON_OFFICE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('UPDATETAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD UPDATETAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('UPDATETAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD UPDATETAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('MANAGE_OFFICE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD MANAGE_OFFICE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_STD')
                AND COLUMN_NAME = UPPER('COND_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_STD ADD COND_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('UPDATETAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_CHECK_MAIN ADD UPDATETAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_CHECK_MAIN ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('UPDATETAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD UPDATETAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PSN_HETH_CHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_RAD_PSN_HETH_CHK (
                 RID                  INTEGER              not null,
                 MAIN_ID              INTEGER,
                 JC_UNIT_ID           INTEGER,
                 CONSION_ID           INTEGER,
                 BHK_DATE             DATE,
                 CREATE_DATE          TIMESTAMP            not null,
                 CREATE_MANID         INTEGER              not null,
                 MODIFY_DATE          TIMESTAMP,
                 MODIFY_MANID         INTEGER,
                 constraint PK_TD_TJ_RAD_PSN_HETH_CHK primary key (RID),
                 constraint FK_TD_TJ_RAD_PSN_HETH_CHK1 foreign key (MAIN_ID)      references TD_TJ_RADHETH_PSN (RID),
                 constraint FK_TD_TJ_RAD_PSN_HETH_CHK2 foreign key (JC_UNIT_ID)      references TS_UNIT (RID),
                 constraint FK_TD_TJ_RAD_PSN_HETH_CHK3 foreign key (CONSION_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PSN_HETH_CHK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PSN_HETH_CHK_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PSN_TRAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_RAD_PSN_TRAIN (
                 RID                  INTEGER              not null,
                 MAIN_ID              INTEGER,
                 TRAIN_ORG_NAME       VARCHAR2(100),
                 TRAIN_RESULT         NUMBER(1),
                 ANNEX_PATH           VARCHAR2(200),
                 TRAIN_DATE           DATE,
                 CREATE_DATE          TIMESTAMP            not null,
                 CREATE_MANID         INTEGER              not null,
                 MODIFY_DATE          TIMESTAMP,
                 MODIFY_MANID         INTEGER,
                 constraint PK_TD_TJ_RAD_PSN_TRAIN primary key (RID),
                 constraint FK_TD_TJ_RAD_PSN_TRAIN1 foreign key (MAIN_ID)      references TD_TJ_RADHETH_PSN (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PSN_TRAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PSN_TRAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PSN_MSMENT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_PSN_MSMENT (      RID                  INTEGER              not null,    MAIN_ID              INTEGER,   JC_UNIT_ID           INTEGER,   DOSE_NO              VARCHAR2(100),   BEGIN_JC_DATE        DATE,   END_JC_DATE          DATE,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_RAD_PSN_MSMENT primary key (RID),   constraint FK_TD_TJ_RAD_PSN_MSMENT1 foreign key (MAIN_ID)      references TD_TJ_RADHETH_PSN (RID),   constraint FK_TD_TJ_RAD_PSN_MSMENT2 foreign key (JC_UNIT_ID)      references TS_UNIT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PSN_MSMENT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PSN_MSMENT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('DOSE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD DOSE_NO VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('DOSE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN MODIFY DOSE_NO NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('MDL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD MDL NUMBER(8,3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INST_ITEM';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_INST_ITEM (
                 RID                  INTEGER              not null,
           ITEM_DESC            VARCHAR2(100),
           ITEM_TYPE            NUMBER(2),
           ITEM_PRE             VARCHAR2(100),
           LENG_LIMIT           NUMBER(4),
           DECIMAL_LIMIT        NUMBER(2),
           DICT_TYPE_ID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_INST_ITEM primary key (RID),
           constraint FK_TB_TJ_RAD_INST_ITEM1 foreign key (DICT_TYPE_ID)
                references TS_CODE_TYPE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INST_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INST_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INST_COMP';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_INST_COMP (
                 RID                  INTEGER              not null,
           COMP_TYPE            NUMBER(2),
         ITEM_DESC            VARCHAR2(50),
         OWN_COL              NUMBER(1),
         IF_NOT_NULL          NUMBER(1),
         IF_OPERATE           NUMBER(1),
         IF_HAS               NUMBER(1),
         TABLE_UPDATE_COMP_ID INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_INST_COMP primary key (RID),
           constraint FK_TB_TJ_RAD_INST_COMP1 foreign key (TABLE_UPDATE_COMP_ID)
                references TB_TJ_RAD_INST_COMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INST_COMP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INST_COMP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INST_ITEM_DICT';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_INST_ITEM_DICT (
                 RID                  INTEGER              not null,
           ITEM_ID              INTEGER,
         DICT_ID              INTEGER,
         DEPEND_COMP_ID       INTEGER,
         DEPEND_DICT_ID       VARCHAR2(100),
         SEL_SHOW_OTHER_IDENT VARCHAR2(100),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_INST_ITEM_DICT primary key (RID),
           constraint FK_TB_TJ_RAD_INST_ITEM_DICT1 foreign key (ITEM_ID)
                references TB_TJ_RAD_INST_ITEM (RID),
            constraint FK_TB_TJ_RAD_INST_ITEM_DICT2 foreign key (DICT_ID)
                references TS_SIMPLE_CODE (RID),
            constraint FK_TB_TJ_RAD_INST_ITEM_DICT3 foreign key (DEPEND_COMP_ID)
                references TB_TJ_RAD_INST_COMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INST_ITEM_DICT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INST_ITEM_DICT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INST_SHOW';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_INST_SHOW (
                 RID                  INTEGER              not null,
           INST_TYPE_ID         INTEGER,
           COMP_ID              INTEGER,
           NUM                  NUMBER(4),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_INST_SHOW primary key (RID),
           constraint FK_TB_TJ_RAD_INST_SHOW1 foreign key (INST_TYPE_ID)
              references TS_SIMPLE_CODE (RID),
            constraint FK_TB_TJ_RAD_INST_SHOW2 foreign key (COMP_ID)
              references TB_TJ_RAD_INST_COMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INST_SHOW_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INST_SHOW_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INST_ITEM_COMP';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_INST_ITEM_COMP (
                 RID                  INTEGER              not null,
           COMP_ID              INTEGER,
           SUB_COMP_ID          INTEGER,
           ITEM_ID              INTEGER,
           NUM                  NUMBER(4),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_INST_ITEM_COMP primary key (RID),
           constraint FK_TB_TJ_RAD_INST_ITEM_COMP1 foreign key (COMP_ID)
              references TB_TJ_RAD_INST_COMP (RID),
            constraint FK_TB_TJ_RAD_INST_ITEM_COMP2 foreign key (ITEM_ID)
              references TB_TJ_RAD_INST_ITEM (RID),
            constraint FK_TB_TJ_RAD_INST_ITEM_COMP3 foreign key (SUB_COMP_ID)
              references TB_TJ_RAD_INST_COMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INST_ITEM_COMP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INST_ITEM_COMP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INST_RST_MAIN';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TD_TJ_RAD_INST_RST_MAIN (
                 RID                  INTEGER              not null,
           INST_ID              INTEGER,
           COMP_ID              INTEGER,
           IF_HAS               NUMBER(1),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_TJ_RAD_INST_RST_MAIN primary key (RID),
           constraint FK_TD_TJ_RAD_INST_RST_MAIN1 foreign key (INST_ID)
              references TB_TJ_RAD_INST (RID),
            constraint FK_TD_TJ_RAD_INST_RST_MAIN2 foreign key (COMP_ID)
              references TB_TJ_RAD_INST_COMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INST_RST_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INST_RST_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INST_RST_SUB';
              IF NUM = 0 THEN EXECUTE IMMEDIATE
              'create table TD_TJ_RAD_INST_RST_SUB (
                 RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           ROW_NUM              NUMBER(1),
           SUB_COMP_ID          INTEGER,
           ITEM_ID              INTEGER,
           RST_VAL              VARCHAR2(100),
           DATE_VAL             DATE,
           DICT_ID              INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_TJ_RAD_INST_RST_SUB primary key (RID),
           constraint FK_TD_TJ_RAD_INST_RST_SUB1 foreign key (MAIN_ID)
              references TD_TJ_RAD_INST_RST_MAIN (RID),
            constraint FK_TD_TJ_RAD_INST_RST_SUB2 foreign key (ITEM_ID)
              references TB_TJ_RAD_INST_ITEM (RID),
            constraint FK_TD_TJ_RAD_INST_RST_SUB3 foreign key (DICT_ID)
              references TS_SIMPLE_CODE (RID),
            constraint FK_TD_TJ_RAD_INST_RST_SUB4 foreign key (SUB_COMP_ID)
              references TB_TJ_RAD_INST_COMP (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INST_RST_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INST_RST_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_COL')
                AND COLUMN_NAME = UPPER('MAX_EL_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_COL ADD MAX_EL_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INST_COL2'
                AND TABLE_NAME = 'TD_TJ_RAD_INST_COL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_COL ADD CONSTRAINT FK_TD_TJ_RAD_INST_COL2 FOREIGN KEY (MAX_EL_UNIT_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INST_CONTROL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_RAD_INST_CONTROL (RID                  INTEGER              not null,   MAIN_ID              INTEGER,   INST_NAME            VARCHAR2(100),   COUNT                INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_RAD_INST_CONTROL primary key (RID),   constraint FK_TD_TJ_RAD_INST_CONTROL1 foreign key (MAIN_ID)  references TB_TJ_RAD_INST (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INST_CONTROL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INST_CONTROL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PROS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_RAD_PROS (RID                  INTEGER              not null,   MAIN_ID              INTEGER              not null,   PRO_ID               INTEGER              not null,   ART_NAME             INTEGER,   MMPB                 NUMBER(7,3),   PRODUCT_DATE         DATE,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_RAD_PROS primary key (RID),   constraint FK_PK_TD_TJ_RAD_PROS1 foreign key (MAIN_ID)      references TD_TJ_RAD_INST_EV (RID),   constraint FK_PK_TD_TJ_RAD_PROS3 foreign key (PRO_ID)      references TS_SIMPLE_CODE (RID),   constraint FK_PK_TD_TJ_RAD_PROS2 foreign key (ART_NAME)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PROS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PROS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_RST_SUB')
                AND COLUMN_NAME = UPPER('SUB_COMP_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_SUB ADD SUB_COMP_NUM NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_RST_SUB')
                AND COLUMN_NAME = UPPER('ITEM_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_SUB ADD ITEM_NUM NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_RST_MAIN')
                AND COLUMN_NAME = UPPER('NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_MAIN ADD NUM NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('INSTALL_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD INSTALL_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_ITEM_DICT')
                AND COLUMN_NAME = UPPER('IF_DEFALUT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_ITEM_DICT ADD IF_DEFALUT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_ITEM')
                AND COLUMN_NAME = UPPER('ITEM_DESC');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_ITEM MODIFY ITEM_DESC VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_COMP')
                AND COLUMN_NAME = UPPER('ITEM_SET_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_COMP ADD ITEM_SET_DESC VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_COMP')
                AND COLUMN_NAME = UPPER('EQU_FIELD_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_COMP ADD EQU_FIELD_NAME NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_RST_SUB')
                AND COLUMN_NAME = UPPER('EQU_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_SUB ADD EQU_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INST_EQU';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INST_EQU (
                  RID                  INTEGER              not null,
                    UNIT_ID              INTEGER,
                    INST_NAME_ID         INTEGER,
                    INST_OTHER_NAME      VARCHAR2(50),
                    PRODUCE_UNIT         VARCHAR2(100),
                    INST_TYPE_NO         VARCHAR2(100),
                    INST_UNIT_NO         VARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    constraint PK_TD_TJ_RAD_INST_EQU primary key (RID),
                    constraint FK_TD_TJ_RAD_INST_EQU1 foreign key (UNIT_ID) references TB_TJ_RADHETH (RID),
                    constraint FK_TD_TJ_RAD_INST_EQU2 foreign key (INST_NAME_ID) references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INST_EQU_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INST_EQU_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INST_RST_SUB5'
                AND TABLE_NAME = 'TD_TJ_RAD_INST_RST_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_SUB   ADD CONSTRAINT FK_TD_TJ_RAD_INST_RST_SUB5 FOREIGN KEY (EQU_ID)      REFERENCES TD_TJ_RAD_INST_EQU (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_COMP')
                AND COLUMN_NAME = UPPER('IF_CAL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_COMP ADD IF_CAL NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_RST_SUB')
                AND COLUMN_NAME = UPPER('ROW_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_SUB MODIFY ROW_NUM NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INVEST_COMP';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TB_TJ_RAD_INVEST_COMP
            (
               RID                  INTEGER              not null,
               ITEM_DESC            VARCHAR2(200),
               ITEM_TYPE            NUMBER(2),
               ITEM_PRE             VARCHAR2(100),
               LENG_LIMIT           NUMBER(4),
               DECIMAL_LIMIT        NUMBER(2),
               DICT_TYPE_ID         INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_TJ_RAD_INVEST_COMP primary key (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INVEST_COMP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INVEST_COMP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INVEST_ITEM_DICT';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TB_TJ_RAD_INVEST_ITEM_DICT
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               DICT_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_TJ_RAD_INVEST_ITEM_DICT primary key (RID),
               constraint FK_TB_TJ_RAD_INVEST_ITEM_DICT1 foreign key (MAIN_ID)
              references TB_TJ_RAD_INVEST_COMP (RID),
              constraint FK_TB_TJ_RAD_INVEST_ITEM_DICT2 foreign key (DICT_ID)
              references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INVEST_ITEM_DICT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INVEST_ITEM_DICT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INVEST_ITEM';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TB_TJ_RAD_INVEST_ITEM
            (
               RID                  INTEGER              not null,
               COMP_TYPE            NUMBER(2),
               ITEM_DESC            VARCHAR2(50),
               ITEM_SET_DESC        VARCHAR2(200),
               OWN_COL              NUMBER(1),
               IF_NOT_NULL          NUMBER(1),
               IF_OTHER             NUMBER(1),
               IF_CAL               NUMBER(1),
               COUNT_FIELD_TAG      NUMBER(2),
               TABLE_TITLE_TAG      NUMBER(1),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_TJ_RAD_INVEST_ITEM primary key (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INVEST_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INVEST_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INVEST_COMP_ITEM';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TB_TJ_RAD_INVEST_COMP_ITEM
            (
               RID                  INTEGER              not null,
               INFO_ID              INTEGER,
               SUB_INFO_ID          INTEGER,
               ITEM_ID              INTEGER,
               NUM                  NUMBER(4),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_TJ_RAD_INVEST_COMP_ITEM primary key (RID),
               constraint FK_TB_TJ_RAD_INVEST_ITEM_COMP1 foreign key (INFO_ID)
                  references TB_TJ_RAD_INVEST_ITEM (RID),
                  constraint FK_TB_TJ_RAD_INVEST_ITEM_COMP2 foreign key (SUB_INFO_ID)
                  references TB_TJ_RAD_INVEST_ITEM (RID),
                  constraint FK_TB_TJ_RAD_INVEST_COMP_ITEM3 foreign key (ITEM_ID)
                  references TB_TJ_RAD_INVEST_COMP (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INVEST_COMP_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INVEST_COMP_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_INVEST_INST_SHOW';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TB_TJ_RAD_INVEST_INST_SHOW
            (
               RID                  INTEGER              not null,
               INST_TYPE_ID         INTEGER,
               COMP_ID              INTEGER,
               NUM                  NUMBER(4),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_TJ_RAD_INVEST_INST_SHOW primary key (RID),
               constraint FK_TB_TJ_RAD_INVEST_INST_SHOW1 foreign key (COMP_ID)
                  references TB_TJ_RAD_INVEST_ITEM (RID),
               constraint FK_TB_TJ_RAD_INVEST_INST_SHOW2 foreign key (INST_TYPE_ID)
                  references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_INVEST_INST_SHOW_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_INVEST_INST_SHOW_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_INFO';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INVEST_INFO
            (
               RID                  INTEGER              not null,
               RPT_YEAR             INTEGER,
               UNIT_NAME            VARCHAR2(100),
               UNIT_GRADE_ID        INTEGER,
               ADDRESS              VARCHAR2(200),
               CERT_LEVEL_ID        INTEGER,
               WORK_FORCE           INTEGER,
               RAD_PSNS             INTEGER,
               MANAGE_UNIT          NUMBER(1),
               MANAGE_UNIT_NAME     VARCHAR2(100),
               MANAGE_USER          NUMBER(1),
               USER_QUAL_ID         INTEGER,
               ADMIN_TRAIN          NUMBER(1),
               ADMIN_TRAIN_NAME     VARCHAR2(100),
               DAILY_MONITOR        NUMBER(1),
               MONITOR_RST_ADDR     VARCHAR2(200),
               NOTIFY_ID            INTEGER,
               OTHER_NOTIFY_NAME    VARCHAR2(100),
               BUILD_ARCH           NUMBER(1),
               ARCH_STORAGE_ADDR    VARCHAR2(200),
               HETH_TRAIN           NUMBER(1),
               HETH_TRAIN_NAME      VARCHAR2(100),
               HETH_CHECK           NUMBER(1),
               HETH_CHECK_NAME      VARCHAR2(100),
               MANAGE_UNIT_ID       INTEGER              not null,
               INVEST_UNIT_NAME     VARCHAR2(100),
               FILL_NAME            VARCHAR2(50),
               FILL_LINK_PHONE      VARCHAR2(20),
               FILL_DATE            DATE,
               ANNEX_NAME           VARCHAR2(200),
               ANNEX_PATH           VARCHAR2(200),
               STATE                NUMBER(1),
               BACK_RSN             VARCHAR2(200),
               CHECK_USER_ID        INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_RAD_INVEST_INFO primary key (RID),
                  constraint FK_TD_TJ_RAD_INVEST_INFO1 foreign key (UNIT_GRADE_ID)
                  references TS_SIMPLE_CODE (RID),
                  constraint FK_TD_TJ_RAD_INVEST_INFO2 foreign key (CERT_LEVEL_ID)
                  references TS_SIMPLE_CODE (RID),
                  constraint FK_TD_TJ_RAD_INVEST_INFO3 foreign key (USER_QUAL_ID)
                  references TS_SIMPLE_CODE (RID),
                  constraint FK_TD_TJ_RAD_INVEST_INFO4 foreign key (NOTIFY_ID)
                  references TS_SIMPLE_CODE (RID),
                  constraint FK_TD_TJ_RAD_INVEST_INFO5 foreign key (MANAGE_UNIT_ID)
                  references TB_TJ_RADHETH (RID),
                  constraint FK_TD_TJ_RAD_INVEST_INFO6 foreign key (CHECK_USER_ID)
                  references TS_USER_INFO (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_INFO_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_HETH_ARCH';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INVEST_HETH_ARCH
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ARCH_ID              INTEGER,
               OTHER_ARCH           VARCHAR2(100),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_RAD_INVEST_HETH_ARCH primary key (RID),
                  constraint FK_TD_TJ_RAD_INVEST_HETH_ARCH1 foreign key (MAIN_ID)
                  references TD_TJ_RAD_INVEST_INFO (RID),
                  constraint FK_TD_TJ_RAD_INVEST_HETH_ARCH2 foreign key (ARCH_ID)
                  references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_HETH_ARCH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_HETH_ARCH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_RST_MAIN';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INVEST_RST_MAIN
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               INST_TYPE_ID         INTEGER,
               INFO_ID              INTEGER,
               NUM                  NUMBER(4),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_RAD_INVEST_RST_MAIN primary key (RID),
                  constraint FK_TD_TJ_RAD_INVEST_RST_MAIN1 foreign key (MAIN_ID)
                  references TD_TJ_RAD_INVEST_INFO (RID),
                  constraint FK_TD_TJ_RAD_INVEST_RST_MAIN2 foreign key (INFO_ID)
                  references TB_TJ_RAD_INVEST_ITEM (RID),
                  constraint FK_TD_TJ_RAD_INVEST_RST_MAIN3 foreign key (INST_TYPE_ID)
                  references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_RST_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_RST_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_SAFF';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INVEST_SAFF
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               INST_TYPE_ID         INTEGER,
               PHY_PSNS             INTEGER,
               RAD_PSNS             INTEGER,
               MAIN_PSNS            INTEGER,
               WOMAN_PSNS           INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_RAD_INVEST_SAFF primary key (RID),
                  constraint FK_TD_TJ_RAD_INVEST_SAFF1 foreign key (MAIN_ID)
                  references TD_TJ_RAD_INVEST_INFO (RID),
                  constraint FK_TD_TJ_RAD_INVEST_SAFF2 foreign key (INST_TYPE_ID)
                  references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_SAFF_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_SAFF_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_RST_SUB';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INVEST_RST_SUB
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           SUB_COMP_ID          INTEGER,
           SUB_COMP_NUM         NUMBER(4),
           ITEM_ID              INTEGER,
           ITEM_NUM             NUMBER(4),
           RST_VAL              VARCHAR2(100),
           OTHER_RST_VAL        VARCHAR2(200),
           DATE_VAL             DATE,
           DICT_ID              INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_TJ_RAD_INVEST_RST_SUB primary key (RID),
               constraint FK_TD_TJ_RAD_INVEST_RST_SUB1 foreign key (MAIN_ID)
              references TD_TJ_RAD_INVEST_RST_MAIN (RID),
               constraint FK_TD_TJ_RAD_INVEST_RST_SUB2 foreign key (SUB_COMP_ID)
              references TB_TJ_RAD_INVEST_ITEM (RID),
               constraint FK_TD_TJ_RAD_INVEST_RST_SUB3 foreign key (ITEM_ID)
              references TB_TJ_RAD_INVEST_COMP (RID),
              constraint FK_TD_TJ_RAD_INVEST_RST_SUB4 foreign key (DICT_ID)
              references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_RST_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_RST_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INST_DETAIL';
              IF NUM = 0 THEN EXECUTE IMMEDIATE '
              create table TD_TJ_RAD_INST_DETAIL
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               PRODUCE_UNIT         VARCHAR2(100),
               INST_TYPE_NO         VARCHAR2(100),
               INSTALL_DATE         DATE,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_RAD_INST_DETAIL primary key (RID),
              constraint FK_TD_TJ_RAD_INST_DETAIL1 foreign key (MAIN_ID)
                references TD_TJ_RAD_INVEST_RST_SUB (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INST_DETAIL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INST_DETAIL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('INSTITUTION_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD INSTITUTION_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('JOB_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD JOB_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RADHETH_PSN4'
                AND TABLE_NAME = 'TD_TJ_RADHETH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN   ADD CONSTRAINT FK_TD_TJ_RADHETH_PSN4 FOREIGN KEY (JOB_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('TITLE_LEVEL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD TITLE_LEVEL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RADHETH_PSN5'
                AND TABLE_NAME = 'TD_TJ_RADHETH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN   ADD CONSTRAINT FK_TD_TJ_RADHETH_PSN5 FOREIGN KEY (TITLE_LEVEL_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('OTHER_JOB');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD OTHER_JOB VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('STOP_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD STOP_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_FREQ_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_RAD_FREQ_RCD (
                RID                  INTEGER              not null,
                   RPT_YEAR             INTEGER,
                   UNIT_NAME            VARCHAR2(100)        not null,
                   XRAY_DIAG_NUM        NUMBER(8),
                   XRAY_CT_NUM          NUMBER(8),
                   XRAY_OTHER_NUM       NUMBER(8),
                   INTERVTION_NUM       NUMBER(8),
                   RAD_TRMT_NUM         NUMBER(8),
                   NUCS_MEDICAL_DIAG_NUM NUMBER(8),
                   NUCS_MEDICAL_TRMT_NUM NUMBER(8),
                   MANAGE_UNIT_ID       INTEGER              not null,
                   INVEST_UNIT_NAME     VARCHAR2(100),
                   FILL_NAME            VARCHAR2(50),
                   FILL_LINK_PHONE      VARCHAR2(20),
                   FILL_DATE            DATE,
                   ANNEX_NAME           VARCHAR2(200),
                   ANNEX_PATH           VARCHAR2(200),
                   STATE                NUMBER(1),
                   BACK_RSN             VARCHAR2(200),
                   CHECK_USER_ID        INTEGER,
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,
                               MODIFY_MANID         INTEGER,
                                  constraint PK_TD_TJ_RAD_FREQ_RCD primary key (RID)
                                  , CONSTRAINT FK_TD_TJ_RAD_FREQ_RCD1 FOREIGN KEY (MANAGE_UNIT_ID) REFERENCES TB_TJ_RADHETH(RID)
                                  ,   CONSTRAINT FK_TD_TJ_RAD_FREQ_RCD2 FOREIGN KEY (CHECK_USER_ID) REFERENCES TS_USER_INFO(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_FREQ_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_FREQ_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>

    <!-- +非医疗机构用人单位基本情况调查表20200817 -->
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_NON_MEDICAL_BASE_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_NON_MEDICAL_BASE_RCD (
                   RID                  INTEGER              not null,
                   RPT_YEAR             INTEGER,
                   UNIT_NAME            VARCHAR2(100)        not null,
                   INSTITUTION_CODE     VARCHAR2(50)         not null,
                   ZONE_ID              INTEGER,
                   ADDRESS              VARCHAR2(200)        not null,
                   ON_DUTY_PSN_NUM      NUMBER(8),
                   FS_WORK_PSN_NUM      NUMBER(8),
                   PSNAL_DOSE_PSN_NUM   NUMBER(8),
                   FS_TRAIN_PSN_NUM     NUMBER(8),
                   FS_HETH_CHECK_PSN_NUM NUMBER(8),
                   ACCELERATOR_NUM      NUMBER(4),
                   XRAY_FLAW_DECT_NUM   NUMBER(4),
                   LUGGAGE_DECT_NUM     NUMBER(4),
                   XRAY_INSTRU_NUM      NUMBER(4),
                   XRAY_OTHER_NUM       NUMBER(4),
                   IRRAD_DEV_NUM        NUMBER(4),
                   GM_FLAW_DECT_NUM     NUMBER(4),
                   SOURCE_INSTRU_NUM    NUMBER(4),
                   SOURCE_OTHER_NUM     NUMBER(4),
                   MINE_TYPES           VARCHAR2(100),
                   UNCLEAR_POWER_NUM    NUMBER(4),
                   WORKPLACE_GRADEA     NUMBER(4),
                   WORKPLACE_GRADEB     NUMBER(4),
                   WORKPLACE_GRADEC     NUMBER(4),
                   RPT_UNIT_ID          INTEGER              not null,
                   SURVEY_UNITNAME      VARCHAR2(100),
                   FILL_NAME            VARCHAR2(50),
                   FILL_LINK_PHONE      VARCHAR2(20),
                   FILL_DATE            DATE,
                   ANNEX_NAME           VARCHAR2(200),
                   ANNEX_PATH           VARCHAR2(200),
                   STATE                NUMBER(1),
                   BACK_RSN             VARCHAR2(200),
                   CHECK_USER_ID        INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_NON_MEDICAL_BASE_RCD primary key (RID),
                   constraint FK_TD_TJ_NON_MEDICAL_BASE_RCD1 foreign key (ZONE_ID)
                         references TS_ZONE (RID),
                   constraint FK_TD_TJ_NON_MEDICAL_BASE_RCD2 foreign key (RPT_UNIT_ID)
                         references TS_UNIT (RID),
                   constraint FK_TD_TJ_NON_MEDICAL_BASE_RCD3 foreign key (CHECK_USER_ID)
                         references TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_NON_MEDICAL_BASE_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_NON_MEDICAL_BASE_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_NON_MDC_JC_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_NON_MDC_JC_TYPE (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   JC_TYPE_ID           INTEGER,
                   OTHER_DESC           VARCHAR2(50),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_NON_MDC_JC_TYPE primary key (RID),
                   constraint FK_TD_TJ_NON_MDC_JC_TYPE1 foreign key (MAIN_ID)
                         references TD_TJ_NON_MEDICAL_BASE_RCD (RID),
                   constraint FK_TD_TJ_NON_MDC_JC_TYPE2 foreign key (JC_TYPE_ID)
                         references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_NON_MDC_JC_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_NON_MDC_JC_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_HETH_RCD
                (
                   RID                  INTEGER              not null,
                   RPT_YEAR             INTEGER,
                   UNIT_NAME            VARCHAR2(100)        not null,
                   INSTITUTION_CODE     VARCHAR2(50)         not null,
                   ZONE_ID              INTEGER,
                   ADDRESS              VARCHAR2(200),
                   ON_DUTY_PSN_NUM      NUMBER(8),
                   FS_WORK_PSN_NUM      NUMBER(8),
                   UNCLEAR_POWER_NUM    NUMBER(4),
                   WORKPLACE_GRADEA     NUMBER(4),
                   WORKPLACE_GRADEB     NUMBER(4),
                   WORKPLACE_GRADEC     NUMBER(4),
                   MINE_TYPES           VARCHAR2(100),
                   MIME_METHODS         VARCHAR2(100),
                   IF_DEV_TRAIN         NUMBER(1),
                   TRAIN_PSN_NUM        NUMBER(8),
                   IF_DEV_DOSE_MONIT    NUMBER(1),
                   DOSE_MONIT_PSN_NUM   NUMBER(8),
                   FIVE_YEAR_GT_20MSV   NUMBER(8),
                   MAX_MSV              NUMBER(12,6),
                   IF_DEV_CHK_GT_20MSV  NUMBER(1),
                   IF_HETH_CHECK        NUMBER(1),
                   IF_CHK_ONE_PER_ONE   NUMBER(1),
                   ONE_HETH_CHK_PSN_NUM NUMBER(8),
                   IF_CHK_ONE_PER_TWO   NUMBER(1),
                   TWO_HETH_CHK_PSN_NUM NUMBER(8),
                   BEFORE_CHK_PSN_NUM   NUMBER(8),
                   ON_CHK_PSN_NUM       NUMBER(8),
                   OUT_CHK_PSN_NUM      NUMBER(8),
                   EMERG_CHK_PSN_NUM    NUMBER(8),
                   IF_CHK_OTHER_ITEM    NUMBER(1),
                   IF_PROTECT_SET       NUMBER(1),
                   PROTECT_TOTAL_NUM    NUMBER(8),
                   RUBBER_APRON_NUM     NUMBER(8),
                   RUBBER_HATS_NUM      NUMBER(8),
                   RUBBER_NECK_NUM      NUMBER(8),
                   RUBBER_GLOVE_NUM     NUMBER(8),
                   PROTECTIVE_GLASS_NUM NUMBER(8),
                   PROTECTIVE_SCREEN_NUM NUMBER(8),
                   PROTECT_OTHER_NUM    NUMBER(8),
                   IF_DOSE_SET          NUMBER(1),
                   DOSE_TOTAL_NUM       NUMBER(8),
                   IF_RAD_PROTECT       NUMBER(8),
                   RAD_PROTECT_TOTAL_NUM NUMBER(8),
                   XY_DOSE_NUM          NUMBER(8),
                   MIDDLE_DOSE_NUM      NUMBER(8),
                   SURFACE_DOSE_NUM     NUMBER(8),
                   RAD_PROTECT_OTHER_NUM NUMBER(8),
                   IF_CURRENT_EVAL      NUMBER(1),
                   DEV_JC_BY_SELF       NUMBER(1),
                   DEV_JC_BY_ENTRUST    NUMBER(1),
                   IF_JC_RST            NUMBER(1),
                   IF_ZYB_RPT           NUMBER(1),
                   RPT_UNIT_ID          INTEGER              not null,
                   SURVEY_UNITNAME      VARCHAR2(100),
                   FILL_NAME            VARCHAR2(50),
                   FILL_LINK_PHONE      VARCHAR2(20),
                   FILL_DATE            DATE,
                   ANNEX_NAME           VARCHAR2(200),
                   ANNEX_PATH           VARCHAR2(200),
                   STATE                NUMBER(1),
                   BACK_RSN             VARCHAR2(200),
                   CHECK_USER_ID        INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_CRPT_HETH_RCD primary key (RID),
                   constraint FK_TD_TJ_CRPT_HETH_RCD1 foreign key (ZONE_ID)
                   references TS_ZONE (RID),
                   constraint FK_TD_TJ_CRPT_HETH_RCD2 foreign key (RPT_UNIT_ID)
                   references TS_UNIT (RID),
                   constraint FK_TD_TJ_CRPT_HETH_RCD3 foreign key (CHECK_USER_ID)
                   references TS_USER_INFO (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_CRPT_HETH_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_CRPT_HETH_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_HETH_JC_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_HETH_JC_TYPE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   JC_TYPE_ID           INTEGER,
                   OTHER_DESC           VARCHAR2(50),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_CRPT_HETH_JC_TYPE primary key (RID),
                   constraint FK_TD_TJ_CRPT_HETH_JC_TYPE1 foreign key (MAIN_ID)
                   references TD_TJ_CRPT_HETH_RCD (RID),
                   constraint FK_TD_TJ_CRPT_HETH_JC_TYPE2 foreign key (JC_TYPE_ID)
                   references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>115</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_CRPT_HETH_JC_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_CRPT_HETH_JC_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>116</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_RAD_DEVICE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_RAD_DEVICE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   RAD_DEVICE_ID        INTEGER,
                   RAD_OTHER_DEVICE     VARCHAR2(50),
                   RAD_TYPE_ID          INTEGER,
                   RAD_OTHER_TYPE       VARCHAR2(50),
                   EMV                  VARCHAR2(50),
                   PURPOSE              VARCHAR2(100),
                   RAD_SET_TYPE_ID      INTEGER,
                   RAD_SET_NUM          NUMBER(4),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_CRPT_RAD_DEVICE primary key (RID),
                   constraint FK_TD_TJ_CRPT_RAD_DEVICE1 foreign key (MAIN_ID)
                   references TD_TJ_CRPT_HETH_RCD (RID),
                   constraint FK_TD_TJ_CRPT_RAD_DEVICE2 foreign key (RAD_DEVICE_ID)
                   references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_TJ_CRPT_RAD_DEVICE3 foreign key (RAD_TYPE_ID)
                   references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_TJ_CRPT_RAD_DEVICE4 foreign key (RAD_SET_TYPE_ID)
                   references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>117</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_CRPT_RAD_DEVICE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_CRPT_RAD_DEVICE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>118</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_SOURCE_DEVICE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_SOURCE_DEVICE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SOURCE_DEVICE_ID     INTEGER,
                   SOURCE_OTHER_DEVICE  VARCHAR2(50),
                   RADIONUCLIDE_NAME    VARCHAR2(50),
                   INITIAL_ACTIVITY     VARCHAR2(50),
                   INVEST_ACTIVITY      VARCHAR2(50),
                   SOURCE_TYPE_ID       INTEGER,
                   SOURCE_NUM           NUMBER(4),
                   PURPOSE              VARCHAR2(100),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_CRPT_SOURCE_DEVICE primary key (RID),
                   constraint FK_TD_TJ_CRPT_SOURCE_DEVICE1 foreign key (MAIN_ID)
                   references TD_TJ_CRPT_HETH_RCD (RID),
                   constraint FK_TD_TJ_CRPT_SOURCE_DEVICE2 foreign key (SOURCE_DEVICE_ID)
                   references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_TJ_CRPT_SOURCE_DEVICE3 foreign key (SOURCE_TYPE_ID)
                   references TS_SIMPLE_CODE (RID)
                  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>119</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_CRPT_SOURCE_DEVICE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_CRPT_SOURCE_DEVICE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>120</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ADDRESS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY ADDRESS NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>121</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('EDUCATION_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD EDUCATION_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>122</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RADHETH_PSN6'
                AND TABLE_NAME = 'TD_TJ_RADHETH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN   ADD CONSTRAINT FK_TD_TJ_RADHETH_PSN6 FOREIGN KEY (EDUCATION_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>123</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_COMP')
                AND COLUMN_NAME = UPPER('DEFAULT_ROW');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_COMP ADD DEFAULT_ROW NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>124</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_RST_MAIN')
                AND COLUMN_NAME = UPPER('TUB_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_RST_MAIN ADD TUB_NUM NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>125</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_COMP')
                AND COLUMN_NAME = UPPER('IF_HAS_TUB');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_COMP ADD IF_HAS_TUB NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>126</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('UNCLEAR_POWER_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY UNCLEAR_POWER_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>127</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('WORKPLACE_GRADEA');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY WORKPLACE_GRADEA NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>128</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('WORKPLACE_GRADEB');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY WORKPLACE_GRADEB NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>129</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('WORKPLACE_GRADEC');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY WORKPLACE_GRADEC NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>130</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_RAD_DEVICE')
                AND COLUMN_NAME = UPPER('RAD_SET_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_RAD_DEVICE MODIFY RAD_SET_NUM NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>131</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_SOURCE_DEVICE')
                AND COLUMN_NAME = UPPER('SOURCE_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_SOURCE_DEVICE MODIFY SOURCE_NUM NUMBER(8)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>132</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('PROTECT_TOTAL_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY PROTECT_TOTAL_NUM NUMBER(10)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>133</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('RAD_PROTECT_TOTAL_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY RAD_PROTECT_TOTAL_NUM NUMBER(10)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>134</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('IF_ZYBWH_RPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD IF_ZYBWH_RPT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>135</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('BASE_RCD_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD BASE_RCD_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>136</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD4'
                AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD   ADD CONSTRAINT FK_TD_TJ_CRPT_HETH_RCD4 FOREIGN KEY (BASE_RCD_ID)      REFERENCES TD_TJ_NON_MEDICAL_BASE_RCD (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>137</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('ACCELERATOR_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY ACCELERATOR_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>138</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('XRAY_FLAW_DECT_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY XRAY_FLAW_DECT_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>139</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('LUGGAGE_DECT_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY LUGGAGE_DECT_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>140</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('XRAY_INSTRU_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY XRAY_INSTRU_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>141</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('XRAY_OTHER_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY XRAY_OTHER_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>142</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('IRRAD_DEV_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY IRRAD_DEV_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>143</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('GM_FLAW_DECT_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY GM_FLAW_DECT_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>144</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('SOURCE_INSTRU_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY SOURCE_INSTRU_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>145</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('SOURCE_OTHER_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY SOURCE_OTHER_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>146</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('UNCLEAR_POWER_NUM');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY UNCLEAR_POWER_NUM NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>147</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('WORKPLACE_GRADEA');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY WORKPLACE_GRADEA NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>148</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('WORKPLACE_GRADEB');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY WORKPLACE_GRADEB NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>149</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('WORKPLACE_GRADEC');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY WORKPLACE_GRADEC NUMBER(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>150</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('BACK_RSN');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY BACK_RSN VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>151</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_NON_MEDICAL_BASE_RCD')
                AND COLUMN_NAME = UPPER('BACK_RSN');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_NON_MEDICAL_BASE_RCD MODIFY BACK_RSN VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>152</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('BACK_RSN');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD MODIFY BACK_RSN VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>153</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('BACK_RSN');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO MODIFY BACK_RSN VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>154</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INVEST_ITEM_DICT')
                AND COLUMN_NAME = UPPER('ANALY_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INVEST_ITEM_DICT ADD ANALY_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>155</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('FINAL_BACK_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD FINAL_BACK_RSN VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>156</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('FINAL_CHECK_USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD FINAL_CHECK_USER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>157</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD5'
                AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONSTRAINT FK_TD_TJ_CRPT_HETH_RCD5 FOREIGN KEY (FINAL_CHECK_USER_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>158</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('FINAL_BACK_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD FINAL_BACK_RSN VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>159</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('FINAL_CHECK_USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD FINAL_CHECK_USER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>160</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INVEST_INFO6'
                AND TABLE_NAME = 'TD_TJ_RAD_INVEST_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD CONSTRAINT FK_TD_TJ_RAD_INVEST_INFO6 FOREIGN KEY (FINAL_CHECK_USER_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>161</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('FINAL_BACK_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD FINAL_BACK_RSN VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>162</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('FINAL_CHECK_USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD FINAL_CHECK_USER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>163</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_FREQ_RCD3'
                AND TABLE_NAME = 'TD_TJ_RAD_FREQ_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD CONSTRAINT FK_TD_TJ_RAD_FREQ_RCD3 FOREIGN KEY (FINAL_CHECK_USER_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>164</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PRO_ITEM_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_PRO_ITEM_REL (
                    RID INTEGER NOT NULL,
                    INST_ID INTEGER,
                    ITEM_ID INTEGER NOT NULL,
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_PRO_ITEM_REL PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM_REL1 FOREIGN KEY (INST_ID) REFERENCES TS_SIMPLE_CODE (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM_REL2 FOREIGN KEY (ITEM_ID) REFERENCES TB_TJ_RAD_ITEM (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>165</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_ITEM_PLACE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_ITEM_PLACE (
                    RID INTEGER NOT NULL,
                    MAIN_ID INTEGER,
                    CODION_CONT VARCHAR2 (100),
                    IF_DFT NUMBER (1),
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_ITEM_PLACE PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_ITEM_PLACE FOREIGN KEY (MAIN_ID) REFERENCES TD_TJ_RAD_PRO_ITEM_REL (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>166</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_ITEM_POINT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_ITEM_POINT (
                    RID INTEGER NOT NULL,
                    MAIN_ID INTEGER,
                    ITEM_STDVALUE VARCHAR2 (100),
                    IF_DFT NUMBER (1),
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_ITEM_POINT PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_ITEM_POINT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_TJ_RAD_ITEM_PLACE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>167</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_ITEM_PLACE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_ITEM_PLACE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>168</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_ITEM_POINT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_ITEM_POINT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>169</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PRO_ITEM_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PRO_ITEM_REL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>170</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('CHECK_POINT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD CHECK_POINT VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>171</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PRO_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_PRO_ITEM
                (
                    RID INTEGER NOT NULL,
                    MAIN_ID              INTEGER              not null,
            ITEM_ID              INTEGER              not null,
            MISS_TAG             NUMBER(1)            not null,
            IF_REMOVE_BACKGRD_VALUE NUMBER(1),
            BACKGRD_VALUE        VARCHAR2(100),
            TEST_CASE            VARCHAR2(400),
            ITEM_REL_ID          INTEGER,
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_PRO_ITEM PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM1 FOREIGN KEY (MAIN_ID)
                      REFERENCES TD_TJ_RAD_CHECK_SUB (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM2 FOREIGN KEY (ITEM_ID)
                      REFERENCES TB_TJ_RAD_ITEM (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM3 FOREIGN KEY (ITEM_REL_ID)
                      REFERENCES TD_TJ_RAD_ITEM_REL (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>172</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PRO_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PRO_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>173</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('MEASURE_VAL')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 200;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT MODIFY MEASURE_VAL NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>174</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD XH NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>175</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('IF_REMOVE_BACKGRD_VALUE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD IF_REMOVE_BACKGRD_VALUE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>176</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('BCKVAL_MEASURE_UNIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD BCKVAL_MEASURE_UNIT NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>177</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_POINT')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_POINT ADD XH NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>178</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_PLACE')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_PLACE ADD XH NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>179</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_PRODUCT_UNIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_PRODUCT_UNIT
        (
           RID                  INTEGER              not null,
           PRODUCT_UNIT         NVARCHAR2(50),
           INST_TYPE_NO         NVARCHAR2(50),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_PRODUCT_UNIT primary key (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>180</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_PRODUCT_UNIT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_PRODUCT_UNIT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>181</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_STD_PRODUCT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_STD_PRODUCT
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           PRODUCT_ID           INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_STD_PRODUCT primary key (RID),
           constraint FK_TB_TJ_RAD_STD_UNIT1 foreign key (MAIN_ID)
               references TD_TJ_RAD_ITEM_STD (RID),
               constraint FK_TB_TJ_RAD_STD_UNIT2 foreign key (PRODUCT_ID)
               references TB_TJ_RAD_PRODUCT_UNIT (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>182</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_STD_PRODUCT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_STD_PRODUCT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>183</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('PRODUCT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD PRODUCT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>184</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_TJ_RAD_INST4'
                AND TABLE_NAME = 'TB_TJ_RAD_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST  ADD CONSTRAINT FK_TB_TJ_RAD_INST4 FOREIGN KEY (PRODUCT_ID)
                     REFERENCES TB_TJ_RAD_PRODUCT_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>185</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_REL')
                AND COLUMN_NAME = UPPER('JC_COND_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_REL ADD JC_COND_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>186</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_STD')
                AND COLUMN_NAME = UPPER('SORT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_STD ADD SORT NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>187</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_STD_PARAM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TB_TJ_RAD_STD_PARAM
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           INFO_ID              INTEGER,
           COMP_ID              INTEGER,
           DICT_ID              INTEGER,
           RULE                 NUMBER(2),
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TB_TJ_RAD_STD_PARAM primary key (RID),
           constraint FK_TB_TJ_RAD_STD_PARAM1 foreign key (MAIN_ID)
               references TD_TJ_RAD_ITEM_STD (RID),
               constraint FK_TB_TJ_RAD_STD_PARAM4 foreign key (DICT_ID)
               references TS_SIMPLE_CODE (RID),
                   constraint FK_TB_TJ_RAD_STD_PARAM2 foreign key (INFO_ID)
               references TB_TJ_RAD_INST_COMP (RID),
               constraint FK_TB_TJ_RAD_STD_PARAM3 foreign key (COMP_ID)
               references TB_TJ_RAD_INST_ITEM (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>188</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_STD_PARAM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_STD_PARAM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>189</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST_COMP')
                AND COLUMN_NAME = UPPER('IF_INST_PARAM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST_COMP ADD IF_INST_PARAM NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>190</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('INFO_XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD INFO_XH INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>191</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('INFO_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD INFO_ID INTEGER add constraint FK_TD_TJ_RAD_RESULT6 foreign key (INFO_ID)
      references TB_TJ_RAD_INST_COMP (RID) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>192</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('ON_DUTY_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD ON_DUTY_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>193</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('OUT_DUTY_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD OUT_DUTY_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>194</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('ON_PLACE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD ON_PLACE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>195</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PRO_ITEM_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_PRO_ITEM_REL (
                    RID INTEGER NOT NULL,
                    INST_ID INTEGER,
                    ITEM_ID INTEGER NOT NULL,
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_PRO_ITEM_REL PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM_REL1 FOREIGN KEY (INST_ID) REFERENCES TS_SIMPLE_CODE (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM_REL2 FOREIGN KEY (ITEM_ID) REFERENCES TB_TJ_RAD_ITEM (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>196</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PRO_ITEM_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PRO_ITEM_REL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>197</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_ITEM_PLACE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_ITEM_PLACE (
                    RID INTEGER NOT NULL,
                    MAIN_ID INTEGER,
                    CODION_CONT VARCHAR2 (100),
                    IF_DFT NUMBER (1),
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_ITEM_PLACE PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_ITEM_PLACE FOREIGN KEY (MAIN_ID) REFERENCES TD_TJ_RAD_PRO_ITEM_REL (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>198</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_ITEM_POINT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_ITEM_POINT (
                    RID INTEGER NOT NULL,
                    MAIN_ID INTEGER,
                    ITEM_STDVALUE VARCHAR2 (100),
                    IF_DFT NUMBER (1),
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_ITEM_POINT PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_ITEM_POINT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_TJ_RAD_ITEM_PLACE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>199</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_ITEM_PLACE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_ITEM_PLACE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>200</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_ITEM_POINT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_ITEM_POINT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>201</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('CHECK_POINT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD CHECK_POINT VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>202</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_PRO_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_RAD_PRO_ITEM
                (
                    RID INTEGER NOT NULL,
                    MAIN_ID              INTEGER              not null,
            ITEM_ID              INTEGER              not null,
            MISS_TAG             NUMBER(1)            not null,
            IF_REMOVE_BACKGRD_VALUE NUMBER(1),
            BACKGRD_VALUE        VARCHAR2(100),
            TEST_CASE            VARCHAR2(400),
            ITEM_REL_ID          INTEGER,
                    CREATE_DATE TIMESTAMP NOT NULL,
                    CREATE_MANID INTEGER NOT NULL,
                    MODIFY_DATE TIMESTAMP,
                    MODIFY_MANID INTEGER,
                    CONSTRAINT PK_TD_TJ_RAD_PRO_ITEM PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM1 FOREIGN KEY (MAIN_ID)
                      REFERENCES TD_TJ_RAD_CHECK_SUB (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM2 FOREIGN KEY (ITEM_ID)
                      REFERENCES TB_TJ_RAD_ITEM (RID),
                    CONSTRAINT FK_TD_TJ_RAD_PRO_ITEM3 FOREIGN KEY (ITEM_REL_ID)
                      REFERENCES TD_TJ_RAD_ITEM_REL (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>203</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_PRO_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_PRO_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>204</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('MEASURE_VAL')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 200;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT MODIFY MEASURE_VAL NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>205</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD XH NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>206</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('IF_REMOVE_BACKGRD_VALUE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD IF_REMOVE_BACKGRD_VALUE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>207</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND COLUMN_NAME = UPPER('BCKVAL_MEASURE_UNIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD BCKVAL_MEASURE_UNIT NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>208</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_POINT')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_POINT ADD XH NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>209</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_PLACE')
                AND COLUMN_NAME = UPPER('XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_PLACE ADD XH NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>210</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_REL')
                AND COLUMN_NAME = UPPER('JC_COND_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_REL ADD JC_COND_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>211</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
                AND COLUMN_NAME = UPPER('CENT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN ADD CENT_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>212</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PROS')
                AND COLUMN_NAME = UPPER('NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PROS ADD NUM NUMBER(5)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>213</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('XN_JC_COND_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD XN_JC_COND_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>214</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_EQU_ACCPT_EVAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_EQU_ACCPT_EVAL (   RID                  INTEGER              not null,   UNIT_ID              INTEGER              not null,   PROJECT_ADDR         VARCHAR2(200)        not null,   PROJECT_LINK_MAN     VARCHAR2(100),   PROJECT_LINK_TEL     VARCHAR2(50),   RPT_UNITNAME         VARCHAR2(100),   LINK_MAN             VARCHAR2(100),   LINK_TEL             VARCHAR2(50),   ACCEPT_CASE          VARCHAR2(4000),   IMPROVE_CASE         VARCHAR2(4000),   RPT_CONCLUSION       VARCHAR2(4000),   STATE_MARK           NUMBER(1),   FILL_UNIT_ID         INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_EQU_ACCPT_EVAL primary key (RID),    constraint FK_TD_TJ_EQU_ACCPT_EVAL1 foreign key (UNIT_ID)     references TB_TJ_RADHETH (RID),   constraint FK_TD_TJ_EQU_ACCPT_EVAL2 foreign key (FILL_UNIT_ID)     references TD_ZW_SRVORGINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>215</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_EQU_ACCPT_EVAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_EQU_ACCPT_EVAL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>216</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_EQU_ACCPT_EVAL')
                AND COLUMN_NAME = UPPER('SUMMARY_DESC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_EQU_ACCPT_EVAL ADD SUMMARY_DESC VARCHAR2(4000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>217</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_EQU_ACCPT_EVAL')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('PROJECT_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_EQU_ACCPT_EVAL MODIFY PROJECT_ADDR NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>218</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_EQU_ACCPT_EVAL')
                AND COLUMN_NAME = UPPER('PROJECT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_EQU_ACCPT_EVAL ADD PROJECT_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>219</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_ITEM_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_ITEM_SUMMARY (   RID                  INTEGER              not null,   MAIN_ID              INTEGER              not null,   EQU_ID               INTEGER              not null,   RAD_ACTIVITY         VARCHAR2(100),   LOCATION             VARCHAR2(100),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_ITEM_SUMMARY primary key (RID),    constraint FK_TD_TJ_ITEM_SUMMARY1 foreign key (MAIN_ID)     references TD_TJ_EQU_ACCPT_EVAL (RID),   constraint FK_TD_TJ_ITEM_SUMMARY2 foreign key (EQU_ID)     references TB_TJ_RAD_INST (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>220</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_ITEM_SUMMARY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_ITEM_SUMMARY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>221</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_ITEM_SUMMARY')
                AND COLUMN_NAME = UPPER('EQU_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_ITEM_SUMMARY ADD EQU_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>222</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_ITEM_SUMMARY3'
                AND TABLE_NAME = 'TD_TJ_ITEM_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_ITEM_SUMMARY   ADD CONSTRAINT FK_TD_TJ_ITEM_SUMMARY3 FOREIGN KEY (EQU_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>223</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_ITEM_SUMMARY')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('EQU_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_ITEM_SUMMARY MODIFY EQU_ID NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>224</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_ITEM_QUALITY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_ITEM_QUALITY (   RID                  INTEGER              not null,   MAIN_ID              INTEGER              not null,   SORT_ID              INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_ITEM_QUALITY primary key (RID),    constraint FK_TD_TJ_ITEM_QUALITY1 foreign key (MAIN_ID)     references TD_TJ_EQU_ACCPT_EVAL (RID),   constraint FK_TD_TJ_ITEM_QUALITY2 foreign key (SORT_ID)     references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>225</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_ITEM_QUALITY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_ITEM_QUALITY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>226</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_ITEM_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_ITEM_TYPE (   RID                  INTEGER              not null,   MAIN_ID              INTEGER              not null,   SORT_ID              INTEGER              not null,   RPT_DATE             DATE,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_TJ_ITEM_TYPE primary key (RID),    constraint FK_TD_TJ_ITEM_TYPE1 foreign key (MAIN_ID)     references TD_TJ_EQU_ACCPT_EVAL (RID),   constraint FK_TD_TJ_ITEM_TYPE2 foreign key (SORT_ID)     references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>227</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_ITEM_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_ITEM_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>228</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_PSN_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_TJ_PSN_DETAIL
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER              not null,
           JOB_ID               INTEGER,
           OTHER_JOB            VARCHAR2(50),
           TITLE_LEVEL_ID       INTEGER,
           PSN_NUM              NUMBER(5),
           PSN_ID               INTEGER,
           PSN_NAME             VARCHAR2(200),
           PSN_SEX              INTEGER,
           EDUCATION_ID         INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_TJ_PSN_DETAIL primary key (RID),
           constraint FK_TD_TJ_PSN_DETAIL1 foreign key (MAIN_ID)
              references TD_TJ_EQU_ACCPT_EVAL (RID),
               constraint FK_TD_TJ_PSN_DETAIL2 foreign key (PSN_ID)
                references TD_TJ_RADHETH_PSN (RID),
               constraint FK_TD_TJ_PSN_DETAIL3 foreign key (JOB_ID)
                references TS_SIMPLE_CODE (RID),
               constraint FK_TD_TJ_PSN_DETAIL4 foreign key (TITLE_LEVEL_ID)
                references TS_SIMPLE_CODE (RID),
               constraint FK_TD_TJ_PSN_DETAIL5 foreign key (EDUCATION_ID)
                references TS_SIMPLE_CODE (RID)
        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>229</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_PSN_DETAIL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_PSN_DETAIL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>230</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('NUCLEAR_STACK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD NUCLEAR_STACK NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>231</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('NUCLEAR_POWER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD NUCLEAR_POWER NUMBER(6,5)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>232</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('IF_SYN_DEVELOP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_SYN_DEVELOP NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>233</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_UNSEALED_SOURCE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_TJ_CRPT_UNSEALED_SOURCE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   NUCLIDE_ID           INTEGER,
                   OTHER_NUCLIDE        NVARCHAR2(50),
                   DAILY_OPERATE_NUM    NUMBER(6,5),
                   OPERATE_WAY_ID       INTEGER,
                   PURPOSE              NVARCHAR2(100),
                   NUM                  INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_TJ_CRPT_UNSEALED_SOURCE primary key (RID)
                )
                 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>234</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
           DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_UNSEALED_SOURCE1'
                AND TABLE_NAME = 'TD_TJ_CRPT_UNSEALED_SOURCE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' alter table TD_TJ_CRPT_UNSEALED_SOURCE add constraint FK_TD_TJ_CRPT_UNSEALED_SOURCE1 foreign key (MAIN_ID)
                   references TD_TJ_CRPT_HETH_RCD (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>235</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_UNSEALED_SOURCE2'
                AND TABLE_NAME = 'TD_TJ_CRPT_UNSEALED_SOURCE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                 alter table TD_TJ_CRPT_UNSEALED_SOURCE
                add constraint FK_TD_TJ_CRPT_UNSEALED_SOURCE2 foreign key (NUCLIDE_ID)
                references TS_SIMPLE_CODE (RID)
                ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>236</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_UNSEALED_SOURCE3'
                AND TABLE_NAME = 'TD_TJ_CRPT_UNSEALED_SOURCE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                alter table TD_TJ_CRPT_UNSEALED_SOURCE
                add constraint FK_TD_TJ_CRPT_UNSEALED_SOURCE3 foreign key (OPERATE_WAY_ID)
                references TS_SIMPLE_CODE (RID)
                ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>237</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_CRPT_UNSEALED_SOURCE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_CRPT_UNSEALED_SOURCE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>238</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_SAFF')
                AND COLUMN_NAME = UPPER('PART_PHY_PSNS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_SAFF ADD PART_PHY_PSNS INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>239</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_SAFF')
                AND COLUMN_NAME = UPPER('PHY_JC_CYCLE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_SAFF ADD PHY_JC_CYCLE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>240</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('FILL_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD FILL_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>241</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INVEST_INFO8'
                AND TABLE_NAME = 'TD_TJ_RAD_INVEST_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO   ADD CONSTRAINT FK_TD_TJ_RAD_INVEST_INFO8 FOREIGN KEY (FILL_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>242</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('FILL_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD FILL_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>243</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_FREQ_RCD4'
                AND TABLE_NAME = 'TD_TJ_RAD_FREQ_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD   ADD CONSTRAINT FK_TD_TJ_RAD_FREQ_RCD4 FOREIGN KEY (FILL_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>244</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('RCD_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD RCD_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>245</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_TJ_RADHETH6'
                AND TABLE_NAME = 'TB_TJ_RADHETH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD CONSTRAINT FK_TB_TJ_RADHETH6 FOREIGN KEY (RCD_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>246</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('FILL_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD FILL_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>247</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD6'
                AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_CRPT_HETH_RCD add constraint FK_TD_TJ_CRPT_HETH_RCD6 foreign key (FILL_UNIT_ID) references TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>248</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_DETAIL')
                AND COLUMN_NAME = UPPER('INSTALL_DATE_CLT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_DETAIL ADD INSTALL_DATE_CLT NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>249</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_DETAIL')
                AND COLUMN_NAME = UPPER('PRODUCE_UNIT')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 200;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_DETAIL MODIFY PRODUCE_UNIT NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>250</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INST_DETAIL')
                AND COLUMN_NAME = UPPER('INST_TYPE_NO')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 200;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INST_DETAIL MODIFY INST_TYPE_NO NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>251</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_UNSEALED_SOURCE')
                AND COLUMN_NAME = UPPER('DAILY_OPERATE_NUM')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 200;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_UNSEALED_SOURCE MODIFY DAILY_OPERATE_NUM NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>252</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('NUCLEAR_POWER')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 200;
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY NUCLEAR_POWER NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>253</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('RAD_WORK_YEAR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD RAD_WORK_YEAR INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>254</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_MSMENT')
                AND COLUMN_NAME = UPPER('DOSE_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_MSMENT ADD DOSE_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>255</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_PSN_MSMENT3'
                AND TABLE_NAME = 'TD_TJ_RAD_PSN_MSMENT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_PSN_MSMENT add constraint FK_TD_TJ_RAD_PSN_MSMENT3 foreign key (DOSE_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>256</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_DC_HOSPITAL_BASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_RAD_DC_HOSPITAL_BASE
                (
                   RID                  INTEGER              not null,
                   RPT_YEAR             INTEGER,
           ZONE_ID              INTEGER,
           UNIT_NAME            NVARCHAR2(100),
           UNIT_GRADE_ID        INTEGER,
           INSTITUTION_CODE     NVARCHAR2(50),
           MED_UNIT_ID          INTEGER,
           FILL_USER_ID         INTEGER,
           FILL_DATE            DATE,
           BACK_RSN             NVARCHAR2(200),
           CHECK_USER_ID        INTEGER,
           CHECK_DATE           DATE,
           FINAL_CHECK_USER_ID  INTEGER,
           FINAL_CHECK_DATE     DATE,
           QUE_LIB_ID           INTEGER,
           STATE_MARK           NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_RAD_DC_HOSPITAL_BASE primary key (RID),
                   constraint FK_TD_RAD_DC_HOSPITAL_BASE1 foreign key (ZONE_ID)
                references TS_ZONE (RID),
               constraint FK_TD_RAD_DC_HOSPITAL_BASE2 foreign key (UNIT_GRADE_ID)
                references TS_SIMPLE_CODE (RID),
               constraint FK_TD_RAD_DC_HOSPITAL_BASE3 foreign key (MED_UNIT_ID)
                references TS_UNIT (RID),
               constraint FK_TD_RAD_DC_HOSPITAL_BASE4 foreign key (FILL_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_HOSPITAL_BASE5 foreign key (CHECK_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_HOSPITAL_BASE6 foreign key (FINAL_CHECK_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_HOSPITAL_BASE7 foreign key (QUE_LIB_ID)
                references TS_PROB_LIB (RID)
                )
                 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>257</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_DC_HOSPITAL_BASE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_DC_HOSPITAL_BASE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>258</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_HOSPITAL_BASE')
                AND COLUMN_NAME = UPPER('DC_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_HOSPITAL_BASE ADD DC_TYPE NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>259</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_DC_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_RAD_DC_PSNS
                (
                   RID                  INTEGER              not null,
                   DC_TYPE              NUMBER(2),
           RPT_YEAR             INTEGER,
           PSN_ID               INTEGER,
           ZONE_ID              INTEGER,
           UNIT_NAME            NVARCHAR2(100),
           PSN_NAME             NVARCHAR2(200)       not null,
           PSN_SEX              INTEGER,
           BIRTH_DATE           DATE,
           IDC_CARD             NVARCHAR2(20),
           JOB_ID               INTEGER,
           FILL_USER_ID         INTEGER,
           FILL_DATE            DATE,
           BACK_RSN             NVARCHAR2(200),
           CHECK_USER_ID        INTEGER,
           CHECK_DATE           DATE,
           FINAL_CHECK_USER_ID  INTEGER,
           FINAL_CHECK_DATE     DATE,
           QUE_LIB_ID           INTEGER,
           STATE_MARK           NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_RAD_DC_PSNS primary key (RID),
                   constraint FK_TD_RAD_DC_PSNS1 foreign key (PSN_ID)
                references TD_TJ_RADHETH_PSN (RID),
               constraint FK_TD_RAD_DC_PSNS2 foreign key (ZONE_ID)
                references TS_ZONE (RID),
               constraint FK_TD_RAD_DC_PSNS3 foreign key (JOB_ID)
                references TS_SIMPLE_CODE (RID),
               constraint FK_TD_RAD_DC_PSNS4 foreign key (FILL_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_PSNS5 foreign key (CHECK_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_PSNS6 foreign key (FINAL_CHECK_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_PSNS7 foreign key (QUE_LIB_ID)
                references TS_PROB_LIB (RID)
                )
                 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>260</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_DC_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_DC_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>261</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_PSNS')
                AND COLUMN_NAME = UPPER('OTHER_JOB');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_PSNS ADD OTHER_JOB NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>262</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_HOSPITAL_BASE')
                AND COLUMN_NAME = UPPER('QUE_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_HOSPITAL_BASE ADD QUE_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>263</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_RAD_DC_HOSPITAL_BASE8'
                AND TABLE_NAME = 'TD_RAD_DC_HOSPITAL_BASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_RAD_DC_HOSPITAL_BASE add constraint FK_TD_RAD_DC_HOSPITAL_BASE8 foreign key (QUE_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>264</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_HOSPITAL_BASE')
                AND COLUMN_NAME = UPPER('FILL_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_HOSPITAL_BASE ADD FILL_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>265</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_RAD_DC_HOSPITAL_BASE9'
                AND TABLE_NAME = 'TD_RAD_DC_HOSPITAL_BASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_RAD_DC_HOSPITAL_BASE add constraint FK_TD_RAD_DC_HOSPITAL_BASE9 foreign key (FILL_UNIT_ID) references TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>266</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_DC_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_RAD_DC_INST
                (
                   RID                  INTEGER              not null,
           QUE_TYPE_ID          INTEGER,
           RPT_YEAR             INTEGER,
           ZONE_ID              INTEGER,
           UNIT_NAME            NVARCHAR2(100),
           MED_UNIT_ID          INTEGER,
           INST_NO              NVARCHAR2(50),
           FILL_UNIT_ID         INTEGER,
           FILL_USER_ID         INTEGER,
           FILL_DATE            DATE,
           BACK_RSN             NVARCHAR2(200),
           CHECK_USER_ID        INTEGER,
           CHECK_DATE           DATE,
           FINAL_CHECK_USER_ID  INTEGER,
           FINAL_CHECK_DATE     DATE,
           QUE_LIB_ID           INTEGER,
           STATE_MARK           NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_RAD_DC_INST primary key (RID),
                   constraint FK_TD_RAD_DC_INST1 foreign key (QUE_TYPE_ID)
                references TS_SIMPLE_CODE (RID),
               constraint FK_TD_RAD_DC_INST2 foreign key (ZONE_ID)
                references TS_ZONE (RID),
               constraint FK_TD_RAD_DC_INST3 foreign key (MED_UNIT_ID)
                references TS_UNIT (RID),
               constraint FK_TD_RAD_DC_INST4 foreign key (FILL_UNIT_ID)
                references TS_UNIT (RID),
               constraint FK_TD_RAD_DC_INST5 foreign key (FILL_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_INST6 foreign key (CHECK_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_INST7 foreign key (FINAL_CHECK_USER_ID)
                references TS_USER_INFO (RID),
               constraint FK_TD_RAD_DC_INST8 foreign key (QUE_LIB_ID)
                references TS_PROB_LIB (RID)
                )
                 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>267</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_DC_INST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_DC_INST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>268</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_RAD_DC_INST')
                    AND COLUMN_NAME = UPPER('INST_NO')
                    AND DATA_TYPE = UPPER('NVARCHAR2')
                    AND DATA_LENGTH = 100;
                  IF NUM = 1 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_INST MODIFY INST_NO NVARCHAR2(100)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>269</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_PSNS')
                AND COLUMN_NAME = UPPER('QUE_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_PSNS ADD QUE_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>270</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_RAD_DC_PSNS8'
                AND TABLE_NAME = 'TD_RAD_DC_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_RAD_DC_PSNS add constraint FK_TD_RAD_DC_PSNS8 foreign key (QUE_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>271</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_PSNS')
                AND COLUMN_NAME = UPPER('FILL_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_PSNS ADD FILL_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>272</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_RAD_DC_PSNS9'
                AND TABLE_NAME = 'TD_RAD_DC_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_RAD_DC_PSNS add constraint FK_TD_RAD_DC_PSNS9 foreign key (FILL_UNIT_ID) references TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>273</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_REL')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('CHECK_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_REL MODIFY CHECK_TYPE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>274</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_ITEM_LAW')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('CHECK_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_ITEM_LAW MODIFY CHECK_TYPE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>275</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('CHECK_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT MODIFY CHECK_TYPE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>276</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_CRPT_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_RAD_CRPT_CHECK_MAIN
                    (
                       RID                  INTEGER              not null,
                       RPT_CODE             NVARCHAR2(100),
                       CHECK_DATE           DATE,
                       CHECK_UNIT_ID        INTEGER,
                       CRPT_ID              INTEGER,
                       STATE                NUMBER(1),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_RAD_CRPT_CHECK_MAIN primary key (RID),
                       constraint FK_TD_RAD_CRPT_CHECK_MAIN1 foreign key (CHECK_UNIT_ID)
                         references TD_ZW_SRVORGINFO (RID),
                       constraint FK_TD_RAD_CRPT_CHECK_MAIN2 foreign key (CRPT_ID)
                         references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>277</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_CRPT_CHECK_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_CRPT_CHECK_MAIN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>278</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_CRPT_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_RAD_CRPT_CHECK_SUB
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       SAMP_CODE            NVARCHAR2(50),
                       RAD_TYPE_ID          INTEGER,
                       OTHER_RAD_TYPE_NAME  NVARCHAR2(50),
                       RAD_NAME_ID          INTEGER,
                       CHECK_TYPE_ID        INTEGER,
                       IF_HG                NUMBER(1),
                       RECHECK              NUMBER(1),
                       CHECK_DATE           DATE,
                       STATE_MARK           NUMBER(1),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_RAD_CRPT_CHECK_SUB primary key (RID),
                       constraint FK_TD_RAD_CRPT_CHECK_SUB2 foreign key (RAD_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_RAD_CRPT_CHECK_SUB3 foreign key (RAD_NAME_ID)
                        references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_RAD_CRPT_CHECK_SUB4 foreign key (CHECK_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_RAD_CRPT_CHECK_SUB1 foreign key (MAIN_ID)
                        references TD_RAD_CRPT_CHECK_MAIN (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>279</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_CRPT_CHECK_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_CRPT_CHECK_SUB_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>280</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_CRPT_PRO_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_RAD_CRPT_PRO_ITEM
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_ID              INTEGER,
                       MISS_TAG             NUMBER(1),
                       BACKGRD_VALUE        NVARCHAR2(100),
                       BCKVAL_MEASURE_UNIT  NVARCHAR2(50),
                       TEST_CASE            NVARCHAR2(500),
                       ITEM_REL_ID          INTEGER,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_RAD_CRPT_PRO_ITEM primary key (RID),
                       constraint FK_TD_RAD_CRPT_PRO_ITEM2 foreign key (ITEM_ID)
                        references TB_TJ_RAD_ITEM (RID),
                       constraint FK_TD_RAD_CRPT_PRO_ITEM3 foreign key (ITEM_REL_ID)
                        references TD_TJ_RAD_ITEM_REL (RID),
                       constraint FK_TD_RAD_CRPT_PRO_ITEM1 foreign key (MAIN_ID)
                        references TD_RAD_CRPT_CHECK_SUB (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>281</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_CRPT_PRO_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_CRPT_PRO_ITEM_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>282</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_CRPT_RESULT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_RAD_CRPT_RESULT
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       PLACE                NVARCHAR2(50),
                       CHECK_POINT          NVARCHAR2(50),
                       MEASURE_VAL          NVARCHAR2(100),
                       MEASURE_UNIT         NVARCHAR2(50),
                       STD_ID               INTEGER,
                       HG_TAG               NUMBER(1),
                       IF_REMOVE_BACKGRD_VALUE NUMBER(1),
                       RECHECK              NUMBER(1),
                       MISS_TAG             NUMBER(1),
                       RMK                  NVARCHAR2(100),
                       XH                   NUMBER(4),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_RAD_CRPT_RESULT primary key (RID),
                       constraint FK_TD_RAD_CRPT_RESULT1 foreign key (MAIN_ID)
                        references TD_RAD_CRPT_PRO_ITEM (RID),
                       constraint FK_TD_RAD_CRPT_RESULT2 foreign key (STD_ID)
                        references TD_TJ_RAD_ITEM_STD (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>283</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_CRPT_RESULT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_RAD_CRPT_RESULT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>284</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('REGISTER_NO');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD REGISTER_NO NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>285</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('REGISTER_ADDR');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD REGISTER_ADDR NVARCHAR2(200)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>286</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('INDUS_TYPE_ID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD INDUS_TYPE_ID INTEGER';
        END IF;
      END;
      ]]>
        </sql>
        <ver>287</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('ORG_FZ');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD ORG_FZ NVARCHAR2(20)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>288</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('LINK_PSN');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD LINK_PSN NVARCHAR2(20)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>289</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('LINK_PHONE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD LINK_PHONE NVARCHAR2(20)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>290</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('REGISTER_TYPE_ID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD REGISTER_TYPE_ID INTEGER';
        END IF;
      END;
      ]]>
        </sql>
        <ver>291</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('CRPT_SIZE_ID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CRPT_SIZE_ID INTEGER';
        END IF;
      END;
      ]]>
        </sql>
        <ver>292</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('DISPATCH_WORK_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD DISPATCH_WORK_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>293</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('MINE_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD MINE_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>294</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_CRPT_LEAD_TRAIN');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_CRPT_LEAD_TRAIN NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>295</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_HETH_MANAGER_TRAIN');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_HETH_MANAGER_TRAIN NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>296</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('JC_UNIT_NAME');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD JC_UNIT_NAME NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>297</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('JC_REPORT_NO');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD JC_REPORT_NO NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>298</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('PJ_UNIT_NAME');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD PJ_UNIT_NAME NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>299</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('PJ_REPORT_NO');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD PJ_REPORT_NO NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>300</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('TJ_UNIT_NAME');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD TJ_UNIT_NAME NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>301</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('KEEP_FS_WORK_PSN_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD KEEP_FS_WORK_PSN_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>302</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('LIMIT_FS_WORK_PSN_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD LIMIT_FS_WORK_PSN_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>303</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('AWAY_FS_WORK_PSN_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD AWAY_FS_WORK_PSN_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>304</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('CANNOT_FS_WORK_PSN_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CANNOT_FS_WORK_PSN_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>305</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('DOSE_MONIT_UNIT_NAME');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD DOSE_MONIT_UNIT_NAME NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>306</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('GT_5MSV');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD GT_5MSV NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>307</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('GT_20MSV_BHK_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD GT_20MSV_BHK_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>308</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_XY_DOSE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_XY_DOSE NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>309</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('XY_DOSE_TYPE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD XY_DOSE_TYPE NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>310</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('XY_DOSE_FACT');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD XY_DOSE_FACT NVARCHAR2(200)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>311</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_MIDDLE_DOSE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_MIDDLE_DOSE NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>312</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('MIDDLE_DOSE_TYPE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD MIDDLE_DOSE_TYPE NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>313</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('MIDDLE_DOSE_FACT');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD MIDDLE_DOSE_FACT NVARCHAR2(200)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>314</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_SURFACE_DOSE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_SURFACE_DOSE NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>315</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('SURFACE_DOSE_TYPE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD SURFACE_DOSE_TYPE NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>316</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('SURFACE_DOSE_FACT');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD SURFACE_DOSE_FACT NVARCHAR2(200)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>317</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_ALARM_INST');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_ALARM_INST NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>318</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('ALARM_INST_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD ALARM_INST_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>319</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('ALARM_INST_TYPE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD ALARM_INST_TYPE NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>320</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('ALARM_INST_FACT');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD ALARM_INST_FACT NVARCHAR2(200)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>321</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('IF_RADON_INST');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_RADON_INST NUMBER(1)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>322</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('RADON_INST_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD RADON_INST_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>323</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('RADON_INST_TYPE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD RADON_INST_TYPE NVARCHAR2(100)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>324</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('RADON_INST_FACT');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD RADON_INST_FACT NVARCHAR2(200)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>325</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('CHECK_PSN');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CHECK_PSN NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>326</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD7'
                AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONSTRAINT FK_TD_TJ_CRPT_HETH_RCD7 FOREIGN KEY (INDUS_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>327</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD8'
                AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONSTRAINT FK_TD_TJ_CRPT_HETH_RCD8 FOREIGN KEY (REGISTER_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>328</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD9'
                AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONSTRAINT FK_TD_TJ_CRPT_HETH_RCD9 FOREIGN KEY (CRPT_SIZE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>329</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_UNSEALED_SOURCE')
        AND COLUMN_NAME = UPPER('RADIA_SOURCE_ID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_UNSEALED_SOURCE ADD RADIA_SOURCE_ID INTEGER';
        END IF;
      END;
      ]]>
        </sql>
        <ver>330</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_UNSEALED_SOURCE4'
                AND TABLE_NAME = 'TD_TJ_CRPT_UNSEALED_SOURCE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_UNSEALED_SOURCE ADD CONSTRAINT FK_TD_TJ_CRPT_UNSEALED_SOURCE4 FOREIGN KEY (RADIA_SOURCE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>331</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
        AND COLUMN_NAME = UPPER('LINK_MAN');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD LINK_MAN NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>332</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
        AND COLUMN_NAME = UPPER('LINK_TEL');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD LINK_TEL NVARCHAR2(30)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>333</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INVEST_ITEM')
        AND COLUMN_NAME = UPPER('TYPE_NAME');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INVEST_ITEM ADD TYPE_NAME NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>334</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
        AND COLUMN_NAME = UPPER('XRAY_IMAGE_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD XRAY_IMAGE_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>335</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
        AND COLUMN_NAME = UPPER('XRAY_THROUGH_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD XRAY_THROUGH_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>336</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
        AND COLUMN_NAME = UPPER('XRAY_BREAST_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD XRAY_BREAST_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>337</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
        AND COLUMN_NAME = UPPER('XRAY_DENTISTRY_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD XRAY_DENTISTRY_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>338</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM INT;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
        AND COLUMN_NAME = UPPER('XRAY_BONE_NUM');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD XRAY_BONE_NUM NUMBER(8)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>339</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_DEVELOP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RAD_INVEST_DEVELOP
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER              not null,
                       DEVELOP_ID           INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RAD_INVEST_DEVELOP primary key (RID),
                       constraint FK_TD_TJ_RAD_INVEST_DEVELOP1 foreign key (MAIN_ID)
                         references TD_TJ_RAD_INVEST_INFO (RID),
                       constraint FK_TD_TJ_RAD_INVEST_DEVELOP2 foreign key (DEVELOP_ID)
                         references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>340</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_DEVELOP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_DEVELOP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>341</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RADHETH_EQU_JC';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RADHETH_EQU_JC
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       EVAL_DATE            DATE,
                       ORG_NAME             NVARCHAR2(100),
                       RPT_NO               NVARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RADHETH_EQU_JC primary key (RID),
                       constraint FK_TD_TJ_RADHETH_EQU_JC1 foreign key (MAIN_ID)
                        references TB_TJ_RADHETH (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>342</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RADHETH_EQU_JC_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RADHETH_EQU_JC_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>343</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RADHETH_EQU_JC_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RADHETH_EQU_JC_REL
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       EQU_ID               INTEGER,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RADHETH_EQU_JC_REL primary key (RID),
                       constraint FK_TD_TJ_RADHETH_EQU_JC_REL1 foreign key (MAIN_ID)
                        references TD_TJ_RADHETH_EQU_JC (RID),
                       constraint FK_TD_TJ_RADHETH_EQU_JC_REL2 foreign key (EQU_ID)
                        references TB_TJ_RAD_INST (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>344</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RADHETH_EQU_JC_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RADHETH_EQU_JC_REL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>345</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RADHETH_TRAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RADHETH_TRAIN
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       TRAIN_DATE           DATE,
                       TRAIN_TYPE_ID        INTEGER,
                       TRAIN_ORG_NAME       NVARCHAR2(100),
                       TRAIN_EXPS           NVARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RADHETH_TRAIN primary key (RID),
                       constraint FK_TD_TJ_RADHETH_TRAIN1 foreign key (MAIN_ID)
                        references TB_TJ_RADHETH (RID),
                       constraint FK_TD_TJ_RADHETH_TRAIN2 foreign key (TRAIN_TYPE_ID)
                        references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>346</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RADHETH_TRAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RADHETH_TRAIN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>347</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RADHETH_PSN_CHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RADHETH_PSN_CHK
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       RPT_DATE             DATE,
                       RPT_CODE             NVARCHAR2(50),
                       ORG_NAME             NVARCHAR2(100),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RADHETH_PSN_CHK primary key (RID),
                       constraint FK_TD_TJ_RADHETH_PSN_CHK1 foreign key (MAIN_ID)
                         references TB_TJ_RADHETH (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>348</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RADHETH_PSN_CHK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RADHETH_PSN_CHK_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>349</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RADHETH_PSN_JC_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RADHETH_PSN_JC_REL
                    (
                       RID                  INTEGER              not null,
                       BUS_TYPE             NUMBER(1),
                       BUS_ID               INTEGER,
                       PSN_ID               INTEGER,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RADHETH_PSN_JC_REL primary key (RID),
                       constraint FK_TD_TJ_RADHETH_PSN_JC_REL1 foreign key (PSN_ID)
                         references TD_TJ_RADHETH_PSN (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>350</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RADHETH_PSN_JC_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RADHETH_PSN_JC_REL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>351</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RADHETH_HETH_CHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_RADHETH_HETH_CHK
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       RPT_DATE             DATE,
                       CHK_ORG_NAME         NVARCHAR2(100),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_RADHETH_HETH_CHK primary key (RID),
                       constraint FK_TD_TJ_RADHETH_HETH_CHK1 foreign key (MAIN_ID)
                         references TB_TJ_RADHETH (RID)
                    )';
                  END IF;
            END;
          ]]>
        </sql>
        <ver>352</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RADHETH_HETH_CHK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RADHETH_HETH_CHK_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>353</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
        AND COLUMN_NAME = UPPER('CARD_TYPE_ID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD CARD_TYPE_ID INTEGER';
        END IF;
      END;
      ]]>
        </sql>
        <ver>354</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RADHETH_PSN7'
                AND TABLE_NAME = 'TD_TJ_RADHETH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN   ADD CONSTRAINT FK_TD_TJ_RADHETH_PSN7 FOREIGN KEY (CARD_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>355</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                    AND COLUMN_NAME = UPPER('IDC_CARD')
                    AND DATA_TYPE = UPPER('VARCHAR2')
                    AND DATA_LENGTH = 20;
                  IF NUM = 1 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN MODIFY IDC_CARD VARCHAR2(60)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>356</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
                    AND COLUMN_NAME = UPPER('CENT_NO')
                    AND DATA_TYPE = UPPER('VARCHAR2')
                    AND DATA_LENGTH = 50;
                  IF NUM = 1 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN MODIFY CENT_NO VARCHAR2(80)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>357</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
        AND COLUMN_NAME = UPPER('UUID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN ADD UUID NVARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>358</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
        AND COLUMN_NAME = UPPER('SYNC_TRAIN_STATE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD SYNC_TRAIN_STATE NUMBER(1) DEFAULT 0 ';
        END IF;
      END;
      ]]>
        </sql>
        <ver>359</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
        AND COLUMN_NAME = UPPER('SYNC_TRAIN_DATE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD SYNC_TRAIN_DATE DATE ';
        END IF;
      END;
      ]]>
        </sql>
        <ver>360</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
        AND COLUMN_NAME = UPPER('SYNC_TRAIN_ERR_MSG');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD SYNC_TRAIN_ERR_MSG NVARCHAR2(1000) ';
        END IF;
      END;
      ]]>
        </sql>
        <ver>361</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
        AND COLUMN_NAME = UPPER('ADD_UP_TIME');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN ADD ADD_UP_TIME NUMBER(6) ';
        END IF;
      END;
      ]]>
        </sql>
        <ver>362</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
        AND COLUMN_NAME = UPPER('SCORE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN ADD SCORE NUMBER(4,1) ';
        END IF;
      END;
      ]]>
        </sql>
        <ver>363</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
        AND COLUMN_NAME = UPPER('DATA_SOURCE');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN ADD DATA_SOURCE NUMBER(1) ';
        END IF;
      END;
      ]]>
        </sql>
        <ver>364</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
        AND COLUMN_NAME = UPPER('UUID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD UUID VARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>365</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
        AND COLUMN_NAME = UPPER('UPDATETAG');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD UPDATETAG NUMBER(1) default 0 not null';
        END IF;
      END;
      ]]>
        </sql>
        <ver>366</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
        AND COLUMN_NAME = UPPER('ERROR_MSG');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD ERROR_MSG VARCHAR2(2000)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>367</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('UUID');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD UUID VARCHAR2(50)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>368</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('UPDATETAG');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD UPDATETAG NUMBER(1) default 0 not null';
        END IF;
      END;
      ]]>
        </sql>
        <ver>369</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
        NUM NUMBER;
      BEGIN
        SELECT COUNT(1)
        INTO NUM
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
        AND COLUMN_NAME = UPPER('ERROR_MSG');
        IF NUM = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD ERROR_MSG VARCHAR2(2000)';
        END IF;
      END;
      ]]>
        </sql>
        <ver>370</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                  AND COLUMN_NAME = UPPER('UUID');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD UUID VARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>371</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                  AND COLUMN_NAME = UPPER('UPDATETAG');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD UPDATETAG NUMBER(1) default 0 not null';
                END IF;
            END;
            ]]>
        </sql>
        <ver>372</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                  AND COLUMN_NAME = UPPER('ERROR_MSG');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD ERROR_MSG VARCHAR2(2000)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>373</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                  AND COLUMN_NAME = UPPER('CODION_CONT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD CODION_CONT NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>374</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_RESULT')
                  AND COLUMN_NAME = UPPER('ITEM_STDVALUE');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_RESULT ADD ITEM_STDVALUE NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>375</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TB_TJ_RAD_CHECK_MAIN')
                  AND COLUMN_NAME = UPPER('DATA_SOURCE');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_CHECK_MAIN ADD DATA_SOURCE NUMBER(1) DEFAULT 0';
                END IF;
            END;
            ]]>
        </sql>
        <ver>376</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_CRPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TB_TJ_RAD_CRPT
                    (
                       RID                  INTEGER              not null,
                       ZONE_ID              INTEGER,
                       UNIT_NAME            NVARCHAR2(100)       not null,
                       INSTITUTION_CODE     NVARCHAR2(50)        not null,
                       IF_SUB_ORG           NUMBER(1),
                       ADDRESS              NVARCHAR2(200),
                       REGISTER_ADDR        NVARCHAR2(200),
                       ORG_FZ               NVARCHAR2(20),
                       LINK_PSN             NVARCHAR2(20),
                       LINK_PHONE           NVARCHAR2(20),
                       ON_DUTY_PSN_NUM      NUMBER(8),
                       DISPATCH_WORK_NUM    NUMBER(8),
                       FS_WORK_PSN_NUM      NUMBER(8),
                       INDUS_TYPE_ID        INTEGER,
                       REGISTER_TYPE_ID     INTEGER,
                       CRPT_SIZE_ID         INTEGER,
                       DC_TYPE_ID           INTEGER,
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_MANID         INTEGER,
                       MODIFY_DATE          TIMESTAMP,
                       constraint PK_TB_TJ_RAD_CRPT primary key (RID),
                       constraint FK_TB_TJ_RAD_CRPT1 foreign key (ZONE_ID) references TS_ZONE (RID),
                       constraint FK_TB_TJ_RAD_CRPT2 foreign key (INDUS_TYPE_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TB_TJ_RAD_CRPT3 foreign key (REGISTER_TYPE_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TB_TJ_RAD_CRPT4 foreign key (CRPT_SIZE_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TB_TJ_RAD_CRPT5 foreign key (DC_TYPE_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
            END;
          ]]>
        </sql>
        <ver>377</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_CRPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_CRPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>378</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                  AND COLUMN_NAME = UPPER('EMERG_PSNS');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD EMERG_PSNS INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>379</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_RAD_DEVICE_NEW';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_TJ_CRPT_RAD_DEVICE_NEW
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       RAD_DEVICE_ID        INTEGER,
                       RAD_SET_NUM          NUMBER(8),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_CRPT_RAD_DEVICE_NEW primary key (RID),
                       constraint FK_TD_TJ_CRPT_RAD_DEVICE_NEW1 foreign key (MAIN_ID)
                            references TD_TJ_CRPT_HETH_RCD (RID),
                       constraint FK_TD_TJ_CRPT_RAD_DEVICE_NEW2 foreign key (RAD_DEVICE_ID)
                            references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
            END;
          ]]>
        </sql>
        <ver>380</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_DEVICE_NEW_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_DEVICE_NEW_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>381</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_CRPT_SOURCE_NEW';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                   create table TD_TJ_CRPT_SOURCE_NEW
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       SOURCE_DEVICE_ID     INTEGER,
                       SOURCE_NUM           NUMBER(8),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_TJ_CRPT_SOURCE_NEW primary key (RID),
                       constraint FK_TD_TJ_CRPT_SOURCE_NEW1 foreign key (MAIN_ID)
                        references TD_TJ_CRPT_HETH_RCD (RID),
                       constraint FK_TD_TJ_CRPT_SOURCE_NEW2 foreign key (SOURCE_DEVICE_ID)
                        references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
            END;
          ]]>
        </sql>
        <ver>382</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_CRPT_SOURCE_NEW_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_CRPT_SOURCE_NEW_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>383</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('ORG_FZ')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 20;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY ORG_FZ NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>384</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TJ_RAD_CRPT_JC_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                   create table TB_TJ_RAD_CRPT_JC_TYPE
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       JC_TYPE_ID           INTEGER,
                       OTHER_DESC           VARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TB_TJ_RAD_CRPT_JC_TYPE primary key (RID),
                       constraint FK_TB_TJ_RAD_CRPT_JC_TYPE1 foreign key (MAIN_ID) references TB_TJ_RAD_CRPT (RID),
                       constraint FK_TB_TJ_RAD_CRPT_JC_TYPE2 foreign key (JC_TYPE_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
            END;
          ]]>
        </sql>
        <ver>385</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TJ_RAD_CRPT_JC_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TJ_RAD_CRPT_JC_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>386</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_CRPT')
                AND COLUMN_NAME = UPPER('ORG_FZ')
                AND DATA_TYPE = UPPER('NVARCHAR2')
                AND CHAR_LENGTH = 20;
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_CRPT MODIFY ORG_FZ NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>387</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                    AND COLUMN_NAME = UPPER('CRPT_TRAIN_UNIT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CRPT_TRAIN_UNIT NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>388</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                    AND COLUMN_NAME = UPPER('HETH_TRAIN_UNIT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD HETH_TRAIN_UNIT NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>389</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                    AND COLUMN_NAME = UPPER('RAD_TRAIN_UNIT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD RAD_TRAIN_UNIT NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>390</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('SMT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD SMT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>391</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('PRE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD PRE_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>392</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('LAST_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD LAST_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>393</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_FLOW';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_TJ_RAD_INVEST_FLOW (RID                  INTEGER              not null,INVEST_TYPE          NUMBER(2),BUS_ID               INTEGER,OPER_FLAG            NUMBER(2),SMT_DATE             DATE,SMT_PSN_ID           INTEGER,BACK_RSN             NVARCHAR2(200),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_TJ_RAD_INVEST_FLOW primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>394</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_TJ_RAD_INVEST_FLOW_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_TJ_RAD_INVEST_FLOW_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>395</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('SMT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD SMT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>396</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('PRE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD PRE_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>397</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('LAST_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD LAST_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>398</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_RAD_DC_HOSPITAL_BASE')
                AND COLUMN_NAME = UPPER('SMT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_RAD_DC_HOSPITAL_BASE ADD SMT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>399</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('ENTER_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD ENTER_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>400</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INVEST_ITEM')
                AND COLUMN_NAME = UPPER('INFO_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INVEST_ITEM ADD INFO_TAG NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>401</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('DEL_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD DEL_MARK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>402</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('DEL_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD DEL_RSN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>403</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('DEL_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD DEL_MARK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>404</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('DEL_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD DEL_RSN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>405</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RADHETH_PSN')
                AND COLUMN_NAME = UPPER('SOURCE_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RADHETH_PSN ADD SOURCE_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>406</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RADHETH_PSN8'
                AND TABLE_NAME = 'TD_TJ_RADHETH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RADHETH_PSN add constraint FK_TD_TJ_RADHETH_PSN8 foreign key (SOURCE_UNIT_ID) references TB_TJ_RADHETH (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>407</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('DEL_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD DEL_MARK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>408</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('DEL_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD DEL_RSN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>409</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('SOURCE_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD SOURCE_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>410</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_TJ_RAD_INST5'
                AND TABLE_NAME = 'TB_TJ_RAD_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TB_TJ_RAD_INST  add constraint FK_TB_TJ_RAD_INST5 foreign key (SOURCE_UNIT_ID) references TB_TJ_RADHETH (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>411</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('SOURCE_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_CHECK_MAIN ADD SOURCE_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>412</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_TJ_RAD_CHECK_MAIN5'
                AND TABLE_NAME = 'TB_TJ_RAD_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TB_TJ_RAD_CHECK_MAIN  add constraint FK_TB_TJ_RAD_CHECK_MAIN5 foreign key (SOURCE_UNIT_ID) references TB_TJ_RADHETH (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>413</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('SOURCE_INST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD SOURCE_INST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>414</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_CHECK_SUB4'
                AND TABLE_NAME = 'TD_TJ_RAD_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_CHECK_SUB  add constraint FK_TD_TJ_RAD_CHECK_SUB4 foreign key (SOURCE_INST_ID) references TB_TJ_RAD_INST (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>415</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>416</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_CHECK_SUB5'
                AND TABLE_NAME = 'TD_TJ_RAD_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_CHECK_SUB  add constraint FK_TD_TJ_RAD_CHECK_SUB5 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>417</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('SOURCE_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD SOURCE_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>418</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INVEST_INFO9'
                AND TABLE_NAME = 'TD_TJ_RAD_INVEST_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_INVEST_INFO add constraint FK_TD_TJ_RAD_INVEST_INFO9 foreign key (SOURCE_UNIT_ID) references TB_TJ_RADHETH (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>419</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_FREQ_RCD')
                AND COLUMN_NAME = UPPER('SOURCE_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_FREQ_RCD ADD SOURCE_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>420</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_FREQ_RCD5'
                AND TABLE_NAME = 'TD_TJ_RAD_FREQ_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_FREQ_RCD add constraint FK_TD_TJ_RAD_FREQ_RCD5 foreign key (SOURCE_UNIT_ID) references TB_TJ_RADHETH (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>421</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_HETH_CHK')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_HETH_CHK ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>422</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_PSN_HETH_CHK4'
                AND TABLE_NAME = 'TD_TJ_RAD_PSN_HETH_CHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_PSN_HETH_CHK add constraint FK_TD_TJ_RAD_PSN_HETH_CHK4 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>423</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_MSMENT')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_MSMENT ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>424</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_PSN_MSMENT4'
                AND TABLE_NAME = 'TD_TJ_RAD_PSN_MSMENT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_PSN_MSMENT add constraint FK_TD_TJ_RAD_PSN_MSMENT4 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>425</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_CHECK_SUB')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_CHECK_SUB ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>426</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_CHECK_SUB5'
                AND TABLE_NAME = 'TD_TJ_RAD_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_CHECK_SUB add constraint FK_TD_TJ_RAD_CHECK_SUB5 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>427</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_PSN_TRAIN')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_PSN_TRAIN ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>428</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_PSN_TRAIN2'
                AND TABLE_NAME = 'TD_TJ_RAD_PSN_TRAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_PSN_TRAIN add constraint FK_TD_TJ_RAD_PSN_TRAIN2 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>429</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>430</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWPX_REGIST_GRPSUB6'
                AND TABLE_NAME = 'TD_ZWPX_REGIST_GRPSUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWPX_REGIST_GRPSUB add constraint FK_TD_ZWPX_REGIST_GRPSUB6 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>431</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REG_SEL_TIMES')
                AND COLUMN_NAME = UPPER('SOURCE_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REG_SEL_TIMES ADD SOURCE_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>432</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWPX_REG_SEL_TIMES4'
                AND TABLE_NAME = 'TD_ZWPX_REG_SEL_TIMES';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWPX_REG_SEL_TIMES add constraint FK_TD_ZWPX_REG_SEL_TIMES4 foreign key (SOURCE_PSN_ID) references TD_TJ_RADHETH_PSN (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>433</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('FINAL_BACK_RSN');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD MODIFY FINAL_BACK_RSN VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>434</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_TJ_CRPT_RAD_ANNEX';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_RAD_ANNEX
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ANNEX_NAME           NVARCHAR2(50),
               ANNEX_PATH           NVARCHAR2(100),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_CRPT_RAD_ANNEX primary key (RID),
               constraint FK_TD_TJ_CRPT_RAD_ANNEX1 foreign key (MAIN_ID)
                  references TD_TJ_CRPT_HETH_RCD (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>435</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_TJ_CRPT_RAD_ANNEX';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>436</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                AND COLUMN_NAME = UPPER('IF_REMIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_REMIT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>437</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_TJ_CRPT_RAD_TYPE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_TJ_CRPT_RAD_TYPE (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              TYPE_ID INTEGER,
              SOURCE_NUM NUMBER(8),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_TJ_CRPT_RAD_TYPE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_TJ_CRPT_RAD_TYPE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_TJ_CRPT_HETH_RCD (RID),
              CONSTRAINT FK_TD_TJ_CRPT_RAD_TYPE2 FOREIGN KEY (TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
            ]]>
        </sql>
        <ver>438</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_TJ_CRPT_RAD_TYPE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
            ]]>
        </sql>
        <ver>439</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('SERIOUS_HAZARDS');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD SERIOUS_HAZARDS NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>440</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('GENERAL_HAZARDS');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD GENERAL_HAZARDS NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>441</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('IF_HAVE_OCC');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD IF_HAVE_OCC NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>442</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('PRE_EVAL');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD PRE_EVAL NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>443</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('CONTROL_EVAL');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONTROL_EVAL NUMBER(1)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>444</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('INVEST_UNIT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD INVEST_UNIT NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>445</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('INVEST_TYPE_ID');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD INVEST_TYPE_ID INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>446</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('INVEST_GRADE_ID');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD INVEST_GRADE_ID INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>447</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD10'
                  AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TD_TJ_CRPT_HETH_RCD add constraint FK_TD_TJ_CRPT_HETH_RCD10 foreign key (INVEST_TYPE_ID) references TS_SIMPLE_CODE (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>448</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TD_TJ_CRPT_HETH_RCD11'
                  AND TABLE_NAME = 'TD_TJ_CRPT_HETH_RCD';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TD_TJ_CRPT_HETH_RCD add constraint FK_TD_TJ_CRPT_HETH_RCD11 foreign key (INVEST_GRADE_ID) references TS_SIMPLE_CODE (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>449</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                  AND COLUMN_NAME = UPPER('UNIT_TYPE_ID');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD UNIT_TYPE_ID INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>450</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TB_TJ_RADHETH7'
                  AND TABLE_NAME = 'TB_TJ_RADHETH';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TB_TJ_RADHETH add constraint FK_TB_TJ_RADHETH7 foreign key (UNIT_TYPE_ID) references TS_SIMPLE_CODE (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>451</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_TJ_CRPT_RAD_PJ_ORG';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_RAD_PJ_ORG
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ORG_ID               INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_TJ_CRPT_RAD_PJ_ORG primary key (RID) ,
             constraint FK_TD_TJ_CRPT_RAD_PJ_ORG1 foreign key (MAIN_ID)
                  references TD_TJ_CRPT_HETH_RCD (RID),
           constraint FK_TD_TJ_CRPT_RAD_PJ_ORG2 foreign key (ORG_ID)
                  references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
            ]]>
        </sql>
        <ver>452</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_TJ_CRPT_RAD_PJ_ORG';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
            ]]>
        </sql>
        <ver>453</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_TJ_CRPT_RAD_TJ_ORG';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_RAD_TJ_ORG
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           ORG_ID               INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_TJ_CRPT_RAD_TJ_ORG primary key (RID),
       constraint FK_TD_TJ_CRPT_RAD_TJ_ORG1 foreign key (MAIN_ID)
              references TD_TJ_CRPT_HETH_RCD (RID),
        constraint FK_TD_TJ_CRPT_RAD_TJ_ORG2 foreign key (ORG_ID)
              references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
            ]]>
        </sql>
        <ver>454</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_TJ_CRPT_RAD_TJ_ORG';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
            ]]>
        </sql>
        <ver>455</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_TJ_CRPT_RAD_JC_ORG';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_TJ_CRPT_RAD_JC_ORG
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           ORG_ID               INTEGER,
           CREATE_DATE          TIMESTAMP            not null,
           CREATE_MANID         INTEGER              not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_TJ_CRPT_RAD_JC_ORG primary key (RID),
        constraint FK_TD_TJ_CRPT_RAD_JC_ORG1 foreign key (MAIN_ID)
              references TD_TJ_CRPT_HETH_RCD (RID),
     constraint FK_TD_TJ_CRPT_RAD_JC_ORG2 foreign key (ORG_ID)
              references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
            ]]>
        </sql>
        <ver>456</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_TJ_CRPT_RAD_JC_ORG';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
            ]]>
        </sql>
        <ver>457</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                  AND COLUMN_NAME = UPPER('UNIT_TYPE_ID');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD UNIT_TYPE_ID INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>458</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INVEST_INFO10'
                  AND TABLE_NAME = 'TD_TJ_RAD_INVEST_INFO';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TD_TJ_RAD_INVEST_INFO add constraint FK_TD_TJ_RAD_INVEST_INFO10 foreign key (UNIT_TYPE_ID) references TS_SIMPLE_CODE (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>459</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('PRE_UNIT_NAME');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD PRE_UNIT_NAME NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>460</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('PRE_REPORT_NO');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD PRE_REPORT_NO NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>461</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('CONTROL_UNIT_NAME');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONTROL_UNIT_NAME NVARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>462</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_CRPT_HETH_RCD')
                  AND COLUMN_NAME = UPPER('CONTROL_REPORT_NO');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_CRPT_HETH_RCD ADD CONTROL_REPORT_NO NVARCHAR2(50)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>463</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_RST_SUB')
                  AND COLUMN_NAME = UPPER('CHINA_MADE_NUM');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_RST_SUB ADD CHINA_MADE_NUM INTEGER';
                END IF;
            END;
            ]]>
        </sql>
        <ver>464</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM INT;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TAB_COLUMNS
                WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INVEST_ITEM_DICT')
                  AND COLUMN_NAME = UPPER('SEL_SHOW_OTHER_IDENT');
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INVEST_ITEM_DICT ADD SEL_SHOW_OTHER_IDENT VARCHAR2(100)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>465</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('ORG_QUALITY_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD ORG_QUALITY_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>466</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TB_TJ_RADHETH'
                AND CONSTRAINT_NAME = 'FK_TB_TJ_RADHETH8';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD constraint FK_TB_TJ_RADHETH8 foreign key (ORG_QUALITY_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>467</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RADHETH')
                AND COLUMN_NAME = UPPER('IF_DENTISTRY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RADHETH ADD IF_DENTISTRY INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>468</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INST')
                AND COLUMN_NAME = UPPER('IF_DOMESTIC');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INST ADD IF_DOMESTIC INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>469</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_TJ_RAD_INVEST_ITEM')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TJ_RAD_INVEST_ITEM ADD RMK NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>470</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_TJ_RAD_INVEST_INFO')
                AND COLUMN_NAME = UPPER('ORG_QUALITY_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD ORG_QUALITY_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>471</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_TJ_RAD_INVEST_INFO'
                AND CONSTRAINT_NAME = 'FK_TD_TJ_RAD_INVEST_INFO11';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_TJ_RAD_INVEST_INFO ADD constraint FK_TD_TJ_RAD_INVEST_INFO11 foreign key (ORG_QUALITY_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>472</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-comm-fs 放射卫生通用模块升级 -->