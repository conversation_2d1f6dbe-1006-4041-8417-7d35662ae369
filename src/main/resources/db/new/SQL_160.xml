<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职卫卫生-查询统计</description>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_ANALY_TYPE (   RID                  INTEGER              not null,   ANALY_TYPE           NUMBER(1),   RMK                  VARCHAR2(100),   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZDZYB_ANALY_TYPE primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZDZYB_ANALY_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZDZYB_ANALY_DETAIL (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   XH                   NUMBER(5),   ANALY_ITEM_ID        INTEGER,   GE                   NUMBER(3),   GT                   NUMBER(3),   LE                   NUMBER(3),   LT                   NUMBER(3),   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZDZYB_ANALY_DETAIL primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZDZYB_ANALY_DETAIL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZDZYB_ANALY_DETAIL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZDZYB_ANALY_TYPE')
                AND COLUMN_NAME = UPPER('BUS_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZDZYB_ANALY_TYPE ADD BUS_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_BP_CRPT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_BP_CRPT_INFO (RID                  INTEGER              not null,CRPT_NUM             NUMBER(6),TCH_RSN_NUM          NUMBER(8),YSB_NUM              NUMBER(6),ZYB_NUM              NUMBER(6),SUPPORT_NUM          NUMBER(5),SUPPORT_PRO_NUM      NUMBER(5),SUPPORT_PRO_ORG_NAME NVARCHAR2(50),SUPPORT_CITY_NUM     NUMBER(5),SUPPORT_DIS_NUM      NUMBER(5),TJ_ORG_NUM           NUMBER(5),ZD_ORG_NUM           NUMBER(5),JD_ORG_NUM           NUMBER(5),FSWS_ORG_NUM         NUMBER(5),ZYWS_ORG_NUM         NUMBER(5),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_BP_CRPT_INFO primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_BP_CRPT_ITEM_DIS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_BP_CRPT_ITEM_DIS (RID                  INTEGER              not null,ANALY_TYPE           NUMBER(1),CODE_NAME            NVARCHAR2(50),XH                   NUMBER(4),ANALY_NUM            NUMBER(6),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_BP_CRPT_ITEM_DIS primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_BP_ZONE_INDEX_DIS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_BP_ZONE_INDEX_DIS (RID                  INTEGER              not null,ZONE_NAME            NVARCHAR2(20),XH                   NUMBER(4),CEN_COORD_XAXIS      NVARCHAR2(20),CEN_COORD_YAXIS      NVARCHAR2(20),TIP_COORD_XAXIS      NVARCHAR2(20),TIP_COORD_YAXIS      NVARCHAR2(20),CRPT_NUM             NUMBER(6),TCH_RSN_NUM          NUMBER(8),YS_NUM               NUMBER(6),ZYB_NUM              NUMBER(6),TJ_ORG_NUM           NUMBER(5),ZD_ORG_NUM           NUMBER(5),JD_ORG_NUM           NUMBER(5),FSWS_ORG_NUM         NUMBER(5),ZYWS_ORG_NUM         NUMBER(5),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_BP_ZONE_INDEX_DIS primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_BP_FBL_TREND_ANALY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_BP_FBL_TREND_ANALY (RID                  INTEGER              not null,ANALY_TYPE           NUMBER(1),ANALY_YEAR           NUMBER(4),CASES                NUMBER(6),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_BP_FBL_TREND_ANALY primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWYJ_ITEM_GROUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWYJ_ITEM_GROUP ( RID                  INTEGER              not null, ITEM_ID              INTEGER, GROUP_ID             INTEGER, CREATE_DATE          TIMESTAMP            not null, CREATE_MANID         INTEGER              not null, MODIFY_DATE          TIMESTAMP, MODIFY_MANID         INTEGER, constraint PK_TD_ZWYJ_ITEM_GROUP primary key (RID), constraint FK_TD_ZWYJ_ITEM_GROUP1 foreign key (ITEM_ID) references TB_TJ_ITEMS (RID), constraint FK_TD_ZWYJ_ITEM_GROUP2 foreign key (GROUP_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWYJ_ITEM_GROUP_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWYJ_ITEM_GROUP_SUB ( RID                  INTEGER              not null, MAIN_ID              INTEGER, TYPE                 NUMBER(2), GROUP_VAL            NUMBER(10,2), GROUP_VAL_DESC       NVARCHAR2(50), STD_RATE             NUMBER(22,20), RISK_LEVEL_ID        INTEGER, GE                   NVARCHAR2(50), GT                   NVARCHAR2(50), LE                   NVARCHAR2(50), LT                   NVARCHAR2(50), XH                   NUMBER(3),CREATE_DATE          TIMESTAMP            not null, CREATE_MANID         INTEGER              not null, MODIFY_DATE          TIMESTAMP, MODIFY_MANID         INTEGER, constraint PK_TD_ZWYJ_ITEM_GROUP_SUB primary key (RID), constraint FK_TD_ZWYJ_ITEM_GROUP_SUB1 foreign key (MAIN_ID) references TD_ZWYJ_ITEM_GROUP (RID), constraint FK_TD_ZWYJ_ITEM_GROUP_SUB2 foreign key (RISK_LEVEL_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWYJ_ITEM_GROUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWYJ_ITEM_GROUP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWYJ_ITEM_GROUP_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWYJ_ITEM_GROUP_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_BP_ZONE_CRPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_BP_ZONE_CRPT
                (
                   RID                  INTEGER              not null,
                   ZONE_ID              INTEGER,
                   CRPT_NUM             NUMBER(6),
                   TCH_RSN_NUM          NUMBER(8),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_BP_ZONE_CRPT primary key (RID),
                   constraint FK_TD_BP_ZONE_CRPT1 foreign key (ZONE_ID)
                    references TS_ZONE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_BP_ZONE_CRPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_BP_ZONE_CRPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_BP_BHK_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_BP_BHK_ITEM
                (
                   RID                  INTEGER              not null,
                   ITEM_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_BP_BHK_ITEM primary key (RID),
                   constraint FK_TD_BP_BHK_ITEM1 foreign key (ITEM_ID)
                    references TB_TJ_ITEMS (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_BP_BHK_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_BP_BHK_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-analy 查询统计相关模块升级 -->