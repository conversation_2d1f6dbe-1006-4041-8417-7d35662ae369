<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>在线培训</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWPX_PLAN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWPX_PLAN (
                 RID                  INTEGER              not null,
                 TRAIN_TYPE_ID        INTEGER              not null,
                 TRAIN_CLS_NAME       VARCHAR2(100)        not null,
                 TRAIN_OBJS           VARCHAR2(100),
                 TRAIN_CONTENT        VARCHAR2(200),
                 OTHER_ITEMS          VARCHAR2(2000),
                 FILE_PATH            VARCHAR2(200),
                 PUBISH_UNIT_ID       INTEGER,
                 END_DATE             DATE,
                 STATE                NUMBER(1),
                 CREATE_DATE          TIMESTAMP            not null,
                 CREATE_MANID         INTEGER              not null,
                 MOD<PERSON>Y_<PERSON>ATE          TIMESTAMP,
                 M<PERSON><PERSON><PERSON>_MANID         INTEGER,
                 constraint PK_TD_ZWPX_PLAN primary key (RID),
                 constraint FK_TD_ZWPX_PLAN1 foreign key (PUBISH_UNIT_ID)
                 references TS_UNIT (RID),constraint FK_TD_ZWPX_PLAN2 foreign key (TRAIN_TYPE_ID)
                 references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWPX_PLAN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWPX_PLAN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('REG_PSNS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD REG_PSNS NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('FILE_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD FILE_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('TRAIN_CONTENT');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN MODIFY TRAIN_CONTENT VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('PUBLISH_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD PUBLISH_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('TRAIN_TYPE_CJ_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  TRAIN_TYPE_CJ_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('TRAIN_CLS_CJ_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  TRAIN_CLS_CJ_NAME VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('TRAIN_OBJS_CJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  TRAIN_OBJS_CJ VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('TRAIN_CONTENT_CJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  TRAIN_CONTENT_CJ VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('OTHER_ITEMS_CJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  OTHER_ITEMS_CJ VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('FILE_PATH_CJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  FILE_PATH_CJ VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('FILE_NAME_CJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD  FILE_NAME_CJ VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND nullable = UPPER('N')
                AND COLUMN_NAME = UPPER('TRAIN_TYPE_ID');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN MODIFY TRAIN_TYPE_ID INTEGER NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND nullable = UPPER('N')
                AND COLUMN_NAME = UPPER('TRAIN_CLS_NAME');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN MODIFY TRAIN_CLS_NAME VARCHAR2(100) NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWPX_REGIST_GROUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWPX_REGIST_GROUP (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER              not null,
                REG_UNIT_ID          INTEGER,
                CREATE_DATE          TIMESTAMP            not null,
                CREATE_MANID         INTEGER              not null,
                MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,
                constraint PK_TD_ZWPX_REGIST_GROUP primary key (RID),
                constraint FK_TD_ZWPX_REGIST_GROUP1 foreign key (MAIN_ID)
                references TD_ZWPX_PLAN (RID),constraint FK_TD_ZWPX_REGIST_GROUP2 foreign key (REG_UNIT_ID)
                references TS_UNIT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWPX_REGIST_GROUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWPX_REGIST_GROUP_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GROUP')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GROUP ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GROUP')
                AND COLUMN_NAME = UPPER('TRAIN_PSNS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GROUP ADD TRAIN_PSNS NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GROUP')
                AND COLUMN_NAME = UPPER('HAS_REG_PSNS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GROUP ADD HAS_REG_PSNS NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWPX_REGIST_GRPSUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWPX_REGIST_GRPSUB (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER              not null,
                TRAIN_TYPE           NUMBER(1)            not null,
                PSN_ID               INTEGER,
                STAY_ID              INTEGER,
                STATE                NUMBER(1),
                BACK_RSN             VARCHAR2(200),
                EXAM_SCORE           NUMERIC(5,2),
                IF_HG                NUMBER(1),
                HG_NO                VARCHAR2(100),
                VALID_BEG_DATE       DATE,
                VALID_END_DATE       DATE,
                CENT_FILE_PATH       VARCHAR2(200),
                CENT_FILE_NAME       VARCHAR2(200),
                TRAIN_TIMES_ID       INTEGER,
                CREATE_DATE          TIMESTAMP            not null,
                CREATE_MANID         INTEGER              not null,
                MODIFY_DATE          TIMESTAMP,
                MODIFY_MANID         INTEGER,
                constraint PK_TD_ZWPX_REGIST_GRPSUB primary key (RID),
                constraint FK_TD_ZWPX_REGIST_GRPSUB1 foreign key (MAIN_ID)
                references TD_ZWPX_REGIST_GROUP (RID),
                constraint FK_TD_ZWPX_REGIST_GRPSUB2 foreign key (TRAIN_TIMES_ID)
                references TD_ZWPX_TIMES (RID),constraint FK_TD_ZWPX_REGIST_GRPSUB3 foreign key (PSN_ID)
                references TD_ZW_PSNINFO (RID),constraint FK_TD_ZWPX_REGIST_GRPSUB4 foreign key (STAY_ID)
                references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWPX_REGIST_GRPSUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWPX_REGIST_GRPSUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('TRAIN_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB MODIFY TRAIN_TYPE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('AGEIN_SEND_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD AGEIN_SEND_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('CENT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD CENT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('IF_NEED_SC_CENT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD IF_NEED_SC_CENT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('RECEIVE_EMP_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD RECEIVE_EMP_NAME VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('RECEIVE_LINK_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD RECEIVE_LINK_ADDR VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('RECEIVE_MOBILE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD RECEIVE_MOBILE_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('RECEIVE_POSTCODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD RECEIVE_POSTCODE VARCHAR2(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_REGIST_GRPSUB')
                AND COLUMN_NAME = UPPER('RECEIVE_WAY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_REGIST_GRPSUB ADD RECEIVE_WAY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWPX_GP_ALY_MAJOR';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWPX_GP_ALY_MAJOR (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER              not null,
                APPLY_ID             INTEGER              not null,
                EXAM_SCORE           NUMERIC(5,2),
                IF_HG                NUMBER(1),
                CREATE_DATE          TIMESTAMP            not null,
                CREATE_MANID         INTEGER              not null,
                MODIFY_DATE          TIMESTAMP,
                MODIFY_MANID         INTEGER,
                constraint PK_TD_ZWPX_GP_ALY_MAJOR primary key (RID),
                constraint FK_TD_ZWPX_GP_ALY_MAJOR1 foreign key (MAIN_ID)
                references TD_ZWPX_REGIST_GRPSUB (RID),constraint FK_TD_ZWPX_GP_ALY_MAJOR2 foreign key (APPLY_ID)
                references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWPX_GP_ALY_MAJOR_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWPX_GP_ALY_MAJOR_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_TRAIN_FILE_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_ZW_TRAIN_FILE_MAIN
                (
                   RID                  INTEGER              not null,
                   TRAIN_TYPE_ID        INTEGER,
                   IF_COMM              NUMBER(1),
                   TRAIN_MAJOR_ID       INTEGER,
                   STATE                NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TB_ZW_TRAIN_FILE_MAIN primary key (RID),
                   constraint FK_TB_ZW_TRAIN_FILE_MAIN1 foreign key (TRAIN_TYPE_ID)
                   references TS_SIMPLE_CODE (RID),
                   constraint FK_TB_ZW_TRAIN_FILE_MAIN2 foreign key (TRAIN_MAJOR_ID)
                   references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_TRAIN_FILE_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_ZW_TRAIN_FILE_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_TRAIN_FILE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_ZW_TRAIN_FILE
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               FILE_NAME            VARCHAR(100),
               FILE_PATH            VARCHAR(100),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_ZW_TRAIN_FILE primary key (RID),
               constraint FK_TB_ZW_TRAIN_FILE1 foreign key (MAIN_ID)
               references TB_ZW_TRAIN_FILE_MAIN (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_TRAIN_FILE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_ZW_TRAIN_FILE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_EXAM_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_EXAM_PSN (   RID                  INTEGER              not null,BUS_TYPE             NUMBER(2),BUS_ID               INTEGER,UNIT_ID              INTEGER,ZONE_ID              INTEGER,UNIT_NAME            NVARCHAR2(200),PRO_PSN_ID           INTEGER,PSN_NAME             NVARCHAR2(50),NOTICE_CONTENT       NVARCHAR2(200),USER_NO              NVARCHAR2(50),LOGIN_PWD            NVARCHAR2(50),START_EXAM_DATE      DATE,END_EXAM_DATE        DATE,ANS_LIMIT_TIME       NUMBER(3),ANS_TIME             DATE,USER_ID              INTEGER,STATE                NUMBER(1),CUT_SCREEN_TIMES     NUMBER(3),EXAM_DESC            NVARCHAR2(500),CERT_NO              NVARCHAR2(100),CERT_START_DATE      DATE,CERT_END_DATE        DATE,CERT_FILE_PATH       NVARCHAR2(100),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZW_EXAM_PSN primary key (RID),constraint FK_TD_ZW_EXAM_PSN1 foreign key (UNIT_ID) references TS_UNIT (RID),constraint FK_TD_ZW_EXAM_PSN2 foreign key (ZONE_ID) references TS_ZONE (RID),constraint FK_TD_ZW_EXAM_PSN3 foreign key (PRO_PSN_ID) references TD_ZW_PSNINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_EXAM_PSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_EXAM_PSN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_EXAM_SUBJECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_EXAM_SUBJECT (RID                  INTEGER              not null,MAIN_ID              INTEGER,QUAL_TYPE_ID         INTEGER,RANGE_TYPE_ID        INTEGER,TOTAL_SCORE          NUMBER(8,3),SCORE                NUMBER(8,3),IF_NEED_SCORE        NUMBER(1),IF_HG                NUMBER(1),SCORE_UNIT_ID        INTEGER,SCORE_PSN_ID         INTEGER,STATE                NUMBER(1),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZW_EXAM_SUBJECT primary key (RID),constraint FK_TD_ZW_EXAM_SUBJECT1 foreign key (MAIN_ID) references TD_ZW_EXAM_PSN (RID),constraint FK_TD_ZW_EXAM_SUBJECT2 foreign key (QUAL_TYPE_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_ZW_EXAM_SUBJECT3 foreign key (RANGE_TYPE_ID) references TS_SIMPLE_CODE (RID),constraint FK_TD_ZW_EXAM_SUBJECT4 foreign key (SCORE_UNIT_ID) references TS_UNIT (RID),constraint FK_TD_ZW_EXAM_SUBJECT5 foreign key (SCORE_PSN_ID) references TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_EXAM_SUBJECT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_EXAM_SUBJECT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_EXAM_QUE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_EXAM_QUE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SUBJECT_ID           INTEGER,
                   SHOW_CODE            VARCHAR2(20),
                   NUM                  INTEGER,
                   TOTAL_SCORE          NUMBER(8,3),
                   SCORE                NUMBER(8,3),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_EXAM_QUE primary key (RID),
                   constraint FK_TD_ZW_EXAM_QUE1 foreign key (MAIN_ID) references TD_ZW_EXAM_SUBJECT (RID),
                   constraint FK_TD_ZW_EXAM_QUE2 foreign key (SUBJECT_ID) references TS_PROB_EXAMPOOL (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_EXAM_QUE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_EXAM_QUE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZW_EXAM_RULE_MAIN';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                        CREATE TABLE TD_ZW_EXAM_RULE_MAIN
                        (
                            RID                  INTEGER              NOT NULL,
                            TRAIN_TYPE_ID        INTEGER,
                            TRAIN_MAJOR_ID       INTEGER,
                            EXAM_TIME            NUMBER(3),
                            STATE                NUMBER(1),
                            CREATE_DATE          TIMESTAMP            NOT NULL,
                            CREATE_MANID         INTEGER              NOT NULL,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            CONSTRAINT PK_TD_ZW_EXAM_RULE_MAIN PRIMARY KEY (RID),
                            CONSTRAINT FK_TD_ZW_EXAM_RULE_MAIN1 FOREIGN KEY (TRAIN_TYPE_ID)
                                REFERENCES TS_SIMPLE_CODE (RID),
                            CONSTRAINT FK_TD_ZW_EXAM_RULE_MAIN2 FOREIGN KEY (TRAIN_MAJOR_ID)
                                REFERENCES TS_SIMPLE_CODE (RID)
                        )
                    ';
                END IF;
            END;
            ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_EXAM_RULE_MAIN_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_EXAM_RULE_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZW_EXAM_RULE';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                        CREATE TABLE TD_ZW_EXAM_RULE
                        (
                            RID                  INTEGER              NOT NULL,
                            MAIN_ID              INTEGER,
                            TYPE_ID              INTEGER,
                            NUMS                 NUMBER(3),
                            TOTAL                NUMBER(3),
                            CREATE_DATE          TIMESTAMP            NOT NULL,
                            CREATE_MANID         INTEGER              NOT NULL,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            CONSTRAINT PK_TD_ZW_EXAM_RULE PRIMARY KEY (RID),
                            CONSTRAINT FK_TD_ZW_EXAM_RULE1 FOREIGN KEY (MAIN_ID)
                                REFERENCES TD_ZW_EXAM_RULE_MAIN (RID),
                            CONSTRAINT FK_TD_ZW_EXAM_RULE2 FOREIGN KEY (TYPE_ID)
                                REFERENCES TS_SIMPLE_CODE (RID)
                        )
                    ';
                END IF;
            END;
            ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_EXAM_RULE_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_EXAM_RULE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('EXAM_START_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD EXAM_START_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWPX_PLAN')
                AND COLUMN_NAME = UPPER('EXAM_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWPX_PLAN ADD EXAM_END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_EXAM_RULE')
                AND COLUMN_NAME = UPPER('MIDDLE_NUMS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_EXAM_RULE ADD MIDDLE_NUMS NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_EXAM_RULE')
                AND COLUMN_NAME = UPPER('EASY_NUMS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_EXAM_RULE ADD EASY_NUMS NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZW_EXAM_TOPIC_NUM';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                  create table TD_ZW_EXAM_TOPIC_NUM
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               NUMS                 NUMBER(3),
               HARD_LEVEL_ID        INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_EXAM_TOPIC_NUM primary key (RID),
             constraint FK_TD_ZW_EXAM_TOPIC_NUM1 foreign key (MAIN_ID)
                  references TD_ZW_EXAM_RULE (RID),
                   constraint FK_TD_ZW_EXAM_TOPIC_NUM2 foreign key (HARD_LEVEL_ID)
                  references TS_SIMPLE_CODE (RID)
                        )
                    ';
                END IF;
            END;
            ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_EXAM_TOPIC_NUM_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_EXAM_TOPIC_NUM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZW_DIAG_DOCT_CERT';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                        create table TD_ZW_DIAG_DOCT_CERT
                        (
                            RID                  INTEGER              not null,
                            YEAR                 INTEGER,
                            UNIT_ID              INTEGER,
                            PSN_NAME             NVARCHAR2(50),
                            IDC                  NVARCHAR2(20),
                            SEX                  NUMBER(1),
                            LINKTEL              NVARCHAR2(20),
                            TRAIN_TYPE           NUMBER(1),
                            CERT_NO              NVARCHAR2(50),
                            CERT_START_DATE      DATE,
                            CERT_END_DATE        DATE,
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            constraint PK_TD_ZW_DIAG_DOCT_CERT primary key (RID),
                            constraint FK_TD_ZW_DIAG_DOCT_CERT1 foreign key (UNIT_ID) references TS_UNIT (RID)
                        )
                    ';
                END IF;
            END;
            ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZW_DIAG_DOCT_CERT_SEQ';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_DIAG_DOCT_CERT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZW_CERT_MAJOR';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE '
                        create table TD_ZW_CERT_MAJOR
                        (
                            RID                  INTEGER              not null,
                            MAIN_ID              INTEGER,
                            TYPE_ID              INTEGER,
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            constraint PK_TD_ZW_CERT_MAJOR primary key (RID),
                            constraint FK_TD_ZW_CERT_MAJOR1 foreign key (MAIN_ID) references TD_ZW_DIAG_DOCT_CERT (RID),
                            constraint FK_TD_ZW_CERT_MAJOR2 foreign key (TYPE_ID) references TS_SIMPLE_CODE (RID)
                        )
                    ';
                END IF;
            END;
            ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZW_CERT_MAJOR_SEQ';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CERT_MAJOR_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_DOCT_CERT')
                AND COLUMN_NAME = UPPER('QUAL_CERT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_DOCT_CERT ADD QUAL_CERT_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_DOCT_CERT')
                AND COLUMN_NAME = UPPER('CERT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_DOCT_CERT ADD CERT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-train 在线培训考试相关模块升级 -->