<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职卫卫生-尘肺病康复</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_PATIENT_INFO
                    (
                       RID                  INTEGER              not null,
             PSN_NO               NVARCHAR2(30),
             PSN_NAME             NVARCHAR2(50),
             IDC                  NVARCHAR2(20),
             SEX                  NUMBER(1),
             BIRTHDAY             DATE,
             NOW_ZONE_ID          INTEGER,
             NOW_ADDR             NVARCHAR2(100),
             PSN_LINK_TEL         NVARCHAR2(20),
             MRD_ID               INTEGER,
             NATION_ID            INTEGER,
             EDU_ID               INTEGER,
             HEIGHT               NUMBER(4,1),
             WEIGHT               NUMBER(4,1),
             RESIDE_PLACE_ID      INTEGER,
             WORK_UNIT            NVARCHAR2(100),
             TAKE_JOB_DATE        DATE,
             WORK_AGE             NUMBER(3),
             TCH_DUST_AGE         NUMBER(3),
             PNEUMO_ID            INTEGER,
             FST_DIAG_DATE        DATE,
             FST_DIAG_LEVEL_ID    INTEGER,
             JIN_DATE             DATE,
             JIN_LEVEL_ID         INTEGER,
             AGAIN_JIN_DATE       DATE,
             AGAIN_JIN_LEVEL_ID   INTEGER,
             SMOKE_CASE_ID        INTEGER,
             BEGIN_SMOKE_DATE     DATE,
             SMOKE_TIMES          NUMBER(3),
             QUIT_SMOKE_DATE      DATE,
             DRINK_WINE_ID        INTEGER,
             DRINK_WINE_AMOUNT_DAY NUMBER(4),
             DRINK_WINE_AMOUNT_WEEK NUMBER(4),
             DRINK_WINE_YEAR      NUMBER(3),
             IF_INJURY_TREAT      NUMBER(1),
             IF_CRPT_INDEMNITY    NUMBER(1),
             IF_URBAN_RURAL_INSURE NUMBER(1),
             IF_BIG_DIS_INSURE    NUMBER(1),
             IF_OTHER_TREAT       NUMBER(1),
             SUBSISTENCE_CASE     NUMBER(1),
             SUBSISTENCE_AMO      NUMBER(9,1),
             RCD_USER_ID          INTEGER,
             RCD_UNIT_ID          INTEGER,
             DEL_MARK             NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_PATIENT_INFO primary key (RID),
             constraint FK_TD_ZWKF_PATIENT_INFO1 foreign key (NOW_ZONE_ID)
                  references TS_ZONE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO2 foreign key (MRD_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO3 foreign key (NATION_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO4 foreign key (EDU_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO5 foreign key (PNEUMO_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO6 foreign key (FST_DIAG_LEVEL_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO7 foreign key (JIN_LEVEL_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO8 foreign key (AGAIN_JIN_LEVEL_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO9 foreign key (SMOKE_CASE_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO10 foreign key (DRINK_WINE_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO11 foreign key (RCD_USER_ID)
                  references TS_USER_INFO (RID),
                 constraint FK_TD_ZWKF_PATIENT_INFO12 foreign key (RCD_UNIT_ID)
                  references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_PATIENT_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_PATIENT_INFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_ZY_HISTORY_CASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_ZY_HISTORY_CASE
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             WORK_PLACE           NVARCHAR2(100),
             JOB                  NVARCHAR2(100),
             END_DATE             DATE,
             START_DATE           DATE,
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_ZY_HISTORY_CASE primary key (RID),
             constraint FK_TD_ZWKF_ZY_HISTORY_CASE1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_ZY_HISTORY_CASE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_ZY_HISTORY_CASE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_JMX_HISTORY_CASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_JMX_HISTORY_CASE
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             ZYB_ID               INTEGER,
             DIAG_DATE            DATE,
             IF_RECOVERY          NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_JMX_HISTORY_CASE primary key (RID),
             constraint FK_TD_ZWKF_JMX_HISTORY_CASE1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_JMX_HISTORY_CASE2 foreign key (ZYB_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_JMX_HISTORY_CASE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_JMX_HISTORY_CASE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_QUIT_RSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_QUIT_RSN
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             QUIT_RNS_ID          INTEGER,
             OTHER_QUIT_RSN       NVARCHAR2(50),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_QUIT_RSN primary key (RID),
             constraint FK_TD_ZWKF_QUIT_RSN1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_QUIT_RSN2 foreign key (QUIT_RNS_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_QUIT_RSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_QUIT_RSN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_SHARE_RECOVERY_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_SHARE_RECOVERY_PSN
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             SHARE_UNIT_ID        INTEGER,
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_SHARE_RECOVERY_PSN primary key (RID),
             constraint FK_TD_ZWKF_SHARE_RECOVERY_PSN1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_SHARE_RECOVERY_PSN2 foreign key (SHARE_UNIT_ID)
                  references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_SHARE_RECOVERY_PSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_SHARE_RECOVERY_PSN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_NOW_DISEASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_NOW_DISEASE
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             DISEASE_ID           INTEGER,
             DIS_DATE             DATE,
             OTHER_DIS_NAME       NVARCHAR2(50),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_NOW_DISEASE primary key (RID),
             constraint FK_TD_ZWKF_NOW_DISEASE1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_NOW_DISEASE2 foreign key (DISEASE_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_NOW_DISEASE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_NOW_DISEASE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_HISTORY_DIS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_HISTORY_DIS
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             DISEASE_ID           INTEGER,
             DIS_DATE             DATE,
             OTHER_DIS_NAME       NVARCHAR2(50),
             IF_RECOVERY          NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_HISTORY_DIS primary key (RID),
             constraint FK_TD_ZWKF_HISTORY_DIS1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_HISTORY_DIS2 foreign key (DISEASE_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_HISTORY_DIS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_HISTORY_DIS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_FAMILY_HIS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_FAMILY_HIS
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             DISEASE_ID           INTEGER,
             OTHER_DIS_NAME       NVARCHAR2(50),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_FAMILY_HIS primary key (RID),
             constraint FK_TD_ZWKF_FAMILY_HIS1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_FAMILY_HIS2 foreign key (DISEASE_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_FAMILY_HIS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_FAMILY_HIS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_EVAL
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             EVAL_QUE_ID          INTEGER,
             EVAL_SCORE           NUMBER(5,2),
             EVAL_TIME            DATE,
             EVAL_USER_ID         INTEGER,
             EVAL_UNIT_ID         INTEGER,
             STATE                NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_EVAL primary key (RID),
             constraint FK_TD_ZWKF_EVAL1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_EVAL2 foreign key (EVAL_QUE_ID)
                  references TS_PROB_LIB (RID),
                 constraint FK_TD_ZWKF_EVAL3 foreign key (EVAL_USER_ID)
                  references TS_USER_INFO (RID),
                 constraint FK_TD_ZWKF_EVAL4 foreign key (EVAL_UNIT_ID)
                  references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_PRESCRIPTION';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_PRESCRIPTION
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             PRESC_NO             NVARCHAR2(20),
             PRESC_DATE           DATE,
             PRESC_USER_ID        INTEGER,
             PRESC_UNIT_ID        INTEGER,
             STATE                NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_PRESCRIPTION primary key (RID),
             constraint FK_TD_ZWKF_PRESCRIPTION1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_PRESCRIPTION2 foreign key (PRESC_USER_ID)
                  references TS_USER_INFO (RID),
                 constraint FK_TD_ZWKF_PRESCRIPTION3 foreign key (PRESC_UNIT_ID)
                  references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_PRESCRIPTION_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_PRESCRIPTION_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_PRESC_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_PRESC_ITEM
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             ITEM_ID              INTEGER,
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_PRESC_ITEM primary key (RID),
             constraint FK_TD_ZWKF_PRESC_ITEM1 foreign key (MAIN_ID)
                  references TD_ZWKF_PRESCRIPTION (RID),
                 constraint FK_TD_ZWKF_PRESC_ITEM2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_PRESC_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_PRESC_ITEM_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_RECOVERY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_RECOVERY
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             REL_PRESC_ID         INTEGER,
             RECOVERY_DATE        DATE,
             RECOVERY_DOCTOR      NVARCHAR2(50),
             RECOVERY_UNIT_ID     INTEGER,
             STATE                NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_RECOVERY primary key (RID),
             constraint FK_TD_ZWKF_RECOVERY1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_RECOVERY2 foreign key (REL_PRESC_ID)
                  references TD_ZWKF_PRESCRIPTION (RID),
                 constraint FK_TD_ZWKF_RECOVERY3 foreign key (RECOVERY_UNIT_ID)
                  references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_RECOVERY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_RECOVERY_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_RECOVERY_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_RECOVERY_ITEM
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             RECOVERY_ITEM_ID     INTEGER,
             RECOVERY_BF_RST      NVARCHAR2(100),
             RECOVERY_BF_RST2     NVARCHAR2(100),
             RECOVERY_AF_RST      NVARCHAR2(100),
             RECOVERY_AF_RST2     NVARCHAR2(100),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_RECOVERY_ITEM primary key (RID),
             constraint FK_TD_ZWKF_RECOVERY_ITEM1 foreign key (MAIN_ID)
                  references TD_ZWKF_RECOVERY (RID),
                 constraint FK_TD_ZWKF_RECOVERY_ITEM2 foreign key (RECOVERY_ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_RECOVERY_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_RECOVERY_ITEM_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_ORG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_ORG
                    (
                       RID                  INTEGER              not null,
             ORG_ID               INTEGER,
             UNIT_DESC            NVARCHAR2(1000),
             STATE_MARK           NUMBER(1),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_ORG primary key (RID),
             constraint FK_TD_ZWKF_ORG1 foreign key (ORG_ID)
                  references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_ORG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_ORG_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_PSNINFO
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             PSN_NAME             NVARCHAR2(50),
             IDC_CARD             NVARCHAR2(20),
             SEX                  NUMBER(1),
             BIRTHDAY             DATE,
             LINK_TEL             NVARCHAR2(20),
             LINK_ADDR            NVARCHAR2(100),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_PSNINFO primary key (RID),
             constraint FK_TD_ZWKF_PSNINFO1 foreign key (MAIN_ID)
                  references TD_ZWKF_ORG (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_PSNINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_PSNINFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_PSN_ATTR';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_PSN_ATTR
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             ATTR_ID              INTEGER,
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_PSN_ATTR primary key (RID),
             constraint FK_TD_ZWKF_PSN_ATTR1 foreign key (MAIN_ID)
                  references TD_ZWKF_PSNINFO (RID),
                 constraint FK_TD_ZWKF_PSN_ATTR2 foreign key (ATTR_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_PSN_ATTR_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_PSN_ATTR_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_APPOINT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_APPOINT
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             CONFIG_TYPE          NUMBER(1),
             COMM_WEEK            NUMBER(2),
             OUTPATI_DATE         DATE,
             IF_OUTPATI           NUMBER(1),
             APPOINT_LIMIT        NUMBER(4),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_APPOINT primary key (RID),
             constraint FK_TD_ZWKF_APPOINT1 foreign key (MAIN_ID)
                  references TD_ZWKF_ORG (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_APPOINT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_APPOINT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_INSTINFO
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             INST_KIND_ID         INTEGER,
             INST_NAME            NVARCHAR2(50),
             INST_MODEL           NVARCHAR2(20),
             CUST_NO              NVARCHAR2(50),
             INST_FACTORY         NVARCHAR2(100),
             INST_PRO_DATE        DATE,
             PURCHASE_DATE        DATE,
             ACPT_REQ_ID          INTEGER,
             IF_NEED_ACPT         NUMBER(1),
             LAST_ACPT_DATE       DATE,
             LAST_ACPT_CIRCLE     INTEGER,
             INST_STATE_ID        INTEGER,
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_INSTINFO primary key (RID),
             constraint FK_TD_ZWKF_INSTINFO1 foreign key (MAIN_ID)
                  references TD_ZWKF_ORG (RID),
                 constraint FK_TD_ZWKF_INSTINFO2 foreign key (ACPT_REQ_ID)
                  references TS_SIMPLE_CODE (RID),
                 constraint FK_TD_ZWKF_INSTINFO3 foreign key (INST_STATE_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_INSTINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_INSTINFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_DRINK_WINE_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_DRINK_WINE_TYPE
                    (
                       RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             WINE_ID              INTEGER,
             OTHER_WINE           NVARCHAR2(50),
             CREATE_DATE          TIMESTAMP            not null,
             CREATE_MANID         INTEGER              not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
             constraint PK_TD_ZWKF_DRINK_WINE_TYPE primary key (RID),
             constraint FK_TD_ZWKF_DRINK_WINE_TYPE1 foreign key (MAIN_ID)
                  references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_DRINK_WINE_TYPE2 foreign key (WINE_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_DRINK_WINE_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_DRINK_WINE_TYPE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_INSTINFO4'
                AND TABLE_NAME = 'TD_ZWKF_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_INSTINFO add constraint FK_TD_ZWKF_INSTINFO4 foreign key (INST_KIND_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL')
                AND COLUMN_NAME = UPPER('STATE_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL ADD STATE_MARK NUMBER(1) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_FOLLOW_UP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_FOLLOW_UP
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       FLOWUP_QUE_ID        INTEGER,
                       FLOWUP_ID            INTEGER,
                       FLOWUP_DATE          DATE,
                       FLOWUP_DOCTOR        NVARCHAR2(50),
                       FLOWUP_UNIT_ID       INTEGER,
                       STATE                NUMBER(1),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_FOLLOW_UP primary key (RID),
                       constraint FK_TD_ZWKF_FOLLOW_UP1 foreign key (MAIN_ID) references TD_ZWKF_PATIENT_INFO (RID),
                       constraint FK_TD_ZWKF_FOLLOW_UP2 foreign key (FLOWUP_QUE_ID) references TS_PROB_LIB (RID),
                       constraint FK_TD_ZWKF_FOLLOW_UP3 foreign key (FLOWUP_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZWKF_FOLLOW_UP4 foreign key (FLOWUP_UNIT_ID) references TS_UNIT (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_FOLLOW_UP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_FOLLOW_UP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_FOLLOW_UP')
                AND COLUMN_NAME = UPPER('STATE_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD STATE_MARK NUMBER(1) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_RECOVERY')
                AND COLUMN_NAME = UPPER('RECOVERY_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_RECOVERY ADD RECOVERY_END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL')
                AND COLUMN_NAME = UPPER('EVAL_QUE_ID');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL drop constraint FK_TD_ZWKF_EVAL2 ';
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL rename column EVAL_QUE_ID to QUE_LIB_ID ';
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL add constraint FK_TD_ZWKF_EVAL2 foreign key (QUE_LIB_ID) references TS_PROB_LIB (RID) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_FOLLOW_UP')
                AND COLUMN_NAME = UPPER('FLOWUP_QUE_ID');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP drop constraint FK_TD_ZWKF_FOLLOW_UP2 ';
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP rename column FLOWUP_QUE_ID to QUE_LIB_ID ';
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP add constraint FK_TD_ZWKF_FOLLOW_UP2 foreign key (QUE_LIB_ID) references TS_PROB_LIB (RID) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_RECOVERY_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                   create table TD_ZWKF_RECOVERY_INST
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       INST_ID              INTEGER,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_RECOVERY_INST primary key (RID),
                       constraint FK_TD_ZWKF_RECOVERY_INST1 foreign key (MAIN_ID)
                        references TD_ZWKF_RECOVERY (RID),
                       constraint FK_TD_ZWKF_RECOVERY_INST2 foreign key (INST_ID)
                        references TD_ZWKF_INSTINFO (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_RECOVERY_INST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_RECOVERY_INST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ORG')
                AND COLUMN_NAME = UPPER('LINK_MAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ORG ADD LINK_MAN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ORG')
                AND COLUMN_NAME = UPPER('UNITTEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ORG ADD UNITTEL NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_RECOVERY_APPOINT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_RECOVERY_APPOINT
                    (
                        RID                  INTEGER              not null,
                         ORG_ID               INTEGER,
                         PSN_ID               INTEGER,
                         APPOINT_PSN_ID       INTEGER,
                         IDC                  NVARCHAR2(20),
                         APPOINT_DATE         DATE,
                         CREATE_DATE          TIMESTAMP            not null,
                         CREATE_MANID         INTEGER              not null,
                         MODIFY_DATE          TIMESTAMP,
                         MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_RECOVERY_APPOINT primary key (RID),
                       constraint FK_TD_ZWKF_RECOVERY_APPOINT1 foreign key (ORG_ID)
                            references TD_ZWKF_ORG (RID),
                       constraint FK_TD_ZWKF_RECOVERY_APPOINT2 foreign key (PSN_ID)
                            references TD_ZWKF_PATIENT_INFO (RID),
                       constraint FK_TD_ZWKF_RECOVERY_APPOINT3 foreign key (APPOINT_PSN_ID)
                            references TD_WXZS_REG_PSN_INFO (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_RECOVERY')
                AND COLUMN_NAME = UPPER('IF_SATISFIED');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_RECOVERY ADD IF_SATISFIED NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_RECOVERY')
                AND COLUMN_NAME = UPPER('FEED_BACK_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_RECOVERY ADD FEED_BACK_MSG NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_RECOVERY_APPOINT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_RECOVERY_APPOINT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1000  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_APPOINT_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_APPOINT_DETAIL
                    (
                         RID                  INTEGER              not null,
                         MAIN_ID              INTEGER,
                         OUTPATI_DATE         DATE,
                         APPOINT_LIMIT        NUMBER(4),
                         BOOKED_PSN           NUMBER(4),
                         CREATE_DATE          TIMESTAMP            not null,
                         CREATE_MANID         INTEGER              not null,
                         MODIFY_DATE          TIMESTAMP,
                         MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_APPOINT_DETAIL primary key (RID),
                       constraint FK_TD_ZWKF_APPOINT_DETAIL1 foreign key (MAIN_ID)
                            references TD_ZWKF_ORG (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_APPOINT_DETAIL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_APPOINT_DETAIL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1000  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_RECOVERY')
                AND COLUMN_NAME = UPPER('FEED_BACK_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_RECOVERY ADD FEED_BACK_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('CUT_DIRECT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD CUT_DIRECT NVARCHAR2(50)';
              END IF;
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('WHICH_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD WHICH_NUM NVARCHAR2(50)';
              END IF;
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('GET_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD GET_NUM NVARCHAR2(50)';
              END IF;
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('SPLIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD SPLIT NVARCHAR2(50)';
              END IF;
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('ROW_ASK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD ROW_ASK NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_INST_INTER';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_ZWKF_INST_INTER
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_FLAG            NVARCHAR2(50),
                       ROW_NUM              NVARCHAR2(50),
                       CHAR_STR             NVARCHAR2(50),
                       WHICH_NUM            NVARCHAR2(50),
                       GET_NUM              NVARCHAR2(50),
                       SCALE                NVARCHAR2(50),
                       FIRST_CHAR_EQ        NVARCHAR2(50),
                       FIRST_CHAR_NOT_EQ    NVARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_INST_INTER primary key (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_INST_INTER_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_INST_INTER_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('INTER_MODEL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD INTER_MODEL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_INSTINFO')
                AND COLUMN_NAME = UPPER('INTER_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_INSTINFO ADD INTER_PATH NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_INSTINFO5'
                AND TABLE_NAME = 'TD_ZWKF_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_INSTINFO add constraint FK_TD_ZWKF_INSTINFO5 foreign key (INTER_MODEL_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_INTER_DB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_ZWKF_INTER_DB
                    (
                       RID                  INTEGER              not null,
                       IDC_CARD             NVARCHAR2(20),
                       INTER_COL1           NVARCHAR2(200),
                       INTER_COL2           NVARCHAR2(200),
                       INTER_COL3           NVARCHAR2(200),
                       INTER_COL4           NVARCHAR2(200),
                       INTER_COL5           NVARCHAR2(200),
                       INTER_COL6           NVARCHAR2(200),
                       INTER_COL7           NVARCHAR2(200),
                       INTER_COL8           NVARCHAR2(200),
                       INTER_COL9           NVARCHAR2(200),
                       INTER_COL10          NVARCHAR2(200),
                       INTER_COL11          NVARCHAR2(200),
                       INTER_COL12          NVARCHAR2(200),
                       INTER_COL13          NVARCHAR2(200),
                       INTER_COL14          NVARCHAR2(200),
                       INTER_COL15          NVARCHAR2(200),
                       INTER_COL16          NVARCHAR2(200),
                       INTER_COL17          NVARCHAR2(200),
                       INTER_COL18          NVARCHAR2(200),
                       INTER_COL19          NVARCHAR2(200),
                       INTER_COL20          NVARCHAR2(200),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_INTER_DB primary key (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_INTER_DB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_INTER_DB_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('FAMILY_MEMBERS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD FAMILY_MEMBERS NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HAS_CHILDREN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HAS_CHILDREN NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PK_CAREGIVER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD PK_CAREGIVER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO13'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO13 foreign key (PK_CAREGIVER_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_PK_CAREGIVER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD OTHER_PK_CAREGIVER NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PK_CAREGIVER_HEALTH_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD PK_CAREGIVER_HEALTH_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO14'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO14 foreign key (PK_CAREGIVER_HEALTH_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('CURR_EMP_STATUS_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD CURR_EMP_STATUS_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO17'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO17 foreign key (CURR_EMP_STATUS_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_CURR_EMP_STATUS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD OTHER_CURR_EMP_STATUS NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RETIREMENT_SALARY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD RETIREMENT_SALARY NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HOUSEHOLD_INCOME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HOUSEHOLD_INCOME NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_MEET_LIVE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_MEET_LIVE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_LOAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_LOAN NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('LOAN_AMO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD LOAN_AMO NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PROPERTY_RIGHT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD PROPERTY_RIGHT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO15'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO15 foreign key (PROPERTY_RIGHT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_PROPERTY_RIGHT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD OTHER_PROPERTY_RIGHT NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HAS_HOUSES');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HAS_HOUSES NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HOUSE_AREA');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HOUSE_AREA NUMBER(6,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_RELAPSE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_RELAPSE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RELAPSE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD RELAPSE_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('DRINK_WINE_AMOUNT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD DRINK_WINE_AMOUNT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO16'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO16 foreign key (DRINK_WINE_AMOUNT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_HISTORY_SURGERY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_HISTORY_SURGERY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HISTORY_SURGERY_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HISTORY_SURGERY_CASE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_HISTORY_TRAUMA');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_HISTORY_TRAUMA NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HISTORY_TRAUMA_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HISTORY_TRAUMA_CASE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_USE_MEDICINE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_USE_MEDICINE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('USE_MEDICINE_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD USE_MEDICINE_CASE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PNEUM_HSPT_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD PNEUM_HSPT_NUM NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PNEUM_HSPT_MONTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD PNEUM_HSPT_MONTH NUMBER(3,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RESP_HSPT_NUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD RESP_HSPT_NUM NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RESP_HSPT_MONTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD RESP_HSPT_MONTH NUMBER(3,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RESP_HSPT_PRO_MED');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD RESP_HSPT_PRO_MED NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RESP_HSPT_REIMBURSE_MED');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD RESP_HSPT_REIMBURSE_MED NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD STATE NUMBER(1) default 0 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('WORK_CONTENT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD WORK_CONTENT NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('TCH_BADRSNS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD TCH_BADRSNS NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('IF_EXAM_INDUCTION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD IF_EXAM_INDUCTION NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('IF_EXAM_QUIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD IF_EXAM_QUIT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('IF_EXAM_OCCUPATION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD IF_EXAM_OCCUPATION NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('OCCUPATION_PERIOD');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD OCCUPATION_PERIOD NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('IF_VENTILATION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD IF_VENTILATION NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('IF_HEALTH_EDU');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD IF_HEALTH_EDU NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('LABOR_CONTRACT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD LABOR_CONTRACT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_ZY_HISTORY_CASE2'
                AND TABLE_NAME = 'TD_ZWKF_ZY_HISTORY_CASE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_ZY_HISTORY_CASE add constraint FK_TD_ZWKF_ZY_HISTORY_CASE2 foreign key (LABOR_CONTRACT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('OTHER_PROTECT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD OTHER_PROTECT NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('WEAR_CONDITION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD WEAR_CONDITION NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('WEAR_TIME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD WEAR_TIME NUMBER(3,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>115</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_FAMILY_HIS_REL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_ZWKF_FAMILY_HIS_REL
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   REL_ID               INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_FAMILY_HIS_REL primary key (RID),
                   constraint FK_TD_ZWKF_FAMILY_HIS_REL1 foreign key (MAIN_ID)
                    references TD_ZWKF_FAMILY_HIS (RID),
                   constraint FK_TD_ZWKF_FAMILY_HIS_REL2 foreign key (REL_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>116</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_FAMILY_HIS_REL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_FAMILY_HIS_REL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1000  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>117</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_NOW_DISEASE')
                AND COLUMN_NAME = UPPER('IF_TREATMENT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_NOW_DISEASE ADD IF_TREATMENT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>118</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_NOW_DISEASE')
                AND COLUMN_NAME = UPPER('IF_TAKE_METFORMIN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_NOW_DISEASE ADD IF_TAKE_METFORMIN NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>119</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_NOW_DISEASE')
                AND COLUMN_NAME = UPPER('TAKE_BEGIN_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_NOW_DISEASE ADD TAKE_BEGIN_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>120</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_NOW_DISEASE')
                AND COLUMN_NAME = UPPER('TAKE_END_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_NOW_DISEASE ADD TAKE_END_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>121</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_ZY_HIS_PROTECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZWKF_ZY_HIS_PROTECT
                    (
                       RID                  INTEGER              NOT NULL,
                       MAIN_ID              INTEGER,
                       PROTECT_EQU_ID       INTEGER,
                       CREATE_DATE          TIMESTAMP            NOT NULL,
                       CREATE_MANID         INTEGER              NOT NULL,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       CONSTRAINT PK_TD_ZWKF_ZY_HIS_PROTECT PRIMARY KEY (RID)
                    )';
              END IF;
            END;
            ]]>
        </sql>
        <ver>122</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_ZY_HIS_PROTECT1'
                AND TABLE_NAME = 'TD_ZWKF_ZY_HIS_PROTECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HIS_PROTECT ADD CONSTRAINT FK_TD_ZWKF_ZY_HIS_PROTECT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWKF_ZY_HISTORY_CASE (RID)';
              END IF;
            END;
            ]]>
        </sql>
        <ver>123</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_ZY_HIS_PROTECT2'
                AND TABLE_NAME = 'TD_ZWKF_ZY_HIS_PROTECT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HIS_PROTECT ADD CONSTRAINT FK_TD_ZWKF_ZY_HIS_PROTECT2 FOREIGN KEY (PROTECT_EQU_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
            ]]>
        </sql>
        <ver>124</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_ZY_HIS_PROTECT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_ZY_HIS_PROTECT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>125</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('EMERG_LINK_MAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD EMERG_LINK_MAN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>126</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('EMERG_LINK_TEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD EMERG_LINK_TEL NVARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>127</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SMOKE_YEARS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD SMOKE_YEARS NUMBER(3,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>128</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SHS_EXPOSE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD SHS_EXPOSE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>129</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NICOTINE_SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD NICOTINE_SCORE NUMBER(6,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>130</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('QUIT_SMOKE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD QUIT_SMOKE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>131</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO18'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO18 foreign key (QUIT_SMOKE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>132</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('MEDICAL_IN_INSURE_RATIO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD MEDICAL_IN_INSURE_RATIO NUMBER(4,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>133</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('MEDICAL_OUT_INSURE_RATIO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD MEDICAL_OUT_INSURE_RATIO NUMBER(4,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>134</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NEW_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD NEW_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>135</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO19'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO19 foreign key (NEW_UNIT_ID) references TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>136</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HU_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HU_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>137</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO20'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO20 foreign key (HU_ZONE_ID) references TS_ZONE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>138</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HU_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HU_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>139</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('TCH_DUST_MONTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD TCH_DUST_MONTH NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>140</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('TCH_DUST_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD TCH_DUST_TYPE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>141</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_SHARE_RECOVERY_PSN')
                AND COLUMN_NAME = UPPER('ARCH_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_SHARE_RECOVERY_PSN ADD ARCH_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>142</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_SHARE_RECOVERY_PSN')
                AND COLUMN_NAME = UPPER('IF_STOP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_SHARE_RECOVERY_PSN ADD IF_STOP NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>143</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_SHARE_RECOVERY_PSN')
                AND COLUMN_NAME = UPPER('STOP_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_SHARE_RECOVERY_PSN ADD STOP_RSN NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>144</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_FILES';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                   create table TD_ZWKF_FILES
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       FILE_TYPE_ID         INTEGER,
                       FILE_NAME            NVARCHAR2(50),
                       FILE_PATH            NVARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_FILES primary key (RID),
                        constraint FK_TD_ZWKF_FILES1 foreign key (MAIN_ID)
                        references TD_ZWKF_PATIENT_INFO (RID),
                        constraint FK_TD_ZWKF_FILES2 foreign key (FILE_TYPE_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>145</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_NICOTINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWKF_NICOTINE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   FIRST_SMOKE_ID       INTEGER,
                   HARD_CONTROL_ID      INTEGER,
                   GIVE_UP_ID           INTEGER,
                   SMOKE_NUM_ID         INTEGER,
                   SMOKE_TIME_ID        INTEGER,
                   SMOKE_IN_BED_ID      INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_NICOTINE primary key (RID),
                   constraint FK_TD_ZWKF_NICOTINE1 foreign key (MAIN_ID)
                      references TD_ZWKF_PATIENT_INFO (RID),
                   constraint FK_TD_ZWKF_NICOTINE2 foreign key (FIRST_SMOKE_ID)
                      references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWKF_NICOTINE3 foreign key (HARD_CONTROL_ID)
                      references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWKF_NICOTINE4 foreign key (GIVE_UP_ID)
                      references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWKF_NICOTINE5 foreign key (SMOKE_NUM_ID)
                      references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWKF_NICOTINE6 foreign key (SMOKE_TIME_ID)
                      references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWKF_NICOTINE7 foreign key (SMOKE_IN_BED_ID)
                      references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>146</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ZY_HISTORY_CASE')
                AND COLUMN_NAME = UPPER('TCH_DEPTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ZY_HISTORY_CASE ADD TCH_DEPTH NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>147</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_NICOTINE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_NICOTINE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>148</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_FILES_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_FILES_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>149</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWKF_EVAL_MAIN
                        (
                           RID                  INTEGER              not null,
                           MAIN_ID              INTEGER,
                           EVAL_TYPE            NUMBER(1),
                           EVAL_DATE            DATE,
                           EVAL_RESULT_ID       INTEGER,
                           STATE_MARK           NUMBER(1),
                           IF_GENERAL           NUMBER(1),
                           HEART_RATE           NUMBER(3),
                           BREATHE              NUMBER(3),
                           DIASTOLIC            NUMBER(3),
                           SYSTOLIC             NUMBER(3),
                           SPO2                 NUMBER(5,2),
                           IF_LUNGS             NUMBER(1),
                           LUNGS                NVARCHAR2(100),
                           IF_BREATHE           NUMBER(1),
                           MUSCLE_ID            INTEGER,
                           IF_ADL               NUMBER(1),
                           ADL                  NUMBER(5,2),
                           IF_MUSCLE            NUMBER(1),
                           LEFT_UP_ID           INTEGER,
                           RIGHT_UP_ID          INTEGER,
                           LEFT_DOWN_ID         INTEGER,
                           RIGHT_DOWN_ID        INTEGER,
                           IF_MUSCLE_TONE       NUMBER(1),
                           UP_ID                INTEGER,
                           DOWN_ID              INTEGER,
                           IF_MMRC              NUMBER(1),
                           MMRC                 NUMBER(1),
                           IF_SIXMINUTE_TEST    NUMBER(1),
                           SIXMINUTE_TEST       NUMBER(6,1),
                           IF_BORG              NUMBER(1),
                           BORG                 NUMBER(5,2),
                           IF_SPORTS_RISK       NUMBER(1),
                           SPORTS_RISK          NVARCHAR2(200),
                           IF_CAT               NUMBER(1),
                           CAT                  NUMBER(5,2),
                           EVAL_USER_ID         INTEGER,
                           EVAL_UNIT_ID         INTEGER,
                           PRESC_DATE           DATE,
                           AGREE_FILE_PATH      NVARCHAR2(100),
                           RECOVERY_DATE        DATE,
                           RECOVERY_END_DATE    DATE,
                           DEL_MARK             NUMBER(1),
                           CREATE_DATE          TIMESTAMP            not null,
                           CREATE_MANID         INTEGER              not null,
                           MODIFY_DATE          TIMESTAMP,
                           MODIFY_MANID         INTEGER,
                           constraint PK_TD_ZWKF_EVAL_MAIN primary key (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN1 foreign key (MAIN_ID)
                             references TD_ZWKF_PATIENT_INFO (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN2 foreign key (EVAL_RESULT_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN3 foreign key (EVAL_USER_ID)
                             references TS_USER_INFO (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN4 foreign key (EVAL_UNIT_ID)
                             references TS_UNIT (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN5 foreign key (MUSCLE_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN6 foreign key (LEFT_UP_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN7 foreign key (RIGHT_UP_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN8 foreign key (LEFT_DOWN_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN9 foreign key (RIGHT_DOWN_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN10 foreign key (UP_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZWKF_EVAL_MAIN11 foreign key (DOWN_ID)
                             references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>150</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_MAIN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>151</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_RECOVERY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZWKF_EVAL_RECOVERY
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   RECOVERY_DATE        DATE,
                   RECOVERY_END_DATE    DATE,
                   RECOVERY_DOCTOR      NVARCHAR2(50),
                   STATE                NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_EVAL_RECOVERY primary key (RID),
                   constraint FK_TD_ZWKF_EVAL_RECOVERY1 foreign key (MAIN_ID)
                      references TD_ZWKF_EVAL_MAIN (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>152</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_RECOVERY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_RECOVERY_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>153</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
               create table TD_ZWKF_EVAL_TABLE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   QUE_LIB_ID           INTEGER,
                   EVAL_SCORE           NUMBER(5,2),
                   EVAL_STD_SCORE       NUMBER(5,2),
                   EVAL_TIME            DATE,
                   STATE_MARK           NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_EVAL_TABLE primary key (RID),
                 constraint FK_TD_ZWKF_EVAL_TABLE1 foreign key (MAIN_ID)
                      references TD_ZWKF_EVAL_MAIN (RID),
                constraint FK_TD_ZWKF_EVAL_TABLE2 foreign key (QUE_LIB_ID)
                      references TS_PROB_LIB (RID)
                        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>154</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_TABLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_TABLE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>155</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_REFER';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
              create table TD_ZWKF_REFER
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               REFER_TIME           DATE,
               REFER_DEST           NVARCHAR2(500),
               REFER_DESC           NVARCHAR2(500),
               UNIT_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZWKF_REFER primary key (RID),
                constraint FK_TD_ZWKF_REFER1 foreign key (MAIN_ID)
              references TD_ZWKF_PATIENT_INFO (RID),
               constraint FK_TD_ZWKF_REFER2 foreign key (UNIT_ID)
              references TS_UNIT (RID)
                        )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>156</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_REFER_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_REFER_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>157</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_PRESCR';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
               create table TD_ZWKF_EVAL_PRESCR
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_ID              INTEGER,
                       FREQ                 NVARCHAR2(50),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZWKF_EVAL_PRESCR primary key (RID),
                       constraint FK_TD_ZWKF_EVAL_PRESCR1 foreign key (MAIN_ID)
                        references TD_ZWKF_EVAL_MAIN (RID),
                       constraint FK_TD_ZWKF_EVAL_PRESCR2 foreign key (ITEM_ID)
                        references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>158</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_PRESCR_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_PRESCR_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>159</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_FILE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'CREATE TABLE TD_ZWKF_EVAL_FILE
                 (
                    RID                  INTEGER              NOT NULL,
                    MAIN_ID              INTEGER,
                    FILE_TYPE            NUMBER(2),
                    FILE_NAME            NVARCHAR2(50),
                    FILE_PATH            NVARCHAR2(50),
                    CREATE_DATE          TIMESTAMP            NOT NULL,
                    CREATE_MANID         INTEGER              NOT NULL,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    CONSTRAINT PK_TD_ZWKF_EVAL_FILE PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_FILE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWKF_EVAL_MAIN (RID)
                 )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>160</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_FILE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_FILE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>161</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_RECOVERY_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'CREATE TABLE TD_ZWKF_EVAL_RECOVERY_SUB
                 (
                    RID                  INTEGER              NOT NULL,
                    MAIN_ID              INTEGER,
                    RECOVERY_ITEM_ID     INTEGER,
                    RECOVERY_BF_RST      NVARCHAR2(100),
                    RECOVERY_BF_RST2     NVARCHAR2(100),
                    RECOVERY_MID_RST     NVARCHAR2(100),
                    RECOVERY_MID_RST2    NVARCHAR2(100),
                    RECOVERY_AF_RST      NVARCHAR2(100),
                    RECOVERY_AF_RST2     NVARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            NOT NULL,
                    CREATE_MANID         INTEGER              NOT NULL,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    CONSTRAINT PK_TD_ZWKF_EVAL_RECOVERY_SUB PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_RECOVERY_SUB1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWKF_EVAL_RECOVERY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_RECOVERY_SUB2 FOREIGN KEY (RECOVERY_ITEM_ID) REFERENCES TS_SIMPLE_CODE (RID)
                 )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>162</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_RECOVERY_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_RECOVERY_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>163</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_RECOVERY_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'CREATE TABLE TD_ZWKF_EVAL_RECOVERY_ITEM
                 (
                    RID                  INTEGER              NOT NULL,
                    MAIN_ID              INTEGER,
                    ITEM_ID              INTEGER,
                    CREATE_DATE          TIMESTAMP            NOT NULL,
                    CREATE_MANID         INTEGER              NOT NULL,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    CONSTRAINT PK_TD_ZWKF_EVAL_RECOVERY_ITEM PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_RECOVERY_ITEM1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWKF_EVAL_RECOVERY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_RECOVERY_ITEM2 FOREIGN KEY (ITEM_ID) REFERENCES TS_SIMPLE_CODE (RID)
                 )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>164</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_RECOVERY_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_RECOVERY_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>165</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_RECOVERY_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'CREATE TABLE TD_ZWKF_EVAL_RECOVERY_INST
                 (
                    RID                  INTEGER              NOT NULL,
                    MAIN_ID              INTEGER,
                    INST_ID              INTEGER,
                    CREATE_DATE          TIMESTAMP            NOT NULL,
                    CREATE_MANID         INTEGER              NOT NULL,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    CONSTRAINT PK_TD_ZWKF_EVAL_RECOVERY_INST PRIMARY KEY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_RECOVERY_INST1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZWKF_EVAL_RECOVERY (RID),
                    CONSTRAINT FK_TD_ZWKF_EVAL_RECOVERY_INST2 FOREIGN KEY (INST_ID) REFERENCES TD_ZWKF_INSTINFO (RID)
                 )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>166</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_RECOVERY_INST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_RECOVERY_INST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>167</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('LIVE_STATE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD LIVE_STATE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>168</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO21'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO21 foreign key (LIVE_STATE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>169</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_LIVE_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD OTHER_LIVE_STATE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>170</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_SHARE_RECOVERY_PSN')
                AND COLUMN_NAME = UPPER('LIVE_STATE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_SHARE_RECOVERY_PSN ADD LIVE_STATE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>171</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_SHARE_RECOVERY_PSN3'
                AND TABLE_NAME = 'TD_ZWKF_SHARE_RECOVERY_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_SHARE_RECOVERY_PSN add constraint FK_TD_ZWKF_SHARE_RECOVERY_PSN3 foreign key (LIVE_STATE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>172</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_SHARE_RECOVERY_PSN')
                AND COLUMN_NAME = UPPER('OTHER_LIVE_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_SHARE_RECOVERY_PSN ADD OTHER_LIVE_STATE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>173</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_RECOVERY')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_RECOVERY ADD RMK NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>174</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ORG')
                AND COLUMN_NAME = UPPER('CF_PSN_NUMS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ORG ADD CF_PSN_NUMS NUMBER(5)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>175</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PSN_ATTR')
                AND COLUMN_NAME = UPPER('OTHER_ATTR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PSN_ATTR ADD OTHER_ATTR NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>176</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_CHEST_PAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'create table TD_ZWKF_EVAL_CHEST_PAIN
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   IF_CHEST_PAIN        NUMBER(1),
                   IF_PAIN              NUMBER(1),
                   PART                 NVARCHAR2(50),
                   NATURE               NVARCHAR2(100),
                   DURATION             NVARCHAR2(50),
                   EFFECT_FACTOR        NVARCHAR2(100),
                   SYMPTOMS             NVARCHAR2(100),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_EVAL_CHEST_PAIN primary key (RID),
                   constraint FK_TD_ZWKF_EVAL_CHEST_PAIN1 foreign key (MAIN_ID)
                     references TD_ZWKF_EVAL_MAIN (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>177</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_CHEST_PAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_CHEST_PAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>178</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('LIVE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD LIVE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>179</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_PATIENT_INFO22'
                AND TABLE_NAME = 'TD_ZWKF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_PATIENT_INFO add constraint FK_TD_ZWKF_PATIENT_INFO22 foreign key (LIVE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>180</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_BAD_RSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'create table TD_ZWKF_BAD_RSN
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   BAD_RSN_ID           INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_BAD_RSN primary key (RID),
                 constraint FK_TD_ZWKF_BAD_RSN1 foreign key (MAIN_ID)
                      references TD_ZWKF_PATIENT_INFO (RID),
                constraint FK_TD_ZWKF_BAD_RSN2 foreign key (BAD_RSN_ID)
                      references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>181</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_BAD_RSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_BAD_RSN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>182</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_TREAT_WAY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'create table TD_ZWKF_TREAT_WAY
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   TREAT_WAY_ID         INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_TREAT_WAY primary key (RID),
                 constraint FK_TD_ZWKF_TREAT_WAY1 foreign key (MAIN_ID)
                      references TD_ZWKF_PATIENT_INFO (RID),
                 constraint FK_TD_ZWKF_TREAT_WAY2 foreign key (TREAT_WAY_ID)
                      references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>183</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_TREAT_WAY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_TREAT_WAY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>184</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NEWEST_TREAT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD NEWEST_TREAT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>185</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_HISTORY_INFECTIOUS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_HISTORY_INFECTIOUS NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>186</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HISTORY_INFECTIOUS_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HISTORY_INFECTIOUS_CASE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>187</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_HISTORY_ALLERGY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_HISTORY_ALLERGY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>188</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HISTORY_ALLERGY_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HISTORY_ALLERGY_CASE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>189</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_HISTORY_LUNG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD IF_HISTORY_LUNG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>190</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('HISTORY_LUNG_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD HISTORY_LUNG_CASE NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>191</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('SPO2_REST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD SPO2_REST NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>192</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('HIGH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD HIGH NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>193</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('WEIGHT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD WEIGHT NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>194</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('HEART_LISTEN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD HEART_LISTEN NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>195</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('IMAGING_TEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD IMAGING_TEST NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>196</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('NICOTINE_SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD NICOTINE_SCORE NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>197</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('NICOTINE_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD NICOTINE_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>198</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('IF_SWALLOW');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD IF_SWALLOW NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>199</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('SWALLOW_TIMES');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD SWALLOW_TIMES NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>200</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('ARCH_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD ARCH_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>201</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_EVAL_MAIN12'
                AND TABLE_NAME = 'TD_ZWKF_EVAL_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_EVAL_MAIN add constraint FK_TD_ZWKF_EVAL_MAIN12 foreign key (NICOTINE_RST_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>202</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_COUGH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'create table TD_ZWKF_EVAL_COUGH
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   IF_COUGH             NUMBER(1),
                   DAILY_RATE           NUMBER(3),
                   DURATION             NUMBER(3),
                   CAUSE                NUMBER(1),
                   CAUSE_RMK            NVARCHAR2(100),
                   SPUTUM_COLOR         NVARCHAR2(100),
                   SPUTUM_NATURE        NVARCHAR2(100),
                   SPUTUM_VOLUME        NUMBER(5,2),
                   SPUTUM_ODOR          NVARCHAR2(100),
                   IF_DISCHARGED        NUMBER(1),
                   IF_CYANOSIS          NUMBER(1),
                   IF_JUGULAR           NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_EVAL_COUGH primary key (RID),
                   constraint FK_TD_ZWKF_EVAL_COUGH1 foreign key (MAIN_ID) references TD_ZWKF_EVAL_MAIN (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>203</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_COUGH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_COUGH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>204</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWKF_EVAL_COUGH_LUNG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
                'create table TD_ZWKF_EVAL_COUGH_LUNG
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   LUNG_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWKF_EVAL_COUGH_LUNG primary key (RID),
                   constraint FK_TD_ZWKF_EVAL_COUGH_LUNG foreign key (LUNG_ID) references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZWKF_EVAL_COUGH_LUNG1 foreign key (MAIN_ID) references TD_ZWKF_EVAL_COUGH (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>205</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_COUGH_LUNG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_COUGH_LUNG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
            ]]>
        </sql>
        <ver>206</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWKF_EVAL_LUNG';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE
                        'create table TD_ZWKF_EVAL_LUNG
                         (
                             RID                  INTEGER              not null,
                             MAIN_ID              INTEGER,
                             IF_LUNG              NUMBER(1),
                             VCL                  NUMBER(8,3),
                             RCL                  NUMBER(8,3),
                             TLC                  NUMBER(8,3),
                             RV_TLC               NUMBER(8,3),
                             FVC                  NUMBER(8,3),
                             FEV                  NUMBER(8,3),
                             VENTILATION          NUMBER(8,3),
                             MAX_VENTILATION      NUMBER(8,3),
                             MINUTE_VENTILATION   NUMBER(8,3),
                             CREATE_DATE          TIMESTAMP            not null,
                             CREATE_MANID         INTEGER              not null,
                             MODIFY_DATE          TIMESTAMP,
                             MODIFY_MANID         INTEGER,
                             constraint PK_TD_ZWKF_EVAL_LUNG primary key (RID),
                             constraint FK_TD_ZWKF_EVAL_LUNG1 foreign key (MAIN_ID) references TD_ZWKF_EVAL_MAIN (RID)
                         )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>207</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_LUNG_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_LUNG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
                END IF;
            END;
            ]]>
        </sql>
        <ver>208</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWKF_EVAL_BREATHE';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE
                        'create table TD_ZWKF_EVAL_BREATHE
                         (
                             RID                  INTEGER              not null,
                             MAIN_ID              INTEGER,
                             IF_BREATHE           NUMBER(1),
                             MIP                  NUMBER(5,2),
                             MEP                  NUMBER(5,2),
                             MVV                  NUMBER(5,2),
                             MSVC                 NUMBER(5,2),
                             CREATE_DATE          TIMESTAMP            not null,
                             CREATE_MANID         INTEGER              not null,
                             MODIFY_DATE          TIMESTAMP,
                             MODIFY_MANID         INTEGER,
                             constraint PK_TD_ZWKF_EVAL_BREATHE primary key (RID),
                             constraint FK_TD_ZWKF_EVAL_BREATHE1 foreign key (MAIN_ID) references TD_ZWKF_EVAL_MAIN (RID)
                         )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>209</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_BREATHE_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_BREATHE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
                END IF;
            END;
            ]]>
        </sql>
        <ver>210</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWKF_EVAL_BLOOD';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE
                        'create table TD_ZWKF_EVAL_BLOOD
                         (
                             RID                  INTEGER              not null,
                             MAIN_ID              INTEGER,
                             IF_BLOOD_ANALY       NUMBER(1),
                             PAO                  NUMBER(5,2),
                             ALVEOLAR_PAO         NUMBER(5,2),
                             SAO                  NUMBER(5,2),
                             OI                   NUMBER(5,2),
                             PACO2                NUMBER(5,2),
                             CREATE_DATE          TIMESTAMP            not null,
                             CREATE_MANID         INTEGER              not null,
                             MODIFY_DATE          TIMESTAMP,
                             MODIFY_MANID         INTEGER,
                             constraint PK_TD_ZWKF_EVAL_BLOOD primary key (RID),
                             constraint FK_TD_ZWKF_EVAL_BLOOD1 foreign key (MAIN_ID) references TD_ZWKF_EVAL_MAIN (RID)
                         )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>211</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_BLOOD_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_BLOOD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
                END IF;
            END;
            ]]>
        </sql>
        <ver>212</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('EVAL_DOCTOR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD EVAL_DOCTOR NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>213</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_TABLE')
                AND COLUMN_NAME = UPPER('RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_TABLE ADD RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>214</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_EVAL_TABLE3'
                AND TABLE_NAME = 'TD_ZWKF_EVAL_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_EVAL_TABLE add constraint FK_TD_ZWKF_EVAL_TABLE3 foreign key (RST_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>215</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_SHARE_RECOVERY_PSN')
                AND COLUMN_NAME = UPPER('STOP_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_SHARE_RECOVERY_PSN ADD STOP_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>216</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_FOLLOW_UP')
                AND COLUMN_NAME = UPPER('LIVE_STATE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD LIVE_STATE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>217</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZWKF_FOLLOW_UP'
                AND CONSTRAINT_NAME = 'FK_TD_ZWKF_FOLLOW_UP6';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD CONSTRAINT FK_TD_ZWKF_FOLLOW_UP6 FOREIGN KEY (LIVE_STATE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>218</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_FOLLOW_UP')
                AND COLUMN_NAME = UPPER('LIVE_STATE_RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD LIVE_STATE_RMK NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>219</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_FOLLOW_UP')
                AND COLUMN_NAME = UPPER('FLOWUP_USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD FLOWUP_USER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>220</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZWKF_FOLLOW_UP'
                AND CONSTRAINT_NAME = 'FK_TD_ZWKF_FOLLOW_UP7';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD CONSTRAINT FK_TD_ZWKF_FOLLOW_UP7 FOREIGN KEY (FLOWUP_USER_ID) REFERENCES TD_ZWKF_PSNINFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>221</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_FOLLOW_UP')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_FOLLOW_UP ADD RMK NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>222</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_REFER')
                AND COLUMN_NAME = UPPER('REFER_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD REFER_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>223</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZWKF_REFER'
                AND CONSTRAINT_NAME = 'FK_TD_ZWKF_REFER3';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD CONSTRAINT FK_TD_ZWKF_REFER3 FOREIGN KEY (REFER_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>224</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_REFER')
                AND COLUMN_NAME = UPPER('REFER_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD REFER_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>225</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZWKF_REFER'
                AND CONSTRAINT_NAME = 'FK_TD_ZWKF_REFER4';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD CONSTRAINT FK_TD_ZWKF_REFER4 FOREIGN KEY (REFER_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>226</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_REFER')
                AND COLUMN_NAME = UPPER('CURR_UNIT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD CURR_UNIT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>227</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE TABLE_NAME = 'TD_ZWKF_REFER'
                AND CONSTRAINT_NAME = 'FK_TD_ZWKF_REFER5';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD CONSTRAINT FK_TD_ZWKF_REFER5 FOREIGN KEY (CURR_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>228</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_REFER')
                AND COLUMN_NAME = UPPER('CURR_UNIT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD CURR_UNIT NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>229</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('PRESCR_DOCTOR_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD PRESCR_DOCTOR_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>230</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_EVAL_MAIN14'
                AND TABLE_NAME = 'TD_ZWKF_EVAL_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_EVAL_MAIN add constraint FK_TD_ZWKF_EVAL_MAIN14 foreign key (PRESCR_DOCTOR_ID) references TD_ZWKF_PSNINFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>231</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWKF_EVAL_LUNG_SUB';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE
                        'create table TD_ZWKF_EVAL_LUNG_SUB
                        (
                           RID                  INTEGER              not null,
                           MAIN_ID              INTEGER,
                           FVC_VAL              NUMBER(8,3),
                           FVC_RATE             NUMBER(8,3),
                           FEV_VAL              NUMBER(8,3),
                           FEV_RATE             NUMBER(8,3),
                           FEF_50_VAL           NUMBER(8,3),
                           FEF_50_RATE          NUMBER(8,3),
                           FEF_75_VAL           NUMBER(8,3),
                           FEF_75_RATE          NUMBER(8,3),
                           MMEF_VAL             NUMBER(8,3),
                           MMEF_RATE            NUMBER(8,3),
                           MIP_VAL              NUMBER(8,3),
                           MIP_RATE             NUMBER(8,3),
                           MEP_VAL              NUMBER(8,3),
                           MEP_RATE             NUMBER(8,3),
                           RESULT               NUMBER(8),
                           CREATE_DATE          TIMESTAMP            not null,
                           CREATE_MANID         INTEGER              not null,
                           MODIFY_DATE          TIMESTAMP,
                           MODIFY_MANID         INTEGER,
                           constraint PK_TD_ZWKF_EVAL_LUNG_SUB primary key (RID),
                           constraint FK_TD_ZWKF_EVAL_LUNG_SUB1 foreign key (MAIN_ID) references TD_ZWKF_EVAL_MAIN (RID)
                        )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>232</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWKF_EVAL_LUNG_SUB_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_EVAL_LUNG_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
                END IF;
            END;
            ]]>
        </sql>
        <ver>233</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('EVAL_DOCTOR_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD EVAL_DOCTOR_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>234</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_EVAL_MAIN13'
                AND TABLE_NAME = 'TD_ZWKF_EVAL_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_EVAL_MAIN add constraint FK_TD_ZWKF_EVAL_MAIN13 foreign key (EVAL_DOCTOR_ID) references TD_ZWKF_PSNINFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>235</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_RECOVERY')
                AND COLUMN_NAME = UPPER('IF_SATISFIED');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_RECOVERY ADD IF_SATISFIED NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>236</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_RECOVERY')
                AND COLUMN_NAME = UPPER('RECOVERY_DOC_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_RECOVERY ADD RECOVERY_DOC_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>237</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_EVAL_RECOVERY2'
                AND TABLE_NAME = 'TD_ZWKF_EVAL_RECOVERY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_EVAL_RECOVERY add constraint FK_TD_ZWKF_EVAL_RECOVERY2 foreign key (RECOVERY_DOC_ID) references TD_ZWKF_PSNINFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>238</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PSNINFO')
                AND COLUMN_NAME = UPPER('SIGN_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PSNINFO ADD SIGN_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>239</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_RECOVERY')
                AND COLUMN_NAME = UPPER('SIGN_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_RECOVERY ADD SIGN_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>240</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_RECOVERY')
                AND COLUMN_NAME = UPPER('ACUPOINT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_RECOVERY ADD ACUPOINT NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>241</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_REFER')
                AND COLUMN_NAME = UPPER('RCV_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_REFER ADD RCV_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>242</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_LUNG_SUB')
                AND COLUMN_NAME = UPPER('RESULT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_LUNG_SUB ADD RESULT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>243</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWKF_EVAL_LUNG_SUB2'
                AND TABLE_NAME = 'TD_ZWKF_EVAL_LUNG_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZWKF_EVAL_LUNG_SUB add constraint FK_TD_ZWKF_EVAL_LUNG_SUB2 foreign key (RESULT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>244</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('DIE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PATIENT_INFO ADD DIE_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>245</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_TABLE')
                AND COLUMN_NAME = UPPER('EVAL_RST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_TABLE ADD EVAL_RST NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>246</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWKF_SIXMINUTE';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE
                        'create table TD_ZWKF_SIXMINUTE
                            (
                               RID                  INTEGER              not null,
                               ID                   NVARCHAR2(20),
                               PSN_NAME             NVARCHAR2(20),
                               SEX                  NUMBER(1),
                               LINK_TEL             NVARCHAR2(20),
                               UNIT_NAME            NVARCHAR2(100),
                               RESULT               NUMBER(6,1),
                               RPT_DATE             DATE,
                               RPT_URL              NVARCHAR2(200),
                               RPT_PATH             NVARCHAR2(50),
                               CREATE_DATE          TIMESTAMP            not null,
                               CREATE_MANID         INTEGER              not null,
                               MODIFY_DATE          TIMESTAMP,
                               MODIFY_MANID         INTEGER,
                               constraint PK_TD_ZWKF_SIXMINUTE primary key (RID)
                            )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>247</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWKF_SIXMINUTE_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWKF_SIXMINUTE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
                END IF;
            END;
            ]]>
        </sql>
        <ver>248</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZWKF_LUNG_PRD';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZWKF_LUNG_PRD (
              RID INTEGER NOT NULL,
              RPT_TYPE NUMBER(2),
              PSN_NAME NVARCHAR2 (20),
              SEX NUMBER(1),
              LINK_TEL NVARCHAR2 (20),
              IDC NVARCHAR2 (50),
              RPT_DATE DATE,
              RPT_PATH NVARCHAR2 (50),
              RPT_JSON CLOB,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZWKF_LUNG_PRD PRIMARY KEY (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>249</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZWKF_LUNG_PRD';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>250</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_ORG')
                AND COLUMN_NAME = UPPER('TOTAL_PSN_NUMS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_ORG ADD TOTAL_PSN_NUMS NUMBER(5)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>251</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_EVAL_MAIN')
                AND COLUMN_NAME = UPPER('SIGN_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_EVAL_MAIN ADD SIGN_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>252</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWKF_PSNINFO')
                AND COLUMN_NAME = UPPER('DUTY_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWKF_PSNINFO ADD DUTY_STATE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>253</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-cfb-kfzd 尘肺病康复相关模块升级 -->