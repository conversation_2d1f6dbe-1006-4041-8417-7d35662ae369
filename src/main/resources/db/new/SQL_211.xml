<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
	<description>重点职业监测迟报、漏报调查表</description>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DIAG_RPT_INVEST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_DIAG_RPT_INVEST
			(
			   RID                  INTEGER              not null,
			   RPT_NO               NVARCHAR2(50),
			   ZONE_ID              INTEGER,
			   UNIT_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   IDC                  NVARCHAR2(50),
			   DIAG_DATE            DATE,
			   DIAG_1_DATE          DATE,
			   DIAG_2_DATE          DATE,
			   DIAG_3_DATE          DATE,
			   ZYB_DIS_TYPE_ID      INTEGER,
			   ZYB_TYPE_ID          INTEGER,
			   ZYB_DIS_NAME         NVARCHAR2(50),
			   IF_LATE              NUMBER(1),
			   LATE_RSN             NVARCHAR2(100),
			   IF_LACK              NUMBER(1),
			   LACK_RSN             NVARCHAR2(100),
			   RMK                  NVARCHAR2(200),
			   CHECK_UNIT_NAME      NVARCHAR2(50),
			   CHECK_PSN            NVARCHAR2(50),
			   CHECK_DATE           DATE,
			   LINKTEL              NVARCHAR2(20),
			   STATE                NUMBER(1),
			   CREATE_UNIT_ID       INTEGER,
			   CHECK_UNIT_ID        INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_DIAG_RPT_INVEST primary key (RID),
			 constraint FK_TD_ZW_DIAG_RPT_INVEST1 foreign key (ZONE_ID)
				  references TS_ZONE (RID),
			constraint FK_TD_ZW_DIAG_RPT_INVEST2 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
			constraint FK_TD_ZW_DIAG_RPT_INVEST3 foreign key (ZYB_DIS_TYPE_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_DIAG_RPT_INVEST4 foreign key (ZYB_TYPE_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_DIAG_RPT_INVEST5 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_DIAG_RPT_INVEST6 foreign key (CREATE_UNIT_ID)
				  references TS_UNIT (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>1</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAG_RPT_INVEST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_DIAG_RPT_INVEST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>2</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_RPT_INVEST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_RPT_INVEST
			(
			   RID                  INTEGER              not null,
			   RPT_NO               NVARCHAR2(50),
			   ZONE_ID              INTEGER,
			   UNIT_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   IDC                  NVARCHAR2(50),
			   DIAG_DATE            DATE,
			   ZYB_DIS_TYPE_ID      INTEGER,
			   ZYB_TYPE_ID          INTEGER,
			   ZYB_DIS_NAME         NVARCHAR2(50),
			   IF_LATE              NUMBER(1),
			   LATE_RSN             NVARCHAR2(100),
			   IF_LACK              NUMBER(1),
			   LACK_RSN             NVARCHAR2(100),
			   RMK                  NVARCHAR2(200),
			   CHECK_UNIT_NAME      NVARCHAR2(50),
			   CHECK_PSN            NVARCHAR2(50),
			   CHECK_DATE           DATE,
			   LINKTEL              NVARCHAR2(20),
			   STATE                NUMBER(1),
			   CREATE_UNIT_ID       INTEGER,
			   CHECK_UNIT_ID        INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_TJORG_RPT_INVEST primary key (RID),
			constraint FK_TD_ZW_TJORG_RPT_INVEST1 foreign key (ZONE_ID)
				  references TS_ZONE (RID),
			 constraint FK_TD_ZW_TJORG_RPT_INVEST2 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_TJORG_RPT_INVEST3 foreign key (ZYB_DIS_TYPE_ID)
				  references TS_SIMPLE_CODE (RID),
			constraint FK_TD_ZW_TJORG_RPT_INVEST4 foreign key (ZYB_TYPE_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_TJORG_RPT_INVEST5 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			constraint FK_TD_ZW_TJORG_RPT_INVEST6 foreign key (CREATE_UNIT_ID)
				  references TS_UNIT (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>3</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_RPT_INVEST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_RPT_INVEST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>4</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_YSCF_RPT_INVEST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_YSCF_RPT_INVEST
			(
			   RID                  INTEGER              not null,
			   RPT_NO               NVARCHAR2(50),
			   ZONE_ID              INTEGER,
			   UNIT_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   IDC                  NVARCHAR2(50),
			   CHEST_NO             NVARCHAR2(50),
			   DUST_YEAR            NUMBER(3,1),
			   CHEST_RST_ID         INTEGER,
			   BHK_RST_ID           INTEGER,
			   CHEST_LEVEL_ID       INTEGER,
			   EXPERT_CHEST_ID      INTEGER,
			   EXPERT_RST_ID        INTEGER,
			   BHK_RPT_PATH         NVARCHAR2(100),
			   CHEST_PATH           NVARCHAR2(100),
			   PROV_CHEST_ID        INTEGER,
			   PROV_ADVICE          NUMBER(1),
			   PROV_OTHER_RMK       NVARCHAR2(100),
			   CHECK_UNIT_NAME      NVARCHAR2(50),
			   CHECK_PSN            NVARCHAR2(50),
			   CHECK_DATE           DATE,
			   LINKTEL              NVARCHAR2(20),
			   PROV_UNIT_NAME       NVARCHAR2(50),
			   PROV_CHECK_PSN       NVARCHAR2(50),
			   PROV_CHECK_DATE      DATE,
			   PROV_LINKTEL         NVARCHAR2(20),
			   STATE                NUMBER(1),
			   CHECK_UNIT_ID        INTEGER,
			   PROV_UNIT_ID         INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_YSCF_RPT_INVEST primary key (RID),
			constraint FK_TD_ZW_YSCF_RPT_INVEST1 foreign key (ZONE_ID)
				  references TS_ZONE (RID),
			constraint FK_TD_ZW_YSCF_RPT_INVEST2 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_YSCF_RPT_INVEST3 foreign key (CHEST_RST_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_YSCF_RPT_INVEST4 foreign key (BHK_RST_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_YSCF_RPT_INVEST5 foreign key (CHEST_LEVEL_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_YSCF_RPT_INVEST6 foreign key (EXPERT_CHEST_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_YSCF_RPT_INVEST7 foreign key (EXPERT_RST_ID)
				  references TS_SIMPLE_CODE (RID),
			constraint FK_TD_ZW_YSCF_RPT_INVEST8 foreign key (PROV_CHEST_ID)
				  references TS_SIMPLE_CODE (RID),
			constraint FK_TD_ZW_YSCF_RPT_INVEST9 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_YSCF_RPT_INVEST10 foreign key (PROV_UNIT_ID)
				  references TS_UNIT (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>5</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_YSCF_RPT_INVEST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_YSCF_RPT_INVEST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>6</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_YSZS_RPT_INVEST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_YSZS_RPT_INVEST
			(
			   RID                  INTEGER              not null,
			   RPT_NO               NVARCHAR2(50),
			   ZONE_ID              INTEGER,
			   UNIT_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   IDC                  NVARCHAR2(50),
			   HEAR_NO              NVARCHAR2(50),
			   NOISE_YEAR           NUMBER(3,1),
			   HEAR_RST             INTEGER,
			   BHK_RST_ID           INTEGER,
			   EXPERT_HEAR_RST      INTEGER,
			   EXPERT_RST_ID        INTEGER,
			   BHK_RPT_PATH         NVARCHAR2(100),
			   HEAR_PATH            NVARCHAR2(100),
			   PROV_HEAR_RST        INTEGER,
			   PROV_ADVICE          NUMBER(1),
			   PROV_OTHER_RMK       NVARCHAR2(100),
			   CHECK_UNIT_NAME      NVARCHAR2(50),
			   CHECK_PSN            NVARCHAR2(50),
			   CHECK_DATE           DATE,
			   LINKTEL              NVARCHAR2(20),
			   PROV_UNIT_NAME       NVARCHAR2(50),
			   PROV_CHECK_PSN       NVARCHAR2(50),
			   PROV_CHECK_DATE      DATE,
			   PROV_LINKTEL         NVARCHAR2(20),
			   STATE                NUMBER(1),
			   CHECK_UNIT_ID        INTEGER,
			   PROV_UNIT_ID         INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_YSZS_RPT_INVEST primary key (RID),
			 constraint FK_TD_ZW_YSZS_RPT_INVEST1 foreign key (ZONE_ID)
				  references TS_ZONE (RID),
			 constraint FK_TD_ZW_YSZS_RPT_INVEST2 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_YSZS_RPT_INVEST3 foreign key (BHK_RST_ID)
				  references TS_SIMPLE_CODE (RID),
			constraint FK_TD_ZW_YSZS_RPT_INVEST4 foreign key (EXPERT_RST_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_YSZS_RPT_INVEST5 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_YSZS_RPT_INVEST6 foreign key (PROV_UNIT_ID)
				  references TS_UNIT (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>7</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_YSZS_RPT_INVEST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_YSZS_RPT_INVEST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>8</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_NOT_DIAG_INVEST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_NOT_DIAG_INVEST
			(
			   RID                  INTEGER              not null,
			   ZONE_ID              INTEGER,
			   UNIT_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   IDC                  NVARCHAR2(50),
			   DIAG_DATE            DATE,
			   ZYB_DIS_TYPE_ID      INTEGER,
			   ZYB_TYPE_ID          INTEGER,
			   ZYB_DIS_NAME         NVARCHAR2(50),
			   INVEST_DATE          DATE,
			   NOT_DIAG_RSN_ID      INTEGER,
			   OTHER_RSN            NVARCHAR2(100),
			   RMK                  NVARCHAR2(100),
			   CHECK_UNIT_NAME      NVARCHAR2(50),
			   CHECK_PSN            NVARCHAR2(50),
			   CHECK_DATE           DATE,
			   LINKTEL              NVARCHAR2(20),
			   STATE                NUMBER(1),
			   CREATE_UNIT_ID       INTEGER,
			   CHECK_UNIT_ID        INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_NOT_DIAG_INVEST primary key (RID),
			 constraint FK_TD_ZW_NOT_DIAG_INVEST1 foreign key (ZONE_ID)
				  references TS_ZONE (RID),
			 constraint FK_TD_ZW_NOT_DIAG_INVEST2 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_NOT_DIAG_INVEST3 foreign key (NOT_DIAG_RSN_ID)
				  references TS_SIMPLE_CODE (RID),
			constraint FK_TD_ZW_NOT_DIAG_INVEST4 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_NOT_DIAG_INVEST5 foreign key (CREATE_UNIT_ID)
				  references TS_UNIT (RID),
				  constraint FK_TD_ZW_NOT_DIAG_INVEST6 foreign key (ZYB_DIS_TYPE_ID)
			  references TS_SIMPLE_CODE (RID),
			constraint FK_TD_ZW_NOT_DIAG_INVEST7 foreign key (ZYB_TYPE_ID)
			  references TS_SIMPLE_CODE (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>9</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_NOT_DIAG_INVEST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_NOT_DIAG_INVEST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>10</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>11</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD AUDIT_ADV NVARCHAR2(500)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>12</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>13</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_NOT_DIAG_INVEST8')
                AND TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD CONSTRAINT FK_TD_ZW_NOT_DIAG_INVEST8 FOREIGN KEY (CHK_PSN_ID) REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>14</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>15</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD AUDIT_ADV NVARCHAR2(500)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>16</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>17</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_TJORG_RPT_INVEST7')
                AND TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD CONSTRAINT FK_TD_ZW_TJORG_RPT_INVEST7 FOREIGN KEY (CHK_PSN_ID) REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>18</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('DATE_SOURCE');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD DATE_SOURCE NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>19</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('DATE_SOURCE');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD DATE_SOURCE NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>20</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>21</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD AUDIT_ADV NVARCHAR2(500)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>22</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>23</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_DIAG_RPT_INVEST7')
                AND TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD CONSTRAINT FK_TD_ZW_DIAG_RPT_INVEST7 FOREIGN KEY (CHK_PSN_ID) REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>24</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('CHK_UNIT_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD CHK_UNIT_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>25</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_DIAG_RPT_INVEST8')
                AND TABLE_NAME = UPPER('TD_ZW_DIAG_RPT_INVEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAG_RPT_INVEST ADD CONSTRAINT FK_TD_ZW_DIAG_RPT_INVEST8 FOREIGN KEY (CHK_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>26</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST')
                      AND COLUMN_NAME = UPPER('CHK_UNIT_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD CHK_UNIT_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>27</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_TJORG_RPT_INVEST8')
                AND TABLE_NAME = UPPER('TD_ZW_TJORG_RPT_INVEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RPT_INVEST ADD CONSTRAINT FK_TD_ZW_TJORG_RPT_INVEST8 FOREIGN KEY (CHK_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>28</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST')
                      AND COLUMN_NAME = UPPER('CHK_UNIT_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD CHK_UNIT_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>29</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_NOT_DIAG_INVEST9')
                AND TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD CONSTRAINT FK_TD_ZW_NOT_DIAG_INVEST9 FOREIGN KEY (CHK_UNIT_ID) REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>30</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZYB_DEATH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_ZYB_DEATH
				(
				   RID                  INTEGER              not null,
				   PERSON_NAME          NVARCHAR2(50),
				   IDC                  NVARCHAR2(50),
				   DIAG_DATE            DATE,
				   ZYB_DIS_TYPE_ID      INTEGER,
				   ZYB_TYPE_ID          INTEGER,
				   ZYB_DIS_NAME         NVARCHAR2(50),
				   DEATH_DATE           DATE,
				   DEATH_CHAIN_A        INTEGER,
				   DEATH_CHAIN_B        INTEGER,
				   DEATH_CHAIN_C        INTEGER,
				   DEATH_CHAIN_D        INTEGER,
				   DEATH_RSN_ID         INTEGER,
				   CHECK_UNIT_NAME      NVARCHAR2(50),
				   CHECK_PSN            NVARCHAR2(50),
				   CHECK_DATE           DATE,
				   LINKTEL              NVARCHAR2(20),
				   STATE                NUMBER(1),
				   CHECK_UNIT_ID        INTEGER,
				   CREATE_MANID         INTEGER              not null,
				   CREATE_DATE          TIMESTAMP            not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_ZW_ZYB_DEATH primary key (RID),
				   constraint FK_TD_ZW_ZYB_DEATH1 foreign key (ZYB_DIS_TYPE_ID) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH2 foreign key (ZYB_TYPE_ID) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH3 foreign key (DEATH_CHAIN_A) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH4 foreign key (DEATH_CHAIN_B) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH5 foreign key (DEATH_CHAIN_C) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH6 foreign key (DEATH_CHAIN_D) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH7 foreign key (DEATH_RSN_ID) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_ZYB_DEATH8 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID)
				)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>31</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZW_ZYB_DEATH';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
		</sql>
		<ver>32</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DEATH_RSN_ZYB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_DEATH_RSN_ZYB
				(
				   RID                  INTEGER              not null,
				   PERSON_NAME          NVARCHAR2(50),
				   IDC                  NVARCHAR2(50),
				   SEX                  NUMBER(1),
				   AGE                  NUMBER(2),
				   UNIT_NAME            NVARCHAR2(100),
				   HUZONE_ID            INTEGER,
				   HUZONE_ADDR          NVARCHAR2(200),
				   ZONE_ID              INTEGER,
				   ZONE_ADDR            NVARCHAR2(200),
				   DEATH_DATE           DATE,
				   DEATH_RSN_ID         INTEGER,
				   CHECK_UNIT_NAME      NVARCHAR2(50),
				   CHECK_PSN            NVARCHAR2(50),
				   CHECK_DATE           DATE,
				   LINKTEL              NVARCHAR2(20),
				   STATE                NUMBER(1),
				   CHECK_UNIT_ID        INTEGER,
				   CREATE_MANID         INTEGER              not null,
				   CREATE_DATE          TIMESTAMP            not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_ZW_DEATH_RSN_ZYB primary key (RID),
				   constraint FK_TD_ZW_DEATH_RSN_ZYB1 foreign key (HUZONE_ID) references TS_ZONE (RID),
				   constraint FK_TD_ZW_DEATH_RSN_ZYB2 foreign key (ZONE_ID) references TS_ZONE (RID),
				   constraint FK_TD_ZW_DEATH_RSN_ZYB3 foreign key (DEATH_RSN_ID) references TS_SIMPLE_CODE (RID),
				   constraint FK_TD_ZW_DEATH_RSN_ZYB4 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID)
				)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>33</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZW_DEATH_RSN_ZYB';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
		</sql>
		<ver>34</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_NOT_DIAG_INVEST')
                      AND COLUMN_NAME = UPPER('DATE_SOURCE');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_NOT_DIAG_INVEST ADD DATE_SOURCE NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>35</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CLINICAL_DIAG_CFB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CLINICAL_DIAG_CFB
			(
			   RID                  INTEGER              not null,
			   ZONE_ID              INTEGER,
			   UNIT_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   IDC                  NVARCHAR2(50),
			   SEX                  NUMBER(1),
			   BIRTH_DATE           DATE,
			   CFB_DIS_ID           INTEGER,
			   DIAG_DATE            DATE,
			   PERIOD_ID            INTEGER,
			   CRPT_NAME            NVARCHAR2(100),
			   DUST_YEAR            NUMBER(3,1),
			   WORK_TYPE_ID         INTEGER,
			   OTHER_WORK_NAME      NVARCHAR2(50),
			   LINK_WAY             NVARCHAR2(50),
			   CHECK_UNIT_NAME      NVARCHAR2(50),
			   CHECK_PSN            NVARCHAR2(50),
			   CHECK_DATE           DATE,
			   LINKTEL              NVARCHAR2(20),
			   STATE                NUMBER(1),
			   CHECK_UNIT_ID        INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_CLINICAL_DIAG_CFB primary key (RID),
				constraint FK_TD_ZW_CLINICAL_DIAG_CFB1 foreign key (ZONE_ID)
				  references TS_ZONE (RID),
				 constraint FK_TD_ZW_CLINICAL_DIAG_CFB2 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
				constraint FK_TD_ZW_CLINICAL_DIAG_CFB3 foreign key (CFB_DIS_ID)
				  references TS_SIMPLE_CODE (RID),
				constraint FK_TD_ZW_CLINICAL_DIAG_CFB4 foreign key (PERIOD_ID)
				  references TS_SIMPLE_CODE (RID),
				constraint FK_TD_ZW_CLINICAL_DIAG_CFB5 foreign key (WORK_TYPE_ID)
				  references TS_SIMPLE_CODE (RID),
				constraint FK_TD_ZW_CLINICAL_DIAG_CFB6 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID)
				)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>36</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZW_CLINICAL_DIAG_CFB';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1000 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
		</sql>
		<ver>37</ver>
	</sqlsentence>
</sqlsentences>
<!-- web-heth-zk-check-rpt 字段升级 -->