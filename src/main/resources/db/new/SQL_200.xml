<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>尘肺病随访</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_CRPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_CRPT (   RID                  INTEGER              not null,   CRPT_ZONE_ID         INTEGER,   CRPT_CREDIT_CODE     VARCHAR2(20),   CRPT_NAME            VARCHAR2(100),   CRPT_ADDR            VARCHAR2(200),   INDUS_TYPE_ID        INTEGER,   ECONOMY_ID           INTEGER,   CRPT_SIZE_ID         INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_<PERSON><PERSON><PERSON>          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_CRPT primary key (RID),   constraint FK_TD_ZWCF_CRPT1 foreign key (CRPT_ZONE_ID) references TS_ZONE (RID),   constraint FK_TD_ZWCF_CRPT2 foreign key (INDUS_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_CRPT3 foreign key (ECONOMY_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_CRPT4 foreign key (CRPT_SIZE_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_PATIENT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_PATIENT_INFO (   RID                  INTEGER              not null,   MAIN_CARD_ID         VARCHAR2(50),   SUB_CARD_ID          VARCHAR2(50),   PATIENT_SOURCE       NUMBER(1),   RPT_CARD_NO          VARCHAR2(50),   RPT_TYPE_ID          INTEGER,   PSN_NAME             VARCHAR2(50),   IDC                  VARCHAR2(20),   SEX                  NUMBER(1),   BIRTHDAY             DATE,   LINK_PSN_NAME        VARCHAR2(50),   LINK_ADDR            VARCHAR2(200),   LINK_PHONE           VARCHAR2(20),   CRPT_ZONE_ID         INTEGER,   CRPT_CREDIT_CODE     VARCHAR2(20),   CRPT_NAME            VARCHAR2(100),   CRPT_ADDR            VARCHAR2(200),   INDUS_TYPE_ID        INTEGER,   ECONOMY_ID           INTEGER,   CRPT_SIZE_ID         INTEGER,   ANALY_WORK_ID        INTEGER,   OTHER_WORK_NAME      VARCHAR2(50),   PNEUMSIS_TYPE_ID     INTEGER,   OTHER_PNES_NAME      VARCHAR2(50),   BEG_TCH_DUST         DATE,   TCH_DUST_YEAR        NUMBER(2),   TCH_DUST_MONTH       NUMBER(2),   DIAG_UNIT_NAME       VARCHAR2(100),   DIAG_RESP_PSN        VARCHAR2(50),   FILL_FORM_PSN        VARCHAR2(50),   FILL_LINK            VARCHAR2(50),   FILL_DATE            DATE,   DIAG_1_DATE          DATE,   DIAG_2_DATE          DATE,   DIAG_3_DATE          DATE,   IF_TB                NUMBER(1),   TB_DIAG_DATE         DATE,   IF_PUL_INFECTION     NUMBER(1),   INFECTION_DIAG_DATE  DATE,   IF_THE_PNEUM         NUMBER(1),   PNEUM_DIAG_DATE      DATE,   IF_PUL_HEART         NUMBER(1),   HEART_DIAG_DATE      DATE,   IF_LUNG_CANCER       NUMBER(1),   LUNG_DIAG_DATE       DATE,   IF_UNIT_EXISTS       NUMBER(1),   IF_WORK_INSURANCE    NUMBER(1),   IF_MEDICAL_TREAT     NUMBER(1),   WORK_LEVEL_ID        INTEGER,   IF_INJURY_TREAT      NUMBER(1),   IF_DEATH_TREAT       NUMBER(1),   IF_OTHER_TREAT       NUMBER(1),   IF_GET_MEDICAL_HELP  NUMBER(1),   IF_GET_LIVE_HELP     NUMBER(1),   DEATH_DATE           DATE,   DEATH_RSN_ID         INTEGER,   RMK                  VARCHAR2(200),   RCD_USER_ID          INTEGER,   RCD_UNIT_ID          INTEGER,   MOD_USER_ID          INTEGER,   MOD_UNIT_ID          INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_PATIENT_INFO primary key (RID),   constraint FK_TD_ZWCF_PATIENT_INFO1 foreign key (RPT_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO10 foreign key (MOD_USER_ID) references TS_USER_INFO (RID),   constraint FK_TD_ZWCF_PATIENT_INFO11 foreign key (RCD_UNIT_ID) references TS_UNIT (RID),   constraint FK_TD_ZWCF_PATIENT_INFO12 foreign key (MOD_UNIT_ID) references TS_UNIT (RID),   constraint FK_TD_ZWCF_PATIENT_INFO13 foreign key (DEATH_RSN_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO14 foreign key (WORK_LEVEL_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO3 foreign key (CRPT_ZONE_ID) references TS_ZONE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO4 foreign key (INDUS_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO5 foreign key (ECONOMY_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO6 foreign key (CRPT_SIZE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO7 foreign key (ANALY_WORK_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO8 foreign key (PNEUMSIS_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_INFO9 foreign key (RCD_USER_ID) references TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_PATIENT_FLOWUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_PATIENT_FLOWUP (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   PSN_NAME             VARCHAR2(50),   IDC                  VARCHAR2(20),   SEX                  NUMBER(1),   BIRTHDAY             DATE,   RPT_TYPE_ID          INTEGER,   FLOWUP_DATE          DATE,   LIVE_STATE           NUMBER(1),   CHECK_SOURCE_ID      INTEGER,   OTHER_CHECK_SOURCE   VARCHAR2(50),   LINK_PSN_NAME        VARCHAR2(50),   LINK_ADDR            VARCHAR2(200),   LINK_PHONE           VARCHAR2(20),   CRPT_ZONE_ID         INTEGER,   CRPT_CREDIT_CODE     VARCHAR2(20),   CRPT_NAME            VARCHAR2(100),   CRPT_ADDR            VARCHAR2(200),   INDUS_TYPE_ID        INTEGER,   ECONOMY_ID           INTEGER,   CRPT_SIZE_ID         INTEGER,   ANALY_WORK_ID        INTEGER,   OTHER_WORK_NAME      VARCHAR2(50),   PNEUMSIS_TYPE_ID     INTEGER,   OTHER_PNES_NAME      VARCHAR2(50),   BEG_TCH_DUST         DATE,   TCH_DUST_YEAR        NUMBER(2),   TCH_DUST_MONTH       NUMBER(2),   DIAG_UNIT_NAME       VARCHAR2(100),   DIAG_1_DATE          DATE,   DIAG_2_DATE          DATE,   DIAG_3_DATE          DATE,   IF_TB                NUMBER(1),   TB_DIAG_DATE         DATE,   IF_PUL_INFECTION     NUMBER(1),   INFECTION_DIAG_DATE  DATE,   IF_THE_PNEUM         NUMBER(1),   PNEUM_DIAG_DATE      DATE,   IF_PUL_HEART         NUMBER(1),   HEART_DIAG_DATE      DATE,   IF_LUNG_CANCER       NUMBER(1),   LUNG_DIAG_DATE       DATE,   IF_UNIT_EXISTS       NUMBER(1),   IF_WORK_INSURANCE    NUMBER(1),   IF_MEDICAL_TREAT     NUMBER(1),   WORK_LEVEL_ID        INTEGER,   IF_INJURY_TREAT      NUMBER(1),   IF_DEATH_TREAT       NUMBER(1),   IF_OTHER_TREAT       NUMBER(1),   IF_GET_MEDICAL_HELP  NUMBER(1),   IF_GET_LIVE_HELP     NUMBER(1),   DEATH_DATE           DATE,   DEATH_RSN_ID         INTEGER,   RMK                  VARCHAR2(200),   FLOWUP_USER_ID       INTEGER,   FLOWUP_UNIT_ID       INTEGER,   DEL_MARK             NUMBER(1),   STATE_MARK           NUMBER(1),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_PATIENT_FLOWUP primary key (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP1 foreign key (MAIN_ID) references TD_ZWCF_PATIENT_INFO (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP10 foreign key (FLOWUP_USER_ID) references TS_USER_INFO (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP11 foreign key (FLOWUP_UNIT_ID) references TS_UNIT (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP12 foreign key (RPT_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP2 foreign key (INDUS_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP3 foreign key (ECONOMY_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP4 foreign key (CRPT_SIZE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP5 foreign key (ANALY_WORK_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP6 foreign key (PNEUMSIS_TYPE_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP7 foreign key (WORK_LEVEL_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP8 foreign key (DEATH_RSN_ID) references TS_SIMPLE_CODE (RID),   constraint FK_TD_ZWCF_PATIENT_FLOWUP9 foreign key (CRPT_ZONE_ID) references TS_ZONE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_FLOWUP_PLAN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_FLOWUP_PLAN (   RID                  INTEGER              not null,   PLAN_YEAR            NUMBER(4),   TASK_NUM             NUMBER(8),   RMK                  VARCHAR2(200),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_FLOWUP_PLAN primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_FLOWUP_TASK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_FLOWUP_TASK (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   ZONE_ID              INTEGER,   TASK_NUM             NUMBER(6),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_FLOWUP_TASK primary key (RID),   constraint FK_TD_ZWCF_FLOWUP_TASK1 foreign key (MAIN_ID) references TD_ZWCF_FLOWUP_PLAN (RID),   constraint FK_TD_ZWCF_FLOWUP_TASK2 foreign key (ZONE_ID) references TS_ZONE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_CRPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_CRPT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_PATIENT_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_PATIENT_INFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_PATIENT_FLOWUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_PATIENT_FLOWUP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_FLOWUP_PLAN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_FLOWUP_PLAN_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_FLOWUP_TASK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_FLOWUP_TASK_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_LOGIN_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_LOGIN_INFO (   RID                  INTEGER              not null,   OPEN_ID              VARCHAR2(50),   USER_ID              INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_LOGIN_INFO primary key (RID),   constraint AK_TD_ZWCF_LOGIN_INFO1 unique (OPEN_ID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_LOGIN_LIMIT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_LOGIN_LIMIT (   RID                  INTEGER              not null,   OPEN_ID              VARCHAR2(50),   LG_IP                VARCHAR2(50),   CTU_FAIL_TIMES       NUMBER(2)            default 0,   LAST_LG_TIME         TIMESTAMP,   LG_NAME              VARCHAR2(200),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_LOGIN_LIMIT primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_LOGIN_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_LOGIN_INFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_LOGIN_LIMIT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_LOGIN_LIMIT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('BACK_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD BACK_RSN VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWCF_PATIENT_FLOWUP13'
                AND TABLE_NAME = 'TD_ZWCF_PATIENT_FLOWUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD CONSTRAINT FK_TD_ZWCF_PATIENT_FLOWUP13 FOREIGN KEY (CHECK_SOURCE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('CRPT_ZONE_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD CRPT_ZONE_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('CRPT_ZONE_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD CRPT_ZONE_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RPT_TYPE_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD RPT_TYPE_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('RPT_TYPE_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD RPT_TYPE_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('ECO_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD ECO_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('ECO_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD ECO_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('INDUS_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD INDUS_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('INDUS_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD INDUS_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SIZE_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SIZE_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SIZE_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SIZE_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('ANALY_WORK_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD ANALY_WORK_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('ANALY_WORK_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD ANALY_WORK_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_WORK_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD OTHER_WORK_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PNE_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD PNE_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PNE_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD PNE_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_PNE_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD OTHER_PNE_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('DEATH_RSN_COD_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD DEATH_RSN_COD_GJ VARCHAR2(20)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('DEATH_RSN_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD DEATH_RSN_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('OTHER_DEATH_NAM_GJ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD OTHER_DEATH_NAM_GJ VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('CRPT_ZONE_NAM_GJ');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO MODIFY CRPT_ZONE_NAM_GJ VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_SUPPORT_FLOWUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_SUPPORT_FLOWUP (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   SUPPORT_ID           INTEGER,   OTHER_DESC           VARCHAR2(100),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_SUPPORT_FLOWUP primary key (RID),   constraint FK_TD_ZWCF_SUPPORT_FLOWUP1 foreign key (MAIN_ID)     references TD_ZWCF_PATIENT_FLOWUP (RID),   constraint FK_TD_ZWCF_SUPPORT_FLOWUP2 foreign key (SUPPORT_ID)     references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_SUPPORT_FLOWUP_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_SUPPORT_FLOWUP_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_SUPPORT_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_SUPPORT_INFO (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   SUPPORT_ID           INTEGER,   OTHER_DESC           VARCHAR2(100),   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_SUPPORT_INFO primary key (RID),   constraint FK_TD_ZWCF_SUPPORT_INFO1 foreign key (MAIN_ID)     references TD_ZWCF_PATIENT_INFO (RID),   constraint FK_TD_ZWCF_SUPPORT_INFO2 foreign key (SUPPORT_ID)     references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_SUPPORT_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_SUPPORT_INFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>

    <!-- 尘肺病随访-尘肺病人随访信息 -->
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('PSN_LINK_WAY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD PSN_LINK_WAY VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DIRECT_DEATH_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DIRECT_DEATH_RSN VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('INDIRECT_DEATH_RSN2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD INDIRECT_DEATH_RSN2 VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('ORIGIN_DEATH_RSN2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD ORIGIN_DEATH_RSN2 VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IF_CRPT_INDEMNITY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD IF_CRPT_INDEMNITY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IF_URBAN_RURAL_INSURE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD IF_URBAN_RURAL_INSURE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IF_BIG_DIS_INSURE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD IF_BIG_DIS_INSURE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IF_OTHER_TREAT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD IF_OTHER_TREAT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('MEDICAL_IN_INSURE_RATIO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD MEDICAL_IN_INSURE_RATIO NUMBER(4,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('MEDICAL_OUT_INSURE_RATIO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD MEDICAL_OUT_INSURE_RATIO NUMBER(4,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('SUBSISTENCE_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD SUBSISTENCE_CASE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('SUBSISTENCE_AMO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD SUBSISTENCE_AMO NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('NORMAL_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD NORMAL_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('NORMAL_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD NORMAL_ADDR VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('FLOWUP_PSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD FLOWUP_PSN VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <!-- 尘肺病随访-尘肺病人档案信息 -->
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('PSN_LINK_WAY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD PSN_LINK_WAY VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('LIVE_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD LIVE_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('DIRECT_DEATH_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD DIRECT_DEATH_RSN VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('INDIRECT_DEATH_RSN2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD INDIRECT_DEATH_RSN2 VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('ORIGIN_DEATH_RSN2');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD ORIGIN_DEATH_RSN2 VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DIAG_AGE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DIAG_AGE NUMBER(3)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM NUMBER;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = 'FK_TD_ZWCF_PATIENT_FLOWUP14'
            AND TABLE_NAME = 'TD_ZWCF_PATIENT_FLOWUP';
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP  ADD constraint FK_TD_ZWCF_PATIENT_FLOWUP14 foreign key (NORMAL_ZONE_ID)      references TS_ZONE (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_INFO_SOURCE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_INFO_SOURCE (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   SOURCE_ID            INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZWCF_INFO_SOURCE primary key (RID),   constraint FK_TD_ZWCF_INFO_SOURCE1 foreign key (MAIN_ID) references TD_ZWCF_PATIENT_FLOWUP (RID),   constraint FK_TD_ZWCF_INFO_SOURCE2 foreign key (SOURCE_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_INFO_SOURCE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_INFO_SOURCE_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('NOW_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD NOW_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM NUMBER;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = 'FK_TD_ZWCF_PATIENT_FLOWUP15'
            AND TABLE_NAME = 'TD_ZWCF_PATIENT_FLOWUP';
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP  ADD constraint FK_TD_ZWCF_PATIENT_FLOWUP15 foreign key (NOW_ZONE_ID)      references TS_ZONE (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('NOW_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD NOW_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('CREDIT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD CREDIT_CODE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DISABLE_LEVEL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DISABLE_LEVEL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('EMPLOY_INJURY_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD EMPLOY_INJURY_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IF_COMPLI_PNEUM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD IF_COMPLI_PNEUM NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('COMPLI_PNEUM_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD COMPLI_PNEUM_NAME NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IF_COPD');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD IF_COPD NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('COPD_DIAG_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD COPD_DIAG_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DEATH_CHAIN_A');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DEATH_CHAIN_A NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DEATH_CHAIN_B');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DEATH_CHAIN_B NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DEATH_CHAIN_C');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DEATH_CHAIN_C NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('FILL_PSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD FILL_PSN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('FILL_LINK_TEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD FILL_LINK_TEL NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('FILL_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD FILL_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DEATH_CHAIN_D');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DEATH_CHAIN_D NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWCF_PATIENT_FLOWUP16'
                AND TABLE_NAME = 'TD_ZWCF_PATIENT_FLOWUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP  ADD constraint FK_TD_ZWCF_PATIENT_FLOWUP16 foreign key (DISABLE_LEVEL_ID)      references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_RECOVERY_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_RECOVERY_RCD (RID                  INTEGER              not null,MAIN_ID              INTEGER,FST_RECOVR_DATE      DATE,LST_RECOVR_DATE      DATE,RECOVR_TIMES         NUMBER(3),RECOVR_AVG_TIME      NUMBER(3,1),FST_RECOVR_BLOOD     NVARCHAR2(50),LST_RECOVR_BLOOD     NVARCHAR2(50),FST_RECOVR_PSY_SCALE NVARCHAR2(50),LST_RECOVR_PSY_SCALE NVARCHAR2(50),FST_RECOVR_DAILY     NVARCHAR2(50),LST_RECOVR_DAILY     NVARCHAR2(50),CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZWCF_RECOVERY_RCD primary key (RID),constraint FK_TD_ZWCF_RECOVERY_RCD1 foreign key (MAIN_ID) references TD_ZWCF_PATIENT_FLOWUP (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_RECOVERY_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_RECOVERY_RCD_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_RECOVERY_WAY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_RECOVERY_WAY (RID                  INTEGER              not null,MAIN_ID              INTEGER,RECOVERY_WAY_ID      INTEGER,CREATE_DATE          TIMESTAMP            not null,CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZWCF_RECOVERY_WAY primary key (RID),constraint FK_TD_ZWCF_RECOVERY_WAY1 foreign key (MAIN_ID) references TD_ZWCF_RECOVERY_RCD (RID),constraint FK_TD_ZWCF_RECOVERY_WAY2 foreign key (RECOVERY_WAY_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_RECOVERY_WAY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_RECOVERY_WAY_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD ANNEX_PATH NVARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('LINKMAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD LINKMAN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('POSTALCODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD POSTALCODE NVARCHAR2(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('LINKMAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD LINKMAN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('POSTALCODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD POSTALCODE NVARCHAR2(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_INJURY_TREAT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD IF_INJURY_TREAT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_CRPT_INDEMNITY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD IF_CRPT_INDEMNITY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_URBAN_RURAL_INSURE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD IF_URBAN_RURAL_INSURE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_BIG_DIS_INSURE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD IF_BIG_DIS_INSURE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('IF_OTHER_TREAT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD IF_OTHER_TREAT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('MEDICAL_IN_INSURE_RATIO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD MEDICAL_IN_INSURE_RATIO NUMBER(4,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('MEDICAL_OUT_INSURE_RATIO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD MEDICAL_OUT_INSURE_RATIO NUMBER(4,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SUBSISTENCE_CASE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SUBSISTENCE_CASE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SUBSISTENCE_AMO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SUBSISTENCE_AMO NUMBER(9,1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NORMAL_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD NORMAL_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NORMAL_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD NORMAL_ADDR VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('FLOWUP_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD FLOWUP_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('FLOWUP_PSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD FLOWUP_PSN VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZWCF_ARCH_INFO_SRC';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZWCF_ARCH_INFO_SRC
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SOURCE_ID            INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZWCF_ARCH_INFO_SRC primary key (RID),
                   constraint FK_TD_ZWCF_ARCH_INFO_SRC1 foreign key (MAIN_ID) references TD_ZWCF_PATIENT_INFO (RID),
                   constraint FK_TD_ZWCF_ARCH_INFO_SRC2 foreign key (SOURCE_ID) references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZWCF_ARCH_INFO_SRC_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_ARCH_INFO_SRC_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_CFBH_DOWN_RCD';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_CFBH_DOWN_RCD
                       (
                           RID                  INTEGER              not null,
                           PLAT_ID              INTEGER,
                           IDC                  NVARCHAR2(20),
                           STATE                NUMBER(1),
                           DOWN_DATE            DATE,
                           ERR_MSG              NVARCHAR2(2000),
                           MODIFY_DATE          TIMESTAMP,
                           MODIFY_MANID         INTEGER,
                           CREATE_DATE          TIMESTAMP            not null,
                           CREATE_MANID         INTEGER              not null,
                           constraint PK_TD_CFBH_DOWN_RCD primary key (RID)
                       )';
                END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_CFBH_DOWN_RCD_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_CFBH_DOWN_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NOW_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD NOW_ZONE_ID INTEGER';
              END IF;
            END;
            ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('NOW_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD NOW_ADDR NVARCHAR2(100)';
              END IF;
            END;
            ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SYNC_KF_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SYNC_KF_STATE NUMBER(1) default 0';
              END IF;
            END;
            ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SYNC_KF_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SYNC_KF_DATE DATE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_INFO')
                AND COLUMN_NAME = UPPER('SYNC_KF_ERR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_INFO ADD SYNC_KF_ERR_MSG NVARCHAR2(1000)';
              END IF;
            END;
            ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWCF_KF_PG';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZWCF_KF_PG
                       (
                            RID                  INTEGER              not null,
                            MAIN_ID              INTEGER,
                            PG_TIME              DATE,
                            PG_PDF_PATH          NVARCHAR2(200),
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            constraint PK_TD_ZWCF_KF_PG primary key (RID),
                            constraint FK_TD_ZWCF__FK_TD_ZWC_TD_ZWCF_ foreign key (MAIN_ID) references TD_ZWCF_PATIENT_INFO (RID)
                       )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWCF_KF_PG_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_KF_PG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>115</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWCF_KF_CF';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZWCF_KF_CF
                       (
                            RID                  INTEGER              not null,
                            MAIN_ID              INTEGER,
                            OPENER_DATE          DATE,
                            CF_PDF_PATH          NVARCHAR2(200),
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            constraint PK_TD_ZWCF_KF_CF primary key (RID),
                            constraint FK_TD_ZWCF_KF_CF1 foreign key (MAIN_ID) references TD_ZWCF_PATIENT_INFO (RID)
                       )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>116</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWCF_KF_CF_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_KF_CF_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>117</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWCF_KF_CF_OPER';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZWCF_KF_CF_OPER
                       (
                            RID                  INTEGER              not null,
                            MAIN_ID              INTEGER,
                            OPER_DATE            DATE,
                            OPER_PDF_PATH        NVARCHAR2(200),
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            constraint PK_TD_ZWCF_KF_CF_OPER primary key (RID),
                            constraint FK_TD_ZWCF_KF_CF_OPER1 foreign key (MAIN_ID) references TD_ZWCF_KF_CF (RID)
                       )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>118</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWCF_KF_CF_OPER_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_KF_CF_OPER_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>119</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO NUM
                FROM USER_TABLES
                WHERE TABLE_NAME = 'TD_ZWCF_KF_RCD';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZWCF_KF_RCD
                       (
                            RID                  INTEGER              not null,
                            MAIN_ID              INTEGER,
                            YEAR                 NUMBER(4),
                            AREA                 NVARCHAR2(150),
                            HOS_NAME             NVARCHAR2(150),
                            FIRST_RECVERY_TIME   DATE,
                            LAST_RECVERY_TIME    DATE,
                            HAS_APPARAT_USRECOVERY NUMBER(1),
                            HAS_RECOVERY         NUMBER(1),
                            RECOVERY_NUM         INTEGER,
                            RECOVERY_TIME        INTEGER,
                            RECOVERYAVE_TIME     INTEGER,
                            FIRST_SPO2           NVARCHAR2(50),
                            THIS_YEARSPO2        NVARCHAR2(50),
                            FIRST_HR             NVARCHAR2(50),
                            THIS_YEAR_HR         NVARCHAR2(50),
                            FIRST_BLOOD          NVARCHAR2(50),
                            THIS_YEAR_BLOOD      NVARCHAR2(50),
                            FIRST_DISTANCE       NVARCHAR2(50),
                            THIS_YEAR_DISTANCE   NVARCHAR2(50),
                            FIRST_MMRC_SCORE     NVARCHAR2(50),
                            THIS_YEAR_MMRC_SCORE NVARCHAR2(50),
                            FIRST_ACT_SCORE      NVARCHAR2(50),
                            THIS_YEAR_ACT_SCORE  NVARCHAR2(50),
                            FIRST_MIP            NVARCHAR2(50),
                            THIS_YEAR_MIP        NVARCHAR2(50),
                            FIRST_MEP            NVARCHAR2(50),
                            THIS_YEAR_MEP        NVARCHAR2(50),
                            CREATE_DATE          TIMESTAMP            not null,
                            CREATE_MANID         INTEGER              not null,
                            MODIFY_DATE          TIMESTAMP,
                            MODIFY_MANID         INTEGER,
                            constraint PK_TD_ZWCF_KF_RCD primary key (RID),
                            constraint FK_TD_ZWCF_KF_RCD1 foreign key (MAIN_ID) references TD_ZWCF_PATIENT_INFO (RID)
                       )';
                END IF;
            END;
            ]]>
        </sql>
        <ver>120</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                V1 NUMBER;
            BEGIN
                SELECT COUNT(1)
                       INTO V1
                FROM USER_SEQUENCES
                WHERE SEQUENCE_NAME = 'TD_ZWCF_KF_RCD_SEQ';
                IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZWCF_KF_RCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 0 INCREMENT BY 1 NOCACHE';
                END IF;
            END;
            ]]>
        </sql>
        <ver>121</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('CARD_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD CARD_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>122</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZWCF_PATIENT_FLOWUP17'
                AND TABLE_NAME = 'TD_ZWCF_PATIENT_FLOWUP';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP  ADD constraint FK_TD_ZWCF_PATIENT_FLOWUP17 foreign key (CARD_TYPE_ID)      references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>123</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('DATA_SOURCE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD DATA_SOURCE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>124</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('GJ_UUID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP ADD GJ_UUID NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>125</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZWCF_PATIENT_FLOWUP')
                AND COLUMN_NAME = UPPER('IDC')
                AND DATA_TYPE = 'VARCHAR2';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZWCF_PATIENT_FLOWUP MODIFY IDC NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>126</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              BEGIN
                EXECUTE IMMEDIATE 'ALTER SEQUENCE TD_ZWCF_PATIENT_INFO_SEQ CACHE 10000';
              END;
          ]]>
        </sql>
        <ver>127</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-cfbsf 尘肺病随访相关模块升级 -->