<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
	<description>尘肺病筛查</description>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PNEUMSIS_EXTRACT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_PNEUMSIS_EXTRACT
			(
			   RID                  INTEGER              not null,
			   YEAR                 INTEGER,
			   UNIT_ID              INTEGER,
			   VISIT_NUM            NUMBER(5),
			   FILMING_NUM          NUMBER(5),
			   DUST_NUM             NUMBER(5),
			   STATE                NUMBER(1),
			   CHECK_UNIT_ID        INTEGER,
			   CHECK_PSN_ID         INTEGER,
			   CHECK_DATE           DATE,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_PNEUMSIS_EXTRACT primary key (RID),
			 constraint FK_TD_ZW_PNEUMSIS_EXTRACT1 foreign key (UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_PNEUMSIS_EXTRACT2 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_PNEUMSIS_EXTRACT3 foreign key (CHECK_PSN_ID)
				  references TS_USER_INFO (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>1</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PNEUMSIS_EXTRACT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PNEUMSIS_EXTRACT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>2</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PNEUMSIS_PATIENT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_PNEUMSIS_PATIENT
			(
			   RID                  INTEGER              not null,
			   MAIN_ID              INTEGER,
			   PERSON_NAME          NVARCHAR2(50),
			   SEX                  NUMBER(1),
			   IDC                  NVARCHAR2(50),
			   LINKTEL              NVARCHAR2(20),
			   VISIT_DATE           DATE,
			   IF_DUST              NUMBER(1),
			   START_RST            NUMBER(1),
			   CHECK_RST            NUMBER(1),
			   CRPT_NAME            NVARCHAR2(100),
			   WORK_ID              INTEGER,
			   OTHER_WORK_NAME      NVARCHAR2(50),
			   DUST_YEAR            NUMBER(3),
			   DUST_MONTH           NUMBER(2),
			   IF_INTO_DIAG         NUMBER(1),
			   RSN_ID               INTEGER,
			   STATE                NUMBER(1),
			   CHECK_UNIT_ID        INTEGER,
			   CHECK_PSN_ID         INTEGER,
			   CHECK_DATE           DATE,
			   OTHER_DUST_NAME      NVARCHAR2(200),
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_PNEUMSIS_PATIENT primary key (RID),
			constraint FK_TD_ZW_PNEUMSIS_PATIENT1 foreign key (MAIN_ID)
				  references TD_ZW_PNEUMSIS_EXTRACT (RID),
			constraint FK_TD_ZW_PNEUMSIS_PATIENT2 foreign key (WORK_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_PNEUMSIS_PATIENT3 foreign key (RSN_ID)
				  references TS_SIMPLE_CODE (RID),
			 constraint FK_TD_ZW_PNEUMSIS_PATIENT4 foreign key (CHECK_UNIT_ID)
				  references TS_UNIT (RID),
			 constraint FK_TD_ZW_PNEUMSIS_PATIENT5 foreign key (CHECK_PSN_ID)
				  references TS_USER_INFO (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>3</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PNEUMSIS_PATIENT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PNEUMSIS_PATIENT_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>4</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PNEUMSIS_DUST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_PNEUMSIS_DUST
			(
			   RID                  INTEGER              not null,
			   MAIN_ID              INTEGER,
			   DUST_ID              INTEGER,
			   CREATE_MANID         INTEGER              not null,
			   CREATE_DATE          TIMESTAMP            not null,
			   MODIFY_DATE          TIMESTAMP,
			   MODIFY_MANID         INTEGER,
			   constraint PK_TD_ZW_PNEUMSIS_DUST primary key (RID),
			constraint FK_TD_ZW_PNEUMSIS_DUST1 foreign key (MAIN_ID)
				  references TD_ZW_PNEUMSIS_PATIENT (RID),
			 constraint FK_TD_ZW_PNEUMSIS_DUST2 foreign key (DUST_ID)
				  references TS_SIMPLE_CODE (RID)
				  )';
              END IF;
            END;
          ]]>
		</sql>
        <ver>5</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PNEUMSIS_DUST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PNEUMSIS_DUST_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>6</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_PATIENT')
                      AND COLUMN_NAME = UPPER('IMAGE_EXAM_TYPE');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_PATIENT ADD IMAGE_EXAM_TYPE INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>7</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_PNEUMSIS_PATIENT6')
                AND TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_PATIENT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_PATIENT ADD CONSTRAINT FK_TD_ZW_PNEUMSIS_PATIENT6 FOREIGN KEY (IMAGE_EXAM_TYPE) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>8</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_PATIENT')
                      AND COLUMN_NAME = UPPER('INDUS_TYPE_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_PATIENT ADD INDUS_TYPE_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>9</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_PNEUMSIS_PATIENT7')
                AND TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_PATIENT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_PATIENT ADD CONSTRAINT FK_TD_ZW_PNEUMSIS_PATIENT7 FOREIGN KEY (INDUS_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>10</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_PATIENT')
                      AND COLUMN_NAME = UPPER('CRPT_ADDRESS');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_PATIENT ADD CRPT_ADDRESS NVARCHAR2(500)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>11</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_PATIENT')
                      AND COLUMN_NAME = UPPER('OTHER_RSN');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_PATIENT ADD OTHER_RSN NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>12</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_PNEUMSIS_EXTRACT')
                AND COLUMN_NAME = UPPER('EXT_MONTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PNEUMSIS_EXTRACT ADD EXT_MONTH INTEGER';
              END IF;
            END;
          ]]>
		</sql>
		<ver>13</ver>
	</sqlsentence>
</sqlsentences>
<!-- web-heth-cfb-extract 字段升级 -->