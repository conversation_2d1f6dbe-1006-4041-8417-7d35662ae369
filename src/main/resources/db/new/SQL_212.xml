<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
	<description>预警模型配置</description>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_WARN_MODEL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_ZW_WARN_MODEL
					(
					   RID                  INTEGER              not null,
					   WARN_TYPE            NUMBER(2),
					   WARN_LEVEL           NUMBER(1),
					   WARN_INDEX           NUMBER(1),
					   GE_IND               NUMBER(5),
					   GT_IND               NUMBER(5),
					   LE_IND               NUMBER(5),
					   LT_IND               NUMBER(5),
					   IF_CYCLE_TIME        NUMBER(1),
					   CYCLE_DAYS           NUMBER(3),
					   RMK                  VARCHAR2(200),
					   STATE_MARK           NUMBER(1),
					   CREATE_DATE          TIMESTAMP            not null,
					   CREATE_MANID         INTEGER              not null,
					   MODIFY_DATE          TIMESTAMP,
					   MODIFY_MANID         INTEGER,
					   constraint PK_TB_ZW_WARN_MODEL primary key (RID)
					)';
              END IF;
            END;
          ]]>
		</sql>
        <ver>1</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_WARN_MODEL_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_ZW_WARN_MODEL_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>2</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_WARN_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_WARN_INFO
					(
					   RID                  INTEGER              not null,
					   MODEL_ID             INTEGER,
					   WARN_ZONE            INTEGER,
					   BUS_TYPE             NUMBER(1),
					   BUS_ID               INTEGER,
					   WARN_CONT            VARCHAR2(500),
					   HAPPEN_DATE          DATE,
					   JC_BEGIN_DATE        DATE,
					   JC_END_DATE          DATE,
					   DEAL_DATE            DATE,
					   DEAL_ORGID           INTEGER,
					   HAPPEN_NUM           NUMBER(5),
					   EXCLUDE_RSN          VARCHAR2(200),
					   STATE_MARK           NUMBER(1),
					   VIEW_LEVEL           NUMBER(1),
					   CREATE_DATE          TIMESTAMP            not null,
					   CREATE_MANID         INTEGER              not null,
					   MODIFY_DATE          TIMESTAMP,
					   MODIFY_MANID         INTEGER,
					   constraint PK_TD_ZW_WARN_INFO primary key (RID),
					   constraint FK_TD_ZW_WARN_INFO1 foreign key (MODEL_ID) references TB_ZW_WARN_MODEL (RID),
					   constraint FK_TD_ZW_WARN_INFO2 foreign key (DEAL_ORGID) references TS_UNIT (RID),
					   constraint FK_TD_ZW_WARN_INFO3 foreign key (WARN_ZONE) references TS_ZONE (RID)
					)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>3</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_WARN_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_WARN_INFO_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>4</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_WARN_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_WARN_PSNS
					(
					   RID                  INTEGER              not null,
					   MAIN_ID              INTEGER,
					   BUS_ID               INTEGER,
					   RCV_DATE             DATE,
					   DEAL_DATE            DATE,
					   CREATE_DATE          TIMESTAMP            not null,
					   CREATE_MANID         INTEGER              not null,
					   MODIFY_DATE          TIMESTAMP,
					   MODIFY_MANID         INTEGER,
					   constraint PK_TD_ZW_WARN_PSNS primary key (RID),
					   constraint FK_TD_ZW_WARN_PSNS1 foreign key (MAIN_ID) references TD_ZW_WARN_INFO (RID)
					)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>5</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_WARN_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_WARN_PSNS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>6</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_BEGIN_DATE_RCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TB_ZW_BEGIN_DATE_RCD
				(
				   RID                  INTEGER              not null,
				   WARN_TYPE            NUMBER(2),
				   BEGIN_DATE           DATE,
				   BUS_TYPE             NUMBER(1),
				   BUS_ID               INTEGER,
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TB_ZW_BEGIN_DATE_RCD primary key (RID)
				)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>7</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_BEGIN_DATE_RCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_ZW_BEGIN_DATE_RCD_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
		</sql>
		<ver>8</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TB_ZW_BEGIN_DATE_RCD')
                      AND COLUMN_NAME = UPPER('WARN_UNIT');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_BEGIN_DATE_RCD ADD WARN_UNIT NUMBER(1)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>9</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_WARN_INFO')
                      AND COLUMN_NAME = UPPER('ANNEX_PATH');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_WARN_INFO ADD ANNEX_PATH NVARCHAR2(100)';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>10</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = UPPER('TD_ZW_WARN_PSNS')
                      AND COLUMN_NAME = UPPER('DIS_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_WARN_PSNS ADD DIS_ID INTEGER';
                    END IF;
                END;
            ]]>
		</sql>
		<ver>11</ver>
	</sqlsentence>
	<sqlsentence>
		<sql>
			<![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_WARN_PSNS2')
                AND TABLE_NAME = UPPER('TD_ZW_WARN_PSNS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_WARN_PSNS ADD CONSTRAINT FK_TD_ZW_WARN_PSNS2 FOREIGN KEY (DIS_ID) REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
		</sql>
		<ver>12</ver>
	</sqlsentence>
</sqlsentences>
<!-- web-heth-warn 字段升级 -->