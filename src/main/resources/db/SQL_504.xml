<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>流程引擎</description>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'ACT_HI_TASKRCD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE ACT_HI_TASKRCD (   RID                  INTEGER              NOT NULL,   TASK_DEF_KEY         VARCHAR2(64),   TASK_ID              VARCHAR2(64),   PROC_INST_ID         VARCHAR2(64),   DEAL_TIME            TIMESTAMP            NOT NULL,   DEAL_ADVICE          VARCHAR2(255),   USER_ID              VARCHAR2(255),   CONSTRAINT PK_ACT_HI_TASKRCD PRIMARY KEY (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'ACT_HI_TASKRCD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE ACT_HI_TASKRCD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_TEMPMETA_DEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_TEMPMETA_DEFINE (   RID                  INTEGER              NOT NULL,   PARAM_TYPE           NUMBER(2)            DEFAULT 0 NOT NULL,   CODE_LEVEL_NO        VARCHAR2(30)         NOT NULL,   META_NAME            VARCHAR2(50)         NOT NULL,   IMP_CLASSNAME        VARCHAR2(200)        NOT NULL,   DEMO_DESC            VARCHAR2(1000),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TB_TEMPMETA_DEFINE PRIMARY KEY (RID),   CONSTRAINT AK_TB_TEMPMETA_DEFINE UNIQUE (META_NAME))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_FLOW_ADVTEMPLATE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_FLOW_ADVTEMPLATE (   RID                  INTEGER              NOT NULL,   FLOW_NODEID          INTEGER              NOT NULL,   TEMPLATE_NAME        VARCHAR2(50)         NOT NULL,   TEMPLATE_CONTENT     VARCHAR2(2000)       NOT NULL,   IS_DEFAULT           NUMBER(1)            NOT NULL,   STATE                NUMBER(1)            NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TB_FLOW_ADVTEMPLATE PRIMARY KEY (RID),   CONSTRAINT FK_TB_FLOW_ADVTEMPLATE1 FOREIGN KEY (FLOW_NODEID) REFERENCES TD_FLOW_NODE(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_TEMPMETA_DEFINE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_TEMPMETA_DEFINE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_FLOW_ADVTEMPLATE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_FLOW_ADVTEMPLATE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('ACT_HI_TASKRCD')
                AND COLUMN_NAME = UPPER('DEAL_ADVICE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE ACT_HI_TASKRCD MODIFY DEAL_ADVICE VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_NODE_BTN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FLOW_NODE_BTN (   RID                  INTEGER              NOT NULL,   FLOW_NODE_ID         INTEGER              NOT NULL,   FLOW_BTN_TYPE        NUMBER(2)            NOT NULL,   FLOW_BTN_NAME        VARCHAR2(20)         NOT NULL,   CONSTRAINT PK_TD_FLOW_NODE_BTN PRIMARY KEY (RID),   CONSTRAINT AK_TD_FLOW_NODE_BTN UNIQUE (FLOW_BTN_TYPE, FLOW_NODE_ID),   CONSTRAINT FK_TD_FLOW_NODE_BTN FOREIGN KEY (FLOW_NODE_ID) REFERENCES TD_FLOW_NODE(RID) )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_NODE_BTN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_NODE_BTN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 200 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_NODE_TXTYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FLOW_NODE_TXTYPE (   RID                  INTEGER              NOT NULL,   TX_ID                INTEGER              not null,   FLOW_NODE_ID         INTEGER              not null,   constraint PK_TD_FLOW_NODE_TXTYPE primary key (RID),   constraint AK_TD_FLOW_NODE_TXTYPE unique (TX_ID, FLOW_NODE_ID),   constraint FK_TD_FLOW_NODE_TXTYPE1 foreign key (TX_ID) references TS_TXTYPE (RID),   constraint FK_TD_FLOW_NODE_TXTYPE2 foreign key (FLOW_NODE_ID) references TD_FLOW_NODE (RID) )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_NODE_TXTYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_NODE_TXTYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 200 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_NODE_PAGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FLOW_NODE_PAGE (   RID                  INTEGER              NOT NULL,   PARAM_TYPE           NUMBER(2)            default 0 not null,   PAGE_CODE            VARCHAR2(50)         not null,   PAGE_DESC            VARCHAR2(50)         not null,   PAGE_URL             VARCHAR2(500)        not null,   IF_REVEAL            NUMBER(1)            default 1 not null,   NUM                  INTEGER,   PAGE_PARAM           VARCHAR2(500),   constraint PK_TD_FLOW_NODE_PAGE primary key (RID),   constraint AK_TD_FLOW_NODE_PAGE unique (PAGE_CODE) )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_NODE_PAGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_NODE_PAGE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 200 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE')
                AND COLUMN_NAME = UPPER('PAGE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE ADD PAGE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FLOW_NODE1'
                AND TABLE_NAME = 'TD_FLOW_NODE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE ADD CONSTRAINT FK_TD_FLOW_NODE1 FOREIGN KEY (PAGE_ID)      REFERENCES TD_FLOW_NODE_PAGE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE_PAGE')
                AND COLUMN_NAME = UPPER('PAGE_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE_PAGE MODIFY PAGE_CODE VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE_PAGE')
                AND COLUMN_NAME = UPPER('PAGE_DESC');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE_PAGE MODIFY PAGE_DESC VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE_PAGE')
                AND COLUMN_NAME = UPPER('PAGE_PARAM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE_PAGE ADD PAGE_PARAM VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE')
                AND COLUMN_NAME = UPPER('FST_NODE_DISP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE ADD FST_NODE_DISP NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE')
                AND COLUMN_NAME = UPPER('FF_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE ADD FF_MSG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TB_TEMPMETA_DEFINE')
                AND COLUMN_NAME = UPPER('META_ENAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TEMPMETA_DEFINE ADD META_ENAME VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
        <!--  
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'AK_TB_TEMPMETA_DEFINE2'
                AND TABLE_NAME = 'TB_TEMPMETA_DEFINE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_TEMPMETA_DEFINE ADD CONSTRAINT AK_TB_TEMPMETA_DEFINE2 UNIQUE (META_ENAME)';
              END IF;
            END;
          ]]>
          -->
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_NODE_SCRIPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FLOW_NODE_SCRIPT(   RID                  INTEGER              not null,   FLOW_NODE_ID         INTEGER              not null,   SCRIPT               CLOB                 not null,   SCRIPT_TYPE          NUMBER(1)            not null,   constraint PK_TD_FLOW_NODE_SCRIPT primary key (RID),   constraint FK_TD_FLOW_NODE_SCRIPT foreign key (FLOW_NODE_ID) references TD_FLOW_NODE (RID)  )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_NODE_SCRIPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_NODE_SCRIPT_SEQ     MINVALUE 1     MAXVALUE 9999999999999999999999999     START WITH 1     INCREMENT BY 1     CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_DEF')
                AND COLUMN_NAME = UPPER('IF_DYNA_FORM');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_DEF ADD IF_DYNA_FORM NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_DEF')
                AND COLUMN_NAME = UPPER('DYNA_FORM_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_DEF ADD DYNA_FORM_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE')
                AND COLUMN_NAME = UPPER('DYNA_FORM_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE ADD DYNA_FORM_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_FLOW_NODE3'
                AND TABLE_NAME = 'TD_FLOW_NODE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE   ADD CONSTRAINT FK_TD_FLOW_NODE3 FOREIGN KEY (DYNA_FORM_ID)      REFERENCES TD_FORM_DEF (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('ACT_HI_TASKRCD')
                AND COLUMN_NAME = UPPER('TRUST_USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE ACT_HI_TASKRCD ADD TRUST_USER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_TRUST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_FLOW_TRUST (   RID                  INTEGER              not null,   USER_ID              INTEGER              not null,   TRUST_USER_ID        INTEGER              not null,   BEGIN_TIME           DATE,   END_TIME             DATE,   STATE                NUMBER(1)            not null,   CONSTRAINT PK_TD_FLOW_TRUST PRIMARY KEY (RID),   CONSTRAINT FK_TD_FLOW_TRUST1 FOREIGN KEY (USER_ID) REFERENCES TS_USER_INFO (RID),   constraint FK_TD_FLOW_TRUST2 foreign key (TRUST_USER_ID) references TS_USER_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_TRUST_DEF';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_FLOW_TRUST_DEF (   RID                  INTEGER              not null,   TRUST_ID             INTEGER              not null,   DEF_ID               VARCHAR2(64)         not null,   constraint PK_TD_FLOW_TRUST_DEF primary key (RID),   constraint FK_TD_FLOW_TRUST_DEF foreign key (TRUST_ID) references TD_FLOW_TRUST (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_TRUST_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_FLOW_TRUST_INST (   RID                  INTEGER              not null,   TRUST_ID             INTEGER              not null,   PROC_INST_ID         VARCHAR2(64)         not null,   constraint PK_TD_FLOW_TRUST_INST primary key (RID),   constraint FK_TD_FLOW_TRUST_INST foreign key (TRUST_ID) references TD_FLOW_TRUST (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_TRUST_TASK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_FLOW_TRUST_TASK (   RID                  INTEGER              not null,   TRUST_ID             INTEGER              not null,   TASK_ID              VARCHAR2(64)         not null,   constraint PK_TD_FLOW_TRUST_TASK primary key (RID),   constraint FK_TD_FLOW_TRUST_TASK foreign key (TRUST_ID)  references TD_FLOW_TRUST (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_TRUST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_TRUST_SEQ     MINVALUE 1     MAXVALUE 9999999999999999999999999     START WITH 1     INCREMENT BY 1     CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_TRUST_INST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_TRUST_INST_SEQ     MINVALUE 1     MAXVALUE 9999999999999999999999999     START WITH 1     INCREMENT BY 1     CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_TRUST_DEF_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_TRUST_DEF_SEQ     MINVALUE 1     MAXVALUE 9999999999999999999999999     START WITH 1     INCREMENT BY 1     CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_TRUST_TASK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_TRUST_TASK_SEQ     MINVALUE 1     MAXVALUE 9999999999999999999999999     START WITH 1     INCREMENT BY 1     CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('ACT_HI_TASKRCD')
                AND COLUMN_NAME = UPPER('ACT_OPT_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE ACT_HI_TASKRCD ADD ACT_OPT_TYPE VARCHAR2(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_TYPE')
                AND COLUMN_NAME = UPPER('MOBILE_PIC_URL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_TYPE ADD MOBILE_PIC_URL VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_FLOW_TITLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_FLOW_TITLE (   RID                  INTEGER              not null,   DEF_ID               INTEGER              not null,   TEMPLATE_NAME        VARCHAR2(50)         not null,   TEMPLATE_CONTENT     VARCHAR2(2000)       not null,   IS_DEFAULT           NUMBER(1)            not null,   STATE                NUMBER(1)            not null,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_FLOW_TITLE primary key (RID),   CONSTRAINT FK_TD_FLOW_TITLE1 FOREIGN KEY (DEF_ID) REFERENCES TD_FLOW_DEF(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_FLOW_TITLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_FLOW_TITLE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_DEF')
                AND COLUMN_NAME = UPPER('IS_DISP_PRO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_DEF ADD IS_DISP_PRO NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_FLOW_NODE')
                AND COLUMN_NAME = UPPER('MUL_NODE_RULE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_FLOW_NODE ADD MUL_NODE_RULE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
          <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('ACT_HI_TASKRCD')
                AND COLUMN_NAME = UPPER('NEXT_USER_IDS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE ACT_HI_TASKRCD ADD NEXT_USER_IDS VARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-system 系统流程引擎相关升级 -->