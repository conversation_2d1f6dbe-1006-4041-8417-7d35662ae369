#1.体检录入加载检查项目去除问诊，配置问诊组合编码
codeNos=100001

#审批查询报告打印开始日期
reportCard.bhkBeginDate=2020-01-01
#通用-批量审核默认审核意见，审核通过默认意见，如GBZ188报告卡审核，个案审核
defaultAuditAdv=通过
#报告卡审批期限
limitTime=5

#查询日期显示，1：接收日期，查询匹配报告卡打印日期(目前所有平台都配置1)；2：显示“体检日期”，查询、显示匹配BHK_DATE；有关模块：疑似职业病报告卡/个案审核
receiveDate=1
#报告卡填报、审核是否显示处理期限
ifshowdeadline=true
#疑似职业病上报期限
yszybNostdTime=15

#重点危害因素检查情况审核——审批期限
zdBadRsnlimitTime=5
#重点危害因素检查情况审核——通过默认意见
zdzybdefaultAuditAdv=通过
#超范围预警处置期限
outRangeLimitTime=3
#吉林企业在线申报间隔月份限制
showLimitMonth=13

#个案审核/职业性有害因素监测卡、疑似职业病报告卡、职业病报告卡、鉴定报告卡、用人单位审核
#审核级别，默认为3级，区-》市-》省；"直辖市平台"或"市级平台"配置为2，比如：重庆市、武汉市、济南市、天津市
checkLevel=3
#市/省级平台（ 1：市级平台；2：直辖市/省级平台）：用人单位审核；用于区分查询条件状态显示“区-市”或者“区-省”
platVersion=2
#个案审核期限：3个工作日
bhkLimitTime=3
#个案异步导出：正在导出次数限制参数配置
asyncExport.times=10

#体检录入上传天数（用来计算及时性）
setRequiredUploadTime=5
#体检录入重卡天数（用来判断是否是重卡）
repeatDays=7

#【陕西职卫】体检录入模块-基本信息体检编号自动生成配置，1自动生成只读，不配置默认或非1保持原有功能(陕西、新疆配置为1)
ifAutoGenBhkCode=1
#【陕西职卫】【云南职卫】体检录入模块-当有参数且值为1时：【检查结论】区域页面显示调整为根据每个危害因素选择5大结论选项,不配置默认或非1保持原有功能
bhkResultStyle=1

#【diag项目】完善单位信息是否显示职业病诊断文书编号(为1：显示；其他情况不显示)
ifShowDiagWritNo=0

#【体检复查结果查询】查询条件-未复检总人数默认值
totalNumOfUnreviewed=20

#【场所监测数据导入】 导入数据中空值判断值，"--"表示空值，导入时不做处理，多个用英文逗号隔开
siteMonitoringNullVal = --,——

#用人单位综合展示  用人单位预警处置期限
warningLimitTime=7

#【职业卫生技术服务申报】模块详情页是否要显示二维码
ifShowQRCode = 1

#【重庆职卫】职业卫生技术服务报送卡模块 暂存和提交时，是否验证需要现场打卡记录
ifNeedOnSiteCheckIn=false
#【重庆职卫】职业卫生技术服务报送卡模块 现场打卡记录验证开始日期,当ifNeedOnSiteCheckIn=true时，日期之前不校验，建议格式yyyy-MM-dd
checkStartDate=2025-07-08