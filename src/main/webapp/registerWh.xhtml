<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<h:head>
    <link rel="stylesheet" type="text/css"
          href="#{request.contextPath}/resources/css/default.css"/>

    <title><h:outputText value="#{applicationBean.appTitle}"/></title>
    <h:outputScript library="js" name="namespace.js"/>
    <h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
    <script type="text/javascript"
            src="/resources/js/validate/system/validate.js"></script>
    <script type="text/javascript">
        var zwxMiniJQ = $.noConflict(true);
    </script>
    <script>
        //<![CDATA[
        function setVerImg() {
            var url = Math.random();
            document.getElementById("abc").src = "/authImage?id=" + url;
        }

        jQuery(document).ready(
            function () {
                //alert(navigator.userAgent.toLowerCase());
                var isFirefox = navigator.userAgent.toLowerCase().match(
                    /firefox/) != null; //是否Chrome浏览器
                var isChrome = navigator.userAgent.toLowerCase().match(
                    /chrome/) != null; //是否Chrome浏览器
                var isWebkit = navigator.userAgent.toLowerCase().match(
                    /applewebkit/) != null; //是否极速模式浏览器
                var isSafari = jQuery.browser.safari; //是否Safari浏览器
                if (!isSafari && !isWebkit && !isChrome && !isFirefox) {
                    //跳转到下载页面
                    forwardBtn();
                }
            });

        function keyLogin(e) {
            if (e.keyCode == 13) {  //回车键的键值为13
                document.getElementById("personForm:registBut").click(); //调用登录按钮的登录事件
            }
        }

        function hideShowPsw() {
            var pwdImg = document.getElementById("showImg");
            var pwdInput = document.getElementById("personForm:password");

            if (pwdInput.getAttribute("type") == "password") {
                pwdInput.setAttribute("type", "text");
                pwdImg.style.background = "url('/resources/images/login/xsmm.png') no-repeat";
            } else {
                pwdInput.setAttribute("type", "password");
                pwdImg.style.background = "url('/resources/images/login/ycmm.png') no-repeat";
            }
        }

        function hideShowPsw1() {
            var pwdImg = document.getElementById("showImg1");
            var pwdInput = document.getElementById("personForm:password1");

            if (pwdInput.getAttribute("type") == "password") {
                pwdInput.setAttribute("type", "text");
                pwdImg.style.background = "url('/resources/images/login/xsmm.png') no-repeat";
            } else {
                pwdInput.setAttribute("type", "password");
                pwdImg.style.background = "url('/resources/images/login/ycmm.png') no-repeat";
            }
        }

        $(document).ready(function () {
            $("body").keyup(function (e) {
                e = window.event || e;
                keyLogin(e);
            });
        });

        //去除空格
        function removeSpace(pwdEle) {
            $(pwdEle).val($(pwdEle).val().replaceAll(/\s/g, ''));
        }
        //]]>
    </script>
    <style type="text/css">
        .panel {
            width: 100%;
        }

        .panel2 {
            background-color: #fcfdfd;;
            box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
            height: 93px;
            width: 100%;
            border-style: hidden;

        }
        .panel3 {
            box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
            width: 1240px;
            height: 974px;
            background-color: #fcfdfd;
            border: 1px solid #adabab59;
            position: absolute;
            z-index: 1;
            top: 130px;
            left: -webkit-calc(50% - 620px);
        }

        ul.ulList li {
            list-style: none;
            float: left;
            height: 113px;
            margin-left: 16px;
            margin-right: 16px;
            margin-top: 20px;
            width: 105px;
            text-align: center;
        }

        .set-current :hover {
            border: #b5b500 1px solid;
            background-color: #ffffcc;
        }


        .left_line {
            width: 5px;
            height: 16px;
            background-color: #2a6fcd;
        }

        .ui-widget {
            font-size: 14px !important;
        }

        .ui-selectmanycheckbox label, .ui-selectoneradio label {
            display: block;
            margin-top: 0px !important;
            margin-right: -2px !important;
            margin-left: -13px;
        }

        .icSvgPwd {
            width: 20px;
            height: 20px;
            float: right;
            position: relative;
            left: -33px;
            top: 10px;
        }

        .icSvgPwd1 {
            width: 20px;
            height: 20px;
            float: right;
            position: relative;
            left: -33px;
            top: 10px;
        }

        .panel3 tr{
            border-color: #adabab59;
            background: #fcfdfd;
        }

    </style>
</h:head>

<h:body leftmargin="0" topmargin="0"
        style="margin-right: 0px;margin-top:0px; overflow: visible;background-color: #F0F0F0;">
    <h:form id="personForm">
        <h:inputHidden id="passHidden" value="#{registerWhBean.password}"/>
        <h:inputHidden id="usernoHidden" value="#{registerWhBean.tdSysApplyAccount.userNo}"/>

        <p:panelGrid styleClass="panel2">
            <p:row>
                <p:column colspan="3"
                          style="width: 1280px; height: 78px;padding-left: 30px; background-color: #2a6fcd; box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.02); ">
                    <p:graphicImage style="vertical-align: middle; height: 63%;"
                                    value="/resources/images/login/registerWhHead.png"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <table class="panel">
            <tr>
                <td>
                    <p:panelGrid styleClass="panel3" id="registPage">
                        <p:row >
                            <p:column colspan="3"
                                      style="text-align: center;  font-weight: 700; width: 1238px;  height: 60px;border-bottom-color: transparent;background: linear-gradient(180deg,#fcfcfd, #f7f8fa); box-shadow: 0px -1px 0px 0px #dcdee3 inset;">
                                <font color="#333333"
                                      style="font-size:23px;text-align: center;line-height: 22px;height: 20px;letter-spacing: 0px;">注册账号</font>
                            </p:column>
                        </p:row>
                        <p:row >
                            <p:column
                                    style="background-color: #fcfdfd;display: flex;  width: 161px;border-color: transparent; margin-left: 20px;margin-top: 30px">
                                <div class="left_line"></div>
                                <p:outputLabel value="单位信息"
                                               style=" width: 85px;   font-size: 16px !important;  font-weight: 600; text-align: right; color: #333333;  margin-top: -4px; margin-left: -10px;"></p:outputLabel>
                            </p:column>
                            <p:column style="border-left-color: transparent;background-color: #fcfdfd;">
                                <p:panelGrid style="margin-top: 3px;background-color: #fcfdfd;">
                                    <p:row>
                                        <p:column
                                                style="background-color:#fcfdfd; width: 150px;border-color: transparent;text-align: right;padding-right: 0; padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="所属地区："></p:outputLabel>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;border-color: transparent;width: 300px;text-align: left;padding-left: 5px; padding-top: 18px; padding-right: 0;padding-bottom: 0;">
                                            <zwx:ZoneSingleNewCompHeight zoneList="#{registerWhBean.zoneList}"
                                                                         zoneCode="#{registerWhBean.zoneCode}"
                                                                         zoneName="#{registerWhBean.zoneName}"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;padding-left: 2px;padding-bottom: 0;">
                                            <div style="#{registerWhBean.ifZoneEmtpy?'display:flex;':'display:none;'}width: 156px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 8px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="所属地区不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="#{registerWhBean.ifZoneError?'display:flex;':'display:none;'}width: 166px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 8px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="请选择到区县及以下" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>

                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;width: 220px;">
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;" />
                                        <p:column style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;" colspan="3">
                                            <div style="color: #7b7b7b;font-size: 12px;line-height: 12px;">规则：必须选择到区县及以下</div>
                                        </p:column>
                                    </p:row>
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="社会信用代码："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                    value="#{registerWhBean.tdSysApplyAccount.creditCode}" placeholder="请输入"
                                                    styleClass="text" id="creditCode" maxlength="25">
                                                <p:ajax event="blur" update=":personForm:unitnameErr,:personForm:creditCodeErr,:personForm:ifSubOrgErr"  process="@this,personForm" listener="#{registerWhBean.changeUnitCode}"/>
                                            </p:inputText>

                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;padding-left: 2px;" colspan="2"  >

                                            <p:outputPanel  id = "creditCodeErr">
                                            <div style="display:#{registerWhBean.ifCodeEmtpy?'flex':'none'};width: 185px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>

                                                <p:outputLabel value="社会信用代码不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifCodeError?'flex':'none'};width: 196px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="社会信用代码格式不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifCodeExsit?'flex':'none'};width: 220px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="单位已有账号，请联系管理员" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifCodeApply?'flex':'none'};width: 255px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="单位账号正在审核中，请耐心等待" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            </p:outputPanel>
                                        </p:column>
                                    </p:row>
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="单位名称："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="text-align:left;padding-left:12px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                     value="#{registerWhBean.tdSysApplyAccount.unitname}" placeholder="请输入"
                                                    styleClass="text" id="unitname" maxlength="50">
                                                <p:ajax event="blur" update=":personForm:unitnameErr,:personForm:creditCodeErr,:personForm:ifSubOrgErr"  process="@this,personForm" listener="#{registerWhBean.changeUnitCode}"/>
                                            </p:inputText>
                                        </p:column>
                                        <p:column style="border-color: transparent;   padding-left: 2px;background-color:#fcfdfd; padding-top: 18px;" >
                                            <p:outputPanel id="unitnameErr">
                                                <div style="display:#{registerWhBean.ifUnitEmtpy?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                    value="/resources/images/login/mustIcon.png"/>
                                                    <p:outputLabel value="单位名称不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                                </div>
                                                <div style="display:#{registerWhBean.ifUnitRepeat?'flex':'none'};width: 185px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                    value="/resources/images/login/mustIcon.png"/>
                                                    <p:outputLabel value="单位名称已存在" style="color: red;margin-top: 3px;"></p:outputLabel>
                                                </div>
                                            </p:outputPanel>
                                        </p:column>
                                        <p:column style="border-color: transparent;background-color:#fcfdfd;">
                                        </p:column>
                                    </p:row>

                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="是否分支机构："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                            <p:selectOneRadio id="ifSubOrg" value="#{registerWhBean.tdSysApplyAccount.ifSubOrg}" style="width: 150px;">
                                                <f:selectItem itemLabel="是" itemValue="1"/>
                                                <f:selectItem itemLabel="否" itemValue="0"/>
                                                <p:ajax event="change" update=":personForm:unitnameErr,:personForm:creditCodeErr,:personForm:ifSubOrgErr"  process="@this,personForm" listener="#{registerWhBean.changeUnitCode}"/>
                                            </p:selectOneRadio>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;padding-left: 2px;" colspan="2" >
                                            <p:outputPanel  id = "ifSubOrgErr">
                                                <div style="display:#{registerWhBean.ifSubOrgEmpty?'flex':'none'};width: 185px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                    value="/resources/images/login/mustIcon.png"/>
                                                    <p:outputLabel value="主体机构不存在" style="color: red;margin-top: 3px;"></p:outputLabel>
                                                </div>
                                            </p:outputPanel>
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;" />
                                        <p:column style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;" colspan="3">
                                            <div style="color: #7b7b7b;font-size: 12px;line-height: 12px;padding-left: 12px;">提示：分院或分公司请选择是！</div>
                                        </p:column>
                                    </p:row>
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 12px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="单位联系电话："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 12px;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                     value="#{registerWhBean.tdSysApplyAccount.unitTel}" placeholder="请输入"
                                                    styleClass="text" id="unitTel" maxlength="25"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent; padding-top: 12px;padding-left: 2px;" colspan="2">
                                            <div style="display:#{registerWhBean.ifPhomeEmtpy?'flex':'none'};width: 185px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="单位联系电话不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifPhomeError?'flex':'none'};width: 196px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="单位联系电话格式不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                    </p:row>
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 22px;vertical-align: top;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="单位属性："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:0px;border-style:hidden;padding-top: 18px; width: 725px;" colspan="2">
                                            <p:selectManyCheckbox value="#{registerWhBean.unitSorts}" onchange="changeSorts()"
                                                                  layout="grid" columns="3" id="unitSort"
                                                                 >
                                                <f:selectItems  value="#{registerWhBean.sortList}" var="sort" itemLabel="#{sort.fkBySortId.sortName}" itemValue="#{sort.rid}" ></f:selectItems>
                                            </p:selectManyCheckbox>
                                            <p:remoteCommand name="changeSorts" action="#{registerWhBean.changeSortAction}" process="@this,unitSort,dataTable" update="dataTable"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                            <div style="display:#{registerWhBean.ifSortEmtpy?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="单位属性不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                    </p:row>
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 14px;vertical-align: top;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="申请资料："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;width: 725px;" colspan="2">
                                            <p:dataTable var="itm" value="#{registerWhBean.tdSysApplyAccount.applyAnnexList}"
                                                         id="dataTable" emptyMessage="没有您要找的记录！" >
                                                <p:column  headerText="证书资料" style="width:220px;">
                                                    <h:outputText value="#{itm.fkByAnnexId.annexName}"/>
                                                </p:column>
                                                <p:column headerText="文件名"  style="width:220px;" >
                                                    <h:outputText value="#{itm.annexName}"/>
                                                </p:column>
                                                <p:column  headerText="操作" style="text-align:center;width:100px;">
                                                    <p:commandLink value="上传"  oncomplete="PF('FileDialog').show()" update=":personForm:fileDialog" process="@this"  rendered="#{itm.annexPath == null}">
                                                        <f:setPropertyActionListener target="#{registerWhBean.annexEntity}" value="#{itm}" />
                                                    </p:commandLink>
                                                    <p:spacer width="5" />
                                                    <p:commandLink value="查看" process="@this" onclick="window.open('/webFile/#{itm.annexPath}')"
                                                                   rendered="#{itm.annexPath != null}" >
                                                    </p:commandLink>
                                                    <p:spacer width="5" />
                                                    <p:commandLink value="删除"  action="#{registerWhBean.deleteAnnexAction}" rendered="#{itm.annexPath != null}" update="dataTable" process="@this,dataTable">
                                                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                                        <f:setPropertyActionListener target="#{registerWhBean.annexEntity}" value="#{itm}" />
                                                    </p:commandLink>
                                                </p:column>

                                            </p:dataTable>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                            <div style="display:#{registerWhBean.ifApplyEmtpy?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="申请资料不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                            </p:column>
                        </p:row>
                        <p:row >
                            <p:column
                                    style="background-color:#fcfdfd;display: flex;  width: 161px;border-color: transparent; margin-left: 20px;margin-top: 30px">
                                <div class="left_line"></div>
                                <p:outputLabel value="用户信息"
                                               style=" width: 85px;   font-size: 16px !important;  font-weight: 600; text-align: right; color: #333333;  margin-top: -4px; margin-left: -10px;"></p:outputLabel>
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-left-color: transparent;">
                                <p:panelGrid style="background-color:#fcfdfd;margin-top: 3px">

                                    <p:row>
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="姓名："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                    redisplay="true" placeholder="请输入"
                                                    value="#{registerWhBean.tdSysApplyAccount.username}" styleClass="text" id="username"
                                                    maxlength="15"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;">
                                            <div style="display:#{registerWhBean.ifUserNameEmtpy?'flex':'none'};;width: 129px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="姓名不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                        <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                                        </p:column>
                                    </p:row>
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="手机号码："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                     value="#{registerWhBean.tdSysApplyAccount.mobileNum}" placeholder="请输入"
                                                    styleClass="text" id="mobileNum" maxlength="25"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                            <div style="display:#{registerWhBean.ifMobileEmtpy?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="手机号码不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifMobileError?'flex':'none'};;width: 170px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="手机号码格式不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                        <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="身份证号："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                    value="#{registerWhBean.tdSysApplyAccount.idc}"
                                                    placeholder="请输入"
                                                    styleClass="text" id="idcard" maxlength="25"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;">
                                            <div style="display:#{registerWhBean.ifIdcEmtpy?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="身份证号不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifIdcError?'flex':'none'};;width: 170px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="身份证号格式不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifIdcExsit?'flex':'none'};;width: 170px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="该身份证号已存在" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                            </p:column>
                        </p:row>
                        <p:row >
                            <p:column
                                    style="border-color: transparent;background-color:#fcfdfd;display: flex;  width: 161px; margin-left: 20px;margin-top: 30px">
                                <div class="left_line"></div>
                                <p:outputLabel value="账号信息"
                                               style=" width: 85px;   font-size: 16px !important;  font-weight: 600; text-align: right; color: #333333;  margin-top: -4px; margin-left: -10px;"></p:outputLabel>
                            </p:column>
                            <p:column style="border-left-color: transparent;background-color:#fcfdfd;">
                                <p:panelGrid style="background-color:#fcfdfd;margin-top: 3px">
                                    <p:row style="border-style:hidden;">
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="登录账号："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;padding-right: 0px;">
                                            <p:inputText
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                    value="#{registerWhBean.tdSysApplyAccount.userNo}" placeholder="请输入"
                                                    styleClass="text" id="userno" maxlength="15"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;padding-right: 0px;padding-left: 0px;">
                                            <div style="display:#{registerWhBean.ifUserNoEmtpy?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="登录账号不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifUserNoExsit?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="该登录账号已存在" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;" />
                                        <p:column style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;" colspan="3">
                                            <div style="color: red;font-size: 12px;line-height: 12px;">提示：用于系统登录的用户名，请牢记！</div>
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="登录密码："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-right: 0px;">
                                            <p:password
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                                    id="password" placeholder="请输入" onkeyup="removeSpace(this);"
                                                    styleClass="text" onchange="removeSpace(this)"
                                                    value="#{registerWhBean.password}" maxlength="16" redisplay="true"/>
                                            <div id="showImg" style="background:url('/resources/images/login/ycmm.png') no-repeat;" class="icSvgPwd" onclick="hideShowPsw()"/>
                                        </p:column>
                                        <p:column
                                                style="text-align:left;padding-left:0px;border-style:hidden;width:220px;background-color:#fcfdfd;">
                                            <div style="display:#{registerWhBean.ifPassEmtpy?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="登录密码不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifPassError?'flex':'none'};;width: 170px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="登录密码不符合规则" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;">
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;" />
                                        <p:column style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;" colspan="3">
                                            <div style="color: #7b7b7b;font-size: 12px;line-height: 14px;">规则：8~16位字符，必须包含大小写字母、数字和特殊字
                                                <br/>
                                                符；不能包含连续3位顺序或逆序或重复的数字、字母、特 <br/>殊字符</div>
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column
                                                style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;">
                                            <font color="red">*</font>
                                            <p:outputLabel value="确认密码："
                                                           redisplay="true"/>
                                        </p:column>
                                        <p:column
                                                style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-right: 0px;">
                                            <p:password  onkeyup="removeSpace(this);"
                                                    style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;" onchange="removeSpace(this)"
                                                    value="#{registerWhBean.password1}" maxlength="16" styleClass="text" placeholder="请输入" redisplay="true"
                                                    id="password1" />
                                            <div id="showImg1" style="background:url('/resources/images/login/ycmm.png') no-repeat;" class="icSvgPwd1" onclick="hideShowPsw1()"/>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-left: 0;">
                                            <div style="display:#{registerWhBean.ifPassComfiremEmtpy?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="确认密码不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                            <div style="display:#{registerWhBean.ifPassComfiremError?'flex':'none'};;width: 185px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                                <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                                value="/resources/images/login/mustIcon.png"/>
                                                <p:outputLabel value="确认密码与密码不一致" style="color: red;margin-top: 3px;"></p:outputLabel>
                                            </div>
                                        </p:column>
                                        <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                            </p:column>
                        </p:row>

                        <p:row >
                            <p:column colspan="2"
                                      style="text-align:center;background-color:#fcfdfd;height: 90px;">
                                <p:commandButton id="registBut"
                                                 update="personForm"
                                                 process="@this,personForm"
                                                 style="margin-right:8px;width:312px;height:40px;color:white;font-size: 16px !important;background: #2a6fcd;border-radius: 3px;"
                                                 action="#{registerWhBean.reigsterAction}" value="注 册 "/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </td>
            </tr>
        </table>

        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog"
                  resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;"><p:outputLabel
                            value="（支持附件格式为：图片、PDF）" styleClass="diagTextClass"
                            style="position: relative;bottom: -6px;padding-right: 108px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;"><p:fileUpload
                            requiredMessage="请选择要上传的文件！" label="文件选择"
                            fileUploadListener="#{registerWhBean.fileUpload}"
                            invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                            style="width:600px;" previewWidth="120" cancelLabel="取消"
                            fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                            dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                            invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                            allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/" update="@this"
                    /></td>
                </tr>
            </table>
        </p:dialog>
        <p:focus id="focus"/>
    </h:form>
    <p:growl id="errorMsg" autoUpdate="true"/>
    <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
</h:body>
</html>