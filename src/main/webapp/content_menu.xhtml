<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
    xmlns:p="http://primefaces.org/ui">
	<h:head>
		<title></title>
	<link rel="stylesheet" type="text/css" href="#{request.contextPath}/resources/css/default.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/fastDesktop_menu.css" />
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
        <script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.widget.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.core.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.mouse.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.sortable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.draggable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.droppable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/desk.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery.dialog.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/ShortcutDesktop_menu.js"></script>
	</h:head>
	<f:view>
        <h:form id="forwardFRM">
            <p:remoteCommand  name="forwardLoutBtn" action="#{loginBean.logOutGo}"/>
        </h:form>
        <body class="deskbody">
	<div class="desktopMain">
	<div class="backLink" style="position: absolute; margin-top: -5px; margin-left: 40px;">
	</div>
		 <div class="desktopNavL">
				</div>

	           <ul class="desktopNav">
	           </ul>
				<div class="desktopNavR"  >
				</div>
				<div class="mainList">
					<div class="ulList">
					</div>
				</div>
			</div>
    <p:growl id="growl" life="3000" showDetail="false" autoUpdate="true"/>
    <h:form id="dialogDeskForm">
        <h:outputStylesheet name="css/default.css"/>
        <h:inputHidden value="#{pwdBean.dialogName}" id="dialogName"/>
    <p:remoteCommand name="showDialog" action="#{pwdBean.showDialogAction}"/>
        <ui:include src="pwdEdit.xhtml" />
    </h:form>
	</body>
	</f:view>
</html>