<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html">
<h:head>
	<title>兼容性</title>
	<style>
img {
	border: 0px;
}
 html, body {   height: 100%; }
</style>
</h:head>

<h:body>
	<table width="100%" height="100%" border="0" cellspacing="0" cellpadding="0"
		style="background: url(/resources/images/browser/back.png) repeat-x; leftmargin:0;topmargin:0;marginwidth：0;marginheight:0;">
		<tr valign="top">
			<td style="height: 30px;"></td>
		</tr>
		<tr valign="top">
			<td>
				<table style="background:url(/resources/images/browser/browser_01.png) center top no-repeat;width: 1024px;text-align: center;vertical-align: top;" border="0" cellpadding="0"
					cellspacing="0" valign="top" align="center">
					<tr align="top">
						<td colspan="5" style="height:180px;width: 1024px;"></td>
					</tr>
					<tr align="top" style="height:65px;">
						<td style="width: 50px;"></td>
						<td onmouseover="this.style.background='url(/resources/images/browser/browser_360_1.png) top left no-repeat'"
							onmouseout="this.style.background='url(/resources/images/browser/browser_360.png) top left no-repeat'"
							style="background:url(/resources/images/browser/browser_360.png) top left no-repeat;width:260px;cursor: pointer;" onclick="window.location.href='http://se.360.cn/'"></td>
						<td style="width: 30px;"></td>
						<td onmouseover="this.style.background='url(/resources/images/browser/browser_lb_1.png) top left no-repeat'"
							onmouseout="this.style.background='url(/resources/images/browser/browser_lb.png) top left no-repeat'"
							style="background:url(/resources/images/browser/browser_lb.png) top left no-repeat;width:260px;cursor: pointer;"
							onclick="window.location.href='http://www.liebao.cn/windows.html#index1'"></td>
						<td style="width: 424px;"></td>
					</tr>
					<tr align="top">
						<td style="height: 480px;"></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>

</h:body>
</html>