<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" 
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{portalLinksBean}"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <style type="text/css">

    </style>
    <script type="text/javascript">

    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="常用链接管理"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{portalLinksBean.searchAction}" update="dataTable" process="@this,searchName,searchType" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" rendered="#{portalLinksBean.flag == false}" process="@this" action="#{portalLinksBean.addInit}"
					update=":mainForm:editDialog" oncomplete="PF('EditDialog').show()">
					<p:resetInput target=":mainForm:editDialog" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchName" value="链接名称：" />
        </p:column>
        <p:column style="text-align:left;padding-left:5px;width:20%;">
            <p:inputText id="searchName" value="#{portalLinksBean.searchName}"
                         maxlength="100"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchType" value="打开方式：" />
        </p:column>
        <p:column style="text-align:left;padding-left:5px;width: 250px;">
            <p:selectManyCheckbox id="searchType"
                                  value="#{portalLinksBean.searchType}">
                <f:selectItem itemLabel="内部打开" itemValue="1" />
                <f:selectItem itemLabel="外部打开" itemValue="0" />
            </p:selectManyCheckbox>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="序号" style="width:80px;text-align: center">
        <h:outputLabel value="#{itm.nums}"/>
    </p:column>
    <p:column headerText="打开方式" style="width:120px;text-align: center">
        <h:outputLabel rendered="#{itm.linkType == 0}" value="外部打开"/>
        <h:outputLabel rendered="#{itm.linkType == 1}" value="内部打开"/>
    </p:column>
    <p:column headerText="链接名称" style="width:200px;padding-left: 3px;">
        <h:outputLabel value="#{itm.linkName}"/>
    </p:column>
    <p:column headerText="链接地址" style="width:400px;padding-left: 3px;">
        <h:outputLabel value="#{itm.linkUrl}"/>
    </p:column>
    <p:column headerText="操作" style="padding-left: 3px;" rendered="#{portalLinksBean.flag == false}">
        <p:commandLink value="详情"  id="moreInfo" update=":mainForm:viewDialog" process="@this" oncomplete="PF('ViewDialog').show()">
            <f:setPropertyActionListener value="#{itm}" target="#{portalLinksBean.tdPortalLinks}"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink value="修改"
                       action="#{portalLinksBean.modInit}" update=":mainForm:editDialog"
                       oncomplete="PF('EditDialog').show()" process="@this">
            <f:setPropertyActionListener target="#{portalLinksBean.tdPortalLinks}" value="#{itm}"/>
            <p:resetInput target=":mainForm:editDialog"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink value="删除" action="#{portalLinksBean.deleteAction}"
                       update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{portalLinksBean.tdPortalLinks}" value="#{itm}"/>
            <f:setPropertyActionListener target="#{portalLinksBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>

    </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
    <!-- 详情 -->
    <p:dialog id="viewDialog" header="链接详情" widgetVar="ViewDialog" resizable="false" width="500" height="200" modal="true">
        <p:panelGrid style="width: 100%;" id="viewPanel">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 30px">
                    <h:outputLabel value="链接名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <h:outputLabel value="#{portalLinksBean.tdPortalLinks.linkName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 30px">
                    <h:outputLabel value="链接地址："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <h:outputLabel value="#{portalLinksBean.tdPortalLinks.linkUrl}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 30px">
                    <h:outputLabel value="打开方式："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <h:outputLabel value="外部打开" rendered="#{portalLinksBean.tdPortalLinks.linkType == 0}"/>
                    <h:outputLabel value="内部打开" rendered="#{portalLinksBean.tdPortalLinks.linkType == 1}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 30px">
                    <h:outputLabel value="链接图标："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <h:graphicImage url="/webFile#{portalLinksBean.tdPortalLinks.linkIcon}" width="100px" height="65px"/>
                    <!--<h:graphicImage url="/files/icons/5737a24c8be34943aed12ade771e91a7.jpg"/>-->
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     oncomplete="PF('ViewDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!-- 新增，修改 -->
    <p:dialog id="editDialog" header="链接维护" widgetVar="EditDialog"
              resizable="false" width="700" height="260" modal="true">
        <p:panelGrid style="width:100%;" id="editGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:15%;height: 40px">
                    <h:outputLabel value="* " style="color: red"/>
                    <h:outputLabel value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText value="#{portalLinksBean.tdPortalLinks.nums}" required="true" requiredMessage="序号不允许为空！"
                                 converterMessage="请输入正确的序号！" size="25" maxlength="2"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:15%;height: 40px">
                    <h:outputLabel value="* " style="color: red"/>
                    <h:outputLabel value="链接名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText value="#{portalLinksBean.tdPortalLinks.linkName}" required="true" requiredMessage="链接名称不允许为空！"
                                 size="25" maxlength="100"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:15%;height: 40px">
                    <h:outputLabel value="* " style="color: red"/>
                    <h:outputLabel value="链接地址："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText value="#{portalLinksBean.tdPortalLinks.linkUrl}" required="true" requiredMessage="链接地址不允许为空！"
                                 size="25" maxlength="100"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:15%;height: 40px">
                    <h:outputLabel value="* " style="color: red"/>
                    <h:outputLabel value="打开方式："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio  style="width: 240px;" value="#{portalLinksBean.tdPortalLinks.linkType}"
                                       required="true" requiredMessage="打开方式不允许为空！">
                        <f:selectItem itemLabel="内部打开" itemValue="1"/>
                        <f:selectItem itemLabel="外部打开" itemValue="0"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:15%;height: 40px">
                    <h:outputLabel value="* " style="color: red"/>
                    <h:outputLabel value="图标："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <h:panelGroup id="uploadGroup">
                        <p:commandButton value="上传" oncomplete="PF('fileUIdVar').show();"
                                         rendered="#{portalLinksBean.tdPortalLinks.linkIcon==null}"
                                         process="@this" update="fileUId"/>
                        <h:panelGroup rendered="#{portalLinksBean.tdPortalLinks.linkIcon!=null}">
                            <h:graphicImage url="/webFile#{portalLinksBean.tdPortalLinks.linkIcon}" width="100px" height="65px"/>
                            <p:spacer width="5"/>
                            <p:commandButton value="删除" update="uploadGroup"  process="@this"
                                             action="#{portalLinksBean.deleteDiskFile}" />
                        </h:panelGroup>
                        
                        <h:outputText style="color:blue;" value="　[推荐像素：250*70]" />
                    </h:panelGroup>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{portalLinksBean.saveAction}"
                                     process="@this,editGrid"
                                     update="dataTable,editGrid"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
                                     oncomplete="PF('EditDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

    <!-- 文件上传 -->
    <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false" modal="true">
        <p:fileUpload requiredMessage="请选择上传图标！" style="width:700px;" previewWidth="50"
                      fileUploadListener="#{portalLinksBean.handleFileUpload}"
                      fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                      label="选择文件" uploadLabel="上传" cancelLabel="取消"
                      sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                      validatorMessage="上传出错啦，重新上传！"
                      invalidFileMessage="无效的文件类型！只能上传gif,jpg,png等图片类型文件！"
                      process="@this" update="uploadGroup"
                      mode="advanced" dragDropSupport="true"
                      allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
    </p:dialog>

</ui:define>
</ui:composition>











