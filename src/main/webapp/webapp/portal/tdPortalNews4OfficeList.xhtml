<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">

	<script type="text/javascript">
		//<![CDATA[
		window.onload = function() {
			loadFile();
		}
		function loadFile() {
			var serverUrl = "#{request.scheme}" + "://"
					+ "#{request.serverName}" + ":" + "#{request.serverPort}"
					+ "#{request.contextPath}" + "/";
			var officeType = document.getElementById("mainForm:officeType").value;
			var officeName = document.getElementById("mainForm:officeName").value;
			var webUrl = serverUrl + "OfficeServlet?mtd=load&fname="
					+ officeName + officeType;

			mainForm.WebOffice.WebUrl = webUrl;
			mainForm.WebOffice.MaxFileSize = 4 * 1024;
			mainForm.WebOffice.Language = "CH";
			mainForm.WebOffice.Template = "";
			mainForm.WebOffice.RecordID = officeName;
			mainForm.WebOffice.officeName = officeName + officeType;
			mainForm.WebOffice.officeType = officeType;
			mainForm.WebOffice.UserName = "chiscdc";
			mainForm.WebOffice.EditType = "2,0";
			mainForm.WebOffice.height = "600";
			mainForm.WebOffice.ShowToolBar = "0";
			mainForm.WebOffice.Param1="true";
			mainForm.WebOffice.WebOpen;
		}
		//]]>
	</script>
	<style type="text/css">
.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 5px;
	padding-right: 0px;
}

.ui-panelgrid td {
	border-width: 1px;
}

a:link {
	color: blue
} /* 未被访问的链接     蓝色 */
a:visited {
	color: red
} /* 已被访问过的链接   蓝色 */
a:hover {
	color: darkorange;
	text-decoration: none;
} /* 鼠标悬浮在上的链接 蓝色 */
a:active {
	color: blue
} /* 鼠标点中激活链接   蓝色 */
</style>
	<table width="80%" cellspacing="0" cellpadding="0">
		<tr
			style="text-align: center;color: black;font-weight: bold;font-size: x-large;">
			<td style="margin-top: 35px;padding-bottom:35px;padding-top:120px;font-family:SimHei;" colspan="2"><h:outputText
					value="#{tdPortalNewsViewBean.tdPortalNews.newsTitle}" /></td>
		</tr>
		<tr>
			<td style="text-align: left;">
				<h:panelGrid columns="2">
				<p:outputLabel style="color:gray;" value="重要程度：" /> 	
					<p:rating value="#{tdPortalNewsViewBean.tdPortalNews.newsLevel}" 
						readonly="true" />
				</h:panelGrid>
			</td>
			<td style="padding-right: 10%;text-align: right;"><p:outputLabel
					style="color:gray;" value="发布时间：" /> <p:outputLabel
					style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.newsDate}">
					<f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8"
						locale="cn" />
				</p:outputLabel> <p:outputLabel value="　　" /> <p:outputLabel style="color:gray;"
					value="发布人：" /> <p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.tsUserInfo.username}" />
				<p:outputLabel value="　　" /> <p:outputLabel style="color:gray;"
					value="信息类型：" /> <p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsType.typeName}" />
			</td>
		</tr>
		<tr>
			<td colspan="2" style="text-align: left;">
				<p:outputLabel value="附件下载：" />
				<p:outputLabel value="无"
					rendered="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsAnnexes.size() == 0}" />
				<c:forEach
					items="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsAnnexes}"
					var="v">
					<p:commandLink ajax="false" style="color:blue;" value="#{v.annexName}" immediate="true" process="@this"
						onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
						<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
						<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
						<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
					</p:commandLink>
					<p:outputLabel value="　　" />
				</c:forEach> <br />
				<hr />
			</td>
		</tr>
		<tr>
			<td colspan="2">
			<br /> 
				<div
					style="overflow-x:hidden;overflow-y:auto;width: 99%;position: absolute; ">
					<a href="/resources/files/extension.crx"
						style="font-weight: bold;font-size: 6px;">请点击此处安装插件，安装完成后请重启浏览器。</a>
				</div> <h:inputHidden id="officeName"
					value="#{tdPortalNewsViewBean.officeName}" /> <h:inputHidden
					id="officeType" value="#{tdPortalNewsViewBean.officeType}" /> <script
					type="text/javascript"
					src="/resources/file/chiscdcWebOfficePlus.crx"></script>
			</td>
		</tr>
	</table>
	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
</ui:composition>