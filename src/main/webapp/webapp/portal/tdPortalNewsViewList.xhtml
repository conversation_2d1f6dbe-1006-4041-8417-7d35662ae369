<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    	<style type="text/css">
			.mainCss {
				background: white;
				width:1024px;
				margin: 0 auto;
				margin-bottom:10px;
			}
		</style>
		<link rel="stylesheet" href="/resources/css/que/default.css" />
		<link rel="stylesheet" href="/resources/css/que/public.css" />
		<link rel="stylesheet" href="/resources/css/que/private.css" />
		<link rel="stylesheet" href="/resources/css/slowque/private.css" />
    </h:head>
    
    <h:body>
    	<h:form id="mainForm">
    		<link rel="stylesheet" href="/resources/css/que/newsolid_104.css" />
    		<h:outputStylesheet name="css/default.css"/>
            <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
            <h:outputStylesheet name="css/ui-tabs.css"/>
            <ui:insert name="insertScripts"/>
            
        <div id="divNotRun" style="height:100px; text-align:center; display:none;"></div>
		<div id="jqContent" class="" style="text-align: left; ">
            <div id="headerCss" style="overflow-x: hidden; overflow-y: hidden; ">
			<div id="ctl00_header"></div>
			</div>
            <div class="mainCss" >
            <div id="mainInner">
			<table width="100%" height="100%" border="0" cellspacing="0"
				cellpadding="0">
				<tr>
					<td align="center" valign="middle">
						<p:outputPanel rendered="#{tdPortalNewsViewBean.newsType == 0}">
							<ui:include src="tdPortalNews4TxtList.xhtml" />
						</p:outputPanel>
						<p:outputPanel rendered="#{tdPortalNewsViewBean.newsType == 1 
							or tdPortalNewsViewBean.newsType == 2 
							or tdPortalNewsViewBean.newsType == 3}">
							<ui:include src="tdPortalNews4OfficeList.xhtml" />
						</p:outputPanel>
						<p:outputPanel rendered="#{tdPortalNewsViewBean.newsType == 4}">
							<ui:include src="tdPortalNews4ImgList.xhtml" />
						</p:outputPanel>
						<p:outputPanel rendered="#{tdPortalNewsViewBean.newsType == 5}" id="pdfPanel">
							<ui:include src="tdPortalNews4PDFList.xhtml" />
						</p:outputPanel>
					</td>
				</tr>
			</table>
			</div>
			<div id="footercss">
				<div id="footerLeft"></div>
				<div id="footerCenter"></div>
				<div id="footerRight"></div>

				<input type="hidden" id="urlParams" /> <input type="hidden" id="fromUrl" /><input type="hidden" id="ansQueId" /><input type="hidden" id="userId" />
			</div>
			<div style="clear: both; height: 10px;"></div>
			<div style="height: 20px;">&nbsp;</div>
			</div>
		</div>
    	</h:form>
    </h:body>
</f:view>
</html>