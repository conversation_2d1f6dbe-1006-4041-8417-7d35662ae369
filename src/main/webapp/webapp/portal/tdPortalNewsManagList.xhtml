<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdPortalNewsManagBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/portal/tdPortalNewsManagEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/portal/tdPortalNewsManagView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">

        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

        <style type="text/css">
            .ui-picklist .ui-picklist-list{
                text-align:left;
                height: 350px;
                width: 340px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
            
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="信息管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdPortalNewsManagBean.searchVerifyAction}" update="dataTable"
					process="@this,searchInfoTitle,searchStartDate,searchEndDate" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:10%;">
                <h:outputText value="信息分类：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:20%;">
                <h:panelGrid columns="2" style="border-color: #ffffff;padding: 0 0 0 0;height: 30px;cellpadding:0;">
                    <p:inputText id="typeName" value="#{tdPortalNewsManagBean.searchNewsTypeName}" readonly="true" style="width:160px;"/>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"
                                   style="position: relative;left: -30px;top:0px;" oncomplete="PF('OveralPanelEdit').show()"/>
                </h:panelGrid>
                <p:overlayPanel id="overalPanel" for="typeName" dynamic="false" style="width:280px;"  widgetVar="OveralPanelEdit" >
                    <p:tree value="#{tdPortalNewsManagBean.portalTypeTree}" var="node"
                            selectionMode="single" id="choiceTree"
                            style="width: 250px;height: 300px;overflow-y: auto;">
                        <p:ajax event="select" oncomplete="PF('OveralPanelEdit').hide();" process="@this"
                                listener="#{tdPortalNewsManagBean.onSearchTypeNodeSelect}"
                                update=":tabView:mainForm:typeName" />
                        <p:treeNode>
                            <h:outputText value="#{node.typeName}"/>
                        </p:treeNode>
                    </p:tree>
                </p:overlayPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:10%;">
                <h:outputText value="信息标题：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:20%;">
                <p:inputText value="#{tdPortalNewsManagBean.searchInfoTitle}" id="searchInfoTitle" maxlength="25" style="width: 160px;" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:10%;">
                <h:outputText value="发布日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:column style="text-align:left;padding-left:8px;">
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="searchStartDate"
                                navigator="true" yearRange="c-10:c+10"  size="11" showButtonPanel="true"
                                converterMessage="发布日期，格式输入不正确！"
                                value="#{tdPortalNewsManagBean.searchStartDate}"/>
                    <p:outputLabel value="～"/>
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="searchEndDate"
                                navigator="true" yearRange="c-10:c+10"  size="11" showButtonPanel="true"
                                converterMessage="发布日期，格式输入不正确！"
                                value="#{tdPortalNewsManagBean.searchEndDate}"/>
                </p:column>

            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列  颜色939393 -->
    <ui:define name="insertDataTable">
        <p:column headerText="信息分类" style="width:150px;text-align: center;">
            <h:outputText value="#{itm[0]}" />
        </p:column>
        <p:column headerText="信息标题" style="padding-left:3px;word-wrap: break-word;word-break: break-all;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="公告栏" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[2]=='1'?'√':'×'}" />
        </p:column>
        <p:column headerText="是否置顶" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[3]=='1'?'√':'×'}" />
        </p:column>
        <p:column headerText="发布部门" style="width:100px;text-align: center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="发布人" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="发布日期" style="width:100px;text-align: center;" >
            <h:outputText value="#{itm[6]}" >
            </h:outputText>
        </p:column>
        <p:column headerText="操作" style=";width: 120px;padding-left: 3px;">
            <p:commandLink value="删除" action="#{tdPortalNewsManagBean.delAction}" update="dataTable" process="@this"
                   rendered="#{itm[8]=='1'}"  >
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{tdPortalNewsManagBean.rid}" value="#{itm[7]}"/>
            </p:commandLink>
            <p:spacer width="5px;" rendered="#{itm[8]=='1'}"  />
            <p:commandLink value="修改" action="#{tdPortalNewsManagBean.modInitAction}" resetValues="true" update=":tabView" process="@this" rendered="#{itm[8]=='1'}" >
                <f:setPropertyActionListener target="#{tdPortalNewsManagBean.rid}" value="#{itm[7]}"/>
            </p:commandLink>
            <p:spacer width="5px;" rendered="#{itm[8]=='1'}" />
            <p:commandLink value="详情" action="#{tdPortalNewsManagBean.viewInitAction}" resetValues="true" update=":tabView" process="@this" >
                <f:setPropertyActionListener target="#{tdPortalNewsManagBean.rid}" value="#{itm[7]}"/>
            </p:commandLink>
            <p:spacer width="5px;"/>
        </p:column>
    </ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherMainContents">

    </ui:define>

</ui:composition>











