<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
        <script type="text/javascript" src="/resources/js/fckeditor/fckeditor.js" />
        <style type="text/css">
            .ui-panelgrid td {
                padding-top: 2px;
                padding-bottom: 2px;
                padding-left: 5px;
                padding-right: 0px;
            }
            .ui-panelgrid td {
                border-width: 1px;
            }

            #tipDiv a:link {color: blue}     /* 未被访问的链接     蓝色 */
            #tipDiv a:visited {color: red}  /* 已被访问过的链接   蓝色 */
            #tipDiv a:hover {
                color: darkorange;
                text-decoration: none;

            }    /* 鼠标悬浮在上的链接 蓝色 */
            #tipDiv a:active {color: blue}   /* 鼠标点中激活链接   蓝色 */

        </style>

        <script language="javascript"  >
            //<![CDATA[
            function initFckVal(){
                if(document.getElementById("tabView:viewForm:newsCont").value!=""){
                    document.getElementById("myTextarea").value=document.getElementById("tabView:viewForm:newsCont").value;
                }
            }
            function loadFCK(){
                var oFCKeditor = new FCKeditor('myTextarea') ;
                oFCKeditor.BasePath = "/resources/js/fckeditor/" ;
                oFCKeditor.Width="100%";
                oFCKeditor.Height="350";
                oFCKeditor.ReplaceTextarea() ;
            }

            function setContentVal()    {
                document.getElementById("tabView:viewForm:newsCont").value
                        =FCKeditorAPI.GetInstance('myTextarea').GetXHTML(true);
            }

            window.onload = function()  {
                showOffBtn();
            }

            function afterSubmit(){
                showOffBtn();
            }

            function beforeSubmit()  {
                if(document.getElementById("tabView:viewForm:newsType:0").checked)    {
                    setContentVal();
                }else if(document.getElementById("tabView:viewForm:newsType:1").checked||
                        document.getElementById("tabView:viewForm:newsType:2").checked||
                        document.getElementById("tabView:viewForm:newsType:3").checked){
                    saveFile();
                }
            }

            function hideOffice() {
    			if (null != document.getElementById("WebOffice")) {
    				document.getElementById("WebOffice").style.width = "0px";
    				document.getElementById("WebOffice").style.height = "0px";
    			}
    		}

    		function showOffice() {
    			if (null != document.getElementById("WebOffice")) {
    				document.getElementById("WebOffice").style.width = "100%";
    				document.getElementById("WebOffice").style.height = "100%";
    			}
    		}
            
            function showOffBtn()   {
            	hideOffice();
                if(document.getElementById("tabView:viewForm:newsType:0").checked)    {
                    document.getElementById("tabView:viewForm:photoRow").style.display="none";
                    document.getElementById("tabView:viewForm:officeRow").style.display="none";
                    document.getElementById("tabView:viewForm:HtextRow").style.display="";
                    document.getElementById("tabView:viewForm:HtextRow2").style.display="";
                    document.getElementById("tabView:viewForm:pdfPanel").style.display = "none";
                    initFckVal();
                    loadFCK();
                }else if(document.getElementById("tabView:viewForm:newsType:1").checked ||
                        document.getElementById("tabView:viewForm:newsType:2").checked ||
                        document.getElementById("tabView:viewForm:newsType:3").checked)    {
                    document.getElementById("tipDiv").innerHTML = "";
                    document.getElementById("tabView:viewForm:photoRow").style.display="none";
                    document.getElementById("tabView:viewForm:pdfPanel").style.display = "none";

                    document.getElementById("tabView:viewForm:officeRow").style.display="";

                    document.getElementById("tabView:viewForm:HtextRow").style.display="none";

                    document.getElementById("tabView:viewForm:HtextRow2").style.display="";
                    showOffice();
                    try{
                        setTimeout("loadFile()",100);
                    }catch(e){
                    }
                }else if(document.getElementById("tabView:viewForm:newsType:5").checked){
                    document.getElementById("tabView:viewForm:photoRow").style.display = "none";
                    document.getElementById("tabView:viewForm:officeRow").style.display = "none";
                    document.getElementById("tabView:viewForm:HtextRow").style.display = "none";
                    document.getElementById("tabView:viewForm:HtextRow2").style.display = "";
                    document.getElementById("tabView:viewForm:pdfPanel").style.display = "";
                    showPDFJs();
                }else{
                    document.getElementById("tabView:viewForm:photoRow").style.display="";
                    document.getElementById("tabView:viewForm:officeRow").style.display="none";
                    document.getElementById("tabView:viewForm:HtextRow").style.display="none";
                    document.getElementById("tabView:viewForm:HtextRow2").style.display="none";
                    document.getElementById("tabView:viewForm:pdfPanel").style.display = "none";
                }
            }

            function loadFile() {
                try{
                    var serverUrl = "#{request.scheme}" + "://" + "#{request.serverName}"+":"+"#{request.serverPort}"+"#{request.contextPath}"+"/";
                    var fileType= document.getElementById("tabView:viewForm:fileType").value;
                    var fileName= document.getElementById("tabView:viewForm:fileName").value;
                    var rid= document.getElementById("tabView:viewForm:rid").value;
                    var webUrl = serverUrl+"OfficeServlet?mtd=load";
                    webUrl += "&fname="+fileName+fileType;

                    WebOffice.WebUrl=webUrl;
                    WebOffice.MaxFileSize = 4 * 1024;
                    WebOffice.Language="CH";
                    WebOffice.Template="";
                    WebOffice.RecordID=fileName;
                    WebOffice.FileName=fileName+fileType;
                    WebOffice.FileType=fileType;
                    WebOffice.UserName="chiscdc";
                    WebOffice.EditType="1,1";
                    WebOffice.showToolBar='0';
                    WebOffice.Param1="true";
                    WebOffice.WebOpen;
                }catch(e){
                    var divStr = "";
                    divStr += '<a href="/resources/files/chiscdcWebOfficePlus.crx" ';
                    divStr += ' style="font-weight: bold;font-size: 12px;" >';
                    divStr += '请点击此处安装插件，安装完成后请重启浏览器。</a>';
                    document.getElementById("tipDiv").innerHTML = divStr;

                    if($('.ui-outputlabel').html() == 'Office文件信息')    {
                        var offSetVal = $('.ui-outputlabel').offset().top;
                        document.getElementById("tipDiv").style.top=(offSetVal+50)+"px";
                    }
                }
            }

            function showPDFJs()    {
                var dynHtml = document.getElementById("tabView:viewForm:pdfhtml").value;
                document.getElementById("tabView:viewForm:pdfCol").innerHTML = dynHtml;
            }
            //]]>
        </script>
        <style type="text/css">
            .ui-tree .ui-tree-container{
                overflow-x:hidden;
            }
        </style>
        <h:form id="viewForm">
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
			id="editPanel">
			<f:facet name="header">
				<p:row>
					<p:column colspan="2"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="信息管理" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="5"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				  <p:column style="text-align:left;padding-left:5px;" colspan="2" >
					<p:commandButton value="阅读情况" icon="ui-icon-search" id="readBtn" rendered="#{tdPortalNewsManagBean.ifHasAdminInType}"
						update="readDialog" oncomplete="PF('ReadDialog').show();"
						process="@this" action="#{tdPortalNewsManagBean.readCaseAction}" />
					<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" update=":tabView"  
                                     action="#{tdPortalNewsManagBean.backAction}" immediate="true" process="@this" />
                </p:column>
			</h:panelGrid>
		</p:outputPanel>
        <p:panelGrid style="width:100%;height:100%;" id="editPanel2" >
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                    <h:inputHidden id="fileName" value="#{tdPortalNewsManagBean.fileName}" />
                    <h:inputHidden id="fileType" value="#{tdPortalNewsManagBean.fileType}" />
                    <h:inputHidden id="rid" value="#{tdPortalNewsManagBean.tdPortalNews.rid}" />
                    <h:outputText value="信息分类："/>
                </p:column>
                <p:column style="text-align:left;height:25px;">
                    <p:outputLabel value="#{tdPortalNewsManagBean.tdPortalNews==null?'':tdPortalNewsManagBean.tdPortalNews.tdPortalNewsType.typeName}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                    <h:outputText value="发布范围："/>
                </p:column>
                <p:column style="text-align:left;word-wrap: break-word;word-break: break-all;" >
                        <p:outputLabel id="selectNames" value="#{tdPortalNewsManagBean.selectOfficeNames}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                    <h:outputText value="信息标题："/>
                </p:column>
                <p:column style="text-align:left;padding-left:12px;" >
                    <p:outputLabel value="#{tdPortalNewsManagBean.tdPortalNews.newsTitle}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                    <h:outputText value="重要程度："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;word-wrap: break-word;word-break: break-all;" >
                    <p:rating value="#{tdPortalNewsManagBean.tdPortalNews.newsLevel}" stars="5" cancel="false"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                   <h:outputText value="添加到公告栏："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" >
                    <p:outputLabel value="#{tdPortalNewsManagBean.tdPortalNews.ifNotice=='1'?'√':'×'}" />
                    <p:outputLabel rendered="#{tdPortalNewsManagBean.tdPortalNews.ifNotice==1}"
                    	 value="  滚动#{tdPortalNewsManagBean.tdPortalNews.rollDays}天" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                   <h:outputText value="是否置顶："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" >
                    <p:outputLabel value="#{tdPortalNewsManagBean.tdPortalNews.ifZd=='1'?'√':'×'}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:20%;height:25px;">
                    <h:outputText value="信息类型："/>
                </p:column>
                <p:column style="text-align:left;" >
                    <p:outputLabel value="#{tdPortalNewsManagBean.tdPortalNews.newsType=='0'?'超文本':(tdPortalNewsManagBean.tdPortalNews.newsType=='1'?'Word':
                     (tdPortalNewsManagBean.tdPortalNews.newsType=='2'?'Excel':(tdPortalNewsManagBean.tdPortalNews.newsType=='3'?'PPT':(tdPortalNewsManagBean.tdPortalNews.newsType=='4'?'图片':'PDF'))))}" />
                    <p:selectOneRadio value="#{tdPortalNewsManagBean.tdPortalNews.newsType}" style="width:408px;display:none;"  id="newsType"  >
                        <f:selectItems value="#{tdPortalNewsManagBean.newsType}" />
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row id="HtextRow2">
                <p:column style="text-align:left;padding-left:3px;" colspan="2" >
                    <h:panelGroup id="downId">
                        <p:dataTable value="#{tdPortalNewsManagBean.annexList}" var="v"
                                     style="width:100%" type="ordered" emptyMessage="没有上传的文件记录！">
                            <p:column headerText="文件名称" style="width: 45%;">
                                <p:outputLabel value="#{v.annexName}" />
                            </p:column>
                            <p:column headerText="操作" style="width: 15%;">
                                <p:commandLink ajax="false" value="下载"  immediate="true" process="@this"
									onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
									<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
									<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
									<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
								</p:commandLink>
                                <p:spacer width="5" />
                            </p:column>
                        </p:dataTable>
                        <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId"
                                  resizable="false">
                            <p:fileUpload requiredMessage="请选择要文件上传！"
                                  fileUploadListener="#{tdPortalNewsManagBean.handleFileUpload}"
                                  fileLimit="3" fileLimitMessage="最多只能上传3个文件！" label="选择文件"
                                  invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
                                  style="width:600px;" previewWidth="120" cancelLabel="取消"
                                  update=":tabView:viewForm:downId" uploadLabel="上传"
                                  dragDropSupport="true" mode="advanced" sizeLimit="10485760" />
                        </p:dialog>
                    </h:panelGroup>
                </p:column>
            </p:row>
            <p:row id="HtextRow" >
                <p:column colspan="2" >
                    <p:row>
                        <p:column style="width: 100%;"  >
                            <textarea id="myTextarea" name="myTextarea"></textarea>
                            <h:inputHidden value="#{tdPortalNewsManagBean.tdPortalNews.newsCont}" id="newsCont" />
                        </p:column>
                    </p:row>
                </p:column>
            </p:row>

            <p:row id="officeRow" >
                <p:column style="text-align:left;padding-left:3px;" colspan="2" >
                    <h:form action="/OfficeServlet" method="post" id="webform" >
                        <p:panelGrid style="width:100%;" id="editGrid">
                            <f:facet name="header">
                                <p:row>
                                    <p:column style="padding-left: 3px;text-align:left;" >
                                        <p:outputLabel value="Office文件信息" />
                                    </p:column>
                                </p:row>
                            </f:facet>
                            <p:row >
                                <p:column style="height:1000px;" >
                                   <div id="DivID" class="DivID" style="height:980px;" >
                                   		<object id="WebOffice" width="100%" height="100%" classid="clsid:8B23EA28-2009-402F-92C4-59BE0E063499" 
											codebase="/resources/component/office/iWebOffice2009.cab#version=10,4,2,0" ></object>
                                   </div>
                                    <center>
                                        <div id="tipDiv" style="overflow-x:hidden;overflow-y:auto;width: 99%;position: absolute;top:350px; "  >
                                            <a  href="/resources/files/extension.crx"    style="font-weight: bold;font-size: 12px;" >请点击此处安装插件，安装完成后请重启浏览器。</a>
                                        </div>
                                    </center>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </h:form>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:layout style="height:500px;width: 99%;overflow-x: hidden;"  id="photoRow"    >
            <p:layoutUnit  position="west"  size="300" gutter="20"
                           style="border:0px;"  collapsible="false"  header="图片列表" >
                <p:tree dynamic="true" value="#{tdPortalNewsManagBean.photoTree}" var="node"
                        selectionMode="single" id="photoTree"
                        style="width: 98%;overflow-y: auto;height: 98%;">
                    <p:ajax event="select"  process="@this"
                            listener="#{tdPortalNewsManagBean.onPhotoNodeSelect}" />
                    <p:treeNode>
                        <h:outputText value="#{node.annexName}"/>
                    </p:treeNode>
                </p:tree>
            </p:layoutUnit>
            <p:layoutUnit position="center" style="border:0px;text-align: center;" header="图片效果展示"   gutter="20">
                <p:panelGrid style="width: 100%;text-align: center;" >
                    <p:row>
                        <p:column style="text-align:left;" colspan="4" >
                            <p:galleria value="#{tdPortalNewsManagBean.annexList}" var="image" style="width:100%;" panelHeight="390"
                                         showCaption="true" effectSpeed="2000"   >
                                <p:graphicImage  value="/webFile#{image.annexAddr}" width="100%"
                                                  alt="#{image.annexDesc}" title="#{image.annexName}"   />
                            </p:galleria>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </p:layoutUnit>
        </p:layout>
        <p:panelGrid style="width:100%;" id="pdfPanel">
            <p:row >
                <p:column style="text-align:left;padding-left:3px;" >
                    <p:commandButton value="上一个"  icon="ui-icon-arrowthick-1-n" process="@this"  update="pdfPanel" oncomplete="showPDFJs()"
                                     disabled="#{(tdPortalNewsManagBean.totalPdfPage gt 0 and tdPortalNewsManagBean.currPdfPage gt 1)?false:true}"  action="#{tdPortalNewsManagBean.upPdf}" /><p:spacer width="5"/>
                    <p:commandButton value="下一个"  icon="ui-icon-arrowthick-1-s" process="@this"   update="pdfPanel" oncomplete="showPDFJs()"
                                     disabled="#{(tdPortalNewsManagBean.totalPdfPage gt 0 and tdPortalNewsManagBean.currPdfPage lt tdPortalNewsManagBean.totalPdfPage)?false:true}" action="#{tdPortalNewsManagBean.downPdf}" /><p:spacer width="5"/>
                    当前数：<p:outputLabel value="#{tdPortalNewsManagBean.currPdfPage}" /><p:spacer width="5"/>
                    总页数：<p:outputLabel value="#{tdPortalNewsManagBean.totalPdfPage}" />
                    <h:inputHidden id="pdfhtml" value="#{tdPortalNewsManagBean.pdfhtml}"/>
                </p:column>
            </p:row>
            <p:row >
                <p:column style="text-align:left;padding-left:3px;" id="pdfCol">
                </p:column>
            </p:row>
        </p:panelGrid>


        <p:dialog id="photoDes" header="图片信息" widgetVar="photoDescript" resizable="false"
                  modal="true" width="770" style="height: 660px;" >
            <p:panelGrid >
                <p:row>
                    <p:column style="text-align:right;padding-right:8px;" colspan="2"  >
                        <div style="overflow-x:auto;overflow-y:auto;width: 730px;height:400px;">
                        <p:imageCropper value="#{tdPortalNewsManagBean.croppedImage}" id="croppedImage" rendered="#{tdPortalNewsManagBean.tdPortalNewsAnnex!=null}"
                                        image="/webFile#{tdPortalNewsManagBean.tdPortalNewsAnnex.annexAddr}"
                                initialCoords="0,0,720,380"  />
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:8px;width: 30%;"   >
                       图片描述：
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;height: 100px;" >
                        <p:inputTextarea id="annexDesc" rows="8" cols="60" autoResize="false"
                                         counter="display" maxlength="200"
                                         value="#{tdPortalNewsManagBean.tdPortalNewsAnnex.annexDesc}"
                                         counterTemplate="还可以输入{0}个字" />
                        <br />
                        <h:outputText id="display" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: center;" colspan="2" >
                        <p:commandButton value="确认" icon="ui-icon-check" id="photoSaveBtn"  process="@this,croppedImage,annexDesc" action="#{tdPortalNewsManagBean.crop}"
                                         update="photoRow"    >
                        </p:commandButton>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="photoBackBtn" onclick="PF('photoDescript').hide();" immediate="true"/>
                        <p:spacer width="5" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
       	<p:dialog id="readDialog" widgetVar="ReadDialog" header="阅读情况" resizable="false" width="600" modal="true" height="450">
			<p:dataTable var="itm" value="#{tdPortalNewsManagBean.readStateList}" 
			rowIndexVar="R"
			id="dataTable2" emptyMessage="没有您要找的记录！">
				<p:column headerText="序号" style="width:40px;text-align:center;">
					<h:outputLabel value="#{R+1}" />
				</p:column>
				<p:column headerText="所属科室" style="padding-left:8px;">
					<h:outputLabel value="#{itm[0]}" />
				</p:column>
				<p:column headerText="阅读人" style="width:100px;text-align:center;">
					<h:outputLabel value="#{itm[1]}" />
				</p:column>
				<p:column headerText="阅读状态" style="width:60px;text-align:center;">
					<h:outputLabel value="#{itm[2]=='1'?'已阅':'未阅'}" />
				</p:column>
				<p:column headerText="阅读日期" style="width:100px;text-align:center;">
					<h:outputLabel value="#{itm[3]}" >
						<f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8" />
					</h:outputLabel>
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" onclick="PF('ReadDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
        </h:form>
</ui:composition>
