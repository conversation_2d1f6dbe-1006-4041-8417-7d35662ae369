<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">
	<script type="text/javascript"
		src="/resources/js/fckeditor/fckeditor.js" />
 	<h:outputScript library="js" name="namespace.js"/>
    <h:outputScript name="js/validate/system/validate.js"/>
	<style type="text/css">
#tipDiv a:link {
	color: blue
}

/* 未被访问的链接     蓝色 */
#tipDiv a:visited {
	color: red
}

/* 已被访问过的链接   蓝色 */
#tipDiv a:hover {
	color: darkorange;
	text-decoration: none;
}

/* 鼠标悬浮在上的链接 蓝色 */
#tipDiv a:active {
	color: blue
}
</style>


	<script language="javascript">
		//<![CDATA[
		/**保存按钮点击执行的脚本，用于被重写*/
		function zwx_flow_save_before() {
			beforeSubmit();
		}
		/**保存按钮执行完成的脚本，用于被重写*/
		function zwx_flow_save_complete() {
			afterSubmit();
		}
		/**提交按钮点击执行的脚本，用于被重写*/
		function zwx_flow_submit_before() {
			beforeSubmit();

		}
		/**提交按钮执行完成的脚本，用于被重写*/
		function zwx_flow_submit_complete() {
			loadDiaJs();
		}
		/**提交按钮执行的弹出框返回事件执行后的脚本，用于被重写*/
		function zwx_flow_submit_dialogrtn() {
			afterSubmit();
			showOffice();
		}

		function loadFCK() {
			var oFCKeditor = new FCKeditor('myTextarea');
			oFCKeditor.BasePath = "/resources/js/fckeditor/";
			oFCKeditor.Width = "100%";
			oFCKeditor.Height = "350";
			oFCKeditor.ReplaceTextarea();
		}

		function setContentVal() {
			document.getElementById("tabView:editForm:newsCont").value = FCKeditorAPI
					.GetInstance('myTextarea').GetXHTML(true);
		}

		window.onload = function() {
			showOffBtn();
		}

		function setLmWidth() {
			var size = document.getElementById("tabView:editForm:colSize").value;
			if (size < 5) {
				var typeWidth = document
						.getElementById("tabView:editForm:typeId").style.width;
				typeWidth = typeWidth.replace("px", "");
				document.getElementById("tabView:editForm:typeId").style.width = 110
						* size + "px";
			}
		}

		function afterSubmit() {
			showOffBtn();
		}

		function beforeSubmit() {
			if (document.getElementById("tabView:editForm:newsType:0").checked) {
				setContentVal();
			} else if (document.getElementById("tabView:editForm:newsType:1").checked
					|| document.getElementById("tabView:editForm:newsType:2").checked
					|| document.getElementById("tabView:editForm:newsType:3").checked) {
				saveFile();
			}
		}

		function showOffBtn() {
			hideOffice();
			if (document.getElementById("tabView:editForm:newsType:0").checked) {
				document.getElementById("tabView:editForm:photoRow").style.display = "none";
				document.getElementById("tabView:editForm:photoPanel").style.display = "none";
				document.getElementById("tabView:editForm:pdfPanel").style.display = "none";
				document.getElementById("tabView:editForm:HtextRow").style.display = "";
				document.getElementById("tabView:editForm:HtextRow2").style.display = "";
				if (document.getElementById("tabView:editForm:newsCont").value != "") {
					document.getElementById("myTextarea").value = document
							.getElementById("tabView:editForm:newsCont").value;
				}
				loadFCK();
			}  else if (document.getElementById("tabView:editForm:newsType:2").checked) {
				document.getElementById("tabView:editForm:photoRow").style.display = "none";
				document.getElementById("tabView:editForm:photoPanel").style.display = "none";
				document.getElementById("tabView:editForm:HtextRow").style.display = "none";
				document.getElementById("tabView:editForm:HtextRow2").style.display = "";
				document.getElementById("tabView:editForm:pdfPanel").style.display = "";
				showPDFJs();
			} else {
				document.getElementById("tabView:editForm:photoRow").style.display = "";
				document.getElementById("tabView:editForm:photoPanel").style.display = "";
				document.getElementById("tabView:editForm:HtextRow").style.display = "none";
				document.getElementById("tabView:editForm:HtextRow2").style.display = "none";
				document.getElementById("tabView:editForm:pdfPanel").style.display = "none";
			}
			setLmWidth();
		}

		function loadFile() {
			try {
				var serverUrl = "#{request.scheme}" + "://"
						+ "#{request.serverName}" + ":"
						+ "#{request.serverPort}" + "#{request.contextPath}"
						+ "/";
				var fileType = document
						.getElementById("tabView:editForm:fileType").value;
				var fileName = document
						.getElementById("tabView:editForm:fileName").value;
				var webUrl = serverUrl + "OfficeServlet?mtd=load" + "&fname="
						+ fileName + fileType;
				WebOffice.WebUrl = webUrl;
				WebOffice.MaxFileSize = 100 * 1024;
				WebOffice.Language = "CH";
				WebOffice.Template = "";
				WebOffice.RecordID = fileName;
				WebOffice.FileName = fileName + fileType;
				WebOffice.FileType = fileType;
				WebOffice.UserName = "#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.username}";
				WebOffice.EditType = "1,1";
				WebOffice.showToolBar = '0';
				WebOffice.ShowMenu = '0';
				WebOffice.Param1="true";
				WebOffice.WebOpen;
			} catch (e) {
				/* var divStr = "";
				divStr += '<a href="/resources/files/extension.crx" ';
            divStr += ' style="font-weight: bold;font-size: 12px;" >';
				divStr += '请点击此处安装插件，安装完成后请重启浏览器。</a>';
				document.getElementById("tipDiv").innerHTML = divStr;

				if ($('.ui-outputlabel').html() == 'Office文件信息') {
					var offSetVal = $('.ui-outputlabel').offset().top;
					document.getElementById("tipDiv").style.top = (offSetVal + 50)
							+ "px";
				} */
			}
		}

		function saveFile() {
			var fileType = document.getElementById("tabView:editForm:fileType").value;
			var fileName = document.getElementById("tabView:editForm:fileName").value;
			WebOffice.WebClearMessage();
			var serverUrl = "#{request.scheme}" + "://"
					+ "#{request.serverName}" + ":" + "#{request.serverPort}"
					+ "#{request.contextPath}" + "/";
			var webUrl = serverUrl + "OfficeServlet?mtd=savefile&fname="
					+ fileName + fileType;
			WebOffice.WebUrl = webUrl;
			WebOffice.Param1="true";
			WebOffice.WebSave;

		}

		function hideOffice() {
			if (null != document.getElementById("WebOffice")) {
				document.getElementById("WebOffice").style.width = "0px";
				document.getElementById("WebOffice").style.height = "0px";
			}
		}
		function showOffice() {
			if (null != document.getElementById("WebOffice")) {
				document.getElementById("WebOffice").style.width = "100%";
				document.getElementById("WebOffice").style.height = "100%";
			}
		}

		function loadDiaJs() {
			$(".ui-dialog-titlebar-close").click(function() {
				showOffice();
			})
		}

		//关闭上传附件弹出框时，增加显示OFFICE控件事件
		function loadCloseD() {
			$(".ui-icon-closethick").click(function() {
				showOffice();
			})
		}

		function showPDFJs() {
			var dynHtml = document.getElementById("tabView:editForm:pdfhtml").value;
			document.getElementById("tabView:editForm:pdfCol").innerHTML = dynHtml;
		}

		//]]>
	</script>
	<style type="text/css">
.ui-tree .ui-tree-container {
	overflow-x: hidden;
}
</style>
	<p:panelGrid style="width:100%;height:100%;margin-top:3px;" id="editPanel">
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;height:30px;">
				<h:inputHidden id="fileName"
					value="#{tdPortalNewsPublishBean.fileName}" />
				<h:inputHidden id="fileType"
					value="#{tdPortalNewsPublishBean.fileType}" />
				<h:inputHidden id="colSize"
					value="#{tdPortalNewsPublishBean.colNewsSize}" />
                <h:panelGroup id="requireBewType">
                    <h:outputText value="*" style="color: red" rendered="#{tdPortalNewsPublishBean.showNewsType  == 1}"/>
                </h:panelGroup>
				<h:outputText value="信息分类：" />
			</p:column>
			<p:column style="text-align:left;height:30px;">
				<p:selectOneRadio id="typeId" disabled="#{tdPortalNewsPublishBean.showNewsType  == 0}"
					value="#{tdPortalNewsPublishBean.tdPortalNews.tdPortalNewsType.rid}"
					layout="grid" columns="5" style="width:#{tdPortalNewsPublishBean.colNews.size() gt 4?'550':tdPortalNewsPublishBean.colNews.size()*110}px;"
					rendered="#{!tdPortalNewsPublishBean.ifHasNewsType}">
					<f:selectItems value="#{tdPortalNewsPublishBean.colNews}" />
				</p:selectOneRadio>
				<p:outputLabel
					value="#{tdPortalNewsPublishBean.tdPortalNews.tdPortalNewsType.typeName}"
					rendered="#{tdPortalNewsPublishBean.ifHasNewsType}" />
			</p:column>
		</p:row>
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;height:30px;">
				<h:outputText value="*" style="color: red" rendered="#{tdPortalNewsPublishBean.ifCanGrantAllUnit}"/>
				<h:outputText value="发布范围：" />
			</p:column>
			<p:column style="text-align:left;">
				<h:panelGrid columns="2"
					style="border-color: #ffffff;padding: 0 0 0 0;height: 30px;cellpadding:0;">
					<p:inputText id="selectNames" size="100"
						value="#{tdPortalNewsPublishBean.selectOfficeNames}" readonly="true"
						style="cursor: hand;font-weight: normal;"
						onclick="document.getElementById('tabView:editForm:pubRangeLink').click();" />
					<p:commandLink styleClass="ui-icon ui-icon-search"
						id="pubRangeLink" process="@this"
						action="#{tdPortalNewsPublishBean.sqInitAction}"
						style="position: relative;left: -30px;top:0px;"
						onclick="hideOffice();" oncomplete="loadDiaJs();">
						<p:ajax event="dialogReturn"
							listener="#{tdPortalNewsPublishBean.onUserSelect}"
							process="@this" update="selectNames" resetValues="true"
							oncomplete="showOffice();" />
					</p:commandLink>
				</h:panelGrid>
			</p:column>
		</p:row>
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;height:30px;">
				<h:outputText value="*" style="color: red" />
				<h:outputText value="信息标题：" />
			</p:column>
			<p:column style="text-align:left;padding-left:10px;">
				<p:inputText
					value="#{tdPortalNewsPublishBean.tdPortalNews.newsTitle}"
					id="newsTitle" maxlength="50" size="100"
					style="font-weight: normal;" />
			</p:column>
		</p:row>
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;height:30px;">
				<h:outputText value="*" style="color: red" />
				<h:outputText value="重要程度：" />
			</p:column>
			<p:column
				style="text-align:left;padding-left:8px;word-wrap: break-word;word-break: break-all;">
				<p:rating value="#{tdPortalNewsPublishBean.tdPortalNews.newsLevel}"
					stars="5" cancel="false" />
			</p:column>
		</p:row>
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;">
				<h:outputText value="*" style="color: red" />
				<h:outputText value="添加到公告栏：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;line-height: 0px;" >
			<h:panelGrid columns="2" id="showDay" 
					style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
				<p:selectOneRadio
					value="#{tdPortalNewsPublishBean.tdPortalNews.ifNotice}"
					style="width:160px">
					<f:selectItem itemValue="0" itemLabel="　否　" />
					<f:selectItem itemValue="1" itemLabel="　是　" />
					<p:ajax event="change" resetValues="true"  process="@this" update="showDay"/>
				</p:selectOneRadio>
				<p:outputPanel rendered="#{tdPortalNewsPublishBean.tdPortalNews.ifNotice == 1}">
					<h:outputText value="*" style="color: red" />
					<h:outputText value="滚动天数："/>
					<p:inputText id="rollDays" value="#{tdPortalNewsPublishBean.tdPortalNews.rollDays}" 
					onkeyup="SYSTEM.clearNoNum(this)" maxlength="5" size="5"/>
					<h:outputText value=" 天"/>
				</p:outputPanel>
				</h:panelGrid>
			</p:column>
		</p:row>
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;height:30px;">
				<h:outputText value="*" style="color: red" />
				<h:outputText value="是否置顶：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:selectOneRadio
					value="#{tdPortalNewsPublishBean.tdPortalNews.ifZd}"
					style="width:160px">
					<f:selectItem itemValue="0" itemLabel="　否　" />
					<f:selectItem itemValue="1" itemLabel="　是　" />
				</p:selectOneRadio>
			</p:column>
		</p:row>
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:20%;height:30px;">
				<h:outputText value="*" style="color: red" />
				<h:outputText value="信息类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:selectOneRadio
					value="#{tdPortalNewsPublishBean.tdPortalNews.newsType}"
					style="width:#{tdPortalNewsPublishBean.newsType.size()*80}px" id="newsType">
					<f:selectItems value="#{tdPortalNewsPublishBean.newsType}" />
					<p:ajax event="change" resetValues="true" process="@this,@parent"
						oncomplete="hideOffice();PF('cd').show();document.getElementById('tabView:editForm:conDia').style.top='170px';" />
				</p:selectOneRadio>
				<p:confirmDialog message="更换信息类型，下方信息将会丢失，是否更换？" header="消息确认框"
					severity="alert" closable="false" widgetVar="cd" id="conDia">
					<p:commandButton value="确定"
						action="#{tdPortalNewsPublishBean.selectNewsType}"
						update=":tabView:editForm:fileName,:tabView:editForm:fileType,:tabView:editForm:photoPanel,:tabView:editForm:downId,:tabView:editForm:photoRow,typeId,requireBewType"
						oncomplete="PF('cd').hide();showOffBtn();showOffice();"
						process="@this,typeId" />
					<p:commandButton value="取消"
						action="#{tdPortalNewsPublishBean.resetNewsType}"
						oncomplete="PF('cd').hide();showOffice();" process="@this"
						update="newsType" />
				</p:confirmDialog>
			</p:column>
		</p:row>
		<p:row id="HtextRow2">
			<p:column style="text-align:left;padding-left:3px;" colspan="2">
				<h:panelGroup id="downId">
					<p:commandButton value="附件上传"
						onclick="hideOffice();PF('fileUIdVar').show();loadCloseD();"
						process="@this" />
					<p:dataTable value="#{tdPortalNewsPublishBean.annexList}" var="v"
						style="width:100%" type="ordered" emptyMessage="没有上传的文件记录！">
						<p:column headerText="文件名称" style="width: 45%;">
							<p:outputLabel value="#{v.annexName}" />
						</p:column>
						<p:column headerText="操作" style="width: 15%;">
							<p:commandLink ajax="false" value="下载"  immediate="true" process="@this"
								onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
								<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
								<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
								<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
							</p:commandLink>
							<p:spacer width="5" />
							<p:commandLink value="删除" update=":tabView:editForm:downId"
								partialSubmit="true" process="@this"
								action="#{tdPortalNewsPublishBean.deleteDiskFile}">
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
								<f:setPropertyActionListener
									target="#{tdPortalNewsPublishBean.tdPortalNewsAnnex}"
									value="#{v}" />
							</p:commandLink>
						</p:column>
					</p:dataTable>
					<p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId"
						resizable="false">
						<p:fileUpload requiredMessage="请选择要文件上传！"
							fileUploadListener="#{tdPortalNewsPublishBean.handleFileUpload}"
							fileLimit="3" fileLimitMessage="最多只能上传3个文件！" label="选择文件"
							invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，重新上传！"
							style="width:600px;" previewWidth="120" cancelLabel="取消"
							update=":tabView:editForm:downId" uploadLabel="上传"
							oncomplete="showOffice();" dragDropSupport="true" mode="advanced"
							sizeLimit="104857600" />
					</p:dialog>
				</h:panelGroup>
			</p:column>
		</p:row>
		<p:row id="HtextRow">
			<p:column colspan="2">
				<p:row>
					<p:column style="width: 100%;">
						<textarea id="myTextarea" name="myTextarea"></textarea>
						<h:inputHidden
							value="#{tdPortalNewsPublishBean.tdPortalNews.newsCont}"
							id="newsCont" />
					</p:column>
				</p:row>
			</p:column>
		</p:row>
	</p:panelGrid>
	<p:panelGrid style="width:100%;" id="photoPanel">
		<f:facet name="header">
			<p:row>
				<p:column style="padding-left: 3px;text-align:left;" colspan="2">
					<p:outputLabel value="图片上传" />
				</p:column>
			</p:row>
		</f:facet>
		<p:row id="Photo">
			<p:column style="text-align:left;padding-left:3px;" colspan="2">
				<p:commandButton value="添加" icon="ui-icon-plus" id="photoAddBtn"
					process="@this" action="#{tdPortalNewsPublishBean.addPhotoAction}"
					oncomplete="PF('photoDialog').show();" />
				<p:dialog header="图片上传" widgetVar="photoDialog" id="photoDial"
					style="width:650px;" resizable="false">
					<p:fileUpload requiredMessage="请选择要上传的图片！"
						invalidFileMessage="请上传格式为jpg、png、gif、jpeg的图片！"
						fileUploadListener="#{tdPortalNewsPublishBean.handlePhotoUpload}"
						fileLimit="1" fileLimitMessage="最多只能上传1张图片！" label="选择图片"
						invalidSizeMessage="图片大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
						style="width:600px;" previewWidth="120" cancelLabel="取消"
						uploadLabel="上传" allowTypes="/(\.|\/)(gif|jpe?g|png)$/"
						update="photoRow,photoPanel,photoDes" dragDropSupport="true"
						mode="advanced" sizeLimit="10485760"
						oncomplete="PF('photoDescript').show();" />
				</p:dialog>
				<p:spacer width="5" />
				<p:commandButton value="修改" icon="ui-icon-check" id="photoModBtn"
					process="@this" action="#{tdPortalNewsPublishBean.modPhoto}"
					update="photoRow,Photo,photoDes" />
				<p:spacer width="5" />
				<p:commandButton value="删除" icon="ui-icon-close" id="photoDelBtn"
					process="@this" action="#{tdPortalNewsPublishBean.delPhoto}"
					update="photoRow,Photo" />
				<p:spacer width="5" />
			</p:column>
		</p:row>
	</p:panelGrid>
	<p:layout style="height:500px;width: 99%;overflow-x: hidden;"
		id="photoRow">
		<p:layoutUnit position="west" size="300" gutter="20"
			style="border:0px;" collapsible="false" header="图片列表">
			<p:tree dynamic="false" value="#{tdPortalNewsPublishBean.photoTree}"
				var="node" selectionMode="single" id="photoTree"
				style="width: 98%;overflow-y: auto;height: 98%;">
				<p:ajax event="select" process="@this"
					listener="#{tdPortalNewsPublishBean.onPhotoNodeSelect}" />
				<p:treeNode>
					<h:outputText value="#{node.annexName}" />
				</p:treeNode>
			</p:tree>
		</p:layoutUnit>
		<p:layoutUnit position="center" style="border:0px;text-align: center;"
			header="图片效果展示" gutter="20">
			<p:panelGrid style="width: 100%;text-align: center;">
				<p:row>
					<p:column style="text-align:left;" colspan="4">
						<p:galleria value="#{tdPortalNewsPublishBean.annexList}"
							var="image" style="width:100%;" panelHeight="390"
							showCaption="true" effectSpeed="2000" transitionInterval="400000">
							<p:graphicImage value="/webFile#{image.annexAddr}" width="100%"
								alt="#{image.annexDesc}" title="#{image.annexName}" />
						</p:galleria>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:layoutUnit>
	</p:layout>
	<p:dialog id="photoDes" header="图片信息" widgetVar="photoDescript"
		resizable="false" modal="true" width="700" style="height: 670px;">
		<p:panelGrid>
			<p:row>
				<p:column style="text-align:right;padding-right:8px;" colspan="2">
					<div
						style="overflow-x:auto;overflow-y:auto;width: 650px;height:395px;">
						<p:imageCropper value="#{tdPortalNewsPublishBean.croppedImage}"
							id="croppedImage"
							rendered="#{tdPortalNewsPublishBean.tdPortalNewsAnnex!=null  and !tdPortalNewsPublishBean.ifModPhoto and tdPortalNewsPublishBean.ifShowCrop }"
							image="#{request.scheme}://#{request.serverName}:#{request.serverPort}#{request.contextPath}/webFile#{tdPortalNewsPublishBean.tdPortalNewsAnnex.annexAddr}"
							initialCoords="0,0,500,370" />
						<p:graphicImage style="float:left;"
							rendered="#{tdPortalNewsPublishBean.tdPortalNewsAnnex!=null  and tdPortalNewsPublishBean.ifModPhoto and tdPortalNewsPublishBean.ifShowCrop }"
							value="#{request.scheme}://#{request.serverName}:#{request.serverPort}#{request.contextPath}/webFile#{tdPortalNewsPublishBean.tdPortalNewsAnnex.annexAddr}" />
					</div>
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:8px;width: 30%;">
					<h:outputText value="*" style="color: red" />图片标题：
                    </p:column>
				<p:column style="text-align:left;padding-left:8px;height: 30px;">
					<p:inputText
						value="#{tdPortalNewsPublishBean.tdPortalNewsAnnex.annexName}"
						id="annexName" maxlength="50" size="60" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:8px;width: 30%;">
					<h:outputText value="*" style="color: red" />图片描述：
                    </p:column>
				<p:column style="text-align:left;padding-left:8px;height: 60px;">
					<p:inputTextarea id="annexDesc" rows="2" cols="60"
						autoResize="false" counter="display" maxlength="200"
						style="font-weight: normal"
						value="#{tdPortalNewsPublishBean.tdPortalNewsAnnex.annexDesc}"
						counterTemplate="还可以输入{0}个字" />
					<br />
					<h:outputText id="display" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align: center;" colspan="2">
					<p:commandButton value="确认" icon="ui-icon-check" id="photoSaveBtn"
						process="@this,croppedImage,annexDesc,annexName"
						action="#{tdPortalNewsPublishBean.crop}" update="photoRow">
					</p:commandButton>
					<p:spacer width="5" />
					<p:commandButton value="取消" icon="ui-icon-close" id="photoBackBtn"  process="@this" update="photoDes"
						oncomplete="PF('photoDescript').hide();"   >
						<f:setPropertyActionListener target="#{tdPortalNewsPublishBean.ifShowCrop}" value="false" />
					 </p:commandButton>
					<p:spacer width="5" />
				</p:column>
			</p:row>
		</p:panelGrid>
	</p:dialog>
	<p:panelGrid style="width:100%;" id="pdfPanel">
		<p:row>
			<p:column style="text-align:left;padding-left:3px;">
				<p:commandButton value="插入正文" icon="ui-icon-plus"
					oncomplete="PF('pdfDialog').show();" process="@this" />
				<p:spacer width="5" />
				<p:dialog header="PDF上传" widgetVar="pdfDialog" id="pdfDial"
					style="width:650px;" resizable="false">
					<p:fileUpload requiredMessage="请选择要上传的PDF文件！"
						invalidFileMessage="请上传格式为pdf的文件！"
						fileUploadListener="#{tdPortalNewsPublishBean.handlePDFUpload}"
						fileLimit="1" fileLimitMessage="最多只能上传1个文件！" label="选择PDF"
						invalidSizeMessage="PDF大小不能超过100M!" validatorMessage="上传出错啦，重新上传！"
						style="width:600px;" previewWidth="120" cancelLabel="取消"
						uploadLabel="上传" allowTypes="/(\.|\/)(pdf)$/" update="pdfPanel"
						oncomplete="showPDFJs()" dragDropSupport="true" mode="advanced"
						sizeLimit="104857600" />
				</p:dialog>
				<p:spacer width="5" />
				<p:commandButton value="删除正文" icon="ui-icon-close" process="@this"
					update="pdfPanel" oncomplete="showPDFJs()"
					disabled="#{tdPortalNewsPublishBean.totalPdfPage eq 0 }"
					action="#{tdPortalNewsPublishBean.delLoadPdf}" />
				<p:spacer width="5" />
				<p:commandButton value="上一个" icon="ui-icon-arrowthick-1-n"
					process="@this" update="pdfPanel" oncomplete="showPDFJs()"
					disabled="#{(tdPortalNewsPublishBean.totalPdfPage gt 0 and tdPortalNewsPublishBean.currPdfPage gt 1)?false:true}"
					action="#{tdPortalNewsPublishBean.upPdf}" />
				<p:spacer width="5" />
				<p:commandButton value="下一个" icon="ui-icon-arrowthick-1-s"
					process="@this" update="pdfPanel" oncomplete="showPDFJs()"
					disabled="#{(tdPortalNewsPublishBean.totalPdfPage gt 0 and tdPortalNewsPublishBean.currPdfPage lt tdPortalNewsPublishBean.totalPdfPage)?false:true}"
					action="#{tdPortalNewsPublishBean.downPdf}" />
				<p:spacer width="5" />
            当前数：<p:outputLabel
					value="#{tdPortalNewsPublishBean.currPdfPage}" />
				<p:spacer width="5" />
            总页数：<p:outputLabel
					value="#{tdPortalNewsPublishBean.totalPdfPage}" />
				<h:inputHidden id="pdfhtml"
					value="#{tdPortalNewsPublishBean.pdfhtml}" />
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:left;padding-left:3px;" id="pdfCol">
			</p:column>
		</p:row>
	</p:panelGrid>
	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
</ui:composition>
