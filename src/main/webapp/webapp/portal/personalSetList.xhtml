<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<h:head>

    <script>
        //<![CDATA[
        function setFirstValue(obj,name, url) {
            document.getElementById("dialogDeskForm:personForm:openName").value = name;
            document.getElementById("dialogDeskForm:personForm:userUrl").value = url;
            submitValToBack();

            $("li table").css({
                "border": "0",
                "background": "#FFFFFF",
                "border-collapse": "collapse"
            })
            obj.style.border = '#b5b500 1px solid';
            obj.style.background = '#ffffcc';

        }

        function initPageFun()  {
            $("li table").css({
                "border": "0",
                "background": "#FFFFFF",
                "border-collapse": "collapse"
            })

           var selectedPage = document.getElementById("dialogDeskForm:personForm:selectedPage").value;
            if( selectedPage != '-1'){
                $("li table").eq(selectedPage).css({
                    "border": "#b5b500 1px solid",
                    "background": "#ffffcc",
                    "border-collapse": "inherit"
                })
            }
        }
		function pwdLevel0() {
			jQuery("#dialogDeskForm\\:personForm\\:password").keyup(
					function() {
						var value = jQuery(this);
						var result = checkPassword(value);
						if (result < 1) {
							Primary0();
							return;
						}
						if (result > 0 && result < 2) {
							Weak0();
						} else if (result >= 2 && result < 4) {
							Medium0();
						} else if (result >= 4) {
							Tough0();
						}
					});
		}
		function Primary0() {
			jQuery("#pwdLevel_10").attr("class", "ywz_zhuce_huixian");
			jQuery("#pwdLevel_20").attr("class", "ywz_zhuce_huixian");
			jQuery("#pwdLevel_30").attr("class", "ywz_zhuce_huixian");
			jQuery("#dialogDeskForm\\:personForm\\:mmjb").attr("value","弱");
		}

		function Weak0() {
			jQuery("#pwdLevel_10").attr("class", "ywz_zhuce_hongxian");
			jQuery("#pwdLevel_20").attr("class", "ywz_zhuce_huixian");
			jQuery("#pwdLevel_30").attr("class", "ywz_zhuce_huixian");
			jQuery("#zt0").text("弱");
			jQuery("#zt0").css("color", "#ff9c3a");
			jQuery("#dialogDeskForm\\:personForm\\:mmjb").attr("value","弱");
		}

		function Medium0() {
			jQuery("#pwdLevel_10").attr("class", "ywz_zhuce_hongxian");
			jQuery("#pwdLevel_20").attr("class", "ywz_zhuce_hongxian");
			jQuery("#pwdLevel_30").attr("class", "ywz_zhuce_huixian");
			jQuery("#zt0").text("中");
			jQuery("#zt0").css("color", "#ff9c3a");
			jQuery("#dialogDeskForm\\:personForm\\:mmjb").attr("value","中");
		}

		function Tough0() {
			jQuery("#pwdLevel_10").attr("class", "ywz_zhuce_hongxian2");
			jQuery("#pwdLevel_20").attr("class", "ywz_zhuce_hongxian2");
			jQuery("#pwdLevel_30").attr("class", "ywz_zhuce_hongxian2");
			jQuery("#zt0").text("强");
			jQuery("#zt0").css("color", "#61d01c");
			jQuery("#dialogDeskForm\\:personForm\\:mmjb").attr("value","强");
		}
		function checkPassword(pwdinput) {
			var str = jQuery(pwdinput).val();
			var len = str.length;
			var cat = /.{16}/g;
			if (len>0 && len < 6) {
				return 1;
			}
			if (len == 0){
				return 0;
			}
			if (len > 16) {
				jQuery(pwdinput).val(str.match(cat)[0]);
			}
			cat = /.*[\u4e00-\u9fa5]+.*$/;
			if (cat.test(str)) {
				return -1;
			}
			cat = /\d/;
			var maths = cat.test(str);
			cat = /[a-z]/;
			var smalls = cat.test(str);
			cat = /[A-Z]/;
			var bigs = cat.test(str);
			var corps = corpses(pwdinput);
			var num = maths + smalls + bigs + corps;
			if (len >= 6 && len <= 8) {
				if (num == 1)
					return 1;
				if (num == 2 || num == 3)
					return 2;
				if (num == 4)
					return 3;
			}

			if (len > 8 && len <= 11) {
				if (num == 1)
					return 2;
				if (num == 2)
					return 3;
				if (num == 3)
					return 4;
				if (num == 4)
					return 5;
			}

			if (len > 11) {
				if (num == 1)
					return 3;
				if (num == 2)
					return 4;
				if (num > 2)
					return 5;
			}
		}
		function corpses(pwdinput) {
			var cat = /./g;
			var str = jQuery(pwdinput).val();
			var sz = str.match(cat);
			for (var i = 0; i < sz.length; i++) {
				cat = /\d/;
				maths_01 = cat.test(sz[i]);
				cat = /[a-z]/;
				smalls_01 = cat.test(sz[i]);
				cat = /[A-Z]/;
				bigs_01 = cat.test(sz[i]);
				if (!maths_01 && !smalls_01 && !bigs_01) {
					return true;
				}
			}
			return false;
		}
        //]]>
    </script>
    <style type="text/css">
        ul.ulList li {
            list-style: none;
            float: left;
            height: 113px;
            margin-left: 16px;
            margin-right: 16px;
            margin-top: 20px;
            width: 105px;
            text-align: center;
        }

        .set-current :hover {
            border: #b5b500 1px solid;
            background: #ffffcc;
        }

        .zwx_toobar_42 {
            background-color: #fafafa;
            background-image: -moz-linear-gradient(top, #ffffff, #f2f2f2);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f2f2f2));
            background-image: -webkit-linear-gradient(top, #ffffff, #f2f2f2);
            background-image: -o-linear-gradient(top, #ffffff, #f2f2f2);
            background-image: linear-gradient(to bottom, #ffffff, #f2f2f2);
            background-repeat: repeat-x;
            border: 1px solid #d4d4d4;
            border-radius: 6px;
            height: 38px;
            vertical-align: middle;
        }
        .ywz_zhuce_hongxian {
			border: #ff9c3a solid 1px;
			background: #ff9c3a;
			width: 30px;
			height: 0px;
			padding-bottom:6px;
			margin-left: 5px;
		}
		.ywz_zhuce_hongxian2 {
			border: #61d01c solid 1px;
			background: #61d01c;
			width: 30px;
			height: 0px;
			padding-bottom:6px;
			margin-left: 5px;
		}
		.ywz_zhuce_huixian {
			border: #ff9c3a solid 1px;
			width: 30px;
			height: 0px;
			margin-bottom:6px;
			padding-bottom:6px;
			margin-left: 5px;
		}

		.ywz_zhuce_hongxianwenzi {
			width: 30px;
			margin-top: 5px;
			margin-left: 5px;
			text-align: center;
			color: #ff9c3a;
			font-size: 12px;
		}
    </style>
</h:head>
<f:view>
    <body>
    <p:dialog header="个人设置" dynamic="true" widgetVar="PersonalSet" id="personalSet" resizable="false" width="700"
              height="600" onShow="initPageFun(),pwdLevel0()"
              modal="true">
        <h:form id="personForm">
            <p:outputPanel styleClass="zwx_toobar_42">
                <h:panelGrid columns="3"
                             style="border-color:transparent;padding:0px;padding-top: 5px;border-collapse: inherit">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
                                     process="@this,mobilePhone,password,password2,qickDesk,mmjb"
                                     action="#{personalSetBean.saveAction}"/>
                    <p:commandButton value="关闭" icon="ui-icon-close" id="backBtn" onclick="PF('PersonalSet').hide();"
                                     immediate="true" process="@this"/>
                </h:panelGrid>
            </p:outputPanel>
            <table style="width:100%;">
                <tr style="vertical-align: top">
                    <td width="100%">
                        <table style="width:98%;">
                            <tr style="height: 35px;vertical-align: bottom;">
                                <td style="text-align: left;padding-left:20px;"><h:outputText value="主页设置"
                                                                                              style="color: black;font-weight: bold;font-family: Arial,'宋体';font-size: 12px;"/>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <hr class="flow_separate"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: left;padding-left:20px;">
                                    <h:panelGroup id="outDataPanle">
                                        <ul class="ulList">
                                            <ui:repeat value="#{personalSetBean.portalList}" var="main">
                                                <li class="set-current"  >
                                                    <table border="0" cellpadding="0"
                                                           onclick="setFirstValue(this,'#{main[0]}','#{main[3]}')"
                                                           style="vertical-align: top;height: 100px;cursor: pointer;text-align: center;margin-top: 5px;margin-left: 0px;"
                                                           cellspacing="0"
                                                           width="50">
                                                        <tr>
                                                            <td valign="top"
                                                                style="padding-left: 0px;padding-top:0px;">
                                                                <img src="#{request.contextPath}/resources/component/quickDesktop/image/64px/#{main[1]}"></img>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="text-align: center;vertical-align: top;padding-top: 3px;"
                                                                height="25px"><h:outputText
                                                                    value="#{main[2]}"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </li>
                                            </ui:repeat>
                                        </ul>
                                    </h:panelGroup>
                                    <p:remoteCommand name="submitValToBack" process="@this,openName,userUrl"/>
                                    <h:inputHidden id="openName" value="#{personalSetBean.openName}"/>
                                    <h:inputHidden id="userUrl" value="#{personalSetBean.url}"/>
                                    <h:inputHidden id="selectedPage" value="#{personalSetBean.selectedPage}"/>
                                </td>
                            </tr>
                            <tr style="height: 35px;vertical-align: bottom;">
                                <td style="text-align: left;padding-left:20px;">
                                    <h:outputText value="密码修改"
                                                  style="color: black;font-weight: bold;font-family: Arial,'宋体';font-size: 12px;"/>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <hr class="flow_separate"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: left;padding-left:100px;">
                                    <table id="personDialog">
                                       	<tr style="height: 30px;">
											<td><h:outputLabel for="password" value="新的密码：" /></td>
											<td><p:password style="width: 200px" id="password"
													maxlength="50" tabindex="1"
													value="#{personalSetBean.password}" redisplay="true" /> </td>
										<td>
								        <input id="pwdLevel_10" class="ywz_zhuce_huixian"></input> </td>
										<td> <input id="pwdLevel_20" class="ywz_zhuce_huixian"></input>  </td>
										<td><input id="pwdLevel_30" class="ywz_zhuce_huixian"></input></td> 
										<td><span id="zt0" class="ywz_zhuce_hongxianwenzi">弱</span></td> 
										<td><h:inputHidden value="#{personalSetBean.passwordLevel}" id="mmjb"></h:inputHidden> </td>
									
										</tr>
                                        <tr style="height: 30px;">
                                            <td>
                                                <h:outputLabel for="password2" value="确认密码："/>
                                            </td>
                                            <td>
                                                <p:password style="width: 200px" id="password2" maxlength="50"
                                                            tabindex="1" value="#{personalSetBean.password2}"
                                                            redisplay="true"/>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr style="height: 35px;vertical-align: bottom;">
                                <td style="text-align: left;padding-left:20px;">
                                    <h:outputText value="基本信息"
                                                  style="color: black;font-weight: bold;font-family: Arial,'宋体';font-size: 12px;"/>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <hr class="flow_separate"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: left;padding-left:100px;">
                                    <table>
                                        <tr>
                                            <td style="vertical-align: middle;">
                                                <h:outputText value="手机号码："
                                                              style="color: black;font-family: Arial,'宋体';font-size: 12px;"/>
                                            </td>
                                            <td style="vertical-align: middle;">
                                                <p:inputText value="#{personalSetBean.mobilePhone}" style="font-size: 13px;font-weight: normal"
                                                             id="mobilePhone"  maxlength="13" ></p:inputText>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr style="height: 35px;vertical-align: bottom;">
                                <td style="text-align: left;padding-left:20px;">
                                    <h:outputText value="快捷菜单"
                                                  style="color: black;font-weight: bold;font-family: Arial,'宋体';font-size: 12px;"/>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <hr class="flow_separate"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: left;padding-left:100px;">
                                    <table>
                                        <tr>
                                            <td style="vertical-align: middle;">
                                                <h:outputText value="是否启用快捷菜单："
                                                              style="color: black;font-family: Arial,'宋体';font-size: 12px;"/>
                                            </td>
                                            <td style="vertical-align: middle;">
                                                <p:selectBooleanCheckbox value="#{personalSetBean.ifUseQickDesk}" id="qickDesk"/>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </h:form>
    </p:dialog>
    </body>
</f:view>
</html>







