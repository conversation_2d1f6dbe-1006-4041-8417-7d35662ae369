<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">

	<h:form id="editForm">
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editTitleGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="门户布局" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{portalMgrBean.saveLayoutAction}" process="@this,editGrid" update=":tabView" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{portalMgrBean.backAction}" update=":tabView" immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;" id="editGrid">
			<p:row>
				<p:column style="width:220px;padding:5px;padding-right:30px;vertical-align: top;">
					<p:panel header="布局版式" style="width:100%;text-align:center" id="layoutPanel">
						<p:selectOneRadio id="layoutRadio" value="#{portalMgrBean.layout}" layout="custom">
							<f:selectItems value="#{portalMgrBean.layOutBean.layoutMap}" />
							<p:ajax event="change" listener="#{portalMgrBean.layoutChgAction}" process="@this,@parent" update="columnPanel,layoutBoard" />
						</p:selectOneRadio>

						<c:forEach items="#{portalMgrBean.layOutBean.htmlMap}" var="v" varStatus="ind">
							<h:outputText value="#{v.value}" escape="false" />
							<p:radioButton for="layoutRadio" itemIndex="#{ind.index}" style="height:30px;padding-top:5px;" />
							<h:outputText value="#{v.key}" style="margin-left:10px;" />
						</c:forEach>
					</p:panel>
					<p:panel header="栏目" style="width:100%;margin-top:10px;text-align:center;height:400px;overflow-y:auto;" id="columnPanel">
						<c:forEach items="#{portalMgrBean.columnList}" var="v">
							<p:commandButton value="#{v.colName}" process="@this" action="#{portalMgrBean.addColumn}" style="margin-top:10px;width:100px;" update="columnPanel,layoutBoard">
								<f:setPropertyActionListener target="#{portalMgrBean.portalColumn}" value="#{v}" />
							</p:commandButton>
							<br />
						</c:forEach>
						<h:outputText escape="false" value="#{portalMgrBean.columnStyle}" />
					</p:panel>
				</p:column>
				<p:column style="vertical-align: top;">
					<p:dashboard id="layoutBoard" binding="#{portalMgrBean.dashboard}" style="width:100%;" model="#{portalMgrBean.dashboardModel}">
						<p:ajax event="reorder" listener="#{portalMgrBean.handleReorder}" update="layoutBoard" process="@this" />
					</p:dashboard>
				</p:column>
			</p:row>
		</p:panelGrid>

		<!-- 栏目设置 -->
		<p:dialog id="colSetDialog" header="栏目设置" widgetVar="ColSetDialog" resizable="false" width="500" height="150" modal="true">
			<p:panelGrid style="width:100%;" id="colSetGrid">
				<p:row rendered="#{portalMgrBean.columnSetBean.portalColType.typeNo=='0' or portalMgrBean.columnSetBean.portalColType.typeNo=='1'
					or portalMgrBean.columnSetBean.portalColType.typeNo=='3'}"  >
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="*" style="color:red" />
						<h:outputText value="显示条数：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="lines" value="#{portalMgrBean.columnSetBean.lines}" maxlength="2" size="6" required="true" requiredMessage="显示条数不允许为空！" converterMessage="显示条数只能填写数字！" />
					</p:column>
				</p:row>

				<p:row rendered="#{portalMgrBean.columnSetBean.portalColType.typeNo=='0' or portalMgrBean.columnSetBean.portalColType.typeNo=='2'}" >
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="*" style="color:red" />
						<h:outputText value="显示日期：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:selectOneRadio id="dispDate" value="#{portalMgrBean.columnSetBean.dispDate}" required="true" requiredMessage="请选择显示日期！">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
						</p:selectOneRadio>
					</p:column>
				</p:row>

				<p:row rendered="#{portalMgrBean.columnSetBean.portalColType.typeNo=='0' or portalMgrBean.columnSetBean.portalColType.typeNo=='2'}">
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="标题字数：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="words" value="#{portalMgrBean.columnSetBean.words}" maxlength="2" size="6" converterMessage="标题字数只能填写数字！" />
					</p:column>
				</p:row>

				<p:row rendered="#{portalMgrBean.columnSetBean.portalColType.typeNo!='2'}">
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="栏目高度：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="heighth" value="#{portalMgrBean.columnSetBean.heighth}" maxlength="3" size="6" converterMessage="栏目高度只能填写数字！" />
					</p:column>
				</p:row>

			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="setSaveBtn" action="#{portalMgrBean.panelSettingSave}" process="@this,colSetGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="setBackBtn" onclick="PF('ColSetDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</h:form>
</ui:composition>

