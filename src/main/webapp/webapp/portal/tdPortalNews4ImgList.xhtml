<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
	<table width="80%" cellspacing="0" cellpadding="0">
		<tr
			style="text-align: center;color: black;font-weight: bold;font-size: x-large;">
			<td style="margin-top: 35px;padding-bottom:35px;padding-top:120px;font-family:SimHei;" colspan="2"><h:outputText
					value="#{tdPortalNewsViewBean.tdPortalNews.newsTitle}" /></td>
		</tr>
		<tr>
			<td style="text-align: left;">
				<h:panelGrid columns="2">
				<p:outputLabel style="color:gray;" value="重要程度：" /> 	
					<p:rating value="#{tdPortalNewsViewBean.tdPortalNews.newsLevel}" 
						readonly="true" />
				</h:panelGrid>
			</td>
			<td style="padding-right: 10%;text-align: right;"><p:outputLabel
					style="color:gray;" value="发布时间：" /> <p:outputLabel
					style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.newsDate}">
					<f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8"
						locale="cn" />
				</p:outputLabel> <p:outputLabel value="　　" /> <p:outputLabel style="color:gray;"
					value="发布人：" /> <p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.tsUserInfo.username}" />
				<p:outputLabel value="　　" /> <p:outputLabel style="color:gray;"
					value="信息类型：" /> <p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsType.typeName}" />
			</td>
		</tr>
		<tr align="center">
			<td colspan="2"><br /> <p:galleria
					value="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsAnnexes}"
					var="image" panelWidth="800" panelHeight="530" showCaption="true">
					<p:graphicImage width="100%" height="100%"
						value="/webFile#{image.annexAddr}" alt="#{image.annexDesc}"
						title="#{image.annexName}" />
				</p:galleria></td>
		</tr>
	</table>
</ui:composition>