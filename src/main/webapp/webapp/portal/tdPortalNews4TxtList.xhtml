<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
	<table width="80%" cellspacing="0" cellpadding="0">
		<tr
			style="text-align: center;color: black;font-weight: bold;font-size: x-large;">
			<td style="margin-top: 35px;padding-bottom:35px;padding-top:120px;font-family:SimHei;" colspan="2"><h:outputText
					value="#{tdPortalNewsViewBean.tdPortalNews.newsTitle}" /></td>
		</tr>
		<p:spacer width="10"/>
		<tr>
			<td style="text-align: left;">
				<h:panelGrid columns="2">
				<p:outputLabel style="color:gray;" value="重要程度：" /> 	
					<p:rating value="#{tdPortalNewsViewBean.tdPortalNews.newsLevel}" 
						readonly="true" />
				</h:panelGrid>
			</td>
			<td style="padding-right: 5%;text-align: right;">
					<p:outputLabel style="color:gray;" value="发布时间：" /> 
					<p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.newsDate}">
					<f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8"
						locale="cn" />
				</p:outputLabel> <p:outputLabel value="　　" /> <p:outputLabel style="color:gray;"
					value="发布人：" /> <p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.tsUserInfo.username}" />
				<p:outputLabel value="　　" /> <p:outputLabel style="color:gray;"
					value="信息类型：" /> <p:outputLabel style="color:gray;"
					value="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsType.typeName}" />
			</td>
		</tr>
		<tr>
			<td colspan="2" style="text-align: left;">
				<p:outputLabel value="附件下载：" />
				<p:outputLabel value="无"
					rendered="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsAnnexes.size() == 0}" />
				<c:forEach
					items="#{tdPortalNewsViewBean.tdPortalNews.tdPortalNewsAnnexes}"
					var="v">
					<p:commandLink ajax="false" value="#{v.annexName}" style="color:blue;" immediate="true" process="@this"
						onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
						<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
						<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
						<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
					</p:commandLink>
					<p:outputLabel value="　　" />
				</c:forEach> <br />
				<hr />
			</td>
		</tr>
		<tr>
			<td colspan="2"><br /> 
				<div style="overflow-x:auto;width:800px;">
					<p:outputLabel escape="false"
					value="#{tdPortalNewsViewBean.tdPortalNews.newsCont}" />
				</div>
			</td>
		</tr>
	</table>
	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
</ui:composition>