<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{portalMgrBean}"/>
    <!-- 布局页面 -->
    <ui:param name="viewPage" value="/webapp/portal/portalMgrEdit.xhtml"/>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="门户管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{portalMgrBean.searchAction}" update="dataTable" process="@this,searchPortalName" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{portalMgrBean.addInit}" update=":tabView:mainForm:portalEditDialog" process="@this"
					oncomplete="PF('PortalEditDialog').show()">
					<p:resetInput target=":tabView:mainForm:portalEditGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:100px;">
                <h:outputText value="门户名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText id="searchPortalName" value="#{portalMgrBean.searchPortalName}" maxlength="20"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="门户名称" style="padding-left: 3px;">
            <h:outputText value="#{itm.portalName}" />
        </p:column>
        <p:column headerText="操作" style="width: 250px;padding-left: 3px;">
            <p:commandLink value="修改" action="#{portalMgrBean.modInit}" update=":tabView:mainForm:portalEditDialog" 
            		process="@this" oncomplete="PF('PortalEditDialog').show()" rendered="#{itm.portalType=='0'}">
                <f:setPropertyActionListener target="#{portalMgrBean.rid}" value="#{itm.rid}"/>
                <p:resetInput target=":tabView:mainForm:portalEditGrid"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm.portalType=='0'}" />
	        <p:commandLink value="删除" action="#{portalMgrBean.deleteAction}" update=":tabView" process="@this"
	                       rendered="#{itm.portalType=='0'}">
	            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
	            <f:setPropertyActionListener target="#{portalMgrBean.rid}" value="#{itm.rid}"/>
	        </p:commandLink>
        	<p:spacer width="5" rendered="#{itm.portalType=='0'}" />
            <p:commandLink value="授权" action="#{portalMgrBean.sqInitAction}" process="@this"  rendered="#{itm.portalType=='0'}">
                <f:setPropertyActionListener target="#{portalMgrBean.rid}" value="#{itm.rid}"/>
                <p:ajax event="dialogReturn"  listener="#{portalMgrBean.onUserSelect}" process="@this" resetValues="true"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm.portalType=='0'}" />     
            <p:commandLink value="布局" action="#{portalMgrBean.viewInitAction}" update=":tabView" process="@this">
                <f:setPropertyActionListener target="#{portalMgrBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>                   
        </p:column>
    </ui:define>

<!-- 弹出框 -->
<ui:define name="insertOtherMainContents">
	<table style="background-color: "></table>


    <!-- 新增、修改门户 -->
    <p:dialog id="portalEditDialog" header="门户维护" widgetVar="PortalEditDialog" resizable="false" width="500" height="80" modal="true">
        <p:panelGrid style="width:100%;" id="portalEditGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputText value="*" style="color:red"/>
                    <h:outputText value="门户名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="portalName" value="#{portalMgrBean.tdProtal.portalName}" maxlength="50" required="true" requiredMessage="门户名称不允许为空！"/>
                </p:column>
            </p:row>
            
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputText value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="xh" value="#{portalMgrBean.tdProtal.xh}" maxlength="2" converterMessage="序号格式错误，只能输入整数！"/>
                </p:column>
            </p:row>

        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{portalMgrBean.saveAction}" process="@this,portalEditGrid"
                                     update="dataTable,portalEditGrid" />
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('PortalEditDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    </ui:define>
</ui:composition>











