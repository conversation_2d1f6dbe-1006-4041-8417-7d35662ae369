<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <ui:param name="onfocus" value="false"/>

    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <script type="text/javascript" src="/resources/component/pager/kkpager.js"></script>
        <link rel="stylesheet" type="text/css" href="/resources/component/pager/kkpager.css" />
        <style type="text/css">
            .ui-tree .ui-tree-container{
                overflow-x:hidden;
            }
            .ui-panelgrid td {
                padding-top: 2px;
                padding-bottom: 2px;
                padding-left: 5px;
                padding-right: 0px;
            }
            .ui-panelgrid td {
                border-width: 1px;
            }

            .ui-datagrid-content{
                border: 0px;
            }
            ul.ulList li {
                list-style: none;
                float: left;
                height: 160px;
                margin-left: 16px;
                margin-top: 20px;
                width:260px;
            }
            #div-a{
                text-align: center;
                line-height: 30px;
                height: 30px;
            }
        </style>
        <script style="text/javascript">
            //<![CDATA[
            function showPager() {
                var totalPage =parseInt(document.getElementById("mainForm:totalPage").value,10);
                var totalRecords = parseInt(document.getElementById("mainForm:totalRecords").value,10);
                var pageNo = parseInt(document.getElementById("mainForm:pageNo").value,10);
                if(totalRecords>0) {
                    //生成分页
                    //有些参数是可选的，比如lang，若不传有默认值
                    kkpager.init({
                        pno: pageNo,
                        //总页码
                        total: totalPage,
                        //总数据条数
                        totalRecords: totalRecords,
                        getLink: function (n) {
                            return "javascript:pageClick(" + n + ");";
                        },
                        lang: {
                            firstPageText: '|<',
                            lastPageText: '>|',
                            prePageText: '<',
                            nextPageText: '>',
                            totalPageBeforeText: '共',
                            totalPageAfterText: '页',
                            totalRecordsAfterText: '条数据',
                            gopageBeforeText: '转到',
                            gopageButtonOkText: '确定',
                            gopageAfterText: '页',
                            buttonTipBeforeText: '第',
                            buttonTipAfterText: '页'
                        }
                    });
                    kkpager.generPageHtml();
                }else{
                    var strhtm='<span class="normalsize">没有您要找的记录！</span>';
                    jQuery("#kkpager").html(strhtm);
                }
            }
            function pageClick(pageno){
                document.getElementById("mainForm:pageNo").value=pageno;
                kkpager.selectPage(pageno);
                pageSeachJS();
            }
            //]]>
        </script>
        <script style="text/javascript">
            //<![CDATA[
            jQuery(window).resize(function() {
                var elemWid = jQuery(window).width();
                var wid=elemWid * 0.9<=710?710:elemWid * 0.9;
                jQuery('ul.ulList').width(wid);
            });
            jQuery(document).ready(function() {
                showPager();
            });
            //]]>
        </script>
        <script style="text/javascript">
            //<![CDATA[
            function stepIntoEvent(regId, eventId) {
                var url = "#{request.scheme}" + "://" + "#{request.serverName}"+":"+"#{request.serverPort}"+"#{request.contextPath}"+"/webapp/emerg/tdEmergEventDeal.faces";
                url = url + "?regId=" + regId + "&eventId=" + eventId;
                this.location.href=url;
            }
            //]]>
        </script>
        <script type="text/javascript">
            //<![CDATA[
            //新打开页面
            function forwordPage(url, title) {
                top.ShortcutMenuClick("01",title,url,"");
            }

            //用于特殊处理含tab的栏目
            jQuery(document).ready(function() {
                jQuery(".ui-tabs-nav a").mouseover(function (c) {
                    this.click();
                    this.style.cursor="pointer";
                });
                jQuery(".ui-tabs-nav a").mousedown(function (c) {
                    var tabIds=this.href.substring(this.href.lastIndexOf(":")+1);
                    var tabId=tabIds.substring(tabIds.lastIndexOf("_")+1);
                    var v=tabId.split("-");
                    var url="/webapp/portal/tdPortalNewsMoreList.faces?newsType="+v[1]+"&pageTitle="+v[0];
                    forwordPage(url, v[0]);
                });
            });
            //]]>
        </script>

    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="图片新闻"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:remoteCommand name="pageSeachJS" action="#{pictuerShowBean.pageSearchAction}" process="@this,pageNo" update="outDataPanle"/>
        <h:inputHidden value="#{pictuerShowBean.totalPage}" id="totalPage"/>
        <h:inputHidden value="#{pictuerShowBean.totalRecords}" id="totalRecords"/>
        <h:inputHidden value="#{pictuerShowBean.pageNo}" id="pageNo"/>

        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{pictuerShowBean.searchAction}"
                                 update=":mainForm:totalPage,:mainForm:totalRecords,:mainForm:pageNo,outDataPanle"
                                 process="@this,:mainForm:mainGrid" oncomplete="showPager()"/>
            </h:panelGrid>
        </p:outputPanel>

    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:6%">
                <p:outputLabel value="标题："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:20%">
                <p:inputText id="searchTitle" value="#{pictuerShowBean.searchTitle}" size="30"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:10%">
                <p:outputLabel value="发布部门："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px">
                <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                    <p:inputText id="searchOfficeName" value="#{pictuerShowBean.searchOfficeName}" readonly="true"
                                 style="width: 180px;cursor: pointer" />
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"
                                   style="position: relative;left: -30px;top:0px;"
                                   oncomplete="PF('OveralPanel').show()"/>
                    <h:inputHidden id="searchOfficeId" value="#{pictuerShowBean.searchOfficeId}"/>
                </h:panelGrid>
                <p:overlayPanel id="overalPanel" for=":mainForm:searchOfficeName"
                                dynamic="false" style="width:280px;" widgetVar="OveralPanel">
                    <p:tree value="#{pictuerShowBean.treeNode}" var="node"
                            selectionMode="single" id="choiceTree"
                            selection="#{pictuerShowBean.selectedNode}"
                            style="width: 260px;height: 200px;overflow-y: auto;">
                        <p:ajax event="select"
                                listener="#{pictuerShowBean.searchSelectTreeNode(pictuerShowBean.selectedNode)}"
                                oncomplete="PF('OveralPanel').hide()"
                                update=":mainForm:searchOfficeName,:mainForm:searchOfficeId"
                                partialSubmit="true" process="@this,@parent"/>
                        <p:treeNode>
                            <p:outputLabel value="#{node.officename}" onclick=""/>
                        </p:treeNode>
                    </p:tree>
                </p:overlayPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="发布人："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchMan" value="#{pictuerShowBean.searchMan}" size="30"/>
            </p:column>
            <p:column style="text-align:right;padding-right:5px;">
                <p:outputLabel value="发布日期："/>
            </p:column>
            <p:column style="text-align:left;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="date4"
                            navigator="true" yearRange="c-10:c+10"
                            converterMessage="发布日期，格式输入不正确！"
                            showButtonPanel="true"
                            value="#{pictuerShowBean.searchStartDate}"/>
                <p:outputLabel value="～"/>
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="date5"
                            navigator="true" yearRange="c-10:c+10"
                            showButtonPanel="true"
                            converterMessage="发布日期，格式输入不正确！"
                            value="#{pictuerShowBean.searchEndDate}"/>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 具体内容 -->
    <ui:define name="insertContent">
        <p:panel >
            <table width="100%" style="height: 100%; text-align: center;" cellpadding="0" cellspacing="0" border="0" >
                <tr>
                    <td style="width: 100%">
                        <h:panelGroup id="outDataPanle" >
                            <ul class="ulList">
                                <ui:repeat value="#{pictuerShowBean.infoList}" var="itm">
                                    <li>
                                        <div style="width: 250px;height: 170px;background-color: #25AAE1">
                                            <div style="width: 100%;height: 80%;background-color: #000066">
                                                <img src="/webFile#{itm[3]}" style="width: 100%;height: 100%;" title="#{itm[1]}"
                                                        onclick="forwordPage('#{itm[4]}','#{itm[1]}')"/>
                                            </div>
                                            <div style="width: 100%;height: 20%;background-color:#d6dff7;text-align: left;">
                                                <div id="div-a">
                                                    <p:outputLabel value="#{itm[2]}"/>
                                                </div>

                                            </div>
                                        </div>
                                    </li>
                                </ui:repeat>
                            </ul>
                        </h:panelGroup>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">
                        <div id="kkpager"></div>
                    </td>
                </tr>
            </table>
        </p:panel>
    </ui:define>


</ui:composition>
