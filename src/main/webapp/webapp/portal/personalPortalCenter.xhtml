<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html" 
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
<h:head>
	<title>个人工作站</title>
	<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
	<script type="text/javascript">
	            var zwxJQ = $.noConflict(true);
	 </script>
	<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/NewPopMenu.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/CreatePopup.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/index.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/zwx.system.js"></script>	
	
	<h:outputStylesheet library="css" name="portal_defalut.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	<h:outputStylesheet library="css" name="imgPlay.css" rendered="#{personalPortalCenterBean.imgColBool}"/>
	<h:outputStylesheet library="css" name="wordPlay.css"  rendered="#{personalPortalCenterBean.gglColBool}"/>
	<h:outputScript name="js/scroll.js"/>
	<style type="text/css">
		.portal_mainGrid {
			padding:2px;
			width: 100%;
			vertical-align: top;
		}
		.carItem {
			width:155px;
			height:55px;
			text-align:center;
		}
	</style>
	<script type="text/javascript">
	//图片连播js
	jQuery(document).ready(function(){		   
		var index=0;
		var slideFlag = true;
		var length=jQuery(".roll-news-image img").length;

		function showImg(i){
			jQuery(".roll-news-image img")
			.eq(i).stop(true,true).fadeIn(800)
			.siblings("img").hide();

			jQuery(".roll-news-index li").removeClass("roll-news-index-hover");
			jQuery(".roll-news-index li").eq(i).addClass("roll-news-index-hover");

			jQuery(".roll-news-title a")
			.eq(i).stop(true,true).fadeIn(800)
			.siblings("a").hide();
		}
		showImg(index);
		
		jQuery(".roll-news-index li").mouseover(function(){
			index = jQuery(".roll-news-index li").index(this);
			showImg(index);
			slideFlag = false;
		});	
		
		function autoSlide() {
			setInterval(function() {
				if(slideFlag) {
					showImg((index+1) % length);
					index = (index+1)%length;
				}
				slideFlag = true;
			}, 3000);
		}
		
		autoSlide();
		jQuery(".no_header").children(".ui-panel-content.ui-widget-content").css("padding","0");
	});
</script>	
	<script type="text/javascript">
	//文字上下滚动
jQuery(function(){
	jQuery("div.list_lh").myScroll({
		speed:40, //数值越大，速度越慢
		rowHeight:24 //li的高度
	});
});
	
	</script>
	

<script type="text/javascript">
            //<![CDATA[
            //新打开页面
            function forwordPage(url, title) {
            	top.ShortcutMenuClick("01",title,url,"");
            }
            
            //用于特殊处理含tab的栏目
    		jQuery(document).ready(function() {
    			jQuery(".ui-tabs-nav a").mouseover(function (c) {
    				this.click();
    				this.style.cursor="pointer";
    			});
    			jQuery(".ui-tabs-nav a").mousedown(function (c) {
    				var tabIds=this.href.substring(this.href.lastIndexOf(":")+1);
    				var tabId=tabIds.substring(tabIds.lastIndexOf("_")+1);
    				var v=tabId.split("-");
    				var url="/webapp/portal/tdPortalNewsMoreList.faces?newsType="+v[1]+"&pageTitle="+v[0]+"&special=1";
    				forwordPage(url, v[0]);
    			});			
    		});            
            //]]>            
</script>	
</h:head>

<h:body>
	<h:form id="mainForm">
	<table style="margin-top: 1px;padding-top: 0px;"></table>
			<p:dashboard id="layoutBoard" binding="#{personalPortalCenterBean.dashboard}"
				 style="width:100%;" model="#{personalPortalCenterBean.dashboardModel}">
				<p:ajax event="reorder" listener="#{personalPortalCenterBean.handleReorder}" update="layoutBoard" process="@form" />
			</p:dashboard>
			<!-- mainGrid每一列的样式 -->
			<h:outputText id="panelGridColumnStyle"  value="#{personalPortalCenterBean.panelGridColumnStyle}"  escape="false"/>
	</h:form>
</h:body>
</f:view>	
</html>