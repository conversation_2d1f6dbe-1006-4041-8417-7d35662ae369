<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{portalColumnBean}" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<style type="text/css">
.ui-picklist .ui-picklist-list {
	text-align: left;
	height: 340px;
	width: 340px;
	overflow: auto;
}

.ui-picklist .ui-picklist-filter {
	padding-right: 0px;
	width: 98%;
}
</style>
		<script type="text/javascript">
			
		</script>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="栏目管理" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{portalColumnBean.searchAction}" update="dataTable"
					process="@this,searchName" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addColumnButton"
					action="#{portalColumnBean.addInitAction}" update="editDialog"
					process="@this" oncomplete="PF('EditDialog').show()" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:10%;">
				<h:outputLabel for="searchName" value="栏目名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;width:20%;">
				<p:inputText id="searchName" value="#{portalColumnBean.searchName}"
					maxlength="50" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:10%;">
				<h:outputLabel for="searchType" value="栏目类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;width: 250px;">
				<p:selectOneMenu id="searchType"
					value="#{portalColumnBean.searchType}" style="width: 180px;">
					<f:selectItem itemLabel="--请选择--" itemValue="" />
					<f:selectItems value="#{portalColumnBean.searchTypeMap}" />
					<p:ajax event="change" update="dataTable" process="@this"
						listener="#{portalColumnBean.onSearchUnitChange}" />
				</p:selectOneMenu>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="栏目类型" style="width:120px;text-align: center">
			<h:outputLabel value="#{itm.colType.typeCN}" />
		</p:column>
		<p:column headerText="栏目名称" style="width:180px;text-align: center">
			<h:outputText value="#{itm.colName}" />
		</p:column>
		<p:column headerText="更多地址" style="width:500px;padding-left: 3px;">
			<h:outputText value="#{itm.moreUrl}" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="修改" action="#{portalColumnBean.modInit}"
				update=":mainForm:editDialog" oncomplete="PF('EditDialog').show()"
				process="@this" rendered="#{itm.colType.typeNo == 4}">
				<f:setPropertyActionListener
					target="#{portalColumnBean.tdPortalColumn}" value="#{itm}" />
				<p:resetInput target=":mainForm:editDialog" />
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm.colType.typeNo == 4}" />
			<p:commandLink rendered="#{itm.colType.typeNo == 4}" value="删除"
				action="#{portalColumnBean.deleteAction}" update="dataTable"
				process="@this">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{portalColumnBean.rid}"
					value="#{itm.rid}" />
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm.colType.typeNo == 4}" />
			<p:commandLink value="授权" rendered="#{!portalColumnBean.ifShowAllUser}"
				action="#{portalColumnBean.userManagerInit}"
				update=":mainForm:userDialog" oncomplete="PF('UserDialog').show()"
				process="@this">
				<f:setPropertyActionListener target="#{portalColumnBean.rid}"
					value="#{itm.rid}" />
				<f:setPropertyActionListener
					target="#{portalColumnBean.tdPortalColumn}" value="#{itm}" />
			</p:commandLink>
			
			<p:commandLink value="授权" rendered="#{portalColumnBean.ifShowAllUser}" process="@this" action="#{portalColumnBean.sqInitAction}">
				<f:setPropertyActionListener target="#{portalColumnBean.rid}" value="#{itm.rid}" />
				<f:setPropertyActionListener target="#{portalColumnBean.tdPortalColumn}" value="#{itm}" />
				<p:ajax event="dialogReturn" listener="#{portalColumnBean.onUserSelect}" process="@this" resetValues="true" />
			</p:commandLink>
			
		</p:column>
	</ui:define>

	<!-- 弹出框 -->
	<ui:define name="insertDialogs">
		<!-- 添加、修改 -->
		<p:dialog id="editDialog" header="栏目维护" widgetVar="EditDialog"
			resizable="false" width="600"  modal="true">
			<p:panelGrid style="width:100%;" id="editGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<p:outputLabel value="*" style="color: red" />
						<h:outputLabel value="栏目名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText value="#{portalColumnBean.tdPortalColumn.colName}"
							maxlength="50" size="20" required="true" requiredMessage="栏目名称不能为空"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="更多地址：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="moreUrl"
							value="#{portalColumnBean.tdPortalColumn.moreUrl}"
							maxlength="100" size="50" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="是否隐藏标题栏：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:selectOneRadio id="ifHideHeader"
							value="#{portalColumnBean.tdPortalColumn.ifHideHeader}"
							style="width:120px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
						</p:selectOneRadio>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="是否需要消息更新：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:selectOneRadio id="needSocketUpdate"
							value="#{portalColumnBean.tdPortalColumn.needSocketUpdate}"
							style="width:120px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
						</p:selectOneRadio>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
							action="#{portalColumnBean.saveAction}"
							process="@this,editDialog" update="dataTable,editGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
							oncomplete="PF('EditDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!-- 授权 -->
		<p:dialog id="userDialog" header="请选择人员" widgetVar="UserDialog"
			resizable="false" width="800" height="450" modal="true"
			dynamic="true">
			<table width="100%">
				<tr>
					<td width="60" style="text-align: right;padding-right: 3px">科室：</td>
					<td style="text-align: left;"><h:panelGrid columns="3"
							style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="userOfficeName"
								value="#{portalColumnBean.userOfficeName}" readonly="true"
								style="width: 120px;" />
							<h:inputHidden id="userOfficeRid"
								value="#{portalColumnBean.userOfficeRid}" />
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="initOfficeTreeLink" process="@this"
								style="position: relative;left: -30px;"
								oncomplete="PF('UserOfficePanel').show()" />
						</h:panelGrid> <p:overlayPanel id="userOfficePanel" for="userOfficeName"
							dynamic="false" style="width:280px;" widgetVar="UserOfficePanel">
							<p:tree value="#{portalColumnBean.userOfficeTreeNode}" var="node"
								selectionMode="single" id="userOfficeTree"
								style="width: 250px;height: 400px;overflow-y: auto;">
								<p:ajax event="select"
									update=":mainForm:userOfficeName,:mainForm:userOfficeRid, :mainForm:userPickList"
									listener="#{portalColumnBean.onOfficeNodeSelect}"
									oncomplete="PF('UserOfficePanel').hide();"
									process="@this, :mainForm:userPickList" />
								<p:treeNode>
									<h:outputText value="#{node.officename}" />
								</p:treeNode>
							</p:tree>
						</p:overlayPanel></td>
				</tr>
			</table>

			<p:pickList id="userPickList"
				value="#{portalColumnBean.userInfoDualListModel}" var="user"
				itemValue="#{user}" itemLabel="#{user.username}"
				converter="system.UserOfficeConvert" showSourceControls="false"
				showTargetControls="false" showCheckbox="true"
				showSourceFilter="true" showTargetFilter="true"
				filterMatchMode="contains" effect="drop">
				<f:facet name="sourceCaption">可选用户</f:facet>
				<f:facet name="targetCaption">已选用户</f:facet>

				<p:column style="width:50%;text-align: left;">#{user.username}</p:column>
				<p:column style="width:50%;text-align: left;">#{user.officeName}</p:column>
			</p:pickList>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="userSaveBtn"
							action="#{portalColumnBean.userManagerAction}"
							process="@this,userPickList"
							oncomplete="PF('UserDialog').hide();" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="userBackBtn"
							onclick="PF('UserDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</ui:define>
</ui:composition>











