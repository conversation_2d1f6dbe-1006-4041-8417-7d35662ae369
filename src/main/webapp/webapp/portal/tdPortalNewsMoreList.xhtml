<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdPortalNewsMoreBean}"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
		<script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript"
			src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript"
			src="/resources/component/quickDesktop/NewPopMenu.js"></script>
		<script type="text/javascript"
			src="/resources/component/quickDesktop/CreatePopup.js"></script>
		<script type="text/javascript"
			src="/resources/component/quickDesktop/index.js"></script>
		<script type="text/javascript"
			src="/resources/component/quickDesktop/zwx.system.js"></script>

		<script type="text/javascript">
            //<![CDATA[
            function forwordPage(type,rid,newsTitle){
                if("" != type && "" != rid && "" != newsTitle){
                	var url = "/webapp/portal/tdPortalNewsViewList.faces?newsType="+type+"&rid="+rid;
                    top.ShortcutMenuClick("01",newsTitle,url,"");
                }
            }
            //]]>
            
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="#{tdPortalNewsMoreBean.pageTitle}"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdPortalNewsMoreBean.searchAction}" update="dataTable" process="@this,@form" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:6%">
                <p:outputLabel value="标题："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:20%">
                <p:inputText id="searchTitle" value="#{tdPortalNewsMoreBean.searchTitle}" size="30"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:10%">
                <p:outputLabel value="发布部门："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px">
                <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                    <p:inputText id="searchOfficeName" value="#{tdPortalNewsMoreBean.searchOfficeName}" readonly="true"
                                 style="width: 180px;"/>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"
                                   style="position: relative;left: -45px;top:0px;"
                                   oncomplete="PF('OveralPanel').show()"/>
                    <h:inputHidden id="searchOfficeId" value="#{tdPortalNewsMoreBean.searchOfficeId}"/>
                </h:panelGrid>
                <p:overlayPanel id="overalPanel" for=":mainForm:searchOfficeName"
                                dynamic="false" style="width:280px;" widgetVar="OveralPanel">
                    <p:tree value="#{tdPortalNewsMoreBean.treeNode}" var="node"
                            selectionMode="single" id="choiceTree" 
                            selection="#{tdPortalNewsMoreBean.selectedNode}"
                            style="width: 260px;height: 200px;overflow-y: auto;">
                            <p:ajax event="select"
                             listener="#{tdPortalNewsMoreBean.searchSelectTreeNode(tdPortalNewsMoreBean.selectedNode)}"
                                     oncomplete="PF('OveralPanel').hide()"
                                     update=":mainForm:searchOfficeName,:mainForm:searchOfficeId"
                                     partialSubmit="true" process="@this,@parent"/>
                        <p:treeNode>
                            <p:outputLabel value="#{node.officename}" onclick=""/>
                        </p:treeNode>
                    </p:tree>
                </p:overlayPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="发布人："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchMan" value="#{tdPortalNewsMoreBean.searchMan}" size="30"/>
            </p:column>
            <p:column style="text-align:right;padding-right:5px;">
                <p:outputLabel value="发布日期："/>
            </p:column>
            <p:column style="text-align:left;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="date4"
                            navigator="true" yearRange="c-10:c+10"
                            converterMessage="发布日期，格式输入不正确！"
                            showButtonPanel="true"
                            value="#{tdPortalNewsMoreBean.searchStartDate}"/>
                <p:outputLabel value="～"/>
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="date5"
                            navigator="true" yearRange="c-10:c+10"
                            showButtonPanel="true"
                            converterMessage="发布日期，格式输入不正确！"
                            value="#{tdPortalNewsMoreBean.searchEndDate}"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="序号" style="width: 3%;text-align: center;">
        	<p:outputLabel value="#{R+1}"/>
        </p:column>
        <p:column headerText="标题" style="width: 37%;">
             <p:commandLink onclick="forwordPage('#{itm.newsType}','#{itm.rid}','#{itm.newsTitle}')"
                            value="#{itm.newsTitle}" process="@this"/>
        </p:column>
        <p:column headerText="重要程度" style="width:8%;">
            <p:rating value="#{itm.newsLevel}" readonly="true" />
        </p:column>
        <p:column headerText="发布时间" style="width: 8%;text-align: center;">
            <p:outputLabel value="#{itm.newsDate}">
                <f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="发布部门" style="width:8%;">
            <p:outputLabel value="#{itm.tsOffice.officename}"  />
        </p:column>
        <p:column headerText="发布人" style="width:8%;">
            <p:outputLabel value="#{itm.tsUserInfo.username}" />
        </p:column>
    </ui:define>
</ui:composition>