<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
	<ui:define name="insertScripts">
		<style type="text/css">
.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 5px;
	padding-right: 0;
}
</style>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column style="text-align:left;padding-left:5px;height: 20px;" colspan="2">
				<h:outputText value="信息类型维护" />
			</p:column>
		</p:row>
	</ui:define>
	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>

				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{portalNewsTypeBean.searchAction}" update="typeTreeDataTable" process="@this,searchTypeName" />
				<p:commandButton value="添加根节点" update="typeEditDialog" icon="ui-icon-plus" process="@this,typeTreeDataTable" oncomplete="PF('TypeEditDialog').show()"
					action="#{portalNewsTypeBean.addInit}" id="addRootBtn">
					<p:resetInput target="typeEditDialog" />
				</p:commandButton>
				<p:commandButton value="用户授权" update="typeTreeDataTable" process="@this, typeTreeDataTable" action="#{portalNewsTypeBean.sqInitAction}">
					<f:setPropertyActionListener target="#{portalNewsTypeBean.ifAdmin}" value="false" />
					<p:ajax event="dialogReturn" listener="#{portalNewsTypeBean.onUserSelect}" process="@this" resetValues="true" />
				</p:commandButton>
				<p:commandButton value="管理员授权" update="typeTreeDataTable" process="@this,typeTreeDataTable" action="#{portalNewsTypeBean.sqInitAction}">
					<f:setPropertyActionListener target="#{portalNewsTypeBean.ifAdmin}" value="true" />
					<p:ajax event="dialogReturn" listener="#{portalNewsTypeBean.onUserSelect}" process="@this" resetValues="true" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width: 10%">
				<h:outputText value="类型名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="searchTypeName" value="#{portalNewsTypeBean.searchTypeName}" style="width: 180px;" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格 -->
	<ui:define name="insertContent">
		<p:treeTable id="typeTreeDataTable" value="#{portalNewsTypeBean.rootNode}" var="itm" emptyMessage="没有您要找的记录！" selectionMode="single"
			selection="#{portalNewsTypeBean.selectedNode}">
			<p:column headerText="序号" style="width: 100px;">
				<h:outputText value="#{itm.xh}" />
			</p:column>
			<p:column headerText="类型名称" style="width: 150px;text-align: center">
				<h:outputText value="#{itm.typeName}" />
			</p:column>
			<p:column headerText="置顶条数" style="width: 80px;text-align: center">
				<h:outputText value="#{itm.zdCounts}" />
			</p:column>
			<p:column headerText="说明" style="width: 350px;">
				<h:outputText value="#{itm.typeDesc}" />
			</p:column>
		</p:treeTable>
		<p:contextMenu for="typeTreeDataTable">
			<p:menuitem value="添加子节点" update="typeEditDialog" icon="ui-icon-plus" process="@this,typeTreeDataTable" oncomplete="PF('TypeEditDialog').show()"
				actionListener="#{portalNewsTypeBean.addInit}">
				<f:setPropertyActionListener target="#{portalNewsTypeBean.isCode}" value="true" />
			</p:menuitem>
			<p:menuitem value="修改" update="typeEditDialog" icon="ui-icon-pencile" process="@this,typeTreeDataTable" oncomplete="PF('TypeEditDialog').show()"
				actionListener="#{portalNewsTypeBean.modInit}" />
			<p:menuitem value="删除" update="typeTreeDataTable" icon="ui-icon-close" process="@this,typeTreeDataTable" action="#{portalNewsTypeBean.deleteAction}">
			</p:menuitem>
		</p:contextMenu>
		<!-- 新增、修改 -->
		<p:dialog id="typeEditDialog" header="信息类型维护" widgetVar="TypeEditDialog" resizable="false" width="600" height="170" modal="true">
			<p:panelGrid style="width:100%;" id="typeEditGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="类型名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="typeName" value="#{portalNewsTypeBean.tdPortalNewsType.typeName}" size="24" maxlength="50" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="序号：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="nums" value="#{portalNewsTypeBean.tdPortalNewsType.xh}" size="24" maxlength="3" required="true" requiredMessage="序号不允许为空！" converterMessage="请输入正确的序号！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="置顶条数：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="zdNum" value="#{portalNewsTypeBean.tdPortalNewsType.zdCounts}" size="24" maxlength="2" required="true" requiredMessage="置顶条数不允许为空！" converterMessage="请输入数字！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">
						<h:outputText value="说明：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="typeDesc" value="#{portalNewsTypeBean.tdPortalNewsType.typeDesc}" size="50" maxlength="100" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:center;" colspan="2">
						<p:commandButton value="保存" icon="ui-icon-check" id="typeSaveBtn" action="#{portalNewsTypeBean.saveAction}" process="@form" update="typeTreeDataTable,typeEditGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="typeBackBtn" onclick="PF('TypeEditDialog').hide();" immediate="true" />
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:dialog>
	</ui:define>
</ui:composition>