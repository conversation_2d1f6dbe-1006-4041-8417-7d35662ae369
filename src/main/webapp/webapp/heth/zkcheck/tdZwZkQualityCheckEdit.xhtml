<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function showShade() {
                PF('ShadeTip').show();
            }
            //注意 因文件下载完 PrimeFaces 会刷新页面 所以 我们需要监听onload事件
            window.onload=function (){
                document.getElementById("tabView:editForm:generateReportErrMsgId").click();
            }
            function hideShade() {
                //这个方法不会执行 因下载完后 PrimeFaces会刷新页面
                PF('ShadeTip').hide();
            }
            function generateClick(){
                document.getElementById("tabView:editForm:generateReportId").click();
            }
        </script>
        <style type="text/css">
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }
            .shadeTip>div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }

        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="技术服务机构质量监测" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 rendered="#{ null != mgrbean.pageType() and 1 == mgrbean.pageType() and  mgrbean.checkMain.stateMark==0}"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm"
                                 rendered="#{ null != mgrbean.pageType() and 1 == mgrbean.pageType() and  mgrbean.checkMain.stateMark==0}"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="submitBtn" action="#{mgrbean.cancelAction}"
                                 rendered="#{null != mgrbean.pageType() and 1 == mgrbean.pageType() and  mgrbean.checkMain.stateMark==1 and mgrbean.jCCancelBtn}" update=":tabView" process="@this">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
	<ui:define name="insertOtherContents">
		<p:confirmDialog message="切换被考核机构将清空已考核信息，是否继续？" header="消息确认框" widgetVar="ConfirmDialog">
			<p:outputPanel style="text-align:right;">
	            <p:commandButton value="确定" action="#{mgrbean.changeCheckType}" icon="ui-icon-check" process="@this,editForm" update=":tabView:editForm" oncomplete="PF('ConfirmDialog').hide();"/>
	            <p:commandButton value="取消" icon="ui-icon-close"  process="@this" oncomplete="PF('ConfirmDialog').hide();" />
			</p:outputPanel>
        </p:confirmDialog>
		<p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="Confirm1Dialog">
			<p:outputPanel style="text-align:right;">
	            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" process="@this,editForm" update=":tabView:editForm" oncomplete="PF('Confirm1Dialog').hide();"/>
	            <p:commandButton value="取消" icon="ui-icon-close"  process="@this" oncomplete="PF('Confirm1Dialog').hide();" />
			</p:outputPanel>
        </p:confirmDialog>
        <p:panelGrid id="baseInfo"  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="基本信息"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px;">
                	<p:outputLabel value="*" style="color:red;" rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}"></p:outputLabel>
                    <p:outputLabel value="被考核机构："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;width: 280px;" >
                	<h:panelGrid columns="2" style="border-color: transparent;margin: 0px;padding: 0px;#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}" rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}">
						<p:inputText id="unitName"  readonly="true" style="width:230px;"
							value="#{mgrbean.checkMain.fkByOrgId.unitname}" 
							onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
						<p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
									   action="#{mgrbean.selectOrgList}" process="@this,editForm" style="position: relative;left: -30px;"
									   >
							<p:ajax event="dialogReturn" process="@this,editForm" resetValues="true" listener="#{mgrbean.onOrgSelect}" update="unitName"/>
						</p:commandLink>
					</h:panelGrid>
                    <p:outputLabel value="#{mgrbean.checkMain.fkByOrgId.unitname}" rendered="#{mgrbean.checkMain.stateMark !=null and mgrbean.checkMain.stateMark==1}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                	<p:outputLabel value="*" style="color:red;" rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}"></p:outputLabel>
                    <p:outputLabel value="考核日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;width: 280px;">
                	<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true" style="#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}"  rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c" converterMessage="考核日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()"
                            value="#{mgrbean.checkMain.checkDate}" />
                    <p:outputLabel value="#{mgrbean.checkMain.checkDate}" rendered="#{mgrbean.checkMain.stateMark !=null and mgrbean.checkMain.stateMark==1}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
                                           locale="cn" />
                    </p:outputLabel>
                </p:column>
                <p:column style="text-align:right;width: 260px; ">
                    <p:outputLabel value="*" style="color:red;" rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}"></p:outputLabel>
                    <p:outputLabel value="检测报告名称（编号）："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" >
                    <h:panelGrid columns="2" style="border-color: transparent;margin: 0px;padding: 0px;#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}" rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}">
                        <p:inputText id="rptNo"  readonly="true" style="width:240px;"
                                     value="#{mgrbean.checkMain.fkByJcRptId.rptNo}"
                                     onclick="$('#tabView\\:editForm\\:onRptNoSelect').click()"/>
                        <p:commandLink id="onRptNoSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectRptNoList}" process="@this,editForm" style="position: relative;left: -30px;"
                        >
                            <p:ajax event="dialogReturn" process="@this,editForm" resetValues="true" listener="#{mgrbean.onRptNoSelect}" update=":tabView:editForm"/>
                        </p:commandLink>
                    </h:panelGrid>
                    <p:outputLabel value="#{mgrbean.checkMain.fkByJcRptId.rptNo}" rendered="#{mgrbean.checkMain.stateMark !=null and mgrbean.checkMain.stateMark==1}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="4">
                        <p:outputLabel value="现场考核表"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row >
                <p:column style="text-align:right;height: 38px;width:260px;">
                    <p:outputLabel value="总分值："/>
                </p:column>
                <!--编辑页-->
                <p:column style="text-align:left;padding-left:6px;width:280px;">
                    <p:outputLabel value="#{mgrbean.checkMain.totalCheckVal}"></p:outputLabel>
                </p:column>
                <p:column style="text-align:right;height: 38px;width:260px;">
                    <p:outputLabel value="总得分："/>
                </p:column>
                <!--编辑页-->
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.checkMain.totalScoreVal}"></p:outputLabel>
                </p:column>
            </p:row>
            <p:row >
            	<p:column style="text-align:right;height: 38px;width:260px;">
                    <p:outputLabel value="评估结果："/>
                </p:column>
                <!--编辑页-->
                <p:column style="text-align:left;padding-left:6px;width:280px;">
                	<p:outputLabel value="#{mgrbean.checkMain.fkByCheckRstId.codeName}"></p:outputLabel>
                </p:column>
                <!--编辑页-->
            	<p:column style="width:260px;text-align:right;">
                	<p:outputLabel id="checkTable" value="*" style="color:red;"  rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}"></p:outputLabel>
                	 <p:outputLabel value="考核结果表："/>
                </p:column>
                <!--编辑页-->
                <p:column style="text-align:left;padding-left:6px;padding-top: 0px!important;padding-bottom: 0px!important;"  rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==0}">
                    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;" id="specialBtn">
                        <p:remoteCommand action="#{mgrbean.exeGenerateReport}" name="preGenerateMod" process="@this,:tabView:editForm" onstart="showShade();" />
                        <p:commandButton value="生成" icon="ui-icon-print" process="@this,:tabView:editForm"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.writePath}"
                                         action="#{mgrbean.preGenerateReport}" />
                        <p:commandButton value="上传" icon="ui-icon-arrowreturnthick-1-n"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.writePath}"
                                         action="#{mgrbean.preUploadReport}"/>
                        <p:commandButton value="下载" icon="ui-icon-arrowthick-1-s"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.writePath}"
                                         process="@this" ajax="false" >
                            <p:fileDownload value="#{mgrbean.reportDownloadFile}" />
                        </p:commandButton>
                        <p:commandButton value="删除" icon="ui-icon-trash"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.writePath }"
                                         action="#{mgrbean.deleteReport}" process="@this,:tabView:editForm" update=":tabView:editForm:specialBtn,baseInfo">
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                        </p:commandButton>
                        <p:commandButton style="display: none;"  id="generateReportId" ajax="false" icon="ui-icon-print"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.writePath}"
                                         action="#{mgrbean.deleteVirReportFile}" process="@this,:tabView:editForm" onclick="PrimeFaces.monitorDownload(showShade,hideShade);"  >
                            <p:fileDownload  value="#{mgrbean.reportFile}" />
                        </p:commandButton>
                        <p:commandButton  style="display: none;" id="generateReportErrMsgId" icon="ui-icon-print" rendered="#{null ne mgrbean.checkMain}"
                                         action="#{mgrbean.showGenerateReportErrMsg}" process="@this"  />
                        <p:inputText style="visibility: hidden;width: 0"/>
                    </h:panelGrid>
                </p:column>

                <!-- 详情页 -->
                <p:column style="text-align:left;padding-left:6px;padding-top: 0px!important;padding-bottom: 0px!important;"  rendered="#{mgrbean.checkMain.stateMark==null or mgrbean.checkMain.stateMark==1}">
                    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                        <p:commandButton value="下载" icon="ui-icon-arrowthick-1-s"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.writePath}"
                                         process="@this" ajax="false" >
                            <p:fileDownload value="#{mgrbean.reportDownloadFile}" />
                        </p:commandButton>
                    </h:panelGrid>
                </p:column>
            </p:row>

            <p:row rendered="#{mgrbean.checkMain.checkTables.size()>0}" >
          		<p:column colspan="4">
          			<p:panelGrid  style="width:100%" id="checkTableDatas">
            			<p:row>
            				<p:column style="width:256px;height: 20px;text-align:center;" styleClass="ui-state-default">
			                	<p:outputLabel value="考核表"></p:outputLabel>
			                </p:column>
			                <p:column style="width:240px;text-align:center;" styleClass="ui-state-default">
			                	<p:outputLabel value="考核项目"></p:outputLabel>
			                </p:column>
			                <p:column style="width:100px;text-align:center;" styleClass="ui-state-default">
			                	<p:outputLabel value="分值"></p:outputLabel>
			                </p:column>
			                <p:column style="width:100px;text-align:center;" styleClass="ui-state-default">
			                	<p:outputLabel value="实得分"></p:outputLabel>
			                </p:column>
			                <p:column style="text-align:center;" styleClass="ui-state-default">
			                	<p:outputLabel value="存在问题" ></p:outputLabel>
			                </p:column>
			                <p:column style="width: 80px;text-align:center;" styleClass="ui-state-default">
			                	<p:outputLabel value="状态" ></p:outputLabel>
			                </p:column>
			                <p:column style="text-align:center;width: 120px;" styleClass="ui-state-default">
			                	<p:outputLabel value="操作"></p:outputLabel>
			                </p:column>
			            </p:row>
			            <c:forEach items="#{mgrbean.checkMain.checkTables}" var="table" varStatus="tableIndex">
                            <!--现场-->
			            	<c:forEach items="#{table.zkcheckItems}" var="item" varStatus="itemIndex">
			            		<p:row>
				            		<!-- 考核项目 -->
			            			<p:column rendered="#{itemIndex.index == 0}" rowspan="#{table.zkcheckItems.size()+table.checkItems.size()}">
					                	<p:outputLabel value="#{table.fkByCheckTableId.checkName}"></p:outputLabel>
					                </p:column>
					                <p:column style="height:28px;">
					                	<p:outputLabel value="#{item.fkByItemId.codeName}"></p:outputLabel>
					                </p:column>
					                <p:column style="height:28px;text-align:center;">
					                	<p:outputLabel value="#{item.checkVal}" rendered="#{item.checkVal != null}"></p:outputLabel>
					                	<p:outputLabel value="/" rendered="#{item.checkVal == null}"></p:outputLabel>
					                </p:column>
					                <p:column style="height:28px;text-align:center;">
					                	<p:outputLabel value="#{item.scoreVal}"  rendered="#{item.scoreVal != null}"></p:outputLabel>
                                        <p:outputLabel value="/" rendered="#{item.checkVal == null and item.scoreVal == null}"></p:outputLabel>
					                </p:column>
					                <!-- 存在问题 -->
					                <p:column>
					                	<h:outputText id="zkdeductRsn#{tableIndex.index}#{itemIndex.index}" value="#{item.deductRsn}" styleClass="zwx-tooltip"/>
							            <p:tooltip for="zkdeductRsn#{tableIndex.index}#{itemIndex.index}" style="max-width:450px;">
							            	<p:outputLabel value="#{item.deductRsn2}" escape="false"></p:outputLabel>
							            </p:tooltip>
					                </p:column>
					                <!-- 状态 -->
					                <p:column style="text-align:center;" rendered="#{itemIndex.index==0}" rowspan="#{table.zkcheckItems.size()}">
					                	<p:outputLabel value="已核查" rendered="#{item.fkByItemId.extendS1 != null and item.fkByItemId.extendS1 == 1 and table.zkStateMark==2}"></p:outputLabel>
					                	<p:outputLabel value="待核查" rendered="#{item.fkByItemId.extendS1 != null and item.fkByItemId.extendS1 == 1 and table.zkStateMark==1}"></p:outputLabel>
					                </p:column>
					                <p:column rendered="#{itemIndex.index==0}" rowspan="#{table.zkcheckItems.size()}">
					                	<p:spacer width="5"></p:spacer>
					                	<p:commandLink  value="查看" rendered="#{table.zkStateMark==1 or table.zkStateMark==2 }"
					                		process="@this,:tabView:editForm" action="#{mgrbean.modCheckIndex}"
					                		oncomplete="hideTooltips();" onclick="hideTooltips();">
                                            <f:setPropertyActionListener target="#{mgrbean.curCheckTable}" value="#{table}" />
                                            <f:setPropertyActionListener target="#{mgrbean.subEditTag}" value="0" />
                                        </p:commandLink>
					                </p:column>
			            		</p:row>
			            	</c:forEach>
                            <!--维护-->
			            	<c:forEach items="#{table.checkItems}" var="item" varStatus="itemIndex">
			            		<p:row>
				            		<!-- 考核项目 -->
			            			<p:column rendered="#{itemIndex.index == 0 and (table.zkcheckItems == null or table.zkcheckItems.size() == 0)}" rowspan="#{table.checkItems.size()}">
					                	<p:outputLabel value="#{table.fkByCheckTableId.checkName}"></p:outputLabel>
					                </p:column>
					                <p:column style="height:28px;">
					                	<p:outputLabel value="#{item.fkByItemId.codeName}"></p:outputLabel>
					                </p:column>
					                <p:column style="height:28px;text-align:center;">
					                	<p:outputLabel value="#{item.checkVal}" rendered="#{item.checkVal != null}"></p:outputLabel>
					                	<p:outputLabel value="/" rendered="#{item.checkVal == null}"></p:outputLabel>
					                </p:column>
					                <p:column style="height:28px;text-align:center;">
					                	<p:outputLabel value="#{item.scoreVal}"  rendered="#{item.scoreVal != null}"></p:outputLabel>
                                        <p:outputLabel value="/" rendered="#{item.checkVal == null and item.scoreVal == null}"></p:outputLabel>
					                </p:column>
					                <!-- 存在问题 -->
					                <p:column>
					                	<h:outputText id="deductRsn#{tableIndex.index}#{itemIndex.index}" value="#{item.deductRsn}" styleClass="zwx-tooltip"/>
							            <p:tooltip for="deductRsn#{tableIndex.index}#{itemIndex.index}" style="max-width:450px;">
							            	<p:outputLabel value="#{item.deductRsn2}" escape="false"></p:outputLabel>
							            </p:tooltip>
					                </p:column>
					                <!-- 状态 -->
					                <p:column style="text-align:center;" rendered="#{itemIndex.index==0}" rowspan="#{table.checkItems.size()}">
					                	<p:outputLabel value="#{table.stateMark==1?'已提交':'待提交'}"></p:outputLabel>
					                </p:column>
					                <p:column rendered="#{itemIndex.index==0}" rowspan="#{table.checkItems.size()}">
					                	<p:spacer width="5"></p:spacer>
					                	<p:commandLink  value="#{(null ne table.stateMark and table.stateMark==1)?'查看':'进入'}"
					                		process="@this,:tabView:editForm" action="#{mgrbean.modCheckIndex}"
					                		oncomplete="hideTooltips();" onclick="hideTooltips();">
                                            <f:setPropertyActionListener target="#{mgrbean.curCheckTable}" value="#{table}" />
                                            <f:setPropertyActionListener target="#{mgrbean.subEditTag}" value="1" />
                                        </p:commandLink>
					                </p:column>
			            		</p:row>
			            	</c:forEach>
			            </c:forEach>
           			</p:panelGrid>
          		</p:column>
          	</p:row>
        </p:panelGrid>
        <p:dialog header="文件上传" widgetVar="ReportFileUpload" id="reportFileUpload"
                  resizable="false" modal="true" width="730" >
            <table width="690">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：WORD、PDF）"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择上传文件！" style="width:690px;"
                                      previewWidth="50"   id="fileUpload"
                                      fileUploadListener="#{mgrbean.uploadReport}" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！" label="选择文件" uploadLabel="上传"
                                      cancelLabel="取消" sizeLimit="10485760"
                                      invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传doc，docx，pdf类型文件！" process="@this"
                                      mode="advanced" dragDropSupport="true"
                                      allowTypes="/(\.|\/)(doc|docx|pdf)$/" />
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog id="shadeTip" widgetVar="ShadeTip" modal="true" height="20" resizable="false" showHeader="false" closeOnEscape="true" styleClass="shadeTip">
            <p:panelGrid >
                <p:row style="border:1px solid transparent !important;">
                    <p:column style="border: transparent !important;">
                        <p:graphicImage url="/resources/images/main/loading5.gif" style="margin-top: 4px;"/>
                    </p:column>
                    <p:column style="border: transparent !important;">
                        <h:outputText style="color: #FFFFFF;font-size: 15px;" value="报告生成中，请等待..." />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
    </ui:define>
</ui:composition>