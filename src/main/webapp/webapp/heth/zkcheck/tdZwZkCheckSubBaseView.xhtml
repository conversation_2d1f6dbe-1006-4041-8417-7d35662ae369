<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.AbstractTdZwZkCheckMainListBean"-->
    <!-- 具体数据展示 -->
    <p:outputPanel id="dataPanel">
        <p:panelGrid style="width:100%;height:100%;margin-top:10px;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText
                                value="#{null ne mgrbean.curCheckTable and null ne mgrbean.curCheckTable.fkByCheckTableId ? mgrbean.curCheckTable.fkByCheckTableId.checkName : ''}"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                    <h:outputLabel value="考核人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:9px;" colspan="5">
                    <h:outputText value="#{mgrbean.curCheckTable.checkPsn}" style="width: 260px;"/>
                </p:column>
            </p:row>

        </p:panelGrid>

        <c:forEach var="checkItem" items="#{mgrbean.zwZkCheckItemList}" varStatus="TMR">
            <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;overflow-x: hidden;"
                         styleClass="zwpx-table">
                <p:row>
                    <p:column colspan="5" style="text-align:left;height: 30px; border-right: 0px;">
                        <h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
                            <div style="display:flex; ">
                                <div style="width: 3px; height: 12px; background: #2e6e9e;margin-top: 1px;"/>
                                <label class="ui-outputlabel ui-widget"
                                       style=" font-size: 14px !important; margin-left: 5px;font-weight: 600;color: #2e6ea9;">
                                    #{null ne checkItem.fkByItemId ? checkItem.fkByItemId.codeName : ''}</label>
                            </div>
                        </h:panelGrid>
                    </p:column>
                    <p:column style="text-align: left;width: 450px;  border-left: 0px;">
                        <p:outputLabel
                                value="分值：#{checkItem.checkVal} #{null ne checkItem.checkVal ? '分' : '  '}"
                                rendered="#{null ne checkItem.checkVal}"/>
                        <p:outputLabel
                                style="margin-left: 20px;#{mgrbean.checkMain.ifDevelop eq 0 or checkItem.checkVal eq null?'visibility: hidden;':''}"
                                value="实得分：#{checkItem.scoreVal} #{null ne checkItem.scoreVal ? '分' : '  '}"
                                id="freshItemLabel#{TMR.index}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <c:forEach items="#{checkItem.checkSubList}" var="checkSub" varStatus="SR">
                <p:panelGrid style="width:100%;margin-top:0px;line-height: 14px;overflow-x: hidden;"
                             styleClass="tableTr">
                    <p:row>
                        <p:column colspan="5"
                                  style="text-align:left;height: 30px;border-right: 0px;background-color: #dfeffc; border-top: 0px;border-bottom: 0px;">
                            <div style="line-height: 24px;"><h:outputText escape="false"
                                                                          value="#{null ne checkSub.fkByScoreId ? mgrbean.indexDescMap.get(checkSub.fkByScoreId.rid) : ''}"/>
                            </div>
                        </p:column>
                        <p:column
                                style="text-align: left; width: 450px;border-left: 0px;background-color: #dfeffc;border-top: 0px;border-bottom: 0px;">
                            <p:outputLabel
                                    value="分值：#{null ne checkSub.fkByScoreId ? checkSub.fkByScoreId.score : ''} #{null ne checkSub.fkByScoreId.score ? '分' : '  '}"
                                    rendered="#{checkSub.assessMark eq 0 and null ne checkSub.fkByScoreId and checkSub.fkByScoreId.score ne null}"/>
                            <p:outputLabel value="无需考核"
                                           rendered="#{checkSub.assessMark ne 0}"
                                           style="bottom: 0; right: 120px; background-color: green; color: white; padding: 5px 5px; border-radius: 3px; font-size: 12px !important;"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <p:panelGrid style="width:100%;margin-top:0px;line-height: 14px;overflow-x: hidden;"
                             styleClass="zwpx-table">
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;"
                                  rendered="#{checkSub.fkByScoreId.scoreOptionList.size() eq 0}">
                            <h:outputLabel value="实得分："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:9px;height: 33px;width: 250px;"
                                  rendered="#{checkSub.fkByScoreId.scoreOptionList.size() eq 0}">
                            <p:outputLabel value="#{checkSub.scoreVal}"/>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;"
                                  rendered="#{checkSub.fkByScoreId.scoreOptionList.size() gt 0}">
                            <h:outputLabel value="结果："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:9px;height: 33px;width: 250px;"
                                  rendered="#{checkSub.fkByScoreId.scoreOptionList.size() gt 0}">
                            <p:outputLabel value="#{checkSub.fkByRstId.fkByOptionId.codeName}"/>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;width: 180px;">
                            <!-- 实得分小于评估项分值 -->
                            <h:outputLabel
                                    value="存在问题："
                                    escape="false" id="deductCauseRedId#{TMR.index}PK#{SR.index}"/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:9px;" colspan="3">
                            <div style="width: 710px;line-height: 24px">
                                <h:outputText value="#{checkSub.deductRsn}"/>
                            </div>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                            <h:outputLabel value="备注："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:9px;" colspan="5">
                            <div style="width: 1160px;line-height: 24px">
                                <h:outputText value="#{checkSub.rmk}"/>
                            </div>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </c:forEach>
        </c:forEach>
    </p:outputPanel>
</ui:composition>