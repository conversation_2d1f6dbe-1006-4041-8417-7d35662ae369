<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.CheckExpertListBean"-->
    <ui:param name="mgrbean" value="#{checkExpertListBean}"/>
    <!--编辑页面-->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/checkExpertEdit.xhtml"/>

    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }

            function showStatus() {
                PF('StatusDialog').show();
            }

            function hideStatus() {
                PF('StatusDialog').hide();
            }
        </script>
        <style type="text/css">
            .searchTime input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="报告审核与专家分配"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0;">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}"
                                 oncomplete="datatableOffClick();" update=":tabView:mainForm:dataTable"
                                 process="@this,mainGrid"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" id="extractBtn" process="@this,dataTable"
                                 action="#{mgrbean.beforeCheckAction}"
                                 oncomplete="datatableOffClick();">
                </p:commandButton>
                <p:commandButton value="批量分配" icon="ui-icon-check" id="exportBtn" process="@this,mainGrid,dataTable"
                                 action="#{mgrbean.beforeBatchAllocationAction()}">
                    <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onTaskAllocationSelect}"
                             oncomplete="datatableOffClick();">
                    </p:ajax>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 160px;height:38px;">
                <p:outputLabel value="地区："/>
            </p:column>
            <p:column style="text-align:left;width: 260px;padding-left:10px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="0" onchange="onSearchNodeSelect()"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="unitPanel"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <p:outputLabel value="体检机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;width: 260px;">
                <!-- 弹出框 -->
                <p:outputPanel id="unitPanel">
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                            process="@this"
                                            resetValues="true" update="unitName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <p:outputLabel value="抽取类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkType}">
                    <f:selectItems value="#{mgrbean.bhkTypeList}" var="itm" itemValue="#{itm.rid}"
                                   itemLabel="#{itm.codeName}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <p:outputLabel value="抽取日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchSDate}"
                                              endDate="#{mgrbean.searchEDate}" styleClass="searchTime"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="职业健康检查结论："/>
            </p:column>
            <p:column style="text-align:left;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
                                        selectedIds="#{mgrbean.searchSelBhkrstIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="180" height="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <p:outputLabel value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}" var="itm" itemValue="#{itm.value}"
                                   itemLabel="#{itm.label}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:20px;text-align:center;padding: 4px 10px !important;"
                  disabledSelection="#{itm[15] eq 1 or itm[15] eq 2 ?false:true}"/>
        <p:column headerText="地区" style="width:180px;">
            <h:outputLabel value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="体检机构名称" style="width: 170px;">
            <h:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="抽取类别" style="width: 60px;text-align: center;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="姓名" style="width: 60px;text-align: center;">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="体检编号" style="width: 100px;text-align: center;">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告出具日期" style="width: 90px;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="职业健康检查结论" style="width: 110px;text-align: center;">
            <h:outputLabel value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="胸片/听力检测结论" style="width: 115px;text-align: center;">
            <h:outputLabel value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="接害工龄（年）" style="width: 95px;text-align: center;">
            <h:outputLabel value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width: 170px;">
            <h:outputLabel value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="抽取日期" style="width: 75px;text-align: center;">
            <h:outputLabel value="#{itm[11]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="退回原因" style="width: 180px;">
            <h:outputText id="backRsn" value="#{itm[14]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="backRsn" style="max-width:400px;">
                <p:outputLabel styleClass="cs-break-word" value="#{itm[14]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="状态" style="width: 60px;text-align: center;">
            <h:outputLabel value="待审核" rendered="#{1 eq itm[15]}"/>
            <h:outputLabel value="审核退回" rendered="#{3 eq itm[15]}"/>
            <h:outputLabel value="待分配" rendered="#{2 eq itm[15]}"/>
            <h:outputLabel value="已分配" rendered="#{itm[15] ge 4}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="审核" update=":tabView" process="@this,:tabView:mainForm:mainGrid" rendered="#{1 eq itm[15]}"
                           onclick="hideTooltips();"
                           action="#{mgrbean.modInitAction()}" oncomplete="datatableOffClick();">
                <f:setPropertyActionListener value="false" target="#{mgrbean.ifView}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" rendered="#{itm[15] ge 2}"
                           onclick="hideTooltips();"
                           update=":tabView" process="@this,:tabView:mainForm:mainGrid" oncomplete="datatableOffClick();">
                <f:setPropertyActionListener value="true" target="#{mgrbean.ifView}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{2 eq itm[15]}"/>
            <p:commandLink value="任务分配" rendered="#{2 eq itm[15]}"
                           process="@this,:tabView:mainForm:mainGrid"
                           onclick="hideTooltips();"   action="#{mgrbean.beforeTaskAllocationAction()}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onTaskAllocationSelect}"
                        update=":tabView:mainForm:dataTable" oncomplete="datatableOffClick();">
                </p:ajax>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[15] ge 4}"/>
            <p:commandLink value="专家账号" rendered="#{itm[15] ge 4}" oncomplete="datatableOffClick();"
                           update=":tabView:mainForm:dataTable" process="@this,:tabView:mainForm:mainGrid"
                           action="#{mgrbean.expertAccountAction()}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <p:dialog id="checkConfirmDialog" header="审核意见"
                  widgetVar="CheckConfirmDialog" resizable="false" width="840"
                  modal="true">
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%" id="checkPanel">
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="审核意见："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:selectOneRadio value="#{mgrbean.checkState}" style="width: 120px">
                            <f:selectItem itemLabel="通过" itemValue="1"/>
                            <f:selectItem itemLabel="退回" itemValue="2"/>
                            <p:ajax event="change" process="@this,checkPanel"
                                    listener="#{mgrbean.changeCheckStateBatch}"
                                    update="checkPanel" resetValues="true"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;" rendered="#{2 eq mgrbean.checkState}"/>
                        <p:outputLabel value="退回原因："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px;">
                        <p:inputTextarea rows="5" autoResize="false" id="batchCheckRst"
                                         style="resize: none;width: 594px;height: 120px;margin-left: 5px"
                                         readonly="#{mgrbean.checkState eq null or 1 eq mgrbean.checkState}"
                                         maxlength="200" value="#{mgrbean.checkRst}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" action="#{mgrbean.reviewBatchAction}"
                                         icon="ui-icon-check" onclick="zwx_loading_start();"
                                         process="@this,checkConfirmDialog"
                                         oncomplete="zwx_loading_stop()"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('CheckConfirmDialog').hide();datatableOffClick()"
                                         type="button" resetValues="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <!--专家账号信息-->
        <p:dialog id="expertAccountDialog" header="专家账号" widgetVar="ExpertAccountDialog" resizable="false" width="760" height="380" modal="true">
            <p:inputText style="visibility: hidden;width: 0;height: 0px;"/>
            <p:dataTable var="itm" value="#{mgrbean.bhkExperts}" id="expertAccountTable"
                         rows="10" emptyMessage="没有您要找的记录！" rowsPerPageTemplate="#{'10,20,50'}"
                         paginator="true" paginatorPosition="bottom" pageLinks="5"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                <p:column headerText="地区" style="width: 180px;">
                    <h:outputText value="#{itm[1]}"/>
                </p:column>
                <p:column headerText="单位名称" >
                    <h:outputText value="#{itm[2]}"/>
                </p:column>
                <p:column headerText="姓名" style="width: 100px;text-align: center;">
                    <h:outputText value="#{itm[3]}"/>
                </p:column>
                <p:column headerText="账号" style="width: 100px;text-align: center;">
                    <h:outputText value="#{itm[4]}"/>
                </p:column>
            </p:dataTable>
        </p:dialog>
    </ui:define>
</ui:composition>