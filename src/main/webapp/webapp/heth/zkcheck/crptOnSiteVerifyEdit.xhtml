<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.CrptOnSiteVerifyListBean"-->
    <ui:define name="insertEditScripts">
        <style>
            .blue_label {
                font-size: 14px !important;
                margin-left: 5px;
                font-weight: 600;
                color: #2e6ea9;
            }

            .white_col {
                text-align: left;
                height: 30px;
                background-color: #dfeffc;
                border-top: 0 !important;
                border-bottom: 0 !important;
            }

            .tableTr .ui-widget-content {
                border: 0 solid #a6c9e2;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            jQuery(document).ready(function () {
                windowScrollTop();
            });

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }

            //]]>
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位现场核查"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮组 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="暂存" icon="ui-icon-disk"
                                 onclick="zwx_loading_start()" oncomplete="zwx_loading_stop();"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm"
                                 rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm" resetValues="true"
                                 rendered="#{mgrbean.tdZwCheckRpt.state ne 2}">
                </p:commandButton>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.beforeQuashAction}" process="@this,:tabView:editForm"
                                 rendered="#{mgrbean.tdZwCheckRpt.state eq 2 and mgrbean.quashMark}">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" onclick="hideTooltips();"
                                 action="#{mgrbean.backAction}" process="@this" update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="sticky"/>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog1"
                         styleClass="myConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                             onclick="hideTooltips();zwx_loading_start()"
                             oncomplete="zwx_loading_stop();PF('ConfirmDialog1').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog1').hide();"
                             type="button"/>
        </p:confirmDialog>
        <p:confirmDialog message="确定要撤销吗？" header="消息确认框" widgetVar="ConfirmDialog2"
                         styleClass="myConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.quashAction}" icon="ui-icon-check"
                             onclick="hideTooltips();zwx_loading_start()"
                             oncomplete="zwx_loading_stop();PF('ConfirmDialog2').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog2').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>
    <ui:define name="insertOtherContents">
        <ui:include src="tdZwCheckRptCommView.xhtml"/>
        <c:forEach var="checkTableVO" items="#{mgrbean.tdZwCheckRpt.checkTableVOList}" varStatus="X">
            <p:panelGrid style="width:100%;margin-top: 10px;">
                <f:facet name="header">
                    <p:row>
                        <p:column style="padding-left:3px;text-align:left;" colspan="2">
                            <p:outputLabel value="#{checkTableVO.tableName}"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column style="padding-left: 3px;text-align:right;width: 180px;height: 38px;">
                        <h:outputLabel value="*" style="color: red;"
                                       rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                        <p:outputLabel value="考核人："/>
                    </p:column>
                    <p:column style="padding-left: 9px;text-align:left;">
                        <p:inputText value="#{checkTableVO.tdZwCheckTable.checkPsn}"
                                     style="width: 180px;" maxlength="100"
                                     rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                        <p:outputLabel value="提示：多个考核人以中文逗号隔开！" style="color:blue;padding: 10px;"
                                       rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                        <p:outputLabel value="#{checkTableVO.tdZwCheckTable.checkPsn}"
                                       rendered="#{mgrbean.tdZwCheckRpt.state eq 2}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <c:forEach var="tableItem" items="#{checkTableVO.checkTableItemVOList}" varStatus="Y">
                <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;overflow-x: hidden;">
                    <p:row>
                        <p:column colspan="6" style="text-align:left;height: 30px; border-right: 0;">
                            <div style="display:flex;border-color:transparent;height: 30px;align-items: center;padding-left: 6px;">
                                <div style="width: 3px; height: 12px; background: #2e6e9e;margin-top: 1px;">
                                </div>
                                <label class="ui-outputlabel ui-widget blue_label" style="width: 80%;">
                                    #{tableItem.itemName}
                                </label>
                                <div style='text-align: right;width: 19%;padding: 0 10px;'>
                                    <h:outputText value="分值：#{tableItem.tdZwCheckItem.checkVal}分"
                                                  rendered="#{not tableItem.fjx}"/>
                                    <h:outputText style="padding-left: 15px;"
                                                  id="#{'sdf_'.concat(X.index).concat('_').concat(Y.index)}"
                                                  value="实得分：#{tableItem.tdZwCheckItem.scoreVal}分"
                                                  rendered="#{not tableItem.fjx}"/>
                                </div>
                            </div>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <c:forEach var="tableSubItem" items="#{tableItem.checkTableSubItemVOList}" varStatus="Z">
                    <p:panelGrid style="width:100%;margin-top:0;line-height: 14px;overflow-x: hidden;"
                                 styleClass="tableTr">
                        <p:row>
                            <p:column colspan="6" styleClass="white_col">
                                <div style="line-height: 24px;display: flex;justify-content: space-between;flex-wrap: wrap;">
                                    <c:forEach var="itemName" items="#{tableSubItem.itemNameList}" varStatus="N">
                                        <div style='text-indent: 2em;width: 80%;'>
                                            <h:outputText value="#{itemName}"/>
                                        </div>
                                        <div style='text-align: right;padding-right: 10px;'>
                                            <h:outputText value="分值：#{tableSubItem.tdZwCheckSub.score}分"
                                                          rendered="#{N.first and not tableSubItem.fjx}"/>
                                        </div>
                                    </c:forEach>
                                </div>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid style="width:100%;margin-top:0;line-height: 14px;overflow-x: hidden;"
                                 styleClass="zwpx-table">
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                                <h:outputLabel value="*" style="color: red;"
                                               rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                                <h:outputLabel value="结果："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left: 9px;width:360px;">
                                <p:selectOneRadio value="#{tableSubItem.tdZwCheckSub.scoreRstId}"
                                                  style="width: 90px;"
                                                  rendered="#{tableSubItem.fjx and mgrbean.tdZwCheckRpt.state ne 2}">
                                    <f:selectItems value="#{mgrbean.outcomeList}" var="outcome"
                                                   itemLabel="#{outcome.codeName}"
                                                   itemValue="#{outcome.rid}"/>
                                    <p:ajax event="change" process="@this"
                                            update="#{':tabView:editForm:czwt_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)}"/>
                                </p:selectOneRadio>
                                <h:outputLabel value="#{tableSubItem.tdZwCheckSub.fkByScoreRstId.codeName}"
                                               rendered="#{tableSubItem.fjx and mgrbean.tdZwCheckRpt.state eq 2}"/>
                                <p:inputText value="#{tableSubItem.tdZwCheckSub.scoreVal}"
                                             style="width: 180px;" maxlength="6"
                                             onblur="SYSTEM.verifyNum3(this,3,2,true);"
                                             onkeyup="SYSTEM.verifyNum3(this,3,2,false);"
                                             onkeydown="SYSTEM.verifyNum3(this,3,2,false);"
                                             rendered="#{not tableSubItem.fjx and mgrbean.tdZwCheckRpt.state ne 2}">
                                    <p:ajax event="change" process="@this"
                                            listener="#{mgrbean.calTotalRealScoreAction(tableItem,checkTableVO.tableName)}"
                                            update="#{':tabView:editForm:czwt_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)},#{'sdf_'.concat(X.index).concat('_').concat(Y.index)}"/>
                                </p:inputText>
                                <h:outputLabel value="#{tableSubItem.tdZwCheckSub.scoreVal}"
                                               rendered="#{not tableSubItem.fjx and mgrbean.tdZwCheckRpt.state eq 2}"/>
                            </p:column>
                            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                                <h:outputLabel value="*"
                                               style="color: red;#{mgrbean.tdZwCheckRpt.state ne 2 and (tableSubItem.tdZwCheckSub.scoreRstId eq mgrbean.outcomeNotRid or tableSubItem.tdZwCheckSub.scoreVal lt tableSubItem.tdZwCheckSub.score)?'':'display: none;'}"
                                               id="#{'czwt_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)}"/>
                                <h:outputLabel value="存在问题："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;">
                                <p:outputPanel style="padding-left: 0;display: flex;align-items: center;">
                                    <p:outputLabel value="#{tableSubItem.checkDeductSelVO.selName}"
                                                   rendered="#{mgrbean.tdZwCheckRpt.state eq 2}"/>
                                    <p:inputText
                                            id="#{'selDeductName_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)}"
                                            style="width: 500px;cursor: pointer;" readonly="true"
                                            value="#{tableSubItem.checkDeductSelVO.selName}"
                                            onclick="document.getElementById(#{'\'tabView:editForm:selDeductLink_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index).concat('\'')}).click();"
                                            rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                                    <p:commandLink styleClass="ui-icon ui-icon-search"
                                                   style="position: relative;left: -20px !important;" process="@this"
                                                   id="#{'selDeductLink_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)}"
                                                   action="#{mgrbean.openDeductSelectAction}"
                                                   rendered="#{mgrbean.tdZwCheckRpt.state ne 2}">
                                        <f:setPropertyActionListener
                                                value="#{'tabView:editForm:selDeductName_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)}"
                                                target="#{mgrbean.deductInputId}"/>
                                        <f:setPropertyActionListener value="#{tableSubItem.checkDeductSelVO}"
                                                                     target="#{mgrbean.checkDeductSelVO}"/>
                                    </p:commandLink>
                                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                   style="position: relative;left: -15px;"
                                                   action="#{mgrbean.emptyDeductSelectAction}"
                                                   update="#{'selDeductName_'.concat(X.index).concat('_').concat(Y.index).concat('_').concat(Z.index)}"
                                                   process="@this" rendered="#{mgrbean.tdZwCheckRpt.state ne 2}">
                                        <f:setPropertyActionListener value="#{tableSubItem.checkDeductSelVO}"
                                                                     target="#{mgrbean.checkDeductSelVO}"/>
                                    </p:commandLink>
                                </p:outputPanel>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                                <h:outputLabel value="备注："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;width:360px;" colspan="3">
                                <h:inputText value="#{tableSubItem.tdZwCheckSub.rmk}"
                                             styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all"
                                             style="width: 1060px;" maxlength="1000"
                                             rendered="#{mgrbean.tdZwCheckRpt.state ne 2}"/>
                                <p:outputLabel value="#{tableSubItem.tdZwCheckSub.rmk}"
                                               rendered="#{mgrbean.tdZwCheckRpt.state eq 2}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </c:forEach>
            </c:forEach>
        </c:forEach>
        <p:dialog header="存在问题选择" widgetVar="DeductCauseDialog"
                  resizable="false" width="650" height="480" modal="true" id="deductCauseDialog">
            <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
                <h:panelGrid columns="3"
                             style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span
                                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{mgrbean.saveDeductSelectAction}" resetValues="true"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     onclick="PF('DeductCauseDialog').hide();" process="@this"/>
                </h:panelGrid>
            </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="10" id="searchDeductCausePanel">
                            <p:outputLabel value="存在问题：" styleClass="zwx_dialog_font"/>
                            <p:inputText value="#{mgrbean.checkDeductSelVO.searchName}" style="width: 160px;"
                                         maxlength="50">
                                <p:ajax event="keyup" update="deductDataTable"
                                        process="@this,searchDeductCausePanel"
                                        listener="#{mgrbean.searchDeductSelectAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm" value="#{mgrbean.checkDeductSelVO.showList}" id="deductDataTable"
                         paginator="true" rows="#{10}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         rowsPerPageTemplate="#{10}" lazy="true" emptyMessage="暂无数据！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         paginatorPosition="bottom">
                <p:column headerText="选择" style="text-align:center;width:33px;">
                    <p:selectBooleanCheckbox value="#{itm[3]}">
                        <p:ajax event="change" process="@this"/>
                    </p:selectBooleanCheckbox>
                </p:column>
                <p:column headerText="存在问题">
                    <h:outputText value="#{itm[1]}"/>
                </p:column>
            </p:dataTable>
        </p:dialog>
    </ui:define>
</ui:composition>