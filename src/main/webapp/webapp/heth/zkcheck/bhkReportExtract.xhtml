<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportExtractBean"-->
    <ui:param name="mgrbean" value="#{bhkReportExtractBean}"/>
    <!-- 抽取页面 -->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/extractRuleEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/zkcheck/bhkReportExtractView.xhtml"/>

    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }

            function hideStatus() {
                PF('StatusDialog').hide();
            }

            function getDownloadFileClick() {
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }
        </script>
        <style type="text/css">
            .searchTime input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检报告抽取"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}"
                                 update=":tabView:mainForm:dataTable"
                                 process="@this,mainGrid"/>
                <p:commandButton value="抽取" icon="ui-icon-plus" id="extractBtn"
                                 process="@this,mainGrid" update=":tabView"
                                 action="#{mgrbean.modInitAction}" >
                </p:commandButton>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportBtn"
                                 action="#{mgrbean.exportBefore}" process="@this,:tabView:mainForm:mainGrid"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 160px;height:38px;">
                <p:outputLabel value="地区："/>
            </p:column>
            <p:column style="text-align:left;width: 260px;padding-left:10px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="0" onchange="onSearchNodeSelect()"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="unitPanel"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <p:outputLabel value="体检机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;width: 260px;">
                <!-- 弹出框 -->
                <p:outputPanel id="unitPanel">
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                            process="@this"
                                            resetValues="true" update="unitName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <p:outputLabel value="抽取类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkType}">
                    <f:selectItems value="#{mgrbean.bhkTypeList}" var="itm" itemValue="#{itm.rid}"
                                   itemLabel="#{itm.codeName}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <p:outputLabel value="抽取日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchSDate}"
                                              endDate="#{mgrbean.searchEDate}" styleClass="searchTime"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="危害因素结论："/>
            </p:column>
            <p:column style="text-align:left;" colspan="3">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
                                        selectedIds="#{mgrbean.searchSelBhkrstIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="180" height="200"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width:220px;">
            <h:outputLabel value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="体检机构名称" style="width: 200px;">
            <h:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="抽取类别" style="width: 80px;text-align: center;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="姓名" style="width: 80px;text-align: center;">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="体检编号" style="width: 120px;text-align: center;">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告出具日期" style="width: 90px;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="危害因素结论" style="width: 100px;text-align: center;">
            <h:outputLabel value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="胸片/听力检测结论" style="width: 120px;text-align: center;">
            <h:outputLabel value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="接害工龄（年）" style="width: 100px;text-align: center;">
            <h:outputLabel value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width: 200px;">
            <h:outputLabel value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="抽取日期" style="width: 80px;text-align: center;">
            <h:outputLabel value="#{itm[11]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.beforeViewInit}"
                           update=":tabView" process="@this">
                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.extractBhk}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[12] eq 0}"/>
            <p:commandLink value="删除" action="#{mgrbean.delAction}" rendered="#{itm[12] eq 0}"
                           update=":tabView:mainForm:dataTable" process="@this">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">

    </ui:define>
</ui:composition>