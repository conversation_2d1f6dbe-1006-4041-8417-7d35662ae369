<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.CheckExpertListBean"-->
    <ui:define name="insertEditScripts">

    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="报告审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="通过" icon="ui-icon-check" rendered="#{!mgrbean.ifView}"  onclick="hideTooltips();"
                                 oncomplete="datatableOffClick();"
                                 action="#{mgrbean.passCheckAction}" process="@this,:tabView:editForm">
                    <p:confirm header="消息确认框" message="确定审核通过吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="退回" icon="ui-icon-cancel" rendered="#{!mgrbean.ifView}"
                                 action="#{mgrbean.preBackAction}" process="@this"/>
                <p:commandButton value="退回原因" rendered="#{mgrbean.ifView and 3 eq mgrbean.extractBhk.state}"
                                 icon="icon-alert" style="color:red;"
                                 process="@this" oncomplete="PF('ReasonDialog').show();"
                                 update=":tabView:editForm:reasonDialog">
                    <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="true"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" oncomplete="datatableOffClick();"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <ui:include src="checkExpertBaseInfo.xhtml" />
        <!-- 退回原因 -->
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0;margin:0;padding:0;border:none;width:1px"/>
            <p:inputTextarea value="#{mgrbean.backRsn}" readonly="#{mgrbean.ifView}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="reasonContent" maxlength="200"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="确定" styleClass="submit_btn" rendered="#{!mgrbean.ifView}"
                                         oncomplete="datatableOffClick();"
                                         process="@this,reasonContent" onclick="hideTooltips();"
                                         action="#{mgrbean.returnBackAction}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

    </ui:define>
</ui:composition>
