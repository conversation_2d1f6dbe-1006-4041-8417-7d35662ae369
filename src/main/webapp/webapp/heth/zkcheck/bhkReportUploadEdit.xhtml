<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportUploadBean"-->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检报告上传"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel id="buttons">
            <p:outputPanel style="display: flex;" styleClass="zwx_toobar_42" id="sticky">
                <h:panelGrid columns="9" style="border-color:transparent;padding:0;" id="headButton">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="提交" icon="ui-icon-check"
                                     rendered="#{not mgrbean.ifView and not empty mgrbean.extractType}"
                                     process="@this,:tabView:editForm" action="#{mgrbean.preSubmitAction}">
                    </p:commandButton>
                    <p:commandButton value="退回原因" rendered="#{mgrbean.extractBhk.state eq 3}"
                                     icon="icon-alert" style="color:red;"
                                     process="@this" oncomplete="PF('ReasonDialog').show();"
                                     update=":tabView:editForm:reasonDialog"/>
                    <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                     process="@this" update=":tabView" action="#{mgrbean.backAction}">
                    </p:commandButton>
                    <p:inputText style="visibility: hidden;width: 0"/>
                </h:panelGrid>
            </p:outputPanel>
            <p:sticky target="sticky"/>
        </p:outputPanel>
        <!--提交弹出框-->
        <p:confirmDialog widgetVar="ConfirmDialog" id="confirmDialog"
                         header="消息确认框" message="提交后将无法撤销，是否提交？">
            <div style="text-align: center !important;">
                <p:commandButton value="确定" action="#{mgrbean.submitAction}"
                                 process="@this,:tabView:editForm"
                                 icon="ui-icon-check" oncomplete="PF('ConfirmDialog').hide();"
                                 update="@this,:tabView"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
            </div>
        </p:confirmDialog>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="体检信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column style="height:40px;width: 220px;text-align: right;">
                        <p:outputLabel value="体检机构地区："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;width:360px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.zoneName}"/>
                    </p:column>
                    <p:column style="width: 220px;text-align: right;">
                        <p:outputLabel value="体检机构名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.fkByOrgId.unitName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="height:40px;text-align: right;">
                        <p:outputLabel value="抽取类别："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.fkByExtractTypeId.codeName}"/>
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="姓名："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.personName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="height:40px;text-align: right;">
                        <p:outputLabel value="身份证号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.idc}"/>
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="接害工龄（年）："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.tchbadrsntim}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="height:40px;text-align: right;">
                        <p:outputLabel value="体检编号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.bhkCode}"/>
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="报告出具日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.prtDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </p:outputLabel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="height:40px;text-align: right;">
                        <p:outputLabel value="用工单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.fkByEntrustCrptId.crptName}"/>
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="职业健康检查结论："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel value="#{mgrbean.extractBhk.fkByBhkRstId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{not empty mgrbean.extractType}">
                    <p:column style="height:40px;text-align: right;">
                        <p:outputLabel value="胸片号：" styleClass="#{mgrbean.ifView?'':'cs-required'}"
                                       rendered="#{mgrbean.extractType eq 1}"/>
                        <p:outputLabel value="听力图谱编号：" styleClass="#{mgrbean.ifView?'':'cs-required'}"
                                       rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputLabel styleClass="cs-break-word" value="#{mgrbean.extractBhk.chestNo}"
                                       rendered="#{mgrbean.ifView}"/>
                        <p:inputText value="#{mgrbean.extractBhk.chestNo}" style="" maxlength="50"
                                     rendered="#{not mgrbean.ifView}"/>
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="体检报告附件：" styleClass="#{mgrbean.ifView?'':'cs-required'}"/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputPanel id="bhkRptFilePanel">
                            <p:commandButton value="上传"
                                             process="@this, bhkRptFilePanel" update="fileDialogPanel,bhkRptFilePanel"
                                             rendered="#{not mgrbean.ifView and empty mgrbean.extractBhk.bhkRptPath}"
                                             oncomplete="PF('FileDialog').show();">
                                <f:setPropertyActionListener target="#{mgrbean.uploadOptType}" value="1"/>
                            </p:commandButton>
                            <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                             rendered="#{not empty mgrbean.extractBhk.bhkRptPath and not empty mgrbean.ifShowBhkRptFileDownload and mgrbean.ifShowBhkRptFileDownload}"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                             value="#{mgrbean.extractBhk.bhkRptPath}"/>
                                <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                             value="#{mgrbean.bhkRptFileName}"/>
                                <p:fileDownload value="#{mgrbean.streamedContent}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.extractBhk.bhkRptPath}')"
                                             rendered="#{not empty mgrbean.extractBhk.bhkRptPath and not empty mgrbean.ifShowBhkRptFileDownload and not mgrbean.ifShowBhkRptFileDownload}"/>
                            <p:commandButton style="margin-left: 5px;" value="删除"
                                             rendered="#{not mgrbean.ifView and not empty mgrbean.extractBhk.bhkRptPath}"
                                             process="@this, bhkRptFilePanel" update="bhkRptFilePanel">
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                <f:setPropertyActionListener target="#{mgrbean.extractBhk.bhkRptPath}" value=""/>
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row rendered="#{not empty mgrbean.extractType}">
                    <p:column style="height:40px;text-align: right;">
                        <p:outputLabel value="胸片附件：" styleClass="#{mgrbean.ifView?'':'cs-required'}"
                                       rendered="#{mgrbean.extractType eq 1}"/>
                        <p:outputLabel value="听力图谱附件：" styleClass="#{mgrbean.ifView?'':'cs-required'}"
                                       rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;" colspan="3">
                        <p:outputPanel id="chestFilePanel">
                            <p:commandButton value="上传"
                                             process="@this, chestFilePanel" update="fileDialogPanel,chestFilePanel"
                                             rendered="#{not mgrbean.ifView and empty mgrbean.extractBhk.chestPath}"
                                             oncomplete="PF('FileDialog').show();">
                                <f:setPropertyActionListener target="#{mgrbean.uploadOptType}"
                                                             value="#{mgrbean.extractType eq 1 ? 2 : 3}"/>
                            </p:commandButton>
                            <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                             rendered="#{not empty mgrbean.extractBhk.chestPath and not empty mgrbean.ifShowChestDownload and mgrbean.ifShowChestDownload}"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                             value="#{mgrbean.extractBhk.chestPath}"/>
                                <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                             value="#{mgrbean.chestFileName}"/>
                                <p:fileDownload value="#{mgrbean.streamedContent}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.extractBhk.chestPath}')"
                                             rendered="#{not empty mgrbean.extractBhk.chestPath and not empty mgrbean.ifShowChestDownload and not mgrbean.ifShowChestDownload}"/>
                            <p:commandButton style="margin-left: 5px;" value="删除"
                                             rendered="#{not mgrbean.ifView and not empty mgrbean.extractBhk.chestPath}"
                                             process="@this, chestFilePanel" update="chestFilePanel">
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                <f:setPropertyActionListener target="#{mgrbean.extractBhk.chestPath}" value=""/>
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>

        <!--附件上传（只能上传一个）-->
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true" width="810">
            <p:outputPanel id="fileDialogPanel">
                <table width="100%">
                    <tr>
                        <td style="text-align: right;">
                            <p:outputLabel styleClass="blueColorStyle"
                                           value="（支持附件格式为：#{mgrbean.uploadOptType eq 2 ? 'RAR、ZIP、DCM' : '图片、PDF、RAR、ZIP'}）"
                                           style="position: relative;bottom: -6px;padding-right: 130px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                        </td>
                    </tr>
                    <tr>
                        <td style="position: relative;top: -23px;">
                            <p:fileUpload requiredMessage="请选择要上传的文件！" fileLimit="1"
                                          fileLimitMessage="只能选择一个文件！" id="fileUpload"
                                          label="文件选择" fileUploadListener="#{mgrbean.fileUpload}"
                                          invalidSizeMessage="文件大小不能超过100M!"
                                          validatorMessage="上传出错啦，请重新上传！"
                                          process="@this" update="@this"
                                          style="width:770px;" previewWidth="120" cancelLabel="取消"
                                          styleClass="fileUpload"
                                          uploadLabel="上传" dragDropSupport="true" mode="advanced"
                                          sizeLimit="104857600"
                                          invalidFileMessage="无效的文件类型！只能上传#{mgrbean.uploadOptType eq 2 ? 'RAR、ZIP、DCM' : '图片、PDF、RAR、ZIP'}类型文件"
                                          allowTypes="/(\.|\/)(#{mgrbean.uploadOptType eq 2 ? 'rar|zip|dcm' : 'gif|jpe?g|png|pdf|rar|zip'})$/"/>
                        </td>
                    </tr>
                </table>
            </p:outputPanel>
        </p:dialog>

        <!-- 退回原因 -->
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText style="visibility:hidden;height:0;margin:0;padding:0;border:none;width:1px"/>
            <p:inputTextarea value="#{mgrbean.extractBhk.backRsn}" readonly="true" autoResize="false"
                             style="resize:none;width:97%;height:95%;word-wrap: break-word;word-break: break-all;"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>