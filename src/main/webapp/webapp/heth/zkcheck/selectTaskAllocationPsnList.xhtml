<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>任务分配</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputScript name="js/datatable.js"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>
    <h:body>
        <h:form id="codeForm">
        	<h:outputStylesheet name="css/ui-tabs.css"/>
            <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
                <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                    <p:commandButton value="查询" icon="ui-icon-search" update="dataTable"
                                     action="#{selectTaskAllocationPsnListBean.searchAction}" process="@this,searchPanel" >
                    </p:commandButton>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{selectTaskAllocationPsnListBean.submitAction}" process="@this" >
                    </p:commandButton>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     action="#{selectTaskAllocationPsnListBean.dialogClose}" process="@this"/>
                    <p:inputText style="visibility: hidden;width: 0"/>
                </h:panelGrid>
            </p:outputPanel>
		    <h:panelGrid columns="10" id="searchPanel">
		    	<p:inputText style="visibility: hidden;width: 0"/>
                <p:outputLabel value="地区：" styleClass="zwx_dialog_font" />
                <zwx:ZoneSingleNewComp zoneList="#{selectTaskAllocationPsnListBean.zoneList}" id="searchZone"
                                       zoneCode="#{selectTaskAllocationPsnListBean.searchZoneGb}"
                                       zoneName="#{selectTaskAllocationPsnListBean.searchZoneName}"
                                       zonePaddingLeft="0" zoneWidth="160"/>
                <p:outputLabel value="单位名称：" styleClass="zwx_dialog_font" />
                <p:inputText  value="#{selectTaskAllocationPsnListBean.searchUnitName}" style="width: 120px;"  />
                <p:spacer width="5"/>
                <p:outputLabel value="姓名：" styleClass="zwx_dialog_font" style="padding-left: 17px !important;" />
                <p:inputText  value="#{selectTaskAllocationPsnListBean.searchPsnName}" style="width: 120px;" />
            </h:panelGrid>
            <p:dataTable var="itm" value="#{selectTaskAllocationPsnListBean.psnDataModel}" id="dataTable"
                         paginator="true" rows="10" emptyMessage="没有数据！" rowKey="#{itm.rid}" rowsPerPageTemplate="#{'10,20,50'}"
                         paginatorPosition="bottom" lazy="true" selection="#{selectTaskAllocationPsnListBean.selectedList}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowSelectMode="add"  pageLinks="5">
                <p:ajax event="page" process="@this,dataTable" listener="#{selectTaskAllocationPsnListBean.pageListener}"/>
                <p:ajax event="rowSelect" process="@this,dataTable" listener="#{selectTaskAllocationPsnListBean.rowSelectListener}"
                        immediate="true"/>
                <p:ajax event="rowUnselect" process="@this,dataTable" listener="#{selectTaskAllocationPsnListBean.rowUnselectListener}"
                        immediate="true"/>
                <p:ajax event="rowSelectCheckbox" process="@this,dataTable" listener="#{selectTaskAllocationPsnListBean.rowSelectListener}"
                        immediate="true"/>
                <p:ajax event="rowUnselectCheckbox" process="@this,dataTable" listener="#{selectTaskAllocationPsnListBean.rowUnselectListener}"
                        immediate="true"/>
                <p:ajax event="toggleSelect" process="@this,dataTable" listener="#{selectTaskAllocationPsnListBean.toggleSelectListener}"
                        immediate="true"/>
                <p:column style="width: 16px;text-align: center;" selectionMode="multiple"/>
                <p:column headerText="地区" style="padding-left: 3px;width:180px;">
                    <h:outputText escape="false" value="#{itm.zoneName}" />
                </p:column>
                <p:column headerText="单位名称" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{itm.unitname}" />
                </p:column>
                <p:column headerText="姓名" style="text-align: center;padding-left: 3px;width:100px;">
                    <h:outputText escape="false" value="#{itm.username}" />
                </p:column>
            </p:dataTable>
        </h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>
