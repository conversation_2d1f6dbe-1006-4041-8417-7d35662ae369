<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>检测报告名称（编号）选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>
    <ui:param name="mgrbean" value="#{SelectRptNoListBean}"/>
    <h:body>
        <h:form id="codeForm">
        	<h:outputStylesheet name="css/ui-tabs.css"/>
		    <h:panelGrid columns="10" id="searchPanel" style="height: 40px;">
		    	<p:inputText style="visibility: hidden;width: 0"/>
                <p:outputLabel value="检测报告名称（编号）：" styleClass="zwx_dialog_font" />
                <p:inputText id="pym" value="#{mgrbean.searchRptNo}" style="width: 180px;" >
                    <p:ajax event="keyup" update="dataTable" process="@this,searchPanel" listener="#{mgrbean.searchAction}"/>
                </p:inputText>
            </h:panelGrid>
            <p:dataTable var="itm" value="#{mgrbean.dataModel}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         rowsPerPageTemplate="#{'10,20,50'}" lazy="true" emptyMessage="没有数据！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         id="dataTable">
                <p:column headerText="选择" style="width:40px;text-align:center;">
                    <p:commandLink value="选择" process="@this" action="#{mgrbean.submitAction}">
                        <f:setPropertyActionListener target="#{mgrbean.selectPro}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="检测报告名称（编号）" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{itm[1]}" />
                </p:column>
                <p:column headerText="地区" style="padding-left: 3px;width:180px;">
                    <h:outputText escape="false" value="#{itm[2]}" />
                </p:column>
                <p:column headerText="单位名称" style="padding-left: 3px;width:190px;">
                    <h:outputText escape="false" value="#{itm[3]}" />
                </p:column>
                <p:column headerText="报告日期" style="text-align: center;width:84px;">
                    <h:outputText escape="false" value="#{itm[4]}" >
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
                <p:column headerText="状态" style="text-align: center;width:50px;">
                    <h:outputText escape="false" value="待核查" rendered="#{itm[5] == 1}" />
                    <h:outputText escape="false" value="已核查" rendered="#{itm[5] == 2}" />
                </p:column>
            </p:dataTable>
        </h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>
