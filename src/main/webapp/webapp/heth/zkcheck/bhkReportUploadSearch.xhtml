<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportUploadBean"-->
    <ui:param name="mgrbean" value="#{bhkReportUploadBean}"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputStylesheet library="css" name="ui-cs.css"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 修改页面 -->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/bhkReportUploadEdit.xhtml"/>
    <!-- 详情页面 -->
    <!--<ui:param name="viewPage" value="/webapp/heth/zkcheck/releaseScheduleView.xhtml"/>-->
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="体检报告上传"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
                <p:inputText style="visibility: hidden;width: 0;"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputLabel value="抽取日期："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <zwx:CalendarDynamicLimitComp styleClass="myCalendar1"
                                              startDate="#{mgrbean.searchDrawingSDate}"
                                              endDate="#{mgrbean.searchDrawingEDate}"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="姓名："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <p:inputText value="#{mgrbean.searchPersonName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="体检编号："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv">
                <p:inputText value="#{mgrbean.searchBhkNo}" style="width: 180px;" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputLabel value="抽取类别："/>
            </p:column>
            <p:column style="padding-left: 4px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox value="#{mgrbean.searchDrawingType}">
                    <f:selectItems value="#{mgrbean.drawingTypeList}" var="drawingType"
                                   itemLabel="#{drawingType.codeName}" itemValue="#{drawingType.rid}"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="padding-left: 4px !important;" styleClass="cs-scv" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.searchState}">
                    <f:selectItem itemLabel="待提交" itemValue="0"/>
                    <f:selectItem itemLabel="待审核" itemValue="1"/>
                    <f:selectItem itemLabel="审核通过" itemValue="2"/>
                    <f:selectItem itemLabel="审核退回" itemValue="3"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="抽取类别" style="text-align: center;width: 80px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="姓名" style="text-align: center;width: 80px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="体检编号" style="text-align: center;width: 150px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="报告出具日期" style="text-align: center;width: 90px;">
            <h:outputText value="#{itm[4]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="职业健康检查结论" style="text-align: center;width: 150px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="接害工龄（年）" style="text-align: center;width: 100px;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width: 250px;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="抽取日期" style="text-align: center;width: 80px;">
            <h:outputText value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="退回原因" style="width: 300px;">
            <h:outputText id="backRsn" value="#{itm[9]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="backRsn" style="max-width:300px;">
                <p:outputLabel styleClass="cs-break-word" value="#{itm[9]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="状态" style="text-align: center;width: 80px;">
            <h:outputText value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="操作" style="">
            <p:commandLink value="修改" rendered="#{itm[11] eq 0 or itm[11] eq 3}" resetValues="true"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.modInitAction}" onclick="hideTooltips();">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[11] ne 0 and itm[11] ne 3}"
                           action="#{mgrbean.modInitAction}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>