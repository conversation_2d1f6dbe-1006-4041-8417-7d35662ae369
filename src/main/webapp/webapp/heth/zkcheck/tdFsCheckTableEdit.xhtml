<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 编辑页面的按钮 -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.TdFsCheckTableListBean"-->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function rowSelect(index) {
                $("#tabView\\:editForm\\:standTable").find("tr").eq(index).find("td:eq(0)").click();
            };
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质控考核表维护"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="buttons">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;" id="buttonGrid">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" onclick="hideTooltips();"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm,:tabView:editForm:searchForm"
                                 update="editForm"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}"
                                 update=":tabView" immediate="true"/>
            </h:panelGrid>
            <p:sticky target="buttons"/>
        </p:outputPanel>
    </ui:define>
    <!-- 页面的内容-->
    <ui:define name="insertOtherContents">
        <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="searchForm">
            <p:row>
                <p:column styleClass="cs-scl-first cs-required">
                    <p:outputLabel value="机构类型："/>
                </p:column>
                <p:column styleClass="cs-scv-w">
                    <p:selectOneMenu value="#{mgrbean.checkTypeRid}" style="width: 190px;">
                        <f:selectItems value="#{mgrbean.checkTypeList}" var="item"
                                       itemLabel="#{item.codeName}"
                                       itemValue="#{item.rid}"/>
                    </p:selectOneMenu>
                </p:column>
                <p:column styleClass="cs-scl-w cs-required">
                    <p:outputLabel value="年份："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <p:selectOneMenu value="#{mgrbean.year}" style="width: 100px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{mgrbean.yearList}" var="item"
                                       itemLabel="#{item}" itemValue="#{item}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:outputPanel id="allPanel" style="width: 100%;height: 100%;display: flex;">
            <p:panelGrid id="standGrid" style="border: hidden;width: 40%;height: 100%;">
                <p:row>
                    <p:column>
                        <p:commandButton value="添加" icon="ui-icon-plus" onclick="hideTooltips();"
                                         update="standTable,:tabView:editForm:addStandGrid"
                                         action="#{mgrbean.addStandAction}" process="@this,:tabView:editForm"
                                         resetValues="true">
                        </p:commandButton>

                        <p:dataTable id="standTable" var="stand" value="#{mgrbean.standList}" lazy="true"
                                     emptyMessage="没有您要找的记录！"
                                     rowIndexVar="R" rowKey="#{stand.rid}" style="margin-top: 5px;"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="10,20,50" pageLinks="5"
                                     paginator="true" rows="10" paginatorPosition="bottom"
                                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                     selectionMode="single" selection="#{mgrbean.selStand}">
                            <p:ajax event="rowSelect"
                                    process=":tabView:editForm:standTable,:tabView:editForm:scoresTable"
                                    listener="#{mgrbean.onRowSelect}"
                                    update="standTable,:tabView:editForm:scoresGrid" resetValues="true">
                                <p:resetInput target=":tabView:editForm:scoresGrid,:tabView:editForm:standTable"/>
                            </p:ajax>
                            <p:column headerText="序号" style="text-align: center;width:40px;">
                                <h:outputText value="#{stand.xh}"/>
                            </p:column>
                            <p:column headerText="评估表名称" style="width:300px;">
                                <h:outputText value="#{stand.checkName}"/>
                            </p:column>
                            <p:column headerText="特殊标记" style="text-align: center;width:200px;">
                                <h:outputText value="#{stand.specialFlagName}"/>
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink action="#{mgrbean.modStandAction}" value="修改" resetValues="true" onclick="hideTooltips();"
                                               process="@this,:tabView:editForm:searchForm" update=":tabView:editForm:addStandDialog">
                                    <f:setPropertyActionListener target="#{mgrbean.addStand}" value="#{stand}"/>
                                </p:commandLink>
                                <p:spacer width="5"/>
                                <p:commandLink value="删除" action="#{mgrbean.delStandAction}"
                                               onclick="zwx_loading_start();hideTooltips();"
                                               process="@this" oncomplete="removeExtraZonePanel('#tabView\\:mainForm\\:dialogBoxId');zwx_loading_stop();"
                                               update=":tabView:editForm:standTable,:tabView:editForm:scoresGrid,:tabView:mainForm:dataView">
                                    <f:setPropertyActionListener target="#{mgrbean.addStand}" value="#{stand}"/>
                                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:spacer width="2%"/>
            <p:panelGrid id="scoresGrid" style="border: hidden;width: 58%;height: 100%;">
                <p:row>
                    <p:column>
                        <h:outputLabel value="评估表名称：" rendered="#{mgrbean.selStand ne null}"/>
                        <h:outputLabel value="#{mgrbean.selStand.checkName}" style="display: inline-flex;max-width: 300px;" rendered="#{mgrbean.selStand ne null}"/>
                        <p:commandButton value="添加" icon="ui-icon-plus" style="#{mgrbean.selStand ne null?'margin-left: 20px;':''}"  onclick="hideTooltips();"
                                         action="#{mgrbean.addScoresAction}" process="@this" resetValues="true">
                            <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" resetValues="true"
                                    process="@this" update=":tabView:editForm:scoresTable"/>
                        </p:commandButton>
                        <p:spacer width="5"/>
                        <p:commandButton value="暂存" icon="ui-icon-disk" id="saveScores" onclick="hideTooltips();"
                                         action="#{mgrbean.saveScoresAction}" process="@this,:tabView:editForm:scoresGrid"
                                         update=":tabView:editForm:scoresGrid"/>
                        <p:dataTable var="score" value="#{mgrbean.scoresList}" id="scoresTable"
                                     emptyMessage="没有您要找的记录！" paginatorPosition="top" style="margin-top: 5px;">
                            <p:columnGroup type="header">
                                <p:row>
                                    <p:column headerText="排序" style="width:30px;text-align: center"/>
                                    <p:column headerText="考核内容序号" style="width:50px;text-align: center;"/>
                                    <p:column headerText="考核指标大类" style="width:100px;text-align: center;"/>
                                    <p:column headerText="考核指标" style="width:100px;text-align: center;"/>
                                    <p:column headerText="分值" style="width:60px;text-align: center;"/>
                                    <p:column headerText="项类" style="width:80px;text-align: center;"/>
                                    <p:column headerText="特殊标记" style="width:100px;text-align: center;"/>
                                    <p:column headerText="特殊标记说明" style="width:100px;text-align: center;"/>
                                    <p:column headerText="存在问题" style="width:140px;text-align: center;"/>
                                    <p:column headerText="操作"/>
                                </p:row>
                            </p:columnGroup>
                            <p:column style="line-height: 30px;text-align: center;">
                                <p:inputText value="#{score.xh}" maxlength="3" style="width:30px;text-align: center;"
                                             onkeyup="SYSTEM.clearNoNum(this)"/>
                            </p:column>
                            <p:column style="line-height: 30px;text-align: center;">
                                <p:inputText value="#{score.indexXh}" maxlength="10"
                                             style="width:30px;text-align: center;"/>
                            </p:column>
                            <p:column style="line-height: 30px;">
                                <h:outputText id="firstIndexName" value="#{score.firstIndexName}"
                                              styleClass="zwx-tooltip" />
                                <p:tooltip for="firstIndexName" style="max-width:400px;" >
                                    <p:outputLabel styleClass="cs-break-word"
                                                   value="#{score.firstIndexName}"
                                                   escape="false"/>
                                </p:tooltip>
                            </p:column>
                            <p:column style="line-height: 30px;">
                                <h:outputText id="indexName" value="#{score.fkByIndexId.codeName}"
                                              styleClass="zwx-tooltip" />
                                <p:tooltip for="indexName" style="max-width:400px;" >
                                    <p:outputLabel styleClass="cs-break-word"
                                                   value="#{score.fkByIndexId.codeName}"
                                                   escape="false"/>
                                </p:tooltip>
                            </p:column>
                            <p:column style="line-height: 24px;text-align: center;">
                                <p:inputText value="#{score.score}" style="width:50px;text-align: center;"
                                             onkeydown="SYSTEM.verifyNum3(this, 3, 2, false)"
                                             onkeyup="SYSTEM.verifyNum3(this, 3, 2, false)"
                                             onblur="SYSTEM.verifyNum3(this, 3, 2, true)"/>
                            </p:column>
                            <p:column style="line-height: 24px;text-align: center;">
                                <p:selectOneMenu value="#{score.itemTypeRid}" style="width: 80px;">
                                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                    <f:selectItems value="#{mgrbean.itemTypeList}" var="item"
                                                   itemLabel="#{item.codeName}"
                                                   itemValue="#{item.rid}"/>
                                </p:selectOneMenu>
                            </p:column>
                            <p:column style="line-height: 24px;text-align: center;">
                                <p:selectOneMenu value="#{score.specialFlagRid}" style="width: 90px;">
                                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                    <f:selectItems value="#{mgrbean.specialFlagWithScoreList}" var="item"
                                                   itemLabel="#{item.codeName}"
                                                   itemValue="#{item.rid}"/>
                                </p:selectOneMenu>
                            </p:column>
                            <p:column style="line-height: 24px;text-align: center;">
                                <p:selectOneMenu value="#{score.busExtendsRid}" style="width: 90px;">
                                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                    <f:selectItems value="#{mgrbean.busExtendsList}" var="item"
                                                   itemLabel="#{item.codeName}"
                                                   itemValue="#{item.rid}"/>
                                </p:selectOneMenu>
                            </p:column>
                            <p:column style="line-height: 24px;">
                                <h:outputText id="deductRsn" value="#{score.deductStr}" styleClass="zwx-tooltip"/>
                                <p:tooltip for="deductRsn" style="max-width:400px;">
                                    <p:outputLabel value="#{score.deductStr2}" escape="false"/>
                                </p:tooltip>
                            </p:column>
                            <p:column style="line-height: 24px;">
                                <p:commandLink value="添加存在问题" action="#{mgrbean.addDeductLinkAction}" resetValues="true"
                                               onclick="hideTooltips();"
                                               process="@this,scoresTable" update=":tabView:editForm:deductTable">
                                    <f:setPropertyActionListener target="#{mgrbean.score}" value="#{score}"/>
                                </p:commandLink>
                                <p:spacer width="5"/>
                                <p:commandLink value="删除" action="#{mgrbean.delScoreAction()}"
                                               onclick="zwx_loading_start();hideTooltips();"
                                               oncomplete="removeExtraZonePanel('#tabView\\:mainForm\\:dialogBoxId');zwx_loading_stop();"
                                               process="@this,scoresTable" update="scoresTable">
                                    <f:setPropertyActionListener target="#{mgrbean.score}" value="#{score}"/>
                                    <p:confirm header="消息确认框" message="确定要删除吗？"
                                               icon="ui-icon-alert"/>
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:outputPanel>

        <p:dialog id="addStandDialog" header="添加评估表" widgetVar="AddStandDialog" resizable="false" width="500"
                  height="150" modal="true">
            <p:panelGrid style="width:100%;" id="addStandGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="序号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:inputText value="#{mgrbean.addStand.xh}" maxlength="3" style="width:50px;"
                                     onkeyup="SYSTEM.clearNoNum(this)"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="*" style="color:red;"/>
                        <h:outputText value="评估表名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:inputText value="#{mgrbean.addStand.checkName}" maxlength="200" style="width:180px;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="特殊标记："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu value="#{mgrbean.addStand.selSpFlagId}" style="width: 190px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.standSpFlagList}" var="item"
                                           itemLabel="#{item.codeName}"
                                           itemValue="#{item.rid}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check"
                                         process="@this,addStandGrid" resetValues="true"
                                         action="#{mgrbean.saveStandAction}"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('AddStandDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <p:dialog id="addDeductDialog" header="添加存在问题" widgetVar="AddDeductDialog"
                  resizable="false" width="800" height="350" modal="true">
            <p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 5px;" resetValues="true"
                             action="#{mgrbean.addDeductBtnAction}" process="@this" onclick="hideTooltips();">
                <p:ajax event="dialogReturn" listener="#{mgrbean.onselDeductAction}" resetValues="true"
                        process="@this" update=":tabView:editForm:scoresTable"/>
            </p:commandButton>
            <p:dataTable var="dedu" value="#{mgrbean.scoreDeductList}" id="deductTable"
                         emptyMessage="没有您要找的记录！">
                <p:column styleClass="cs-break-word" style="width:40px;text-align: center;" headerText="序号">
                    <p:inputText value="#{dedu.xh}" maxlength="3" style="width:30px;text-align: center;"
                                 onkeyup="SYSTEM.clearNoNum(this)"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 500px;" headerText="存在问题">
                    <h:outputText value="#{dedu.fkByDeductId.codeName}"/>
                </p:column>
                <p:column styleClass="cs-break-word" headerText="操作">
                    <p:commandLink value="删除" action="#{mgrbean.delDeductAction()}"  onclick="zwx_loading_start()"
                                   oncomplete="removeExtraZonePanel('#tabView\\:mainForm\\:dialogBoxId');zwx_loading_stop();"
                                   process="@this,deductTable">
                        <f:setPropertyActionListener target="#{mgrbean.deductRid}" value="#{dedu.rid}"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？"
                                   icon="ui-icon-alert"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" icon="ui-icon-check" onclick="hideTooltips();"
                                         process="@this,deductTable" action="#{mgrbean.saveDeductAction}"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('AddDeductDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>