<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.QcSkillCheckInputListBean"-->
    <ui:param name="mgrbean" value="#{qcSkillCheckInputListBean}"/>
    <ui:param name="editPage" value= "/webapp/heth/zkcheck/qcSkillCheckInputEdit.xhtml" />
    <ui:param name="viewPage" value= "/webapp/heth/zkcheck/qcSkillCheckInputView.xhtml" />
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <h:outputStylesheet name="css/annexViewDialog.css"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            function windowScrollTop(){
                window.scrollTo(0,0);
            }
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制技能考核录入"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInitAction}"
                                 process="@this"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneGb}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="机构名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchOrgName}" style="width: 180px;"
                             maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="考核日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchCheckBeginDate}"
                                              endDate="#{mgrbean.searchCheckEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px; ">
                <h:outputText value="考核结果："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;">
                <p:selectManyCheckbox value="#{mgrbean.searchCheckRstList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="1" itemLabel="合格"/>
                    <f:selectItem itemValue="0" itemLabel="不合格"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:36px; ">
                <h:outputText value="被考核机构确认意见："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;">
                <p:selectManyCheckbox value="#{mgrbean.searchIfConfirmList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="1" itemLabel="确认"/>
                    <f:selectItem itemValue="0" itemLabel="不确认"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectManyCheckbox value="#{mgrbean.searchStateList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="0" itemLabel="待提交"/>
                    <f:selectItem itemValue="1" itemLabel="已提交"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" style="width: 100px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column headerText="机构名称" style="width: 150px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="考核类型" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="考核结果" style="width: 50px;text-align: center;">
            <h:outputText value="不合格" rendered="#{0 eq itm[4]}"/>
            <h:outputText value="合格" rendered="#{1 eq itm[4]}"/>
        </p:column>
        <p:column headerText="被考核机构确认意见" style="width: 70px;text-align: center;">
            <h:outputText value="不确认" rendered="#{0 eq itm[5]}"/>
            <h:outputText value="确认" rendered="#{1 eq itm[5]}"/>
        </p:column>
        <p:column headerText="考核日期" style="width: 50px;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width: 30px;text-align: center;">
            <h:outputText value="待提交" rendered="#{0 eq itm[7]}"/>
            <h:outputText value="已提交" rendered="#{1 eq itm[7]}"/>
        </p:column>
        <p:column headerText="操作" style="width: 300px;padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}"
                           process="@this" update=":tabView" rendered="#{1 eq itm[7]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" rendered="#{1 ne itm[7]}"
                           process="@this" update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{1 ne itm[7]}"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}"
                           process="@this" update=":tabView" rendered="#{1 ne itm[7]}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>
