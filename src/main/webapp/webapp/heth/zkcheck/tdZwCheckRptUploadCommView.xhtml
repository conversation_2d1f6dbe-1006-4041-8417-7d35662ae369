<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:op="http://java.sun.com/jsf/html">
    <p:fieldset legend="用人单位信息">
        <p:panelGrid  style="margin-bottom: 10px;width: 100%;" id="crptInfoPanel">
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;">
                    <p:outputLabel value="单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.crptName}"/>
                </p:column>
                <p:column style="text-align:right;width: 15%;">
                    <p:outputLabel value="所属区域："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <h:outputText escape="false"
                                  value="#{mgrbean.tdZwCheckRpt.fkByZoneId.zoneType>3?mgrbean.tdZwCheckRpt.fkByZoneId.fullName.substring(mgrbean.tdZwCheckRpt.fkByZoneId.fullName.indexOf('_')+1,mgrbean.tdZwCheckRpt.fkByZoneId.fullName.length()):mgrbean.tdZwCheckRpt.fkByZoneId.zoneName}"/>

                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="社会信用代码："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.creditCode}"/>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="单位地址："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.address}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="行业类别："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByIndusTypeId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="经济性质："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByEconomyId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;" >
                    <p:outputLabel value="企业规模："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByCrptSizeId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="联系人："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkMan}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="联系电话："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkPhone}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="检查报告上传" style="margin-top: 10px;">
        <p:panelGrid  style="margin-bottom: 10px;width: 100%;" id="rptPanel">
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;" >
                    <p:outputLabel value="检测报告编号："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                    <h:outputText value="#{mgrbean.tdZwCheckRpt.rptNo}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;" >
                    <p:outputLabel value="检测报告名称："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                    <h:outputText value="#{mgrbean.tdZwCheckRpt.rptName}" />
                </p:column>
                <p:column style="text-align:right;width: 15%;">
                    <p:outputLabel value="报告日期："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <h:outputText value="#{mgrbean.tdZwCheckRpt.rptDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;">
                    <p:outputLabel value="工作场所名称："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.workName}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;" >
                    <p:outputLabel value="检测报告附件："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.tdZwCheckRpt.filePath}')"
                                     rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                </p:column>
                <p:column style="text-align:right;width: 15%;">
                    <p:outputLabel value="文审材料附件："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                     rendered="#{mgrbean.tdZwCheckRpt.sourceFilePath != null}"
                                     onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{mgrbean.tdZwCheckRpt.sourceFilePath}"/>
                        <f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{mgrbean.dowloadFileName}"/>
                        <p:fileDownload value="#{downLoadPreBean.streamedContent}" />
                    </p:commandButton>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
              height="300" header="退回原因" resizable="false" modal="true">
        <h:inputText
                style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
        <p:inputTextarea value="#{mgrbean.backRsn}"
                         style="resize:none;width:97%;height:95%;" autoResize="false"
                         id="reasonContent" maxlength="100" readonly="#{mgrbean.readOnly}"/>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: right;">
                <h:panelGroup>
                    <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                     process="@this" immediate="true" />
                    <p:spacer width="5" rendered="#{!mgrbean.readOnly}" />
                    <p:commandButton value="确定" styleClass="submit_btn"
                                     process="@this,reasonContent" oncomplete="datatableOffClick()"
                                     action="#{mgrbean.returnAction}"
                                     rendered="#{!mgrbean.readOnly}" />
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:composition>
