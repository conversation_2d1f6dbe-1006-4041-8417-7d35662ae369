<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.TdZwCheckRptUploadAudioListBean"-->
    <ui:param name="mgrbean" value="#{tdZwCheckRptUploadAudioListBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/tdZwCheckRptUploadAudioEdit.xhtml"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            //<![CDATA[
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            //]]>
        </script>
        <style type="text/css">
            .myCalendar input{
                width: 78px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业卫生技术服务机构检测报告审核"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="8" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" action="#{mgrbean.openReviewConfirmDialog}"
                                 oncomplete="datatableOffClick()" process="@this,dataTable" resetValues="true" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:200px;height: 38px;">
                <h:outputText value="地区：" />
            </p:column>
            <p:column style="text-align:left;width:250px;">
                <zwx:ZoneSingleNewComp id="searchZone" zoneList="#{mgrbean.zoneList}" zonePaddingLeft="7"
                                       zoneCode="#{mgrbean.searchZoneCode}"
                                       zoneName="#{mgrbean.searchZoneName}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="技术服务机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:250px;">
                <p:inputText value="#{mgrbean.searchOrgName}" style="width: 180px;"  />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="检测报告编号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchRptNo}" style="width: 180px;"  />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="报告日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp styleClass="myCalendar"  startDate="#{mgrbean.searchReportBeginDate}"
                                              endDate="#{mgrbean.searchReportEndDate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:3%;text-align:center;padding: 4px 10px !important;"
                  disabledSelection="#{itm[7]==1?false:true}"/>
        <p:column headerText="地区" style="width:15%;padding-left: 5px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="技术服务机构" style="width:15%;padding-left: 5px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width:15%;padding-left: 5px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="检测报告名称" style="width: 10%;padding-left: 5px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="检测报告编号" style="width: 10%;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:8%;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width:5%;text-align: center;">
            <h:outputText value="待审核" rendered="#{1 eq itm[7]}"/>
            <h:outputText value="已退回" rendered="#{3 eq itm[7]}"/>
            <h:outputText value="审核通过" rendered="#{2 eq itm[7]}"/>
        </p:column>
        <p:column headerText="退回原因" style="width:15%;padding-left: 5px;">
            <h:outputLabel id="backRsn" rendered="#{itm[7]==3}" styleClass="zwx-tooltip"
                           value="#{itm[8]}" />
            <p:tooltip for="backRsn" value="#{itm[8]}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{itm[7]==3}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 5px;">
            <!-- 已退回 审核通过 -->
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" onclick="hideTooltips();"
                           process="@this" update=":tabView" rendered="#{2 eq itm[7] or 3 eq itm[7]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <!-- 待审核 -->
            <p:commandLink value="审核" action="#{mgrbean.modInitAction}" rendered="#{1 eq itm[7]}" onclick="hideTooltips();"
                           process="@this" update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <p:confirmDialog id="reviewConfirmDialog" message="确定要批量审核通过吗？" header="消息确认框" widgetVar="ReviewConfirmDialog" >
            <h:panelGrid style="width: 100%;text-align: right;">
                <h:panelGroup>
                    <p:commandButton value="确定" action="#{mgrbean.reviewBatchAction}"
                                     update="dataTable" icon="ui-icon-check"
                                     oncomplete="PF('ReviewConfirmDialog').hide();datatableOffClick();"/>
                    <p:spacer width="5"  />
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     onclick="PF('ReviewConfirmDialog').hide();datatableOffClick()"
                                     type="button"/>
                </h:panelGroup>
            </h:panelGrid>
        </p:confirmDialog>
    </ui:define>
</ui:composition>