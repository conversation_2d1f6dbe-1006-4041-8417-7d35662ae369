<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.TdZwZkCheckJudgeBean"-->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function showShade() {
                PF('ShadeTip').show();
            }

            function hideShade() {
                //这个方法不会执行 因下载完后 PrimeFaces会刷新页面
                PF('ShadeTip').hide();
            }

            function generateClick() {
                document.getElementById("tabView:editForm:generateReportId").click();
            }
        </script>
        <style type="text/css">
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip > div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }
            .no-close-button .ui-dialog-titlebar-close {
                display: none !important;
            }
            .noBorder{
                border-color: transparent !important;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="现场考核管理"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:confirmDialog message="提交后将无法撤销，是否提交？" header="消息确认框" widgetVar="ConfirmSubmitDialog">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                                 oncomplete="PF('ConfirmSubmitDialog').hide();" update=":tabView" resetValues="true"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmSubmitDialog').hide();"
                                 type="button"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:fieldset legend="现场考核" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid id="checkInfo" style="margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="考核类型："/>
                    </p:column>
                    <p:column styleClass="cs-content">
                        <p:outputLabel value="#{mgrbean.checkMain.fkByCheckTypeId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="被考核机构："/>
                    </p:column>
                    <p:column styleClass="cs-con" style="padding-left:3px;">
                        <p:outputLabel value="#{mgrbean.checkMain.fkByOrgId.unitname}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="考核日期："/>
                    </p:column>
                    <p:column styleClass="cs-finally">
                        <p:outputLabel value="#{mgrbean.checkMain.checkDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </p:outputLabel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="本年度是否开展工作："/>
                    </p:column>
                    <p:column styleClass="cs-content">
                        <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifNeedImprove eq 0}"/>
                        <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifNeedImprove eq 1}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="总得分："/>
                    </p:column>
                    <p:column styleClass="cs-con">
                        <p:outputLabel value="#{mgrbean.checkMain.totalScoreVal}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="修改总得分：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-finally">
                        <p:selectOneRadio value="#{mgrbean.checkJudge.ifUpdateTotalScore}">
                            <f:selectItem itemLabel="否" itemValue="0"/>
                            <f:selectItem itemLabel="是" itemValue="1"/>
                            <p:ajax event="change" process="@this,:tabView:editForm:checkInfo"
                                    update=":tabView:editForm:checkInfo"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.checkJudge.ifUpdateTotalScore eq 1}">
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="修改后总得分：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-content">
                        <p:inputText style="width: 180px;" value="#{mgrbean.checkJudge.revisedTotalScore}"
                                     onkeydown="SYSTEM.verifyNumRange(this, 3, 1, false, 1, 100)"
                                     onkeyup="SYSTEM.verifyNumRange(this, 3, 1, false, 1, 100)"
                                     onblur="SYSTEM.verifyNumRange(this, 3, 1, true, 1, 100)"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="修改原因说明：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-finally" colspan="3">
                        <p:inputText style="width: 500px;" value="#{mgrbean.checkJudge.modReasons}"
                                     maxlength="200"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="实验室比对" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid id="labComparison" style="margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="考核计划名称："/>
                    </p:column>
                    <p:column styleClass="cs-content">
                        <p:outputLabel value="#{mgrbean.labComp.planName}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="发布日期："/>
                    </p:column>
                    <p:column styleClass="cs-con" style="padding-left:3px;">
                        <p:outputLabel value="#{mgrbean.labComp.planDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </p:outputLabel>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="状态："/>
                    </p:column>
                    <p:column styleClass="cs-finally">
                        <p:outputLabel value="待评判" rendered="#{mgrbean.labComp.state eq 0}"/>
                        <p:outputLabel value="已评判" rendered="#{mgrbean.labComp.state eq 1}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="生化检测："/>
                    </p:column>
                    <p:column styleClass="cs-content">
                        <p:outputLabel value="#{mgrbean.labComp.biochemicalTest}"
                                       rendered="#{mgrbean.labComp.biochemicalTest ne null and mgrbean.labComp.planDate ne null}"/>
                        <p:outputLabel value="未参与" rendered="#{mgrbean.labComp.biochemicalTest eq null and mgrbean.labComp.planDate ne null}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="尿常规检测："/>
                    </p:column>
                    <p:column styleClass="cs-con" style="padding-left:3px;">
                        <p:outputLabel value="#{mgrbean.labComp.routineTest}"
                                       rendered="#{mgrbean.labComp.routineTest ne null and mgrbean.labComp.planDate ne null}"/>
                        <p:outputLabel value="未参与" rendered="#{mgrbean.labComp.routineTest eq null and mgrbean.labComp.planDate ne null}"/>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="血铅："/>
                    </p:column>
                    <p:column styleClass="cs-finally">
                        <p:outputLabel value="#{mgrbean.labComp.bloodLead}"
                                       rendered="#{mgrbean.labComp.bloodLead ne null and mgrbean.labComp.planDate ne null}"/>
                        <p:outputLabel value="未参与" rendered="#{mgrbean.labComp.bloodLead eq null and mgrbean.labComp.planDate ne null}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="尿氟："/>
                    </p:column>
                    <p:column styleClass="cs-finally" colspan="5">
                        <p:outputLabel value="#{mgrbean.labComp.urinaryFluoride}"
                                       rendered="#{mgrbean.labComp.urinaryFluoride ne null and mgrbean.labComp.planDate ne null}"/>
                        <p:outputLabel value="未参与" rendered="#{mgrbean.labComp.urinaryFluoride eq null and mgrbean.labComp.planDate ne null}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="评判结果" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
        <p:panelGrid id="evaResult" style="margin-bottom: 10px;width:100%">
            <p:row rendered="#{mgrbean.checkMain.fkByCheckTypeId ne null and (mgrbean.checkMain.fkByCheckTypeId.extendS1 eq '1' or mgrbean.checkMain.fkByCheckTypeId.extendS1 eq '2')}">
                <p:column styleClass="cs-first">
                    <p:outputLabel value="总分："/>
                </p:column>
                <p:column styleClass="cs-content">
                    <p:outputLabel value="#{mgrbean.checkJudge.totalScore}"/>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="质控结果："/>
                </p:column>
                <p:column styleClass="cs-finally" colspan="3">
                    <p:outputLabel value="不合格" rendered="#{mgrbean.checkJudge.checkRst eq 0}"/>
                    <p:outputLabel value="合格" rendered="#{mgrbean.checkJudge.checkRst eq 1}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.checkMain.fkByCheckTypeId ne null and (mgrbean.checkMain.fkByCheckTypeId.extendS1 eq '1' or mgrbean.checkMain.fkByCheckTypeId.extendS1 eq '2')}">
                <p:column styleClass="cs-first">
                    <p:outputLabel value="关键不符合项：" styleClass="cs-required"/>
                </p:column>
                <p:column styleClass="cs-finally" colspan="5" style="padding-left:3px;">
                    <c:forEach items="#{mgrbean.keyList}" var="keyCode">
                        <table>
                            <tr>
                                <td class="noBorder" style="position: relative;top: 1px;">
                                    <p:selectBooleanCheckbox value="#{keyCode.ifSelected}"
                                                             disabled="#{keyCode.ifDisabled}"
                                                             style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}">
                                        <p:ajax event="change"
                                                listener="#{mgrbean.selectSymCodeAction(keyCode)}"
                                                process="@this,:tabView:editForm:evaResult"
                                                update=":tabView:editForm:evaResult"/>
                                    </p:selectBooleanCheckbox>
                                </td>
                                <td class="noBorder" style="position: relative;top: 1px;padding-right: 30px;">
                                    <h:outputText value="#{keyCode.codeName}"/>
                                </td>
                            </tr>
                        </table>
                    </c:forEach>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.checkMain.fkByCheckTypeId ne null and !(mgrbean.checkMain.fkByCheckTypeId.extendS1 eq '1' or mgrbean.checkMain.fkByCheckTypeId.extendS1 eq '2')}">
                <p:column styleClass="cs-first">
                    <p:outputLabel value="质控结果："/>
                </p:column>
                <p:column styleClass="cs-finally" colspan="5">
                    <p:selectOneRadio value="#{mgrbean.checkJudge.checkRst}">
                        <f:selectItem itemLabel="不合格" itemValue="0"/>
                        <f:selectItem itemLabel="合格" itemValue="1"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
        </p:panelGrid>
        </p:fieldset>
    </ui:define>
</ui:composition>