<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function showShade() {
                PF('ShadeTip').show();
            }

            function hideShade() {
                //这个方法不会执行 因下载完后 PrimeFaces会刷新页面
                PF('ShadeTip').hide();
            }

            function generateClick() {
                document.getElementById("tabView:editForm:generateReportId").click();
            }
        </script>
        <style type="text/css">
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip > div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }
            .no-close-button .ui-dialog-titlebar-close {
                display: none !important;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="现场考核管理"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:confirmDialog message="提交后将无法撤销，是否提交？" header="消息确认框" widgetVar="ConfirmSubmitDialog">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                                 oncomplete="PF('ConfirmSubmitDialog').hide();" update=":tabView" resetValues="true"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmSubmitDialog').hide();"
                                 type="button"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:fieldset legend="用人单位信息">
            <p:panelGrid id="baseInfo" style="margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="考核类型：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-content">
                        <p:outputLabel value="否" />
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="被考核机构：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-con" style="padding-left:3px;">
                        <h:panelGrid columns="2"
                                     style="border-color: transparent;margin: 0px;padding: 0px;#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}">
                            <p:inputText id="unitName" readonly="true" style="width:240px;"
                                         value="#{mgrbean.checkMain.fkByOrgId.unitname}"
                                         onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
                            <p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search"
                                           partialSubmit="true"
                                           action="#{mgrbean.selectOrgList}" process="@this,:tabView:editForm"
                                           style="position: relative;left: -30px;"
                            >
                                <p:ajax event="dialogReturn" process="@this" resetValues="true"
                                        listener="#{mgrbean.onOrgSelect}" update="unitName"/>
                            </p:commandLink>
                        </h:panelGrid>
                    </p:column>
                    <p:column styleClass="cs-title">
                        <p:outputLabel value="*" style="color:red;"></p:outputLabel>
                        <p:outputLabel value="考核日期："/>
                    </p:column>
                    <p:column styleClass="cs-finally">
                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                    style="#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}"
                                    showOtherMonths="true" size="11" navigator="true"
                                    yearRange="c-10:c" converterMessage="考核日期，格式输入不正确！"
                                    showButtonPanel="true" maxdate="new Date()"
                                    value="#{mgrbean.checkMain.checkDate}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="本年度是否开展工作："/>
                    </p:column>
                    <p:column styleClass="cs-content" colspan="3">
                        <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifNeedImprove eq 0}"></p:outputLabel>
                        <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifNeedImprove eq 1}"></p:outputLabel>
                    </p:column>
                    <p:column styleClass="cs-first">
                        <p:outputLabel value="总得分：" styleClass="cs-required"/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;" colspan="5">
                        <p:selectOneRadio value="#{mgrbean.checkMain.ifDevelop}" id="develop"
                                          style="#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}">
                            <f:selectItem itemLabel="是" itemValue="1"/>
                            <f:selectItem itemLabel="否" itemValue="0"/>
                            <p:ajax event="change" oncomplete="PF('ConfirmDevelopDialog').show();" process="@this"
                                    resetValues="true"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
    </ui:define>
</ui:composition>