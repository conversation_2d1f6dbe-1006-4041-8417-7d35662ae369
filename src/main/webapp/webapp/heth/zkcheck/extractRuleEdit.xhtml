<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportExtractBean"-->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
            function startExtract() {
                console.log("点击抽取了");
                $("#tabView\\:editForm\\:extractButton").attr("disabled", true);
                console.log("禁用抽取了");
            }

            function finishExtract() {
                $("#tabView\\:editForm\\:extractButton").removeAttr("disabled");
                console.log("解除禁用抽取");
            }
            /**禁止使用回车事件*/
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    event.stopPropagation();
                }
            });
            //]]>
        </script>
        <style type="text/css">

        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检报告抽取" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="抽取" icon="ui-icon-disk" onclick="startExtract()" id="extractButton"
                                 action="#{mgrbean.exeExtract}"
                                 process="@this,:tabView:editForm"
                                 update=":tabView" onstart="zwx_loading_start();" oncomplete="finishExtract();zwx_loading_stop();" />
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" update=":tabView"  onclick="hideTooltips();"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
	<ui:define name="insertOtherContents">
        <p:fieldset legend="抽取规则" style="margin-top: 5px;margin-bottom: 5px;" toggleable="true" toggleSpeed="500">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" >
                <p:row>
                    <p:column styleClass="cs-scl-first" >
                        <h:outputText  value="地区："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="padding-left: 5px !important;" >
                        <zwx:ZoneSingleNewComp  zoneList="#{mgrbean.zoneList}" id="extractZone"
                                               zoneCode="#{mgrbean.extractZoneGb}"
                                               zoneName="#{mgrbean.extractZoneName}" onchange="onSearchextractSelect()"/>
                        <p:remoteCommand name="onSearchextractSelect" action="#{mgrbean.clearExtractUnit}"
                                         process="@this,extractZone" update="extractUnitPanel"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" >
                        <h:outputText styleClass="cs-required" value="体检机构："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:outputPanel id="extractUnitPanel">
                            <table>
                                <tr>
                                    <td style="padding: 0;border-color: transparent;">
                                        <p:inputText id="extractUnitName"
                                                     value="#{mgrbean.extractUnitName}"
                                                     style="width: 180px;cursor: pointer;"
                                                     onclick="document.getElementById('tabView:editForm:selExtractUnitLink').click();"
                                                     readonly="true"/>
                                    </td>
                                    <td style="border-color: transparent;">
                                        <p:commandLink styleClass="ui-icon ui-icon-search"
                                                       id="selExtractUnitLink"
                                                       action="#{mgrbean.selExtractUnitAction}" process="@this"
                                                       style="position: relative;left: -28px !important;">
                                            <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectExtractUnitAction}" process="@this"
                                                    resetValues="true" update="extractUnitName" />
                                        </p:commandLink>
                                    </td>
                                    <!-- 清空 -->
                                    <td style="border-color: transparent;position: relative;left: -30px;">
                                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                       process="@this" update="extractUnitName" action="#{mgrbean.clearExtractUnit}">
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first">
                        <h:outputText styleClass="cs-required" value="报告出具日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv" style="padding-left:13px !important" >
                        <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.rptDateStr}"
                                                      endDate="#{mgrbean.rptDateEnd}" styleClass="searchTime"
                                                      ifNotMaxDate="true"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w">
                        <h:outputText value="体检日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv" style="padding-left:12px !important" colspan="3">
                        <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.bhkDateStr}"
                                                      endDate="#{mgrbean.bhkDateEnd}" styleClass="searchTime"
                                                      ifNotMaxDate="true"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first">
                        <h:outputText styleClass="cs-required" value="抽取总数："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="3">
                        <h:panelGrid columns="12" style="border-color:transparent;padding:0px;">
                            <c:if test="#{mgrbean.extractTypeList != null and mgrbean.extractTypeList.size() > 0}">
                                <c:forEach items="#{mgrbean.extractTypeList}" var="itm">
                                    <h:outputText value="#{itm[1]}："/>
                                    <p:inputText value="#{itm[2]}" maxlength="5" styleClass="cs-w-80"
                                                 style="text-align: center;"
                                                 onkeyup="SYSTEM.verifyNum3(this,5,0,true)"
                                                 onblur="SYSTEM.verifyNum3(this,5,0,true)"
                                                 onkeydown="SYSTEM.verifyNum3(this,5,0,true)">
                                        <p:ajax event="blur" update="extractTablePanel"
                                                process="@this,extractTablePanel"
                                                listener="#{mgrbean.changeExtractTypeNum(itm)}"/>
                                    </p:inputText>
                                    <p:spacer width="5"/>
                                </c:forEach>
                            </c:if>
                        </h:panelGrid>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first" >
                        <h:outputText  styleClass="cs-required" value="抽取规则："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="3">
                        <p:outputPanel id="extractTablePanel">
                            <p:commandButton value="添加" icon="ui-icon-plus" styleClass="cs-mb-5" onclick="hideTooltips();"
                                             process="@this,extractTable" update="extractTable" action="#{mgrbean.addExtractRuleAction}"
                                             resetValues="true" />
                            <p:dataTable var="itm" value="#{mgrbean.extractRuleList}" paginator="no"  emptyMessage="没有您要找的记录！"
                                         id="extractTable" >
                                <!--@elvariable id="R" type="java.lang.Integer"-->
                                <p:column style="text-align: center;width:4%;" headerText="优先级">
                                    <h:outputText value="#{itm.priority}"/>
                                </p:column>
                                <p:column style="text-align: center;width:10%;" headerText="抽取类别">
                                    <h:outputLabel value="#{itm.fkByExtractTypeId.codeName}"/>
                                </p:column>
                                <p:column style="text-align: center;width:12%;" headerText="在岗状态">
                                    <h:outputText value="#{itm.onguardStateName}" />
                                </p:column>
                                <p:column  style="width:20%;" headerText="体检危害因素">
                                    <h:outputText id="badrsnsName" value="#{itm.badrsnsName}" styleClass="zwx-tooltip"/>
                                    <p:tooltip for="badrsnsName" style="max-width:800px;">
                                        <p:outputLabel styleClass="cs-break-word" value="#{itm.badrsnsName}" escape="false"/>
                                    </p:tooltip>
                                </p:column>
                                <p:column style="width:16%;" headerText="危害因素结论">
                                    <h:outputText id="badrsnsRstName" value="#{itm.badrsnsRstName}" styleClass="zwx-tooltip"/>
                                    <p:tooltip for="badrsnsRstName" style="max-width:450px;">
                                        <p:outputLabel styleClass="cs-break-word" value="#{itm.badrsnsRstName}" escape="false"/>
                                    </p:tooltip>
                                </p:column>
                                <p:column style="text-align: center;width:10%;" headerText="胸片/听力检测结论">
                                    <h:outputText  value="#{itm.chestResultName}" rendered = "#{itm.fkByExtractTypeId.extendS1 eq '1' }"/>
                                    <h:outputText  value="#{itm.hearingRstName}" rendered = "#{itm.fkByExtractTypeId.extendS1 eq '2' }"/>
                                </p:column>
                                <p:column style="text-align: center;" headerText="#{mgrbean.redStar}抽取份数">
                                    <p:inputText value="#{itm.sampleNumber}" maxlength="5" styleClass="cs-w-80"
                                                 style="text-align: center;"
                                                 disabled="#{!itm.ifEdit}"
                                                 onkeyup="SYSTEM.verifyNum3(this,5,0,true)"
                                                 onblur="SYSTEM.verifyNum3(this,5,0,true)"
                                                 onkeydown="SYSTEM.verifyNum3(this,5,0,true)"/>
                                </p:column>
                                <p:column headerText="操作" >
                                    <p:commandLink value="修改"  onclick="hideTooltips();"
                                                   action="#{mgrbean.editExtractAction}"
                                                   process="@this,extractTable"
                                                   update="extractTable" resetValues="true">
                                        <f:setPropertyActionListener target="#{mgrbean.extractId}" value="#{itm.rid}" />
                                    </p:commandLink>
                                    <p:spacer width="5"  />
                                    <p:commandLink value="删除"  onclick="hideTooltips();"
                                                   action="#{mgrbean.delExtractAction}"
                                                   process="@this,extractTable"
                                                   update="extractTable">
                                        <f:setPropertyActionListener target="#{mgrbean.extractId}" value="#{itm.rid}"/>
                                        <p:confirm header="消息确认框" message="确定要删除吗？"
                                                   icon="ui-icon-alert"/>
                                    </p:commandLink>
                                </p:column>
                            </p:dataTable>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:dialog header="抽取配置"  widgetVar="ExtractRuleDialog"  id="extractRuleDialog"
                      resizable="false" width="540"  modal="true">
                <p:panelGrid style="margin-top: 10px;margin-bottom: 50px;width:100%" id="extractRulePanelGrid">
                    <p:row>
                        <p:column styleClass="cs-scl-first" >
                            <h:outputText  styleClass="cs-required" value="抽取类别："/>
                        </p:column>
                        <p:column styleClass="cs-scv">
                            <p:selectOneRadio value="#{mgrbean.extractRule.extractTypeId}" style="width: 330px;"
                                              layout="grid" columns="3">
                                <f:selectItems value="#{mgrbean.bhkTypeList}" var="itm" itemLabel="#{itm.codeName}"
                                               itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,extractRulePanelGrid"
                                        update="@this,extractRulePanelGrid"
                                        listener="#{mgrbean.changeExtractType}"></p:ajax>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="cs-scl-first">
                            <h:outputText value="在岗状态："/>
                        </p:column>
                        <p:column styleClass="cs-scv" >
                            <zwx:SimpleCodeManyComp codeName="#{mgrbean.extractRule.onguardStateName}"
                                                    selectedIds="#{mgrbean.extractRule.onguardStateIds}"
                                                    simpleCodeList="#{mgrbean.onDutyStatusList}"
                                                    height="160" reset="true"></zwx:SimpleCodeManyComp>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="cs-scl-first" >
                            <h:outputText  styleClass="cs-required" value="体检危害因素："/>
                        </p:column>
                        <p:column styleClass="cs-scv" style="padding-left: 11px !important;">
                            <table>
                                <tr>
                                    <td style="padding: 0;border-color: transparent;">
                                        <p:inputText id="extractBadRsnName" style="width: 180px;cursor: pointer;"
                                                     value="#{mgrbean.extractRule.badrsnsName}" readonly="true"
                                                     onclick="document.getElementById('tabView:editForm:selExtractBadRsnLink').click();"/>
                                    </td>
                                    <td style="border-color: transparent;">
                                        <p:commandLink styleClass="ui-icon ui-icon-search" id="selExtractBadRsnLink"
                                                       style="position: relative;left: -28px !important;"
                                                       process="@this" action="#{mgrbean.selExtractBadRsnAction}">
                                            <p:ajax event="dialogReturn" listener="#{mgrbean.onExtractBadRsnAction}"
                                                    process="@this" resetValues="true" update="extractBadRsnName"/>
                                        </p:commandLink>
                                    </td>
                                    <!-- 清空 -->
                                    <td style="border-color: transparent;position: relative;left: -30px;">
                                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                       process="@this" update="extractBadRsnName" action="#{mgrbean.clearExtractBadRsn}">
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="cs-scl-first" >
                            <h:outputText styleClass="cs-required"  value="单危害因素结论："/>
                        </p:column>
                        <p:column styleClass="cs-scv" >
                            <zwx:SimpleCodeManyComp codeName="#{mgrbean.extractRule.badrsnsRstName}"
                                                    selectedIds="#{mgrbean.extractRule.badrsnsRstIds}"
                                                    simpleCodeList="#{mgrbean.searchBhkrstList}"
                                                    inputWidth="180" height="120" reset="true"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="cs-scl-first" style="display:#{mgrbean.extractRule.fkByExtractTypeId.extendS1 eq '1'?'':'none'}">
                            <h:outputText  value="胸片结论："/>
                        </p:column>
                        <p:column styleClass="cs-scv" style="display:#{mgrbean.extractRule.fkByExtractTypeId.extendS1 eq '1'?'':'none'}">
                            <p:selectManyCheckbox value="#{mgrbean.extractRule.chestResultIds}" >
                                <f:selectItems value="#{mgrbean.rabatList}"/>
                            </p:selectManyCheckbox>
                        </p:column>
                        <p:column styleClass="cs-scl-first" style="display:#{mgrbean.extractRule.fkByExtractTypeId.extendS1 eq '2'?'':'none'}">
                            <h:outputText  value="听力检测结论："/>
                        </p:column>
                        <p:column styleClass="cs-scv" style="display:#{mgrbean.extractRule.fkByExtractTypeId.extendS1 eq '2'?'':'none'}">
                            <p:selectOneRadio  value="#{mgrbean.extractRule.hearingRstId}" style="width: 160px;">
                                <f:selectItems value="#{mgrbean.hearingSimpleList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                            </p:selectOneRadio>
                        </p:column>
                        <p:column styleClass="cs-scl-first" style="display:#{mgrbean.extractRule.fkByExtractTypeId.extendS1 eq '3'?'':'none'}">
                            <h:outputText  value="预警关联："/>
                        </p:column>
                        <p:column styleClass="cs-scv" style="display:#{mgrbean.extractRule.fkByExtractTypeId.extendS1 eq '3'?'':'none'}">
                            <p:selectOneRadio  value="#{mgrbean.extractRule.ifWarn}" style="width: 120px;">
                                <f:selectItems value="#{mgrbean.warningList}" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="cs-scl-first" >
                            <h:outputText  styleClass="cs-required" value="优先级："/>
                        </p:column>
                        <p:column styleClass="cs-scv" style="padding-left: 14px !important;">
                            <p:inputText value="#{mgrbean.extractRule.priority}"
                                         maxlength="5"
                                         onkeyup="SYSTEM.clearNoNumBig0(this)"
                                         onblur="SYSTEM.clearNoNumBig0(this)"
                                         onkeydown="SYSTEM.clearNoNumBig0(this)"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <f:facet name="footer">
                    <h:panelGrid style="width: 100%;text-align: center;">
                        <h:panelGroup>
                            <p:commandButton value="保存" styleClass="submit_btn"
                                             action="#{mgrbean.saveExtractRuleAction}" process="@this,:tabView:editForm" />
                            <p:spacer width="5" />
                            <p:commandButton value="取消" onclick="PF('ExtractRuleDialog').hide();"
                                             process="@this" immediate="true" />
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
            </p:dialog>
        </p:fieldset>

    </ui:define>

</ui:composition>