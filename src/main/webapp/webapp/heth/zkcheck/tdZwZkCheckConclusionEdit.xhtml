<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.TdZwCheckConclusionBean"-->
    <ui:define name="insertEditScripts"></ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制考核结论录入" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm" >
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <p:row>
                <p:column style="text-align:right;width: 220px;height: 32px;" >
                    <p:outputLabel value="*" style="color: red;" rendered="#{mgrbean.relCheckState ne 1}"/>
                    <p:outputLabel value="考核类型："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 330px;" >
                    <p:selectOneMenu  value="#{mgrbean.checkTypeId}" id="checkType" style="width:205px;"
                                      rendered="#{mgrbean.relCheckState ne 1}">
                        <c:forEach items="#{mgrbean.checkTypeSimpleCodeList}" var="itm" >
                            <f:selectItem itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
                        </c:forEach>
                        <p:ajax event="change" process="@this" listener="#{mgrbean.changeCheckType}" update="unitName,recordCategory,:tabView:editForm:unPassItemSelect" />
                    </p:selectOneMenu>
                    <p:outputLabel value="#{null == mgrbean.checkConclusion.fkByCheckTypeId ? null : mgrbean.checkConclusion.fkByCheckTypeId.codeName}" rendered="#{mgrbean.relCheckState == 1}" />
                </p:column>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="*" style="color: red;" rendered="#{mgrbean.relCheckState ne 1}"/>
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px #{mgrbean.relCheckState == 1 ? 11 : 5}px;">
                    <h:panelGrid columns="2" style="border-color: transparent;margin: 0px;padding: 0px;"
                                 rendered="#{mgrbean.relCheckState ne 1}">
                        <p:inputText id="unitName"  readonly="true" style="width:205px;"
                                     value="#{null == mgrbean.checkConclusion or null == mgrbean.checkConclusion.fkByUnitId
                                      ? null : mgrbean.checkConclusion.fkByUnitId.unitname}"
                                     onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
                        <p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectOrgList}" process="@this,checkType" style="position: relative;left: -30px;"
                        >
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onOrgSelect}" update="unitName,recordCategory,:tabView:editForm:unPassItemSelect"/>
                        </p:commandLink>
                    </h:panelGrid>
                    <p:outputLabel value="#{null == mgrbean.checkConclusion.fkByUnitId ? null : mgrbean.checkConclusion.fkByUnitId.unitname}" rendered="#{mgrbean.relCheckState == 1}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 32px;" >
                    <p:outputLabel value="备案类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:outputLabel value="#{mgrbean.recordCategory}" id="recordCategory" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 32px;" >
                    <p:outputLabel value="*" style="color: red;" rendered="#{mgrbean.relCheckState ne 1}" />
                    <p:outputLabel value="考核日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3">
                    <p:calendar size="11" navigator="true"  converterMessage="考核日期格式输入不正确！"
                                rendered="#{mgrbean.relCheckState ne 1}" value="#{mgrbean.checkConclusion.checkDate}"
                                yearRange="c-10:c" maxlength="10" styleClass="myCalendar1"
                                pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                                maxdate="new Date()" readonlyInput="true"/>
                    <h:outputText value="#{mgrbean.checkConclusion.checkDate}" rendered="#{mgrbean.relCheckState == 1}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left;" colspan="4">
                        <p:outputLabel value="质量考核结果"/>
                    </p:column>
                </p:row>
            </f:facet>
            <c:forEach items="#{mgrbean.conclusionItemSimpleCodeList}" var="itm">
                <p:row>
                    <p:column style="text-align:right;width: 220px;height: 32px;" >
                        <p:outputLabel value="*" style="color: red;"/>
                        <p:outputLabel value="#{itm.codeName}"/>
                        <p:outputLabel value="："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:11px;vertical-align: center;" colspan="3" >
                        <c:forEach items="#{mgrbean.scoreRstSimpleCodeList}" var="rstItm">
                            <p:outputLabel value="#{rstItm.codeName}："/>
                            <c:forEach items="#{mgrbean.checkItemRstList}" var="curRst">
                                <c:if test="#{itm.rid == curRst.fkByItemId.rid and rstItm.rid == curRst.fkByRstId.rid}">
                                    <p:inputText value="#{curRst.nums}" style="width: 60px;" maxlength="3" onkeyup="SYSTEM.verifyNum3(this,3,0,false)"
                                                 onblur="SYSTEM.verifyNum3(this,3,0,true)" />
                                </c:if>
                            </c:forEach>
                            <p:outputLabel value="项" style="margin-right: 11px;margin-left: 3px;" />
                        </c:forEach>
                    </p:column>
                </p:row>
            </c:forEach>
            <p:row>
                <p:column style="text-align:right;width: 220px;height: 32px;" >
                    <p:outputLabel value="*" style="color: red;"/>
                    <p:outputLabel value="结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left: 11px;vertical-align: center;" colspan="3" >
                    <p:selectOneRadio value="#{mgrbean.checkConclusion.checkRst}" style="width: 120px;" >
                        <f:selectItem itemValue="1" itemLabel="合格"/>
                        <f:selectItem itemValue="0" itemLabel="不合格"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="其他："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.others}"
                            rows="3" autoResize="false" maxlength="1000"
                            style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="总结："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.summary}"
                            rows="3" autoResize="false" maxlength="1000"
                            style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="4">
                        <p:outputLabel value="质量考核结论及意见"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;height: 30px;width: 220px;" >
                    <p:outputLabel value="*" style="color: red;"/>
                    <p:outputLabel value="现场质量考核结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:selectOneRadio value="#{mgrbean.conclusionId}" columns="1" layout="grid" style="width: 50%;" >
                        <f:selectItems value="#{mgrbean.conclusionResultSimpleCodeList}" var="conItm"
                                       itemLabel="#{conItm.codeName}" itemValue="#{conItm.rid}" />
                        <p:ajax event="change" process="@this" listener="#{mgrbean.changeCheckResult}" update="unPassItem,unPassItemSelect" />
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 30px;width: 220px;" >
                    <p:outputLabel id="unPassItem">
                        <p:outputLabel value="*" style="color: red;" rendered="#{mgrbean.checkConclusion.fkByConclusionId.extendS1 == 1}"/>
                        <p:outputLabel value="未通过考核的备案项目："/>
                    </p:outputLabel>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3" >
                    <p:outputPanel id="unPassItemSelect" style="width: 320px;">
                        <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectRecordCateGorys}" reset="#{mgrbean.ifResetComp}" id="unPassItemRecordSelect"
                                                selectedIds="#{mgrbean.selectRecordCateGoryIds}" disabled="#{mgrbean.checkConclusion.fkByConclusionId.extendS1 == 1 ? 'false' : 'true'}"
                                                simpleCodeList="#{mgrbean.recordCategorySimpleCodeList}"
                                                inputWidth="300"
                                                panelWidth="310"
                                                height="200" />
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 32px;width: 220px;" >
                    <p:outputLabel value="*" style="color: red;"/>
                    <p:outputLabel value="被考核机构确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left: 11px;width: 330px;" >
                    <p:selectOneRadio value="#{mgrbean.checkConclusion.ifConfirm}" style="width: 120px;" >
                        <f:selectItem itemValue="1" itemLabel="确认"/>
                        <f:selectItem itemValue="0" itemLabel="不确认"/>
                    </p:selectOneRadio>
                </p:column>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="确认附件："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel>
                        <p:commandButton value="上传"  process="@this,:tabView:editForm" oncomplete="PF('FileDialog').show();"
                                         update=":tabView:editForm:fileDialog"  rendered="#{mgrbean.checkConclusion.filePath==null}" />
                        <p:spacer width="5"/>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.checkConclusion.filePath}')"
                                         rendered="#{mgrbean.checkConclusion.filePath != null}" />
                        <p:spacer width="5"/>
                        <p:commandButton value="删除"
                                         process="@this,:tabView:editForm"
                                         action="#{mgrbean.delConclusFilePath}"
                                         rendered="#{mgrbean.checkConclusion.filePath != null}"
                                         update=":tabView:editForm" />
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="考核专家确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.expertAdv}"
                            rows="3" autoResize="false" maxlength="1000"
                            style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="考核组组长确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.leaderAdv}"
                            rows="3" autoResize="false" maxlength="1000"
                            style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true"  width="810">
            <table>
                <tr>
                    <td style="text-align: right;"><p:outputLabel
                            value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                            style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择上传文件！" style="width:770px;" previewWidth="120"
                                      fileUploadListener="#{mgrbean.executeUploadFile}" id="fileUpload"
                                      label="选择文件" uploadLabel="上传" cancelLabel="取消"
                                      fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                                      sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                                      validatorMessage="上传出错啦，重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      process="@this"
                                      mode="advanced" dragDropSupport="true"
                                      onstart="zwx_loading_start();" oncomplete="zwx_loading_stop()"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>