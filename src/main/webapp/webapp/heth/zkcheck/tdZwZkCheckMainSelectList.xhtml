<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdZwZkCheckMainSelectListBean}"/>
	<ui:param name="viewPage" value= "/webapp/heth/zkcheck/tdZwZkCheckMainView.xhtml" />
	<ui:param name="edit3Page" value= "/webapp/heth/zkcheck/tdZwZkCheckSubView.xhtml" />
	<ui:define name="insertScripts">
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<h:outputStylesheet name="css/annexViewDialog.css"/>
		<style type="text/css">
			.myCalendar1 input{
				width: 78px;
			}
			.cs-first {
				text-align: right !important;
				width: 15% !important;
				height: 38px !important;
			}
			.cs-title {
				text-align: right !important;
				width: 15% !important;
			}
			.cs-content {
				text-align:left !important;
				padding-left:11px !important;
				width: 18% !important;
				word-wrap: break-word;
				word-break: break-all;
			}
			.cs-finally {
				text-align:left !important;
				padding-left:11px !important;
				word-wrap: break-word;
				word-break: break-all;
			}
			.cs-con {
				text-align:left !important;
				width: 18% !important;
				word-wrap: break-word;
				word-break: break-all;
			}
		</style>
		<script type="text/javascript">
			function showFullScreen() {
				$(".fullscreen").show();
			}

			function hideFullScreen() {
				$(".fullscreen").hide();
			}
			function openFullScreen(){
				var elem = document.getElementById("pdf");
				var url = $(elem).attr("src");
				window.open(url.replace("#toolbar=0", ""));
			}
			function hideTooltip(){
				setTimeout("$('.ui-tooltip').hide()",1000);
			}
		</script>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="现场考核查询"/>
				<h:inputText style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
								 update="dataTable" process="@this,mainGrid" />
				<p:spacer width="5"/>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:150px;">
				<h:outputText value="考核日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;;width:300px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchCheckStartTime}"
											  styleClass="myCalendar1"  endDate="#{mgrbean.searchCheckEndTime}"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:150px;">
				<h:outputText value="是否需要整改：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width:150px;">
				<p:selectManyCheckbox value="#{mgrbean.searchNeedImproveStateList}" converter="javax.faces.Integer">
					<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
					<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
				</p:selectManyCheckbox>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:150px;">
				<h:outputText value="整改状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:7px;">
				<p:selectManyCheckbox value="#{mgrbean.searchFinishImproveStateList}" converter="javax.faces.Integer">
					<f:selectItem itemValue="0" itemLabel="待整改" />
					<f:selectItem itemValue="1" itemLabel="待审核" />
					<f:selectItem itemValue="3" itemLabel="审核退回" />
					<f:selectItem itemValue="2" itemLabel="整改完成" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>
	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="考核机构" style="width:300px;">
			<h:outputText value="#{itm[1]}"/>
		</p:column>
		<p:column headerText="考核类型" style="width:200px;text-align: center; ">
			<h:outputText value="#{itm[2]}"/>
		</p:column>
		<p:column headerText="考核日期" style="width:120px;text-align:center; ">
			<h:outputLabel value="#{itm[3]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			</h:outputLabel>
		</p:column>
		<p:column headerText="本年度是否开展工作" style="width:120px;text-align:center; ">
			<h:outputText value="否" rendered="#{itm[4] == 0}"/>
			<h:outputText value="是" rendered="#{itm[4] == 1}"/>
		</p:column>
		<p:column headerText="总得分" style="width:120px;text-align: center">
			<h:outputText value="#{itm[5]}" escape="false"/>
		</p:column>
		<p:column headerText="评审结论" style="width:80px;text-align:center; ">
			<h:outputText value="通过" rendered="#{itm[6] == 1}"/>
			<h:outputText value="整改后通过" rendered="#{itm[6] == 2}"/>
			<h:outputText value="整改后复审" rendered="#{itm[6] == 3}"/>
			<h:outputText value="不通过" rendered="#{itm[6] == 4}"/>
		</p:column>
		<p:column headerText="是否需要整改" style="width:120px;text-align:center; ">
			<h:outputText value = "是" rendered="#{1 == itm[7]}"/>
			<h:outputText value = "否" rendered="#{0 == itm[7]}"/>
		</p:column>
		<p:column headerText="整改状态" style="width:80px;text-align:center; ">
			<h:outputText value="待整改" style="color:red;" rendered="#{itm[8] == 0}"/>
			<h:outputText value="待审核" rendered="#{itm[8] == 1}"/>
			<h:outputText value="整改完成" rendered="#{itm[8] == 2}"/>
			<h:outputText value="审核退回" style="color:red;" rendered="#{itm[8] == 3}"/>
		</p:column>
		<p:column headerText="操作" >
			<p:spacer width="5" />
			<p:commandLink value="整改"
						   action="#{mgrbean.viewInitAction}"
						   process="@this,:tabView:mainForm:mainGrid"  update=":tabView" rendered="#{itm[8] == 0 or itm[8] == 3}" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
			<p:commandLink value="详情"
						   action="#{mgrbean.viewInitAction}"
						   process="@this,:tabView:mainForm:mainGrid"  update=":tabView" rendered="#{0 == itm[7] or itm[8] == 1 or itm[8] == 2}" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
		</p:column>
	</ui:define>
</ui:composition>
