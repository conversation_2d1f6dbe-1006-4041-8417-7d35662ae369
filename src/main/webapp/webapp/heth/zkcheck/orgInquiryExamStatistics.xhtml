<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.OrgInquiryExamStatisticsBean"-->
    <ui:param name="mgrbean" value="#{orgInquiryExamStatisticsBean}"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            //<![CDATA[

            function getDownloadFileClick() {
                document.getElementById("mainForm:downloadFileBtn").click();
            }

            //]]>
        </script>
        <style>
            .myCalendar2 input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="机构质控情况统计"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"
                                 onclick="zwx_loading_start_pub();"
                                 oncomplete="zwx_loading_stop_pub();"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportDataBtn"
                                 action="#{mgrbean.preExport()}"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start_pub, zwx_loading_stop_pub);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:153px;height:38px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:273px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 153px;">
                <h:outputText value="考核类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:267px;">
                <p:selectOneMenu value="#{mgrbean.examTypeRid}" style="width: 190px;">
                    <f:selectItems value="#{mgrbean.examTypeSimpleCodeList}" var="examType"
                                   itemLabel="#{examType.codeName}"
                                   itemValue="#{examType.rid}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 153px;">
                <font color="red">*</font>
                <h:outputText value="考核日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchExamBeginDate}"
                                              endDate="#{mgrbean.searchExamEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.isProvUser}">
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="质控类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.inquiryExamTypeList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="2" itemLabel="省级"/>
                    <f:selectItem itemValue="3" itemLabel="市级"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertContent">
        <p:panelGrid id="dataTable" style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column styleClass="ui-state-default" style="height:20px;text-align:center;width: 300px;"
                              rowspan="2">
                        <p:outputLabel value="一级指标"/>
                    </p:column>
                    <p:column styleClass="ui-state-default" style="height:20px;text-align:center;width: 300px;"
                              rowspan="2">
                        <p:outputLabel value="二级指标"/>
                    </p:column>
                    <p:column styleClass="ui-state-default" style="height:20px;text-align:center;" rowspan="2">
                        <p:outputLabel value="考核内容序号"/>
                    </p:column>
                    <p:column styleClass="ui-state-default" style="height:20px;text-align:center;" rowspan="2">
                        <p:outputLabel value="参与考核机构数"/>
                    </p:column>
                    <c:forEach items="#{mgrbean.rstSimpleCodeList}" var="itm" varStatus="index">
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;" colspan="2">
                            <p:outputLabel value="#{itm.codeName}"/>
                        </p:column>
                    </c:forEach>
                </p:row>
                <p:row>
                    <c:forEach items="#{mgrbean.rstSimpleCodeList}" var="itm" varStatus="index">
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="机构数"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="率(%)"/>
                        </p:column>
                    </c:forEach>
                </p:row>
            </f:facet>
            <c:forEach var="itm" items="#{mgrbean.dataTableList}" varStatus="R">
                <p:row>
                    <p:column style="padding-left:8px;text-align: center;height: 25px;"
                              rendered="#{itm[1] ne '0'}" rowspan="#{itm[1]}">
                        <h:outputText value="#{itm[0]}"/>
                    </p:column>
                    <p:column style="padding-left:8px;text-align: center;height: 25px;"
                              rendered="#{itm[3] ne '0'}" rowspan="#{itm[3]}">
                        <h:outputText value="#{itm[2]}"/>
                    </p:column>
                    <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                              style="padding-left:8px;text-align: center;height: 25px;">
                        <h:outputText value="#{itm[4]}"/>
                    </p:column>
                    <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                              style="padding-left:8px;text-align: center;height: 25px;">
                        <h:outputText value="#{itm[5]}"/>
                    </p:column>
                    <c:forEach items="#{mgrbean.rstSimpleCodeList}" var="rstSimpleCode" varStatus="index">
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[6 + index.index * 3]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[7 + index.index * 3]}"/>
                        </p:column>
                    </c:forEach>
                </p:row>
            </c:forEach>
            <p:row rendered="#{mgrbean.dataTableListCount == 0}">
                <p:column style="height: 35px;text-align:left;padding-left: 10px;"
                          colspan="#{7 + mgrbean.rstSimpleCodeList.size() * 3}">
                    <p:outputLabel value="没有您要找的数据！"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </ui:define>

</ui:composition>