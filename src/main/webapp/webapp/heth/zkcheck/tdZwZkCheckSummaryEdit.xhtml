<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function showShade() {
                PF('ShadeTip').show();
            }
            //注意 因文件下载完 PrimeFaces 会刷新页面 所以 我们需要监听onload事件
            window.onload=function (){
                document.getElementById("tabView:editForm:generateReportErrMsgId").click();
            }
            function hideShade() {
                //这个方法不会执行 因下载完后 PrimeFaces会刷新页面
                PF('ShadeTip').hide();
            }
            function generateClick(){
                document.getElementById("tabView:editForm:generateReportId").click();
            }
        </script>
        <style type="text/css">
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }
            .shadeTip>div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }

        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制考核汇总录入" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm" >
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:panelGrid id="baseInfo"  style="margin-bottom: 10px;width:100%" >
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px;">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="考核类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;">
                    <!-- 刷新反馈表名称 -->
                    <p:selectOneMenu  value="#{mgrbean.checkSummary.fkByCheckTypeId.rid}" id="checkType" style="width:205px;">
                        <c:forEach items="#{mgrbean.checkTypeList}" var="itm" varStatus="varStatus">
                            <f:selectItem itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
                        </c:forEach>
                        <p:ajax event="change" process="@this" listener="#{mgrbean.changeCheckType}" update="unitName"></p:ajax>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <h:panelGrid columns="2" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="unitName"  readonly="true" style="width:240px;"
                                     value="#{mgrbean.checkSummary.fkByUnitId.unitname}"
                                     onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
                        <p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectOrgList}" process="@this,checkType" style="position: relative;left: -30px;"
                        >
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onOrgSelect}" update="unitName"/>
                        </p:commandLink>
                    </h:panelGrid>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="考核日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;" colspan="3">
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                showOtherMonths="true"  size="11" navigator="true"
                                yearRange="c-10:c" converterMessage="考核日期，格式输入不正确！"
                                showButtonPanel="true" maxdate="#{mgrbean.today}"
                                value="#{mgrbean.checkSummary.checkDate}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="问题及建议："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;" colspan="3">
                    <p:inputTextarea rows="5" autoResize="false"
                                     style="resize: none;width: 799px;height: 100px;margin-left: 5px"
                                     maxlength="1000" value="#{mgrbean.checkSummary.checkAdv}"
                    />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="被考核机构确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;width: 280px;">
                    <p:selectOneRadio value="#{mgrbean.checkSummary.ifConfirm}" style="width:120px;">
                        <f:selectItem itemValue="1" itemLabel="确认"></f:selectItem>
                        <f:selectItem itemValue="0" itemLabel="不确认"></f:selectItem>
                    </p:selectOneRadio>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="确认附件："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <p:outputPanel id="noticePanel">
                        <p:spacer width="5" />
                        <p:commandButton value="上传"  process="@this,:tabView:editForm" oncomplete="PF('FileDialog').show();"
                                         update=":tabView:editForm:fileDialog"  rendered="#{mgrbean.checkSummary.filePath==null}"
                        >
                        </p:commandButton>
                        <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.checkSummary.filePath}')" rendered="#{mgrbean.checkSummary.filePath!=null}"
                                         process="@this"/>
                        <p:spacer width="5" />
                        <p:commandButton value="删除"  action="#{mgrbean.delFile}" rendered="#{mgrbean.checkSummary.filePath!=null}"
                                         update="noticePanel" process="@this">
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                        </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="考核专家："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;width: 280px;">
                    <h:panelGrid columns="3" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="checkExperts"   style="width:196px;" maxlength="100"
                                     value="#{mgrbean.checkSummary.checkExperts}" >
                        </p:inputText>
                        <p:commandLink id="onExpertsSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectPsnList}" process="@this,checkExperts" style="position: relative;left: -30px;"
                        >
                            <f:setPropertyActionListener value="1" target="#{mgrbean.psnSelectType}"/>
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onPsnSelect}" update="checkExperts">
                            </p:ajax>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -33px;"
                                       action="#{mgrbean.clearSelectExperts}" process="@this"
                                       update="checkExperts"/>
                    </h:panelGrid>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="考核组组长："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <h:panelGrid columns="3" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="checkLeaders"   style="width:240px;" maxlength="100"
                                     value="#{mgrbean.checkSummary.checkLeaders}" >
                        </p:inputText>
                        <p:commandLink id="onLeadersSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectPsnList}" process="@this,checkLeaders" style="position: relative;left: -30px;"
                        >
                            <f:setPropertyActionListener value="2" target="#{mgrbean.psnSelectType}"/>
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onPsnSelect}" update="checkLeaders">
                            </p:ajax>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -33px;"
                                       action="#{mgrbean.clearSelectLeaders}" process="@this"
                                       update="checkLeaders"/>
                    </h:panelGrid>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true"  width="810">
            <table>
                <tr>
                    <td style="text-align: right;"><p:outputLabel
                            value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                            style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择上传文件！" style="width:770px;" previewWidth="120"
                                      fileUploadListener="#{mgrbean.fileUpload}" id="fileUpload"
                                      label="选择文件" uploadLabel="上传" cancelLabel="取消"
                                      fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                                      sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                                      validatorMessage="上传出错啦，重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      process="@this"
                                      mode="advanced" dragDropSupport="true"
                                      onstart="zwx_loading_start();" oncomplete="zwx_loading_stop()"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>