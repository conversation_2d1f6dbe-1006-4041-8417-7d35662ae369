<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.ExpertMaintenanceListBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{expertMaintenanceListBean}"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <ui:param name="onfocus" value="false"/>
        <script type="text/javascript">
            //elStr 悬浮框(地区框)id
            function removeExtraZonePanel(elStr) {
                var el = jQuery(elStr);
                if (el.length > 1) {
                    el.each(function (index) {
                        if (index > 0) {
                            $(this).remove();
                        }
                    });
                }
            }

            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:expertDataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 80px;
                margin-left: 3px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="专家库维护"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
                <p:commandButton value="选择专家" icon="ui-icon-plus" oncomplete="datatableOffClick();"
                                 action="#{mgrbean.addPsnAction}" update=":tabView" process="@this">
                    <p:ajax event="dialogReturn" process="@this"
                            listener="#{mgrbean.onPsnSel}" update="dataTable"/>
                </p:commandButton>
                <p:commandButton value="添加专家" icon="ui-icon-plus" id="addBtn" resetValues="true"
                                 oncomplete="removeExtraZonePanel('#tabView\\:mainForm\\:expertZone\\:zonePanel')"
                                 action="#{mgrbean.addExpertAction}" process="@this"
                                 update=":tabView:mainForm:addExpertDialog">
                    <f:setPropertyActionListener target="#{mgrbean.type}" value="1"/>
                </p:commandButton>
            </h:panelGrid>

        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="地区："/>
            </p:column>
            <p:column styleClass="cs-scv-w" style="padding-left: 2px !important;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="6" onchange="onZoneSelect();" ifShowTrash="true"/>
                <p:remoteCommand name="onZoneSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="@this,searchZone"/>

            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:inputText value="#{mgrbean.searchUnitName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="姓名："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText value="#{mgrbean.searchUserName}" style="width: 180px;" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="身份证号："/>
            </p:column>
            <p:column styleClass="cs-scv" colspan="5">
                <p:inputText value="#{mgrbean.searchIDC}" placeholder="精确查询" style="width: 180px;" maxlength="50"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width: 300px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width: 300px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="姓名" style="text-align:center;width: 80px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="职称" style="text-align:center;width: 150px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="附件" style="width:100px;text-align:center;">
            <p:commandLink value="查看" process="@this" rendered="#{itm[5] != null and itm[5] != ''}"
                           onclick="window.open('/webFile/#{itm[5]}')"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="修改"
                           action="#{mgrbean.modExpertAction}"
                           process="@this" update=":tabView" resetValues="true">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener target="#{mgrbean.type}" value="2"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" process="@this" action="#{mgrbean.delExpertAction}"
                           update=":tabView:mainForm:dataTable">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 专家信息 -->
        <p:dialog header="专家信息" widgetVar="AddExpertDialog" id="addExpertDialog" resizable="false" modal="true"
                  width="500" height="320">
            <p:remoteCommand name="findSexByIdc" action="#{mgrbean.findSexByIdc}"
                             process="@this,:tabView:mainForm:expertInfo" update=":tabView:mainForm:expertInfo"/>
            <p:panelGrid style="width:100%;" id="expertInfo">
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="单位名称：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <div class="cs-flex cs-flex-ai-center">
                            <p:inputText styleClass="cs-w-180" readonly="true" style="cursor: pointer;" id="crptName"
                                         value="#{mgrbean.tdZwExpert.unitName}"
                                         onclick="jQuery('#tabView\\:mainForm\\:onCrptSelect').click()"/>
                            <p:commandLink id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search"
                                           style="margin-left: -20px;"
                                           partialSubmit="true" action="#{mgrbean.selectCrptList}"
                                           process="@this,:tabView:mainForm:expertInfo">
                                <p:ajax event="dialogReturn" process="@this"
                                        listener="#{mgrbean.onCrptSelect}" update=":tabView:mainForm:expertInfo"/>
                            </p:commandLink>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="姓名：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:inputText value="#{mgrbean.tdZwExpert.userName}" style="width: 180px;" maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="身份证号：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:inputText value="#{mgrbean.tdZwExpert.idc}" onblur="findSexByIdc();" style="width: 180px;"
                                     maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="性别："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="男" rendered="#{1 eq mgrbean.tdZwExpert.sex}"/>
                        <h:outputText value="女" rendered="#{2 eq mgrbean.tdZwExpert.sex}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="手机号码：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:inputText value="#{mgrbean.tdZwExpert.linkPhone}" style="width: 180px;" maxlength="20"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="职称：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:selectOneMenu value="#{mgrbean.tdZwExpert.fkByTitleId.rid}" style="width:188px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.jobTitleList}" var="itm" itemValue="#{itm.rid}"
                                           itemLabel="#{itm.codeName}"/>
                            <p:ajax event="change" listener="#{mgrbean.changeTitle}"
                                    process="@this,:tabView:mainForm:expertInfo" update=":tabView:mainForm:expertInfo"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h ">
                        <h:outputText value="附件：" styleClass="#{mgrbean.ifNotJobTitle?'':'cs-required'}"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:panelGrid columns="10" style="border-color: transparent;" id="fileUploadPanel">
                            <p:commandButton value="上传" style="margin-left: -5px;" update=":tabView:mainForm:upload"
                                             rendered="#{mgrbean.tdZwExpert ==null or mgrbean.tdZwExpert.filePath == null or mgrbean.tdZwExpert.filePath == ''}"
                                             process="@this" onclick="PF('FileDialog').show();">
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             rendered="#{mgrbean.tdZwExpert != null and mgrbean.tdZwExpert.filePath != null and mgrbean.tdZwExpert.filePath != ''}"
                                             onclick="window.open('/webFile/#{mgrbean.tdZwExpert.filePath}')"/>
                            <p:spacer width="3"
                                      rendered="#{mgrbean.tdZwExpert != null and mgrbean.tdZwExpert.filePath != null and mgrbean.tdZwExpert.filePath != ''}"/>
                            <p:commandButton value="删除" style="margin-left: -15px;"
                                             rendered="#{mgrbean.tdZwExpert != null and mgrbean.tdZwExpert.filePath != null and mgrbean.tdZwExpert.filePath != ''}"
                                             process="@this,:tabView:mainForm:fileUploadPanel"
                                             action="#{mgrbean.delFilePath}">
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" process="@this,:tabView:mainForm:expertInfo"
                                         action="#{mgrbean.saveExpertAction}">
                            <f:setPropertyActionListener target="#{mgrbean.saveMark}" value="1"/>
                            <f:setPropertyActionListener target="#{mgrbean.dataSource}" value="2"/>
                        </p:commandButton>
                        <p:spacer width="5"/>
                        <p:commandButton value="连续保存" icon="ui-icon-check" rendered="#{1 eq mgrbean.type}"
                                         process="@this,:tabView:mainForm:expertInfo"
                                         action="#{mgrbean.saveExpertAction}" resetValues="true">
                            <f:setPropertyActionListener target="#{mgrbean.saveMark}" value="2"/>
                            <f:setPropertyActionListener target="#{mgrbean.dataSource}" value="2"/>
                        </p:commandButton>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('AddExpertDialog').hide();"
                                         immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog"
                  resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;"><p:outputLabel
                            value="（支持附件格式为：图片、PDF）"
                            styleClass="blueColorStyle"
                            style="position: relative;bottom: -6px;padding-right: 30px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}"
                                invalidSizeMessage="文件大小不能超过100M!" id="upload"
                                validatorMessage="上传出错啦，请重新上传！" style="width:600px;"
                                previewWidth="120" cancelLabel="取消" update="upload"
                                uploadLabel="上传" dragDropSupport="true" mode="advanced"
                                sizeLimit="104857600" fileLimit="1"
                                fileLimitMessage="最多只能上传1个文件！"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                        /></td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>

</ui:composition>
