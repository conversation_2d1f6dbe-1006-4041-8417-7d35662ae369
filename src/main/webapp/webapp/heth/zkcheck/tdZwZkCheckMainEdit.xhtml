<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function showShade() {
                PF('ShadeTip').show();
            }

            function hideShade() {
                //这个方法不会执行 因下载完后 PrimeFaces会刷新页面
                PF('ShadeTip').hide();
            }

            function generateClick() {
                document.getElementById("tabView:editForm:generateReportId").click();
            }
        </script>
        <style type="text/css">
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip > div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }
            .no-close-button .ui-dialog-titlebar-close {
                display: none !important;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="现场考核管理"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:commandButton style="display: none;" id="generateReportId" ajax="false" icon="ui-icon-print"
                                 process="@this,:tabView:editForm"
                                 onclick="PrimeFaces.monitorDownload(showStatus,hideStatus);">
                    <p:fileDownload value="#{mgrbean.getReportFile()}"/>
                </p:commandButton>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:confirmDialog message="提交后将无法撤销，是否提交？" header="消息确认框" widgetVar="ConfirmSubmitDialog">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                                 oncomplete="PF('ConfirmSubmitDialog').hide();" update=":tabView" resetValues="true"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmSubmitDialog').hide();"
                                 type="button"/>
            </p:outputPanel>
        </p:confirmDialog>

        <p:confirmDialog message="切换考核类型将重置考核信息，是否继续？" header="消息确认框"
                         widgetVar="ConfirmDialog" styleClass="no-close-button">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.changeCheckType}" icon="ui-icon-check"
                                 process="@this,:tabView:editForm" update=":tabView:editForm"
                                 oncomplete="PF('ConfirmDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" action="#{mgrbean.cancelChangeCheckType}"
                                 process="@this" oncomplete="PF('ConfirmDialog').hide();" update="checkType"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:confirmDialog message="切换被考核机构将重置考核信息，是否继续？" header="消息确认框"
                         widgetVar="ConfirmOrgDialog" styleClass="no-close-button">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.changeOrg}" icon="ui-icon-check"
                                 process="@this,:tabView:editForm" update=":tabView:editForm"
                                 oncomplete="PF('ConfirmOrgDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" action="#{mgrbean.cancelChangeOrg}" process="@this"
                                 oncomplete="PF('ConfirmOrgDialog').hide();" update="unitName"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:confirmDialog message="切换本年度是否开展工作将重置考核信息，是否继续？" header="消息确认框"
                         widgetVar="ConfirmDevelopDialog" styleClass="no-close-button">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.changeDevelop}" icon="ui-icon-check"
                                 process="@this,:tabView:editForm" update=":tabView:editForm"
                                 oncomplete="PF('ConfirmDevelopDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" action="#{mgrbean.cancelChangeDevelop}"
                                 process="@this" oncomplete="PF('ConfirmDevelopDialog').hide();" update="develop"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:panelGrid id="baseInfo" style="margin-bottom: 10px;width:100%">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="基本信息"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column styleClass="cs-first">
                    <p:outputLabel value="考核类型：" styleClass="cs-required"/>
                </p:column>
                <p:column styleClass="cs-content">
                    <p:selectOneMenu value="#{mgrbean.checkMain.checkTypeId}" id="checkType"
                                     style="width:240px;#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}">
                        <f:selectItems value="#{mgrbean.checkTypeList}"></f:selectItems>
                        <p:ajax event="change" oncomplete="PF('ConfirmDialog').show();" process="@this"
                                update="checkType"></p:ajax>
                    </p:selectOneMenu>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="被考核机构：" styleClass="cs-required"/>
                </p:column>
                <p:column styleClass="cs-con" style="padding-left:3px;">
                    <h:panelGrid columns="2"
                                 style="border-color: transparent;margin: 0px;padding: 0px;#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}">
                        <p:inputText id="unitName" readonly="true" style="width:240px;"
                                     value="#{mgrbean.checkMain.fkByOrgId.unitname}"
                                     onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
                        <p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search"
                                       partialSubmit="true"
                                       action="#{mgrbean.selectOrgList}" process="@this,:tabView:editForm"
                                       style="position: relative;left: -30px;"
                        >
                            <p:ajax event="dialogReturn" process="@this" resetValues="true"
                                    listener="#{mgrbean.onOrgSelect}" update="unitName"/>
                        </p:commandLink>
                    </h:panelGrid>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="*" style="color:red;"></p:outputLabel>
                    <p:outputLabel value="考核日期："/>
                </p:column>
                <p:column styleClass="cs-finally">
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                style="#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}"
                                showOtherMonths="true" size="11" navigator="true"
                                yearRange="c-10:c" converterMessage="考核日期，格式输入不正确！"
                                showButtonPanel="true" maxdate="new Date()"
                                value="#{mgrbean.checkMain.checkDate}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-first">
                    <p:outputLabel value="备案类别："/>
                </p:column>
                <p:column styleClass="cs-content" colspan="3">
                    <p:outputLabel value="#{mgrbean.checkMain.recordStr}" id="recordStr"/>
                </p:column>
                <p:column styleClass="cs-first">
                    <p:outputLabel value="本年度是否开展工作：" styleClass="cs-required"/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="5">
                    <p:selectOneRadio value="#{mgrbean.checkMain.ifDevelop}" id="develop"
                                      style="#{mgrbean.checkMain.writePath ne null?'pointer-events:none;':''}">
                        <f:selectItem itemLabel="是" itemValue="1"/>
                        <f:selectItem itemLabel="否" itemValue="0"/>
                        <p:ajax event="change" oncomplete="PF('ConfirmDevelopDialog').show();" process="@this"
                                resetValues="true"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid style="margin-bottom: 10px;width:100%">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="现场考核表"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row rendered="#{mgrbean.ifScore}">
                <p:column styleClass="cs-first">
                    <p:outputLabel value="总分值："/>
                </p:column>
                <p:column styleClass="cs-content">
                    <p:outputLabel value="#{mgrbean.checkMain.totalCheckVal}"></p:outputLabel>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="总得分："/>
                </p:column>
                <p:column styleClass="cs-content">
                    <p:outputLabel value="#{mgrbean.checkMain.totalScoreVal}"></p:outputLabel>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="是否需要整改："/>
                </p:column>
                <p:column styleClass="cs-finally">
                    <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifNeedImprove eq 0}"></p:outputLabel>
                    <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifNeedImprove eq 1}"></p:outputLabel>
                </p:column>
            </p:row>
            <p:row rendered="#{!mgrbean.ifScore}">
                <p:column styleClass="cs-first">
                    <p:outputLabel value="评审结论："/>
                </p:column>
                <p:column styleClass="cs-content">
                    <p:outputLabel value="通过" rendered="#{mgrbean.checkMain.reviewConclusion eq 1}"></p:outputLabel>
                    <p:outputLabel value="整改后通过"
                                   rendered="#{mgrbean.checkMain.reviewConclusion eq 2}"></p:outputLabel>
                    <p:outputLabel value="整改后复审"
                                   rendered="#{mgrbean.checkMain.reviewConclusion eq 3}"></p:outputLabel>
                    <p:outputLabel value="不通过" rendered="#{mgrbean.checkMain.reviewConclusion eq 4}"></p:outputLabel>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="是否需要整改："/>
                </p:column>
                <p:column styleClass="cs-finally" colspan="3">
                    <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifNeedImprove eq 0}"></p:outputLabel>
                    <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifNeedImprove eq 1}"></p:outputLabel>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-first">
                    <p:outputLabel value="现场考核表：" styleClass="cs-required"/>
                </p:column>
                <p:column styleClass="cs-con" style="padding-left:6px;">
                    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;" id="specialBtn">
                        <p:commandButton value="生成" icon="ui-icon-print" process="@this,:tabView:editForm"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.writePath}"
                                         onstart="showShade()"
                                         action="#{mgrbean.preGenerateReport}">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="2"/>
                        </p:commandButton>
                        <p:commandButton value="上传" icon="ui-icon-arrowreturnthick-1-n"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.writePath}"
                                         action="#{mgrbean.preUploadReport}">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="2"/>
                        </p:commandButton>
                        <p:commandButton value="下载" icon="ui-icon-arrowthick-1-s"
                                         action="#{mgrbean.preDownLoadReport}"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.writePath}"
                                         process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.checkMain.writePath}"
                                                         target="#{mgrbean.reportFilePath}"/>
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="2"/>
                        </p:commandButton>
                        <p:commandButton value="删除" icon="ui-icon-trash"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.writePath and  null == mgrbean.checkMain.checkTablePath}"
                                         action="#{mgrbean.deleteReport}" process="@this,:tabView:editForm"
                                         update=":tabView:editForm:specialBtn,baseInfo">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="2"/>
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        </p:commandButton>
                        <p:inputText style="visibility: hidden;width: 0"/>
                    </h:panelGrid>
                </p:column>
                <p:column styleClass="cs-title">
                    <p:outputLabel value="现场考核意见书：" styleClass="cs-required"/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;" id="checkPathBtn">
                        <p:commandButton value="生成" icon="ui-icon-print" process="@this,:tabView:editForm"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.checkTablePath}"
                                         onstart="showShade()"
                                         action="#{mgrbean.preGenerateReport}">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="3"/>
                        </p:commandButton>
                        <p:commandButton value="上传" icon="ui-icon-arrowreturnthick-1-n"
                                         rendered="#{null ne mgrbean.checkMain and null == mgrbean.checkMain.checkTablePath}"
                                         action="#{mgrbean.preUploadReport}">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="3"/>
                        </p:commandButton>
                        <p:commandButton value="下载" icon="ui-icon-arrowthick-1-s"
                                         action="#{mgrbean.preDownLoadReport}"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.checkTablePath}"
                                         process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.checkMain.checkTablePath}"
                                                         target="#{mgrbean.reportFilePath}"/>
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="3"/>
                        </p:commandButton>
                        <p:commandButton value="删除" icon="ui-icon-trash"
                                         rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.checkTablePath }"
                                         action="#{mgrbean.deleteReport}" process="@this,:tabView:editForm"
                                         update=":tabView:editForm:checkPathBtn,:tabView:editForm:specialBtn">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="3"/>
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        </p:commandButton>
                        <p:inputText style="visibility: hidden;width: 0"/>
                    </h:panelGrid>
                </p:column>
            </p:row>

            <p:row rendered="#{mgrbean.checkMain.checkTables.size()>0}">
                <p:column colspan="6">
                    <p:panelGrid style="width:100%">
                        <p:row>
                            <p:column style="width:256px;height: 20px;text-align:center;" styleClass="ui-state-default">
                                <p:outputLabel value="考核表"></p:outputLabel>
                            </p:column>
                            <p:column style="width:288px;text-align:center;" styleClass="ui-state-default">
                                <p:outputLabel value="考核项目"></p:outputLabel>
                            </p:column>
                            <p:column style="width:100px;text-align:center;" styleClass="ui-state-default"
                                      rendered="#{mgrbean.ifScore}">
                                <p:outputLabel value="分值"></p:outputLabel>
                            </p:column>
                            <p:column style="width:100px;text-align:center;" styleClass="ui-state-default"
                                      rendered="#{mgrbean.ifScore}">
                                <p:outputLabel value="实得分"></p:outputLabel>
                            </p:column>
                            <p:column style="text-align:center;" styleClass="ui-state-default">
                                <p:outputLabel value="存在问题"></p:outputLabel>
                            </p:column>
                            <p:column style="width: 80px;text-align:center;" styleClass="ui-state-default">
                                <p:outputLabel value="状态"></p:outputLabel>
                            </p:column>
                            <p:column style="text-align:center;width: 120px;" styleClass="ui-state-default">
                                <p:outputLabel value="操作"></p:outputLabel>
                            </p:column>
                        </p:row>
                        <c:forEach items="#{mgrbean.checkMain.checkTables}" var="table" varStatus="tableIndex">
                            <c:forEach items="#{table.checkItems}" var="item" varStatus="itemIndex">
                                <p:row>
                                    <!-- 考核项目 -->
                                    <p:column rendered="#{itemIndex.index==0}" rowspan="#{table.checkItems.size()}">
                                        <p:outputLabel value="#{table.fkByCheckTableId.checkName}"></p:outputLabel>
                                    </p:column>
                                    <p:column style="height:28px;">
                                        <p:outputLabel value="#{item.fkByItemId.codeName}"></p:outputLabel>
                                    </p:column>
                                    <p:column style="text-align:center;" rendered="#{mgrbean.ifScore}">
                                        <p:outputLabel value="#{item.checkVal}"
                                                       rendered="#{item.checkVal != null}"></p:outputLabel>
                                        <p:outputLabel value="/" rendered="#{item.checkVal == null}"></p:outputLabel>
                                    </p:column>
                                    <p:column style="text-align:center;" rendered="#{mgrbean.ifScore}">
                                        <p:outputLabel value="#{item.scoreVal}"
                                                       rendered="#{item.scoreVal != null and  mgrbean.checkMain.ifDevelop eq 1}"></p:outputLabel>
                                        <p:outputLabel value="/"
                                                       rendered="#{item.checkVal == null and item.scoreVal == null  and  mgrbean.checkMain.ifDevelop eq 1 }"></p:outputLabel>
                                        <p:outputLabel value="/"
                                                       rendered="#{mgrbean.checkMain.ifDevelop eq 0}"></p:outputLabel>
                                    </p:column>
                                    <!-- 存在问题 -->
                                    <p:column>
                                        <h:outputText id="deductRsn#{tableIndex.index}#{itemIndex.index}"
                                                      value="#{item.deductRsn}" styleClass="zwx-tooltip"/>
                                        <p:tooltip for="deductRsn#{tableIndex.index}#{itemIndex.index}"
                                                   style="width:450px;">
                                            <p:outputLabel value="#{item.deductRsn2}" escape="false"></p:outputLabel>
                                        </p:tooltip>
                                    </p:column>
                                    <!-- 状态 -->
                                    <p:column style="text-align:center;" rendered="#{itemIndex.index==0}"
                                              rowspan="#{table.checkItems.size()}">
                                        <p:outputLabel value="待提交"
                                                       rendered="#{table.stateMark eq 0}"></p:outputLabel>
                                        <p:outputLabel value="已提交"
                                                       rendered="#{table.stateMark eq 1}"></p:outputLabel>
                                        <p:outputLabel value="无需考核"
                                                       rendered="#{table.stateMark eq 2}"></p:outputLabel>
                                    </p:column>
                                    <p:column rendered="#{itemIndex.index==0}" rowspan="#{table.checkItems.size()}">
                                        <p:spacer width="5"></p:spacer>
                                        <p:commandLink value="查看"
                                                       rendered="#{null ne table.stateMark and table.stateMark==1}"
                                                       process="@this,:tabView:editForm"
                                                       action="#{mgrbean.modCheckIndex}" onclick="hideTooltips();">
                                            <f:setPropertyActionListener target="#{mgrbean.curCheckTable}"
                                                                         value="#{table}"/>
                                        </p:commandLink>
                                        <p:commandLink value="进入"
                                                       rendered="#{null ne table.stateMark and table.stateMark==0}"
                                                       process="@this,:tabView:editForm"
                                                       action="#{mgrbean.modCheckIndex}" onclick="hideTooltips();">
                                            <f:setPropertyActionListener target="#{mgrbean.curCheckTable}"
                                                                         value="#{table}"/>
                                        </p:commandLink>
                                    </p:column>
                                </p:row>
                            </c:forEach>
                        </c:forEach>
                    </p:panelGrid>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid id="checkContentPanel" style="margin-bottom: 10px;width:100%">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left;" colspan="2">
                        <p:outputLabel value="考核证明材料"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column styleClass="cs-first">
                    <p:outputLabel value="考核证明材料："/>
                </p:column>
                <p:column style="padding-left:6px;text-align:left;">
                    <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                        <p:commandButton value="上传附件" action="#{mgrbean.uploadFile}"
                                         update=":tabView:editForm:fileDialog"
                                         icon="ui-icon-arrowthickstop-1-n">
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="0"/>
                        </p:commandButton>
                        <p:commandButton value="查看附件（#{mgrbean.checkMain.materialCheckProves.size()}）"
                                         rendered="#{mgrbean.checkMain.materialCheckProves.size()>0}"
                                         action="#{mgrbean.toAnnexView()}"
                                         icon="ui-icon-folder-open">
                            <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="0"/>
                            <f:setPropertyActionListener target="#{mgrbean.annexType}" value="0"/>
                        </p:commandButton>
                    </h:panelGrid>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 附件预览 -->
        <p:dialog id="annexViewDialog" widgetVar="AnnexViewDialog"
                  styleClass="annex_view_dialog" resizable="false" header="附件预览"
                  width="900" modal="true">
            <p:outputPanel id="annexViewPanel">
                <div style="width:100%;height:500px;">
                    <table class="annex_content_table">
                        <tr>
                            <td class="annex_link">
                                <p:commandLink styleClass="annex_pre"
                                               update="annexViewPanel" process="@this"
                                               rendered="#{mgrbean.checkProveTmp.preIndex!=null}"
                                               action="#{mgrbean.toAnnexView}">
                                    <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}"
                                                                 value="#{mgrbean.checkProveTmp.preIndex}"/>
                                </p:commandLink>
                            </td>
                            <td style="text-align:center">
                                <h:outputText value="#{mgrbean.fileTemp.filePath}" escape="false"/>
                            </td>
                            <td class="annex_link">
                                <p:commandLink styleClass="annex_next" update="annexViewPanel" process="@this"
                                               rendered="#{mgrbean.checkProveTmp.nextIndex!=null}"
                                               action="#{mgrbean.toAnnexView}">
                                    <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}"
                                                                 value="#{mgrbean.checkProveTmp.nextIndex}"/>
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </div>
                <p:outputPanel style="position: absolute;bottom: 0;width: 100%;">
                    <table class="annex_info_table">
                        <tr>
                            <td>
                                附件名称：
                                <h:outputText value="#{mgrbean.fileTemp.fileName}" escape="false"/>
                                <p:commandLink value="删除" style="padding-left:10px;"
                                               action="#{mgrbean.delFileAction}"
                                               rendered="#{mgrbean.checkMain.stateMark!=1}"
                                               update="annexViewPanel,checkContentPanel"
                                >
                                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:outputPanel>
        </p:dialog>
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true" width="800">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要上传的文件！" multiple="true" id="fileUpload"
                                      label="文件选择" fileUploadListener="#{mgrbean.fileUpload}"
                                      invalidSizeMessage="文件大小不能超过10M!"
                                      validatorMessage="上传出错啦，请重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                                      style="width:765px;" previewWidth="120" cancelLabel="取消" update="@this"
                                      uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="10485760"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog header="报表上传" widgetVar="ReportFileUpload" id="reportFileUpload"
                  resizable="false" modal="true" width="730">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：WORD、PDF）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择上传文件！" style="width:695px;"
                                      previewWidth="50" id="uploadReport"
                                      fileUploadListener="#{mgrbean.uploadReport}" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！" label="选择文件" uploadLabel="上传"
                                      cancelLabel="取消" sizeLimit="10485760"
                                      invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传doc，docx，pdf类型文件！" process="@this"
                                      mode="advanced" dragDropSupport="true"
                                      allowTypes="/(\.|\/)(doc|docx|pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog id="shadeTip" widgetVar="ShadeTip" modal="true" height="20" resizable="false" showHeader="false"
                  closeOnEscape="true" styleClass="shadeTip">
            <p:panelGrid>
                <p:row style="border:1px solid transparent !important;">
                    <p:column style="border: transparent !important;">
                        <p:graphicImage url="/resources/images/main/loading5.gif" style="margin-top: 4px;"/>
                    </p:column>
                    <p:column style="border: transparent !important;">
                        <h:outputText style="color: #FFFFFF;font-size: 15px;" value="报告生成中，请等待..."/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
    </ui:define>
</ui:composition>