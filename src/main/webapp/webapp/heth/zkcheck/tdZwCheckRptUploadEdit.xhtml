<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">

    <ui:define name="insertEditScripts">
        <style type="text/css">
            .myCalendar2 input {
                width: 78px;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function datatableOffClick(){
            }
            //]]>
        </script>

    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业卫生技术服务机构检测报告上传" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="退回原因"
                                 icon="icon-alert" action="#{mgrbean.initDialog}" style="color:red;"
                                 process="@this" oncomplete="PF('ReasonDialog').show();"
                                 update=":tabView:editForm:reasonDialog"
                                 rendered="#{mgrbean.tdZwCheckRpt.state==3}">
                    <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="true"></f:setPropertyActionListener>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
                <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
                    <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" oncomplete="PF('ConfirmDialog').hide();"/>
                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
                </p:confirmDialog>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="用人单位信息">
            <p:panelGrid  style="margin-bottom: 10px;width: 100%;" id="crptInfoPanel">
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                        <div style="display: flex;align-items: center;">
                            <p:inputText id="crptName" readonly="true" style="width: 260px;"
                                         value="#{mgrbean.tdZwCheckRpt.crptName}"/>
                            <p:commandLink id="onCrptSelect"
                                           partialSubmit="true" value="选择"
                                           action="#{mgrbean.selectCrptList}" process="@this"
                                           style="margin-left: 10px;">
                                <p:ajax event="dialogReturn" process="@this,crptName" resetValues="true"
                                        listener="#{mgrbean.onCrptSelect}" update="crptInfoPanel"/>
                            </p:commandLink>
                        </div>
                    </p:column>
                    <p:column style="text-align:right;width: 15%;">
                        <p:outputLabel value="所属区域："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <h:outputText escape="false"
                                      value="#{mgrbean.tdZwCheckRpt.fkByZoneId.zoneType>3?mgrbean.tdZwCheckRpt.fkByZoneId.fullName.substring(mgrbean.tdZwCheckRpt.fkByZoneId.fullName.indexOf('_')+1,mgrbean.tdZwCheckRpt.fkByZoneId.fullName.length()):mgrbean.tdZwCheckRpt.fkByZoneId.zoneName}"/>

                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;">
                        <p:outputLabel value="社会信用代码："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.creditCode}"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="单位地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.address}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;">
                        <p:outputLabel value="行业类别："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByIndusTypeId.codeName}"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="经济性质："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByEconomyId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;" >
                        <p:outputLabel value="企业规模："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByCrptSizeId.codeName}"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="联系人："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkMan}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;">
                        <p:outputLabel value="联系电话："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkPhone}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="检查报告上传" style="margin-top: 10px;">
            <p:panelGrid  style="margin-bottom: 10px;width: 100%;" id="rptPanel">
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;" >
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="检测报告编号："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%; border-right: 0px;" >
                        <p:inputText value="#{mgrbean.tdZwCheckRpt.rptNo}" maxlength="50" style="width: 56.8%;"/>
                    </p:column>
                    <p:column style="border-left: 0px;" colspan="2" />
                </p:row>
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;" >
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="检测报告名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                        <p:inputText value="#{mgrbean.tdZwCheckRpt.rptName}" maxlength="100" style="width: 56.8%;"/>
                    </p:column>
                    <p:column style="text-align:right;width: 15%;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="报告日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar2"
                                    showOtherMonths="true" id="rptDate" navigator="true"
                                    yearRange="c-10:c" converterMessage="报告日期，格式输入不正确！"
                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                    value="#{mgrbean.tdZwCheckRpt.rptDate}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="工作场所名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                        <p:inputText value="#{mgrbean.tdZwCheckRpt.workName}" maxlength="500" style="width: 56.8%;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;" >
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="检测报告附件："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                        <p:commandButton value="上传"
                                         process="@this,:tabView:editForm" update="fileDialog"
                                         action="#{mgrbean.beforeUpload}"
                                         rendered="#{mgrbean.tdZwCheckRpt.filePath == null}">
                            <f:setPropertyActionListener target="#{mgrbean.uploadType}" value="0" />
                        </p:commandButton>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.tdZwCheckRpt.filePath}')"
                                         rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                        <p:spacer width="5" rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                        <p:commandButton value="删除"
                                         process="@this,:tabView:editForm"
                                         onclick="PF('DeleteDialog').show();"
                                         rendered="#{mgrbean.tdZwCheckRpt.filePath != null}">
                            <f:setPropertyActionListener target="#{mgrbean.uploadType}" value="0" />
                        </p:commandButton>
                        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteDialog">
                            <p:commandButton value="确定" action="#{mgrbean.delRptFile}" icon="ui-icon-check"
                                             update=":tabView:editForm:fileDialog"
                                             oncomplete="PF('DeleteDialog').hide();"/>
                            <p:commandButton value="取消" icon="ui-icon-close"
                                             onclick="PF('DeleteDialog').hide();"
                                             type="button"/>
                        </p:confirmDialog>
                    </p:column>
                    <p:column style="text-align:right;width: 15%;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="文审材料附件："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:commandButton value="上传"
                                         process="@this,:tabView:editForm" update="sourceFileDialog"
                                         action="#{mgrbean.beforeUpload}"
                                         rendered="#{mgrbean.tdZwCheckRpt.sourceFilePath == null}">
                            <f:setPropertyActionListener target="#{mgrbean.uploadType}" value="1" />
                        </p:commandButton>
                        <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                       rendered="#{mgrbean.tdZwCheckRpt.sourceFilePath != null}"
                                       onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                            <f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{mgrbean.tdZwCheckRpt.sourceFilePath}"/>
                            <f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{mgrbean.dowloadFileName}"/>
                            <p:fileDownload value="#{downLoadPreBean.streamedContent}" />
                        </p:commandButton>
                        <p:spacer width="5" rendered="#{mgrbean.tdZwCheckRpt.sourceFilePath != null}"/>
                        <p:commandButton value="删除"
                                         process="@this,:tabView:editForm"
                                         onclick="PF('DeleteSourceDialog').show();"
                                         rendered="#{mgrbean.tdZwCheckRpt.sourceFilePath != null}">
                            <f:setPropertyActionListener target="#{mgrbean.uploadType}" value="1" />
                        </p:commandButton>
                        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteSourceDialog">
                            <p:commandButton value="确定" action="#{mgrbean.delRptFile}" icon="ui-icon-check"
                                             update=":tabView:editForm:sourceFileDialog"
                                             oncomplete="PF('DeleteSourceDialog').hide();"/>
                            <p:commandButton value="取消" icon="ui-icon-close"
                                             onclick="PF('DeleteSourceDialog').hide();"
                                             type="button"/>
                        </p:confirmDialog>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:dialog header="附件上传" widgetVar="SourceFileDialog" id="sourceFileDialog" resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel
                                value="（支持附件格式为：图片、PDF、zip或rar压缩文件）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 30px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}" id="sourceFileUpload"
                                invalidSizeMessage="文件大小不能超过2G!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="2147483648"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf,zip,rar类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf|zip|rar)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel
                                value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}" id="fileUpload"
                                invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>

        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.backRsn}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="reasonContent" maxlength="100" readonly="#{mgrbean.readOnly}"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" rendered="#{!mgrbean.readOnly}" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         process="@this,reasonContent" oncomplete="datatableOffClick()"
                                         action="#{mgrbean.returnAction}"
                                         rendered="#{!mgrbean.readOnly}" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>