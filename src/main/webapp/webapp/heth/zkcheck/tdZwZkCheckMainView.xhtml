<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
            function generateClick() {
                document.getElementById("tabView:viewForm:generateReportId").click();
            }

            //]]>
        </script>
        <style type="text/css">

        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="现场考核管理"
                              rendered="#{null != mgrbean.pageType() and 1 == mgrbean.pageType()}"/>
                <h:outputText value="现场考核查询"
                              rendered="#{null != mgrbean.pageType() and 2 == mgrbean.pageType()}"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <!--现场考核管理-->
                <!--                <p:commandButton value="撤销" icon="ui-icon-cancel" id="submitBtn" action="#{mgrbean.beforeCancelAction}"-->
                <!--                                 rendered="#{mgrbean.showCancel and null != mgrbean.pageType() and 1 == mgrbean.pageType()}" process="@this">-->
                <!--                </p:commandButton>-->
                <!--现场考核管理-审核（质控表已提交且整改状态为已整改/待审核）-->
                <p:commandButton value="提交" icon="ui-icon-check" id="beforeImproveAudit"
                                 action="#{mgrbean.beforeImproveAudit}"
                                 rendered="#{mgrbean.viewType  eq '3'}" process="@this,auditPanel">
                </p:commandButton>
                <!--现场考核查询-整改（质控表已提交且整改状态为待整改或者已退回）-->
                <p:commandButton value="暂存" icon="ui-icon-check" id="improveSave" action="#{mgrbean.improveSave}"
                                 rendered="#{mgrbean.viewType  eq '2'}" process="@this,improvePanel">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" id="beforeImproveSumbit"
                                 action="#{mgrbean.beforeImproveSumbit}"
                                 rendered="#{mgrbean.viewType  eq '2'}" process="@this,improvePanel">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:commandButton style="display: none;" id="generateReportId" ajax="false" icon="ui-icon-print"
                                 process="@this,:tabView:editForm"
                                 onclick="PrimeFaces.monitorDownload(showStatus,hideStatus);">
                    <p:fileDownload value="#{mgrbean.getReportFile()}"/>
                </p:commandButton>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要撤销吗？" header="消息确认框" widgetVar="ConfirmCancelDialog">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.cancelAction}" icon="ui-icon-check" update=":tabView"
                                 onclick="hideTooltips();zwx_loading_start()"
                                 oncomplete="zwx_loading_stop();PF('ConfirmCancelDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmCancelDialog').hide();"
                                 type="button"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:confirmDialog message="提交后将无法撤销，是否提交？" header="消息确认框" widgetVar="ImproveAuditDialog">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.improveAudit}" icon="ui-icon-check" update=":tabView"
                                 onclick="hideTooltips();zwx_loading_start()"
                                 oncomplete="zwx_loading_stop();PF('ImproveAuditDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ImproveAuditDialog').hide();"
                                 type="button"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:confirmDialog message="提交后将无法撤销，是否提交？" header="消息确认框" widgetVar="ImproveSumbitDialog">
            <p:outputPanel style="text-align:center;">
                <p:commandButton value="确定" action="#{mgrbean.improveSumbit}" icon="ui-icon-check" update=":tabView"
                                 onclick="hideTooltips();zwx_loading_start()"
                                 oncomplete="zwx_loading_stop();PF('ImproveSumbitDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ImproveSumbitDialog').hide();"
                                 type="button"/>
            </p:outputPanel>
        </p:confirmDialog>
    </ui:define>
    <ui:define name="insertOtherContents">
        <!--整改、审核-->
        <ui:include src="tdZwZkCheckMainImproveAudit.xhtml"/>
        <!--详情-->
        <ui:include src="tdZwZkCheckMainInfoView.xhtml"/>
    </ui:define>
</ui:composition>