<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--整改情况：已整改、审核通过、已退回时显示-->
    <p:panelGrid style="margin-bottom: 10px;width:100%"
                 rendered="#{mgrbean.viewType  eq '1'}">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="整改情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="整改报告："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:commandButton value="查看（#{mgrbean.checkMain.improveCheckProves.size()}）"
                                 rendered="#{mgrbean.checkMain.improveCheckProves.size()>0}"
                                 action="#{mgrbean.toAnnexView()}"
                                 icon="ui-icon-folder-open">
                    <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="0"/>
                    <f:setPropertyActionListener target="#{mgrbean.annexType}" value="1"/>
                </p:commandButton>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="联系人："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.improveLinkman}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="联系电话："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="#{mgrbean.checkMain.improvePhone}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.checkMain.ifImproveEnd eq 2 or mgrbean.checkMain.ifImproveEnd eq 3}">
            <p:column styleClass="cs-first">
                <p:outputLabel value="审核结果："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="退回" rendered="#{mgrbean.checkMain.ifImproveEnd eq 3}"/>
                <p:outputLabel value="通过" rendered="#{mgrbean.checkMain.ifImproveEnd eq 2}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="审核日期："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.improveDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
                                       locale="cn"/>
                </p:outputLabel>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="审核机构："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="#{mgrbean.checkMain.fkByCheckOrgId.unitname}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.checkMain.ifImproveEnd eq 2 or mgrbean.checkMain.ifImproveEnd eq 3}">
            <p:column styleClass="cs-first">
                <p:outputLabel value="审核意见："/>
            </p:column>
            <p:column styleClass="cs-finally" colspan="5">
                <p:outputLabel value="#{mgrbean.checkMain.checkOpinion}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid style="margin-bottom: 10px;width:100%">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="基本信息"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="考核类型："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.fkByCheckTypeId.codeName}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="被考核机构："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.fkByOrgId.unitname}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="考核日期："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="#{mgrbean.checkMain.checkDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
                                       locale="cn"/>
                </p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="备案类别："/>
            </p:column>
            <p:column styleClass="cs-content" colspan="3">
                <p:outputLabel value="#{mgrbean.checkMain.recordStr}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="本年度是否开展工作："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifDevelop eq 0}"/>
                <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifDevelop eq 1}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid style="margin-bottom: 10px;width:100%">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="现场考核表"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row rendered="#{mgrbean.ifScore}">
            <p:column styleClass="cs-first">
                <p:outputLabel value="总分值："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.totalCheckVal}"></p:outputLabel>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="总得分："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.totalScoreVal}"></p:outputLabel>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="是否需要整改："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifNeedImprove eq 0}"></p:outputLabel>
                <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifNeedImprove eq 1}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{!mgrbean.ifScore}">
            <p:column styleClass="cs-first">
                <p:outputLabel value="评审结论："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="通过" rendered="#{mgrbean.checkMain.reviewConclusion eq 1}"></p:outputLabel>
                <p:outputLabel value="整改后通过"
                               rendered="#{mgrbean.checkMain.reviewConclusion eq 2}"></p:outputLabel>
                <p:outputLabel value="整改后复审"
                               rendered="#{mgrbean.checkMain.reviewConclusion eq 3}"></p:outputLabel>
                <p:outputLabel value="不通过" rendered="#{mgrbean.checkMain.reviewConclusion eq 4}"></p:outputLabel>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="是否需要整改："/>
            </p:column>
            <p:column styleClass="cs-finally" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.checkMain.ifNeedImprove eq 0}"></p:outputLabel>
                <p:outputLabel value="是" rendered="#{mgrbean.checkMain.ifNeedImprove eq 1}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="现场考核表："/>
            </p:column>
            <p:column styleClass="cs-con" style="padding-left:6px;">
                <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                    <p:commandButton value="下载" icon="ui-icon-arrowthick-1-s"
                                     action="#{mgrbean.preDownLoadReport}"
                                     rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.writePath}"
                                     process="@this">
                        <f:setPropertyActionListener value="#{mgrbean.checkMain.writePath}"
                                                     target="#{mgrbean.reportFilePath}"/>
                        <f:setPropertyActionListener target="#{mgrbean.annexType}" value="2"/>
                    </p:commandButton>

                </h:panelGrid>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="现场考核意见书："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" colspan="3">
                <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                    <p:commandButton value="下载" icon="ui-icon-arrowthick-1-s"
                                     action="#{mgrbean.preDownLoadReport}"
                                     rendered="#{null ne mgrbean.checkMain and null ne mgrbean.checkMain.checkTablePath}"
                                     process="@this">
                        <f:setPropertyActionListener value="#{mgrbean.checkMain.checkTablePath}"
                                                     target="#{mgrbean.reportFilePath}"/>
                        <f:setPropertyActionListener target="#{mgrbean.annexType}" value="3"/>
                    </p:commandButton>
                </h:panelGrid>
            </p:column>
        </p:row>

        <p:row rendered="#{mgrbean.checkMain.checkTables.size()>0}">
            <p:column colspan="6">
                <p:panelGrid style="width:100%">
                    <p:row>
                        <p:column style="width:256px;height: 20px;text-align:center;" styleClass="ui-state-default">
                            <p:outputLabel value="考核表"></p:outputLabel>
                        </p:column>
                        <p:column style="width:288px;text-align:center;" styleClass="ui-state-default">
                            <p:outputLabel value="考核项目"></p:outputLabel>
                        </p:column>
                        <p:column style="width:100px;text-align:center;" styleClass="ui-state-default"
                                  rendered="#{mgrbean.ifScore}">
                            <p:outputLabel value="分值"></p:outputLabel>
                        </p:column>
                        <p:column style="width:100px;text-align:center;" styleClass="ui-state-default"
                                  rendered="#{mgrbean.ifScore}">
                            <p:outputLabel value="实得分"></p:outputLabel>
                        </p:column>
                        <p:column style="text-align:center;" styleClass="ui-state-default">
                            <p:outputLabel value="存在问题"></p:outputLabel>
                        </p:column>
                        <p:column style="width: 80px;text-align:center;" styleClass="ui-state-default">
                            <p:outputLabel value="状态"></p:outputLabel>
                        </p:column>
                        <p:column style="text-align:center;width: 120px;" styleClass="ui-state-default">
                            <p:outputLabel value="操作"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <c:forEach items="#{mgrbean.checkMain.checkTables}" var="table" varStatus="tableIndex">
                        <c:forEach items="#{table.checkItems}" var="item" varStatus="itemIndex">
                            <p:row>
                                <!-- 考核项目 -->
                                <p:column rendered="#{itemIndex.index==0}" rowspan="#{table.checkItems.size()}">
                                    <p:outputLabel value="#{table.fkByCheckTableId.checkName}"></p:outputLabel>
                                </p:column>
                                <p:column style="height:28px;">
                                    <p:outputLabel value="#{item.fkByItemId.codeName}"></p:outputLabel>
                                </p:column>
                                <p:column style="height:28px;text-align:center;" rendered="#{mgrbean.ifScore}">
                                    <p:outputLabel value="#{item.checkVal}"
                                                   rendered="#{item.checkVal != null}"></p:outputLabel>
                                    <p:outputLabel value="/" rendered="#{item.checkVal == null}"></p:outputLabel>
                                </p:column>
                                <p:column style="height:28px;text-align:center;" rendered="#{mgrbean.ifScore}">
                                    <p:outputLabel value="#{item.scoreVal}"
                                                   rendered="#{item.scoreVal != null and  mgrbean.checkMain.ifDevelop eq 1}"></p:outputLabel>
                                    <p:outputLabel value="/"
                                                   rendered="#{item.checkVal == null and item.scoreVal == null  and  mgrbean.checkMain.ifDevelop eq 1 }"></p:outputLabel>
                                    <p:outputLabel value="/"
                                                   rendered="#{mgrbean.checkMain.ifDevelop eq 0}"></p:outputLabel>
                                </p:column>
                                <!-- 存在问题 -->
                                <p:column>
                                    <h:outputText id="deductRsn#{tableIndex.index}#{itemIndex.index}"
                                                  value="#{item.deductRsn}" styleClass="zwx-tooltip"/>
                                    <p:tooltip for="deductRsn#{tableIndex.index}#{itemIndex.index}"
                                               style="width:450px;">
                                        <p:outputLabel value="#{item.deductRsn2}" escape="false"></p:outputLabel>
                                    </p:tooltip>
                                </p:column>
                                <!-- 状态 -->
                                <p:column style="text-align:center;" rendered="#{itemIndex.index==0}"
                                          rowspan="#{table.checkItems.size()}">
                                    <p:outputLabel value="待提交"
                                                   rendered="#{table.stateMark eq 0}"></p:outputLabel>
                                    <p:outputLabel value="已提交"
                                                   rendered="#{table.stateMark eq 1}"></p:outputLabel>
                                    <p:outputLabel value="无需考核"
                                                   rendered="#{table.stateMark eq 2}"></p:outputLabel>
                                </p:column>
                                <p:column rendered="#{itemIndex.index==0}" rowspan="#{table.checkItems.size()}">
                                    <p:spacer width="5"></p:spacer>
                                    <p:commandLink value="查看" process="@this" action="#{mgrbean.modCheckIndex}"
                                                   onclick="hideTooltip();"
                                                   rendered="#{table.stateMark eq 0 or table.stateMark eq 1}">
                                        <f:setPropertyActionListener target="#{mgrbean.curCheckTable}"
                                                                     value="#{table}"/>
                                    </p:commandLink>
                                </p:column>
                            </p:row>
                        </c:forEach>
                    </c:forEach>
                </p:panelGrid>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid id="checkContentPanel" style="margin-bottom: 10px;width:100%">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left;" colspan="2">
                    <p:outputLabel value="考核证明材料"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column style="text-align:right;width: 260px;height: 38px;">
                <p:outputLabel value="考核证明材料："/>
            </p:column>
            <p:column style="padding-left:6px;text-align:left;">
                <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                    <p:commandButton value="查看附件（#{mgrbean.checkMain.materialCheckProves.size()}）"
                                     rendered="#{mgrbean.checkMain.materialCheckProves.size()>0}"
                                     action="#{mgrbean.toAnnexView()}"
                                     icon="ui-icon-folder-open">
                        <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="0"/>
                        <f:setPropertyActionListener target="#{mgrbean.annexType}" value="0"/>
                    </p:commandButton>
                </h:panelGrid>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 附件预览 -->
    <p:dialog id="annexViewDialogView" widgetVar="AnnexViewDialog"
              styleClass="annex_view_dialog" resizable="false" header="查看附件"
              width="900" modal="true">
        <p:outputPanel id="annexViewPanel">
            <div style="width:100%;height:500px;">
                <table class="annex_content_table">
                    <tr>
                        <td class="annex_link">
                            <p:commandLink styleClass="annex_pre"
                                           update="annexViewPanel" process="@this"
                                           rendered="#{mgrbean.checkProveTmp.preIndex!=null}"
                                           action="#{mgrbean.toAnnexView}">
                                <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}"
                                                             value="#{mgrbean.checkProveTmp.preIndex}"/>
                            </p:commandLink>
                        </td>
                        <td style="text-align:center">
                            <h:outputText value="#{mgrbean.fileTemp.filePath}" escape="false"/>
                        </td>
                        <td class="annex_link">
                            <p:commandLink styleClass="annex_next" update="annexViewPanel" process="@this"
                                           rendered="#{mgrbean.checkProveTmp.nextIndex!=null}"
                                           action="#{mgrbean.toAnnexView}">
                                <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}"
                                                             value="#{mgrbean.checkProveTmp.nextIndex}"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </div>
            <p:outputPanel style="position: absolute;bottom: 0;width: 100%;">
                <table class="annex_info_table">
                    <tr>
                        <td>
                            附件名称：
                            <h:outputText value="#{mgrbean.fileTemp.fileName}" escape="false"/>
                            <p:commandLink value="删除" style="padding-left:10px;"
                                           action="#{mgrbean.delFileAction}"
                                           rendered="#{mgrbean.viewType  eq '2' and mgrbean.annexType eq 1}"
                                           update="annexViewPanel,improvePanel"
                            >
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:outputPanel>
        </p:outputPanel>
    </p:dialog>
</ui:composition>