<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.InquiryExamStatisticsBean"-->
    <ui:param name="mgrbean" value="#{inquiryExamStatisticsBean}"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            //<![CDATA[
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }

            function getDownloadFileClick() {
                document.getElementById("mainForm:downloadFileBtn").click();
            }

            //]]>
        </script>
        <style>
            .myCalendar2 input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质控考核情况查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"
                                 onclick="zwx_loading_start_pub();" oncomplete="zwx_loading_stop_pub();datatableOffClick();"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportDataBtn"
                                 onclick="getDownloadFileClick();"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start_pub, zwx_loading_stop_pub);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:38px;">
                <h:outputText value="考核类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;width:250px;">
                <p:selectOneMenu value="#{mgrbean.examTypeRid}" style="width: 190px;">
                    <f:selectItems value="#{mgrbean.examTypeSimpleCodeList}" var="examType"
                                   itemLabel="#{examType.codeName}"
                                   itemValue="#{examType.rid}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:38px;">
                <font color="red">*</font>
                <h:outputText value="考核日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchExamBeginDate}"
                                              endDate="#{mgrbean.searchExamEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="汇总维度："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;" colspan="5">
                <p:selectOneRadio value="#{mgrbean.statisticalDimension}" style="width:200px;">
                    <f:selectItems value="#{mgrbean.statisticalDimensionList}"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertContent">
        <p:panel id="dataTable">
            <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;"
                         rendered="#{mgrbean.statisticalDimension == null or mgrbean.statisticalDimension != '2'}">
                <f:facet name="header">
                    <p:row>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;width: 300px;">
                            <p:outputLabel value="考核二级指标"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="考核内容序号"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="参与考核机构数"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="“符合”的机构数"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="“基本符合”的机构数"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;">
                            <p:outputLabel value="各项考核指标符合率（%）"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <c:forEach var="itm" items="#{mgrbean.dataTableList}" varStatus="R">
                    <p:row>
                        <p:column style="padding-left:8px;text-align: center;" rendered="#{itm[6] == '1'}"
                                  rowspan="#{itm[7]}">
                            <h:outputText value="#{itm[0]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[1]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[2]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[3]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[4]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[5]}"/>
                        </p:column>
                    </p:row>
                </c:forEach>
                <p:row rendered="#{mgrbean.dataTableListCount == 0}">
                    <p:column style="height: 35px;text-align:left;" colspan="6">
                        <p:outputLabel value="没有您要找的数据！"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;"
                         rendered="#{mgrbean.statisticalDimension == '2'}">
                <f:facet name="header">
                    <p:row>
                        <p:column styleClass="ui-state-default" rowspan="2"
                                  style="height:20px;text-align:center;width: 300px;">
                            <p:outputLabel value="地区"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" rowspan="2" style="height:20px;text-align:center;width: 400px;">
                            <p:outputLabel value="机构名称"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" rowspan="2"
                                  style="height:20px;text-align:center;width: 250px;">
                            <p:outputLabel value="总分"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" colspan="2"
                                  style="height:20px;text-align:center;">
                            <p:outputLabel value="符合情况"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" rowspan="2"
                                  style="height:20px;text-align:center;width: 250px;">
                            <p:outputLabel value="得分"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;width: 250px;">
                            <p:outputLabel value="符合"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="height:20px;text-align:center;width: 250px;">
                            <p:outputLabel value="基本符合"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <c:forEach var="itm" items="#{mgrbean.dataTableList}" varStatus="R">
                    <p:row>
                        <p:column style="padding-left:8px;text-align: center;" rendered="#{itm[6] == '1'}"
                                  rowspan="#{itm[7]}">
                            <h:outputText value="#{itm[0]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: left;height: 25px;">
                            <h:outputText value="#{itm[1]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[2]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[3]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[4]}"/>
                        </p:column>
                        <p:column styleClass="#{R.index % 2 == 1?'ui-datatable-odd':''}"
                                  style="padding-left:8px;text-align: center;height: 25px;">
                            <h:outputText value="#{itm[5]}"/>
                        </p:column>
                    </p:row>
                </c:forEach>
                <p:row rendered="#{mgrbean.dataTableListCount == 0}">
                    <p:column style="height: 35px;text-align:left;" colspan="6">
                        <p:outputLabel value="没有您要找的数据！"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:panel>
    </ui:define>

</ui:composition>