<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <ui:define name="insertEditScripts"></ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制考核结论录入" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.cancelAction}" process="@this"
                                 update=":tabView">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <p:row>
                <p:column style="text-align:right;width: 220px;height: 32px;" >
                    <p:outputLabel value="考核类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 330px;" >
                    <p:outputLabel value="#{null == mgrbean.checkConclusion.fkByCheckTypeId ? null : mgrbean.checkConclusion.fkByCheckTypeId.codeName}"  />
                </p:column>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{null == mgrbean.checkConclusion.fkByUnitId ? null : mgrbean.checkConclusion.fkByUnitId.unitname}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 32px;" >
                    <p:outputLabel value="备案类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:outputLabel value="#{mgrbean.recordCategory}" id="recordCategory" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 32px;" >
                    <p:outputLabel value="考核日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3">
                    <h:outputText value="#{mgrbean.checkConclusion.checkDate}" >
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left;" colspan="4">
                        <p:outputLabel value="质量考核结果"/>
                    </p:column>
                </p:row>
            </f:facet>
            <c:forEach items="#{mgrbean.conclusionItemSimpleCodeViewList}" var="itm">
                <p:row>
                    <p:column style="text-align:right;width: 220px;height: 32px;" >
                        <p:outputLabel value="#{itm.codeName}"/>
                        <p:outputLabel value="："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:11px;vertical-align: center;" colspan="3" >
                        <c:forEach items="#{mgrbean.scoreRstSimpleCodeViewList}" var="rstItm">
                            <p:outputLabel value="#{rstItm.codeName}："/>
                            <c:forEach items="#{mgrbean.checkItemRstList}" var="curRst">
                                <c:if test="#{itm.rid == curRst.fkByItemId.rid and rstItm.rid == curRst.fkByRstId.rid}">
                                    <p:outputLabel value="#{curRst.nums}" />
                                </c:if>
                            </c:forEach>
                            <p:outputLabel value="项" style="margin-right: 11px;margin-left: 3px;" />
                        </c:forEach>
                    </p:column>
                </p:row>
            </c:forEach>
            <p:row>
                <p:column style="text-align:right;width: 220px;height: 32px;" >
                    <p:outputLabel value="结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;vertical-align: center;" colspan="3" >
                    <p:outputLabel value="合格" rendered="#{1 == mgrbean.checkConclusion.checkRst}" />
                    <p:outputLabel value="不合格" rendered="#{0 == mgrbean.checkConclusion.checkRst}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="其他："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.others}" readonly="true"
                                     rows="3" autoResize="false" maxlength="1000"
                                     style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="总结："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.summary}" readonly="true"
                                     rows="3" autoResize="false" maxlength="1000"
                                     style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="4">
                        <p:outputLabel value="质量考核结论及意见"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;height: 32px;width: 220px;" >
                    <p:outputLabel value="现场质量考核结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:outputLabel value="#{null == mgrbean.checkConclusion.fkByConclusionId ? null : mgrbean.checkConclusion.fkByConclusionId.codeName}"  />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.checkConclusion.fkByConclusionId.extendS1 == 1}">
                <p:column style="text-align:right;height: 32px;width: 220px;" >
                    <p:outputLabel id="unPassItem">
                        <p:outputLabel value="未通过考核的备案项目："/>
                    </p:outputLabel>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:outputLabel value="#{mgrbean.unPassItem}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 32px;width: 220px;" >
                    <p:outputLabel value="被考核机构确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 330px;" >
                    <p:outputLabel value="不确认" rendered="#{0 == mgrbean.checkConclusion.ifConfirm}" />
                    <p:outputLabel value="确认" rendered="#{1 == mgrbean.checkConclusion.ifConfirm}" />
                </p:column>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="确认附件："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.checkConclusion.filePath}')"
                                         rendered="#{mgrbean.checkConclusion.filePath != null}" />
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="考核专家确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.expertAdv}" readonly="true"
                                     rows="3" autoResize="false" maxlength="1000"
                                     style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 220px;" >
                    <p:outputLabel value="考核组组长确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3" >
                    <p:inputTextarea value="#{mgrbean.checkConclusion.leaderAdv}" readonly="true"
                                     rows="3" autoResize="false" maxlength="1000"
                                     style="resize: none;width: 778px;height: 60px;margin-top: 3px;" />
                </p:column>
            </p:row>
        </p:panelGrid>
    </ui:define>
</ui:composition>