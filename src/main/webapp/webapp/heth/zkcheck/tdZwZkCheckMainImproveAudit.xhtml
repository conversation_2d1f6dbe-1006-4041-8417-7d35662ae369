<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--整改-->
    <p:panelGrid style="margin-bottom: 10px;width:100%" rendered="#{mgrbean.viewType  eq '2'}" id="improvePanel">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="整改情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="整改报告：" styleClass="cs-required"/>
            </p:column>
            <p:column styleClass="cs-con" style="padding-left:6px;">
                <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                    <p:commandButton value="上传" action="#{mgrbean.uploadFile}"
                                     update=":tabView:viewForm:fileDialog"
                                     icon="ui-icon-arrowthickstop-1-n">
                        <f:setPropertyActionListener target="#{mgrbean.annexType}" value="1"/>
                    </p:commandButton>
                    <p:commandButton value="查看（#{mgrbean.checkMain.improveCheckProves.size()}）"
                                     rendered="#{mgrbean.checkMain.improveCheckProves.size()>0}"
                                     action="#{mgrbean.toAnnexView()}"
                                     icon="ui-icon-folder-open">
                        <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="0"/>
                        <f:setPropertyActionListener target="#{mgrbean.annexType}" value="1"/>
                    </p:commandButton>
                </h:panelGrid>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="联系人：" styleClass="cs-required"/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:inputText value="#{mgrbean.checkMain.improveLinkman}" maxlength="50"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="联系电话：" styleClass="cs-required"/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:inputText value="#{mgrbean.checkMain.improvePhone}" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.checkMain.ifImproveEnd eq '2' or mgrbean.checkMain.ifImproveEnd eq '3'}">
            <p:column styleClass="cs-first">
                <p:outputLabel value="审核结果："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="退回" rendered="#{mgrbean.checkMain.ifImproveEnd eq 3}"/>
                <p:outputLabel value="通过" rendered="#{mgrbean.checkMain.ifImproveEnd eq 2}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="审核日期："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.improveDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
                                       locale="cn"/>
                </p:outputLabel>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="审核机构："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="#{mgrbean.checkMain.fkByCheckOrgId.unitname}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.checkMain.ifImproveEnd eq '2' or mgrbean.checkMain.ifImproveEnd eq '3'}">
            <p:column styleClass="cs-first">
                <p:outputLabel value="审核意见："/>
            </p:column>
            <p:column styleClass="cs-finally" colspan="5">
                <p:outputLabel value="#{mgrbean.checkMain.checkOpinion}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--审核-->
    <p:panelGrid style="margin-bottom: 10px;width:100%" rendered="#{mgrbean.viewType  eq '3'}" id="auditPanel">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="整改情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="整改报告："/>
            </p:column>
            <p:column styleClass="cs-con" style="padding-left:6px;">
                <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                    <p:commandButton value="查看（#{mgrbean.checkMain.improveCheckProves.size()}）"
                                     rendered="#{mgrbean.checkMain.improveCheckProves.size()>0}"
                                     action="#{mgrbean.toAnnexView()}"
                                     icon="ui-icon-folder-open">
                        <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="0"/>
                        <f:setPropertyActionListener target="#{mgrbean.annexType}" value="1"/>
                    </p:commandButton>
                </h:panelGrid>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="联系人："/>
            </p:column>
            <p:column styleClass="cs-content">
                <p:outputLabel value="#{mgrbean.checkMain.improveLinkman}"/>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="联系电话："/>
            </p:column>
            <p:column styleClass="cs-finally">
                <p:outputLabel value="#{mgrbean.checkMain.improvePhone}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-first">
                <p:outputLabel value="审核结果：" styleClass="cs-required"/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectOneRadio value="#{mgrbean.checkMain.ifImproveEnd}" style="width:120px;">
                    <f:selectItem itemLabel="通过" itemValue="2"/>
                    <f:selectItem itemLabel="退回" itemValue="3"/>
                    <p:ajax event="change" process="@this,auditPanel" update="auditPanel"/>
                </p:selectOneRadio>
            </p:column>
            <p:column styleClass="cs-title">
                <p:outputLabel value="审核意见："
                               styleClass="#{mgrbean.checkMain.ifImproveEnd eq '3' ? 'cs-required' :''}"/>
            </p:column>
            <p:column styleClass="cs-finally" colspan="3">
                <p:inputText value="#{mgrbean.checkMain.checkOpinion}" maxlength="1000" style="width:80%;"/>
            </p:column>
        </p:row>
    </p:panelGrid>

    <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true" width="800">
        <table width="100%">
            <tr>
                <td style="text-align: right;">
                    <p:outputLabel value="（支持附件格式为：PDF）" styleClass="blueColorStyle"
                                   style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload requiredMessage="请选择要上传的文件！" multiple="true" id="fileUpload"
                                  label="文件选择" fileUploadListener="#{mgrbean.fileUpload}"
                                  invalidSizeMessage="文件大小不能超过50M!"
                                  validatorMessage="上传出错啦，请重新上传！"
                                  invalidFileMessage="无效的文件类型！只能上传pdf类型文件"
                                  allowTypes="/(\.|\/)(pdf)$/"
                                  style="width:760px;" previewWidth="120" cancelLabel="取消" update="@this"
                                  uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="52428800"/>
                </td>
            </tr>
        </table>
    </p:dialog>
</ui:composition>