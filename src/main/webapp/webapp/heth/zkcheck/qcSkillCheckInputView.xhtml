<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:op="http://java.sun.com/jsf/html">

    <h:form id="editForm3">
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editTitleGrid2">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="质量控制技能考核录入" />
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="buttonPanelId">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="submitBtn" action="#{mgrbean.cancelAction}"
                                 oncomplete="windowScrollTop();"
                                 update=":tabView" process="@this">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="buttonPanelId" />
        <!-- 具体数据展示 -->
        <p:outputPanel>
            <p:panelGrid id="baseInfo"  style="margin-bottom: 10px;width:100%;margin-top: 10px;" >
                <p:row>
                    <p:column style="text-align:right;width: 250px;height: 38px;">
                        <p:outputLabel value="考核类型："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px;" >
                        <p:outputLabel value="#{mgrbean.checkTechMain.fkByCheckTypeId.codeName}" style="margin-left: 5px;"/>
                    </p:column>
                    <p:column style="text-align:right;width: 250px;">
                        <p:outputLabel value="机构名称："/>
                    </p:column>
                    <p:column style="text-align:left;" >
                        <div style="display: table-cell;margin-top: 3px;width: 253px;">
                            <p:outputLabel value="#{mgrbean.checkTechMain.fkByUnitId.unitname}" style="margin-left: 5px;"/>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="备案类别："/>
                    </p:column>
                    <p:column style="text-align:left;" colspan="3">
                        <div style="display: table-cell;padding-left: 5px;width:810px;">
                            <p:outputLabel value="#{mgrbean.checkTechMain.filiTypeName}"/>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核日期："/>
                    </p:column>
                    <p:column style="text-align:left;" colspan="3">
                        <p:outputLabel value="#{mgrbean.checkTechMain.checkDate}" style="margin-left: 5px;">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
                                               locale="cn" />
                        </p:outputLabel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="被考核人员：" style="line-height: 12px;" />
                    </p:column>
                    <p:column colspan="3">
                        <p:outputPanel  styleClass="writeSortInfo" style="margin-left: 5px;" id="careeHistory">
                            <div style="display: table-row;margin-top: 5px;">
                                <div style="display: table-cell;vertical-align: middle;">
                                    <p:dataTable id="techPsnTable" paginatorPosition="bottom"
                                                 value="#{mgrbean.checkTechPsnList}" style="padding-top: 5px;width: 814px;"
                                                 widgetVar="techPsnTable" var="techPsn"
                                                 emptyMessage="没有数据！" rowIndexVar="R">
                                        <p:column headerText="姓名" style="width:150px;text-align:center;">
                                            <p:outputLabel value="#{techPsn.psnName}" />
                                        </p:column>
                                        <p:column headerText="职称" style="width:150px;text-align:center;">
                                            <p:outputLabel value="#{null eq techPsn.fkByTitleId or null eq techPsn.fkByTitleId.rid?'':techPsn.fkByTitleId.codeName}" />
                                        </p:column>
                                        <p:column headerText="人员属性" style="text-align:center;width: 150px;" >
                                            <p:outputLabel value="#{null eq techPsn.fkByPsnTypeId or null eq techPsn.fkByPsnTypeId.rid?'':techPsn.fkByPsnTypeId.codeName}" />
                                        </p:column>
                                    </p:dataTable>
                                </div>
                            </div>
                        </p:outputPanel >
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核地点："/>
                    </p:column>
                    <p:column style="text-align:left;"  colspan="3">
                        <div style="display: table-cell;padding-left: 7px;margin-top: 3px;width: 811px;">
                            <p:outputLabel value="#{mgrbean.checkTechMain.checkAddr}" />
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核内容："/>
                    </p:column>
                    <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                        <div style="display: table-cell;padding-left: 7px;margin-top: 3px;width: 820px;">
                            <p:inputTextarea value="#{mgrbean.checkTechMain.checkContent}" readonly="true"
                                             style="width:805px;height: 70px;resize: none;" autoResize="false"
                                             maxlength="1000"/>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核过程及结果描述："/>
                    </p:column>
                    <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                        <div style="display: table-cell;padding-left: 7px;margin-top: 3px;width: 820px;">
                            <p:inputTextarea value="#{mgrbean.checkTechMain.checkDesc}" readonly="true"
                                             style="width:805px;height: 70px;resize: none;" autoResize="false"
                                             maxlength="1000"/>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核结果："/>
                    </p:column>
                    <p:column style="text-align:left;" >
                        <p:outputLabel value="合格" rendered="#{mgrbean.checkTechMain.checkRst==1}" style="margin-left: 5px;"/>
                        <p:outputLabel value="不合格" rendered="#{mgrbean.checkTechMain.checkRst==0}" style="margin-left: 5px;"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="考核专家："/>
                    </p:column>
                    <p:column style="text-align:left;" >
                        <div style="display: table-cell;padding-left: 5px;width: 260px;">
                            <p:outputLabel value="#{mgrbean.checkTechMain.checkExperts}" />
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px; ">
                        <h:outputText value="被考核机构确认意见："/>
                    </p:column>
                    <p:column style="text-align:left;">
                        <p:outputLabel value="确认" rendered="#{mgrbean.checkTechMain.ifConfirm==1}" style="margin-left: 5px;"/>
                        <p:outputLabel value="不确认" rendered="#{mgrbean.checkTechMain.ifConfirm==0}" style="margin-left: 5px;"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="确认附件："/>
                    </p:column>
                    <p:column style="text-align:left;">
                        <p:outputPanel id="annexPanel">
                            <p:spacer width="5" rendered="#{null ne mgrbean.checkTechMain.filePath}"/>
                            <p:commandButton value="查看" rendered="#{null ne mgrbean.checkTechMain.filePath}"
                                             action="#{mgrbean.toAnnexView()}">
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核专家确认意见："/>
                    </p:column>
                    <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                        <div style="display: table-cell;padding-left: 7px;margin-top: 3px;width: 820px;">
                            <p:inputTextarea value="#{mgrbean.checkTechMain.expertAdv}" readonly="true"
                                             style="width:805px;height: 70px;resize: none;" autoResize="false"
                                             maxlength="1000"/>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 38px;">
                        <p:outputLabel value="考核组组长确认意见："/>
                    </p:column>
                    <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                        <div style="display: table-cell;padding-left: 7px;margin-top: 3px;width: 820px;">
                            <p:inputTextarea value="#{mgrbean.checkTechMain.leaderAdv}" readonly="true"
                                             style="width:805px;height: 70px;resize: none;" autoResize="false"
                                             maxlength="1000"/>
                        </div>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:outputPanel>
    </h:form>
</ui:composition>