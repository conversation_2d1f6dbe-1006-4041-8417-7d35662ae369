<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportProvincialReviewBean"-->
    <ui:param name="mgrbean" value="#{bhkReportProvincialReviewBean}"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputStylesheet library="css" name="ui-cs.css"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 修改页面 -->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/bhkExpertProvincialReviewEdit.xhtml"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="省级复核"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
                <p:inputText style="visibility: hidden;width: 0;"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputLabel value="地区："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="0" onchange="onSearchNodeSelect()"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="unitPanel"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="体检机构："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <p:outputPanel style="display: flex;align-items: center;" id="unitPanel">
                    <p:inputText id="unitName"
                                 value="#{mgrbean.searchUnitName}"
                                 style="width: 180px;cursor: pointer; margin-left: 0;"
                                 onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                 readonly="true"/>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="selUnitLink"
                                   style="position: relative;left: -20px !important;"
                                   action="#{mgrbean.selUnitAction}" process="@this">
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                resetValues="true" process="@this" update="unitName"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -13px !important;"
                                   process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                    </p:commandLink>
                </p:outputPanel>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="抽取类别："/>
            </p:column>
            <p:column style="padding-left: 4px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkType}">
                    <f:selectItems value="#{mgrbean.bhkTypeList}" var="bhkType"
                                   itemLabel="#{bhkType.codeName}" itemValue="#{bhkType.rid}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputLabel value="抽取日期："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv">
                <zwx:CalendarDynamicLimitComp styleClass="myCalendar1"
                                              startDate="#{mgrbean.searchSDate}"
                                              endDate="#{mgrbean.searchEDate}"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="职业健康检查结论："/>
            </p:column>
            <p:column style="padding-left: 3px !important;" styleClass="cs-scv">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
                                        selectedIds="#{mgrbean.searchSelBhkrstIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="180" height="200"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="padding-left: 4px !important;">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItem itemLabel="待复核" itemValue="6"/>
                    <f:selectItem itemLabel="复核完成" itemValue="7"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" style="width: 160px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="体检机构名称" style="width: 250px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="抽取类别" style="text-align: center;width: 70px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="姓名" style="text-align: center;width: 70px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="职业健康检查结论" style="text-align: center;width: 120px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="专家阅片/判定结论" style="text-align: center;width: 130px;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="专家建议体检结论" style="text-align: center;width: 120px;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="省级复核阅片/判定结论" style="text-align: center;width: 150px;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="省级复核体检结论修改意见" style="text-align: center;width: 170px;">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="抽取日期" style="text-align: center;width: 80px;">
            <h:outputText value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="复核日期" style="text-align: center;width: 80px;">
            <h:outputText value="#{itm[11]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="状态" style="text-align: center;width: 70px;">
            <h:outputText value="#{itm[12]}"/>
        </p:column>
        <p:column headerText="操作" style="">
            <p:commandLink value="复核" rendered="#{itm[13] eq 6}" resetValues="true"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.modInitAction}" onclick="hideTooltips();">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[13] eq 7}"
                           action="#{mgrbean.modInitAction}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>