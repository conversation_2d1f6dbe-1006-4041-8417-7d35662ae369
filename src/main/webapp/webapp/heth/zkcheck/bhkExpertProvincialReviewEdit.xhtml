<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportProvincialReviewBean"-->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="省级复核"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel id="buttons">
            <p:outputPanel style="display: flex;" styleClass="zwx_toobar_42" id="sticky">
                <h:panelGrid columns="9" style="border-color:transparent;padding:0;" id="headButton">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="提交" icon="ui-icon-check"
                                     rendered="#{not mgrbean.ifView and not empty mgrbean.extractType}"
                                     process="@this,:tabView:editForm" action="#{mgrbean.preSubmitAction}"/>
                    <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                     process="@this" update=":tabView" action="#{mgrbean.backAction}">
                    </p:commandButton>
                    <p:inputText style="visibility: hidden;width: 0"/>
                </h:panelGrid>
            </p:outputPanel>
            <p:sticky target="sticky"/>
        </p:outputPanel>
        <!--提交弹出框-->
        <p:confirmDialog widgetVar="ConfirmDialog" id="confirmDialog"
                         header="消息确认框" message="提交后将无法撤销，是否提交？">
            <div style="text-align: center !important;">
                <p:commandButton value="确定" action="#{mgrbean.submitAction}"
                                 process="@this,:tabView:editForm"
                                 icon="ui-icon-check" oncomplete="PF('ConfirmDialog').hide();"
                                 update="@this,:tabView"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
            </div>
        </p:confirmDialog>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="体检信息" style="margin-top: 5px;margin-bottom: 5px;" toggleable="false">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="basicPanel">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;">
                        <h:outputText value="体检机构地区："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.zoneName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="体检机构名称："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.fkByOrgId.unitName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="抽取类别："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExtractTypeId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="姓名："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.personName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="身份证号："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.idcEncrypted}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">

                        <h:outputText value="接害工龄（年）："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.tchbadrsntim}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="职业健康检查结论："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="#{empty mgrbean.extractType?'5':'1'}">
                        <h:outputText value="#{mgrbean.extractBhk.fkByBhkRstId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" rendered="#{not empty mgrbean.extractType}">
                        <h:outputText
                                value="#{mgrbean.extractType eq 1 ? '职业健康检查胸片结论' : '职业健康检查听力检测结论'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv" rendered="#{not empty mgrbean.extractType}">
                        <h:outputText value="#{mgrbean.extractBhk.fkByChestRstId.codeName}"
                                      rendered="#{mgrbean.extractType eq 1}"/>
                        <h:outputText value="#{mgrbean.extractBhk.fkByHearRstId.codeName}"
                                      rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" rendered="#{not empty mgrbean.extractType}">
                        <h:outputText
                                value="#{mgrbean.extractType eq 1 ? '胸片号' : '听力图谱编号'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv" rendered="#{not empty mgrbean.extractType}">
                        <h:outputText styleClass="cs-break-word" value="#{mgrbean.extractBhk.chestNo}"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{not empty mgrbean.extractType}">
                    <p:column styleClass="cs-scl-h" style="text-align: right;">
                        <h:outputText value="体检报告附件："/>
                    </p:column>
                    <p:column styleClass="cs-scv" style="text-align: left;">
                        <p:outputPanel id="bhkRptPanel">
                            <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                             rendered="#{not empty mgrbean.extractBhk.bhkRptPath and not empty mgrbean.ifShowFileDownload and mgrbean.ifShowFileDownload}"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                             value="#{mgrbean.extractBhk.bhkRptPath}"/>
                                <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                             value="#{mgrbean.bhkRptFileName}"/>
                                <p:fileDownload value="#{mgrbean.streamedContent}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.extractBhk.bhkRptPath}')"
                                             rendered="#{not empty mgrbean.extractBhk.bhkRptPath and not empty mgrbean.ifShowFileDownload and not mgrbean.ifShowFileDownload}"/>
                        </p:outputPanel>
                    </p:column>
                    <p:column styleClass="cs-scl" style="text-align: right;">
                        <h:outputText
                                value="#{mgrbean.extractType eq 1 ? '胸片附件' : '听力图谱附件'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="3">
                        <p:outputPanel id="chestPanel">
                            <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                             rendered="#{not empty mgrbean.ifShowChest and not empty mgrbean.extractBhk.chestPath and mgrbean.ifShowChest}"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                             value="#{mgrbean.extractBhk.chestPath}"/>
                                <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                             value="#{mgrbean.chestFileName}"/>
                                <p:fileDownload value="#{mgrbean.streamedContent}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.extractBhk.chestPath}')"
                                             rendered="#{not empty mgrbean.ifShowChest and not empty mgrbean.extractBhk.chestPath and not mgrbean.ifShowChest}"/>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="判定信息" style="margin-top: 5px;margin-bottom: 5px;" toggleable="false">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5">
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="专家阅片结论："
                                      rendered="#{mgrbean.extractType eq 1}"/>
                        <h:outputText value="专家判定结论："
                                      rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExpertChestId.codeName}"
                                      rendered="#{mgrbean.extractType eq 1}"/>
                        <h:outputText value="#{mgrbean.extractBhk.fkByExpertHearRstId.codeName}"
                                      rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="专家建议体检结论："/>
                    </p:column>
                    <p:column styleClass="cs-scv"
                              colspan="#{mgrbean.extractType eq 1 ? '1' : '3'}">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExpertRstId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl"
                              rendered="#{mgrbean.extractType eq 1}">
                        <h:outputText value="胸片质量："/>
                    </p:column>
                    <p:column styleClass="cs-scv" rendered="#{mgrbean.extractType eq 1}">
                        <h:outputText value="#{mgrbean.extractBhk.fkByChestLevelId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;">
                        <h:outputText value="填报单位："/>
                    </p:column>
                    <p:column styleClass="cs-scv" style="width: 20%;">
                        <h:outputText styleClass="cs-break-word" value="#{mgrbean.extractBhk.checkUnitName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" style="width: 12%;">
                        <h:outputText value="填报人："/>
                    </p:column>
                    <p:column styleClass="cs-scv" style="width: 20%;">
                        <h:outputText styleClass="cs-break-word" value="#{mgrbean.extractBhk.checkPsn}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" style="width: 12%;">
                        <h:outputText value="填报日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputLabel value="#{mgrbean.extractBhk.checkDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </h:outputLabel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="联系电话："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="5">
                        <h:outputText value="#{mgrbean.extractBhk.checkLinktel}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="省级复核结果" style="margin-top: 5px;margin-bottom: 5px;" toggleable="false"
                    rendered="#{not empty mgrbean.extractType}">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="provincialReviewPanel"
                         rendered="#{not mgrbean.ifView}">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;">
                        <h:outputText value="省级复核阅片结论：" styleClass="cs-required"
                                      rendered="#{mgrbean.extractType eq 1}"/>
                        <h:outputText value="省级复核判定结论：" styleClass="cs-required"
                                      rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <p:selectOneMenu style="width: 188px;"
                                         value="#{mgrbean.extractBhk.provChestId}"
                                         rendered="#{mgrbean.extractType eq 1}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.expertChestList}" var="itm"
                                           itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                        </p:selectOneMenu>
                        <p:selectOneRadio style="width:120px;" value="#{mgrbean.extractBhk.provHearRstId}"
                                          rendered="#{mgrbean.extractType eq 2}">
                            <f:selectItems value="#{mgrbean.expertHearRstList}" var="examType"
                                           itemLabel="#{examType.codeName}" itemValue="#{examType.rid}"/>
                            <p:ajax event="change" process="@this" update="provOtherRmkReq"/>
                        </p:selectOneRadio>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="省级复核体检结论修改意见：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <p:selectOneRadio style="width:120px;" value="#{mgrbean.extractBhk.provAdvice}">
                            <f:selectItem itemLabel="同意" itemValue="1"/>
                            <f:selectItem itemLabel="不同意" itemValue="2"/>
                            <p:ajax event="change" process="@this" update="provOtherRmkReq"/>
                        </p:selectOneRadio>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="其他：" id="provOtherRmkReq"
                                      styleClass="#{mgrbean.extractBhk.provAdvice eq 2?'cs-required':''}"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:inputText value="#{mgrbean.extractBhk.provOtherRmk}" maxlength="100" style="width: 180px;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="复核单位：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:inputText value="#{mgrbean.extractBhk.provUnitName}" maxlength="100" style="width: 180px;"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="复核人：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:inputText value="#{mgrbean.extractBhk.provCheckPsn}" maxlength="50" style="width: 180px;"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="复核日期：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                    showOtherMonths="true" size="11" navigator="true"
                                    yearRange="c-10:c+10" converterMessage="复核日期，格式输入不正确！"
                                    showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                                    value="#{mgrbean.extractBhk.provCheckDate}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h" style="text-align: right;">
                        <h:outputText value="联系电话：" styleClass="cs-required"/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="5">
                        <p:inputText value="#{mgrbean.extractBhk.provLinktel}" maxlength="20" style="width: 180px;"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" rendered="#{mgrbean.ifView}">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;">
                        <h:outputText value="省级复核阅片结论：" rendered="#{mgrbean.extractType eq 1}"/>
                        <h:outputText value="省级复核判定结论：" rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.fkByProvChestId.codeName}"
                                      rendered="#{mgrbean.extractType eq 1}"/>
                        <h:outputText value="#{mgrbean.extractBhk.fkByProvHearRstId.codeName}"
                                      rendered="#{mgrbean.extractType eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="省级复核体检结论修改意见："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <h:outputText value="同意" rendered="#{mgrbean.extractBhk.provAdvice eq 1}"/>
                        <h:outputText value="不同意" rendered="#{mgrbean.extractBhk.provAdvice eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="其他："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText styleClass="cs-break-word" value="#{mgrbean.extractBhk.provOtherRmk}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="复核单位："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText styleClass="cs-break-word" value="#{mgrbean.extractBhk.provUnitName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="复核人："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText styleClass="cs-break-word" value="#{mgrbean.extractBhk.provCheckPsn}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="复核日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.provCheckDate}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h" style="text-align: right;">
                        <h:outputText value="联系电话："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="5">
                        <h:outputText value="#{mgrbean.extractBhk.provLinktel}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
    </ui:define>
</ui:composition>