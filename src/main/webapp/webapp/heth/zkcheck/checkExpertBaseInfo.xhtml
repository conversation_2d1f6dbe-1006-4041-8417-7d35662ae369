<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:op="http://java.sun.com/jsf/html">

    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.CheckExpertListBean"-->
    <p:fieldset legend="体检信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%">
            <p:row>
                <p:column style="height:40px;width: 220px;text-align: right;">
                    <p:outputLabel value="体检机构地区："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;width:360px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.zoneName}"/>
                </p:column>
                <p:column style="width: 220px;text-align: right;">
                    <p:outputLabel value="体检机构名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.fkByOrgId.unitName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="抽取类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.fkByExtractTypeId.codeName}"/>
                </p:column>
                <p:column style=";text-align: right;">
                    <p:outputLabel value="姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.personName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="身份证号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.idcEncrypted}"/>
                </p:column>
                <p:column style="text-align: right;">
                    <p:outputLabel value="接害工龄（年）："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.tchbadrsntim}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="体检编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.bhkCode}"/>
                </p:column>
                <p:column style="text-align: right;">
                    <p:outputLabel value="报告出具日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.prtDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>

            </p:row>
            <p:row>
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="用工单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.fkByEntrustCrptId.crptName}"/>
                </p:column>
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="职业健康检查结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.fkByBhkRstId.codeName}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{'1' eq mgrbean.extractBhk.fkByExtractTypeId.extendS1}">
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="职业健康检查胸片结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.fkByChestRstId.codeName}"/>
                </p:column>
                <p:column style="text-align: right;">
                    <p:outputLabel value="胸片号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.chestNo}"/>
                </p:column>

            </p:row>
            <p:row rendered="#{'2' eq mgrbean.extractBhk.fkByExtractTypeId.extendS1}">
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="职业健康检查听力检测结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.fkByHearRstId.codeName}"/>
                </p:column>
                <p:column style="text-align: right;">
                    <p:outputLabel value="听力图谱编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:outputLabel value="#{mgrbean.extractBhk.chestNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="height:40px;text-align: right;">
                    <p:outputLabel value="体检报告附件："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                     rendered="#{mgrbean.ifShowFileDownload}"
                                     onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                     value="#{mgrbean.extractBhk.bhkRptPath}"/>
                        <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                     value="#{mgrbean.bhkRptFileName}"/>
                        <p:fileDownload value="#{mgrbean.streamedContent}"/>
                    </p:commandButton>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.extractBhk.bhkRptPath}')"
                                     rendered="#{!mgrbean.ifShowFileDownload}"/>
                </p:column>
                <p:column style="text-align: right;"
                          rendered="#{'1' eq mgrbean.extractBhk.fkByExtractTypeId.extendS1}">
                    <p:outputLabel value="胸片附件："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="3"
                          rendered="#{'1' eq mgrbean.extractBhk.fkByExtractTypeId.extendS1}">
                    <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                     rendered="#{mgrbean.extractBhk.chestPath != null}"
                                     onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                     value="#{mgrbean.extractBhk.chestPath}"/>
                        <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                     value="#{mgrbean.chestFileName}"/>
                        <p:fileDownload value="#{mgrbean.streamedContent}"/>
                    </p:commandButton>
                </p:column>
                <p:column style="text-align: right;"
                          rendered="#{'2' eq mgrbean.extractBhk.fkByExtractTypeId.extendS1}">
                    <p:outputLabel value="听力图谱附件："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="3"
                          rendered="#{'2' eq mgrbean.extractBhk.fkByExtractTypeId.extendS1}">
                    <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                     rendered="#{mgrbean.ifShowChest}"
                                     onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                     value="#{mgrbean.extractBhk.chestPath}"/>
                        <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                     value="#{mgrbean.chestFileName}"/>
                        <p:fileDownload value="#{mgrbean.streamedContent}"/>
                    </p:commandButton>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.extractBhk.chestPath}')"
                                     rendered="#{!mgrbean.ifShowChest}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
</ui:composition>
