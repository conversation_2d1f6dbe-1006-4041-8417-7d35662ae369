<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_turn.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.TdFsCheckTableListBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdFsCheckTableListBean}"/>
    <!--编辑页面-->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/tdFsCheckTableEdit.xhtml"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <ui:param name="condition" value="1"/>

    <ui:define name="insertScripts">
        <script type="application/javascript">
            //<![CDATA[
            function showStatus() {
                PF('StatusDialog').show();
            }

            function hideStatus() {
                PF('StatusDialog').hide();
            }

            function rowSelect(index) {
                $("#tabView\\:editForm\\:dataTable").find("tr").eq(index).find("td:eq(0)").click();
            }

            //页面添加js方法
            //elStr 悬浮框(地区框)id
            function removeExtraZonePanel(elStr) {
                var el = jQuery(elStr);
                if (el.length > 1) {
                    el.each(function(index) {
                        if (index > 0) {
                            $(this).remove();
                        }
                    });
                }
            }

            //]]>
        </script>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/hideTooltips.xhtml"/>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质控考核表维护"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <h:outputStylesheet name="css/ui-cs.css"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.modInitAction}"
                                 update=":tabView" process="@this">
                    <f:setPropertyActionListener target="#{mgrbean.standObj}" value="#{null}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <ui:define name="insertContents">
        <p:outputPanel id="dataView">
            <p:panelGrid style="width:100%;margin-bottom:5px;margin-top: 10px;">
                <f:facet name="header">
                    <p:row>
                        <p:column styleClass="ui-state-default" style="text-align: center;width:300px;">
                            <h:outputText value="机构类型"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="text-align: center;width:100px;">
                            <h:outputText value="年份"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="text-align: center;width:400px;">
                            <h:outputText value="评估表名称"/>
                        </p:column>
                        <p:column styleClass="ui-state-default" style="text-align: center;">
                            <h:outputText value="操作"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <c:forEach items="#{mgrbean.tableList}" var="data" varStatus="row">
                    <p:row>
                        <p:column style="text-align: center;" rowspan="#{data[5]}" rendered="#{data[1] ne null}">
                            <h:outputText value="#{data[1]}"/>
                        </p:column>
                        <p:column style="text-align: center;line-height: 24px;" rowspan="#{data[6]}"
                                  rendered="#{data[2] ne null}">
                            <h:outputText value="" rendered="#{'9999' eq data[2]}"/>
                            <h:outputText value="#{data[2]}" rendered="#{'9999' ne data[2]}"/>
                        </p:column>
                        <p:column>
                            <h:outputText value="#{data[3]}"/>
                        </p:column>
                        <p:column rowspan="#{data[6]}" rendered="#{data[2] ne null}">
                            <p:commandLink value="修改" resetValues="true"
                                           process="@this" update=":tabView"
                                           oncomplete="removeExtraZonePanel('#tabView\\:mainForm\\:dialogBoxId');"
                                           action="#{mgrbean.modInitAction}">
                                <f:setPropertyActionListener target="#{mgrbean.standObj}" value="#{data}"/>
                            </p:commandLink>
                            <p:spacer width="5"/>
                            <p:commandLink value="复制" resetValues="true"
                                           process="@this,:tabView:mainForm:mainGrid" update=":tabView:mainForm:dataView"
                                           action="#{mgrbean.copyAction}">
                                <f:setPropertyActionListener target="#{mgrbean.standObj}" value="#{data}"/>
                            </p:commandLink>
                        </p:column>
                    </p:row>
                </c:forEach>
                <p:row rendered="#{null==mgrbean.tableList or mgrbean.tableList.size()==0}">
                    <p:column colspan="4">
                        <h:outputText value="没有您要找的记录！"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:outputPanel>
    </ui:define>
</ui:composition>











