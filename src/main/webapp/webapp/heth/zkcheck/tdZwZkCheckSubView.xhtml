<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:op="http://java.sun.com/jsf/html">

    <script type="text/javascript" src="/resources/js/namespace.js" />
    <script type="text/javascript"
            src="/resources/js/validate/system/validate.js" />
    <style>
        .tableTr .ui-widget-content {
            border: 0px solid #a6c9e2;
        }
    </style>
    <h:form id="editForm3">
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editTitleGrid2">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="现场考核管理" rendered="#{null != mgrbean.pageType() and 1 == mgrbean.pageType()}"/>
                        <h:outputText value="现场考核查询"  rendered="#{null != mgrbean.pageType() and 2 == mgrbean.pageType()}"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="buttonPanelId">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="submitBtn" action="#{mgrbean.cancelValidate}"
                                 rendered="#{(null eq mgrbean.checkMain.stateMark or mgrbean.checkMain.stateMark eq 0) and null ne mgrbean.curCheckTable and 1 == mgrbean.curCheckTable.stateMark and null ne mgrbean.checkMain and (null == mgrbean.checkMain.writePath or  '' == mgrbean.checkMain.writePath)}" update=":tabView:editForm2" process="@this" />
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backSubAction(1)}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要撤销吗？" header="消息确认框" widgetVar="CancelConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.cancelSubAction}" icon="ui-icon-check"
                             oncomplete="PF('CancelConfirmDialog').hide();" update=":tabView"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('CancelConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>


        <p:sticky target="buttonPanelId" />
        <ui:include src="/webapp/heth/zkcheck/tdZwZkCheckSubBaseView.xhtml" />
    </h:form>
</ui:composition>