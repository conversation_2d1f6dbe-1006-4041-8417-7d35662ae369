<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">

    <ui:define name="insertEditScripts">
        <style type="text/css">
            .myCalendar2 input {
                width: 78px;
            }
        </style>

    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="检测报告上传" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
                <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
                    <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" oncomplete="PF('ConfirmDialog').hide();"/>
                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
                </p:confirmDialog>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="用人单位信息">
            <p:panelGrid  style="margin-bottom: 10px;width: 100%;" id="crptInfoPanel">
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                        <div style="display: flex;align-items: center;">
                            <p:inputText id="crptName" readonly="true" style="width: 350px;"
                                         value="#{mgrbean.tdZwCheckRpt.crptName}"/>
                            <p:commandLink id="onCrptSelect"
                                           partialSubmit="true" value="选择"
                                           action="#{mgrbean.selectCrptList}" process="@this"
                                           style="margin-left: 10px;">
                                <p:ajax event="dialogReturn" process="@this,crptName" resetValues="true"
                                        listener="#{mgrbean.onCrptSelect}" update="crptInfoPanel"/>
                            </p:commandLink>
                        </div>
                    </p:column>
                    <p:column style="text-align:right;width: 15%;">
                        <p:outputLabel value="所属区域："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <h:outputText escape="false"
                                      value="#{mgrbean.tdZwCheckRpt.fkByZoneId.zoneType>3?mgrbean.tdZwCheckRpt.fkByZoneId.fullName.substring(mgrbean.tdZwCheckRpt.fkByZoneId.fullName.indexOf('_')+1,mgrbean.tdZwCheckRpt.fkByZoneId.fullName.length()):mgrbean.tdZwCheckRpt.fkByZoneId.zoneName}"/>

                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;">
                        <p:outputLabel value="社会信用代码："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByCrptId.institutionCode}"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="单位地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.address}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;">
                        <p:outputLabel value="行业类别："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByIndusTypeId.codeName}"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="经济性质："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByEconomyId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;" >
                        <p:outputLabel value="企业规模："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByCrptSizeId.codeName}"/>
                    </p:column>
                    <p:column style="text-align:right;">
                        <p:outputLabel value="联系人："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkMan}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;height: 40px;">
                        <p:outputLabel value="联系电话："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkPhone}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="检查报告上传" style="margin-top: 10px;">
            <p:panelGrid  style="margin-bottom: 10px;width: 100%;" id="rptPanel">
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="工作场所名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                        <p:inputText value="#{mgrbean.tdZwCheckRpt.workName}" maxlength="500" style="width: 56.8%;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;width: 15%;height: 40px;" >
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="报告日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar2"
                                    showOtherMonths="true" id="rptDate" navigator="true"
                                    yearRange="c-10:c" converterMessage="报告日期，格式输入不正确！"
                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                    value="#{mgrbean.tdZwCheckRpt.rptDate}"/>
                    </p:column>
                    <p:column style="text-align:right;width: 15%;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="检测报告名称（编号）："/>
                    </p:column>
                    <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                        <p:inputText value="#{mgrbean.tdZwCheckRpt.rptNo}" maxlength="50" style="margin-right: 10px;"/>
                        <p:commandButton value="上传"
                                         process="@this,:tabView:editForm" update="fileDialog"
                                         action="#{mgrbean.beforeUpload}"
                                         rendered="#{mgrbean.tdZwCheckRpt.filePath == null}"/>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.tdZwCheckRpt.filePath}')"
                                         rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                        <p:spacer width="5" rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                        <p:commandButton value="删除"
                                         process="@this,:tabView:editForm"
                                         onclick="PF('DeleteDialog').show();"
                                         rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteDialog">
                            <p:commandButton value="确定" action="#{mgrbean.delRptFile}" icon="ui-icon-check"
                                             update=":tabView:editForm:fileDialog"
                                             oncomplete="PF('DeleteDialog').hide();"/>
                            <p:commandButton value="取消" icon="ui-icon-close"
                                             onclick="PF('DeleteDialog').hide();"
                                             type="button"/>
                        </p:confirmDialog>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel
                                value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload id="fileUpload"
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}"
                                invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>


</ui:composition>
