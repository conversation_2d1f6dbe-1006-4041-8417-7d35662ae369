<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <style type="text/css">
            .myCalendar2 input {
                width: 78px;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>

    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="放射卫生技术服务机构检测报告审核" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="退回原因"
                                 icon="icon-alert" action="#{mgrbean.initDialog}" style="color:red;"
                                 process="@this" oncomplete="PF('ReasonDialog').show();"
                                 update=":tabView:editForm:reasonDialog"
                                 rendered="#{mgrbean.radCheckRpt.state==3}">
                    <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="true"></f:setPropertyActionListener>
                </p:commandButton>
                <p:commandButton value="审核通过" icon="ui-icon-disk" update=":tabView:editForm"
                                 rendered="#{mgrbean.radCheckRpt.state==1}" oncomplete="datatableOffClick()"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                    <p:confirm header="消息确认框" message="确定要审核通过吗？"
                               icon="ui-icon-alert" />
                </p:commandButton>
                <p:commandButton value="退回" icon="ui-icon-cancel" update=":tabView:editForm:reasonDialog"
                                 rendered="#{mgrbean.radCheckRpt.state==1}"
                                 oncomplete="PF('ReasonDialog').show();datatableOffClick();"
                                 action="#{mgrbean.initDialog}" process="@this" >
                    <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="false" />
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" oncomplete="datatableOffClick();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <ui:include src="tdRadCheckRptUploadCommView.xhtml" />
    </ui:define>
</ui:composition>