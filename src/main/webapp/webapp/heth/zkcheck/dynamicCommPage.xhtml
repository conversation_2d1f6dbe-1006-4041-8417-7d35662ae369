<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <p:panelGrid style="width:100%;margin-bottom:5px;" id="dataTable">
        <f:facet name="header">
            <c:forEach items="#{mgrbean.headRowList}" var="headRow" varStatus="row">
                <p:row>
                    <c:forEach items="#{headRow.cols}" var="col">
                        <p:column style="text-align:center;height: 20px;#{null ne col.style ? col.style :  ''}"
                                  styleClass="ui-state-default" rowspan="#{col.rowspan}" colspan="#{col.colspan}">
                            <h:outputText value="#{col.colStrVal}" escape="false"/>
                        </p:column>
                    </c:forEach>
                </p:row>
            </c:forEach>
        </f:facet>
        <c:forEach items="#{mgrbean.dataRowList}" var="row" varStatus="rowStatus">
            <p:row>
                <c:forEach items="#{row.cols}" var="col" varStatus="colStatus">
                    <p:column style="height: 25px; text-align:#{col.textAlign};" styleClass="#{rowStatus.index % 2 == 1 and col.rowspan == 1 ?'ui-datatable-odd':''}" rowspan="#{col.rowspan}" colspan="#{col.colspan}">
                        <h:outputText value="#{col.colStrVal}" style="font-weight: #{col.fontWeight};" escape="false" rendered="#{col.type == 1}"/>
                        <h:outputText value="#{col.colIntVal}" rendered="#{col.type == 2}" />
                        <h:outputText value="#{col.colDecimalVal}" rendered="#{col.type == 3}" />
                    </p:column>
                </c:forEach>
            </p:row>
        </c:forEach>
        <p:row rendered="#{null==mgrbean.dataRowList or mgrbean.dataRowList.size()==0}">
            <p:column colspan="#{mgrbean.topColSpan}" style="height: 35px;text-align:left;padding-left: 10px;">
                <h:outputText value="没有您要找的数据！" />
            </p:column>
        </p:row>
    </p:panelGrid>
</ui:composition>