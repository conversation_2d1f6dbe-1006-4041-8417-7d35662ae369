<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.OrgExamResultOverViewBean"-->
    <ui:param name="mgrbean" value="#{orgExamResultOverViewBean}"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            //<![CDATA[
                function downloadFileClick() {
                    document.getElementById("mainForm:downloadFileBtn").click();
                }
            //]]>
        </script>
        <style>
            .myCalendar2 input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="机构考核结果一览表"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"
                                 onclick="zwx_loading_start_pub();"
                                 oncomplete="zwx_loading_stop_pub();"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportDataBtn"
                                 action="#{mgrbean.preExport()}"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start_pub, zwx_loading_stop_pub);">
                    <p:fileDownload value="#{mgrbean.downloadFile}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:153px;height:38px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:273px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 153px;">
                <h:outputText value="考核类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:267px;">
                <p:selectOneMenu value="#{mgrbean.checkTypeRid}" style="width: 190px;">
                    <f:selectItems value="#{mgrbean.checkTypeList}" var="examType"
                                   itemLabel="#{examType.codeName}"
                                   itemValue="#{examType.rid}"/>
                    <p:ajax event="change" process="@this,mainGrid" listener="#{mgrbean.changeCheckType}" update="mainGrid" />
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 153px;">
                <font color="red">*</font>
                <h:outputText value="考核日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchCheckDateStart}"
                                              endDate="#{mgrbean.searchCheckDateEnd}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:153px;height:38px;">
                <h:outputText value="考核等级："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;#{mgrbean.ifProvUser ? 'width:273px;' : ''}" colspan="#{mgrbean.ifProvUser ? 1 : 5}">
                <zwx:SelectManyMenuNewComp dataMap="#{mgrbean.selectCheckRstMap}" height="200" zonePaddingLeft="0"
                                           dataValue="#{mgrbean.checkRstSelectRids}" dataLabel="#{mgrbean.checkRstSelectNames}"
                                           disabled="#{mgrbean.ifCheckRstCanSelect ? 'false' : 'true'}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;" rendered="#{mgrbean.ifProvUser}">
                <h:outputText value="质控类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="3" rendered="#{mgrbean.ifProvUser}">
                <p:selectManyCheckbox value="#{mgrbean.inquiryExamTypeList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="2" itemLabel="省级"/>
                    <f:selectItem itemValue="3" itemLabel="市级"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertContent">
        <ui:include src="dynamicCommPage.xhtml" />
    </ui:define>

</ui:composition>