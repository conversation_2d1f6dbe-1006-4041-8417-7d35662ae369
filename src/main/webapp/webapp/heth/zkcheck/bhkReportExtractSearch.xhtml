<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkReportExtractSearchBean"-->
    <ui:param name="mgrbean" value="#{bhkReportExtractSearchBean}"/>
   <!--详情-->
    <ui:param name="viewPage" value="/webapp/heth/zkcheck/bhkReportExtractView.xhtml"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false" />
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }

            function hideStatus() {
                PF('StatusDialog').hide();
            }
        </script>
        <style type="text/css">
            .searchTime input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检报告抽取查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}"
                                 update=":tabView:mainForm:dataTable"
                                 process="@this,mainGrid"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;width: 200px;">
                <p:outputLabel value="抽取日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;width: 280px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchSDate}"
                                              endDate="#{mgrbean.searchEDate}" styleClass="searchTime"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;width: 200px;">
                <p:outputLabel value="姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;width: 280px;">
                <p:inputText value="#{mgrbean.searchPersonName}" maxlength="100"
                             style="width: 180px;"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;width: 200px;">
                <p:outputLabel value="体检编号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:inputText value="#{mgrbean.searchBhkCode}" maxlength="50"
                             style="width: 180px;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 200px;height:38px;">
                <p:outputLabel value="抽取类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkType}">
                    <f:selectItems value="#{mgrbean.bhkTypeList}" var="itm" itemValue="#{itm.rid}"
                                   itemLabel="#{itm.codeName}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="抽取类别" style="width: 100px;text-align: center;">
            <h:outputLabel value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="姓名" style="width: 100px;text-align: center;">
            <h:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="体检编号" style="width: 120px;text-align: center;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="报告出具日期" style="width: 90px;text-align: center;">
            <h:outputLabel value="#{itm[4]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="危害因素结论" style="width: 120px;text-align: center;">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="接害工龄（年）" style="width: 100px;text-align: center;">
            <h:outputLabel value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width: 250px;">
            <h:outputLabel value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="抽取日期" style="width: 80px;text-align: center;">
            <h:outputLabel value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.beforeViewInit}"
                           update=":tabView" process="@this">
                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.extractBhk}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">

    </ui:define>
</ui:composition>