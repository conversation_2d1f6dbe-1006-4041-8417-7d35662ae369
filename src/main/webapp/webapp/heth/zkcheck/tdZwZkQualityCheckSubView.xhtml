<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:op="http://java.sun.com/jsf/html">

    <script type="text/javascript" src="/resources/js/namespace.js" />
    <script type="text/javascript"
            src="/resources/js/validate/system/validate.js" />
    <style>
        .tableTr .ui-widget-content {
            border: 0px solid #a6c9e2;
        }
    </style>
    <h:form id="editForm3">
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editTitleGrid2">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="技术服务机构质量监测" rendered="#{null != mgrbean.pageType() and 1 == mgrbean.pageType()}"/>
                        <h:outputText value="技术服务机构质量监测查询"  rendered="#{null != mgrbean.pageType() and 2 == mgrbean.pageType()}"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="buttonPanelId">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="submitBtn" action="#{mgrbean.cancelSubAction}" rendered="#{0 ne mgrbean.subEditTag and (null == mgrbean.checkMain.stateMark or mgrbean.checkMain.stateMark ne 1) and null ne mgrbean.curCheckTable and 1 == mgrbean.curCheckTable.stateMark and null ne mgrbean.checkMain and (null == mgrbean.checkMain.writePath or  '' == mgrbean.checkMain.writePath)}" update=":tabView:editForm2" process="@this">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backSubAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>


        <p:sticky target="buttonPanelId" />

        <!-- 具体数据展示 -->
        <p:outputPanel>
            <p:panelGrid style="width:100%;height:100%;margin-top:10px;margin-bottom:5px;">
                <f:facet name="header">
                    <p:row>
                        <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                            <h:outputText value="#{null ne mgrbean.curCheckTable and null ne mgrbean.curCheckTable.fkByCheckTableId ? mgrbean.curCheckTable.fkByCheckTableId.checkName : ''}" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                        <h:outputLabel value="考核人："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:9px;" colspan="5">
                        <h:outputText value="#{mgrbean.curCheckTable.checkPsn}" />
                    </p:column>
                </p:row>

            </p:panelGrid>
            <c:forEach var="checkItem" items="#{mgrbean.zwZkCheckItemList}" varStatus="TMR" >
                <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;overflow-x: hidden;"
                             styleClass="zwpx-table">
                    <p:row>
                        <p:column colspan="5" style="text-align:left;height: 30px; border-right: 0px;">
                            <h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
                                <div style="display:flex; ">
                                    <div style="width: 3px; height: 12px; background: #2e6e9e;margin-top: 1px;" />
                                    <label class="ui-outputlabel ui-widget"
                                           style=" font-size: 14px !important; margin-left: 5px;font-weight: 600;color: #2e6ea9;">
                                        #{null ne checkItem.fkByItemId ? checkItem.fkByItemId.codeName : ''}</label>
                                </div>
                            </h:panelGrid>
                        </p:column>
                        <p:column style="text-align: left;width: 450px;  border-left: 0px;">
                            <p:outputLabel value="分值：#{checkItem.checkVal} #{null ne checkItem.checkVal ? '分' : '  '}" rendered="#{checkItem.ifHasScore}" />
                            <p:outputLabel value="实得分：#{checkItem.scoreVal} #{null ne checkItem.scoreVal ? '分' : '  '}" style="margin-left: 20px;" rendered="#{checkItem.ifHasScore}"
                                           id="freshItemLabel#{TMR.index}" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <c:forEach items="#{checkItem.checkSubList}" var="checkSub" varStatus="SR">
                    <p:panelGrid style="width:100%;margin-top:0px;line-height: 14px;overflow-x: hidden;" styleClass="tableTr">
                        <p:row>
                            <p:column colspan="5" style="text-align:left;height: 30px;border-right: 0px;background-color: #dfeffc; border-top: 0px;border-bottom: 0px;">
                                <div style="line-height: 24px;"><h:outputText escape="false" value="#{null ne checkSub.fkByScoreId ? mgrbean.indexDescMap.get(checkSub.fkByScoreId.rid) : ''}" /></div>
                            </p:column>
                            <p:column style="text-align: left; width: 450px;border-left: 0px;background-color: #dfeffc;border-top: 0px;border-bottom: 0px;">
                                <p:outputLabel rendered="#{checkSub.fkByScoreId.fkByItemTypeId.extendS1 ne 3}"
                                               value="分值：#{null ne checkSub.fkByScoreId ? checkSub.fkByScoreId.score : ''} #{null ne checkSub.fkByScoreId.score ? '分' : '  '}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid style="width:100%;margin-top:0px;line-height: 14px;overflow-x: hidden;"
                                 styleClass="zwpx-table">
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                                <h:outputLabel value="结果："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;width:180px;" rendered="#{checkSub.fkByScoreId.fkByItemTypeId.extendS1 == 3}">
                                <h:outputText value="#{null == checkSub.fkByScoreRstId ? '' : checkSub.fkByScoreRstId.codeName}" />
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;width: 180px;" rendered="#{checkSub.fkByScoreId.fkByItemTypeId.extendS1 ne 3}">
                                <h:outputText value="#{checkSub.scoreVal}" />
                            </p:column>
                            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                                <!-- 实得分小于评估项分值 -->
                                <h:outputLabel value="存在问题：" escape="false" />
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;" colspan="3">
                                <div style="width: 500px;line-height: 24px">
                                    <h:outputText value="#{checkSub.deductCause}"/>
                                </div>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                                <h:outputLabel value="备注："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;" colspan="5">
                                <div style="width: 1065px;line-height: 24px; word-break: break-all;">
                                    #{checkSub.rmk}
                                </div>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </c:forEach>
            </c:forEach>
        </p:outputPanel>
    </h:form>
</ui:composition>