<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdZwZkQualityCheckListBean}"/>
	<ui:param name="editPage" value= "/webapp/heth/zkcheck/tdZwZkQualityCheckEdit.xhtml" />
	<ui:param name="edit2Page" value= "/webapp/heth/zkcheck/tdZwZkQualityCheckSubEdit.xhtml" />
	<ui:param name="edit3Page" value= "/webapp/heth/zkcheck/tdZwZkQualityCheckSubView.xhtml" />
	<ui:define name="insertScripts">
		<script type="text/javascript">
			function showFullScreen() {
				$(".fullscreen").show();
			}

			function hideFullScreen() {
				$(".fullscreen").hide();
			}
			function openFullScreen(){
				var elem = document.getElementById("pdf");
				var url = $(elem).attr("src");
				window.open(url.replace("#toolbar=0", ""));
			}
			function hideTooltip(){
				setTimeout("$('.ui-tooltip').hide()",1000);
			}
		</script>
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<h:outputStylesheet name="css/annexViewDialog.css"/>
		<style type="text/css">
			.myCalendar1 input{
				width: 78px;
			}
		</style>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="技术服务机构质量监测"/>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
								 update="dataTable" process="@this,mainGrid" />
				<p:spacer width="5"/>
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
				<h:outputText value="地区：" />
			</p:column>
			<p:column style="text-align:left;width:260px;">
				<p:outputPanel id="zonearea">
					<div style="border-color: #ffffff;margin: 0px;padding: 0px;display: table-cell;" >
						<zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
											   zoneCodeNew="#{mgrbean.searchZoneCode}"
											   zoneName="#{mgrbean.searchZoneName}" />
					</div>
				</p:outputPanel>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;">
				<h:outputText value="被考核机构：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;width: 260px;" >
				<p:inputText value="#{mgrbean.searchOrgName}" style="width: 182px;" maxlength="50"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;">
				<h:outputText value="考核日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchCheckStartTime}"
											  endDate="#{mgrbean.searchCheckEndTime}"  styleClass="myCalendar1"/>
			</p:column>
		</p:row>
		<p:row >
			<p:column style="text-align:right;padding-right:3px;height:37px;">
				<h:outputText value="评估结果：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;" >
				<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectRst}"
										selectedIds="#{mgrbean.selectRstIds}"
										simpleCodeList="#{mgrbean.checkRstList}"
										inputWidth="180"
										panelWidth="190"
										height="200"></zwx:SimpleCodeManyComp>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;" colspan="3">
				<p:selectManyCheckbox value="#{mgrbean.searchStateMarkList}" converter="javax.faces.Integer" >
					<f:selectItem itemValue="0" itemLabel="待提交" />
					<f:selectItem itemValue="1" itemLabel="已提交" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="地区" style="width:260px;">
			<h:outputText value="#{itm[1]}" escape="false"/>
		</p:column>
		<p:column headerText="被考核机构" style="width:300px;">
			<h:outputText value="#{itm[2]}"/>
		</p:column>
		<p:column headerText="总得分" style="width:80px;text-align:center; " >
			<h:outputText value="#{itm[3]}"/>
		</p:column>
		<p:column headerText="评估结果" style="width:80px;text-align:center; ">
			<h:outputText value="#{itm[4]}"/>
		</p:column>
		<p:column headerText="考核日期" style="width:100px;text-align:center; ">
			<h:outputLabel value="#{itm[5]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			</h:outputLabel>
		</p:column>
		<p:column headerText="状态" style="width:80px;text-align:center; ">
			<h:outputLabel value="#{null ne itm[6] and 1 == itm[6] ? '已提交' : '待提交'}"/>
		</p:column>
		<p:column headerText="操作" >
			<p:spacer width="5" rendered="#{null == itm[6] or 1 ne itm[6]}" />
			<p:commandLink value="修改" action="#{mgrbean.modInitAction}" rendered="#{null == itm[6] or 1 ne itm[6]}"
						   process="@this"  update=":tabView" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
			<p:spacer width="5" rendered="#{null ne itm[6] and 1 == itm[6]}" />
			<p:commandLink value="详情" action="#{mgrbean.modInitAction}"
						   process="@this"  update=":tabView" rendered="#{null ne itm[6] and 1 == itm[6]}" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
			<p:spacer width="5" rendered="#{null ne itm[6] and 0 == itm[6]}" />
			<p:commandLink value="删除" action="#{mgrbean.deleteAction}"
						   process="@this"  update=":tabView" rendered="#{null ne itm[6] and 0 == itm[6]}" >
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
		</p:column>
	</ui:define>
</ui:composition>
