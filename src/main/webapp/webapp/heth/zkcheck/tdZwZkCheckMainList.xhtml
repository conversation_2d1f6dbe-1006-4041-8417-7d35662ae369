<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdZwZkCheckMainListBean}"/>
	<ui:param name="editPage" value= "/webapp/heth/zkcheck/tdZwZkCheckMainEdit.xhtml" />
	<ui:param name="viewPage" value= "/webapp/heth/zkcheck/tdZwZkCheckMainView.xhtml" />
	<ui:param name="edit2Page" value= "/webapp/heth/zkcheck/tdZwZkCheckSubEdit.xhtml" />
	<ui:param name="edit3Page" value= "/webapp/heth/zkcheck/tdZwZkCheckSubView.xhtml" />
	<ui:define name="insertScripts">
		<script type="text/javascript">
			function showFullScreen() {
				$(".fullscreen").show();
			}

			function hideFullScreen() {
				$(".fullscreen").hide();
			}
			function openFullScreen(){
				var elem = document.getElementById("pdf");
				var url = $(elem).attr("src");
				window.open(url.replace("#toolbar=0", ""));
			}
			function hideTooltip(){
				setTimeout("$('.ui-tooltip').hide()",1000);
			}

		</script>
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<h:outputStylesheet name="css/annexViewDialog.css"/>
		<style type="text/css">
			.myCalendar1 input{
				width: 78px;
			}
			.cs-first {
				text-align: right !important;
				width: 15% !important;
				height: 38px !important;
			}
			.cs-title {
				text-align: right !important;
				width: 15% !important;
			}
			.cs-content {
				text-align:left !important;
				padding-left:11px !important;
				width: 18% !important;
				word-wrap: break-word;
				word-break: break-all;
			}
			.cs-finally {
				text-align:left !important;
				padding-left:11px !important;
				word-wrap: break-word;
				word-break: break-all;
			}
			.cs-con {
				text-align:left !important;
				width: 18% !important;
				word-wrap: break-word;
				word-break: break-all;
			}
		</style>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="现场考核管理"/>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
								 update="dataTable" process="@this,mainGrid" />
				<p:spacer width="5"/>
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this,:tabView:mainForm:mainGrid">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:150px;height:36px;">
				<h:outputText value="地区：" />
			</p:column>
			<p:column style="text-align:left;width:200px;">
				<p:outputPanel id="zonearea">
					<div style="border-color: #ffffff;margin: 0px;padding: 0px;display: table-cell;" >
						<zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
											   zoneCodeNew="#{mgrbean.searchZoneCode}"
											   zoneName="#{mgrbean.searchZoneName}" />
					</div>
				</p:outputPanel>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:150px;">
				<h:outputText value="被考核机构：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;width: 200px;" >
				<p:inputText value="#{mgrbean.searchOrgName}" style="width: 182px;" maxlength="50"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:150px;">
				<h:outputText value="考核日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchCheckStartTime}"
											  styleClass="myCalendar1"  endDate="#{mgrbean.searchCheckEndTime}"/>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px;">
				<h:outputText value="考核类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:11px;padding-top: 6px;">
				<p:selectOneMenu  value="#{mgrbean.queryCheckType}" style="width:190px;">
					<f:selectItem itemLabel="--全部--" />
					<f:selectItems value="#{mgrbean.queryCheckTypeList}" var="typeItm" itemValue="#{typeItm.rid}" itemLabel="#{typeItm.codeName}" />
				</p:selectOneMenu>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="本年度是否开展工作：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;">
				<p:selectManyCheckbox value="#{mgrbean.ifDevelopList}" converter="javax.faces.Integer">
					<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
					<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
				</p:selectManyCheckbox>
			</p:column>
			<p:column style="text-align:right;padding-right:8px;">
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:selectManyCheckbox value="#{mgrbean.searchStateMarkList}" converter="javax.faces.Integer" >
					<f:selectItem itemValue="0" itemLabel="待提交" />
					<f:selectItem itemValue="1" itemLabel="已提交" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px;">
				<h:outputText value="是否需要整改：" />
			</p:column>
			<p:column style="text-align:left;padding-left:11px;padding-top: 6px;">
				<p:selectManyCheckbox value="#{mgrbean.searchNeedImproveStateList}" converter="javax.faces.Integer">
					<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
					<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
				</p:selectManyCheckbox>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="整改状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:12px;" colspan="3">
				<p:selectManyCheckbox value="#{mgrbean.searchFinishImproveStateList}" converter="javax.faces.Integer">
					<f:selectItem itemValue="0" itemLabel="待整改" />
					<f:selectItem itemValue="1" itemLabel="待审核" />
					<f:selectItem itemValue="3" itemLabel="审核退回" />
					<f:selectItem itemValue="2" itemLabel="整改完成" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="地区" style="width:260px;">
			<h:outputText value="#{itm[1]}" escape="false"/>
		</p:column>
		<p:column headerText="被考核机构" style="width:300px;">
			<h:outputText value="#{itm[2]}"/>
		</p:column>
		<p:column headerText="考核类型" style="width:200px;text-align: center; ">
			<h:outputText value="#{itm[3]}"/>
		</p:column>
		<p:column headerText="考核日期" style="width:100px;text-align:center; ">
			<h:outputLabel value="#{itm[4]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			</h:outputLabel>
		</p:column>
		<p:column headerText="本年度是否开展工作" style="width:120px;text-align:center; ">
			<h:outputText value="否" rendered="#{itm[5] == 0}"/>
			<h:outputText value="是" rendered="#{itm[5] == 1}"/>
		</p:column>
		<p:column headerText="总得分" style="width:80px;text-align:center; ">
			<h:outputText value="#{itm[6]}"/>
		</p:column>
		<p:column headerText="评审结论" style="width:80px;text-align:center; ">
			<h:outputText value="通过" rendered="#{itm[7] == 1}"/>
			<h:outputText value="整改后通过" rendered="#{itm[7] == 2}"/>
			<h:outputText value="整改后复审" rendered="#{itm[7] == 3}"/>
			<h:outputText value="不通过" rendered="#{itm[7] == 4}"/>
		</p:column>
		<p:column headerText="状态" style="width:80px;text-align:center; ">
			<h:outputText value="待提交" rendered="#{itm[8] == 0}"/>
			<h:outputText value="已提交" rendered="#{itm[8] == 1}"/>
		</p:column>
		<p:column headerText="是否需要整改" style="width:80px;text-align:center; ">
			<h:outputText value="否" rendered="#{itm[9] == 0}"/>
			<h:outputText value="是" rendered="#{itm[9] == 1}"/>
		</p:column>
		<p:column headerText="整改状态" style="width:80px;text-align:center; ">
			<h:outputText value="待整改" rendered="#{itm[10] == 0}"/>
			<h:outputText value="待审核" rendered="#{itm[10] == 1}"/>
			<h:outputText value="整改完成" rendered="#{itm[10] == 2}"/>
			<h:outputText value="审核退回" rendered="#{itm[10] == 3}"/>
		</p:column>
		<p:column headerText="操作" >
			<p:spacer width="5" />
			<p:commandLink value="修改" action="#{mgrbean.modInitAction}" rendered="#{0 == itm[8]}"
						   process="@this,:tabView:mainForm:mainGrid"  update=":tabView" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
			<p:commandLink value="详情" action="#{mgrbean.viewInitAction}"
						   process="@this,:tabView:mainForm:mainGrid"  update=":tabView" rendered="#{1 == itm[8] and (null == itm[10] or 1 ne itm[10])}" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
			<p:commandLink value="审核" action="#{mgrbean.viewInitAction}" rendered="#{1 == itm[8] and 1 == itm[10]}"
						   process="@this,:tabView:mainForm:mainGrid"  update=":tabView" >
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
			<p:spacer width="5" rendered="#{1 == itm[12] and 0 == itm[8]}" />
			<p:commandLink value="删除" action="#{mgrbean.deleteAction}"
						   process="@this,:tabView:mainForm:mainGrid"  update=":tabView" rendered="#{1 == itm[12] and 0 == itm[8]}" >
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
				<f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
			</p:commandLink>
		</p:column>
	</ui:define>
</ui:composition>
