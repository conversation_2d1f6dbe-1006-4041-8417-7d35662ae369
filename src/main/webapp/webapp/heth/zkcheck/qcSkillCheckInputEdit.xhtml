<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制技能考核录入" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="btnPanel">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm:techPsnTable"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm,:tabView:editForm:techPsnTable" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" oncomplete="windowScrollTop()"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm" >
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="btnPanel" />
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:panelGrid id="baseInfo"  style="margin-bottom: 10px;width:100%" >
            <p:row>
                <p:column style="text-align:right;width: 250px;height: 38px;">
                    <p:outputLabel value="*" style="color:red;" />
                    <p:outputLabel value="考核类型："/>
                </p:column>
                <p:column style="text-align:left;width: 300px;" >
                    <p:selectOneMenu  value="#{mgrbean.checkTechMain.fkByCheckTypeId.rid}" id="checkType" style="width:205px;margin-left: 5px;">
                        <f:selectItems value="#{mgrbean.checkTypeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
                        <p:ajax event="change" process="@this" listener="#{mgrbean.changeCheckType}" update="unitName,checkAddr,filiTypeName" />
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:right;width: 250px;">
                    <p:outputLabel value="*" style="color:red;"/>
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column style="text-align:left;" >
                    <h:panelGrid columns="2" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="unitName"  readonly="true" style="width:240px;"
                                     value="#{mgrbean.checkTechMain.fkByUnitId.unitname}"
                                     onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
                        <p:commandLink id="onOrgSelect"  styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectUnitList}" process="@this,checkType,filiTypeName" style="position: relative;left: -30px;">
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onUnitSelect}" update="unitName,checkAddr,filiTypeName"/>
                        </p:commandLink>
                    </h:panelGrid>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;" >
                    <p:outputLabel value="备案类别："/>
                </p:column>
                <p:column style="text-align:left;" colspan="3">
                    <div style="display: table-cell;padding-left: 5px;width:810px;">
                        <p:outputLabel id="filiTypeName" value="#{mgrbean.checkTechMain.filiTypeName}" />
                    </div>
                </p:column>

            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="*" style="color:red;" />
                    <p:outputLabel value="考核日期："/>
                </p:column>
                <p:column style="text-align:left;" colspan="3">
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                showOtherMonths="true"  navigator="true" style="margin-left: 5px;"
                                yearRange="c-10:c" converterMessage="考核日期，格式输入不正确！"
                                showButtonPanel="true" maxdate="new Date()"
                                value="#{mgrbean.checkTechMain.checkDate}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color:red;" />
                    <p:outputLabel value="被考核人员：" style="line-height: 12px;" />
                </p:column>
                <p:column colspan="3">
                    <p:outputPanel  styleClass="writeSortInfo" style="margin-top: 10px;margin-left: 5px;" id="careeHistory">
                        <div style="display: table-row;">
                            <div style="display: table-cell;vertical-align: middle;">
                                    <p:commandButton value="选择人员"
                                                     action="#{mgrbean.chooseCheckTechPsn}" process="@this,techPsnTable,checkType" >
                                        <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onTechPsnSelect}" update="techPsnTable">
                                        </p:ajax>
                                    </p:commandButton>
                                    <p:commandButton value="新增人员" style="margin-left: 5px;"
                                                     action="#{mgrbean.addCheckTechPsn}" process="@this,techPsnTable,checkType" update="techPsnTable">
                                    </p:commandButton>
                            </div>
                        </div>
                        <div style="display: table-row;margin-top: 5px;">
                            <div style="display: table-cell;vertical-align: middle;">
                                    <p:dataTable id="techPsnTable" paginatorPosition="bottom"
                                                 value="#{mgrbean.checkTechPsnList}" style="padding-top: 5px;width: 814px;"
                                                 widgetVar="techPsnTable" var="techPsn"
                                                 emptyMessage="没有数据！" rowIndexVar="R">
                                        <p:column headerText="#{mgrbean.redStar}姓名" style="width:150px;text-align:center;">
                                            <p:inputText value="#{techPsn.psnName}" style="width: 150px;" maxlength="50" />
                                        </p:column>
                                        <p:column headerText="职称" style="width:150px;text-align:center;">
                                            <p:selectOneMenu value="#{techPsn.titleId}"  style="width:150px;margin-left: 5px;">
                                                <f:selectItem itemLabel="--请选择--" itemValue="" />
                                                <f:selectItems value="#{mgrbean.jobTitleList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
                                            </p:selectOneMenu>
                                        </p:column>
                                        <p:column headerText="#{mgrbean.redStar}人员属性" style="text-align:center;width: 150px;" >
                                            <p:selectOneMenu value="#{techPsn.psnTypeId}"  style="width:150px;margin-left: 5px;">
                                                <f:selectItem itemLabel="--请选择--" itemValue="" />
                                                <f:selectItems value="#{mgrbean.psnTypeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
                                            </p:selectOneMenu>
                                        </p:column>
                                        <p:column headerText="操作" style="width:100px;">
                                            <p:commandLink value="删除"
                                                           update="techPsnTable"
                                                           process="@this,techPsnTable" action="#{mgrbean.delTechPsnAction}">
                                                <f:setPropertyActionListener
                                                        target="#{mgrbean.techPsn}" value="#{techPsn}" />
                                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                            </p:commandLink>
                                        </p:column>
                                    </p:dataTable>
                            </div>
                        </div>
                    </p:outputPanel >
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="*" style="color:red;"/>
                    <p:outputLabel value="考核地点："/>
                </p:column>
                <p:column style="text-align:left;"  colspan="3">
                    <p:inputText  style="width:805px;margin-left: 5px;" id="checkAddr" maxlength="100" value="#{mgrbean.checkTechMain.checkAddr}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="*" style="color:red;"/>
                    <p:outputLabel value="考核内容："/>
                </p:column>
                <p:column style="text-align:left;"  colspan="3">
                        <h:panelGrid columns="2"
                                     style="border-color: #ffffff;padding: 0;margin-left: -7px;margin-top: 3px;">
                            <p:inputTextarea id="checkContentNames"
                                         value="#{mgrbean.checkTechMain.checkContent}"
                                         style="width:805px;margin-left: 5px;height: 70px;resize: none;" autoResize="false"
                                         maxlength="1000"/>
                            <p:commandLink type="button" styleClass="ui-icon ui-icon-search" id="onCheckSelect"
                                           style="position: relative;left: -8px;" update="checkContentOverlayPanel"
                                           process="@this,checkContentNames" action="#{mgrbean.clickCheckContent}"/>
                        </h:panelGrid>
                        <p:remoteCommand name="HideCheckContentOverlayPanel"
                                             action="#{mgrbean.hideCheckContentAction}"
                                             process="@this,checkContentOverlayPanel"
                                             update="checkContentNames,checkContentOverlayPanel"/>
                        <p:overlayPanel id="checkContentOverlayPanel" style="width:280px;"
                                        widgetVar="CheckContentOverlayPanel" for="onCheckSelect" dynamic="false"
                                        showCloseIcon="true" onHide="HideCheckContentOverlayPanel();"  hideEvent="mousedown">
                            <p:tree id="checkContentTree" selectionMode="checkbox" style="width: 250px;height: 320px;overflow-y: auto;"
                                    value="#{mgrbean.checkContentTree}" var="checkContent">
                                <p:ajax event="select"  process="@this" listener="#{mgrbean.onCheckNodeSelect}"/>
                                <p:ajax event="unselect"  process="@this" listener="#{mgrbean.onCheckNodeNoSelect}"/>
                                <p:treeNode>
                                    <p:outputLabel value="#{checkContent.codeName}"/>
                                </p:treeNode>
                            </p:tree>
                        </p:overlayPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="*" style="color:red;"/>
                    <p:outputLabel value="考核过程及结果描述："/>
                </p:column>
                <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                    <p:inputTextarea style="width:805px;margin-left: 7px;height: 70px;margin-top: 3px;resize: none;" autoResize="false" maxlength="1000" value="#{mgrbean.checkTechMain.checkDesc}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="*" style="color:red;"/>
                    <p:outputLabel value="考核结果："/>
                </p:column>
                <p:column style="text-align:left;" >
                    <p:selectOneRadio  value="#{mgrbean.checkTechMain.checkRst}" style="width: 190px;">
                        <f:selectItem itemValue="1" itemLabel="合格"/>
                        <f:selectItem itemValue="0" itemLabel="不合格"/>
                    </p:selectOneRadio>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color:red;"/>
                    <p:outputLabel value="考核专家："/>
                </p:column>
                <p:column style="text-align:left;" >
                    <h:panelGrid columns="3" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="expertLeaders"   style="width:240px;" maxlength="100"
                                     value="#{mgrbean.checkTechMain.checkExperts}" >
                        </p:inputText>
                        <p:commandLink id="onLeadersSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectPsnList}" process="@this,expertLeaders" style="position: relative;left: -30px;">
                            <f:setPropertyActionListener value="1" target="#{mgrbean.psnSelectType}"/>
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onPsnSelect}" update="expertLeaders">
                            </p:ajax>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -33px;"
                                       action="#{mgrbean.clearSelectLeaders}" process="@this"
                                       update="expertLeaders"/>
                    </h:panelGrid>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px; ">
                    <p:outputLabel value="*" style="color:red;"/>
                    <h:outputText value="被考核机构确认意见："/>
                </p:column>
                <p:column style="text-align:left;">
                    <p:selectOneRadio  value="#{mgrbean.checkTechMain.ifConfirm}" style="width: 190px;">
                        <f:selectItem itemValue="1" itemLabel="确认"/>
                        <f:selectItem itemValue="0" itemLabel="不确认"/>
                    </p:selectOneRadio>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="确认附件："/>
                </p:column>
                <p:column style="text-align:left;">
                    <p:outputPanel id="annexPanel">
                        <p:spacer width="5" rendered="#{null eq mgrbean.checkTechMain.filePath}"/>
                        <p:commandButton value="上传" action="#{mgrbean.uploadFile}"
                                         rendered="#{null eq mgrbean.checkTechMain.filePath}"
                                         update=":tabView:editForm:fileDialog">
                        </p:commandButton>
                        <p:spacer width="5" rendered="#{null ne mgrbean.checkTechMain.filePath}"/>
                        <p:commandButton value="查看" rendered="#{null ne mgrbean.checkTechMain.filePath}"
                                         action="#{mgrbean.toAnnexView()}">
                        </p:commandButton>
                        <p:spacer width="5" rendered="#{null ne mgrbean.checkTechMain.filePath}"/>
                        <p:commandButton value="删除" update="annexPanel" process="@this"
                                         action="#{mgrbean.delAnnex()}"
                                         rendered="#{null ne mgrbean.checkTechMain.filePath}">
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                        </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="考核专家确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                    <p:inputTextarea style="width:805px;margin-left: 7px;height: 70px;margin-top: 3px;resize: none;" autoResize="false" maxlength="1000" value="#{mgrbean.checkTechMain.expertAdv}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 38px;">
                    <p:outputLabel value="考核组组长确认意见："/>
                </p:column>
                <p:column style="text-align:left;padding: 9px 0px 9px 0px;"  colspan="3">
                    <p:inputTextarea autoResize="false" style="width:805px;margin-left: 7px;margin-top: 3px;height: 70px;resize: none;" maxlength="1000" value="#{mgrbean.checkTechMain.leaderAdv}"  rows="3" cols="5" scrollHeight="300"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true"  width="800">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：图片、PDF）" styleClass="diagTextClass"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要上传的文件！" id="fileUpload"
                                      label="文件选择" fileUploadListener="#{mgrbean.fileUpload}"
                                      invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，请重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                                      style="width:770px;" previewWidth="120"  cancelLabel="取消" update="@this"
                                      uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="10485760" />
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>