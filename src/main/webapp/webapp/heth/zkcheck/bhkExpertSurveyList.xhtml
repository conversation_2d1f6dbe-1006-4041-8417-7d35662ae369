<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkExpertSurveyListBean"-->
    <ui:param name="mgrbean" value="#{bhkExpertSurveyListBean}"/>
    <ui:param name="editPage" value="/webapp/heth/zkcheck/bhkExpertSurveyEdit.xhtml"/>
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style type="text/css">
            .searchTime input {
                width: 77px;
            }
            .myCalendar1 input {
                width: 180px;
            }
        </style>
    </ui:define>
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="专家判定"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color: transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="地区："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="0" onchange="onSearchNodeSelect()"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="unitPanel"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="体检机构："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:outputPanel id="unitPanel">
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                            process="@this"
                                            resetValues="true" update="unitName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="抽取类别："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkType}">
                    <f:selectItems value="#{mgrbean.bhkTypeList}" var="itm" itemValue="#{itm.rid}"
                                   itemLabel="#{itm.codeName}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="抽取日期："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchSDate}"
                                              endDate="#{mgrbean.searchEDate}" styleClass="searchTime"/>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="职业健康检查结论："/>
            </p:column>
            <p:column styleClass="cs-scv" style="padding-left:5px !important">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
                                        selectedIds="#{mgrbean.searchSelBhkrstIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="180" height="200"/>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="状态："/>
            </p:column>
            <p:column styleClass="cs-scv" >
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" styleClass="cs-break-word" style="width: 220px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="体检机构名称" styleClass="cs-break-word" style="width: 240px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="抽取类别" styleClass="cs-break-word" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="姓名" styleClass="cs-break-word" style="width: 140px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="职业健康检查结论" styleClass="cs-break-word" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="专家阅片/判定结论" styleClass="cs-break-word" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[6]}" rendered="#{itm[12] eq '1'}"/>
            <h:outputText value="#{itm[7]}" rendered="#{itm[12] eq '2'}"/>
        </p:column>
        <p:column headerText="专家建议体检结论" styleClass="cs-break-word" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="抽取日期" styleClass="cs-break-word" style="width: 80px;text-align: center;">
            <h:outputLabel value="#{itm[9]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="填报日期" styleClass="cs-break-word" style="width: 80px;text-align: center;">
            <h:outputLabel value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" styleClass="cs-break-word" style="width: 70px;text-align: center;">
            <h:outputText value="待判定" rendered="#{itm[11] eq 4}"/>
            <h:outputText value="判定完成" rendered="#{itm[11] eq 5}"/>
            <h:outputText value="省级复核" rendered="#{itm[11] eq 6}"/>
            <h:outputText value="复核完成" rendered="#{itm[11] eq 7}"/>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink action="#{mgrbean.modInitAction}" value="判定"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           rendered="#{itm[11] eq 4}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink action="#{mgrbean.modInitAction}" value="详情"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView" rendered="#{itm[11] ne 4}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>