<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

    <script type="text/javascript" src="/resources/js/namespace.js" />
    <script type="text/javascript"
            src="/resources/js/validate/system/validate.js" />
    <script type="text/javascript">
        //<![CDATA[
        function validateScoreVal(obj,index,subIndex,maxVal){
            try {
                var scoreVal = obj.value;
                var values = scoreVal.split(".");
                if(values.length > 1 && values[1] == ""){
                    obj.value = values[0];
                    scoreVal = obj.value;
                }
                if(null != scoreVal && undefined != scoreVal && "" != scoreVal && null != maxVal && undefined != maxVal){
                    if(scoreVal*1 > maxVal || scoreVal*1 < 0){
                        eval("showCheckSubMsgTip"+String(index)+"PK"+String(subIndex)+"()");
                        //obj.focus();
                    }else{
                        eval("refreshItemScore"+String(index)+"()");
                        eval("refreshDeductSelect"+String(index)+"PK"+String(subIndex)+"()");
                    }
                }else{
                    eval("refreshItemScore"+String(index)+"()");
                    eval("refreshDeductSelect"+String(index)+"PK"+String(subIndex)+"()");
                }
            }catch(e){
            }
        }
        //]]>
    </script>
    <style>
        .tableTr .ui-widget-content {
            border: 0px solid #a6c9e2;
        }
    </style>
    <h:form id="editForm2">
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editTitleGrid2">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="技术服务机构质量监测" />
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="buttonPanelId">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" rendered="#{1 ne mgrbean.curCheckTable.stateMark}"
                                 action="#{mgrbean.saveSubAction}" process="@this,:tabView:editForm2" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" rendered="#{1 ne mgrbean.curCheckTable.stateMark}"
                                 action="#{mgrbean.submitSubAction}" process="@this,:tabView:editForm2" >
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backSubAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>

        <p:sticky target="buttonPanelId" />
        <!-- 具体数据展示 -->
        <p:outputPanel  id="dataPanel">
            <p:panelGrid style="width:100%;height:100%;margin-top:10px;margin-bottom:5px;">
                <f:facet name="header">
                    <p:row>
                        <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                            <h:outputText value="#{null ne mgrbean.curCheckTable and null ne mgrbean.curCheckTable.fkByCheckTableId ? mgrbean.curCheckTable.fkByCheckTableId.checkName : ''}" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                        <font style="color: red;">*</font>
                        <h:outputLabel value="考核人："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:9px;" colspan="5">
                        <p:inputText value="#{mgrbean.curCheckTable.checkPsn}" style="width: 260px;" maxlength="100"/>
                        <p:outputLabel value="提示：多个考核人以中文逗号隔开！" style="color:blue;padding: 10px;" />
                    </p:column>
                </p:row>

            </p:panelGrid>
            <p:remoteCommand name="showScoreErrorMessage" process="@this,:tabView:editForm2"
                             action="#{mgrbean.showScoreErrorMessage}"  />
            <c:forEach var="checkItem" items="#{mgrbean.zwZkCheckItemList}" varStatus="TMR" >
                <p:remoteCommand name="refreshItemScore#{TMR.index}" process="@this,:tabView:editForm2"
                                 action="#{mgrbean.changeItemScore(checkItem)}" update="freshItemLabel#{TMR.index}"  />
                <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;overflow-x: hidden;"
                             styleClass="zwpx-table">
                    <p:row>
                        <p:column colspan="5" style="text-align:left;height: 30px; border-right: 0px;">
                            <h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
                                <div style="display:flex; ">
                                    <div style="width: 3px; height: 12px; background: #2e6e9e;margin-top: 1px;" />
                                    <label class="ui-outputlabel ui-widget"
                                           style=" font-size: 14px !important; margin-left: 5px;font-weight: 600;color: #2e6ea9;">
                                        #{null ne checkItem.fkByItemId ? checkItem.fkByItemId.codeName : ''}</label>
                                </div>
                            </h:panelGrid>
                        </p:column>
                        <p:column style="text-align: left;width: 450px;  border-left: 0px;">
                            <p:outputLabel value="分值：#{checkItem.checkVal} #{null ne checkItem.checkVal ? '分' : '  '}" rendered="#{checkItem.ifHasScore}" />
                            <p:outputLabel value="实得分：#{checkItem.scoreVal} #{null ne checkItem.scoreVal ? '分' : '  '}" style="margin-left: 20px;" rendered="#{checkItem.ifHasScore}"
                                           id="freshItemLabel#{TMR.index}" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <c:forEach items="#{checkItem.checkSubList}" var="checkSub" varStatus="SR">
                    <p:remoteCommand name="refreshDeductSelect#{TMR.index}PK#{SR.index}" process="@this,:tabView:editForm2"
                                     update="deductCauseRedId#{TMR.index}PK#{SR.index}"  />
                    <p:remoteCommand name="showCheckSubMsgTip#{TMR.index}PK#{SR.index}" process="@this,:tabView:editForm2" action="#{mgrbean.showCheckSubMsgTip(checkSub)}" />
                    <p:panelGrid style="width:100%;margin-top:0px;line-height: 14px;overflow-x: hidden;" styleClass="tableTr">
                        <p:row>
                            <p:column colspan="5" style="text-align:left;height: 30px;border-right: 0px;background-color: #dfeffc; border-top: 0px;border-bottom: 0px;">
                                <div style="line-height: 24px;"><h:outputText escape="false" value="#{null ne checkSub.fkByScoreId ? mgrbean.indexDescMap.get(checkSub.fkByScoreId.rid) : ''}" /></div>
                            </p:column>
                            <p:column style="text-align: left; width: 450px;border-left: 0px;background-color: #dfeffc;border-top: 0px;border-bottom: 0px;">
                                <p:outputLabel rendered="#{checkSub.fkByScoreId.fkByItemTypeId.extendS1 ne 3}"
                                        value="分值：#{null ne checkSub.fkByScoreId ? checkSub.fkByScoreId.score : ''} #{null ne checkSub.fkByScoreId.score ? '分' : '  '}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid style="width:100%;margin-top:0px;line-height: 14px;overflow-x: hidden;"
                                 styleClass="zwpx-table">
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                                <font style="color: red;">*</font>
                                <h:outputLabel value="结果："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;width:180px;" rendered="#{checkSub.fkByScoreId.fkByItemTypeId.extendS1 == 3}">
                                <p:selectOneRadio value="#{checkSub.soreRstRid}"  >
                                    <f:selectItems value="#{mgrbean.scoreRstSimpleList}" var="subScoreRst" itemLabel="#{subScoreRst.codeName}" itemValue="#{subScoreRst.rid}"/>
                                    <p:ajax event="change" process="@this,:tabView:editForm2" update="deductCauseRedId#{TMR.index}PK#{SR.index}"/>
                                </p:selectOneRadio>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;width: 180px;" rendered="#{checkSub.fkByScoreId.fkByItemTypeId.extendS1 ne 3}">
                                <h:inputText value="#{checkSub.scoreVal}" onkeyup="SYSTEM.verifyNum3(this,3,2,false)"
                                             onfocus="SYSTEM.verifyNum3(this,3,2,true)" onblur="SYSTEM.verifyNum3(this,3,2,true),validateScoreVal(this,#{TMR.index},#{SR.index},#{null ne checkSub.fkByScoreId ? checkSub.fkByScoreId.score : null})"
                                             styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all" />
                            </p:column>
                            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                                <!-- 实得分小于评估项分值 -->
                                <h:outputLabel value="#{((null ne mgrbean.unhgSoreRstRid and checkSub.soreRstRid == mgrbean.unhgSoreRstRid) or
                                (null ne checkSub.scoreVal and null ne checkSub.fkByScoreId and null ne checkSub.fkByScoreId.score and checkSub.scoreVal lt checkSub.fkByScoreId.score)) ?
                                '&lt;font style=\'color: red;\' &gt;*&lt;/font&gt;存在问题：' : '存在问题：'}" escape="false" id="deductCauseRedId#{TMR.index}PK#{SR.index}" />
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;" colspan="3">
                                <div style="display: table-row;">
                                    <div style="display:table-cell;">
                                        <p:inputText  style="width:700px;" readonly="true" value="#{checkSub.deductCause}" id="deductCauseNames#{TMR.index}PK#{SR.index}"
                                                      onclick="document.getElementById('tabView:editForm2:selDeductLink#{TMR.index}PK#{SR.index}').click();"/>
                                    </div>
                                    <div style="display:table-cell;vertical-align: middle;">
                                        <p:commandLink styleClass="ui-icon ui-icon-search" type="button" style="position: relative;left: -20px;"
                                                       id="selDeductLink#{TMR.index}PK#{SR.index}" action="#{mgrbean.initDeductCause(checkSub,TMR.index,SR.index)}"
                                                       process="@this"
                                                       resetValues="true"
                                        />
                                    </div>
                                    <div style="display:table-cell;vertical-align: middle;">
                                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                       action="#{mgrbean.clearDeductCause(checkSub)}" style="position: relative;left: -15px;"
                                                       process="@this" update="deductCauseNames#{TMR.index}PK#{SR.index}"
                                        >
                                        </p:commandLink>
                                    </div>
                                </div>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 33px;width: 180px;">
                                <h:outputLabel value="备注："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:9px;" colspan="5">
                                <h:inputText value="#{checkSub.rmk}" styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all" style="width: 1080px;" maxlength="200" />
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </c:forEach>
            </c:forEach>
            <p:dialog header="扣分原因选择" widgetVar="DeductCauseDialog"
                      resizable="false" width="650" height="480" modal="true" id="deductCauseDialog">
                <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
                    <h:panelGrid columns="3"
                                 style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span
                                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                        <p:commandButton value="确定" icon="ui-icon-check"
                                         action="#{mgrbean.surePickDeductCause}"
                                         process="@this,selectedDeductCauseTable"  resetValues="true"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('DeductCauseDialog').hide();" process="@this" />
                    </h:panelGrid>
                </p:outputPanel>
                <table width="100%">
                    <tr>
                        <td style="text-align: left;padding-left: 3px">
                            <h:panelGrid columns="10" id="searchDeductCausePanel">
                                <p:outputLabel value="扣分原因：" styleClass="zwx_dialog_font" />
                                <p:inputText value="#{mgrbean.deductQuery}" style="width: 160px;" maxlength="50">
                                    <p:ajax event="keyup" update="selectedDeductCauseTable" process="@this,searchDeductCausePanel" listener="#{mgrbean.executeQueryDeduct}" />
                                </p:inputText>
                            </h:panelGrid>
                        </td>
                    </tr>
                </table>
                <p:dataTable var="itm" value="#{mgrbean.showScoreDeductList}" id="selectedDeductCauseTable"
                             paginator="true" rows="#{10}"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                             rowsPerPageTemplate="#{10}" lazy="true" emptyMessage="暂无数据！"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             paginatorPosition="bottom">
                    <p:column headerText="选择" style="text-align:center;width:33px;">
                        <p:selectBooleanCheckbox value="#{itm.selected}">
                            <p:ajax event="change" listener="#{mgrbean.selectDeductCause(itm)}" process="@this" />
                        </p:selectBooleanCheckbox>
                    </p:column>
                    <p:column headerText="扣分原因" >
                        <h:outputText value="#{null ne itm.fkByDeductId ? itm.fkByDeductId.codeName : ''}"/>
                    </p:column>
                </p:dataTable>
            </p:dialog>
        </p:outputPanel>
    </h:form>
</ui:composition>