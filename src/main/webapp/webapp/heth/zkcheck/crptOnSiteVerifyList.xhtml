<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.CrptOnSiteVerifyListBean"-->
    <ui:param name="mgrbean" value="#{crptOnSiteVerifyListBean}"/>
    <!-- 核查页面 -->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/crptOnSiteVerifyEdit.xhtml"/>
    <!-- 详情页面 -->
    <!--<ui:param name="viewPage" value="/webapp/heth/zkcheck/crptOnSiteVerifyView.xhtml"/>-->
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style>
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位现场核查"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 process="@this,mainGrid" update=":tabView:mainForm:dataTable"
                                 action="#{mgrbean.searchAction}"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="报告日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptBeginDate}"
                                              endDate="#{mgrbean.searchRptEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="检测报告名称（编号）："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;width:280px;">
                <p:inputText value="#{mgrbean.searchRptNo}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 5px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" style="width: 220px;height:22px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width: 300px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="联系人" style="width: 50px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width: 80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="检测报告名称（编号）" style="width: 175px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width: 75px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
        </p:column>
        <p:column headerText="状态" style="width: 45px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="检测机构" style="width: 275px;padding-left: 8px;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="操作" style="">
            <p:commandLink value="核查" rendered="#{itm[7] eq 1}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.modInitAction}" resetValues="true">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[7] eq 2}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.modInitAction}" resetValues="true">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
    </ui:define>
</ui:composition>