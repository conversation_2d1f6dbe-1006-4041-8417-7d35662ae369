<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.BhkExpertSurveyListBean"-->
    <ui:define name="insertEditScripts">
        <style>

        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <h:outputText value="专家判定"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky" style="display:flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm"
                                 rendered="#{mgrbean.extractBhk.state eq '4'}">
                </p:commandButton>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.beforeRevocationAction}" process="@this,:tabView:editForm"
                                 rendered="#{mgrbean.extractBhk.state eq '5' or  mgrbean.extractBhk.state eq '6'}">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView,:tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:sticky target="sticky"/>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                             oncomplete="PF('ConfirmDialog').hide();" update=":tabView"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:confirmDialog message="确定要撤销吗？" header="消息确认框" widgetVar="ConfirmRevocationDialog">
            <p:commandButton value="确定" action="#{mgrbean.revocationAction}" icon="ui-icon-check"
                             oncomplete="PF('ConfirmRevocationDialog').hide();" update=":tabView"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmRevocationDialog').hide();" type="button"/>
        </p:confirmDialog>
    </ui:define>
    <ui:define name="insertEditContent">
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="体检信息" style="margin-top: 5px;margin-bottom: 5px;" toggleable="false">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="basicPanel">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;">
                        <h:outputText value="体检机构地区："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.zoneName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="体检机构名称："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.fkByOrgId.unitName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;">
                        <h:outputText value="抽取类别："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExtractTypeId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="姓名："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.personName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="身份证号："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.idcEncrypted}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">

                        <h:outputText value="接害工龄（年）："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.tchbadrsntim}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="职业健康检查结论："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.fkByBhkRstId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText
                                value="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' ? '职业健康检查胸片结论' : '职业健康检查听力检测结论'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.fkByChestRstId.codeName}" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}"/>
                        <h:outputText value="#{mgrbean.extractBhk.fkByHearRstId.codeName}" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2'}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText
                                value="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' ? '胸片号' : '听力图谱编号'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv cs-break-word">
                        <h:outputText value="#{mgrbean.extractBhk.chestNo}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h" style="text-align: right;">
                        <h:outputText value="体检报告附件："/>
                    </p:column>
                    <p:column styleClass="cs-scv" style="text-align: left;">
                        <p:outputPanel id="bhkRptPanel">
                            <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                             rendered="#{mgrbean.ifShowFileDownload}"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                             value="#{mgrbean.extractBhk.bhkRptPath}"/>
                                <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                             value="#{mgrbean.bhkRptFileName}"/>
                                <p:fileDownload value="#{mgrbean.streamedContent}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.extractBhk.bhkRptPath}')"
                                             rendered="#{!mgrbean.ifShowFileDownload}"/>
                        </p:outputPanel>
                    </p:column>
                    <p:column styleClass="cs-scl" style="text-align: right;">
                        <h:outputText
                                value="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' ? '胸片附件' : '听力图谱附件'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="3">
                        <p:outputPanel id="chestPanel">
                            <p:commandButton ajax="false" value="下载" immediate="true" process="@this"
                                             rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' or mgrbean.ifShowChest}"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <f:setPropertyActionListener target="#{mgrbean.filePath}"
                                                             value="#{mgrbean.extractBhk.chestPath}"/>
                                <f:setPropertyActionListener target="#{mgrbean.fileName}"
                                                             value="#{mgrbean.chestFileName}"/>
                                <p:fileDownload value="#{mgrbean.streamedContent}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.extractBhk.chestPath}')"
                                             rendered="#{!mgrbean.ifShowChest  and mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2'}"/>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="判定信息" style="margin-top: 5px;margin-bottom: 5px;" toggleable="false">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5"  rendered="#{mgrbean.extractBhk.state eq '4'}">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="text-align: right;">
                        <h:outputText value="*" style="color:red;" />
                        <h:outputText value="专家阅片结论：" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}"/>
                        <h:outputText value="专家判定结论：" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2'}"/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="text-align: left;">
                        <p:selectOneMenu style="width: 188px; margin-left: 2px;"
                                         value="#{mgrbean.extractBhk.fkByExpertChestId.rid}"
                                         rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.expertChestList}" var="itm" itemLabel="#{itm.codeName}"
                                           itemValue="#{itm.rid}"/>
                            <p:ajax event="change" listener="#{mgrbean.changeExpertChest(true)}"
                                    process="@this,:tabView:editForm" update=":tabView:editForm"/>
                        </p:selectOneMenu>

                        <p:selectOneRadio value="#{mgrbean.extractBhk.fkByExpertHearRstId.rid}"  style="width:120px;"
                                          rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2'}">
                            <f:selectItems value="#{mgrbean.expertHearRstList}" var="itm" itemLabel="#{itm.codeName}"
                                           itemValue="#{itm.rid}"/>
                            <p:ajax event="change" listener="#{mgrbean.changeExpertChest(true)}"
                                    process="@this,:tabView:editForm" update=":tabView:editForm"/>
                        </p:selectOneRadio>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="text-align: right;">
                        <h:outputText value="*" style="color:red;" rendered="#{!mgrbean.ifReadOnly}"/>
                        <h:outputText value="专家建议体检结论："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' ? '1' : '3'}">
                        <p:selectOneMenu style="width: 188px; margin-left: 2px;"
                                         value="#{mgrbean.extractBhk.fkByExpertRstId.rid}" disabled="#{mgrbean.ifReadOnly}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.expertRstList}" var="itm" itemLabel="#{itm.codeName}"
                                           itemValue="#{itm.rid}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="text-align: right;" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}">
                        <h:outputText value="*" style="color:red;" />
                        <h:outputText value="胸片质量："/>
                    </p:column>
                    <p:column styleClass="cs-scv"  rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}">
                        <p:selectOneMenu style="width: 188px; margin-left: 2px;"
                                         value="#{mgrbean.extractBhk.fkByChestLevelId.rid}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.chestLevelList}" var="itm" itemLabel="#{itm.codeName}"
                                           itemValue="#{itm.rid}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;text-align: right;">
                        <h:outputText value="*" style="color:red;" />
                        <h:outputText value="填报单位："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;text-align: left;">
                        <p:inputText value="#{mgrbean.extractBhk.checkUnitName}" maxlength="100" style="width: 180px;"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;text-align: right;">
                        <h:outputText value="*" style="color:red;" />
                        <h:outputText value="填报人："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;text-align: left;">
                        <p:inputText value="#{mgrbean.extractBhk.checkPsn}" maxlength="50" style="width: 180px;"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;text-align: right;">
                        <h:outputText value="*" style="color:red;" />
                        <h:outputText value="填报日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                    showOtherMonths="true" size="11" navigator="true"
                                    yearRange="c-10:c+10" converterMessage="填报日期，格式输入不正确！"
                                    showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                                    value="#{mgrbean.extractBhk.checkDate}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first" style="text-align: right;">
                        <h:outputText value="*" style="color:red;" />
                        <h:outputText value="联系电话："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="5">
                        <p:inputText  value="#{mgrbean.extractBhk.checkLinktel}" maxlength="20" style="width: 180px;"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" rendered="#{mgrbean.extractBhk.state ne '4'}">
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="专家阅片结论：" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}"/>
                        <h:outputText value="专家判定结论：" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2'}"/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExpertChestId.codeName}" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}"/>
                        <h:outputText value="#{mgrbean.extractBhk.fkByExpertHearRstId.codeName}" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2'}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="专家建议体检结论："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' ? '1' : '3'}">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExpertRstId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}">
                        <h:outputText value="胸片质量："/>
                    </p:column>
                    <p:column styleClass="cs-scv" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1'}">
                        <h:outputText value="#{mgrbean.extractBhk.fkByChestLevelId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h" style="width: 12%;">
                        <h:outputText value="填报单位："/>
                    </p:column>
                    <p:column styleClass="cs-scv cs-break-word" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.checkUnitName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" style="width: 12%;">
                        <h:outputText value="填报人："/>
                    </p:column>
                    <p:column styleClass="cs-scv  cs-break-word" style="width: 20%;">
                        <h:outputText value="#{mgrbean.extractBhk.checkPsn}"/>
                    </p:column>
                    <p:column styleClass="cs-scl" style="width: 12%;">
                        <h:outputText value="填报日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputLabel value="#{mgrbean.extractBhk.checkDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </h:outputLabel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="联系电话："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="5">
                        <h:outputText value="#{mgrbean.extractBhk.checkLinktel}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="省级复核结果" style="margin-top: 5px;margin-bottom: 5px;" toggleable="false" rendered="#{mgrbean.extractBhk.state eq 7}">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="provResult">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 12%;text-align: right;">
                        <h:outputText value="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' ? '省级复核阅片结论' : '省级复核判定结论'}："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;text-align: left;">
                        <h:outputText value="#{mgrbean.extractBhk.fkByProvChestId.codeName}" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '1' }"/>
                        <h:outputText value="#{mgrbean.extractBhk.fkByProvHearRstId.codeName}" rendered="#{mgrbean.extractBhk.fkByExtractTypeId.extendS1 eq '2' }"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;text-align: right;">
                        <h:outputText value="省级复核体检结论修改意见："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 20%;text-align: left;">
                        <h:outputText value="同意" rendered="#{mgrbean.extractBhk.provAdvice eq 1}"/>
                        <h:outputText value="不同意" rendered="#{mgrbean.extractBhk.provAdvice eq 2}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 12%;text-align: right;">
                        <h:outputText value="其他："/>
                    </p:column>
                    <p:column styleClass="cs-scv cs-break-word">
                        <h:outputText value="#{mgrbean.extractBhk.provOtherRmk}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first" >
                        <h:outputText value="复核单位："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w cs-break-word" >
                        <h:outputText value="#{mgrbean.extractBhk.provUnitName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w" >
                        <h:outputText value="复核人："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w cs-break-word" >
                        <h:outputText value="#{mgrbean.extractBhk.provCheckPsn}"/>
                    </p:column>
                    <p:column styleClass="cs-scl-w">
                        <h:outputText value="复核日期："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputLabel value="#{mgrbean.extractBhk.provCheckDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </h:outputLabel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-first" >
                        <h:outputText value="联系电话："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="5">
                        <h:outputText value="#{mgrbean.extractBhk.provLinktel}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
    </ui:define>
</ui:composition>