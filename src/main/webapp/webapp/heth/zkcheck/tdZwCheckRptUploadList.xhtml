<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zkcheck.web.TdZwCheckRptUploadListBean"-->
    <ui:param name="mgrbean" value="#{tdZwCheckRptUploadListBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/zkcheck/tdZwCheckRptUploadEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/zkcheck/tdZwCheckRptUploadView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">

        </script>
        <style>
            .myCalendar input {
                width: 78px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业卫生技术服务机构检测报告上传"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable"
                                 process="@this,mainGrid" />
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInitAction}"
                                 process="@this"/>
                <p:inputText style="visibility: hidden;width: 0;"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:200px;height: 38px;">
                <h:outputText value="报告日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:250px;">
                <zwx:CalendarDynamicLimitComp styleClass="myCalendar"  startDate="#{mgrbean.searchReportBeginDate}"
                                              endDate="#{mgrbean.searchReportEndDate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:250px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="检测报告编号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchRptNo}" style="width: 180px;"  />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="检测报告编号" style="width: 10%;text-align: center;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="检测报告名称" style="width: 15%;padding-left: 5px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="地区" style="width:15%;padding-left: 5px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width:15%;padding-left: 5px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="联系人" style="width:6%;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width:8%;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:8%;text-align: center;">
            <h:outputLabel value="#{itm[7]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width:5%;text-align: center;">
            <h:outputText value="待提交" rendered="#{0 eq itm[8]}"/>
            <h:outputText value="待审核" rendered="#{1 eq itm[8]}"/>
            <h:outputText value="已退回" rendered="#{3 eq itm[8]}"/>
            <h:outputText value="审核通过" rendered="#{2 eq itm[8]}"/>
        </p:column>
        <p:column headerText="退回原因" style="width:10%;padding-left: 5px;">
            <h:outputLabel id="backRsn" rendered="#{itm[8]==3}" styleClass="zwx-tooltip"
                           value="#{itm[9]}" />
            <p:tooltip for="backRsn" value="#{itm[9]}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{itm[8]==3}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 5px;">
            <!-- 待审核 审核通过 -->
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}" onclick="hideTooltips();"
                           process="@this" update=":tabView" rendered="#{1 eq itm[8] or 2 eq itm[8]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <!-- 待提交 已退回 -->
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" onclick="hideTooltips();"
                           rendered="#{0 eq itm[8] or 3 eq itm[8]}"
                           process="@this" update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{0 eq itm[8] or 3 eq itm[8]}"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}"
                           process="@this" update=":tabView" rendered="#{0 eq itm[8] or 3 eq itm[8]}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>