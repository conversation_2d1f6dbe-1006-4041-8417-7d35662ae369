<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:op="http://java.sun.com/jsf/html">
    <p:fieldset legend="用人单位信息">
        <p:panelGrid style="margin-bottom: 10px;width: 100%;" id="crptInfoPanel">
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;">
                    <p:outputLabel value="单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.crptName}"/>
                </p:column>
                <p:column style="text-align:right;width: 15%;">
                    <p:outputLabel value="所属区域："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <h:outputText escape="false"
                                  value="#{mgrbean.tdZwCheckRpt.fkByZoneId.zoneType>3?mgrbean.tdZwCheckRpt.fkByZoneId.fullName.substring(mgrbean.tdZwCheckRpt.fkByZoneId.fullName.indexOf('_')+1,mgrbean.tdZwCheckRpt.fkByZoneId.fullName.length()):mgrbean.tdZwCheckRpt.fkByZoneId.zoneName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="社会信用代码："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByCrptId.institutionCode}"/>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="单位地址："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.address}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="行业类别："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByIndusTypeId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="经济性质："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByEconomyId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="企业规模："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.fkByCrptSizeId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;">
                    <p:outputLabel value="联系人："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkMan}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;">
                    <p:outputLabel value="联系电话："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.linkPhone}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="检查报告上传" style="margin-top: 10px;">
        <p:panelGrid style="margin-bottom: 10px;width: 100%;" id="rptPanel">
            <p:row>
                <p:column style="text-align:right;width: 15%;height: 40px;">
                    <p:outputLabel value="工作场所名称："/>
                </p:column>
                <p:column style="text-align:left;" colspan="3">
                    <div style="width: 89%;padding: 10px 0px 10px 0px;margin-left: 8px;">
                        <p:outputLabel value="#{mgrbean.tdZwCheckRpt.workName}" />
                    </div>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 40px;width: 15%;">
                    <p:outputLabel value="报告日期："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;width: 25%;">
                    <h:outputText value="#{mgrbean.tdZwCheckRpt.rptDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
                <p:column style="text-align:right;width: 15%;">
                    <p:outputLabel value="检测报告名称（编号）："/>
                </p:column>
                <p:column style="text-align:left;padding:0px 3px 0px 11px;">
                    <p:outputLabel value="#{mgrbean.tdZwCheckRpt.rptNo}" style="width: 73%;margin-right: 10px;"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.tdZwCheckRpt.filePath}')"
                                     rendered="#{mgrbean.tdZwCheckRpt.filePath != null}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
</ui:composition>
