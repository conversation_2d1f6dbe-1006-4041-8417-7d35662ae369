<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwCheckSummaryBean}"/>
    <ui:param name="editPage" value= "/webapp/heth/zkcheck/tdZwZkCheckSummaryEdit.xhtml" />
    <ui:param name="viewPage" value= "/webapp/heth/zkcheck/tdZwZkCheckSummaryView.xhtml" />
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <h:outputStylesheet name="css/annexViewDialog.css"/>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制考核汇总录入"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" />
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this">
                    <p:resetInput target=":tabView:editForm:editGrid" />
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:36px;">
                <h:outputText value="地区：" />
            </p:column>
            <p:column style="text-align:left;width:200px;">
                <p:outputPanel id="zonearea">
                    <div style="border-color: #ffffff;margin: 0px;padding: 0px;display: table-cell;" >
                        <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
                                               zoneCodeNew="#{mgrbean.searchZoneCode}"
                                               zoneName="#{mgrbean.searchZoneName}" />
                    </div>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="机构名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;width: 206px;" >
                <p:inputText value="#{mgrbean.searchOrgName}" style="width: 180px;" placeholder="模糊查询" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="考核日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="考核日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchCheckStartTime}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="考核日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchCheckEndTime}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:36px; ">
                <h:outputText value="被考核机构确认意见：" />
            </p:column>
            <p:column style="text-align:left;padding-left:7px;">
                <p:selectManyCheckbox value="#{mgrbean.searchLeaderAdvList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="1" itemLabel="确认"/>
                    <f:selectItem itemValue="0" itemLabel="不确认"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.searchStateMarkList}" converter="javax.faces.Integer" >
                    <f:selectItem itemValue="0" itemLabel="待提交" />
                    <f:selectItem itemValue="1" itemLabel="已提交" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:columnGroup type="header">
            <p:row>
                <p:column headerText="地区" style="width:80px;text-align: center"/>
                <p:column headerText="机构名称" style="width:120px;text-align: center;"/>
                <p:column headerText="考核类型" style="width:80px;text-align: center;"/>
                <p:column headerText="被考核机构确认意见" style="width:80px;text-align: center;"/>
                <p:column headerText="考核日期" style="width:50px;text-align: center;"/>
                <p:column headerText="状态" style="width:50px;text-align: center;"/>
                <p:column headerText="操作" style="width:240px;text-align: center;"/>
            </p:row>
        </p:columnGroup>

        <p:column>
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column style="text-align: left;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column style="text-align: center; ">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputText value="确认" rendered="#{1 eq itm[4]}"/>
            <h:outputText value="不确认" rendered="#{0 eq itm[4]}"/>
        </p:column>
        <p:column style="text-align:center; ">
            <h:outputLabel value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align:center; ">
            <h:outputLabel value="#{(1 eq itm[6]) ? '已提交' : '待提交'}"/>
        </p:column>
        <p:column>
            <p:spacer width="5" rendered="#{1 ne itm[6]}" />
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" rendered="#{1 ne itm[6]}"
                           process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{1 eq itm[6]}" />
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}"
                           process="@this"  update=":tabView" rendered="#{1 eq itm[6]}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{1 ne itm[6]}" />
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}"
                           process="@this"  update=":tabView" rendered="#{1 ne itm[6]}" >
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>
