<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_turn.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjStadItemsStatusListBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
	
	<ui:define name="insertScripts">
		<script type="application/javascript">
		//<![CDATA[
			function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
			//]]>
		</script>
		
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
	</ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="标准值配置"/>
            </p:column>
        </p:row>
    </ui:define>

	<!-- 按钮 -->
    <ui:define name="insertButtons">
    	
    	<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataView"
					process="@this" />
				<p:commandButton value="保存" icon="ui-icon-disk" id="searchBtn1" action="#{mgrbean.saveAction}" update="dataView" oncomplete="hideStatus()"
					process="@this,:tabView" rendered="#{mgrbean.state==0}" onclick="showStatus()" />
				<p:commandButton value="提交" icon="ui-icon-check" id="searchBtn2" action="#{mgrbean.submitAction}" update=":tabView"
					process="@this,:tabView" rendered="#{mgrbean.state==0}" onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="撤销" icon="ui-icon-cancel" id="searchBtn3" action="#{mgrbean.cancleAction}" update=":tabView" 
					process="@this,:tabView" rendered="#{mgrbean.state==1}"/>				
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:292px;">
                <h:outputText value="体检项目：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
				<table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="itemName"
                                         value="#{mgrbean.searchItemName}"
                                         style="width: 700px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selItemLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink id="selItemLink" styleClass="ui-icon ui-icon-search"
                                           action="#{mgrbean.selItemCodeAction}" process="@this"
                                           style="position: relative;left: -28px !important;">
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onItemCodeAction}" process="@this"
                                        resetValues="true" update="itemName" />
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="itemName" action="#{mgrbean.clearItemCode}">
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
        </p:row>
    </ui:define>

	<ui:define name="insertContents">
		<p:outputPanel id="dataView">
			<p:panelGrid style="width:99.5%;margin-bottom:5px;" >
				<f:facet name="header">
					<p:row>
						<p:column  styleClass="ui-state-default" style="text-align: center;width:200px;">
							<h:outputText value="项目分类"></h:outputText>
						</p:column>
						<p:column  styleClass="ui-state-default" style="text-align: center;width:200px;">
							<h:outputText value="项目名称"></h:outputText>
						</p:column>
						<p:column  styleClass="ui-state-default" style="text-align: center;width:200px;">
							<h:outputText value="判断模式"></h:outputText>
						</p:column>
						<p:column  styleClass="ui-state-default" style="text-align: center;width:200px;">
							<h:outputText value="性别"></h:outputText>
						</p:column>
						<p:column  styleClass="ui-state-default" style="text-align: center;width:200px;">
							<h:outputText value="计量单位"></h:outputText>
						</p:column>
						<p:column  styleClass="ui-state-default" style="text-align: center;width:300px;">
							<h:outputText value="最小值" ></h:outputText>
						</p:column>
						<p:column styleClass="ui-state-default" style="text-align: center;width:300px;" >
							<h:outputText value="最大值"></h:outputText>
						</p:column>
					</p:row>
				</f:facet>
				<c:forEach items="#{mgrbean.resultList}" var="data" varStatus="row">
					<p:row>
						<p:column style="#{mgrbean.state==1?'line-height: 24px;':''}text-align: center;" rendered="#{data[7]!='0'}" rowspan="#{data[7]}">
                            <h:outputText value="#{data[0]}" rendered="#{data[15]!=1 and mgrbean.state==1}" />
                            <h:outputText value="#{data[0]}（原始值）" rendered="#{data[15]==1 and mgrbean.state==1 and data[16]==1}" />
                            <h:outputText value="#{data[0]}（修正值）" rendered="#{data[15]==1 and mgrbean.state==1 and data[16]==2}" />
                            <h:panelGrid style="border-color:transparent;text-align: center;width: 100%;text-align: center;" rendered="#{mgrbean.state!=1}">
                                <p:row>
                                    <p:column style="line-height: 24px;">
                                        <h:outputText value="#{data[0]}"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:selectOneMenu value="#{data[16]}" rendered="#{data[15]==1}" style="#{mgrbean.state==1?'pointer-events:none;':''}width: 90px;">
                                            <f:selectItem itemValue="" itemLabel="&#45;&#45;请选择&#45;&#45;"/>
                                            <f:selectItem itemValue="1" itemLabel="原始值"/>
                                            <f:selectItem itemValue="2" itemLabel="修正值"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                            </h:panelGrid>
						</p:column>
						<p:column style="text-align: center;line-height: 24px;" rendered="#{data[12] != '0'}" rowspan="#{data[12]}" >
							<h:outputText value="#{data[1]}"></h:outputText>
						</p:column>
						<p:column style="text-align: center;" rendered="#{data[12] != '0'}" rowspan="#{data[12]}">
							<p:selectOneMenu value="#{data[9]}" rendered="#{mgrbean.state==0 and data[13] == 2}" style="width: 80px;">
								<f:selectItem itemValue="1" itemLabel="定性"></f:selectItem>
								<f:selectItem itemValue="2" itemLabel="定量"></f:selectItem>
								<p:ajax event="change" process="@this, dataView" update="dataView"
										listener="#{mgrbean.changeJdgptn(data)}" >
								</p:ajax>
							</p:selectOneMenu>
							<h:outputText value="#{data[9] == 1 ? '定性' : '定量'}"
										  rendered="#{mgrbean.state==1 or data[13] == 1}" ></h:outputText>
						</p:column>
						<p:column style="text-align: center;line-height: 24px;">
							<h:outputText value=" " rendered="#{data[9] == 1}"></h:outputText>
							<h:outputText value="#{data[10] == null ? '通用' : (data[10] == 1 ? '男' : '女')}" rendered="#{data[9] == 2}"></h:outputText>
						</p:column>
						<p:column style="text-align: center;">
							<p:selectOneMenu value="#{data[2]}" rendered="#{mgrbean.state==0 and data[9] != 1 and data[14] != 1}" style="width: 150px;">
								<f:selectItems value="#{mgrbean.itemUnitRelMap.get(data[8])}" var="unitRel"
											   itemValue="#{unitRel.fkByMsruntId.rid}"
											   itemLabel="#{unitRel.fkByMsruntId.codeName}"/>
								<p:ajax event="change" process="@this,dataView" update="dataView"
										listener="#{mgrbean.changeUnitRel(data)}" >
								</p:ajax>
							</p:selectOneMenu>
							<h:outputText value=" " rendered="#{data[9] == 1}"></h:outputText>
							<h:outputText value="#{mgrbean.unitMap.get(data[2])}" rendered="#{mgrbean.state==1 and data[9] != 1}"></h:outputText>
							<h:outputText value="#{mgrbean.unitMap.get(data[2])}" rendered="#{mgrbean.state==0 and data[9] == 2 and data[14] == 1}"></h:outputText>
						</p:column>
						<p:column style="text-align: center;line-height: 24px;">
							<p:inputText value="#{data[3]}"  style="width:250px;" rendered="#{mgrbean.state==0 and data[9] != 1}"
										 onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)" ></p:inputText>
							<h:outputText value="#{data[3]}" rendered="#{mgrbean.state==1 and data[9] != 1}"></h:outputText>
							<h:outputText value=" " rendered="#{data[9] == 1}"></h:outputText>
						</p:column>
						<p:column style="text-align: center;line-height: 24px;">
							<p:inputText value="#{data[4]}"   style="width:250px;" rendered="#{mgrbean.state==0 and data[9] != 1}"
										 onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)" ></p:inputText>
							<h:outputText value="#{data[4]}" rendered="#{mgrbean.state==1 and data[9] != 1}"></h:outputText>
							<h:outputText value=" " rendered="#{data[9] == 1}"></h:outputText>
						</p:column>
					</p:row>
				</c:forEach>
				<p:row rendered="#{null==mgrbean.resultList or mgrbean.resultList.size()==0}">
					<p:column colspan="7">
						<h:outputText value="未查询到数据！"></h:outputText>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:outputPanel>
	</ui:define>
</ui:composition>











