<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">

<f:view contentType="text/html">
    <h:head>
        <title>体检项目选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>
    </h:head>

    <h:body style="overflow-y:hidden;"  onload="document.getElementById('itemForm:pym').focus();">
        <h:form id="itemForm">
        	<h:outputStylesheet name="css/ui-tabs.css"/>
            <p:outputPanel styleClass="zwx_toobar_42">
                <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{tbTjItemsSelectListCommBean.submitAction}"
                                     process="@this,selectedItemTable"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     action="#{tbTjItemsSelectListCommBean.dialogClose}" process="@this"/>
                </h:panelGrid>
            </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchItemPanel">
                            <p:outputLabel value="项目分类：" styleClass="zwx_dialog_font"/>
                            <p:selectOneMenu id="firstCodeNo" value="#{tbTjItemsSelectListCommBean.itemSortId}">
                                <f:selectItem itemValue="" itemLabel="--全部--"/>
                                <f:selectItems value="#{tbTjItemsSelectListCommBean.itemSortList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{(itm.levelIndex == '3') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}"/>
                                <p:ajax event="change" listener="#{tbTjItemsSelectListCommBean.searchAction}" process="@this,searchItemPanel,selectedItemTable" update="selectedItemTable"/>
                            </p:selectOneMenu>
                            <p:spacer width="25px" />
                            <p:outputLabel value="名称/拼音码：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{tbTjItemsSelectListCommBean.searchNamOrPy}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedItemTable" process="@this,searchItemPanel,selectedItemTable" listener="#{tbTjItemsSelectListCommBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable id="selectedItemTable" value="#{tbTjItemsSelectListCommBean.displayList}" var="itm"
                         paginator="true" rows="8" emptyMessage="没有数据！"  selection="#{tbTjItemsSelectListCommBean.selectList}"
                         paginatorPosition="bottom" lazy="true" rowKey="#{itm.rid}">
                <p:column selectionMode="multiple" style="width:30px;text-align:center"/>         
                <!-- <p:column headerText="选择" style="text-align:center;width:30px;">
                    <p:selectBooleanCheckbox value="#{itm.ifSelected}"/>
                </p:column> -->

                <p:column headerText="项目分类" style="width:35%;text-align:center;">
                    <h:outputText value="#{itm.tsSimpleCode.codeName}" />
                </p:column>
                <p:column headerText="项目名称" style="text-align:center;">
                    <h:outputText value="#{itm.itemName}" />
                </p:column>
            </p:dataTable>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>