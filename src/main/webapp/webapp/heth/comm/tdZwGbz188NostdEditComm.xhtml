<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/webapp/heth/comm/tbTjBhkInfo.xhtml">

    <ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
    <ui:param name="birthIfShow" value="true"></ui:param>
    <ui:param name="pageParam" value="1"/>

    <!-- 标题 -->
    <ui:define name="titleContent">
        <p:panelGrid style="width:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <p:outputLabel value="GBZ188不规范数据上报" rendered="#{mgrbean.ifModify}"/>
                        <p:outputLabel value="GBZ188不规范数据详情" rendered="#{!mgrbean.ifModify}"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="buttons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="暂存" action="#{mgrbean.saveAction}" icon="ui-icon-disk"
                                 process="@this,nostdDesc" rendered="#{mgrbean.newFlow.state==0 or mgrbean.newFlow.state==2 or mgrbean.newFlow.state==4 or mgrbean.newFlow.state==6 }"/>
                <p:commandButton value="提交" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                                 process="@this,nostdDesc" rendered="#{mgrbean.newFlow.state==0 or mgrbean.newFlow.state==2 or mgrbean.newFlow.state==4 or mgrbean.newFlow.state==6}">
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="#{(mgrbean.newFlow.state==2 or (mgrbean.ifCityDirect ==1 and mgrbean.newFlow.state==4)) ?'初审':((mgrbean.ifCityDirect ==0 and mgrbean.newFlow.state==4)?'复审':'终审')}退回原因"
                                 icon="icon-alert" action="#{mgrbean.initDialog}" style="color:red;"
                                 process="@this" oncomplete="PF('ReasonDialog').show();"
                                 update="reasonDialog"
                                 rendered="#{mgrbean.newFlow.state==2 or mgrbean.newFlow.state==6 or mgrbean.newFlow.state==4}">
                    <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="true"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{mgrbean.backAction}"
                                 update=":tabView" immediate="true"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <ui:define name="editContent">
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="不规范原因"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="不规范原因：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea readonly="true"
                                     rows="5" autoResize="false"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.tjBhkInfoBean.tdTjBhkShow.lackMsg}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <p:outputLabel value="*" style="color: red" rendered="#{mgrbean.ifModify}"/>
                    <p:outputLabel value="不规范原因说明：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" id="nostdDesc"
                                     style="resize: none;width: 594px;"
                                     maxlength="100" readonly="#{!mgrbean.ifModify}"
                                     value="#{mgrbean.gbz188Nostd.nostdDesc}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="#{(mgrbean.newFlow.state==2 or (mgrbean.ifCityDirect ==1 and mgrbean.newFlow.state==4)) ?'初审':((mgrbean.ifCityDirect ==0 and mgrbean.newFlow.state==4)?'复审':'终审')}退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.backRsn}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="reasonContent" maxlength="100" readonly="#{mgrbean.readOnly}"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         process="@this,reasonContent" oncomplete="datatableOffClick()"
                                         action="#{mgrbean.returnAction}"
                                         rendered="#{!mgrbean.readOnly}" />
                        <p:commandButton value="确定" styleClass="submit_btn" oncomplete="datatableOffClick()"
                                         onclick="PF('ReasonDialog').hide();"
                                         rendered="#{mgrbean.readOnly}" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>