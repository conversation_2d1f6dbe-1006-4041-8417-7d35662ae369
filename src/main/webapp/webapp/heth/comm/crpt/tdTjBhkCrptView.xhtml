<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css"/>
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdTjBHkCheckListBean"-->
    <!--用人单位-->
    <p:panelGrid style="margin-top: 10px;width:100%">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="用人单位情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="所属地区："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 180px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;">
                <p:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.institutionCode}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="是否分支机构："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="是" rendered="#{mgrbean.tdTjBhk.tbTjCrpt.ifSubOrg == 1}"/>
                <p:outputLabel value="否" rendered="#{mgrbean.tdTjBhk.tbTjCrpt.ifSubOrg ne 1}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.tsSimpleCodeByEconomyId.codeName}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.tsSimpleCodeByCrptSizeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel
                        value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndepend ==null ? '' : mgrbean.tjBhkInfoBean.tbTjCrptIndepend.linkman2}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="联系人电话："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel
                        value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndepend == null ?  '' : mgrbean.tjBhkInfoBean.tbTjCrptIndepend.linkphone2}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="职工总人数："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.workForce}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="接触职业病危害因素人数："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.holdCardMan}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="详细地址："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.address}"/>
            </p:column>
        </p:row>

        <p:row rendered="#{mgrbean.tdTjBhk.tbTjCrpt.ifSubOrg == 1}">
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="上级单位地区："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.fkByUpperUnitId.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="上级单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.fkByUpperUnitId.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="上级单位行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.fkByUpperUnitId.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--用工单位-->
    <p:panelGrid style="margin-top: 10px;width:100%"
                 rendered="#{mgrbean.tdTjBhk.tbTjCrpt.tsSimpleCodeByIndusTypeId.extendS1 == '2'}">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="用工单位情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="所属地区："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 180px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;">
                <p:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.institutionCode}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="是否分支机构："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="是" rendered="#{mgrbean.tdTjBhk.fkByEntrustCrptId.ifSubOrg == 1}"/>
                <p:outputLabel value="否" rendered="#{mgrbean.tdTjBhk.fkByEntrustCrptId.ifSubOrg ne 1}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.tsSimpleCodeByEconomyId.codeName}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.tsSimpleCodeByCrptSizeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel
                        value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust ==null ? '' : mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust.linkman2}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="联系人电话："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel
                        value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust == null ?  '' : mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust.linkphone2}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="职工总人数："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.workForce}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="接触职业病危害因素人数："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.holdCardMan}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="详细地址："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.address}"/>
            </p:column>
        </p:row>

        <p:row rendered="#{mgrbean.tdTjBhk.fkByEntrustCrptId.ifSubOrg == 1}">
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="上级单位地区："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.fkByUpperUnitId.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="上级单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.fkByUpperUnitId.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="上级单位行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel
                        value="#{mgrbean.tdTjBhk.fkByEntrustCrptId.fkByUpperUnitId.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>


</ui:composition>
