<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.CrptCheckBean"-->
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位审核" />
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="viewSticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" action="#{mgrbean.cancelAction}"
                                 rendered="#{mgrbean.ifShowCancel}" onclick="hideTooltips();"
                                 process="@this,:tabView"  update=":tabView">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="#{mgrbean.tipInfo}" style="color:blue;" rendered="#{not empty mgrbean.tipInfo}"/>
            </p:outputPanel>
            <p:sticky target="viewSticky" />
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <!--历次审核意见-->
        <p:fieldset legend="历次审核意见" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable value="#{mgrbean.historyList}" var="item" emptyMessage="暂无数据！">
                <p:column headerText="审核类型"  style="width:100px;text-align: center">
                    <p:outputLabel value="市级退回" rendered="#{item[7]==21}"/>
                    <p:outputLabel value="省级退回" rendered="#{item[7]==22 or item[7]==32}"/>
                    <p:outputLabel value="区县级审核通过" rendered="#{item[7]==31 or item[7]==43}"/>
                    <p:outputLabel value="市级审核通过" rendered="#{item[7]==33 or item[7]==41}"/>
                    <p:outputLabel value="省级审核通过" rendered="#{item[7]==42}"/>
                </p:column>
                <p:column headerText="审核意见" style="width:510px;">
                    <p:outputLabel value="#{item[1]}"/>
                </p:column>
                <p:column headerText="审核人"  style="width:150px;text-align: center">
                    <p:outputLabel value="#{item[2]}"/>
                </p:column>
                <p:column headerText="审核日期" style="width:140px;text-align: center">
                    <p:outputLabel value="#{item[3]}">
                        <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8"></f:convertDateTime>
                    </p:outputLabel>
                </p:column>
            </p:dataTable>
        </p:fieldset>
        <p:fieldset legend="基本信息" toggleable="true" toggleSpeed="500" id="baseInfo"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid  style="margin-bottom: 10px;width:100%" >
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="所属地区："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.tsZoneByZoneId ? '' :  mgrbean.tbTjCrpt.tsZoneByZoneId.fullName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="height: 30px;text-align:right;">
                        <p:outputLabel value="单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.crptName}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="社会信用代码："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' : mgrbean.tbTjCrpt.institutionCode}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="是否分支机构：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.ifSubOrg ? '' : (0 == mgrbean.tbTjCrpt.ifSubOrg ? '否' : (1 == mgrbean.tbTjCrpt.ifSubOrg ? '是' : ''))}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{null ne mgrbean.tbTjCrpt and 1 == mgrbean.tbTjCrpt.ifSubOrg}">
                    <p:column styleClass="column_title" style="height: 30px;text-align:right;">
                        <p:outputLabel value="上级单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.fkByUpperUnitId ? '' : mgrbean.tbTjCrpt.fkByUpperUnitId.crptName}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="height: 30px;text-align:right;">
                        <p:outputLabel value="单位地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.address}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="经济类型："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.tsSimpleCodeByEconomyId ? '' :  mgrbean.tbTjCrpt.tsSimpleCodeByEconomyId.codeName}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="企业规模：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.tsSimpleCodeByCrptSizeId ? '' :  mgrbean.tbTjCrpt.tsSimpleCodeByCrptSizeId.codeName}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:outputLabel value=""/>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="行业类别："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.tsSimpleCodeByIndusTypeId ? '' :  mgrbean.tbTjCrpt.tsSimpleCodeByIndusTypeId.codeName}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="邮政编码：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.postCode}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="法人："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.corporateDeputy}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="法人联系电话：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.phone}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="联系人："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.linkman2}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="联系人电话：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.linkphone2}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="职工人数："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.workForce}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="接触职业病危害因素人数：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.holdCardMan}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="生产工人数："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.workmanNum}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="外委人员数：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' :  mgrbean.tbTjCrpt.outsourceNum}" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="同社会信用代码关联单位" toggleable="true" toggleSpeed="500" id="relationUnitId"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable value="#{mgrbean.showRelationUnitList}" var="item" emptyMessage="暂无数据！">
                <p:column headerText="地区"  style="width:220px;padding-left: 8px;height: 30px;">
                    <p:outputLabel value="#{item[1]}"/>
                </p:column>
                <p:column headerText="单位名称" style="width:240px;padding-left: 8px;">
                    <p:outputLabel value="#{item[2]}"/>
                    <p:outputLabel rendered="#{null ne mgrbean.tbTjCrpt and item[0] == mgrbean.tbTjCrpt.rid}"
                                   style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;white-space:nowrap;">
                        <h:outputText value="本单位" style="color:#FFFFFF"></h:outputText>
                    </p:outputLabel>
                </p:column>
                <p:column headerText="社会信用代码"  style="width:160px;text-align: center;">
                    <p:outputLabel value="#{item[3]}"/>
                </p:column>
                <p:column headerText="是否分支机构" style="width:90px;text-align: center;">
                    <p:outputLabel value="是" rendered="#{1 == item[4]}" />
                    <p:outputLabel value="否" rendered="#{0 == item[4]}" />
                </p:column>
                <p:column headerText="单位地址" style="width:360px;padding-left: 8px;">
                    <p:outputLabel value="#{item[6]}"/>
                </p:column>
                <p:column headerText="上级单位名称" style="padding-left: 8px;width:180px;">
                    <h:outputText id="fatherUnit" value="#{item[5]}" styleClass="zwx-tooltip" style="-webkit-line-clamp: 1;"/>
                    <p:tooltip for="fatherUnit" style="max-width:450px;">
                        <p:outputLabel value="#{item[5]}"/>
                    </p:tooltip>
                </p:column>
            </p:dataTable>
        </p:fieldset>
    </ui:define>
</ui:composition>