<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.CrptCheckBean"-->
    <ui:param name="mgrbean" value="#{crptCheckBean}"/>
    <!-- 审核页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/crpt/check/crptCheckEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/crpt/check/crptCheckView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <script type="text/javascript">
            //<![CDATA[
            function getDownloadFileClick() {
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }
            //]]>
        </script>
        <style>
            .myCalendar1 input {
                width: 78px;
            }

            table.ui-selectoneradio td label {
                white-space: nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 process="@this,mainGrid" action="#{mgrbean.searchAction}"
                                 oncomplete="datatableOffClick()"/>
                <p:commandButton value="全部审核" icon="ui-icon-check" id="allCheckBtn"
                                 process="@this,:tabView:mainForm:mainGrid"
                                 action="#{mgrbean.openCheckConfirmDialog(2)}"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportBtn"
                                 action="#{mgrbean.exportBefore}" process="@this,:tabView:mainForm:mainGrid"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="用人单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;"
                             maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchInstitutionCode}" style="width: 180px;" maxlength="25"
                             placeholder="模糊查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 7px;width:280px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="indusTypeName"
                                         value="#{mgrbean.selectIndusTypeNames}"
                                         style="width: 180px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           style="position: relative;left: -28px !important;"
                                           id="selIndusTypeLink"
                                           process="@this" action="#{mgrbean.selSimpleCodeAction}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
                                        resetValues="true" update="indusTypeName"/>
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;width: 280px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="encomyName"
                                         value="#{mgrbean.selectEconomyNames}"
                                         style="width: 180px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selEconomyLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           style="position: relative;left: -28px !important;"
                                           id="selEconomyLink"
                                           process="@this" action="#{mgrbean.selSimpleCodeAction}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
                                        resetValues="true" update="encomyName"/>
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;">
                <zwx:SimpleCodeManyComp selectedIds="#{mgrbean.selectCrptSizeIds}"
                                        simpleCodeList="#{mgrbean.crptSizeList}"
                                        height="200"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <!--<p:column selectionMode="multiple" style="width:30px;text-align:center;"
                  disabledSelection="#{!itm[13]}"/>-->
        <p:column headerText="地区" style="width: 220px;height:22px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width: 340px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
            <p:outputLabel rendered="#{1==itm[14]}" style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;color: white;white-space:nowrap;">
                <h:outputText value="分支机构" />
            </p:outputLabel>

        </p:column>
        <p:column headerText="社会信用代码" style="width: 160px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="行业类别" style="width: 160px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="经济类型" style="width: 120px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="企业规模" style="width: 80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="状态" style="width: 80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[8]}" rendered="#{4 ne itm[15] and 6 ne itm[15]}"/>
            <h:outputText value="#{itm[8]}" style="color: red;" rendered="#{4 == itm[15] or 6 == itm[15]}"/>
        </p:column>
        <p:column headerText="退回原因" style="width: 220px;padding-left: 8px;">
            <h:outputText id="returnReason" value="#{itm[9]}" styleClass="zwx-tooltip" style="-webkit-line-clamp: 1;"/>
            <p:tooltip for="returnReason" style="max-width:450px;">
                <p:outputLabel value="#{itm[9]}" escape="false"/>
            </p:tooltip>

        </p:column>
        <p:column headerText="操作" style="width: 60px;text-align: center;">
            <p:commandLink value="审核" rendered="#{itm[13]}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView" action="#{mgrbean.modView}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="详情"  rendered="#{!itm[13]}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid"
                           update=":tabView"  action="#{mgrbean.viewInitAction}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}" />
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <!-- 批量/全部审核弹出框 -->
        <ui:include src="/webapp/heth/comm/crpt/check/checkConfirmDialogComm.xhtml"/>
    </ui:define>
</ui:composition>