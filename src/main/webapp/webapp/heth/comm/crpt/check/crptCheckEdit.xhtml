<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.CrptCheckBean"-->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
            function removeExtraZonePael(elStr){
                //移除掉多余的地区框
                var el = jQuery(elStr);
                if(el.length>1){
                    el.each(function(index){
                        if(index>0){
                            $(this).remove();
                        }
                    });
                }
            }
            //]]>
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位审核" />
            </p:column>
        </p:row>
    </ui:define>

    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="保存" icon="ui-icon-disk"
                                 action="#{mgrbean.saveAction}" update=":tabView:editForm:editGrid"
                                 onclick="zwx_loading_start()" oncomplete="zwx_loading_stop()"
                                 process="@this,:tabView:editForm"></p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 onclick="hideTooltips();zwx_loading_start()" oncomplete="zwx_loading_stop()"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" onclick="hideTooltips();"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="#{mgrbean.tipInfo}" style="color:blue;" rendered="#{not empty mgrbean.tipInfo}"/>
            </p:outputPanel>
            <p:sticky target="sticky"></p:sticky>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="审核意见" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid  style="margin-bottom: 10px;width:100%" id="checkGrid">
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <h:outputText value="*" style="color:red;" />
                        <p:outputLabel value="审核结果："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:selectOneRadio  style="width:#{mgrbean.itemDisabled?'60px':'120px'};"  value="#{mgrbean.checkState}" >
                            <f:selectItem itemLabel="通过" itemValue="1" />
                            <c:if test="#{!mgrbean.itemDisabled}">
                                <f:selectItem itemLabel="退回" itemValue="2" />
                            </c:if>
                            <p:ajax event="change" process="@this,:tabView:editForm" listener="#{mgrbean.changeCheckState}" update=":tabView:editForm" />
                        </p:selectOneRadio>
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="审核意见：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputTextarea rows="1" autoResize="false"
                                         style="overflow-y:hidden;resize: none;width: 594px;"
                                         maxlength="100" value="#{mgrbean.checkRst}" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <!--历次审核意见-->
        <p:fieldset legend="历次审核意见" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                <p:dataTable value="#{mgrbean.historyList}" var="item" emptyMessage="暂无数据！">
                    <p:column headerText="审核类型"  style="width:100px;text-align: center">
                        <p:outputLabel value="市级退回" rendered="#{item[7]==21}"/>
                        <p:outputLabel value="省级退回" rendered="#{item[7]==22 or item[7]==32}"/>
                        <p:outputLabel value="区县级审核通过" rendered="#{item[7]==31 or item[7]==43}"/>
                        <p:outputLabel value="市级审核通过" rendered="#{item[7]==33 or item[7]==41}"/>
                        <p:outputLabel value="省级审核通过" rendered="#{item[7]==42}"/>
                    </p:column>
                    <p:column headerText="审核意见" style="width:510px;">
                        <p:outputLabel value="#{item[1]}"/>
                    </p:column>
                    <p:column headerText="审核人"  style="width:150px;text-align: center">
                        <p:outputLabel value="#{item[2]}"/>
                    </p:column>
                    <p:column headerText="审核日期" style="width:140px;text-align: center">
                        <p:outputLabel value="#{item[3]}">
                            <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8"></f:convertDateTime>
                        </p:outputLabel>
                    </p:column>
                </p:dataTable>
        </p:fieldset>
        <p:fieldset legend="基本信息" toggleable="true" toggleSpeed="500" id="baseInfo"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid  style="margin-bottom: 10px;width:100%" >
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="所属地区："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:outputLabel value="#{null ne mgrbean.editAreaZone ? mgrbean.editAreaZone.fullName : ''}" id="editAreaZoneLabel"/>
                        <p:spacer width="5" />
                        <p:commandButton icon="ui-icon-pencil" style="margin-top: 2px;margin-left: 3px;" value="地区变更" action="#{mgrbean.beforeChangeZoneAction}"
                                          process="@this,baseInfo" resetValues="true" oncomplete="removeExtraZonePael('#tabView\\:editForm\\:changeSearchZone\\:zonePanel')"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="height: 30px;text-align:right;">
                        <h:outputText value="*" style="color:red;" />
                        <p:outputLabel value="单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:inputText value="#{mgrbean.editCrptName}" style="width: 597px;" maxlength="50" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="社会信用代码："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt ? '' : mgrbean.tbTjCrpt.institutionCode}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="是否分支机构：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.ifSubOrg ? '' : (0 == mgrbean.tbTjCrpt.ifSubOrg ? '否' : (1 == mgrbean.tbTjCrpt.ifSubOrg ? '是' : ''))}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{null ne mgrbean.tbTjCrpt and 1 == mgrbean.tbTjCrpt.ifSubOrg}">
                    <p:column styleClass="column_title" style="height: 30px;text-align:right;">
                        <p:outputLabel value="上级单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:outputLabel value="#{null == mgrbean.tbTjCrpt or null == mgrbean.tbTjCrpt.fkByUpperUnitId ? '' : mgrbean.tbTjCrpt.fkByUpperUnitId.crptName}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="height: 30px;text-align:right;">
                        <p:outputLabel value="单位地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;padding-top: 0px; padding-bottom: 0px;" colspan="3">
                        <p:inputText value="#{mgrbean.editAddress}" style="width: 597px;" maxlength="100" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <h:outputText value="*" style="color:red;" />
                        <p:outputLabel value="经济类型："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <table>
                            <tr>
                                <td style="padding: 0;border-color: transparent;">
                                    <p:inputText id="economyName"
                                                 value="#{null == mgrbean.editEconomy ? null : mgrbean.editEconomy.codeName}"
                                                 style="width: 180px;cursor: pointer;"
                                                 onclick="document.getElementById('tabView:editForm:selEconomyLink').click();"
                                                 readonly="true"/>
                                </td>
                                <td style="border-color: transparent;">
                                    <p:commandLink styleClass="ui-icon ui-icon-search"
                                                   id="selEconomyLink"
                                                   action="#{mgrbean.selCodeTypeAction}" process="@this"
                                                   oncomplete="PF('selDialog').show()"
                                                   update=":tabView:editForm:selectedIndusTable,:tabView:editForm:searchPanel"
                                                   style="position: relative;left: -35px !important;">
                                        <f:setPropertyActionListener target="#{mgrbean.selCodeName}" value="经济类型"/>
                                    </p:commandLink>
                                </td>
                            </tr>
                        </table>
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <h:outputText value="*" style="color:red;" />
                        <p:outputLabel value="企业规模：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:selectOneMenu value="#{mgrbean.editCrptSizeId}"
                                         style="width: 186px;margin-bottom: -5px;">
                            <f:selectItem itemLabel="--请选择--" itemValue="" />
                            <f:selectItems value="#{mgrbean.crptSizeList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <h:outputText value="*" style="color:red;" />
                        <p:outputLabel value="行业类别："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <table>
                            <tr>
                                <td style="padding: 0;border-color: transparent;">
                                    <p:inputText id="indusTypeName"
                                                 value="#{null == mgrbean.editIndusType ? null : mgrbean.editIndusType.codeName}"
                                                 style="width: 180px;cursor: pointer;"
                                                 onclick="document.getElementById('tabView:editForm:selIndusTypeLink').click();"
                                                 readonly="true"/>
                                </td>
                                <td style="border-color: transparent;">
                                    <p:commandLink styleClass="ui-icon ui-icon-search"
                                                   id="selIndusTypeLink"
                                                   action="#{mgrbean.selCodeTypeAction}" process="@this"
                                                   oncomplete="PF('selIndusTypeDialog').show()"
                                                   update=":tabView:editForm:selectedIndusCodeTable,:tabView:editForm:searchIndusPanel"
                                                   style="position: relative;left: -35px !important;">
                                        <f:setPropertyActionListener target="#{mgrbean.selCodeName}" value="行业类别"/>
                                    </p:commandLink>
                                </td>
                            </tr>
                        </table>
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="邮政编码：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputText value="#{mgrbean.editPostCard}" maxlength="6" style="width: 180px;" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="法人："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:inputText value="#{mgrbean.editCorporateName}" maxlength="25" style="width: 180px;" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="法人联系电话：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputText value="#{mgrbean.editCorporatePhone}" maxlength="15" style="width: 180px;" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="联系人："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:inputText value="#{mgrbean.editLinkMan2}" maxlength="25" style="width: 180px;" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="联系人电话：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputText value="#{mgrbean.editLinkPhone2}" maxlength="15" style="width: 180px;" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="职工人数："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:inputText value="#{mgrbean.editWorkForce}" maxlength="9" style="width: 180px;"
                                     onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="接触职业病危害因素人数：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputText value="#{mgrbean.editHoldCardMan}" maxlength="9" style="width: 180px;"
                                     onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="生产工人数："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:6px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:inputText value="#{mgrbean.editWorkmanNum}" maxlength="9" style="width: 180px;"
                                     onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="外委人员数：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputText value="#{mgrbean.editOutsourceNum}" maxlength="9" style="width: 180px;"
                                     onkeyup="SYSTEM.verifyNum3(this,9,0,false)" onblur="SYSTEM.verifyNum3(this,9,0,true)" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="同社会信用代码关联单位" toggleable="true" toggleSpeed="500" id="relationUnitId"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable value="#{mgrbean.showRelationUnitList}" var="item" emptyMessage="暂无数据！">
                <p:column headerText="地区"  style="width:220px;padding-left: 8px;height: 30px;">
                    <p:outputLabel value="#{item[1]}"/>
                </p:column>
                <p:column headerText="单位名称" style="width:240px;padding-left: 8px;">
                    <p:outputLabel value="#{item[2]}"/>
                    <p:outputLabel rendered="#{null ne mgrbean.tbTjCrpt and item[0] == mgrbean.tbTjCrpt.rid}" style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;white-space:nowrap;">
                        <h:outputText value="本单位" style="color:#FFFFFF"></h:outputText>
                    </p:outputLabel>
                </p:column>
                <p:column headerText="社会信用代码"  style="width:160px;text-align: center;">
                    <p:outputLabel value="#{item[3]}"/>
                </p:column>
                <p:column headerText="是否分支机构" style="width:90px;text-align: center;">
                    <p:outputLabel value="是" rendered="#{1 == item[4]}" />
                    <p:outputLabel value="否" rendered="#{0 == item[4]}" />
                </p:column>
                <p:column headerText="单位地址" style="width:360px;padding-left: 8px;">
                    <p:outputLabel value="#{item[6]}"/>
                </p:column>
                <p:column headerText="上级单位名称" style="width:180px;padding-left: 8px;">
                    <h:outputText id="fatherUnit" value="#{item[5]}" styleClass="zwx-tooltip" style="-webkit-line-clamp: 1;"/>
                    <p:tooltip for="fatherUnit" style="max-width:450px;">
                        <p:outputLabel value="#{item[5]}"/>
                    </p:tooltip>
                </p:column>
                <p:column headerText="操作" style="text-align: center;width: 80px;">
                    <p:commandLink value="改为主体机构"   process="@this,:tabView:editForm"
                                   rendered="#{null ne item[4] and 1 == item[4]}"
                                   update=":tabView:editForm:baseInfo,:tabView:editForm:relationUnitId" action="#{mgrbean.changeUpperUnit}"  >
                        <p:confirm header="消息确认框" message="将该单位切换为主体机构，其他单位全部更新为分支机构，是否确定？" icon="ui-icon-alert" />
                        <f:setPropertyActionListener target="#{mgrbean.editUpperUnitId}" value="#{item[0]}" />
                    </p:commandLink>
                </p:column>
            </p:dataTable>
        </p:fieldset>


        <!-- 经济类型/行业类别弹出框 -->
        <p:dialog header="经济类型选择" widgetVar="selDialog" width="600" height="380" modal="true" resizable="false" >
            <ui:include src="/webapp/heth/comm/codeRadioPannelComm.xhtml" />
        </p:dialog>

        <p:dialog header="行业类别选择" widgetVar="selIndusTypeDialog" width="800" height="430" modal="true" resizable="false" >
            <ui:include src="/webapp/system/indusTypeCodePanel.xhtml">
                <ui:param name="indusbean" value="#{mgrbean.codePanelBean}"/>
            </ui:include>
        </p:dialog>

        <p:dialog header="地区变更" widgetVar="changeZoneDialog" width="600" height="118" modal="true" resizable="false" >
            <p:outputPanel id="changeZonePanel">
                <p:panelGrid  style="margin-bottom: 10px;width:100%" >
                    <p:row>
                        <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="变更地区："/>
                        </p:column>
                        <p:column style="text-align:left;height: 30px;padding-left:10px;padding-top: 5px; padding-bottom: 0px;">
                            <zwx:ZoneSingleNewComp zoneList="#{mgrbean.changeZoneList}"
                                                   zoneCodeNew="#{mgrbean.changeZoneCode}" id="changeSearchZone"
                                                   zonePaddingLeft="0" zoneHeight="300"
                                                   zoneName="#{mgrbean.changeZoneName}"
                                                   realZoneType="#{mgrbean.changeZoneType}"
                                                   zoneId="#{mgrbean.changeZoneId}"
                                                   ifShowTrash="false"/>
                            <div style="padding-top: 5px;">
                            <h:outputText style="color: red;" value="说明："/>
                            </div><div style="padding-top: 5px;">
                            <h:outputText value="1、变更地区请选择至街道！" style="color: blue;" escape="false" />
                            </div><div style="padding-top: 5px;padding-bottom:5px;">
                            <h:outputText value="2、跨辖区变更，需要变更后地区的管理机构确认！" style="color: blue;" escape="false" />
                            </div>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </p:outputPanel>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确认" styleClass="submit_btn"
                                         process="@this,changeSearchZone" icon="ui-icon-check"
                                         action="#{mgrbean.confirmZoneChange}" />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" onclick="PF('changeZoneDialog').hide();"
                                         process="@this" immediate="true" icon="ui-icon-close"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>
