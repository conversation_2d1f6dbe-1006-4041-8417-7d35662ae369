<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="危急值详情"/>
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
		        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
						action="#{mgrbean.backAction}" process="@this"
						update=":tabView" />
					<p:inputText style="visibility: hidden;width: 0"/>
		    </h:panelGrid>
			<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;" rendered="#{!mgrbean.ifBhkInfoExist}">
				<h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
				<h:outputLabel value="该数据已被体检机构删除，请确认！" style="color:blue;"></h:outputLabel>
			</p:outputPanel>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">

		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGrid3">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6" style="text-align:left;height: 20px;">
						<p:outputLabel value="危急值情况"/>
					</p:column>
				</p:row>
			</f:facet>
			<p:row >
				<p:column style="text-align:left;padding-left:6px;" >
					<p:dataTable id="cfwTable" value="#{mgrbean.tdZwyjDangerBhk.tdZwyjDangerResultList}"
								 emptyMessage="没有数据！" var="itm">
						<p:column headerText="项目名称" style="text-align:center;width:160px;">
							<h:outputText value="#{itm.fkByDangerId.fkByItemId.itemName}"/>
						</p:column>
						<p:column headerText="体检结果" style="text-align:center;width:160px;">
							<h:outputText value="#{itm.itemRst}"/>
						</p:column>
						<p:column headerText="参考值" style="text-align:center;width:150px;">
							<h:outputText value="#{itm.itemStdvalue}"/>
						</p:column>
						<p:column headerText="计量单位" style="text-align:center;width: 150px;">
							<h:outputText value="#{itm.msrunt}"></h:outputText>
						</p:column>
						<p:column headerText="危急值低值" style="text-align:center;width:150px;" >
							<h:outputText value="#{itm.fkByDangerId.lVal}" ></h:outputText>
						</p:column>
						<p:column headerText="危急值高值" style="text-align:center;width:150px;" >
							<h:outputText value="#{itm.fkByDangerId.gVal}" ></h:outputText>
						</p:column>
					</p:dataTable>
				</p:column>
			</p:row>
		</p:panelGrid>

		<ui:include src="tbTjBhkInfo.xhtml">
			<ui:param name="pageParam" value="1"/>
			<ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
		</ui:include>
	</ui:define>
</ui:composition>