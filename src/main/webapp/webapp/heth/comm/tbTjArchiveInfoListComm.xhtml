<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">

    <h:form id="viewForm">
        <!-- 标题 -->
        <p:panelGrid style="width:100%;margin-bottom:5px;" id="viewTitleGrid" rendered="#{pageParam == null}">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <p:outputLabel value="体检详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <!-- 标题 -->
        <ui:insert name="titleContent"/>
        <!-- 按钮 -->
        <ui:insert name="buttons"/>

        <p:outputPanel styleClass="zwx_toobar_42" rendered="#{pageParam == null}">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{mgrbean.forwardEditPage}"
                                 update=":tabView" immediate="true"/>
            </h:panelGrid>
        </p:outputPanel>

        <ui:insert name="editContent"/>

        <!-- 体检基本信息 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="体检基本信息"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="体检编号："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width: 180px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.bhkCode}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width: 180px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.personName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px">
                    <h:outputLabel value="性别："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.sex}"/>
                </p:column>

            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="证件号码："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.idc}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="出生日期："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="**********" rendered="#{!birthIfShow}"/>
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.brth}" rendered="#{birthIfShow}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputLabel>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="婚姻状况："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.isxmrd=='否'?'未婚':(mgrbean.tdTjBhkShow.isxmrd=='是'?'已婚':mgrbean.tdTjBhkShow.isxmrd)}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.tbTjCrpt.crptName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="所属部门："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.dpt}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="在岗状态："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.tsSimpleCode.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="体检类型："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.bhkType == 1}" value="从业体检"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.bhkType == 2}" value="职业普通体检"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.bhkType == 3}" value="职业健康检查"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.bhkType == 4}" value="放射卫生体检"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.bhkType == 5}" value="学校卫生体检"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.bhkType == 6}" value="福利性体检"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="体检机构："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.tbTjSrvorg.unitName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="体检日期："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.bhkDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputLabel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="工种："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.workName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="总工龄："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;;">
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.wrklnt == null}" value="0"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.wrklnt != null}"
                                   value="#{mgrbean.tdTjBhkShow.wrklnt}">
                        <f:convertNumber type="number" minFractionDigits="0"/>
                    </h:outputLabel>
                    <h:outputLabel value="年"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.wrklntmonth == null}" value="0"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.wrklntmonth != null}"
                                   value="#{mgrbean.tdTjBhkShow.wrklntmonth}"/>
                    <h:outputLabel value="月"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="接害工龄："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.tchbadrsntim == null}" value="0"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.tchbadrsntim != null}"
                                   value="#{mgrbean.tdTjBhkShow.tchbadrsntim}">
                        <f:convertNumber type="number" minFractionDigits="0"/>
                    </h:outputLabel>
                    <h:outputLabel value="年"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.tchbadrsnmonth == null}" value="0"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjBhkShow.tchbadrsnmonth != null}"
                                   value="#{mgrbean.tdTjBhkShow.tchbadrsnmonth}"/>
                    <h:outputLabel value="月"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="危害因素："/>
                </p:column>
                <p:column colspan="5" style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.badrsn}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="体检结果："/>
                </p:column>
                <p:column colspan="5" style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.bhkrst}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="主检建议："/>
                </p:column>
                <p:column colspan="5" style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjBhkShow.mhkadv}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 人员职业史 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column style="text-align:left;height: 20px;">
                        <p:outputLabel value="人员职业史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="width: 100%">
                    <p:dataTable var="Emhistoriesitm" value="#{mgrbean.tdTjEmhistoryList}"
                                 emptyMessage="没有数据">
                        <p:column headerText="起止日期" style="width:140px;text-align: center">
                            <h:outputLabel value="#{Emhistoriesitm.stastpDate}"/>
                        </p:column>
                        <p:column headerText="工作单位名称" style="width:180px;padding-left: 5px;">
                            <h:outputLabel value="#{Emhistoriesitm.unitName}"/>
                        </p:column>
                        <p:column headerText="部门车间" style="width:100px;text-align: center">
                            <h:outputLabel value="#{Emhistoriesitm.department}"/>
                        </p:column>
                        <p:column headerText="工种" style="width:100px;text-align: center">
                            <h:outputLabel value="#{Emhistoriesitm.workType}"/>
                        </p:column>
                        <p:column headerText="危害因素" style="width:200px;padding-left: 5px;">
                            <h:outputLabel value="#{Emhistoriesitm.prfraysrt}"/>
                        </p:column>
                        <p:column headerText="防护措施" style="padding-left: 5px;">
                            <h:outputLabel value="#{Emhistoriesitm.defendStep}"/>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 人员放射职业史 -->
        <p:panelGrid style="width:100%;margin-top:10px;" rendered="#{mgrbean.tdTjBhkShow.bhkType == 4}">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;height: 20px;">
                        <p:outputLabel value="人员放射职业史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="width: 100%">
                    <p:dataTable var="TjEmhistoriesitm2" value="#{mgrbean.tdTjEmhistoryList2}"
                                 emptyMessage="没有数据">
                        <p:column headerText="起止日期" style="width:120px;text-align: center">
                            <h:outputLabel value="#{TjEmhistoriesitm2.stastpDate}"/>
                        </p:column>
                        <p:column headerText="工作单位名称" style="width:150px;padding-left: 5px;">
                            <h:outputLabel value="#{TjEmhistoriesitm2.unitName}"/>
                        </p:column>
                        <p:column headerText="部门车间" style="width:100px;text-align: center">
                            <h:outputLabel value="#{TjEmhistoriesitm2.department}"/>
                        </p:column>
                        <p:column headerText="工种" style="width:100px;text-align: center">
                            <h:outputLabel value="#{TjEmhistoriesitm2.workType}"/>
                        </p:column>
                        <p:column headerText="放射线种类" style="width:100px;text-align: center">
                            <h:outputLabel value="#{TjEmhistoriesitm2.fsszl}"/>
                        </p:column>
                        <p:column headerText="每日工作时数或工作量" style="width:100px;text-align: center">
                            <h:outputLabel value="#{TjEmhistoriesitm2.prfwrklod}"/>
                        </p:column>
                        <p:column headerText="累积受照剂量" style="width:100px;text-align: center">
                            <h:outputLabel value="#{TjEmhistoriesitm2.prfshnvlu}"/>
                        </p:column>
                        <p:column headerText="过量照射史" style="padding-left: 5px;">
                            <h:outputLabel value="#{TjEmhistoriesitm2.prfexcshn}"/>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 既往病史 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;height: 20px;">
                        <p:outputLabel value="既往病史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="width: 100%">
                    <p:dataTable var="TjAnamnesisesitm" value="#{mgrbean.tdTjBhkShow.tdTjAnamnesises}"
                                 emptyMessage="没有数据">
                        <p:column headerText="疾病名称" style="width:150px;padding-left: 5px;">
                            <h:outputLabel value="#{TjAnamnesisesitm.hstnam}"/>
                        </p:column>
                        <p:column headerText="诊断日期" style="width:120px;text-align: center">
                            <h:outputLabel value="#{TjAnamnesisesitm.hstdat}"/>
                        </p:column>
                        <p:column headerText="诊断单位" style="width:180px;padding-left: 5px;">
                            <h:outputLabel value="#{TjAnamnesisesitm.hstunt}"/>
                        </p:column>
                        <p:column headerText="治疗经过" style="width:280px;padding-left: 5px;">
                            <h:outputLabel value="#{TjAnamnesisesitm.hstcruprc}"/>
                        </p:column>
                        <p:column headerText="转归" style="padding-left: 5px;">
                            <h:outputLabel value="#{TjAnamnesisesitm.hstlps}"/>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 月经史 -->
        <p:panelGrid style="width:100%;margin-top:10px;" rendered="#{mgrbean.tdTjBhkShow.sex == '女'}">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="月经史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="初潮(岁)："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width:160px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.mnrage}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="经期（天）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width:160px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.mns}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="周期（天）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.cyc}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="停经年龄（岁）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.mnlage}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="是否经期："/>
                </p:column>
                <p:column colspan="3" style="text-align:left;padding-right:3px;">
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.isxmns == 0}" value="否"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.isxmns == 1}" value="是"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 婚姻史 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="婚姻史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="结婚日期："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width:160px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.mrydat}" />
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="配偶接触放射线情况："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.cplrdtcnd}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="配偶职业及健康状况："/>
                </p:column>
                <p:column colspan="3" style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.cplprfhthcnd}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 生育史 -->
        <p:panelGrid style="width:100%;margin-top:10px;" rendered="#{mgrbean.tdTjBhkShow.sex == '女'}">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="生育史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="现有子女（人）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width: 160px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.chldqty}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="流产（次）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width: 160px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.abrqty}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="早产（次）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.slnkqty}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="死产（次）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.stlqty}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="畸胎（次）："/>
                </p:column>
                <p:column colspan="3" style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.trsqty}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 烟酒史 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="烟酒史"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="吸烟情况："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width: 160px;">
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.smksta == 0}" value="不吸烟"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.smksta == 1}" value="偶尔吸"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.smksta == 2}" value="经常吸"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="吸烟量（支/天）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;width: 160px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.smkdayble}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputLabel value="烟龄（年）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.smkyerqty}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <h:outputLabel value="饮酒情况："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.winsta == 0}" value="不饮酒"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.winsta == 1}" value="偶尔饮"/>
                    <h:outputLabel rendered="#{mgrbean.tdTjExmsdata.winsta == 2}" value="经常饮"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="酒量（ml/天）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.windaymlx}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputLabel value="酒龄（年）："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.winyerqty}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 其他 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;height: 20px;">
                        <p:outputLabel value="其他"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;  ">
                    <h:outputLabel value="家族史："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.jzs}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="个人史："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.grs}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
                    <h:outputLabel value="其他："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <h:outputLabel value="#{mgrbean.tdTjExmsdata.oth}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 自觉症状 -->
        <p:panelGrid style="width:100%;margin-top:10px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;height: 20px;">
                        <p:outputLabel value="自觉症状"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="width: 100%">
                    <p:dataTable var="tdTjSymptomItm" value="#{mgrbean.tdTjBhkShow.tdTjSymptoms}"
                                 emptyMessage="没有数据">
                        <p:column headerText="症状" style="width:180px;padding-left: 5px;">
                            <h:outputLabel value="#{tdTjSymptomItm.symId.codeName}"/>
                        </p:column>
                        <p:column headerText="其他症状" style="padding-left: 5px;">
                            <h:outputLabel value="#{tdTjSymptomItm.othsym}"/>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!--多个体检板块的展示-->
        <p:outputPanel style="margin-top: 10px;" id="archivePanel" binding="#{mgrbean.archivePanel}"/>

        <table style="width: 100%">
            <tr>
                <td style="text-align: center;">
                    <p:commandButton value="返回" icon="ui-icon-close" id="backBtn2" action="#{mgrbean.forwardEditPage}"
                                     update=":tabView" immediate="true" rendered="#{pageParam == null}"/>
                </td>
            </tr>
        </table>
    </h:form>
</ui:composition>