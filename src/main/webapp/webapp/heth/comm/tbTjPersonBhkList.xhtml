<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TbTjPersonSearchBean"-->
<!-- 标题栏 -->
<ui:define name="insertEditTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="人员基本信息"/>
        </p:column>
    </p:row>
</ui:define>

<ui:define name="insertEditScripts">
	<style>
		.add {
			width:100px;
			height:2px;
			background-color: red;
		}
		.button {
		    background-color: #4CAF50 !important;; 
		    border: none;
		    color: white;
		    padding: 1px 1px;
		    text-align: center;
		    text-decoration: none;
		    display: inline-block;
		    font-size: 16px;
		    cursor: pointer;
		}
</style>
</ui:define>




<!-- 编辑页面的按钮 -->
<ui:define name="insertEditButtons">
    <p:outputPanel styleClass="zwx_toobar_42">
        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
            <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}"
                             process="@this" update=":tabView" immediate="true"/>
        </h:panelGrid>
    </p:outputPanel>
</ui:define>

<!-- 页面的内容-->
<ui:define name="insertEditContent">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:150px;height: 30px;">
            <p:outputLabel value="证件号码："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;width: 320px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.idc}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:150px;">
            <p:outputLabel value="姓名："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.personName}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;">
            <p:outputLabel value="出生日期："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="**********">
            </h:outputLabel>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <p:outputLabel value="年龄："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.age}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;">
            <p:outputLabel value="性别："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.sex}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <p:outputLabel value="婚姻状况："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.isxmrd=='否'?'未婚':(mgrbean.tdTjBhk.isxmrd=='是'?'已婚':mgrbean.tdTjBhk.isxmrd)}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;">
            <p:outputLabel value="用人单位："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.tbTjCrpt.crptName}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <p:outputLabel value="接害工龄："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <h:outputLabel value="#{mgrbean.tdTjBhk.tchbadrsntim}">
                <f:convertNumber type="number" minFractionDigits="0"/>
            </h:outputLabel>
            <h:outputLabel value="年" rendered="#{null!=mgrbean.tdTjBhk.tchbadrsntim}"/>
            <h:outputLabel value="#{mgrbean.tdTjBhk.tchbadrsnmonth}"/>
            <h:outputLabel value="月" rendered="#{null!=mgrbean.tdTjBhk.tchbadrsnmonth}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column colspan="4" style="text-align:left;padding-left:3px;height: 30px;">
            <h:outputLabel value="共有"/>
            <h:outputLabel value="#{mgrbean.tdTjBhkCount}"/>
            <h:outputLabel value="条体检记录"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 其它内容 -->
<ui:define name="insertOtherContents">
    <p:dataTable paginatorPosition="bottom" var="itm" paginator="true" rows="10" value="#{mgrbean.tdTjBhkList.size()==0?mgrbean.tdTjBhkFjList:mgrbean.tdTjBhkList}"
                 emptyMessage="没有数据！">
        <p:column headerText="体检日期" style="width:100px;text-align: center;line-height: 35px;">
            <h:outputLabel value="#{itm.bhkDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="体检编号" style="width:120px;;line-height: 35px;">
            <h:outputLabel value="#{itm.bhkCode}"/>
            <p:spacer width="8" />
            <p:commandButton icon="ui-icon-plusthick" style="color: red;font-size: 14px !important;font-weight: bold;width: 18px;height: 18px;vertical-align: middle;" action="#{mgrbean.searchFjItem}" rendered="#{itm.ifFjNeedShow == 1 and itm.ifRhk==0}" process="@this,:tabView:editForm" update=":tabView:editForm" >
            	<f:setPropertyActionListener target="#{mgrbean.firstRid}" value="#{itm.rid}"></f:setPropertyActionListener>
            </p:commandButton>
            <p:commandButton icon="ui-icon-minusthick" style="color: red;font-size: 14px !important;font-weight: bold;width: 18px;height: 18px;vertical-align: middle;" action="#{mgrbean.shrinkFjItem}" rendered="#{itm.ifFjNeedShow == 2 and itm.ifRhk==0}" process="@this,:tabView:editForm" update=":tabView:editForm" >
            	<f:setPropertyActionListener target="#{mgrbean.firstRid}" value="#{itm.rid}"></f:setPropertyActionListener>
            </p:commandButton>
            <div style="background-color: #39B507;display: inline;padding: 3px;border-radius: 3px;display:#{itm.ifRhk==1?'':'none'}">
			      <p:outputLabel value="复检" style="color:white;"
				   rendered="#{itm.ifRhk==1}"/>
	        </div>
        </p:column>
        <p:column headerText="体检机构" style="width:150px;line-height: 35px;">
            <h:outputLabel value="#{itm.tbTjSrvorg.unitName}"/>
        </p:column>
        <p:column headerText="在岗状态" style="width:100px;text-align: center;line-height: 35px;">
            <h:outputLabel value="#{itm.tsSimpleCode.codeName}"/>
        </p:column>
        <p:column headerText="体检危害因素" style="width:120px;line-height: 35px;">
            <h:outputLabel value="#{itm.bhkBadrsn}"/>
        </p:column>
        <p:column headerText="工种" style="width:100px;line-height: 35px;">
            <h:outputLabel value="#{itm.workName}"/>
        </p:column>
        <p:column headerText="监测类型" style="width:50px;text-align: center;line-height: 35px;">
            <h:outputLabel value="常规监测" rendered="#{itm.jcType==1}"/>
            <h:outputLabel value="主动监测" rendered="#{itm.jcType==2}"/>
        </p:column>
        <p:column headerText="主检结论" style="width:100px;line-height: 35px;">
            <h:outputLabel value="#{itm.tdTjMhkrsts.get(0).tsSimpleCode.codeName}" rendered="#{not empty itm.tdTjMhkrsts}"/>
        </p:column>
        <p:column headerText="用人单位" style="width:200px;line-height: 35px;">
            <h:outputLabel value="#{itm.tbTjCrpt.crptName}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;width: 200px;line-height: 35px;">
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}"
                           update=":tabView"
                           process="@this">
                <f:setPropertyActionListener value="#{itm.rid}" target="#{mgrbean.tdTjBhkRid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm.tdTjSupoccdiselists.size()>0}"/>
            <p:commandLink value="疑似职业病" action="#{mgrbean.bzybInitAction}"  rendered="#{itm.tdTjSupoccdiselists.size()>0}"
                           update=":tabView:editForm:byszybDialog" process="@this"
                           oncomplete="PF('BYszybDialog').show()">
                <f:setPropertyActionListener value="#{itm.rid}" target="#{mgrbean.tdTjBhkRid}"/>
            </p:commandLink>
            <p:spacer width="5"  rendered="#{itm.tdTjContraindlists.size()>0}" />
            <p:commandLink value="职业禁忌证" action="#{mgrbean.bjjzInitAction}"  rendered="#{itm.tdTjContraindlists.size()>0}"
                           update=":tabView:editForm:bzyjjzDialog" process="@this"
                           oncomplete="PF('BZyjjzDialog').show()">
                <f:setPropertyActionListener value="#{itm.rid}" target="#{mgrbean.tdTjBhkRid}"/>
            </p:commandLink>
        </p:column>
    </p:dataTable>
    <p:dialog id="byszybDialog" widgetVar="BYszybDialog" header="本次疑似职业病记录" resizable="false" width="750"
              modal="true">
        <p:dataTable var="bsupitm" value="#{mgrbean.btdTjSupoccdiselists}" paginatorPosition="bottom"
                     id="byszybListTable" paginator="true" rows="10" emptyMessage="没有数据！">
            <p:column headerText="体检日期" style="width: 100px;text-align: center">
                <h:outputText value="#{bsupitm.tdTjBhk.bhkDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputText>
            </p:column>
            <p:column headerText="体检机构" style="width: 150px;padding-left: 3px;">
                <h:outputText value="#{bsupitm.tdTjBhk.tbTjSrvorg.unitName}"/>
            </p:column>
            <p:column headerText="在岗状态" style="width: 100px;text-align: center">
                <h:outputText value="#{bsupitm.tdTjBhk.tsSimpleCode.codeName}"/>
            </p:column>
            <p:column headerText="危害因素" style="width: 120px;padding-left: 3px;">
                <h:outputText value="#{bsupitm.tsSimpleCodeByBadrsnId.codeName}"/>
            </p:column>
            <p:column headerText="疑似职业病" style="padding-left: 3px;">
                <h:outputText value="#{bsupitm.tsSimpleCodeByOccDiseid.codeName}"/>
            </p:column>
        </p:dataTable>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="关闭" icon="ui-icon-close"  onclick="PF('BYszybDialog').hide();"
                                     type="button"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <p:dialog id="bzyjjzDialog" widgetVar="BZyjjzDialog" header="本次职业禁忌证记录" resizable="false" width="750"
              modal="true">
        <p:dataTable var="bconitm" value="#{mgrbean.btdTjContraindlists}" paginatorPosition="bottom"
                     id="bzyjjzListTable" paginator="true" rows="10" emptyMessage="没有数据！">
            <p:column headerText="体检日期" style="width: 100px;text-align: center">
                <h:outputText value="#{bconitm.tdTjBhk.bhkDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputText>
            </p:column>
            <p:column headerText="体检机构" style="width: 150px;padding-left: 3px;">
                <h:outputText value="#{bconitm.tdTjBhk.tbTjSrvorg.unitName}"/>
            </p:column>
            <p:column headerText="在岗状态" style="width: 100px;text-align: center">
                <h:outputText value="#{bconitm.tdTjBhk.tsSimpleCode.codeName}"/>
            </p:column>
            <p:column headerText="危害因素" style="width: 120px;padding-left: 3px;">
                <h:outputText value="#{bconitm.tsSimpleCodeByBadrsnId.codeName}"/>
            </p:column>
            <p:column headerText="职业禁忌证" style="padding-left: 3px;">
                <h:outputText value="#{bconitm.tsSimpleCodeByContraindId.codeName}"/>
            </p:column>
        </p:dataTable>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="关闭"  icon="ui-icon-close"  onclick="PF('BZyjjzDialog').hide();"
                                     type="button"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:define>
</ui:composition>