<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate_noPage2.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tbZwtjItemCommBean}" />
	<!-- 其它内容 -->
	<ui:define name="insertContent">
		<p:layout style="width:100%;" id="contentLay" fullPage="true">
			<p:layoutUnit position="west" size="360" style="border:1px;"
				collapsible="false" header="项目组合">
				<p:outputPanel styleClass="zwx_toobar_42">
					<h:panelGrid columns="5"
						style="border-color:transparent;padding:0px;">
						<p:outputLabel value="名称/拼音码：" />
						<p:inputText id="searchName" value="#{mgrbean.searchName}">
							<p:ajax event="keyup"
								update=":mainForm:dataTable,:mainForm:itemsName,:mainForm:dataTable1"
								process="@this,ifReveal"
								listener="#{mgrbean.searchAction}" />
						</p:inputText>
						<p:spacer width="5" />
						<p:outputLabel value="显示停用：" />
						<p:selectBooleanCheckbox id="ifReveal"
							value="#{mgrbean.searchIfReveal}">
							<p:ajax event="change"
								update=":mainForm:dataTable,:mainForm:itemsName,:mainForm:dataTable1"
								process="@this,searchName"
								listener="#{mgrbean.searchAction}" />
						</p:selectBooleanCheckbox>
					</h:panelGrid>
				</p:outputPanel>
				<p:dataTable id="dataTable" var="item"
					value="#{mgrbean.showItemsList}" selectionMode="single"
					selection="#{mgrbean.selectCode}" rowKey="#{item.rid}"
					paginator="true" rows="20" paginatorPosition="bottom"
					paginatorTemplate="{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
					rowsPerPageTemplate="20" lazy="true" emptyMessage="没有您要找的记录！">
					<p:ajax event="rowSelect" listener="#{mgrbean.onRowSelect}"
						update=":mainForm:itemsName,:mainForm:dataTable1" />
					<p:column headerText="项目组合编码"
						style="width:2%;text-align:center;color:#{item.ifReveal == 0 ? 'red' : ''}">
						<h:outputText value="#{item.codeNo}" />
					</p:column>
					<p:column headerText="项目组合名称"
						style="width:5%;text-align:left;color:#{item.ifReveal == 0 ? 'red' : ''}">
						<h:outputText value="#{item.codeName}" />
					</p:column>
				</p:dataTable>
			</p:layoutUnit>
			<p:layoutUnit position="center" style="border:0px;" header="体检项目">
				<p:outputPanel styleClass="zwx_toobar_42">
					<table style="width: 100%" border="0">
						<tr>
							<td><p:outputLabel id="itemsName"
									value="项目组合名称：    #{mgrbean.itemsName}" /></td>
							<td style="text-align: right;">
								<p:commandButton value="保存"
									update="editDialog,selectedTable" icon="ui-icon-disk" process="@this"
									action="#{mgrbean.saveDeterWayAction}" id="saveRootBtn">
								</p:commandButton>
								<p:spacer width="5"/>
								<p:commandButton value="添加"
									update="editDialog,selectedTable" icon="ui-icon-plus" process="@this"
									action="#{mgrbean.addInitAction}" id="addRootBtn">
									<p:resetInput target="editDialog" />
								</p:commandButton>
							</td>
						</tr>
					</table>
				</p:outputPanel>
				<p:dataTable id="dataTable1" var="item"
					value="#{mgrbean.showZwtjItemsList}" paginator="true"
					rows="20" paginatorPosition="bottom" style="width:100%"
					paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} "
					rowsPerPageTemplate="20" pageLinks="5" lazy="true"
					emptyMessage="没有您要找的记录！"
					currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
					rowIndexVar="R">
					<p:column headerText="项目分类" style="width:10%;text-align:center;">
						<h:outputText value="#{item.tbTjItems.tsSimpleCode.codeName}" />
					</p:column>
					<p:column headerText="项目名称" style="width:15%;">
						<h:outputText value="#{item.tbTjItems.itemName}" />
					</p:column>
					<p:column headerText="判断模式" style="width:7%;text-align:center;">
						<h:outputText value="#{item.tbTjItems.jdgptn == 1 ? '定性' : '定量'}" />
					</p:column>
					<p:column headerText="计量单位" style="width:10%;text-align:center;">
						<h:outputText value="#{item.tbTjItems.msrunt}" />
					</p:column>
					<p:column headerText="判定方式" style="width:10%;text-align:center;">
						<p:selectOneMenu value="#{item.deterWay}" style="width: 150px;">
							<f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
							<f:selectItem itemValue="1" itemLabel="任一满足"></f:selectItem>
							<f:selectItem itemValue="2" itemLabel="同时满足"></f:selectItem>
							<p:ajax event="change" listener="#{mgrbean.updateDeterWay(item)}" process="@this" update="@this" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="状态" style="width:5%;text-align:center;">
						<h:outputText value="#{item.stopTag == 0 ? '停用' : '启用'}" />
					</p:column>
					<p:column headerText="操作" style="width:10%;">
						<p:commandLink rendered="#{item.stopTag == 0}" value="启用"
							action="#{mgrbean.startOrStopAction}"
							update=":mainForm:dataTable1" process="@this">
							<p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{mgrbean.rid}"
								value="#{item.rid}" />
							<f:setPropertyActionListener target="#{mgrbean.state}"
								value="1" />
						</p:commandLink>
						<p:commandLink rendered="#{item.stopTag == 1}" value="停用"
							action="#{mgrbean.startOrStopAction}"
							update=":mainForm:dataTable1" process="@this">
							<p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{mgrbean.rid}"
								value="#{item.rid}" />
							<f:setPropertyActionListener target="#{mgrbean.state}"
								value="0" />
						</p:commandLink>
						<p:spacer rendered="#{item.stopTag == 0}" width="5px;" />
						<p:commandLink rendered="#{item.stopTag == 0}" value="删除"
							action="#{mgrbean.deleteAction}"
							update=":mainForm:dataTable1" process="@this">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{mgrbean.rid}"
								value="#{item.rid}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:layoutUnit>
		</p:layout>

		<!-- 新增、修改 -->
		<p:dialog id="editDialog" header="选择项目" widgetVar="EditDialog"
			resizable="false" width="800" height="400" modal="true">
			<table width="100%">
				<tr>
					<td width="140" style="text-align: right;padding-right: 3px; "
						class="zwx_dialog_font">项目名称/拼音码：</td>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							columns="5">
							<p:inputText id="editItemsName"
								value="#{mgrbean.editItemsName}" style="width: 120px;">
								<p:ajax event="keyup" update="selectedTable" process="@this"
									listener="#{mgrbean.addSearchAction}" />
							</p:inputText>
							<p:spacer width="30" />
							<h:outputText value="您已经选择了 " class="zwx_dialog_font" />
							<h:outputText value="#{mgrbean.saveTjItemsList.size()}"
								class="zwx_dialog_font" id="size"
								style="color:blue;font-weight: bold" />
							<h:outputText value=" 条记录！" class="zwx_dialog_font" />
						</h:panelGrid></td>
				</tr>
				<tr>
					<td width="140" colspan="2"><p:dataTable var="itm"
							value="#{mgrbean.showTjItemsList}" id="selectedTable"
							paginator="true" rows="10" emptyMessage="没有数据！"
							paginatorPosition="bottom">
							<p:column headerText="选择" style="width:5%;text-align:center">
								<p:commandLink value="选择" action="#{mgrbean.selectItems}"
									process="@this" update=":mainForm:selectedTable,:mainForm:size">
									<f:setPropertyActionListener value="#{itm}"
										target="#{mgrbean.selectItems}" />
								</p:commandLink>
							</p:column>
							<p:column headerText="项目分类" style="width:10%;text-align:center;">
								<h:outputText value="#{itm.tsSimpleCode.codeName}" />
							</p:column>
							<p:column headerText="项目名称" style="width:15%;">
								<h:outputText value="#{itm.itemName}" />
							</p:column>
							<p:column headerText="判断模式" style="width:7%;text-align:center;">
								<h:outputText value="#{itm.jdgptn == 1 ? '定性' : '定量'}" />
							</p:column>
							<p:column headerText="参考值" style="width:10%;">
								<h:outputText value="#{itm.itemStdvalue}" />
							</p:column>
							<p:column headerText="计量单位" style="width:10%;text-align:center;">
								<h:outputText value="#{itm.msrunt}" />
							</p:column>
						</p:dataTable></td>
				</tr>
				<tr>
					<td width="140" style="text-align:center;" colspan="2"><p:commandButton
							value="保存" icon="ui-icon-check" id="typeSaveBtn"
							action="#{mgrbean.saveAction}" process="@this,@form"
							update=":mainForm:dataTable1" /> <p:spacer width="5" /> <p:commandButton
							value="取消" icon="ui-icon-close" id="typeBackBtn"
							onclick="PF('EditDialog').hide();" immediate="true" /></td>
				</tr>
			</table>
		</p:dialog>

	</ui:define>
</ui:composition>