<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" template="/WEB-INF/templates/system/viewTemplate.xhtml"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://primefaces.org/ui">
	<ui:define name="insertViewScripts">
		<script language="javascript">
			//<![CDATA[
			function displayChart() {
				//wu shan chu
			}
			//]]>
		</script>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="体检项目维护详情" />
			</p:column>
		</p:row>
	</ui:define>
	<!-- 编辑页面的按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="height:38px">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tbTjItemsCommBean.modTbTjRstdescBackAction}" update=":tabView" immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
	<!-- 编辑页面的内容-->
	<ui:define name="insertEditContent">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px;width:220px;">
				<h:outputText value="业务分类：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 250px;">
				<p:outputLabel value="#{tbTjItemsCommBean.tbTjItems.tsSimpleCode.codeName}" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:220px;height:36px;">
				<h:outputText value="体检项目编码：" />
			</p:column>
			<p:column style="padding-left:3px;">
				<p:outputLabel value="#{tbTjItemsCommBean.tbTjItems.itemCode}" />
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px">
				<h:outputText value="体检项目名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:outputLabel value="#{tbTjItemsCommBean.tbTjItems.itemName}" style="width:180px;" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;height:36px">
				<h:outputText value="性别：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:outputLabel value="男" rendered="#{tbTjItemsCommBean.tbTjItems.sex==1}" />
				<p:outputLabel value="女" rendered="#{tbTjItemsCommBean.tbTjItems.sex==2}" />
				<p:outputLabel value="通用" rendered="#{tbTjItemsCommBean.tbTjItems.sex==3}" />
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px">
				<h:outputText value="判断模式：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:outputLabel value="定性" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==1}" />
				<p:outputLabel value="定量" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;height:36px">
                <h:outputText value="参考值：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:outputLabel value="#{tbTjItemsCommBean.tbTjItems.itemStdvalue}" />
            </p:column>
		</p:row>
		
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px">
                <h:outputText value="序号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:outputLabel value="#{tbTjItemsCommBean.tbTjItems.num}" />
            </p:column>
			<p:column style="text-align:right;padding-right:3px;height:36px;">
				<h:outputText value="项目标记：" />
			</p:column>
			<p:column style="padding-left:8px;">
				<p:outputLabel value="#{tbTjItemsCommBean.tbTjItems.itemTagId.codeName}" />
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:36px">
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;" colspan="3">
				<p:outputLabel value="启用" rendered="#{tbTjItemsCommBean.tbTjItems.stopTag==1}" />
				<p:outputLabel value="停用" rendered="#{tbTjItemsCommBean.tbTjItems.stopTag==0}" />
			</p:column>
		</p:row>

	</ui:define>
	<ui:define name="insertOtherContents">

		<p:fieldset legend="计量单位" toggleable="true" id="diag0" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">
			<h:panelGrid style="width:100%;height:100%;" id="mainGrid0">
	            <p:dataTable  var="itm" value="#{tbTjItemsCommBean.showTbTjItemUnitRelList}" emptyMessage="没有您要找的记录！" >
	                <p:columnGroup type="header">
	                	<p:row>
			                <p:column headerText="计量单位" style="width: 150px;" />
			                <p:column headerText="最小值" style="width: 120px;" />
			                <p:column headerText="最大值"  style="width: 120px;" />
			                <p:column headerText="是否默认"  style="width: 150px;" />
			            </p:row>
	                </p:columnGroup>
	                
	                <p:column  style="text-align:center ;">
	                    <h:outputText value="#{itm.fkByMsruntId.codeName}" escape="false"/>
	                </p:column>
	                <p:column  style="text-align: center;">
	                    <p:outputLabel value="#{itm.minval}"   />
	                </p:column>
	                <p:column  style="text-align: center;">
	                    <p:outputLabel value="#{itm.maxval}"   />
	                </p:column>
	                <p:column  style="text-align: center;">
	                    <p:outputLabel value="是"  rendered="#{itm.ifDefaut==1}"  />
	                    <p:outputLabel value="否"  rendered="#{itm.ifDefaut==0}"  />
	                </p:column>
	            </p:dataTable>
	        </h:panelGrid>
		</p:fieldset>
		<p:fieldset legend="特殊标准" toggleable="true" id="diag2" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">
        <h:panelGrid style="width:100%;height:100%;" id="mainGrid2">
            <p:dataTable  var="itm" value="#{tbTjItemsCommBean.showItemsSpeList}" emptyMessage="没有您要找的记录！" >
                <p:columnGroup type="header">
                	<p:row>
		                <p:column headerText="在岗状态" style="width: 150px;" />
		                <p:column headerText="有害因素" style="width: 150px;" />
		                <p:column headerText="性别"  style="width: 80px;" />
		                <p:column headerText="计量单位"  style="width: 150px;" />
		                 <p:column headerText="最小值"  style="width: 150px;" />
		                <p:column headerText="最大值"  style="width: 150px;" />
		            </p:row>
                </p:columnGroup>
                <p:column  style="text-align:center ;">
                    <h:outputText value="#{itm.fkByOnguardStateid.codeName}" />
                </p:column>
                <p:column  style="text-align:center ;">
                    <h:outputText   value="#{itm.fkByBadRsnId.codeName}"  />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.sex}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.msrunt}"  escape="false" />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.minval}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.maxval}"   />
                </p:column>
            </p:dataTable>
        </h:panelGrid>
    </p:fieldset>
		<p:fieldset legend="国家接口标准" toggleable="true" id="diag3" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">
			<h:panelGrid style="width:100%;height:100%;" id="mainGrid3">
				<p:dataTable  var="itm" value="#{tbTjItemsCommBean.showTbtjItemsGjList}" emptyMessage="没有您要找的记录！" >
					<p:columnGroup type="header">
						<p:row>
							<p:column headerText="类型" style="width: 150px;" />
							<p:column headerText="性别"  style="width: 80px;" />
							<p:column headerText="计量单位"  style="width: 150px;" />
							<p:column headerText="最小值"  style="width: 150px;" />
							<p:column headerText="最大值"  style="width: 150px;" />
						</p:row>
					</p:columnGroup>
					<p:column  style="text-align:center ;">
						<h:outputText value="#{null == itm.type ? '' : (1 == itm.type ? '参考值' : (2 == itm.type ? '结果限值' : ''))}" />
					</p:column>
					<p:column  style="text-align:center ;">
						<h:outputText   value="#{null == itm.sex ? '通用' : (1 == itm.sex ? '男' : (2 == itm.sex ? '女' : ''))}"  />
					</p:column>
					<p:column  style="text-align: center;">
						<p:outputLabel value="#{null == itm.fkByMsruntId ? '' : itm.fkByMsruntId.codeName}" escape="false"  />
					</p:column>
					<p:column  style="text-align: center;">
						<p:outputLabel value="#{itm.minval}"   />
					</p:column>
					<p:column  style="text-align: center;">
						<p:outputLabel value="#{itm.maxval}"   />
					</p:column>

				</p:dataTable>
			</h:panelGrid>
		</p:fieldset>
	<p:fieldset legend="定性项目描述" toggleable="true" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==1 or tbTjItemsCommBean.tbTjItems.jdgptn==2}">
		<h:panelGrid style="width:100%;height:100%;">
			<p:dataTable var="itm" value="#{tbTjItemsCommBean.tbTjItems.tbTjRstdescs}" emptyMessage="没有您要找的记录！">
				<p:column headerText="排列顺序" style="width: 200px;text-align:center ;">
					<h:outputText value="#{itm.num}" />
				</p:column>
				<p:column headerText="描述结果" style="width: 500px;">
					<h:outputText value="#{itm.rstDesc}" title="#{itm.rstDesc}" />
				</p:column>
				<p:column headerText="是否合格" style="width:200px;text-align: center;">
					<p:outputLabel value="不合格" rendered="#{itm.egbTag==0}" />
					<p:outputLabel value="合格" rendered="#{itm.egbTag==1}" />
				</p:column>
			</p:dataTable>
		</h:panelGrid>
	</p:fieldset>
	</ui:define>


</ui:composition>

