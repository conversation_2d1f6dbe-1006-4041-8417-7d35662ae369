<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwGbz188NostdListCommBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/tdZwGbz188NostdEditComm.xhtml" />
    <!-- 详情页面 -->

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 77px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="GBZ188不规范数据上报"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" />
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputLabel value="提示：" style="color:red;"/>
                <h:outputLabel value="只查询#{mgrbean.ifRptDate?'报告出具日期':'体检日期'}（#{mgrbean.bhkBeginDate}）之后的数据" style="color:blue;"/>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="用人单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:200px;">
                <h:panelGrid columns="3" style="border-color: transparent;margin: -6px;padding: 0px;" id="crptZone">
                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                        zoneCode="#{mgrbean.searchZoneCode}"
                                        zoneName="#{mgrbean.searchZoneName}"/>
                </h:panelGrid>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width:240px;">
                <p:inputText value="#{mgrbean.searchUnitName}" style="width:200px;"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:inputText value="#{mgrbean.searchName}" style="width:180px;" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:120px;">
                <h:outputLabel value="#{mgrbean.ifRptDate?'接收日期':'体检日期'}："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 8px;height: 38px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="#{mgrbean.ifRptDate?'接收日期':'体检日期'}，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="#{mgrbean.ifRptDate?'接收日期':'体检日期'}，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchEdate}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="人员姓名" style="width:150px;text-align: center;width:100px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="证件号码" style="padding-left: 3px; width:180px;text-align: center;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="联系电话" style="padding-left: 3px; width:100px;text-align:center; ">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="padding-left: 3px; width:200px;">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="体检编号" style="padding-left: 3px; width:120px;text-align:center; ">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="体检日期" style="padding-left: 3px; width:100px;text-align:center; " rendered="#{!mgrbean.ifRptDate}">
            <h:outputLabel value="#{itm[20]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="不规范原因" style="#{itm[7]=='——'?'text-align:center; width:200px;':'padding-left: 3px; width:200px;'}">
            <p:outputPanel rendered="#{null!=itm[7]}">
                <h:outputLink value="#" id="fade" rendered="#{itm[7].length() gt 20}">
                    <h:outputText value="#{itm[7].substring(0,20)}..."/>
                    <p:tooltip for="@parent" value="#{itm[7]}" style="width:300px"/>
                </h:outputLink>
                <h:outputText value="#{itm[7]}" rendered="#{itm[7].length() le 20}"/>
            </p:outputPanel>

            <h:outputText value="——" rendered="#{null==itm[7]}"/>
        </p:column>
        <p:column headerText="不规范原因说明" style="#{itm[8]=='——'?'text-align:center; width:160px;':'padding-left: 3px; width:160px;'}">
            <p:outputPanel rendered="#{null!=itm[8]}">
                <h:outputLink value="#" id="nostdDesc" rendered="#{itm[8].length() gt 20}">
                    <h:outputText value="#{itm[8].substring(0,20)}..."/>
                    <p:tooltip for="@parent" value="#{itm[8]}" style="width:300px"/>
                </h:outputLink>
                <h:outputText value="#{itm[8]}" rendered="#{itm[8].length() le 20}"/>
            </p:outputPanel>

            <h:outputText value="——" rendered="#{null==itm[8]}"/>
        </p:column>
        <p:column headerText="接收日期" style="padding-left: 3px; width:100px;text-align:center; " rendered="#{mgrbean.ifRptDate}">
            <h:outputLabel value="#{itm[10]==null?'——':itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处理期限" style="padding-left: 3px; width:100px;text-align:center; " rendered="#{mgrbean.ifshowdeadline}">
            <p:outputLabel rendered="#{itm[12]=='1'}">
                <p:outputLabel rendered="#{itm[13] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"></h:outputText>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[13] gt 0}" style="padding:3px;border-radius:2px;background:#{itm[13]==1?'#EB7E10':'#2F6EA0'};">
                    <h:outputText value="剩余#{itm[13]}天" style="color:#FFFFFF" rendered="#{itm[13] gt 1}"/>
                    <h:outputText value="当天截止" style="color:#FFFFFF" rendered="#{itm[13]==1}"/>
                </p:outputLabel>
            </p:outputLabel>
            <h:outputText value="——" rendered="#{itm[12]=='0'}"/>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputLabel value="待填报" rendered="#{itm[11]==0.5 or itm[11]==null}" style="color:red"/>
            <h:outputLabel value="待提交" rendered="#{itm[11]==0}" style="color:red"/>
            <h:outputLabel value="已退回" rendered="#{itm[11]==2 or itm[11]==4 or itm[11]==6}" style="color:red"/>
            <h:outputLabel value="待初审" rendered="#{itm[11]==1 or(itm[11]==3 and itm[18]==1)}" />
            <h:outputLabel value="待复审" rendered="#{itm[11]==3 and itm[18]==0}" />
            <h:outputLabel value="待终审" rendered="#{itm[11]==5}" />
            <h:outputLabel value="终审完成" rendered="#{itm[11]==7}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;">
            <p:spacer width="5"/>
            <p:commandLink value="上报" action="#{mgrbean.addInitAction}" oncomplete="$('html, body, .content').animate({scrollTop: 0}, 'slow');"
                           process="@this"  update=":tabView"
                           rendered="#{itm[11]==0.5 or itm[11]==null}" >
                <f:setPropertyActionListener value="#{itm[17]}" target="#{mgrbean.bhkId}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{itm[18]}" target="#{mgrbean.ifCityDirect}"/>
                <f:setPropertyActionListener value="true" target="#{mgrbean.ifModify}"/>
            </p:commandLink>
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" oncomplete="$('html, body, .content').animate({scrollTop: 0}, 'slow');"
                           process="@this"  update=":tabView"
                           rendered="#{itm[11]==0 or  itm[11]==2 or itm[11]==4 or itm[11]==6}">
                <f:setPropertyActionListener value="#{itm[17]}" target="#{mgrbean.bhkId}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{itm[18]}" target="#{mgrbean.ifCityDirect}"/>
                <f:setPropertyActionListener value="true" target="#{mgrbean.ifModify}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" oncomplete="$('html, body, .content').animate({scrollTop: 0}, 'slow');"
                           process="@this"  update=":tabView"
                           rendered="#{itm[11]==1 or  itm[11]==3 or itm[11]==5 or itm[11]==7}">
                <f:setPropertyActionListener value="#{itm[17]}" target="#{mgrbean.bhkId}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{itm[18]}" target="#{mgrbean.ifCityDirect}"/>
                <f:setPropertyActionListener value="false" target="#{mgrbean.ifModify}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>