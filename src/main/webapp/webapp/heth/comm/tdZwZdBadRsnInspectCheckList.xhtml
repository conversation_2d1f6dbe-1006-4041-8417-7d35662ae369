<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwZdBadRsnInspectCheckListBean}"/>

    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/tdZwZdBadRsnInspectCheckEdit.xhtml" />

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
            a:hover {
                color: #25AAE1;
                text-decoration: none;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
            .ui-chkbox{
                margin-top: 4px;
            }
            .ui-radiobutton-box{
                margin-top:3px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="重点危害因素检查情况审核-#{tdZwZdBadRsnInspectCheckListBean.zoneType==2?'终审':(tdZwZdBadRsnInspectCheckListBean.zoneType==3?'复审':'初审')}"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdZwZdBadRsnInspectCheckListBean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:spacer width="5"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" id="plsh" action="#{mgrbean.openReviewConfirmDialog}" oncomplete="datatableOffClick()"
                                 rendered="#{tdZwZdBadRsnInspectCheckListBean.zoneType==2 or tdZwZdBadRsnInspectCheckListBean.zoneType==3}" process="@this,dataTable"/>
            	<h:outputText value="提示：" style="color:red;" rendered="#{mgrbean.ifShowTipInfo and tdZwZdBadRsnInspectCheckListBean.zoneType!= 4}"></h:outputText>
            	<h:outputText value="#{mgrbean.tipInfo}"
            	 	style="color:blue;" rendered="#{mgrbean.ifShowTipInfo and tdZwZdBadRsnInspectCheckListBean.zoneType!= 4}"></h:outputText>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="机构地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:300px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{tdZwZdBadRsnInspectCheckListBean.zoneList}"
                                    zoneCode="#{tdZwZdBadRsnInspectCheckListBean.searchZoneCode}"
                                    zoneName="#{tdZwZdBadRsnInspectCheckListBean.searchZoneName}"
                                    onchange="onSearchNodeSelect()" />
                <p:remoteCommand name="onSearchNodeSelect"
                                 action="#{tdZwZdBadRsnInspectCheckListBean.onZoneSelect}"
                                 process="@this,searchZone"
                                 update=":tabView:mainForm:unitName" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="检查机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;" >
                    <!-- 弹出框 -->
                    <p:outputPanel >
                        <table>
                            <tr>
                                <td style="padding: 0;border-color: transparent;">
                                    <p:inputText id="unitName"
                                                 value="#{mgrbean.searchUnitName}"
                                                 style="width: 180px;cursor: pointer;"
                                                 onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                                 readonly="true"/>
                                </td>
                                <td style="border-color: transparent;">
                                    <p:commandLink styleClass="ui-icon ui-icon-search"
                                                   id="selUnitLink"
                                                   action="#{mgrbean.selUnitAction}" process="@this"
                                                   style="position: relative;left: -28px !important;">
                                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
                                                resetValues="true" update="unitName" />
                                    </p:commandLink>
                                </td>
                                <!-- 清空 -->
                                <td style="border-color: transparent;position: relative;left: -30px;">
                                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                   process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                    </p:commandLink>
                                </td>
                            </tr>
                        </table>
                    </p:outputPanel>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="接收日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{tdZwZdBadRsnInspectCheckListBean.searchRcvBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{tdZwZdBadRsnInspectCheckListBean.searchRcvEdate}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;" >
                <p:inputText id="searchCrptName" value="#{tdZwZdBadRsnInspectCheckListBean.searchCrptName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;">
                <p:inputText id="searchPersonnelName" value="#{tdZwZdBadRsnInspectCheckListBean.searchPersonName}" style="width: 180px;" maxlength="25" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchPersonnelIdc" value="#{tdZwZdBadRsnInspectCheckListBean.searchPersonIdc}" style="width: 180px;" maxlength="25" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="在岗状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.selectOnGuardIds}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="危害因素：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnIds}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        inputWidth="180"
                                        ifTree="true"
                                        ifSelectParent = "false"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="体检日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{tdZwZdBadRsnInspectCheckListBean.searchBhkBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="体检日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{tdZwZdBadRsnInspectCheckListBean.searchBhkEdate}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 33px;;width: 150px;">
                <h:outputText value="结论是否正常：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;;width: 300px;" >
                <p:selectManyCheckbox value="#{tdZwZdBadRsnInspectCheckListBean.concluStates}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="3">
                <p:selectManyCheckbox value="#{tdZwZdBadRsnInspectCheckListBean.states}">
                    <f:selectItems value="#{tdZwZdBadRsnInspectCheckListBean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{itm[14]=='1' and (itm[15]!='1' or mgrbean.zoneType ==2) ?false:true}" rendered="#{mgrbean.zoneType !=4}"/>

        <p:column headerText="机构地区" style="width: 120px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="检查机构" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="人员姓名" style="width:60px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="在岗状态" style="text-align:center;width:80px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="危害因素" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="结论是否正常" style="text-align:center;width:100px;padding-left: 8px;">
            <h:outputText value="是" rendered="#{itm[7]==1}"/>
            <h:outputText value="否" rendered="#{itm[7]==0}"/>
        </p:column>
        <p:column headerText="体检日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;text-align: center;">
            <!--<h:outputText value="——" rendered="#{itm[14]!='1'}"/>-->
            <h:outputLabel value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处理期限" style="width:80px;text-align: center;">
            <h:outputText value="——" rendered="#{itm[14]!='1'}"/>
            <p:outputLabel rendered="#{itm[14]=='1'}">
                <p:outputLabel rendered="#{itm[13] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[13] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
                    <h:outputText value="当天截止" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[13] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
                    <h:outputText value="剩余#{itm[13]}天" style="color:#FFFFFF"/>
                </p:outputLabel>
            </p:outputLabel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <p:outputPanel rendered="#{tdZwZdBadRsnInspectCheckListBean.zoneType lt 3}">
                <h:outputLabel value="待审核" rendered="#{itm[11]==5}"/>
                <h:outputLabel value="已退回" rendered="#{itm[11]==4}" style="color:red;"/>
                <h:outputLabel value="审核通过" rendered="#{itm[11]==6}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{tdZwZdBadRsnInspectCheckListBean.zoneType eq 3}">
                <h:outputLabel value="待审核" rendered="#{itm[11]==3 or itm[11] == 1 or itm[11] == null}"/>
                <h:outputLabel value="已退回" rendered="#{itm[11]==2}" style="color:red;"/>
                <h:outputLabel value="待终审" rendered="#{itm[11]==5}" />
                <h:outputLabel value="终审退回" rendered="#{itm[11]==4}" style="color:red;"/>
                <h:outputLabel value="终审通过" rendered="#{itm[11]==6}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{tdZwZdBadRsnInspectCheckListBean.zoneType gt 3}">
                <h:outputLabel value="待审核" rendered="#{itm[11]==1 or itm[11] == null}"/>
                <h:outputLabel value="待复审" rendered="#{itm[11]==3 or itm[11]==4 }"/>
                <h:outputLabel value="复审退回" rendered="#{itm[11]==2}" style="color:red;"/>
                <h:outputLabel value="复审通过" rendered="#{itm[11]==5}"/>
                <h:outputLabel value="终审通过" rendered="#{itm[11]==6}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="#{itm[14]=='1'?'审核':'详情'}" action="#{tdZwZdBadRsnInspectCheckListBean.modInitAction}" process="@this"  update=":tabView" resetValues="true">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{tdZwZdBadRsnInspectCheckListBean.rid}"/>
                <f:setPropertyActionListener value="#{itm[14]=='1'}" target="#{tdZwZdBadRsnInspectCheckListBean.ifCheck}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <ui:include src="reviewConfirmDialogComm.xhtml"/>
    </ui:define>
</ui:composition>