<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:c="http://java.sun.com/jsp/jstl/core">
	<ui:insert name="insertEditScripts">
		<script type="text/javascript">
				//<![CDATA[	
				function showStatus() {
					PF('StatusDialog').show();
				}
				function hideStatus() {
					PF('StatusDialog').hide();
				}
	
				//]]>
			</script>
	</ui:insert>
	<h:outputScript name="js/validate/system/validate.js" />

	<style type="text/css">

	</style>

	<h:form id="mhkrstListForm">
		<!-- 标题栏 -->
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="mhkrstTitleGrid" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="体检信息录入-->基本信息-->体检问诊-->检查结果-->检查结论"
									rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk!=1}"/>
						<h:outputText value="体检信息录入-->基本信息-->检查结果-->检查结论"
									rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk==1}"/>
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42" id="mhkRstPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
				<p:commandButton value="保存" icon="ui-icon-disk" action="#{tdTjBhkCltListCommBean.saveMhkRst}"
						onclick="showStatus()" oncomplete="hideStatus()"
						 update=":tabView:editTabView" process="@this,:tabView:editTabView:mhkrstListForm"/>
				<p:commandButton value="上一步" action="#{tdTjBhkCltListCommBean.lastAction(3)}" update=":tabView:editTabView"
								onclick="showStatus()" oncomplete="hideStatus()" process="@this,:tabView:editTabView:mhkrstListForm"/>
				<p:commandButton value="提交" icon="ui-icon-check" action="#{tdTjBhkCltListCommBean.submitMhkrst(1)}"
								onclick="showStatus()" oncomplete="hideStatus()" update=":tabView" process="@this,:tabView:editTabView:mhkrstListForm"/>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdTjBhkCltListCommBean.backAction}" process="@this"
						 update=":tabView" immediate="true"/>
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="mhkRstPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}" />
		<p:confirmDialog message="胸片检查异常，请确认结论是否正确？"  header="消息确认框" severity="alert" closable="false" styleClass="fsryConfirmDialog"
						  widgetVar="SubmitMhkRstConfirmDialog" >
			<p:outputPanel style="text-align:center;">
				<p:commandButton value="确定" action="#{tdTjBhkCltListCommBean.submitMhkrst(2)}"
								 onclick="showStatus()"
								 update=":tabView" process="@this,:tabView:editTabView:mhkrstListForm"
								 oncomplete="hideStatus();PF('SubmitMhkRstConfirmDialog').hide()"
				/>
				<p:commandButton value="取消"  oncomplete="PF('SubmitMhkRstConfirmDialog').hide()"/>
			</p:outputPanel>
		</p:confirmDialog>
		<p:fieldset legend="检查结论" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{!tdTjBhkCltListCommBean.hasBhkResultStyle}">
			<p:panelGrid style="width:100%;" id="mhkrstGrid">
				<!-- 未见异常 -->
				<p:row>
					<p:column style="height:30px">
						<p:selectBooleanCheckbox value="#{tdTjBhkCltListCommBean.ncagSelect}" itemLabel="&#160;#{tdTjBhkCltListCommBean.mhkrstList.get(0).codeName}"
												 style="#{tdTjBhkCltListCommBean.view ne null?'padding-left:10px;pointer-events:none;':'padding-left:10px;'}"
												 id="ncagSelect">
							<p:ajax event="change" update=":tabView:editTabView:mhkrstListForm" process="@this,:tabView:editTabView:mhkrstListForm"/>
						</p:selectBooleanCheckbox>
						<p:panelGrid style="width:100%;" id="ncagGrid" rendered="#{tdTjBhkCltListCommBean.ncagSelect}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:180px;">
									<h:outputText value="*" style="color:red;" />
									<h:outputText value="危害因素：" />
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<table>
										<tr>
											<td style="padding: 0;border-color: transparent;">
												<p:inputText id="searchNcagCheckBadName"
															 value="#{tdTjBhkCltListCommBean.searchNcagCheckBadName}"
															 style="width: 700px;cursor: pointer;"
															 onclick="document.getElementById('tabView:editTabView:mhkrstListForm:selItemLink2').click();"
															 readonly="true"/>
											</td>
											<td style="border-color: transparent;">
												<p:commandLink id="selItemLink2" styleClass="ui-icon ui-icon-search"
															   action="#{tdTjBhkCltListCommBean.selItemCodeAction}" process="@this"
															   disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
															   style="position: relative;left: -28px !important;">
													<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.resultType}" value="0"></f:setPropertyActionListener>
													<p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onItemCodeAction}" process="@this"
															resetValues="true" update="searchNcagCheckBadName" />
												</p:commandLink>
											</td>
											<!-- 清空 -->
											<td style="border-color: transparent;position: relative;left: -30px;">
												<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
															   process="@this" update="searchNcagCheckBadName" action="#{tdTjBhkCltListCommBean.clearItemCode}">
													<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.resultType}" value="0"></f:setPropertyActionListener>
												</p:commandLink>
											</td>
										</tr>
									</table>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:column>
				</p:row>
				<!-- 复查 -->
				<p:row>
					<p:column style="height:30px">
						<p:selectBooleanCheckbox value="#{tdTjBhkCltListCommBean.recheckSelect}" itemLabel="&#160;#{tdTjBhkCltListCommBean.mhkrstList.get(1).codeName}"
												 style="#{tdTjBhkCltListCommBean.view ne null?'padding-left:10px;pointer-events:none;':'padding-left:10px;'}"
												 id="recheckSelect">
							<p:ajax event="change" update=":tabView:editTabView:mhkrstListForm" process="@this,:tabView:editTabView:mhkrstListForm"/>
						</p:selectBooleanCheckbox>
						<p:panelGrid style="width:100%;" id="recheckGrid" rendered="#{tdTjBhkCltListCommBean.recheckSelect}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:180px;">
									<h:outputText value="*" style="color:red;" />
									<h:outputText value="危害因素：" />
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<table>
										<tr>
											<td style="padding: 0;border-color: transparent;">
												<p:inputText id="searchReCheckBadName"
															 value="#{tdTjBhkCltListCommBean.searchReCheckBadName}"
															 style="width: 700px;cursor: pointer;"
															 onclick="document.getElementById('tabView:editTabView:mhkrstListForm:selItemLink1').click();"
															 readonly="true"/>
											</td>
											<td style="border-color: transparent;">
												<p:commandLink id="selItemLink1" styleClass="ui-icon ui-icon-search"
															   action="#{tdTjBhkCltListCommBean.selItemCodeAction}" process="@this"
															   disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
															   style="position: relative;left: -28px !important;">
													<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.resultType}" value="1"></f:setPropertyActionListener>
													<p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onItemCodeAction}" process="@this"
															resetValues="true" update="searchReCheckBadName" />
												</p:commandLink>
											</td>
											<!-- 清空 -->
											<td style="border-color: transparent;position: relative;left: -30px;">
												<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
															   process="@this" update="searchReCheckBadName" action="#{tdTjBhkCltListCommBean.clearItemCode}">
													<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.resultType}" value="1"></f:setPropertyActionListener>
												</p:commandLink>
											</td>
										</tr>
									</table>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:column>
				</p:row>

				<!-- 其他疾病或异常 -->
				<p:row>
					<p:column style="height:30px">
						<p:selectBooleanCheckbox value="#{tdTjBhkCltListCommBean.otherSelect}"
												 itemLabel="&#160;#{tdTjBhkCltListCommBean.mhkrstList.get(2).codeName}"
												 style="#{tdTjBhkCltListCommBean.view ne null?'padding-left:10px;pointer-events:none;':'padding-left:10px;'}"
												 id="otherSelect">
							<p:ajax event="change" update=":tabView:editTabView:mhkrstListForm" process="@this,:tabView:editTabView:mhkrstListForm"/>
						</p:selectBooleanCheckbox>
						<p:panelGrid style="width:100%;" id="otherGrid" rendered="#{tdTjBhkCltListCommBean.otherSelect}">

							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:180px;">
									<h:outputText value="*" style="color:red;" />
									<h:outputText value="危害因素：" />
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<table>
										<tr>
											<td style="padding: 0;border-color: transparent;">
												<p:inputText id="searchOtherBadName"
															 value="#{tdTjBhkCltListCommBean.searchOtherBadName}"
															 style="width: 700px;cursor: pointer;"
															 onclick="document.getElementById('tabView:editTabView:mhkrstListForm:selItemLink').click();"
															 readonly="true"/>
											</td>
											<td style="border-color: transparent;">
												<p:commandLink id="selItemLink" styleClass="ui-icon ui-icon-search" disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
															   action="#{tdTjBhkCltListCommBean.selItemCodeAction}" process="@this"
															   style="position: relative;left: -28px !important;">
													<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.resultType}" value="2"></f:setPropertyActionListener>
													<p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onItemCodeAction}" process="@this"
															resetValues="true" update="searchOtherBadName" />
												</p:commandLink>
											</td>
											<!-- 清空 -->
											<td style="border-color: transparent;position: relative;left: -30px;">
												<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
															   process="@this" update="searchOtherBadName" action="#{tdTjBhkCltListCommBean.clearItemCode}">
													<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.resultType}" value="2"></f:setPropertyActionListener>
												</p:commandLink>
											</td>
										</tr>
									</table>
								</p:column>
							</p:row>
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:180px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="其他疾病或异常描述："/>
								</p:column>
								<p:column colspan="5" style="padding-right:5px">
									<p:inputTextarea value="#{tdTjBhkCltListCommBean.otherDesc}" readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}"
													 style="resize: none;width: 700px;height:150px;" rows="5" autoResize="false" maxlength="500"
													 id="otherDesc">
									</p:inputTextarea>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:column>
				</p:row>

				<!-- 职业禁忌证 -->
				<p:row>
					<p:column style="height:30px">
						<p:selectBooleanCheckbox value="#{tdTjBhkCltListCommBean.contraindSelect}"
												 itemLabel="&#160;#{tdTjBhkCltListCommBean.mhkrstList.get(3).codeName}"
												 style="#{tdTjBhkCltListCommBean.view ne null?'padding-left:10px;pointer-events:none;':'padding-left:10px;'}"
												 id="contraindSelect">
							<p:ajax event="change" update=":tabView:editTabView:mhkrstListForm"  process="@this,:tabView:editTabView:mhkrstListForm"/>
						</p:selectBooleanCheckbox>
						<p:panelGrid style="width:100%;" id="contraindGrid" rendered="#{tdTjBhkCltListCommBean.contraindSelect}">
							<p:row>
								<p:column style="text-align:left;padding-left:5px;">
									<p:panelGrid style="width:100%;" rendered="#{tdTjBhkCltListCommBean.view eq null}">
										<p:row>
											<p:column style="width:70px;text-align:left;border-color: transparent;position: relative;left: -3px;">
												<p:commandButton value="添加" icon="ui-icon-plus"  resetValues="true"
													 action="#{tdTjBhkCltListCommBean.addContraind}" update="contraindTable"/>
											</p:column>
										</p:row>
									</p:panelGrid>

									<p:dataTable id="contraindTable" paginatorPosition="bottom" value="#{tdTjBhkCltListCommBean.contraindCltWebList}"
                                                 style="padding-bottom: 20px;" widgetVar="mhkrstTable" var="contraindCltWeb" emptyMessage="没有数据！" rowIndexVar="R">
										<p:column headerText="危害因素" style="width:300px;text-align: center;">
											<p:selectOneMenu value="#{contraindCltWeb.badRsnId}" style="#{tdTjBhkCltListCommBean.view ne null?'width: 260px;pointer-events:none;':'width: 260px;'}"
												id="badRsnId">
												<f:selectItem itemLabel="--请选择--" itemValue=""/>
												<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjBhkClt.badList}" var="badrsnsClt"
															itemLabel="#{badrsnsClt.codeName}" itemValue="#{badrsnsClt.rid}"/>
												<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.getContraindList(contraindCltWeb)}"
														process="@this" update="contraindTable"/>
											</p:selectOneMenu>
										</p:column>

										<p:column headerText="职业禁忌证" style="width:800px;text-align: center;">
                                            <!-- 职业禁忌症弹出框 -->
                                            <table>
                                                <tr>
                                                    <td style="padding: 0;border-color: transparent;display: flex;height: auto;">
                                                        <p:inputText id="contraind" onclick="$(this).next().click()" value="#{contraindCltWeb.contraind}" style="#{(contraindCltWeb.contraindCltList == null or contraindCltWeb.contraindCltList.size() le 0)?'pointer-events:none;':''};width: 700px;" readonly="true"/>
                                                        <p:commandLink styleClass="ui-icon ui-icon-search"
                                                                       style="#{contraindCltWeb.contraindCltList.size() le 0?'pointer-events:none;':''};float: right;position: relative;top: 5px;left: -28px;"
                                                                       action="#{tdTjBhkCltListCommBean.selContraindAction}"
                                                                       disabled="#{tdTjBhkCltListCommBean.view ne null or contraindCltWeb.contraindCltList.size() le 0?true:false}"
                                                                       process="@this">
                                                            <f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
                                                            <p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onSelContraindAction}" process="@this"
                                                                    resetValues="true" update="contraind" />
                                                        </p:commandLink>
                                                        <!-- 清空 -->
                                                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                                       style="#{(contraindCltWeb.contraindCltList == null or contraindCltWeb.contraindCltList.size() le 0)?'pointer-events:none;':''};position: relative;top: 5px;left: -13px;"
                                                                       disabled="#{tdTjBhkCltListCommBean.view ne null or contraindCltWeb.contraindCltList == null or contraindCltWeb.contraindCltList.size() le 0?true:false}"
                                                                       process="@this" update="contraind" action="#{tdTjBhkCltListCommBean.clearContraindAction}">
                                                            <f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
                                                        </p:commandLink>
                                                    </td>
                                                </tr>
                                            </table>
										</p:column>
										<p:column headerText="操作" style="padding-left:2px;">
											<p:spacer width="5" />
											<p:commandLink value="删除" update=":tabView:editTabView:mhkrstListForm:contraindTable"
														   action="#{tdTjBhkCltListCommBean.delContraindList(contraindCltWeb)}"
														   rendered="#{tdTjBhkCltListCommBean.view eq null}">
														<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />   
											</p:commandLink>
										</p:column>
									</p:dataTable>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:column>
				</p:row>

				<!-- 疑似职业病 -->
				<p:row>
					<p:column style="height:30px">
						<p:selectBooleanCheckbox value="#{tdTjBhkCltListCommBean.supoccdiseSelect}"
												 itemLabel="&#160;#{tdTjBhkCltListCommBean.mhkrstList.get(4).codeName}"
												 style="#{tdTjBhkCltListCommBean.view ne null?'padding-left:10px;pointer-events:none;':'padding-left:10px;'}"
												 id="supoccdiseSelect">
							<p:ajax event="change" update=":tabView:editTabView:mhkrstListForm" process="@this,:tabView:editTabView:mhkrstListForm"/>
						</p:selectBooleanCheckbox>

						<p:panelGrid style="width:100%;" id="supoccdiseGrid" rendered="#{tdTjBhkCltListCommBean.supoccdiseSelect}">
							<p:row>
								<p:column style="text-align:left;padding-left:5px;">
									<p:panelGrid style="width:100%;" rendered="#{tdTjBhkCltListCommBean.view eq null}">
										<p:row>
											<p:column style="width:70px;text-align:left;border-color: transparent;position: relative;left: -3px;">
												<p:commandButton value="添加" icon="ui-icon-plus" resetValues="true" 
																action="#{tdTjBhkCltListCommBean.addOccdise}" update="supoccdiseTable"/>
											</p:column>
										</p:row>
									</p:panelGrid>

									<p:dataTable id="supoccdiseTable" paginatorPosition="bottom" value="#{tdTjBhkCltListCommBean.supoccdiseCltWebList}"
												style="padding-bottom: 20px;"
												 widgetVar="mhkrstTable" var="supoccdiseClt" emptyMessage="没有数据！" rowIndexVar="R">
										<p:column headerText="危害因素" style="width:300px;text-align: center;">
											<p:selectOneMenu value="#{supoccdiseClt.badRsnId}" style="#{tdTjBhkCltListCommBean.view ne null?'width: 260px;pointer-events:none;':'width: 260px;'}"
												id="badRsnId0">
												<f:selectItem itemLabel="--请选择--" itemValue=""/>
												<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjBhkClt.badList}" var="badrsnsClt"
															 itemLabel="#{badrsnsClt.codeName}" itemValue="#{badrsnsClt.rid}"/>
												<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.getOccdiseList(supoccdiseClt)}"
														process="@this" update="supoccdiseTable"/>
											</p:selectOneMenu>
										</p:column>
										<p:column headerText="疑似职业病" style="width:800px;text-align: center;">
                                            <!-- 疑似职业病弹出框 -->
                                            <table>
                                                <tr>
                                                    <td style="padding: 0;border-color: transparent;display: flex;height: auto;">
                                                        <p:inputText id="supoccdise" onclick="$(this).next().click()" value="#{supoccdiseClt.supoccdise}" style="#{(supoccdiseClt.supoccdiseCltList == null or supoccdiseClt.supoccdiseCltList.size() le 0)?'pointer-events:none;':''};width: 700px;" readonly="true"/>
                                                        <p:commandLink styleClass="ui-icon ui-icon-search"
                                                                       style="#{(supoccdiseClt.supoccdiseCltList == null or supoccdiseClt.supoccdiseCltList.size() le 0)?'pointer-events:none;':''};float: right;position: relative;top: 5px;left: -28px;"
                                                                       action="#{tdTjBhkCltListCommBean.selSupoccdiseAction}"
                                                                       disabled="#{tdTjBhkCltListCommBean.view ne null or supoccdiseClt.supoccdiseCltList.size() le 0?true:false}"
                                                                       process="@this">
                                                            <f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
                                                            <p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onSelSupoccdiseAction}" process="@this"
                                                                    resetValues="true" update="supoccdise" />
                                                        </p:commandLink>
                                                        <!-- 清空 -->
                                                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空#{supoccdiseClt.supoccdiseCltList.size()}"
                                                                       style="#{(supoccdiseClt.supoccdiseCltList == null or supoccdiseClt.supoccdiseCltList.size() le 0)?'pointer-events:none;':''};position: relative;top: 5px;left: -13px;"
                                                                       disabled="#{tdTjBhkCltListCommBean.view ne null or supoccdiseClt.supoccdiseCltList == null or supoccdiseClt.supoccdiseCltList.size() le 0?true:false}"
                                                                       process="@this" update="supoccdise" action="#{tdTjBhkCltListCommBean.clearSupoccdiseAction}">
                                                            <f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
                                                        </p:commandLink>
                                                    </td>
                                                </tr>
                                            </table>
										</p:column>
										<p:column headerText="接触相应职业病危害因素的用人单位名称" style="width:300px;text-align: center;" rendered="#{tdTjBhkCltListCommBean.ifBeforeOnPost}">
											<p:inputText  style="width:270px;#{(tdTjBhkCltListCommBean.view ne null or supoccdiseClt.supoccdiseCltList == null or supoccdiseClt.supoccdiseCltList.size() le 0)?'pointer-events:none;':''};" value="#{supoccdiseClt.crptName}"
														  maxlength="100" id="crptName"/>
										</p:column>
										<p:column headerText="操作" style="padding-left:2px;">
											<p:spacer width="5" />
											<p:commandLink value="删除" update=":tabView:editTabView:mhkrstListForm:supoccdiseTable"
														   action="#{tdTjBhkCltListCommBean.delOccdiseList(supoccdiseClt)}"
														   rendered="#{tdTjBhkCltListCommBean.view eq null}">
												 <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
											</p:commandLink>
										</p:column>
									</p:dataTable>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:fieldset>

		<p:fieldset legend="检查结论" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{tdTjBhkCltListCommBean.hasBhkResultStyle}">
			<p:dataTable  paginatorPosition="bottom" value="#{tdTjBhkCltListCommBean.tdTjBadrsnsCltList}"
						 style="padding-bottom: 20px;" id="mhkrstTable1"
						 widgetVar="mhkrstTable1" var="itm" emptyMessage="没有数据！" rowIndexVar="R">
				<p:column headerText="序号" style="width:60px;text-align: center;">
					<h:outputLabel value="#{R+1}"/>
				</p:column>
				<p:column headerText="危害因素" style="width:300px;">
					<h:outputLabel value="#{itm.fkByBadrsnId.codeName}"/>
				</p:column>
				<p:column headerText="体检结论" style="width:300px;text-align: center;">
					<p:selectOneMenu  value="#{itm.examConclusionId}" style="#{tdTjBhkCltListCommBean.view ne null?'width: 260px;pointer-events:none;':'width: 260px;'}" id="badRsnId1">
						<f:selectItem itemLabel="--请选择--" itemValue=""/>
						<f:selectItems value="#{tdTjBhkCltListCommBean.mhkrstList}" var="badrsnsClt"
									   itemLabel="#{badrsnsClt.codeName}" itemValue="#{badrsnsClt.rid}"/>
						<p:ajax event="change" process="@this,mhkrstTable1" listener="#{tdTjBhkCltListCommBean.onExamConclusionChange(R)}"
								update="mhkrstTable1"/>
					</p:selectOneMenu>
				</p:column>

				<p:column headerText="疑似职业病/职业禁忌证/其他疾病或异常" style="width:700px;" rendered="#{itm.fkByExamConclusionId.extendS2!=3 and itm.fkByExamConclusionId.extendS2!=4 and itm.fkByExamConclusionId.extendS2!=5}"/>
				<p:column headerText="疑似职业病/职业禁忌证/其他疾病或异常" style="width:700px;" rendered="#{itm.fkByExamConclusionId.extendS2==3}">
					<!--其他疾病或异常-->
					<p:inputText style="width: 95%;" value="#{itm.qtjbName}" readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" maxlength="500"/>
				</p:column>
				<p:column headerText="疑似职业病/职业禁忌证/其他疾病或异常" style="width:700px;" rendered="#{itm.fkByExamConclusionId.extendS2==4}">
					<!--职业禁忌证-->
					<!-- 职业禁忌症弹出框 -->
					<table>
						<tr>
							<td style="padding: 0;border-color: transparent;display: flex;height: auto;">
								<p:inputText id="contraind1" onclick="$(this).next().click()" value="#{itm.contraindCltWeb.contraind}"
											 rendered="#{itm.fkByExamConclusionId.extendS2==4}" style="#{itm.contraindCltWeb.contraindCltList.size() le 0?'pointer-events:none;':''};width: 95%;" readonly="true"/>
								<p:commandLink styleClass="ui-icon ui-icon-search"
											   style="#{contraindCltWeb.contraindCltList.size() le 0?'pointer-events:none;':''};float: right;position: relative;top: 5px;left: -28px;"
											   action="#{tdTjBhkCltListCommBean.selContraindNew1Action}"
											   rendered="#{itm.fkByExamConclusionId.extendS2==4}"
											   disabled="#{tdTjBhkCltListCommBean.view ne null or itm.contraindCltWeb.contraindCltList.size() le 0?true:false}"
											   process="@this">
									<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
									<p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onSelContraindNew1Action}" process="@this"
											resetValues="true" update="contraind1" />
								</p:commandLink>
								<!-- 清空 -->
								<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" rendered="#{itm.fkByExamConclusionId.extendS2==4}"
											   style="#{contraindCltWeb.contraindCltList.size() le 0?'pointer-events:none;':''};position: relative;top: 5px;left: -13px;"
											   disabled="#{tdTjBhkCltListCommBean.view ne null or itm.contraindCltWeb.contraindCltList.size() le 0?true:false}"
											   process="@this" update="contraind1" action="#{tdTjBhkCltListCommBean.clearContraindNew1Action}">
									<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
								</p:commandLink>
							</td>
						</tr>
					</table>
				</p:column>
				<p:column headerText="疑似职业病/职业禁忌证/其他疾病或异常" style="width:700px;" rendered="#{itm.fkByExamConclusionId.extendS2==5}">
					<!--疑似职业病-->
					<!--疑似职业病弹出框 -->
					<table>
						<tr>
							<td style="padding: 0;border-color: transparent;display: flex;height: auto;">
								<p:inputText id="supoccdise1" onclick="$(this).next().click()" value="#{itm.supoccdiseCltWeb.supoccdise}"
											 rendered="#{itm.fkByExamConclusionId.extendS2==5}" style="#{itm.supoccdiseCltWeb.supoccdiseCltList.size() le 0?'pointer-events:none;':''};width: 95%;" readonly="true"/>
								<p:commandLink styleClass="ui-icon ui-icon-search"
											   style="#{supoccdiseClt.supoccdiseCltList.size() le 0?'pointer-events:none;':''};float: right;position: relative;top: 5px;left: -28px;"
											   action="#{tdTjBhkCltListCommBean.selSupoccdiseNew1Action}" rendered="#{itm.fkByExamConclusionId.extendS2==5}"
											   disabled="#{tdTjBhkCltListCommBean.view ne null or itm.supoccdiseCltWeb.supoccdiseCltList.size() le 0?true:false}"
											   process="@this">
									<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
									<p:ajax event="dialogReturn" listener="#{tdTjBhkCltListCommBean.onSelSupoccdiseNew1Action}" process="@this"
											resetValues="true" update="supoccdise1" />
								</p:commandLink>
								<!-- 清空 -->
								<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
											   style="#{supoccdiseClt.supoccdiseCltList.size() le 0?'pointer-events:none;':''};position: relative;top: 5px;left: -13px;" rendered="#{itm.fkByExamConclusionId.extendS2==5}"
											   disabled="#{tdTjBhkCltListCommBean.view ne null or itm.supoccdiseCltWeb.supoccdiseCltList.size() le 0?true:false}"
											   process="@this" update="supoccdise1" action="#{tdTjBhkCltListCommBean.clearSupoccdiseNew1Action}">
									<f:setPropertyActionListener target="#{tdTjBhkCltListCommBean.contraindIndex}" value="#{R}"/>
								</p:commandLink>
							</td>
						</tr>
					</table>
				</p:column>
				<p:column headerText="接触相应职业病危害因素的用人单位名称" style="text-align: center;" rendered="#{tdTjBhkCltListCommBean.ifBeforeOnPost}">
					<p:inputText  style="width:#{tdTjBhkCltListCommBean.hasBhkResultStyle?'370':'270'}px" value="#{itm.supoccdiseCltWeb.crptName}"
								  rendered="#{itm.fkByExamConclusionId.extendS2 eq 5}"
								  readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}"
								  maxlength="100" />
				</p:column>
			</p:dataTable>
		</p:fieldset>

		<p:fieldset legend="主检建议" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:remoteCommand name="initChiefDoctor" action="#{tdTjBhkCltListCommBean.tdTjChiefDoctorBean.hideChiefDoctorAction}" process="@this,chiefDoctorGrid"/>
            <p:overlayPanel id="chiefDoctorPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}" for="onChiefDoctorSelect" hideEvent="mousedown" onHide="initChiefDoctor()" dynamic="false"
                            style="width:280px;" widgetVar="ChiefDoctorPanel" showCloseIcon="true" appendToBody="false">
                <p:tree id="chiefDoctorTree" value="#{tdTjBhkCltListCommBean.tdTjChiefDoctorBean.chiefDoctorSortTree}" var="node" selectionMode="checkbox"
                        style="width: 250px;height: 300px;overflow-y: auto;">
                    <p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjChiefDoctorBean.onChiefDoctorNodeSelect}"/>
                    <p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjChiefDoctorBean.onChiefDoctorNodeNoSelect}"/>
                    <p:treeNode>
                        <h:outputText value="#{node.empName}" />
                    </p:treeNode>
                </p:tree>
            </p:overlayPanel>
            <p:panelGrid style="width:100%;" id="chkGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:100px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="体检结果："/>
					</p:column>
					<p:column colspan="5">
						<p:inputTextarea value="#{tdTjBhkCltListCommBean.tdTjBhkClt.bhkrst}" readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}"
											style="resize: none;width: 80%;height:150px" rows="5" autoResize="false"
											id="bhkrst">
						</p:inputTextarea>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:100px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="综合建议："/>
					</p:column>
					<p:column colspan="5">
						<p:inputTextarea value="#{tdTjBhkCltListCommBean.tdTjBhkClt.mhkadv}" readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}"
											style="resize: none;width: 80%;height:150px" rows="5" autoResize="false"
											id="mhkadv">
						</p:inputTextarea>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:130px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="主检医师："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;width: 240px;">
                        <!-- 主检医师多选框 -->
                        <p:outputPanel>
                            <h:panelGrid id="chiefDoctorGrid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                                <p:inputText id="chiefDoctor" readonly="true" onclick="document.getElementById('tabView:editTabView:mhkrstListForm:onChiefDoctorSelect').click();"
                                             value="#{tdTjBhkCltListCommBean.tdTjChiefDoctorBean.chiefDoctor}" style="width:200px;"/>
                                <p:commandLink id="onChiefDoctorSelect" disabled="#{tdTjBhkCltListCommBean.view eq null?false:true}" styleClass="ui-icon ui-icon-search"
                                               process="@this" style="position: relative;left: -30px;top:0px;" update="chiefDoctorPanel"  oncomplete="PF('ChiefDoctorPanel').show()"/>
                            </h:panelGrid>
                        </p:outputPanel>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:130px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="主检日期："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;width:240px;">
						<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true" style="#{tdTjBhkCltListCommBean.view ne null?'width: 200px;pointer-events:none;':'width: 200px;'}"
									showOtherMonths="true" size="22" navigator="true"
									yearRange="c-70:c" converterMessage="判定日期，格式输入不正确！"
									showButtonPanel="true" maxdate="new Date()"
									value="#{tdTjBhkCltListCommBean.tdTjBhkClt.jdgdat}" 
									id="jdgdat"/>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:130px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="报告出具日期："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true" style="#{tdTjBhkCltListCommBean.view ne null?'width: 200px;pointer-events:none;':'width: 200px;'}"
									showOtherMonths="true" size="22" navigator="true"
									yearRange="c-70:c" converterMessage="报告出具日期，格式输入不正确！"
									showButtonPanel="true" maxdate="new Date()"
									value="#{tdTjBhkCltListCommBean.tdTjBhkClt.rptPrintDate}" 
									id="rptPrintDate"/>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:fieldset>
	</h:form>
	
	<ui:insert name="insertOtherMainContents">
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
	</ui:insert>
</ui:composition>
