<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<ui:insert name="insertEditScripts">
		<script type="text/javascript">
			function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
		</script>
	</ui:insert>
	<h:outputScript name="js/validate/system/validate.js" />
	<style>
		.myCalendar1 input{
			width: 160px;
		}
		table.ui-selectoneradio td label{
			white-space:nowrap;
			overflow: hidden;
		}
	</style>
	<h:form id="basicInfoForm">
		<!-- 标题栏 -->
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="basicTitleGrid" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="体检信息录入-->基本信息"/>
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>

		<p:outputPanel styleClass="zwx_toobar_42"  style="display:flex;" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
				<p:commandButton value="保存" icon="ui-icon-disk" action="#{tdTjBhkCltListCommBean.beforeSaveBasicInfo}" update=":tabView:editTabView" process="@this,:tabView:editTabView:basicInfoForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="下一步" action="#{tdTjBhkCltListCommBean.beforeNextBasicInfo}" update=":tabView:editTabView" process="@this,:tabView:editTabView:basicInfoForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdTjBhkCltListCommBean.backAction}" process="@this" update=":tabView" immediate="true"/>
			</h:panelGrid>
			<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">

				<h:outputLabel value="注：" style="color:red;"></h:outputLabel>
				<h:outputLabel value="主动监测的工种和防护用品佩戴情况由劳动者花名册自动载入，无需选择！" style="color:blue;"></h:outputLabel>
			</p:outputPanel>
		</p:outputPanel>
		<p:confirmDialog  header="消息确认框" severity="alert" closable="false" styleClass="fsryConfirmDialog"
						  widgetVar="SaveBasicConfirmDialog" style="text-align: left">
			<f:facet name="message">
				<h:outputText value="#{tdTjBhkCltListCommBean.saveBasicMessage}" escape="false"/>
			</f:facet>
			<p:outputPanel style="text-align:right;">
				<p:commandButton value="确定" action="#{tdTjBhkCltListCommBean.saveAction}"
								 onclick="showStatus()"
								 update=":tabView:editTabView:basicInfoForm" process="@this"
								 oncomplete="hideStatus();PF('SaveBasicConfirmDialog').hide()"
				/>
				<p:commandButton value="取消" style="text-align:right;" oncomplete="PF('SaveBasicConfirmDialog').hide()"/>
			</p:outputPanel>
		</p:confirmDialog>
		<p:confirmDialog  header="消息确认框" severity="alert" closable="false" styleClass="fsryConfirmDialog"
						  widgetVar="NextBasicConfirmDialog" style="text-align: left">
			<f:facet name="message">
				<h:outputText value="#{tdTjBhkCltListCommBean.saveBasicMessage}" escape="false"/>
			</f:facet>
			<p:outputPanel style="text-align:right;">
				<p:commandButton value="确定" action="#{tdTjBhkCltListCommBean.nextAction(0)}"
								 onclick="showStatus()"
								 update=":tabView:editTabView" process="@this"
								 oncomplete="hideStatus();PF('NextBasicConfirmDialog').hide()"
				/>
				<p:commandButton value="取消" style="text-align:right;" oncomplete="PF('NextBasicConfirmDialog').hide()"/>
			</p:outputPanel>
		</p:confirmDialog>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;" id="rhkPanel">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:200px;height: 39px;">
					<h:outputText value="*" style="color:red;" />
					<p:outputLabel value="体检编号："/>
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width: 260px;">
					<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifNotMod eq 1?true:false or tdTjBhkCltListCommBean.ifRhkAndZdJc}"
								 style="width:160px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.bhkCode}" maxlength="25"
								 id="bhkCode" rendered="#{!tdTjBhkCltListCommBean.hasAutoGenBhkCode }"/>
					<p:outputLabel value="自动生成" style="color:gray;" rendered="#{tdTjBhkCltListCommBean.hasAutoGenBhkCode and  empty tdTjBhkCltListCommBean.tdTjBhkClt.bhkCode}"/>
					<p:outputLabel value="#{tdTjBhkCltListCommBean.tdTjBhkClt.bhkCode}" rendered="#{tdTjBhkCltListCommBean.hasAutoGenBhkCode and not empty tdTjBhkCltListCommBean.tdTjBhkClt.bhkCode}"/>
				</p:column>

				<p:column style="text-align:right;padding-right:3px;width:200px;">
					<h:outputText value="*" style="color:red;" />
					<p:outputLabel value="体检日期："/>
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width: 300px;">
					<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
								showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
								yearRange="c-70:c" converterMessage="体检日期，格式输入不正确！"
								showButtonPanel="true" maxdate="new Date()" style="#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}"
								value="#{tdTjBhkCltListCommBean.tdTjBhkClt.bhkDate}"
								id="bhkDate"/>
				</p:column>

				<p:column style="text-align:right;padding-right:3px;width:200px;">
					<h:outputText value="*" style="color:red;" />
					<p:outputLabel value="是否复检："/>
				</p:column>
				<p:column style="text-align:left;padding-left:3px;">
					<p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
						<p:selectOneRadio value="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk}" style="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifNotMod eq 1?'width:80px;pointer-events:none;':'width:80px;'}"
										  id="ifRhk">
							<f:selectItem itemLabel="是" itemValue="1"/>
							<f:selectItem itemLabel="否" itemValue="0"/>
							<p:ajax event="change" process="@this,:tabView:editTabView:basicInfoForm" listener="#{tdTjBhkCltListCommBean.changeIfRhk}" update="rhkPanel"/>
						</p:selectOneRadio>
						<p:spacer width="5" />
						<p:commandButton style="margin-top: 2px;margin-left: 3px;" value="关联" rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 and tdTjBhkCltListCommBean.tdTjBhkClt.ifNotMod eq 0}" action="#{tdTjBhkCltListCommBean.selectBhkCltList}"  process="@this" resetValues="true">
							<p:ajax event="dialogReturn" update="rhkPanel,basicPanel"  process="@this" resetValues="true" listener="#{tdTjBhkCltListCommBean.onBhkCltSelect}" />
						</p:commandButton>
					</p:outputPanel>
				</p:column>
			</p:row>
		</p:panelGrid>

		<p:outputPanel id="basicOutputPanel">
			<p:panelGrid style="width:100%;margin-top:-1px;" id="basicPanel">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:200px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="用人单位名称："/>
					</p:column>
					<p:column style="text-align:left;padding-left:2px;" colspan="3">
						<h:panelGrid columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="crptName"  readonly="true" style="width:538px;" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.crptName}" onclick="$('#tabView\\:editTabView\\:basicInfoForm\\:onCrptSelect').click()"/>
							<p:commandLink disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}" id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
										   action="#{tdTjBhkCltListCommBean.selectCrptList(1)}" process="@this" style="position: relative;left: -30px;"
										   rendered="#{!tdTjBhkCltListCommBean.ifRhkAndZdJc and tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1 }"
							>
								<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.ifTjlrCrpt}"/>
								<p:ajax event="dialogReturn" process="@this,basicPanel" resetValues="true" listener="#{tdTjBhkCltListCommBean.onCrptSelect}" update="basicPanel"/>
							</p:commandLink>
						</h:panelGrid>
					</p:column>

					<p:column style="text-align:right;padding-right:3px;width:200px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="姓名："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:163px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.personName}" maxlength="100" id="personName"/>
					</p:column>
				</p:row>

				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="证件类型："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;width: 260px;">
						<p:selectOneMenu value="#{tdTjBhkCltListCommBean.editPsnTypeId}"
										 style="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 ?'width: 167px;pointer-events:none;':'width: 167px;'}"
										 id="psnType" disabled="#{tdTjBhkCltListCommBean.ifRhkAndZdJc}">
							<f:selectItems value="#{tdTjBhkCltListCommBean.editPsnTypeMap}"/>
							<p:ajax event="change" process="@this,basicPanel" update="basicPanel"
									listener="#{tdTjBhkCltListCommBean.onPsnTypeChangeAction}" />
						</p:selectOneMenu>
					</p:column>

					<p:column style="text-align:right;padding-right:3px;width:200px;">
						<h:outputText value="*" style="color:red;" rendered="#{tdTjBhkCltListCommBean.ifIdcAble}"/>
						<p:outputLabel value="证件号码："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;width: 300px;">
						<p:inputText id="idc" readonly="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 ?true:false}"
									 style="width:160px;float: left;" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.idc}" maxlength="25" onblur="findFlowByIdc()"
									 disabled="#{!tdTjBhkCltListCommBean.ifIdcAble or tdTjBhkCltListCommBean.ifRhkAndZdJc}"/>
						<p:remoteCommand name="findFlowByIdc" action="#{tdTjBhkCltListCommBean.findFlowByIdc}" process="@this,idc" update="sex,brth"/>
						<p:commandButton  style="margin-top: 2px;margin-left: 10px;" value="查询历史人员" action="#{tdTjBhkCltListCommBean.selectPersonInfo}"
										  process="@this,basicPanel" resetValues="true" update="basicPanel"
										  rendered="#{tdTjBhkCltListCommBean.view eq null and tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 0 }"
										  disabled="#{!tdTjBhkCltListCommBean.ifIdcAble or  tdTjBhkCltListCommBean.ifRhkAndZdJc}"/>
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="性别："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneRadio id="sex" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.sex}" style="#{tdTjBhkCltListCommBean.view ne null  or tdTjBhkCltListCommBean.ifIdc?'pointer-events:none;':''}">
							<f:selectItem itemLabel="男" itemValue="男"/>
							<f:selectItem itemLabel="女" itemValue="女"/>
						</p:selectOneRadio>
					</p:column>
				</p:row>

				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="出生日期："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:calendar id="brth" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
									showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
									yearRange="c-70:c" converterMessage="出生日期，格式输入不正确！"
									showButtonPanel="true" maxdate="new Date()" style="#{tdTjBhkCltListCommBean.view ne null  or tdTjBhkCltListCommBean.ifIdc?'pointer-events:none;':''}"
									value="#{tdTjBhkCltListCommBean.tdTjBhkClt.brth}" />
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<p:outputLabel value="婚姻状况："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneMenu value="#{tdTjBhkCltListCommBean.tdTjBhkClt.isxmrd}" style="#{tdTjBhkCltListCommBean.view ne null?'width: 167px;pointer-events:none;':'width: 167px;'}">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdTjBhkCltListCommBean.marrayList}"/>
						</p:selectOneMenu>
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="联系电话："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:163px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.lnktel}" maxlength="25"
									 id="lnktel"/>
						<h:outputText value="（格式：0510-85373786 / 13712341234）" style="color: red"/>
					</p:column>
				</p:row>

				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 39px;">
						<p:outputLabel value="部门："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:160px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.dpt}" maxlength="25" />
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<p:outputLabel value="工号："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:160px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.wrknum}" maxlength="25" />
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="工种："/>
					</p:column>
					<p:column style="text-align:left;padding-left:6px;">
						<h:panelGrid columns="4" id="work"
									 style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText readonly="true" style="width:160px"
										 id="workTypeName" value="#{tdTjBhkCltListCommBean.workTypeName}" maxlength="25"
										 onclick="$('#tabView\\:editTabView\\:basicInfoForm\\:selWorkLink').click()"/>
							<p:commandLink styleClass="ui-icon ui-icon-search"
										   id="selWorkLink" disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}"
										   action="#{tdTjBhkCltListCommBean.selectWorkTypeAction}" process="@this"
										   style="position: relative;left: -30px;"
										   rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1 and tdTjBhkCltListCommBean.tdTjBhkClt.jcType ne 2}">
								<p:ajax event="dialogReturn"
										listener="#{tdTjBhkCltListCommBean.onWorkTypeSearch}" process="@this"
										resetValues="true" update="work" />
							</p:commandLink>
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" style="position: relative;left: -30px;"
										   disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}" rendered="false"
										   process="@this" update="work" action="#{tdTjBhkCltListCommBean.clearWorkType}" >
							</p:commandLink>
							<p:inputText readonly="#{(tdTjBhkCltListCommBean.view ne null) or !(tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1 and tdTjBhkCltListCommBean.tdTjBhkClt.jcType ne 2)?true:false}" id="ifOtherWork"
										 style="width:160px;position: relative;#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1 and tdTjBhkCltListCommBean.tdTjBhkClt.jcType ne 2?'left: -20px;':''}"
										 value="#{tdTjBhkCltListCommBean.tdTjBhkClt.workOther}" maxlength="25"
										 rendered="#{tdTjBhkCltListCommBean.ifOtherWork}"/>
						</h:panelGrid>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="在岗状态："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 ?'width: 167px;pointer-events:none;':'width: 167px;'}"
										 value="#{tdTjBhkCltListCommBean.editOnguadrStateId}"
										 id="editOnguadrStateId" disabled="#{tdTjBhkCltListCommBean.ifRhkAndZdJc}">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdTjBhkCltListCommBean.editOnguadrStateMap}"/>
							<p:ajax event="change" process="@this,:tabView:editTabView:basicInfoForm"
									update="@this,:tabView:editTabView:basicInfoForm"
									listener="#{tdTjBhkCltListCommBean.onguadrStateChangeAction}" />
						</p:selectOneMenu>
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="体检危害因素："/>
					</p:column>
					<p:column style="text-align:left;padding-left:2px;" colspan="3">
						<h:panelGrid columns="4" id="badRsnPanel"
									 style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText readonly="true" style="width:583px"
										 id="badrsns" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.badrsns}" maxlength="1000"
										 onclick="$('#tabView\\:editTabView\\:basicInfoForm\\:onBadrsnsSelect').click()"/>
							<p:commandLink styleClass="ui-icon ui-icon-search"
										   id="onBadrsnsSelect" disabled="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 ?true:false}"
										   action="#{tdTjBhkCltListCommBean.selectBadtree}" process="@this,touchBadRsnPanel"
										   style="position: relative;left: -30px;">
								<p:ajax event="dialogReturn"
										listener="#{tdTjBhkCltListCommBean.onBadtreeSelect}" process="@this"
										resetValues="true" update="badrsns" />
								<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.ifMainDust}"/>
							</p:commandLink>
							<p:inputText id="otherBadRsn" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.otherBadRsn}"
										 maxlength="200" style="width: 200px;position: relative;left: -30px;"
										 rendered="#{tdTjBhkCltListCommBean.ifOtherBadRsn}"
										 readonly="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 ?true:false}"/>
						</h:panelGrid>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="监测类型："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneRadio id="jcType" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.jcType}" style="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1 ?'width: 167px;pointer-events:none;':'width: 167px;'}"
										  disabled="#{tdTjBhkCltListCommBean.ifRhkAndZdJc}">
							<f:selectItem itemLabel="常规监测" itemValue="1"/>
							<f:selectItem itemLabel="主动监测" itemValue="2"/>
							<p:ajax event="change" process="@this,basicPanel" listener="#{tdTjBhkCltListCommBean.clearWorkType}"
									update="basicPanel" resetValues="true"/>
						</p:selectOneRadio>
					</p:column>

					<p:column style="text-align:right;padding-right:3px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="接触危害因素："/>
					</p:column>
					<p:column style="text-align:left;padding-left:2px;" colspan="3">
						<h:panelGrid columns="4" id="touchBadRsnPanel"
									 style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText readonly="true" style="width:583px"
										 id="touchBadrsns" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.touchBadrsns}" maxlength="1000"
										 onclick="$('#tabView\\:editTabView\\:basicInfoForm\\:onTouchBadrsnsSelect').click()"/>
							<p:commandLink styleClass="ui-icon ui-icon-search"
										   id="onTouchBadrsnsSelect" disabled="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1}"
										   action="#{tdTjBhkCltListCommBean.selectBadtree}" process="@this,touchBadRsnPanel"
										   style="position: relative;left: -30px;">
								<p:ajax event="dialogReturn"
										listener="#{tdTjBhkCltListCommBean.onBadtreeSelect}" process="@this"
										resetValues="true" update="touchBadrsns" />
								<f:setPropertyActionListener value="0" target="#{tdTjBhkCltListCommBean.ifMainDust}"/>
							</p:commandLink>
							<p:inputText id="tchOtherBadrsn" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.tchOtherBadrsn}"
										 maxlength="200" style="width: 200px;position: relative;left: -30px;"
										 rendered="#{tdTjBhkCltListCommBean.ifOtherTouchBadRsn}"
										 readonly="#{tdTjBhkCltListCommBean.view ne null or tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1}"/>
							<p:commandLink value="同体检危害因素"
										   disabled="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1}"
										   rendered="#{tdTjBhkCltListCommBean.view == null}"
										   style="position: relative;left: -25px;#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1?'display:none;':''}"
										   action="#{mgrbean.copyBadrsnsToTouch}" process="@this,badRsnPanel"
										   update="touchBadRsnPanel"/>
						</h:panelGrid>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 39px;" >
						<h:outputText value="*" style="color:red;"
									  rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.wrklnt !=null or tdTjBhkCltListCommBean.tdTjBhkClt.wrklntmonth !=null}"/>
						<p:outputLabel value="总工龄：" />
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:58px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.wrklnt}"
									 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"  onkeydown="SYSTEM.clearNoNum(this)"
									 maxlength="3" id="wrklnt" onchange="changezgl()"/>
						<p:remoteCommand name="changezgl"  process="@this,basicPanel" update="basicPanel"/>
						<p:spacer width="10" />
						<h:outputLabel value="年"/>
						<p:spacer width="10" />
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:58px" value="#{tdTjBhkCltListCommBean.tdTjBhkClt.wrklntmonth}"
									 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"  onkeydown="SYSTEM.clearNoNum(this)"
									 maxlength="2" id="wrklntmonth" onchange="changezgl()"/>
						<p:spacer width="10" />
						<h:outputLabel value="月"/>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;" id="jhgl">
						<h:outputText value="*" style="color:red;" rendered="#{!tdTjBhkCltListCommBean.tchbadrsntimAble}"/>
						<p:outputLabel value="接害工龄："/>
						<br/>
						<p:outputLabel value="（实际接触危害因素的累计工龄）"/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;" colspan="#{(tdTjBhkCltListCommBean.tdTjBhkClt.jcType != null and tdTjBhkCltListCommBean.tdTjBhkClt.jcType eq '2') ? '1' : '3'}">
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:58px"
									 value="#{tdTjBhkCltListCommBean.tdTjBhkClt.tchbadrsntim}"
									 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"  onkeydown="SYSTEM.clearNoNum(this)"
									 maxlength="3" id="tchbadrsntim" disabled="#{tdTjBhkCltListCommBean.tchbadrsntimAble}"/>
						<p:spacer width="10" />
						<h:outputLabel value="年"/>
						<p:spacer width="10" />
						<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" style="width:58px"
									 value="#{tdTjBhkCltListCommBean.tdTjBhkClt.tchbadrsnmonth}"
									 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"  onkeydown="SYSTEM.clearNoNum(this)"
									 maxlength="2" id="tchbadrsnmonth" disabled="#{tdTjBhkCltListCommBean.tchbadrsntimAble}"/>
						<p:spacer width="10" />
						<h:outputLabel value="月"/>
					</p:column>
					<!--防护用品佩戴情况 只读显示-->
					<p:column style="text-align:right;padding-right:3px;display:#{(tdTjBhkCltListCommBean.tdTjBhkClt.jcType != null and tdTjBhkCltListCommBean.tdTjBhkClt.jcType eq '2') ? '' : 'none'}" >
						<p:outputLabel value="防护用品佩戴情况："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;#{(tdTjBhkCltListCommBean.tdTjBhkClt.jcType != null and tdTjBhkCltListCommBean.tdTjBhkClt.jcType eq '2') ? '' : 'none'}" >
						<p:outputLabel value="#{tdTjBhkCltListCommBean.tdTjBhkClt.fkByProtectEquId.codeName}"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="用工单位名称："/>
						<br/>
						<p:outputLabel value="（劳动者实际工作单位）"/>
					</p:column>
					<p:column style="text-align:left;padding-left:2px;" colspan="5">
						<h:panelGrid columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="entrsutCrptName"  readonly="true" style="width:538px;"
										 value="#{tdTjBhkCltListCommBean.tdTjBhkClt.fkByEmpCrptId.crptName}"
										 onclick="$('#tabView\\:editTabView\\:basicInfoForm\\:onEntrsutCrptSelect').click()"/>
							<p:commandLink disabled="#{tdTjBhkCltListCommBean.view ne null?true:false}" id="onEntrsutCrptSelect"
										   styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
										   action="#{tdTjBhkCltListCommBean.selectCrptList(2)}" process="@this"
										   style="position: relative;left: -30px;"
										   rendered="#{!tdTjBhkCltListCommBean.ifRhkAndZdJc and tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1 }">
								<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.ifTjlrCrpt}"/>
								<p:ajax event="dialogReturn" process="@this,basicPanel" resetValues="true"
										listener="#{tdTjBhkCltListCommBean.onEmpCrptSelect}" update="basicPanel"/>
							</p:commandLink>
						</h:panelGrid>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:outputPanel>

		<!-- 是复检：遮罩 -->
		<p:blockUI block="basicOutputPanel" widgetVar="basicButtonBlock"/>
	</h:form>
</ui:composition>
