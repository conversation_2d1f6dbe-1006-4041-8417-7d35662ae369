<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:c="http://java.sun.com/jsp/jstl/core">
	<ui:insert name="insertEditScripts">
		<script type="text/javascript">
			function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
		</script>
	</ui:insert>

	<h:outputScript name="js/validate/system/validate.js" />

	<style type="text/css">
		body {
			margin: 0;
			padding: 0;
			width: 100%;
			height: 100%;
			overflow-x: hidden;
		}

		table.ui-selectoneradio td label{
			white-space:nowrap;
			overflow: hidden;
		}
		table.ui-selectmanycheckbox td label{
			white-space:nowrap;
			overflow: hidden;
		}
		#triangle-up {
			width: 0;
			height: 0;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			border-bottom: 10px solid #4D4D4D;
			position: relative;
			left: 100px;
			top: 3px;
		}
		.myCalendar2 input{
			width: 204px;
		}
		.ui-dialog .ui-dialog-buttonpane{
			text-align: center;
		}
		.hg a {
			display: none;
		}
		.ui-selectonemenu-filter-container > span{
			right: 20px !important;
		}
	</style>

	<h:form id="bhkSubListForm">
		<!-- 标题栏 -->
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="resultTitleGrid" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="体检信息录入-->基本信息-->体检问诊-->检查结果" rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1}"/>
						<h:outputText value="体检信息录入-->基本信息-->检查结果" rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk eq 1}"/>
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42" id="resultPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
				<p:commandButton value="保存" icon="ui-icon-disk" action="#{tdTjBhkCltListCommBean.saveAction}" update=":tabView:editTabView:bhkSubListForm" process="@this,:tabView:editTabView:bhkSubListForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="上一步" action="#{tdTjBhkCltListCommBean.lastAction(2)}" update=":tabView:editTabView" process="@this,:tabView:editTabView:bhkSubListForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="下一步" action="#{tdTjBhkCltListCommBean.preNextAction(2)}" update=":tabView:editTabView" process="@this,:tabView:editTabView:bhkSubListForm"
								onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="添加项目组合" action="#{tdTjBhkCltListCommBean.addSchemeItemsInit}"  update="schemeItemsDialog" icon="ui-icon-plus" process="@this,:tabView:editTabView:bhkSubListForm" />
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdTjBhkCltListCommBean.backAction}" process="@this" update=":tabView" immediate="true"/>
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="resultPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}" />
		<p:outputPanel id="checkResultPanel">
			<c:forEach var="itm" items="#{tdTjBhkCltListCommBean.mustItemSortList}" varStatus="index" >
				<p:fieldset legend="#{itm.codeName}" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
					<p:dataTable var="dta" value="#{itm.bhksubCltList}" emptyMessage="没有您要找的记录!" rowIndexVar="R" id="bhksubCltList#{index.index}">
						<p:column headerText="项目名称" style="width: 180px;text-align: center;height: 42px;">
							<h:outputLabel value="#{dta.fkByItemId.itemName}"/>
						</p:column>
						<p:column headerText="符号" style="width: 100px;text-align: center;#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}" rendered="#{itm.extendS1==1 and tdTjBhkCltListCommBean.tjStadItemsStatus.audioModel==1}">
							<p:outputLabel id="dataVersionOriLabel" rendered="#{dta.fkByItemId.itemTag ge 3 and dta.fkByItemId.itemTag le 14}">
								<p:selectOneMenu style="width: 50px;text-align: left;" value="#{dta.dataVersionOriId}"
												 rendered="#{dta.jdgptn==2 and dta.ifLack==0 }" id="dataVersionOriMenu">
									<f:selectItems value="#{tdTjBhkCltListCommBean.dataVersionList}" var="dataVersion" itemLabel="#{dataVersion.codeName}" itemValue="#{dataVersion.rid}"/>
								</p:selectOneMenu>
							</p:outputLabel>
						</p:column>
						<p:column headerText="原始值" style="width: 260px;text-align: center" rendered="#{itm.extendS1==1 and tdTjBhkCltListCommBean.tjStadItemsStatus.audioModel==1}">
							<p:outputLabel id="firstRstdesc" rendered="#{dta.fkByItemId.itemTag ge 3 and dta.fkByItemId.itemTag le 14}">
								<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view ne null?'width: 210px;pointer-events:none;':'width: 210px;'}"
									editable="true" value="#{dta.itemRstOri}" maxlength="100"
									rendered="#{dta.jdgptn==1 and dta.ifLack==0 }" id="itemRstOri">
									<f:selectItem itemLabel="" itemValue=""/>
									<f:selectItems value="#{dta.tbTjRstdescList}" var="rstdesc" itemLabel="#{rstdesc.rstDesc}" itemValue="#{rstdesc.rid}"/>
									<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.calRglTagAndRstDesc(dta)}" process="@this" update="rgltag">
									</p:ajax>
								</p:selectOneMenu>
								<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" value="#{dta.itemRstOri}"
									style="width: 204px;" rendered="#{dta.jdgptn==2 and dta.ifLack==0}"
									onkeyup="SYSTEM.verifyNumMinus(this,18,6)" onblur="SYSTEM.verifyNumMinus2(this,18,6)" onkeydown="SYSTEM.verifyNumMinus(this,18,6)"
									maxlength="100" id="itemRstInputOri">
									<p:ajax event="blur" listener="#{tdTjBhkCltListCommBean.calDctItemRstAndRgltag(dta,index.index,R)}" process="@this" update="rstdesc,rgltag"/>
								</p:inputText>
							</p:outputLabel>
						</p:column>
						<p:column headerText="符号" style="width: 100px;text-align: center">
							<p:outputLabel id="dataVersionLabel">
								<p:selectOneMenu style=" width: 50px;text-align: left;#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}" value="#{dta.dataVersionId}"
									rendered="#{dta.jdgptn==2 and dta.ifLack==0 }" id="dataVersionMenu">
									<f:selectItems value="#{tdTjBhkCltListCommBean.dataVersionList}" var="dataVersion" itemLabel="#{dataVersion.codeName}" itemValue="#{dataVersion.rid}"/>
								</p:selectOneMenu>
							</p:outputLabel>
						</p:column>
						<p:column headerText="#{itm.extendS1==1?'修正值':'项目结果'}" style="width: 260px;text-align: center">
							<p:outputLabel id="rstdesc">
								<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view ne null?'width: 210px;pointer-events:none;':'width: 210px;'}"
									editable="true" value="#{dta.itemRst}" maxlength="100"
									rendered="#{dta.jdgptn==1 and dta.ifLack==0 }" id="itemRst">
									<f:selectItem itemLabel="" itemValue=""/>
									<f:selectItems value="#{dta.tbTjRstdescList}" var="rstdesc" itemLabel="#{rstdesc.rstDesc}" itemValue="#{rstdesc.rid}"/>
									<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.calRglTagAndRstDesc(dta)}" process="@this" update="rgltag,rstFlag">
									</p:ajax>
								</p:selectOneMenu>
								<p:inputText readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" value="#{dta.itemRst}"
									style="width: 204px;" rendered="#{dta.jdgptn==2 and dta.ifLack==0}"
									onkeyup="SYSTEM.verifyNumMinus(this,18,6)" onblur="SYSTEM.verifyNumMinus2(this,18,6)" onkeydown="SYSTEM.verifyNumMinus(this,18,6)"
									maxlength="100" id="itemRstInput">
									<p:ajax event="blur" listener="#{tdTjBhkCltListCommBean.calRglTagAndRstDescNew(dta,index.index,R,true)}" process="@this" update="rgltag"/>
								</p:inputText>
							</p:outputLabel>
						</p:column>
						<p:column headerText="合格" style="width: 80px;text-align: center">
							<p:outputPanel id="rgltag">
								<p:selectBooleanCheckbox style="#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}" value="#{dta.ifrgltag}" rendered="#{dta.ifLack eq 0}">
									<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.changeRgltag(dta,index.index)}" process="@this" update="rgltag,rstFlag" />
								</p:selectBooleanCheckbox>
							</p:outputPanel>
							<p:confirmDialog message="项目结果为不合格，请确认是否勾选为合格？" header="消息确认框" widgetVar="ConfirmDialog#{index.index}" styleClass="hg">
								<p:commandButton value="确定" icon="ui-icon-check"  onclick="PF('ConfirmDialog#{index.index}').hide();"  />
								<p:commandButton value="取消"   icon="ui-icon-close"  oncomplete="PF('ConfirmDialog#{index.index}').hide();" action="#{tdTjBhkCltListCommBean.changeSetRgltag()}" process="@this,:tabView:editTabView:bhkSubListForm:bhksubCltList#{index.index}" update=":tabView:editTabView:bhkSubListForm:bhksubCltList#{index.index}"/>
							</p:confirmDialog>
						</p:column>
						<p:column headerText="结果判定" style="width: 218px;text-align: center" rendered="#{itm.ifShowRstFlag}">
							<p:outputPanel id="rstFlag">
								<h:outputLabel value="未见异常" rendered="#{dta.ifLack==0 and dta.fkByItemId.itemTag==30 and dta.ifrgltag}"/>
								<p:selectOneRadio id="rstFlagRadio" value="#{dta.rstFlag}" rendered="#{dta.ifLack==0 and dta.fkByItemId.itemTag==30 and !dta.ifrgltag}" style="#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}">
									<f:selectItem itemLabel="尘肺样改变" itemValue="1"/>
									<f:selectItem itemLabel="其他异常" itemValue="2"/>
								</p:selectOneRadio>
							</p:outputPanel>
						</p:column>
						<p:column headerText="未检" style="width: 80px;text-align: center">
							<p:selectBooleanCheckbox style="#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}" value="#{dta.lack}" id="ifLack">
								<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.changeIfLack(dta)}" process="@this" update="dataVersionOriLabel,dataVersionLabel,rstdesc,rgltag,firstRstdesc,rstFlag" />
							</p:selectBooleanCheckbox>
						</p:column>
						<p:column headerText="参考值" style="width: 120px;text-align: center">
							<h:outputLabel value="#{dta.itemStdvalue}"/>
						</p:column>
						<p:column headerText="计量单位" style="width: 80px;text-align: center">
							<h:outputLabel value="#{dta.msrunt}"/>
						</p:column>
						<p:column headerText="操作" style="padding-left: 3px;" >
							<p:spacer width="5" />
							<p:commandLink value="删除" update=":tabView:editTabView:bhkSubListForm:checkResultPanel" process="@this" action="#{tdTjBhkCltListCommBean.deleteBhkSubClt}" rendered="#{tdTjBhkCltListCommBean.view eq null and dta.isMust ne 1}">
								<f:setPropertyActionListener value="#{dta}" target="#{tdTjBhkCltListCommBean.opBhkSubClt}"/>
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							</p:commandLink>
						</p:column>
					</p:dataTable>

					<p:panelGrid style="width:100%;margin-top: -1px;">
						<p:row>
							<p:column colspan="4" style="height: 21px;padding-left: 10px;">
								<span style="border-left: 3px solid #4A90E2;padding-right: 10px"/>
								<p:outputLabel value="体检小结："/>
							</p:column>
						</p:row>

						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:194px;">
								<h:outputText value="*" style="color:red;" />
								<p:outputLabel value="检查日期："/>
							</p:column>
							<p:column style="text-align:center;width: 274px;">
								<p:calendar style="#{tdTjBhkCltListCommBean.view ne null?'pointer-events:none;':''}" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
											showOtherMonths="true" size="26" navigator="true" styleClass="myCalendar2"
											yearRange="c-70:c" converterMessage="检查日期格式输入不正确！"
											showButtonPanel="true" maxdate="new Date()"
											value="#{itm.bhksubCltList.get(0).chkdat}"
											id="chkdat#{index.index}">
								</p:calendar>
							</p:column>
							<p:column style="text-align:right;padding-right:3px;width:94px;">
								<h:outputText value="*" style="color:red;" />
								<p:outputLabel value="体检医师："/>
							</p:column>
							<p:column style="text-align:left;padding-left:8px;">
								<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view ne null?'width: 200px;pointer-events:none;':'width: 200px;'}"
												 filter="true" filterMatchMode="contains"
												 value="#{itm.bhksubCltList.get(0).chkdoctId}" id="chkdoct#{index.index}" >
									<f:selectItem itemLabel="--请选择--" itemValue=""/>
									<f:selectItems value="#{tdTjBhkCltListCommBean.editChkdocMap}"/>
								</p:selectOneMenu>
							</p:column>
						</p:row>

						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:194px;">
								<p:outputLabel value="检查结论："/>
							</p:column>
							<p:column style="text-align:left;padding-left:32px;" colspan="3">
								<p:inputTextarea readonly="#{tdTjBhkCltListCommBean.view ne null?true:false}" rows="3" cols="80" style="width:600px;height:50px;overflow: auto;"
									value="#{itm.bhksubCltList.get(0).diagRest}"
									placeholder="定性项目必填" id="diagRest#{index.index}">
								</p:inputTextarea>
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:fieldset>
			</c:forEach>
		</p:outputPanel>
		<p:dialog header="添加项目组合" widgetVar="schemeItemsDialog" width="700" id="schemeItemsDialog" modal="true" resizable="false">
			<table style="width: 100%">
				<tr>
					<td style="text-align: right;padding-right: 3px;width: 140px;" class="zwx_dialog_font">
						<h:outputText value="项目组合名称/拼音码："/>
					</td>
					<td style="text-align: left;padding-left: 3px;vertical-align: middle;width: 120px;">
						<p:inputText size="10" value="#{tdTjBhkCltListCommBean.searchItemCode}">
							<p:ajax event="keyup" update="selectItemListTable" process="@this,@parent" listener="#{tdTjBhkCltListCommBean.filterSelectItem}" />
						</p:inputText>
					</td>
					<td style="text-align: right;padding-right: 3px;width: 130px;" class="zwx_dialog_font">
						<h:outputText value="只包含GBZ188标准："/>
					</td>
					<td style="text-align: left;padding-left: 3px">
						<h:panelGrid columns="5">
							<p:selectBooleanCheckbox  value="#{tdTjBhkCltListCommBean.ifOnlyGBZ188}">
								<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.filterSelectItem}" process="@this,@parent" update="selectItemListTable" />
							</p:selectBooleanCheckbox>
							<p:spacer width="30" />
							<h:outputText value="您已经选择了 " styleClass="zwx_dialog_font" />
							<h:outputText value="#{tdTjBhkCltListCommBean.selectCount}" styleClass="zwx_dialog_font" id="itemSize" style="color:blue;font-weight: bold" />
							<h:outputText value=" 条记录！" styleClass="zwx_dialog_font" />
						</h:panelGrid>
					</td>
				</tr>
			</table>
			<p:dataTable var="itm" value="#{tdTjBhkCltListCommBean.filterSelectItemList}" widgetVar="selectItemListTable" id="selectItemListTable" paginator="true" rows="10" paginatorPosition="bottom" emptyMessage="没有数据！">
				<p:column headerText="选择" style="width:30px;text-align:center">
					<p:commandLink value="选择" action="#{tdTjBhkCltListCommBean.selectItemAction}" process="@this,selectItemListTable" update=":tabView:editTabView:bhkSubListForm:itemSize, :tabView:editTabView:bhkSubListForm:selectItemListTable">
						<f:setPropertyActionListener value="#{itm}" target="#{tdTjBhkCltListCommBean.selectTbzwItems}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="项目组合名称">
					<h:outputText value="#{itm.tsSimpleCode.codeName}@@@#{itm.tsSimpleCode.codeLevelNo}" id="itemCodeName">
						<f:converter converterId="heth.ItemCodeNameConverterComm" for="itemCodeName"/>
					</h:outputText>
				</p:column>
				<p:column headerText="GBZ188标准" style="text-align: center;width: 80px;">
					<h:outputText value="#{null ne tdTjBhkCltListCommBean.gbz188ItemList and tdTjBhkCltListCommBean.gbz188ItemList.contains(itm)  ?  '是' : '否'}"  />
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" action="#{tdTjBhkCltListCommBean.saveSchemeItems}" process="schemeItemsDialog,@this" update=":tabView:editTabView:bhkSubListForm:checkResultPanel"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" onclick="PF('schemeItemsDialog').hide();" type="button"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<p:confirmDialog message="#{mgrbean.itemTagDiagTip}的项目结果大于100，请确认！" id="itemTagDiag"
						 header="消息确认框" widgetVar="ItemTagDiag">
			<p:commandButton value="确定" action="#{mgrbean.nextAction(2)}"
							 update=":tabView:editTabView" process="@this,:tabView:editTabView:bhkSubListForm"
							 onclick="showStatus()" oncomplete="hideStatus();PF('ItemTagDiag').hide();" />
			<p:commandButton value="取消"
							 onclick="PF('ItemTagDiag').hide();"
							 type="button"/>
		</p:confirmDialog>
		<p:confirmDialog message="必检项目标准发生变化，请重新加载！" id="itemLackDiag"
						 header="消息确认框" widgetVar="ItemLackDiag">
			<p:commandButton value="确定" action="#{mgrbean.refreshLackItems}"
							 update=":tabView:editTabView" process="@this,:tabView:editTabView:bhkSubListForm"
							 onclick="showStatus()" oncomplete="hideStatus();PF('ItemLackDiag').hide();" />
			<p:commandButton value="取消"
							 onclick="PF('ItemLackDiag').hide();"
							 type="button"/>
		</p:confirmDialog>
	</h:form>
</ui:composition>
