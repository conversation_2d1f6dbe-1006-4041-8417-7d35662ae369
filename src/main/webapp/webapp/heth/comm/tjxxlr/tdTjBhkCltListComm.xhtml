<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">

	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdTjBhkCltListCommBean}" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
        <script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<h:outputScript library="js" name="namespace.js" />
	    <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript" src="/resources/component/quickDesktop/Common.js"/>
        <script type="text/javascript" src="/resources/component/quickDesktop/NewPopMenu.js"/>
        <script type="text/javascript" src="/resources/component/quickDesktop/CreatePopup.js"/>
        <script type="text/javascript" src="/resources/component/quickDesktop/index.js"/>
        <script type="text/javascript" src="/resources/component/quickDesktop/zwx.system.js"/>
	    <script type="text/javascript">
            //<![CDATA[
		    function markErrorInfo(id){
		    	if(id){
		    		var arr = id.split(",");
		    		$.each(arr,function(index,obj){
		    			var $id = $("#"+obj);
				    	if($id){console.log($id.hasClass("ui-selectmanycheckbox"));
				    		if($id.hasClass("ui-selectoneradio")){
				    			$id.find(".ui-radiobutton-box").addClass("ui-state-error");
				    		}else if($id.hasClass("ui-selectmanycheckbox")||$id.hasClass("ui-chkbox")||$id.hasClass("checkbox-padding")){
				    			$id.find(".ui-chkbox-box").addClass("ui-state-error");
				    		}else{
				    			$id.addClass("ui-state-error");
						    	if($id.hasClass("ui-selectonemenu")){
						    		$id.find(".ui-selectonemenu-trigger").addClass("ui-state-error");
						    	}
				    		}
				    	}
		    		});
		    	}
		    }

            function forwordPage(unitId, newsTitle){
                if("" != unitId && "" != newsTitle){
                    var url = "/webapp/heth/comm/tbTjStadItemsStatusList.faces?unitId="+unitId;
                    top.ShortcutMenuClick("01",newsTitle,url,"");
                }
            }
            //]]>
		</script>
		<style type="text/css">
			.myCalendar1 input{
				width:77px;
			}
		</style>
	</ui:define>
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />
	<!-- 编辑页面 -->
	<ui:param name="editPage" value="/webapp/heth/comm/tjxxlr/tdTjBhkCltEditComm.xhtml" />

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="体检信息录入" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{tdTjBhkCltListCommBean.searchAction}" update="dataTable"
					process="@this,:tabView:mainForm:mainGrid" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addButton" resetValues="true"
					update=":tabView" action="#{tdTjBhkCltListCommBean.addInitAction}"
					process="@this" />

                <p:commandButton value="标准值配置" icon="ui-icon-newwin" onclick="forwordPage('#{tdTjBhkCltListCommBean.unitId}', '#{tdTjBhkCltListCommBean.newsTitle}');"/>
			</h:panelGrid>

            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;"
                           rendered="#{tdTjBhkCltListCommBean.stadItemsStatus != 1}">
                <h:outputLabel value="提示：" style="color:red;"/>
                <h:outputLabel value="请先在【标准值配置】模块检查并提交标准值" style="color:blue;"/>
            </p:outputPanel>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:100px;height: 38px;" >
				<h:outputLabel value="用人单位地区："/>
			</p:column>
			<p:column style="text-align:left;padding-left:0px;width: 250px;">
				<zwx:ZoneSingleNewComp zoneList="#{tdTjBhkCltListCommBean.zoneList}"
									   zoneCode="#{tdTjBhkCltListCommBean.searchZoneCode}"
									   zoneName="#{tdTjBhkCltListCommBean.searchZoneName}" id="searchZone" ifShowTrash="true" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;">
				<h:outputLabel value="用人单位名称："/>
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 250px;">
				<p:inputText id="searchCrptName" value="#{tdTjBhkCltListCommBean.searchCrptName}" maxlength="50" size="25" style="width: 208px;"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;">
				<h:outputLabel value="社会信用代码："/>
			</p:column>
			<p:column style="text-align:left;padding-left: 8px;">
				<p:inputText id="searchInstitutionCode" value="#{tdTjBhkCltListCommBean.searchInstitutionCode}" style="width: 208px;" maxlength="50"/>
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;height: 38px;">
				<h:outputLabel value="姓名："/>
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="searchPersonName" value="#{tdTjBhkCltListCommBean.searchPersonName}" style="width: 180px;" maxlength="100"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="证件号码：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="searchIdc" value="#{tdTjBhkCltListCommBean.searchIdc}" maxlength="18" style="width: 208px;" placeholder="精确查询"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="体检日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
							showOtherMonths="true" id="bhkBdate" size="11" navigator="true"
							yearRange="c-10:c+10" converterMessage="体检开始日期，格式输入不正确！"
							showButtonPanel="true" maxdate="new Date()"
							value="#{tdTjBhkCltListCommBean.searchBhkBdate}" />
				~
				<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
							showOtherMonths="true" id="bhkEdate" size="11" navigator="true"
							yearRange="c-10:c+10" converterMessage="体检结束日期，格式输入不正确！"
							showButtonPanel="true" maxdate="new Date()"
							value="#{tdTjBhkCltListCommBean.searchBhkEdate}" />
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;height: 38px;">
				<h:outputLabel value="体检编号："/>
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="searchBhkCode" value="#{tdTjBhkCltListCommBean.searchBhkCode}" style="width: 180px;" maxlength="50"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputLabel value="监测类型："/>
			</p:column>
			<p:column style="text-align:left;padding-left:4px;">
				<p:selectManyCheckbox value="#{tdTjBhkCltListCommBean.jcTypeList}">
					<f:selectItem itemLabel="常规监测" itemValue="1"/>
					<f:selectItem itemLabel="主动监测" itemValue="2"/>
				</p:selectManyCheckbox>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="是否复检：" />
			</p:column>
			<p:column style="text-align:left;padding-left:4px;">
				<p:selectManyCheckbox id="searchBhkState" value="#{tdTjBhkCltListCommBean.rhkStateList}">
					<f:selectItem itemLabel="是" itemValue="1"/>
					<f:selectItem itemLabel="否" itemValue="0"/>
				</p:selectManyCheckbox>
			</p:column>

		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height: 38px;">
				<h:outputLabel value="报告出具日期："/>
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<zwx:CalendarDynamicLimitComp styleClass="myCalendar1"
											  startDate="#{mgrbean.searchRptSDate}"
											  endDate="#{mgrbean.searchRptEDate}"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;height: 38px;">
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:4px;" colspan="3">
				<p:selectManyCheckbox id="searchBhkRcdState" value="#{tdTjBhkCltListCommBean.bhkRcdStateList}">
					<f:selectItem itemLabel="待提交" itemValue="1,2,3,4"/>
					<f:selectItem itemLabel="已提交" itemValue="5"/>
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="用人单位地区" style="width:180px;text-align: center;">
			<h:outputText value="#{itm[1]}"/>
		</p:column>
		<p:column headerText="用人单位名称" style="padding-left: 3px; width:250px; ">
			<h:outputText value="#{itm[2]}"/>
		</p:column>
		<p:column headerText="体检编号" style="text-align: center; width:100px; ">
			<h:outputText value="#{itm[3]}"/>
		</p:column>
		<p:column headerText="姓名" style="text-align: center; width:100px; ">
			<h:outputText value="#{itm[4]}"/>
		</p:column>
		<p:column headerText="体检日期" style="width:100px;text-align: center;">
			<h:outputText value="#{itm[6]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
			</h:outputText>
		</p:column>
		<p:column headerText="在岗状态" style="text-align: center; width:80px; ">
			<h:outputText value="#{itm[7]}"/>
		</p:column>
		<p:column headerText="危害因素" style="padding-left: 3px; width:300px; ">
			<h:outputText value="#{itm[8]}"/>
		</p:column>
		<p:column headerText="是否复检" style="width:60px;text-align: center;">
			<h:outputText value="是" rendered="#{itm[9]==1}"/>
			<h:outputText value="否" rendered="#{itm[9]==0}"/>
		</p:column>
		<p:column headerText="报告出具日期" style="width:100px;text-align: center;">
			<h:outputText value="#{itm[12]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
			</h:outputText>
		</p:column>
		<p:column headerText="最新提交时间" style="width:140px;text-align: center;">
			<h:outputText value="#{itm[13]}">
				<f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="Asia/Shanghai" locale="zh_CN" />
			</h:outputText>
		</p:column>
		<p:column headerText="状态" style="width:60px;text-align: center;">
			<h:outputText value="待提交" rendered="#{itm[10]!=5}" />
			<h:outputText value="已提交" rendered="#{itm[10]==5}" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:spacer width="5" rendered="#{itm[10] ne 5}"/>
			<p:commandLink value="修改" update=":tabView" process="@this" rendered="#{itm[10] ne 5}" oncomplete="window.scrollTo(0, 0);" resetValues="true" action="#{tdTjBhkCltListCommBean.modInitAction}">
				<f:setPropertyActionListener value="#{itm[0]}" target="#{tdTjBhkCltListCommBean.rid}"/>
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm[10]==5}"/>
			<p:commandLink value="详情" update=":tabView" process="@this" rendered="#{itm[10] eq 5}"  resetValues="true" action="#{tdTjBhkCltListCommBean.modInitAction}">
				<f:setPropertyActionListener value="#{itm[0]}" target="#{tdTjBhkCltListCommBean.rid}"/>
				<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.view}"/>
				<f:setPropertyActionListener value="#{itm[11]}" target="#{tdTjBhkCltListCommBean.ifDraw}"/>
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm[10] eq 5}"/>
			<p:commandLink value="撤销" update=":tabView" rendered="#{itm[10] eq 5 and itm[11] eq 0}" action="#{tdTjBhkCltListCommBean.repealAction}" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()">
				<f:setPropertyActionListener value="#{itm[0]}" target="#{tdTjBhkCltListCommBean.rid}"/>
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm[10] ne 5}"/>
			<p:commandLink value="删除" process="@this" action="#{tdTjBhkCltListCommBean.openDelConfirmDialog}" rendered="#{itm[10] ne 5}">
				<!--<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />-->
				<f:setPropertyActionListener value="#{itm[0]}" target="#{tdTjBhkCltListCommBean.rid}"/>
			</p:commandLink>
		</p:column>
	</ui:define>

	<!-- 其它内容 -->
	<ui:define name="insertOtherMainContents">
        <p:confirmDialog id="delConfirmDialog"  message="确定要删除吗？" header="消息确认框" widgetVar="DelConfirmDialog" >
            <p:commandButton value="确定" action="#{tdTjBhkCltListCommBean.delAction}" update=":tabView" icon="ui-icon-check" oncomplete="PF('DelConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('DelConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
	</ui:define>
</ui:composition>
