<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<script type="text/javascript">
	</script>

	<style>
	</style>

	<p:outputPanel styleClass="zwx_toobar_42" id="viewPanel" rendered="#{tdTjBhkCltListCommBean.view ne null}">
		<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
			<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
			<p:commandButton value="撤销" icon="ui-icon-cancel" rendered="#{tdTjBhkCltListCommBean.view ne null and tdTjBhkCltListCommBean.tdTjBhkClt.bhkRcdState eq 5 and tdTjBhkCltListCommBean.ifDraw eq 0}"
			action="#{tdTjBhkCltListCommBean.repealAction}" update=":tabView" process="@this" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()">
				<f:setPropertyActionListener value="#{tdTjBhkCltListCommBean.tdTjBhkClt.rid}" target="#{tdTjBhkCltListCommBean.rid}"/>
			</p:commandButton>
			<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdTjBhkCltListCommBean.backAction}" process="@this" update=":tabView" immediate="true"/>
		</h:panelGrid>
	</p:outputPanel>
	<p:sticky target="viewPanel" rendered="#{tdTjBhkCltListCommBean.view ne null}" />

	<p:tabView id="editTabView" dynamic="true" cache="true" activeIndex="#{mgrbean.addActiveTab}"
			   style="border:none !important; padding:0px !important;background:none">
		<p:tab title="基本信息" titleStyle="display:#{tdTjBhkCltListCommBean.view eq null?'none':''}">
			<ui:include src="/webapp/heth/comm/tjxxlr/bhkCltBasicInfoComm.xhtml"/>
		</p:tab>
		<p:tab title="体检问诊" titleStyle="display:#{tdTjBhkCltListCommBean.view eq null?'none':''}" rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk ne 1}">
			<ui:include src="/webapp/heth/comm/tjxxlr/exmsdataListComm.xhtml"/>
		</p:tab>
		<p:tab title="检查结果" titleStyle="display:#{tdTjBhkCltListCommBean.view eq null?'none':''}">
			<ui:include src="/webapp/heth/comm/tjxxlr/bhkSubListComm.xhtml"/>
		</p:tab>
		<p:tab title="检查结论" titleStyle="display:#{tdTjBhkCltListCommBean.view eq null?'none':''}">
			<ui:include src="/webapp/heth/comm/tjxxlr/mhkrstListComm.xhtml"/>
		</p:tab>
	</p:tabView>
	<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
</ui:composition>
