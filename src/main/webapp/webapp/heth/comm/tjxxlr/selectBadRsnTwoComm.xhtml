<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title><h:outputText value="选择危害因素" /></title>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
	</h:head>
	<h:body>
		<h:form id="selectForm">
		<p:outputPanel styleClass="zwx_toobar_42" >
            <h:panelGrid columns="3">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="确定" icon="ui-icon-check" id="submitBtn" action="#{selectedBadRsnCommBeanTwo.submitAction}" process="@this,:selectForm:leftPanel" />
                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectedBadRsnCommBeanTwo.dialogClose}" process="@this"/>
            </h:panelGrid>
        </p:outputPanel>
		<div style="float: left;font-size: 12px;width: 100%;">
			<p:outputPanel id="leftPanel" style="overflow-y: auto;width:100%;">
				<p:tree value="#{selectedBadRsnCommBeanTwo.sortTree}" var="node"
					selectionMode="checkbox" id="choiceTree" 
					style="width: 99%;height: 280px;overflow-y: auto;"
					selection="#{selectedBadRsnCommBeanTwo.selectedBadRsn}">
					<p:treeNode>
						<h:outputText value="#{node.codeName}" />
					</p:treeNode>
				</p:tree>
			</p:outputPanel>
		</div>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
