<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title><h:outputText value="可关联体检信息" /></title>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<style type="text/css">
			/* .ui-commandlink{
			    left: -42px !important;
			} */
		</style>
	</h:head>
	<h:body>
		<h:form id="selectForm">
		<div style="float: left;font-size: 12px;width: 100%;">
			<table style="width: 100%">
				<tr>
					<td style="text-align: right;padding-right: 1px;width: 100px;" class="zwx_dialog_font">
						<h:outputText value="用人单位地区："/>
					</td>
					<td style="text-align:left;padding-left:3px;width: 120px;">
						<zwx:ZoneSingleComp id="searchZone" zoneList="#{selectBhkCltCommBean.zoneList}"
											zoneCode="#{selectBhkCltCommBean.searchZoneCode}"
											zoneName="#{selectBhkCltCommBean.searchZoneName}" />
					</td>

					<td style="text-align: right;padding-right: 1px;width: 100px;" class="zwx_dialog_font">
						<h:outputText value="*" style="color:red;" />
						<h:outputText value="#{selectBhkCltCommBean.ifEmployer ? '用人单位名称':'企业名称'}：" style="width: 10%;"/>
					</td>
					<td style="text-align: left;padding-left: 8px;vertical-align: middle;width:100px;">
						<p:inputText id="searchCrptName" value="#{selectBhkCltCommBean.searchCrptName}" maxlength="50" size="25" style="width: 208px;"/>
					</td>

					<td style="text-align: right;padding-right: 1px;width: 80px;" class="zwx_dialog_font">
						<h:outputText value="姓名：" style="width: 10%;"/>
					</td>
					<td style="text-align: left;padding-left: 8px;vertical-align: middle;width:280px;">
						<p:inputText id="searchPersonName" value="#{selectBhkCltCommBean.searchPersonName}" maxlength="100" style="width: 208px;"/>
					</td>
				</tr>

				<tr>
					<td style="text-align: right;padding-right: 1px;width: 100px;" class="zwx_dialog_font">
						<h:outputText value="证件号码：" style="width: 10%;"/>
					</td>
					<td style="text-align: left;padding-left: 6px;vertical-align: middle;width:300px;" colspan="2">
						<p:inputText id="searchIdc" value="#{selectBhkCltCommBean.searchIdc}" maxlength="25" style="width: 180px;" placeholder="精确查询"/>
					</td>

					<td style="text-align: left;padding-left: 8px;" colspan="3">
						<p:commandButton value="查询" icon="ui-icon-search" action="#{selectBhkCltCommBean.searchAction}" update=":selectForm:bhkCltTable"
										 process="@this,searchZone,searchCrptName,searchIdc,searchPersonName"/>
					</td>
				</tr>
			</table>
			<p:dataTable id="bhkCltTable" paginatorPosition="bottom" value="#{selectBhkCltCommBean.bhkCltList}" widgetVar="bhkCltTable" var="bhkClt" paginator="true" rows="10" emptyMessage="没有数据！">
				<p:column headerText="选择" style="width:60px;text-align:center;">
					<p:commandLink value="选择" process="@this" action="#{selectBhkCltCommBean.selectBhkCltAction}">
						<f:setPropertyActionListener target="#{selectBhkCltCommBean.selectBhkClt}" value="#{bhkClt}"/>
					</p:commandLink>
				</p:column>
				<p:column headerText="体检编号"  width="60px;" style="text-align:center;">
					<h:outputText value="#{bhkClt.bhkCode}"/>
				</p:column>
				<p:column headerText="姓名"  width="80px;" style="text-align:center;">
					<h:outputText value="#{bhkClt.personName}"/>
				</p:column>
				<p:column headerText="体检日期"  width="100px;" style="text-align:center;">
					<h:outputText value="#{bhkClt.bhkDate}">
						<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
					</h:outputText>
				</p:column>
				<p:column headerText="在岗状态"  width="80px;" style="text-align:center;">
					<h:outputText value="#{bhkClt.fkByOnguardStateid.codeName}"/>
				</p:column>
				<p:column headerText="危害因素" width="360px;">
					<h:outputText value="#{bhkClt.badrsns}"/>
				</p:column>
			</p:dataTable>
		</div>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
