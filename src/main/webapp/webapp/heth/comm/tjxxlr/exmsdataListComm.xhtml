<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
	<ui:insert name="insertEditScripts">
		<script type="text/javascript">
			//<![CDATA[
			function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
			function removeExtraPanel(elStr){
				//移除掉多余的地区框
				var el = jQuery(elStr);
				if(el.length>1){
					el.each(function(index){
						if(index>0){
							$(this).remove();
						}
					});
				}
			}
			//]]>
		</script>
	</ui:insert>

	<h:outputScript name="js/validate/system/validate.js" />

	<style type="text/css">
		body {
			margin: 0;
			padding: 0;
			width: 100%;
			height: 100%;
			overflow-x: hidden;
		}

		table.ui-selectoneradio td label{
			white-space:nowrap;
			overflow: hidden;
		}
		table.ui-selectmanycheckbox td label{
			white-space:nowrap;
			overflow: hidden;
		}
		#triangle-up {
			width: 0;
			height: 0;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			border-bottom: 10px solid #4D4D4D;
			position: relative;
			left: 100px;
			top: 3px;
		}
		.myCalendar3 input{
			width: 200px;
		}

        .mySymptomPanelGrid tr{
            border: 0px transparent !important;
        }
        .ui-selectonemenu-panel .ui-selectonemenu-filter-container .ui-icon {
            right: 17px;
        }
		.myCalendar1 input{
			width: 87px;
		}
		.stastpDateMarkClass label{
			margin-top: 0px;
		}
		.ui-selectonemenu-filter-container > span{
			right: 20px !important;
		}
	</style>

	<h:form id="exmsdataListForm">
		<!-- 标题栏 -->
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="exmsdataTitleGrid" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="体检信息录入-->基本信息-->体检问诊"/>
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42" id="checkAskPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}">
			<h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
				<p:commandButton value="保存" icon="ui-icon-disk" action="#{tdTjBhkCltListCommBean.saveAction}" update=":tabView:editTabView:exmsdataListForm" process="@this,:tabView:editTabView:exmsdataListForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="上一步" action="#{tdTjBhkCltListCommBean.lastAction(1)}" update=":tabView:editTabView" process="@this,:tabView:editTabView:exmsdataListForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="下一步" action="#{tdTjBhkCltListCommBean.nextAction(1)}" update=":tabView:editTabView" process="@this,:tabView:editTabView:exmsdataListForm"
								 onclick="showStatus()" oncomplete="hideStatus()"/>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdTjBhkCltListCommBean.backAction}" process="@this" update=":tabView" immediate="true"/>
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="checkAskPanel" rendered="#{tdTjBhkCltListCommBean.view eq null and tdTjBhkCltListCommBean.tdTjBhkClt.ifRhk==0}" />
		<div style="overflow: auto;">
		<table width="100%">
			<!-- 职业史 -->
			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="职业史" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:outputPanel styleClass="zwx_toobar_42" style="display:flex" rendered="#{tdTjBhkCltListCommBean.view eq null}">
							<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
								<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton value="添加" icon="ui-icon-plus" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.addInitAction}" process="@this" update=":tabView:editTabView:exmsdataListForm:employmentHisDialog"
												 oncomplete="PF('EmploymentHisDialog').show();removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:prfraysrtPanel');removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:defendStepPanel');">
									<f:setPropertyActionListener value="2" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hisType}"/>
								</p:commandButton>
							</h:panelGrid>
							<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
								<h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
								<h:outputLabel value="起止时间月份不明确填6月，日期不明确填15日。" style="color:blue;"></h:outputLabel>
							</p:outputPanel>
						</p:outputPanel>
						<p:dataTable id="employmentHisList" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisList}" var="itm" emptyMessage="暂无职业史信息" style="margin-top: 5px;">
							<p:column headerText="起止时间" style="width:180px;text-align: center;">
								<h:outputLabel value="#{itm.stastpDate}"/>
							</p:column>
							<p:column headerText="工作单位" style="padding-left: 3px; width:180px;">
								<h:outputLabel value="#{itm.unitName}"/>
							</p:column>
							<p:column headerText="部门车间" style="width:100px;text-align: center;">
								<h:outputLabel value="#{itm.department}"/>
							</p:column>
							<p:column headerText="工种" style="width:100px;text-align: center;">
								<h:outputLabel value="#{itm.workType}"/>
							</p:column>
							<p:column headerText="危害因素" style="padding-left: 3px; width:180px;">
								<h:outputLabel value="#{itm.prfraysrt}"/>
							</p:column>
							<p:column headerText="防护措施" style="width:120px;text-align: center;">
								<h:outputLabel value="#{itm.defendStep}"/>
							</p:column>
							<p:column headerText="操作" >
								<p:commandLink value="修改" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.modInitAction}" update=":tabView:editTabView:exmsdataListForm:employmentHisDialog"
											   oncomplete="PF('EmploymentHisDialog').show();removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:prfraysrtPanel');removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:defendStepPanel');" process="@this" rendered="#{tdTjBhkCltListCommBean.view eq null}">
									<f:setPropertyActionListener value="#{itm.rid}" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.rid}"/>
									<f:setPropertyActionListener value="2" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hisType}"/>
									<p:resetInput target=":tabView:editTabView:exmsdataListForm:employmentHisDialog" />
								</p:commandLink>
								<p:spacer width="5" />
								<p:commandLink value="删除" process="@this" update="employmentHisList" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.deleteAction}" rendered="#{tdTjBhkCltListCommBean.view eq null}">
									<f:setPropertyActionListener value="#{itm}" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt}"/>
									<f:setPropertyActionListener value="2" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hisType}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
							</p:column>
						</p:dataTable>
					</p:fieldset>
				</td>
			</tr>
			<tr>
				<!--吸烟史-->
				<td style="vertical-align: top">
					<p:fieldset legend="吸烟史" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:panelGrid style="width:100%;" id="smkdataGrid">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width: 200px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="目前吸烟情况："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 250px;">
									<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view eq null?'width: 200px;':'width: 200px;pointer-events:none;'}"
													 value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.smkSelRid}" id="smkSelRid">
										<f:selectItem itemLabel="--请选择--" itemValue=""/>
										<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.smklList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
										<p:ajax event="change" listener="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.changeSmk}"
												process="@this,smkyerqty,smkmthqty,smkdayble" update="smkyerqty,smkmthqty,smkdayble"/>
									</p:selectOneMenu>
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:200px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="吸烟史（年）："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 200px;">
									<p:inputText id="smkyerqty"
												 style="width:150px;#{tdTjBhkCltListCommBean.view eq null and tdTjBhkCltListCommBean.tdTjExmsdataCltBean.isSmkEdit?'':'pointer-events:none;'}"
												 value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.smkyerqty}"
												 onkeyup="SYSTEM.verifyNum3(this,2,0,true)" onblur="SYSTEM.verifyNum3(this,2,0,true)"
												 onkeydown="SYSTEM.verifyNum3(this,2,0,true)"
												 maxlength="2" />

								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:200px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="吸烟史（月）："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width:200px;">
									<p:inputText id="smkmthqty"
												 style="width:150px;#{tdTjBhkCltListCommBean.view eq null and tdTjBhkCltListCommBean.tdTjExmsdataCltBean.isSmkEdit?'':'pointer-events:none;'}"
												 value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.smkmthqty}"
												 onkeyup="SYSTEM.verifyNum3(this,2,0,true)" onblur="SYSTEM.verifyNum3(this,2,0,true)"
												 onkeydown="SYSTEM.verifyNum3(this,2,0,true)"
												 maxlength="2"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:200px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="平均每天吸烟量（支）："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:inputText id="smkdayble"
												 style="width:150px;#{tdTjBhkCltListCommBean.view eq null and tdTjBhkCltListCommBean.tdTjExmsdataCltBean.isSmkEdit?'':'pointer-events:none;'}"
												 value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.smkdayble}"
												 onkeyup="SYSTEM.verifyNum3(this,2,0,true)" onblur="SYSTEM.verifyNum3(this,2,0,true)"
												 onkeydown="SYSTEM.verifyNum3(this,2,0,true)"
												 maxlength="2"  />
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
				</td>
			</tr>
			<!-- 放射史 -->
			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="放射史" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{tdTjBhkCltListCommBean.ifShowRad == 1}">
						<p:outputPanel styleClass="zwx_toobar_42" rendered="#{tdTjBhkCltListCommBean.view eq null}">
							<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
								<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton value="添加" icon="ui-icon-plus" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.addInitAction}" process="@this" update="radiationHisDialog"
												 oncomplete="PF('RadiationHisDialog').show();removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:prfraysrt2Panel');removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:fsszlPanel');">
									<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hisType}"/>
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel>
						<p:dataTable id="radiationHisList" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisList}" var="itm" emptyMessage="暂无放射史信息" style="margin-top: 5px;">
							<p:column headerText="起止时间" style="width:100px;text-align: center;">
								<h:outputLabel value="#{itm.stastpDate}"/>
							</p:column>
							<p:column headerText="工作单位" style="padding-left: 3px; width:180px;">
								<h:outputLabel value="#{itm.unitName}"/>
							</p:column>
							<p:column headerText="每日工作时数或工作量" style="width:160px;text-align: center;">
								<h:outputLabel value="#{itm.prfwrklod}"/>
							</p:column>
							<p:column headerText="职业史累积受照剂量" style="width:150px;text-align: center;">
								<h:outputLabel value="#{itm.prfshnvlu}"/>
							</p:column>
							<p:column headerText="职业史过量照射史" style="width:150px;text-align: center;">
								<h:outputLabel value="#{itm.prfexcshn}"/>
							</p:column>
							<p:column headerText="职业照射种类" style="width:180px;text-align: center;">
								<h:outputLabel value="#{itm.prfraysrt2}"/>
							</p:column>
							<p:column headerText="放射线种类" style="width:180px;text-align: center;">
								<h:outputLabel value="#{itm.fsszl}"/>
							</p:column>
							<p:column headerText="操作" >
								<p:commandLink value="修改" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.modInitAction}" update=":tabView:editTabView:exmsdataListForm:radiationHisDialog"
											   oncomplete="PF('RadiationHisDialog').show();removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:prfraysrt2Panel');removeExtraPanel('#tabView\\:editTabView\\:exmsdataListForm\\:fsszlPanel');" process="@this" rendered="#{tdTjBhkCltListCommBean.view eq null}">
									<f:setPropertyActionListener value="#{itm.rid}" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.rid}"/>
									<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hisType}"/>
									<p:resetInput target=":tabView:editTabView:exmsdataListForm:radiationHisDialog" />
								</p:commandLink>
								<p:spacer width="5" />
								<p:commandLink value="删除" process="@this" update="radiationHisList" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.deleteAction}" rendered="#{tdTjBhkCltListCommBean.view eq null}">
									<f:setPropertyActionListener value="#{itm}" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt}"/>
									<f:setPropertyActionListener value="1" target="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hisType}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
							</p:column>
						</p:dataTable>
					</p:fieldset>
				</td>
			</tr>

			<!-- 既往史（包括职业病史） -->
			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="既往史" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:outputPanel styleClass="zwx_toobar_42" style="display: flex" rendered="#{tdTjBhkCltListCommBean.view eq null}">
							<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
								<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton value="添加" icon="ui-icon-plus" action="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.addInitAction}" process="@this" update="anamnesisHisDialog" oncomplete="PF('AnamnesisHisDialog').show();"/>
							</h:panelGrid>
							<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;" rendered="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.ifFstChkNeedAnamnesis}">
								<h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
								<h:outputLabel value="若无既往史可填写【无】。" style="color:blue;"></h:outputLabel>
							</p:outputPanel>
						</p:outputPanel>
						<p:dataTable id="anamnesisHisList" value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.anamnesisHisList}" var="itm" emptyMessage="暂无既往史信息" style="margin-top: 5px;">
							<p:column headerText="疾病名称" style="text-align: center; width:180px;">
								<h:outputLabel value="#{itm.hstnam}"/>
							</p:column>
							<p:column headerText="诊断日期" style="width:80px;text-align: center;">
								<h:outputText value="#{itm.hstdat}">
									<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
								</h:outputText>
							</p:column>
							<p:column headerText="诊断单位" style="padding-left: 3px; width:180px;">
								<h:outputLabel value="#{itm.hstunt}"/>
							</p:column>
							<p:column headerText="治疗经过" style="padding-left: 3px; width:180px;">
								<h:outputLabel value="#{itm.hstcruprc}"/>
							</p:column>
							<p:column headerText="转归" style="width:180px;text-align: center;">
								<h:outputLabel value="#{itm.hstlps}"/>
							</p:column>
							<p:column headerText="操作">
								<p:commandLink value="修改" action="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.modInitAction}" update=":tabView:editTabView:exmsdataListForm:anamnesisHisDialog" oncomplete="PF('AnamnesisHisDialog').show()" process="@this" rendered="#{tdTjBhkCltListCommBean.view eq null}">
									<f:setPropertyActionListener value="#{itm.rid}" target="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.rid}"/>
								</p:commandLink>
								<p:spacer width="5" />
								<p:commandLink value="删除" process="@this" update="anamnesisHisList" action="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.deleteAction}" rendered="#{tdTjBhkCltListCommBean.view eq null}">
									<f:setPropertyActionListener value="#{itm}" target="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.tdTjAnamnesisClt}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
							</p:column>
						</p:dataTable>
					</p:fieldset>
				</td>
			</tr>
			<tr>
				<!-- 月经史、生育史、饮酒史 -->
				<td style="vertical-align: top">
					<p:fieldset legend="月经史、生育史、饮酒史" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:panelGrid style="width:100%;" id="exmsdataGrid">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="个人史："/>
								</p:column>
								<p:column style="text-align:left;padding-left:2px;" colspan="5">
									<h:panelGrid id="grsGrid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
										<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" id="grs" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.grs}" style="width:740px;" maxlength="127"/>
										<p:commandLink disabled="#{tdTjBhkCltListCommBean.view eq null?false:true}" styleClass="ui-icon ui-icon-search" id="onGrsSelect" process="@this" style="position: relative;left: -30px;top:0px;" update="grsPanel" action="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.openGrsPanel}" />
									</h:panelGrid>
									<p:remoteCommand name="init1" action="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.hideGrsAction}" process="@this,grsGrid"/>
									<p:overlayPanel rendered="#{tdTjBhkCltListCommBean.view eq null}" id="grsPanel" for="onGrsSelect" hideEvent="mousedown" onHide="init1()" dynamic="false"
													style="width:280px;" widgetVar="GrsPanel" showCloseIcon="true" >
										<p:tree id="grsTree" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.grsSortTree}" var="node" selectionMode="checkbox" style="width: 250px;height: 300px;overflow-y: auto;">
											<p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.onGrsNodeSelect}"/>
											<p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.onGrsNodeNoSelect}"/>
											<p:treeNode>
												<h:outputText value="#{node.codeName}" />
											</p:treeNode>
										</p:tree>
									</p:overlayPanel>
								</p:column>
							</p:row>
							<p:row>
								<p:column style="text-align:right;padding-right:3px;">
									<p:outputLabel value="饮酒情况："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view eq null?'width: 200px;':'width: 200px;pointer-events:none;'}" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.winsta}">
										<f:selectItem itemLabel="--请选择--" itemValue=""/>
										<f:selectItem itemLabel="不饮酒" itemValue="0"/>
										<f:selectItem itemLabel="偶尔饮" itemValue="1"/>
										<f:selectItem itemLabel="经常饮" itemValue="2"/>
										<p:ajax event="change" process="@this" update="windaymlx, winyerqty"/>
									</p:selectOneMenu>
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="酒量(ml/天)："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText id="windaymlx" readonly="#{tdTjBhkCltListCommBean.view eq null and (tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.winsta eq 1 or tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.winsta eq 2)?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.windaymlx}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="25"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="酒龄(年)："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:inputText id="winyerqty" readonly="#{tdTjBhkCltListCommBean.view eq null and (tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.winsta eq 1 or tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.winsta eq 2)?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.winyerqty}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="25"  />
								</p:column>
							</p:row>
						</p:panelGrid>

						<p:panelGrid style="width:100%;margin-top:-1px;" id="girlPanel" rendered="#{tdTjBhkCltListCommBean.tdTjBhkClt.sex eq '女'}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width: 100px;">
									<p:outputLabel value="初潮年龄(岁)："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.mnrage}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="经期(天)："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.mns}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="周期(天)："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.cyc}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
							</p:row>

							<p:row>
								<p:column style="text-align:right;padding-right:3px;">
									<p:outputLabel value="停经年龄(岁)："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.mnlage}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="是否经期："/>
								</p:column>
								<p:column style="text-align:left;padding-left:10px;" colspan="3">
									<p:selectBooleanCheckbox style="#{tdTjBhkCltListCommBean.view eq null?'width: 120px;':'width: 120px;pointer-events:none;'}" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.isxmns}"/>
								</p:column>
							</p:row>

							<p:row>
								<p:column style="text-align:right;padding-right:3px;">
									<p:outputLabel value="子女人数："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.chldqty}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="流产胎次："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.abrqty}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width: 100px;">
									<p:outputLabel value="早产胎次："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.slnkqty}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
							</p:row>

							<p:row>
								<p:column style="text-align:right;padding-right:3px;">
									<p:outputLabel value="死产胎次："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 260px;">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.stlqty}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="异常胎次："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;" colspan="3">
									<p:inputText readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" style="width:60px" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.trsqty}"
												 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" onkeydown="SYSTEM.clearNoNum(this)" maxlength="3"  />
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
				</td>
			</tr>

			<!-- 家族史 -->
			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="家族史" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:panelGrid style="width:100%;" id="jzsDataGrid">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="家族史："/>
								</p:column>
								<p:column style="text-align:left;padding-left:2px;">
									<h:panelGrid id="jzsGrid" columns="10" style="border-color: #ffffff;margin: 0px;padding: 0px;">
										<p:inputText id="jzs" readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.jzs}" style="width:740px;" maxlength="127"/>
										<p:commandLink disabled="#{tdTjBhkCltListCommBean.view eq null?false:true}" styleClass="ui-icon ui-icon-search" id="onJzsSelect" process="@this" style="position: relative;left: -30px;top:0px;" update="jzsPanel" action="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.openJzsPanel}"/>
										<p:outputPanel style="position: relative;left: -25px;top: 0px;" rendered="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.ifFstChkNeedJzs}">
											<h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
											<h:outputLabel value="若无家族史可填写【无】。" style="color:blue;"></h:outputLabel>
										</p:outputPanel>
									</h:panelGrid>
									<p:remoteCommand name="init2" action="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.hideJzsAction}" process="@this,jzsGrid"/>
									<p:overlayPanel id="jzsPanel" rendered="#{tdTjBhkCltListCommBean.view eq null}" for="onJzsSelect" hideEvent="mousedown" onHide="init2()" dynamic="false"
													style="width:280px;" widgetVar="JzsPanel" showCloseIcon="true" >
										<p:tree id="jzsTree" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.jzsSortTree}" var="node" selectionMode="checkbox" style="width: 250px;height: 300px;overflow-y: auto;">
											<p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.onJzsNodeSelect}"/>
											<p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.onJzsNodeNoSelect}"/>
											<p:treeNode>
												<h:outputText value="#{node.codeName}" />
											</p:treeNode>
										</p:tree>
									</p:overlayPanel>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
				</td>
			</tr>

			<!-- 其他 -->
			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="其他" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:panelGrid style="width:100%;" id="otherDataGrid">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<p:outputLabel value="其他："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:inputTextarea readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" rows="5" cols="88" value="#{tdTjBhkCltListCommBean.tdTjExmsdataCltBean.tdTjExmsdataClt.oth}" autoResize="true" maxlength="127" />
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
				</td>
			</tr>

			<!-- 症状 -->
			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="症状" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:panelGrid style="width:100%;" id="symptomGrid">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="症状："/>
								</p:column>

								<p:column style="text-align:left;padding-left:2px;">
									<!--<p:selectManyCheckbox style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}" value="#{tdTjBhkCltListCommBean.tdTjSymptomCltBean.selectSymIds}" columns="5" layout="grid">
										<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjSymptomCltBean.symCodeList}" var="symCode" itemLabel="#{symCode.codeName}" itemValue="#{symCode.rid}"/>
										<p:ajax event="click" listener="#{tdTjBhkCltListCommBean.selectCheckAction}" process="@this,symptomGrid"/>
									</p:selectManyCheckbox>-->

                                    <p:panelGrid styleClass="mySymptomPanelGrid" style="width: 100%;">
                                        <p:row>
                                            <p:column>
                                                <p:outputPanel styleClass="div-layout checkbox-padding" id="symCodeRowCommPOList">
                                                    <table>
                                                        <c:forEach items="#{tdTjBhkCltListCommBean.tdTjSymptomCltBean.symCodeRowCommPOList}" var="symCodeRowCommPO">
                                                            <tr>
                                                                <c:forEach items="#{symCodeRowCommPO.symCodeList}" var="symCode">
                                                                    <td class="noBorder" style="position: relative;top: 1px;">
                                                                        <p:selectBooleanCheckbox value="#{symCode.ifSelected}"
                                                                                                 disabled="#{symCode.selectAble eq false}"
                                                                                                 style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}">
                                                                            <p:ajax event="change" listener="#{tdTjBhkCltListCommBean.selectSymCodeAction(symCode)}" process="@this,symptomGrid" update="symptomGrid"/>
                                                                        </p:selectBooleanCheckbox>
                                                                    </td>

                                                                    <td class="noBorder" style="position: relative;top: 1px;padding-right: 30px;">
                                                                        <h:outputText value="#{symCode.codeName}" />
                                                                    </td>
                                                                </c:forEach>
                                                            </tr>
                                                        </c:forEach>
                                                    </table>
                                                </p:outputPanel>
                                            </p:column>
                                        </p:row>
                                    </p:panelGrid>
								</p:column>
							</p:row>

							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:80px;" rendered="#{tdTjBhkCltListCommBean.tdTjSymptomCltBean.showOtherInfo}">
									<h:outputText value="*" style="color:red;" />
									<h:outputText value="其他："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;" rendered="#{tdTjBhkCltListCommBean.tdTjSymptomCltBean.showOtherInfo}">
									<p:inputTextarea readonly="#{tdTjBhkCltListCommBean.view eq null?false:true}" rows="3" cols="120" value="#{tdTjBhkCltListCommBean.tdTjSymptomCltBean.othsym}" maxlength="100"
										id="othsym"/>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
				</td>
			</tr>

			<tr>
				<td style="vertical-align: top">
					<p:fieldset legend="问诊医生/日期" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
						<p:panelGrid style="width:100%;" id="chkGrid">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="问诊医生："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 330px;">
									<p:selectOneMenu style="#{tdTjBhkCltListCommBean.view eq null?'width: 200px;':'width: 200px;pointer-events:none;'}" value="#{tdTjBhkCltListCommBean.editChkdocId}"
													 filter="true" filterMatchMode="contains" id="editChkdocId">
										<f:selectItem itemLabel="--请选择--" itemValue=""/>
										<f:selectItems value="#{tdTjBhkCltListCommBean.editChkdocMap}"/>
									</p:selectOneMenu>
								</p:column>
								<p:column style="text-align:right;padding-right:3px;width:100px;">
									<h:outputText value="*" style="color:red;" />
									<p:outputLabel value="问诊日期："/>
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:calendar style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
												showOtherMonths="true" size="22" navigator="true"
												yearRange="c-70:c" converterMessage="问诊日期，格式输入不正确！"
												showButtonPanel="true" maxdate="new Date()"
												value="#{tdTjBhkCltListCommBean.tdTjBhkClt.wzChkdat}" 
												id="wzChkdat"/>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
				</td>
			</tr>
		</table>
		</div>

		<!-- 添加弹出框：职业史 -->
		<p:dialog id="employmentHisDialog" header="职业史" widgetVar="EmploymentHisDialog" resizable="false" modal="true" width="550" style="height: 670px;" focus="cancelBtn">
			<p:panelGrid id="employmentHisGrid" style="width:100%;">
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:120px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="起止时间："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="stastpDate" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.stastpDate}" maxlength="50" style="width: 200px;" rendered="#{!tdTjBhkCltListCommBean.ifZysTimeRange}"/>
						<p:outputPanel id="zysTimeRangePanel" style="display:flex" rendered="#{tdTjBhkCltListCommBean.ifZysTimeRange}">
						<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true" disabled="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.disableDate}"
									showOtherMonths="true" id="bDate" size="11" navigator="true"
									yearRange="c-10:c+10" converterMessage="开始日期，格式输入不正确！"
									showButtonPanel="true" styleClass="myCalendar1"
									mindate="#{tdTjBhkCltListCommBean.employmentHisCltStartDateMin}"
									maxdate="#{tdTjBhkCltListCommBean.employmentHisCltStartDateMax}"
									value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.startDate}">
							<p:ajax event="dateSelect" process="@this,bDate,eDate" update="bDate,eDate"/>
						</p:calendar>
						<p:outputLabel value="~" style="padding: 5px 4px 0px 4px;"/>
						<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true" disabled="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.disableDate}"
									showOtherMonths="true" id="eDate" size="11" navigator="true"
									yearRange="c-10:c+10" converterMessage="结束日期，格式输入不正确！"
									showButtonPanel="true" styleClass="myCalendar1"
									mindate="#{tdTjBhkCltListCommBean.employmentHisCltStopDateMin}"
									value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.stopDate}">
							<p:ajax event="dateSelect" process="@this,bDate,eDate" update="bDate,eDate"/>
						</p:calendar>
						<p:selectManyCheckbox styleClass="stastpDateMarkClass" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.stastpDateMark}" onchange="changeMark()">
							<f:selectItem itemLabel="无" itemValue="1"/>
						</p:selectManyCheckbox>
						<p:remoteCommand name="changeMark" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.changeStastpDateMark}" process="@this,zysTimeRangePanel,unitName" update="zysTimeRangePanel,unitName"/>
						</p:outputPanel>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="工作单位："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="unitName" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.unitName}" maxlength="50" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="部门车间："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="department" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.department}" maxlength="50" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="工种："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="workType" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.workType}" maxlength="25" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="危害因素："/>
					</p:column>
					<p:column style="text-align:left;padding-left:2px;">
						<h:panelGrid id="prfraysrtGrid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="prfraysrt" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.prfraysrt}" style="width:330px;" maxlength="500"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="onPrfraysrtSelect" process="@this" style="position: relative;left: -30px;top:0px;" update="prfraysrtPanel" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.openPrfraysrtPanel}" />
						</h:panelGrid>
						<p:remoteCommand name="init4" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hidePrfraysrtAction}" process="@this,prfraysrt" update="prfraysrt"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="防护措施："/>
					</p:column>
					<p:column style="text-align:left;padding-left:2px;">
						<h:panelGrid id="defendStepGrid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="defendStep" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.employmentHisClt.defendStep}" style="width:330px;" maxlength="25"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="onDefendStepSelect" process="@this" style="position: relative;left: -30px;top:0px;" update="defendStepPanel" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.openDefendStepPanel}"/>
						</h:panelGrid>
						<p:remoteCommand name="init3" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hideDefendStepAction}" process="@this,defendStepGrid"/>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-disk" id="employmentSaveBtn"
										 process="@this,stastpDate,unitName,department,workType,defendStep,prfraysrt"
										 action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.saveEmploymentHistClt}" update="employmentHisList">
						</p:commandButton>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" process="@this" id="cancelBtn"
										 oncomplete="PF('EmploymentHisDialog').hide();"   >
						</p:commandButton>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<p:overlayPanel id="prfraysrtPanel" for="onPrfraysrtSelect" hideEvent="mousedown" onHide="init4()" dynamic="false"
						style="width:280px;" widgetVar="PrfraysrtPanel" showCloseIcon="true" >
			<p:tree id="prfraysrtTree"  value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.prfraysrtSortTree}" var="node" selectionMode="checkbox" style="width: 250px;height: 300px;overflow-y: auto;">
				<p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onPrfraysrtNodeSelect}"/>
				<p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onPrfraysrtNodeNoSelect}"/>
				<p:treeNode >
					<h:outputText value="#{node.codeName}" />
				</p:treeNode>
			</p:tree>
		</p:overlayPanel>
		<p:overlayPanel id="defendStepPanel" for="onDefendStepSelect" hideEvent="mousedown" onHide="init3()" dynamic="false"
						style="width:280px;" widgetVar="DefendStepPanel" showCloseIcon="true" >
			<p:tree id="defendStepTree" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.defendStepSortTree}" var="node" selectionMode="checkbox" style="width: 250px;height: 300px;overflow-y: auto;">
				<p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onDefendStepNodeSelect}"/>
				<p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onDefendStepNodeNoSelect}"/>
				<p:treeNode>
					<h:outputText value="#{node.codeName}" />
				</p:treeNode>
			</p:tree>
		</p:overlayPanel>

		<!-- 添加弹出框：放射史 -->
		<p:dialog id="radiationHisDialog" header="放射史" widgetVar="RadiationHisDialog" resizable="false" modal="true" width="550" style="height: 670px;">
			<p:panelGrid id="radiationHisGrid" style="width:100%;">
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:100px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="起止时间："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="stastpDate2" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.stastpDate}" maxlength="50" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:100px;height: 39px;">
						<h:outputText value="*" style="color:red;" />
						<p:outputLabel value="工作单位："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="unitName2" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.unitName}" maxlength="25" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:160px;height: 39px;">
						<p:outputLabel value="每日工作时数或工作量："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneMenu id="prfwrklod" editable="true" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.prfwrklod}" maxlength="50" style="width: 210px;">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.prfwrklodList}"/>
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:160px;height: 39px;">
						<p:outputLabel value="职业史累积受照剂量："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneMenu id="prfshnvlu" editable="true" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.prfshnvlu}" maxlength="50" style="width: 210px;">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.prfshnvluList}"/>
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:160px;height: 39px;">
						<p:outputLabel value="职业史过量照射史："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="prfexcshn" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.prfexcshn}" maxlength="50" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:160px;height: 39px;">
						<p:outputLabel value="职业照射种类："/>
					</p:column>
					<!-- 多选可修改 -->
					<p:column style="text-align:left;padding-left:2px;">
						<h:panelGrid id="prfraysrt2Grid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="prfraysrt2" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.prfraysrt2}" style="width:270px;" maxlength="250"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="onPrfraysrt2Select" process="@this" style="position: relative;left: -30px;top:0px;" update="prfraysrt2Panel" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.openPrfraysrt2Panel}" />
						</h:panelGrid>
						<p:remoteCommand name="init5" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hidePrfraysrt2Action}" process="@this,prfraysrt2Grid"/>
					</p:column>
					<!-- 单选可修改 -->
					<!--<p:column style="text-align:left;padding-left:2px;">
						<h:panelGrid id="prfraysrt2Grid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="prfraysrt2" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.prfraysrt2}" style="width:203px;" onclick="document.getElementById('tabView:editTabView:exmsdataListForm:onPrfraysrt2Select').click();" maxlength="127"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="onPrfraysrt2Select" process="@this" style="position: relative;left: -30px;top:0px;" oncomplete="PF('Prfraysrt2Panel').show()" />
						</h:panelGrid>
						<p:overlayPanel id="prfraysrt2Panel" for="prfraysrt2" hideEvent="mousedown" dynamic="false"
										style="width:280px;" widgetVar="Prfraysrt2Panel" showCloseIcon="true" >
							<p:tree id="prfraysrt2Tree" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.prfraysrt2SortTree}" var="node" selectionMode="single" style="width: 250px;height: 300px;overflow-y: auto;">
								<p:ajax event="select" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onPrfraysrt2SortTreeNodeSelect}" process="@this" />
								<p:treeNode>
									<h:outputText value="#{node.codeName}" />
								</p:treeNode>
							</p:tree>
						</p:overlayPanel>
					</p:column>-->
				</p:row>

                <!-- 放射线种类-单选 -->
				<!--<p:row>
					<p:column style="text-align:right;padding-right:8px;width:160px;">
						<p:outputLabel value="放射线种类："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneMenu id="fsszl" editable="true" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.fsszl}" maxlength="100" style="width: 210px;">
							<f:selectItem itemLabel="&#45;&#45;请选择&#45;&#45;" itemValue=""/>
							<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.fsszlList}"/>
						</p:selectOneMenu>
					</p:column>
				</p:row>-->

                <!-- 放射线种类-多选 -->
                <p:row>
                    <p:column style="text-align:right;padding-right:8px;width:160px;height: 39px;">
                        <p:outputLabel value="放射线种类："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:2px;">
                        <h:panelGrid id="fsszlGrid" columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                            <p:inputText id="fsszl" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.radiationHisClt.fsszl}" style="width:270px;" maxlength="250"/>
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="fsszlSelect" process="@this" style="position: relative;left: -30px;top:0px;" update="fsszlPanel" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.openFsszlPanel}" />
                        </h:panelGrid>
                        <p:remoteCommand name="init6" action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.hideFszlAction}" process="@this,fsszlGrid"/>
                    </p:column>
                </p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-disk" id="radiationSaveBtn"
										 process="@this,stastpDate2,unitName2,prfwrklod,prfshnvlu,prfexcshn,prfraysrt2,fsszl"
										 action="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.savRadiationHistClt}" update="radiationHisList">
						</p:commandButton>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" process="@this"
										 oncomplete="PF('RadiationHisDialog').hide();"   >
						</p:commandButton>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

        <!-- 职业照射种类 -->
		<p:overlayPanel id="prfraysrt2Panel" for="onPrfraysrt2Select" hideEvent="mousedown" onHide="init5()" dynamic="false"
						style="width:280px;" widgetVar="Prfraysrt2Panel" showCloseIcon="true" >
			<p:tree id="prfraysrt2Tree" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.prfraysrt2SortTree}" var="node" selectionMode="checkbox" style="width: 250px;height: 300px;overflow-y: auto;">
				<p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onPrfraysrt2NodeSelect}"/>
				<p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onPrfraysrt2NodeNoSelect}"/>
				<p:treeNode>
					<h:outputText value="#{node.codeName}" />
				</p:treeNode>
			</p:tree>
		</p:overlayPanel>

        <!-- 放射线种类 -->
        <p:overlayPanel id="fsszlPanel" for="fsszlSelect" hideEvent="mousedown" onHide="init6()" dynamic="false"
                        style="width:280px;" widgetVar="FsszlPanel" showCloseIcon="true" >
            <p:tree id="fsszlTree" value="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.fsszlSortTree}" var="node" selectionMode="checkbox" style="width: 250px;height: 300px;overflow-y: auto;">
                <p:ajax event="select"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onFsszlNodeSelect}"/>
                <p:ajax event="unselect"  process="@this" listener="#{tdTjBhkCltListCommBean.tdTjEmhistoryCltListBean.onFsszlNodeNoSelect}"/>
                <p:treeNode>
                    <h:outputText value="#{node.codeName}" />
                </p:treeNode>
            </p:tree>
        </p:overlayPanel>

		<!-- 添加弹出框：既往史 -->
		<p:dialog id="anamnesisHisDialog" header="既往史" widgetVar="AnamnesisHisDialog" resizable="false" modal="true" width="500"  height="225">
			<p:panelGrid id="anamnesisHisGrid" style="width:100%;">
				<p:row>
					<p:column style="text-align:right;padding-right:8px;width:120px;">
						<h:outputText value="*" style="color:red;height: 39px;"/>
						<p:outputLabel value="疾病名称："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:selectOneMenu id="hstnam" editable="true" height="150" value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.tdTjAnamnesisClt.hstnam}" filter="true" filterMatchMode="contains" maxlength="50" style="width: 210px;">
							<f:selectItem itemLabel="" itemValue="" />
							<f:selectItems value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.hstnamList}"/>
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="诊断日期："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:calendar id="hstdat" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
									showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar3"
									yearRange="c-70:c+10" converterMessage="诊断日期，格式输入不正确！"
									showButtonPanel="true" maxdate="new Date()"
									value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.tdTjAnamnesisClt.hstDate}"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="诊断单位："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="hstunt" value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.tdTjAnamnesisClt.hstunt}" maxlength="50" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="治疗经过："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="hstcruprc" value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.tdTjAnamnesisClt.hstcruprc}" maxlength="127" style="width: 200px;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:8px;height: 39px;">
						<p:outputLabel value="转归："/>
					</p:column>
					<p:column style="text-align:left;padding-left:8px;">
						<p:inputText id="hstlps" value="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.tdTjAnamnesisClt.hstlps}" maxlength="25" style="width: 200px;"/>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-disk" id="anamnesisSaveBtn"
										 process="@this,hstnam,hstdat,hstunt,hstcruprc,hstlps"
										 action="#{tdTjBhkCltListCommBean.tdTjAnamnesisCltListBean.saveAction}" update="anamnesisHisList">
						</p:commandButton>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" process="@this"
										 oncomplete="PF('AnamnesisHisDialog').hide();"   >
						</p:commandButton>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</h:form>
</ui:composition>
