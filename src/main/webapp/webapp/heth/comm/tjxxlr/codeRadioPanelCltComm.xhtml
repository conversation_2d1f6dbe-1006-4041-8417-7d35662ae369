<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <table width="100%" >
        <tr>
            <td style="text-align: left;padding-left: 3px;border-color:transparent" >
                <h:panelGrid columns="5" id="searchPanel" style="border-color:transparent">
                    <p:outputLabel value="#{mgrbean.selCodeName}大类：" style="width: 180px;" styleClass="zwx_dialog_font" />
                    <p:selectOneMenu value="#{mgrbean.firstCodeNo}" id="firstCodeNo" style="width: 180px;">
                        <f:selectItem itemValue="" itemLabel="--全部--"/>
                        <f:selectItems value="#{mgrbean.firstList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                        <p:ajax event="change" listener="#{mgrbean.searchCodeAction}" process="@this,searchPanel" update="selectedIndusTable"/>
                    </p:selectOneMenu>
                    <p:outputLabel value="名称/拼音码：" style="width: 180px;" styleClass="zwx_dialog_font" />
                    <p:inputText id="pym" value="#{mgrbean.searchNamOrPy}" maxlength="20">
                        <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{mgrbean.searchCodeAction}"/>
                    </p:inputText>
                </h:panelGrid>
            </td>
        </tr>
    </table>
    <p:dataTable var="itm"   value="#{mgrbean.displayList}" id="selectedIndusTable"
                 paginator="true" rows="10" emptyMessage="没有数据！"
                 paginatorPosition="bottom">
        <p:column headerText="选择" style="width:50px;text-align:center">
            <p:commandLink value="选择" action="#{mgrbean.selectAction}" process="@this" rendered="#{itm.levelIndex != '0' and -1 != itm.createManid}">
                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.selectPro}"/>
            </p:commandLink>
        </p:column>
        <p:column headerText="名称" style="padding-left: 3px;">
            <h:outputText value="#{(itm.levelIndex == '4') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '3') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}" />
        </p:column>
    </p:dataTable>
</ui:composition>