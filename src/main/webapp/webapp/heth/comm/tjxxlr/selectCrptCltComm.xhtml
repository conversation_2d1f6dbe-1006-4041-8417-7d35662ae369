<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title><h:outputText value="企业选择" /></title>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<h:outputScript name="js/namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<style type="text/css">
			.ui-commandlink{
			    left: -42px !important;
			}
		</style>
	</h:head>
	<h:body>
		<h:form id="selectForm">
		<div style="float: left;font-size: 12px;width: 100%;">
			<!-- 查询条件 -->
			<table style="width: 100%">
				<tr>
					<td style="text-align: right;padding-right: 3px;width: 100px;"
						class="zwx_dialog_font"><h:outputText value="单位名称："/>
					</td>
					<td style="text-align: left;padding-left: 8px;vertical-align: middle;width: 120px;">
						<p:inputText size="25" value="#{selectCrptCltCommBean.searchCrptName}">
							<p:ajax event="keyup" update="crptTable" process="@this,@parent" listener="#{selectCrptCltCommBean.searchAction}" />
						</p:inputText>
					</td>
					<td style="text-align: right;padding-right: 3px;width: 100px;" class="zwx_dialog_font">
						<h:outputText value="社会信用代码："/>
					</td>
					<td style="text-align: left;padding-left: 8px;width: 120px;">
						<p:inputText value="#{selectCrptCltCommBean.searchCrptCode}" size="20">
							<p:ajax event="keyup" update="crptTable" process="@this" listener="#{selectCrptCltCommBean.searchAction}" />
						</p:inputText>
					</td>
					<td style="text-align: left;padding-left: 8px;width: 60px;">
						<p:commandButton process="@this" value="添加" action="#{selectCrptCltCommBean.addCrptAction}" resetValues=":selectForm:addCrptPanel" update=":selectForm:addCrptPanel"/>
					</td>
					<td style="text-align: left;padding-left: 6px;">
						<p:commandButton value="关闭" action="#{selectCrptCltCommBean.dialogClose}" process="@this"/>
					</td>
				</tr>
			</table>

			<!-- 表格列 -->
			<p:dataTable id="crptTable" paginatorPosition="bottom" value="#{selectCrptCltCommBean.crptList}" widgetVar="crptTable" var="crpt" paginator="true" rows="10" emptyMessage="没有数据！">
				<!-- 按钮 -->
				<p:column headerText="选择" style="width:60px;text-align:center;">
					<p:commandLink value="选择" process="@this" action="#{selectCrptCltCommBean.selectCrptAction}">
						<f:setPropertyActionListener target="#{selectCrptCltCommBean.selectCrpt}" value="#{crpt}"/>
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink process="@this" value="修改" action="#{selectCrptCltCommBean.modCrptAction}" resetValues=":selectForm:addCrptPanel" update=":selectForm:addCrptPanel" oncomplete="PF('addCrpt').show();">
						<f:setPropertyActionListener target="#{selectCrptCltCommBean.crptId}" value="#{crpt.rid}"/>
					</p:commandLink>
				</p:column>

				<p:column headerText="地区名称" width="80px;">
					<h:outputText value="#{crpt.tsZoneByZoneId.zoneName}"/>
				</p:column>
				<p:column headerText="单位名称">
					<h:outputText value="#{crpt.crptName}"/>
				</p:column>
				<p:column headerText="社会信用代码"  width="100px;">
					<h:outputText value="#{crpt.institutionCode}"/>
				</p:column>
				<p:column headerText="单位地址"  width="200px;">
					<h:outputText value="#{crpt.address}"/>
				</p:column>
				<p:column headerText="联系人"  width="80px;">
					<h:outputText value="#{crpt.linkman2}"/>
				</p:column>
				<p:column headerText="联系电话"  width="100px;">
					<h:outputText value="#{crpt.linkphone2}"/>
				</p:column>
			</p:dataTable>

			<!-- 添加弹出框 -->
			<p:dialog header="企业信息" widgetVar="addCrpt" width="650" height="330" modal="true" resizable="false" >
				<p:panelGrid style="width:100%;margin-bottom:5px;" id="addCrptPanel">
					<p:row>
						<p:column style="text-align: right;width: 30%;height: 30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="所属地区："/>
						</p:column>
						<p:column style="padding:0 0;" id="areaZone">
							<zwx:ZoneSingleComp zoneList="#{selectCrptCltCommBean.zoneList}" zoneId="#{selectCrptCltCommBean.areaZoneId}"
								zoneName="#{selectCrptCltCommBean.areaZoneName}" zoneType="#{selectCrptCltCommBean.areaZoneType}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="企业名称："/>
						</p:column>
						<p:column style="padding-left: 8px;">
							<p:inputText maxlength="50" value="#{selectCrptCltCommBean.editCrpt.crptName}" style="width:260px"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="社会信用代码："/>
						</p:column>
						<p:column style="padding-left: 8px;">
							<p:inputText maxlength="25" value="#{selectCrptCltCommBean.editCrpt.institutionCode}" style="width:260px"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="单位地址："/>
						</p:column>
						<p:column style="padding-left: 8px;">
							<p:inputText value="#{selectCrptCltCommBean.editCrpt.address}" maxlength="100" style="width:260px"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="行业类别："/>
						</p:column>
						<p:column style="padding-left: 4px;">
							<table>
								<tr>
									<td style="padding: 0;border-color: transparent;">
										<p:inputText id="indusTypeName" value="#{selectCrptCltCommBean.editCrpt.tsSimpleCodeByIndusTypeId.codeName==null?null:selectCrptCltCommBean.editCrpt.tsSimpleCodeByIndusTypeId.codeName}"
											 style="width: 260px;cursor: pointer;" onclick="document.getElementById('selectForm:selIndusTypeLink').click();" readonly="true"/>
									</td>
									<td style="border-color: transparent;">
										<p:commandLink styleClass="ui-icon ui-icon-search" id="selIndusTypeLink"
										   	action="#{selectCrptCltCommBean.selCodeTypeAction}" process="@this" oncomplete="PF('selIndusTypeDialog').show()"
										   	update=":selectForm:selectedIndusCodeTable,:selectForm:searchIndusPanel" style="position: relative;left: -35px !important;">
											<f:setPropertyActionListener target="#{selectCrptCltCommBean.selCodeName}" value="行业类别"/>
										</p:commandLink>
									</td>
									<!-- 清空 -->
									<td style="border-color: transparent;position: relative;left: -52px;">
										<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" process="@this,:selectForm:indusTypeName" action="#{selectCrptCltCommBean.delCodeName}">
											<f:setPropertyActionListener target="#{selectCrptCltCommBean.selCodeName}" value="行业类别"/>
										</p:commandLink>
									</td>
								</tr>
							</table>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="经济类型："/>
						</p:column>
						<p:column style="padding-left: 4px;">
							<table>
								<tr>
									<td style="padding: 0;border-color: transparent;">
										<p:inputText id="economyName" value="#{selectCrptCltCommBean.editCrpt.tsSimpleCodeByEconomyId.codeName==null?null:selectCrptCltCommBean.editCrpt.tsSimpleCodeByEconomyId.codeName}"
											style="width: 260px;cursor: pointer;" onclick="document.getElementById('selectForm:selEconomyLink').click();" readonly="true"/>
									</td>
									<td style="border-color: transparent;">
										<p:commandLink styleClass="ui-icon ui-icon-search" id="selEconomyLink" action="#{selectCrptCltCommBean.selCodeTypeAction}" process="@this"
											oncomplete="PF('selDialog').show()" update=":selectForm:selectedIndusTable,:selectForm:searchPanel" style="position: relative;left: -35px !important;">
											<f:setPropertyActionListener target="#{selectCrptCltCommBean.selCodeName}" value="经济类型"/>
										</p:commandLink>
									</td>
									<td style="border-color: transparent;position: relative;left: -52px;">
										<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" update=":selectForm:selectedIndusTable"
										   	process="@this,:selectForm:economyName" action="#{selectCrptCltCommBean.delCodeName}">
											   <f:setPropertyActionListener target="#{selectCrptCltCommBean.selCodeName}" value="经济类型"/>
										</p:commandLink>
									</td>
								</tr>
							</table>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="企业规模："></h:outputText>
						</p:column>
						<p:column style="padding-left: 8px;">
							<p:selectOneMenu value="#{selectCrptCltCommBean.editCrpt.tsSimpleCodeByCrptSizeId.rid}" style="width: 188px;margin-bottom: -5px;">
								<f:selectItem itemLabel="--请选择--" itemValue="" />
								<f:selectItems value="#{selectCrptCltCommBean.crptsizwList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"  />
							</p:selectOneMenu>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="联系人："/>
						</p:column>
						<p:column style="padding-left: 8px;">
							<p:inputText value="#{selectCrptCltCommBean.editCrpt.linkman2}" maxlength="25" style="width:179px" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="*" style="color:red;"/>
							<h:outputText value="联系电话："/>
						</p:column>
						<p:column style="padding-left: 8px;" >
							<p:inputText value="#{selectCrptCltCommBean.editCrpt.linkphone2}" maxlength="15" style="width:255px"
							placeholder="格式：0514-84832423/18885219632"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="法定代表人（负责人）："/>
						</p:column>
						<p:column style="padding-left: 8px;">
							 <p:inputText value="#{selectCrptCltCommBean.editCrpt.corporateDeputy}" maxlength="25" style="width:179px" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="法人联系电话："/>
						</p:column>
						<p:column style="padding-left: 8px;" >
							 <p:inputText value="#{selectCrptCltCommBean.editCrpt.phone}" maxlength="15" style="width:179px"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align: right;width: 30%;height:30px;">
							<h:outputText value="邮政编码："/>
						</p:column>
						<p:column style="padding-left: 8px;" >
							 <p:inputText value="#{selectCrptCltCommBean.editCrpt.postCode}" maxlength="6" style="width:179px"/>
						</p:column>
					</p:row>
                    <p:row rendered="#{selectCrptCltCommBean.ifTjlrCrpt ne null and selectCrptCltCommBean.ifTjlrCrpt == '1'}">
                        <p:column style="text-align: right;width: 30%;height:30px;">
                            <h:outputText value="*" style="color:red;"/>
                            <h:outputText value="职工人数："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
							<p:inputText value="#{selectCrptCltCommBean.editCrpt.workForce}"  style="width:179px"
										 onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
										 maxlength="9" />
                        </p:column>
                    </p:row>
                    <p:row rendered="#{selectCrptCltCommBean.ifTjlrCrpt ne null and selectCrptCltCommBean.ifTjlrCrpt == '1'}">
                        <p:column style="text-align: right;width: 40%;height:30px;">
                            <h:outputText value="*" style="color:red;"/>
                            <h:outputText value="接触职业病危害因素人数："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
							<p:inputText value="#{selectCrptCltCommBean.editCrpt.holdCardMan}"  style="width:179px"
										 onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
										 maxlength="9" />
                        </p:column>
                    </p:row>
				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="确定" process="@this,addCrptPanel" action="#{selectCrptCltCommBean.saveCrptAction}"/>
							<p:commandButton value="取消" type="button" process="@this" onclick="PF('addCrpt').hide();"/>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
		</p:dialog>
		</div>
		<p:dialog header="#{selectCrptCltCommBean.selCodeName}选择" widgetVar="selDialog" width="600" height="380" modal="true" resizable="false" >
			<ui:include src="/webapp/heth/comm/tjxxlr/codeRadioPanelCltComm.xhtml">
				<ui:param name="mgrbean" value="#{selectCrptCltCommBean}"/>
			</ui:include>
		</p:dialog>
		<p:dialog header="行业类别选择" widgetVar="selIndusTypeDialog" width="800" height="430" modal="true" resizable="false" >
			<ui:include src="/webapp/system/indusTypeCodePanel.xhtml">
				<ui:param name="mgrbean" value="#{selectCrptCltCommBean}"/>
				<ui:param name="indusbean" value="#{selectCrptCltCommBean.codePanelBean}"/>
			</ui:include>
		</p:dialog>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
	</h:body>
</f:view>
</html>
