<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

    <style>
        .myCalendar input {
            width: 160px;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[
        function disabledInput(ifView,id){
            if(ifView=="false"){
                return ;
            }

            var text;
            var $tabView ;
            if(id){
                $tabView = $("#"+id)
            }else{
                $tabView = $("#tabView\\:editForm\\:tabViewEdit\\:mainTabView");
            }
            $tabView.find("input,textarea").each(function(){
                if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
                    $(this).css("pointer-events","none");
                }else{
                    $(this).prop("disabled",true);
                }
                $(this).css("opacity","1");
            });
            //单选框label的for标签处理
            $tabView.find("label").each(function(){
                $(this).css("pointer-events","none");
            });
            $tabView.find("a").each(function(){
                text = $(this).text();
                if(!text){
                    text = $(this).attr("title");
                }
                if("选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
                    $(this).remove();
                }else if("查看"==text||"修改"==text){

                }else{
                    $(this).prop("disabled",true);
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","0.35");
                }

            });
            $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
                $(this).addClass("ui-state-disabled");
                $(this).css("opacity","1");
                $(this).css("pointer-events","none");
            });
            //下拉
            $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                $(this).addClass("ui-state-disabled");
                $(this).css("pointer-events","none");
                $(this).css("opacity","1");
            });
            //按钮
            $tabView.find("button").each(function(){
                text = $(this).text();
                if("扫描"==text||"预览"==text||"制作"==text||"上传"==text||"添加"==text||"设计"==text||"保存"==text){
                    $(this).remove();
                }else if("进入"==text||"查看"==text||"返回"==text||"< 返回"==text||"删除"==text){

                }else{
                    $(this).prop("disabled",true);
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","0.35");
                }
            });
        }
        //]]>
    </script>
    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/diagnosisMain.css" />

        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="超范围服务汇报"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="15" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="保存" icon="ui-icon-disk" style="float:right;" rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath==null}"
                                 action="#{mgrbean.saveRptAction}" process="@this,:tabView:viewForm">
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{mgrbean.modInitAction}"
                                 update=":tabView" process="@this"/>
            </h:panelGrid>
        </p:outputPanel>
        <div id="writ_yszyb">
            <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);height: 620px">
                <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first " id="writeSortPanel" >
                    <p:row>
                        <p:column colspan="2" ><span>本环节文书</span></p:column>
                    </p:row>
                    <p:row>
                        <p:column style="padding: 0;" colspan="2">
                            <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="width: 190px;">
                            <h:outputText value="《超范围服务汇报》"/>
                        </p:column>
                        <p:column>
                            <p:outputPanel>
                                <p:spacer width="5"/>
                                <p:commandButton value="设计" icon="ui-icon-pencil" action="#{mgrbean.designWritReport}" oncomplete="frpt_design();"
                                                 update=":tabView:viewForm" process="@this,:tabView:viewForm" resetValues="true" rendered="#{mgrbean.ifdesign}"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="预览" onclick="PF('shadeTip').show();" oncomplete="PF('shadeTip').hide();"
                                                 process="@this,:tabView:viewForm"
                                                 action="#{mgrbean.buildWritReport}"
                                                 rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath==null}"
                                                 update=":tabView:viewForm">
                                </p:commandButton>
                                <p:spacer width="5"/>
                                <p:commandButton value="上传"
                                                 process="@this,:tabView:viewForm" action="#{mgrbean.beforeUpload}"
                                                 rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath==null}" >
                                </p:commandButton>
                                <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath}')" rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath!=null}"
                                                 process="@this,:tabView:viewForm"/>
                                <p:spacer width="5" />
                                <p:commandButton value="删除"  action="#{mgrbean.delMadedwrit}" rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath!=null}"
                                                 update=":tabView:viewForm" process="@this,:tabView:viewForm">
                                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                </p:commandButton>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <p:panelGrid styleClass="writeSortInfo word-all" style="margin-top: 30px;">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;">
                            <h:outputText value="超范围服务汇报" style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="border-color:transparent">
                            <p:outputPanel styleClass="writeSortInfo" rendered="true" style="padding-bottom: 50px;;border-color:transparent">
                            <div style="margin-top: 10px;border-color:transparent ">
                                <p:inputText  style="resize: none;width: 200px;" maxlength="100" value="#{mgrbean.tdZwyjOtrDealWrit.noticeUnit}" placeholder="请输入卫生行政部门名称"/>
                                <span>：</span>
                                <div style="padding-bottom: 10px;text-indent:35px;padding-top: 5px">
                                    我单位在 “吉林省职业健康质量控制平台” 中发现
                                    <p:inputTextarea rows="5" autoResize="false" value="#{mgrbean.tdZwyjOtrDealWrit.writContent}"
                                                     style="resize: none;width: 100%;"
                                                     maxlength="2000"/>
                                    <br/>按照《职业病防治法》、《职业健康检查管理办法》、《职业病诊断与鉴定管理办法》相关要求，特向您报告反馈。
                                </div>
                            </div>
                                <div style="margin-top: 10px;margin-left: 10px">
                                    <h:outputText  value="#{mgrbean.tdZwyjOtrDealWrit.dealUnit}" style="line-height: 30px;font-size: 15px;margin-left: 20px"/>
                                </div>
                                <div style="float: right;margin-top: 30px;margin-right: 20px">
                                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                                showOtherMonths="true"  size="11" navigator="true"
                                                value="#{mgrbean.tdZwyjOtrDealWrit.writDate}"
                                                yearRange="c-10:c+10" converterMessage="发现日期，格式输入不正确！"
                                                showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"/>
                                </div>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </p:outputPanel>
        </div>
        <ui:insert name="editContent">
            <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog"
                      resizable="false" modal="true">
                <table>
                    <tr>
                        <td style="text-align: right;"><p:outputLabel
                                value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                        </td>
                    </tr>
                    <tr>
                        <td style="position: relative;top: -23px;"><p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}"
                                invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/" update="@this"
                        /></td>
                    </tr>
                </table>
            </p:dialog>
        </ui:insert>
        <ui:include src="/WEB-INF/templates/system/frpt.xhtml">
            <ui:param name="printBackingBean" value="#{mgrbean}" />
        </ui:include>
        <ui:include src="/WEB-INF/templates/system/frpt2.xhtml">
            <ui:param name="updateId" value=":tabView:viewForm" />
            <ui:param name="printBackingBean" value="#{mgrbean}" />
        </ui:include>
    </h:form>
</ui:composition>