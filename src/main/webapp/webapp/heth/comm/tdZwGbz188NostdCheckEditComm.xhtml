<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/webapp/heth/comm/tbTjBhkInfo.xhtml">

    <ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
    <ui:param name="birthIfShow" value="false"></ui:param>
    <ui:param name="pageParam" value="1"/>

    <!-- 标题 -->
    <ui:define name="titleContent">
        <p:panelGrid style="width:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="GBZ188不规范数据-#{mgrbean.zoneType==-1?'详情':mgrbean.zoneType==2?'终审':mgrbean.zoneType==3?'复审':'初审'}" />
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="buttons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:outputPanel rendered="#{mgrbean.ifCheck}">
                    <p:commandButton value="审核通过" icon="ui-icon-check"
                                     action="#{mgrbean.saveAction}"
                                     process="@this,:tabView:viewForm" oncomplete="datatableOffClick()"
                                     update=":tabView">
                        <p:confirm header="消息确认框" message="确定要审核通过吗？" icon="ui-icon-alert"/>
                    </p:commandButton>
                    <p:spacer width="5"/>
                    <p:commandButton value="退回" icon="ui-icon-cancel"
                                     process="@this" oncomplete="PF('ReasonDialog').show();"
                                     action="#{mgrbean.initDialog}"
                                     update="reasonDialog">
                        <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="false"/>
                    </p:commandButton>

                </p:outputPanel>
                <p:commandButton value="#{(mgrbean.newFlow.state==2 or (mgrbean.ifCityDirect ==1 and mgrbean.newFlow.state==4)) ?'初审':((mgrbean.ifCityDirect ==0 and mgrbean.newFlow.state==4)?'复审':'终审')}退回原因"
                                 icon="icon-alert" action="#{mgrbean.initDialog}" style="color:red;"
                                 process="@this" oncomplete="PF('ReasonDialog').show();"
                                 update="reasonDialog"
                                 rendered="#{mgrbean.newFlow.state==2 or mgrbean.newFlow.state==6 or mgrbean.newFlow.state==4}">
                    <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="true"/>
                </p:commandButton>
                <p:spacer width="5"  rendered="#{mgrbean.newFlow.state==2 or mgrbean.newFlow.state==6 or mgrbean.newFlow.state==4}"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
                                 action="#{mgrbean.backAction}" process="@this" oncomplete="datatableOffClick()"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <ui:define name="editContent">
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="不规范原因"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="不规范原因：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea readonly="true"
                                     rows="5" autoResize="false"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.tjBhkInfoBean.tdTjBhkShow.lackMsg}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <p:outputLabel value="不规范原因说明：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" id="nostdDesc"
                                     style="resize: none;width: 594px;"
                                     maxlength="100" readonly="#{mgrbean.newFlow.state!=0 and mgrbean.newFlow.state!=2}"
                                     value="#{mgrbean.gbz188Nostd.nostdDesc}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{(mgrbean.newFlow.state>=3 and mgrbean.ifCityDirect ==0) or (mgrbean.newFlow.state>=5 and mgrbean.ifCityDirect ==1) or (mgrbean.zoneType == 4 and mgrbean.ifCheck) or (mgrbean.zoneType == 3 and mgrbean.ifCityDirect == 1 and mgrbean.ifCheck) }">
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <p:outputLabel style="color:red;" value="*" rendered="#{(mgrbean.zoneType == 4 and mgrbean.ifCheck) or (mgrbean.zoneType == 3 and mgrbean.ifCityDirect == 1 and mgrbean.ifCheck)}" />
                    <p:outputLabel value="规范性审核：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" rendered="#{!((mgrbean.zoneType == 4 and mgrbean.ifCheck) or (mgrbean.zoneType == 3 and mgrbean.ifCityDirect == 1 and mgrbean.ifCheck))}">
                    <p:selectOneRadio  style="width:100px;pointer-events:none;"  value="#{mgrbean.gbz188Nostd.chkStdFlag}"  >
                        <f:selectItem itemLabel="是" itemValue="1" />
                        <f:selectItem itemLabel="否" itemValue="0" />
                    </p:selectOneRadio>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" rendered="#{(mgrbean.zoneType == 4 and mgrbean.ifCheck) or (mgrbean.zoneType == 3 and mgrbean.ifCityDirect == 1 and mgrbean.ifCheck)}">
                    <p:selectOneRadio  style="width:100px;"  value="#{mgrbean.gbz188Nostd.chkStdFlag}"  >
                        <f:selectItem itemLabel="是" itemValue="1" />
                        <f:selectItem itemLabel="否" itemValue="0" />
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row rendered="#{(mgrbean.newFlow.state>=5 and mgrbean.ifCityDirect ==1) or (mgrbean.newFlow.state>=3 and mgrbean.ifCityDirect ==0) }">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="初审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" readonly="true"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.ifCityDirect == 1?mgrbean.newFlow.cityAuditAdv:mgrbean.newFlow.countAuditAdv}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{(mgrbean.newFlow.state>=5 and mgrbean.ifCityDirect ==1) or (mgrbean.newFlow.state>=3 and mgrbean.ifCityDirect ==0) }">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="初审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.ifCityDirect == 1?mgrbean.newFlow.cityChkPsn:mgrbean.newFlow.countyChkPsn}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.ifCityDirect ==0 and  mgrbean.newFlow.state>=5 and mgrbean.zoneType!=4}">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="复审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false"  readonly="true"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.cityAuditAdv}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.ifCityDirect ==0 and  mgrbean.newFlow.state>=5 and mgrbean.zoneType!=4}">
                <!-- 地区级别为2 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="复审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.newFlow.cityChkPsn}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state==7 and mgrbean.zoneType!=4 and mgrbean.zoneType!=3 }">
                <!-- 地区级别为2 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="终审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false"  readonly="true"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.proAuditAdv}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state==7 and mgrbean.zoneType!=4 and mgrbean.zoneType!=3}">
                <!-- 地区级别为2 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="终审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.newFlow.proChkPsn}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.ifCheck and !(mgrbean.newFlow.state==2 or mgrbean.newFlow.state==4 or mgrbean.newFlow.state==6)}" >
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifCheck}"/>
                    <p:outputLabel value="审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" readonly="#{mgrbean.ifCheck?'false':'true'}"
                                     placeholder="无审核意见，请填写无"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.proAuditAdv}"
                                     maxlength="100"
                                     rendered="#{mgrbean.zoneType == 2}"/>
                    <!-- 非市直属，且地区级别为4，为 countAuditAdv;否则cityAuditAdv-->
                    <p:inputTextarea rows="5" autoResize="false" readonly="#{mgrbean.ifCheck?'false':'true'}"
                                     placeholder="无审核意见，请填写无"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.countAuditAdv}"
                                     maxlength="100"
                                     rendered="#{mgrbean.zoneType == 4 and mgrbean.ifCityDirect != 1}"/>
                    <p:inputTextarea rows="5" autoResize="false" readonly="#{mgrbean.ifCheck?'false':'true'}"
                                     placeholder="无审核意见，请填写无"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.cityAuditAdv}"
                                     maxlength="100"
                                     rendered="#{!(mgrbean.zoneType == 4 and mgrbean.ifCityDirect != 1) and mgrbean.zoneType!=2}"/>
                </p:column>
            </p:row>

            <!-- 审核人：默认当前单位联系人 -->
            <p:row rendered="#{mgrbean.ifCheck and !(mgrbean.newFlow.state==2 or mgrbean.newFlow.state==4 or mgrbean.newFlow.state==6)}" >
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifCheck}"/>
                    <p:outputLabel value="审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputText value="#{mgrbean.newFlow.proChkPsn}" maxlength="25" style="width: 595px;" rendered="#{mgrbean.zoneType == 2}" readonly="#{mgrbean.ifCheck?'false':'true'}"/>
                    <p:inputText value="#{mgrbean.newFlow.countyChkPsn}" maxlength="25" style="width: 595px;" rendered="#{mgrbean.zoneType == 4 and mgrbean.ifCityDirect != 1}" readonly="#{mgrbean.ifCheck?'false':'true'}"/>
                    <p:inputText value="#{mgrbean.newFlow.cityChkPsn}" maxlength="25" style="width: 595px;" rendered="#{!(mgrbean.zoneType == 4 and mgrbean.ifCityDirect != 1) and mgrbean.zoneType!=2}" readonly="#{mgrbean.ifCheck?'false':'true'}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <ui:include src="/webapp/heth/comm/reasonDialogComm.xhtml"/>
    </ui:define>
</ui:composition>