<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>用人单位选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputScript name="js/datatable.js" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>

    <h:body onload="document.getElementById('codeForm:pym').focus();">
        <h:form id="codeForm">
        	<h:outputStylesheet name="css/ui-tabs.css"/>
        	<p:outputPanel styleClass="zwx_toobar_42">
		        <h:panelGrid columns="3"
		                     style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
		                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
		            <p:commandButton value="确定" icon="ui-icon-check"
		                             action="#{selectYrOrgCommListBean.submitAction}"
		                             process="@this,selectedTable"/>
		             <p:commandButton value="取消" icon="ui-icon-close"
		                             action="#{selectYrOrgCommListBean.dialogClose}" process="@this"/>
		        </h:panelGrid>
		    </p:outputPanel>
            <table width="100%">
                <tr>
                	<td style="width: 40px;">
                        <p:outputLabel value="地区：" styleClass="zwx_dialog_font" />
                    </td>
                    <td style="text-align: left;width:200px;">
                    	<zwx:ZoneSingleComp zoneList="#{selectYrOrgCommListBean.zoneList}"
                                    zoneCode="#{selectYrOrgCommListBean.searchZoneCode}"
                                    zoneName="#{selectYrOrgCommListBean.searchZoneName}" 
                                    onchange="onSearchNodeSelect()"
                                    id="searchZone"/>
                         <p:remoteCommand name="onSearchNodeSelect" action="#{selectYrOrgCommListBean.searchAction}"
                         		process="@this,searchZone,selectedTable" update="selectedTable"/>
                    </td>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                            <p:outputLabel value="单位名称：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{selectYrOrgCommListBean.searchName}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedTable" process="@this,searchPanel,selectedTable" listener="#{selectYrOrgCommListBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{selectYrOrgCommListBean.displayList}" id="selectedTable"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         pageLinks="5" paginatorPosition="bottom"
                         rowsPerPageTemplate="#{'10,20,50'}"
                         selection="#{selectYrOrgCommListBean.selectedList}"  rowKey="#{itm.rid}" rowSelectMode="add">
                <p:ajax event="page" process="@this,selectedTable"/>
                <p:ajax event="rowSelect" process="@this,selectedTable" listener="#{selectYrOrgCommListBean.rowSelectListener}" immediate="true" />
                <p:ajax event="rowUnselect" process="@this,selectedTable" listener="#{selectYrOrgCommListBean.rowUnselectListener}" immediate="true" />
                <p:ajax event="rowSelectCheckbox" process="@this,selectedTable" listener="#{selectYrOrgCommListBean.rowSelectListener}" immediate="true" />
                <p:ajax event="rowUnselectCheckbox" process="@this,selectedTable" listener="#{selectYrOrgCommListBean.rowUnselectListener}" immediate="true" />
                <p:ajax event="toggleSelect" process="@this,selectedTable" listener="#{selectYrOrgCommListBean.toggleSelectListener}" immediate="true" />
                <p:column style="width: 16px;text-align: center;" selectionMode="multiple"/>
                <p:column headerText="地区" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{itm.tsZoneByZoneId.zoneType>3?itm.tsZoneByZoneId.fullName.substring(itm.tsZoneByZoneId.fullName.indexOf('_')+1,itm.tsZoneByZoneId.fullName.length()):itm.tsZoneByZoneId.zoneName}" />
                </p:column>
                <p:column headerText="单位名称" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{itm.crptName}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>
