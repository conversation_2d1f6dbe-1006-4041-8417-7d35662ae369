<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdGjBhklistBean"-->
    <ui:param name="mgrbean" value="#{tdGjBhklistBean}"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/tdGjBhkView.xhtml"/>
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <ui:param name="onfocus" value="false"/>
        <script type="text/javascript">
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
        </style>
    </ui:define>
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="国家体检数据导入"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex">
            <h:panelGrid columns="6" style="border-color: transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}" onclick="hideTooltips();"
                                 update=":tabView:mainForm:dataTable"
                                 process="@this,mainGrid"/>
                <p:commandButton value="导入" icon="ui-icon-arrowreturnthick-1-n" id="importBtn"
                                 update="uploadFileDialog" rendered="#{mgrbean.ifExport}"
                                 oncomplete="PF('UploadFileDialog').show();"/>
                <p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                 process="@this"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
                                 rendered="#{null ne mgrbean.importErrFilePath and mgrbean.failedNum gt 0}">
                    <p:fileDownload value="#{mgrbean.errorImportFile}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="地区："/>
            </p:column>
            <p:column styleClass="cs-scv-w" style="padding-left: 0px !important;">
                <zwx:ZoneSingleNewComp id="searchZone"
                                       zoneList="#{mgrbean.zoneList}"
                                       zoneCode="#{mgrbean.searchZoneCode}" zoneName="#{mgrbean.searchZoneName}"
                                       onchange="onZoneSelect();" ifShowTrash="false"/>
                <p:remoteCommand name="onZoneSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="@this,unitName"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="体检机构："/>
            </p:column>
            <p:column styleClass="cs-scv-w" style="padding-left: 5px !important;">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                            process="@this"
                                            resetValues="true" update="unitName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="报告卡编码："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.searchBhkCode}" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="姓名："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.searchName}" maxlength="100"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="体检危害因素："/>
            </p:column>
            <p:column styleClass="cs-scv-w" style="padding-left: 5px !important;">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="badRsnName" style="width: 180px;cursor: pointer;"
                                             value="#{mgrbean.searchHazardNames}" readonly="true"
                                             onclick="document.getElementById('tabView:mainForm:selBadRsnLink').click();"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search" id="selBadRsnLink"
                                               style="position: relative;left: -28px !important;"
                                               process="@this" action="#{mgrbean.selSimpleCode5007Action}">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCode5007Action}"
                                            process="@this" resetValues="true" update="badRsnName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="badRsnName"
                                               action="#{mgrbean.clearSimpleCode5007}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="体检结论："/>
            </p:column>
            <p:column styleClass="cs-scv" style="padding-left: 3px !important;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchConclusionName}"
                                        selectedIds="#{mgrbean.searchConclusionIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="180" height="200"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="体检日期："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchStartDate}"
                                              endDate="#{mgrbean.searchEndDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="报告出具日期："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchReportStartDate}"
                                              endDate="#{mgrbean.searchReportEndDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="导入日期："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchImportStartDate}"
                                              endDate="#{mgrbean.searchImportEndDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" styleClass="cs-break-word" style="width: 220px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="体检机构" styleClass="cs-break-word"
                  style="width: 230px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="报告卡编码" styleClass="cs-break-word"
                  style="width: 180px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="姓名" styleClass="cs-break-word" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="体检日期" styleClass="cs-break-word" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
        </p:column>
        <p:column headerText="报告出具日期" styleClass="cs-break-word" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
        </p:column>
        <p:column headerText="体检结论" styleClass="cs-break-word" style="width: 200px;">
            <h:outputText id="returnReason" value="#{itm[7]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="returnReason" style="max-width:450px;">
                <p:outputLabel value="#{itm[7]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="用工单位名称" styleClass="cs-break-word" style="width: 230px;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="导入日期" styleClass="cs-break-word" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[9]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink action="#{mgrbean.viewInitAction}" value="详情" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog header="导入" widgetVar="UploadFileDialog" id="uploadFileDialog" resizable="false"
                  modal="true" width="600">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要文件上传！" styleClass="table-border-none"
                                      id="fileUpload"
                                      process="@this" fileUploadListener="#{mgrbean.importDataAction}"
                                      label="选择文件" invalidSizeMessage="文件大小不能超过200M!"
                                      validatorMessage="上传出错啦，请重新上传！"
                                      allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！"
                                      invalidFileMessage="只能上传xls、xlsx格式的文件！"
                                      previewWidth="120" cancelLabel="取消"
                                      uploadLabel="导入" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                      dragDropSupport="true" mode="advanced" sizeLimit="209715200"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>