<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/webapp/heth/comm/tbTjBhkInfo.xhtml">

    <ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
    <ui:param name="birthIfShow" value="false"></ui:param>
    <ui:param name="pageParam" value="1"/>

    <!-- 标题 -->
    <ui:define name="titleContent">
        <p:panelGrid style="width:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="GBZ188不规范数据-详情" />
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="buttons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:outputPanel >
                    <p:spacer width="5"/>
                    <p:commandButton value="#{(mgrbean.newFlow.state==2 or (mgrbean.ifCityDirect ==1 and mgrbean.newFlow.state==4)) ?'初审':((mgrbean.ifCityDirect ==0 and mgrbean.newFlow.state==4)?'复审':'终审')}退回原因"
                                     icon="icon-alert" action="#{mgrbean.initDialog}" style="color:red;"
                                     process="@this" oncomplete="PF('ReasonDialog').show();"
                                     update="reasonDialog"
                                     rendered="#{mgrbean.newFlow.state==2 or mgrbean.newFlow.state==6 or mgrbean.newFlow.state==4}">
                        <f:setPropertyActionListener target="#{mgrbean.readOnly}" value="true"/>
                    </p:commandButton>
                </p:outputPanel>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
                                 action="#{mgrbean.backAction}" process="@this" oncomplete="datatableOffClick()"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <ui:define name="editContent">
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="6" style="text-align:left;height: 20px;">
                        <p:outputLabel value="不规范原因"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="不规范原因：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea readonly="true"
                                     rows="5" autoResize="false"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.tjBhkInfoBean.tdTjBhkShow.lackMsg}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <p:outputLabel value="不规范原因说明：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" id="nostdDesc"
                                     style="resize: none;width: 594px;"
                                     maxlength="100" readonly="true"
                                     value="#{mgrbean.gbz188Nostd.nostdDesc}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{(mgrbean.newFlow.state>=3 and mgrbean.ifCityDirect ==0) or (mgrbean.newFlow.state>=5 and mgrbean.ifCityDirect ==1)}">
                <p:column style="text-align:right;padding-right:3px;height: 30px">
                    <p:outputLabel value="规范性审核：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" >
                    <p:selectOneRadio  style="width:100px;pointer-events:none;"  value="#{mgrbean.gbz188Nostd.chkStdFlag}"  >
                        <f:selectItem itemLabel="是" itemValue="1" />
                        <f:selectItem itemLabel="否" itemValue="0" />
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state>=3 and mgrbean.ifCityDirect ==0 }">
                <!-- 非市直属，且地区级别为3 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="初审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" readonly="true"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.countAuditAdv}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state>=3 and mgrbean.ifCityDirect ==0}">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="初审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.newFlow.countyChkPsn}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state>=5 and mgrbean.ifCityDirect ==1}">
                <!-- 非市直属，且地区级别为3 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="初审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" readonly="true"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.cityAuditAdv}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state>=5 and mgrbean.ifCityDirect ==1}">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="初审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.newFlow.cityChkPsn}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.ifCityDirect ==0 and  mgrbean.newFlow.state>=5}">
                <!-- 地区级别为2 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="复审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false"  readonly="true"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.cityAuditAdv}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.ifCityDirect ==0 and  mgrbean.newFlow.state>=5}">
                <!-- 地区级别为2 -->
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="复审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.newFlow.cityChkPsn}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state>=7}">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="终审审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea rows="5" autoResize="false" readonly="true"
                                     placeholder="无审核意见，请填写无"
                                     style="resize: none;width: 594px;"
                                     value="#{mgrbean.newFlow.proAuditAdv}"
                                     maxlength="100"
                                     />
                    <!-- 非市直属，且地区级别为4，为 countAuditAdv;否则cityAuditAdv-->
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.newFlow.state>=7}">
                <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                    <p:outputLabel value="终审审核人：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:outputLabel value="#{mgrbean.newFlow.proChkPsn}"  />
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="#{(mgrbean.newFlow.state==2 or (mgrbean.ifCityDirect ==1 and mgrbean.newFlow.state==4)) ?'初审':((mgrbean.ifCityDirect ==0 and mgrbean.newFlow.state==4)?'复审':'终审')}退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.backRsn}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="reasonContent" maxlength="100" readonly="#{mgrbean.readOnly}"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         process="@this,reasonContent" oncomplete="datatableOffClick()"
                                         action="#{mgrbean.returnAction}"
                                         rendered="#{!mgrbean.readOnly}" />
                        <p:commandButton value="确定" styleClass="submit_btn" oncomplete="datatableOffClick()"
                                         onclick="PF('ReasonDialog').hide();"
                                         rendered="#{mgrbean.readOnly}" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>