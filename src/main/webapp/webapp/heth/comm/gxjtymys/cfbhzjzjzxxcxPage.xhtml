<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.gxjtymys.CfbhzjzjzxxcxBean"-->
    <ui:param name="mgrbean" value="#{cfbhzjzjzxxcxBean}"/>
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar2 input {
                width: 78px;
            }
        </style>
    </h:head>
    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="尘肺病患者救治救助信息查询"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                                <p:commandButton value="查询" icon="ui-icon-search" process="@this"/>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
                                style="margin-top: 5px;margin-bottom: 5px;">
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;height: 37px;">
                                    <h:outputLabel value="入院时间："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchHospitalizationStartDate}"
                                                                  endDate="#{mgrbean.searchHospitalizationEndDate}"
                                                                  styleClass="myCalendar2"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px;">
                                    <h:outputLabel value="出院时间："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchDischargeStartDate}"
                                                                  endDate="#{mgrbean.searchDischargeEndDate}"
                                                                  styleClass="myCalendar2"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px">
                                    <h:outputLabel value="录入时间："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchInputStartDate}"
                                                                  endDate="#{mgrbean.searchInputEndDate}"
                                                                  styleClass="myCalendar2"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 37px;">
                                    <h:outputLabel value="姓名："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;">
                                    <h:outputLabel value="身份证号码："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;" colspan="3">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;">
                                    <h:outputLabel value="报告地区："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:4px;">
                                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                                        zoneCode="#{mgrbean.searchZoneCode}"
                                                        zoneName="#{mgrbean.searchZoneName}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;">
                                    <h:outputLabel value="报告单位："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;" colspan="3">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{mgrbean.dataList}" paginator="true" rows="20"
                                 rowIndexVar="R"
                                 id="dataTable" emptyMessage="没有您要找的记录！" paginatorPosition="bottom"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{mgrbean.perPageSize}"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                        <p:column headerText="序号" style="width: 4%; height:30px; text-align: center">
                            <h:outputText value="#{itm[0]}"/>
                        </p:column>
                        <p:column headerText="姓名" style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[1]}"/>
                        </p:column>
                        <p:column headerText="身份证号码" style="width: 15%;text-align: center;">
                            <h:outputText value="#{itm[2]}"/>
                        </p:column>
                        <p:column headerText="报告单位" style="width: 20%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[3]}"/>
                        </p:column>
                        <p:column headerText="录入时间" style="width: 7%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[4]}"/>
                        </p:column>
                        <p:column headerText="本次救助金额（元）"
                                  style="width: 10%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[5]}"/>
                        </p:column>
                        <p:column headerText="剩余金额（元）" style="width: 10%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[6]}"/>
                        </p:column>
                        <p:column headerText="操作" style="padding-left: 10px;">
                            <p:commandLink value="详情" process="@this"/>
                        </p:column>
                    </p:dataTable>
                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>