<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="灯横幅板块"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel" style="margin-bottom: 10px;">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="切换" icon="ui-icon-transferthick-e-w" process="@this" />

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:commandButton type="button" onclick="PF('switcher').previous();" value="上一页"
                                     icon="ui-icon-circle-triangle-w" id="prev"/>
                    <p:spacer width="5" />
                    <p:commandButton type="button" onclick="PF('switcher').next();" value="下一页"
                                     icon="ui-icon-circle-triangle-e" id="next"/>

                    <p:imageSwitch effect="wipe" widgetVar="switcher" id="manuelSwitcher"
                                   slideshowAuto="false">
                        <p:graphicImage name="/images/gxjtymys/dpf1.png" />
                        <p:graphicImage name="/images/gxjtymys/dpf2.png" />
                        <p:graphicImage name="/images/gxjtymys/dpf3.png" />
                        <p:graphicImage name="/images/gxjtymys/dpf4.png" />
                    </p:imageSwitch>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>