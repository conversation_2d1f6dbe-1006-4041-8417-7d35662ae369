<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
<!-- 托管Bean -->
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="尘肺病患者救治救助信息统计"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="分析" icon="ui-icon-search" process="@this" />
                                <p:commandButton value="模板下载" icon="ui-icon-arrowthick-1-s" process="@this" />
                                <p:commandButton value="导出" icon="ui-icon-document" process="@this" />

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" >
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;height: 44px;">
                                    <h:outputLabel  value="地区：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:3px;width:250px">
                                    <p:column style="text-align:left;padding-left:3px;width:200px;">
                                        <zwx:ZoneSingleComp zoneList="#{patientBringAnalyYS.zoneList}" zoneCode="#{patientBringAnalyYS.searchZoneCode}" zoneName="#{patientBringAnalyYS.searchZoneName}" id="searchZone"/>
                                    </p:column>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px">
                                    <h:outputLabel  value="出院日期：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <zwx:CalendarDynamicLimitComp startDate="#{patientBringAnalyYS.searchSDate}" endDate="#{patientBringAnalyYS.searchEDate}" styleClass="myCalendar1"></zwx:CalendarDynamicLimitComp>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;height: 44px;">
                                    <h:outputLabel  value="统计维度：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                                    <p:selectOneRadio  style="width:880px;"  >
                                        <f:selectItem itemLabel="年度内，或指定时间区段内诊断病例的地区分布" itemValue="1" />
                                        <f:selectItem itemLabel="尘肺病例诊断类别分布" itemValue="2" />
                                        <f:selectItem itemLabel="尘肺病例各类分布" itemValue="3" />
                                        <f:selectItem itemLabel="尘肺病例性别、年龄、发病工龄、期别分布" itemValue="4" />
                                    </p:selectOneRadio>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{patientBringAnalyYS.dataList}"
                                 id="dataTable" emptyMessage="没有您要找的记录！">
                        <p:column headerText="地区" style="width: 10%; height:30px; text-align: center">
                            <h:outputText value="#{itm[1]}" />
                        </p:column>
                        <p:column headerText="人数" style="width: 5%;text-align: center;">
                            <h:outputText value="#{itm[2]}" />
                        </p:column>
                        <p:column headerText="医疗总费用（元）" style="width: 7%;text-align: center;">
                            <h:outputText value="#{itm[3]}" />
                        </p:column>
                        <p:column headerText="医保报销（元）" style="width: 6%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[4]}" />
                        </p:column>
                        <p:column headerText="尘肺补助（元）" style="width: 6%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[5]}" />
                        </p:column>
                        <p:column headerText="自费（元）" style="width: 5%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[6]}" />
                        </p:column>
                        <p:column headerText="医疗机构承担（元）" style="width: 8%;padding-left: 10px;text-align: center;">
                            <h:outputText value="#{itm[7]}" />
                        </p:column>
                    </p:dataTable>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>