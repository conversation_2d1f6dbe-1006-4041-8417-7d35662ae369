<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="诊断、鉴定工作流程管理界面"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="添加" icon="ui-icon-plus" process="@this" />
                                <p:commandButton value="查询" icon="ui-icon-search" process="@this" />
                                <p:commandButton value="流程设置" process="@this" />

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" >
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;">
                                    <h:outputLabel  value="业务流程类型：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:3px;width:250px">
                                    <p:selectOneMenu style="width: 160px;" >
                                        <f:selectItem itemLabel="-- 请选择 --"/>
                                        <f:selectItem itemValue="0" itemLabel="职业病诊断"/>
                                        <f:selectItem itemValue="1" itemLabel="职业病鉴定"/>
                                    </p:selectOneMenu>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px">
                                    <h:outputLabel  value="流程节点：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:selectManyCheckbox >
                                        <f:selectItem itemValue="0" itemLabel="提交申请"/>
                                        <f:selectItem itemValue="1" itemLabel="申请受理"/>
                                        <f:selectItem itemValue="2" itemLabel="全国查重"/>
                                        <f:selectItem itemValue="3" itemLabel="初审反馈"/>
                                        <f:selectItem itemValue="4" itemLabel="驳回"/>
                                        <f:selectItem itemValue="5" itemLabel="材料补充"/>
                                        <f:selectItem itemValue="6" itemLabel="诊断受理"/>
                                    </p:selectManyCheckbox>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{diagProcessNodeManageBean.dataList}" paginator="true" rows="20" rowIndexVar="R"
                                  id="dataTable" emptyMessage="没有您要找的记录！" paginatorPosition="bottom"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{diagProcessNodeManageBean.perPageSize}"  currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                  >
                        <p:column headerText="流程顺序" style="width: 15%; height:30px; text-align: center">
                            <h:outputText value="#{itm[0]}" />
                        </p:column>
                        <p:column headerText="流程类型" style="width: 30%;text-align: center;">
                            <h:outputText value="#{itm[1]}" />
                        </p:column>
                        <p:column headerText="流程编码" style="width: 15%;text-align: center;">
                            <h:outputText value="#{itm[2]}" />
                        </p:column>
                        <p:column headerText="流程名称" style="width: 30%;padding-left: 10px;">
                            <h:outputText value="#{itm[3]}" />
                        </p:column>
                        <p:column headerText="操作" style="padding-left: 10px;">
                            <p:commandLink value="查看" process="@this"   />
                            <p:spacer width="5" />
                            <p:commandLink value="修改" process="@this"   />
                            <p:spacer width="5" />
                            <p:commandLink value="删除" process="@this"   />
                        </p:column>
                    </p:dataTable>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>