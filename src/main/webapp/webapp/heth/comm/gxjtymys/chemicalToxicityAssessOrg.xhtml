<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <ui:param name="mgrbean" value="#{chemicalToxicityAssessOrgBean}"/>
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .calendarClass input{
                width:180px;
            }
        </style>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="化学品毒性鉴定机构资质信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel" style="margin-bottom: 10px;">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="保存" icon="ui-icon-disk" process="@this"  />
                                <p:commandButton value="提交" icon="ui-icon-check"  process="@this" />
                                <p:commandButton value="添加" icon="ui-icon-plus"  process="@this" rendered="#{mgrbean.tabTitle == '人员信息'}" />

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:tabView id="innerTab" style="margin-top: 5px;" cache="false" dynamic="false" >
                        <p:ajax event="tabChange" process="@this" listener="#{mgrbean.tableChange}" update=":tabView:mainForm:buttonsPanel" />
                        <p:tab title="基本信息">
                            <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;" >
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="单位名称：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                                        <p:inputText  style="width:604px;"  />
                                    </p:column>
                                </p:row>
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="注册地址：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                                        <p:inputText style="width:604px;"  />
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="法定代表人：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                                        <p:inputText  style=" width: 205px;" />
                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="法定代表人职务：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                                        <p:inputText  style=" width: 180px;" />
                                    </p:column>
                                </p:row>
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="联系人：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                                        <p:inputText style="width: 205px;" />
                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="联系人手机：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                                        <p:inputText style="width:180px;" />
                                    </p:column>
                                </p:row>
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="电话：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 450px;">
                                        <p:inputText  style="width: 205px;" />
                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="传真：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                                        <p:inputText style="width:180px;" />
                                    </p:column>
                                </p:row>
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="邮编：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                                        <p:inputText  style="width: 205px;" />
                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="电子邮箱：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                                        <p:inputText style="width:180px;" />
                                    </p:column>
                                </p:row>
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="备案编号："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                                        <p:inputText style="width:205px;" />

                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="备案日期："  />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;" >
                                        <p:calendar styleClass="calendarClass" navigator="true"
                                                    yearRange="c-20:c" converterMessage="备案日期格式输入不正确！"
                                                    pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                                                    maxdate="new Date()" readonlyInput="true" />
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="状态：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                                        <p:selectOneRadio style="width: 130px;" >
                                            <f:selectItem itemLabel="正常" itemValue="0"/>
                                            <f:selectItem itemLabel="注销" itemValue="1"/>
                                        </p:selectOneRadio>
                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
                                        <p:outputLabel value="注销日期：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;" >
                                        <p:calendar size="25" navigator="true"
                                                    yearRange="c-20:c" converterMessage="注销日期格式输入不正确！"
                                                    pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                                                    id="cancelDate" styleClass="calendarClass" />
                                    </p:column>
                                </p:row>
                                <p:row >
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="业务范围：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                                        <p:inputText  style="width: 205px;" />
                                    </p:column>
                                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                                        <p:outputLabel value="机构属性：" />
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                                        <p:inputText style="width:180px;" />
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </p:tab>
                        <p:tab title="人员信息">
                            <p:dataTable var="itm"
                                         emptyMessage="没有您要找的记录！" paginator="true" rows="20" paginatorPosition="bottom" rowIndexVar="R"
                                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                         rowsPerPageTemplate="#{mgrbean.perPageSize}" lazy="true" pageLinks="5"
                                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                         style="margin-top:5px;width:100%">
                                <p:column headerText="姓名" style="text-align:center;width:80px;" />
                                <p:column headerText="性别" style="text-align:center;width:40px;" />
                                <p:column headerText="职称" style="text-align:center;width:100px;" />
                                <p:column headerText="人员属性" style="width:260px; " />
                                <p:column headerText="在岗状态" style="text-align:center;width:60px; " />
                                <p:column headerText="证书编号" style="width:200px;" />
                            </p:dataTable>
                        </p:tab>
                    </p:tabView>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>