<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar input {
                width: 160px;
            }

            .myCalendar2 input {
                width: 78px;
            }

            .blue_label {
                font-size: 14px !important;
                margin-left: 5px;
                font-weight: 600;
                color: #2e6ea9;
            }

            .white_col {
                text-align: left;
                height: 30px;
                background-color: #dfeffc;
                border-top: 0 !important;
                border-bottom: 0 !important;
            }

            .tableTr .ui-widget-content {
                border: 0 solid #a6c9e2;
            }
        </style>

        <script type="text/javascript">
            //<![CDATA[
            jQuery(document).ready(function () {
                windowScrollTop();
            });

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }

            //]]>
        </script>
    </h:head>
    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="职业健康检查机构能力保持情况表填报"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                                <p:commandButton value="暂存" icon="ui-icon-disk" process="@this"/>
                                <p:commandButton value="提交" icon="ui-icon-check" process="@this"/>
                                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" process="@this"/>
                                <p:inputText style="visibility: hidden;width: 0"/>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>

                    <p:outputPanel styleClass="businessInfo">
                        <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first"
                                     id="writeSortPanel">
                            <p:row>
                                <p:column colspan="2"><span>本环节文书</span></p:column>
                            </p:row>
                            <p:row>
                                <p:column style="padding: 0;" colspan="2">
                                    <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="width: 190px;">
                                    <h:outputText value="《能力保持情况表》"/>
                                </p:column>
                                <p:column>
                                    <p:outputPanel>
                                        <p:commandButton value="制作" process="@this"/>
                                    </p:outputPanel>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                        <div id="writ_yszyb">
                            <!-- 标题 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                                <p:row>
                                    <p:column styleClass="noBorder" style="text-align: center;padding: 20px 0;"
                                              colspan="3">
                                        <h:outputText value="职业健康检查机构能力保持情况表"
                                                      style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 表单 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;width:250px;">
                                        <p:outputLabel value="填报单位名称："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;width:240px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title" style="height:30px;width:200px;">
                                        <p:outputLabel value="组织机构代码："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;width:240px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="单位详细地址："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:inputText style="width:616px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="width:190px;">
                                        <p:outputLabel value="邮政编码："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="经济类型："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="电话："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="传真："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="电子邮件："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="法定代表人："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="身份证号码："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="职务/职称："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="是否参加了实验室间比对和室间质评："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="职业健康检查专业技术人员数："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:inputText style="width:160px;"/>
                                        <p:outputLabel value="；其中，执业（助理）医师人数："/>
                                        <p:inputText style="width:160px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="开展检查项目类别："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:inputTextarea style="width:616px;"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <div style="padding: 40px;">
                            </div>
                        </div>
                    </p:outputPanel>
                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
    <!-- 按钮组 -->
    <!--<ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" process="@this"/>
                <p:commandButton value="撤销" icon="ui-icon-cancel" process="@this">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" process="@this"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="sticky"/>
    </ui:define>-->
</f:view>
</html>