<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.gxjtymys.CfbhzjzjzxxcxBean"-->
    <ui:param name="mgrbean" value="#{cfbhzjzjzxxcxBean}"/>
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar input {
                width: 160px;
            }

            .myCalendar2 input {
                width: 78px;
            }

            .blue_label {
                font-size: 14px !important;
                margin-left: 5px;
                font-weight: 600;
                color: #2e6ea9;
            }

            .white_col {
                text-align: left;
                height: 30px;
                background-color: #dfeffc;
                border-top: 0 !important;
                border-bottom: 0 !important;
            }

            .tableTr .ui-widget-content {
                border: 0 solid #a6c9e2;
            }
        </style>

        <script type="text/javascript">
            //<![CDATA[
            jQuery(document).ready(function () {
                windowScrollTop();
            });

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }

            //]]>
        </script>
    </h:head>
    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="农民工尘肺病患者救助基本信息录入"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                                <p:commandButton value="暂存" icon="ui-icon-disk" process="@this"/>
                                <p:commandButton value="结算单生成" icon="ui-icon-print" process="@this"/>
                                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" process="@this"/>
                                <p:inputText style="visibility: hidden;width: 0"/>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>

                    <p:outputPanel styleClass="businessInfo">
                        <div id="writ_yszyb">
                            <!-- 标题 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                                <p:row>
                                    <p:column styleClass="noBorder" style="text-align: center;padding: 20px 0;" colspan="3">
                                        <h:outputText value="农民工尘肺病患者救助基本信息录入"
                                                      style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 表单 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;width:170px">
                                        <p:outputLabel value="报告地区："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:5px;width: 220px;">
                                        <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                                            zoneCode="#{mgrbean.searchZoneCode}"
                                                            zoneName="#{mgrbean.searchZoneName}"
                                                            width="160"/>
                                    </p:column>
                                    <p:column styleClass="column_title" style="width: 170px;">
                                        <p:outputLabel value="报告单位："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="姓名："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="身份证号："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="家庭地址："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:inputText style="width:569px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="入院日期："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-200:c" converterMessage="入院日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="出院日期："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-200:c" converterMessage="入院日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="补助类型："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:selectOneMenu style="width: 168px;">
                                            <f:selectItem itemLabel="-- 请选择 --"/>
                                            <f:selectItem itemValue="0" itemLabel="可行肺灌洗的尘肺（一般治疗+肺灌洗治疗）"/>
                                            <f:selectItem itemValue="1" itemLabel="单纯尘肺"/>
                                            <f:selectItem itemValue="2" itemLabel="可行肺灌洗的尘肺"/>
                                            <f:selectItem itemValue="3" itemLabel="有并发症的尘肺"/>
                                            <f:selectItem itemValue="4" itemLabel="肺功能重度损伤的叁期尘肺"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="医疗总费用（元）："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="是否建档立卡贫困户："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:selectOneRadio>
                                            <f:selectItem itemLabel="是" itemValue="1"/>
                                            <f:selectItem itemLabel="否" itemValue="0"/>
                                        </p:selectOneRadio>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="医疗机构承担费用（元）："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="医保报销（元）："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="尘肺救助（元）："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="自费（元）："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:60px;">
                                        <p:outputLabel value="计算公式说明："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:outputLabel value="医疗总费用=医疗机构承担+补偿标准；"/>
                                        <br/>
                                        <p:outputLabel value="医疗报销=补偿标准*65%；自费+尘肺补助=补偿标准*35%；"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <div style="padding: 50px;">
                            </div>
                        </div>
                    </p:outputPanel>
                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
    <!-- 按钮组 -->
    <!--<ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" process="@this"/>
                <p:commandButton value="撤销" icon="ui-icon-cancel" process="@this">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" process="@this"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="sticky"/>
    </ui:define>-->
</f:view>
</html>