<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <link rel="stylesheet" href="/resources/css/default.css" />
        <link rel="stylesheet" href="/resources/css/ui-tabs.css" />
        <style type="text/css">
            .myCalendar1 input{
                width: 146px;
            }
            .column_title{
                width: 150px!important;
            }
        </style>
    </h:head>
    <h:body>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="职业健康检查项目表填报"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"  process="@this"  >
                                </p:commandButton>
                                <p:commandButton value="提交" icon="ui-icon-check"  process="@this" >
                                </p:commandButton>

                                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"  process="@this"/>

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:outputPanel styleClass="businessInfo">
                        <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                            <p:row>
                                <p:column colspan="2" ><span>本环节文书</span></p:column>
                            </p:row>
                            <p:row>
                                <p:column style="padding: 0;" colspan="2">
                                    <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="width: 190px;">
                                    <h:outputText value="《职业健康检查项目表》"/>
                                </p:column>
                                <p:column>
                                    <p:outputPanel>
                                        <p:commandButton value="制作"  process="@this" ></p:commandButton>
                                    </p:outputPanel>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                        <div id="chkSmaryDiv">
                            <!-- 标题 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                                <p:row>
                                    <p:column styleClass="noBorder" style="text-align: center;">
                                        <p:outputPanel style="padding-bottom:8px;">
                                            <h:outputText value="职业健康检查项目表开展情况"
                                                          style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                                        </p:outputPanel>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 100px;" id="basicPanel">
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="填报单位名称："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="组织机构代码："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="单位地址："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="3">
                                        <p:inputText value="" style="width: 300px"></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column  colspan="4">
                                        <p:commandButton value="添加"  process="@this" ></p:commandButton>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="职业健康检查项目名称："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;"  colspan="3">
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column colspan="4">
                                        <p:dataTable  paginatorPosition="bottom"
                                                     value=""
                                                     widgetVar="badRsnTable" var="jcSub"
                                                     emptyMessage="没有数据！" rowIndexVar="R">
                                            <p:columnGroup type="header">
                                                <p:row>
                                                    <p:column  headerText="序号" />
                                                    <p:column  headerText="是否开展职业健康检查" />
                                                    <p:column  headerText="是否具备相应生物样本检测能力" />
                                                    <p:column  headerText="备注（是否外送）" />
                                                </p:row>
                                            </p:columnGroup>
                                            <p:column style="text-align: center">
                                                <h:outputText value="1" />
                                            </p:column>
                                            <p:column style="text-align: center">
                                                <p:inputText value="" ></p:inputText>
                                            </p:column>
                                            <p:column style="text-align: center">
                                                <p:inputText value="" ></p:inputText>
                                            </p:column>
                                            <p:column style="text-align: center">
                                                <p:inputText value="" ></p:inputText>
                                            </p:column>
                                        </p:dataTable>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="填报人："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="填报日期："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-10:c" converterMessage="诊断日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        />
                                    </p:column>
                                </p:row>
                            </p:panelGrid>

                        </div>
                    </p:outputPanel>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>