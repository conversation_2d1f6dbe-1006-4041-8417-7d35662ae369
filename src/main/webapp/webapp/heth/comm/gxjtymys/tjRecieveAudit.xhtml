<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar2 input {
                width: 78px;
            }
        </style>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="职业健康体检受理审核"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="查询" icon="ui-icon-search" process="@this" />
                                <p:commandButton value="批量审核" icon="ui-icon-check" process="@this" />

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" >
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:160px; height: 35px;">
                                    <h:outputLabel  value="用人单位地区：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:3px;width:250px">
                                    <zwx:ZoneSingleNewComp  zoneList="#{tjRecieveAuditBean.auditZoneList}"
                                                            zoneCode="#{tjRecieveAuditBean.searchAuditZoneCode}"
                                                            zoneName="#{tjRecieveAuditBean.searchAuditZoneName}" />
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:160px;">
                                    <h:outputLabel  value="用人单位名称：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width: 250px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:160px">
                                    <h:outputLabel  value="申请日期：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <zwx:CalendarDynamicLimitComp startDate="#{tjRecieveAuditBean.auditStartDate}"
                                                                  endDate="#{tjRecieveAuditBean.auditEndDate}"
                                                                  styleClass="myCalendar2"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:160px;height: 35px;">
                                    <h:outputLabel  value="状态：" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:3px;" colspan="5">
                                    <p:selectManyCheckbox >
                                        <f:selectItem itemValue="0" itemLabel="待审核"/>
                                        <f:selectItem itemValue="1" itemLabel="审核通过"/>
                                        <f:selectItem itemValue="2" itemLabel="已退回"/>
                                    </p:selectManyCheckbox>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{tjRecieveAuditBean.auditList}" paginator="true" rows="20" rowIndexVar="R"
                                 id="dataTable" emptyMessage="没有您要找的记录！" paginatorPosition="bottom"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{tjRecieveAuditBean.perPageSize}"  currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                    >
                        <p:column headerText="地区" style="width: 15%; height:30px; text-align: center">
                            <h:outputText value="#{itm[0]}" />
                        </p:column>
                        <p:column headerText="用人单位名称" style="width: 15%;text-align: center;">
                            <h:outputText value="#{itm[1]}" />
                        </p:column>
                        <p:column headerText="姓名" style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[2]}" />
                        </p:column>
                        <p:column headerText="体检日期" style="width: 15%;text-align: center;">
                            <h:outputText value="#{itm[3]}" />
                        </p:column>
                        <p:column headerText="状态" style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[4]}" />
                        </p:column>
                        <p:column headerText="操作" style="padding-left: 10px;">
                            <p:commandLink value="查看" process="@this"   />
                            <p:spacer width="5" />
                            <p:commandLink value="审核" process="@this"   />
                        </p:column>
                    </p:dataTable>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>