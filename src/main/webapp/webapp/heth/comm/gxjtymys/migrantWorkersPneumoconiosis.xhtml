<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
    <h:head>
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <link rel="stylesheet" href="/resources/css/default.css" />
        <link rel="stylesheet" href="/resources/css/ui-tabs.css" />
        <style type="text/css">
            .myCalendar1 input{
                width: 146px;
            }
        </style>
    </h:head>
    <h:body>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="农民工尘肺病病例报告"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"  process="@this"  >
                                </p:commandButton>
                                <p:commandButton value="提交" icon="ui-icon-check"  process="@this" >
                                </p:commandButton>

                                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"  process="@this"/>

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:outputPanel styleClass="businessInfo">
                        <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                            <p:row>
                                <p:column colspan="2" ><span>本环节文书</span></p:column>
                            </p:row>
                            <p:row>
                                <p:column style="padding: 0;" colspan="2">
                                    <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="width: 190px;">
                                    <h:outputText value="《农民工尘肺病病例报告卡》"/>
                                </p:column>
                                <p:column>
                                    <p:outputPanel>
                                        <p:commandButton value="制作"  process="@this" ></p:commandButton>
                                    </p:outputPanel>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                        <div id="chkSmaryDiv">
                            <!-- 标题 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                                <p:row>
                                    <p:column styleClass="noBorder" style="text-align: center;">
                                        <p:outputPanel style="padding-bottom:8px;">
                                            <h:outputText value="农民工尘肺病病例报告"
                                                          style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                                        </p:outputPanel>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="姓名："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="性别："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="男" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="女" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="出生日期："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-10:c" converterMessage="出生日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        />
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="证件类型："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:selectOneMenu  style="width: 156px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="身份证"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="证件号码："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="联系电话："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="统计工种："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="3">
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="接触有害因素名称："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;"  >
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="开始接害日期："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="3">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-10:c" converterMessage="开始接害日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        />
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="实际接害工龄："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:inputText value="" maxlength="2" style="width: 20px;" />
                                        <p:spacer width="5" />
                                        <h:outputLabel value="年"/>
                                        <p:spacer width="5" />
                                        <p:inputText value="" maxlength="2" style="width: 20px;" />
                                        <p:spacer width="5" />
                                        <h:outputLabel value="月"/>
                                        <p:spacer width="5" />
                                        <p:inputText value="" maxlength="2" style="width: 20px;" />
                                        <p:spacer width="5" />
                                        <h:outputLabel value="日"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="尘肺病诊断类型："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="5" >
                                        <p:selectOneMenu  style="width: 300px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="法定尘肺病诊断"/>
                                            <f:selectItem itemValue="1" itemLabel="临床尘肺病诊断"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="初诊或晋期诊断："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="5" >
                                        <p:selectOneMenu  style="width: 300px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="初诊"/>
                                            <f:selectItem itemValue="1" itemLabel="晋期"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="尘肺病类别："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="5" >
                                        <p:selectOneMenu  style="width: 300px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="矽肺"/>
                                            <f:selectItem itemValue="1" itemLabel="煤工尘肺"/>
                                            <f:selectItem itemValue="2" itemLabel="石墨尘肺"/>
                                            <f:selectItem itemValue="3" itemLabel="碳黑尘肺"/>
                                            <f:selectItem itemValue="4" itemLabel="石棉肺"/>
                                            <f:selectItem itemValue="5" itemLabel="滑石尘肺"/>
                                            <f:selectItem itemValue="6" itemLabel="水泥尘肺"/>
                                            <f:selectItem itemValue="7" itemLabel="云母尘肺"/>
                                            <f:selectItem itemValue="8" itemLabel="陶工尘肺"/>
                                            <f:selectItem itemValue="9" itemLabel="铝尘肺"/>
                                            <f:selectItem itemValue="10" itemLabel="电焊工尘肺"/>
                                            <f:selectItem itemValue="11" itemLabel="铸工尘肺"/>
                                            <f:selectItem itemValue="12" itemLabel="其他尘肺"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="诊断期别："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="5" >
                                        <p:selectOneMenu  style="width: 300px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="壹期"/>
                                            <f:selectItem itemValue="1" itemLabel="贰期"/>
                                            <f:selectItem itemValue="2" itemLabel="叁期"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="发现日期："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="3">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-10:c" converterMessage="发现日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        />
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="信息来源："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:selectOneMenu  style="width: 167px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="职业健康检查"/>
                                            <f:selectItem itemValue="1" itemLabel="职业病诊断"/>
                                            <f:selectItem itemValue="2" itemLabel="门诊治疗"/>
                                            <f:selectItem itemValue="3" itemLabel="住院治疗"/>
                                            <f:selectItem itemValue="4" itemLabel="职业病事故"/>
                                            <f:selectItem itemValue="5" itemLabel="其他"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" rowspan="9">
                                        <p:outputLabel value="患者现况及保障情况："></p:outputLabel>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="现常住地："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="4" >
                                        <p:inputText value="" style="width: 290px"></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="户籍所在地："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="4">
                                        <p:inputText value="" style="width: 290px"></p:inputText>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title"  >
                                        <p:outputLabel value="伤残程度："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px; " colspan="4">
                                        <p:selectOneMenu  style="width: 300px;">
                                            <f:selectItem itemValue="" itemLabel="请选择"/>
                                            <f:selectItem itemValue="0" itemLabel="1级"/>
                                            <f:selectItem itemValue="1" itemLabel="未定级"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="目前是否仍在工作："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="4">
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="是否享受工伤保险："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="2">
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                    <p:column styleClass="column_title" rowspan="2">
                                        <p:outputLabel value="是否由用人单位赔付："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" rowspan="2">
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="享受工伤保险日期："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="2">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-10:c" converterMessage="享受工伤保险日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        />
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="是否享受基本医保："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="2">
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="其他保障情况："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title"  >
                                        <p:outputLabel value="尘肺患合并症情况："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="2">
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="既往是否患慢病阻肺病疾病（COPD）："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:selectOneRadio >
                                            <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                            <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                        </p:selectOneRadio>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" >
                                        <p:outputLabel value="具体合并症："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" colspan="2">
                                        <p:inputText value="" ></p:inputText>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="诊断日期："></p:outputLabel>
                                    </p:column>
                                    <p:column  style="padding-left: 8px;" >
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-10:c" converterMessage="诊断日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        />
                                    </p:column>
                                </p:row>
                            </p:panelGrid>

                        </div>
                    </p:outputPanel>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>