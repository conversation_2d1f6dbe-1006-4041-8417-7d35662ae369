<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar input {
                width: 186px;
            }

            .myCalendar2 input {
                width: 80px;
            }

            .aloneTitleTable {
                color: #334B9A;
                font-weight: bold;
                padding-left: 6px;
            }

            .column_title {
                width: 15% !important;
            }

            .column_content {
                width: 35% !important;
                padding-left: 8px !important;
            }

            .content_input {
                width: 186px !important;
            }

            .content_checkbox {
                padding: 0 3px;
            }

            table.ui-selectmanycheckbox tr {
                display: flex;
                align-items: center;
            }

            table.ui-selectmanycheckbox td {
                display: flex;
                align-items: center;
            }

            table.ui-selectmanycheckbox td label {
                white-space: normal;
                overflow: hidden;
            }

            .ui-state-default.th_required_header:before {
                content: '*';
                color: red;
            }

            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip > div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }
        </style>

        <script type="text/javascript">
            //<![CDATA[
            jQuery(document).ready(function () {
                windowScrollTop();
            });

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }

            //]]>
        </script>
    </h:head>
    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="职业卫生技术服务信息报送卡填报"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel styleClass="zwx_toobar_42" id="btnPanel">
                        <h:panelGrid columns="15" style="border-color:transparent;padding:0;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                            <p:commandButton value="暂存" icon="ui-icon-disk" process="@this"/>
                            <p:commandButton value="提交" icon="ui-icon-check" process="@this"/>
                            <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" process="@this"/>
                            <p:inputText style="visibility: hidden;width: 0"/>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:sticky target="btnPanel"/>
                    <p:outputPanel id="editPanel" styleClass="businessInfo"
                                   style="margin-top:40px;background: rgb(252, 253, 253);">
                        <!--文书-->
                        <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first"
                                     id="writeSortPanel">
                            <p:row>
                                <p:column colspan="2"><span>本环节文书</span></p:column>
                            </p:row>
                            <p:row>
                                <p:column style="padding: 0;" colspan="2">
                                    <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="width: 220px;">
                                    <h:outputText value="《职业卫生技术服务信息报送卡》"/>
                                </p:column>
                                <p:column>
                                    <p:outputPanel>
                                        <p:commandButton value="预览" process="@this"/>
                                        <p:spacer width="5"/>
                                        <p:commandButton value="上传" process="@this"/>
                                    </p:outputPanel>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                        <div id="occHethCard">
                            <!-- 标题 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;margin-bottom: 15px;">
                                <p:row>
                                    <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                                        <h:outputText value="职业卫生技术服务信息报送卡"
                                                      style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 机构信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="orgInfoPanel">
                                <p:row>
                                    <p:column colspan="4">
                                        <p:outputLabel value="机构信息" styleClass="aloneTitleTable"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="机构名称："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="法定代表人："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="注册地址："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="机构资质证书编号："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="项目负责人："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="联系电话："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="资质业务范围："/>
                                    </p:column>
                                    <p:column style="padding-left: 8px;" colspan="3">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 参与人员信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;"
                                         id="occhethCardPsnPanel">
                                <p:row>
                                    <p:column style="border-bottom-color: transparent;" colspan="2">
                                        <p:outputLabel value="参与人员信息"
                                                       style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"/>
                                        <p:commandButton value="添加" icon="ui-icon-plus" process="@this"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column colspan="2">
                                        <p:dataTable id="occhethCardPsnTable" paginatorPosition="bottom"
                                                     value="#{1}"
                                                     widgetVar="occhethCardPsnTable" var="occhethCardPsn"
                                                     emptyMessage="没有数据！" rowIndexVar="R"
                                                     styleClass="writeSortInfo1">
                                            <!--@elvariable id="R" type="java.lang.Integer"-->
                                            <p:column headerText="序号" style="width:40px;text-align: center;">
                                                <p:outputLabel value="1"/>
                                            </p:column>
                                            <p:column headerText="姓名" style="width:100px;text-align: center;">
                                                <p:outputLabel value="测试"/>
                                            </p:column>
                                            <p:column headerText="承担的服务事项"
                                                      style="width:600px;text-align: center;"
                                                      styleClass="th_required_header">
                                                <p:selectManyCheckbox columns="8" layout="grid" style="width: 460px;">
                                                    <f:selectItem itemValue="1" itemLabel="现场调查"/>
                                                    <f:selectItem itemValue="2" itemLabel="现场采样/检测"/>
                                                    <f:selectItem itemValue="3" itemLabel="实验室检测"/>
                                                    <f:selectItem itemValue="4" itemLabel="评价"/>
                                                </p:selectManyCheckbox>
                                            </p:column>
                                            <p:column headerText="操作">
                                                <p:commandLink value="删除" process="@this"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 服务的用人单位信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="crptInfoPanel">
                                <p:row>
                                    <p:column colspan="4">
                                        <p:outputLabel value="服务的用人单位信息" styleClass="aloneTitleTable"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="单位名称："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <div style="display: flex;align-items: center;">
                                            <p:inputText styleClass="content_input"/>
                                            <p:commandLink style="margin-left: -20px;"
                                                           styleClass="mysearch-icon ui-icon ui-icon-search"
                                                           onclick=""
                                                           partialSubmit="true" process="@this"/>
                                        </div>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="注册地址："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="联系人："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="联系电话："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="企业规模："/>
                                    </p:column>
                                    <p:column style="padding-left: 8px;" colspan="3">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" colspan="4" style="text-align: left;">
                                        <div style="display: flex;align-items: center;justify-content: space-between;padding: 5px 0;height: 32px;">
                                            <div>
                                                <p:commandButton value="添加" icon="ui-icon-plus" process="@this"/>
                                            </div>
                                            <div>
                                                <span class="ui-widget" style="color: red;">提示：</span>
                                                <span class="ui-widget"
                                                      style="color: blue;">技术服务机构地址与注册地址不一致的，请详细填写服务地址。</span>
                                            </div>
                                        </div>
                                        <div style="padding-right: 3px;">
                                            <p:panelGrid style="width:100%;" id="occhethCardZoneTable"
                                                         styleClass="writeSortInfo1">
                                                <p:row>
                                                    <p:column styleClass="ui-state-default th_required_header"
                                                              style="text-align:center;padding-right:3px;width:215px;height: 19px;">
                                                        <p:outputLabel value="技术服务地址"/>
                                                    </p:column>
                                                    <p:column styleClass="ui-state-default"
                                                              style="text-align:center;padding-right:3px;width:400px;height: 19px;">
                                                        <p:outputLabel value="详细地址"/>
                                                    </p:column>
                                                    <p:column styleClass="ui-state-default"
                                                              style="text-align:center;padding-right:3px;height: 19px;">
                                                        <p:outputLabel value="操作"/>
                                                    </p:column>
                                                </p:row>
                                                <p:row>
                                                    <p:column style="padding-right:3px;">
                                                        <div style="display: flex;align-items: center;">
                                                            <p:inputText styleClass="content_input"/>
                                                            <p:commandLink style="margin-left: -20px;"
                                                                           styleClass="mysearch-icon ui-icon ui-icon-search"
                                                                           partialSubmit="true" process="@this"/>
                                                        </div>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding:4px 10px;">
                                                        <p:inputText styleClass="content_input"/>
                                                    </p:column>
                                                    <p:column style="text-align:left;padding:4px 10px;">
                                                        <p:commandLink value="删除" process="@this"/>
                                                    </p:column>
                                                </p:row>
                                            </p:panelGrid>
                                        </div>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 技术服务信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicesInfoPanel">
                                <p:row>
                                    <p:column colspan="4">
                                        <p:outputLabel value="技术服务信息" styleClass="aloneTitleTable"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="技术服务领域："/>
                                    </p:column>
                                    <p:column style="padding-left: 8px;" colspan="3">
                                        <p:selectManyCheckbox columns="5" layout="grid">
                                            <f:selectItem itemValue="1" itemLabel="采矿业"/>
                                            <f:selectItem itemValue="2" itemLabel="化工、石化及医药"/>
                                            <f:selectItem itemValue="3" itemLabel="冶金、建材"/>
                                            <f:selectItem itemValue="4"
                                                          itemLabel="机械制造、电力、纺织、建筑和交通运输等行业领域"/>
                                            <f:selectItem itemValue="5" itemLabel="核设施"/>
                                            <f:selectItem itemValue="6" itemLabel="核技术工业应用"/>
                                        </p:selectManyCheckbox>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="现场调查时间："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    styleClass="myCalendar2"
                                                    showOtherMonths="true" navigator="true"
                                                    yearRange="c-10:c"
                                                    converterMessage="出具技术报告日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                        ~
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    styleClass="myCalendar2"
                                                    showOtherMonths="true" navigator="true"
                                                    yearRange="c-10:c"
                                                    converterMessage="出具技术报告日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="现场采样/检测时间："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    styleClass="myCalendar2"
                                                    showOtherMonths="true" navigator="true"
                                                    yearRange="c-10:c"
                                                    converterMessage="出具技术报告日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                        ~
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    styleClass="myCalendar2"
                                                    showOtherMonths="true" navigator="true"
                                                    yearRange="c-10:c"
                                                    converterMessage="出具技术报告日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="出具技术报告日期："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    styleClass="myCalendar2"
                                                    showOtherMonths="true" id="rptDate" navigator="true"
                                                    yearRange="c-10:c"
                                                    converterMessage="出具技术报告日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="报告编号："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column colspan="4">
                                        <p:outputLabel value="技术服务结果" styleClass="aloneTitleTable"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="writeSortInfo" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"/>
                                        <p:outputLabel value="职业病危害因素检测"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" colspan="4" style="text-align: left;">
                                        <div style="display: flex;align-items: center;justify-content: space-between;padding: 5px;">
                                            <p:commandButton value="添加" icon="ui-icon-plus" process="@this"/>
                                        </div>
                                        <div style="padding-right: 3px;">
                                            <p:panelGrid style="width:100%;" id="badrsnJcDataTable">
                                                <f:facet name="header">
                                                    <p:row>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:220px;">
                                                            <p:outputLabel value="职业病危害因素"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default "
                                                                  style="text-align:center;padding-right:3px;width:150px;">
                                                            <p:outputLabel value="岗位/工种数"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:220px;">
                                                            <p:outputLabel value="超标岗位/工种数"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:240px;">
                                                            <p:outputLabel value="超标岗位/工种名称"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:160px;">
                                                            <p:outputLabel value="超标检测项目"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:160px;">
                                                            <p:outputLabel value="超标检测指标"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:150px;">
                                                            <p:outputLabel value="超标值下限"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:150px;">
                                                            <p:outputLabel value="超标值上限"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:120px;">
                                                            <p:outputLabel value="计量单位"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:200px;">
                                                            <p:outputLabel value="操作"/>
                                                        </p:column>
                                                    </p:row>
                                                </f:facet>
                                                <p:row>
                                                    <p:column style="padding-right:3px;" colspan="10">
                                                        <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                                    </p:column>
                                                </p:row>
                                            </p:panelGrid>
                                        </div>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="writeSortInfo" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"/>
                                        <p:outputLabel value="职业病危害现状评价"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" colspan="4" style="text-align: left;">
                                        <div style="display: flex;align-items: center;justify-content: space-between;padding: 5px;">
                                            <p:commandButton value="添加" icon="ui-icon-plus" process="@this"/>
                                        </div>
                                        <div style="padding-right: 3px;">
                                            <p:panelGrid style="width:100%;" id="badrsnPjDataTable">
                                                <f:facet name="header">
                                                    <p:row>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:220px;">
                                                            <p:outputLabel value="职业病危害因素"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:150px;">
                                                            <p:outputLabel value="岗位/工种数"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:220px;">
                                                            <p:outputLabel value="超标岗位/工种数"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:240px;">
                                                            <p:outputLabel value="超标岗位/工种名称"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:160px;">
                                                            <p:outputLabel value="超标检测项目"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:160px;">
                                                            <p:outputLabel value="超标检测指标"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:150px;">
                                                            <p:outputLabel value="超标值下限"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:150px;">
                                                            <p:outputLabel value="超标值上限"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:120px;">
                                                            <p:outputLabel value="计量单位"/>
                                                        </p:column>
                                                        <p:column styleClass="ui-state-default"
                                                                  style="text-align:center;padding-right:3px;width:200px;">
                                                            <p:outputLabel value="操作"/>
                                                        </p:column>
                                                    </p:row>
                                                </f:facet>
                                                <p:row>
                                                    <p:column style="padding-right:3px;" colspan="10">
                                                        <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                                    </p:column>
                                                </p:row>
                                            </p:panelGrid>
                                        </div>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="writeSortInfo" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"/>
                                        <p:outputLabel value="职业病防护设备设施与防护用品的效果评价"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"
                                                                 style="margin-left: 20px;margin-top: 5px;"/>
                                        <p:outputLabel value="开展职业病防护设备设施防护效果检测，检测设备设施数量"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="台（套）"/>
                                        <br/>
                                        <p:outputLabel value="检测结果不合格的设备设施数量"
                                                       style="margin-left: 44px;margin-top: 5px;margin-bottom: 5px;"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="台（套），不合格的设备设施名称"/>
                                        <p:inputText style="margin: 0 5px;width: 300px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"
                                                                 style="margin-left: 20px;margin-top: 5px;"/>
                                        <p:outputLabel value="开展职业病防护用品防护效果检测，检测防护用品数量"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个（件）"/>
                                        <br/>
                                        <p:outputLabel value="结果不合格的防护用品数量"
                                                       style="margin-left: 44px;margin-top: 5px;margin-bottom: 5px;"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个（件）， 不合格的防护用品名称"/>
                                        <p:inputText style="margin: 0 5px;width: 300px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="writeSortInfo" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"/>
                                        <p:outputLabel value="职业性有害因素检测情况"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="width: 18% !important;">
                                        <p:outputLabel value="职业性有害因素："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="工作场所："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="岗位/工种："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="浓度（强度）："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="范围："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="检测时间："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="writeSortInfo" style="text-align: left;" colspan="4">
                                        <p:selectBooleanCheckbox value="#{true}" styleClass="content_checkbox"/>
                                        <p:outputLabel value="“三同时”职业病危害控制效果评价"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="项目职业病危害风险程度："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="职业病危害种类："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="评价机构名称："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="评价负责人姓名："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="评审专家："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="联系方式："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="评审时间："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    showOtherMonths="true" navigator="true"
                                                    yearRange="c-10:c" converterMessage="填表日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="评审结果："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="评审报告编号："/>
                                    </p:column>
                                    <p:column styleClass="column_content" colspan="3">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="检测结果："/>
                                    </p:column>
                                    <p:column styleClass="column_content" colspan="3" style="padding: 10px 0;">
                                        <p:outputLabel value="共检测岗位或工种数量"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，其中，职业病危害因素浓度/强度水平"/>
                                        <br/>
                                        <p:outputLabel value="超标岗位或工种数量"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，超标危害因素类型：粉尘"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，"/>
                                        <br/>
                                        <p:outputLabel value="化学因素"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，物理因素"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，放射因素"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，"/>
                                        <br/>
                                        <p:outputLabel value="生物因素"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个，其他因素"/>
                                        <p:inputText style="margin: 0 5px;width: 80px;"/>
                                        <p:outputLabel value="个"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 报告信息 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;" id="rptInfoPanel">
                                <p:row>
                                    <p:column colspan="4">
                                        <p:outputLabel value="报告信息" styleClass="aloneTitleTable"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="width: 18% !important;">
                                        <p:outputLabel value="填报单位："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="单位负责人："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="填表人："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="填表人联系电话："/>
                                    </p:column>
                                    <p:column styleClass="column_content">
                                        <p:inputText styleClass="content_input"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title">
                                        <h:outputText value="*" style="color:red;"/>
                                        <p:outputLabel value="填表日期："/>
                                    </p:column>
                                    <p:column style="padding-left: 8px;" colspan="3">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                                    showOtherMonths="true" id="fillDate" navigator="true"
                                                    yearRange="c-10:c" converterMessage="填表日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </div>
                    </p:outputPanel>
                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>