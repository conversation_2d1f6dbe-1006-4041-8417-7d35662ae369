<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.gxjtymys.CfbhzjzjzxxcxBean"-->
    <ui:param name="mgrbean" value="#{cfbhzjzjzxxcxBean}"/>
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar2 input {
                width: 78px;
            }
        </style>
    </h:head>
    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="放射卫生服务情况统计"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                                <p:commandButton value="分析" icon="ui-icon-search" process="@this"/>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
                                style="margin-top: 5px;margin-bottom: 5px;">
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;height: 37px;">
                                    <h:outputLabel value="地区："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                                        zoneCode="#{mgrbean.searchZoneCode}"
                                                        zoneName="#{mgrbean.searchZoneName}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px;">
                                    <h:outputLabel value="统计维度："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <p:selectOneRadio style="width: auto;">
                                        <f:selectItem itemLabel="年度" itemValue="1"/>
                                        <f:selectItem itemLabel="月度" itemValue="2"/>
                                        <f:selectItem itemLabel="年龄" itemValue="3"/>
                                        <f:selectItem itemLabel="地区" itemValue="4"/>
                                    </p:selectOneRadio>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px">
                                    <h:outputLabel value="报告日期："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchInputStartDate}"
                                                                  endDate="#{mgrbean.searchInputEndDate}"
                                                                  styleClass="myCalendar2"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{mgrbean.dataList1}"
                                 id="dataTable" emptyMessage="没有您要找的记录！">
                        <p:column headerText="地区" style="width: 5%; height:30px; text-align: center">
                            <h:outputText value="#{itm[0]}"/>
                        </p:column>
                        <p:column headerText="用人单位数量" style="width: 4%;text-align: center;">
                            <h:outputText value="#{itm[1]}"/>
                        </p:column>
                        <p:column headerText="职业病危害因素检测报告数" style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[2]}"/>
                        </p:column>
                        <p:column headerText="完成放射病危害预评价项目数"
                                  style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[3]}"/>
                        </p:column>
                        <p:column headerText="完成放射病危害防护设施设计项目数"
                                  style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[4]}"/>
                        </p:column>
                        <p:column headerText="完成放射病危害控制效果评价项目数"
                                  style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[5]}"/>
                        </p:column>
                    </p:dataTable>
                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>