<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar2 input {
                width: 180px;
            }
        </style>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="化学品毒性鉴定报告查询"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton value="查询" icon="ui-icon-search" process="@this" />

                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" >
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;height: 37px;">
                                    <h:outputLabel value="地区："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px;">
                                    <h:outputLabel value="鉴定机构："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px">
                                    <h:outputLabel value="姓名："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;width:150px;height: 37px;">
                                    <h:outputLabel value="业务类型："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px;">
                                    <h:outputLabel value="报告日期："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:250px;">
                                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                                showOtherMonths="true"  size="11" navigator="true"
                                                yearRange="c-10:c+10" converterMessage="报告日期，格式输入不正确！"
                                                showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar2"  />
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:200px">
                                    <h:outputLabel value="身份证号："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:inputText style="width: 180px;"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{diagProcessNodeManageBean.chemicalDataList}" paginator="true" rows="20" rowIndexVar="R"
                                 id="dataTable" emptyMessage="没有您要找的记录！" paginatorPosition="bottom"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{diagProcessNodeManageBean.perPageSize}"  currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                    >
                        <p:column headerText="序号" style="width: 10%; height:30px; text-align: center">
                            <h:outputText value="#{itm[0]}" />
                        </p:column>
                        <p:column headerText="地区" style="width: 20%;text-align: center;">
                            <h:outputText value="#{itm[1]}" />
                        </p:column>
                        <p:column headerText="鉴定机构" style="width: 15%;padding-left: 10px;">
                            <h:outputText value="#{itm[2]}" />
                        </p:column>
                        <p:column headerText="姓名" style="width: 15%;text-align: center;">
                            <h:outputText value="#{itm[3]}" />
                        </p:column>
                        <p:column headerText="中毒原因" style="width: 20%;padding-left: 10px;">
                            <h:outputText value="#{itm[4]}" />
                        </p:column>
                        <p:column headerText="状态" style="width: 10%;text-align: center;">
                            <h:outputText value="#{itm[5]}" />
                        </p:column>
                        <p:column headerText="操作" style="padding-left: 10px;">
                            <p:commandLink value="详情" process="@this"   />
                        </p:column>
                    </p:dataTable>

                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>