<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .myCalendar input {
                width: 160px;
            }
        </style>
    </h:head>
    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <p:tabView id="tabView" dynamic="true" cache="true" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="放射诊疗机构检测受理"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>

                    <p:outputPanel id="buttonsPanel">
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="5" style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                                <p:commandButton value="暂存" icon="ui-icon-disk" process="@this"/>
                                <p:commandButton value="提交" icon="ui-icon-check" process="@this"/>
                                <p:commandButton value="退出" icon="ui-icon-arrowreturnthick-1-w" process="@this"/>
                                <p:inputText style="visibility: hidden;width: 0"/>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:outputPanel>

                    <p:outputPanel styleClass="businessInfo">
                        <div id="writ_yszyb">
                            <!-- 标题 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                                <p:row>
                                    <p:column styleClass="noBorder" style="text-align: center;padding: 20px 0;" colspan="3">
                                        <h:outputText value="检测受理申请"
                                                      style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <!-- 表单 -->
                            <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;width:170px">
                                        <p:outputLabel value="地区："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title" style="width: 170px;">
                                        <p:outputLabel value="单位名称："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="详细地址："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                        <p:inputText style="width:569px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="机构类型："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="检测类别："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="选择检测单位："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="检测名称："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column styleClass="column_title" style="height:30px;">
                                        <p:outputLabel value="申请人："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:inputText style="width:160px"/>
                                    </p:column>
                                    <p:column styleClass="column_title">
                                        <p:outputLabel value="申请日期："/>
                                    </p:column>
                                    <p:column style="text-align:left;padding-left:11px;">
                                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                                    showOtherMonths="true" size="11" navigator="true"
                                                    yearRange="c-200:c" converterMessage="申请日期，格式输入不正确！"
                                                    showButtonPanel="true" readonlyInput="true" maxdate="new Date()"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <div style="padding: 50px;">
                            </div>
                        </div>
                    </p:outputPanel>
                </h:form>
            </p:tab>
        </p:tabView>
    </h:body>
</f:view>
</html>