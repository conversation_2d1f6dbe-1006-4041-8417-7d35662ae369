<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 标题 -->
    <h:head>
        <title><h:outputText value="系统单位信息" /></title>
        <h:outputStylesheet name="css/default.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css" />
        <h:outputScript name="js/namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style type="text/css">
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function removeExtraZonePael(elStr){
                //移除掉多余的地区框
                var el = jQuery(elStr);
                if(el.length>1){
                    el.each(function(index){
                        if(index>0){
                            $(this).remove();
                        }
                    });
                }
            }
            //]]>
        </script>
    </h:head>

    <h:body>
        <h:form id="selectForm">
            <div style="float: left;font-size: 12px;width: 100%;">
                <!-- 查询条件 -->
                <table style="width: 100%">
                    <tr>
                        <td style="text-align: right;width: 45px;" class="zwx_dialog_font">
                            <h:outputText value="地区："/>
                        </td>
                        <td style="text-align:left;width: 100px;">
                            <zwx:ZoneSingleNewComp zoneList="#{unitCommSelectListBean.zoneList}"
                                                   zoneCode="#{unitCommSelectListBean.searchZoneCode}"
                                                   zoneId="#{unitCommSelectListBean.searchZoneId}" zonePaddingLeft="0"
                                                   zoneName="#{unitCommSelectListBean.searchZoneName}" id="searchZone"
                                                   ifShowTrash="false"/>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="单位名称："/>
                        </td>
                        <td style="text-align: left;vertical-align: middle;width: 120px;">
                            <p:inputText maxlength="50" style="width:180px;" id="searchCrptName" value="#{unitCommSelectListBean.searchUnitName}">
                            </p:inputText>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="社会信用代码："/>
                        </td>
                        <td style="text-align: left;width: 120px;">
                            <p:inputText id="searchCrptCode" value="#{unitCommSelectListBean.searchCreditCode}" maxlength="25" style="width:180px;">
                            </p:inputText>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this,searchZone,searchCrptName,searchCrptCode" value="查询"
                                             action="#{unitCommSelectListBean.searchAction}"
                                             resetValues="selectForm:unitCommTable"
                                             update="selectForm:unitCommTable"/>
                        </td>
                        <td style="text-align: left;width: 60px;">
                            <p:commandButton process="@this,addUnitCommPanel" value="添加"
                                             action="#{unitCommSelectListBean.addUnitCommAction}"
                                             resetValues="true" oncomplete="removeExtraZonePael('#selectForm\\:areaZone\\:zonePanel');removeExtraZonePael('#selectForm\\:zone\\:zonePanel')"
                                             />
                        </td>
                    </tr>
                </table>

                <!-- 表格 -->
                <p:dataTable var="unitComm" value="#{unitCommSelectListBean.unitCommList}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="10,20,50" id="unitCommTable" lazy="true" emptyMessage="没有您要找的记录！"
                             rowKey="#{unitComm.rid}" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                    <p:column headerText="选择" style="width:70px;text-align:center;">
                        <p:commandLink value="选择" process="@this" action="#{unitCommSelectListBean.selectUnitCommAction}">
                            <f:setPropertyActionListener target="#{unitCommSelectListBean.selectUnitComm}" value="#{unitComm}"/>
                        </p:commandLink>
                        <p:spacer width="5" />
                        <p:commandLink process="@this" value="修改" oncomplete="removeExtraZonePael('#selectForm\\:areaZone\\:zonePanel');removeExtraZonePael('#selectForm\\:zone\\:zonePanel')"
                                       action="#{unitCommSelectListBean.modUnitCommAction}"
                                       resetValues="true"
                        >
                            <f:setPropertyActionListener target="#{unitCommSelectListBean.unitCommId}" value="#{unitComm.rid}"/>
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="地区" width="180px;">
                        <h:outputText value="#{unitComm.tsZone.fullName}" rendered="#{2 >= unitComm.tsZone.realZoneType}"/>
                        <h:outputText value="#{fn:substringAfter(unitComm.tsZone.fullName,'_')}" rendered="#{unitComm.tsZone.realZoneType > 2}"/>
                    </p:column>
                    <p:column headerText="单位名称"  width="230px;">
                        <h:outputText value="#{unitComm.unitname}"/>
                    </p:column>
                    <p:column headerText="社会信用代码" width="90px;" style="text-align: center;">
                        <h:outputText value="#{unitComm.creditCode}"/>
                    </p:column>
                    <p:column headerText="是否分支机构" width="90px;" style="text-align: center;" rendered="#{unitCommSelectListBean.ifHasSubOrg}">
                        <h:outputText value="否" rendered="#{'0' eq unitComm.ifSubOrg}"/>
                        <h:outputText value="是" rendered="#{'1' eq unitComm.ifSubOrg}"/>
                    </p:column>
                    <p:column headerText="单位地址" width="210px;">
                        <h:outputLabel value="#{unitComm.unitaddr}" id="tips0" styleClass="zwx-tooltip"/>
                        <p:tooltip for="tips0" style="max-width:230px;">
                            <p:outputLabel value="#{unitComm.unitaddr}" escape="false"></p:outputLabel>
                        </p:tooltip>
                    </p:column>
                </p:dataTable>

                <!-- 添加企业信息—各单位独立信息弹出框 -->
                <p:dialog header="系统单位信息编辑" widgetVar="addUnitComm" id="addUnitCommDialog" width="600" height="380" modal="true" resizable="false">
                    <p:remoteCommand name="checkCptNameAndCode" action="#{unitCommSelectListBean.checkCptNameAndCode}" process="@this,unitname,ifSubOrg,creditCode"/>
                    <p:panelGrid style="width:100%;margin-bottom:5px;" id="addUnitCommPanel">
                        <p:row>
                            <p:column style="text-align: right;width: 35%;height: 30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="行政区划所属地区："/>
                            </p:column>
                            <p:column style="padding:0" styleClass="areaZone">
                                <zwx:ZoneSingleNewComp zoneList="#{unitCommSelectListBean.zoneList}"
                                                       zoneId="#{unitCommSelectListBean.editZoneId}"  id="zone" zonePaddingLeft="13"
                                                       zoneName="#{unitCommSelectListBean.editZoneName}" zoneWidth="179" zoneHeight="300"
                                                       zoneCode="#{unitCommSelectListBean.editZoneCode}"  />
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;height: 30px;">
                                <h:outputText value="*" style="color:red;" />
                                <h:outputText value="业务管辖地区："/>
                            </p:column>
                            <p:column style="padding:0" styleClass="areaZone">
                                <zwx:ZoneSingleNewComp zoneList="#{unitCommSelectListBean.manageZoneList}"
                                                       zoneId="#{unitCommSelectListBean.editManageZoneId}"  id="areaZone" zonePaddingLeft="13"
                                                       zoneName="#{unitCommSelectListBean.editManageZoneName}" zoneWidth="179" zoneHeight="300"
                                                       zoneCode="#{unitCommSelectListBean.editManageZoneCode}"  />
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="*" style="color:red;" />
                                <h:outputText value="单位名称："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText maxlength="50" value="#{unitCommSelectListBean.editUnitComm.unitname}"
                                             id="unitname" style="width:179px"  />
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <p:outputPanel id="test">
                                    <h:outputText value="*" style="color:red;" />
                                    <h:outputText value="社会信用代码："/>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="padding-left: 13px;height:30px;">
                                <p:inputText id="creditCode" maxlength="25" onblur="checkCptNameAndCode()"
                                             value="#{unitCommSelectListBean.editUnitComm.creditCode}" style="width:179px" />
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="*" style="color:red;" />
                                <h:outputText value="单位简称："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText maxlength="50" value="#{unitCommSelectListBean.editUnitComm.unitSimpname}"
                                             id="unitName" style="width:179px" />
                            </p:column>
                        </p:row>

                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="单位地址："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{unitCommSelectListBean.editUnitComm.unitaddr}" maxlength="50" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{unitCommSelectListBean.ifHasSubOrg}">
                            <p:column style="text-align: right;height:30px;" >
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="是否分支机构："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:selectOneRadio  style="width:120px;" id="ifSubOrg"  value="#{unitCommSelectListBean.editUnitComm.ifSubOrg}">
                                    <f:selectItem itemLabel="否" itemValue="0" />
                                    <f:selectItem itemLabel="是" itemValue="1" />
                                </p:selectOneRadio>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="法定代表人："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{unitCommSelectListBean.editUnitComm.orgFz}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{unitCommSelectListBean.editUnitComm.unittel}" maxlength="15" style="width:179px"/>
                            </p:column>
                        </p:row>

                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="联系人："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{unitCommSelectListBean.editUnitComm.linkMan}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>

                        <p:row>
                            <p:column style="text-align: right;height:30px;">
                                <h:outputText value="联系人电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{unitCommSelectListBean.editUnitComm.orgTel}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <f:facet name="footer">
                        <h:panelGrid style="width: 100%;text-align: right;">
                            <h:panelGroup>
                                <p:commandButton value="保存"
                                                 process="@this,addUnitCommPanel"
                                                 action="#{unitCommSelectListBean.saveUnitCommAction}"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="取消" type="button" process="@this"
                                                 onclick="PF('addUnitComm').hide();"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </f:facet>
                </p:dialog>

                <p:confirmDialog message="#{unitCommSelectListBean.message}" id="confirmDialog" header="消息确认框" widgetVar="ConfirmDialog" style="text-align: center">
                    <p:outputPanel style="text-align: right;">
                        <p:commandButton value="确定" action="#{unitCommSelectListBean.saveAction}" icon="ui-icon-check" process="@this,:selectForm" oncomplete="PF('ConfirmDialog').hide();" update=":selectForm" />
                        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
                    </p:outputPanel>
                </p:confirmDialog>
            </div>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    </h:body>
</f:view>
</html>
