<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
    <h:head>
        <title>#{orgSelectListBean.titleName}</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <script type="text/javascript">
            //<![CDATA[
            document.onkeypress = function(event){
				if(event.keyCode == 13) {
					submit();
					return false;
				}
			};           
            //]]>
        </script>
    </h:head>

    <h:body >
        <h:form id="form">
		<p:remoteCommand name="submit"
			actionListener="#{orgSelectListBean.searchAction}" process="@this,form"/>
        <p:outputPanel id="searchPanel" styleClass="zwx_dialog_font" style="padding-bottom: 3px;">
            <table width="100%">
                <tr>
                    <td>
                        <h:panelGrid columns="5">
                            <p:outputLabel value="地区：" />
                            <zwx:ZoneSingleNewComp zoneList="#{orgSelectListBean.zoneList}"
                                                   zoneCodeNew="#{orgSelectListBean.searchZoneCode}"
                                                   zoneName="#{orgSelectListBean.searchZoneName}"
                                                   zonePaddingLeft="0" id="searchZone" zoneHeight="340"
                                                   onchange="onSearchNodeSelect()"/>
                            <p:remoteCommand name="onSearchNodeSelect" action="#{orgSelectListBean.searchAction}"
                                             process="@this,searchZone" update="dataTable"/>
                            <p:outputLabel value="机构名称：" />
                            <p:inputText id="orgName" value="#{orgSelectListBean.orgName}"  style="width: 170px;" maxlength="50"/>
                            <p:ajax event="keyup" update="dataTable" process="@this,@parent" listener="#{orgSelectListBean.searchAction}" />
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            </p:outputPanel>
          	  <p:dataTable var="itm" value="#{orgSelectListBean.sendFsDataModel}"
          	  			 id="dataTable" selectionMode="single"
                         paginatorPosition="bottom"
                         paginator="true" rows="10" emptyMessage="没有数据！" lazy="true"
						 rowsPerPageTemplate="10"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
	                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowKey="#{itm}">
                  <p:column headerText="选择" style="width:50px;text-align:center;">
                      <p:commandLink value="选择" process="@this" action="#{orgSelectListBean.selectOrgAction}">
                          <f:setPropertyActionListener target="#{orgSelectListBean.selectPro}" value="#{itm}"/>
                      </p:commandLink>
                  </p:column>
                <p:column headerText="地区" style="width: 250px;">
                    <h:outputText value="#{itm[1]}" />
                </p:column>
                <p:column headerText="机构名称" >
                    <h:outputText value="#{itm[2]}" />
                </p:column>
            </p:dataTable>

        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    </h:body>
</f:view>
</html>
