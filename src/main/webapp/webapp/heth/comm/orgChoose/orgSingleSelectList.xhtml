<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
    <h:head>
        <title>#{orgSingleSelectListBean.titleName}</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <script type="text/javascript">
            //<![CDATA[
            document.onkeypress = function (event) {
                if (event.keyCode === 13) {
                    submit();
                    return false;
                }
            };
            //]]>
        </script>
    </h:head>

    <h:body>
        <h:form id="orgForm">
            <p:remoteCommand name="submit"
                             actionListener="#{orgSingleSelectListBean.searchAction}" process="@this,orgForm"/>
            <p:outputPanel id="searchPanel" styleClass="zwx_dialog_font" style="padding-bottom: 3px;">
                <h:panelGrid columns="5">
                    <p:outputLabel value="地区："/>
                    <zwx:ZoneSingleNewComp zoneList="#{orgSingleSelectListBean.zoneList}"
                                           zoneCodeNew="#{orgSingleSelectListBean.searchZoneCode}"
                                           zoneName="#{orgSingleSelectListBean.searchZoneName}"
                                           zonePaddingLeft="0" id="searchZone" zoneHeight="340"
                                           onchange="onSearchNodeSelect()"/>
                    <p:remoteCommand name="onSearchNodeSelect"
                                     action="#{orgSingleSelectListBean.searchAction}"
                                     process="@this,searchZone" update="dataTable"/>
                    <p:outputLabel value="机构名称："/>
                    <p:inputText id="orgName" value="#{orgSingleSelectListBean.orgName}"
                                 style="width: 170px;" maxlength="50"/>
                    <p:ajax event="keyup" update="dataTable" process="@this,@parent"
                            listener="#{orgSingleSelectListBean.searchAction}"/>
                </h:panelGrid>
            </p:outputPanel>
            <p:dataTable var="itm" value="#{orgSingleSelectListBean.showDataList}"
                         id="dataTable" rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5"
                         paginatorPosition="bottom" paginator="true" rows="10" emptyMessage="没有数据！" lazy="true"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowKey="#{itm}">
                <p:column headerText="选择" style="width:50px;text-align:center;">
                    <p:commandLink value="选择" process="@this" action="#{orgSingleSelectListBean.selectOrgAction}">
                        <f:setPropertyActionListener target="#{orgSingleSelectListBean.selectData}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="地区" style="width: 200px;">
                    <h:outputText value="#{itm[2]}"/>
                </p:column>
                <p:column headerText="机构名称">
                    <h:outputText value="#{itm[3]}"/>
                </p:column>
            </p:dataTable>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    </h:body>
</f:view>
</html>
