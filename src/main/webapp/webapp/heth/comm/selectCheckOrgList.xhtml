<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>#{request.getParameter("title")==null?'选择单位':request.getParameter("title")}</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>
    </h:head>

    <h:body>
        <h:form id="codeForm">
        	<h:outputStylesheet name="css/ui-tabs.css"/>
		    <h:panelGrid columns="10" id="searchPanel">
		    	<p:inputText style="visibility: hidden;width: 0"/>
		    	<p:outputLabel value="地区：" styleClass="zwx_dialog_font" />
		    	<zwx:ZoneSingleComp zoneList="#{selectCheckOrgCommListBean.zoneList}"
                                    zoneCode="#{selectCheckOrgCommListBean.searchZoneCode}"
                                    zoneName="#{selectCheckOrgCommListBean.searchZoneName}" 
                                    onchange="onSearchNodeSelect()"
                                    panelHeight="300"
                                    height="280"
                                    id="searchZone"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{selectCheckOrgCommListBean.searchAction}" 
                         		process="@this,searchZone" update="dataTable"/>
                <p:outputLabel value="#{null eq selectCheckOrgCommListBean.searchUnitName?'单位名称':selectCheckOrgCommListBean.searchUnitName}：" styleClass="zwx_dialog_font" />
                <p:inputText id="pym" value="#{selectCheckOrgCommListBean.searchName}" style="width: 180px;" maxlength="20">
                    <p:ajax event="keyup" update="dataTable" process="@this,searchPanel" listener="#{selectCheckOrgCommListBean.searchAction}"/>
                </p:inputText>
            </h:panelGrid>
            <p:dataTable var="itm" value="#{selectCheckOrgCommListBean.dataModel}" id="dataTable"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorPosition="bottom" lazy="true" rowsPerPageTemplate="#{'10,20,50'}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                <p:column headerText="选择" style="width:80px;text-align:center;">
                    <p:commandLink value="选择" process="@this" action="#{selectCheckOrgCommListBean.submitAction}">
                        <f:setPropertyActionListener target="#{selectCheckOrgCommListBean.selectPro}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="地区" style="padding-left: 3px;width:240px;">
                    <h:outputText escape="false" value="#{itm[1]}" />
                </p:column>
                <p:column headerText="#{null eq selectCheckOrgCommListBean.searchUnitName?'单位名称':selectCheckOrgCommListBean.searchUnitName}" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{itm[2]}" />
                </p:column>
            </p:dataTable>
        </h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>
