<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/viewTemplate.xhtml">
	<ui:param name="pageParam" value=""></ui:param>
	<ui:param name="birthIfShow" value="false"></ui:param>
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="重点危害因素检查情况查询"/>
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
		        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
						action="#{mgrbean.backAction}" process="@this"
						update=":tabView" />
					<p:inputText style="visibility: hidden;width: 0"/>

		    </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;" rendered="#{!mgrbean.ifBhkInfoExist}">
                <h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
                <h:outputLabel value="该数据已被体检机构删除，请确认！" style="color:blue;"></h:outputLabel>
            </p:outputPanel>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGrid1">
		   	<f:facet name="header">
		        <p:row>
		            <p:column colspan="6" style="text-align:left;height: 20px;">
		                <p:outputLabel value="判定结论"/>
		            </p:column>
		        </p:row>
		    </f:facet>
			<p:row rendered="#{mgrbean.newFlow.state == 2 or mgrbean.newFlow.state == 4 }">
				<!-- 地区级别不是2 -->
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="退回原因：" style="color: red;" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;color: red;" rendered="#{mgrbean.newFlow.state == 2}"
									 value="#{mgrbean.newFlow.cityAuditAdv}"
									 maxlength="100"
									 />
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;color: red;" rendered="#{mgrbean.newFlow.state == 4}"
									 value="#{mgrbean.newFlow.proAuditAdv}"
									 maxlength="100"
					/>
				</p:column>
			</p:row>
			<p:row  rendered="#{mgrbean.newFlow.state == 2}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="复审机构：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByCiytChkOrgid.unitname}"  style="color: red;"></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="复审日期：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.citySmtDate}"  style="color: red;"></h:outputText>
				</p:column>
			</p:row>
			<p:row  rendered="#{mgrbean.newFlow.state == 4}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="终审机构：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByProChkOrgid.unitname}" style="color: red;"></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="终审日期：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.proSmtDate}"  style="color: red;"></h:outputText>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 30px;width: 200px;">
					<p:outputLabel value="结论是否正常：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3" >
					<p:outputLabel value="是" rendered="#{mgrbean.newFlow.ifNormal==1}"></p:outputLabel>
					<p:outputLabel value="否" rendered="#{mgrbean.newFlow.ifNormal==0}"></p:outputLabel>
				</p:column>
			</p:row>
			<p:row  rendered="#{(mgrbean.ifCityDirect and mgrbean.newFlow.state > 3) or (!mgrbean.ifCityDirect and mgrbean.newFlow.state > 1)}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="初审意见：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;"
									 maxlength="100" value="#{mgrbean.newFlow.countyAuditAdv}"
					/>
				</p:column>
			</p:row>

			<p:row rendered="#{(mgrbean.ifCityDirect and mgrbean.newFlow.state > 3) or (!mgrbean.ifCityDirect and mgrbean.newFlow.state > 1)}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="初审机构：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByCountyChkOrgid.unitname}"  ></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="初审日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.countySmtDate}"  ></h:outputText>
				</p:column>
			</p:row>
			<p:row rendered="#{!mgrbean.ifCityDirect and mgrbean.newFlow.state > 3}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="复审意见：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;"
									 maxlength="100" value="#{mgrbean.newFlow.cityAuditAdv}"
					/>
				</p:column>
			</p:row>
			<p:row rendered="#{!mgrbean.ifCityDirect and mgrbean.newFlow.state > 3}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="复审机构：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByCiytChkOrgid.unitname}" ></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="复审日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.citySmtDate}" ></h:outputText>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.newFlow.state ==6}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="终审意见：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;"
									 maxlength="100" value="#{mgrbean.newFlow.proAuditAdv}"
					/>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.newFlow.state ==6}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="终审机构：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByProChkOrgid.unitname}" ></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="终审日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.proSmtDate}" ></h:outputText>
				</p:column>
			</p:row>
	    </p:panelGrid>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6" style="text-align:left;height: 20px;">
						<p:outputLabel value="判定情况"/>
					</p:column>
				</p:row>
			</f:facet>
			<p:row rendered="#{null !=mgrbean.zdzybBadRsnInfoList and mgrbean.zdzybBadRsnInfoList.size()>0}">
				<p:column style="text-align:right;padding-right:3px;height: 30px;width:200px;">
					<p:outputLabel value="在岗状态：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:outputLabel value="#{mgrbean.tdZwyjBsnBhk.fkByOnguardStateid.codeName}"/>
				</p:column>
			</p:row>
			<c:forEach items="#{mgrbean.zdzybBadRsnInfoList}" var="data" varStatus="rowIndex">
				<p:row>
					<p:column  style="text-align: right;height: 30px;">
						<h:outputText value="危害因素："></h:outputText>
					</p:column>
					<p:column  style="text-align: left;width:300px;">
						<h:outputText value="#{data.badName}"></h:outputText>
					</p:column>
					<p:column  style="text-align: right;width:200px;">
						<h:outputText value="实际结论："></h:outputText>
					</p:column>
					<p:column  style="text-align: left;">
						<h:outputText value="#{data.finalConslusion}"></h:outputText>
					</p:column>
				</p:row>
                <p:row>
                    <p:column  style="text-align: center;height: 30px;text-align: right;">
                        <h:outputText value="异常项目："></h:outputText>
                    </p:column>
                    <p:column colspan="3">
                        <p:outputPanel styleClass="div-layout" >
                            <p:panelGrid  style="width:100%;height:100%;">
                                <p:row>
                                    <p:column styleClass="ui-state-default" style="text-align: center;height: 30px;width: 242px">
                                        <h:outputText value="项目名称"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 290px">
                                        <h:outputText value="体检结果"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 116px">
                                        <h:outputText value="参考值"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 100px">
                                        <h:outputText value="计量单位"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 200px">
                                        <h:outputText value="判定标准"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 400px">
                                        <h:outputText value="建议结论"></h:outputText>
                                    </p:column>
                                </p:row>
                                <c:forEach items="#{data.zdzybItemRstList}" var="item" varStatus="itemIndex">
                                    <p:row>
                                        <p:column style="text-align: center;height:25px;">
                                            <h:outputText value="#{item.itemName}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.result}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.stdValue}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.msrunt}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.pdStd}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;" rendered="#{item.index != 0}" rowspan="#{item.index}">
                                            <h:outputText value="#{item.conslusion}"></h:outputText>
                                        </p:column>
                                    </p:row>
                                </c:forEach>
                            </p:panelGrid>
                        </p:outputPanel>
                    </p:column>
                </p:row>
			</c:forEach>
			<p:row rendered="#{mgrbean.ifAbnormal}">
				<p:column style="text-align:right;padding-right:3px;height: 30px;width:200px;">
					<p:outputLabel value="其他情况：" rendered="#{null !=mgrbean.zdzybBadRsnInfoList and mgrbean.zdzybBadRsnInfoList.size()>0}"/>
					<p:outputLabel value="判定信息：" rendered="#{null ==mgrbean.zdzybBadRsnInfoList or mgrbean.zdzybBadRsnInfoList.size()==0}"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:outputLabel value="#{mgrbean.abnormalInfo}"/>
				</p:column>
			</p:row>
		</p:panelGrid>
	    <ui:include src="tbTjBhkInfo.xhtml">
			<ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
		</ui:include>
	</ui:define>
</ui:composition>