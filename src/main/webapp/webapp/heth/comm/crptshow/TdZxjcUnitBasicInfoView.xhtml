<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>
    <style type="text/css">
        .ms-result-dot-true {
            width: 10px;
            height: 10px;
            background: #46bc15;
            border-radius: 50%;
            display: inline-block;
            margin-right: 4px;
        }
        .ms-result-dot-false {
            width: 10px;
            height: 10px;
            background: red;
            border-radius: 50%;
            display: inline-block;
            margin-right: 4px;
        }
    </style>
    <h:body>
        <p:outputPanel style="border-radius: 6px;background-color: #ffffff;">
            <h:form id="mainForm">
                <h:outputStylesheet name="css/default.css"/>
                <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
                <h:outputStylesheet name="css/ui-tabs.css"/>

                <ui:param name="mgrbean" value="#{tdZxjcUnitBasicInfoViewBean}"/>
                    <p:panelGrid style="width:100%;height:100%;">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="场所监测档案"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:fieldset legend="基本信息" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="单位名称："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.unitName}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:217px;">
                                    <p:outputLabel value="统一社会信用代码："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.creditCode}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:220px;">
                                    <p:outputLabel value="所属地区："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.fkByZoneId.fullName}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                                    <p:outputLabel value="所属行业："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.fkByIndustryId.codeName}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;">
                                    <p:outputLabel value="经济类型："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.fkByEconomicId.codeName}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;">
                                    <p:outputLabel value="用人单位规模："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.fkByEnterpriseScaleId.codeName}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                                    <p:outputLabel value="本单位在册职工总数："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.empNum}"/>
                                    <p:outputLabel value="人" rendered="#{null ne mgrbean.jcUnitBasicInfo.empNum}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;">
                                    <p:outputLabel value="外委人员数量："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;" colspan="3">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.externalNum}"/>
                                    <p:outputLabel value="人" rendered="#{null ne mgrbean.jcUnitBasicInfo.externalNum}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 30px;">
                                    <div style="display:flex;justify-content: flex-start;margin-left: 20px;color: #135AAD;font-weight: bold;">
                                        <h:outputText value="职业卫生培训情况："/>
                                    </div>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="用人单位负责人培训情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.jcUnitBasicInfo.ifLeadersTrain}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.jcUnitBasicInfo.ifLeadersTrain}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifLeadersTrain}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:217px;">
                                    <p:outputLabel value="职业卫生管理人员培训情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.jcUnitBasicInfo.ifManagersTrain}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.jcUnitBasicInfo.ifManagersTrain}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifManagersTrain}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:230px;">
                                    <p:outputLabel value="接触职业病危害劳动者培训人数："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel
                                            value="#{null eq mgrbean.jcUnitBasicInfo.trainSum?'/':mgrbean.jcUnitBasicInfo.trainSum}"/>
                                    <p:outputLabel value="人" rendered="#{null!=mgrbean.jcUnitBasicInfo.trainSum}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 30px;">
                                    <div style="display:flex;justify-content: flex-start;margin-left: 20px;color: #135AAD;font-weight: bold;">
                                        <h:outputText value="职业病危害项目申报情况："/>
                                    </div>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="是否进行了申报："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.jcUnitBasicInfo.ifDeclare}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.jcUnitBasicInfo.ifDeclare}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifDeclare}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:217px;">
                                    <p:outputLabel value="是否进行了年度更新："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.jcUnitBasicInfo.ifAnnualUpdate}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.jcUnitBasicInfo.ifAnnualUpdate}"/>
                                    <p:outputLabel value="/"  rendered="#{null eq mgrbean.jcUnitBasicInfo.ifAnnualUpdate}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 30px;">
                                    <div style="display:flex;justify-content: flex-start;margin-left: 20px;color: #135AAD;font-weight: bold;">
                                        <h:outputText value="防护设施“三同时”情况："/>
                                    </div>
                                </p:column>
                            </p:row>

                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="上一年度新改扩建及技术改造、引进项目情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.jcUnitBasicInfo.ifImport}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.jcUnitBasicInfo.ifImport}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifImport}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:217px;">
                                    <p:outputLabel value="当前工作阶段："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.workNode}"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.workNode}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.workNode}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:230px;">
                                    <p:outputLabel value="预评价开展情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="全部"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifPreLaunch and mgrbean.jcUnitBasicInfo.ifPreLaunch==1}"/>
                                    <p:outputLabel value="部分"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifPreLaunch and mgrbean.jcUnitBasicInfo.ifPreLaunch==2}"/>
                                    <p:outputLabel value="无"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifPreLaunch and mgrbean.jcUnitBasicInfo.ifPreLaunch==3}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifPreLaunch}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="职业病防护设施设计专篇："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="全部"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifDesign and mgrbean.jcUnitBasicInfo.ifDesign==1}"/>
                                    <p:outputLabel value="部分"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifDesign and mgrbean.jcUnitBasicInfo.ifDesign==2}"/>
                                    <p:outputLabel value="无"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifDesign and mgrbean.jcUnitBasicInfo.ifDesign==3}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifDesign}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;width:217px;">
                                    <p:outputLabel value="控制效果评价开展情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="全部"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifLaunch and mgrbean.jcUnitBasicInfo.ifLaunch==1}"/>
                                    <p:outputLabel value="部分"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifLaunch and mgrbean.jcUnitBasicInfo.ifLaunch==2}"/>
                                    <p:outputLabel value="无"
                                                   rendered="#{null ne mgrbean.jcUnitBasicInfo.ifLaunch and mgrbean.jcUnitBasicInfo.ifLaunch==3}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifLaunch}"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="职业病危害因素种类及接触情况" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="接触职业病危害因素总人数："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="5">
                                    <p:outputLabel value="#{mgrbean.unitFactorcrowd[1]}人"
                                                   rendered="#{null ne mgrbean.unitFactorcrowd and null ne mgrbean.unitFactorcrowd[0]}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 30px;">
                                    <div style="display:flex;justify-content: flex-start;margin-left: 20px;color: #135AAD;font-weight: bold;">
                                        <h:outputText value="粉尘种类：#{mgrbean.unitFactorItemMap.get('1').size()>0?'有':'无'}"/>
                                    </div>
                                </p:column>
                            </p:row>
                            <c:if test="#{mgrbean.unitFactorItemMap.get('1').size()>0}">
                                <c:forEach items="#{mgrbean.unitFactorItemMap.get('1')}" var="items" varStatus="index1">
                                    <p:row>
                                        <c:forEach items="#{items}" var="item" varStatus="index">
                                            <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                                <p:outputLabel value="#{item[8]}："/>
                                            </p:column>
                                            <c:if test="#{!(index.last and index1.last)}">
                                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                                    <p:outputLabel value="#{null==item[9]?'/':item[9]}"/>
                                                    <p:outputLabel value="#{null!=item[9]?'人':''}"/>
                                                </p:column>
                                            </c:if>
                                            <c:if test="#{index.last and index1.last}">
                                                <p:column style="text-align:left;padding-left:10px;width:220px;"
                                                          colspan="#{items.size()==1?5:items.size()==2?3:''}">
                                                    <p:outputLabel value="#{null==item[9]?'/':item[9]}"/>
                                                    <p:outputLabel value="#{null!=item[9]?'人':''}"/>
                                                </p:column>
                                            </c:if>

                                        </c:forEach>
                                    </p:row>
                                </c:forEach>
                            </c:if>
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 30px;">
                                    <div style="display:flex;justify-content: flex-start;margin-left: 20px;color: #135AAD;font-weight: bold;">
                                        <h:outputText value="化学毒物种类：#{mgrbean.unitFactorItemMap.get('2').size()>0?'有':'无'}"/>
                                    </div>
                                </p:column>
                            </p:row>
                            <c:if test="#{mgrbean.unitFactorItemMap.get('2').size()>0}">
                                <c:forEach items="#{mgrbean.unitFactorItemMap.get('2')}" var="items" varStatus="index1">
                                    <p:row>
                                        <c:forEach items="#{items}" var="item" varStatus="index">
                                            <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                                <p:outputLabel value="#{item[8]}："/>
                                            </p:column>
                                            <c:if test="#{!(index.last and index1.last)}">
                                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                                    <p:outputLabel value="#{null==item[9]?'/':item[9]}"/>
                                                    <p:outputLabel value="#{null!=item[9]?'人':''}"/>
                                                </p:column>
                                            </c:if>
                                            <c:if test="#{index.last and index1.last}">
                                                <p:column style="text-align:left;padding-left:10px;width:220px;"
                                                          colspan="#{items.size()==1?5:items.size()==2?3:''}">
                                                    <p:outputLabel value="#{null==item[9]?'/':item[9]}"/>
                                                    <p:outputLabel value="#{null!=item[9]?'人':''}"/>
                                                </p:column>
                                            </c:if>
                                        </c:forEach>
                                    </p:row>
                                </c:forEach>
                            </c:if>
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 30px;">
                                    <div style="display:flex;justify-content: flex-start;margin-left: 20px;color: #135AAD;font-weight: bold;">
                                        <h:outputText value="物理因素种类：#{mgrbean.unitFactorItemMap.get('3').size()>0?'有':'无'}"/>
                                    </div>
                                </p:column>
                            </p:row>
                            <c:if test="#{mgrbean.unitFactorItemMap.get('3').size()>0}">
                                <c:forEach items="#{mgrbean.unitFactorItemMap.get('3')}" var="items" varStatus="index1">
                                    <p:row>
                                        <c:forEach items="#{items}" var="item" varStatus="index">
                                            <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                                <p:outputLabel value="#{item[8]}："/>
                                            </p:column>
                                            <c:if test="#{!(index.last and index1.last)}">
                                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                                    <p:outputLabel value="#{null==item[9]?'/':item[9]}"/>
                                                    <p:outputLabel value="#{null!=item[9]?'人':''}"/>
                                                </p:column>
                                            </c:if>
                                            <c:if test="#{index.last and index1.last}">
                                                <p:column style="text-align:left;padding-left:10px;width:220px;"
                                                          colspan="#{items.size()==1?5:items.size()==2?3:''}">
                                                    <p:outputLabel value="#{null==item[9]?'/':item[9]}"/>
                                                    <p:outputLabel value="#{null!=item[9]?'人':''}"/>
                                                </p:column>
                                            </c:if>
                                        </c:forEach>
                                    </p:row>
                                </c:forEach>
                            </c:if>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="上一年度职业病危害因素检测情况" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="上一年度职业病危害因素检测情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="5">
                                    <p:outputLabel
                                            value="#{(null ne mgrbean.unitharmChk and 1==mgrbean.unitharmChk[1])?'已检测':'未检测'}"/>
                                </p:column>
                            </p:row>
                            <p:row rendered="#{null ne mgrbean.unitharmChk and 1==mgrbean.unitharmChk[1]}">
                                <p:column colspan="6">
                                    <p:panelGrid style="width:100%; line-height: 20px;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="width:55px;text-align: center;">
                                                    <h:outputText value="监测大类"/>
                                                </p:column>
                                                <p:column style="width:55px;text-align: center;">
                                                    <h:outputText value="监测情况"/>
                                                </p:column>
                                                <p:column style="width:100px;text-align: center;">
                                                    <h:outputText value="检测因素"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="有/无因素"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="场所检测点数"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="检测岗位/工种数"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="超标岗位数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach var="sub" items="#{mgrbean.unitharmcheckSub}">
                                            <p:row>
                                                <p:column style="text-align: center;" rowspan="#{null ne sub[13]?sub[13]:''}"
                                                          rendered="#{null ne sub[13] or null eq sub[0]}">
                                                    <h:outputText value="#{sub[15]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;" rowspan="#{null ne sub[13]?sub[13]:''}"
                                                          rendered="#{null ne sub[13] or null eq sub[0]}">
                                                    <h:outputText value="#{1==sub[10]?'已检测':'未检测'}"/>
                                                </p:column>
                                                <p:column style="text-align: left;">
                                                    <h:outputText value="#{sub[3]}" escape="false"/>
                                                    <h:outputText value="/" rendered="#{null eq sub[3]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="有" rendered="#{1==sub[4]}"/>
                                                    <h:outputText value="无" rendered="#{0==sub[4]}"/>
                                                    <h:outputText value="/" rendered="#{null eq sub[4]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[5]?'/':sub[5]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[6]?'/':sub[6]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[7]?'/':sub[7]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[8]?'/':sub[8]}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="上一年度在岗期间职业健康检查情况" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:221px;">
                                    <p:outputLabel value="上一年度在岗期间职业健康检查情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel
                                            value="#{(null ne mgrbean.unitHethCus and 1==mgrbean.unitHethCus[1])?'已体检':'未体检'}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="体检总人数："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="#{mgrbean.unitHethCus[2]}"/>
                                </p:column>
                            </p:row>
                            <p:row rendered="#{null ne mgrbean.unitHethCus and 1==mgrbean.unitHethCus[1]}">
                                <p:column colspan="6">
                                    <p:panelGrid style="width:100%; line-height: 20px;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="width:55px;text-align: center;">
                                                    <h:outputText value="监测大类"/>
                                                </p:column>
                                                <p:column style="width:55px;text-align: center;">
                                                    <h:outputText value="检查情况"/>
                                                </p:column>
                                                <p:column style="width:100px;text-align: center;">
                                                    <h:outputText value="检测因素"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="有/无因素"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="应复查人数"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="实际复查人数"/>
                                                </p:column>
                                                <p:column style="width:50px;text-align: center;">
                                                    <h:outputText value="异常人数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach var="sub" items="#{mgrbean.unitHealthItem}">
                                            <p:row>
                                                <p:column style="text-align: center;" rowspan="#{null ne sub[13]?sub[13]:''}"
                                                          rendered="#{null ne sub[13] or null eq sub[0]}">
                                                    <h:outputText value="#{sub[15]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;" rowspan="#{null ne sub[13]?sub[13]:''}"
                                                          rendered="#{null ne sub[13] or null eq sub[0]}">
                                                    <h:outputText value="#{1==sub[10]?'已体检':'未体检'}"/>
                                                </p:column>
                                                <p:column style="text-align: left;">
                                                    <h:outputText value="#{sub[3]}" escape="false"/>
                                                    <h:outputText value="/" rendered="#{null eq sub[3]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="有" rendered="#{1==sub[4]}"/>
                                                    <h:outputText value="无" rendered="#{0==sub[4]}"/>
                                                    <h:outputText value="/" rendered="#{null eq sub[4]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[5]?'/':sub[5]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[6]?'/':sub[6]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[7]?'/':sub[7]}"/>
                                                </p:column>
                                                <p:column style="text-align: center;">
                                                    <h:outputText value="#{null eq sub[8]?'/':sub[8]}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="职业病防护设施设置及运行情况" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防尘设施设置情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.facilitiesOperation[1]}"/>
                                    <p:outputLabel value="部分有" rendered="#{2==mgrbean.facilitiesOperation[1]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.facilitiesOperation[1]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.facilitiesOperation[1]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防护效果："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有效" rendered="#{1==mgrbean.facilitiesOperation[2]}"/>
                                    <p:outputLabel value="部分有效" rendered="#{2==mgrbean.facilitiesOperation[2]}"/>
                                    <p:outputLabel value="无效" rendered="#{3==mgrbean.facilitiesOperation[2]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.facilitiesOperation[2]}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防毒设施设置情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.facilitiesOperation[3]}"/>
                                    <p:outputLabel value="部分有" rendered="#{2==mgrbean.facilitiesOperation[3]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.facilitiesOperation[3]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.facilitiesOperation[3]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防护效果："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有效" rendered="#{1==mgrbean.facilitiesOperation[4]}"/>
                                    <p:outputLabel value="部分有效" rendered="#{2==mgrbean.facilitiesOperation[4]}"/>
                                    <p:outputLabel value="无效" rendered="#{3==mgrbean.facilitiesOperation[4]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.facilitiesOperation[4]}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防噪声设施设置情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.facilitiesOperation[5]}"/>
                                    <p:outputLabel value="部分有" rendered="#{2==mgrbean.facilitiesOperation[5]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.facilitiesOperation[5]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.facilitiesOperation[5]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防护效果："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有效" rendered="#{1==mgrbean.facilitiesOperation[6]}"/>
                                    <p:outputLabel value="部分有效" rendered="#{2==mgrbean.facilitiesOperation[6]}"/>
                                    <p:outputLabel value="无效" rendered="#{3==mgrbean.facilitiesOperation[6]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.facilitiesOperation[6]}"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="职业病防护用品配备及发放情况" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防尘口罩发放："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[1]}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.equimentDis[1]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[1]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="佩戴情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[2]}"/>
                                    <p:outputLabel value="部分" rendered="#{2==mgrbean.equimentDis[2]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.equimentDis[2]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[2]}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防毒口罩或面罩发放："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[3]}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.equimentDis[3]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[3]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="佩戴情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[4]}"/>
                                    <p:outputLabel value="部分" rendered="#{2==mgrbean.equimentDis[4]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.equimentDis[4]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[4]}"/>
                                </p:column>
                            </p:row>
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="防噪声耳塞或耳罩发放："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[5]}"/>
                                    <p:outputLabel value="无" rendered="#{0==mgrbean.equimentDis[5]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[5]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="佩戴情况："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[6]}"/>
                                    <p:outputLabel value="部分" rendered="#{2==mgrbean.equimentDis[6]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.equimentDis[6]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[6]}"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="职业病危害警示标识及警示说明设置情况" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="粉尘警示标识及警示说明："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[7]}"/>
                                    <p:outputLabel value="部分有" rendered="#{2==mgrbean.equimentDis[7]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.equimentDis[7]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[7]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="化学毒物警示标识及警示说明："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[8]}"/>
                                    <p:outputLabel value="部分有" rendered="#{2==mgrbean.equimentDis[8]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.equimentDis[8]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[8]}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="噪声警示标识及警示说明："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:outputLabel value="有" rendered="#{1==mgrbean.equimentDis[9]}"/>
                                    <p:outputLabel value="部分有" rendered="#{2==mgrbean.equimentDis[9]}"/>
                                    <p:outputLabel value="无" rendered="#{3==mgrbean.equimentDis[9]}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.equimentDis[9]}"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:fieldset legend="职业病危害因素检测结果登记" style="margin-top:10px;">
                        <p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                            <p:row>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="行业："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;">
                                    <p:outputLabel value="#{mgrbean.jcUnitBasicInfo.fkByIndustryId.codeName}"/>
                                </p:column>
                                <p:column style="text-align:right;padding-right:3px;height: 30px;width:217px;">
                                    <p:outputLabel value="针对有机溶剂开展定性分析："/>
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;width:220px;" colspan="3">
                                    <p:outputLabel value="是" rendered="#{1==mgrbean.jcUnitBasicInfo.ifAnalysis}"/>
                                    <p:outputLabel value="否" rendered="#{2==mgrbean.jcUnitBasicInfo.ifAnalysis}"/>
                                    <p:outputLabel value="未监测" rendered="#{3==mgrbean.jcUnitBasicInfo.ifAnalysis}"/>
                                    <p:outputLabel value="/" rendered="#{null eq mgrbean.jcUnitBasicInfo.ifAnalysis}"/>
                                </p:column>
                            </p:row>
                            <c:forEach var="resultVo" items="#{mgrbean.resultPro}">
                                <p:row rendered="#{resultVo.resultPro.size()>0}">
                                    <p:column colspan="6" style="text-align:left;padding-left: 15px;height: 30px;width:217px;font-weight: bold;">
                                        <p:outputLabel value="#{resultVo.factorName}" escape="false" />
                                    </p:column>
                                </p:row>
                                <p:row rendered="#{resultVo.resultPro.size()>0}">
                                    <p:column colspan="6">
                                        <p:panelGrid style="width:100%; line-height: 20px;">
                                            <f:facet name="header">
                                                <p:row>
                                                    <p:column style="width:100px;text-align: center;">
                                                        <h:outputText value="岗位/环节"/>
                                                    </p:column>
                                                    <p:column style="width:50px;text-align: center;">
                                                        <h:outputText value="作业人数"/>
                                                    </p:column>
                                                    <p:column style="width:60px;text-align: center;">
                                                        <h:outputText value="工作班制时长"/>
                                                    </p:column>
                                                    <p:column style="width:60px;text-align: center;">
                                                        <h:outputText value="每班接触时间"/>
                                                    </p:column>
                                                    <p:column style="width:60px;text-align: center;">
                                                        <h:outputText value="每周接触天数"/>
                                                    </p:column>
                                                    <p:column style="width:60px;text-align: center;">
                                                        <h:outputText value="每周接触时间"/>
                                                    </p:column>
                                                    <p:column style="width:100px;text-align: center;">
                                                        <h:outputText value="个体/定点"/>
                                                    </p:column>
                                                    <p:column style="width:100px;text-align: center;">
                                                        <h:outputText value="检测项目"/>
                                                    </p:column>
                                                    <p:column style="width:100px;text-align: center;">
                                                        <h:outputText value="接触时间"/>
                                                    </p:column>
                                                    <p:column style="width:40px;text-align: center;">
                                                        <h:outputText value="样本1"/>
                                                    </p:column>
                                                    <p:column style="width:40px;text-align: center;">
                                                        <h:outputText value="样本2"/>
                                                    </p:column>
                                                    <p:column style="width:40px;text-align: center;">
                                                        <h:outputText value="样本3"/>
                                                    </p:column>
                                                    <p:column style="width:40px;text-align: center;">
                                                        <h:outputText value="样本4"/>
                                                    </p:column>
                                                    <p:column style="width:40px;text-align: center;">
                                                        <h:outputText value="样本5"/>
                                                    </p:column>
                                                    <p:column style="width:40px;text-align: center;">
                                                        <h:outputText value="样本6"/>
                                                    </p:column>
                                                    <c:if test="#{1==resultVo.flag}">
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{mgrbean.titleNames.get(0)}" escape="false"/>
                                                        </p:column>
                                                    </c:if>
                                                    <c:if test="#{1==resultVo.flag || 2==resultVo.flag}">
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{mgrbean.titleNames.get(1)}" escape="false"/>
                                                        </p:column>
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{mgrbean.titleNames.get(2)}" escape="false"/>
                                                        </p:column>
                                                    </c:if>
                                                    <c:if test="#{3==resultVo.flag}">
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="平均值" escape="false"/>
                                                        </p:column>
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{mgrbean.titleNames.get(3)}" escape="false"/>
                                                        </p:column>
                                                    </c:if>
                                                </p:row>
                                            </f:facet>
                                            <c:forEach var="pro" items="#{resultVo.resultPro}">
                                                <p:row>
                                                    <p:column style="text-align: center;" rowspan="#{null ne pro[33]?pro[33]:''}"
                                                              rendered="#{null ne pro[33]}" >
                                                        <h:outputText styleClass="ms-result-dot-true" rendered="#{1==pro[4]}"/>
                                                        <h:outputText styleClass="ms-result-dot-false" rendered="#{null eq pro[4] || 0==pro[4]}"/>
                                                        <h:outputText value="#{pro[2]}"/>
                                                        <h:outputText value="（" rendered="#{null ne pro[3]}"/>
                                                        <h:outputText value="#{pro[3]}" rendered="#{null ne pro[3]}" />
                                                        <h:outputText value="）" rendered="#{null ne pro[3]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;" rowspan="#{null ne pro[33]?pro[33]:''}"
                                                              rendered="#{null ne pro[33]}">
                                                        <h:outputText value="#{null ne pro[7]?pro[7]:'/'}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;" rowspan="#{null ne pro[33]?pro[33]:''}"
                                                              rendered="#{null ne pro[33]}">
                                                        <h:outputText value="#{pro[8]}h" rendered="#{null ne pro[8]}"/>
                                                        <h:outputText value="/" rendered="#{null eq pro[8]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;" rowspan="#{null ne pro[33]?pro[33]:''}"
                                                              rendered="#{null ne pro[33]}">
                                                        <h:outputText value="#{pro[30]}h" rendered="#{null ne pro[30]}"/>
                                                        <h:outputText value="/" rendered="#{null eq pro[30]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;" rowspan="#{null ne pro[33]?pro[33]:''}"
                                                              rendered="#{null ne pro[33]}">
                                                        <h:outputText value="#{pro[9]}d" rendered="#{null ne pro[9]}"/>
                                                        <h:outputText value="/" rendered="#{null eq pro[9]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;" rowspan="#{null ne pro[33]?pro[33]:''}"
                                                              rendered="#{null ne pro[33]}">
                                                        <h:outputText value="#{pro[10]}h" rendered="#{null ne pro[10]}"/>
                                                        <h:outputText value="/" rendered="#{null eq pro[10]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{1==pro[11]?'个体':'定点'}"/>
                                                        <h:outputText value="（" rendered="#{null ne pro[13]}"/>
                                                        <h:outputText value="#{pro[13]}" rendered="#{null ne pro[13]}"/>
                                                        <h:outputText value="）" rendered="#{null ne pro[13]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[12]?'/':pro[12]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[14]?'/':pro[14]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[15]?'/':pro[15]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[16]?'/':pro[16]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[17]?'/':pro[17]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[18]?'/':pro[18]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[19]?'/':pro[19]}"/>
                                                    </p:column>
                                                    <p:column style="text-align: center;">
                                                        <h:outputText value="#{null eq pro[20]?'/':pro[20]}"/>
                                                    </p:column>
                                                    <c:if test="#{1==pro[27]}">
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{null eq pro[21]?'/':pro[21]}"/>
                                                        </p:column>
                                                    </c:if>
                                                    <c:if test="#{1==pro[27] || 2==pro[27]}">
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{null eq pro[22]?'/':pro[22]}"/>
                                                        </p:column>
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{null eq pro[24]?'/':pro[24]}"/>
                                                        </p:column>
                                                    </c:if>
                                                    <c:if test="#{3==pro[27]}">
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{null eq pro[23]?'/':pro[23]}"/>
                                                        </p:column>
                                                        <p:column style="width:50px;text-align: center;">
                                                            <h:outputText value="#{null eq pro[26]?'/':pro[26]}"/>
                                                        </p:column>
                                                    </c:if>
                                                </p:row>
                                            </c:forEach>
                                        </p:panelGrid>
                                    </p:column>
                                </p:row>
                            </c:forEach>
                        </p:panelGrid>
                    </p:fieldset>
            </h:form>
        </p:outputPanel>
    </h:body>
</f:view>
</html>
