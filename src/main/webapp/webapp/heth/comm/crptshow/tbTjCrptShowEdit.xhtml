<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				template="/WEB-INF/templates/system/editTemplate.xhtml">
	<ui:define name="insertEditScripts">
		<style type="text/css">
			.checkboxVisible .ui-state-disabled{
				opacity: 1;
			}
			.ui-state-hover .icon-alert{
				background-image: url(/resources/images/alert-tip-w.png) !important;
				background-size: 12px 12px;
				margin-left: 3px;
				margin-top: -6px !important;
			}
			.ui-state-hover .icon-alert{
				color : #fff;
			}

		</style>
		<h:outputStylesheet library="css" name="default.css" />
		<h:outputStylesheet library="css" name="ui-tabs.css" />
		<script type="text/javascript"
				src="/resources/echarts/3.0/echarts.min.js"></script>
		<script type="text/javascript"
				src="/resources/echarts/3.0/macarons.js"></script>
		<script type="text/javascript">
			var chatLeft ;
			var chatRight ;

			function buildChart() {
				if (chatLeft != null) {
					chatLeft.clear();
				}
				var leftDiv = document.getElementById("chartLeft");
				if (leftDiv != null) {
					chatLeft = echarts.init(leftDiv, 'macarons');
					var option = document.getElementById("tabView:editForm:showTabView:xml1").value;
					console.log("左"+option);
					if (option != '') {
						if(null != option){
							chatLeft.setOption( eval("(" + option + ")")	 );
						}
					}
				}

				if (chatRight != null) {
					chatRight.clear();
				}
				var rightDiv = document.getElementById("chartRight");
				if (rightDiv != null) {
					chatRight = echarts.init(rightDiv, 'macarons');
					var option = document.getElementById("tabView:editForm:showTabView:xml2").value;
					console.log("右1"+option);
					if (option != '') {
						if( null != option){
							chatRight.setOption( eval("(" + option + ")")	 );
						}
					}
				}
			}
		</script>
	</ui:define>

	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="企业综合展示" />
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
								 action="#{mgrbean.backAction}" process="@this"
								 update=":tabView" />
				<p:inputText style="visibility: hidden;width: 0"/>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">
		<p:outputPanel style="border-top-color: transparent;">
			<p:fieldset legend="基本信息"  style="margin-top: 5px;margin-bottom: 5px;">
				<div style="display:flex;margin-top: 2px;margin-bottom: -4px;">
					<p:spacer width="8" />
					<p:outputLabel  value="数据来源："/>
					<p:selectOneRadio value="#{mgrbean.dataSource}" style="width: 250px;position: relative; top: -5px;left: 5px;" onchange="showStatus()"   >
						<f:selectItem itemLabel="职业健康检查" itemValue="1" itemDisabled="#{mgrbean.dataSource==2 and mgrbean.otherId==null}"></f:selectItem>
						<f:selectItem itemLabel="企业申报" itemValue="2" itemDisabled="#{mgrbean.dataSource==1 and mgrbean.otherId==null}"></f:selectItem>
						<p:ajax listener="#{mgrbean.changeSource}"  process="@this" update=":tabView:editForm:dataSourceGrid" oncomplete="hideStatus()" ></p:ajax>
						<!-- 注意 process不可以提交:tabView:editForm 所有数据 若需要提交所有数据 需要在changeSource 给year赋值 但赋的值可能和用户之前选择的不一致 -->
						<p:ajax listener="#{mgrbean.changeSource}"  process="@this" update=":tabView:editForm:dataSourceGrid" ></p:ajax>
					</p:selectOneRadio>
				</div>
				<p:panelGrid style="width:100%;margin-top:5px;margin-bottom:5px;" id="dataSourceGrid" >
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="单位名称："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.crptName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.crptName !=mgrbean.tdZyUnitbasicinfo.unitName?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.unitName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.crptName !=mgrbean.tdZyUnitbasicinfo.unitName?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="所属地区："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.tsZoneByZoneId.fullName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsZoneByZoneId.fullName !=mgrbean.tdZyUnitbasicinfo.fkByZoneId.fullName?'color: red':''}"  rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.fkByZoneId.fullName}"  style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsZoneByZoneId.fullName !=mgrbean.tdZyUnitbasicinfo.fkByZoneId.fullName?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="社会信用代码："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.institutionCode}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.institutionCode !=mgrbean.tdZyUnitbasicinfo.creditCode?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.creditCode}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.institutionCode !=mgrbean.tdZyUnitbasicinfo.creditCode?'color: red':''}"  rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="注册地址："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.address}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.address !=mgrbean.tdZyUnitbasicinfo.regAddr?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.regAddr}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.address !=mgrbean.tdZyUnitbasicinfo.regAddr?'color: red':''}"  rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="法人（负责人）："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.corporateDeputy}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.corporateDeputy !=mgrbean.tdZyUnitbasicinfo.legalPerson?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.legalPerson}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.corporateDeputy !=mgrbean.tdZyUnitbasicinfo.legalPerson?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="作业地址："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.enrolAddress}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.enrolAddress !=mgrbean.tdZyUnitbasicinfo.workAddr?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.workAddr}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.enrolAddress !=mgrbean.tdZyUnitbasicinfo.workAddr?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="经济类型："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.tsSimpleCodeByEconomyId.codeName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsSimpleCodeByEconomyId.codeName !=mgrbean.tdZyUnitbasicinfo.fkByEconomicId.codeName?'color: red':''}"  rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.fkByEconomicId.codeName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsSimpleCodeByEconomyId.codeName !=mgrbean.tdZyUnitbasicinfo.fkByEconomicId.codeName?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="企业规模："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.tsSimpleCodeByCrptSizeId.codeName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsSimpleCodeByCrptSizeId.codeName !=mgrbean.tdZyUnitbasicinfo.fkByEnterpriseScaleId.codeName?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.fkByEnterpriseScaleId.codeName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsSimpleCodeByCrptSizeId.codeName !=mgrbean.tdZyUnitbasicinfo.fkByEnterpriseScaleId.codeName?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="行业类别："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.tsSimpleCodeByIndusTypeId.codeName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsSimpleCodeByIndusTypeId.codeName !=mgrbean.tdZyUnitbasicinfo.fkByIndustryCateId.codeName?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.fkByIndustryCateId.codeName}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.tsSimpleCodeByIndusTypeId.codeName !=mgrbean.tdZyUnitbasicinfo.fkByIndustryCateId.codeName?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="联系人：" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="职业卫生管理联系人：" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.linkman2}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.linkman2 !=mgrbean.tdZyUnitbasicinfo.linkManager?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.linkManager}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.linkman2 !=mgrbean.tdZyUnitbasicinfo.linkManager?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="职工人数："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.workForce}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.workForce !=mgrbean.tdZyUnitbasicinfo.empNum?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.empNum}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.workForce !=mgrbean.tdZyUnitbasicinfo.empNum?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="接触职业病危害因素人数："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrpt.holdCardMan}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.holdCardMan !=mgrbean.tdZyUnitbasicinfo.victimsNum?'color: red':''}" rendered="#{mgrbean.dataSource==1}"/>
							<p:outputLabel value="#{mgrbean.tdZyUnitbasicinfo.victimsNum}" style="#{mgrbean.otherId!=null and mgrbean.tbTjCrpt.holdCardMan !=mgrbean.tdZyUnitbasicinfo.victimsNum?'color: red':''}" rendered="#{mgrbean.dataSource==2}"/>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>

			<!-- 提示 -->
			<p:fieldset legend="提示"  style="margin-top: 20px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;">
					<c:if test="#{mgrbean.badConditionList.size()>0 }">
						<c:forEach items="#{mgrbean.badConditionList}" var="itm" varStatus="varStatus"  >
							<p:row>
								<p:column style="text-align:left;height: 30px;border-color: transparent;padding: 0 0 0 10px;">
									<p:outputLabel value="#{varStatus.index+1}、#{itm}"/>
								</p:column>
							</p:row>
						</c:forEach>
					</c:if>
					<c:if test="#{mgrbean.badConditionList.size()==0 }">
						<p:row>
							<p:column style="text-align:left;height: 30px;border-color: transparent;padding: 0 0 0 10px;">
								<p:outputLabel value="无"/>
							</p:column>
						</p:row>
					</c:if>
<!--					<p:row>-->
<!--						<p:column style="text-align:left;height: 30px;border-color: transparent;">-->
<!--							<p:outputLabel value="2、体检危害因素中化学类的体检人数与申报的人数不等"/>-->
<!--						</p:column>-->
<!--					</p:row>-->
				</p:panelGrid>
			</p:fieldset>

		<!-- 综合分析展示 -->
			<p:fieldset legend="综合分析展示"  style="margin-top: 20px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;margin-top:5px;margin-bottom:5px;" id="analyGrid">
					<c:if test="#{mgrbean.msgList.size()>0 }">
						<p:row>
							<c:forEach items="#{mgrbean.msgList}" var="itm" varStatus="varStatus"  >
								<p:column style="padding-left: 0px;padding-right:15px;padding-bottom:13px;border-color: transparent;height: 71px;width: 230px;" >
									<div style="cursor:pointer;width: 220px;height: 70px;opacity: 1;border-radius: 6pt;background-color: #{itm[3]}; " onmouseover="this.style.boxShadow='3px 3px 10px #909090';this.style.height='71px';" onmouseout="this.style.boxShadow='none';this.style.height='70px';"  onclick="jumpAction#{varStatus.index}()"  >
										<p:remoteCommand name="jumpAction#{varStatus.index}" process="@this,:tabView:editForm:analyGrid" onstart="showStatus()" oncomplete="hideStatus()" action="#{mgrbean.analyAction}" update=":tabView:editForm:analyGrid,:tabView:editForm:showTabView">
											<f:setPropertyActionListener target="#{mgrbean.analyType}" value="#{itm[0]}"></f:setPropertyActionListener>
										</p:remoteCommand>
										<div style="margin-top: 0px;text-align: center;width: 210px; height: 15px;">
	                            			<span style="opacity: 1;font-size: 18px;font-family: PingFangSC;color:white;line-height: 15px;letter-spacing: 0px;position: relative;top: 26px; ">
		                            			<h:outputText value="#{itm[1]}"/>
		                            		</span>
										</div>
										<div style="position: relative;top: -14px;width: 220px;height: 70px;">
											<img  style="width: 219px;height: 70px;opacity: 1;"  align="bottom" src="#{itm[8]}" />
										</div>
									</div>
								</p:column>
							</c:forEach>
							<p:column style="border-color:transparent; "></p:column>
						</p:row>
					</c:if>
				</p:panelGrid>
				<p:tabView id="showTabView" dynamic="true" cache="true" activeIndex="#{mgrbean.analyType}" style="border:1px; padding:0px;">
				 	 <p:tab title="badRsnAnaly" titleStyle="display:none;">
				 	 	<!-- 危害因素分析 -->
						 <!-- 危害因素分析 analyType 0 start  -->
						 <p:outputPanel id="dangerDataPanel">
							 <div style="display:flex;margin-top: 2px;margin-bottom: 8px;" >
								 <p:spacer width="8" />
								 <p:outputLabel  value="年份："/>
								 <p:selectOneMenu value="#{mgrbean.year}" style="width: 100px;position: relative; top: -5px;left: 5px;" onchange="showStatus()" >
									 <f:selectItems value="#{mgrbean.yearList}" var="yearItem"
													itemLabel="#{yearItem}" itemValue="#{yearItem}"/>
									 <p:ajax event="change" listener="#{mgrbean.changeYear}"
											 process="@this,:tabView:editForm" update=":tabView:editForm:showTabView:dangerDataPanel" oncomplete="hideStatus()"  />
								 </p:selectOneMenu>
							 </div>
							 <p:dataTable var="itm" value="#{mgrbean.dangerTableList}"
										  emptyMessage="没有数据！" style="line-height: 31px;" >
								 <p:column headerText="危害因素类别" style="width:25%;text-align: center">
									 <h:outputLabel value="#{itm.dangerType}"/>
								 </p:column>
								 <p:column headerText="检查人数（健康检查）" style="width:25%;text-align: center">
									 <h:outputLabel value="#{itm.checkNumber == null ? '/' : itm.checkNumber}" rendered="#{!itm.unMatch}" />
									 <h:outputLabel value="#{itm.checkNumber == null ? '/' : itm.checkNumber}" style="color: red;" rendered="#{itm.unMatch}" />
								 </p:column>
								 <p:column headerText="接害人数（企业申报）" style="width:25%;text-align: center">
									 <h:outputLabel value="#{itm.reciveNumber == null ? '/' : itm.reciveNumber}" rendered="#{!itm.unMatch}" />
									 <h:outputLabel value="#{itm.reciveNumber == null ? '/' : itm.reciveNumber}" style="color: red;" rendered="#{itm.unMatch}" />
								 </p:column>
								 <p:column headerText="检查人数（企业申报）" style="text-align: center">
									 <h:outputLabel value="#{itm.crptCheckNumber == null ? '/' : itm.crptCheckNumber}"  />
								 </p:column>
							 </p:dataTable>
							 <br/>
							 <h:inputHidden id="xml1" value="#{mgrbean.lineLeftJson}"/>
							 <h:inputHidden id="xml2" value="#{mgrbean.lineRightJson}"/>
							 <p:fieldset legend="统计图表">
								 <table width="100%" >
									 <tr>
										 <td width="48%">
											 <!-- 名称体检机构换成健康检查 -->
											 <div id="chartLeft" style="height: 500px;"></div>
										 </td>
										 <!-- style=" border-right-style: solid; border-right-width: 1px; " -->
										 <td >
											 <div style="height:459px; padding-left: 50%; width:1px; border-right:1px darkgrey solid" />
										 </td>
										 <td width="48%">
											 <div id="chartRight" style="height: 500px;"></div>
										 </td>
									 </tr>
								 </table>
							 </p:fieldset>
						 </p:outputPanel>
						 <!-- 危害因素分析 end  -->
				 	 </p:tab>
				 	 <p:tab title="personAnaly" titleStyle="display:none;">
				 	 	<!-- 人群分析 -->
				 	 </p:tab>
				 	 <p:tab title="tjData" titleStyle="display:none;">
				 	 	<!-- 体检情况 -->
				 	 </p:tab>
				 	 <p:tab title="onLineReport" titleStyle="display:none;">
				 	 	<!-- 在线申报情况 -->
				 	 	<ui:include src="onLineCrptTableList.xhtml"></ui:include>
				 	 </p:tab>
				 	 <p:tab title="onLineCheck" titleStyle="display:none;">
				 	 	<!-- 在线检测情况 -->
				 	 </p:tab>
				 </p:tabView>
			</p:fieldset>
		</p:outputPanel>
	</ui:define>
</ui:composition>
