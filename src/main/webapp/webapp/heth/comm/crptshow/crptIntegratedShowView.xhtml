<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.CrptIntegratedShowListBean"-->
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位综合展示" />
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="margin: 5px 0;">
            <h:panelGrid columns="10" style="border-color:transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" onclick="hideTooltips();"
                                 action="#{mgrbean.backAction}" process="@this" update=":page_view"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <style type="text/css">
            #tabView {
                border-radius: 6px;
            }

            #tabView\:viewForm {
                height: calc(100vh - 5px);
            }

            #tabView\:viewForm\:editTitleGrid, #tabView\:viewForm\:editGrid {
                margin: 0 !important;
                height: 0 !important;
            }

            #tabView\:viewForm, #tabView\:viewForm\:archives, #tabView\:viewForm\:archivesInfo, .da-item, .da-item-b, .left_con, .right_con {
                display: flex;
                flex-direction: column;
            }

            .zwx_toobar_new, .da-panel, .archives_date_sel_panel, .toobar_3 {
                display: flex;
                align-items: center;
            }

            #tabView\:viewForm\:archives, #tabView\:viewForm\:archivesInfo, #tabView\:viewForm\:archivesDetail {
                flex: 1;
                height: 0;
            }

            .full_table {
                width: 100%;
            }

            .none_user_select {
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }

            .zwx_toobar_new {
                border-radius: 6px;
                box-shadow: 0 -1px 0 0 #d5d9e0 inset;
                border: none;
                padding: 7px 0;
            }

            .table_header_column {
                text-align: left;
                padding-left: 5px !important;
                height: 20px;
            }

            .column_title1 {
                text-align: right;
            }

            .column_title2 {
                padding: 3px 6px !important;
            }

            .da-panel {
                padding: 20px 8px 16px 8px;
            }

            .da-item-panel, .da-item {
                margin: 0 8px;
            }

            .da-item, .da-item-b {
                justify-content: space-between;
                padding: 0 10px;
                width: 134px;
                height: 80px;
                border-radius: 4px;
                background: #FCFDFF;
                border: 0.5px solid #FFFFFF00;
                box-shadow: 0 1px 6px -2px rgba(170, 182, 193, 0.50);
            }

            .da-item-panel .da-item-b, .da-item:active {
                border: 0.5px solid #198989;
                background: #faffff;
                box-shadow: 0 3px 5px 0 #dee8f0, 0 1px 6px -2px rgba(170, 182, 193, 0.50);
            }

            .da-item-panel .da-item-a {
                display: none;
                background: #198989 url(/resources/images/crptShow/tick.svg) 9px 5px no-repeat;
            }

            .da-item-panel.sel .da-item-a {
                display: flex;
                width: 24px;
                height: 24px;
                border-radius: 0 4px 0 108px;
                padding: 0;
                margin: 0 0 0 -26px;
                float: right;
            }

            .da-item.block {
                opacity: 0.4;
                background: #DAE4F2;
                color: #191919;
                border: 0.5px solid #FFFFFF00 !important;
                box-shadow: 0 1px 6px -2px rgba(170, 182, 193, 0.50) !important;
                align-items: center;
                justify-content: center;
            }

            .archives_date_sel_panel {
                justify-content: space-between;
                border: none;
                padding: 7px 0 7px 20px;
                box-shadow: 0 1px 0 0 #cdcdcd inset, 0 -1px 0 0 #cdcdcd inset;
            }

            .left_con, .right_con {
                overflow: hidden;
                padding: 2px;
                height: 99%;
            }

            .con {
                display: flex;
                flex-direction: row;
            }

            .left_con {
                width: 45%;
                box-shadow: -1px 0 0 0 #e6e6e6 inset;
            }

            .right_con {
                flex: 1;
            }

            .toobar_2 {
                color: #000000;
                font-weight: bold;
                padding: 8px 10px;
            }

            .toobar_3 {
                padding: 10px;
            }

            .con_sep {
                height: 1px;
                box-shadow: 0 1px 0 0 #e6e6e6 inset;
                padding: 4px 0;
                margin: 5px 0 0 0;
            }

            .left_con1, .right_con1 {
                overflow: auto;
            }

            .left_con1::-webkit-scrollbar, .right_con1::-webkit-scrollbar {
                -webkit-appearance: none;
                background: rgba(255, 0, 0, 0);
                width: 4px;
            }

            .left_con1::-webkit-scrollbar-thumb, .right_con1::-webkit-scrollbar-thumb {
                background-color: #9996;
                border-radius: 10px;
                width: 4px;
                display: none;
            }

            .left_con1:hover::-webkit-scrollbar-thumb, .right_con1:hover::-webkit-scrollbar-thumb {
                display: block;
            }

            .left_con1::-webkit-scrollbar-track, .right_con1::-webkit-scrollbar-track {
                background-color: transparent;
                display: none;
            }

            .left_con1:hover::-webkit-scrollbar-track, .right_con1:hover::-webkit-scrollbar-track {
                display: block;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function changeDaPanelItemSel(e) {
                jQuery('.da-item-panel.sel').removeClass('sel');
                jQuery(e).addClass('sel');
            }

            function changeDaPanelItemSelByArchives(type) {
                jQuery('.da-item-panel.sel').removeClass('sel');
                if (type == 1) {
                    jQuery('.da-item-panel.archives1').addClass('sel');
                } else if (type == 2) {
                    jQuery('.da-item-panel.archives2').addClass('sel');
                }
            }

            //]]>
        </script>
        <p:outputPanel>
            <p:panelGrid styleClass="full_table">
                <f:facet name="header">
                    <p:row>
                        <p:column styleClass="table_header_column" colspan="6">
                            <p:outputLabel value="基本信息"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="width: 150px;height: 30px;">
                        <p:outputLabel value="地区："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" style="width: 300px;">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[0]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1" style="width: 150px;">
                        <p:outputLabel value="单位名称："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" style="width: 300px;">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[1]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1" style="width: 150px;">
                        <p:outputLabel value="社会信用代码："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[2]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="注册地址："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[3]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="作业场所地址："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[4]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="行业类别："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[5]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="经济类型："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[6]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="企业规模："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[7]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="联系人："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[8]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="联系电话："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[9]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="职业风险分类："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" colspan="3">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[10]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="职业病危害因素种类："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" style="height: 30px;" colspan="5">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[11]}"/>
                        <p:commandLink value="查看详情 >>"
                                       style="float: right;margin-right: 25px;color: #194dff;"
                                       process="@this" update="@this,:tabView:viewForm:occRiskClassTablePanel"
                                       action="#{mgrbean.openBadrsnDialog}"
                                       rendered="#{not empty mgrbean.crptBaseInfo[11]}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:outputPanel>
        <p:outputPanel id="archives">
            <p:panelGrid styleClass="full_table">
                <f:facet name="header">
                    <p:row>
                        <p:column styleClass="table_header_column" colspan="6">
                            <p:outputLabel value="职业健康管理档案"/>
                        </p:column>
                    </p:row>
                </f:facet>
            </p:panelGrid>
            <div class="da-panel none_user_select">
                <p:remoteCommand name="onDeclareClick"
                                 process="@this" update="archivesInfo" action="#{mgrbean.onDeclareClick}"/>
                <p:outputPanel rendered="#{not empty mgrbean.declareData}">
                    <div class="da-item-panel archives1" onclick="changeDaPanelItemSel(this);onDeclareClick();">
                        <div class="da-item-a">
                        </div>
                        <div class="da-item-b">
                            <div style="display: flex;flex-direction: column;">
                                <p:outputLabel
                                        style="padding-top: 10px;font-weight: bold;"
                                        value="在线申报档案"/>
                                <p:outputLabel style="color: #198989;padding-top: 5px;"
                                               value="年度无档案" rendered="#{!mgrbean.hasDeclareDataForTheYear}"/>
                            </div>
                            <p:outputLabel style="color: #646464;padding-bottom: 10px;"
                                           value="最新申报：#{mgrbean.declareData}"/>
                        </div>
                    </div>
                </p:outputPanel>
                <p:outputPanel styleClass="da-item block" rendered="#{empty mgrbean.declareData}">
                    <p:outputLabel value="在线申报档案" style="font-weight: bold;"/>
                </p:outputPanel>
                <p:remoteCommand name="onMonitoringClick"
                                 process="@this" update="archivesInfo" action="#{mgrbean.onMonitoringClick}"/>
                <p:outputPanel rendered="#{not empty mgrbean.placeDate}">
                    <div class="da-item-panel archives2" onclick="changeDaPanelItemSel(this);onMonitoringClick();">
                        <div class="da-item-a">
                        </div>
                        <div class="da-item-b">
                            <div style="display: flex;flex-direction: column;">
                                <p:outputLabel
                                        style="font-weight: bold;padding-top: 10px;"
                                        value="场所监测档案"/>
                                <p:outputLabel style="color: #198989;padding-top: 5px;"
                                               value="年度无档案" rendered="#{!mgrbean.placeThatYear}"/>
                            </div>
                            <p:outputLabel style="color: #646464;padding-bottom: 10px;"
                                           value="最新记录：#{mgrbean.placeDate}"/>
                        </div>
                    </div>
                </p:outputPanel>
                <p:outputPanel styleClass="da-item block" rendered="#{empty mgrbean.placeDate}">
                    <p:outputLabel value="场所监测档案" style="font-weight: bold;"/>
                </p:outputPanel>
                <div class="da-item block">
                    <p:outputLabel value="专项治理档案" style="font-weight: bold;"/>
                </div>
                <div class="da-item block">
                    <p:outputLabel value="“三同时”管理档案" style="font-weight: bold;"/>
                </div>
            </div>
            <p:outputPanel id="archivesInfo" style="#{empty mgrbean.showArchives?'display:none;':''}">
                <p:outputPanel styleClass="archives_date_sel_panel" rendered="#{mgrbean.showArchives eq 1}">
                    <p:outputPanel style="display: flex;align-items: center;">
                        <p:selectOneMenu id="archivesDateSel" value="#{mgrbean.unitbasicinfoRid}"
                                         styleClass="none_user_select">
                            <f:selectItems value="#{mgrbean.unitbasicinfoSelList}" var="unitbasicinfo"
                                           itemLabel="#{unitbasicinfo.declareDate}" itemValue="#{unitbasicinfo.rid}"/>
                            <p:ajax event="change"
                                    process="@this" update="@this,archivesDetail"
                                    listener="#{mgrbean.pakDeclareInfo(mgrbean.unitbasicinfoRid)}"/>
                        </p:selectOneMenu>
                    </p:outputPanel>
                    <p:commandLink value="查看详情 >>"
                                   style="margin-right: 25px;color: #194dff;"
                                   process="@this" action="#{mgrbean.viewUnitBasicInfo}"/>
                </p:outputPanel>
                <p:outputPanel styleClass="archives_date_sel_panel" rendered="#{mgrbean.showArchives eq 2}">
                    <p:outputPanel style="display: flex;align-items: center;">
                        <p:selectOneMenu id="archivesYearSel" value="#{mgrbean.placeDate}"
                                         styleClass="none_user_select" style="width: 80px">
                            <f:selectItems value="#{mgrbean.placeDateList}"/>
                            <p:ajax event="change"
                                    process="@this" update="@this,archivesDetail"
                                    listener="#{mgrbean.initPlaceInfo}"/>
                        </p:selectOneMenu>
                    </p:outputPanel>
                    <p:commandLink value="查看详情 >>"
                                   style="margin-right: 25px;color: #194dff;"
                                   process="@this" action="#{mgrbean.csjcViewAction}"/>
                </p:outputPanel>
                <p:outputPanel id="archivesDetail" styleClass="con">
                    <p:outputPanel styleClass="left_con">
                        <p:outputPanel styleClass="left_con1">
                            <p:outputPanel rendered="#{mgrbean.showArchives eq 1}">
                                <div class="toobar_2">申报情况概况</div>
                                <div class="toobar_3">
                                    <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                    </div>
                                    总体情况
                                </div>
                                <div style="display: flex;flex-direction: row;align-items: center;padding: 10px 0">
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasicinfo.empNum}人"
                                                       style="color: #0237a6;"/>
                                        <div style="padding-top: 5px;">在册职工总数</div>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasicinfo.externalNum}人"
                                                       style="color: #0237a6;"/>
                                        <div style="padding-top: 5px;">外委人员数</div>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasicinfo.victimsNum}人"
                                                       style="color: #ca681f;"/>
                                        <div style="padding-top: 5px;">接害总人数</div>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasicinfo.occupationalDiseasesNum}人"
                                                       style="color: #ca681f;"/>
                                        <div style="padding-top: 5px;">职业病累计人数</div>
                                    </div>
                                </div>
                                <div class="con_sep">
                                </div>
                                <div class="toobar_3">
                                    <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                    </div>
                                    培训情况
                                </div>
                                <div style="display: flex;flex-direction: row;align-items: baseline;padding: 10px 0">
                                    <div style="display: flex;flex-direction: row;align-items: baseline;padding: 0 10px;">
                                        <div style="">主要负责人：</div>
                                        <p:outputLabel
                                                value="#{mgrbean.unitbasicinfo.trainSituation.ifLeadersTrain eq 1?'已':'未'}培训"
                                                style=""/>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: row;align-items: baseline;padding: 0 10px;">
                                        <div style="padding-top: 5px;">职业卫生管理员：</div>
                                        <p:outputLabel
                                                value="#{mgrbean.unitbasicinfo.trainSituation.ifManagersTrain eq 1?'已':'未'}培训"
                                                style=""/>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: row;align-items: baseline;padding: 0 10px;">
                                        <div style="padding-top: 5px;">接害职业病危害因素培训总人数：</div>
                                        <p:outputLabel value="#{mgrbean.unitbasicinfo.trainSituation.trainSum}人"
                                                       style=""/>
                                    </div>
                                </div>
                                <div class="con_sep">
                                </div>
                                <div class="toobar_3">
                                    <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                    </div>
                                    主要产品
                                </div>
                                <div style="padding: 10px 0">
                                    <div style="padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasicinfo.proStr}" style=""/>
                                    </div>
                                </div>
                                <div class="con_sep">
                                </div>
                                <div class="toobar_3">
                                    <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                    </div>
                                    技术服务机构
                                </div>
                                <div style="padding: 0;">
                                    <div style="padding: 10px;display: flex;align-items: flex-start;flex-direction: row;width: calc(100% - 20px);">
                                        <p:outputLabel value="检查机构" style="width: 15%;"/>
                                        <p:outputLabel style="flex: 1;"
                                                       value="#{mgrbean.unitbasicinfo.hethOrgStr}"/>
                                    </div>
                                    <div style="padding: 10px;display: flex;align-items: flex-start;flex-direction: row;width: calc(100% - 20px);">
                                        <p:outputLabel value="检测机构" style="width: 15%;"/>
                                        <p:outputLabel style="flex: 1;"
                                                       value="#{mgrbean.unitbasicinfo.jcOrgStr}"/>
                                    </div>
                                </div>
                            </p:outputPanel>
                            <!--场所监测-->
                            <p:outputPanel rendered="#{mgrbean.showArchives eq 2}">
                                <div class="toobar_2">基本情况概况</div>
                                <div class="toobar_3">
                                    <div style="width: 3px; height: 13px; background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                    </div>
                                    总体情况
                                </div>
                                <div style="display: flex;flex-direction: row;align-items: center;padding: 10px 0">
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasic.value1}人" style="color: #0237a6;"/>
                                        <div style="padding-top: 5px;">在岗职工总数</div>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasic.value2}人"
                                                       style="color: #0237a6;"/>
                                        <div style="padding-top: 5px;">外委人员数</div>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: column;align-items: center;padding: 0 10px;">
                                        <p:outputLabel value="#{mgrbean.unitbasic.value3}人"
                                                       style="color: #ca681f;"/>
                                        <div style="padding-top: 5px;">接害总人数</div>
                                    </div>
                                </div>
                                <div class="con_sep">
                                </div>
                                <div class="toobar_3">
                                    <div style="width: 3px; height: 13px; background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                    </div>
                                    培训情况
                                </div>
                                <div style="display: flex;flex-direction: row;align-items: center;padding: 10px 0">
                                    <div style="display: flex;flex-direction: row;align-items: baseline;padding: 0 10px;">
                                        <div>用人单位负责人：</div>
                                        <p:outputLabel
                                                value="#{mgrbean.unitbasic.value4}"/>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: row;align-items: baseline;padding: 0 10px;">
                                        <div style="padding-top: 5px;">职业卫生管理人员：</div>
                                        <p:outputLabel
                                                value="#{mgrbean.unitbasic.value5}"/>
                                    </div>
                                    <div style="width: 1px;height: 12px;background: #e6e6e6;margin: 0 5px;">
                                    </div>
                                    <div style="display: flex;flex-direction: row;align-items: baseline;padding: 0 10px;">
                                        <div style="padding-top: 5px;">接害职业病危害因素培训总人数：</div>
                                        <p:outputLabel value="#{mgrbean.unitbasic.value6}人"/>
                                    </div>
                                </div>
                                <div class="con_sep">
                                </div>
                                <c:if test="#{mgrbean.unitharmChkInfos != null and mgrbean.unitharmChkInfos.size() > 0}">
                                    <div class="toobar_2">上一年度检测情况</div>
                                    <c:forEach items="#{mgrbean.unitharmChkInfos}" var="unitharmChkInfo">
                                        <p:outputPanel>
                                            <div class="toobar_3">
                                                <div style="width: 3px; height: 13px; background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                                </div>
                                                <div>#{unitharmChkInfo.name}</div>
                                            </div>
                                            <p:panelGrid style="width: 100%;">
                                                <f:facet name="header">
                                                    <p:row>
                                                        <p:column style="padding-left:5px;height: 20px;width: 28%;">
                                                            <h:outputText value="接触危害因素"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="检测点数"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="超标点数"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="检测岗位数"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="超标岗位数"/>
                                                        </p:column>
                                                    </p:row>
                                                </f:facet>
                                                <c:if test="#{unitharmChkInfo.unitbasicInfos != null and unitharmChkInfo.unitbasicInfos.size() > 0}">
                                                    <c:forEach items="#{unitharmChkInfo.unitbasicInfos}" var="itm">
                                                        <p:row>
                                                            <p:column style="padding-left:5px;height: 20px;">
                                                                <h:outputText value="#{itm.value1}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value2}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value3}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value4}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value5}"/>
                                                            </p:column>
                                                        </p:row>
                                                    </c:forEach>
                                                </c:if>
                                            </p:panelGrid>
                                        </p:outputPanel>
                                    </c:forEach>
                                </c:if>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:outputPanel styleClass="right_con">
                        <p:outputPanel styleClass="right_con1">
                            <p:outputPanel rendered="#{mgrbean.showArchives eq 1}">
                                <div class="toobar_2">职业病危害因素综合分析</div>
                                <p:outputPanel rendered="#{mgrbean.unitbasicinfo.unitfactorcrowd.ifhfDust eq 1}">
                                    <div class="toobar_3">
                                        <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                        </div>
                                        <div style="width: 100px;">粉尘</div>
                                        <div style="display: flex;flex-direction: row;align-items: baseline;">
                                            <div>接触总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitfactorcrowd.hfDustPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitfactorcrowd.hfDustPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitfactorcrowd.hfDustPeoples}"/>
                                            <div>体检总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unithealthcustody.heaDustPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unithealthcustody.heaDustPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unithealthcustody.heaDustPeoples}"/>
                                            <div>检测总点数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatDustAllChecknum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatDustAllChecknum}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatDustAllChecknum}"/>
                                            <div>总超标点数：</div>
                                            <p:outputLabel style="color: #FE1118;width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatDustAllExcessnum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatDustAllExcessnum}"/>
                                            <p:outputLabel style="color: #FE1118;width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatDustAllExcessnum}"/>
                                        </div>
                                    </div>
                                    <p:panelGrid style="width: 100%;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;width: 200px;">
                                                    <h:outputText value="接触危害因素"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="接触人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="检测点数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach items="#{mgrbean.unitbasicinfo.detailList.get(0)}" var="itm">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;">
                                                    <h:outputText value="#{itm.get(0)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(1)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(2)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(3)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(4)}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{mgrbean.unitbasicinfo.unitfactorcrowd.ifhfChemistry eq 1}">
                                    <div class="toobar_3">
                                        <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                        </div>
                                        <div style="width: 100px;">化学</div>
                                        <div style="display: flex;flex-direction: row;align-items: baseline;">
                                            <div>接触总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitfactorcrowd.hfChemistryPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitfactorcrowd.hfChemistryPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitfactorcrowd.hfChemistryPeoples}"/>
                                            <div>体检总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unithealthcustody.heaChemistryPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unithealthcustody.heaChemistryPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unithealthcustody.heaChemistryPeoples}"/>
                                            <div>检测总点数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatChemistryAllChecknum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatChemistryAllChecknum}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatChemistryAllChecknum}"/>
                                            <div>总超标点数：</div>
                                            <p:outputLabel style="color: #FE1118;width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatChemistryAllExcessnum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatChemistryAllExcessnum}"/>
                                            <p:outputLabel style="color: #FE1118;width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatChemistryAllExcessnum}"/>
                                        </div>
                                    </div>
                                    <p:panelGrid style="width: 100%;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;width: 200px;">
                                                    <h:outputText value="接触危害因素"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="接触人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="检测点数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach items="#{mgrbean.unitbasicinfo.detailList.get(1)}" var="itm">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;">
                                                    <h:outputText value="#{itm.get(0)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(1)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(2)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(3)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(4)}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{mgrbean.unitbasicinfo.unitfactorcrowd.ifhfPhysics eq 1}">
                                    <div class="toobar_3">
                                        <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                        </div>
                                        <div style="width: 100px;">物理</div>
                                        <div style="display: flex;flex-direction: row;align-items: baseline;">
                                            <div>接触总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitfactorcrowd.hfPhysicsPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitfactorcrowd.hfPhysicsPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitfactorcrowd.hfPhysicsPeoples}"/>
                                            <div>体检总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unithealthcustody.heaPhysicsPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unithealthcustody.heaPhysicsPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unithealthcustody.heaPhysicsPeoples}"/>
                                            <div>检测总点数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatPhysicsAllChecknum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatPhysicsAllChecknum}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatPhysicsAllChecknum}"/>
                                            <div>总超标点数：</div>
                                            <p:outputLabel style="color: #FE1118;width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatPhysicsAllExcessnum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatPhysicsAllExcessnum}"/>
                                            <p:outputLabel style="color: #FE1118;width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatPhysicsAllExcessnum}"/>
                                        </div>
                                    </div>
                                    <p:panelGrid style="width: 100%;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;width: 200px;">
                                                    <h:outputText value="接触危害因素"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="接触人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="检测点数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach items="#{mgrbean.unitbasicinfo.detailList.get(2)}" var="itm">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;">
                                                    <h:outputText value="#{itm.get(0)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(1)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(2)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(3)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(4)}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:outputPanel>
                                <p:outputPanel
                                        rendered="#{mgrbean.unitbasicinfo.unitfactorcrowd.ifhfRadioactivity eq 1}">
                                    <div class="toobar_3">
                                        <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                        </div>
                                        <div style="width: 100px;">放射</div>
                                        <div style="display: flex;flex-direction: row;align-items: baseline;">
                                            <div>接触总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitfactorcrowd.hfRadioactivityPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitfactorcrowd.hfRadioactivityPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitfactorcrowd.hfRadioactivityPeoples}"/>
                                            <div>体检总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unithealthcustody.heaRadioactivityPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unithealthcustody.heaRadioactivityPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unithealthcustody.heaRadioactivityPeoples}"/>
                                            <div>检测总点数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatRadioactivityChecknum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatRadioactivityChecknum}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatRadioactivityChecknum}"/>
                                            <div>总超标点数：</div>
                                            <p:outputLabel style="color: #FE1118;width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatRadioactivityExcessnum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatRadioactivityExcessnum}"/>
                                            <p:outputLabel style="color: #FE1118;width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatRadioactivityExcessnum}"/>
                                        </div>
                                    </div>
                                    <p:panelGrid style="width: 100%;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;width: 200px;">
                                                    <h:outputText value="接触危害因素"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="接触人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="检测点数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach items="#{mgrbean.unitbasicinfo.detailList.get(3)}" var="itm">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;">
                                                    <h:outputText value="#{itm.get(0)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(1)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(2)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(3)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(4)}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{mgrbean.unitbasicinfo.unitfactorcrowd.ifhfBiology eq 1}">
                                    <div class="toobar_3">
                                        <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                        </div>
                                        <div style="width: 100px;">生物</div>
                                        <div style="display: flex;flex-direction: row;align-items: baseline;">
                                            <div>接触总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitfactorcrowd.hfBiologyPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitfactorcrowd.hfBiologyPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitfactorcrowd.hfBiologyPeoples}"/>
                                            <div>体检总人数：</div>
                                            <p:outputLabel style="width: 30px;" value="—"/>
                                            <div>检测总点数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherChecknum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherChecknum}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherChecknum}"/>
                                            <div>总超标点数：</div>
                                            <p:outputLabel style="color: #FE1118;width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherExcessnum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherExcessnum}"/>
                                            <p:outputLabel style="color: #FE1118;width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherExcessnum}"/>
                                        </div>
                                    </div>
                                    <p:panelGrid style="width: 100%;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;width: 200px;">
                                                    <h:outputText value="接触危害因素"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="接触人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="检测点数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach items="#{mgrbean.unitbasicinfo.detailList.get(4)}" var="itm">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;">
                                                    <h:outputText value="#{itm.get(0)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(1)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(2)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(3)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(4)}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{mgrbean.unitbasicinfo.unitfactorcrowd.ifhfOther eq 1}">
                                    <div class="toobar_3">
                                        <div style="width: 3px;height: 13px;background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                        </div>
                                        <div style="width: 100px;">其他</div>
                                        <div style="display: flex;flex-direction: row;align-items: baseline;">
                                            <div>接触总人数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitfactorcrowd.hfOtherPeoples}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitfactorcrowd.hfOtherPeoples}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitfactorcrowd.hfOtherPeoples}"/>
                                            <div>体检总人数：</div>
                                            <p:outputLabel style="width: 30px;" value="—"/>
                                            <div>检测总点数：</div>
                                            <p:outputLabel style="width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherChecknum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherChecknum}"/>
                                            <p:outputLabel style="width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherChecknum}"/>
                                            <div>总超标点数：</div>
                                            <p:outputLabel style="color: #FE1118;width: 30px;"
                                                           value="#{mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherExcessnum}"
                                                           rendered="#{not empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherExcessnum}"/>
                                            <p:outputLabel style="color: #FE1118;width: 30px;" value="—"
                                                           rendered="#{empty mgrbean.unitbasicinfo.unitharmfactorcheck.ifatBiologyotherExcessnum}"/>
                                        </div>
                                    </div>
                                    <p:panelGrid style="width: 100%;">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;width: 200px;">
                                                    <h:outputText value="接触危害因素"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="接触人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="体检人数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="检测点数"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;width: 80px;">
                                                    <h:outputText value="超标点数"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach items="#{mgrbean.unitbasicinfo.detailList.get(5)}" var="itm">
                                            <p:row>
                                                <p:column style="padding-left:5px;height: 20px;">
                                                    <h:outputText value="#{itm.get(0)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(1)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(2)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(3)}"/>
                                                </p:column>
                                                <p:column style="padding-left:5px;height: 20px;text-align: center;">
                                                    <h:outputText value="#{itm.get(4)}"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </p:panelGrid>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!--场所监测-->
                            <p:outputPanel rendered="#{mgrbean.showArchives eq 2}">
                                <c:if test="#{mgrbean.unitHethCusInfos != null and mgrbean.unitHethCusInfos.size() > 0}">
                                    <div class="toobar_2">上一年度职业健康检查情况</div>
                                    <c:forEach items="#{mgrbean.unitHethCusInfos}" var="unitHethCusInfo">
                                        <p:outputPanel>
                                            <div class="toobar_3">
                                                <div style="width: 3px; height: 13px; background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                                </div>
                                                <div>#{unitHethCusInfo.name}</div>
                                            </div>
                                            <p:panelGrid style="width: 100%;">
                                                <f:facet name="header">
                                                    <p:row>
                                                        <p:column style="padding-left:5px;height: 20px;width: 28%;">
                                                            <h:outputText value="接触危害因素"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="接触人数"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="应复查人数"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="实际复查人数"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 18%;">
                                                            <h:outputText value="异常人数"/>
                                                        </p:column>
                                                    </p:row>
                                                </f:facet>
                                                <c:if test="#{unitHethCusInfo.unitbasicInfos != null and unitHethCusInfo.unitbasicInfos.size() > 0}">
                                                    <c:forEach items="#{unitHethCusInfo.unitbasicInfos}" var="itm">
                                                        <p:row>
                                                            <p:column style="padding-left:5px;height: 20px;">
                                                                <h:outputText value="#{itm.value1}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value2}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value3}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value4}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value5}"/>
                                                            </p:column>
                                                        </p:row>
                                                    </c:forEach>
                                                </c:if>
                                            </p:panelGrid>
                                        </p:outputPanel>
                                    </c:forEach>
                                </c:if>
                                <c:if test="#{mgrbean.resultProInfos != null and mgrbean.resultProInfos.size() > 0}">
                                    <div class="toobar_2">监测结果</div>
                                    <c:forEach items="#{mgrbean.resultProInfos}" var="resultProInfo">
                                        <p:outputPanel>
                                            <div class="toobar_3">
                                                <div style="width: 3px; height: 13px; background: #2e6e9e;margin-top: 1px;margin-right: 8px;">
                                                </div>
                                                <div>#{resultProInfo.name}</div>
                                            </div>
                                            <p:panelGrid style="width: 100%;">
                                                <f:facet name="header">
                                                    <p:row>
                                                        <p:column style="padding-left:5px;height: 20px;width: 25%;">
                                                            <h:outputText value="岗位/环节"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 25%;">
                                                            <h:outputText value="检测项目"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 25%;">
                                                            <h:outputText value="检测项目是否合格"/>
                                                        </p:column>
                                                        <p:column style="padding-left:5px;height: 20px;width: 25%;">
                                                            <h:outputText value="检测岗位是否合格"/>
                                                        </p:column>
                                                    </p:row>
                                                </f:facet>
                                                <c:if test="#{resultProInfo.unitbasicInfos != null and resultProInfo.unitbasicInfos.size() > 0}">
                                                    <c:forEach items="#{resultProInfo.unitbasicInfos}" var="itm">
                                                        <p:row>
                                                            <p:column style="padding-left:5px;height: 20px;">
                                                                <h:outputText value="#{itm.value1}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value2}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value3}"/>
                                                            </p:column>
                                                            <p:column
                                                                    style="padding-left:5px;height: 20px;text-align: center;">
                                                                <h:outputText value="#{itm.value4}"/>
                                                            </p:column>
                                                        </p:row>
                                                    </c:forEach>
                                                </c:if>
                                            </p:panelGrid>
                                        </p:outputPanel>
                                    </c:forEach>
                                </c:if>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:outputPanel>
            </p:outputPanel>
        </p:outputPanel>
        <p:dialog id="occRiskClassDialog" header="职业病危害因素" widgetVar="OccRiskClassDialog"
                  resizable="false" width="850" height="400" modal="true">
            <p:inputText style="visibility: hidden;width: 0;position: absolute;"/>
            <p:scrollPanel id="occRiskClassTablePanel" mode="native" style="border:0;padding-bottom:5px;margin-top:5px">
                <p:dataTable id="occRiskClassTable"
                             style="margin-top: 5px;margin-bottom: 5px;"
                             value="#{mgrbean.occRiskClassList}" var="occRiskClass" rows="10"
                             paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="10,20,50" pageLinks="5"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             emptyMessage="没有您要找的记录！">
                    <p:column headerText="职业病危害因素种类" style="text-align:center;width:150px;">
                        <h:outputText value="#{occRiskClass[0]}"/>
                    </p:column>
                    <p:column headerText="职业病危害因素">
                        <h:outputText value="#{occRiskClass[1]}"/>
                        <p:outputLabel
                                style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;color: white;white-space:nowrap;"
                                rendered="#{occRiskClass[3] eq '1'}">
                            <h:outputText value="高毒"/>
                        </p:outputLabel>
                        <p:outputLabel
                                style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;color: white;white-space:nowrap;"
                                rendered="#{occRiskClass[4] eq '1'}">
                            <h:outputText value="致癌"/>
                        </p:outputLabel>
                    </p:column>
                </p:dataTable>
            </p:scrollPanel>
        </p:dialog>
    </ui:define>
</ui:composition>