<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <p:outputPanel style="border-radius: 6px;background-color: #ffffff;">
            <h:form id="mainForm">
                <h:outputStylesheet name="css/default.css"/>
                <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
                <h:outputStylesheet name="css/ui-tabs.css"/>
				
				<ui:param name="mgrbean" value="#{tdZyUnitBasicInfoViewBean}"/>
				<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                    <f:facet name="header">
                    	<p:row>
				            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				                <h:outputText value="在线申报档案"/>
				            </p:column>
				        </p:row>
                    </f:facet>
                </p:panelGrid>
                <p:fieldset legend="基本信息">
					<p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="申报类型："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;width:220px;">
				                <p:outputLabel value="初次申报" rendered="#{mgrbean.unitbasicinfo.declareType==0}"/>
				                <p:outputLabel value="变更申报" rendered="#{mgrbean.unitbasicinfo.declareType==1}"/>
				                <p:outputLabel value="年度更新" rendered="#{mgrbean.unitbasicinfo.declareType==2}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;width:217px;">
				                <p:outputLabel value="申报年份："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;width:220px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.declareYear}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;width:230px;">
				                <p:outputLabel value="申报日期："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.declareDate}">
				                	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
				                </p:outputLabel>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="单位名称："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.unitName}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="所属地区："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.fkByZoneId.fullName}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="统一社会信用代码："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.creditCode}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="单位注册地址："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;" colspan="3">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.regAddr}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="作业场所地址："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.workAddr}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="企业规模："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.fkByEnterpriseScaleId.codeName}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="行业类别："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.fkByIndustryCateId.codeName}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="经济类型："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.fkByEconomicId.codeName}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="填报人："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.fillMan}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="联系电话："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.fillPhone}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="本单位在册职工总数："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{null==mgrbean.unitbasicinfo.empNum?'/':mgrbean.unitbasicinfo.empNum}"/>
				                <p:outputLabel value="人" rendered="#{null!=mgrbean.unitbasicinfo.empNum}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="法定代表人："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.legalPerson}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="联系电话："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.legalPersonPhone}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="外委人员总数："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{null==mgrbean.unitbasicinfo.externalNum?'/':mgrbean.unitbasicinfo.externalNum}"/>
				                <p:outputLabel value="人" rendered="#{null!=mgrbean.unitbasicinfo.externalNum}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="职业卫生管理联系人："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.linkManager}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="手机号码："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mgrbean.unitbasicinfo.linkPhone}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;">
				                <p:outputLabel value="接害总人数（含外委）："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{mull==mgrbean.unitbasicinfo.victimsNum?'/':mgrbean.unitbasicinfo.victimsNum}"/>
				                <p:outputLabel value="人" rendered="#{null!=mgrbean.unitbasicinfo.victimsNum}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;">
				                <p:outputLabel value="职业病累计人数："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;" colspan="5">
				                <p:outputLabel value="#{null==mgrbean.unitbasicinfo.occupationalDiseasesNum?'/':mgrbean.unitbasicinfo.occupationalDiseasesNum}"/>
				                <p:outputLabel value="人" rendered="#{null!=mgrbean.unitbasicinfo.occupationalDiseasesNum}"/>
				            </p:column>
				        </p:row>
				   </p:panelGrid>
				</p:fieldset>
				<p:fieldset legend="职业卫生培训情况" style="margin-top:10px;">
					<p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="主要负责人培训："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;width:220px;">
				                <p:outputLabel value="有" rendered="#{1==mgrbean.trainSituation.ifLeadersTrain}"/>
				                <p:outputLabel value="无" rendered="#{0==mgrbean.trainSituation.ifLeadersTrain}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;width:217px;">
				                <p:outputLabel value="职业卫生管理人员培训："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;width:220px;">
				            	<p:outputLabel value="有" rendered="#{1==mgrbean.trainSituation.ifManagersTrain}"/>
				                <p:outputLabel value="无" rendered="#{0==mgrbean.trainSituation.ifManagersTrain}"/>
				            </p:column>
				            <p:column style="text-align:right;padding-right:3px;width:230px;">
				                <p:outputLabel value="接触职业病危害因素年度培训总人数："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;">
				                <p:outputLabel value="#{null==mgrbean.trainSituation.trainSum?'/':mgrbean.trainSituation.trainSum}"/>
				                <p:outputLabel value="人" rendered="#{null!=mgrbean.trainSituation.trainSum}"/>
				            </p:column>
				        </p:row>
				   </p:panelGrid>
				</p:fieldset>
                <p:fieldset legend="主要产品" style="margin-top:10px;">
                	<p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                		<p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="军工等涉密产品："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="6">
				                <p:outputLabel value="/" rendered="#{null==mgrbean.unitbasicinfo.ifWarProduct}"/>
				                <p:outputLabel value="是" rendered="#{1==mgrbean.unitbasicinfo.ifWarProduct}"/>
				                <p:outputLabel value="否" rendered="#{0==mgrbean.unitbasicinfo.ifWarProduct}"/>
				            </p:column>
				        </p:row>
                		<c:forEach items="#{mgrbean.proList}" var="pro">
                			<p:row>
                				<p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                	<p:outputLabel value="产品名称："/>
					            </p:column>
					            <p:column style="text-align:left;padding-left:10px;width:220px;">
					                <p:outputLabel value="#{pro.prodName}"/>
					            </p:column>
					            <p:column style="text-align:right;padding-right:3px;width:217px;">
					                <p:outputLabel value="年产量："/>
					            </p:column>
					            <p:column style="text-align:left;padding-left:10px;width:220px;">
					                <p:outputLabel value="#{pro.annualOutput}"/>
					            </p:column>
					            <p:column style="text-align:right;padding-right:3px;width:230px;">
					                <p:outputLabel value="计量单位："/>
					            </p:column>
					            <p:column style="text-align:left;padding-left:10px;">
					                <p:outputLabel value="#{pro.unitName}"/>
					            </p:column>
				        	</p:row>
                		</c:forEach>
				    </p:panelGrid>
                </p:fieldset>
                <p:fieldset legend="职业病危害因素种类" style="margin-top:10px;">
                	<p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
						<c:forEach items="#{mgrbean.factorcDetailList}" var="rowItm">
							<p:row>
								<c:forEach items="#{rowItm.colList}" var="colItm">
									<p:column style="#{colItm.style}" colspan="#{colItm.colspan}" rowspan="#{colItm.rowspan}">
										<p:outputLabel value="#{null == colItm.colVal ? '/' : colItm.colVal}"/>
									</p:column>
								</c:forEach>
							</p:row>
						</c:forEach>
				    </p:panelGrid>
                </p:fieldset>
                <p:fieldset legend="职业病危害因素检测情况" style="margin-top:10px;">
                	<p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                		<p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="本年度检测情况："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="5">
				                <p:outputLabel value="检测" rendered="#{1==mgrbean.factorcheck.ifat}"/>
				                <p:outputLabel value="未检测" rendered="#{0==mgrbean.factorcheck.ifat}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="检测机构名称："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="5">
				                <p:outputLabel value="#{null==mgrbean.factorcheck.testUnitNames?'/':mgrbean.factorcheck.testUnitNames}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="检验报告编号："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="5">
				            	<p:outputPanel style="width:98%;word-break: break-all;">
					                <p:outputLabel value="#{null==mgrbean.factorcheck.testReportNos?'/':mgrbean.factorcheck.testReportNos}"/>
				            	</p:outputPanel>
				            </p:column>
				        </p:row>
						<c:forEach items="#{mgrbean.harmfactDetailList}" var="rowItm">
							<p:row>
								<c:forEach items="#{rowItm.colList}" var="colItm">
									<p:column style="#{colItm.style}" colspan="#{colItm.colspan}" rowspan="#{colItm.rowspan}">
										<p:outputLabel value="#{null == colItm.colVal ? '/' : colItm.colVal}"/>
									</p:column>
								</c:forEach>
							</p:row>
						</c:forEach>
			      	</p:panelGrid>
			    </p:fieldset>
			    <p:fieldset legend="职业健康监护开展情况" style="margin-top:10px;">
                	<p:panelGrid style="width:100%;margin-top:10px;line-height: 14px;">
                		<p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="本年度岗中职业健康检查开展情况："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="5">
				                <p:outputLabel value="开展" rendered="#{1==mgrbean.healthcustody.ifhea}"/>
				                <p:outputLabel value="未开展" rendered="#{0==mgrbean.healthcustody.ifhea}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="检查机构名称："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="5">
				                <p:outputLabel value="#{null==mgrbean.healthcustody.checkUnitNames?'/':mgrbean.healthcustody.checkUnitNames}"/>
				            </p:column>
				        </p:row>
				        <p:row>
				            <p:column style="text-align:right;padding-right:3px;height: 38px;width:217px;">
				                <p:outputLabel value="检查总结报告编号："/>
				            </p:column>
				            <p:column style="text-align:left;padding-left:10px;height: 38px;" colspan="5">
				            	<p:outputPanel style="width:98%;word-break: break-all;">
					                <p:outputLabel value="#{null==mgrbean.healthcustody.checkReportNos?'/':mgrbean.healthcustody.checkReportNos}" />
				            	</p:outputPanel>
				            </p:column>
				        </p:row>
						<c:forEach items="#{mgrbean.healthDetailList}" var="rowItm">
							<p:row>
								<c:forEach items="#{rowItm.colList}" var="colItm">
									<p:column style="#{colItm.style}" colspan="#{colItm.colspan}" rowspan="#{colItm.rowspan}">
										<p:outputLabel value="#{null == colItm.colVal ? '/' : colItm.colVal}"/>
									</p:column>
								</c:forEach>
							</p:row>
						</c:forEach>
				     </p:panelGrid>
				</p:fieldset>
            </h:form>
        </p:outputPanel>
    </h:body>
</f:view>
</html>
