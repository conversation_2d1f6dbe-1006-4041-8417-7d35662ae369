<ui:composition xmlns="http://www.w3.org/1999/xhtml"
		xmlns:ui="http://java.sun.com/jsf/facelets"
		xmlns:h="http://java.sun.com/jsf/html"
		xmlns:f="http://java.sun.com/jsf/core"
		xmlns:p="http://primefaces.org/ui"
		xmlns:c="http://java.sun.com/jsp/jstl/core"
		xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
		<p:dataTable var="itm" value="#{mgrbean.onLineReportModel}" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
			paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
			rowsPerPageTemplate="#{mgrbean.pageSize}" lazy="true" emptyMessage="没有您要找的记录！"
            currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
            <p:column headerText="申报年份" style="text-align:center;width:200px;">
            	<h:outputLabel value="#{itm[4]}">
            	</h:outputLabel>
            </p:column>
            <p:column headerText="申报日期" style="text-align:center;width:200px;">
            	<h:outputLabel value="#{itm[1]}">
            		<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            	</h:outputLabel>
            </p:column>
            <p:column headerText="申报类型" style="text-align:center;width:200px;">
            	<h:outputLabel value="初次申报" rendered="#{itm[2]==0}"></h:outputLabel>
            	<h:outputLabel value="变更申报" rendered="#{itm[2]==1}"></h:outputLabel>
            	<h:outputLabel value="年度更新" rendered="#{itm[2]==2}"></h:outputLabel>
            </p:column>
            <p:column headerText="是否培训" style="text-align:center;width:150px;">
            	<h:outputLabel value="#{itm[3]==1?'是':'否'}"></h:outputLabel>
            </p:column>
            <p:column headerText="操作">
	            <p:commandLink value="详情" action="#{mgrbean.viewUnitBasicInfo}" process="@this">
	                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.unitBasicId}"/>
	            </p:commandLink>
            </p:column>
		</p:dataTable>
	<p:spacer style="height: 100px;"></p:spacer>
</ui:composition>