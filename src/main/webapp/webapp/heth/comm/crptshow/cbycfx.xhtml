<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <p:dataTable var="itm" value="#{mgrbean.warnAbnormalList}"  lazy="true" emptyMessage="没有您要找的记录！">
        <p:columnGroup type="header">
            <p:row>
                <p:column headerText="危害因素名称" rowspan="2" style="width:100px;text-align: center"/>
                <p:column headerText="职业检出异常人数" colspan="2" style="width:120px;text-align: center;"/>
                <p:column headerText="岗位超标点数" colspan="2" style="width:240px;text-align: center;"/>
            </p:row>
            <p:row>
                <p:column headerText="职业健康检查" style="width:100px;text-align: center"/>
                <p:column headerText="职业病危害申报" style="width:120px;text-align: center;"/>
                <p:column headerText="职业病危害申报" style="width:120px;text-align: center;"/>
                <p:column headerText="职业病危害因素监测" style="width:240px;text-align: center;"/>
            </p:row>
        </p:columnGroup>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.badrsnName}" />
        </p:column>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.checkNhgCheck}" style="color:#{itm.checkNhgRpt eq itm.checkNhgCheck?'':'red'};"/>
        </p:column>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.checkNhgRpt}" style="color:#{itm.checkNhgRpt eq itm.checkNhgCheck?'':'red'};"/>
        </p:column>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.postNhgRpt}" style="color:#{itm.postNhgRpt eq itm.postNhgJc?'':'red'};"/>
        </p:column>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.postNhgJc}" style="color:#{itm.postNhgRpt eq itm.postNhgJc?'':'red'};"/>
        </p:column>
    </p:dataTable>
    <p:spacer style="height: 100px;"></p:spacer>
</ui:composition>