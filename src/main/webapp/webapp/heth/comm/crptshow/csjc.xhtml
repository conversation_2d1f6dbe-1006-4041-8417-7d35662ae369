<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

    <p:dataTable id="csjcDataTable" var="itm" value="#{mgrbean.unitbasicinfoList}" paginator="true" rows="#{mgrbean.pageSize}"
                 paginatorPosition="bottom" rowIndexVar="R"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                 rowsPerPageTemplate="#{mgrbean.pageSize}" lazy="true" emptyMessage="没有您要找的记录！"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
        <p:column headerText="年份" style="text-align: center;width:200px;">
            <h:outputText value="#{null eq itm[1] ?'/':itm[1]}"/>
        </p:column>
        <p:column headerText="调查时是否申报" style="text-align: center;width:200px;">
            <h:outputText value="#{null eq itm[2]?'/':(itm[2]==1?'已申报':'未申报')}"/>
        </p:column>
        <p:column headerText="是否年度更新" style="text-align: center;width:200px;">
            <h:outputText value="#{null eq itm[3]?'/':(itm[3]==1?'已更新':'未更新')}"/>
        </p:column>
        <p:column headerText="在册职工数" style="text-align: center;width:200px;">
            <h:outputText value="#{null eq itm[4]?'/':itm[4]}"/>
        </p:column>
        <p:column headerText="外委人数" style="text-align: center;width:200px;">
            <h:outputText value="#{null eq itm[5]?'/':itm[5]}"/>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink value="详情" action="#{mgrbean.csjcViewAction}"
                           process="@this">
                <f:setPropertyActionListener target="#{mgrbean.basicInfoRid}"
                                             value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </p:dataTable>
    <p:spacer style="height: 100px;"></p:spacer>
</ui:composition>