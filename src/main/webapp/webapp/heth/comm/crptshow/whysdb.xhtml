<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

    <p:dataTable var="itm" value="#{mgrbean.warnBadrsnList}"  lazy="true" emptyMessage="没有您要找的记录！">
        <p:columnGroup type="header">
            <p:row>
                <p:column headerText="职业健康检查" style="width:100px;text-align: center"/>
                <p:column headerText="职业病危害申报" style="width:120px;text-align: center;"/>
                <p:column headerText="职业病危害因素监测" style="width:240px;text-align: center;"/>
            </p:row>
        </p:columnGroup>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.badrsnNameCheck}" style="color:#{itm.badrsnNameCheck eq itm.badrsnNameRpt and itm.badrsnNameRpt eq itm.badrsnNameJc?'':'red'};"/>
        </p:column>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.badrsnNameRpt}" style="color:#{itm.badrsnNameCheck eq itm.badrsnNameRpt and itm.badrsnNameRpt eq itm.badrsnNameJc?'':'red'};"/>
        </p:column>
        <p:column style="text-align: center">
            <h:outputText value="#{itm.badrsnNameJc}" style="color:#{itm.badrsnNameCheck eq itm.badrsnNameRpt and itm.badrsnNameRpt eq itm.badrsnNameJc?'':'red'};"/>
        </p:column>
    </p:dataTable>
    <p:spacer style="height: 100px;"></p:spacer>
</ui:composition>