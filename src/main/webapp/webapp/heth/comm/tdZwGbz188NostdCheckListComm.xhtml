<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwGbz188NostdCheckListCommBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/tdZwGbz188NostdCheckEditComm.xhtml" />

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
            a:hover {
                color: #25AAE1;
                text-decoration: none;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="GBZ188不规范数据审核-#{mgrbean.zoneType==2?'终审':(mgrbean.zoneType==3?'复审':'初审')}"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:spacer width="5"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" id="plsh" action="#{mgrbean.openReviewConfirmDialog}" process="@this,dataTable"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="提示：" style="color:red;" rendered="#{mgrbean.ifShowTipInfo}"/>
                <h:outputText value="#{mgrbean.tipInfo}"
                              style="color:blue;" rendered="#{mgrbean.ifShowTipInfo}"/>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="用人单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 6px;width:360px;">
                <h:panelGrid columns="3" id="searchCrptZone" style="border-color: transparent;margin: -6px;padding: 0px;">
                    <zwx:ZoneSingleComp zoneList="#{mgrbean.crptZoneList}"
                                        zoneCode="#{mgrbean.searchCrptZoneCode}"
                                        zoneName="#{mgrbean.searchCrptZoneName}"/>
                </h:panelGrid>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;" >
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchPersonnelName" value="#{mgrbean.searchPersonName}" style="width: 180px;" maxlength="25" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 160px;height: 38px;">
                <h:outputText value="检查机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:7px;" >
                <p:outputPanel >
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
                                            resetValues="true" update="unitName" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="#{mgrbean.receiveDate==2?'体检日期':'接收日期'}：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvBdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBhkBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvEdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收结束日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBhkEdate}" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width: 160px;height: 33px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{itm[14]=='1'?false:true}"/>

        <p:column headerText="地区" style="width: 120px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="用人单位名称" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="人员姓名" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="检查机构" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="体检日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="报告出具日期" style="width:80px;text-align: center;" rendered="#{mgrbean.receiveDate==1}">
            <h:outputLabel value="#{itm[7]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="不规范原因" style="padding-left: 3px; width:200px;">
            <p:outputPanel rendered="#{null!=itm[8]}">
                <h:outputLink value="#" id="fade" rendered="#{itm[8].length() gt 20}">
                    <h:outputText value="#{itm[8].substring(0,20)}..."/>
                    <p:tooltip for="@parent" value="#{itm[8]}" style="width:300px"/>
                </h:outputLink>
                <h:outputText value="#{itm[8]}" rendered="#{itm[8].length() le 20}"/>
            </p:outputPanel>
            <h:outputText value="——" rendered="#{null==itm[8]}"/>
        </p:column>
        <p:column headerText="不规范原因说明" style="width:160px;">
            <p:outputPanel rendered="#{null!=itm[9]}">
                <h:outputLink value="#" id="nostdDesc" rendered="#{itm[9].length() gt 20}">
                    <h:outputText value="#{itm[9].substring(0,20)}..."/>
                    <p:tooltip for="@parent" value="#{itm[9]}" style="width:300px"/>
                </h:outputLink>
                <h:outputText value="#{itm[9]}" rendered="#{itm[9].length() le 20}"/>
            </p:outputPanel>
            <h:outputText value="——" rendered="#{null==itm[9]}"/>
        </p:column>
        <p:column headerText="审核规范" style="width:60px;text-align: center;">
            <h:outputText value="——" rendered="#{itm[10]==null}"/>
            <h:outputText value="否" rendered="#{itm[10]==0}"/>
            <h:outputText value="是" rendered="#{itm[10]==1}"/>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;text-align: center;" >
            <h:outputLabel value="#{itm[11]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="审核期限" style="width:80px;text-align: center;" rendered="#{mgrbean.ifshowdeadline=='true'}">
            <p:outputLabel rendered="#{itm[14]=='1'}">
                <p:outputLabel rendered="#{itm[13] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[13] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
                    <h:outputText value="当天截止" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[13] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
                    <h:outputText value="剩余#{itm[13]}天" style="color:#FFFFFF"/>
                </p:outputLabel>
            </p:outputLabel>
            <h:outputText value="——" rendered="#{itm[14]!='1'}"/>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <p:outputPanel rendered="#{mgrbean.zoneType lt 3}">
                <h:outputLabel value="待审核" rendered="#{itm[12]==5}"/>
                <h:outputLabel value="已审核" rendered="#{itm[12]==7}"/>
                <h:outputLabel value="已退回" rendered="#{itm[12]==2 or itm[12]==4 or itm[12]==6}" style="color: red"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.zoneType eq 3}">
                <h:outputLabel value="待审核" rendered="#{itm[12]==3}"/>
                <h:outputLabel value="已审核" rendered="#{itm[12]==5 or itm[12]==7}"/>
                <h:outputLabel value="已退回" rendered="#{itm[12]==2 or itm[12]==4 or itm[12]==6}" style="color: red"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.zoneType gt 3}">
                <h:outputLabel value="待审核" rendered="#{itm[12]==1}"/>
                <h:outputLabel value="已审核" rendered="#{itm[12]==3 or itm[12]==5 or itm[12]==7}"/>
                <h:outputLabel value="已退回" rendered="#{itm[12]==2 or itm[12]==4 or itm[12]==6}" style="color: red"/>
            </p:outputPanel>
        </p:column>

        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="#{itm[14]=='1'?'审核':'详情'}" action="#{mgrbean.modInitAction}" process="@this" oncomplete="$('html, body, .content').animate({scrollTop: 0}, 'slow');" update=":tabView" >
                <f:setPropertyActionListener value="#{itm[17]}" target="#{mgrbean.bhkId}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{itm[14]=='1'}" target="#{mgrbean.ifCheck}"/>
                <f:setPropertyActionListener value="#{itm[18]}" target="#{mgrbean.ifCityDirect}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <ui:include src="/webapp/heth/comm/reviewConfirmDialogComm.xhtml"/>
    </ui:define>
</ui:composition>