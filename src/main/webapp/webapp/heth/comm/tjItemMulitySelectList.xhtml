<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body style="overflow-y:hidden;"  onload="document.getElementById('codeForm:pym').focus();">
    <title>体检项目</title>
    <h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	<h:outputStylesheet name="css/ui-tabs.css"/>
        <h:form id="codeForm">
        	<p:outputPanel styleClass="zwx_toobar_42">
		        <h:panelGrid columns="3"
		                     style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
		                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
		            <p:commandButton value="确定" icon="ui-icon-check"
		                             action="#{tjItemMulitySelectListBean.submitAction}"
		                             process="@this,selectedTable"/>
		             <p:commandButton value="取消" icon="ui-icon-close"
		                             action="#{tjItemMulitySelectListBean.dialogClose}" process="@this"/>
		        </h:panelGrid>
		    </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                            <p:outputLabel value="项目分类：" styleClass="zwx_dialog_font"/>
                            <p:selectOneMenu value="#{tjItemMulitySelectListBean.firstCodeNo}">
                                <f:selectItem itemValue="" itemLabel="--全部--"/>
                                <f:selectItems value="#{tjItemMulitySelectListBean.firstList}" var="itm" itemValue="#{itm.codeLevelNo}" itemLabel="#{itm.codeName}"/>
                                <p:ajax event="change" listener="#{tjItemMulitySelectListBean.searchAction}" process="@this,searchPanel" update="selectedTable"/>
                            </p:selectOneMenu>
                            <p:spacer width="5"></p:spacer>
                            <p:outputLabel value="名称：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{tjItemMulitySelectListBean.searchNamOrPy}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedTable" process="@this,searchPanel" listener="#{tjItemMulitySelectListBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{tjItemMulitySelectListBean.displayList}" id="selectedTable"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorPosition="bottom">
                <p:column headerText="选择" style="text-align:center;width:30px;">
                	<p:selectBooleanCheckbox value="#{itm.ifSelected}">
                		<p:ajax event="change" listener="#{tjItemMulitySelectListBean.selectAction(itm)}" process="@this,selectedTable" update="selectedTable"></p:ajax>
                	</p:selectBooleanCheckbox>
                </p:column>
                <p:column headerText="名称" style="padding-left: 3px;">
                	<div style="display: flex;align-items: center;">
	                	<h:outputText escape="false" value="#{(itm.rid == -1) ? '' :'&#160;&#160;&#160;&#160;'}#{itm.itemName}" style="padding-right:10px;"/>
                	</div>
                </p:column>
                <p:column headerText="不合格" style="width:50px;text-align:center;">
                	<p:selectBooleanCheckbox value="#{itm.ifNotHg}" disabled="#{!itm.ifSelected}" rendered="#{itm.rid != -1}">
                		<p:ajax event="change" process="@this,selectedTable" update="selectedTable"></p:ajax>
                	</p:selectBooleanCheckbox>
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
    </h:body>
</f:view>
</html>
