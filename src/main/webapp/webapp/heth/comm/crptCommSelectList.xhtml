<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 标题 -->
    <h:head>
        <title><h:outputText value="单位信息" /></title>
        <h:outputStylesheet name="css/default.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css" />
        <h:outputScript name="js/namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style type="text/css">
            .areaZone .ui-commandlink{
                left: -24px !important;
            }
            #selectForm\:addCrptCommDialog_title{
                margin:0px;
            }
            .mySymptomPanelGrid tr{
                border: 0px transparent !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function removeExtraZonePael(elStr){
                //移除掉多余的地区框
                var el = jQuery(elStr);
                if(el.length>1){
                    el.each(function(index){
                        if(index>0){
                            $(this).remove();
                        }
                    });
                }
            }
            //]]>
        </script>
    </h:head>

    <h:body>
        <h:form id="selectForm">
            <div style="float: left;font-size: 12px;width: 100%;">
                <!-- 查询条件 -->
                <table style="width: 100%">
                    <tr>
                        <td style="text-align: right;width: 45px;" class="zwx_dialog_font">
                            <h:outputText value="地区："/>
                        </td>
                        <td style="text-align:left;width: 100px;">
                            <zwx:ZoneSingleNewComp zoneList="#{crptCommSelectListBean.zoneList}"
                                                   zoneCode="#{crptCommSelectListBean.searchZoneCode}"
                                                   zoneId="#{crptCommSelectListBean.searchZoneId}" zonePaddingLeft="0"
                                                   zoneName="#{crptCommSelectListBean.searchZoneName}" id="searchZone"
                                                   ifShowTrash="false"/>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="单位名称："/>
                        </td>
                        <td style="text-align: left;vertical-align: middle;width: 120px;">
                            <p:inputText maxlength="50" style="width:180px;" id="searchCrptName" value="#{crptCommSelectListBean.searchCrptName}">
                            </p:inputText>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="社会信用代码："/>
                        </td>
                        <td style="text-align: left;width: 120px;">
                            <p:inputText id="searchCrptCode" value="#{crptCommSelectListBean.searchCrptCode}" maxlength="25" style="width:180px;">
                            </p:inputText>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this,searchZone,searchCrptName,searchCrptCode" value="查询"
                                             action="#{crptCommSelectListBean.searchAction}"
                                             resetValues="selectForm:crptCommTable"
                                             update="selectForm:crptCommTable"/>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this,addCrptCommPanel" value="添加"
                                action="#{crptCommSelectListBean.addCrptCommAction}"
                                resetValues="true" oncomplete="removeExtraZonePael('#selectForm\\:areaZone\\:zonePanel')"
                                 rendered="#{crptCommSelectListBean.ifEdit}"/>
                        </td>
                    </tr>
                </table>

                <!-- 表格 -->
                <p:dataTable var="crptComm" value="#{crptCommSelectListBean.crptCommList}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="10,20,50" id="crptCommTable" lazy="true" emptyMessage="没有您要找的记录！"
                             rowKey="#{crptComm.rid}" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                    <p:column headerText="选择" style="width:60px;text-align:center;">
                        <p:commandLink value="选择" process="@this" action="#{crptCommSelectListBean.selectCrptCommAction}">
                            <f:setPropertyActionListener target="#{crptCommSelectListBean.selectCrptComm}" value="#{crptComm}"/>
                        </p:commandLink>
                        <p:spacer width="5" rendered="#{crptCommSelectListBean.ifEdit}"/>
                        <p:commandLink process="@this" value="修改" oncomplete="removeExtraZonePael('#selectForm\\:areaZone\\:zonePanel')"
                                       action="#{crptCommSelectListBean.modCrptCommAction}" rendered="#{crptCommSelectListBean.ifEdit}"
                                       resetValues="true"
                        >
                            <f:setPropertyActionListener target="#{crptCommSelectListBean.crptCommId}" value="#{crptComm.rid}"/>
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="地区名称" width="180px;">
                        <h:outputText value="#{crptComm.tsZoneByZoneId.fullName}" rendered="#{2 >= crptComm.tsZoneByZoneId.realZoneType}"/>
                        <h:outputText value="#{fn:substringAfter(crptComm.tsZoneByZoneId.fullName,'_')}" rendered="#{crptComm.tsZoneByZoneId.realZoneType > 2}"/>
                    </p:column>
                    <p:column headerText="单位名称"  width="230px;">
                        <h:outputText value="#{crptComm.crptName}"/>
                    </p:column>
                    <p:column headerText="社会信用代码" width="100px;" style="text-align: center;">
                        <h:outputText value="#{crptComm.institutionCode}"/>
                    </p:column>
                    <p:column headerText="分支机构" width="60px;" style="text-align: center;" >
                        <h:outputText value="否" rendered="#{crptComm.ifSubOrg == 0}"/>
                        <h:outputText value="是" rendered="#{crptComm.ifSubOrg == 1}"/>
                    </p:column>
                    <p:column headerText="单位地址" width="230px;">
                        <h:outputLabel value="#{crptComm.address}" id="tips0" styleClass="zwx-tooltip"/>
                        <p:tooltip for="tips0" style="max-width:230px;">
                            <p:outputLabel value="#{crptComm.address}" escape="false"></p:outputLabel>
                        </p:tooltip>
                    </p:column>
                </p:dataTable>

                <!-- 添加企业信息—各单位独立信息弹出框 -->
                <p:dialog header="单位信息编辑" widgetVar="addCrptComm" id="addCrptCommDialog" width="650" height="380" modal="true" resizable="false">
                    <p:remoteCommand name="checkCptNameAndCode" action="#{crptCommSelectListBean.checkCptNameAndCode}" process="@this,crptName,ifSubOrg,institutionCode"/>
                    <p:panelGrid style="width:100%;margin-bottom:5px;" id="addCrptCommPanel">
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height: 30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifCheck}"/>
                                <h:outputText value="所属地区："/>
                            </p:column>
                            <p:column style="padding:0" styleClass="areaZone">
                                <zwx:ZoneSingleNewComp zoneList="#{crptCommSelectListBean.zoneList}"
                                                    zoneId="#{crptCommSelectListBean.editZoneId}"  id="areaZone" zonePaddingLeft="13"
                                                    zoneName="#{crptCommSelectListBean.editZoneName}" zoneWidth="260" zoneHeight="300"
                                                       zoneType="#{crptCommSelectListBean.editZoneType}"  rendered="#{!crptCommSelectListBean.ifCheck}"/>
                                <h:outputText style="padding-left: 13px;" value="#{crptCommSelectListBean.editCrptComm.tsZoneByZoneId.fullName}" rendered="#{2 >= crptCommSelectListBean.editCrptComm.tsZoneByZoneId.realZoneType and crptCommSelectListBean.ifCheck}"/>
                                <h:outputText style="padding-left: 13px;" value="#{fn:substringAfter(crptCommSelectListBean.editCrptComm.tsZoneByZoneId.fullName,'_')}" rendered="#{crptCommSelectListBean.editCrptComm.tsZoneByZoneId.realZoneType > 2 and crptCommSelectListBean.ifCheck}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifCheck}"/>
                                <h:outputText value="单位名称："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText maxlength="50" value="#{crptCommSelectListBean.editCrptComm.crptName}" id="crptName" style="width:260px" onblur="checkCptNameAndCode()" rendered="#{!crptCommSelectListBean.ifCheck}"/>
                                <h:outputText value="#{crptCommSelectListBean.editCrptComm.crptName}" rendered="#{crptCommSelectListBean.ifCheck}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"  rendered="#{!crptCommSelectListBean.ifCheck and !crptCommSelectListBean.ifLink}"/>
                                <h:outputText value="是否分支机构："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:selectOneRadio  style="width:120px;" id="ifSubOrg"  value="#{crptCommSelectListBean.editCrptComm.ifSubOrg}" rendered="#{!crptCommSelectListBean.ifCheck and !crptCommSelectListBean.ifLink}">
                                    <f:selectItem itemLabel="否" itemValue="0" />
                                    <f:selectItem itemLabel="是" itemValue="1" />
                                    <p:ajax event="change" process="@this,addCrptCommPanel" listener="#{crptCommSelectListBean.changeIfSubOrgListener}" update="selectForm:addCrptCommPanel" oncomplete="removeExtraZonePael('#selectForm\\:areaZone\\:zonePanel')"/>
                                </p:selectOneRadio>
                                <h:outputText value="否" rendered="#{crptCommSelectListBean.ifCheck and crptCommSelectListBean.editCrptComm.ifSubOrg == 0}"/>
                                <h:outputText value="是" rendered="#{crptCommSelectListBean.ifCheck and crptCommSelectListBean.editCrptComm.ifSubOrg == 1}"/>
                                <p:outputPanel style="flex:1;align-items:center;justify-content: flex-end;padding-right:10px;"
                                    rendered="#{!crptCommSelectListBean.ifCheck and crptCommSelectListBean.ifLink}">
                                    <h:outputText value="否（ "/>
                                    <h:outputText style="color: red;" value="提示："/>
                                    <h:outputText value="当前单位已有分支机构，无法修改！" style="color: blue;" escape="false" />
                                    <h:outputText value=" ）"/>
                                </p:outputPanel>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListBean.editCrptComm.ifSubOrg == 1}">
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"  rendered="#{!crptCommSelectListBean.ifCheck}"/>
                                <h:outputText value="上级单位名称："/>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{!crptCommSelectListBean.ifCheck}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="upperUnitName" title="#{crptCommSelectListBean.editCrptComm.fkByUpperUnitId==null?null:crptCommSelectListBean.editCrptComm.fkByUpperUnitId.crptName}"
                                                         value="#{crptCommSelectListBean.editCrptComm.fkByUpperUnitId==null?null:crptCommSelectListBean.editCrptComm.fkByUpperUnitId.crptName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectForm:selUpperUnitLink').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selUpperUnitLink" process="@this,addCrptCommPanel" resetValues="true"
                                                           action="#{crptCommSelectListBean.selUpperUnitAction}"
                                                           oncomplete="removeExtraZonePael('#selectForm\\:upperSearchZone\\:zonePanel');"
                                                           style="position: relative;left: -35px !important;">
                                            </p:commandLink>
                                        </td>
                                        <!-- 清空 -->
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           process="@this,addCrptCommPanel" action="#{crptCommSelectListBean.delUpperUnitName}" update="addCrptCommPanel" oncomplete="removeExtraZonePael('#selectForm\\:areaZone\\:zonePanel')">
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{crptCommSelectListBean.ifCheck}">
                                <h:outputText value="#{crptCommSelectListBean.editCrptComm.fkByUpperUnitId==null?null:crptCommSelectListBean.editCrptComm.fkByUpperUnitId.crptName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <p:outputPanel id="test">
                                    <h:outputText value="*" style="color:red;"  rendered="#{crptCommSelectListBean.editCrptComm.ifSubOrg == 0 and !crptCommSelectListBean.ifCheck}"/>
                                    <h:outputText value="社会信用代码："/>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="padding-left: 13px;height:30px;">
                                <p:inputText id="institutionCode" maxlength="25" onblur="checkCptNameAndCode()" value="#{crptCommSelectListBean.editCrptComm.institutionCode}" style="width:260px" rendered="#{crptCommSelectListBean.editCrptComm.ifSubOrg == 0 and !crptCommSelectListBean.ifCheck}"/>
                                <h:outputText value="#{crptCommSelectListBean.editCrptComm.institutionCode}" rendered="#{crptCommSelectListBean.editCrptComm.ifSubOrg == 1 or crptCommSelectListBean.ifCheck}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifGs}"/>
                                <h:outputText value="单位地址："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.address}" maxlength="100" style="width:260px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifCheck and !crptCommSelectListBean.ifGs}"/>
                                <h:outputText value="行业类别："/>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{!crptCommSelectListBean.ifCheck}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="indusTypeName"
                                                         value="#{crptCommSelectListBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName==null?null:crptCommSelectListBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectForm:selIndusTypeLink').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selIndusTypeLink"
                                                           action="#{crptCommSelectListBean.selCodeTypeAction}" process="@this,addCrptCommPanel"
                                                           oncomplete="PF('selIndusTypeDialog').show()"
                                                           update=":selectForm:selectedIndusCodeTable,:selectForm:searchIndusPanel"
                                                           style="position: relative;left: -35px !important;">
                                                <f:setPropertyActionListener target="#{crptCommSelectListBean.selCodeName}" value="行业类别"/>
                                            </p:commandLink>
                                        </td>
                                        <!-- 清空 -->
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           process="@this,:selectForm:addCrptCommPanel" action="#{crptCommSelectListBean.delCodeName}">
                                                <f:setPropertyActionListener target="#{crptCommSelectListBean.selCodeName}" value="行业类别"/>
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{crptCommSelectListBean.ifCheck}">
                                <h:outputText value="#{crptCommSelectListBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName==null?null:crptCommSelectListBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifCheck and !crptCommSelectListBean.ifGs}"/>
                                <h:outputText value="经济类型："/>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{!crptCommSelectListBean.ifCheck}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="economyName"
                                                         value="#{crptCommSelectListBean.editCrptComm.tsSimpleCodeByEconomyId.codeName==null?null:crptCommSelectListBean.editCrptComm.tsSimpleCodeByEconomyId.codeName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectForm:selEconomyLink').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selEconomyLink"
                                                           action="#{crptCommSelectListBean.selCodeTypeAction}" process="@this"
                                                           oncomplete="PF('selDialog').show()"
                                                           update=":selectForm:selectedIndusTable,:selectForm:searchPanel"
                                                           style="position: relative;left: -35px !important;">
                                                <f:setPropertyActionListener target="#{crptCommSelectListBean.selCodeName}" value="经济类型"/>
                                            </p:commandLink>
                                        </td>
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           update=":selectForm:selectedIndusTable"
                                                           process="@this,:selectForm:economyName"
                                                           action="#{crptCommSelectListBean.delCodeName}">
                                                <f:setPropertyActionListener target="#{crptCommSelectListBean.selCodeName}" value="经济类型"/>
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{crptCommSelectListBean.ifCheck}">
                                <h:outputText value="#{crptCommSelectListBean.editCrptComm.tsSimpleCodeByEconomyId.codeName==null?null:crptCommSelectListBean.editCrptComm.tsSimpleCodeByEconomyId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifCheck and !crptCommSelectListBean.ifGs}"/>
                                <h:outputText value="企业规模："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:selectOneMenu value="#{crptCommSelectListBean.editCrptComm.tsSimpleCodeByCrptSizeId.rid}" rendered="#{!crptCommSelectListBean.ifCheck}"
                                        style="width: 188px;margin-bottom: -5px;">
                                    <f:selectItem itemLabel="--请选择--" itemValue="" />
                                    <f:selectItems value="#{crptCommSelectListBean.crptsizwList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                </p:selectOneMenu>
                                <h:outputText value="#{crptCommSelectListBean.editCrptComm.tsSimpleCodeByCrptSizeId.codeName}" rendered="#{crptCommSelectListBean.ifCheck}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifGs}"/>
                                <h:outputText value="联系人："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.linkman2}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;" rendered="#{!crptCommSelectListBean.ifGs}"/>
                                <h:outputText value="联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.linkphone2}" maxlength="15" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="法定代表人（负责人）："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.corporateDeputy}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="法人联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.phone}" maxlength="15" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="邮政编码："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.postCode}" maxlength="6" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListBean.ifTjlrCrpt or crptCommSelectListBean.ifJcCrpt}" >
                            <p:column style="text-align: right;width: 30%;height:30px;" >
                                <h:outputText value="*" style="color:red;"   rendered="#{ ( crptCommSelectListBean.ifTjlrCrpt and crptCommSelectListBean.ifTj ) or crptCommSelectListBean.ifJcCrpt}" />
                                <h:outputText value="职工人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.workForce}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <!--职业性有害因素监测卡填报 不显示-->
                        <p:row rendered="#{crptCommSelectListBean.ifTjlrCrpt}" >
                            <p:column style="text-align: right;width: 40%;height:30px;" >
                                <h:outputText value="*" style="color:red;"  rendered="#{crptCommSelectListBean.ifTj}"/>
                                <h:outputText value="接触职业病危害因素人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.holdCardMan}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <!-- 健康企业建设情况 -->
                        <p:row rendered="#{crptCommSelectListBean.ifShowHealthCrpt}">
                            <p:column style="text-align: right;width: 40%;height:30px;" >
                                <h:outputText value="*" style="color:red;" />
                                <h:outputText value="健康企业建设情况："/>
                            </p:column>
                            <p:column style="padding-left: 0px;" >
                                <p:panelGrid styleClass="mySymptomPanelGrid" id="symptomGrid" style="width: 100%;">
                                    <c:forEach items="#{crptCommSelectListBean.groupSymCodeList}" var="symCodeRowCommPO">
                                        <p:row>
                                            <c:forEach items="#{symCodeRowCommPO}" var="symCode">
                                                <p:column>
                                                    <p:selectBooleanCheckbox value="#{symCode.ifSelected}"
                                                                             disabled="#{symCode.selectAble eq false}" >
                                                        <p:ajax event="change" listener="#{crptCommSelectListBean.selectSymCodeAction(symCode)}" process="@this,symptomGrid" update="symptomGrid"/>
                                                    </p:selectBooleanCheckbox>
                                                    <h:outputText style="margin-left: 5px;" value="#{symCode.codeName}" />
                                                </p:column>
                                            </c:forEach>
                                        </p:row>
                                    </c:forEach>
                                </p:panelGrid>
                            </p:column>
                        </p:row>
                        <!--职业性有害因素监测卡填报 显示-->
                        <p:row rendered="#{crptCommSelectListBean.ifJcCrpt}">
                            <p:column style="text-align: right;width: 40%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="生产工人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.workmanNum}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListBean.ifJcLcCrpt}">
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="在册职工总数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.workForce}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListBean.ifJcLcCrpt}">
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="外委人员数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptCommSelectListBean.editCrptComm.outsourceNum}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <f:facet name="footer">
                        <h:panelGrid style="width: 100%;text-align: right;">
                            <h:panelGroup>
                                <p:outputPanel rendered="#{crptCommSelectListBean.ifCheck}" style="float:left;flex:1;align-items:center;justify-content: flex-end;padding-top: 5px;"
                                >
                                    <h:outputText style="color: red;" value="提示："/>
                                    <h:outputText value="已审核数据，关键字段无法修改！" style="color: blue;" escape="false" />
                                </p:outputPanel>
                                <p:outputPanel rendered="#{crptCommSelectListBean.ifActiveMonitoringTask and !crptCommSelectListBean.ifCheck}" style="float:left;flex:1;align-items:center;justify-content: flex-end;padding-top: 5px;"
                                >
                                    <h:outputText style="color: red;" value="提示："/>
                                    <h:outputText value="修改信息后，请重新选择单位！" style="color: red;" escape="false" />
                                </p:outputPanel>
                                <p:commandButton value="保存"
                                                 process="@this,addCrptCommPanel"
                                                 action="#{crptCommSelectListBean.saveCrptCommAction}"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="取消" type="button" process="@this"
                                                 onclick="PF('addCrptComm').hide();"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </f:facet>
                </p:dialog>
            </div>

            <!-- 经济类型/行业类别弹出框 -->
            <p:dialog header="#{crptCommSelectListBean.selCodeName}选择" widgetVar="selDialog" width="600" height="380" modal="true" resizable="false" >
                <ui:include src="/webapp/heth/comm/codeRadioPannelComm.xhtml">
                    <ui:param name="mgrbean" value="#{crptCommSelectListBean}"/>
                </ui:include>
            </p:dialog>

            <p:dialog header="行业类别选择" widgetVar="selIndusTypeDialog" width="800" height="430" modal="true" resizable="false" >
                <ui:include src="/webapp/system/indusTypeCodePanel.xhtml">
                    <ui:param name="mgrbean" value="#{crptCommSelectListBean}"/>
                    <ui:param name="indusbean" value="#{crptCommSelectListBean.codePanelBean}"/>
                </ui:include>
            </p:dialog>

            <!--上级单位弹出框-->
            <p:dialog header="上级单位" widgetVar="selUpperUnitDialog" width="1000" height="450" modal="true" resizable="false" >
                <!-- 查询条件 -->
                <p:outputPanel id="upperUnitPanel">
                <table style="width: 100%">
                    <tr>
                        <td style="text-align: right;width: 45px;" class="zwx_dialog_font">
                            <h:outputText value="地区："/>
                        </td>
                        <td style="text-align:left;width: 100px;">
                            <zwx:ZoneSingleNewComp zoneList="#{crptCommSelectListBean.zoneUpperList}"
                                                   zoneCode="#{crptCommSelectListBean.searchUpperZoneCode}" id="upperSearchZone"
                                                   zoneId="#{crptCommSelectListBean.searchUpperZoneId}" zonePaddingLeft="0" zoneHeight="300"
                                                   zoneName="#{crptCommSelectListBean.searchUpperZoneName}"
                                                   ifShowTrash="false"/>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="单位名称："/>
                        </td>
                        <td style="text-align: left;vertical-align: middle;width: 120px;">
                            <p:inputText maxlength="50" style="width:170px;"  value="#{crptCommSelectListBean.searchUpperCrptName}">
                            </p:inputText>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="社会信用代码："/>
                        </td>
                        <td style="text-align: left;width: 120px;">
                            <p:inputText  value="#{crptCommSelectListBean.searchUpperCrptCode}" maxlength="25" style="width:180px;">
                            </p:inputText>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this,upperUnitPanel" value="查询"
                                             action="#{crptCommSelectListBean.searchUpperAction}"
                                             resetValues=":selectForm:crptUpperTable"
                                             update=":selectForm:crptUpperTable"/>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this" value="关闭" onclick="PF('selUpperUnitDialog').hide();"/>
                        </td>
                    </tr>
                </table>
                </p:outputPanel>
                <!-- 表格 -->
                <p:dataTable var="crptUpper" value="#{crptCommSelectListBean.crptUpperList}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="10,20,50" id="crptUpperTable" lazy="true" emptyMessage="没有您要找的记录！"
                             rowKey="#{crptComm.rid}" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                    <p:column headerText="选择" style="width:40px;text-align:center;">
                        <p:commandLink value="选择" process="@this" action="#{crptCommSelectListBean.selectCrptUpperAction}">
                            <f:setPropertyActionListener target="#{crptCommSelectListBean.selectCrptUpper}" value="#{crptUpper}"/>
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="地区名称" width="180px;">
                        <h:outputText value="#{crptUpper.tsZoneByZoneId.fullName}" rendered="#{2 >= crptUpper.tsZoneByZoneId.realZoneType}"/>
                        <h:outputText value="#{fn:substringAfter(crptUpper.tsZoneByZoneId.fullName,'_')}" rendered="#{crptUpper.tsZoneByZoneId.realZoneType > 2}"/>
                    </p:column>
                    <p:column headerText="单位名称" width="230px;">
                        <h:outputText value="#{crptUpper.crptName}"/>
                    </p:column>
                    <p:column headerText="社会信用代码" width="100px;" style="text-align: center;">
                        <h:outputText value="#{crptUpper.institutionCode}"/>
                    </p:column>
                    <p:column headerText="单位地址" width="230px;">
                        <h:outputLabel value="#{crptUpper.address}" id="tips1" styleClass="zwx-tooltip"/>
                        <p:tooltip for="tips1" style="max-width:230px;">
                            <p:outputLabel value="#{crptUpper.address}" escape="false"></p:outputLabel>
                        </p:tooltip>
                    </p:column>
                </p:dataTable>
            </p:dialog>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    </h:body>
</f:view>
</html>
