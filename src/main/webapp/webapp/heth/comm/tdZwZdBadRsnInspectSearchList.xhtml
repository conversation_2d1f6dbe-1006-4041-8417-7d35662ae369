<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwZdBadRsnInspectSearchListBean}"/>

    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/tdZwZdBadRsnInspectSearchView.xhtml" />

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
            a:hover {
                color: #25AAE1;
                text-decoration: none;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
            .ui-chkbox{
                margin-top: 4px;
            }
            .ui-radiobutton-box{
                margin-top:3px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="重点危害因素检查情况查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="机构地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:300px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"
                                    onchange="onSearchNodeSelect()" />
                <p:remoteCommand name="onSearchNodeSelect"
                                 action="#{mgrbean.onZoneSelect}"
                                 process="@this,searchZone"
                                 update=":tabView:mainForm:unitName" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="检查机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 300px;" >
                    <!-- 弹出框 -->
                    <p:outputPanel >
                        <table>
                            <tr>
                                <td style="padding: 0;border-color: transparent;">
                                    <p:inputText id="unitName"
                                                 value="#{mgrbean.searchUnitName}"
                                                 style="width: 180px;cursor: pointer;"
                                                 onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                                 readonly="true"/>
                                </td>
                                <td style="border-color: transparent;">
                                    <p:commandLink styleClass="ui-icon ui-icon-search"
                                                   id="selUnitLink"
                                                   action="#{mgrbean.selUnitAction}" process="@this"
                                                   style="position: relative;left: -28px !important;">
                                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
                                                resetValues="true" update="unitName" />
                                    </p:commandLink>
                                </td>
                                <!-- 清空 -->
                                <td style="border-color: transparent;position: relative;left: -30px;">
                                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                   process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                    </p:commandLink>
                                </td>
                            </tr>
                        </table>
                    </p:outputPanel>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="体检日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBhkBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="体检日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBhkEdate}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;" >
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;">
                <p:inputText id="searchPersonnelName" value="#{mgrbean.searchPersonName}" style="width: 180px;" maxlength="25" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchPersonnelIdc" value="#{mgrbean.searchPersonIdc}" style="width: 180px;" maxlength="25" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="在岗状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.selectOnGuardIds}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="危害因素：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnIds}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        inputWidth="180"
                                        ifTree="true"
                                        ifSelectParent = "false"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 33px;;width: 150px;">
                <h:outputText value="结论是否正常：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" >
                <p:selectManyCheckbox value="#{mgrbean.concluStates}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="机构地区" style="width: 120px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="检查机构" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="人员姓名" style="width:60px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="在岗状态" style="text-align:center;width:80px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="危害因素" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="结论是否正常" style="text-align:center;width:100px;padding-left: 8px;">
            <h:outputText value="是" rendered="#{itm[7]==1}"/>
            <h:outputText value="否" rendered="#{itm[7]==0}"/>
        </p:column>
        <p:column headerText="体检日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputLabel value="待初审" rendered="#{itm[11]==1 or itm[11] == null or (itm[11]==3 and itm[15]==1) }"/>
            <h:outputLabel value="待复审" rendered="#{itm[11]==3 and itm[15]!=1}"/>
            <h:outputLabel value="复审退回" rendered="#{itm[11]==2}" style="color:red;"/>
            <h:outputLabel value="待终审" rendered="#{itm[11]==5}"/>
            <h:outputLabel value="终审退回" rendered="#{itm[11]==4}" style="color:red;"/>
            <h:outputLabel value="终审完成" rendered="#{itm[11]==6}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" resetValues="true">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>