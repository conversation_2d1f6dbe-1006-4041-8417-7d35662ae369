<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:p="http://primefaces.org/ui">
<!--体检信息-->
<p:panelGrid style="width:100%;">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="体检信息"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="体检机构地区："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByBhkorgId.tsZone.fullName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="体检机构："/>
        </p:column>
        <p:column styleClass="cs-scv" colspan="3">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByBhkorgId.unitName}"/>
        </p:column>

    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="报告卡编码："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.bhkCode}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="姓名："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.personName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="证件类型："/>
        </p:column>
        <p:column styleClass="cs-scv">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByCardTypeId.codeName}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="证件号码："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.idc}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="性别："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="男" rendered="#{mgrbean.gjBhk.sex != null and mgrbean.gjBhk.sex ==1 }"/>
            <h:outputLabel value="女" rendered="#{mgrbean.gjBhk.sex != null and  mgrbean.gjBhk.sex == 2}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="出生日期："/>
        </p:column>
        <p:column styleClass="cs-scv">
            <h:outputLabel value="**********" rendered="#{mgrbean.gjBhk.brth !=null}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="用工单位地区："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByEntrustCrptId.tsZoneByZoneId.fullName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="用工单位名称："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByEntrustCrptId.crptName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="用工单位社会信用代码："/>
        </p:column>
        <p:column styleClass="cs-scv">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByEntrustCrptId.institutionCode}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="用工单位企业类型："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByEntrustCrptId.tsSimpleCodeByEconomyId.codeName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="用工单位行业类别："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByEntrustCrptId.tsSimpleCodeByIndusTypeId.codeName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="用工单位企业规模："/>
        </p:column>
        <p:column styleClass="cs-scv">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByEntrustCrptId.tsSimpleCodeByCrptSizeId.codeName}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="工种："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByWorkTypeId.codeName}"/>
            <h:outputLabel value="（"
                           rendered="#{mgrbean.gjBhk.workOther != null and mgrbean.gjBhk.workOther != '' }"/>
            <h:outputLabel value="#{mgrbean.gjBhk.workOther}"
                           rendered="#{mgrbean.gjBhk.workOther != null and mgrbean.gjBhk.workOther != ''}"/>
            <h:outputLabel value="）"
                           rendered="#{mgrbean.gjBhk.workOther != null and mgrbean.gjBhk.workOther != ''}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="专业工龄："/>
        </p:column>
        <p:column styleClass="cs-scv" colspan="3">
            <h:outputLabel value="#{mgrbean.gjBhk.tchbadrsntim}">
                <f:convertNumber type="number" minFractionDigits="0"/>
            </h:outputLabel>
            <h:outputLabel value="年" rendered="#{null!=mgrbean.gjBhk.tchbadrsntim}"/>
            <h:outputLabel value="#{mgrbean.gjBhk.tchbadrsnmonth}"/>
            <h:outputLabel value="月" rendered="#{null!=mgrbean.gjBhk.tchbadrsnmonth}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="接触的危害因素："/>
        </p:column>
        <p:column styleClass="cs-scv" colspan="5">
            <h:outputLabel value="#{mgrbean.gjBhk.tchBadrsns}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="体检类型："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.fkByOnguardStateid.codeName}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="体检日期："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="#{mgrbean.gjBhk.bhkDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="报告出具日期："/>
        </p:column>
        <p:column styleClass="cs-scv">
            <h:outputLabel value="#{mgrbean.gjBhk.rptPrintDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="是否复查："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <h:outputLabel value="是"
                           rendered="#{mgrbean.gjBhk.ifRhk != null and mgrbean.gjBhk.ifRhk==1}"/>
            <h:outputLabel value="否"
                           rendered="#{mgrbean.gjBhk.ifRhk != null and mgrbean.gjBhk.ifRhk==0}"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputLabel value="监测类型："/>
        </p:column>
        <p:column styleClass="cs-scv" colspan="3">
            <h:outputLabel value="常规监测"
                           rendered="#{mgrbean.gjBhk.jcType != null and  mgrbean.gjBhk.jcType==1}"/>
            <h:outputLabel value="主动监测"
                           rendered="#{mgrbean.gjBhk.jcType != null and mgrbean.gjBhk.jcType==2}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputLabel value="体检结论："/>
        </p:column>
        <p:column styleClass="cs-scv" colspan="5">
            <h:outputLabel value="#{mgrbean.gjBhk.tjConclusions}"/>
        </p:column>
    </p:row>
</p:panelGrid>
<c:if test="#{mgrbean.gjBhk.tdGjBhksub.size() > 0}">
    <p:outputPanel style="margin-top: 10px;width: 100%;margin-top:10px;" id="archivePanel">
        <p:fieldset legend="体检项目" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid style="width:100%;margin-top:10px;" var="item">
                <p:row>
                    <!-- 项目列 -->
                    <p:column style="width:100px; text-align: center; height:20px;"
                              styleClass="ui-state-default">
                        <h:outputText value="项目"/>
                    </p:column>
                    <!-- 结果列 -->
                    <p:column style="width:120px; text-align: center;" styleClass="ui-state-default">
                        <h:outputText value="结果"/>
                    </p:column>

                    <!-- 是否异常（国家） -->
                    <p:column style="width:100px; text-align: center;" styleClass="ui-state-default">
                        <h:outputText value="是否异常（国家）"/>
                    </p:column>


                </p:row>
                <c:forEach var="itm" items="#{mgrbean.gjBhk.tdGjBhksub}">
                    <p:row>
                        <!-- 项目列 -->
                        <p:column headerText="项目" style="height:20px; ">
                            <h:outputText value="#{itm.fkByItemId.itemName}"/>
                        </p:column>

                        <!-- 结果列 -->
                        <p:column headerText="结果" style="text-align: center;">
                            <h:outputText value="#{itm.itemRst}"/>
                            <h:outputText value="#{itm.msrunt}" escape="false"/>
                        </p:column>

                        <!-- 是否异常（国家） -->
                        <p:column headerText="是否异常（国家）" style="text-align: center;">
                            <h:outputText value="未见异常"
                                          rendered="#{itm.ifAbnormal != null and itm.ifAbnormal == 0}"/>
                            <h:outputText value="尘肺样改变"
                                          rendered="#{itm.ifAbnormal != null and itm.ifAbnormal == 1}"/>
                            <h:outputText value="其他异常"
                                          rendered="#{itm.ifAbnormal != null and itm.ifAbnormal == 2}"/>
                            <h:outputText value="未检查"
                                          rendered="#{itm.ifAbnormal != null and itm.ifAbnormal == 3}"/>
                            <h:outputText value="正常"
                                          rendered="#{itm.ifAbnormal != null and itm.ifAbnormal == 4}"/>
                            <h:outputText value="异常"
                                          rendered="#{itm.ifAbnormal != null and itm.ifAbnormal == 5}"/>
                        </p:column>
                    </p:row>
                </c:forEach>
            </p:panelGrid>
        </p:fieldset>
    </p:outputPanel>
</c:if>
</html>
