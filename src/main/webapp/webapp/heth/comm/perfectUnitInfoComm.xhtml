<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>

        <!-- 托管bean-->
        <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.PerfectUnitInfoCommBean"-->
        <ui:param name="mgrbean" value="#{perfectUnitInfoCommBean}"/>

        <script src="#{request.contextPath}/resources/js/namespace.js" type="text/javascript">
        </script>
        <script src="#{request.contextPath}/resources/js/validate/system/validate.js" type="text/javascript">
        </script>
        <script type="text/javascript">
            function newBlank() {
                window.parent
                    .open("http://api.map.baidu.com/lbsapi/getpoint/index.html");
            }
        </script>
        <!-- 标题栏 -->
        <h:form id="editForm">
            <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
            <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
                         id="editTitleGrid">
                <f:facet name="header">
                    <p:row>
                        <p:column colspan="2"
                                  style="text-align:left;padding-left:5px;height: 20px;">
                            <h:outputText value="完善单位信息"/>
                        </p:column>
                    </p:row>
                </f:facet>
            </p:panelGrid>
            <!-- 编辑页面的按钮 -->
            <p:outputPanel styleClass="zwx_toobar_42">
                <h:panelGrid columns="3"
                             style="border-color:transparent;padding:0;">
					<span class="ui-separator">
                        <span class="ui-icon ui-icon-grip-dotted-vertical"/>
                    </span>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
                                     action="#{mgrbean.saveAction}" process="@this,editGrid"
                                     update="editForm"/>
                </h:panelGrid>
            </p:outputPanel>
            <!-- 编辑页面的内容-->
            <p:panelGrid
                    style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;"
                    id="editGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="unitname" value="#{mgrbean.tsUnit.unitname}"
                                     style="width:330px;" maxlength="50" required="true"
                                     requiredMessage="单位名称不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="单位简称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="unitSimpname" style="width:180px;"
                                     value="#{mgrbean.tsUnit.unitSimpname}" maxlength="50"
                                     required="true" requiredMessage="单位简称不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="单位地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="unitaddr" style="width:330px;"
                                     value="#{mgrbean.tsUnit.unitaddr}" maxlength="50" size="50"
                                     required="true" requiredMessage="单位地址不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="邮政编码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="unitzip" value="#{mgrbean.tsUnit.unitzip}"
                                     maxlength="6" size="10" required="true" style="width:180px;"
                                     requiredMessage="邮政编码不允许为空！">
                            <f:validateLength maximum="6"/>
                        </p:inputText>
                        <h:outputText value="（格式：214406）" style="color: red"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="法定代表人："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="orgFz" value="#{mgrbean.tsUnit.orgFz}"
                                     style="width:180px;" maxlength="25" required="true"
                                     requiredMessage="法定代表人不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="联系人："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="linkman" value="#{mgrbean.tsUnit.linkMan}"
                                     style="width:180px;" maxlength="50" required="true"
                                     requiredMessage="联系人不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="联系人电话："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="unittel" value="#{mgrbean.tsUnit.orgTel}"
                                     style="width:180px;" maxlength="20" required="true"
                                     requiredMessage="联系人电话不允许为空！"/>
                        <h:outputText value="（格式：0510-85373786/13616161616）"
                                      style="color: red"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <h:outputText value="单位传真："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="unitfax" value="#{mgrbean.tsUnit.unitfax}"
                                     style="width:180px;" maxlength="20"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="高拍仪版本："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:selectOneMenu id="searchSortId" value="#{mgrbean.tsUnit.gpyVersion}" style="width: 188px;">
                            <f:selectItem itemLabel="&nbsp;汉王T510 Plus" escape="true" itemValue="1"/>
                            <f:selectItem itemLabel="&nbsp;汉王E1100 Air" escape="true" itemValue="2"/>
                            <f:selectItem itemLabel="&nbsp;得力15159" escape="true" itemValue="3"/>
                            <f:selectItem itemLabel="&nbsp;得力15162" escape="true" itemValue="4"/>
                            <f:selectItem itemLabel="&nbsp;得力15165" escape="true" itemValue="5"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="职业病诊断证明书备注："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputTextarea value="#{mgrbean.tsUnit.proveBak}" maxlength="500" required="true"
                                         requiredMessage="职业病诊断证明书备注不允许为空！"
                                         style="width:450px;resize: none;height:100px;" autoResize="false"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifShowDiagWritNo}">
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="接诊登记编号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="occDiagnosisDocumentNo" style="width:180px;"
                                     value="#{mgrbean.tsUnit.writeNo}" maxlength="50"
                                     required="true" requiredMessage="接诊登记编号不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifShowDiagWritNo}">
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="职业病诊断委托协议编号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="trustWriteNo" style="width:180px;"
                                     value="#{mgrbean.tsUnit.trustWriteNo}" maxlength="50"
                                     required="true" requiredMessage="职业病诊断委托协议编号不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifShowDiagWritNo}">
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <span style="color: red; ">*</span>
                        <h:outputText value="职业病诊断证明书编号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="proveWriteNo" style="width:180px;"
                                     value="#{mgrbean.tsUnit.proveWriteNo}" maxlength="50"
                                     required="true" requiredMessage="职业病诊断证明书编号不允许为空！"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifShowDiagWritNo}">
                    <p:column style="text-align:right;padding-right:3px;width:220px;">
                        <h:outputText value="红头文件的单位名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:12px;">
                        <p:inputText id="redUnitName" style="width:180px;"
                                     value="#{mgrbean.tsUnit.redUnitName}" maxlength="100"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    </h:body>
</f:view>
</html>
        <!-- 带转向、真分页的模板 -->

