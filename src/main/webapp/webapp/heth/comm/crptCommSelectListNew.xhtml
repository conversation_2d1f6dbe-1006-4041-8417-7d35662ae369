<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 标题 -->
    <h:head>
        <title><h:outputText value="单位信息"/></title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <h:outputScript name="js/namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style type="text/css">
            .areaZone .ui-commandlink {
                left: -24px !important;
            }

            #selectFormNew\:addCrptCommDialog_title {
                margin: 0px;
            }

            .mySymptomPanelGrid tr {
                border: 0px transparent !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function removeExtraZonePael(elStr) {
                //移除掉多余的地区框
                var el = jQuery(elStr);
                if (el.length > 1) {
                    el.each(function (index) {
                        if (index > 0) {
                            $(this).remove();
                        }
                    });
                }
            }

            //]]>
        </script>
    </h:head>

    <h:body>
        <h:form id="selectFormNew">
            <div style="float: left;font-size: 12px;width: 100%;">
                <!-- 查询条件 -->
                <table style="width: 100%">
                    <tr>
                        <td style="text-align: right;width: 45px;" class="zwx_dialog_font">
                            <h:outputText value="地区："/>
                        </td>
                        <td style="text-align:left;width: 100px;">
                            <zwx:ZoneSingleNewComp zoneList="#{crptCommSelectListNewBean.zoneList}"
                                                   zoneCode="#{crptCommSelectListNewBean.searchZoneCode}"
                                                   zoneId="#{crptCommSelectListNewBean.searchZoneId}"
                                                   zonePaddingLeft="0"
                                                   zoneName="#{crptCommSelectListNewBean.searchZoneName}"
                                                   id="searchZoneNew"
                                                   ifShowTrash="false"/>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="单位名称："/>
                        </td>
                        <td style="text-align: left;vertical-align: middle;width: 120px;">
                            <p:inputText maxlength="50" style="width:180px;" id="searchCrptNameNew"
                                         value="#{crptCommSelectListNewBean.searchCrptName}">
                            </p:inputText>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="社会信用代码："/>
                        </td>
                        <td style="text-align: left;width: 120px;">
                            <p:inputText id="searchCrptCodeNew" value="#{crptCommSelectListNewBean.searchCrptCode}"
                                         maxlength="25" style="width:180px;">
                            </p:inputText>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this,searchZoneNew,searchCrptNameNew,searchCrptCodeNew"
                                             value="查询"
                                             oncomplete="zwx_loading_stop()" onclick="zwx_loading_start()"
                                             action="#{crptCommSelectListNewBean.searchAction}"
                                             resetValues="selectFormNew:crptCommTableNew"
                                             update="selectFormNew:crptCommTableNew"/>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this,addCrptCommPanelNew" value="添加"
                                             action="#{crptCommSelectListNewBean.addCrptCommAction}"
                                             resetValues="true"
                                             oncomplete="removeExtraZonePael('#selectFormNew\\:areaZoneNew\\:zonePanel')"
                                             rendered="#{crptCommSelectListNewBean.ifEdit}"/>
                        </td>
                    </tr>
                </table>

                <!-- 表格 -->
                <p:dataTable var="crptComm" value="#{crptCommSelectListNewBean.crptCommList}" paginator="true" rows="10"
                             paginatorPosition="bottom" rowIndexVar="R"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="10,20,50" id="crptCommTableNew" lazy="true"
                             emptyMessage="没有您要找的记录！"
                             rowKey="#{crptComm.rid}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                    <p:column headerText="操作" style="width:60px;text-align:center;">
                        <p:commandLink value="选择" process="@this"
                                       action="#{crptCommSelectListNewBean.selectCrptCommAction}"
                                       oncomplete="zwx_loading_stop()" onclick="zwx_loading_start()">
                            <f:setPropertyActionListener target="#{crptCommSelectListNewBean.selectCrptComm}"
                                                         value="#{crptComm}"/>
                        </p:commandLink>
                        <p:spacer width="5" rendered="#{crptCommSelectListNewBean.ifEdit}"/>
                        <p:commandLink process="@this" value="修改"
                                       oncomplete="removeExtraZonePael('#selectFormNew\\:areaZoneNew\\:zonePanel')"
                                       action="#{crptCommSelectListNewBean.modCrptCommAction}"
                                       rendered="#{crptCommSelectListNewBean.ifEdit}"
                                       resetValues="true"
                        >
                            <f:setPropertyActionListener target="#{crptCommSelectListNewBean.crptCommId}"
                                                         value="#{crptComm.rid}"/>
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="地区名称" width="180px;">
                        <h:outputText value="#{crptComm.tsZoneByZoneId.fullName}"
                                      rendered="#{2 >= crptComm.tsZoneByZoneId.realZoneType}"/>
                        <h:outputText value="#{fn:substringAfter(crptComm.tsZoneByZoneId.fullName,'_')}"
                                      rendered="#{crptComm.tsZoneByZoneId.realZoneType > 2}"/>
                    </p:column>
                    <p:column headerText="单位名称" width="230px;">
                        <h:outputText value="#{crptComm.crptName}"/>
                    </p:column>
                    <p:column headerText="社会信用代码" width="100px;" style="text-align: center;">
                        <h:outputText value="#{crptComm.institutionCode}"/>
                    </p:column>
                    <p:column headerText="单位地址" width="230px;">
                        <h:outputLabel value="#{crptComm.address}" id="tips0New" styleClass="zwx-tooltip"/>
                        <p:tooltip for="tips0New" style="max-width:230px;">
                            <p:outputLabel value="#{crptComm.address}" escape="false"></p:outputLabel>
                        </p:tooltip>
                    </p:column>
                </p:dataTable>

                <!-- 添加企业信息—各单位独立信息弹出框 -->
                <p:dialog header="单位信息编辑" widgetVar="addCrptCommNew" id="addCrptCommDialogNew" width="650"
                          height="380" modal="true" resizable="false">
                    <p:remoteCommand name="checkCptNameAndCode"
                                     action="#{crptCommSelectListNewBean.checkCptNameAndCode}"
                                     process="@this,addCrptCommPanelNew"/>
                    <p:panelGrid style="width:100%;margin-bottom:5px;" id="addCrptCommPanelNew">
                        <p:row rendered="#{crptCommSelectListNewBean.ifSelectOperationStopData}">
                            <p:column style="text-align: right;width: 30%;height: 32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{crptCommSelectListNewBean.addOption}"/>
                                <h:outputText value="经营状态："/>
                            </p:column>
                            <p:column style="padding:0" styleClass="areaZone">
                                <p:selectOneRadio style="width: auto;"
                                                  value="#{crptCommSelectListNewBean.editCrptComm.operationStatus}"
                                                  rendered="#{crptCommSelectListNewBean.addOption}">
                                    <f:selectItem itemLabel="存续" itemValue="1"/>
                                    <f:selectItem itemLabel="注销" itemValue="0"/>
                                    <p:ajax event="change"
                                            process="@this,addCrptCommPanelNew"
                                            listener="#{crptCommSelectListNewBean.changeOperationStatusOrIfSubOrg(1)}"/>
                                </p:selectOneRadio>
                                <h:outputText value="存续" style="padding-left: 13px;"
                                              rendered="#{!crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.operationStatus eq 1}"/>
                                <h:outputText value="注销" style="padding-left: 13px;"
                                              rendered="#{!crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.operationStatus eq 0}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height: 32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifCheck}"/>
                                <h:outputText value="地区："/>
                            </p:column>
                            <p:column style="padding:0" styleClass="areaZone">
                                <zwx:ZoneSingleNewComp zoneList="#{crptCommSelectListNewBean.zoneList}"
                                                       zoneId="#{crptCommSelectListNewBean.editZoneId}" id="areaZoneNew"
                                                       zonePaddingLeft="13"
                                                       zoneName="#{crptCommSelectListNewBean.editZoneName}"
                                                       zoneWidth="260" zoneHeight="300"
                                                       zoneType="#{crptCommSelectListNewBean.editZoneType}"
                                                       rendered="#{!crptCommSelectListNewBean.ifCheck}"/>
                                <h:outputText style="padding-left: 13px;"
                                              value="#{crptCommSelectListNewBean.editCrptComm.tsZoneByZoneId.fullName}"
                                              rendered="#{2 >= crptCommSelectListNewBean.editCrptComm.tsZoneByZoneId.realZoneType and crptCommSelectListNewBean.ifCheck}"/>
                                <h:outputText style="padding-left: 13px;"
                                              value="#{fn:substringAfter(crptCommSelectListNewBean.editCrptComm.tsZoneByZoneId.fullName,'_')}"
                                              rendered="#{crptCommSelectListNewBean.editCrptComm.tsZoneByZoneId.realZoneType > 2 and crptCommSelectListNewBean.ifCheck}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column
                                    style="text-align: right;width: 30%;height: 32px;#{crptCommSelectListNewBean.editCrptComm.operationStatus ne 0?'':'display: none;'}">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{crptCommSelectListNewBean.addOption}"/>
                                <h:outputText value="有无独立社会信用代码："/>
                            </p:column>
                            <p:column styleClass="areaZone"
                                      style="padding:0;#{crptCommSelectListNewBean.editCrptComm.operationStatus ne 0?'':'display: none;'}">
                                <p:selectOneRadio style="width: auto;"
                                                  value="#{crptCommSelectListNewBean.editCrptComm.ifSubOrg}"
                                                  rendered="#{crptCommSelectListNewBean.addOption}">
                                    <f:selectItem itemLabel="有" itemValue="0"/>
                                    <f:selectItem itemLabel="无" itemValue="1"/>
                                    <p:ajax event="change"
                                            process="@this,addCrptCommPanelNew"
                                            listener="#{crptCommSelectListNewBean.changeOperationStatusOrIfSubOrg(2)}"/>
                                </p:selectOneRadio>
                                <h:outputText value="有" style="padding-left: 13px;"
                                              rendered="#{!crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.ifSubOrg eq 0}"/>
                                <h:outputText value="无" style="padding-left: 13px;"
                                              rendered="#{!crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.ifSubOrg eq 1}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column
                                    style="text-align: right;width: 30%;height:32px;#{crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.ifSubOrg eq 1?'':'display: none;'}">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="上级单位名称："/>
                            </p:column>
                            <p:column
                                    style="padding-left: 9px;#{crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.ifSubOrg eq 1?'':'display: none;'}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.upperCrptName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectFormNew:searchUpperCompanyLinkNew').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="searchUpperCompanyLinkNew"
                                                           process="@this,addCrptCommPanelNew"
                                                           resetValues="true"
                                                           action="#{crptCommSelectListNewBean.showSearchCompanyDialog}"
                                                           style="position: relative;left: -35px !important;">
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListNewBean.ifCheck}">
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="单位名称："/>
                            </p:column>

                            <p:column style="padding-left: 9px;">
                                <p:outputPanel style="display: inline;">
                                    <h:outputText value="#{crptCommSelectListNewBean.editCrptComm.crptName}" style="vertical-align: top;"/>
                                </p:outputPanel>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{!crptCommSelectListNewBean.ifCheck}">
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{crptCommSelectListNewBean.addOption or crptCommSelectListNewBean.editCrptComm.operationStatus eq 0 or crptCommSelectListNewBean.editCrptComm.ifSubOrg ne 0}"/>
                                <h:outputText value="单位名称："/>
                            </p:column>
                            <p:column style="padding-left: 9px;#{crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.operationStatus ne 0 and crptCommSelectListNewBean.editCrptComm.ifSubOrg eq 0?'':'display:none;'}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.crptName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectFormNew:searchCompanyLinkNew').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="searchCompanyLinkNew" process="@this,addCrptCommPanelNew"
                                                           resetValues="true"
                                                           action="#{crptCommSelectListNewBean.showSearchCompanyDialog}"
                                                           style="position: relative;left: -35px !important;">
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                            <p:column style="padding-left: 9px;#{!crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.operationStatus ne 0 and crptCommSelectListNewBean.editCrptComm.ifSubOrg eq 0?'':'display:none;'}">
                                <p:outputPanel style="display: inline;">
                                    <h:outputText value="#{crptCommSelectListNewBean.editCrptComm.crptName}" style="vertical-align: top;"/>
                                    <p:commandLink style="display: inline-flex;align-items: center;"
                                                   process="@this,addCrptCommPanelNew"
                                                   action="#{crptCommSelectListNewBean.updateCompanyName}"
                                                   onclick="zwx_loading_start()" oncomplete="zwx_loading_stop()"
                                                   rendered="#{!crptCommSelectListNewBean.addOption}">
                                        <i class='ui-icon ui-icon-refresh'/>更新
                                    </p:commandLink>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="padding-left: 13px;#{crptCommSelectListNewBean.editCrptComm.operationStatus eq 0 or crptCommSelectListNewBean.editCrptComm.ifSubOrg ne 0?'':'display:none;'}">
                                <p:inputText maxlength="50" value="#{crptCommSelectListNewBean.editCrptComm.crptName}"
                                             style="width:260px" onblur="checkCptNameAndCode()"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <p:outputPanel>
                                    <h:outputText value="社会信用代码："/>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="padding-left: 13px;height:30px;">
                                <h:outputText value="自动生成" style="color:gray;"
                                              rendered="#{crptCommSelectListNewBean.addOption and crptCommSelectListNewBean.editCrptComm.operationStatus eq 0}"/>
                                <h:outputText value="#{crptCommSelectListNewBean.editCrptComm.institutionCode}"
                                              rendered="#{!crptCommSelectListNewBean.addOption or crptCommSelectListNewBean.editCrptComm.operationStatus ne 0}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifGs}"/>
                                <h:outputText value="单位地址："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.address}" maxlength="100"
                                             style="width:260px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifCheck and !crptCommSelectListNewBean.ifGs}"/>
                                <h:outputText value="行业类别："/>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{!crptCommSelectListNewBean.ifCheck}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="indusTypeNameNew"
                                                         value="#{crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName==null?null:crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectFormNew:selIndusTypeLinkNew').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selIndusTypeLinkNew"
                                                           action="#{crptCommSelectListNewBean.selCodeTypeAction}"
                                                           process="@this,addCrptCommPanelNew"
                                                           oncomplete="PF('selIndusTypeDialog').show()"
                                                           update=":selectFormNew:selectedIndusCodeTable,:selectFormNew:searchIndusPanel"
                                                           style="position: relative;left: -35px !important;">
                                                <f:setPropertyActionListener
                                                        target="#{crptCommSelectListNewBean.selCodeName}"
                                                        value="行业类别"/>
                                            </p:commandLink>
                                        </td>
                                        <!-- 清空 -->
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           process="@this,:selectFormNew:addCrptCommPanelNew"
                                                           action="#{crptCommSelectListNewBean.delCodeName}">
                                                <f:setPropertyActionListener
                                                        target="#{crptCommSelectListNewBean.selCodeName}"
                                                        value="行业类别"/>
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{crptCommSelectListNewBean.ifCheck}">
                                <h:outputText
                                        value="#{crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName==null?null:crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByIndusTypeId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifCheck and !crptCommSelectListNewBean.ifGs}"/>
                                <h:outputText value="经济类型："/>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{!crptCommSelectListNewBean.ifCheck}">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="economyNameNew"
                                                         value="#{crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByEconomyId.codeName==null?null:crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByEconomyId.codeName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectFormNew:selEconomyLinkNew').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selEconomyLinkNew"
                                                           action="#{crptCommSelectListNewBean.selCodeTypeAction}"
                                                           process="@this,addCrptCommPanelNew"
                                                           oncomplete="PF('selDialog').show()"
                                                           update=":selectFormNew:selectedIndusTable,:selectFormNew:searchPanel"
                                                           style="position: relative;left: -35px !important;">
                                                <f:setPropertyActionListener
                                                        target="#{crptCommSelectListNewBean.selCodeName}"
                                                        value="经济类型"/>
                                            </p:commandLink>
                                        </td>
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           update=":selectFormNew:selectedIndusTable"
                                                           process="@this,addCrptCommPanelNew,:selectFormNew:economyNameNew"
                                                           action="#{crptCommSelectListNewBean.delCodeName}">
                                                <f:setPropertyActionListener
                                                        target="#{crptCommSelectListNewBean.selCodeName}"
                                                        value="经济类型"/>
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                            <p:column style="padding-left: 9px;" rendered="#{crptCommSelectListNewBean.ifCheck}">
                                <h:outputText
                                        value="#{crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByEconomyId.codeName==null?null:crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByEconomyId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifCheck and !crptCommSelectListNewBean.ifGs}"/>
                                <h:outputText value="企业规模："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:selectOneMenu
                                        value="#{crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByCrptSizeId.rid}"
                                        rendered="#{!crptCommSelectListNewBean.ifCheck}"
                                        style="width: 188px;margin-bottom: -5px;">
                                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                    <f:selectItems value="#{crptCommSelectListNewBean.crptsizwList}" var="itm"
                                                   itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                </p:selectOneMenu>
                                <h:outputText
                                        value="#{crptCommSelectListNewBean.editCrptComm.tsSimpleCodeByCrptSizeId.codeName}"
                                        rendered="#{crptCommSelectListNewBean.ifCheck}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifGs}"/>
                                <h:outputText value="联系人："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.linkman2}" maxlength="25"
                                             style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{!crptCommSelectListNewBean.ifGs}"/>
                                <h:outputText value="联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.linkphone2}" maxlength="15"
                                             style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="法定代表人（负责人）："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.corporateDeputy}"
                                             maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="法人联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.phone}" maxlength="15"
                                             style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="邮政编码："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.postCode}" maxlength="6"
                                             style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListNewBean.ifTjlrCrpt or crptCommSelectListNewBean.ifJcCrpt}">
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{ ( crptCommSelectListNewBean.ifTjlrCrpt and crptCommSelectListNewBean.ifTj ) or crptCommSelectListNewBean.ifJcCrpt}"/>
                                <h:outputText value="职工人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.workForce}"
                                             style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9"/>
                            </p:column>
                        </p:row>
                        <!--职业性有害因素监测卡填报 不显示-->
                        <p:row rendered="#{crptCommSelectListNewBean.ifTjlrCrpt}">
                            <p:column style="text-align: right;width: 40%;height:32px;">
                                <h:outputText value="*" style="color:red;"
                                              rendered="#{crptCommSelectListNewBean.ifTj}"/>
                                <h:outputText value="接触职业病危害因素人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.holdCardMan}"
                                             style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9"/>
                            </p:column>
                        </p:row>
                        <!-- 健康企业建设情况 -->
                        <p:row rendered="#{crptCommSelectListNewBean.ifShowHealthCrpt}">
                            <p:column style="text-align: right;width: 40%;height:32px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="健康企业建设情况："/>
                            </p:column>
                            <p:column style="padding-left: 0px;">
                                <p:panelGrid styleClass="mySymptomPanelGrid" id="symptomGridNew" style="width: 100%;">
                                    <c:forEach items="#{crptCommSelectListNewBean.groupSymCodeList}"
                                               var="symCodeRowCommPO">
                                        <p:row>
                                            <c:forEach items="#{symCodeRowCommPO}" var="symCode">
                                                <p:column>
                                                    <p:selectBooleanCheckbox value="#{symCode.ifSelected}"
                                                                             disabled="#{symCode.selectAble eq false}">
                                                        <p:ajax event="change"
                                                                listener="#{crptCommSelectListNewBean.selectSymCodeAction(symCode)}"
                                                                process="@this,symptomGridNew" update="symptomGridNew"/>
                                                    </p:selectBooleanCheckbox>
                                                    <h:outputText style="margin-left: 5px;"
                                                                  value="#{symCode.codeName}"/>
                                                </p:column>
                                            </c:forEach>
                                        </p:row>
                                    </c:forEach>
                                </p:panelGrid>
                            </p:column>
                        </p:row>
                        <!--职业性有害因素监测卡填报 显示-->
                        <p:row rendered="#{crptCommSelectListNewBean.ifJcCrpt}">
                            <p:column style="text-align: right;width: 40%;height:32px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="生产工人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.workmanNum}"
                                             style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListNewBean.ifJcLcCrpt}">
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="在册职工总数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.workForce}"
                                             style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptCommSelectListNewBean.ifJcLcCrpt}">
                            <p:column style="text-align: right;width: 30%;height:32px;">
                                <h:outputText value="外委人员数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptCommSelectListNewBean.editCrptComm.outsourceNum}"
                                             style="width:179px"
                                             onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"
                                             maxlength="9"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <f:facet name="footer">
                        <h:panelGrid style="width: 100%;text-align: right;">
                            <h:panelGroup>
                                <p:outputPanel rendered="#{crptCommSelectListNewBean.ifCheck}"
                                               style="float:left;flex:1;align-items:center;justify-content: flex-end;padding-top: 5px;"
                                >
                                    <h:outputText style="color: red;" value="提示："/>
                                    <h:outputText value="已审核数据，关键字段无法修改！" style="color: blue;"
                                                  escape="false"/>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{crptCommSelectListBean.ifActiveMonitoringTask and !crptCommSelectListBean.ifCheck}" style="float:left;flex:1;align-items:center;justify-content: flex-end;padding-top: 5px;"
                                >
                                    <h:outputText style="color: red;" value="提示："/>
                                    <h:outputText value="修改信息后，请重新选择单位！" style="color: red;" escape="false" />
                                </p:outputPanel>
                                <p:commandButton value="保存"
                                                 process="@this,addCrptCommPanelNew"
                                                 action="#{crptCommSelectListNewBean.saveCrptCommAction}"
                                                 oncomplete="zwx_loading_stop()" onclick="zwx_loading_start()"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="取消" type="button" process="@this"
                                                 onclick="PF('addCrptCommNew').hide();"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </f:facet>
                </p:dialog>
            </div>

            <!-- 经济类型/行业类别弹出框 -->
            <p:dialog header="#{crptCommSelectListNewBean.selCodeName}选择" widgetVar="selDialog" width="600"
                      height="380" modal="true" resizable="false">
                <ui:include src="/webapp/heth/comm/codeRadioPannelComm.xhtml">
                    <ui:param name="mgrbean" value="#{crptCommSelectListNewBean}"/>
                </ui:include>
            </p:dialog>

            <p:dialog header="行业类别选择" widgetVar="selIndusTypeDialog" width="800" height="430" modal="true"
                      resizable="false">
                <ui:include src="/webapp/system/indusTypeCodePanel.xhtml">
                    <ui:param name="mgrbean" value="#{crptCommSelectListNewBean}"/>
                    <ui:param name="indusbean" value="#{crptCommSelectListNewBean.codePanelBean}"/>
                </ui:include>
            </p:dialog>

            <!--单位弹出框-->
            <p:dialog header="查询单位" widgetVar="SearchCompanyDialog" width="1000" height="450" modal="true"
                      resizable="false">
                <script type="application/javascript">
                    document.addEventListener('keydown', function(event) {
                        if (event.key === 'Enter') {
                            event.preventDefault();
                        }
                    });
                </script>
                <!-- 查询条件 -->
                <p:outputPanel id="searchCompanyPanel" style="#{crptCommSelectListNewBean.showSearchCompany?'':'display:none;'}">
                    <table style="width: 100%">
                        <tr>
                            <td style="text-align: right;width: 160px;" class="zwx_dialog_font">
                                <h:outputText value="单位名称或社会信用代码："/>
                            </td>
                            <td style="text-align: left;width: 160px;">
                                <p:inputText maxlength="100" style="width:170px;"
                                             value="#{crptCommSelectListNewBean.searchCompanyWord}">
                                </p:inputText>
                            </td>
                            <td style="text-align: left;padding-left: 8px;">
                                <p:commandButton value="查询"
                                                 process="@this, searchCompanyPanel" update="searchCompanyTable"
                                                 action="#{crptCommSelectListNewBean.searchCompanyAction}"
                                                 resetValues=":selectFormNew:searchCompanyTable"
                                                 oncomplete="zwx_loading_stop()" onclick="zwx_loading_start()"/>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
                <!-- 表格 -->
                <p:dataTable id="searchCompanyTable" value="#{crptCommSelectListNewBean.companyList}" var="company"
                             paginator="true" rows="20" paginatorPosition="bottom" rowIndexVar="R"
                             paginatorTemplate="{CurrentPageReport}"
                             currentPageReportTemplate="查询到{totalRecords}条记录"
                             emptyMessage="没有您要找的记录！" lazy="true">
                    <p:column headerText="操作" style="width: 40px;text-align:center;">
                        <p:commandLink value="选择" process="@this,:selectFormNew:addCrptCommPanelNew"
                                       update=":selectFormNew:addCrptCommPanelNew"
                                       action="#{crptCommSelectListNewBean.selectCompanyAction(company)}"
                                       onsuccess="PF('SearchCompanyDialog').hide();checkCptNameAndCode();"/>
                    </p:column>
                    <p:column headerText="单位名称">
                        <h:outputText value="#{company.name}"/>
                    </p:column>
                    <p:column headerText="社会信用代码" style="width: 300px;text-align: center;">
                        <h:outputText value="#{company.creditCode}"/>
                    </p:column>
                </p:dataTable>
            </p:dialog>

            <p:confirmDialog id="clearConfirmDialog" message="将清空单位名称或社会信用代码，是否继续？"
                             header="消息确认框" closable="false" widgetVar="ClearConfirmDialog">
                <p:outputPanel style="display: flex;justify-content: center;">
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{crptCommSelectListNewBean.clearCrptInstitutionCodeAndName}"
                                     oncomplete="PF('ClearConfirmDialog').hide();"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     action="#{crptCommSelectListNewBean.cancelChangeOperationStatusOrIfSubOrg}"
                                     oncomplete="PF('ClearConfirmDialog').hide();"/>
                </p:outputPanel>
            </p:confirmDialog>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
    </h:body>
</f:view>
</html>
