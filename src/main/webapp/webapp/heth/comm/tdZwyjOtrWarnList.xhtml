<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwyjOtrWarnListBean}"/>

    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/tdZwyjOtrWarnEdit.xhtml" />
    <ui:param name="viewPage" value="/webapp/heth/comm/tdZwyjOtrWarnReport.xhtml" />

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
            a:hover {
                color: #25AAE1;
                text-decoration: none;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
            .ui-chkbox{
                margin-top: 4px;
            }
            .ui-radiobutton-box{
                margin-top:3px;
            }
            .rowBackNone tr{
                background: none;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="超范围服务预警处置-#{mgrbean.checkType==2?'终审':'初审'}"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;" rendered="false">
                <h:outputText value="机构地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;" rendered="false">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="预警类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px">
                <p:selectOneMenu value="#{mgrbean.warnType}" style="width:188px;">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItem itemLabel="超备案地区" itemValue="1"/>
                    <f:selectItem itemLabel="超服务项目" itemValue="2"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="发现日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px; width:250px;" >
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="发现日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.happenStartDate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="发现日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.happenEndDate}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;" rendered="#{mgrbean.checkType==1}">
                <h:outputText value="处置日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" rendered="#{mgrbean.checkType==1}">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="处置日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.dealStartDate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="处置日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.dealEndDate}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;" rendered="#{mgrbean.checkType==2}">
                <h:outputText value="审核日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" rendered="#{mgrbean.checkType==2}">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="审核日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.checkStartDate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="审核日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.checkEndDate}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 33px;;width: 150px;">
                <h:outputText value="是否超范围：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" >
                <p:selectManyCheckbox value="#{mgrbean.outRanges}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="体检机构地区" style="width: 150px;padding-left: 8px;" >
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="体检机构名称" style="width: 250px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="预警类型" style="width:100px;text-align: center;">
            <h:outputText value="#{itm[3]==1?'超备案地区':'超服务项目'}"/>
        </p:column>
        <p:column headerText="超范围服务人数" style="width:100px;padding-left: 8px;text-align:center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="发现日期" style="text-align:center;width:80px;padding-left: 8px;">
            <h:outputLabel value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="是否超范围" style="text-align:center;width:80px;padding-left: 8px;">
            <h:outputText value="是" rendered="#{itm[6]==1}"/>
            <h:outputText value="否" rendered="#{itm[6]==0}"/>
        </p:column>
        <p:column headerText="退回日期" style="text-align:center;width:80px;padding-left: 8px;" rendered="#{mgrbean.checkType==1}">
            <h:outputLabel value="#{itm[7]}"  rendered="#{itm[12]==2}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处置日期" style="width:80px;text-align: center;" rendered="#{mgrbean.checkType==2}">
            <h:outputLabel value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处置机构" style="width:250px;" rendered="#{mgrbean.checkType==2}">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="审核日期" style="width:80px;text-align: center;" rendered="#{mgrbean.checkType==2}">
            <h:outputLabel value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处理期限" style="width:80px;text-align: center;">
            <h:outputText value="" rendered="#{(mgrbean.checkType ==1 and (itm[12]==1 or itm[12]==3))
                                        or (mgrbean.checkType ==2 and (itm[12]==2 or itm[12]==3))}"/>
            <p:outputLabel rendered="#{(mgrbean.checkType ==1 and (itm[12]==0 or itm[12]==2))
                                        or (mgrbean.checkType ==2 and (itm[12]==1))}">
                <p:outputLabel rendered="#{itm[11] le 0}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[11] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
                    <h:outputText value="当天截止" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[11] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
                    <h:outputText value="剩余#{itm[11]}天" style="color:#FFFFFF"/>
                </p:outputLabel>
            </p:outputLabel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <p:outputPanel rendered="#{mgrbean.checkType ==2}">
                <h:outputLabel value="待审核" rendered="#{itm[12]==1}"/>
                <h:outputLabel value="已退回" rendered="#{itm[12]==2}" style="color:red;"/>
                <h:outputLabel value="审核通过" rendered="#{itm[12]==3}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkType ==1}">
                <h:outputLabel value="待处置" rendered="#{itm[12]==0}"/>
                <h:outputLabel value="已处置" rendered="#{itm[12]==1}"/>
                <h:outputLabel value="终审退回" rendered="#{itm[12]==2}"  style="color:red;"/>
                <h:outputLabel value="终审通过" rendered="#{itm[12]==3}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="处置" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" resetValues="true"
                    rendered="#{mgrbean.checkType ==1 and (itm[12]==0 or itm[12]==2)}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:commandLink value="审核" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" resetValues="true"
                           rendered="#{mgrbean.checkType ==2 and itm[12]==1}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" resetValues="true"
                           rendered="#{(mgrbean.checkType ==1 and (itm[12]==1 or itm[12]==3)) or (mgrbean.checkType ==2 and (itm[12]==2 or itm[12]==3))}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

</ui:composition>