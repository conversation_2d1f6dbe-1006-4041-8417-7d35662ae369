<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
   <h:head>
      <title>#{request.getParameter("title")==null?'机构':request.getParameter("title")}选择</title>
      <h:outputStylesheet name="css/default.css"/>
      <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
      <script type="text/javascript">
         //<![CDATA[
         //]]>
      </script>
      <style type="text/css">
      </style>

   </h:head>

   <h:body onload="document.getElementById('codeForm:pym').focus();">
      <h:form id="codeForm">
         <h:outputStylesheet name="css/ui-tabs.css"/>
         <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3"
                         style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
                            class="ui-icon ui-icon-grip-dotted-vertical"/></span>
               <p:commandButton value="确定" icon="ui-icon-check"
                                action="#{selectManyOrgListBean.submitAction}"
                                process="@this,selectedTable"/>
               <p:commandButton value="取消" icon="ui-icon-close"
                                action="#{selectManyOrgListBean.dialogClose}" process="@this"/>
            </h:panelGrid>
            <p:inputText style="visibility: hidden;width: 0;"/>
         </p:outputPanel>
         <h:panelGrid columns="10" id="searchPanel">
            <p:inputText style="visibility: hidden;width: 0"/>
            <p:outputLabel value="地区：" styleClass="zwx_dialog_font" />
            <zwx:ZoneSingleComp zoneList="#{selectManyOrgListBean.zoneList}"
                                zoneCode="#{selectManyOrgListBean.searchZoneCode}"
                                zoneName="#{selectManyOrgListBean.searchZoneName}"
                                onchange="onSearchNodeSelect()"  panelHeight="300"
                                height="280" id="searchZone"/>
            <p:remoteCommand name="onSearchNodeSelect" action="#{selectManyOrgListBean.searchAction}"
                             process="@this,searchZone,selectedTable" update="selectedTable"/>
            <p:outputLabel value="#{null eq selectManyOrgListBean.searchUnitName?'单位名称':selectManyOrgListBean.searchUnitName}：" styleClass="zwx_dialog_font" />
            <p:inputText id="pym" value="#{selectManyOrgListBean.searchName}" style="width: 180px;" maxlength="20">
               <p:ajax event="keyup" update="selectedTable" process="@this,searchPanel,selectedTable" listener="#{selectManyOrgListBean.searchAction}"/>
            </p:inputText>
         </h:panelGrid>
         <p:dataTable var="itm"   value="#{selectManyOrgListBean.displayList}" id="selectedTable"
                      paginator="true" rows="10" emptyMessage="没有数据！"
                      paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                      currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                      pageLinks="5"
                      paginatorPosition="bottom"  rowsPerPageTemplate="#{'10,20,50'}">
            <p:column headerText="选择" style="text-align:center;width:30px;">
               <p:selectBooleanCheckbox value="#{itm.ifSelected}">
               </p:selectBooleanCheckbox>
            </p:column>
            <p:column headerText="地区" style="padding-left: 3px;width:240px;">
               <h:outputText escape="false" value="#{itm.zoneFullName}" />
            </p:column>
            <p:column headerText="#{null eq selectManyOrgListBean.searchUnitName?'单位名称':selectManyOrgListBean.searchUnitName}" style="padding-left: 3px;">
               <h:outputText escape="false" value="#{itm.unitName}" />
            </p:column>
         </p:dataTable>
      </h:form>
      <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
      <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
   </h:body>
</f:view>
</html>
