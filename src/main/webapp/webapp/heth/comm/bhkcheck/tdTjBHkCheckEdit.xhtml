<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdTjBHkCheckListBean"-->
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业健康检查数据审核" />
            </p:column>
        </p:row>
    </ui:define>

    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm" update=":tabView" rendered="#{mgrbean.editFlag}" oncomplete="datatableOffClick();">
                </p:commandButton>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.revokeCheck}" process="@this,:tabView"
                                 update=":tabView" rendered="#{mgrbean.cancelFlag}" oncomplete="datatableOffClick()"/>
                <p:commandButton value="下一个"
                                 action="#{mgrbean.nextAction}" process="@this,:tabView"
                                 update=":tabView" rendered="#{mgrbean.nextFlag}" oncomplete="datatableOffClick()"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" oncomplete="datatableOffClick()"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="#{mgrbean.tipInfo}" style="color:blue;" rendered="#{not empty mgrbean.tipInfo}"/>
            </p:outputPanel>
            <p:sticky target="sticky"></p:sticky>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:panelGrid  style="margin-bottom: 10px;margin-top: 5px;width: 100%" rendered="#{tdTjBHkCheckListBean.tdTjBhk.orgStateDesc!=null}">
            <f:facet name="header">
                <p:row>
                    <p:column  style="height:20px;font-size: 20px;text-align:left;" colspan="4" >
                        <p:outputLabel value="机构情况说明"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column  style="height:30px;width: 180px;text-align:right;">
                    <p:outputLabel value="情况说明："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                    <p:outputLabel  value="#{mgrbean.tdTjBhk.orgStateDesc}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" rendered="#{!mgrbean.proFlag}">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="审核意见"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column styleClass="column_title" style="width: 183px;height: 30px;">
                    <h:outputText value="*" style="color:red;" rendered="#{!mgrbean.proFlag}" />
                    <p:outputLabel value="审核结果："/>
                </p:column>
                <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                    <p:selectOneRadio  style="width:120px;"  value="#{mgrbean.checkState}" rendered="#{!mgrbean.proFlag}" >
                        <f:selectItem itemLabel="通过" itemValue="1" />
                        <f:selectItem itemLabel="退回" itemValue="2" />
                        <p:ajax event="change" process="@this,:tabView:editForm" listener="#{mgrbean.changeCheckState}" update=":tabView:editForm" />
                    </p:selectOneRadio>
                </p:column>
                <p:column styleClass="column_title" style="width: 183px;">
                    <h:outputText value="*" style="color:red;" rendered="#{!mgrbean.proFlag}" />
                    <p:outputLabel value="审核意见：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputTextarea rows="1" autoResize="false"
                                     style="overflow-y:hidden;resize: none;width: 594px;"
                                     maxlength="200" value="#{mgrbean.checkRst}" rendered="#{!mgrbean.proFlag}"
                    />
                </p:column>
            </p:row>
        </p:panelGrid>
        <!--国家退回-->
        <p:panelGrid  style="margin-bottom: 10px;margin-top: 5px;width: 100%" rendered="#{mgrbean.tdTjBhk.state==7}">
            <f:facet name="header">
                <p:row>
                    <p:column  style="height:20px;font-size: 20px;text-align:left;" colspan="4" >
                        <p:outputLabel value="国家退回原因"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row >
                <p:column style="height:30px;padding-left: 3px;border: 0px; text-indent:50px;" >
                    <h:outputText value="#{mgrbean.tdTjBhk.errMsg}" style="line-height: 30px;"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!--历次审核意见-->
        <p:panelGrid  style="margin-bottom: 10px;width:100%" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="历次审核意见"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column>
                    <p:dataTable value="#{mgrbean.historyList}" var="item" emptyMessage="暂无数据！">
                        <p:column headerText="审核类型"  style="width:100px;text-align: center">
                            <p:outputLabel value="#{item[4]}"/>
                        </p:column>
                        <p:column headerText="审核意见" style="width:510px;">
                            <p:outputLabel value="#{item[1]}"/>
                        </p:column>
                        <p:column headerText="审核人"  style="width:150px;text-align: center">
                            <p:outputLabel value="#{item[2]}"/>
                        </p:column>
                        <p:column headerText="审核日期" style="width:140px;text-align: center">
                            <p:outputLabel value="#{item[3]}">
                                <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8"></f:convertDateTime>
                            </p:outputLabel>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <ui:include src="tdTjBhkCheckAbnormalBase.xhtml" />
        <ui:include src="/webapp/heth/comm/tbTjBhkInfo.xhtml">
            <ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
            <ui:param name="pageParam" value="1"/>
            <ui:param name="birthIfShow" value="#{true}"/>
        </ui:include>
    </ui:define>

</ui:composition>
