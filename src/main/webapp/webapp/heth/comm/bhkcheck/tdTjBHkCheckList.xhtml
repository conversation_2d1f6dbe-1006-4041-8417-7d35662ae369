<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdTjBHkCheckListBean"-->
    <ui:param name="mgrbean" value="#{tdTjBHkCheckListBean}"/>
    <ui:param name="editPage" value="/webapp/heth/comm/bhkcheck/tdTjBHkCheckEdit.xhtml" />

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            //<![CDATA[
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            $(window).scroll(function() {
                if($("#tabView\\:mainForm\\:dataTable").offset()){
                    var tableHd = $("#tabView\\:mainForm\\:dataTable_head");
                    var tableBd = $("#tabView\\:mainForm\\:dataTable_data");
                    var tableTop = $("#tabView\\:mainForm\\:dataTable").offset().top;
                    var tableHdHeight = tableHd.height();
                    var cols = tableHd.find('th').length;
                    var _t = $(window).scrollTop();
                    var fixed =  tableHd.css("position");
                     if (_t - tableTop > 0 || (fixed == 'fixed' && (_t -tableTop + tableHdHeight > 0))) {
                        //table-header 已经到窗口顶部了
                        tableHd.css({
                            position: "fixed",
                            top: 0,
                            "z-index": PrimeFaces.zindex
                        })
                         tableHd.width(tableBd.width()+20);
                        if(tableBd.find('tr').length>0){
                            for (var i = 0; i < cols; i++) {
                                var tableHdTh = tableHd.find('tr').eq(0).find('th').eq(i);
                                var tableBdTh = tableBd.find('tr').eq(0).find('td').eq(i);
                                tableHdTh.width(tableBdTh.width()+1);
                            }
                        }
                    } else {
                        tableHd.css({
                            "position": ""
                        })
                    }
                }
            });
             //滚动条置顶
            function toTop(){
                window.scrollTo(0,0);
            }
            function getDownloadFileClick() {
                document.getElementById("tabView:mainForm:downloadErrFileBtn").click();
            }
            //]]>
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业健康检查数据审核"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="16" style="border-color:transparent;padding:0px;" >
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:spacer width="5"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" action="#{mgrbean.openReviewConfirmDialog}"
                				 oncomplete="datatableOffClick()" process="@this,dataTable" resetValues="true">
                    <f:setPropertyActionListener value="1" target="#{mgrbean.checkType}"/>
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="全部审核" icon="ui-icon-check" action="#{mgrbean.openReviewConfirmDialog}"
                                 oncomplete="datatableOffClick()" process="@this,mainGrid">
                    <f:setPropertyActionListener value="2" target="#{mgrbean.checkType}"/>
                </p:commandButton>
                <p:spacer width="5" rendered="#{mgrbean.ifImportExp}"/>
                <p:menuButton id="importRadhethBtn" value="批量退回数据导入" rendered="#{mgrbean.ifImportExp}" >
                    <p:menuitem value="模板下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <p:fileDownload value="#{mgrbean.getTemplateFile()}"/>
                    </p:menuitem>
                    <p:menuitem value="导入" icon="ui-icon-arrowreturnthick-1-n"
                                update=":tabView:mainForm:uploadFileDialog"
                                process="@this,mainGrid"
                                action="#{mgrbean.importBefore}"
                    >
                    </p:menuitem>
                </p:menuButton>
                <p:spacer width="5"  rendered="#{mgrbean.ifErrFileBnt}"/>
                <p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s"  rendered="#{mgrbean.ifErrFileBnt}"
                                 action="#{mgrbean.errFileBefore}" process="@this,:tabView:mainForm:mainGrid"/>
                <p:spacer width="5"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportBtn"
                                 process="@this" update="exportItemDialog" action="#{mgrbean.exportAction}">
                    <f:setPropertyActionListener value="1" target="#{mgrbean.needEnctryInfo}"/>
                </p:commandButton>
                <p:spacer width="5" rendered="#{mgrbean.ifHasInnerExp}" />
                <p:commandButton value="内部导出" icon="ui-icon-document" rendered="#{mgrbean.ifHasInnerExp}"
                                 process="@this" update="exportItemDialog" action="#{mgrbean.exportAction}">
                    <f:setPropertyActionListener value="0" target="#{mgrbean.needEnctryInfo}"/>
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="导出文件下载" icon="ui-icon-arrowthickstop-1-s" action="#{mgrbean.fileDownloadAction}"
                                 process="@this" />
                <p:commandButton value="错误数据下载" style="display: none;" id="downloadErrFileBtn"
                                 icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                 process="@this"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
                >
                    <p:fileDownload value="#{mgrbean.exportErr()}"/>
                </p:commandButton>

            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:200px;height:33px;">
                <h:outputText value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:300px;">
                <zwx:ZoneSingleNewComp id="searchZoneEmp" zoneList="#{mgrbean.zoneListEmp}"
                                    zoneCode="#{mgrbean.searchZoneCodeEmp}"
                                    zoneName="#{mgrbean.searchZoneNameEmp}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;" >
                <p:inputText value="#{mgrbean.searchCrptNameEmp}" style="width: 180px;" maxlength="50" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="用工单位社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchCreditCodeEmp}" style="width: 180px;" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:200px;height:33px;">
                <h:outputText value="用人单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:300px;">
                <zwx:ZoneSingleNewComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                      zoneCode="#{mgrbean.searchZoneCode}"
                      zoneName="#{mgrbean.searchZoneName}" ifShowTrash="true" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;" >
            	<p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;">
                <h:outputText value="用人单位社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
            	<p:inputText value="#{mgrbean.searchCreditCode}" style="width: 180px;" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="25" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchPersonName}" style="width: 180px;" maxlength="25" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="体检类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkType}" >
                    <f:selectItem itemLabel="职业健康检查" itemValue="3"/>
                    <f:selectItem itemLabel="放射卫生健康检查" itemValue="4" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;" >
                <p:outputLabel value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchBhkBdate}"
                                              endDate="#{mgrbean.searchBhkEdate}"  styleClass="myCalendar1"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.startCreateDate}"
                                              endDate="#{mgrbean.endCreateDate}" styleClass="myCalendar1"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="报告出具日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.startRptPrintDate}"
                                              endDate="#{mgrbean.endRptPrintDate}"  styleClass="myCalendar1"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:33px;">
                <h:outputText value="在岗状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.selectOnGuardIds}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="体检危害因素：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnIds}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        inputWidth="180"
                                        ifTree="true"
                                        ifSelectParent = "false"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:33px;">
                <h:outputText value="单危害因素结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
                                        selectedIds="#{mgrbean.searchSelBhkrstIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="180" height="200"/>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="监测类别：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" >
                <p:selectManyCheckbox value="#{mgrbean.jcTypes}">
                    <f:selectItem itemValue="1" itemLabel="常规监测"></f:selectItem>
                    <f:selectItem itemValue="2" itemLabel="主动监测"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="是否复检：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" >
                <p:selectManyCheckbox value="#{mgrbean.ifRhks}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="接收日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvEdate}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:33px;">
                <h:outputText value="是否异常：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:selectManyCheckbox value="#{mgrbean.ifAbnormals}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="异常原因：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" colspan="3">
                <p:inputText value="#{mgrbean.searchAbnormals}" style="width: 500px;" maxlength="50" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:33px;">
                <h:outputText value="检查机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <!-- 弹出框 -->
                <p:outputPanel >
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
                                            resetValues="true" update="unitName" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
        	<p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:20px;text-align:center;padding: 4px 10px !important;" disabledSelection="#{itm[15]=='1' or itm[19]=='1' ?false:true}"/>

        <p:column headerText="用工单位地区" style="width: 150px;padding-left: 8px !important;padding-right: 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <p:commandLink value="#{itm[1]}" action="#{mgrbean.modView(itm[15],'0')}" process="@this"  update=":tabView" resetValues="true" oncomplete="toTop();" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
        <p:column headerText="用工单位名称" style="width: 240px;padding-left: 8px !important;padding-right: 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <p:commandLink value="#{itm[2]}" action="#{mgrbean.modView(itm[15],'0')}" process="@this"  update=":tabView" resetValues="true" oncomplete="toTop();" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
        <p:column headerText="人员姓名" style="width:80px;text-align: center;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <p:commandLink value="#{itm[3]}" action="#{mgrbean.modView(itm[15],'0')}" process="@this"  update=":tabView" resetValues="true" oncomplete="toTop();" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
<!--        <p:column headerText="证件号码" style="width:135px;text-align: center;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">-->
<!--            <h:outputText value="#{itm[4]}" style="word-wrap: break-word; word-break: break-all;"/>-->
<!--        </p:column>-->
        <p:column headerText="在岗状态" style="text-align:center;width:80px;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="监测类别" style="text-align:center;width:80px;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <h:outputText value="常规监测" rendered="#{itm[6]==1}"/>
            <h:outputText value="主动监测" rendered="#{itm[6]==2}"/>
        </p:column>
        <p:column headerText="是否复检" style="text-align:center;width:60px;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <h:outputText value="是" rendered="#{itm[7]==1}"/>
            <h:outputText value="否" rendered="#{itm[7]==0}"/>
        </p:column>
<!--        <p:column headerText="检查机构" style="width: 240px;padding-left: 8px;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">-->
<!--            <h:outputText value="#{itm[9]}" />-->
<!--        </p:column>-->
        <p:column headerText="体检日期" style="width:80px;text-align: center;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <h:outputLabel value="#{itm[10]}" style="word-wrap: break-word; word-break: break-all;">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="是否异常" style="text-align:center;width:60px;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <h:outputText value="是" rendered="#{itm[8]==1}"/>
            <h:outputText value="否" rendered="#{itm[8]==0}"/>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;text-align: center;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}" >
            <h:outputLabel value="#{itm[17]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处理期限" style="width:80px;text-align: center;padding: 4px 10px !important;" >
            <h:outputText value="——" rendered="#{itm[15]!='1'}"/>
            <p:outputLabel rendered="#{itm[15]=='1'}">
                <p:outputLabel rendered="#{itm[16] lt 1}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[16] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
                    <h:outputText value="当天截止" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[16] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
                    <h:outputText value="剩余#{itm[16]}天" style="color:#FFFFFF"/>
                </p:outputLabel>
            </p:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width:80px;text-align:center;padding: 4px 10px !important;color:#{itm[8]==1 and itm[15]=='1'?'red':''}">
            <h:outputText value="#{itm[18]}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px !important;;padding-right: 10px !important;">
            <p:spacer width="5"/>
            <p:commandLink value="#{itm[15]=='1'?'审核':'详情'}" action="#{mgrbean.modView(itm[15],'0')}" process="@this"  update=":tabView" resetValues="true" oncomplete="toTop();">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <ui:include src="/webapp/heth/comm/reviewConfirmDialogCommNew.xhtml"/>

        <!-- 导出项目 -->
        <p:dialog id="exportItemDialog" header="体检项目（不勾选导出所有）"
                  widgetVar="ExportItemDialog" resizable="false" width="350" height="400" modal="true">

            <p:tree value="#{mgrbean.exportTreeNode}" var="node"
                    selectionMode="checkbox"
                    selection="#{mgrbean.selectedExportNodes}" id="exportTree"
                    style="width: 320px;height: 390px;overflow-y: auto;">
                <p:treeNode>
                    <h:outputText value="#{node.itemName}"/>
                </p:treeNode>
            </p:tree>

            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="导出" icon="ui-icon-document" id="menuSaveBtn2"
                                         actionListener="#{mgrbean.exportData}" process="@this,exportTree,:tabView"
                                         onclick="PF('ExportItemDialog').hide()"
                        >
                        </p:commandButton>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close" id="menuBackBtn2"
                                         onclick="PF('ExportItemDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <p:dialog header="职业健康检查数据审核退回导入" widgetVar="UploadFileDialog"
                  id="uploadFileDialog" resizable="false"
                  modal="true" width="800">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要文件上传！" styleClass="table-border-none"
                                      id="fileUpload"
                                      process="@this" fileUploadListener="#{mgrbean.importDataAction}"
                                      label="选择文件" invalidSizeMessage="文件大小不能超过200M!"
                                      validatorMessage="上传出错啦，请重新上传！"
                                      allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！"
                                      invalidFileMessage="只能上传xls、xlsx格式的文件！"
                                      previewWidth="120" cancelLabel="取消"
                                      uploadLabel="导入" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                      dragDropSupport="true" mode="advanced" sizeLimit="209715200"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>
