<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwMonthBhkProvListBean"-->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="职业健康检查常规监测月度汇总"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="buttons" style="display:flex">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;" id="buttonGrid">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <ui:insert name="insertSubEditButtons"/>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.beforeCancel}" process="@this,:tabView:viewForm">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}"
                                 update=":tabView" immediate="true"/>
            </h:panelGrid>
            <p:sticky target="buttons"/>
        </p:outputPanel>
        <p:confirmDialog message="确定要撤销吗？" header="消息确认框" widgetVar="ConfirmCancelDialog">
            <p:commandButton value="确定" action="#{mgrbean.cancelAction}" icon="ui-icon-check"
                             oncomplete="PF('ConfirmCancelDialog').hide();" update=":tabView"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmCancelDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>
    <!-- 页面的内容-->
    <ui:define name="insertOtherContents">
        <p:panelGrid style="width:100%;height:100%;" id="titleGrid">
            <p:row>
                <p:column style="text-align:right;padding-left:5px;width:150px;height: 38px;">
                    <p:outputLabel value="报告出具日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;">
                    <h:outputText value="#{mgrbean.startDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                    <h:outputText value=" ~ "/>
                    <h:outputText value="#{mgrbean.endDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:outputPanel id="groupPanel">
            <table style="width:100%;height:100%;">
                <tr>
                    <td style="width:43%;vertical-align: top;">
                        <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
                                    style="margin-top: 5px;margin-bottom: 5px;">
                            <p:panelGrid id="orgQueryInfo" style="width:100%;">
                                <p:row>
                                    <p:column styleClass="cs-scl-first">
                                        <h:outputText value="地区："/>
                                    </p:column>
                                    <p:column styleClass="cs-scv-w" style="padding-left: 6px !important;">
                                        <zwx:ZoneSingleComp id="editZone" zoneList="#{mgrbean.zoneList}"
                                                            panelHeight="350"
                                                            height="330"
                                                            zoneCode="#{mgrbean.editZoneCode}"
                                                            zoneName="#{mgrbean.editZoneName}"
                                                            onchange="onEditZoneSelect()"/>
                                        <p:remoteCommand name="onEditZoneSelect" action="#{mgrbean.clearEditUnit}"
                                                         process="@this,editZone" update="@this,unitEditName"/>
                                    </p:column>
                                    <p:column styleClass="cs-scl-w">
                                        <h:outputText value="职业健康检查机构："/>
                                    </p:column>
                                    <p:column styleClass="cs-scv">
                                        <!-- 弹出框 -->
                                        <p:outputPanel>
                                            <table>
                                                <tr>
                                                    <td style="padding: 0;border-color: transparent;">
                                                        <p:inputText id="unitEditName"
                                                                     value="#{mgrbean.editUnitName}"
                                                                     style="width: 180px;cursor: pointer;"
                                                                     onclick="document.getElementById('tabView:viewForm:selEditUnitLink').click();"
                                                                     readonly="true"/>
                                                    </td>
                                                    <td style="border-color: transparent;">
                                                        <p:commandLink styleClass="ui-icon ui-icon-search"
                                                                       id="selEditUnitLink"
                                                                       action="#{mgrbean.selEditUnitAction}"
                                                                       process="@this"
                                                                       style="position: relative;left: -28px !important;">
                                                            <p:ajax event="dialogReturn"
                                                                    listener="#{mgrbean.onEditSelectUnitAction}"
                                                                    process="@this"
                                                                    resetValues="true" update="unitEditName"/>
                                                        </p:commandLink>
                                                    </td>
                                                    <!-- 清空 -->
                                                    <td style="border-color: transparent;position: relative;left: -30px;">
                                                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                                       process="@this" update="unitEditName"
                                                                       action="#{mgrbean.clearEditUnit}">
                                                        </p:commandLink>
                                                    </td>
                                                </tr>
                                            </table>
                                        </p:outputPanel>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </p:fieldset>
                        <p:commandButton value="查询" action="#{mgrbean.searchViewAction}" update=":tabView:viewForm"
                                         process="@this,:tabView:viewForm"
                                         styleClass="button-inline"/>
                        <p:dataTable var="itm" value="#{mgrbean.orgList}"
                                     id="orgTable" lazy="true" emptyMessage="没有您要找的记录！"
                                     rowIndexVar="R" rowKey="#{itm}"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="10,20,50" pageLinks="5"
                                     paginator="true" rows="10" paginatorPosition="bottom"
                                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                     selectionMode="single" selection="#{mgrbean.orgObj}">
                            <p:ajax event="rowSelect" process="orgTable" listener="#{mgrbean.onRowViewSelect}"
                                    update=":tabView:viewForm">
                                <p:resetInput target=":tabView:viewForm"/>
                            </p:ajax>
                            <p:column headerText="地区" style="width:100px;line-height: 24px;">
                                <h:outputText value="#{itm[1]}"/>
                            </p:column>
                            <p:column headerText="职业健康检查机构" style="width:200px;">
                                <h:outputText value="#{itm[2]}"/>
                            </p:column>
                        </p:dataTable>
                    </td>
                    <td style="width:2%;vertical-align: top;">
                    </td>
                    <td style="vertical-align: top;">
                        <p:outputLabel value="" style="line-height: 5px;"/>
                        <br/>
                        <p:outputLabel value="职业健康检查机构名称：" style="line-height: 38px;" id="orgNameText"
                                       rendered="#{mgrbean.orgBadrsns ne null and mgrbean.orgBadrsns.size() >0 }"/>
                        <p:outputLabel value="#{mgrbean.orgName}" style="line-height: 38px;" id="orgName"
                                       rendered="#{mgrbean.orgBadrsns ne null and mgrbean.orgBadrsns.size() >0 }"/>
                        <p:dataTable var="itm" value="#{mgrbean.orgBadrsns}" id="orgBadrsnsTable"
                                     emptyMessage="没有您要找的记录！" paginatorPosition="top" style="margin-top: 5px;"
                                     rendered="#{mgrbean.orgBadrsns ne null and mgrbean.orgBadrsns.size() >0 }">
                            <p:column headerText="危害因素" style="width:100px;line-height: 24px;">
                                <h:outputText value="#{itm.fkByBadrsnId.codeName}"/>
                            </p:column>
                            <p:column headerText="接触职业病危害因素人数"
                                      style="width:100px;text-align:center">
                                <h:outputText value="#{itm.holdCardNum}"/>
                            </p:column>
                            <p:column headerText="职业健康检查人数"
                                      style="width:100px;text-align:center">
                                <h:outputText value="#{itm.bhkNum}"/>
                            </p:column>
                            <p:column headerText="发现职业禁忌证人数"
                                      style="width:100px;text-align:center">
                                <h:outputText value="#{itm.contraindlistNum}"/>
                            </p:column>
                            <p:column headerText="发现疑似职业病数量"
                                      style="width:100px;text-align:center">
                                <h:outputText value="#{itm.suspectedNum}"/>
                            </p:column>
                        </p:dataTable>
                    </td>
                </tr>
            </table>
        </p:outputPanel>
    </ui:define>
</ui:composition>