<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.RoutineMonitoringSummarizeAndFillInBean"-->
    <ui:param name="mgrbean" value="#{routineMonitoringSummarizeAndFillInBean}"/>
    <!--编辑页面-->
    <ui:param name="editPage" value="/webapp/heth/comm/bhkcheck/routineMonitoring/summarizeAndFillInEdit.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputStylesheet library="css" name="ui-cs.css"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
        <script type="text/javascript">
            function getDownloadFileClick(){
                document.getElementById("tabView:editForm:downloadFileBtn").click();
            }

            function markErrorInfo(id){
                if(id){
                    var arr = id.split(",");
                    $.each(arr,function(index,obj){
                        var $id = $("#"+obj);
                        if($id){console.log($id.hasClass("ui-selectmanycheckbox"));
                            if($id.hasClass("ui-selectoneradio")){
                                $id.find(".ui-radiobutton-box").addClass("ui-state-error");
                            }else if($id.hasClass("ui-selectmanycheckbox")||$id.hasClass("ui-chkbox")||$id.hasClass("checkbox-padding")){
                                $id.find(".ui-chkbox-box").addClass("ui-state-error");
                            }else{
                                $id.addClass("ui-state-error");
                                if($id.hasClass("ui-selectonemenu")){
                                    $id.find(".ui-selectonemenu-trigger").addClass("ui-state-error");
                                }
                            }
                        }
                    });
                }
            }
        </script>
    </ui:define>
    <!-- 修改页面 -->
    <!--<ui:param name="editPage" value="/webapp/heth/comm/.xhtml"/>-->
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="职业健康检查常规监测汇总填报"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
                <p:commandButton value="添加" icon="ui-icon-plus" action="#{mgrbean.preAddAction}"
                                 process="@this,:tabView:mainForm:mainGrid"/>
                <p:inputText style="visibility: hidden;width: 0;"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputLabel value="报告出具日期："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <zwx:CalendarDynamicLimitComp styleClass="myCalendar1"
                                              startDate="#{mgrbean.searchRptSDate}"
                                              endDate="#{mgrbean.searchRptEDate}"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <p:inputText style="width: 180px;" value="#{mgrbean.searchCrptName}" maxlength="50"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="padding-left: 4px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox value="#{mgrbean.searchState}">
                    <f:selectItem itemLabel="待提交" itemValue="0"/>
                    <f:selectItem itemLabel="已提交" itemValue="1"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="报告出具日期" style="text-align: center;width: 200px;">
            <h:outputText value="#{itm[1]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
            <h:outputText value=" ~ "/>
            <h:outputText value="#{itm[2]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="用人单位数" style="text-align: center;width: 100px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用人单位已填报数" style="text-align: center;width: 150px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="填报日期" style="text-align: center;width: 100px;">
            <h:outputText value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="最后修订日期" style="text-align: center;width: 150px;">
            <h:outputText value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputText>
        </p:column>
        <p:column headerText="状态" style="text-align: center;width: 70px;">
            <h:outputText value="待提交" rendered="#{itm[7] eq 0}"/>
            <h:outputText value="已提交" rendered="#{itm[7] eq 1 or itm[7] eq 2}"/>
        </p:column>
        <p:column headerText="操作" style="">
            <p:commandLink value="修改" rendered="#{itm[7] eq 0}" resetValues="true"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           onclick="zwx_loading_start();" action="#{mgrbean.preModAction(true)}" >
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="false"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[7] eq 1 or itm[7] eq 2}"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.preModAction(false)}" resetValues="true">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="true"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" rendered="#{itm[7] eq 0}" resetValues="true"
                           process="@this,:tabView:mainForm:mainGrid" update="dataTable"
                           action="#{mgrbean.delAction}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog id="addDialog" header="添加报告出具周期" widgetVar="AddDialog" resizable="false" width="500"
                  height="95" modal="true">
            <p:panelGrid style="width:100%;" id="addGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="报告出具周期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu value="#{mgrbean.monthBhk.selYear}" style="width: 100px;">
                            <f:selectItems value="#{mgrbean.selYearList}"/>
                            <p:ajax event="change" process="@this,addGrid" update="addGrid"
                                    listener="#{mgrbean.onAddDialogYearChange}"/>
                        </p:selectOneMenu>
                        <p:spacer width="5"/>
                        <p:selectOneMenu value="#{mgrbean.monthBhk.selMonth}" style="width: 100px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.selMonthList}"/>
                            <p:ajax event="change" process="@this,addGrid" update="addGrid"
                                    listener="#{mgrbean.onAddDialogMonthChange}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="报告出具日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputPanel rendered="#{not empty mgrbean.monthBhk.startBhkDate}">
                            <h:outputText value="#{mgrbean.monthBhk.startBhkDate}">
                                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                            </h:outputText>
                            <h:outputText value=" ~ "/>
                            <h:outputText value="#{mgrbean.monthBhk.endBhkDate}">
                                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                            </h:outputText>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" icon="ui-icon-check" resetValues="true"
                                         process="@this,addGrid" onclick="zwx_loading_start();"
                                         action="#{mgrbean.addInit}" />
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('AddDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>