<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
   <!-- 托管Bean -->
   <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.SummarizeStatisticsByMonthOrQuarterBean"-->
   <ui:param name="mgrbean" value="#{summarizeStatisticsByMonthOrQuarterBean}"/>
   <!-- 样式或javascripts -->
   <ui:define name="insertScripts">
      <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
      <h:outputScript library="js" name="namespace.js"/>
      <h:outputScript name="js/validate/system/validate.js"/>
      <script type="text/javascript">
         //<![CDATA[
         function generateClick(){
            document.getElementById("tabView:mainForm:downLoadFileBtnId").click();
         }
         //]]>
      </script>
      <style type="text/css">
         .myCalendar1 input {
            width: 78px;
         }
      </style>
   </ui:define>
   <!-- 标题栏 -->
   <ui:define name="insertTitle">
      <p:row>
         <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
            <h:outputText value="职业健康检查常规监测月度/季度汇总"/>
         </p:column>
      </p:row>
   </ui:define>
   <!-- 按钮 -->
   <ui:define name="insertButtons">
      <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
         <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
            <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.preSearchAction}"
                             process="@this,mainGrid" update="dataTable"
                             onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
            <p:commandButton value="导出" icon="ui-icon-document" process="@this,mainGrid" action="#{mgrbean.preDownLoadFile}" />
            <p:commandButton style="display: none;" id="downLoadFileBtnId" ajax="false" icon="ui-icon-print"
                             process="@this,mainForm" onclick="PrimeFaces.monitorDownload(zwx_loading_start,zwx_loading_stop);"  >
               <p:fileDownload  value="#{mgrbean.getDownloadFile()}" />
            </p:commandButton>
            <p:remoteCommand update="dataTable" name="exeSearch" process="@this" action="#{mgrbean.searchAction}" />
         </h:panelGrid>
      </p:outputPanel>
   </ui:define>
   <!-- 查询条件 -->
   <ui:define name="insertSearchConditons">
      <p:row>
         <p:column style="text-align:right;padding-right:3px;height:38px;width:160px;">
            <h:outputText value="地区："/>
         </p:column>
         <p:column style="text-align:left;padding-left: 8px;width:260px;" id="zoneCol">
            <zwx:ZoneSingleNewComp zoneList="#{mgrbean.searchZoneList}"
                                   zoneCode="#{mgrbean.searchCheckOrgZoneGb}"
                                   zoneName="#{mgrbean.searchCheckOrgZoneName}"
                                   zonePaddingLeft="0" onchange="#{mgrbean.clearCheckOrg()}"/>
         </p:column>
         <p:column style="text-align:right;padding-right:3px;height:38px;width:160px;">
            <h:outputText value="职业健康检查机构："/>
         </p:column>
         <p:column style="text-align:left;padding-left:4px;width:260px;">
            <p:outputPanel>
               <table>
                  <tr>
                     <td style="padding: 0;border-color: transparent;">
                        <p:inputText id="checkOrgName" style="width: 180px;cursor: pointer;"
                                     value="#{mgrbean.searchCheckOrgName}"
                                     onclick="document.getElementById('tabView:mainForm:selCheckOrgLink').click();"
                                     readonly="true"/>
                     </td>
                     <td style="border-color: transparent;">
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="selCheckOrgLink"
                                       style="position: relative;left: -28px !important;"
                                       action="#{mgrbean.selCheckOrgAction}"
                                       process="@this,:tabView:mainForm:zoneCol">
                           <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectCheckOrgAction}"
                                   process="@this" update="checkOrgName" resetValues="true"/>
                        </p:commandLink>
                     </td>
                     <!-- 清空 -->
                     <td style="border-color: transparent;position: relative;left: -30px;">
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       process="@this" update="checkOrgName" action="#{mgrbean.clearCheckOrg()}">
                        </p:commandLink>
                     </td>
                  </tr>
               </table>
            </p:outputPanel>
         </p:column>
         <p:column style="text-align:right;padding-right:3px;width:160px;">
            <h:outputText value="统计类型："/>
         </p:column>
         <p:column style="text-align:left;padding-left:7px;">
            <p:selectOneRadio value="#{mgrbean.analyType}" style="width: 180px;">
               <f:selectItem itemLabel="按月度" itemValue="0"/>
               <f:selectItem itemLabel="按季度" itemValue="1"/>
               <p:ajax event="change" process="@this" update="rptRangeId"
                       listener="#{mgrbean.changeAnalyType}" />
            </p:selectOneRadio>
         </p:column>
      </p:row>
      <p:row>
         <p:column style="text-align:right;padding-right:3px;height:38px;">
            <p:outputLabel style="color:red;" value="*"/>
            <h:outputText value="报告出具周期："/>
         </p:column>
         <p:column style="text-align:left;padding-left: 8px;" colspan="5">
            <p:outputLabel id="rptRangeId">
               <p:selectOneMenu value="#{mgrbean.searchYear}" style="width: 80px;">
                  <f:selectItems value="#{mgrbean.searchYearList}" var="itm" itemValue="#{itm}" itemLabel="#{itm}年"/>
                  <p:ajax event="change" listener="#{mgrbean.changeYear}" process="@this,@parent" update="rptRangeId" />
               </p:selectOneMenu>
               <p:selectOneMenu value="#{mgrbean.searchOt}" style="width: 90px; margin-left: 5px;" id="searchOt">
                  <f:selectItem itemLabel="--请选择--" />
                  <c:forEach items="#{mgrbean.searchOtList}" var="itm">
                     <c:if test="#{mgrbean.analyType == 0}">
                        <f:selectItem itemValue="#{itm}" itemLabel="#{itm}月" />
                     </c:if>
                     <c:if test="#{mgrbean.analyType == 1}">
                        <f:selectItem itemValue="#{itm}" itemLabel="第#{itm == 1 ? '一' : (itm == 2 ? '二' : (itm == 3 ? '三' : '四'))}季度" />
                     </c:if>
                  </c:forEach>
               </p:selectOneMenu>
            </p:outputLabel>
         </p:column>
      </p:row>
   </ui:define>
   <!-- 表格列 -->
   <ui:define name="insertDataTable">
      <p:columnGroup type="header">
         <p:row>
            <p:column colspan="7" style="height: 26px;">
               <f:facet name="header">
                  <h:outputText value="#{mgrbean.tableTitle}"/>
               </f:facet>
            </p:column>
         </p:row>
         <p:row>
            <p:column style="height: 26px; width: 15%; text-align: center;">
               <f:facet name="header">
                  <h:outputText value="地区"/>
               </f:facet>
            </p:column>
            <p:column style="width: 20%; text-align: center;">
               <f:facet name="header">
                  <h:outputText value="职业健康检查机构"/>
               </f:facet>
            </p:column>
            <p:column style="width: 15%; text-align: center;">
               <f:facet name="header">
                  <h:outputText value="危害因素"/>
               </f:facet>
            </p:column>
            <p:column style="width: 15%; text-align: center;">
               <f:facet name="header">
                  <h:outputText value="接触职业病危害因素人数"/>
               </f:facet>
            </p:column>
            <p:column style="width: 15%; text-align: center;">
               <f:facet name="header">
                  <h:outputText value="职业健康检查人数"/>
               </f:facet>
            </p:column>
            <p:column style="width: 10%; text-align: center;">
               <f:facet name="header">
                  <h:outputText value="发现职业禁忌证人数"/>
               </f:facet>
            </p:column>
            <p:column style="text-align: center;">
               <f:facet name="header">
                  <h:outputText value="发现疑似职业病数量"/>
               </f:facet>
            </p:column>
         </p:row>
      </p:columnGroup>
      <p:column style="padding-left: 5px; height: 26px;">
         <h:outputText value="#{itm[0]}" />
      </p:column>
      <p:column style="padding-left: 5px;">
         <h:outputText value="#{itm[1]}" />
      </p:column>
      <p:column style="padding-left: 5px;">
         <h:outputText value="#{itm[2]}" />
      </p:column>
      <p:column style="text-align: center;">
         <h:outputText value="#{itm[3]}" />
      </p:column>
      <p:column style="text-align: center;">
         <h:outputText value="#{itm[4]}" />
      </p:column>
      <p:column style="text-align: center;">
         <h:outputText value="#{itm[5]}" />
      </p:column>
      <p:column style="text-align: center;">
         <h:outputText value="#{itm[6]}" />
      </p:column>
   </ui:define>
</ui:composition>