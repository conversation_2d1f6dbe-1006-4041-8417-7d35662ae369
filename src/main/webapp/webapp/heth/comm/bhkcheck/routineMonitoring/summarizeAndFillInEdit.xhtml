<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.RoutineMonitoringSummarizeAndFillInBean"-->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业健康检查常规监测汇总填报" />
                <p:inputText style="visibility: hidden;width: 0;"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="buttons">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;" id="buttonGrid">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <ui:insert name="insertSubEditButtons"/>
                <p:commandButton value="提交" icon="ui-icon-check" update="@form" rendered="#{!mgrbean.ifView}"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm,:tabView:editForm:monthBadrsnTable" resetValues="true">
                </p:commandButton>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="searchBtn3" action="#{mgrbean.beforeCancleAction}"
                                 process="@this" rendered="#{mgrbean.monthBhk.state ge 1}" resetValues="true">
                </p:commandButton>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportBtn"
                                 action="#{mgrbean.exportBefore}" process="@this,:tabView:editForm"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}"
                                 update=":tabView" immediate="true"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.downloadFile}"/>
                </p:commandButton>
            </h:panelGrid>
            <p:sticky target="buttons"/>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                             oncomplete="PF('ConfirmDialog').hide();" update=":tabView" resetValues="true"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:confirmDialog message="确定要撤销吗？" header="消息确认框" widgetVar="CancleDialog">
            <p:commandButton value="确定" action="#{mgrbean.cancleAction}" icon="ui-icon-check"
                             oncomplete="PF('CancleDialog').hide();" resetValues="true" />
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('CancleDialog').hide();" type="button"/>
        </p:confirmDialog>
    </ui:define>
    <!-- 页面的内容-->
    <ui:define name="insertOtherContents">
        <p:panelGrid style="width:100%;height:100%;margin-bottom:20px;" id="titleGrid">
            <p:row>
                <p:column style="text-align:right;padding-left:5px;width:150px;height: 38px;">
                    <p:outputLabel value="报告出具日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;">
                    <h:outputText value="#{mgrbean.monthBhk.startBhkDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                    <h:outputText value=" ~ "/>
                    <h:outputText value="#{mgrbean.monthBhk.endBhkDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
            </p:row>
        </p:panelGrid>
        <div style="display: flex;">
            <div style="display: table-cell;vertical-align: middle;width: 35%;">

                    <div style="display: table-row;">
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputLabel value="用人单位名称："/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:inputText style="width: 180px;" id="searchECrptName" value="#{mgrbean.searchECrptName}"
                                         maxlength="50" >
                                <p:ajax event="keyup"  process="@this,:tabView:editForm" listener="#{mgrbean.searchCrptAction}" resetValues="true"/>
                            </p:inputText>
                        </div>
                        <c:if test="#{!mgrbean.ifView}">
                            <div style="display: table-cell;vertical-align: middle;">
                                <h:outputLabel value="状态：" style="margin-left: 50px;"/>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;">
                                <p:selectManyCheckbox id="searchEState" value="#{mgrbean.searchEState}"  process="@this" onchange="changeStatue()">
                                    <f:selectItem itemLabel="未填报" itemValue="0"/>
                                    <f:selectItem itemLabel="已填报" itemValue="1"/>
                                </p:selectManyCheckbox>
                                <p:remoteCommand name="changeStatue" action="#{mgrbean.searchCrptAction}"
                                                 process="@this,:tabView:editForm" resetValues="true" />
                            </div>
                        </c:if>
                    </div>

                <p:dataTable var="itm" value="#{mgrbean.dataTableList}"
                             id="dataTable" lazy="true" emptyMessage="没有您要找的记录！"
                             rowIndexVar="R" rowKey="#{itm[0]}" style="margin-top: 5px;"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="10,20,50" pageLinks="5"
                             paginator="true" rows="10" paginatorPosition="bottom"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             selectionMode="single" selection="#{mgrbean.selCrpt}">
                    <p:ajax event="rowSelect" process="dataTable,:tabView:editForm:monthBadrsnTable" listener="#{mgrbean.onRowSelect}"
                            update=":tabView:editForm" resetValues="true">
                        <p:resetInput target=":tabView:editForm"/>
                    </p:ajax>
                    <p:column headerText="用人单位名称" style="width:100px;line-height: 24px;">
                        <h:outputText value="#{itm[1]}"/>
                    </p:column>
                    <p:column headerText="状态" style="text-align:center;width: 50px;line-height: 24px;">
                        <h:outputText value="未填报" rendered="#{itm[2] eq 0}" style="color: red;"/>
                        <h:outputText value="已填报" rendered="#{itm[2] eq 1}"/>
                    </p:column>
                </p:dataTable>
            </div>
            <p:outputPanel id="badrsnPanel" style="width: 63%;margin-left: 35px;#{mgrbean.selCrpt eq null?'display:none;':''};"  >
                <div style="display: table-cell;vertical-align: middle;width: inherit;">
                    <div style="display: flex;justify-content: space-between;">
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputLabel value="用人单位名称："/>
                            <h:outputLabel value="#{mgrbean.selCrptTemp[1]}"/>
                        </div>
                        <c:if test="#{!mgrbean.ifView}">
                            <div style="display: table-cell;vertical-align: middle;">
                                <p:commandButton value="重新汇总"  style="margin-left: 100px;"
                                                 onclick="PF('ReSummarizeConfirmDialog').show();"
                                                 rendered="#{!mgrbean.ifView}"/>
                                <h:outputLabel value="注：" style="color: red;margin-left: 10px;font-weight: 400 !important;"/>
                                <h:outputLabel value="体检数据发生变更时，需点击重新汇总！" style="font-weight: 400 !important;;"/>
                            </div>
                        </c:if>
                    </div>
                    <p:dataTable var="item" value="#{mgrbean.monthBadrsnList}" id="monthBadrsnTable"
                                 emptyMessage="没有您要找的记录！" paginatorPosition="top" style="margin-top: 5px;" >
                        <p:columnGroup type="header">
                            <p:row>
                                <p:column headerText="危害因素" style="width:140px;text-align: center"/>
                                <p:column headerText="接触职业病危害因素劳动者（人）#{mgrbean.redFont}本周期内职业健康检查对应的接害人数" style="width:220px;text-align: center;" />
                                <p:column headerText="当年实际接受职业健康检查劳动者（人）" style="width:210px;text-align: center;"/>
                                <p:column headerText="检出的疑似职业病（人）" style="width:140px;text-align: center;"/>
                                <p:column headerText="职业禁忌证（人）" style="width:140px;text-align: center;"/>
                            </p:row>
                        </p:columnGroup>
                        <p:column style="line-height: 24px;">
                            <h:outputLabel value="#{item[1]}"/>
                        </p:column>
                        <p:column style="text-align: center;line-height: 24px;">
                            <p:inputText style="width: 180px;" id="psnNum#{item[0]}" value="#{item[2]}" rendered="#{!mgrbean.ifView}"
                                         maxlength="6" onkeydown="SYSTEM.verifyNum3(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum3(this, 6, 0, false)" onblur="SYSTEM.verifyNum3(this, 6, 0, true)"/>
                            <h:outputLabel value="#{item[2]}" rendered="#{mgrbean.ifView}"/>
                        </p:column>
                        <p:column style="text-align: center;line-height: 24px;">
                            <p:inputText style="width: 180px;" id="actualPsnNum#{item[0]}" value="#{item[3]}" rendered="#{!mgrbean.ifView}"
                                         maxlength="6"  onkeydown="SYSTEM.verifyNum3(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum3(this, 6, 0, false)" onblur="SYSTEM.verifyNum3(this, 6, 0, true)"/>
                            <h:outputLabel value="#{item[3]}" rendered="#{mgrbean.ifView}"/>
                        </p:column>
                        <p:column style="text-align: center;line-height: 24px;">
                            <p:inputText style="width: 180px;" id="occPsnNum#{item[0]}" value="#{item[4]}" rendered="#{!mgrbean.ifView}"
                                         maxlength="6"  onkeydown="SYSTEM.verifyNum3(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum3(this, 6, 0, false)" onblur="SYSTEM.verifyNum3(this, 6, 0, true)"/>
                            <h:outputLabel value="#{item[4]}" rendered="#{mgrbean.ifView}"/>
                        </p:column>
                        <p:column style="text-align: center;line-height: 24px;">
                            <p:inputText style="width: 180px;" id="conPsnNum#{item[0]}" value="#{item[5]}" rendered="#{!mgrbean.ifView}"
                                         maxlength="6" onkeydown="SYSTEM.verifyNum3(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum3(this, 6, 0, false)" onblur="SYSTEM.verifyNum3(this, 6, 0, true)" />
                            <h:outputLabel value="#{item[5]}" rendered="#{mgrbean.ifView}"/>
                        </p:column>
                    </p:dataTable>
                    <p:commandButton value="保存" icon="ui-icon-disk" style="margin-top: 10px;" rendered="#{!mgrbean.ifView}"
                                     process="@this,:tabView:editForm,:tabView:editForm:monthBadrsnTable" update="@this,:tabView:editForm,:tabView:editForm:monthBadrsnTable,:tabView:editForm:dataTable"
                                     action="#{mgrbean.saveBadrsnAction(true,true)}"/>
                </div>

                <p:confirmDialog message="将重新汇总并刷新#{mgrbean.selCrpt[1]}的数据，是否确认？"
                                 header="消息确认框" widgetVar="ReSummarizeConfirmDialog">
                    <p:outputPanel style="text-align: center;">
                        <p:commandButton value="确定" action="#{mgrbean.beforeReInitSummarizeValue}"
                                         icon="ui-icon-check"  onclick="zwx_loading_start();"
                                         process="@this" oncomplete="PF('ReSummarizeConfirmDialog').hide();zwx_loading_stop();"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('ReSummarizeConfirmDialog').hide();"
                                         type="button"/>
                    </p:outputPanel>
                </p:confirmDialog>
                <p:confirmDialog message="#{mgrbean.selCrpt[1]}在当前周期内无体检数据，将删除此记录，是否确认？"
                                 header="消息确认框" widgetVar="ReReSummarizeConfirmDialog">
                    <p:outputPanel style="text-align: center;">
                        <p:commandButton value="确定" action="#{mgrbean.removeCrpt}" icon="ui-icon-check"
                                         process="@this"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('ReReSummarizeConfirmDialog').hide();"
                                         type="button"/>
                    </p:outputPanel>
                </p:confirmDialog>
                <p:commandButton style="display: none;" ajax="false">
                </p:commandButton>
            </p:outputPanel>
        </div>
    </ui:define>
</ui:composition>