<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.RoutineMonitoringAnnualSummaryBean"-->
    <ui:param name="mgrbean" value="#{routineMonitoringAnnualSummaryBean}"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            //<![CDATA[
            $(document).ready(function () {
                hbDyg();
            });

            /** 合并表格的第一列 */
            function hbDyg() {
                if (jQuery("input[name='mainForm:summaryType']:checked").val() === '2') {
                    return;
                }
                const trs = jQuery("#mainForm\\:dataTable_data").children();
                const size = trs.length;
                const td0 = jQuery(trs[0]).children()[0];
                if (jQuery(trs[0]).hasClass('ui-datatable-empty-message')) {
                    jQuery(td0).css("text-align", "left");
                    return;
                }
                jQuery(trs).each(function (i) {
                    const firstChild = jQuery(this).children()[0];
                    if (i === 0) {
                        jQuery(firstChild).attr("rowspan", size);
                        jQuery(firstChild).css("text-align", "center");
                    } else {
                        jQuery(firstChild).hide();
                    }
                });
            }

            function generateClick() {
                document.getElementById("mainForm:downLoadFileBtnId").click();
            }

            //]]>
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="职业健康检查常规监测年度汇总"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.preSearchAction}"
                                 process="@this,mainGrid" onclick="zwx_loading_start_pub();"/>
                <p:commandButton value="导出" icon="ui-icon-document" process="@this,mainGrid"
                                 action="#{mgrbean.preDownLoadFile}"/>
                <p:commandButton style="display: none;" id="downLoadFileBtnId" ajax="false" icon="ui-icon-print"
                                 process="@this,mainForm"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start_pub,zwx_loading_stop_pub);">
                    <p:fileDownload value="#{mgrbean.getDownloadFile()}"/>
                </p:commandButton>
                <p:remoteCommand name="exeSearch" action="#{mgrbean.searchAction}"
                                 process="@this" update="dataTable" oncomplete="hbDyg();zwx_loading_stop_pub();"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;width:160px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;width:260px;" id="zoneCol">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="0"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;width:160px;">
                <p:outputLabel style="color:red;" value="*"/>
                <h:outputText value="报告出具周期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:260px;">
                <p:outputLabel id="rptRangeId">
                    <p:selectOneMenu value="#{mgrbean.searchYear}" style="width: 190px;">
                        <f:selectItems value="#{mgrbean.searchYearList}"
                                       var="itm" itemValue="#{itm}" itemLabel="#{itm}年"/>
                        <p:ajax event="change" listener="#{mgrbean.changeSearchYear}"
                                process="@this,rptRangeId" update="rptRangeId"/>
                    </p:selectOneMenu>
                </p:outputLabel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="统计类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;">
                <p:selectOneRadio id="summaryType" value="#{mgrbean.summaryType}" style="width: 160px;">
                    <f:selectItem itemLabel="按合计" itemValue="1"/>
                    <f:selectItem itemLabel="按辖区" itemValue="2"/>
                    <p:ajax event="change" process="@this" update="@this"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertContent">
        <p:dataTable var="itm" value="#{mgrbean.dataModel}" paginator="#{mgrbean.summaryType eq 2}"
                     rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     rowsPerPageTemplate="#{mgrbean.perPageSize}" id="dataTable" lazy="true"
                     emptyMessage="没有您要找的记录！"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     rendered="#{dataTable==null}">
            <p:columnGroup type="header">
                <p:row>
                    <p:column colspan="7" style="height: 26px;">
                        <f:facet name="header">
                            <h:outputText value="#{mgrbean.tableTitle}"/>
                        </f:facet>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="height: 26px; width: 15%; text-align: center;">
                        <f:facet name="header">
                            <h:outputText value="地区"/>
                        </f:facet>
                    </p:column>
                    <p:column style="width: 20%; text-align: center;">
                        <f:facet name="header">
                            <h:outputText value="危害因素"/>
                        </f:facet>
                    </p:column>
                    <p:column style="text-align: center;">
                        <f:facet name="header">
                            <h:outputText value="接触职业病危害因素劳动者（人）"/>
                        </f:facet>
                    </p:column>
                    <p:column style="text-align: center;">
                        <f:facet name="header">
                            <h:outputText value="实际接受职业健康检查劳动者（人）"/>
                        </f:facet>
                    </p:column>
                    <p:column style="text-align: center;">
                        <f:facet name="header">
                            <h:outputText value="检出的疑似职业病（人）"/>
                        </f:facet>
                    </p:column>
                    <p:column style="text-align: center;">
                        <f:facet name="header">
                            <h:outputText value="职业禁忌证（人）"/>
                        </f:facet>
                    </p:column>
                </p:row>
            </p:columnGroup>
            <p:column style="padding-left: 5px;">
                <h:outputText value="#{itm[0]}"/>
            </p:column>
            <p:column style="padding-left: 5px;height: 26px;">
                <h:outputText value="#{itm[1]}"/>
            </p:column>
            <p:column style="text-align: center;">
                <h:outputText value="#{itm[2]}"/>
            </p:column>
            <p:column style="text-align: center;">
                <h:outputText value="#{itm[3]}"/>
            </p:column>
            <p:column style="text-align: center;">
                <h:outputText value="#{itm[4]}"/>
            </p:column>
            <p:column style="text-align: center;">
                <h:outputText value="#{itm[5]}"/>
            </p:column>
        </p:dataTable>
    </ui:define>
</ui:composition>