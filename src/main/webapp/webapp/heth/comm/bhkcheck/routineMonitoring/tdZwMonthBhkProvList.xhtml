<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwMonthBhkProvListBean"-->
    <ui:param name="mgrbean" value="#{tdZwMonthBhkProvListBean}"/>
    <ui:param name="editPage" value="/webapp/heth/comm/bhkcheck/routineMonitoring/tdZwMonthBhkProvEdit.xhtml"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/bhkcheck/routineMonitoring/tdZwMonthBhkProvView.xhtml"/>
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/jquery.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <ui:param name="onfocus" value="false"/>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }

            .button-inline {
                display: inline-block;
                float: right;
                margin-top: 5px;
                margin-bottom: 5px;
                margin-right: 5px;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            const zwxJQ = $.noConflict(true);

            function getDownloadFileClick() {
                document.getElementById("tabView:editForm:downloadFileBtn").click();
            }

            // ]]>
        </script>

    </ui:define>
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="职业健康检查常规监测月度汇总"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex">
            <h:panelGrid columns="3" style="border-color: transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 process="@this,dataTable,mainGrid" update=":tabView"
                                 action="#{mgrbean.addBeforAction}">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="地区："/>
            </p:column>
            <p:column styleClass="cs-scv-w" style="padding-left: 3px !important;">
                <zwx:ZoneSingleNewComp id="searchZone"
                                       zoneList="#{mgrbean.zoneList}"
                                       zoneCode="#{mgrbean.searchZoneCode}" zoneName="#{mgrbean.searchZoneName}"
                                       onchange="onZoneSelect();" ifShowTrash="true"/>
                <p:remoteCommand name="onZoneSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="@this,unitName"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="职业健康检查机构："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                            process="@this"
                                            resetValues="true" update="unitName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="报告出具日期："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.startBhkDate}"
                                              endDate="#{mgrbean.endBhkDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="状态："/>
            </p:column>
            <p:column styleClass="cs-scv" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchState}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="报告出具日期" styleClass="cs-break-word" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[1]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
            <h:outputText value=" ~ "/>
            <h:outputText value="#{itm[2]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
        </p:column>
        <p:column headerText="职业健康检查机构数" styleClass="cs-break-word"
                  style="width: 240px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="职业健康检查机构已提交数" styleClass="cs-break-word"
                  style="width: 260px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="填报日期" styleClass="cs-break-word" style="width: 160px;text-align: center;">
            <h:outputText value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"/>
            </h:outputText>
        </p:column>
        <p:column headerText="状态" styleClass="cs-break-word" style="width: 100px;text-align: center;">
            <h:outputText value="待提交" rendered="#{itm[6] eq 0}"/>
            <h:outputText value="已提交" rendered="#{itm[6] eq 1}"/>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink action="#{mgrbean.modInitAction}" value="修改"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           rendered="#{itm[6] eq 0 }">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.startDate}" value="#{itm[1]}"/>
                <f:setPropertyActionListener target="#{mgrbean.endDate}" value="#{itm[2]}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[6] eq 0 }"/>
            <p:commandLink action="#{mgrbean.deleteBeforeAction}" value="删除"
                           process="@this,:tabView:mainForm:mainGrid" rendered="#{itm[6] eq 0}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink action="#{mgrbean.viewInitAction}" value="详情"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView" rendered="#{itm[6] eq 1}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.startDate}" value="#{itm[1]}"/>
                <f:setPropertyActionListener target="#{mgrbean.endDate}" value="#{itm[2]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteDialog">
            <p:commandButton value="确定" action="#{mgrbean.deleteAction}" icon="ui-icon-check"
                             update=":tabView"
                             oncomplete="PF('DeleteDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('DeleteDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
        <p:dialog id="addDialog" header="添加报告出具周期" widgetVar="AddDialog" resizable="false" width="500"
                  height="95" modal="true">
            <p:panelGrid style="width:100%;" id="addGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="报告出具周期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu value="#{mgrbean.monthBhkProv.selYear}" style="width: 100px;">
                            <f:selectItems value="#{mgrbean.selYearList}"/>
                            <p:ajax event="change" process="@this,addGrid" update="addGrid"
                                    listener="#{mgrbean.onAddDialogYearChange}"/>
                        </p:selectOneMenu>
                        <p:spacer width="5"/>
                        <p:selectOneMenu value="#{mgrbean.monthBhkProv.selMonth}" style="width: 100px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.selMonthList}"/>
                            <p:ajax event="change" process="@this,addGrid" update="addGrid"
                                    listener="#{mgrbean.onAddDialogMonthChange}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 40px;">
                        <h:outputText value="报告出具日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:outputPanel rendered="#{not empty mgrbean.monthBhkProv.startBhkDate}">
                            <h:outputText value="#{mgrbean.monthBhkProv.startBhkDate}">
                                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                            </h:outputText>
                            <h:outputText value=" ~ "/>
                            <h:outputText value="#{mgrbean.monthBhkProv.endBhkDate}">
                                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                            </h:outputText>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" icon="ui-icon-check"
                                         process="@this,addGrid"
                                         action="#{mgrbean.addInit}"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('AddDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>