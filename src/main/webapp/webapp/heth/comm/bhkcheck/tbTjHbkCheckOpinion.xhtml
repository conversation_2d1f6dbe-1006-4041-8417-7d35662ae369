<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">

    <p:panelGrid  style="margin-bottom: 10px;margin-top: 5px;width: 100%" rendered="#{tdTjBHkCheckOrgBean.tdTjBhk.state==7}">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;font-size: 20px;text-align:left;" colspan="4" >
                    <p:outputLabel value="国家退回原因"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row >
            <p:column style="height:30px;padding-left: 3px;border: 0px; text-indent:50px;" >
                <h:outputText value="#{tdTjBHkCheckOrgBean.tdTjBhk.errMsg}" style="line-height: 30px;"/>
            </p:column>
        </p:row>
    </p:panelGrid>

    <p:panelGrid  style="margin-bottom: 10px;margin-top: 5px;width: 100%" rendered="#{tdTjBHkCheckOrgBean.ifReturnPart}">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;font-size: 20px;text-align:left;" colspan="4" >
                    <p:outputLabel value="退回意见"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column  style="height:30px;width: 180px;text-align:right;">
                <p:outputLabel value="退回意见："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel  value="#{tdTjBHkCheckOrgBean.auditAdv}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column  style="height:30px;width: 180px;text-align:right;">
                <p:outputLabel value="审核机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 210px;">
                <p:outputLabel value="#{tdTjBHkCheckOrgBean.unitName}"/>
            </p:column>
            <p:column  style="width: 180px;text-align:right;">
                <p:outputLabel value="审核日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:outputLabel value="#{tdTjBHkCheckOrgBean.smtDate}"/>
            </p:column>
        </p:row>
    </p:panelGrid>

    <p:panelGrid  style="margin-bottom: 10px;margin-top: 5px;width: 100%" rendered="#{tdTjBHkCheckOrgBean.tdTjBhk.orgStateDesc!=null}">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;font-size: 20px;text-align:left;" colspan="4" >
                    <p:outputLabel value="情况说明"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column  style="height:30px;width: 180px;text-align:right;">
                <p:outputLabel value="情况说明："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel  value="#{tdTjBHkCheckOrgBean.tdTjBhk.orgStateDesc}"/>
            </p:column>
        </p:row>
    </p:panelGrid>



</ui:composition>

