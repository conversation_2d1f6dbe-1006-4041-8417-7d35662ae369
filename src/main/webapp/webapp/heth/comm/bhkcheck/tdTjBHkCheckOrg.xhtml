<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdTjBHkCheckOrgBean"-->
    <ui:param name="mgrbean" value="#{tdTjBHkCheckOrgBean}"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/bhkcheck/tdTjBHkCheckOrgView.xhtml" />
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            //<![CDATA[
            function getDownloadFileClick(){
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }
            //]]>
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业健康检查审核情况查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:spacer width="5"/>
                <p:commandButton value="情况说明" icon="ui-icon-pencil" action="#{mgrbean.preInfoTipDiagShow}"
                                 process="@this,dataTable" oncomplete="datatableOffClick()"/>
                <p:spacer width="5"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportReturnDataBtn"
                                 action="#{mgrbean.exportReturnDataAction}" process="@this,mainGrid"/>
                <p:spacer width="5"/>
                <p:commandButton value="导出文件下载" icon="ui-icon-arrowthickstop-1-s" action="#{mgrbean.fileDownloadAction}"
                                 process="@this" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:183px;height:38px;">
                <h:outputText value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 8px;width:300px;">
                <zwx:ZoneSingleNewComp id="searchEntrustCrptZone"
                                       zoneList="#{mgrbean.entrustCrptZoneList}"
                                       zoneCode="#{mgrbean.searchEntrustCrptZoneCode}"
                                       zoneName="#{mgrbean.searchEntrustCrptZoneName}"
                                       zonePaddingLeft="0"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:183px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 300px;" >
                <p:inputText value="#{mgrbean.searchEntrustCrptName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:183px;">
                <h:outputText value="用工单位社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText value="#{mgrbean.searchEntrustCreditCode}" style="width: 180px;" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="用人单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 8px;">
                <zwx:ZoneSingleNewComp id="searchZone"
                                       zoneList="#{mgrbean.zoneList}"
                                       zoneCode="#{mgrbean.searchZoneCode}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       ifShowTrash="true" zonePaddingLeft="0"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="用人单位社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width: 180px;" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="体检编号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText value="#{mgrbean.searchBhkCode}" style="width: 180px;" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="25" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText value="#{mgrbean.searchPersonName}" style="width: 180px;" maxlength="25"/>
            </p:column>

        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="体检日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBhkBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true"  size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="体检日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchBhkEdate}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="在岗状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:2px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.selectOnGuardIds}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="体检危害因素：" />
            </p:column>
            <p:column style="text-align:left;padding-left:2px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnIds}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        inputWidth="180"
                                        ifTree="true"
                                        ifSelectParent = "false"></zwx:SimpleCodeManyComp>
            </p:column>

        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="报告出具日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.startRptPrintDate}"
                                              endDate="#{mgrbean.endRptPrintDate}"  styleClass="myCalendar1"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="监测类别：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
                <p:selectManyCheckbox value="#{mgrbean.jcTypes}">
                    <f:selectItem itemValue="1" itemLabel="常规监测"></f:selectItem>
                    <f:selectItem itemValue="2" itemLabel="主动监测"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="是否复检：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
                <p:selectManyCheckbox value="#{mgrbean.ifRhks}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="是否异常：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
                <p:selectManyCheckbox value="#{mgrbean.ifAbnormals}">
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="异常信息：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:inputText value="#{mgrbean.searchAbnomalInfo}" style="width: 780px;" maxlength="1000" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="退回原因：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
                <p:inputText value="#{mgrbean.searchBackUpRsn}" style="width: 780px;" maxlength="500" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{itm[6] ne 1 or (itm[6] == 1 and (null == itm[11] or (0 ne itm[11] and 2 ne itm[11] and 4 ne itm[11])))}"/>
        <p:column headerText="用工单位地区" style="width: 150px;padding-left: 8px;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="用工单位名称" style="width: 240px;padding-left: 8px;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="人员姓名" style="width:80px;text-align: center;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="证件号码" style="width:100px;text-align: center;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="在岗状态" style="text-align:center;width:80px;padding-left: 8px;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="监测类别" style="text-align:center;width:80px;padding-left: 8px;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="常规监测" rendered="#{itm[6]==1}"/>
            <h:outputText value="主动监测" rendered="#{itm[6]==2}"/>
        </p:column>
        <p:column headerText="是否复检" style="text-align:center;width:80px;padding-left: 8px;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="是" rendered="#{itm[7]==1}"/>
            <h:outputText value="否" rendered="#{itm[7]==0}"/>
        </p:column>

        <p:column headerText="体检日期" style="width:80px;text-align: center;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputLabel value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="报告出具日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[20]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="是否异常" style="text-align:center;width:80px;padding-left: 8px;color:#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1?'red':''}">
            <h:outputText value="是" rendered="#{itm[8]==1}"/>
            <h:outputText value="否" rendered="#{itm[8]==0}"/>
        </p:column>
        <p:column headerText="异常信息" style="width:200px;text-align: center;" >
            <h:outputLabel id="abnomalInfo" rendered="#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[11]!=7}"
                           onmouseover="this.style.cssText='cursor: pointer;text-align: left;color:orange;text-decoration:underline;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           onmouseout="this.style.cssText='cursor: pointer;text-align: left;color:red;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           value="#{itm[18]}"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:red;" />
            <p:tooltip for="abnomalInfo" style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[11]!=7}">
                <p:outputLabel value="#{itm[19]}" escape="false"/>
            </p:tooltip>
            <h:outputLabel id="abnomalInfo1" rendered="#{(itm[11]!=0 and itm[11]!=2 and itm[11]!=4) or itm[11]==7}"
                           onmouseover="this.style.cssText='cursor: pointer;text-align: left;color:orange;text-decoration:underline;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           onmouseout="this.style.cssText='cursor: pointer;text-align: left;color:#25AAE1;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           value="#{itm[18]}" styleClass="zwx-tooltip"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:#25AAE1;" />
            <p:tooltip for="abnomalInfo1" style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{(itm[11]!=0 and itm[11]!=2 and itm[11]!=4) or itm[11]==7}" >
                <p:outputLabel value="#{itm[19]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <p:outputPanel>
                <h:outputLabel value="初审退回" rendered="#{itm[11]==0}" style="color:red;"/>
                <h:outputLabel value="待初审" rendered="#{itm[11]==1 or (mgrbean.checkLevel==3 and itm[11]==null) or (mgrbean.checkLevel==2 and (itm[14]==0 or itm[14]==null) and itm[11]==null)}"/>
                <h:outputLabel value="复审退回" rendered="#{itm[11]==2}" style="color:red;"/>
                <h:outputLabel value="待复审" rendered="#{itm[11]==3}"/>
                <h:outputLabel value="终审退回" rendered="#{itm[11]==4}" style="color:red;"/>
                <h:outputLabel value="待终审" rendered="#{itm[11]==5 or (mgrbean.checkLevel==2 and itm[14]==1 and itm[11]==null) }"/>
                <h:outputLabel value="终审通过" rendered="#{itm[11]==6}"/>
                <h:outputLabel value="国家退回" rendered="#{itm[11]==7}" style="color:red;"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="退回原因" style="width:200px;text-align: center;" >
            <h:outputLabel id="backRsn" rendered="#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1 and itm[11]!=7}"
                           onmouseover="this.style.cssText='cursor: pointer;text-align: left;color:orange;text-decoration:underline;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           onmouseout="this.style.cssText='cursor: pointer;text-align: left;color:red;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           value="#{itm[16]}"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:red"></h:outputLabel>
            <h:outputLabel id="backRsn1" rendered="#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]!=1 and itm[11]!=7 }"
                           onmouseover="this.style.cssText='cursor: pointer;text-align: left;color:orange;text-decoration:underline;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           onmouseout="this.style.cssText='cursor: pointer;text-align: left;color:#25AAE1;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           value="#{itm[16]}" styleClass="zwx-tooltip"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:#25AAE1"></h:outputLabel>
            <h:outputLabel id="backRsn2" rendered="#{itm[11]==7}"
                           onmouseover="this.style.cssText='cursor: pointer;text-align: left;color:orange;text-decoration:underline;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           onmouseout="this.style.cssText='cursor: pointer;text-align: left;color:#25AAE1;word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;'"
                           value="#{itm[17]}" styleClass="zwx-tooltip"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:#25AAE1"></h:outputLabel>
            <p:tooltip for="backRsn2" value="#{itm[17]}" style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{itm[11]==7}"/>
            <p:tooltip for="backRsn1" value="#{itm[16]}" style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]!=1 }" />
            <p:tooltip for="backRsn" value="#{itm[16]}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" rendered="#{(itm[11]==0 or itm[11]==2 or itm[11]==4) and itm[8]==1}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="详情"  process="@this" update=":tabView"
                           action="#{mgrbean.viewInitAction}" onclick="hideTooltips();">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog id="infoTipDialog" widgetVar="InfoTipDialog" width="800"
                  height="300" header="情况说明" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:panelGrid style="width: 100%;height: calc(100% - 20px);">
                <p:row style="border:1px solid;">
                    <p:column style="width: 100px;text-align: right;">
                        <h:outputText  value="*" style="color: red;"/>
                        <h:outputText value="情况说明：" />
                    </p:column>
                    <p:column style=" text-align: center;width: 400">
                        <p:inputTextarea value="#{mgrbean.infoTipMsg}"
                                         style="resize:none;width:97%;height:95%;" autoResize="false"
                                         id="infoTipContent" maxlength="200" />
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" icon="ui-icon-check"
                                         process="@this,infoTipContent"
                                         action="#{mgrbean.sureCommitInfoTipMsg}"  />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" onclick="PF('InfoTipDialog').hide();" icon="ui-icon-close"
                                         process="@this" immediate="true" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>

</ui:composition>