<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />

    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdTjBHkCheckListBean"-->

    <!--异常情况-->
    <p:panelGrid  style="margin-top: 10px;width:100%" >
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="异常情况"/>
                </p:column>
            </p:row>
        </f:facet>

        <p:row rendered="#{mgrbean.unAbnormalsList.size() == 0}">
            <p:column style="text-align:left;height: 30px;padding: 0 0 0 53px;">
                <p:outputLabel value="无"/>
            </p:column>
        </p:row>

        <p:row rendered="#{mgrbean.unAbnormalsList.size() > 0}">
            <p:column style="text-align:left;height: 30px;padding-left: 53px;" colspan="3">
                <p:outputLabel value="提示：" style="color: red"/>
            </p:column>
        </p:row>

        <c:forEach items="#{mgrbean.unAbnormalsList}" varStatus="unAbnormal"  var="itm"  >
            <p:row>
                <p:column style="text-align:left;height: 30px; padding-left: 83px;border: 0px;width: 20px;vertical-align: top">
                    <h:outputText style="line-height: 30px;" value="#{unAbnormal.index+1}、" />
                </p:column>
                <p:column style="text-align:left;height: 30px;border: 0px;">
                    <h:outputText value="#{itm}" style="line-height: 30px;"/>
                </p:column>
                <p:column style="text-align:left;height: 30px;border-left: 0px; width: 30%">
                </p:column>
            </p:row>
        </c:forEach>

    </p:panelGrid>
</ui:composition>
