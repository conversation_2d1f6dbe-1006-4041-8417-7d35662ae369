<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/viewTemplate.xhtml">
	<ui:param name="pageParam" value=""></ui:param>
	<ui:param name="ifNotBaseOnly" value="false"></ui:param>
	<ui:param name="birthIfShow" value="false"></ui:param>
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="重点危害因素检查情况审核-#{tdZwZdBadRsnInspectCheckListBean.zoneType==2?'终审':(tdZwZdBadRsnInspectCheckListBean.zoneType==3?'复审':'初审')}"/>
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
		        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:outputPanel rendered="#{mgrbean.ifCheck}">
						<p:commandButton value="暂存" icon="ui-icon-disk"
										 action="#{mgrbean.saveAction}" rendered="#{mgrbean.ifCheck}"
										 process="@this,:tabView:viewForm" oncomplete="datatableOffClick()"
										 update=":tabView">
						</p:commandButton>
						<p:spacer width="5"/>
						<p:commandButton value="提交" icon="ui-icon-check"
							action="#{mgrbean.submitAction}" rendered="#{mgrbean.ifCheck}"
							process="@this,:tabView:viewForm" oncomplete="datatableOffClick()"
							update=":tabView">
							<p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
						</p:commandButton>
						<p:spacer width="3" rendered="#{mgrbean.ifCheck}"/>
					</p:outputPanel>
					<p:commandButton value="撤销" action="#{mgrbean.revokeAction}" icon="ui-icon-cancel" process="@this,:tabView"  update=":tabView"
									 rendered="#{!mgrbean.ifCheck and ((mgrbean.zoneType==4 and (mgrbean.newFlow.state ==3 or mgrbean.newFlow.state ==4) ) or (mgrbean.zoneType==3 and mgrbean.newFlow.state ==5 ) or (mgrbean.zoneType==2 and mgrbean.newFlow.state ==6))  }">
						<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
					</p:commandButton>
					<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
						action="#{mgrbean.backAction}" process="@this" oncomplete="datatableOffClick()"
						update=":tabView" />
					<p:inputText style="visibility: hidden;width: 0"/>

		    </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;" rendered="#{!mgrbean.ifBhkInfoExist}">
                <h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
                <h:outputLabel value="该数据已被体检机构删除，请确认！" style="color:blue;"></h:outputLabel>
            </p:outputPanel>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGrid1">
		   	<f:facet name="header">
		        <p:row>
		            <p:column colspan="6" style="text-align:left;height: 20px;">
		                <p:outputLabel value="判定结论"/>
		            </p:column>
		        </p:row>
		    </f:facet>
			<p:row rendered="#{(mgrbean.zoneType == 4 and mgrbean.newFlow.state == 2) or (mgrbean.zoneType == 3 and mgrbean.newFlow.state == 4) }">
				<!-- 地区级别不是2 -->
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="退回原因：" style="color: red;" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<!-- 非市直属，且地区级别为4，为 countAuditAdv;否则cityAuditAdv-->
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;color: red;" rendered="#{mgrbean.zoneType == 4 and mgrbean.newFlow.state == 2}"
									 value="#{mgrbean.newFlow.cityAuditAdv}"
									 maxlength="100"
									 />
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;color: red;" rendered="#{mgrbean.zoneType == 3 and mgrbean.newFlow.state == 4}"
									 value="#{mgrbean.newFlow.proAuditAdv}"
									 maxlength="100"
					/>
				</p:column>
			</p:row>
			<p:row  rendered="#{mgrbean.zoneType == 4 and mgrbean.newFlow.state == 2}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="复审机构：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByCiytChkOrgid.unitname}"  style="color: red;"></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="复审日期：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.citySmtDate}"  style="color: red;"></h:outputText>
				</p:column>
			</p:row>
			<p:row  rendered="#{mgrbean.zoneType == 3 and mgrbean.newFlow.state == 4}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="终审机构：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByProChkOrgid.unitname}" style="color: red;"></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="终审日期：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.proSmtDate}"  style="color: red;"></h:outputText>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 30px;width: 200px;">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifCheck and (mgrbean.zoneType == 4 or (mgrbean.zoneType == 3 and mgrbean.ifCityDirect))}"></h:outputText>
					<p:outputLabel value="结论是否正常：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3" >
					<p:selectOneRadio  style="width:100px;"  value="#{mgrbean.newFlow.ifNormal}"   rendered="#{(mgrbean.zoneType == 4 or (mgrbean.zoneType == 3 and mgrbean.ifCityDirect) ) and mgrbean.ifCheck}" >
						<f:selectItem itemLabel="是" itemValue="1" />
						<f:selectItem itemLabel="否" itemValue="0" />
						<p:ajax event="change" process="@this,:tabView:viewForm:planGrid1"  update=":tabView:viewForm:planGrid1" />
					</p:selectOneRadio>
					<p:outputLabel value="#{mgrbean.newFlow.ifNormal==1?'是':'否'}" rendered="#{(mgrbean.zoneType != 4 and !(mgrbean.zoneType == 3 and mgrbean.ifCityDirect) ) or !mgrbean.ifCheck}"></p:outputLabel>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.zoneType == 2 or (mgrbean.zoneType == 3 and !mgrbean.ifCityDirect)}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="初审意见：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;"
									 maxlength="100" value="#{mgrbean.newFlow.countyAuditAdv}"
					/>
				</p:column>
			</p:row>

			<p:row rendered="#{mgrbean.zoneType == 2 or (mgrbean.zoneType == 3 and !mgrbean.ifCityDirect)}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="初审机构：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByCountyChkOrgid.unitname}"  ></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="初审日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.countySmtDate}"  ></h:outputText>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.zoneType == 2 and !mgrbean.ifCityDirect}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="复审意见：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;"
									 maxlength="100" value="#{mgrbean.newFlow.cityAuditAdv}"
					/>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.zoneType == 2  and !mgrbean.ifCityDirect}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<p:outputLabel value="复审机构：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 240px;">
					<h:outputText value="#{mgrbean.newFlow.fkByCiytChkOrgid.unitname}" ></h:outputText>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="复审日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.newFlow.citySmtDate}" ></h:outputText>
				</p:column>
			</p:row>

			<p:row rendered="#{mgrbean.zoneType == 3 and !mgrbean.ifCityDirect}">
				<p:column style="text-align:right;padding-right:3px;height: 30px">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifCheck}"></h:outputText>
					<p:outputLabel value="审核结果：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:selectOneRadio  style="width:150px;#{mgrbean.ifCheck?'':'pointer-events:none'}"   value="#{mgrbean.newFlow.cityRst}"  >
						<f:selectItem itemLabel="通过" itemValue="1" />
						<f:selectItem itemLabel="退回" itemValue="2" />
						<p:ajax event="change" process="@this,:tabView:viewForm:planGrid1" listener="#{mgrbean.changeSubmitRst}" update=":tabView:viewForm:planGrid1" />
					</p:selectOneRadio>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.zoneType == 2}">
				<p:column style="text-align:right;padding-right:3px;height: 30px">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifCheck}"></h:outputText>
					<p:outputLabel value="审核结果：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:selectOneRadio  style="width:150px;#{mgrbean.ifCheck?'':'pointer-events:none'}"   value="#{mgrbean.newFlow.cityRst2}"  >
						<f:selectItem itemLabel="通过" itemValue="1" />
						<f:selectItem itemLabel="退回" itemValue="2" />
						<p:ajax event="change" process="@this,:tabView:viewForm:planGrid1" listener="#{mgrbean.changeSubmitRst}" update=":tabView:viewForm:planGrid1" />
					</p:selectOneRadio>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 30px;width: 200px;">
					<h:outputText  value="*" style="color:red;" rendered="#{mgrbean.ifCheck and mgrbean.zoneType == 3 and !mgrbean.ifCityDirect and  mgrbean.newFlow.cityRst==2}"></h:outputText>
					<h:outputText  value="*" style="color:red;" rendered="#{mgrbean.ifCheck and mgrbean.zoneType == 2 and mgrbean.newFlow.cityRst2==2}"></h:outputText>
					<h:outputText  value="*" style="color:red;" rendered="#{mgrbean.ifCheck and (mgrbean.zoneType == 4 or (mgrbean.ifCityDirect and mgrbean.zoneType == 3)) and mgrbean.newFlow.ifNormal==1}"></h:outputText>
					<p:outputLabel value="审核意见：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:inputTextarea rows="5" autoResize="false"  rendered="#{mgrbean.zoneType == 4 or (mgrbean.ifCityDirect and mgrbean.zoneType == 3)}"
									 style="resize: none;width: 594px;" readonly="#{!mgrbean.ifCheck}"
									 maxlength="100" value="#{mgrbean.newFlow.countyAuditAdv}"
					/>
					<p:inputTextarea rows="5" autoResize="false"  rendered="#{mgrbean.zoneType == 3 and !mgrbean.ifCityDirect}"
									 style="resize: none;width: 594px;" readonly="#{!mgrbean.ifCheck}"
									 maxlength="100" value="#{mgrbean.newFlow.cityAuditAdv}"
					/>
					<p:inputTextarea rows="5" autoResize="false"  rendered="#{mgrbean.zoneType == 2}"
									 style="resize: none;width: 594px;" readonly="#{!mgrbean.ifCheck}"
									 maxlength="100" value="#{mgrbean.newFlow.proAuditAdv}"
					/>
				</p:column>
			</p:row>
	    </p:panelGrid>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6" style="text-align:left;height: 20px;">
						<p:outputLabel value="判定情况"/>
					</p:column>
				</p:row>
			</f:facet>
			<p:row rendered="#{null !=mgrbean.zdzybBadRsnInfoList and mgrbean.zdzybBadRsnInfoList.size()>0}">
				<p:column style="text-align:right;padding-right:3px;height: 30px;width:200px;">
					<p:outputLabel value="在岗状态：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:outputLabel value="#{mgrbean.tdZwyjBsnBhk.fkByOnguardStateid.codeName}"/>
				</p:column>
			</p:row>
			<c:forEach items="#{mgrbean.zdzybBadRsnInfoList}" var="data" varStatus="rowIndex">
				<p:row>
					<p:column  style="text-align: right;height: 30px;">
						<h:outputText value="危害因素："></h:outputText>
					</p:column>
					<p:column  style="text-align: left;width:300px;">
						<h:outputText value="#{data.badName}"></h:outputText>
					</p:column>
					<p:column  style="text-align: right;width:200px;">
						<h:outputText value="实际结论："></h:outputText>
					</p:column>
					<p:column  style="text-align: left;">
						<h:outputText value="#{data.finalConslusion}"></h:outputText>
					</p:column>
				</p:row>
                <p:row>
                    <p:column  style="text-align: center;height: 30px;text-align: right;">
                        <h:outputText value="异常项目："></h:outputText>
                    </p:column>
                    <p:column colspan="3">
                        <p:outputPanel styleClass="div-layout" >
                            <p:panelGrid  style="width:100%;height:100%;">
                                <p:row>
                                    <p:column styleClass="ui-state-default" style="text-align: center;height: 30px;width: 242px">
                                        <h:outputText value="项目名称"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 290px">
                                        <h:outputText value="体检结果"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 116px">
                                        <h:outputText value="参考值"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 100px">
                                        <h:outputText value="计量单位"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 200px">
                                        <h:outputText value="判定标准"></h:outputText>
                                    </p:column>
                                    <p:column styleClass="ui-state-default" style="text-align: center;width: 400px">
                                        <h:outputText value="建议结论"></h:outputText>
                                    </p:column>
                                </p:row>
                                <c:forEach items="#{data.zdzybItemRstList}" var="item" varStatus="itemIndex">
                                    <p:row>
                                        <p:column style="text-align: center;height:25px;">
                                            <h:outputText value="#{item.itemName}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.result}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.stdValue}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.msrunt}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;">
                                            <h:outputText value="#{item.pdStd}"></h:outputText>
                                        </p:column>
                                        <p:column style="text-align: center;" rendered="#{item.index != 0}" rowspan="#{item.index}">
                                            <h:outputText value="#{item.conslusion}"></h:outputText>
                                        </p:column>
                                    </p:row>
                                </c:forEach>
                            </p:panelGrid>
                        </p:outputPanel>
                    </p:column>
                </p:row>
			</c:forEach>
			<p:row rendered="#{mgrbean.ifAbnormal}">
				<p:column style="text-align:right;padding-right:3px;height: 30px;width:200px;">
					<p:outputLabel value="其他情况：" rendered="#{null !=mgrbean.zdzybBadRsnInfoList and mgrbean.zdzybBadRsnInfoList.size()>0}"/>
					<p:outputLabel value="判定信息：" rendered="#{null ==mgrbean.zdzybBadRsnInfoList or mgrbean.zdzybBadRsnInfoList.size()==0}"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3">
					<p:outputLabel value="#{mgrbean.abnormalInfo}"/>
				</p:column>
			</p:row>
		</p:panelGrid>
	    <ui:include src="tbTjBhkInfo.xhtml">
			<ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
		</ui:include>
	</ui:define>
</ui:composition>