<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html" 
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
<h:head>
</h:head>

<h:body>
<h:outputStylesheet name="css/default.css"/>
<h:outputStylesheet name="css/ui-tabs.css"/>
<p:tabView id="tabView" dynamic="true" cache="true"  style="border:1px; padding:0px;margin-left:10px;margin-right:10px;">
	 	 <p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<ui:param name="mgrbean" value="#{tbTjBhkShowBean}"/>
				<ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
				<ui:param name="pageParam" value="1"/>
				<ui:include src="/webapp/heth/comm/tbTjBhkInfo.xhtml"/>
		</p:tab>
</p:tabView>			
		
</h:body>
</f:view>	
</html>


