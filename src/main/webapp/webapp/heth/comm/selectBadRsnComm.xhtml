<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>危害因素选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>

    <h:body style="overflow-y:hidden;"  onload="document.getElementById('codeForm:pym').focus();">
        <h:form id="codeForm">
        	<p:outputPanel styleClass="zwx_toobar_42" rendered="#{!selectedBadRsnCommBean.ifSelf}">
	            <h:panelGrid columns="3">
	                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
	                <p:commandButton value="确定" icon="ui-icon-check" id="submitBtn" action="#{selectedBadRsnCommBean.submitAction}" />
	                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectedBadRsnCommBean.dialogClose}"/>
	            </h:panelGrid>
        	</p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                        	<p:outputLabel value="#{selectedBadRsnCommBean.titleName}大类：" styleClass="zwx_dialog_font" rendered="#{selectedBadRsnCommBean.ifShowFirstCode}"/>
                        	<p:selectOneMenu value="#{selectedBadRsnCommBean.firstCodeNo}" 
                        		rendered="#{selectedBadRsnCommBean.ifShowFirstCode}" id="firstCodeNo">
                        		<f:selectItem itemValue="" itemLabel="--全部--"></f:selectItem>
                        		<f:selectItems value="#{selectedBadRsnCommBean.firstList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"></f:selectItems>
                        		<p:ajax event="change" listener="#{selectedBadRsnCommBean.searchAction}" process="@this,searchPanel" update="selectedIndusTable"></p:ajax>
                        	</p:selectOneMenu>
                            <p:outputLabel value="名称/拼音码：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{selectedBadRsnCommBean.searchNamOrPy}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{selectedBadRsnCommBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{selectedBadRsnCommBean.displayList}" id="selectedIndusTable"
                         paginator="true" rows="10" emptyMessage="没有数据！" pageLinks="5"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorPosition="bottom" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column headerText="选择" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{selectedBadRsnCommBean.selectAction}" process="@this" 
                    update="selectedIndusTable"
                    rendered="#{selectedBadRsnCommBean.ifAllSelect?'true':itm.levelIndex != '0'}">
                        <f:setPropertyActionListener value="#{itm}" target="#{selectedBadRsnCommBean.selectPro}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="名称" style="padding-left: 3px;">
                    <h:outputText value="#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
    </h:body>
</f:view>
</html>
