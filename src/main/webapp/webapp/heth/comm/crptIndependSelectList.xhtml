<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <!-- 标题 -->
    <h:head>
        <title><h:outputText value="#{crptIndependSelectListBean.crpyName}" /></title>
        <h:outputStylesheet name="css/default.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css" />
        <h:outputScript name="js/namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style type="text/css">
            .areaZone .ui-commandlink{
                left: -42px !important;
            }
        </style>
    </h:head>

    <h:body>
        <h:form id="selectForm">
            <div style="float: left;font-size: 12px;width: 100%;">
                <!-- 查询条件 -->
                <table style="width: 100%">
                    <tr>
                        <td style="text-align: right;width: 45px;" class="zwx_dialog_font">
                            <h:outputText value="地区："/>
                        </td>
                        <td style="text-align:left;width: 100px;">
                            <zwx:ZoneSingleNewComp zoneList="#{crptIndependSelectListBean.zoneList}"
                                                   zoneCodeNew="#{crptIndependSelectListBean.searchZoneCode}"
                                                   zoneId="#{crptIndependSelectListBean.searchZoneId}" zonePaddingLeft="0"
                                                   zoneName="#{crptIndependSelectListBean.searchZoneName}" id="searchZone"
                                                   ifShowTrash="false"
                                                   onchange="onSearchNodeSelect()" />
                            <p:remoteCommand name="onSearchNodeSelect" action="#{crptIndependSelectListBean.searchAction}"
                                             process="@this,searchZone" update="crptIndependTable"/>
                        </td>
                        <td >
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" process="@this,searchZone" update="selectForm"
                                           action="#{crptIndependSelectListBean.clearCodeName}"  style="position: relative;left: -24px;"
                            rendered="#{crptIndependSelectListBean.defaultZone == '1'}"></p:commandLink>
                        </td>
                        <td style="text-align: right;width: 65px;" class="zwx_dialog_font">
                            <h:outputText value="单位名称："/>
                        </td>
                        <td style="text-align: left;vertical-align: middle;width: 120px;">
                            <p:inputText size="25" style="width:180px;" value="#{crptIndependSelectListBean.searchCrptName}">
                                <p:ajax event="keyup" update="crptIndependTable" process="@this,@parent" listener="#{crptIndependSelectListBean.searchAction}" />
                            </p:inputText>
                        </td>
                        <td style="text-align: right;" class="zwx_dialog_font">
                            <h:outputText value="社会信用代码："/>
                        </td>
                        <td style="text-align: left;width: 120px;">
                            <p:inputText id="searchCrptCode" value="#{crptIndependSelectListBean.searchCrptCode}" size="20" style="width:150px;">
                                <p:ajax event="keyup" update="crptIndependTable" process="@this" listener="#{crptIndependSelectListBean.searchAction}" />
                            </p:inputText>
                        </td>
                        <td style="text-align: left;padding-left: 8px;width: 60px;">
                            <p:commandButton process="@this" value="添加"
                                action="#{crptIndependSelectListBean.addCrptIndependAction}"
                                resetValues=":selectForm:addCrptIndependPanel"
                                update=":selectForm:addCrptIndependPanel"/>
                        </td>
                        <td style="text-align: left;padding-left: 6px;">
                            <p:commandButton value="关闭" action="#{crptIndependSelectListBean.dialogClose}" process="@this"/>
                        </td>
                    </tr>
                </table>

                <!-- 表格 -->
                <p:dataTable id="crptIndependTable" paginatorPosition="bottom"
                             value="#{crptIndependSelectListBean.crptIndependList}"
                             widgetVar="crptIndependTable" var="crptIndepend" paginator="true" rows="10"
                             emptyMessage="没有数据！" lazy="true" rowKey="#{crptIndependId}" rowIndexVar="R">
                    <p:column headerText="选择" style="width:80px;text-align:center;">
                        <p:commandLink value="选择" process="@this" action="#{crptIndependSelectListBean.selectCrptIndependAction}">
                            <f:setPropertyActionListener target="#{crptIndependSelectListBean.selectCrptIndepend}" value="#{crptIndepend}"/>
                        </p:commandLink>
                        <p:spacer width="5" />
                        <p:commandLink process="@this" value="修改"
                                       action="#{crptIndependSelectListBean.modCrptIndependAction}"
                                       resetValues=":selectForm:addCrptIndependPanel"
                                       update=":selectForm:addCrptIndependPanel"
                                       oncomplete="PF('addCrptIndepend').show();">
                            <f:setPropertyActionListener target="#{crptIndependSelectListBean.crptIndependId}" value="#{crptIndepend.rid}"/>
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="地区名称" width="80px;">
                        <h:outputText value="#{crptIndepend.fkByCrptId.tsZoneByZoneId.zoneName}"/>
                    </p:column>
                    <p:column headerText="单位名称">
                        <h:outputText value="#{crptIndepend.fkByCrptId.crptName}"/>
                    </p:column>
                    <p:column headerText="社会信用代码" width="100px;">
                        <h:outputText value="#{crptIndepend.fkByCrptId.institutionCode}"/>
                    </p:column>
                    <p:column headerText="单位地址" width="200px;">
                        <h:outputText value="#{crptIndepend.fkByCrptId.address}"/>
                    </p:column>
                    <p:column headerText="联系人" width="80px;">
                        <h:outputText value="#{crptIndepend.linkman2}"/>
                    </p:column>
                    <p:column headerText="联系电话" width="100px;">
                        <h:outputText value="#{crptIndepend.linkphone2}"/>
                    </p:column>
                </p:dataTable>

                <!-- 添加企业信息—各单位独立信息弹出框 -->
                <p:dialog header="添加企业" widgetVar="addCrptIndepend" width="650" height="330" modal="true" resizable="false" >
                    <p:panelGrid style="width:100%;margin-bottom:5px;" id="addCrptIndependPanel">
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <p:outputPanel id="test">
                                    <h:outputText value="*" style="color:red;"/>
                                    <h:outputText value="社会信用代码："/>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="padding-left: 13px;height:30px;">
                                <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                                    <p:inputText id="institutionCode" maxlength="25" value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.institutionCode}" style="width:179px"/>
                                    <p:spacer width="5" />
                                    <p:commandButton value="检索" action="#{crptIndependSelectListBean.searchCrptByInstitutionCodeAction}"
                                                     process="@this,institutionCode" resetValues="true"
                                                     update=":selectForm:addCrptIndependPanel"/>
                                </p:outputPanel>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height: 30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="所属地区："/>
                            </p:column>
                            <p:column style="padding:0;" id="areaZone" styleClass="areaZone">
                                <zwx:ZoneSingleComp zoneList="#{crptIndependSelectListBean.zoneList}"
                                                    zoneId="#{crptIndependSelectListBean.editZoneId}"
                                                    zoneName="#{crptIndependSelectListBean.editZoneName}"
                                                    zoneType="#{crptIndependSelectListBean.editZoneType}" panelHeight="320" height="300"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="企业名称："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText maxlength="50" value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.crptName}" style="width:260px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="单位地址："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.address}" maxlength="100" style="width:260px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="行业类别："/>
                            </p:column>
                            <p:column style="padding-left: 9px;" >
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="indusTypeName"
                                                         value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.tsSimpleCodeByIndusTypeId.codeName==null?null:crptIndependSelectListBean.editCrptIndepend.fkByCrptId.tsSimpleCodeByIndusTypeId.codeName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectForm:selIndusTypeLink').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selIndusTypeLink"
                                                           action="#{crptIndependSelectListBean.selCodeTypeAction}" process="@this"
                                                           oncomplete="PF('selIndusTypeDialog').show()"
                                                           update=":selectForm:selectedIndusCodeTable,:selectForm:searchIndusPanel"
                                                           style="position: relative;left: -35px !important;">
                                                <f:setPropertyActionListener target="#{crptIndependSelectListBean.selCodeName}" value="行业类别"/>
                                            </p:commandLink>
                                        </td>
                                        <!-- 清空 -->
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           process="@this,:selectForm:indusTypeName" action="#{crptIndependSelectListBean.delCodeName}">
                                                <f:setPropertyActionListener target="#{crptIndependSelectListBean.selCodeName}" value="行业类别"/>
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="经济类型："/>
                            </p:column>
                            <p:column style="padding-left: 9px;">
                                <table>
                                    <tr>
                                        <td style="padding: 0;border-color: transparent;">
                                            <p:inputText id="economyName"
                                                         value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.tsSimpleCodeByEconomyId.codeName==null?null:crptIndependSelectListBean.editCrptIndepend.fkByCrptId.tsSimpleCodeByEconomyId.codeName}"
                                                         style="width: 260px;cursor: pointer;"
                                                         onclick="document.getElementById('selectForm:selEconomyLink').click();"
                                                         readonly="true"/>
                                        </td>
                                        <td style="border-color: transparent;">
                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                           id="selEconomyLink"
                                                           action="#{crptIndependSelectListBean.selCodeTypeAction}" process="@this"
                                                           oncomplete="PF('selDialog').show()"
                                                           update=":selectForm:selectedIndusTable,:selectForm:searchPanel"
                                                           style="position: relative;left: -35px !important;">
                                                <f:setPropertyActionListener target="#{crptIndependSelectListBean.selCodeName}" value="经济类型"/>
                                            </p:commandLink>
                                        </td>
                                        <td style="border-color: transparent;position: relative;left: -52px;">
                                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                           update=":selectForm:selectedIndusTable"
                                                           process="@this,:selectForm:economyName"
                                                           action="#{crptIndependSelectListBean.delCodeName}">
                                                <f:setPropertyActionListener target="#{crptIndependSelectListBean.selCodeName}" value="经济类型"/>
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="企业规模："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:selectOneMenu value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.tsSimpleCodeByCrptSizeId.rid}"
                                        style="width: 188px;margin-bottom: -5px;">
                                    <f:selectItem itemLabel="--请选择--" itemValue="" />
                                    <f:selectItems value="#{crptIndependSelectListBean.crptsizwList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                </p:selectOneMenu>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="联系人："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.linkman2}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.linkphone2}" maxlength="15" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="法定代表人（负责人）："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.corporateDeputy}" maxlength="25" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="法人联系电话："/>
                            </p:column>
                            <p:column style="padding-left: 13px;">
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.phone}" maxlength="15" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="邮政编码："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.postCode}" maxlength="6" style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{(crptIndependSelectListBean.ifTjlrCrpt ne null and crptIndependSelectListBean.ifTjlrCrpt == '1' and crptIndependSelectListBean.isShowContract!='1' ) or (crptIndependSelectListBean.ifJcCrpt ne null and crptIndependSelectListBean.ifJcCrpt == '1')}">
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="职工人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.workForce}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <!--职业性有害因素监测卡填报 不显示-->
                        <p:row rendered="#{crptIndependSelectListBean.ifTjlrCrpt ne null and crptIndependSelectListBean.ifTjlrCrpt == '1'}">
                            <p:column style="text-align: right;width: 40%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="接触职业病危害因素人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.holdCardMan}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <!--职业性有害因素监测卡填报 显示-->
                        <p:row rendered="#{crptIndependSelectListBean.ifJcCrpt ne null and crptIndependSelectListBean.ifJcCrpt == '1'}">
                            <p:column style="text-align: right;width: 40%;height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <h:outputText value="生产工人数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.workmanNum}"  style="width:179px"
                                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)"
                                             maxlength="9" />
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptIndependSelectListBean.isShowContract=='1'}">
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="在册职工总数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.workForce}"  style="width:179px"/>
                            </p:column>
                        </p:row>
                        <p:row rendered="#{crptIndependSelectListBean.isShowContract=='1'}">
                            <p:column style="text-align: right;width: 30%;height:30px;">
                                <h:outputText value="外委人员数："/>
                            </p:column>
                            <p:column style="padding-left: 13px;" >
                                <p:inputText value="#{crptIndependSelectListBean.editCrptIndepend.fkByCrptId.outsourceNum}"  style="width:179px"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <f:facet name="footer">
                        <h:panelGrid style="width: 100%;text-align: center;">
                            <h:panelGroup>
                                <p:commandButton value="确定"
                                                 process="@this,addCrptIndependPanel"
                                                 action="#{crptIndependSelectListBean.saveCrptIndependAction}"/>
                                <p:commandButton value="取消" type="button" process="@this"
                                                 onclick="PF('addCrptIndepend').hide();"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </f:facet>
                </p:dialog>
            </div>

            <!-- 经济类型/行业类别弹出框 -->
            <p:dialog header="#{crptIndependSelectListBean.selCodeName}选择" widgetVar="selDialog" width="600" height="380" modal="true" resizable="false" >
                <ui:include src="/webapp/heth/comm/codeRadioPannelComm.xhtml">
                    <ui:param name="mgrbean" value="#{crptIndependSelectListBean}"/>
                </ui:include>
            </p:dialog>

            <p:dialog header="行业类别选择" widgetVar="selIndusTypeDialog" width="800" height="430" modal="true" resizable="false" >
                <ui:include src="/webapp/system/indusTypeCodePanel.xhtml">
                    <ui:param name="mgrbean" value="#{crptIndependSelectListBean}"/>
                    <ui:param name="indusbean" value="#{crptIndependSelectListBean.codePanelBean}"/>
                </ui:include>
            </p:dialog>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    </h:body>
</f:view>
</html>
