<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
<ui:define name="insertEditScripts">

    <script language="javascript"  >
        //<![CDATA[
        function displayChart(){
            if(document.getElementById('tabView:editForm:statePD:0').checked){
                document.getElementById('tabView:editForm:diag').style.display='block';
            }else{
                document.getElementById('tabView:editForm:diag').style.display='none';
            }
        }
        function handleUserSave(xhr, status, args) {
            if(args.validationFailed) {
                displayChart();
            }
        }

        //]]>
    </script>
    <style>
    	.ui-selectonemenu-panel .ui-selectonemenu-filter-container .ui-icon {
		    position: absolute;
		    top: 10px;
		    right: 10px;
		    margin-right: 7px;
		} 
		.ui-shadow-22 {
		    box-shadow: 0px 1px 2px #00000059;
		}
    </style>
</ui:define>
<h:outputScript name="js/validate/system/validate.js" />

<!-- 标题栏 -->
<ui:define name="insertEditTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="体检项目维护" />
        </p:column>
    </p:row>
</ui:define>
<!-- 编辑页面的按钮 -->
<ui:define name="insertEditButtons">
    <p:outputPanel styleClass="zwx_toobar_42">
        <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
            <p:commandButton value="保存" icon="ui-icon-disk" id="saveBtn" action="#{tbTjItemsCommBean.saveAction}" process="@this,:tabView:editForm:editGrid,:tabView:editForm:field" update=":tabView"  oncomplete="handleUserSave(xhr, status, args);"/>
            <p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tbTjItemsCommBean.modTbTjRstdescBackAction}" update=":tabView" immediate="true" />
        </h:panelGrid>
    </p:outputPanel>
</ui:define>
<!-- 编辑页面的内容-->
<ui:define name="insertEditContent">
    <p:row >
        <p:column style="text-align:right;padding-right:3px;height:36px">
            <font color="red">*</font>
            <h:outputText value="业务分类："/>
        </p:column>
        <p:column style="text-align:left;padding-left:2px;width: 400px;">
            <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                <p:inputText id="editYeWuName2"
                             value="#{tbTjItemsCommBean.editYeWuName}" readonly="true"
                             style="width: 180px;"  />
                <p:commandLink styleClass="ui-icon ui-icon-search"
                               id="initTreeLink2" process="@this"
                               style="position: relative;left: -40px;top:0px;"
                               oncomplete="PF('overalPanel2').show()" />
                <h:inputHidden id="editYeWuRid2"  rendered="true"
                               requiredMessage="请选择业务分类！"
                               value="#{tbTjItemsCommBean.editYeWuRid}" />
            </h:panelGrid>
            <p:overlayPanel id="overalPanel2" for="editYeWuName2"
                            style="width:280px;" widgetVar="overalPanel2"
                            dynamic="false">
                <p:tree var="node" selectionMode="single" id="choiceTree2"
                        value="#{tbTjItemsCommBean.badRsnTree}"
                        style="width: 250px;height: 300px;overflow-y: auto;">
                    <p:ajax event="select"
                            update=":tabView:editForm:editYeWuName2,:tabView:editForm:editYeWuRid2"
                            listener="#{tbTjItemsCommBean.onAddNodeSelect}" process="@this"
                             />
                    <p:treeNode>
                        <h:outputText value="#{node.codeName}" />
                    </p:treeNode>
                </p:tree>
            </p:overlayPanel>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:220px;height:36px;">
            <font color="red">*</font>
            <h:outputText value="体检项目编码："/>
        </p:column>
        <p:column style="padding-left:8px;">
            <p:inputText  value="#{tbTjItemsCommBean.tbTjItems.itemCode}" onkeyup="SYSTEM.clearNoNum(this)" maxlength="25" style="width:180px;"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:220px;height:36px">
            <font color="red">*</font>
            <h:outputText value="体检项目名称："/>
        </p:column>
        <p:column style="text-align:left;padding-left:8px;">
            <p:inputText  value="#{tbTjItemsCommBean.tbTjItems.itemName}" maxlength="50" style="width:180px;"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:220px;height:36px;">
            <h:outputText value="性别："/>
        </p:column>
        <p:column style="padding-left:8px;">
           <p:selectOneRadio   value="#{tbTjItemsCommBean.tbTjItems.sex}" style="width: 190px;">
                <f:selectItem itemLabel="男" itemValue="1"/>
                <f:selectItem itemLabel="女" itemValue="2"/>
                <f:selectItem itemLabel="通用" itemValue="3"/>
            </p:selectOneRadio>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:220px;height:36px">
            <font color="red">*</font>
            <h:outputText value="判断模式："/>
        </p:column>
        <p:column style="text-align:left;padding-left:8px;">
            <p:selectOneRadio id="statePD"  value="#{tbTjItemsCommBean.tbTjItems.jdgptn}" style="width: 190px;">
                <f:selectItem itemLabel="定性" itemValue="1"/>
                <f:selectItem itemLabel="定量" itemValue="2"/>
                <p:ajax event="change" process="@this,:tabView:editForm" update=":tabView:editForm"></p:ajax>
            </p:selectOneRadio>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:220px;height:36px;">
            <h:outputText value="参考值："/>
        </p:column>
        <p:column style="padding-left:8px;" colspan="3">
            <p:inputText  value="#{tbTjItemsCommBean.tbTjItems.itemStdvalue}" disabled="#{tbTjItemsCommBean.tbTjItems.jdgptn == 2}" maxlength="50" style="width:180px;"    />
        </p:column>
    </p:row>
    <p:row>
   		 <p:column style="text-align:right;padding-right:3px;width:220px;height:36px;">
            <h:outputText value="序号："/>
        </p:column>
        <p:column style="padding-left:8px;" >
            <p:inputText  value="#{tbTjItemsCommBean.tbTjItems.num}"  maxlength="8" style="width:180px;"  onkeyup="SYSTEM.clearNoNum(this)"  />
        </p:column>
    	  <p:column style="text-align:right;padding-right:3px;width:220px;height:36px;">
            <h:outputText value="项目标记："/>
        </p:column>
        <p:column style="padding-left:8px;">
            <!--<p:selectOneMenu value="#{tbTjItemsCommBean.tbTjItems.itemTag}" style="width: 190px;">-->
            <p:selectOneMenu value="#{tbTjItemsCommBean.tbTjItems.itemTagNew}"  style="width: 190px;">
                <f:selectItem itemLabel="--请选择--" itemValue=""/>
                <f:selectItems value="#{tbTjItemsCommBean.tjMarkList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}-#{itm.extendS3}"/>
                <!--<f:selectItem itemLabel="收缩压" itemValue="1"/>
                <f:selectItem itemLabel="舒张压" itemValue="2"/>
                <f:selectItem itemLabel="左耳500Hz" itemValue="3"/>
                <f:selectItem itemLabel="左耳1000Hz" itemValue="4"/>
                <f:selectItem itemLabel="左耳2000Hz" itemValue="5"/>
                <f:selectItem itemLabel="左耳3000Hz" itemValue="6"/>
                <f:selectItem itemLabel="左耳4000Hz" itemValue="7"/>
                <f:selectItem itemLabel="左耳6000Hz" itemValue="8"/>
                <f:selectItem itemLabel="右耳500Hz" itemValue="9"/>
                <f:selectItem itemLabel="右耳1000Hz" itemValue="10"/>
                <f:selectItem itemLabel="右耳2000Hz" itemValue="11"/>
                <f:selectItem itemLabel="右耳3000Hz" itemValue="12"/>
                <f:selectItem itemLabel="右耳4000Hz" itemValue="13"/>
                <f:selectItem itemLabel="右耳6000Hz" itemValue="14"/>
                <f:selectItem itemLabel="右耳听阈加权" itemValue="15"/>
                <f:selectItem itemLabel="左耳听阈加权" itemValue="16"/>
                <f:selectItem itemLabel="左耳语频平均听阈" itemValue="17"/>
                <f:selectItem itemLabel="右耳语频平均听阈" itemValue="18"/>
                <f:selectItem itemLabel="双耳语频平均听阈" itemValue="19"/>
                <f:selectItem itemLabel="双耳高频平均听阈" itemValue="20"/>-->
            </p:selectOneMenu>
        </p:column>
    </p:row>
    <p:row>
    	<p:column style="text-align:right;padding-right:3px;width:220px;height:36px">
            <font color="red">*</font>
            <h:outputText value="状态："/>
        </p:column>
        <p:column style="text-align:left;padding-left:8px;" colspan="3">
            <p:selectOneRadio  value="#{tbTjItemsCommBean.tbTjItems.stopTag}" style="width: 190px;">
                <f:selectItem itemLabel="启用" itemValue="1"/>
                <f:selectItem itemLabel="停用" itemValue="0"/>
            </p:selectOneRadio>
        </p:column>
    </p:row>
</ui:define>
<ui:define name="insertOtherContents" >
<p:fieldset legend="计量单位" toggleable="true" id="diag0" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;" >
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn0" action="#{tbTjItemsCommBean.addItemUnitAction}" style="position: relative;top: 3px;"
                                 update=":tabView:editForm:dig0" oncomplete="PF('dig0').show()" process="@this">
                    <p:resetInput target=":tabView:editForm:dig0"></p:resetInput>
                    <f:setPropertyActionListener target="#{tbTjItemsCommBean.ifAdd}" value="true"/>
                </p:commandButton>
                <p:outputPanel style="margin-left: 10px;margin-top: 6px;">
                    <h:outputLabel value="提示：" style="color:red;" />
                    <h:outputLabel value="计量单位的最大值、最小值不能超出国家接口标准同计量单位的参考值。" style="color:blue;" />
                </p:outputPanel>
            </h:panelGrid>
        </p:outputPanel>
        <h:panelGrid style="width:100%;height:100%;" id="mainGrid0">
            <p:dataTable  var="itm" value="#{tbTjItemsCommBean.showTbTjItemUnitRelList}" emptyMessage="没有您要找的记录！" >
                <p:columnGroup type="header">
                	<p:row>
		                <p:column headerText="操作"  style="width: 150px;" />
		                <p:column headerText="计量单位" style="width: 150px;" />
		                <p:column headerText="最小值" style="width: 120px;" />
		                <p:column headerText="最大值"  style="width: 120px;" />
		                <p:column headerText="是否默认"  style="width: 150px;" />
		            </p:row>
                </p:columnGroup>
                
                <p:column style="padding-left:8px;">
                    <p:commandLink value="修改" action="#{tbTjItemsCommBean.modTbTjItemUnitAction}" oncomplete="PF('dig0').show()" update=":tabView:editForm:dig0" process="@this" resetValues="true" >
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjItemUnitRel}" value="#{itm}"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.ifAdd}" value="false"/>
                    </p:commandLink>
                    <p:spacer width="5"  />
                    <p:commandLink value="删除" action="#{tbTjItemsCommBean.deleteTbTjItemUnitAction}" update=":tabView:editForm:mainGrid0" process="@this">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjItemUnit}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column  style="text-align:center ;">
                    <h:outputText value="#{itm.fkByMsruntId.codeName}"  escape="false"/>
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.minval}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.maxval}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="是"  rendered="#{itm.ifDefaut==1}"  />
                    <p:outputLabel value="否"  rendered="#{itm.ifDefaut==0}"  />
                </p:column>
            </p:dataTable>
        </h:panelGrid>
    </p:fieldset>
<p:outputPanel id="field">
    <p:fieldset legend="特殊标准" toggleable="true" id="diag2" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;" >
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn2" action="#{tbTjItemsCommBean.addSpecialAction}" style="position: relative;top: 3px;"
                                  process="@this">
                    <p:resetInput target=":tabView:editForm:dig2"></p:resetInput>
                    <f:setPropertyActionListener target="#{tbTjItemsCommBean.ifAdd}" value="true"/>
                </p:commandButton>
                <p:outputPanel style="margin-left: 10px;margin-top: 6px;">
                    <h:outputLabel value="提示：" style="color:red;" />
                    <h:outputLabel value="特殊标准的最大值、最小值不能超出国家接口标准同性别同计量单位的参考值。" style="color:blue;" />
                </p:outputPanel>
            </h:panelGrid>
        </p:outputPanel>
        <h:panelGrid style="width:100%;height:100%;" id="mainGrid2">
            <p:dataTable  var="itm" value="#{tbTjItemsCommBean.showItemsSpeList}" emptyMessage="没有您要找的记录！" >
                <p:columnGroup type="header">
                	<p:row>
		                <p:column headerText="操作"  style="width: 150px;" />
		                <p:column headerText="在岗状态" style="width: 150px;" />
		                <p:column headerText="危害因素" style="width: 150px;" />
		                <p:column headerText="性别"  style="width: 80px;" />
		                <p:column headerText="计量单位"  style="width: 150px;" />
		                <p:column headerText="最小值"  style="width: 150px;" />
		                <p:column headerText="最大值"  style="width: 150px;" />
		               
		            </p:row>
                </p:columnGroup>
                
                <p:column style="padding-left:8px;">
                    <p:commandLink value="修改" action="#{tbTjItemsCommBean.modTbTjRstSpeInitAction}" process="@this" resetValues="true" >
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.editTbTjItemsSpe}" value="#{itm}"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.ifAdd}" value="false"/>
                    </p:commandLink>
                    <p:spacer width="5"  />
                    <p:commandLink value="删除" action="#{tbTjItemsCommBean.deleteTbTjSpeAction}" update=":tabView:editForm:mainGrid2" process="@this">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjSpe}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column  style="text-align:center ;">
                    <h:outputText value="#{itm.fkByOnguardStateid.codeName}" />
                </p:column>
                <p:column  style="text-align:center ;">
                    <h:outputText   value="#{itm.fkByBadRsnId.codeName}"  />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.sex}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.msrunt}" escape="false"  />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.minval}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.maxval}"   />
                </p:column>
                
            </p:dataTable>
        </h:panelGrid>
    </p:fieldset>
    <p:fieldset legend="国家接口标准" toggleable="true" id="diag3" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;" >
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn3" action="#{tbTjItemsCommBean.addItemGjAction}" style="position: relative;top: 3px;"
                                 process="@this">
                    <p:resetInput target=":tabView:editForm:dig3"></p:resetInput>
                    <f:setPropertyActionListener target="#{tbTjItemsCommBean.ifAdd}" value="true"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
        <h:panelGrid style="width:100%;height:100%;" id="mainGrid3">
            <p:dataTable  var="itm" value="#{tbTjItemsCommBean.showTbtjItemsGjList}" emptyMessage="没有您要找的记录！" >
                <p:columnGroup type="header">
                    <p:row>
                        <p:column headerText="操作"  style="width: 150px;" />
                        <p:column headerText="类型" style="width: 150px;" />
                        <p:column headerText="性别"  style="width: 80px;" />
                        <p:column headerText="计量单位"  style="width: 150px;" />
                        <p:column headerText="最小值"  style="width: 150px;" />
                        <p:column headerText="最大值"  style="width: 150px;" />
                    </p:row>
                </p:columnGroup>
                <p:column style="padding-left:8px;">
                    <p:commandLink value="修改" action="#{tbTjItemsCommBean.modTbTjItemsGjAction}" process="@this" resetValues="true" >
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.editTbtjItemsGj}" value="#{itm}"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.ifAdd}" value="false"/>
                    </p:commandLink>
                    <p:spacer width="5"  />
                    <p:commandLink value="删除" action="#{tbTjItemsCommBean.deleteTbTjItemsGjAction}" update=":tabView:editForm:mainGrid3" process="@this">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjItemsGj}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column  style="text-align:center ;">
                    <h:outputText value="#{null == itm.type ? '' : (1 == itm.type ? '参考值' : (2 == itm.type ? '结果限值' : ''))}" />
                </p:column>
                <p:column  style="text-align:center ;">
                    <h:outputText   value="#{null == itm.sex ? '通用' : (1 == itm.sex ? '男' : (2 == itm.sex ? '女' : ''))}"  />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{null == itm.fkByMsruntId ? '' : itm.fkByMsruntId.codeName}" escape="false"  />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.minval}"   />
                </p:column>
                <p:column  style="text-align: center;">
                    <p:outputLabel value="#{itm.maxval}"   />
                </p:column>

            </p:dataTable>
        </h:panelGrid>
    </p:fieldset>
    <p:fieldset legend="定性项目描述" toggleable="true" id="diag" toggleSpeed="500" style="display:block;margin-top: 5px;margin-bottom: 5px;" rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==1 or tbTjItemsCommBean.tbTjItems.jdgptn==2 }">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;" >
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{tbTjItemsCommBean.addTjRstdesctAction}" style="position: relative;top: 3px;"
                                 update=":tabView:editForm:dig" oncomplete="PF('dig').show()" process="@this">
                    <p:resetInput target=":tabView:editForm:dig"></p:resetInput>
                </p:commandButton>
                <p:outputLabel style="color: red;margin-left: 5px; position: relative; top: 3px;"  rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">提示：</p:outputLabel>
                <p:outputLabel style="color: blue; position: relative; top: 3px;"  rendered="#{tbTjItemsCommBean.tbTjItems.jdgptn==2}">体检机构将此项目作为定性时，此处维护定性选项。</p:outputLabel>
            </h:panelGrid>
        </p:outputPanel>
        <h:panelGrid style="width:100%;height:100%;" id="mainGrid">
            <p:dataTable  var="itm" value="#{tbTjItemsCommBean.tbTjItems.tbTjRstdescs}" emptyMessage="没有您要找的记录！" >
                <p:column headerText="操作" style="width: 100px;padding-left:8px;">
                    <p:commandLink value="修改" action="#{tbTjItemsCommBean.modTbTjRstdescInitAction}" oncomplete="PF('dig').show()" update=":tabView:editForm:dig" process="@this" resetValues="true" >
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjRstdesc}" value="#{itm}"/>
                    </p:commandLink>
                    <p:spacer width="5"  />
                    <p:commandLink value="删除" action="#{tbTjItemsCommBean.deleteTbTjRstdescAction}" update=":tabView:editForm:mainGrid" process="@this">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjRstdesc}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="排列顺序" style="width: 100px;text-align:center ;">
                    <h:outputText value="#{itm.num}" />
                </p:column>
                <p:column headerText="结果描述" style="width: 300px;">
                    <h:outputText    value="#{itm.rstDesc}"    title="#{itm.rstDesc}" >

                        <f:converter  converterId="heth.DiscriptionJianXieConvertComm"/>
                    </h:outputText>
                </p:column>
                <p:column headerText="是否合格" style="width: 100px;text-align: center;">
                    <p:outputLabel value="不合格"  rendered="#{itm.egbTag==0}" />
                    <p:outputLabel value="合格"  rendered="#{itm.egbTag==1}" />
                </p:column>
            </p:dataTable>
        </h:panelGrid>
    </p:fieldset>
    </p:outputPanel>

    <p:dialog header="国家接口标准维护"  widgetVar="dig3" closable="true" id="dig3" width="470px" height="220" showEffect="fade" resizable="false">
        <p:panelGrid >
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:165px;height:36px">
                    <h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="类型：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:selectOneRadio value="#{tbTjItemsCommBean.editType}" style="width: 150px;">
                        <f:selectItem itemLabel="参考值" itemValue="1"/>
                        <f:selectItem itemLabel="结果限值" itemValue="2"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height:36px">
                    <h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="性别：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:selectOneRadio value="#{tbTjItemsCommBean.editSex}" style="width: 150px;">
                        <f:selectItem itemLabel="男" itemValue="1"/>
                        <f:selectItem itemLabel="女" itemValue="2"/>
                        <f:selectItem itemLabel="通用" itemValue="3"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column
                        style="text-align:right;padding-right:3px;height:36px">
                    <h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="计量单位："></h:outputText>
                </p:column>
                <p:column style="width:263px;text-align:left;padding-left:8px;">
                    <p:selectOneMenu filter="true" filterMatchMode="contains" value="#{tbTjItemsCommBean.editMsruntId}" style="width:166px;" >
                        <f:selectItem itemLabel="--请选择--" itemValue=""></f:selectItem>
                        <f:selectItems value="#{tbTjItemsCommBean.gjItemUnitList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" ></f:selectItems>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                    <h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="最小值：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:inputText onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)"
                                 value="#{tbTjItemsCommBean.editMinval}"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                    <h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="最大值：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:inputText onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)"
                                 value="#{tbTjItemsCommBean.editMaxVal}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer" >
            <p:column style="align: center">
                <p:spacer width="38%" height="0"/> <p:commandButton process="@this,dig3"  action="#{tbTjItemsCommBean.saveOrUpdateTbTjItemsGjAction}" value="保存" />
                <p:spacer width="8" height="0"/> <p:commandButton onclick="PF('dig3').hide()" immediate="true" value="关闭" />
            </p:column>
        </f:facet>
    </p:dialog>

    <p:dialog header="定性项目描述"  widgetVar="dig" closable="true" id="dig" width="670px" height="200" showEffect="fade" resizable="false">
        <p:panelGrid >
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;width:100px;height:36px">
                    <h:outputText value="排列顺序：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;width:175px;">
                    <p:inputText value="#{tbTjItemsCommBean.rstDescNum}" maxlength="8" onkeyup="SYSTEM.clearNoNum(this)"  converterMessage="排列顺序只能为数字！" />
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width:100px;">
                    <h:outputText value="是否合格：" />
                </p:column>
                <p:column>
                    <p:selectOneRadio id="searchState" value="#{tbTjItemsCommBean.egbTag}" style="width: 190px;">
                        <f:selectItem itemLabel="合格" itemValue="1"/>
                        <f:selectItem itemLabel="不合格" itemValue="0"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:220px;height:36px">
                    <font color="red">*</font>
                    <h:outputText value="结果描述："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="3">
                    <p:inputTextarea rows="7" cols="50"  value="#{tbTjItemsCommBean.rstDesc}"   maxlength="500">

                    </p:inputTextarea>
                </p:column>

            </p:row>
        </p:panelGrid>
        <f:facet name="footer" >
            <p:column style="align: center">
                <p:spacer width="38%" height="0"/> <p:commandButton process="@this,dig" update=":tabView:editForm:diag" action="#{tbTjItemsCommBean.saveTjRstdesctAction}" value="保存" />
                <p:spacer width="8" height="0"/> <p:commandButton onclick="PF('dig').hide()" immediate="true" value="关闭" />
            </p:column>
        </f:facet>
    </p:dialog>
    
    
    
    <p:dialog header="特殊标准维护"  widgetVar="dig2" closable="true" id="dig2" width="470px" height="262" showEffect="fade" resizable="false">
        <p:panelGrid >
			<p:row>
				<p:column
						style="text-align:right;padding-right:3px;width:165px;height:36px">
						<h:outputText value="在岗状态："></h:outputText>
				</p:column>
				<p:column style="width:263px;text-align:left;padding-left:8px;">
						<p:selectOneMenu value="#{tbTjItemsCommBean.editOnguardStateRid}" style="width:166px;">
							<f:selectItem itemLabel="--请选择--" itemValue=""></f:selectItem>
							<f:selectItems value="#{tbTjItemsCommBean.jobStateList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" ></f:selectItems>
						</p:selectOneMenu>
				</p:column>
			</p:row>
			<p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
					<h:outputText value="危害因素：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 1px;">
                    <h:panelGrid columns="4"
					style="border-color: #ffffff;margin: 0px;padding: 0px;">
					<p:inputText id="searchBadrsns" 
						value="#{tbTjItemsCommBean.editBadRsnName}" style="width: 159px;"
						readonly="true" />
					<p:commandLink styleClass="ui-icon ui-icon-search"
						id="initTreeLink" process="@this"
						style="position: relative;left: -30px;top:0px;"
						oncomplete="PF('OveralPanel').show()" />
                        <!-- 清空 -->
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" style="margin-left: -30px;"
                                       process="@this" update="searchBadrsns" action="#{tbTjItemsCommBean.clearItemCode}">
                        </p:commandLink>
				</h:panelGrid>
				<p:overlayPanel id="overalPanel" for="searchBadrsns" dynamic="false" styleClass="ui-shadow-22"
					style="width:280px;" widgetVar="OveralPanel" showCloseIcon="true">
					<p:tree value="#{tbTjItemsCommBean.sortTree}" var="node"
						selectionMode="single" id="choiceTree"
						style="width: 250px;height: 300px;overflow-y: auto;">
						<p:ajax event="select" process="@this" 
							listener="#{tbTjItemsCommBean.onsortNodeSelect}" />
						<p:treeNode>
							<h:outputText value="#{node.codeName}" />
						</p:treeNode>
					</p:tree>
				</p:overlayPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column style="text-align:right;padding-right:3px;height:36px">
                    <h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="性别：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:selectOneRadio id="searchState2" value="#{tbTjItemsCommBean.editSexStr}" style="width: 150px;">
                        <f:selectItem itemLabel="男" itemValue="男"/>
                        <f:selectItem itemLabel="女" itemValue="女"/>
                        <f:selectItem itemLabel="通用" itemValue="通用"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                    <h:outputText value="计量单位：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:outputLabel value="#{tbTjItemsCommBean.editMsrunt}" escape="false" />
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                	<h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="最小值：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:inputText onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)"
                    			value="#{tbTjItemsCommBean.editMinval}"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                	<h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="最大值：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:inputText onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)" value="#{tbTjItemsCommBean.editMaxVal}"/>
                </p:column>
            </p:row>
            
        </p:panelGrid>
        <f:facet name="footer" >
            <p:column style="align: center">
                <p:spacer width="38%" height="0"/> <p:commandButton process="@this,dig2"  action="#{tbTjItemsCommBean.saveTjSpecialAction}" value="保存" />
                <p:spacer width="8" height="0"/> <p:commandButton onclick="PF('dig2').hide()" immediate="true" value="关闭" />
            </p:column>
        </f:facet>
    </p:dialog>
    
    
    <p:dialog header="计量单位配置"  widgetVar="dig0" closable="true" id="dig0" width="470px" height="176" showEffect="fade" resizable="false">
        <p:panelGrid >
			<p:row>
				<p:column
						style="text-align:right;padding-right:3px;width:165px;height:36px">
						<h:outputText value="*" style="color:red;"></h:outputText>
						<h:outputText value="计量单位："></h:outputText>
				</p:column>
				<p:column style="width:263px;text-align:left;padding-left:8px;">
						<p:selectOneMenu filter="true" filterMatchMode="contains" value="#{tbTjItemsCommBean.editMsruntId}" style="width:166px;" >
							<f:selectItem itemLabel="--请选择--" itemValue=""></f:selectItem>
							<f:selectItems value="#{tbTjItemsCommBean.itemUnitList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" ></f:selectItems>
						</p:selectOneMenu>
				</p:column>
			</p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                	<h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="最小值：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:inputText onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)"
                    			value="#{tbTjItemsCommBean.editMinval}"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="text-align:right;padding-right:3px;height:36px">
                	<h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="最大值：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:inputText onkeyup="SYSTEM.verifyNumMinus(this,12,6)" onblur="SYSTEM.verifyNumMinus(this,12,6)" 
                    value="#{tbTjItemsCommBean.editMaxVal}"/>
                </p:column>
            </p:row>
            <p:row>
            	<p:column style="text-align:right;padding-right:3px;height:36px">
            		<h:outputText value="*" style="color:red;"></h:outputText>
                    <h:outputText value="是否默认：" />
                </p:column>
                <p:column style="text-align: left;padding-left: 8px;">
                    <p:selectOneRadio id="searchState0" value="#{tbTjItemsCommBean.editIfDefault}" style="width: 150px;">
                        <f:selectItem itemLabel="是" itemValue="1"/>
                        <f:selectItem itemLabel="否" itemValue="0"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer" >
            <p:column style="align: center">
                <p:spacer width="38%" height="0"/> <p:commandButton process="@this,dig0"  action="#{tbTjItemsCommBean.saveTjItemUnitAction}" value="保存" />
                <p:spacer width="8" height="0"/> <p:commandButton onclick="PF('dig0').hide()" immediate="true" value="关闭" />
            </p:column>
        </f:facet>
    </p:dialog>
</ui:define>
</ui:composition>

