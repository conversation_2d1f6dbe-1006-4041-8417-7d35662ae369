<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdGjBhklistBean"-->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="国家体检数据导入"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="buttons" style="display:flex">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;" id="buttonGrid">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <ui:insert name="insertSubEditButtons"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}" onclick="hideTooltips();"
                                 update=":tabView" immediate="true"/>
            </h:panelGrid>
            <p:sticky target="buttons"/>
        </p:outputPanel>
    </ui:define>
    <!-- 页面的内容-->
    <ui:define name="insertOtherContents">
        <ui:include src="/webapp/heth/comm/tdGjBhkInfoComm.xhtml">
        </ui:include>
    </ui:define>
</ui:composition>