<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjItemsCommBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/tbTjItemsEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/tbTjItemsView.xhtml"/>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检项目维护"/>
            </p:column>
        </p:row>
    </ui:define>
    
	<ui:define name="insertScripts">
		<style type="text/css">
			.ui-orderlist .ui-orderlist-list {
				list-style-type: none;
				margin: 0;
				padding: 0;
				overflow: auto;
				height: 400px;
				width: 250px;
			}
			
			.orderList {
				padding-left: 13px;
			}
		</style>
	</ui:define>

	<!-- 按钮 -->
    <ui:define name="insertButtons">
    	<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tbTjItemsCommBean.searchAction}" update="dataTable"
					process="@this,searchItemName,searchItemCode,searchState,searchPDState,searchYeWuRid" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{tbTjItemsCommBean.addInitAction}" resetValues="true" update=":tabView" process="@this">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="业务分类：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 259px;border-right: transparent;">

                    <h:panelGrid columns="3"
                                 style="border-color: #ffffff;margin: 0px;padding: 0px;">
                        <p:inputText id="searchYeWuName"
                                     value="#{tbTjItemsCommBean.searchYeWuName}" readonly="true"
                                     style="width: 180px;" />
                        <p:commandLink styleClass="ui-icon ui-icon-search"
                                       id="initTreeLink" process="@this"
                                       style="position: relative;left: -33px;top:0px;"
                                       oncomplete="PF('overalPanel').show()" />
                        <h:inputHidden id="searchYeWuRid"
                                       requiredMessage="请选择业务分类！"
                                       value="#{tbTjItemsCommBean.searchYeWuRid}" />
                    </h:panelGrid>
                    <p:overlayPanel id="overalPanel" for="searchYeWuName"
                                    style="width:280px;" widgetVar="overalPanel" 
                                    dynamic="false">
                        <p:tree var="node" selectionMode="single" id="choiceTree"
                                value="#{tbTjItemsCommBean.badRsnTree}"
                                style="width: 250px;height: 300px;overflow-y: auto;">
                            <p:ajax event="select"
                                    update=":tabView:mainForm:searchYeWuName,:tabView:mainForm:searchYeWuRid"
                                    listener="#{tbTjItemsCommBean.onNodeSelect}" process="@this"
                                    oncomplete="PF('overalPanel').hide();" />
                            <p:treeNode>
                                <h:outputText value="#{node.codeName}" />
                            </p:treeNode>
                        </p:tree>
                    </p:overlayPanel>
            </p:column>
            <p:column style="border-left: transparent;width:20px;">
            	<p:commandLink style="position: relative; left: -64px;" styleClass="ui-icon ui-icon-trash" title="清空" update=":tabView:mainForm:searchYeWuName"
				   	process="@this,:tabView:mainForm:searchYeWuName" action="#{tbTjItemsCommBean.delSearchYeWuName}">
				</p:commandLink>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="项目编码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 280px;">
                <p:inputText id="searchItemCode" value="#{tbTjItemsCommBean.searchItemCode}" maxlength="25" style="width: 180px;"/>
            </p:column>
             <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="项目名称/拼音码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchItemName" value="#{tbTjItemsCommBean.searchItemName}" maxlength="50" style="width: 180px;"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="判断模式：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="2">
                <p:selectManyCheckbox id="searchPDState" value="#{tbTjItemsCommBean.searchPanDuanState}" >
                    <f:selectItem itemLabel="定性" itemValue="1"/>
                    <f:selectItem itemLabel="定量" itemValue="2"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectManyCheckbox id="searchState" value="#{tbTjItemsCommBean.searchState}" >
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="业务分类" style="width:300px;padding-left: 3px;">
            <h:outputText value="#{itm.tsSimpleCode.codeName}" />
        </p:column>
        <p:column headerText="项目编码" style="width:100px;text-align: center;">
            <h:outputText value="#{itm.itemCode}" />
        </p:column>
        <p:column headerText="项目名称" style="padding-left: 3px;width: 200px;">
            <h:outputText value="#{itm.itemName}" />
        </p:column>
        <p:column headerText="判断模式" style="width:100px;text-align: center;">
            <h:outputText value="定性" rendered="#{itm.jdgptn==1}"/>
            <h:outputText value="定量" rendered="#{itm.jdgptn==2}"/>
    	</p:column>
        <p:column headerText="序号" style="width:80px;text-align: center;">
            <h:outputText value="#{itm.num}" />
        </p:column>
        <p:column headerText="状态" style="width: 80px;text-align: center;">
            <h:outputText value="启用" rendered="#{itm.stopTag==1}"/>
            <h:outputText value="停用" rendered="#{itm.stopTag==0}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left:5px;">
            <p:commandLink value="详情"   process="@this"  action="#{tbTjItemsCommBean.viewInitAction}" update=":tabView">
                <f:setPropertyActionListener target="#{tbTjItemsCommBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm.stopTag==0}" />
            <p:commandLink value="修改" action="#{tbTjItemsCommBean.modInitAction}" update=":tabView" resetValues="true" process="@this" rendered="#{itm.stopTag==0}">
                <f:setPropertyActionListener target="#{tbTjItemsCommBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm.stopTag==0}" />
            <p:commandLink value="删除" action="#{tbTjItemsCommBean.deleteAction}" update="dataTable" process="@this" rendered="#{itm.stopTag==0}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{tbTjItemsCommBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
            <p:spacer width="5"  />
            <p:commandLink value="停用" action="#{tbTjItemsCommBean.stopOrStartAction}" update="dataTable" process="@this" rendered="#{itm.stopTag==1}">
                <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjItems}" value="#{itm}"/>
            </p:commandLink>
            <p:commandLink value="启用" action="#{tbTjItemsCommBean.stopOrStartAction}" update="dataTable" process="@this" rendered="#{itm.stopTag!=1}">
                <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjItems}" value="#{itm}"/>
            </p:commandLink>
            <p:spacer width="5" /> 
            <p:commandLink value="排序" action="#{tbTjItemsCommBean.orderAction}" update=":tabView:mainForm:codeOrderDialog" process="@this" oncomplete="PF('CodeOrderDialog').show();" >
                <f:setPropertyActionListener target="#{tbTjItemsCommBean.tbTjItems}" value="#{itm}"/>
            </p:commandLink>

        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
    	
		<!-- 排序码表 -->
		<p:dialog id="codeOrderDialog" header="项目排序" widgetVar="CodeOrderDialog" resizable="false" width="340" height="440" modal="true">
			<p:orderList value="#{tbTjItemsCommBean.orderList}"  id="orderL" var="code" style="width:250px;text-align:center;" styleClass="orderList" 
			itemValue="#{code}" converter="heth.TjItemsConvertComm"
				controlsLocation="right">
				<f:facet name="caption">项目名称</f:facet>

				<p:column>
					<h:outputText value="#{code.itemName}" />
				</p:column>
			</p:orderList>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn2" action="#{tbTjItemsCommBean.saveOrder}" update="dataTable" 
						process="@this,orderL" oncomplete="PF('CodeOrderDialog').hide();" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn2" onclick="PF('CodeOrderDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
    
   </ui:define>
</ui:composition>











