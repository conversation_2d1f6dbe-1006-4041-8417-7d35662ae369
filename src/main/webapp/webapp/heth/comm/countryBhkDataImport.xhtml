<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{countryBhkDataImportBean}" />
	<ui:define name="insertScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
		<script type="text/javascript">
			function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
		</script>
		<style type="text/css">
			.searchTime input{
				width:77px;
			}
		</style>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="国家体检数据导入" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="6"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					onclick="showStatus()" action="#{mgrbean.searchAction}"
					oncomplete="hideStatus()" update=":tabView:mainForm:dataTable"
					process="@this,mainGrid" />
				<p:commandButton value="导入" icon="ui-icon-arrowreturnthick-1-n" id="importBtn"
								 update="uploadFileDialog"
								 oncomplete="PF('UploadFileDialog').show();"/>
				<p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
								 process="@this"
								 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
								 rendered="#{null ne mgrbean.importErrFilePath}">
					<p:fileDownload value="#{mgrbean.errorImportFile}"/>
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>

	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:38px;width: 160px">
				<p:outputLabel value="用人单位地区：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 260px">
				<zwx:ZoneSingleNewComp zoneList="#{mgrbean.crptZoneList}"
									   zoneCode="#{mgrbean.searchZoneCode}"
									   zoneName="#{mgrbean.searchZoneName}"
									   ifShowTrash="true" zonePaddingLeft="0"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width: 160px">
				<p:outputLabel value="用人单位名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 260px">
				<p:inputText value="#{mgrbean.searchCrptName}"
							 maxlength="50" style="width: 180px;"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width: 153px;width: 160px">
				<p:outputLabel value="用人单位社会信用代码：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText value="#{mgrbean.searchCode}"
							 maxlength="25" style="width: 180px;" placeholder="精确查询"/>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width: 153px;height:38px;">
				<p:outputLabel value="姓名：" />
			</p:column>
			<p:column style="text-align:left;width: 280px;padding-left:8px;">
				<p:inputText value="#{mgrbean.searchPersonName}" maxlength="100"
							 style="width: 180px;"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width: 153px;">
				<p:outputLabel value="证件号码：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText value="#{mgrbean.searchIDC}"
							 maxlength="25" style="width: 180px;" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;height:38px;" >
				<p:outputLabel value="体检日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchStartTime}"
											  endDate="#{mgrbean.searchEndTime}"  styleClass="searchTime"/>
			</p:column>
		</p:row>
	</ui:define>
	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="用人单位地区" style="width:210px;">
			<h:outputLabel value="#{itm[0]}" />
		</p:column>
		<p:column headerText="用人单位名称" style="width: 220px;">
			<h:outputLabel value="#{itm[1]}" />
		</p:column>
		<p:column headerText="社会信用代码" style="width: 120px;text-align: center;" >
			<h:outputLabel value="#{itm[2]}" />
		</p:column>
		<p:column headerText="体检编号" style="width: 230px;">
			<h:outputLabel value="#{itm[3]}" />
		</p:column>
		<p:column headerText="姓名" style="width: 80px;text-align: center">
			<h:outputLabel value="#{itm[4]}" />
		</p:column>
		<p:column headerText="证件号码" style="width: 110px;text-align: center">
			<h:outputLabel value="#{itm[5]}" />
		</p:column>
		<p:column headerText="体检日期" style="width: 80px;text-align: center;">
			<h:outputLabel value="#{itm[6]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			</h:outputLabel>
		</p:column>
		<p:column headerText="检查机构" >
			<h:outputLabel value="#{itm[7]}" />
		</p:column>
	</ui:define>
	<ui:define name="insertOtherMainContents">
		<p:dialog header="导入" widgetVar="UploadFileDialog" id="uploadFileDialog" resizable="false"
				  modal="true" width="600">
			<table width="100%">
				<tr>
					<td style="text-align: right;">
						<p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
									   style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
					</td>
				</tr>
				<tr>
					<td style="position: relative;top: -23px;">
						<p:fileUpload requiredMessage="请选择要文件上传！" styleClass="table-border-none"
									  id="fileUpload"
									  process="@this" fileUploadListener="#{mgrbean.importDataAction}"
									  label="选择文件" invalidSizeMessage="文件大小不能超过200M!"
									  validatorMessage="上传出错啦，请重新上传！"
									  allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1"
									  fileLimitMessage="最多只能上传1个文件！"
									  invalidFileMessage="只能上传xls、xlsx格式的文件！"
									  previewWidth="120" cancelLabel="取消"
									  uploadLabel="导入" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
									  dragDropSupport="true" mode="advanced" sizeLimit="209715200"/>
					</td>
				</tr>
			</table>
		</p:dialog>
	</ui:define>
</ui:composition>