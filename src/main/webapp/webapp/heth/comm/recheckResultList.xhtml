<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.RecheckResultBean"-->
    <ui:param name="mgrbean" value="#{recheckResultBean}"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }

            function hideStatus() {
                PF('StatusDialog').hide();
            }
        </script>
        <style>
            .myCalendar2 input {
                width: 77px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="体检复查结果查询"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4"
                         style="border-color:transparent;padding:0;">
			<span class="ui-separator"><span
                    class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="analyseBtn" oncomplete="hideStatus()"
                                 action="#{mgrbean.searchAction}" onclick="showStatus()"
                                 process="@this,mainGrid" update="dataTable"/>
                <p:commandButton value="导出" ajax="false" icon="ui-icon-document" id="exportBtn"
                                 process="@this,mainGrid"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload  value="#{mgrbean.downloadFile}" />
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px">
                <h:outputText value="*" style="color:red;"/>
                <p:outputLabel value="用工单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:280px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:280px;">
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50"
                             placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width: 180px;" maxlength="25"
                             placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px">
                <p:outputLabel value="检查机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:280px;">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table style="border-spacing: initial;">
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -25px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                            process="@this"
                                            resetValues="true" update="unitName"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -26px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="体检日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:280px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchBhkBeginDate}"
                                              endDate="#{mgrbean.searchBhkEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="*" style="color:red;"/>
                <h:outputText value="报告出具日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptPrintBeginDate}"
                                              endDate="#{mgrbean.searchRptPrintEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px">
                <p:outputLabel value="在岗状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:280px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.selectOnGuardRids}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="复查危害因素："/>
            </p:column>
            <p:column style="text-align:left;width:280px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnRids}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        ifTree="true" ifSelectParent="false"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="未复检总人数："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.totalNumOfUnreviewed}" style="width: 180px;" maxlength="4"
                             onkeyup="SYSTEM.clearNoNumBig0(this);"
                             onblur="SYSTEM.clearNoNumBig0(this);"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="用工单位地区" style="width: 250px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="社会信用代码" style="width: 90px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="单位地址" style="width: 260px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="行业类别" style="width: 200px;padding-left: 8px;">
            <h:outputText value="#{itm[11]}"/>
        </p:column>
        <p:column headerText="经济类型" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="企业规模" style="width: 60px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="联系人" style="width: 60px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width: 80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="未复检总人数" style="width: 85px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[10]}"/>
        </p:column>
    </ui:define>
</ui:composition>
