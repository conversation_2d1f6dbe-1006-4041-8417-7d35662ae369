<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.OccHethCardListBean"-->
    <style>
        .myCalendar input {
            width: 186px;
        }

        .myCalendar2 input {
            width: 80px;
        }

        .aloneTitleTable {
            color: #334B9A;
            font-weight: bold;
            padding-left: 6px;
        }

        .column_title {
            width: 15% !important;
        }

        .column_content {
            width: 35% !important;
            padding-left: 8px !important;
            word-wrap: break-word;
            word-break: break-all;
        }

        .content_input {
            width: 186px !important;
        }

        .content_checkbox {
            padding: 0 3px;
        }

        table.ui-selectmanycheckbox tr {
            display: flex;
            align-items: center;
        }

        table.ui-selectmanycheckbox td {
            display: flex;
            align-items: center;
        }

        table.ui-selectmanycheckbox td label {
            white-space: normal;
            overflow: hidden;
        }

        .ui-state-default.th_required_header:before {
            content: '*';
            color: red;
        }

        .shadeTip {
            border-radius: 5px;
            padding: 10px;
            background: #4D4D4D !important;
            text-align: left;
            color: white !important;
            word-wrap: break-word;
            border: none;
        }

        .shadeTip .ui-dialog-content {
            border: 1px solid transparent;
            background: #4D4D4D !important;
        }

        .shadeTip .ui-widget-content {
            border: 1px solid transparent;
            background: #4D4D4D !important;
        }

        .shadeTip > div:first-child {
            overflow: hidden;
            margin-top: -10px;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[
       function disabledInputReturn() {
           $('input, textarea').on('keypress', function(e) {
               if (e.which === 13) {
                   e.preventDefault(); // 阻止回车键
               }
           });
       }
        function disabledInput(ifView, id) {
            if (ifView == "false") {
                return;
            }
            var text;
            var $tabView;
            if (id) {
                $tabView = $("#" + id)
            } else {
                $tabView = $("#tabView\\:editForm");
            }
            $tabView.find("input,textarea").each(function () {
                if ($(this).attr("type") == "radio" || $(this).attr("type") == "checkbox") {
                    $(this).css("pointer-events", "none");
                } else {
                    $(this).prop("disabled", true);
                    $(this).css("pointer-events", "none");
                }
                $(this).css("opacity", "1");
            });
            //单选框label的for标签处理
            $tabView.find("label").each(function () {
                $(this).css("pointer-events", "none");
            });
            $tabView.find("a").each(function () {
                text = $(this).text();
                if (!text) {
                    text = $(this).attr("title");
                }
                if ("删除" == text|| "修改" == text || "选择" == text || "附件删除" == text || "上传附件" == text || "附件删除" == text) {
                    $(this).remove();
                } else if ("查看" == text || "修改" == text) {

                } else {
                    $(this).prop("disabled", true);
                    $(this).css("pointer-events", "none");
                    $(this).css("opacity", "0.35");
                }

            });
            $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function () {
                $(this).addClass("ui-state-disabled");
                $(this).css("opacity", "1");
                $(this).css("pointer-events", "none");
            });
            //下拉
            $tabView.find("div[class*='ui-selectonemenu']").each(function () {
                $(this).addClass("ui-state-disabled");
                $(this).css("pointer-events", "none");
                $(this).css("opacity", "1");
            });
            //按钮
            $tabView.find("button").each(function () {
                text = $(this).text();
                if ("扫描" == text || "预览" == text || "制作" == text || "删除" == text || "修改" == text || "上传" == text || "添加" == text || "设计" == text || "汇总" == text) {
                    $(this).remove();
                } else if ("查看" == text) {

                } else {
                    $(this).prop("disabled", true);
                    $(this).css("pointer-events", "none");
                    $(this).css("opacity", "0.35");
                }
            });
        }

        function openPDFClick() {
            document.getElementById("tabView:editForm:openPDF").click();
        }

        /**
         * 移除掉多余的地区框
         */
        var removeExtraTreePanel = function(){
            var badrsnSubTable = jQuery("#tabView\\:editForm\\:badrsnSubTable");
            var size = badrsnSubTable.find("tr").length;
            for (var i=0;i<size-1;i++){
                var el = jQuery("#tabView\\:editForm\\:msruntSelectPanel"+i);
                if(el.length>1){
                    el.each(function(index){
                        if(index>0){
                            $(this).remove();
                        }
                    });
                }
            }

        }

        function getDownloadFileClick() {
            document.getElementById("tabView:editForm:qrCodeDownLoad").click();
        }

        //]]>
    </script>
    <h:form id="editForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <!-- 标题 -->
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="职业卫生技术服务信息报送卡填报"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮组 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="btnPanel">
            <h:panelGrid columns="15" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton style="float:right; display:none;"/>
                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"
                                 action="#{mgrbean.saveAction}"
                                 process="@this,:tabView:editForm" update=":tabView:editForm:editPanel">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.checkSubmitAction}" process="@this,:tabView:editForm"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}"
                                 update=":tabView" process="@this"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                             update=":tabView:editForm:editPanel,:tabView:editForm:servicesInfoPanel"
                             oncomplete="PF('ConfirmDialog').hide();windowScrollTop();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
        <p:confirmDialog message="切换职业病危害因素将清空超标岗位/工种信息，是否继续？" header="消息确认框" widgetVar="ConfirmDelDialog">
            <p:outputPanel style="text-align:right;">
                <p:commandButton value="确定" action="#{mgrbean.changeBadRsn}"
                                 icon="ui-icon-check" process="@this"
                                 oncomplete="PF('ConfirmDelDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close"
                                 process="@this" oncomplete="PF('ConfirmDelDialog').hide();" />
            </p:outputPanel>
        </p:confirmDialog>
        <p:sticky target="btnPanel"/>
        <!-- 正文 -->
        <p:outputPanel id="editPanel" styleClass="businessInfo" style="margin-top:40px;background: rgb(252, 253, 253);">
            <!-- 文书 -->
            <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                <p:row>
                    <p:column colspan="2"><span>本环节文书</span></p:column>
                </p:row>
                <p:row>
                    <p:column style="padding: 0;" colspan="2">
                        <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 250px;">
                        <h:outputText value="*" style="color:red;"/>
                        <h:outputText value="《职业卫生技术服务信息报送卡》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="预览" onclick="PF('ShadeTip').show();"
                                             action="#{mgrbean.buildWritReport}"
                                             process="@this,:tabView:editForm"
                                             oncomplete="PF('ShadeTip').hide();"
                                             rendered="#{mgrbean.occhethCard.annexPath==null}"/>
                            <p:spacer width="5" rendered="#{mgrbean.occhethCard.annexPath == null}"/>
                            <p:commandButton value="上传"
                                             process="@this,:tabView:editForm" update="fileDialog"
                                             action="#{mgrbean.beforeUpload}"
                                             rendered="#{mgrbean.occhethCard.annexPath == null}"/>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.occhethCard.annexPath}')"
                                             rendered="#{mgrbean.occhethCard.annexPath != null}"/>
                            <p:spacer width="5" rendered="#{mgrbean.occhethCard.annexPath != null}"/>
                            <p:commandButton value="删除"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.delMadedwrit}"
                                             rendered="#{mgrbean.occhethCard.annexPath != null}"
                                             update=":tabView:editForm:editPanel,:tabView:editForm:fileDialog,:tabView:editForm:shadeTip">
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:commandButton style="display: none;" id="openPDF"
                                             icon="ui-icon-print"
                                             onclick="window.open('/webFile/#{mgrbean.reportFilePath}')"
                                             process="@this"/>
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 400px;">
                        <h:outputText value="*" style="color:red;"/>
                        <h:outputText value="《职业卫生技术服务信息报告首页（含质控号）、签发页》（盖章）"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="上传"
                                             process="@this,:tabView:editForm" update="fileSignDialog"
                                             action="#{mgrbean.beforeSignUpload}"
                                             rendered="#{mgrbean.occhethCard.signAddress == null}"/>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.occhethCard.signAddress}')"
                                             rendered="#{mgrbean.occhethCard.signAddress != null}"/>
                            <p:spacer width="5" rendered="#{mgrbean.occhethCard.signAddress != null}"/>
                            <p:commandButton value="删除"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.delSignwrit}"
                                             rendered="#{mgrbean.occhethCard.signAddress != null}"
                                             update=":tabView:editForm:writeSortPanel,:tabView:editForm:fileSignDialog">
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.occhethCard.source == 2}">
                    <p:column style="width: 400px;">
                        <h:outputText value="质控二维码"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="下载" action="#{mgrbean.beforeDownloadQrCode}"
                                             process="@this,:tabView:editForm" update=":tabView:editForm" />
                            <p:commandButton style="display: none;" id="qrCodeDownLoad" icon="ui-icon-document" ajax="false"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                <p:fileDownload value="#{mgrbean.getQRCodeStreamedContent()}"/>
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <div id="occHethCard">
                <!-- 标题 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                            <h:outputText value="职业卫生技术服务信息报送卡"
                                          style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: left;">
                            <h:outputText value="报告卡编码："/>
                            <p:outputLabel value="自动生成" style="color:gray;"
                                           rendered="#{mgrbean.occhethCard.rid == null}"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.cardNo}"
                                           rendered="#{mgrbean.occhethCard.rid != null}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 机构信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="orgInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="机构信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="机构名称："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgName}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="法定代表人" style="padding-right: 8px; "/>
                            <br/>
                            <p:outputLabel value="（或主要负责人）："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgFz}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="注册地址："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgAddr}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="机构资质证书编号："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel
                                    value="#{mgrbean.occhethCard.certNo}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="项目负责人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:inputText value="#{mgrbean.occhethCard.proFz}" maxlength="50"
                                         styleClass="content_input"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="联系电话："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:inputText value="#{mgrbean.occhethCard.proLinktel}" maxlength="20"
                                         styleClass="content_input"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="资质业务范围："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:outputLabel value="#{mgrbean.occhethCard.occhethCardItemsStr}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 参与人员信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="occhethCardPsnPanel">
                    <p:row>
                        <p:column style="border-bottom-color: transparent;" colspan="2">
                            <p:outputLabel value="参与人员信息"
                                           style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"/>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             action="#{mgrbean.addOcchethCardPsn}"
                                             process="@this,:tabView:editForm:occhethCardPsnTable">
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onOcchethCardPsnSel}" process="@this"
                                        resetValues="true" update=":tabView:editForm:occhethCardPsnPanel"/>
                            </p:commandButton>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="2">
                            <p:dataTable id="occhethCardPsnTable" paginatorPosition="bottom"
                                         value="#{mgrbean.occhethCard.occhethCardPsnList}"
                                         widgetVar="occhethCardPsnTable" var="occhethCardPsn"
                                         emptyMessage="没有数据！" rowIndexVar="R" styleClass="writeSortInfo1">
                                <!--@elvariable id="R" type="java.lang.Integer"-->
                                <p:column headerText="序号" style="width:40px;text-align: center;">
                                    <p:outputLabel value="#{R+1}"/>
                                </p:column>
                                <p:column headerText="姓名" style="width:100px;text-align: center;">
                                    <p:outputLabel value="#{occhethCardPsn.psnName}"/>
                                </p:column>
                                <p:column headerText="承担的服务事项" style="width:600px;text-align: center;"
                                          styleClass="th_required_header">
                                    <p:selectManyCheckbox columns="8" layout="grid" style="width: 540px;"
                                                          value="#{occhethCardPsn.occhethCardItemSimpleCodeList}">
                                        <f:selectItems value="#{mgrbean.servicesUndertakenList}" var="simpleCode"
                                                       itemValue="#{simpleCode.rid}"
                                                       itemLabel="#{simpleCode.codeName}"/>
                                    </p:selectManyCheckbox>
                                </p:column>
                                <p:column headerText="操作">
                                    <p:commandLink value="删除" rendered="#{!occhethCardPsn.isApp}"
                                                   onclick="PF('PsnDeleteDialog#{R+1}').show();"
                                                   process="@this,:tabView:editForm:occhethCardPsnTable"/>
                                    <p:confirmDialog message="确定要删除吗？" header="消息确认框"
                                                     widgetVar="PsnDeleteDialog#{R+1}">
                                        <p:commandButton value="确定" icon="ui-icon-check"
                                                         action="#{mgrbean.delOcchethCardPsn(occhethCardPsn)}"
                                                         update=":tabView:editForm:occhethCardPsnTable"
                                                         oncomplete="PF('PsnDeleteDialog#{R+1}').hide();"/>
                                        <p:commandButton type="button" value="取消" icon="ui-icon-close"
                                                         onclick="PF('PsnDeleteDialog#{R+1}').hide();"/>
                                    </p:confirmDialog>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 服务的用人单位信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="crptInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="服务的用人单位信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" rendered="#{mgrbean.occhethCard.source ne 2}"/>
                            <p:outputLabel value="单位名称："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <div style="display: flex;align-items: center;display: #{mgrbean.occhethCard.source eq 2?'none':''};" >
                                <p:inputText id="crptName" readonly="true" styleClass="content_input"
                                             value="#{mgrbean.occhethCard.crptName}"
                                             onclick="document.getElementById('tabView:editForm:onCrptSelect').click()"/>
                                <p:commandLink id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search"
                                               partialSubmit="true"
                                               action="#{mgrbean.selectCrptList}" process="@this"
                                               style="margin-left: -20px;">
                                    <p:ajax event="dialogReturn" process="@this,crptName" resetValues="true"
                                            listener="#{mgrbean.onCrptSelect}" update="crptInfoPanel"/>
                                </p:commandLink>
                            </div>
                            <p:outputLabel value="#{mgrbean.occhethCard.crptName}" rendered="#{mgrbean.occhethCard.source eq 2}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="统一社会信用代码："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.creditCode}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="注册地址："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.address}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="企业规模："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.occhethCard.fkByCrptSizeId.codeName}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："/>
                        </p:column>
                        <p:column styleClass="column_content" colspan="3">
                            <p:outputLabel value="#{mgrbean.occhethCard.fkByIndusTypeId.codePath}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="联系人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.safeposition}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="联系电话："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.safephone}"/>
                        </p:column>
                    </p:row>

                    <p:row>
                        <p:column styleClass="column_title" colspan="4" style="text-align: left;">
                            <div style="display: flex;align-items: center;justify-content: space-between;padding: 5px 0;height: 32px;">
                                <div>
                                    <p:commandButton value="添加" icon="ui-icon-plus"
                                                     action="#{mgrbean.addOcchethCardZone}"
                                                     process="@this,:tabView:editForm:occhethCardZoneTable"
                                                     update=":tabView:editForm:occhethCardZoneTable"/>
                                </div>
                                <div>
                                    <span class="ui-widget" style="color: red;">提示：</span>
                                    <span class="ui-widget"
                                          style="color: blue;">技术服务机构地址与注册地址不一致的，请详细填写服务地址。</span>
                                </div>
                            </div>
                            <div style="padding-right: 3px;">
                                <p:panelGrid style="width:100%;" id="occhethCardZoneTable" styleClass="writeSortInfo1">
                                    <p:row>
                                        <p:column styleClass="ui-state-default th_required_header"
                                                  style="text-align:center;padding-right:3px;width:215px;height: 19px;">
                                            <p:outputLabel value="技术服务地址"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:400px;height: 19px;">
                                            <p:outputLabel value="详细地址"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;height: 19px;">
                                            <p:outputLabel value="操作"/>
                                        </p:column>
                                    </p:row>
                                    <c:forEach var="occhethCardZone" items="#{mgrbean.occhethCard.occhethCardZoneList}"
                                               varStatus="status">
                                        <p:row>
                                            <p:column style="padding-right:3px;">
                                                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.editZoneList}"
                                                                       zoneId="#{occhethCardZone.zoneRid}"
                                                                       zoneName="#{occhethCardZone.zoneName}"
                                                                       zoneCode="#{occhethCardZone.zoneGb}"
                                                                       choseZoneTypeMin="4" choseZoneTypeMax="4"
                                                                       onchange="onZoneSelect#{status.index}()"/>
                                                <p:remoteCommand name="onZoneSelect#{status.index}"
                                                                 action="#{mgrbean.onOcchethCardZoneSelect(occhethCardZone)}"
                                                                 process="@this,occhethCardZoneTable"
                                                                 update="occhethCardZoneTable"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding:4px 10px;">
                                                <p:outputLabel value="#{occhethCardZone.fullName}"/>
                                            </p:column>
                                            <p:column style="text-align:left;padding:4px 10px;">
                                                <p:commandLink value="删除"
                                                               onclick="PF('ZoneDeleteDialog#{status.index+1}').show();"
                                                               process="@this,:tabView:editForm:occhethCardZoneTable"/>
                                                <p:confirmDialog message="确定要删除吗？" header="消息确认框"
                                                                 widgetVar="ZoneDeleteDialog#{status.index+1}">
                                                    <p:commandButton value="确定" icon="ui-icon-check"
                                                                     action="#{mgrbean.delOcchethCardZone(occhethCardZone)}"
                                                                     update=":tabView:editForm:occhethCardZoneTable"
                                                                     oncomplete="PF('ZoneDeleteDialog#{status.index+1}').hide();"/>
                                                    <p:commandButton type="button" value="取消" icon="ui-icon-close"
                                                                     onclick="PF('ZoneDeleteDialog#{status.index+1}').hide();"/>
                                                </p:confirmDialog>
                                            </p:column>
                                        </p:row>
                                    </c:forEach>
                                    <p:row rendered="#{mgrbean.occhethCard.occhethCardZoneList.size() eq 0}">
                                        <p:column style="padding-right:3px;height: 37px;" colspan="3">
                                            <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                            </div>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 技术服务信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicesInfoPanel" >
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="技术服务信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="技术服务领域："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:selectManyCheckbox columns="5" layout="grid"
                                                  value="#{mgrbean.occhethCard.occhethCardServiceSimpleCodeList}">
                                <f:selectItems value="#{mgrbean.businessScopeList}" var="simpleCode"
                                               itemValue="#{simpleCode.rid}"
                                               itemLabel="#{simpleCode.codeName}"/>
                            </p:selectManyCheckbox>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="现场调查时间："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.occhethCard.investStartDate}"
                                                          endDate="#{mgrbean.occhethCard.investEndDate}"
                                                          styleClass="myCalendar2"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="现场采样/检测时间："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.occhethCard.jcStartDate}"
                                                          endDate="#{mgrbean.occhethCard.jcEndDate}"
                                                          styleClass="myCalendar2"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="出具技术服务报告" style="padding-right: 10px;"/>
                            <br/>
                            <p:outputLabel value="时间："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11" styleClass="myCalendar2"
                                        showOtherMonths="true" id="rptDate" navigator="true"
                                        yearRange="c-10:c" converterMessage="出具技术服务报告时间，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.occhethCard.rptDate}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="技术服务报告编号："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:inputText value="#{mgrbean.occhethCard.rptNo}" maxlength="50"
                                         styleClass="content_input"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 技术服务结果 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicesResultPanel">
                    <p:row>
                        <p:column colspan="2">
                            <p:outputLabel value="技术服务结果" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.selServiceResultExtends1 eq '1'
                                or mgrbean.selServiceResultExtends1 eq '2'
                                or mgrbean.selServiceResultExtends1 eq '3'}">
                        <p:column styleClass="writeSortInfo" style="padding-left: 5px;" colspan="2">
                            <p:outputLabel value="职业病危害因素检测"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '1'}"/>
                            <p:outputLabel value="职业病危害现状评价"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '2'}"/>
                            <p:outputLabel value="职业病防护设备设施与防护用品的效果评价"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '3'}"/>
                            <p:outputLabel value="，质控编号："/>
                            <p:outputLabel value="自动生成" style="color:gray;"
                                           rendered="#{empty mgrbean.occhethCard.checkNo}"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.checkNo}"
                                           rendered="#{not empty mgrbean.occhethCard.checkNo}"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.selServiceResultExtends1 eq '1'
                                or mgrbean.selServiceResultExtends1 eq '2'}">
                        <p:column styleClass="cs-break-word"
                                  style="padding-left: 15px; border-bottom-color: transparent;" colspan="2">
                            <p:outputLabel value="共检测岗位或者工种数量："/>
                            <p:outputLabel value="#{mgrbean.occhethCard.jcPostNum}"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '1'}"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.pjPostNum}"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '2'}"/>
                            <p:outputLabel value="（个），超标岗位/工种数量："/>
                            <p:outputLabel value="#{mgrbean.occhethCard.jcOverNum}"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '1'}"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.pjOverNum}"
                                           rendered="#{mgrbean.selServiceResultExtends1 eq '2'}"/>
                            <p:outputLabel value="（个），超标危害因素类型："/>
                            <p:outputLabel value="#{mgrbean.occhethCard.selectedBadrsnNames}"
                                           rendered="#{not empty mgrbean.occhethCard.selectedBadrsnNames}"/>
                            <p:outputLabel value="无" rendered="#{empty mgrbean.occhethCard.selectedBadrsnNames}"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.selServiceResultExtends1 eq '1' or mgrbean.selServiceResultExtends1 eq '2'}">
                        <p:column colspan="2" style="padding-left: 15px;border-bottom-color: transparent;">
                            <p:outputPanel styleClass="cs-break-word"
                                           style="display: flex;align-items: center;flex-wrap: wrap;">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="职业病危害因素："/>
                                <p:selectManyCheckbox columns="6" layout="grid" style="width:auto;"
                                                      value="#{mgrbean.occhethCard.selBadrsnList}">
                                    <f:selectItems value="#{mgrbean.parentBadRsnList}" var="simpleCode"
                                                   itemValue="#{simpleCode.rid}"
                                                   itemLabel="#{simpleCode.codeName}"/>
                                    <p:ajax event="change" listener="#{mgrbean.chooseBadrsnAction}"
                                            process="servicesResultPanel" update="servicesResultPanel"/>
                                </p:selectManyCheckbox>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="2" style="padding-left: 15px;border-bottom-color: transparent;"
                                  rendered="#{mgrbean.selServiceResultExtends1 eq '1' or mgrbean.selServiceResultExtends1 eq '2'}">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="职业病危害因素检测结果"/>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             action="#{mgrbean.openBadrsnAddJcDialog}"
                                             process="@this,servicesResultPanel"
                                             disabled="#{mgrbean.occhethCard.selBadrsnList == null or mgrbean.occhethCard.selBadrsnList.size()==0}"
                                             style="margin-left:15px;">
                            </p:commandButton>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.selServiceResultExtends1 eq '1' or mgrbean.selServiceResultExtends1 eq '2'}">
                        <p:column styleClass="column_title" colspan="2" style="text-align: left;">
                            <div style="padding-right: 3px; max-height: 465px; overflow-y: auto; position: relative;">
                                <!-- 固定表头 -->
                                <div  style="position: sticky; top: 0; z-index: 100; background-color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <p:panelGrid style="width:100%;" id="badrsnJcDataTableHeader">
                                        <p:row>
                                            <p:column styleClass="ui-state-default header-col" data-index="0"
                                                      style="text-align:center;padding-right:3px;width:110px;">
                                                <p:outputLabel value="职业病危害因素"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="1"
                                                      style="text-align:center;padding-right:3px;width:90px;">
                                                <p:outputLabel value="接触危害人数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="2"
                                                      style="text-align:center;padding-right:3px;width:80px;">
                                                <p:outputLabel value="岗位/工种数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="3"
                                                      style="text-align:center;padding-right:3px;width:100px;">
                                                <p:outputLabel value="超标岗位/工种数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="4"
                                                      style="text-align:center;padding-right:3px;width:110px;">
                                                <p:outputLabel value="超标岗位/工种名称"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="5"
                                                      style="text-align:center;padding-right:3px;width:80px;">
                                                <p:outputLabel value="超标检测项目"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="6"
                                                      style="text-align:center;padding-right:3px;width:80px;">
                                                <p:outputLabel value="超标检测指标"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="7"
                                                      style="text-align:center;padding-right:3px;width:70px;">
                                                <p:outputLabel value="超标检测值"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="8"
                                                      style="text-align:center;padding-right:3px;width:60px;">
                                                <p:outputLabel value="计量单位"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default header-col" data-index="9"
                                                      style="text-align:center;padding-right:3px;width:70px;">
                                                <p:outputLabel value="操作"/>
                                            </p:column>
                                        </p:row>
                                    </p:panelGrid>
                                </div>
                                <!-- 内容表格（包含滚动表头） -->
                                <div style="margin-top: -42px;">
                                    <p:panelGrid style="width:100%;" id="badrsnJcDataTable">
                                        <f:facet name="header">
                                            <p:row>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:110px;">
                                                    <p:outputLabel value="职业病危害因素"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default "
                                                          style="text-align:center;padding-right:3px;width:90px;">
                                                    <p:outputLabel value="接触危害人数"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default "
                                                          style="text-align:center;padding-right:3px;width:80px;">
                                                    <p:outputLabel value="岗位/工种数"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:100px;">
                                                    <p:outputLabel value="超标岗位/工种数"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:110px;">
                                                    <p:outputLabel value="超标岗位/工种名称"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:80px;">
                                                    <p:outputLabel value="超标检测项目"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:80px;">
                                                    <p:outputLabel value="超标检测指标"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:70px;">
                                                    <p:outputLabel value="超标检测值"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:60px;">
                                                    <p:outputLabel value="计量单位"/>
                                                </p:column>
                                                <p:column styleClass="ui-state-default"
                                                          style="text-align:center;padding-right:3px;width:70px;">
                                                    <p:outputLabel value="操作"/>
                                                </p:column>
                                            </p:row>
                                        </f:facet>
                                        <c:forEach var="badrsnSub" items="#{mgrbean.badrsnSubList}"
                                                   varStatus="status">
                                            <c:forEach var="badrsnItem" items="#{badrsnSub.badrsnItems}"
                                                       varStatus="status">
                                                <p:row>
                                                    <p:column style="padding-right:3px;text-align: center;" styleClass="cs-break-word"
                                                              rowspan="#{badrsnItem.badrsnRowspan}"
                                                              rendered="#{null ne badrsnItem.badrsnName}">
                                                        <p:outputLabel value="#{badrsnItem.badrsnName}" escape="false"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                              rowspan="#{badrsnItem.badrsnRowspan}"
                                                              rendered="#{null ne badrsnItem.badrsnName}">
                                                        <p:outputLabel
                                                                value="#{badrsnItem.badrsnNum==0?'':badrsnItem.badrsnNum}"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                              rowspan="#{badrsnItem.badrsnRowspan}"
                                                              rendered="#{null ne badrsnItem.postNum}">
                                                        <p:outputLabel
                                                                value="#{badrsnItem.postNum==0?'':badrsnItem.postNum}"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                              rowspan="#{badrsnItem.badrsnRowspan}"
                                                              rendered="#{null ne badrsnItem.num}">
                                                        <p:outputLabel value="#{badrsnItem.num}"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                              rowspan="#{badrsnItem.postRowspan}"
                                                              rendered="#{null ne badrsnItem.postNameShow}">
                                                        <p:outputLabel value="#{badrsnItem.postNameShow}"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                              rowspan="#{badrsnItem.itemsRowspan}"
                                                              rendered="#{null ne badrsnItem.itermName}">
                                                        <p:outputLabel value="#{badrsnItem.itermName}" escape="false"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word">
                                                        <p:outputLabel value="#{badrsnItem.fkByIndexId.codeName}" escape="false"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word">
                                                        <p:outputLabel value="#{badrsnItem.rst}"/>
                                                    </p:column>
                                                    <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word">
                                                        <p:outputLabel value="#{badrsnItem.fkByMsruntId.codeName}"
                                                                       escape="false"/>
                                                    </p:column>
                                                    <p:column style="text-align:left;padding-right:3px;"
                                                              rowspan="#{badrsnItem.badrsnRowspan}"
                                                              rendered="#{null ne badrsnItem.badrsnName}">
                                                        <p:spacer width="5"/>
                                                        <p:commandLink value="修改"
                                                                       update=":tabView:editForm:badrsnJcPanel,:tabView:editForm:badrsnJcDataTable,:tabView:editForm:addBadrsnBtn,:tabView:editForm:addBadrsnJcDialog"
                                                                       process="@this,:tabView:editForm:badrsnJcDataTable"
                                                                       action="#{mgrbean.openBadrsnUpdateJcDialog}"
                                                                       oncomplete="removeExtraTreePanel()">
                                                            <f:setPropertyActionListener value="#{badrsnSub}"
                                                                                         target="#{mgrbean.occhethBpBadrsnSub}"/>
                                                        </p:commandLink>
                                                        <p:spacer width="5"/>
                                                        <p:commandLink value="删除"
                                                                       update="servicesResultPanel"
                                                                       process="@this,servicesResultPanel"
                                                                       action="#{mgrbean.delOcchethBpBadrsn}">
                                                            <p:confirm header="消息确认框" message="确定要删除吗？"
                                                                       icon="ui-icon-alert"/>
                                                            <f:setPropertyActionListener value="#{badrsnSub}"
                                                                                         target="#{mgrbean.occhethBpBadrsnSub}"/>
                                                        </p:commandLink>
                                                    </p:column>
                                                </p:row>
                                            </c:forEach>
                                        </c:forEach>
                                        <p:row rendered="#{mgrbean.badrsnSubList == null or mgrbean.badrsnSubList.size() eq 0}">
                                            <p:column style="padding-right:3px;" colspan="10">
                                                <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                            </p:column>
                                        </p:row>
                                    </p:panelGrid>
                                </div>
                            </div>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.selServiceResultExtends1 eq '1'
                                or mgrbean.selServiceResultExtends1 eq '2'
                                or mgrbean.selServiceResultExtends1 eq '3'}">
                        <p:column style="width: 280px;">
                            <p:selectBooleanCheckbox value="#{mgrbean.occhethCard.hasJcInst}" id="hasJcInst"
                                                     styleClass="content_checkbox">
                                <p:ajax event="change" resetValues="true"
                                        process="@this,jcInstNum,jcNotHgInstNum,notHgInstName"
                                        update="jcInstNum,jcNotHgInstNum,notHgInstName"/>
                            </p:selectBooleanCheckbox>
                            <p:outputLabel for="hasJcInst" value="开展职业病防护设备设施防护效果检测"/>
                        </p:column>
                        <p:column style="padding: 6px 0 6px 0;">
                            <p:outputLabel value="检测设备设施数量：" style="padding-left: 5px;"/>
                            <p:inputText id="jcInstNum" value="#{mgrbean.occhethCard.jcInstNum}"
                                         maxlength="6" style="margin: 5px;width: 50px;"
                                         disabled="#{!mgrbean.occhethCard.hasJcInst}"
                                         onkeyup="SYSTEM.clearNoNumBig0(this);"
                                         onblur="SYSTEM.clearNoNumBig0(this);"
                                         onchange="SYSTEM.clearNoNumBig0(this)"
                            />
                            <p:outputLabel value="台（套），"/>
                            <br/>
                            <p:outputLabel value="检测结果不合格的设备设施数量：" style="padding-left: 5px;"/>
                            <p:inputText id="jcNotHgInstNum" value="#{mgrbean.occhethCard.jcNotHgInstNum}"
                                         maxlength="6" style="margin: 5px;width: 50px;"
                                         disabled="#{!mgrbean.occhethCard.hasJcInst}"
                                         onkeyup="SYSTEM.clearNoNum(this);"
                                         onchange="SYSTEM.clearNoNum(this)"
                                         onblur="SYSTEM.clearNoNum(this);checkjcNotHgInstNum();"/>
                            <p:remoteCommand name="checkjcNotHgInstNum"
                                             process="@this,jcNotHgInstNum,jcInstNum,notHgInstName"
                                             update="jcNotHgInstNum,jcInstNum,notHgInstName"/>
                            <p:outputLabel value="台（套），不合格的设备设施名称："/>
                            <p:inputText id="notHgInstName" value="#{mgrbean.occhethCard.notHgInstName}"
                                         disabled="#{(!mgrbean.occhethCard.hasJcInst or null eq mgrbean.occhethCard.jcNotHgInstNum or mgrbean.occhethCard.jcNotHgInstNum==0)}"
                                         maxlength="500" style="margin: 5px;width: 170px;"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.selServiceResultExtends1 eq '1'
                                or mgrbean.selServiceResultExtends1 eq '2'
                                or mgrbean.selServiceResultExtends1 eq '3'}">
                        <p:column>
                            <p:selectBooleanCheckbox value="#{mgrbean.occhethCard.hasJcUse}" id="hasJcUse"
                                                     styleClass="content_checkbox">
                                <p:ajax event="change" resetValues="true"
                                        process="@this,jcUseNum,jcNotHgUseNum,notHgUseName"
                                        update="jcUseNum,jcNotHgUseNum,notHgUseName"/>
                            </p:selectBooleanCheckbox>
                            <p:outputLabel for="hasJcUse" value="开展职业病防护用品防护效果检测"/>
                        </p:column>
                        <p:column style="padding: 6px 0 6px 0;">
                            <p:outputLabel value="检测防护用品数量：" style="padding-left: 5px;"/>
                            <p:inputText id="jcUseNum" value="#{mgrbean.occhethCard.jcUseNum}"
                                         maxlength="6" style="margin: 5px;width: 50px;"
                                         onkeyup="SYSTEM.clearNoNumBig0(this);"
                                         onblur="SYSTEM.clearNoNumBig0(this);"
                                         onchange="SYSTEM.clearNoNumBig0(this);"
                                         disabled="#{!mgrbean.occhethCard.hasJcUse}"/>
                            <p:outputLabel value="个（件），"/>
                            <br/>
                            <p:outputLabel value="结果不合格的防护用品数量：" style="padding-left: 5px;"/>
                            <p:remoteCommand name="checkjcNotHgUseNum"
                                             process="@this,jcNotHgUseNum,jcUseNum,notHgUseName"
                                             update="jcNotHgUseNum,jcUseNum,notHgUseName"/>
                            <p:inputText id="jcNotHgUseNum" value="#{mgrbean.occhethCard.jcNotHgUseNum}"
                                         maxlength="6" style="margin: 5px;width: 50px;"
                                         disabled="#{!mgrbean.occhethCard.hasJcUse}"
                                         onkeyup="SYSTEM.clearNoNum(this);"
                                         onchange="SYSTEM.clearNoNum(this);"
                                         onblur="SYSTEM.clearNoNum(this);checkjcNotHgUseNum();"/>
                            <p:outputLabel value="个（件），不合格的防护用品名称："/>
                            <p:inputText id="notHgUseName"
                                         disabled="#{(!mgrbean.occhethCard.hasJcUse or null eq mgrbean.occhethCard.jcNotHgUseNum or mgrbean.occhethCard.jcNotHgUseNum==0)}"
                                         value="#{mgrbean.occhethCard.notHgUseName}" maxlength="500"
                                         style="margin: 5px;width: 170px;"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 报告信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;" id="rptInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="报告信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="填报单位："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.fillUnitName}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="单位负责人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:inputText value="#{mgrbean.occhethCard.orgFzMan}" maxlength="25"
                                         styleClass="content_input"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="填表人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:inputText value="#{mgrbean.occhethCard.fillFormPsn}" maxlength="25"
                                         styleClass="content_input"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="填表人联系电话："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:inputText value="#{mgrbean.occhethCard.fillLink}" maxlength="25"
                                         styleClass="content_input"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="填表日期："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" size="11"
                                        showOtherMonths="true" id="fillDate" navigator="true"
                                        yearRange="c-10:c" converterMessage="填表日期，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.occhethCard.fillDate}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </div>
            <p:dialog header="职业病危害因素检测" widgetVar="AddBadrsnJcDialog" id="addBadrsnJcDialog" resizable="false"
                      width="980"
                      modal="true" height="500">
                <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="addBadrsnBtn">
                    <h:panelGrid columns="4"
                                 style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span
                                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                        <p:commandButton value="保存" icon="ui-icon-disk"
                                         action="#{mgrbean.addBadrsnItemAction}"
                                         process="@this,searchBadrsnPanel,badrsnJcPanel" resetValues="true"
                                         />
                        <p:commandButton value="连续保存" icon="ui-icon-check"
                                         action="#{mgrbean.nextBadrsnItemAction}"
                                         process="@this,searchBadrsnPanel,badrsnJcPanel" resetValues="true"
                                         />
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('AddBadrsnJcDialog').hide();" process="@this"/>
                    </h:panelGrid>
                </p:outputPanel>
                <p:panelGrid style="margin-top: 10px;width: -webkit-fill-available;" id="badrsnJcPanel">
                    <p:row>
                        <p:column
                                style="text-align: right;height: 37px;">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="职业病危害因素："/>
                        </p:column>
                        <p:column>
                            <div style="display: flex;align-items: center;width: 220px;margin-right: -40px;">
                                <div onclick="document.getElementById('tabView:editForm:onBadRsnSelect').click()">
                                    <h:outputText escape="false"
                                                  value="#{mgrbean.occhethBpBadrsnSub.badrsnName}"
                                                  id="badRsn"
                                                  style="width: 180px;display: block;height: 16px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                                                  readonly="true"
                                                  styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all"/>
                                </div>
                                <p:commandLink id="onBadRsnSelect" styleClass="ui-icon ui-icon-search"
                                               partialSubmit="true"
                                               action="#{mgrbean.openBadrsnDialog}" process="@this,addBadrsnJcDialog"
                                               style="position: relative;left: -20px;top:0px;"
                                >
                                </p:commandLink>
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               style="position: relative;left: -15px;"
                                               process="@this,badrsnJcPanel,searchBadrsnPanel"
                                               update="badrsnJcPanel,searchBadrsnPanel"
                                               action="#{mgrbean.clearBpBadrsn}"/>
                            </div>
                        </p:column>
                        <p:column
                                style="text-align: right;">
                            <p:outputLabel value="接触危害人数："/>
                        </p:column>
                        <p:column>
                            <p:inputText value="#{mgrbean.occhethBpBadrsnSub.bulletBadrsnNum}" maxlength="6"
                                         onkeyup="SYSTEM.clearNoNumBig0(this,6)"
                                         onblur="SYSTEM.clearNoNumBig0(this,6)"
                                         onchange="SYSTEM.clearNoNumBig0(this,6)"
                                         style="width: 80px;"
                            />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align: right;height: 37px;">
                            <h:outputText value="*" style="color:red;"/>
                            <p:outputLabel value="岗位/工种数："/>
                        </p:column>
                        <p:column>
                            <p:inputText value="#{mgrbean.occhethBpBadrsnSub.bulletPostNum}" maxlength="5"
                                         onkeyup="SYSTEM.clearNoNumBig0(this,5)"
                                         onblur="SYSTEM.clearNoNumBig0(this,5)"
                                         onchange="SYSTEM.clearNoNumBig0(this,5)"
                                         style="width: 80px;"
                            />
                        </p:column>
                        <p:column
                                style="text-align: right;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="超标岗位/工种数："/>
                        </p:column>
                        <p:column>
                            <p:inputText value="#{mgrbean.occhethBpBadrsnSub.bulletOverPostNum}" maxlength="5"
                                         id="overPostNum"
                                         onkeyup="SYSTEM.clearNoNum(this,5);"
                                         onchange="SYSTEM.clearNoNum(this,5);"
                                         onblur="SYSTEM.clearNoNum(this,5);"
                                         style="width: 80px;"
                            />
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <p:outputPanel id="searchBadrsnPanel">
                    <p:row>
                        <p:column>
                            <p:outputPanel style="margin-top: 10px;">
                                <div style="display: table-row;">
                                    <div style="display: table-cell;vertical-align: middle;">
                                        <p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 10px;"
                                                         id="badrsnAddBtn"
                                                         action="#{mgrbean.addOcchethCardBadrsnItem}"
                                                         process="@this,searchBadrsnPanel"
                                                         update="badrsnSubTable"
                                                         oncomplete="removeExtraTreePanel()"
                                                         disabled="#{!( mgrbean.occhethBpBadrsnSub.badrsnEx1 eq '1' or mgrbean.occhethBpBadrsnSub.badrsnEx1 eq '2' or mgrbean.occhethBpBadrsnSub.badrsnEx1 eq '3' )}"
                                                         >
                                        </p:commandButton>
                                    </div>
                                </div>
                                <div style="display: table-row;margin-top: 5px;">
                                    <div style="display: table-cell;vertical-align: middle;">
                                        <p:panelGrid style="width:100%;" id="badrsnSubTable">
                                            <f:facet name="header">
                                                <p:row>
                                                    <p:column
                                                            styleClass="ui-state-default  #{mgrbean.occhethBpBadrsnSub.state ne '2' ? 'th_required_header' : ''}"
                                                            style="width:200px;text-align: center">
                                                        <p:outputLabel value="超标岗位/工种名称"/>
                                                    </p:column>
                                                    <p:column
                                                            styleClass="ui-state-default  #{mgrbean.occhethBpBadrsnSub.state ne '2' ? 'th_required_header' : ''}"
                                                            style="width:200px;text-align: center">
                                                        <p:outputLabel value="超标检测项目"/>
                                                    </p:column>
                                                    <p:column
                                                            styleClass="ui-state-default  #{mgrbean.occhethBpBadrsnSub.state ne '2' ? 'th_required_header' : ''}"
                                                            style="width:200px;text-align: center">
                                                        <p:outputLabel value="超标检测指标"/>
                                                    </p:column>
                                                    <p:column
                                                            styleClass="ui-state-default #{mgrbean.occhethBpBadrsnSub.state ne '2' ? 'th_required_header' : ''}"
                                                            style="width:145px;text-align: center">
                                                        <p:outputLabel value="超标检测值"/>
                                                    </p:column>
                                                    <p:column styleClass="ui-state-default"
                                                              style="width: #{mgrbean.occhethBpBadrsnSub.state ne '2' ? '100' : '180'}px;text-align: center;">
                                                        <p:outputLabel value="计量单位"/>
                                                    </p:column>
                                                    <p:column styleClass="ui-state-default"
                                                              style="width:80px;text-align: center;"
                                                              >
                                                        <p:outputLabel value="操作"/>
                                                    </p:column>
                                                </p:row>
                                            </f:facet>
                                            <c:forEach var="badrsnSub" items="#{mgrbean.occhethBpBadrsnSub.bulletBadrsnItems}"
                                                       varStatus="status">
                                                <p:row>
                                                    <p:column style="text-align:center;">
                                                        <p:inputText value="#{badrsnSub.postName}" style="width: 140px;"
                                                                     maxlength="50"
                                                                     />
                                                    </p:column>
                                                    <p:column style="text-align:center;">
                                                        <p:selectOneMenu value="#{badrsnSub.itemId}"
                                                                         style="width:170px;"
                                                                         >
                                                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                                            <f:selectItems value="#{mgrbean.itemList2}" var="itm"
                                                                           itemValue="#{itm[0]}" itemLabel="#{itm[8]}"/>
                                                            <p:ajax event="change"
                                                                    listener="#{mgrbean.changeItemAction(badrsnSub,true)}"
                                                                    process="@this,:tabView:editForm:badrsnSubTable"
                                                                    update="msruntSelectTree#{status.index},msruntVal#{status.index}">
                                                            </p:ajax>
                                                        </p:selectOneMenu>
                                                    </p:column>
                                                    <p:column style="text-align:center;">
                                                        <p:selectOneMenu value="#{badrsnSub.indexRid}"
                                                                         style="width:120px;margin-left: 5px;"
                                                                         >
                                                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                                            <f:selectItems value="#{mgrbean.indexList}" var="itm"
                                                                           itemValue="#{itm.rid}"
                                                                           itemLabel="#{itm.codeName}"/>
                                                        </p:selectOneMenu>
                                                    </p:column>
                                                    <p:column style="text-align:center;">
                                                        <p:inputText value="#{badrsnSub.rst}"
                                                                     onkeyup="SYSTEM.verifyNum3(this,12,6)"
                                                                     onblur="SYSTEM.verifyNum3(this,12,6)"
                                                                     onchange="SYSTEM.verifyNum3(this,12,6)"
                                                                     style="width: 80px;" maxlength="50"
                                                                     />
                                                    </p:column>
                                                    <p:column style="text-align:center;">
                                                        <div style="display: flex;align-items: center;width: 105px;margin-right: -40px;">
                                                            <div onclick="document.getElementById('tabView:editForm:onMsruntSelect#{status.index}').click()">
                                                                <h:outputText escape="false"
                                                                              value="#{badrsnSub.msruntName}"
                                                                              id="msruntVal#{status.index}"
                                                                              style="width: 82px;display: block;height: 16px;word-break: break-all"
                                                                              readonly="true"
                                                                              styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all"/>
                                                            </div>
                                                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                                                           process="@this"
                                                                           id="onMsruntSelect#{status.index}"
                                                                           style="position: relative;left: -20px;top:0px;"
                                                                           oncomplete="PF('MsruntSelectPanel#{status.index}').show()"
                                                            >
                                                                <f:setPropertyActionListener
                                                                        target="#{mgrbean.occhethBadrsnItem}"
                                                                        value="#{badrsnSub}"/>
                                                            </p:commandLink>
                                                        </div>
                                                        <p:overlayPanel id="msruntSelectPanel#{status.index}"
                                                                        for="msruntVal#{status.index}"
                                                                        dynamic="false" style="width:158px;"
                                                                        widgetVar="MsruntSelectPanel#{status.index}"
                                                                        showCloseIcon="true">
                                                            <p:tree value="#{badrsnSub.msruntTree}" var="node"
                                                                    selectionMode="single"
                                                                    style="width: 130px;height: 140px;overflow-y: auto;"
                                                                    id="msruntSelectTree#{status.index}">
                                                                <p:ajax event="select"
                                                                        process="@this,:tabView:editForm:badrsnSubTable"
                                                                        listener="#{mgrbean.onMsruntSelect}"
                                                                        update=":tabView:editForm:msruntVal#{status.index}"
                                                                        oncomplete="PF('MsruntSelectPanel#{status.index}').hide()">
                                                                </p:ajax>
                                                                <p:treeNode>
                                                                    <div style="width:100px;">
                                                                        <h:outputText value="#{node.codeName}"
                                                                                      escape="false"/>
                                                                    </div>
                                                                </p:treeNode>
                                                            </p:tree>
                                                        </p:overlayPanel>
                                                    </p:column>
                                                    <p:column headerText="操作" style="width:80px;">
                                                        <p:spacer width="5"/>
                                                        <p:commandLink value="删除"
                                                                       update="badrsnSubTable"
                                                                       process="@this,badrsnSubTable"
                                                                       action="#{mgrbean.delBadrsnItemAction}"
                                                                       oncomplete="removeExtraTreePanel()">
                                                            <f:setPropertyActionListener
                                                                    target="#{mgrbean.occhethBadrsnItem}"
                                                                    value="#{badrsnSub}"/>
                                                            <p:confirm header="消息确认框" message="确定要删除吗？"
                                                                       icon="ui-icon-alert"/>
                                                        </p:commandLink>
                                                    </p:column>
                                                </p:row>
                                            </c:forEach>
                                            <p:row rendered="#{mgrbean.occhethBpBadrsnSub.bulletBadrsnItems.size()==0}">
                                                <p:column style="padding-right:3px;" colspan="6">
                                                    <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                                </p:column>
                                            </p:row>
                                        </p:panelGrid>
                                    </div>
                                </div>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:outputPanel>
            </p:dialog>

            <p:dialog header="选择职业病危害因素" widgetVar="BadRsnDialog" id="badRsnDialog"
                      resizable="false" width="800" height="413" modal="true">
                <h:panelGrid columns="10" id="searchPanel">
                    <p:inputText style="visibility: hidden;width: 0"/>
                    <p:outputLabel value="危害因素大类：" styleClass="zwx_dialog_font"/>
                    <p:selectOneMenu value="#{mgrbean.firstBadRsnId}" id="firsBadRsn"
                                     style="width:150px;">
                        <f:selectItem itemValue="" itemLabel="--全部--"/>
                        <f:selectItems value="#{mgrbean.firsBadRsntList}" var="itm" itemValue="#{itm.rid}"
                                       itemLabel="#{itm.codeName}"/>
                        <p:ajax event="change" listener="#{mgrbean.badRsnSearch}" process="@this,searchPanel"
                                update="selectedBadRsnTable"/>
                    </p:selectOneMenu>
                    <p:spacer width="5"></p:spacer>
                    <p:outputLabel value="名称/拼音码：" styleClass="zwx_dialog_font"/>
                    <p:inputText id="pym" value="#{mgrbean.searchNamOrPy}" style="width: 180px;" maxlength="50">
                        <p:ajax event="keyup" update="selectedBadRsnTable" process="@this,searchPanel"
                                listener="#{mgrbean.badRsnSearch}"/>
                    </p:inputText>
                </h:panelGrid>
                <p:dataTable var="itm"
                             value="#{mgrbean.limitVals}" id="selectedBadRsnTable"
                             paginator="true" rows="10" emptyMessage="没有数据！"
                             rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             paginatorPosition="bottom">
                    <p:column headerText="选择" style="width:45px;text-align:center">
                        <p:commandLink value="选择"
                                       process="@this"
                                       action="#{mgrbean.chooseBadRsn}"
                                       rendered="#{!itm.ifParent}" oncomplete="PF('BadRsnDialog').hide();">
                            <f:setPropertyActionListener value="#{itm}"
                                                         target="#{mgrbean.selBadRsn}"/>
                        </p:commandLink>
                    </p:column>
                    <p:column headerText="危害因素" style="padding-left:5px;">
                        <h:outputText value="#{itm.badRsnName}" escape="false"
                                      style="padding-left: #{itm.ifParent ? '0': '10'}px;"/>
                    </p:column>
                </p:dataTable>
            </p:dialog>
        </p:outputPanel>
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel
                                value="（支持附件格式为：PDF）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}"
                                invalidSizeMessage="文件大小不能超过1M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="1048576"
                                invalidFileMessage="无效的文件类型！只能上传pdf类型文件"
                                allowTypes="/(\.|\/)(pdf)$/" update="@this"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog header="附件上传" widgetVar="FileSignDialog" id="fileSignDialog" resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel
                                value="（支持附件格式为：PDF）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileSingUpload}"
                                invalidSizeMessage="文件大小不能超过1M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="1048576"
                                invalidFileMessage="无效的文件类型！只能上传pdf类型文件"
                                allowTypes="/(\.|\/)(pdf)$/" update="@this"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog id="shadeTip" widgetVar="ShadeTip" modal="true" height="20" resizable="false" showHeader="false"
                  closeOnEscape="true" styleClass="shadeTip">
            <p:panelGrid>
                <p:row style="border:1px solid transparent !important;">
                    <p:column style="border: transparent !important;">
                        <p:graphicImage url="/resources/images/main/loading5.gif" style="margin-top: 4px;"/>
                    </p:column>
                    <p:column style="border: transparent !important;">
                        <h:outputText style="color: #FFFFFF;font-size: 15px;" value="生成文书中，请等待..."/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
    </h:form>
</ui:composition>