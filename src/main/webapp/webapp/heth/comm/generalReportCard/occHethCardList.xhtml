<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.OccHethCardListBean"-->
    <ui:param name="mgrbean" value="#{occHethCardListBean}"/>
    <ui:param name="editPage" value="/webapp/heth/comm/generalReportCard/occHethCardEdit.xhtml"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/generalReportCard/occHethCardView.xhtml"/>
    <!-- 无按钮详情 -->
    <ui:param name="otherPage" value="/webapp/heth/comm/generalReportCard/occHethCardOther.xhtml" />
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业卫生技术服务信息报送卡填报"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable" oncomplete="datatableOffClick()"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="服务单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;" id="zoneCol">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.searchZoneList}"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       ifShowTrash="#{true}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="服务单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 275px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="出具技术服务报告时间："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptBeginDate}"
                                              endDate="#{mgrbean.searchRptEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;height:38px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchStateList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="0" itemLabel="待提交"/>
                    <f:selectItem itemValue="1" itemLabel="已提交"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="服务单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column headerText="服务单位名称" style="width: 520px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="出具技术服务报告时间" style="width: 140px;text-align: center;">
            <h:outputLabel value="#{itm[3]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="数据来源" style="width: 100px;padding-left: 8px;text-align: center;">
            <h:outputText value="检测任务" rendered="#{itm[6] eq 2}"/>
            <h:outputText value="国家导入" rendered="#{itm[6] eq 1}"/>
            <h:outputText value="系统录入" rendered="#{itm[6] eq 0}"/>
        </p:column>
        <p:column headerText="状态" style="width: 75px;text-align: center;">
            <h:outputText value="待提交" rendered="#{0 eq itm[4]}"/>
            <h:outputText value="已提交" rendered="#{1 eq itm[4]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView" rendered="#{itm[6] ne 2 or 1 eq itm[4]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
                <f:setPropertyActionListener value="true" target="#{mgrbean.ifView}"/>
            </p:commandLink>
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" rendered="#{1 ne itm[4] and itm[6] eq 2}"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
                <f:setPropertyActionListener value="#{itm[7]}" target="#{mgrbean.contractId}"/>
                <f:setPropertyActionListener value="false" target="#{mgrbean.ifView}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[6] ne 2 or 1 eq itm[4]}"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView" rendered="#{1 ne itm[4] and itm[6] ne 2}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>
