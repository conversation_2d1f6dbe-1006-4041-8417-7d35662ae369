<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.OccHethCardBaseBean"-->
    <!-- 文书 -->
    <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
        <p:row>
            <p:column colspan="2"><span>本环节文书</span></p:column>
        </p:row>
        <p:row>
            <p:column style="padding: 0;" colspan="2">
                <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 250px;">
                <h:outputText value="《职业卫生技术服务信息报送卡》"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.occhethCard.annexPath}')"
                                     rendered="#{mgrbean.occhethCard.annexPath != null}">
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 400px;">
                <h:outputText value="《职业卫生技术服务信息报告首页（含质控号）、签发页》（盖章）"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.occhethCard.signAddress}')"
                                     rendered="#{mgrbean.occhethCard.signAddress != null}">
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.occhethCard.state == 1 and mgrbean.occhethCard.source == 2}">
            <p:column style="width: 400px;">
                <h:outputText value="质控二维码"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="下载" action="#{mgrbean.beforeDownloadQrCode}"
                                     process="@this" />
                    <p:commandButton style="display: none;" id="qrCodeDownLoad" icon="ui-icon-document" ajax="false"
                                     onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <p:fileDownload value="#{mgrbean.getQRCodeStreamedContent()}"/>
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <div id="occHethCard">
        <!-- 标题 -->
        <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;">
            <p:row>
                <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                    <h:outputText value="职业卫生技术服务信息报送卡"
                                  style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="noBorder" style="text-align: left;">
                    <h:outputText value="报告卡编码："/>
                    <p:outputLabel value="自动生成" style="color:gray;"
                                   rendered="#{mgrbean.occhethCard.rid == null}"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.cardNo}"
                                   rendered="#{mgrbean.occhethCard.rid != null}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 机构信息 -->
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="orgInfoPanel">
            <p:row>
                <p:column colspan="4">
                    <p:outputLabel value="机构信息" styleClass="aloneTitleTable"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.orgName}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="法定代表人" style="padding-right: 16px; "/>
                    <br/>
                    <p:outputLabel value="（或主要负责人）："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.orgFz}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="注册地址："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.orgAddr}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="机构资质证书编号："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel
                            value="#{mgrbean.occhethCard.certNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="项目负责人："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.proFz}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="联系电话："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.proLinktel}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="资质业务范围："/>
                </p:column>
                <p:column style="padding-left: 8px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.occhethCard.occhethCardItemsStr}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 参与人员信息 -->
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="occhethCardPsnPanel">
            <p:row>
                <p:column style="border-bottom-color: transparent;" colspan="2">
                    <p:outputLabel value="参与人员信息"
                                   style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"/>
                    <p:commandButton value="查看现场打卡记录"  rendered="#{ifHasRecord eq 1 and mgrbean.occhethCard.source eq 2}" resetValues="true"
                                     action="#{mgrbean.showAppPsnDiag}" update=":tabView:viewForm:appClockDialog">
                    </p:commandButton>
                </p:column>
            </p:row>
            <p:row>
                <p:column colspan="2">
                    <p:dataTable id="occhethCardPsnTable" paginatorPosition="bottom"
                                 value="#{mgrbean.occhethCard.occhethCardPsnList}"
                                 widgetVar="occhethCardPsnTable" var="occhethCardPsn"
                                 emptyMessage="没有数据！" rowIndexVar="R">
                        <!--@elvariable id="R" type="java.lang.Integer"-->
                        <p:column headerText="序号" style="width:50px;text-align: center;">
                            <p:outputLabel value="#{R+1}"/>
                        </p:column>
                        <p:column headerText="姓名" style="width:150px;text-align: center;">
                            <p:outputLabel value="#{occhethCardPsn.psnName}"/>
                        </p:column>
                        <p:column headerText="承担的服务事项" style="text-align: center;">
                            <p:outputLabel value="#{occhethCardPsn.occhethCardItemStr}"/>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 服务的用人单位信息 -->
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="crptInfoPanel">
            <p:row>
                <p:column colspan="4">
                    <p:outputLabel value="服务的用人单位信息" styleClass="aloneTitleTable"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="单位名称："/>
                </p:column>
                <p:column styleClass="column_content">
                    <div style="display: flex;align-items: center;">
                        <p:outputLabel value="#{mgrbean.occhethCard.crptName}"/>
                    </div>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="统一社会信用代码："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.creditCode}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="注册地址："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.address}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="企业规模："/>
                </p:column>
                <p:column style="padding-left: 8px;" >
                    <p:outputLabel value="#{mgrbean.occhethCard.fkByCrptSizeId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="行业类别："/>
                </p:column>
                <p:column styleClass="column_content" colspan="3">
                    <p:outputLabel value="#{mgrbean.occhethCard.fkByIndusTypeId.codePath}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="联系人："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.safeposition}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="联系电话："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.safephone}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" colspan="4" style="text-align: left !important;">
                    <p:dataTable id="occhethCardZoneTable" paginatorPosition="bottom"
                                 value="#{mgrbean.occhethCard.occhethCardZoneList}"
                                 widgetVar="occhethCardZoneTable" var="occhethCardZone"
                                 emptyMessage="没有数据！" rowIndexVar="R">
                        <!--@elvariable id="R" type="java.lang.Integer"-->
                        <p:column headerText="技术服务地址" style="text-align: center;">
                            <p:outputLabel value="#{occhethCardZone.fullName}"/>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 技术服务信息 -->
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicesInfoPanel">
            <p:row>
                <p:column colspan="4">
                    <p:outputLabel value="技术服务信息" styleClass="aloneTitleTable"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="技术服务领域："/>
                </p:column>
                <p:column style="padding-left: 8px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.occhethCard.occhethCardServiceStr}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="现场调查时间："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel
                            value="#{mgrbean.occhethCard.investStartDate} ~ #{mgrbean.occhethCard.investEndDate}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="现场采样/检测时间："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel
                            value="#{mgrbean.occhethCard.jcStartDate} ~ #{mgrbean.occhethCard.jcEndDate}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="出具技术服务报告" style="padding-right: 10px;"/>
                    <br/>
                    <p:outputLabel value="时间："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.rptDate}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="技术服务报告编号："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.rptNo}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!--技术服务结果-->
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="serviceInfoPanel">
            <p:row>
                <p:column colspan="2">
                    <p:outputLabel value="技术服务结果" styleClass="aloneTitleTable"/>
                </p:column>
            </p:row>
            <p:row rendered="#{((mgrbean.selServiceResultExtends1 eq '1'
                                or mgrbean.selServiceResultExtends1 eq '2'
                                or mgrbean.selServiceResultExtends1 eq '3') or (mgrbean.occhethCard.hasBadrsnJc or mgrbean.occhethCard.hasStatusPj or mgrbean.occhethCard.hasJcUse or mgrbean.occhethCard.hasJcInst)) and mgrbean.occhethCard.checkNo ne null}">
                <p:column styleClass="writeSortInfo" style="padding-left: 5px;" colspan="2">
                    <p:outputLabel value="#{mgrbean.serviceResultMsg}"
                                   rendered="#{mgrbean.serviceResultMsg ne null}"/>
                    <p:outputLabel value="，质控编号："/>
                    <p:outputLabel value="#{mgrbean.occhethCard.checkNo}" />
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.occhethCard.hasBadrsnJc or  mgrbean.occhethCard.hasStatusPj}">
                <p:column styleClass="cs-break-word" style="padding-left: 15px;border-bottom-color: transparent;" colspan="2">
                    <p:outputLabel value="共检测岗位或者工种数量："/>
                    <p:outputLabel value="#{mgrbean.occhethCard.jcPostNum}" rendered="#{mgrbean.occhethCard.hasBadrsnJc}"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.pjPostNum}" rendered="#{mgrbean.occhethCard.hasStatusPj}"/>
                    <p:outputLabel value="（个），超标岗位/工种数量："/>
                    <p:outputLabel value="#{mgrbean.occhethCard.jcOverNum}" rendered="#{mgrbean.occhethCard.hasBadrsnJc}"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.pjOverNum}" rendered="#{mgrbean.occhethCard.hasStatusPj}"/>
                    <p:outputLabel value="（个），超标危害因素类型："/>
                    <p:outputLabel value="#{mgrbean.occhethCard.selectedBadrsnNames}" rendered="#{not empty mgrbean.occhethCard.selectedBadrsnNames}"/>
                    <p:outputLabel value="无" rendered="#{empty mgrbean.occhethCard.selectedBadrsnNames}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.occhethCard.hasBadrsnJc or  mgrbean.occhethCard.hasStatusPj}">
                <p:column colspan="2" style="padding-left: 15px;border-bottom-color: transparent;">
                    <p:outputPanel styleClass="cs-break-word"
                                   style="display: flex;align-items: center;flex-wrap: wrap;">
                        <p:outputLabel value="职业病危害因素："/>
                        <p:outputLabel value="#{mgrbean.occhethCard.selBadrsnListStr}"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.occhethCard.hasBadrsnJc or  mgrbean.occhethCard.hasStatusPj}">
                <p:column colspan="2" style="padding-left: 15px;border-bottom-color: transparent;">
                    <p:outputLabel value="职业病危害因素检测结果"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.occhethCard.hasBadrsnJc or  mgrbean.occhethCard.hasStatusPj}">
                <p:column styleClass="column_title" colspan="2" style="text-align: left;">
                    <div style="padding-right: 3px; max-height: 465px; overflow-y: auto; position: relative;">

                        <div  style="position: sticky; top: 0; z-index: 100; background-color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <p:panelGrid style="width:100%;" id="badrsnJcDataTableHeader">
                                <p:row>
                                    <p:column styleClass="ui-state-default header-col" data-index="0"
                                              style="text-align:center;padding-right:3px;width:110px;">
                                        <p:outputLabel value="职业病危害因素"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="1"
                                              style="text-align:center;padding-right:3px;width:90px;">
                                        <p:outputLabel value="接触危害人数"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="2"
                                              style="text-align:center;padding-right:3px;width:80px;">
                                        <p:outputLabel value="岗位/工种数"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="3"
                                              style="text-align:center;padding-right:3px;width:100px;">
                                        <p:outputLabel value="超标岗位/工种数"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="4"
                                              style="text-align:center;padding-right:3px;width:110px;">
                                        <p:outputLabel value="超标岗位/工种名称"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="5"
                                              style="text-align:center;padding-right:3px;width:80px;">
                                        <p:outputLabel value="超标检测项目"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="6"
                                              style="text-align:center;padding-right:3px;width:80px;">
                                        <p:outputLabel value="超标检测指标"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="7"
                                              style="text-align:center;padding-right:3px;width:70px;">
                                        <p:outputLabel value="超标检测值"/>
                                    </p:column>
                                    <p:column styleClass="ui-state-default header-col" data-index="8"
                                              style="text-align:center;padding-right:3px;width:60px;">
                                        <p:outputLabel value="计量单位"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </div>
                        <!-- 固定表头 -->
                        <div style="margin-top: -42px;">
                            <p:panelGrid style="width:100%;" id="badrsnJcDataTable">
                                <f:facet name="header">
                                    <p:row>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:110px;">
                                            <p:outputLabel value="职业病危害因素"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default "
                                                  style="text-align:center;padding-right:3px;width:90px;">
                                            <p:outputLabel value="接触危害人数"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default "
                                                  style="text-align:center;padding-right:3px;width:80px;">
                                            <p:outputLabel value="岗位/工种数"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:100px;">
                                            <p:outputLabel value="超标岗位/工种数"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:110px;">
                                            <p:outputLabel value="超标岗位/工种名称"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:80px;">
                                            <p:outputLabel value="超标检测项目"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:80px;">
                                            <p:outputLabel value="超标检测指标"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:70px;">
                                            <p:outputLabel value="超标检测值"/>
                                        </p:column>
                                        <p:column styleClass="ui-state-default"
                                                  style="text-align:center;padding-right:3px;width:60px;">
                                            <p:outputLabel value="计量单位"/>
                                        </p:column>
                                    </p:row>
                                </f:facet>
                                <c:forEach var="badrsnSub" items="#{mgrbean.badrsnSubList}"
                                           varStatus="status">
                                    <c:forEach var="badrsnItem" items="#{badrsnSub.badrsnItems}"
                                               varStatus="status">
                                        <p:row>
                                            <p:column style="padding-right:3px;text-align: center;" styleClass="cs-break-word"
                                                      rowspan="#{badrsnItem.badrsnRowspan}"
                                                      rendered="#{null ne badrsnItem.badrsnName}">
                                                <p:outputLabel value="#{badrsnItem.badrsnName}" escape="false"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                      rowspan="#{badrsnItem.badrsnRowspan}"
                                                      rendered="#{null ne badrsnItem.badrsnName}">
                                                <p:outputLabel
                                                        value="#{badrsnItem.badrsnNum==0?'':badrsnItem.badrsnNum}"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                      rowspan="#{badrsnItem.badrsnRowspan}"
                                                      rendered="#{null ne badrsnItem.postNum}">
                                                <p:outputLabel value="#{badrsnItem.postNum==0?'':badrsnItem.postNum}"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                      rowspan="#{badrsnItem.badrsnRowspan}"
                                                      rendered="#{null ne badrsnItem.num}">
                                                <p:outputLabel value="#{badrsnItem.num}"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                      rowspan="#{badrsnItem.postRowspan}"
                                                      rendered="#{null ne badrsnItem.postNameShow}">
                                                <p:outputLabel value="#{badrsnItem.postNameShow}"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word"
                                                      rowspan="#{badrsnItem.itemsRowspan}"
                                                      rendered="#{null ne badrsnItem.itermName}">
                                                <p:outputLabel value="#{badrsnItem.itermName}" escape="false"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word">
                                                <p:outputLabel value="#{badrsnItem.fkByIndexId.codeName}"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word">
                                                <p:outputLabel value="#{badrsnItem.rst}"/>
                                            </p:column>
                                            <p:column style="text-align:center;padding-right:3px;" styleClass="cs-break-word">
                                                <p:outputLabel value="#{badrsnItem.fkByMsruntId.codeName}"
                                                               escape="false"/>
                                            </p:column>
                                        </p:row>
                                    </c:forEach>
                                </c:forEach>
                                <p:row rendered="#{mgrbean.badrsnSubList == null or mgrbean.badrsnSubList.size() eq 0}">
                                    <p:column style="padding-right:3px;" colspan="10">
                                        <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </div>
                    </div>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.occhethCard.hasJcInst}">
                <p:column style="width: 30%;">
                    <p:outputLabel value="开展职业病防护设备设施防护效果检测"/>
                </p:column>
                <p:column style="padding: 6px 6px;" >
                    <p:outputLabel value="检测设备设施数量：" style="line-height:26px;"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.jcInstNum}"/>
                    <p:outputLabel value="台（套），"/>
                    <br/>
                    <p:outputLabel value="检测结果不合格的设备设施数量：" style="line-height:26px;"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.jcNotHgInstNum}"/>
                    <p:outputLabel value="台（套），不合格的设备设施名称："/>
                    <p:outputLabel value="#{mgrbean.occhethCard.notHgInstName}" style="line-height:26px;"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.occhethCard.hasJcUse}">
                <p:column style="width: 30%;">
                    <p:outputLabel value="开展职业病防护用品防护效果检测"/>
                </p:column>
                <p:column style="padding: 6px 6px;" >
                    <p:outputLabel value="检测防护用品数量：" style="line-height:26px;"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.jcUseNum}"/>
                    <p:outputLabel value="个（件），"/>
                    <br/>
                    <p:outputLabel value="结果不合格的防护用品数量：" style="line-height:26px;"/>
                    <p:outputLabel value="#{mgrbean.occhethCard.jcNotHgUseNum}"/>
                    <p:outputLabel value="个（件），不合格的防护用品名称："/>
                    <p:outputLabel value="#{mgrbean.occhethCard.notHgUseName}" style="line-height:26px;"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 报告信息 -->
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;" id="rptInfoPanel">
            <p:row>
                <p:column colspan="4">
                    <p:outputLabel value="报告信息" styleClass="aloneTitleTable"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="填报单位："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.fillUnitName}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="单位负责人："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.orgFzMan}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="填表人："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.fillFormPsn}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="填表人联系电话："/>
                </p:column>
                <p:column styleClass="column_content">
                    <p:outputLabel value="#{mgrbean.occhethCard.fillLink}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title">
                    <p:outputLabel value="填表日期："/>
                </p:column>
                <p:column style="padding-left: 8px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.occhethCard.fillDate}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </div>
    <!-- 现场打卡记录 -->
    <p:dialog header="现场打卡记录" widgetVar="AppClockDialog" id="appClockDialog"
              resizable="false" width="850" height="350" modal="true">
        <p:dataTable var="item" value="#{mgrbean.appClockList}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                     rowsPerPageTemplate="10,20,50" id="appClockTable" lazy="true" emptyMessage="没有您要找的记录！">
            <p:column headerText="序号" width="35" style="text-align: center;">
                <h:outputText value="#{item[0]}" />
            </p:column>
            <p:column headerText="服务类型" width="60" style="text-align: center;">
                <h:outputText value="现场调查" rendered="#{item[1] eq 1}" />
                <h:outputText value="采样测量"  rendered="#{item[1] eq 2}"/>
            </p:column>
            <p:column headerText="现场服务人员" width="200" >
                <h:outputText value="#{item[2]}" id="psns" styleClass="zwx-tooltip"/>
                <p:tooltip for="psns" style="max-width:260px;">
                    <p:outputLabel value="#{item[2]}" escape="false"/>
                </p:tooltip>
            </p:column>
            <p:column headerText="签到时间" width="160" style="text-align: center;">
                <p:outputPanel id="signInTime" styleClass="zwx-tooltip">
                    <h:outputText value="#{item[3]}"/>
                    <img style="vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;cursor: pointer; display: #{item[3] eq null ? 'none' : 'inline'}"
                          src="/resources/images/location_icon.svg"/>
                </p:outputPanel>
                <p:tooltip for="signInTime" style="max-width:260px;">
                    <p:outputLabel value="#{item[5]}" escape="false"/>
                </p:tooltip>
            </p:column>
            <p:column headerText="签退时间" width="160" style="text-align: center;">
                <p:outputPanel id="signOutTime" styleClass="zwx-tooltip">
                    <h:outputText value="#{item[4]}"/>
                    <img style="vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;cursor: pointer;display: #{item[4] eq null ? 'none' : 'inline'}"
                      src="/resources/images/location_icon.svg" />
                </p:outputPanel>
                <p:tooltip for="signOutTime" style="max-width:260px;">
                    <p:outputLabel value="#{item[6]}" escape="false"/>
                </p:tooltip>
            </p:column>
            <p:column headerText="现场照片" style="text-align: center;">
                <p:commandLink value="查看（#{item[7]}）"
                                 action="#{mgrbean.toAnnexDialogView}">
                    <f:setPropertyActionListener target="#{mgrbean.processRid}" value="#{item[8]}"/>
                    <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="0"/>
                </p:commandLink>
            </p:column>
        </p:dataTable>
    </p:dialog>
    <!-- 附件预览 -->
    <p:dialog id="annexViewDialog" widgetVar="AnnexViewDialog"
              styleClass="annex_view_dialog" resizable="false" header="附件预览"
              width="900" modal="true">
        <p:outputPanel id="annexViewPanel">
            <div style="width:100%;height:500px;">
                <table class="annex_content_table">
                    <tr>
                        <td class="annex_link">
                            <p:commandLink styleClass="annex_pre"
                                           update="annexViewPanel" process="@this"
                                           rendered="#{mgrbean.contractAnnex[2]!=null}"
                                           action="#{mgrbean.toAnnexDialogView}">
                                <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="#{mgrbean.contractAnnex[2]}"/>
                            </p:commandLink>
                        </td>
                        <td style="text-align:center">
                            <h:outputText value="#{mgrbean.fileTemp.filePath}" escape="false"/>
                        </td>
                        <td class="annex_link">
                            <p:commandLink styleClass="annex_next"  update="annexViewPanel" process="@this"
                                           rendered="#{mgrbean.contractAnnex[3]!=null}" action="#{mgrbean.toAnnexDialogView}">
                                <f:setPropertyActionListener target="#{mgrbean.annexViewIndex}" value="#{mgrbean.contractAnnex[3]}"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </div>
            <p:outputPanel style="position: absolute;bottom: 0;width: 100%;" >
                <table class="annex_info_table">
                    <tr>
                        <td>
                            照片类型：<h:outputText value="入厂照片" rendered="#{mgrbean.contractAnnex[1] eq 1}"/>
                            <h:outputText value="现场工作照片" rendered="#{mgrbean.contractAnnex[1] eq 2}"/>
                            <h:outputText value="出厂照片" rendered="#{mgrbean.contractAnnex[1] eq 3}"/>
                        </td>
                    </tr>
                </table>
            </p:outputPanel>
        </p:outputPanel>
    </p:dialog>
</ui:composition>