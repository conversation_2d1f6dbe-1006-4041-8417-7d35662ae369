<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.OccHethCardListBean"-->
    <style>
        .aloneTitleTable {
            color: #334B9A;
            font-weight: bold;
            padding-left: 6px;
        }

        .column_title {
            width: 15% !important;
        }

        .column_content {
            width: 35% !important;
            padding-left: 8px !important;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[
        function getViewDownloadFileClick() {
            document.getElementById("tabView:otherForm:qrCodeDownLoad").click();
        }
        //]]>
    </script>
    <h:form id="otherForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <!-- 标题 -->
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="职业卫生技术服务信息报送卡详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮组 -->
        <!-- 正文 -->
        <p:outputPanel styleClass="businessInfo" style="margin-top:40px;background: rgb(252, 253, 253);">
            <ui:include src="occHethCardInfo.xhtml"/>
        </p:outputPanel>

    </h:form>
</ui:composition>