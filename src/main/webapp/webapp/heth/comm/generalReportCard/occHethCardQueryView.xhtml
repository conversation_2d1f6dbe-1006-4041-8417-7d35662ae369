<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <style>
        .aloneTitleTable {
            color: #334B9A;
            font-weight: bold;
            padding-left: 6px;
        }

        .column_title {
            width: 15% !important;
        }

        .column_content {
            width: 35% !important;
            padding-left: 8px !important;
            word-wrap: break-word;
            word-break: break-all;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[
        function getViewDownloadFileClick() {
            document.getElementById("tabView:viewForm:qrCodeDownLoad").click();
        }
        //]]>
    </script>
    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <!-- 标题 -->
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="职业卫生技术服务信息报送卡详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮组 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="btnPanel">
            <h:panelGrid columns="15" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}"
                                 update=":tabView" process="@this"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="btnPanel"/>
        <!-- 正文 -->
        <p:outputPanel styleClass="businessInfo" style="margin-top:40px;background: rgb(252, 253, 253);">
            <ui:param name="ifHasRecord" value="1"/>
            <ui:include src="occHethCardInfo.xhtml"/>
        </p:outputPanel>

    </h:form>
</ui:composition>