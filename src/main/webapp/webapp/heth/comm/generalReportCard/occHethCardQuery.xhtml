<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.OccHethCardQueryBean"-->
    <ui:param name="mgrbean" value="#{occHethCardQueryBean}"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/generalReportCard/occHethCardQueryView.xhtml"/>
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
            .annex_info_table {
                width: 100%;
                background: #2D2D2D;
                box-shadow: 0 -2 px 0 0 rgba(5, 5, 5, 0 .50), 0 -1 px 0 0 rgba(255, 255, 255, 0 .41);
                color: #FFFFFF;
                font-size: 12px;
                font-family: microsoft yahei;
            }
            .annex_info_table tr {
                height: 25px;
            }

            .annex_info_table td {
                text-align: left;
                padding-left: 20px;
            }

            .annex_view_dialog  .ui-dialog-content {
                background-color: #1F1F1F !important;
                border: none;
                box-shadow: 0 11px 7px 0 rgba(0, 0, 0, 0.25);
                padding: 0;
                position: relative;
            }
            .annex_view_dialog  .ui-dialog-content {
                background-color: #1F1F1F !important;
                border: none;
                box-shadow: 0 11px 7px 0 rgba(0, 0, 0, 0.25);
                padding: 0;
                position: relative;
            }

            .annex_content_table {
                width: 100%;
                height: 475px;
            }

            .annex_link {
                width: 50px;
            }

            .annex_link a {
                width: 28px;
                height: 48px;
                display: block;
            }

            .annex_pre {
                background-image: url("/resources/images/heth/annex_pre.png");
                margin-left: 20px;
            }

            .annex_next {
                background-image: url("/resources/images/heth/annex_next.png");
                margin-right: 20px;
            }
            .fullscreen {
                height:32px;
                width:786px;
                background:#000000;
                position: absolute;
                top:0px;
                z-index:999;
                opacity:0.8;
                display: none;
            }

            .fullscreen span{
                width:16px;
                height:16px;
                display: block;
                background: url("/resources/images/heth/fullscreen.png") no-repeat;
                margin:8px auto;
                cursor: pointer;
            }
            .annex_view_dialog .ui-dialog-titlebar {
                background-image: linear-gradient(to bottom, #f5f5f5 1%, #eeeeee 98%) !important;
                border-bottom: solid 1px #cdcdcd !important;
            }
            .annex_view_dialog .ui-dialog-title {
                color: #444444;
                font-weight: bold;
            }
            .annex_view_dialog .ui-icon {
                background-image: url("/resources/primeui/themes/redmond/images/ui-icons_469bdd_256x240.png") !important;
            }
            .annex_view_dialog .ui-dialog-content {
                overflow: hidden;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业卫生技术服务信息报送卡查询"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable" oncomplete="datatableOffClick()"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="报告单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:260px;" >
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="报告单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 260px;">
                <p:inputText id="searchOrgName" value="#{mgrbean.searchOrgName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="服务单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>

        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="技术服务领域："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectServiceNames}"
                                        selectedIds="#{mgrbean.selectServiceRids}"
                                        simpleCodeList="#{mgrbean.businessScopeList}"
                                        height="160" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="出具技术服务报告时间："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptBeginDate}"
                                              endDate="#{mgrbean.searchRptEndDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
            <p:column style="text-align:right;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:selectManyCheckbox value="#{mgrbean.searchStateList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="0" itemLabel="待提交"/>
                    <f:selectItem itemValue="1" itemLabel="已提交"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <!--<p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="数据来源："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchSourceList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="0" itemLabel="系统录入"/>
                    <f:selectItem itemValue="1" itemLabel="国家导入"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>-->
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="报告单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column headerText="报告单位名称" style="width: 360px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="服务单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[3]}" escape="false"/>
        </p:column>
        <p:column headerText="服务单位名称" style="width: 360px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="出具技术服务报告时间" style="width: 140px;text-align: center;">
            <h:outputLabel value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="数据来源" style="width: 75px;text-align: center;">
            <h:outputText value="系统录入" rendered="#{0 eq itm[9]}"/>
            <h:outputText value="国家导入" rendered="#{1 eq itm[9]}"/>
            <h:outputText value="检测任务" rendered="#{2 eq itm[9]}"/>
        </p:column>
        <p:column headerText="状态" style="width: 75px;text-align: center;">
            <h:outputText value="待提交" rendered="#{0 eq itm[6]}"/>
            <h:outputText value="已提交" rendered="#{1 eq itm[6]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
                <f:setPropertyActionListener value="#{itm[10]}" target="#{mgrbean.contractId}"/>
                <f:setPropertyActionListener value="true" target="#{mgrbean.ifView}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog header="职业卫生技术服务信息报送卡导入" widgetVar="UploadFileDialog"
                  id="uploadFileDialog" resizable="false"
                  modal="true" width="800">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要文件上传！" styleClass="table-border-none"
                                      id="fileUpload"
                                      process="@this" fileUploadListener="#{mgrbean.importDataAction}"
                                      label="选择文件" invalidSizeMessage="文件大小不能超过200M!"
                                      validatorMessage="上传出错啦，请重新上传！"
                                      allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！"
                                      invalidFileMessage="只能上传xls、xlsx格式的文件！"
                                      previewWidth="120" cancelLabel="取消"
                                      uploadLabel="导入" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                      dragDropSupport="true" mode="advanced" sizeLimit="209715200"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>
