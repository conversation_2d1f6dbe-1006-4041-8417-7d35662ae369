<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TbTjPersonSearchBean"-->
	<ui:param name="mgrbean" value="#{tbTjPersonSearchBean}" />
	<!-- 是否启用光标定位功能 -->
	<ui:param name="onfocus" value="false" />
	<!-- 编辑页面 -->
	<ui:param name="editPage" value="/webapp/heth/comm/tbTjPersonBhkList.xhtml" />
	<!-- 详情页面 -->
	<ui:param name="viewPage" value="/webapp/heth/comm/tbTjBhkInfo.xhtml" />
	<ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
	<ui:param name="birthIfShow" value="false"></ui:param>

	<ui:define name="insertScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
		<script type="text/javascript">
			function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
		</script>
		<style type="text/css">
			.searchTime input{
				width:77px;
			}
		</style>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="个案查询" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="6"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					onclick="showStatus()" action="#{mgrbean.searchAction}"
					oncomplete="hideStatus()" update=":tabView:mainForm:dataTable"
					process="@this,mainGrid" />

				
				<p:commandButton value="导出"  icon="ui-icon-document"
					 action="#{mgrbean.tjExportItemAction}" id="exportBtn" process="@this" update="exportItemDialog"
					>
					<f:setPropertyActionListener target="#{mgrbean.exportFlag}" value="1"></f:setPropertyActionListener>
				</p:commandButton>
				<p:commandButton value="内部导出"  icon="ui-icon-document"
					action="#{mgrbean.tjExportItemAction}" id="exportBtnInside" process="@this" update="exportItemDialog" rendered="#{mgrbean.ifNbExport}"
					>
					<f:setPropertyActionListener target="#{mgrbean.exportFlag}" value="2"></f:setPropertyActionListener>
				</p:commandButton>
				<p:commandButton value="导出文件下载" icon="ui-icon-arrowthickstop-1-s" action="#{mgrbean.fileDownloadAction}"
								 process="@this" />
			</h:panelGrid>
			<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
            	<h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
             	<h:outputLabel value="仅能导出符合查询条件的数据" style="color:blue;"></h:outputLabel>
            </p:outputPanel>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width: 153px;height:38px;">
				<p:outputLabel value="人员姓名：" />
			</p:column>
			<p:column style="text-align:left;width: 280px;padding-left:8px;">
				<p:inputText value="#{mgrbean.conditionPO.searchPersonName}" maxlength="100"
					style="width: 180px;"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width: 153px;">
				<p:outputLabel value="证件类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 280px;">
				<p:selectOneMenu value="#{mgrbean.conditionPO.searchPsnType}" style="width:188px;">
					<f:selectItem itemLabel="--全部--" itemValue=""/>
					<f:selectItems value="#{mgrbean.cardTypeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"></f:selectItems>
				</p:selectOneMenu>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width: 153px;">
				<p:outputLabel value="证件号码：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText value="#{mgrbean.conditionPO.searchIDC}"
					maxlength="25" style="width: 180px;" placeholder="精确查询"/>
			</p:column>
		</p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <p:outputLabel value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
				<zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
									   zoneCode="#{mgrbean.conditionPO.searchEntrustCrptZoneGb}"
									   zoneName="#{mgrbean.conditionPO.searchEntrustCrptZoneName}"
									   zonePaddingLeft="0"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
				<p:outputLabel value="用工单位：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText value="#{mgrbean.conditionPO.searchEntrustCrptName}"
					maxlength="25" style="width: 180px;"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width: 120px;">
				<p:outputLabel value="体检类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<p:selectManyCheckbox value="#{mgrbean.conditionPO.searchBhkType}" >
                    <f:selectItem itemLabel="职业健康检查" itemValue="3"/>
                    <f:selectItem itemLabel="放射卫生健康检查" itemValue="4" />
                </p:selectManyCheckbox>
			</p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <p:outputLabel value="用人单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
				<zwx:ZoneSingleNewComp zoneList="#{mgrbean.crptZoneList}"
									   zoneCode="#{mgrbean.conditionPO.searchZoneCode}"
									   zoneName="#{mgrbean.searchZoneName}"
									   ifShowTrash="true" zonePaddingLeft="0"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
				<p:outputLabel value="用人单位：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText value="#{mgrbean.conditionPO.searchCrptName}"
					maxlength="25" style="width: 180px;"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;height:38px;">
				<h:outputText value="监测类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;" colspan="5">
				<p:selectManyCheckbox value="#{mgrbean.conditionPO.searchJcType}">
					<f:selectItem itemLabel="常规监测" itemValue="1"/>
					<f:selectItem itemLabel="主动监测" itemValue="2" />
				</p:selectManyCheckbox>
			</p:column>
        </p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:38px;" >
				<p:outputLabel value="体检日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.conditionPO.searchStartTime}"
											  endDate="#{mgrbean.conditionPO.searchEndTime}"  styleClass="searchTime"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<p:outputLabel value="报告出具日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.conditionPO.startRptPrintDate}"
											  endDate="#{mgrbean.conditionPO.endRptPrintDate}"  styleClass="searchTime"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<p:outputLabel value="报告日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.conditionPO.startCreateDate}"
											  endDate="#{mgrbean.conditionPO.endCreateDate}" styleClass="searchTime"/>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;height:38px;">
				<h:outputText value="在岗状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:2px;">
				<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
					selectedIds="#{mgrbean.conditionPO.selectOnGuardIds}"
					simpleCodeList="#{mgrbean.onGuardList}"
					height="200"></zwx:SimpleCodeManyComp>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="体检危害因素：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;">
				<table>
					<tr>
						<td style="padding: 0;border-color: transparent;">
							<p:inputText id="badRsnName" style="width: 180px;cursor: pointer;"
										 value="#{mgrbean.selectBadRsnNames}" readonly="true"
										 onclick="document.getElementById('tabView:mainForm:selBadRsnLink').click();"/>
						</td>
						<td style="border-color: transparent;">
							<p:commandLink styleClass="ui-icon ui-icon-search" id="selBadRsnLink"
										   style="position: relative;left: -28px !important;"
										   process="@this" action="#{mgrbean.selSimpleCode5007Action}">
								<p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCode5007Action}"
										process="@this" resetValues="true" update="badRsnName"/>
							</p:commandLink>
						</td>
						<!-- 清空 -->
						<td style="border-color: transparent;position: relative;left: -30px;">
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
										   process="@this" update="badRsnName" action="#{mgrbean.clearSimpleCode5007}">
							</p:commandLink>
						</td>
					</tr>
				</table>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;height:38px;">
				<h:outputText value="单危害因素结论：" />
			</p:column>
			<p:column style="text-align:left;padding-left:2px;" colspan="5">
				<zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
										selectedIds="#{mgrbean.conditionPO.searchSelBhkrstIds}"
										simpleCodeList="#{mgrbean.searchBhkrstList}"
										inputWidth="180" height="200"/>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width: 120px;height:38px;">
				<p:outputLabel value="年龄：" />
			</p:column>
			<p:column style="text-align:left;padding-left:2px; ">
				<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchAgeGrid">
					<p:inputText id="searchAgeName" value="#{mgrbean.selectAgeName}" style="width: 180px;" readonly="true" />
					<p:commandLink styleClass="ui-icon ui-icon-search" process="@this"  style="position: relative;left: -30px;"
								   oncomplete="PF('AgeOverlayPanel').show()" type="button" />
					<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" 
								   style="position: relative;left: -33px;" 
								   action="#{mgrbean.clearSelectAge}" process="@this" 
								   update="searchAgeGrid,ageOverlayPanel"/>
				</h:panelGrid>
				<p:remoteCommand name="hideAge" process="@this,ageOverlayPanel"
								 action="#{mgrbean.hideAgeAction}"
								 update="searchAgeGrid" />
				<p:overlayPanel id="ageOverlayPanel" for="searchAgeName"
								style="width:280px;" widgetVar="AgeOverlayPanel"
								showCloseIcon="true" onHide="hideAge();">
					<p:tree var="node" selectionMode="checkbox"
							value="#{mgrbean.ageSortTree}"
							style="width: 250px;height: 200px;overflow-y: auto;"
							selection="#{mgrbean.selectAges}">
						<p:treeNode>
							<p:outputLabel value="#{node.analyItem.codeName}" />
						</p:treeNode>
					</p:tree>
				</p:overlayPanel>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="接害工龄：" />
			</p:column>
			<p:column style="text-align:left;padding-left:2px;">
				<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchWorkGrid">
					<p:inputText id="searchWorkName" value="#{mgrbean.selectWorkName}" style="width: 180px;" readonly="true" />
					<p:commandLink styleClass="ui-icon ui-icon-search" process="@this"  style="position: relative;left: -30px;"
								   oncomplete="PF('WorkOverlayPanel').show()" type="button" />
					<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" 
								   style="position: relative;left: -33px;" 
								   action="#{mgrbean.clearSelectWorkAge}" process="@this" 
								   update="searchWorkGrid,workOverlayPanel"/>
				</h:panelGrid>
				<p:remoteCommand name="hideWork" process="@this,searchWorkGrid,workOverlayPanel"
								 action="#{mgrbean.hideWorkAgeAction}"
								 update="searchWorkGrid" />
				<p:overlayPanel id="workOverlayPanel" for="searchWorkName"
								style="width:280px;" widgetVar="WorkOverlayPanel"
								showCloseIcon="true" onHide="hideWork();">
					<p:tree var="node" selectionMode="checkbox"
							value="#{mgrbean.workSortTree}"
							style="width: 250px;height: 320px;overflow-y: auto;"
							selection="#{mgrbean.selectWorkAges}">
						<p:treeNode>
							<p:outputLabel value="#{node.analyItem.codeName}" />
						</p:treeNode>
					</p:tree>
				</p:overlayPanel>
			</p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="主检结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:2px;">
				<zwx:SimpleCodeManyComp codeName="#{mgrbean.conditionPO.searchSelMhkrstName}"
										selectedIds="#{mgrbean.conditionPO.searchSelMhkrstIds}"
										simpleCodeList="#{mgrbean.searchBhkrstList}"
										inputWidth="180" height="200"/>
            </p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="体检机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
            	<p:inputText value="#{mgrbean.searchUnitName}" 
            		style="width: 180px;" readonly="true" rendered="#{!mgrbean.ifAdmin}"/>
            	<!-- 弹出框 -->
            	<p:outputPanel rendered="#{mgrbean.ifAdmin}">
            		<table>
						<tr>
							<td style="padding: 0;border-color: transparent;">
								<p:inputText id="unitName"
									value="#{mgrbean.searchUnitName}"
									style="width: 180px;cursor: pointer;"
									onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
									readonly="true"/>
							</td>
							<td style="border-color: transparent;">
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="selUnitLink"
									action="#{mgrbean.selUnitAction}" process="@this"
									style="position: relative;left: -28px !important;">
									<p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
										resetValues="true" update="unitName" />
								</p:commandLink>
							</td>
							<!-- 清空 -->
							<td style="border-color: transparent;position: relative;left: -30px;">
								<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
	                                        process="@this" update="unitName" action="#{mgrbean.clearUnit}">
	                            </p:commandLink>
							</td>
						</tr>
					</table>
            	</p:outputPanel>
            </p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="体检项目：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;">
				<table>
					<tr>
						<td style="padding: 0;border-color: transparent;">
							<p:inputText id="searchItemNames"
								value="#{mgrbean.searchItemNames}"
								style="width: 180px;cursor: pointer;"
								onclick="document.getElementById('tabView:mainForm:selItemLink').click();"
								readonly="true"/>
						</td>
						<td style="border-color: transparent;">
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="selItemLink"
								action="#{mgrbean.selItemAction}" process="@this"
								style="position: relative;left: -28px !important;">
								<p:ajax event="dialogReturn" listener="#{mgrbean.onItemAction}" process="@this"
									resetValues="true" update="searchItemNames" />
							</p:commandLink>
						</td>
						<!-- 清空 -->
						<td style="border-color: transparent;position: relative;left: -30px;">
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                         process="@this" update="searchItemNames" action="#{mgrbean.clearSelectedItem}">
                            </p:commandLink>
						</td>
					</tr>
				</table>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;">
				<h:outputText value="档案份数：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;height:38px;">
				<p:inputText value="#{mgrbean.conditionPO.searchBhkNum}" 
            		style="width: 180px;" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"/>
			</p:column>
		</p:row>
	</ui:define>
	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="人员姓名" style="width:120px;text-align: center">
			<h:outputLabel value="#{itm[1]}" />
		</p:column>
		<p:column headerText="性别" style="width: 80px;text-align: center;">
			<h:outputLabel value="#{itm[2]}" />
		</p:column>
		<p:column headerText="证件号码" style="width: 110px;text-align: center;" rendered="#{mgrbean.conditionPO.searchPsnType!='3'}">
			<h:outputLabel value="#{itm[3]}" />
		</p:column>
		<p:column headerText="用人单位" style="width: 320px;">
			<h:outputLabel value="#{itm[7]}" />
		</p:column>
		<p:column headerText="初次建档日期" style="width: 100px;text-align: center;">
			<h:outputLabel value="#{itm[4]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			</h:outputLabel>
		</p:column>
		<p:column headerText="更新日期" style="width: 100px;text-align: center;">
			<h:outputLabel value="#{itm[5]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			</h:outputLabel>
		</p:column>
		<p:column headerText="档案份数" style="width: 80px;text-align: center;">
			<h:outputLabel value="#{itm[6]}" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 8px;">
			<p:commandLink value="详情" action="#{mgrbean.modInitAction}"
				update=":tabView" process="@this">
				<f:setPropertyActionListener value="#{itm[0]}"
					target="#{mgrbean.personRid}" />
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm[8]>=1}" />
			<p:commandLink value="疑似职业病"
				action="#{mgrbean.zybInitAction}" rendered="#{itm[8]>=1}"
				update=":tabView:mainForm:yszybDialog" process="@this"
				oncomplete="PF('YszybDialog').show()">
				<f:setPropertyActionListener value="#{itm[0]}"
					target="#{mgrbean.personRid}" />
			</p:commandLink>
			<p:spacer width="5" rendered="#{itm[9]>=1}" />
			<p:commandLink value="职业禁忌证"
				action="#{mgrbean.jjzInitAction}" rendered="#{itm[9]>=1}"
				update=":tabView:mainForm:zyjjzDialog" process="@this"
				oncomplete="PF('ZyjjzDialog').show()">
				<f:setPropertyActionListener value="#{itm[0]}"
					target="#{mgrbean.personRid}" />
			</p:commandLink>
		</p:column>
	</ui:define>

	<ui:define name="insertOtherMainContents">
		<p:dialog id="yszybDialog" widgetVar="YszybDialog" header="历次疑似职业病记录"
			resizable="false" width="750" modal="true">
			<p:dataTable var="supitm" paginatorPosition="bottom"
				value="#{mgrbean.tdTjSupoccdiselists}" id="yszybListTable"
				paginator="true" rows="10" emptyMessage="没有数据！">
				<p:column headerText="体检日期" style="width: 100px;text-align: center">
					<h:outputText value="#{supitm.tdTjBhk.bhkDate}">
						<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
							locale="cn" />
					</h:outputText>
				</p:column>
				<p:column headerText="体检机构" style="width: 150px;padding-left: 3px;">
					<h:outputText value="#{supitm.tdTjBhk.tbTjSrvorg.unitName}" />
				</p:column>
				<p:column headerText="在岗状态" style="width: 100px;text-align: center">
					<h:outputText value="#{supitm.tdTjBhk.tsSimpleCode.codeName}" />
				</p:column>
				<p:column headerText="危害因素" style="width: 120px;padding-left: 3px;">
					<h:outputText value="#{supitm.tsSimpleCodeByBadrsnId.codeName}" />
				</p:column>
				<p:column headerText="疑似职业病" style="padding-left: 3px;">
					<h:outputText value="#{supitm.tsSimpleCodeByOccDiseid.codeName}" />
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('YszybDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<p:dialog id="zyjjzDialog" widgetVar="ZyjjzDialog" header="历次职业禁忌证记录"
			resizable="false" width="750" modal="true">
			<p:dataTable var="conitm" paginatorPosition="bottom"
				value="#{mgrbean.tdTjContraindlists}" id="zyjjzListTable"
				paginator="true" rows="10" emptyMessage="没有数据！">
				<p:column headerText="体检日期" style="width: 100px;text-align: center">
					<h:outputText value="#{conitm.tdTjBhk.bhkDate}">
						<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
							locale="cn" />
					</h:outputText>
				</p:column>
				<p:column headerText="体检机构" style="width: 150px;padding-left: 3px;">
					<h:outputText value="#{conitm.tdTjBhk.tbTjSrvorg.unitName}" />
				</p:column>
				<p:column headerText="在岗状态" style="width: 100px;text-align: center">
					<h:outputText value="#{conitm.tdTjBhk.tsSimpleCode.codeName}" />
				</p:column>
				<p:column headerText="危害因素" style="width: 120px;padding-left: 3px;">
					<h:outputText value="#{conitm.tsSimpleCodeByBadrsnId.codeName}" />
				</p:column>
				<p:column headerText="职业禁忌证" style="padding-left: 3px;">
					<h:outputText value="#{conitm.tsSimpleCodeByContraindId.codeName}" />
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('ZyjjzDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 导出项目 -->
		<p:dialog id="exportItemDialog" header="体检项目（不勾选导出所有）" 
			widgetVar="ExportItemDialog" resizable="false" width="350"
			height="400" modal="true">

			<p:tree value="#{mgrbean.exportTreeNode}" var="node"
				selectionMode="checkbox"
				selection="#{mgrbean.selectedExportNodes}" id="exportTree"
				style="width: 320px;height: 390px;overflow-y: auto;">
				<p:treeNode>
					<h:outputText value="#{node.itemName}" />
				</p:treeNode>
			</p:tree>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="导出"  icon="ui-icon-document" id="menuSaveBtn2"
							 actionListener="#{mgrbean.exportData}" process="@this,exportTree,:tabView"
							 onclick="PF('ExportItemDialog').hide()"
							>
						</p:commandButton>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="menuBackBtn2"
							onclick="PF('ExportItemDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<p:dataTable id="dataTable1"  style="display:none;"></p:dataTable>	
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
	</ui:define>
</ui:composition>