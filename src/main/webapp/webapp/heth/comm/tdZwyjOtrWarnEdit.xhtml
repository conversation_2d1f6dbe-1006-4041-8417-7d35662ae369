<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="超范围服务预警处置-#{mgrbean.checkType==2?'终审':'初审'}"/>
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
		        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:outputPanel>
						<p:commandButton value="暂存" icon="ui-icon-disk" rendered="#{(mgrbean.checkType==1 and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2))or (mgrbean.checkType==2 and mgrbean.tdZwyjOtrWarn.stateMark ==1) }"
										 action="#{mgrbean.saveAction}"
										 process="@this,:tabView:editForm"
										 update=":tabView">
						</p:commandButton>
						<p:spacer width="3" rendered="#{(mgrbean.checkType==1 and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2))or (mgrbean.checkType==2 and mgrbean.tdZwyjOtrWarn.stateMark ==1) }"/>
						<p:commandButton value="提交" icon="ui-icon-check"
							action="#{mgrbean.submitAction}" rendered="#{(mgrbean.checkType==1 and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2))or (mgrbean.checkType==2 and mgrbean.tdZwyjOtrWarn.stateMark ==1) }"
							process="@this,:tabView:editForm" >
							<p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
						</p:commandButton>
					</p:outputPanel>
<!--					<p:commandButton value="撤销" action="#{mgrbean.revokeAction}" icon="ui-icon-cancel" process="@this,:tabView"  update=":tabView" rendered="#{(mgrbean.checkType==1 and mgrbean.tdZwyjOtrWarn.stateMark ==1)or (mgrbean.checkType==2 and mgrbean.tdZwyjOtrWarn.stateMark ==3) }">-->
<!--						<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>-->
<!--					</p:commandButton>-->
					<p:commandButton value="撤销" action="#{mgrbean.revokeAction}" icon="ui-icon-cancel" process="@this,:tabView"  update=":tabView" rendered="#{mgrbean.checkType==1 and mgrbean.tdZwyjOtrWarn.stateMark ==1 }">
						<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
					</p:commandButton>
					<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
						action="#{mgrbean.backAction}" process="@this"
						update=":tabView" />
					<p:inputText style="visibility: hidden;width: 0"/>

		    </h:panelGrid>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGridRpt">
			<f:facet name="header">
				<p:row>
					<p:column colspan="2" style="text-align:left;height: 20px;">
						<p:outputLabel value="预警汇报文书"/>
					</p:column>
				</p:row>
			</f:facet>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
						<p:outputLabel value="预警汇报文书：" />
					</p:column>
					<p:column style="text-align:left;padding-right:3px;">
						<p:spacer width="10px"  rendered="#{mgrbean.checkType ==1 and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2)}"/>
						<p:commandButton value="进入" id="rpt1" rendered="#{mgrbean.checkType ==1 and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2)}"
										 action="#{mgrbean.viewInitAction}" process="@this"
										 update=":tabView" />
						<p:spacer width="10px" rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath!=null}"/>
						<p:commandButton value="查看" id="rpt3"
										 onclick="window.open('/webFile/#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath}')" process="@this"
										 update=":tabView" rendered="#{mgrbean.tdZwyjOtrDealWrit.writAnnexPath!=null}"/>
						<p:spacer width="10px" rendered="#{mgrbean.checkType ==1 and mgrbean.tdZwyjOtrDealWrit.writAnnexPath!=null and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2)}"/>
						<p:commandButton value="删除"  id="rpt4"
										 action="#{mgrbean.deleteRpt}" process="@this"
										 update=":tabView" rendered="#{mgrbean.checkType ==1 and mgrbean.tdZwyjOtrDealWrit.writAnnexPath!=null and (mgrbean.tdZwyjOtrWarn.stateMark ==0 or mgrbean.tdZwyjOtrWarn.stateMark ==2)}">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						</p:commandButton>
					</p:column>
				</p:row>

		</p:panelGrid>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGrid1">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6" style="text-align:left;height: 20px;">
						<p:outputLabel value="预警处置信息"/>
					</p:column>
				</p:row>
			</f:facet>
			<p:row rendered="#{mgrbean.checkType == 1 and mgrbean.tdZwyjOtrWarn.stateMark==2}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="退回原因：" style="color: red;" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;color: red;"
									 value="#{mgrbean.tdZwyjOtrWarn.checkCont}"
									 maxlength="100"
					/>
				</p:column>
			</p:row>
			<p:row  rendered="#{mgrbean.checkType == 1 and mgrbean.tdZwyjOtrWarn.stateMark==2}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="退回日期：" style="color: red;"/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;">
					<h:outputText value="#{mgrbean.tdZwyjOtrWarn.checkDate}"  style="color: red;"></h:outputText>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="预警内容："  />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<p:inputTextarea rows="5" autoResize="false" readonly="true"
									 style="resize: none;width: 594px;"
									 value="#{mgrbean.tdZwyjOtrWarn.warnCont}"
									 maxlength="100"
					/>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 35px;width: 200px;">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==0 or mgrbean.tdZwyjOtrWarn.stateMark==2)}"></h:outputText>
					<p:outputLabel value="是否超范围：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 312px;" colspan="#{mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==1 or mgrbean.tdZwyjOtrWarn.stateMark==3)?'1':'5'}" >
					<p:selectOneRadio  style="width:100px;"  value="#{mgrbean.tdZwyjOtrWarn.ifOutRange}"   rendered="#{mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==0 or mgrbean.tdZwyjOtrWarn.stateMark==2)}" >
						<f:selectItem itemLabel="是" itemValue="1" />
						<f:selectItem itemLabel="否" itemValue="0" />
					</p:selectOneRadio>
					<p:outputLabel value="#{mgrbean.tdZwyjOtrWarn.ifOutRange==1?'是':'否'}" rendered="#{!(mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==0 or mgrbean.tdZwyjOtrWarn.stateMark==2))}"></p:outputLabel>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width: 150px;" rendered="#{mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==1 or mgrbean.tdZwyjOtrWarn.stateMark==3)}">
					<p:outputLabel value="处置日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3" rendered="#{mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==1 or mgrbean.tdZwyjOtrWarn.stateMark==3)}">
					<p:outputLabel value="#{mgrbean.tdZwyjOtrWarn.dealDate}" ></p:outputLabel>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==0 or mgrbean.tdZwyjOtrWarn.stateMark==2)}"></h:outputText>
					<p:outputLabel value="处置说明：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<p:inputTextarea rows="5" autoResize="false" readonly="#{!(mgrbean.checkType == 1 and (mgrbean.tdZwyjOtrWarn.stateMark==0 or mgrbean.tdZwyjOtrWarn.stateMark==2))}"
									 style="resize: none;width: 594px;"
									 maxlength="500" value="#{mgrbean.tdZwyjOtrWarn.dealCont}"
					/>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.checkType == 2}">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width: 200px;" >
					<p:outputLabel value="处置机构：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;width: 312px;"  >
					<p:outputLabel value="#{mgrbean.tdZwyjOtrWarn.fkByDealOrgId.unitname}" ></p:outputLabel>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;height: 35px;width: 150px;" >
					<p:outputLabel value="处置日期：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="3" >
					<p:outputLabel value="#{mgrbean.tdZwyjOtrWarn.dealDate}" ></p:outputLabel>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.checkType == 2 }">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.checkType == 2 and mgrbean.tdZwyjOtrWarn.stateMark==1}"></h:outputText>
					<p:outputLabel value="审核结果：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<p:selectOneRadio  style="width:120px;"  value="#{mgrbean.tdZwyjOtrWarn.checkRst}"   rendered="#{mgrbean.checkType == 2 and mgrbean.tdZwyjOtrWarn.stateMark==1}" >
						<f:selectItem itemLabel="通过" itemValue="1" />
						<f:selectItem itemLabel="退回" itemValue="2" />
						<p:ajax event="change" process="@this,:tabView:editForm:planGrid1" listener="#{mgrbean.changeSubmitRst}" update=":tabView:editForm:planGrid1" />
					</p:selectOneRadio>
					<p:outputLabel value="#{mgrbean.tdZwyjOtrWarn.checkRst==1?'通过':'退回'}" rendered="#{!(mgrbean.checkType == 2 and mgrbean.tdZwyjOtrWarn.stateMark==1)}"></p:outputLabel>
				</p:column>
			</p:row>
			<p:row rendered="#{mgrbean.checkType == 2 }">
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:200px;">
					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.checkType == 2 and mgrbean.tdZwyjOtrWarn.stateMark==1}"></h:outputText>
					<p:outputLabel value="审核说明：" />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<p:inputTextarea rows="5" autoResize="false" readonly="#{!(mgrbean.checkType == 2 and mgrbean.tdZwyjOtrWarn.stateMark==1)}"
									 style="resize: none;width: 594px;"
									 maxlength="500" value="#{mgrbean.tdZwyjOtrWarn.checkCont}"
					/>
				</p:column>
			</p:row>
		</p:panelGrid>

		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGrid2">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6" style="text-align:left;height: 20px;">
						<p:outputLabel value="体检机构基本信息"/>
					</p:column>
				</p:row>
			</f:facet>
			<p:row >
					<p:column style="text-align:right;padding-right:3px;height: 35px;width: 200px;" >
						<p:outputLabel value="机构名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;width: 220px;"  >
						<p:outputLabel value="#{mgrbean.ifZzOrg?mgrbean.tdZwTjorginfoComm.orgName:mgrbean.tsUnit.unitname}" ></p:outputLabel>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;height: 35px;width: 150px;" >
						<p:outputLabel value="地区：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;width: 220px;"  >
						<p:outputLabel value="#{mgrbean.ifZzOrg?mgrbean.tdZwTjorginfoComm.tsUnit.tsZone.fullName:mgrbean.tsUnit.tsZone.fullName}" ></p:outputLabel>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;height: 35px;width: 150px;" >
						<p:outputLabel value="单位地址：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;"  >
						<p:outputLabel  value="#{mgrbean.ifZzOrg?mgrbean.tdZwTjorginfoComm.orgAddr:mgrbean.tsUnit.unitaddr}" ></p:outputLabel>
					</p:column>
			</p:row>
			<p:row >
					<p:column style="text-align:right;padding-right:3px;height: 35px;width: 200px;" >
						<p:outputLabel value="社会信用代码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;width: 220px;"  >
						<p:outputLabel value="#{mgrbean.ifZzOrg?mgrbean.tdZwTjorginfoComm.tsUnit.creditCode:mgrbean.tsUnit.creditCode}" ></p:outputLabel>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;height: 35px;width: 150px;" >
						<p:outputLabel value="联系人：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;width: 220px;"  >
						<p:outputLabel value="#{mgrbean.ifZzOrg?mgrbean.tdZwTjorginfoComm.linkMan:mgrbean.tsUnit.linkMan}" ></p:outputLabel>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;height: 35px;width: 150px;" >
						<p:outputLabel value="联系电话：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;"  >
						<p:outputLabel value="#{mgrbean.ifZzOrg?mgrbean.tdZwTjorginfoComm.linkMb:mgrbean.tsUnit.orgTel}" ></p:outputLabel>
					</p:column>
			</p:row>

			<p:row >
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="备案信息："  />
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<p:dataTable id="recordTable" value="#{mgrbean.tjorgRecords}" rendered="#{mgrbean.ifZzba}"
								 emptyMessage="没有数据！" var="itm">
						<p:column headerText="备案地区" style="text-align:center;width:100px;">
							<h:outputText value="#{mgrbean.recordZoneMap.get(itm.zoneId).zoneName}"/>
						</p:column>
						<p:column headerText="备案编号" style="text-align:center;width:150px;">
							<h:outputText value="#{itm.rcdNo}"/>
						</p:column>
						<p:column headerText="备案日期" style="text-align:center;width:80px;">
							<h:outputText value="#{itm.certDate}">
								<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
							</h:outputText>
						</p:column>
						<p:column headerText="服务项目" style="width: 300px;" >
							<h:outputText value="#{itm.serviceItems}"></h:outputText>
						</p:column>
						<p:column headerText="状态" style="text-align:center;width:80px;">
							<h:outputText value="正常" rendered="#{itm.stateMark==0}"></h:outputText>
							<h:outputText value="注销" rendered="#{itm.stateMark==1}"></h:outputText>
						</p:column>
						<p:column headerText="注销日期" style="text-align:center;width:80px;">
							<h:outputText value="#{itm.logoutDate}">
								<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
							</h:outputText>
						</p:column>
					</p:dataTable>
					<p:outputLabel value="#{mgrbean.managerZone}无备案记录" rendered="#{!mgrbean.ifZzba}" ></p:outputLabel>
				</p:column>
			</p:row>
			<p:row  >
				<p:column style="text-align:right;padding-right:3px;height: 35px;width:150px;">
					<p:outputLabel value="备案更新日期："/>
				</p:column>
				<p:column style="text-align:left;padding-left:6px;" colspan="5">
					<h:outputText value="#{mgrbean.tdZwTjorginfoDate.lastSmtDate==null?'无':mgrbean.tdZwTjorginfoDate.lastSmtDate}"  style="color: red;" ></h:outputText>
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid" id="planGrid3">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6" style="text-align:left;height: 20px;">
						<p:outputLabel value="超范围服务信息"/>
					</p:column>
				</p:row>
			</f:facet>
			<p:row >
				<p:column style="text-align:left;padding-left:6px;border-bottom-color: transparent;" >
					<p:dataTable id="cfwTable" value="#{mgrbean.tdZwyjOtrCrptList}"
								 emptyMessage="没有数据！" var="itm">
						<p:column headerText="用工单位地区" style="width:180px;">
							<h:outputText value="#{itm.fkByCrptZoneId.fullName}"/>
						</p:column>
						<p:column headerText="用工单位" style="width:200px;">
							<h:outputText value="#{itm.crptName}"/>
						</p:column>
						<p:column headerText="社会信用代码" style="text-align:center;width:150px;">
							<h:outputText value="#{itm.creditCode}"/>
						</p:column>
						<p:column headerText="超范围服务人数" style="text-align:center;width: 100px;">
							<h:outputText value="#{itm.otrPsns}"></h:outputText>
						</p:column>
						<p:column headerText="体检日期" style="text-align:center;width:270px;">
							<p:panelGrid  style="border-color: transparent;" styleClass="rowBackNone"  >
								<p:row style="background: none;" >
									<p:column style="padding: 0;border-color: transparent;text-align:right;">
										<h:outputText value="#{itm.beginBhkDate}">
											<f:convertDateTime pattern="yyyy年MM月dd日" timeZone="Asia/Shanghai" locale="cn"/>
										</h:outputText>
									</p:column>
									<p:column style="padding: 0;border-color: transparent;">
										<p:outputLabel value="~" ></p:outputLabel>
									</p:column>
									<p:column style="padding: 0;border-color: transparent;text-align:left;">
										<h:outputText value="#{itm.endBhkDate}">
											<f:convertDateTime pattern="yyyy年MM月dd日" timeZone="Asia/Shanghai" locale="cn"/>
										</h:outputText>
									</p:column>
								</p:row>
							</p:panelGrid>
						</p:column>
						<p:column headerText="超服务项目" style="padding-left: 3px;width:300px;" rendered="#{mgrbean.tdZwyjOtrWarn.warnType == 2}">
							<h:outputText value="#{itm.items}" ></h:outputText>
						</p:column>
						<p:column headerText="明细" style="text-align:center;width:60px;">
							<p:commandLink value="查看" process="@this" action="#{mgrbean.viewPersonList}" update=":tabView:editForm:recordDialog" oncomplete="PF('RecordDialog').show()">
								<f:setPropertyActionListener value="#{itm.rid}" target="#{mgrbean.crptId}"></f:setPropertyActionListener>
							</p:commandLink>
						</p:column>
					</p:dataTable>
				</p:column>
			</p:row>
			<p:row >
				<p:column style="border-top-color: transparent;height: 10px;" >
				</p:column>
			</p:row>
		</p:panelGrid>


		<p:dialog id="recordDialog" widgetVar="RecordDialog" width="850"  header="体检记录" resizable="false" modal="true">
			<p:panelGrid style="width:100%" id="recordPanel">
				<p:row>
					<p:column style="width: 100%;border-color: transparent;" >
						<p:dataTable value="#{mgrbean.personList}" paginator="true" rows="10" paginatorPosition="bottom" var="psn" emptyMessage="没有数据！">
							<p:column headerText="劳动者姓名" style="width: 120px;text-align: center;">
								<h:outputText value="#{psn.personName}"></h:outputText>
							</p:column>
							<p:column headerText="证件号码" style="width: 150px;text-align: center;">
								<h:outputText value="#{psn.idc}"></h:outputText>
							</p:column>
							<p:column headerText="体检编号" style="width: 100px;text-align: center;">
								<h:outputText value="#{psn.bhkCode}"></h:outputText>
							</p:column>
							<p:column headerText="体检日期" style="width: 100px;text-align: center;">
								<h:outputText value="#{psn.bhkDate}">
									<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
								</h:outputText>
							</p:column>
							<p:column headerText="接触危害因素" style="width: 250px;" >
								<h:outputText value="#{psn.badrsn}"></h:outputText>
							</p:column>
							<p:column headerText="发现日期" style="width: 100px;text-align: center;">
								<h:outputText value="#{psn.happenDate}">
									<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
								</h:outputText>
							</p:column>
						</p:dataTable>

					</p:column>
				</p:row>
			</p:panelGrid>
		</p:dialog>
	</ui:define>
</ui:composition>