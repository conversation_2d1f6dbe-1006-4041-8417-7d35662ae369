<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
	  xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	  xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<!-- 标题 -->
	<h:head>
		<title><h:outputText value="选择单位" /></title>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<h:outputScript name="js/namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<style type="text/css">
		</style>
		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
	</h:head>

	<h:body>
		<h:form id="mainForm">
				<ui:param name="mgrbean" value="#{selectRadHethListBean}"></ui:param>
				<p:outputPanel styleClass="zwx_toobar_42">
					<h:panelGrid columns="4"
								 style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
							class="ui-icon ui-icon-grip-dotted-vertical"/></span>
						<p:commandButton value="查询" icon="ui-icon-search"
										 action="#{mgrbean.searchAction}"
										 process="@this,searchPanel" update="dataTable"/>
						<p:commandButton value="取消" icon="ui-icon-close"
										 action="#{mgrbean.dialogClose}" process="@this"/>
					</h:panelGrid>
				</p:outputPanel>
				<table style="width: 100%">
					<h:panelGrid columns="6" id="searchPanel">
						<h:outputText value="地区：" styleClass="zwx_dialog_font"/>
						<zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
											   zoneCodeNew="#{mgrbean.searchZoneGb}" zonePaddingLeft="0"
											   zoneName="#{mgrbean.searchZoneName}" id="searchZone"
											   ifShowTrash="false" />
						<h:outputText value="单位名称：" styleClass="zwx_dialog_font"/>
						<p:inputText maxlength="50"  id="searchName" value="#{mgrbean.searchName}">
						</p:inputText>
						<h:outputText value="社会信用代码：" styleClass="zwx_dialog_font" style="padding-left: 23px;"/>
						<p:inputText id="searchCode" value="#{mgrbean.searchCode}" maxlength="25" >
						</p:inputText>
					</h:panelGrid>
				</table>

				<p:dataTable var="itm" value="#{mgrbean.dataModel}" id="dataTable" rowIndexVar="R"
							 paginator="true" rows="10" emptyMessage="没有数据！"
							 paginatorPosition="bottom" lazy="true"
							 rowsPerPageTemplate="#{'10,20,50'}"
							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
					<p:column headerText="选择" style="text-align:center;width:30px;">
						<p:commandLink value="选择"  action="#{mgrbean.selectAction}" process="@this,dataTable"  >
							<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.object}"/>
						</p:commandLink>
					</p:column>
					<p:column headerText="地区" style="width: 170px;padding-left: 8px;" >
						<h:outputText value="#{itm[1]}"  escape="false"/>
					</p:column>
					<p:column headerText="单位名称" style="padding-left: 8px;">
						<h:outputText value="#{itm[3]}" />
					</p:column>
					<p:column headerText="社会信用代码" style="width: 180px;text-align: center">
						<h:outputText value="#{itm[2]}" />
					</p:column>
				</p:dataTable>

		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
	</h:body>
</f:view>
</html>
