<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
	  xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	  xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<!-- 标题 -->
	<h:head>
		<title><h:outputText value="选择单位" /></title>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<h:outputScript name="js/namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<style type="text/css">
		</style>
		<script type="text/javascript">
			//<![CDATA[
			/**
			 * 移除掉多余的地区框
			 */
			var removeExtraTreePanel = function(){
				//移除掉多余的地区框
				var el = jQuery('#mainForm\\:searchNoticeZone\\:zonePanel');
				if(el.length>1){
					el.each(function(index){
						if(index>0){
							$(this).remove();
						}
					});
				}

			}

			//]]>
		</script>
	</h:head>

	<h:body>
		<h:form id="mainForm">
				<ui:param name="mgrbean" value="#{selectOrgUnitListBean}"></ui:param>
				<p:outputPanel styleClass="zwx_toobar_42">
					<h:panelGrid columns="4"
								 style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
							class="ui-icon ui-icon-grip-dotted-vertical"/></span>
						<p:commandButton value="查询" icon="ui-icon-search"
										 action="#{mgrbean.searchAction}"
										 process="@this,searchPanel" update="dataTable"/>
						<p:commandButton value="添加" icon="ui-icon-plus"
										 action="#{mgrbean.addUnit}" oncomplete="removeExtraTreePanel()"
										 process="@this" resetValues="true"/>
						<p:commandButton value="取消" icon="ui-icon-close"
										 action="#{mgrbean.dialogClose}" process="@this"/>
					</h:panelGrid>
				</p:outputPanel>
				<table style="width: 100%">
					<h:panelGrid columns="7" id="searchPanel">
						<h:outputText value="地区：" styleClass="zwx_dialog_font"/>
						<zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
											   zoneCodeNew="#{mgrbean.searchZoneGb}" zonePaddingLeft="0"
											   zoneName="#{mgrbean.searchZoneName}" id="searchZone"
											   ifShowTrash="false" />
						<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
									   style="position: relative;left: -20px;"
									   action="#{mgrbean.clearSearchZone}" process="@this,searchPanel"
									   update="searchPanel"/>
						<h:outputText value="单位名称：" styleClass="zwx_dialog_font"/>
						<p:inputText maxlength="50"  id="searchName" value="#{mgrbean.searchName}">
						</p:inputText>
						<h:outputText value="社会信用代码：" styleClass="zwx_dialog_font" style="padding-left: 23px;"/>
						<p:inputText id="searchCode" value="#{mgrbean.searchCode}" maxlength="25" >
						</p:inputText>
					</h:panelGrid>
				</table>

				<p:dataTable var="itm" value="#{mgrbean.dataModel}" id="dataTable" rowIndexVar="R"
							 paginator="true" rows="10" emptyMessage="没有数据！"
							 paginatorPosition="bottom" lazy="true"
							 rowsPerPageTemplate="#{'10,20,50'}"
							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
					<p:column headerText="选择" style="text-align:center;width:60px;">
						<p:commandLink value="选择"  action="#{mgrbean.selectAction}" process="@this,dataTable"  >
							<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.object}"/>
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" action="#{mgrbean.updateUnit}" process="@this"  update=":mainForm:addUnitDialog"  oncomplete="removeExtraTreePanel()">
							<f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="地区" style="width: 170px;padding-left: 8px;">
						<h:outputText value="#{itm[1]}" />
					</p:column>
					<p:column headerText="单位名称" style="padding-left: 8px;">
						<h:outputText value="#{itm[3]}" />
					</p:column>
					<p:column headerText="社会信用代码" style="width: 180px;text-align: center">
						<h:outputText value="#{itm[2]}" />
					</p:column>
				</p:dataTable>

				<p:dialog header="单位新增" widgetVar="AddUnitDialog" id="addUnitDialog"
						  resizable="false" modal="true" styleClass="addUnitDialog">
					<p:panelGrid>
						<p:row>
							<p:column  style="text-align: right;height:35px;">
								<h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="所属地区："/>
							</p:column>
							<p:column style="width:70px;border-right:0px"  rendered="#{mgrbean.ifCheck}" >
								<p:outputPanel styleClass="unitZone" >
									<zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
														   zoneCodeNew="#{mgrbean.zoneGb}"
														   zoneName="#{mgrbean.zoneName}"
														   zoneId="#{mgrbean.zoneRid}"
														   zoneHeight="340"
									 id="searchNoticeZone"/>
								</p:outputPanel>
							</p:column>
							<p:column style="border-left:0px;" rendered="#{mgrbean.ifCheck}">
								<h:outputText  value="（只能选择区县及以下）" style="color: blue;margin-left:-20px" />
							</p:column>
							<p:column colspan="2" style="padding-left: 18px;"  rendered="#{!mgrbean.ifCheck}">
								<h:outputText value="#{mgrbean.tsUnit.tsZone.zoneName}" />
							</p:column>

						</p:row>
						<p:row>
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="社会信用代码："/></p:column>
							<p:column colspan="2" style="padding-left: 18px;">
								<p:inputText  value="#{mgrbean.tsUnit.creditCode}"  style="width: 320px;" maxlength="50" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="#{mgrbean.tsUnit.creditCode}" rendered="#{!mgrbean.ifCheck}"/>
							</p:column>
						</p:row>
						<p:row>
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="单位名称："/></p:column>
							<p:column colspan="2" style="padding-left: 18px;">
								<p:inputText  value="#{mgrbean.tsUnit.unitname}"  style="width: 320px;" maxlength="50" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="#{mgrbean.tsUnit.unitname}" rendered="#{!mgrbean.ifCheck}"/>
							</p:column>
						</p:row>
						<p:row>
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="单位简称："/>
							</p:column>
							<p:column colspan="2" style="padding-left: 18px;">
								<p:inputText  value="#{mgrbean.tsUnit.unitSimpname}"  style="width: 320px;" maxlength="50" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="#{mgrbean.tsUnit.unitSimpname}" rendered="#{!mgrbean.ifCheck}"/>
							</p:column>
						</p:row>
						<p:row>
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000"/>
								<h:outputText value="单位地址："/></p:column>
							<p:column colspan="2" style="padding-left: 18px;">
								<p:inputText  value="#{mgrbean.tsUnit.unitaddr}"  style="width: 320px;" maxlength="50"/>
							</p:column>
						</p:row>
						<p:row rendered="#{mgrbean.ifHasSubOrg}">
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000" rendered="#{mgrbean.ifCheck}"/>
								<h:outputText value="是否分支机构："/>
							</p:column>
							<p:column colspan="2">
								<p:selectOneRadio value="#{mgrbean.tsUnit.ifSubOrg}" rendered="#{mgrbean.ifCheck}">
									<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
									<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
								</p:selectOneRadio>
								<h:outputText value="是" rendered="#{!mgrbean.ifCheck and mgrbean.tsUnit.ifSubOrg == 1}" style="padding-left: 10px;"/>
								<h:outputText value="否" rendered="#{!mgrbean.ifCheck and mgrbean.tsUnit.ifSubOrg == 0}" style="padding-left: 10px;" />
							</p:column>
						</p:row>
						<p:row>
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000"/>
								<h:outputText value="联系人："/></p:column>
							<p:column colspan="2" style="padding-left: 18px;">
								<p:inputText  value="#{mgrbean.tsUnit.linkMan}"  style="width: 320px;" maxlength="50"/>
							</p:column>
						</p:row>
						<p:row>
							<p:column style="text-align: right;height:35px;">
								<h:outputText value="*" style="color: #FF0000"/>
								<h:outputText value="联系电话："/></p:column>
							<p:column colspan="2" style="padding-left: 18px;">
								<p:inputText  value="#{mgrbean.tsUnit.orgTel}" style="width: 320px;"  maxlength="50"/>
							</p:column>
						</p:row>
					</p:panelGrid>
					<p:outputPanel style="text-align: center;padding: 10px;">
						<p:commandButton value="确定" icon="ui-icon-check"
										 action="#{mgrbean.submitUnitAction}" process="@form" update="mainForm:dataTable" />
						<p:spacer width="10"/>
						<p:commandButton value="取消" icon="ui-icon-close"
										 onclick="PF('AddUnitDialog').hide()" process="@this" />
					</p:outputPanel>
				</p:dialog>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
	</h:body>
</f:view>
</html>
