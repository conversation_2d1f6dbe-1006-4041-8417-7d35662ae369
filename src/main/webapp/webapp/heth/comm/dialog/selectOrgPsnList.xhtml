<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title>选择人员</title>
		<h:outputStylesheet name="css/default.css"/>
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	</h:head>

	<h:body  >
		<h:form id="mainForm">
			<h:outputStylesheet name="css/ui-tabs.css"/>
			<ui:param name="mgrbean" value="#{selectOrgPsnListBean}"></ui:param>
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="4"
							 style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
							class="ui-icon ui-icon-grip-dotted-vertical"/></span>
					<p:commandButton value="查询" icon="ui-icon-search"
									 action="#{mgrbean.searchAction}"
									 process="@this,searchPanel" update="dataTable"/>
					<p:commandButton value="确定" icon="ui-icon-check"
									 action="#{mgrbean.submitAction}"
									 process="@this,dataTable"/>
					<p:commandButton value="取消" icon="ui-icon-close"
									 action="#{mgrbean.dialogClose}" process="@this"/>
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px">
						<h:panelGrid columns="5" id="searchPanel">
							<p:outputLabel value="姓名：" styleClass="zwx_dialog_font" />
							<p:inputText id="searchName" value="#{mgrbean.searchName}" style="width: 180px;" maxlength="20">
							</p:inputText>
						</h:panelGrid>
					</td>
				</tr>
			</table>
			<p:dataTable var="itm" value="#{mgrbean.extractDataModel}" id="dataTable"
						 rowsPerPageTemplate="#{'10,20,50'}"
						 paginator="true" rows="10" emptyMessage="没有数据！" rowKey="#{itm.rid}"
						 paginatorPosition="bottom" lazy="true" selection="#{mgrbean.selectedDatas}"
						 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
						 rowSelectMode="add">
			<p:ajax event="page" process="@this,dataTable" listener="#{mgrbean.pageListener}"/>
			<p:ajax event="rowSelect" process="@this,dataTable" listener="#{mgrbean.rowSelectListener}"
					immediate="true"/>
			<p:ajax event="rowUnselect" process="@this,dataTable" listener="#{mgrbean.rowUnselectListener}"
					immediate="true"/>
			<p:ajax event="rowSelectCheckbox" process="@this,dataTable" listener="#{mgrbean.rowSelectListener}"
					immediate="true"/>
			<p:ajax event="rowUnselectCheckbox" process="@this,dataTable" listener="#{mgrbean.rowUnselectListener}"
					immediate="true"/>
			<p:ajax event="toggleSelect" process="@this,dataTable" listener="#{mgrbean.toggleSelectListener}"
					immediate="true"/>

				<p:column selectionMode="multiple" style="width:16px;text-align:center"/>
				<p:column headerText="姓名" style="width: 80px;text-align: center;">
					<h:outputText value="#{itm.empName}" />
				</p:column>
				<p:column headerText="性别" style="width: 60px;text-align: center">
					<h:outputText value="#{itm.sex}" />
				</p:column>
				<p:column headerText="职称" style="text-align: center">
					<h:outputText value="#{itm.codeName}" />
				</p:column>
			</p:dataTable>
			<br/><br/>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
	</h:body>
</f:view>
</html>
