<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>#{request.getParameter("title")==null?'选择人员':request.getParameter("title")}</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <script type="text/javascript">
            //页面添加js方法
            function datatableOffClick() {
                $(document).off("click.datatable", "#mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
    </h:head>
    <h:body>
        <h:form id="mainForm">
            <h:outputStylesheet name="css/ui-tabs.css"/>
            <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.SelectOrgPsnByZoneListBean"-->
            <ui:param name="mgrbean" value="#{selectOrgPsnByZoneListBean}"/>
            <p:outputPanel styleClass="zwx_toobar_42">
                <h:panelGrid columns="4"
                             style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
                            class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="查询" icon="ui-icon-search" oncomplete="datatableOffClick();"
                                     action="#{mgrbean.searchAction}"
                                     process="@this,searchPanel" update="dataTable"/>
                    <p:commandButton value="确定" icon="ui-icon-check" oncomplete="datatableOffClick();"
                                     action="#{mgrbean.submitAction}"
                                     process="@this,dataTable"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     action="#{mgrbean.dialogClose}" process="@this"/>
                </h:panelGrid>
            </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="6" id="searchPanel">
                            <p:outputLabel value="地区：" styleClass="zwx_dialog_font"/>
                            <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                                   zoneCode="#{mgrbean.searchZoneGb}"
                                                   zoneName="#{mgrbean.searchZoneName}"
                                                   zonePaddingLeft="0"/>
                            <p:outputLabel value="单位名称：" styleClass="zwx_dialog_font"/>
                            <p:inputText id="searchUnitName" value="#{mgrbean.searchUnitName}" style="width: 180px;"
                                         maxlength="50"/>
                            <p:outputLabel value="姓名：" styleClass="zwx_dialog_font" style="padding-left: 20px;"/>
                            <p:inputText id="searchName" value="#{mgrbean.searchName}" style="width: 180px;"
                                         maxlength="50"/>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm" value="#{mgrbean.extractDataModel}" id="dataTable"
                         rowsPerPageTemplate="#{'10,20,50'}"
                         paginator="true" rows="10" emptyMessage="没有数据！" rowKey="#{itm.rid}"
                         paginatorPosition="bottom" lazy="true" selection="#{mgrbean.selectedDatas}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowSelectMode="add">
                <p:ajax event="page" process="@this,dataTable" listener="#{mgrbean.pageListener}"/>
                <p:ajax event="rowSelect" process="@this,dataTable" listener="#{mgrbean.rowSelectListener}"
                        immediate="true"/>
                <p:ajax event="rowUnselect" process="@this,dataTable" listener="#{mgrbean.rowUnselectListener}"
                        immediate="true"/>
                <p:ajax event="rowSelectCheckbox" process="@this,dataTable" listener="#{mgrbean.rowSelectListener}"
                        immediate="true"/>
                <p:ajax event="rowUnselectCheckbox" process="@this,dataTable" listener="#{mgrbean.rowUnselectListener}"
                        immediate="true"/>
                <p:ajax event="toggleSelect" process="@this,dataTable" listener="#{mgrbean.toggleSelectListener}"
                        immediate="true"/>

                <p:column selectionMode="multiple" style="width:16px;text-align:center"/>
                <p:column headerText="地区" style="width: 200px;">
                    <h:outputText value="#{itm.zoneName}"/>
                </p:column>
                <p:column headerText="单位名称" style="width: 200px;">
                    <h:outputText value="#{itm.unitName}"/>
                </p:column>
                <p:column headerText="姓名" style="width: 80px;text-align: center;">
                    <h:outputText value="#{itm.empName}"/>
                </p:column>
                <p:column headerText="性别" style="width: 40px;text-align: center">
                    <h:outputText value="#{itm.sex}"/>
                </p:column>
                <p:column headerText="职称" style="text-align: center">
                    <h:outputText value="#{itm.titleName}"/>
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>
