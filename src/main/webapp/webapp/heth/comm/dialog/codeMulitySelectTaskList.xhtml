<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.dialog.CodeMulitySelectTaskListBean"-->
<ui:param name="mgrbean" value="#{codeMulitySelectTaskListBean}"/>
<f:view contentType="text/html">
	<h:head>
	</h:head>

	<h:body   onload="document.getElementById('codeForm:pym').focus();">
		<title>#{mgrbean.titleName}选择</title>
		<h:outputStylesheet name="css/default.css"/>
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<style type="text/css" >
		</style>
		<h:outputStylesheet name="css/ui-tabs.css"/>
		<h:form id="codeForm">
			<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
				<h:panelGrid columns="3"
							 style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
							class="ui-icon ui-icon-grip-dotted-vertical"/></span>
					<p:commandButton value="确定" icon="ui-icon-check"
									 action="#{mgrbean.submitAction}"
									 process="@this,selectedIndusTable"/>
					<p:commandButton value="取消" icon="ui-icon-close"
									 action="#{mgrbean.dialogClose}" process="@this"/>
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px">
						<h:panelGrid columns="6" id="searchPanel">
							<p:outputLabel value="#{mgrbean.titleName}大类：" styleClass="zwx_dialog_font" rendered="#{mgrbean.ifShowFirstCode}"/>
							<zwx:SimpleCodeManyComp selectedIds="#{mgrbean.selectIds}"
													simpleCodeList="#{mgrbean.firstList}"
													height="200" panelWidth="200" inputWidth="120" onchange="onSearchSelect()" clientWidth="23" clearWidth="23"
													rendered="#{mgrbean.ifShowFirstCode}" />
							<p:remoteCommand name="onSearchSelect" action="#{mgrbean.searchAction}"
											 process="@this,searchPanel" update="selectedIndusTable"/>
							<p:spacer width="5"></p:spacer>
							<p:outputLabel value="#{mgrbean.searchName==null?'名称/拼音码':mgrbean.searchName}：" styleClass="zwx_dialog_font" />
							<p:inputText id="pym" value="#{mgrbean.searchNamOrPy}" style="width: 120px;height: 18px;margin: 2px 0 3px 0;" maxlength="20">
								<p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{mgrbean.searchAction}"/>
							</p:inputText>
						</h:panelGrid>
					</td>
				</tr>
			</table>
			<p:dataTable var="itm"   value="#{mgrbean.displayList}" id="selectedIndusTable"
						 rowsPerPageTemplate="#{'10,20,50'}"  pageLinks="5"
						 paginator="true" rows="10" emptyMessage="没有数据！"
						 paginatorPosition="bottom"
						 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
				<p:column headerText="选择" style="text-align:center;width:30px;">
					<p:selectBooleanCheckbox value="#{itm.ifSelected}" disabled="#{itm.ifDisabled}">
						<p:ajax event="change" listener="#{mgrbean.selectAction(itm)}" process="@this" update="selectedIndusTable" />
					</p:selectBooleanCheckbox>
				</p:column>
				<p:column headerText="名称" style="padding-left: 3px;">
					<h:outputText escape="false"  value="#{(itm.levelIndex == '3') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}"/>
				</p:column>
			</p:dataTable>
			<br/><br/>
		</h:form>
		<!-- <ui:include src="/WEB-INF/templates/system/focus.xhtml"/> -->
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
		<ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
	</h:body>
</f:view>
</html>
