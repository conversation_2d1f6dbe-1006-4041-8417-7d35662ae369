<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.dialog.CodeRadioSelectTaskListBean"-->
<ui:param name="mgrbean" value="#{codeRadioSelectTaskListBean}"/>
<f:view contentType="text/html">
    <h:head>
        

    </h:head>

    <h:body style="overflow-y:hidden;"  onload="document.getElementById('codeForm:pym').focus();">
    <title>#{mgrbean.titleName}选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <h:form id="codeForm">
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                        	<p:outputLabel value="#{mgrbean.titleName}大类：" styleClass="zwx_dialog_font" rendered="#{mgrbean.ifShowFirstCode}"/>
                        	<p:selectOneMenu value="#{mgrbean.firstCodeNo}"
                        		rendered="#{mgrbean.ifShowFirstCode}" id="firstCodeNo">
                        		<f:selectItem itemValue="" itemLabel="--全部--"></f:selectItem>
                        		<f:selectItems value="#{mgrbean.firstList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"></f:selectItems>
                        		<p:ajax event="change" listener="#{mgrbean.searchAction}" process="@this,searchPanel" update="selectedIndusTable"></p:ajax>
                        	</p:selectOneMenu>
                            <p:outputLabel value="名称/拼音码：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{mgrbean.searchNamOrPy}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{mgrbean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{mgrbean.displayList}" id="selectedIndusTable"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         pageLinks="5"
                         paginatorPosition="bottom">
                <p:column headerText="选择" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{mgrbean.selectAction}" process="@this" rendered="#{mgrbean.ifAllSelect?'true':itm.levelIndex != '0'}">
                        <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.selectPro}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="名称" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
    </h:body>
</f:view>
</html>
