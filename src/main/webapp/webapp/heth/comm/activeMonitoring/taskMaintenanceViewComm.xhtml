<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.activeMonitoring.ActiveMonitoringTaskMaintenanceListBean"-->
    <p:fieldset legend="单位信息" style="margin-top: 5px;margin-bottom: 5px;" toggleable="true" toggleSpeed="500">
        <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="crptGrid">
            <p:row>
                <p:column styleClass="cs-scl-first" style="width: 15% !important;">
                    <h:outputText value="单位名称："/>
                </p:column>
                <p:column styleClass="cs-scv-w" style="width: 25% !important;">
                    <div class="cs-flex cs-flex-ai-center">
                        <h:outputText value="#{mgrbean.jcTask.crptName}"/>
                    </div>
                </p:column>
                <p:column styleClass="cs-scl-w" style="width: 15% !important;">
                    <h:outputText value="所属区域："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.fkByZoneId.fullName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-scl-h">
                    <h:outputText value="社会信用代码："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.creditCode}"/>
                </p:column>
                <p:column styleClass="cs-scl">
                    <h:outputText value="单位地址："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.address}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-scl-h">
                    <h:outputText value="行业类别："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.fkByIndusTypeId.codeName}"/>
                </p:column>
                <p:column styleClass="cs-scl">
                    <h:outputText value="经济性质："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.fkByEconomyId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-scl-h">
                    <h:outputText value="企业规模："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.fkByCrptSizeId.codeName}"/>
                </p:column>
                <p:column styleClass="cs-scl">
                    <h:outputText value="联系人："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.jcTask.linkMan}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-scl-h">
                    <h:outputText value="联系电话："/>
                </p:column>
                <p:column styleClass="cs-scv" colspan="3">
                    <h:outputText value="#{mgrbean.jcTask.linkPhone}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="劳动者花名册" style="margin-top: 5px;margin-bottom: 5px;"
                toggleable="true" toggleSpeed="500">
        <p:dataTable value="#{mgrbean.jcTask.jcTaskPsnList}" var="jcTaskPsn" style="text-align: left;"
                     id="jcTaskPsnTable" rows="20" rowIndexVar="R" emptyMessage="没有您要找的记录！"
                     paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="20,50,100,200" lazy="true"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
            <!--@elvariable id="R" type="java.lang.Integer"-->
            <p:ajax event="page" process="@this,jcTaskPsnTable"/>
            <p:column style="width: 50px;text-align: center;" headerText="序号">
                <h:outputText value="#{R + 1}"/>
            </p:column>
            <p:column style="width: 120px;text-align: center;" headerText="姓名">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.psnName}"/>
            </p:column>
            <p:column style="width: 180px;text-align: center;" headerText="证件类型">
                <h:outputText styleClass="cs-break-word" value="#{null == jcTaskPsn.fkByCardTypeId ? null : jcTaskPsn.fkByCardTypeId.codeName}"/>
            </p:column>
            <p:column style="width: 160px;text-align: center;" headerText="证件号码">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.idc}"/>
            </p:column>
            <p:column style="width: 240px;text-align: center;" headerText="监测岗位名称">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.postName}"/>
                <h:outputText styleClass="cs-break-word" value="（#{jcTaskPsn.workOther}）"
                              rendered="#{jcTaskPsn.needWorkOther and not empty jcTaskPsn.workOther}"/>
            </p:column>
            <p:column style="width: 120px;text-align: center;" headerText="防护用品佩戴情况">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.fkByProtectEquId.codeName}"/>
            </p:column>
            <p:column style="width: 90px;text-align: center;" headerText="在岗状态">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.fkByOnguardStateid.codeName}"/>
            </p:column>
            <p:column style="width: 300px;text-align: center;" headerText="职业病主动监测因素">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.rsnName}"/>
            </p:column>
            <p:column headerText="备注（接触的其他职业病危害因素）">
                <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.rmk}"/>
            </p:column>
            <p:column headerText="审核通过时间" style="width:150px;text-align: center;" rendered="#{mgrbean.jcTask.state == 2 and null ne ifShowModifyDate and 1 == ifShowModifyDate}">
                <h:outputLabel value="#{jcTaskPsn.modifyDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
            </p:column>
        </p:dataTable>
    </p:fieldset>
    <!-- 查看退回原因 -->
    <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500" height="300" header="退回原因"
              resizable="false" modal="true">
        <h:inputText styleClass="cs-hidden"/>
        <h:outputText value="#{mgrbean.jcTask.backRsn}" styleClass="cs-tal cs-fs-14 cs-break-word" escape="false"/>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: right;">
                <h:panelGroup>
                    <p:commandButton value="关闭" process="@this" onclick="PF('ReasonDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:composition>