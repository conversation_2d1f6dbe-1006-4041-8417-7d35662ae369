<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.activeMonitoring.ActiveMonitoringTaskMaintenanceListBean"-->
    <ui:define name="insertEditScripts">
        <style>
            #tabView\:editForm\:editGrid {
                margin: 0 !important;
            }
        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <h:outputText value="主动监测任务维护"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" action="#{mgrbean.saveAction}"
                                 process="@this,:tabView:editForm" update="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.beforeSubmitAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="退回原因" icon="icon-alert" style="color:red;"
                                 process="@this" update="reasonDialog1" oncomplete="PF('ReasonDialog1').show();"
                                 rendered="#{not empty mgrbean.jcTask.backRsn}"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView,:tabView:mainForm:dataTable"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:sticky target="sticky"/>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                             oncomplete="PF('ConfirmDialog').hide();" update=":tabView"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:confirmDialog message="将清除当前主动监测重点行业匹配失败的监测岗位名称和职业病主动监测因素，是否继续？"
                         header="消息确认框" widgetVar="CrptConfirmDialog">
            <p:outputPanel style="text-align:right;">
                <p:commandButton value="确定" action="#{mgrbean.crptChangeAction}" icon="ui-icon-check"
                                 process="@this,:tabView:editForm"
                                 update=":tabView:editForm" oncomplete="PF('CrptConfirmDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" process="@this"
                                 oncomplete="PF('CrptConfirmDialog').hide();"/>
            </p:outputPanel>
        </p:confirmDialog>
    </ui:define>
    <ui:define name="insertEditContent">
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="单位信息" style="margin-top: 5px;margin-bottom: 5px;"
                    toggleable="true" toggleSpeed="500">
            <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5" id="crptGrid">
                <p:row>
                    <p:column styleClass="cs-scl-first" style="width: 15% !important;">
                        <h:outputText styleClass="#{mgrbean.jcTask.ifWhBhkCode?'cs-required':''} " value="单位名称："/>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 25% !important;" rendered="#{mgrbean.jcTask.ifWhBhkCode}">
                        <div class="cs-flex cs-flex-ai-center">
                            <p:inputText styleClass="cs-w-260" readonly="true" style="cursor: pointer;" id="crptName"
                                         value="#{mgrbean.jcTask.crptName}"
                                         onclick="jQuery('#tabView\\:editForm\\:onCrptSelect').click()"/>
                            <p:commandLink id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search cs-ml-5"
                                           partialSubmit="true" action="#{mgrbean.selectCrptList}" process="@this">
                                <p:ajax event="dialogReturn" process="@this,crptGrid" resetValues="true"
                                        listener="#{mgrbean.onCrptSelect}" update="crptGrid"/>
                            </p:commandLink>
                        </div>
                    </p:column>
                    <p:column styleClass="cs-scv-w" style="width: 25% !important;" rendered="#{!mgrbean.jcTask.ifWhBhkCode}">
                        <div class="cs-flex cs-flex-ai-center">
                            <h:outputText value="#{mgrbean.jcTask.crptName}"/>
                        </div>
                    </p:column>
                    <p:column styleClass="cs-scl-w" style="width: 15% !important;">
                        <h:outputText value="所属区域："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.fkByZoneId.fullName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="社会信用代码："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.creditCode}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="单位地址："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.address}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="行业类别："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.fkByIndusTypeId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="经济性质："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.fkByEconomyId.codeName}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="企业规模："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.fkByCrptSizeId.codeName}"/>
                    </p:column>
                    <p:column styleClass="cs-scl">
                        <h:outputText value="联系人："/>
                    </p:column>
                    <p:column styleClass="cs-scv">
                        <h:outputText value="#{mgrbean.jcTask.linkMan}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="cs-scl-h">
                        <h:outputText value="联系电话："/>
                    </p:column>
                    <p:column styleClass="cs-scv" colspan="3">
                        <h:outputText value="#{mgrbean.jcTask.linkPhone}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="劳动者花名册" style="margin-top: 5px;margin-bottom: 5px;"
                    toggleable="true" toggleSpeed="500">
            <p:outputPanel id="jcTaskPsnTablePanel">
                <p:commandButton value="添加" icon="ui-icon-plus" styleClass="cs-mb-5"
                                 process="@this,jcTaskPsnTable" update="jcTaskPsnTable"
                                 action="#{mgrbean.addJcTaskPsnAction}"/>
                <p:dataTable value="#{mgrbean.jcTask.jcTaskPsnList}" var="jcTaskPsn" style="text-align: left;"
                             id="jcTaskPsnTable" rows="20" rowIndexVar="R" emptyMessage="没有您要找的记录！"
                             paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="20,50,100,200" lazy="true"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                    <!--@elvariable id="R" type="java.lang.Integer"-->
                    <p:ajax event="page" process="@this,jcTaskPsnTable"/>
                    <p:column style="width: 30px;text-align: center;" headerText="序号">
                        <h:outputText value="#{R + 1}"/>
                    </p:column>
                    <p:column style="width: 180px;text-align: center;" headerText="姓名">
                        <p:inputText styleClass="cs-w-160" value="#{jcTaskPsn.psnName}" maxlength="50" rendered="#{jcTaskPsn.bhkCode eq null}"/>
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.psnName}" rendered="#{jcTaskPsn.bhkCode ne null}"/>
                    </p:column>
                    <p:column style="width: 180px;text-align: center;" headerText="证件类型">
                        <h:outputText styleClass="cs-break-word" value="#{null == jcTaskPsn.fkByCardTypeId ? null : jcTaskPsn.fkByCardTypeId.codeName}" rendered="#{jcTaskPsn.bhkCode ne null}"/>
                        <p:selectOneMenu value="#{jcTaskPsn.cardTypeId}" style="text-align:left;" rendered="#{jcTaskPsn.bhkCode eq null}">
                            <f:selectItems value="#{mgrbean.cardTypeSimpleCodeList}" var="cardType" itemLabel="#{cardType.codeName}" itemValue="#{cardType.rid}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column style="width: 180px;text-align: center;" headerText="证件号码">
                        <p:inputText styleClass="cs-w-160" value="#{jcTaskPsn.idc}" maxlength="50" rendered="#{jcTaskPsn.bhkCode eq null}"/>
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.idc}" rendered="#{jcTaskPsn.bhkCode ne null}"/>
                    </p:column>
                    <p:column style="width: 240px;text-align: center;" headerText="监测岗位名称" rendered="#{jcTaskPsn.bhkCode ne null}">
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.postName}"/>
                        <h:outputText styleClass="cs-break-word" value="（#{jcTaskPsn.workOther}）"
                                      rendered="#{jcTaskPsn.needWorkOther and not empty jcTaskPsn.workOther}"/>
                    </p:column>
                    <p:column style="width: 350px;text-align: center;padding: 4px 0 !important;" rendered="#{jcTaskPsn.bhkCode eq null}"
                              headerText="监测岗位名称">
                        <div class="cs-flex cs-flex-ai-center"
                             style="padding: 0 10px;">
                            <p:inputText readonly="true" style="width:170px"
                                         id="postName" value="#{jcTaskPsn.postName}" maxlength="25"
                                         onclick="jQuery('#tabView\\:editForm\\:jcTaskPsnTable\\:#{R}\\:selPostLink').click()" />
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="selPostLink"
                                           style="position: relative;left: -23px !important;"
                                           action="#{mgrbean.selSimpleCodeAction}" process="@this">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5595"/>
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpTag}" value="#{R}"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                        process="@this" update="postName,rsnName,workOther"/>
                            </p:commandLink>
                            <p:inputText style="width:100px;#{jcTaskPsn.needWorkOther?'':'display:none;'}"
                                         id="workOther" maxlength="50" value="#{jcTaskPsn.workOther}"/>
                        </div>
                    </p:column>
                    <p:column style="width: 120px;text-align: center;" headerText="防护用品佩戴情况">
                        <p:selectOneMenu value="#{jcTaskPsn.protectEquId}" style="text-align:left;" rendered="#{jcTaskPsn.bhkCode eq null}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.protectEquList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                        </p:selectOneMenu>
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.fkByProtectEquId.codeName}" rendered="#{jcTaskPsn.bhkCode ne null}"/>
                    </p:column>
                    <p:column style="width: 60px;text-align: center;" headerText="在岗状态">
                        <p:selectOneMenu value="#{jcTaskPsn.onguadrStateId}" style="text-align:left;" rendered="#{jcTaskPsn.bhkCode eq null}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{mgrbean.editOnguadrStateMap}"/>
                        </p:selectOneMenu>
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.fkByOnguardStateid.codeName}" rendered="#{jcTaskPsn.bhkCode ne null}"/>
                    </p:column>
                    <p:column style="width: 220px;text-align: center;" headerText="职业病主动监测因素" rendered="#{jcTaskPsn.bhkCode ne null}">
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.rsnName}"/>
                    </p:column>
                    <p:column style="width: 220px;text-align: center;padding-right: 0 !important;" rendered="#{jcTaskPsn.bhkCode eq null}"
                              headerText="职业病主动监测因素">
                        <div class="cs-flex cs-flex-ai-center cs-flex-jc-center">
                            <p:inputText readonly="true" style="width:200px"
                                         id="rsnName" value="#{jcTaskPsn.rsnName}" maxlength="25"
                                         onclick="jQuery('#tabView\\:editForm\\:jcTaskPsnTable\\:#{R}\\:selRsnLink').click()"/>
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="selRsnLink"
                                           style="position: relative;left: -23px !important;"
                                           action="#{mgrbean.selSimpleCodeAction}" process="@this">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5007"/>
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpTag}" value="#{R}"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                        process="@this" update="rsnName"/>
                            </p:commandLink>
                        </div>
                    </p:column>
                    <p:column style="width: 220px;text-align: center;" headerText="备注（接触的其他职业病危害因素）">
                        <p:inputText styleClass="cs-w-200" value="#{jcTaskPsn.rmk}" maxlength="200" rendered="#{jcTaskPsn.bhkCode eq null}"/>
                        <h:outputText styleClass="cs-break-word" value="#{jcTaskPsn.rmk}" rendered="#{jcTaskPsn.bhkCode ne null}"/>
                    </p:column>
                    <p:column headerText="操作" >
                        <p:commandLink value="删除" action="#{mgrbean.delJcTaskPsnAction(R)}" rendered="#{jcTaskPsn.bhkCode eq null}"
                                       process="@this,jcTaskPsnTable" update="jcTaskPsnTable">
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </p:outputPanel>
        </p:fieldset>
        <!-- 查看退回原因 -->
        <p:dialog id="reasonDialog1" widgetVar="ReasonDialog1" width="500" height="300" header="退回原因"
                  resizable="false" modal="true">
            <h:inputText styleClass="cs-hidden"/>
            <h:outputText value="#{mgrbean.jcTask.backRsn}" styleClass="cs-tal cs-fs-14 cs-break-word" escape="false"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="关闭" process="@this" onclick="PF('ReasonDialog1').hide();"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>