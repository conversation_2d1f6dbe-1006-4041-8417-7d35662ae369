<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.activeMonitoring.ActiveMonitoringTaskExamineBean"-->
    <ui:param name="mgrbean" value="#{activeMonitoringTaskExamineBean}"/>
    <ui:param name="editPage" value="/webapp/heth/comm/activeMonitoring/taskExamineEdit.xhtml"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/activeMonitoring/taskExamineView.xhtml"/>
    <ui:define name="insertScripts">
        <h:outputStylesheet name="css/ui-cs.css"/>
        <script type="text/javascript">
            //<![CDATA[
            function getDownloadFileClick(){
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }
            //]]>
        </script>
        <style type="text/css">
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
    </ui:define>
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="主动监测任务审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color: transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}" update="dataTable" oncomplete="datatableOffClick()"
                                 process="@this,mainGrid"/>
                <p:commandButton value="批量审核" icon="ui-icon-check"
                                 action="#{mgrbean.beforeBatchExamineAction}"
                />
                <p:commandButton value="导出" icon="ui-icon-document" id="exportBtn" styleClass="btn2"
                                 action="#{mgrbean.exportBefore}" process="@this,mainGrid"
                                 update="dataTable" oncomplete="datatableOffClick()" />
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document"
                                 ajax="false" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.exportData()}"/>
                </p:commandButton>
            </h:panelGrid>
            <p:confirmDialog id="reviewConfirmDialog" message="确定要批量审核通过吗？" header="消息确认框" widgetVar="ReviewConfirmDialog" >
                <p:outputPanel style="text-align:right;">
                    <p:commandButton value="确定" action="#{mgrbean.batchExamineAction}"
                                     icon="ui-icon-check" oncomplete="PF('ReviewConfirmDialog').hide();datatableOffClick()" update=":tabView"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     onclick="PF('ReviewConfirmDialog').hide();"
                                     type="button"/>
                </p:outputPanel>
            </p:confirmDialog>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="检查机构地区："/>
            </p:column>
            <p:column style="width: 260px">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
                                       zoneCodeNew="#{mgrbean.taskExamineCondition.zoneCode}"
                                       zoneName="#{mgrbean.taskExamineCondition.zoneName}"
                                       zonePaddingLeft="6"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="检查机构："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.taskExamineCondition.orgName}"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="年份："/>

            </p:column>
            <p:column styleClass="cs-scv">
                <p:selectOneMenu value="#{mgrbean.taskExamineCondition.year}" style="width: 188px;">
                    <f:selectItems value="#{mgrbean.yearList}"/>
                </p:selectOneMenu>

            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.taskExamineCondition.creditCode}"/>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="行业类别："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <div class="cs-flex cs-flex-ai-center">
                    <p:inputText id="indusTypeName" style="width: 180px;cursor: pointer;"
                                 value="#{mgrbean.taskExamineCondition.indusTypeNames}" readonly="true"
                                 onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"/>
                    <p:commandLink styleClass="ui-icon ui-icon-trash cs-ml-3" title="清空"
                                   process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="selIndusTypeLink"
                                   style="position: relative;left: -39px !important;"
                                   process="@this" action="#{mgrbean.selSimpleCodeAction}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                process="@this" update="indusTypeName" resetValues="true"/>
                    </p:commandLink>
                </div>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="经济类型："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <div class="cs-flex cs-flex-ai-center">
                    <p:inputText id="encomyName" style="width: 180px;cursor: pointer;"
                                 value="#{mgrbean.taskExamineCondition.economyNames}" readonly="true"
                                 onclick="document.getElementById('tabView:mainForm:selEconomyLink').click();"/>
                    <p:commandLink styleClass="ui-icon ui-icon-trash cs-ml-3" title="清空"
                                   process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="selEconomyLink"
                                   style="position: relative;left: -39px !important;"
                                   process="@this" action="#{mgrbean.selSimpleCodeAction}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                process="@this" resetValues="true" update="encomyName"/>
                    </p:commandLink>
                </div>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="单位名称："/>

            </p:column>
            <p:column styleClass="cs-scv-w" >
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.taskExamineCondition.crptName}"/>

            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="企业规模："/>
            </p:column>
            <p:column style="padding-left: 2px !important;">
                <zwx:SimpleCodeManyComp selectedIds="#{mgrbean.taskExamineCondition.crptSizeIds}"
                                        simpleCodeList="#{mgrbean.crptSizeList}"
                                        height="160" />
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="姓名："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.taskExamineCondition.psnName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="证件号码："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.taskExamineCondition.idc}" placeholder="精确查询"/>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="状态："/>
            </p:column>
            <p:column  styleClass="cs-scv" style="padding-left: 3px !important;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.taskExamineCondition.state}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:20px;text-align:center;" disabledSelection="#{itm[10] ne 1}"/>
        <p:column headerText="年份" styleClass="cs-break-word" style="width: 60px;text-align: center;">
            <h:outputText value="#{itm[12]}"/>
        </p:column>
        <p:column headerText="检查机构地区" styleClass="cs-break-word" style="width: 160px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="检查机构" styleClass="cs-break-word" style="width: 220px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="单位名称" styleClass="cs-break-word" style="width: 220px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="社会信用代码" styleClass="cs-break-word" style="width: 160px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="行业类别" styleClass="cs-break-word" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="经济类型" styleClass="cs-break-word" style="width: 145px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="企业规模" styleClass="cs-break-word" style="width: 60px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="状态" styleClass="cs-break-word" style="width: 55px;text-align: center;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="退回原因" styleClass="cs-break-word" style="width: 240px;">
            <h:outputText id="backRsn" value="#{itm[9]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="backRsn" style="width:450px;">
                <p:outputLabel styleClass="cs-break-word" value="#{itm[9]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink action="#{mgrbean.modInitAction}" value="审核" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           rendered="#{itm[10] eq 1}" oncomplete="datatableOffClick()">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink action="#{mgrbean.viewInitAction}" value="详情" onclick="hideTooltips();" oncomplete="datatableOffClick()"
                           process="@this" update=":tabView" rendered="#{itm[10] ne 1}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>