<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.activeMonitoring.ActiveMonitoringTaskExamineBean"-->
    <ui:define name="insertEditScripts">
        <style>
            #tabView\:viewForm\:editGrid {
                margin: 0 !important;
            }
        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <h:outputText value="主动监测任务审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" action="#{mgrbean.cancleAction}"
                                 process="@this"  update=":tabView"
                                 rendered="#{mgrbean.jcTask.state eq 2}">
                    <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{mgrbean.jcTask.rid}"/>
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="退回原因" icon="icon-alert" style="color:red;"
                                 process="@this" update="reasonDialog" oncomplete="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.jcTask.state eq 3 and not empty mgrbean.jcTask.backRsn}"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" oncomplete="datatableOffClick()"
                                 action="#{mgrbean.backAction}" process="@this" update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertEditContent">
    </ui:define>
    <ui:define name="insertOtherContents">
        <ui:include src="/webapp/heth/comm/activeMonitoring/taskMaintenanceViewComm.xhtml">
            <ui:param name="ifShowModifyDate" value="1" />
        </ui:include>
    </ui:define>
</ui:composition>