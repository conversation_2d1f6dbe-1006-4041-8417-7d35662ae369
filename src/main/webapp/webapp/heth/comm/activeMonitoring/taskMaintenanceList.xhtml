<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.activeMonitoring.ActiveMonitoringTaskMaintenanceListBean"-->
    <ui:param name="mgrbean" value="#{activeMonitoringTaskMaintenanceListBean}"/>
    <ui:param name="editPage" value="/webapp/heth/comm/activeMonitoring/taskMaintenanceEdit.xhtml"/>
    <ui:param name="viewPage" value="/webapp/heth/comm/activeMonitoring/taskMaintenanceView.xhtml"/>
    <ui:define name="insertScripts">
    </ui:define>
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" styleClass="cs-title">
                <h:outputText value="主动监测任务维护"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color: transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 process="@this,dataTable,mainGrid" update=":tabView"
                                 action="#{mgrbean.addInitAction}" onclick="hideTooltips();">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputText value="年份："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:selectOneMenu value="#{mgrbean.searchYear}" style="width: 188px;">
                    <f:selectItems value="#{mgrbean.searchYearList}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column styleClass="cs-scv-w">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.searchCrptName}"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.searchInstitutionCode}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="行业类别："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <div class="cs-flex cs-flex-ai-center">
                    <p:inputText id="indusTypeName" style="width: 180px;cursor: pointer;"
                                 value="#{mgrbean.selectIndusTypeNames}" readonly="true"
                                 onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"/>
                    <p:commandLink styleClass="ui-icon ui-icon-trash cs-ml-3" title="清空"
                                   process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="selIndusTypeLink"
                                   style="position: relative;left: -39px !important;"
                                   process="@this" action="#{mgrbean.selSimpleCodeAction}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                process="@this" update="indusTypeName" resetValues="true"/>
                    </p:commandLink>
                </div>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="经济类型："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <div class="cs-flex cs-flex-ai-center">
                    <p:inputText id="encomyName" style="width: 180px;cursor: pointer;"
                                 value="#{mgrbean.selectEconomyNames}" readonly="true"
                                 onclick="document.getElementById('tabView:mainForm:selEconomyLink').click();"/>
                    <p:commandLink styleClass="ui-icon ui-icon-trash cs-ml-3" title="清空"
                                   process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="selEconomyLink"
                                   style="position: relative;left: -39px !important;"
                                   process="@this" action="#{mgrbean.selSimpleCodeAction}">
                        <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                process="@this" resetValues="true" update="encomyName"/>
                    </p:commandLink>
                </div>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="企业规模："/>
            </p:column>
            <p:column styleClass="cs-scv" style="padding-left: 2px !important;">
                <zwx:SimpleCodeManyComp selectedIds="#{mgrbean.selectCrptSizeIds}"
                                        simpleCodeList="#{mgrbean.crptSizeList}"
                                        height="200"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputText value="姓名："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.searchPsnName}"/>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="证件号码："/>
            </p:column>
            <p:column styleClass="cs-scv">
                <p:inputText styleClass="cs-w-180" value="#{mgrbean.searchIdc}" placeholder="精确查询"/>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputText value="状态："/>
            </p:column>
            <p:column styleClass="cs-scv" style="padding-left: 3px !important;">
                <p:selectManyCheckbox value="#{mgrbean.searchState}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="年份" styleClass="cs-break-word" style="width: 50px;text-align: center;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="单位地区" styleClass="cs-break-word" style="width: 240px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="单位名称" styleClass="cs-break-word" style="width: 260px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="社会信用代码" styleClass="cs-break-word" style="width: 160px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="行业类别" styleClass="cs-break-word" style="width: 210px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="经济类型" styleClass="cs-break-word" style="width: 190px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="企业规模" styleClass="cs-break-word" style="width: 60px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="状态" styleClass="cs-break-word" style="width: 60px;text-align: center;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="退回原因" styleClass="cs-break-word" style="width: 240px;">
            <h:outputText id="backRsn" value="#{itm[9]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="backRsn" style="width:450px;">
                <p:outputLabel styleClass="cs-break-word" value="#{itm[9]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink action="#{mgrbean.modInitAction}" value="修改" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           rendered="#{itm[10] eq 0 or itm[10] eq 3}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{(itm[10] eq 0 or itm[10] eq 3) and itm[11]}"/>
            <p:commandLink action="#{mgrbean.deleteAction}" value="删除"
                           process="@this" update="dataTable" rendered="#{(itm[10] eq 0 or itm[10] eq 3) and itm[11]}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink action="#{mgrbean.viewInitAction}" value="详情" onclick="hideTooltips();"
                           process="@this" update=":tabView" rendered="#{itm[10] eq 1 or itm[10] eq 2}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>