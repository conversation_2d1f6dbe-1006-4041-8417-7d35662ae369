<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.activeMonitoring.ActiveMonitoringTaskExamineBean"-->
    <ui:define name="insertEditScripts">
        <style>
            #tabView\:editForm\:editGrid {
                margin: 0 !important;
            }
        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <h:outputText value="主动监测任务审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="审核通过" icon="ui-icon-check"
                                 action="#{mgrbean.beforeExamineAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="退回" icon="ui-icon-cancel"
                                 action="#{mgrbean.beforeReturnAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" oncomplete="datatableOffClick()"
                                 action="#{mgrbean.backAction}" process="@this" update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:sticky target="sticky"/>
            <p:confirmDialog message="确定要审核通过吗？" header="消息确认框" widgetVar="ConfirmDialog">
                <p:commandButton value="确定" action="#{mgrbean.examineAction}" icon="ui-icon-check"
                                 oncomplete="PF('ConfirmDialog').hide();datatableOffClick();" update=":tabView:editForm"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
            </p:confirmDialog>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertEditContent">
    </ui:define>
    <ui:define name="insertOtherContents">
        <ui:include src="/webapp/heth/comm/activeMonitoring/taskMaintenanceViewComm.xhtml">
            <ui:param name="ifShowModifyDate" value="0" />
        </ui:include>
        <!-- 退回原因 -->
        <p:dialog id="returnDialog" widgetVar="ReturnDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.jcTask.backRsn}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="returnContent" maxlength="200" />
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReturnDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         process="@this,returnContent"
                                         oncomplete="datatableOffClick()"
                                         action="#{mgrbean.returnBackAction}" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>