<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{crptInfoAuditBean}"/>
	<ui:param name="editPage" value="/webapp/heth/comm/crptaudit/crptAuditEdit.xhtml" />
	<ui:param name="viewPage" value="/webapp/heth/comm/crptaudit/crptAuditView.xhtml" />
	<ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />
        <style type="text/css">
        	table.ui-selectoneradio td label{
			 	white-space:nowrap;
			    overflow: hidden;
			}
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位综合管理"/>
            </p:column>
        </p:row>
    </ui:define>


    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="批量填报" icon="ui-icon-check" 
                				 action="#{mgrbean.openInvestDialog}" 
	            				 oncomplete="datatableOffClick()"
	                             process="@this,dataTable" />
            </h:panelGrid>
        </p:outputPanel>
        <p:overlayPanel id="miningPanel" for="miningName"
						style="width:190px;" widgetVar="MiningPanel"
						showCloseIcon="true" onHide="hideMining();">
			<p:tree var="node" selectionMode="checkbox"
					value="#{mgrbean.miningSortTree}"
					style="width: 160px;height: 200px;overflow-y: auto;">
				<p:ajax event="select"  process="@this" listener="#{mgrbean.onNodeSelect}"/>
				<p:ajax event="unselect"  process="@this" listener="#{mgrbean.onNodeNoSelect}"/>
				<p:treeNode>
					<p:outputLabel value="#{node.codeName}" />
				</p:treeNode>
			</p:tree>
		</p:overlayPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputLabel value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:240px;">
                <h:panelGrid columns="3" style="border-color: transparent;margin: -9px;padding: 0px;">
                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.searchZoneList}"
                                        zoneCode="#{mgrbean.searchZoneCode}"
                                        zoneName="#{mgrbean.searchZoneName}"
                                        zoneId="#{mgrbean.searchZoneId}"/>
                </h:panelGrid>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width:240px;">
                <p:inputText value="#{mgrbean.searchUnitName}" style="width:200px;"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width:200px;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="indusTypeName"
                                         value="#{mgrbean.selectIndusTypeNames}"
                                         style="width: 180px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           id="selIndusTypeLink"
                                           action="#{mgrbean.selSimpleCodeAction}" process="@this"
                                           style="position: relative;left: -28px !important;">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
                                        resetValues="true" update="indusTypeName" />
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="encomyName"
                                         value="#{mgrbean.selectEconomyNames}"
                                         style="width: 200px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selEncomyLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           id="selEncomyLink"
                                           action="#{mgrbean.selSimpleCodeAction}" process="@this"
                                           style="position: relative;left: -28px !important;">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
                                        resetValues="true" update="encomyName" />
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectCrptSizes}"
                                        selectedIds="#{mgrbean.selectCrptSizeIds}"
                                        simpleCodeList="#{mgrbean.crptSizeList}"
                                        inputWidth="200"
                                        panelWidth="210"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.stateArray}" >
                    <f:selectItem itemLabel="待填报" itemValue="0,5"></f:selectItem>
                    <f:selectItem itemLabel="待审核" itemValue="1"></f:selectItem>
                    <f:selectItem itemLabel="已退回" itemValue="2"></f:selectItem>
                    <f:selectItem itemLabel="审核完成" itemValue="3"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:30px;text-align:center;" disabledSelection="#{itm[16] == '1' ? true : false}"/>
        <p:column headerText="地区" style="width:200px;">
            <font style="color:red;" >
                <h:outputText value="#{itm[0]}" rendered="#{itm[19] == '1' }"/>
            </font>
            <h:outputText value="#{itm[0]}" rendered="#{itm[19] == '0' }"/>
        </p:column>
        <p:column headerText="用人单位名称" style="padding-left: 3px; width:260px;">
            <font style="color:red;" >
                <h:outputText value="#{itm[1]}" rendered="#{itm[20] == '1' }"/>
            </font>
            <h:outputText value="#{itm[1]}" rendered="#{itm[20] == '0' }"/>
        </p:column>
        <p:column headerText="社会信用代码" style=" width:180px;text-align:center; ">
            <font style="color:red;" >
                <h:outputText value="#{itm[2]}" rendered="#{itm[30] == '1' }"/>
            </font>
            <h:outputText value="#{itm[2]}" rendered="#{itm[30] == '0' }"/>
        </p:column>
        <p:column headerText="行业类别" style=" width:180px;text-align:center; ">
            <font style="color:red;" >
                <h:outputText value="#{itm[3]}" rendered="#{itm[21] == '1' }"/>
            </font>
            <h:outputText value="#{itm[3]}" rendered="#{itm[21] == '0' }"/>
        </p:column>
        <p:column headerText="经济类型" style=" width:180px;text-align:center;">
            <font style="color:red;" >
                <h:outputText value="#{itm[4]}" rendered="#{itm[22] == '1' }" />
            </font>
            <h:outputText value="#{itm[4]}" rendered="#{itm[22] == '0' }" />
        </p:column>
        <p:column headerText="企业规模" style=" width:60px;text-align:center; ">
            <font style="color:red;" >
                <h:outputText value="#{itm[5]}" rendered="#{itm[23] == '1' }" />
            </font>
            <h:outputText value="#{itm[5]}" rendered="#{itm[23] == '0' }" />
        </p:column>
        <p:column headerText="联系人" style="width:60px;text-align:center; ">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width:100px;text-align:center; ">
            <font style="color:red;" >
                <h:outputText value="#{itm[7]}" rendered="#{itm[31] == '1' }" />
            </font>
            <h:outputText value="#{itm[7]}"  rendered="#{itm[31] == '0' }"/>
        </p:column>
        <p:column headerText="存在情况" style=" width:60px;text-align:center; ">
            <h:outputText value="#{null == itm[8] ? '' : '1' == itm[8] ? '存在' : '0' == itm[8] ?  '不存在' : ''}"/>
        </p:column>
        <p:column headerText="填报日期" style=" width:100px;text-align:center; ">
            <h:outputLabel value="#{itm[9]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style=" width:100px;text-align:center; ">
            <font style="color:red;" ><h:outputText value="已退回" rendered="#{null != itm[10] and itm[10] == '2'}" /></font>
            <h:outputText value="#{itm[10] == '0' ? '待填报' : itm[10] == '1' ? '待审核' : itm[10] == '3' ? '审核完成' : itm[10] == '4' ? '地区变更' : itm[10] == '5' ? '待填报' : ''}" rendered="#{null != itm[10] and itm[10] != '2'}"/>
        </p:column>
        <p:column headerText="操作" >
            <p:spacer width="5" rendered="#{null != itm[10] and (itm[10] == '2' or itm[10] == '0' or itm[10] == '5')}"/>
            <p:commandLink value="填报" process="@this"  rendered="#{null != itm[10] and (itm[10] == '2' or itm[10] == '0' or itm[10] == '5')}"
            	action="#{mgrbean.modInitAction}" update=":tabView">
            	<f:setPropertyActionListener value="#{itm[11]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{null != itm[10] and (itm[10] == '1' or itm[10] == '3')}"/>
            <p:commandLink value="详情"  process="@this"  rendered="#{null != itm[10] and (itm[10] == '1' or itm[10] == '3')}"
            	action="#{mgrbean.viewInitAction}" update=":tabView">
            	<f:setPropertyActionListener value="#{itm[11]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
    	<p:dialog header="填报信息" widgetVar="InvestInfoDialog" width="500" height="#{mgrbean.ifMining?'330':'230'}" modal="true" resizable="false" 
    			id="investInfoDialog">
    		<p:panelGrid style="width:100%;" id="investBatchGrid">
    			<p:row>
    				<p:column colspan="2" style="height:32px;">
						<div style="display:flex;margin:8px;">
                            <div style="width: 3px; height: 12px; background: #2e6e9e;"/>
                            <p:outputLabel value="基本情况" style="margin-left: 5px;color: #334B9A;line-height: 12px;font-weight: bold;"></p:outputLabel>
                        </div>
						
					</p:column>
    			</p:row>
				<p:row>
					<p:column styleClass="column_title" style="height:38px;width: 120px">
	            		<h:outputText value="*" style="color:red;" />
	                    <p:outputLabel value="存在情况："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:2px;">
	                    <p:selectOneRadio value="#{mgrbean.crptInvest.existState}">
	                    	<f:selectItem itemValue="1" itemLabel="存在"></f:selectItem>
	                    	<f:selectItem itemValue="0" itemLabel="不存在"></f:selectItem>
	                    	<p:ajax event="change" process="@this,investBatchGrid" update="investBatchGrid"></p:ajax>
	                    </p:selectOneRadio>
	                </p:column>
				</p:row>
				<p:row>
					<p:column styleClass="column_title" style="height:38px;">
	                	<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
	                    <p:outputLabel value="生产情况："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:2px;">
                		<p:selectOneRadio value="#{mgrbean.crptInvest.produceState}" disabled="#{mgrbean.crptInvest.existState==0}">
	                    	<f:selectItem itemValue="1" itemLabel="正常生产"></f:selectItem>
	                    	<f:selectItem itemValue="0" itemLabel="非正常生产"></f:selectItem>
	                    	<p:ajax event="change" process="@this,investBatchGrid" update="investBatchGrid"></p:ajax>
	                    </p:selectOneRadio>
	                </p:column>
				</p:row>
				<p:row rendered="#{mgrbean.crptInvest.produceState==0}">
					<p:column styleClass="column_title" style="height:38px;">
	                	<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
	                    <p:outputLabel value="非正常生产情况："/>
	                </p:column>
	                <p:column style="text-align:left;padding:1px;">
	                	<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
							<p:selectOneMenu value="#{mgrbean.crptInvest.fkByNoProduceStateId.rid}" 
		                    	disabled="#{mgrbean.crptInvest.existState==0}"
		                    	style="wdith:120px;">
		                    	<f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
		                    	<f:selectItems value="#{mgrbean.noProduceStateList}" var="itm" 
		                    		itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"></f:selectItems>
		                    	<p:ajax event="change" process="@this,investBatchGrid" update="investBatchGrid"/>
		                    </p:selectOneMenu>
		                    <p:inputText value="#{mgrbean.crptInvest.otherProduceState}" style="width: 160px;" maxlength="25" 
		                    	rendered="#{mgrbean.noProduceStateMap.get(mgrbean.crptInvest.fkByNoProduceStateId.rid).extendS1==1}"
		                    	disabled="#{mgrbean.crptInvest.existState==0}"></p:inputText>	                    
	                	</h:panelGrid>
	                </p:column>
				</p:row>
				<p:row rendered="#{mgrbean.ifMining}">
    				<p:column colspan="2">
						<div style="display:flex;margin:8px;align-items: center;">
							<div style="width: 3px; height: 12px; background: #2e6e9e;"/>
                            <p:outputLabel value="采矿业情况" style="margin-left: 5px;color: #334B9A;line-height: 12px;font-weight: bold;"></p:outputLabel>
                        	<p:outputLabel value="（仅针对已选择的采矿业用人单位填写）" style="color:blue;"></p:outputLabel>
                        </div>
						
					</p:column>
    			</p:row>
				<p:row rendered="#{mgrbean.ifMining}">
					<p:column styleClass="column_title" style="height:38px;">
	            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
	                    <p:outputLabel value="开采方式："/>
	                </p:column>
	                <p:column style="text-align:left;padding:0px;">
	                	<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="miningGrid">
							<p:inputText id="miningName" value="#{mgrbean.selectMiningNames}" style="width: 180px;" readonly="true" 
								disabled="#{mgrbean.crptInvest.existState==0}"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" process="@this"  style="position: relative;left: -30px;"
										   oncomplete="PF('MiningPanel').show()" type="button" 
										   disabled="#{mgrbean.crptInvest.existState==0}"/>
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" 
										   style="position: relative;left: -33px;" 
										   action="#{mgrbean.clearSelectMining}" process="@this" 
										   update="miningGrid,miningPanel"
										   disabled="#{mgrbean.crptInvest.existState==0}"/>
						</h:panelGrid>
						<p:remoteCommand name="hideMining" process="@this,miningPanel"
										 action="#{mgrbean.hideMiningAction}"
										 update="miningGrid"/>
	                </p:column>
				</p:row>
				<p:row rendered="#{mgrbean.ifMining}">
					<p:column styleClass="column_title" style="height:38px;">
	            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
	                    <p:outputLabel value="含铀情况："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                    <p:selectOneRadio value="#{mgrbean.crptInvest.ifHasUranium}"
	                    	disabled="#{mgrbean.crptInvest.existState==0}">
	                    	<f:selectItem itemValue="1" itemLabel="含铀"></f:selectItem>
	                    	<f:selectItem itemValue="0" itemLabel="不含铀"></f:selectItem>
	                    </p:selectOneRadio>
	                </p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="提交" icon="ui-icon-check" process="@this,investInfoDialog" action="#{mgrbean.investBatchAction}"/>
						<p:spacer width="10"></p:spacer>
						<p:commandButton value="关闭" icon="ui-icon-close" type="button" process="@this" onclick="PF('InvestInfoDialog').hide();"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
    	</p:dialog>
    </ui:define>
</ui:composition>