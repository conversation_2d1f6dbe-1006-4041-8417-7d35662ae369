<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjCrptInvestCheckListBean}"/>
	<ui:param name="editPage" value="/webapp/heth/comm/crptaudit/tbTjCrptInvestCheckEdit.xhtml" />
	<ui:param name="viewPage" value="/webapp/heth/comm/crptaudit/tbTjCrptInvestCheckView.xhtml" />
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="用人单位信息审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
        <h:panelGrid columns="4"
                     style="border-color:transparent;padding:0;">
			<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
            <p:commandButton value="查询" icon="ui-icon-search"
                             action="#{mgrbean.searchAction}"
                             update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
            <p:commandButton value="批量审核" icon="ui-icon-check"
            				action="#{mgrbean.openReviewConfirmDialog}" 
            				oncomplete="datatableOffClick()"
                            process="@this,dataTable"/>
        </h:panelGrid>
    </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
	      <p:row>
	        <p:column style="text-align:right;padding-right:3px;width: 120px;">
	            <p:outputLabel value="地区："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:3px;width:280px;">
	            <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
	                                zoneCode="#{mgrbean.searchZoneCode}"
	                                zoneName="#{mgrbean.searchZoneName}"/>
	        </p:column>
	        <p:column style="text-align:right;padding-right:3px;width: 120px;">
	            <p:outputLabel value="用人单位名称："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:6px;width: 260px;">
	        	<p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50"></p:inputText>
	        </p:column>
	        <p:column style="text-align:right;padding-right:3px;width: 120px;">
	            <p:outputLabel value="社会信用代码："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:6px;">
	        	<p:inputText value="#{mgrbean.searchCreditCode}" style="width: 180px;" maxlength="25"></p:inputText>
	        </p:column>
	    </p:row>
	    <p:row>
	    	<p:column style="text-align:right;padding-right:3px;width: 120px;">
	            <p:outputLabel value="行业类别："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:6px;">
	        	<table>
					<tr>
						<td style="padding: 0;border-color: transparent;">
							<p:inputText id="indusTypeName"
								value="#{mgrbean.searchIndusTypeName}"
								style="width: 180px;cursor: pointer;"
								onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"
								readonly="true"/>
						</td>
						<td style="border-color: transparent;">
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="selIndusTypeLink"
								action="#{mgrbean.selSimpleCodeAction}" process="@this"
								style="position: relative;left: -28px !important;">
								<f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
								<p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
									resetValues="true" update="indusTypeName" />
							</p:commandLink>
						</td>
						<!-- 清空 -->
						<td style="border-color: transparent;position: relative;left: -30px;">
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                         process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                                  <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                            </p:commandLink>
						</td>
					</tr>
				</table>
	        </p:column>
	        <p:column style="text-align:right;padding-right:3px;width: 120px;height:38px;">
	            <p:outputLabel value="经济类型："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:3px;">
	            <table>
					<tr>
						<td style="padding: 0;border-color: transparent;">
							<p:inputText id="economyName"
								value="#{mgrbean.searchEconomyName}"
								style="width: 180px;cursor: pointer;"
								onclick="document.getElementById('tabView:mainForm:selEconomyLink').click();"
								readonly="true"/>
						</td>
						<td style="border-color: transparent;">
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="selEconomyLink"
								action="#{mgrbean.selSimpleCodeAction}" process="@this"
								style="position: relative;left: -28px !important;">
								<f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
								<p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
									resetValues="true" update="economyName" />
							</p:commandLink>
						</td>
						<td style="border-color: transparent;position: relative;left: -30px;">
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                         update="economyName" process="@this"
                                         action="#{mgrbean.clearSimpleCode}">
                               <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                            </p:commandLink>
						</td>
					</tr>
				</table>
	        </p:column>
	        <p:column style="text-align:right;padding-right:3px;width: 120px;">
	            <p:outputLabel value="企业规模："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:0px;">
	        	<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectCrptSizeNames}"
                      selectedIds="#{mgrbean.selectCrptSizeIds}"
                      simpleCodeList="#{mgrbean.crptSizeList}"
                      inputWidth="180" panelWidth="190"
                      height="200"/>
	        </p:column>
	    </p:row>
    	<p:row>
    		<p:column style="text-align:right;padding-right:3px;width: 120px;">
	            <p:outputLabel value="状态："/>
	        </p:column>
	        <p:column style="text-align:left;padding-left:6px;" colspan="5">
	        	<p:selectManyCheckbox value="#{mgrbean.searchState}">
	        		<f:selectItem itemValue="1" itemLabel="待审核"></f:selectItem>
	        		<f:selectItem itemValue="4" itemLabel="地区变更"></f:selectItem>
	        		<f:selectItem itemValue="2" itemLabel="已退回"></f:selectItem>
	        		<f:selectItem itemValue="3" itemLabel="审核完成"></f:selectItem>
	        	</p:selectManyCheckbox>
	        </p:column>
    	</p:row>
    </ui:define>

    <ui:define name="insertDataTable">
    	<p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{itm[12]=='1'?false:true}"/>

        <p:column headerText="地区" style="width: 150px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="用人单位名称" style="width: 240px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="社会信用代码" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="行业类别" style="text-align:center;width:150px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="经济类型" style="text-align:center;width:150px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="企业规模" style="text-align:center;width:60px;padding-left: 8px;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="联系人" style="text-align:center;width:100px;padding-left: 8px;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="联系电话" style="text-align:center;width:100px;padding-left: 8px;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="存在情况" style="text-align:center;width:60px;padding-left: 8px;">
            <h:outputText value="存在" rendered="#{itm[9]==1}"/>
            <h:outputText value="不存在" rendered="#{itm[9]==0}"/>
        </p:column>
        <p:column headerText="填报单位" style="width:210px;padding-left: 8px;">
            <h:outputText value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="填报日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[11]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputLabel value="待审核" rendered="#{itm[12]==1}"/>
            <h:outputLabel value="已退回" rendered="#{itm[12]==2}" style="color:red;"/>
            <h:outputLabel value="审核完成" rendered="#{itm[12]==3}"/>
            <h:outputLabel value="地区变更" rendered="#{itm[12]==4}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="审核" action="#{mgrbean.modInitAction}" 
            	process="@this"  update=":tabView"  resetValues="true" rendered="#{itm[12]=='1' or itm[12]=='4'}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}" 
            	process="@this"  update=":tabView"  resetValues="true" rendered="#{itm[12]=='2' or itm[12]=='3'}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
	<ui:define name="insertOtherMainContents">
		<p:confirmDialog id="reviewConfirmDialog" message="确定要批量审核通过吗？" header="消息确认框" widgetVar="ReviewConfirmDialog" >
			<p:commandButton value="确定" action="#{mgrbean.reviewBatchAction}"
							 update="dataTable" icon="ui-icon-check"
							 oncomplete="PF('ReviewConfirmDialog').hide();datatableOffClick();"/>
			<p:commandButton value="取消" icon="ui-icon-close"
							 onclick="PF('ReviewConfirmDialog').hide();datatableOffClick()"
							 type="button"/>
		</p:confirmDialog>
	</ui:define>
</ui:composition>