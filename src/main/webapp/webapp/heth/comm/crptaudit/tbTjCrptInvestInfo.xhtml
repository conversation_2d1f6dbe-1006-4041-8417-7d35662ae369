<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
	<link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />
	<p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" rendered="#{2==mgrbean.crptInvest.stateMark}">
       <f:facet name="header">
             <p:row>
                 <p:column style="height:20px;text-align:left; " colspan="6">
                     <p:outputLabel value="退回原因"/>
                 </p:column>
             </p:row>
         </f:facet>
       <p:row>
       	  <p:column styleClass="column_title" style="width: 180px">
               <p:outputLabel value="退回原因：" style="color:red;"/>
          </p:column>
          <p:column style="text-align:left;">
               <p:inputTextarea rows="5" autoResize="false" readonly="true"
					 style="resize: none;width: 594px;color: red;"
					 value="#{mgrbean.crptInvest.backRsn}"
						 maxlength="200"/>
          </p:column>
       </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" id="zoneChangeInfo" rendered="#{null != mgrbean.crptInvest.stateMark and mgrbean.crptInvest.stateMark==4}">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="4">
                    <p:outputLabel value="地区变更信息"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 180px">
                <p:outputLabel value="地区变更信息："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;">
                <p:outputLabel value="#{mgrbean.orginZoneName} --> #{mgrbean.zoneName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
	<p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" rendered="#{null == mgrbean.crptInvest.stateMark or (mgrbean.crptInvest.stateMark!=4 and mgrbean.crptInvest.stateMark!=0 and mgrbean.crptInvest.stateMark!=5)}">
         <f:facet name="header">
             <p:row>
                 <p:column style="height:20px;text-align:left; " colspan="6">
                     <p:outputLabel value="填报信息"/>
                 </p:column>
             </p:row>
         </f:facet>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;width: 180px">
                 <p:outputLabel value="存在情况："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;width: #{mgrbean.crptInvest.existState==1?'200px':''};">
             	<p:outputLabel value="存在" rendered="#{1==mgrbean.crptInvest.existState}"></p:outputLabel>
             	<p:outputLabel value="不存在" rendered="#{0==mgrbean.crptInvest.existState}"></p:outputLabel>
             </p:column>
             <p:column styleClass="column_title" style="width: 180px" rendered="#{1==mgrbean.crptInvest.existState}">
                 <p:outputLabel value="生产情况："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;width: #{mgrbean.crptInvest.produceState==0?'200px':''};"
             		   rendered="#{1==mgrbean.crptInvest.existState}">
            	 <p:outputLabel value="正常生产" rendered="#{1==mgrbean.crptInvest.produceState}"></p:outputLabel>
             	 <p:outputLabel value="非正常生产" rendered="#{0==mgrbean.crptInvest.produceState}"></p:outputLabel>
             </p:column>
             <p:column styleClass="column_title" style="width: 180px" rendered="#{mgrbean.crptInvest.produceState==0}">
                 <p:outputLabel value="非正常生产情况："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;" rendered="#{mgrbean.crptInvest.produceState==0}">
             	<p:outputLabel value="#{mgrbean.crptInvest.fkByNoProduceStateId.codeName}"/>
             	<p:outputLabel value="（#{mgrbean.crptInvest.otherProduceState}）" rendered="#{mgrbean.crptInvest.fkByNoProduceStateId.extendS1==1}"/>
             </p:column>
         </p:row>
         <p:row rendered="#{1==mgrbean.crptInvest.existState and mgrbean.crptInvest.fkByIndusTypeId.extendS1==1}">
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="开采方式："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.selectMiningNames}"></p:outputLabel>
             </p:column>
             <p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="含铀情况："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;" colspan="3">
             	<p:outputLabel value="含铀" rendered="#{1==mgrbean.crptInvest.ifHasUranium}"></p:outputLabel>
             	<p:outputLabel value="不含铀" rendered="#{0==mgrbean.crptInvest.ifHasUranium}"></p:outputLabel>
             </p:column>
         </p:row>
     </p:panelGrid>

     <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" id="crptInfo">
         <f:facet name="header">
             <p:row>
                 <p:column style="height:20px;text-align:left; " colspan="4">
                     <p:outputLabel value="用人单位情况"/>
                 </p:column>
             </p:row>
         </f:facet>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;width: 180px">
                 <p:outputLabel value="所属地区："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
                 <p:outputLabel value="#{mgrbean.zoneName}"></p:outputLabel>
                 <p:spacer width="5" />
                 <p:commandButton icon="ui-icon-pencil" style="margin-top: 2px;margin-left: 3px;" value="地区变更"
                                  action="#{mgrbean.openChangeZoneDialogAction}" process="@this" resetValues="true" rendered="#{isShowChangeZone == 1  and mgrbean.crptInvest.stateMark==4}" />
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="用人单位名称："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
                 <p:outputLabel value="#{mgrbean.crptInvest.crptName}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="社会信用代码："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.institutionCode}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="是否分支机构："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="是" rendered="#{1==mgrbean.crptInvest.ifSubOrg}"></p:outputLabel>
             	<p:outputLabel value="否" rendered="#{0==mgrbean.crptInvest.ifSubOrg}"></p:outputLabel>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="单位地址："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.address}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="行业类别："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.fkByIndusTypeId.codeName}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="经济类型："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.fkByEconomyId.codeName}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="企业规模："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.fkByCrptSizeId.codeName}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="法人："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.corporateDeputy}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="法人联系电话："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.phone}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="联系人："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.linkman2}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="联系人电话："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.linkphone2}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="职工总人数："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.workForce}"/>
             </p:column>
         </p:row>
         <p:row>
         	<p:column styleClass="column_title" style="height:30px;">
                 <p:outputLabel value="接害总人数："/>
             </p:column>
             <p:column style="text-align:left;padding-left:8px;">
             	<p:outputLabel value="#{mgrbean.crptInvest.holdCardMan}"/>
             </p:column>
         </p:row>
     </p:panelGrid>
</ui:composition>