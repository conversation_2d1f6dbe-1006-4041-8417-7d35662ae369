<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位信息审核" />
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="审核通过" icon="ui-icon-check" rendered="#{mgrbean.crptInvest.stateMark!=4}"
                                 action="#{mgrbean.passAction}" process="@this,:tabView:editForm" >
                    <p:confirm header="消息确认框" message="确定审核通过吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="退回" icon="ui-icon-cancel" rendered="#{mgrbean.crptInvest.stateMark!=4}"
                                  action="#{mgrbean.preBackAction}" process="@this" />
                <p:commandButton value="确认" icon="ui-icon-check" rendered="#{mgrbean.crptInvest.stateMark==4}"
                                 action="#{mgrbean.submitZoneChangeAction}" process="@this,:tabView:editForm">
                    <p:confirm icon="ui-icon-alert" message="确定要确认吗？" header="消息提示框"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <ui:include src="tbTjCrptInvestInfo.xhtml">
            <ui:param value="1" name="isShowChangeZone"/>
        </ui:include>

        <!-- 退回原因 -->
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.backRsn}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="reasonContent" maxlength="200" />
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         oncomplete="PF('ReasonDialog').hide();"
                                         process="@this,reasonContent"
                                         action="#{mgrbean.returnBackAction}" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <!-- 地区变更弹出框 -->
        <p:dialog id="zoneChangeDialog" header="地区变更" modal="true" resizable="false" widgetVar="ZoneChangeDialog" width="480" height="165">
            <p:panelGrid id="zoneChangePanel" style="width:100%;">
                <p:row>
                    <p:column style="text-align: right;width: 120px;height: 31px;">
                        <font color="red">*</font>
                        <h:outputText value="变更地区："/>
                    </p:column>
                    <p:column style="text-align: left;padding-left: 3px;padding-bottom: 16px;">
                        <p:outputPanel>
                            <h:panelGrid style="border-color: transparent;padding-top: 10px;">
                                <p:row>
                                    <p:column>
                                        <zwx:ZoneSingleComp zoneList="#{mgrbean.changeZoneList}"
                                                            zoneCode="#{mgrbean.changeZoneCode}"
                                                            zoneId = "#{mgrbean.changeZoneId}"
                                                            zoneName="#{mgrbean.changeZoneName}"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:outputLabel value="说明：" style="color: red;padding-left: 6px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:outputLabel value="1、本辖区内请选择至区县及以下地区！" style="color: blue;padding-left: 6px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:outputLabel value="2、跨市级变更，需要变更后市级机构确认！" style="color: blue;padding-left: 6px;"/>
                                    </p:column>
                                </p:row>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确认" icon="ui-icon-check" action="#{mgrbean.saveChangeZoneAction}" process="@this,zoneChangeDialog"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ZoneChangeDialog').hide();" immediate="true" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <!-- 地区变更确认框 -->
        <p:confirmDialog style="top: 26px;" id="zoneChangeConfirmDialog" message="用人单位将变更到#{mgrbean.changeZoneFullName}进行确认，是否继续？" header="消息确认框" widgetVar="ZoneChangeConfirmDialog" >
            <p:commandButton value="确定" action="#{mgrbean.zoneChangeConfirmAction}" icon="ui-icon-check"
                             oncomplete="PF('ZoneChangeConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('ZoneChangeConfirmDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>
</ui:composition>
