<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				template="/WEB-INF/templates/system/editTemplate.xhtml">
	<ui:define name="insertEditScripts">
		<style type="text/css">
			.checkboxVisible .ui-state-disabled{
				opacity: 1;
			}
			.ui-state-hover .icon-alert{
				background-image: url(/resources/images/alert-tip-w.png) !important;
				background-size: 12px 12px;
				margin-left: 3px;
				margin-top: -6px !important;
			}
			.ui-state-hover .icon-alert{
				color : #fff;
			}

		</style>
		<h:outputStylesheet library="css" name="default.css" />
		<h:outputStylesheet library="css" name="ui-tabs.css" />
		<script type="text/javascript"
				src="/resources/echarts/3.0/echarts.min.js"></script>
		<script type="text/javascript"
				src="/resources/echarts/3.0/macarons.js"></script>
		<script type="text/javascript">
			var chatLeft ;
			var chatRight ;

			function buildChart() {
				if (chatLeft != null) {
					chatLeft.clear();
				}
				var leftDiv = document.getElementById("chartLeft");
				if (leftDiv != null) {
					chatLeft = echarts.init(leftDiv, 'macarons');
					var option = document.getElementById("tabView:editForm:showTabView:xml1").value;
					console.log("左"+option);
					if (option != '') {
						if(null != option){
							chatLeft.setOption( eval("(" + option + ")")	 );
						}
					}
				}

				if (chatRight != null) {
					chatRight.clear();
				}
				var rightDiv = document.getElementById("chartRight");
				if (rightDiv != null) {
					chatRight = echarts.init(rightDiv, 'macarons');
					var option = document.getElementById("tabView:editForm:showTabView:xml2").value;
					console.log("右1"+option);
					if (option != '') {
						if( null != option){
							chatRight.setOption( eval("(" + option + ")")	 );
						}
					}
				}
			}
		</script>
	</ui:define>

	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="用人单位综合展示" />
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
								 action="#{mgrbean.backAction}" process="@this"
								 update=":tabView" />
				<p:inputText style="visibility: hidden;width: 0"/>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">
		<p:outputPanel style="border-top-color: transparent;">
			<!--基本信息-->
			<p:fieldset legend="基本信息"  style="margin-top: 5px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;margin-top:5px;margin-bottom:5px;" id="dataSourceGrid" >
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="单位名称："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.crptName}" />
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="所属地区："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<h:outputText escape="false" value="#{mgrbean.tbTjCrptCheck.fkByZoneId.zoneType>3?mgrbean.tbTjCrptCheck.fkByZoneId.fullName.substring(mgrbean.tbTjCrptCheck.fkByZoneId.fullName.indexOf('_')+1,mgrbean.tbTjCrptCheck.fkByZoneId.fullName.length()):mgrbean.tbTjCrptCheck.fkByZoneId.zoneName}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="社会信用代码："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.institutionCode}" />
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="是否分支机构："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="否" rendered="#{mgrbean.tbTjCrptCheck.ifSubOrg == 0}"  />
							<p:outputLabel value="是" rendered="#{mgrbean.tbTjCrptCheck.ifSubOrg == 1}"  />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="单位地址："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;" colspan="3">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.address}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="经济类型："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.fkByEconomyId.codeName}" />
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="企业规模："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.fkByCrptSizeId.codeName}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="行业类别："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;" colspan="3">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.fkByIndusTypeId.codeName}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="法人："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.corporateDeputy}" />
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="法人联系电话："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.phone}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="联系人："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.linkman2}" />
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="联系人电话："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.linkphone2}" />
						</p:column>
					</p:row>


					<p:row>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="职工人数："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;width:300px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.workForce}" />
						</p:column>
						<p:column style="text-align:right;width:180px;height: 38px;">
							<p:outputLabel value="接触职业病危害因素人数："/>
						</p:column>
						<p:column style="text-align:left;padding-left:10px;">
							<p:outputLabel value="#{mgrbean.tbTjCrptCheck.holdCardMan}" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>
			<!-- 提示 -->
			<p:fieldset legend="提示"  style="margin-top: 20px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;">
					<c:if test="#{mgrbean.badConditionList.size()>0 }">
						<c:forEach items="#{mgrbean.badConditionList}" var="itm" varStatus="varStatus"  >
							<p:row>
								<p:column style="text-align:left;height: 30px;border-color: transparent;padding: 0 0 0 10px;">
									<p:outputLabel value="#{varStatus.index+1}、#{itm}"/>
								</p:column>
							</p:row>
						</c:forEach>
					</c:if>
					<c:if test="#{mgrbean.badConditionList.size()==0 }">
						<p:row>
							<p:column style="text-align:left;height: 30px;border-color: transparent;padding: 0 0 0 10px;">
								<p:outputLabel value="无"/>
							</p:column>
						</p:row>
					</c:if>
				</p:panelGrid>
			</p:fieldset>
			<!-- 综合分析展示 -->
			<p:fieldset legend="综合分析展示"  style="margin-top: 20px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;margin-top:5px;margin-bottom:5px;" id="analyGrid">
					<c:if test="#{mgrbean.msgList.size()>0 }">
						<p:row>
							<c:forEach items="#{mgrbean.msgList}" var="itm" varStatus="varStatus"  >
								<p:column style="padding-left: 0px;padding-right:15px;padding-bottom:13px;border-color: transparent;height: 71px;width: 230px;" >
									<div style="cursor:pointer;width: 220px;height: 70px;opacity: 1;border-radius: 6pt;background-color: #{itm[3]}; " onmouseover="this.style.boxShadow='3px 3px 10px #909090';this.style.height='71px';"
										 onmouseout="this.style.boxShadow='none';this.style.height='70px';"
										 onclick="jumpAction#{varStatus.index}()"  >
										<p:remoteCommand name="jumpAction#{varStatus.index}" process="@this,:tabView:editForm:analyGrid"
														 onstart="showStatus()" oncomplete="hideStatus()"
														 action="#{mgrbean.analyAction}"
														 update=":tabView:editForm:analyGrid,:tabView:editForm:showTabView">
											<f:setPropertyActionListener target="#{mgrbean.analyType}" value="#{itm[0]}"/>
										</p:remoteCommand>
										<div style="margin-top: 0px;text-align: center;width: 210px; height: 15px;">
	                            			<span style="opacity: 1;font-size: 18px;font-family: PingFangSC;color:white;line-height: 15px;letter-spacing: 0px;position: relative;top: 26px; ">
		                            			<h:outputText value="#{itm[1]}"/>
		                            		</span>
										</div>
										<div style="position: relative;top: -14px;width: 220px;height: 70px;">
											<img  style="width: 219px;height: 70px;opacity: 1;"  align="bottom" src="#{itm[8]}" />
										</div>
									</div>
								</p:column>
							</c:forEach>
							<p:column style="border-color:transparent; "></p:column>
						</p:row>
					</c:if>
				</p:panelGrid>
				<p:tabView id="showTabView" dynamic="true" cache="true" activeIndex="#{mgrbean.analyType}" style="border:1px; padding:0px;">
					<p:tab title="badRsnAnaly" titleStyle="display:none;">
						<!-- 危害因素分析 -->
						<!-- 危害因素分析 analyType 0 start  -->
						<p:outputPanel id="dangerDataPanel">
							<div style="display:flex;margin-top: 10px;margin-bottom: 8px;" >
								<p:spacer width="8" />
								<p:outputLabel  value="年份："/>
								<p:selectOneMenu value="#{mgrbean.year}" style="width: 100px;position: relative; top: -5px;left: 5px;" onchange="showStatus()" >
									<f:selectItems value="#{mgrbean.yearList}" var="yearItem"
												   itemLabel="#{yearItem}" itemValue="#{yearItem}"/>
									<p:ajax event="change" listener="#{mgrbean.changeYear}"
											process="@this,:tabView:editForm" update=":tabView:editForm:showTabView:dangerDataPanel" oncomplete="hideStatus()"  />
								</p:selectOneMenu>
							</div>
							<p:dataTable var="itm" value="#{mgrbean.dangerTableList}"
										 emptyMessage="没有数据！" style="line-height: 31px;" >
								<p:column headerText="危害因素类别" style="width:25%;text-align: center">
									<h:outputLabel value="#{itm.dangerType}"/>
								</p:column>
								<p:column headerText="检查人数（健康检查）" style="width:25%;text-align: center">
									<h:outputLabel value="#{itm.checkNumber == null ? '/' : itm.checkNumber}" rendered="#{!itm.unMatch}" />
									<h:outputLabel value="#{itm.checkNumber == null ? '/' : itm.checkNumber}" style="color: red;" rendered="#{itm.unMatch}" />
								</p:column>
								<p:column headerText="接害人数（企业申报）" style="width:25%;text-align: center">
									<h:outputLabel value="#{itm.reciveNumber == null ? '/' : itm.reciveNumber}" rendered="#{!itm.unMatch}" />
									<h:outputLabel value="#{itm.reciveNumber == null ? '/' : itm.reciveNumber}" style="color: red;" rendered="#{itm.unMatch}" />
								</p:column>
								<p:column headerText="检查人数（企业申报）" style="text-align: center">
									<h:outputLabel value="#{itm.crptCheckNumber == null ? '/' : itm.crptCheckNumber}"  />
								</p:column>
							</p:dataTable>
							<br/>
							<h:inputHidden id="xml1" value="#{mgrbean.lineLeftJson}"/>
							<h:inputHidden id="xml2" value="#{mgrbean.lineRightJson}"/>
							<p:fieldset legend="统计图表">
								<table width="100%" >
									<tr>
										<td width="48%">
											<!-- 名称体检机构换成健康检查 -->
											<div id="chartLeft" style="height: 500px;"></div>
										</td>
										<!-- style=" border-right-style: solid; border-right-width: 1px; " -->
										<td >
											<div style="height:459px; padding-left: 50%; width:1px; border-right:1px darkgrey solid" />
										</td>
										<td width="48%">
											<div id="chartRight" style="height: 500px;"></div>
										</td>
									</tr>
								</table>
							</p:fieldset>
						</p:outputPanel>
						<!-- 危害因素分析 end  -->
					</p:tab>
					<p:tab title="personAnaly" titleStyle="display:none;">
						<!-- 人群分析 -->
					</p:tab>
					<p:tab title="tjData" titleStyle="display:none;">
						<!-- 体检情况 -->
					</p:tab>
					<p:tab title="onLineReport" titleStyle="display:none;">
						<!-- 在线申报情况 -->
						<ui:include src="/webapp/heth/comm/crptshow/onLineCrptTableList.xhtml"></ui:include>
					</p:tab>
					<p:tab title="onLineCheck" titleStyle="display:none;">
						<!-- 在线检测情况 -->
					</p:tab>
					<p:tab title="whysdb" titleStyle="display:none;">
						<ui:include src="/webapp/heth/comm/crptshow/whysdb.xhtml"></ui:include>
					</p:tab>
					<p:tab title="cbycfx" titleStyle="display:none;">
						<ui:include src="/webapp/heth/comm/crptshow/cbycfx.xhtml"></ui:include>
					</p:tab>
					<p:tab title="gwdb" titleStyle="display:none;">
						<ui:include src="/webapp/heth/comm/crptshow/gwdb.xhtml"></ui:include>
					</p:tab>
					<p:tab title="csjc" titleStyle="display:none;">
						<ui:include src="/webapp/heth/comm/crptshow/csjc.xhtml"></ui:include>
					</p:tab>
				</p:tabView>
			</p:fieldset>
		</p:outputPanel>
	</ui:define>
</ui:composition>
