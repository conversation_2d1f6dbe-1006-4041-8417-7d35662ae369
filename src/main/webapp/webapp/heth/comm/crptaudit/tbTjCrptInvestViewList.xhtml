<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjCrptInvestViewListBean}"/>
	<ui:param name="viewPage" value="/webapp/heth/comm/crptaudit/tbTjCrptInvestView.xhtml" />
	<ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style type="text/css">
        	table.ui-selectoneradio td label{
			 	white-space:nowrap;
			    overflow: hidden;
			}
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位信息查询"/>
            </p:column>
        </p:row>
    </ui:define>


    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_h_auto">
            <span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
            <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                             update="dataTable" process="@this,mainGrid" />
            <p:spacer width="5"/>
            <p:commandButton value="导出" ajax="false" icon="ui-icon-document" id="exportBtn"
                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
            >
                <p:fileDownload value="#{mgrbean.downloadFile}"/>
            </p:commandButton>
        </p:outputPanel>

    </ui:define>

    <!-- 查询条件 -->
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputLabel value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:240px;">
                <h:panelGrid columns="3" style="border-color: transparent;margin: -9px;padding: 0px;">
                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.searchZoneList}"
                                        zoneCode="#{mgrbean.searchZoneCode}"
                                        zoneName="#{mgrbean.searchZoneName}"
                                        zoneId="#{mgrbean.searchZoneId}"/>
                </h:panelGrid>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width:240px;">
                <p:inputText value="#{mgrbean.searchUnitName}" style="width:200px;"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" colspan="5">
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width:200px;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="indusTypeName"
                                         value="#{mgrbean.selectIndusTypeNames}"
                                         style="width: 180px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           id="selIndusTypeLink"
                                           action="#{mgrbean.selSimpleCodeAction}" process="@this"
                                           style="position: relative;left: -28px !important;">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
                                        resetValues="true" update="indusTypeName" />
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="encomyName"
                                         value="#{mgrbean.selectEconomyNames}"
                                         style="width: 200px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selEncomyLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           id="selEncomyLink"
                                           action="#{mgrbean.selSimpleCodeAction}" process="@this"
                                           style="position: relative;left: -28px !important;">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
                                        resetValues="true" update="encomyName" />
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" >
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectCrptSizes}"
                                        selectedIds="#{mgrbean.selectCrptSizeIds}"
                                        simpleCodeList="#{mgrbean.crptSizeList}"
                                        inputWidth="200"
                                        panelWidth="210"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>
        <!--20210728新增-->
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="存在情况："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;">
                <p:selectManyCheckbox  value="#{mgrbean.existArray}">
                    <f:selectItem itemLabel="存在" itemValue="1"></f:selectItem>
                    <f:selectItem itemLabel="不存在" itemValue="0"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="生产情况："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;">
                <p:selectManyCheckbox  value="#{mgrbean.productArray}">
                    <f:selectItem itemLabel="正常" itemValue="1"></f:selectItem>
                    <f:selectItem itemLabel="非正常" itemValue="0"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="非正常生产情况："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectUnpros}"
                                        selectedIds="#{mgrbean.selectUnproIds}"
                                        simpleCodeList="#{mgrbean.unproducteList}"
                                        inputWidth="200"
                                        panelWidth="210"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="开采方式："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectMinings}"
                                        selectedIds="#{mgrbean.selectMiningIds}"
                                        simpleCodeList="#{mgrbean.miningList}"
                                        inputWidth="180"
                                        panelWidth="190"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="是否含铀："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" colspan="3">
                <p:selectManyCheckbox  value="#{mgrbean.uraniumArray}">
                    <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                    <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.stateArray}" >
                    <f:selectItem itemLabel="待填报" itemValue="0,5"></f:selectItem>
                    <f:selectItem itemLabel="待审核" itemValue="1"></f:selectItem>
                    <f:selectItem itemLabel="地区变更" itemValue="4"></f:selectItem>
                    <f:selectItem itemLabel="已退回" itemValue="2"></f:selectItem>
                    <f:selectItem itemLabel="审核完成" itemValue="3"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>


    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width:150px;height:22px;padding-left: 8px;">
            <h:outputText value="#{itm[0]}" />
        </p:column>
        <p:column headerText="用人单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="社会信用代码" style="width:120px;text-align:center; ">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="行业类别" style="width:150px;text-align:center; ">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="经济类型" style="width:150px;text-align:center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="企业规模" style="width:60px;text-align:center; ">
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="联系人" style="width:100px;text-align:center; ">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width:100px;text-align:center; ">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="存在情况" style="width:60px;text-align:center; ">
            <h:outputText value="#{null == itm[8] ? '' : '1' == itm[8] ? '存在' : '0' == itm[8] ?  '不存在' : ''}"/>
        </p:column>
        <p:column headerText="填报单位" style="width:210px; padding-left: 8px;">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="填报日期" style="width:80px;text-align:center; ">
            <h:outputLabel value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px;width:80px;text-align:center; ">
            <font style="color:red;" ><h:outputText value="已退回" rendered="#{null != itm[11] and itm[11] == '2'}" /></font>
            <h:outputText value="#{itm[11] == '0' ? '待填报' : itm[11] == '1' ? '待审核' : itm[11] == '3' ? '审核完成' : itm[11] == '4' ? '地区变更' : itm[11] == '5' ? '待填报' : ''}" rendered="#{null != itm[11] and itm[11] != '2'}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5" />
            <p:commandLink value="详情"  process="@this"
            	action="#{mgrbean.viewInitAction}" update=":tabView">
            	<f:setPropertyActionListener value="#{itm[12]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>