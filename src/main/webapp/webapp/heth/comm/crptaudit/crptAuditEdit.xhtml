<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="用人单位综合管理" />
			</p:column>
		</p:row>
	</ui:define>
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="暂存" icon="ui-icon-disk"
								 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" />
				<p:commandButton value="提交" icon="ui-icon-check"
								 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm">
				</p:commandButton>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
								 action="#{mgrbean.backAction}" process="@this"
								 update=":tabView" oncomplete="datatableOffClick()"/>
				<p:inputText style="visibility: hidden;width: 0"/>
			</h:panelGrid>
			<p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
		        <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" oncomplete="PF('ConfirmDialog').hide();datatableOffClick();"/>
		        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
		    </p:confirmDialog>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertOtherContents">
		<p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" rendered="#{2==mgrbean.crptInvest.stateMark}">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="退回原因"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
            	<p:column styleClass="column_title" style="width: 180px">
                    <p:outputLabel value="退回原因：" style="color:red;"/>
                </p:column>
                <p:column style="text-align:left;">
                    <p:inputTextarea rows="5" autoResize="false" readonly="true"
						 style="resize: none;width: 594px;color: red;"
						 value="#{mgrbean.crptInvest.backRsn}"
						 maxlength="200"/>
                </p:column>
            </p:row>
        </p:panelGrid>
		<p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" id="investInfo">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="填报信息"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
            	<p:column styleClass="column_title" style="height:38px;width: 180px">
            		<h:outputText value="*" style="color:red;" />
                    <p:outputLabel value="存在情况："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;width: 260px;">
                    <p:selectOneRadio value="#{mgrbean.crptInvest.existState}">
                    	<f:selectItem itemValue="1" itemLabel="存在"></f:selectItem>
                    	<f:selectItem itemValue="0" itemLabel="不存在"></f:selectItem>
                    	<p:ajax event="change" process="@this,investInfo,crptInfo" update="investInfo,crptInfo"></p:ajax>
                    </p:selectOneRadio>
                </p:column>
                <p:column styleClass="column_title" style="width: 180px">
                	<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="生产情况："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;width: #{mgrbean.crptInvest.produceState==0?'240px':''};">
               		<p:selectOneRadio value="#{mgrbean.crptInvest.produceState}" disabled="#{mgrbean.crptInvest.existState==0}">
                    	<f:selectItem itemValue="1" itemLabel="正常生产"></f:selectItem>
                    	<f:selectItem itemValue="0" itemLabel="非正常生产"></f:selectItem>
                    	<p:ajax event="change" process="@this,investInfo" update="investInfo"></p:ajax>
                    </p:selectOneRadio>
                </p:column>
                <p:column styleClass="column_title" style="width: 180px" rendered="#{mgrbean.crptInvest.produceState==0}">
                	<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="非正常生产情况："/>
                </p:column>
                <p:column style="text-align:left;padding:0px;display:#{mgrbean.crptInvest.produceState==0?'':'none'}">
                	<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
	                	<p:selectOneMenu value="#{mgrbean.crptInvest.fkByNoProduceStateId.rid}" 
	                    	disabled="#{mgrbean.crptInvest.existState==0}" style="width:120px;">
	                    	<f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
	                    	<f:selectItems value="#{mgrbean.noProduceStateList}" var="itm" 
	                    		itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"></f:selectItems>
	                    	<p:ajax event="change" process="@this,investInfo" update="investInfo"/>
	                    </p:selectOneMenu>
	                    <p:inputText value="#{mgrbean.crptInvest.otherProduceState}" maxlength="25" 
	                    	disabled="#{mgrbean.crptInvest.existState==0}"
	                    	style="width: 160px;display:#{mgrbean.noProduceStateMap.get(mgrbean.crptInvest.fkByNoProduceStateId.rid).extendS1==1?'':'none'}"></p:inputText>
                	</h:panelGrid>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.crptInvest.fkByIndusTypeId.extendS1==1}">
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="开采方式："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                	<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="miningGrid">
						<p:inputText id="miningName" value="#{mgrbean.selectMiningNames}" style="width: 180px;" readonly="true" 
							disabled="#{mgrbean.crptInvest.existState==0}"/>
						<p:commandLink styleClass="ui-icon ui-icon-search" process="@this"  style="position: relative;left: -30px;"
									   oncomplete="PF('MiningPanel').show()" type="button" 
									   disabled="#{mgrbean.crptInvest.existState==0}"/>
						<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" 
									   style="position: relative;left: -33px;" 
									   action="#{mgrbean.clearSelectMining}" process="@this" 
									   update="miningGrid,miningPanel"
									   disabled="#{mgrbean.crptInvest.existState==0}"/>
					</h:panelGrid>
					<p:remoteCommand name="hideMining" process="@this,miningPanel"
									 action="#{mgrbean.hideMiningAction}"
									 update="miningGrid"/>
					<p:overlayPanel id="miningPanel" for="miningName"
									style="width:190px;" widgetVar="MiningPanel"
									showCloseIcon="true" onHide="hideMining();">
						<p:tree var="node" selectionMode="checkbox"
								value="#{mgrbean.miningSortTree}"
								style="width: 160px;height: 200px;overflow-y: auto;">
							<p:ajax event="select"  process="@this" listener="#{mgrbean.onNodeSelect}"/>
							<p:ajax event="unselect"  process="@this" listener="#{mgrbean.onNodeNoSelect}"/>
							<p:treeNode>
								<p:outputLabel value="#{node.codeName}" />
							</p:treeNode>
						</p:tree>
					</p:overlayPanel>
                </p:column>
                <p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="含铀情况："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="3">
                    <p:selectOneRadio value="#{mgrbean.crptInvest.ifHasUranium}"
                    	disabled="#{mgrbean.crptInvest.existState==0}">
                    	<f:selectItem itemValue="1" itemLabel="含铀"></f:selectItem>
                    	<f:selectItem itemValue="0" itemLabel="不含铀"></f:selectItem>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 比对信息 -->
        <p:overlayPanel id="comparePanel" for="#{mgrbean.compareLink}" dynamic="false"
                        style="width: 420px;" widgetVar="ComparePanel" showCloseIcon="true" appendToBody="false">
            <p:panelGrid style="width:100%;">
                <c:forEach items="#{mgrbean.showCompareList}" var="itm" varStatus="varStatus">
                    <p:row>
                        <p:column style="text-align: left;padding-left: 8px;width: 30px;" rendered="#{mgrbean.compareType!=1}">
                            <p:selectBooleanCheckbox value="#{itm.ifselected}">
                                <p:ajax event="change" listener="#{mgrbean.compareChangeAction(itm)}" process="@this" oncomplete="PF(PF('ComparePanel').hide())"
                                        update=":tabView:editForm:comparePanel,:tabView:editForm:crptInfo,:tabView:editForm:investInfo"/>
                            </p:selectBooleanCheckbox>
                        </p:column>
                        <p:column style="text-align: left;padding-left: 8px;width: 120px;height: 28px;">
                            <h:outputText value="#{itm.fkByInfoSourceId.codeName}"/>
                        </p:column>
                        <p:column style="text-align: left;padding-left: 8px;" >
                            <h:outputText value="#{itm.fullName}" rendered="#{mgrbean.compareType==1}"/>
                            <h:outputText value="#{itm.crptName}" rendered="#{mgrbean.compareType==2}"/>
                            <h:outputText value="#{itm.address}" rendered="#{mgrbean.compareType==3}"/>
                            <h:outputText value="#{itm.fkByIndusTypeId.codeName}" rendered="#{mgrbean.compareType==4}"/>
                            <h:outputText value="#{itm.fkByEconomyId.codeName}" rendered="#{mgrbean.compareType==5}"/>
                            <h:outputText value="#{itm.fkByCrptSizeId.codeName}" rendered="#{mgrbean.compareType==6}"/>
                            <h:outputText value="#{itm.corporateDeputy}" rendered="#{mgrbean.compareType==7}"/>
                            <h:outputText value="#{itm.phone}" rendered="#{mgrbean.compareType==8}"/>
                            <h:outputText value="#{itm.linkman2}" rendered="#{mgrbean.compareType==9}"/>
                            <h:outputText value="#{itm.linkphone2}" rendered="#{mgrbean.compareType==10}"/>
                            <h:outputText value="#{itm.workForce}" rendered="#{mgrbean.compareType==11}"/>
                            <h:outputText value="#{itm.holdCardMan}" rendered="#{mgrbean.compareType==12}"/>
                        </p:column>
                    </p:row>
                </c:forEach>
            </p:panelGrid>
        </p:overlayPanel>

        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;width:100%" id="crptInfo">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="4">
                        <p:outputLabel value="用人单位情况"/>
                        <p:outputLabel value="（综合对比后推荐信息）" rendered="#{mgrbean.crptInvest.stateMark==0 or mgrbean.crptInvest.stateMark==5}"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;width: 180px">
                    <p:outputLabel value="所属地区："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                        <p:outputLabel value="#{mgrbean.zoneName}"></p:outputLabel>
                        <p:spacer width="5" />
                        <p:commandButton icon="ui-icon-pencil" style="margin-top: 2px;margin-left: 3px;" value="地区变更"
                                         action="#{mgrbean.openChangeZoneDialogAction}" process="@this" resetValues="true"/>
                        <p:outputPanel rendered="#{mgrbean.showZoneNameCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="zoneNameCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="1" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="zoneNameCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="用人单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                        <p:inputText value="#{mgrbean.crptInvest.crptName}" style="width: 260px;" maxlength="50"></p:inputText>
                        <p:outputPanel rendered="#{mgrbean.showCrptNameCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="crptNameCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="2" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="crptNameCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="社会信用代码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                	<p:inputText value="#{mgrbean.crptInvest.institutionCode}" maxlength="25" style="width: 180px;"></p:inputText>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="是否分支机构："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                	<p:selectOneRadio value="#{mgrbean.crptInvest.ifSubOrg}">
                		<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                		<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                	</p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="单位地址："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                	    <p:inputText value="#{mgrbean.crptInvest.address}" style="width: 400px;" maxlength="100"></p:inputText>
                        <p:outputPanel rendered="#{mgrbean.showAddressCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="addressCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="3" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="addressCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="行业类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                	<table>
						<tr>
							<td style="padding: 0;border-color: transparent;">
								<p:inputText id="editIndusTypeName"
									value="#{mgrbean.crptInvest.fkByIndusTypeId.codeName}"
									style="width: 180px;cursor: pointer;"
									onclick="document.getElementById('tabView:editForm:editSelIndusTypeLink').click();"
									readonly="true"/>
							</td>
							<td style="border-color: transparent;">
	                            <p:commandLink styleClass="ui-icon ui-icon-search"
	                                           id="editSelIndusTypeLink"
	                                           action="#{mgrbean.selIndusCodeTypeAction}" process="@this,investInfo"
                                               oncomplete="PF('selIndusTypeDialog').show()"
                                               update=":tabView:editForm:selectedIndusCodeTable,:tabView:editForm:searchIndusPanel"
	                                           style="position: relative;left: -28px !important;" />
	                        </td>
	                        <!-- 清空 -->
	                        <td style="border-color: transparent;position: relative;left: -30px;">
	                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
	                                           process="@this,investInfo" update="editIndusTypeName,investInfo" action="#{mgrbean.clearEditSimpleCode}">
	                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
	                            </p:commandLink>
	                        </td>
                            <td style="border-color: transparent;position: relative;left: -25px;">
                                <p:outputPanel rendered="#{mgrbean.showIndusTypeCompare}">
                                    <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                                    <p:commandLink id="indusTypeCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                                   oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                        <f:setPropertyActionListener value="4" target="#{mgrbean.compareType}"/>
                                        <f:setPropertyActionListener value="indusTypeCompareLink" target="#{mgrbean.compareLink}"/>
                                    </p:commandLink>
                                </p:outputPanel>
                            </td>
						</tr>
					</table>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="经济类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                	<table>
						<tr>
							<td style="padding: 0;border-color: transparent;">
								<p:inputText id="editEconomyName"
									value="#{mgrbean.crptInvest.fkByEconomyId.codeName}"
									style="width: 180px;cursor: pointer;"
									onclick="document.getElementById('tabView:editForm:editSelEncomyLink').click();"
									readonly="true"/>
							</td>
							<td style="border-color: transparent;">
	                            <p:commandLink styleClass="ui-icon ui-icon-search"
	                                           id="editSelEncomyLink"
	                                           action="#{mgrbean.editSelSimpleCodeAction}" process="@this"
	                                           style="position: relative;left: -28px !important;">
	                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
	                                <p:ajax event="dialogReturn" listener="#{mgrbean.onEditSimpleCodeAction}" process="@this"
	                                        resetValues="true" update="editEconomyName" />
	                            </p:commandLink>
	                        </td>
	                        <!-- 清空 -->
	                        <td style="border-color: transparent;position: relative;left: -30px;">
	                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
	                                           process="@this" update="editEconomyName" action="#{mgrbean.clearEditSimpleCode}">
	                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
	                            </p:commandLink>
	                        </td>
                            <td style="border-color: transparent;position: relative;left: -25px;">
                                <p:outputPanel rendered="#{mgrbean.showEconomyCompare}">
                                    <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                                    <p:commandLink id="economyCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                                   oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                        <f:setPropertyActionListener value="5" target="#{mgrbean.compareType}"/>
                                        <f:setPropertyActionListener value="economyCompareLink" target="#{mgrbean.compareLink}"/>
                                    </p:commandLink>
                                </p:outputPanel>
                            </td>
						</tr>
					</table>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
            		<h:outputText value="*" style="color:red;" rendered="#{mgrbean.crptInvest.existState==1}"/>
                    <p:outputLabel value="企业规模："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                        <p:selectOneMenu
                                value="#{mgrbean.crptInvest.fkByCrptSizeId.rid}"
                                style="width: 188px;margin-bottom: -5px;">
                            <f:selectItem itemLabel="--请选择--" itemValue="" />
                            <f:selectItems value="#{mgrbean.crptSizeList}" var="itm"
                                           itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"  />
                        </p:selectOneMenu>
                        <p:outputPanel rendered="#{mgrbean.showCrptSizeCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="crptSizeCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="6" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="crptSizeCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="法人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                	    <p:inputText value="#{mgrbean.crptInvest.corporateDeputy}" style="width: 180px;" maxlength="25"></p:inputText>
                        <p:outputPanel rendered="#{mgrbean.showCorporateDeputyCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="corporateDeputyCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="7" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="corporateDeputyCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="法人联系电话："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                	    <p:inputText value="#{mgrbean.crptInvest.phone}" style="width: 180px;" maxlength="20"></p:inputText>
                	    <p:outputLabel value="（格式：0510-85373786/13616161616）" style="color: red"/>
                        <p:outputPanel rendered="#{mgrbean.showPhoneCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="phoneCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="8" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="phoneCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="联系人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                	    <p:inputText value="#{mgrbean.crptInvest.linkman2}" style="width: 180px;" maxlength="25"></p:inputText>
                        <p:outputPanel rendered="#{mgrbean.showLinkmanCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="linkmanCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="9" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="linkmanCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="联系人电话："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                    	<p:inputText value="#{mgrbean.crptInvest.linkphone2}" style="width: 180px;" maxlength="20"></p:inputText>
                	    <p:outputLabel value=" （格式：0510-85373786/13616161616）" style="color: red"/>
                        <p:outputPanel rendered="#{mgrbean.showLinkphoneCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="linkphoneCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="10" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="linkphoneCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="职工总人数："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                	    <p:inputText value="#{mgrbean.crptInvest.workForce}" style="width: 180px;" maxlength="8"
                		    onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"></p:inputText>
                        <p:outputPanel rendered="#{mgrbean.showWorkForceCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="workForceCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="11" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="workForceCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
            	<p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="接害总人数："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                	    <p:inputText value="#{mgrbean.crptInvest.holdCardMan}" style="width: 180px;" maxlength="8"
                		    onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"></p:inputText>
                        <p:outputPanel rendered="#{mgrbean.showHoldCardManCompare}" style="padding-left: 20px;">
                            <img style='vertical-align: sub;padding: 0 5px 1px 0;width: 15px;height: 15px;' src="/resources/images/alert.svg"/>
                            <p:commandLink id="holdCardManCompareLink" value="比对其它数据源信息" action="#{mgrbean.openCompareOverlayPanel}"
                                           oncomplete="PF('ComparePanel').show()" update=":tabView:editForm:comparePanel">
                                <f:setPropertyActionListener value="12" target="#{mgrbean.compareType}"/>
                                <f:setPropertyActionListener value="holdCardManCompareLink" target="#{mgrbean.compareLink}"/>
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>

        <!-- 地区变更弹出框 -->
        <p:dialog id="zoneChangeDialog" header="地区变更" modal="true" resizable="false" widgetVar="ZoneChangeDialog" width="480" height="165">
            <p:panelGrid id="zoneChangePanel" style="width:100%;">
                <p:row>
                    <p:column style="text-align: right;width: 120px;height: 31px;">
                        <font color="red">*</font>
                        <h:outputText value="变更地区："/>
                    </p:column>
                    <p:column style="text-align: left;padding-left: 3px;padding-bottom: 16px;">
                        <p:outputPanel>
                            <h:panelGrid style="border-color: transparent;padding-top: 10px;">
                                <p:row>
                                    <p:column>
                                        <zwx:ZoneSingleComp zoneList="#{mgrbean.changeZoneList}"
                                                            zoneCode="#{mgrbean.changeZoneCode}"
                                                            zoneId = "#{mgrbean.changeZoneId}"
                                                            zoneName="#{mgrbean.changeZoneName}"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:outputLabel value="说明：" style="color: red;padding-left: 6px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:outputLabel value="1、本辖区内请选择至区县及以下地区！" style="color: blue;padding-left: 6px;"/>
                                    </p:column>
                                </p:row>
                                <p:row>
                                    <p:column>
                                        <p:outputLabel value="2、跨区县变更，需要变更后市级机构确认！" style="color: blue;padding-left: 6px;"/>
                                    </p:column>
                                </p:row>
                            </h:panelGrid>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确认" icon="ui-icon-check" action="#{mgrbean.saveChangeZoneAction}" process="@this,zoneChangeDialog"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ZoneChangeDialog').hide();" immediate="true" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <!-- 地区变更确认框 -->
        <p:confirmDialog id="zoneChangeConfirmDialog" message="用人单位将变更到#{mgrbean.changeZoneFullName}进行填报，是否确认？" header="消息确认框" widgetVar="ZoneChangeConfirmDialog" >
            <p:commandButton value="确定" action="#{mgrbean.zoneChangeConfirmAction}" icon="ui-icon-check"
                             oncomplete="PF('ZoneChangeConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('ZoneChangeConfirmDialog').hide();"
                             type="button"/>
        </p:confirmDialog>

        <p:dialog header="行业类别选择" widgetVar="selIndusTypeDialog" width="800" height="430" modal="true" resizable="false" >
            <ui:include src="/webapp/system/indusTypeCodePanel.xhtml">
                <ui:param name="mgrbean" value="#{mgrbean}"/>
                <ui:param name="indusbean" value="#{mgrbean.codePanelBean}"/>
            </ui:include>
        </p:dialog>
    </ui:define>
</ui:composition>