<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjCrptCheckShowListBean}"/>
    <!-- 详情页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/crptaudit/tbTjCrptCheckShowEdit.xhtml" />
    <ui:define name="insertScripts">
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }
            function hideStatus() {
                PF('StatusDialog').hide();
            }
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位综合展示"/>
            </p:column>
        </p:row>
    </ui:define>


    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputLabel value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:200px;">
                <h:panelGrid columns="3" style="border-color: transparent;margin: -9px;padding: 0px;">
                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                        zoneCode="#{mgrbean.searchZoneCode}"
                                        zoneName="#{mgrbean.searchZoneName}"/>
                </h:panelGrid>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width:240px;">
                <p:inputText value="#{mgrbean.searchUnitName}" style="width:200px;"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width:200px;"/>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="经济类型："/>
            </p:column>
        	<p:column style="text-align:left;padding-left:3px;">
        		<table>
					<tr>
						<td style="padding: 0;border-color: transparent;">
							<p:inputText id="encomyName"
								value="#{mgrbean.selectEconomyNames}"
								style="width: 180px;cursor: pointer;"
								onclick="document.getElementById('tabView:mainForm:selEncomyLink').click();"
								readonly="true"/>
						</td>
						<td style="border-color: transparent;">
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="selEncomyLink"
								action="#{mgrbean.selSimpleCodeAction}" process="@this"
								style="position: relative;left: -28px !important;">
								<f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
								<p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
									resetValues="true" update="encomyName" />
							</p:commandLink>
						</td>
						<!-- 清空 -->
						<td style="border-color: transparent;position: relative;left: -30px;">
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                         process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                                  <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                            </p:commandLink>
						</td>
					</tr>
				</table>
        	</p:column>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="企业规模："/>
            </p:column>
        	<p:column style="text-align:left;padding-left:0px;">
        		<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectCrptSizes}"
					selectedIds="#{mgrbean.selectCrptSizeIds}"
					simpleCodeList="#{mgrbean.crptSizeList}"
					inputWidth="200"
					panelWidth="210"
					height="200"></zwx:SimpleCodeManyComp>
        	</p:column>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="行业类别："/>
            </p:column>
        	<p:column style="text-align:left;padding-left:3px;">
        		<table>
					<tr>
						<td style="padding: 0;border-color: transparent;">
							<p:inputText id="indusTypeName"
								value="#{mgrbean.selectIndusTypeNames}"
								style="width: 200px;cursor: pointer;"
								onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"
								readonly="true"/>
						</td>
						<td style="border-color: transparent;">
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="selIndusTypeLink"
								action="#{mgrbean.selSimpleCodeAction}" process="@this"
								style="position: relative;left: -28px !important;">
								<f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
								<p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}" process="@this"
									resetValues="true" update="indusTypeName" />
							</p:commandLink>
						</td>
						<!-- 清空 -->
						<td style="border-color: transparent;position: relative;left: -30px;">
							<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                         process="@this" update="indusTypeName" action="#{mgrbean.clearSimpleCode}">
                                  <f:setPropertyActionListener target="#{mgrbean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                            </p:commandLink>
						</td>
					</tr>
				</table>
        	</p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel value="异常信息："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" colspan="5">
            	<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectTipNames}"
                            selectedIds="#{mgrbean.selectTipIds}"
                            simpleCodeList="#{mgrbean.tipList}"
                            height="200" panelWidth="340"></zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width:150px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width:320px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="社会信用代码" style="width:180px;text-align:center; ">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="经济类型" style="width:180px;text-align:center;">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="企业规模" style="width:80px;text-align:center; ">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="行业类别" style="width:150px;text-align:center; ">
            <h:outputLabel value="#{itm[6]}">
            </h:outputLabel>
        </p:column>
        <p:column headerText="异常信息" style="width:280px;">
            <h:outputLabel value="#{itm[7]}" id="tips" styleClass="zwx-tooltip"/>
            <p:tooltip for="tips" style="max-width:350px;">
            	<p:outputLabel value="#{itm[7]}" escape="false"></p:outputLabel>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;">
            <p:spacer width="5"/>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}"
                           process="@this"  update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.crptCheckId}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>