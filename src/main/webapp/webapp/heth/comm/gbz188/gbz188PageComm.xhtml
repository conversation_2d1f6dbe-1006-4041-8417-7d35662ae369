<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<script type="text/javascript">
			var removeExtraTreePanel = function(){
				var el = jQuery('#mainForm\\:overalPanel');
				if(el.length>1){
					el.each(function(index){
						if(index>0){
							$(this).remove();
						}
					});
				}

			}
		</script>
	</h:head>
	<h:body>
		<!-- 托管Bean -->
		<!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.Gbz188PageCommBean"-->
		<ui:param name="mgrbean" value="#{gbz188PageCommBean}"/>
		<h:form id="mainForm">
			<h:outputStylesheet library="css" name="default.css" />
			<h:outputStylesheet name="css/ui-tabs.css"/>
			<style type="text/css">
				.ui-datatable-data, .ui-datatable-data > tr {
					border-width: 1px !important;
				}
				.ui-datatable th.ui-state-default {
					background: #e9eff6 !important;
					height: 30px !important;
					padding: 5px 3px !important;
				}
				.ui-datatable .ui-column-filter {
					display: block;
					width: 100px;
					margin: auto;
					margin-top: 3px;
				}
			</style>
			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
						 id="titleGrid">
				<f:facet name="header">
					<p:row>
						<p:column colspan="4"
								  style="text-align:left;padding-left:5px; height: 20px;">
							<h:outputText value="职业健康监护方案（GBZ 188）维护" />
						</p:column>
					</p:row>
				</f:facet>
			</p:panelGrid>
			<table width="100%">
				<tr>
					<td style="width: 25%;vertical-align: top; text-align: left;"><p:outputPanel
							styleClass="zwx_toobar_42">
						<table  border="0">
							<tr>
								<td><span class="ui-separator"><span
										class="ui-icon ui-icon-grip-dotted-vertical" /></span></td>
								<td style="text-align: left;"><p:commandButton value="添加"
																			   icon="ui-icon-plus" process="@this"
																			   action="#{mgrbean.addMainInit}"
																			   oncomplete="PF('dlg1').show();removeExtraTreePanel();" update=":mainForm:dlg1">
									<p:resetInput target=":mainForm:dlg1"></p:resetInput>
								</p:commandButton></td>
								<td style="width: 80px;"><p:commandButton value="编辑" process="@this"
																		  icon="ui-icon-document"
																		  action="#{mgrbean.modMainInit}"
																		  update=":mainForm:dlg1" oncomplete="removeExtraTreePanel()">
									<p:resetInput target=":mainForm:dlg1"></p:resetInput>
								</p:commandButton></td>
								<td style="text-align: right;vertical-align: middle;">
									<table width="100%">
										<tr>
											<td style="text-align: right;"><p:selectBooleanCheckbox
													value="#{mgrbean.showStop}" label="停用">
												<p:ajax listener="#{mgrbean.showStopChange}"
														process="@this,@parent,mainForm:dataTable" update=":mainForm:dataTable"></p:ajax>
											</p:selectBooleanCheckbox></td>
											<td style="text-align: left;"><h:outputText value="停用"></h:outputText></td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</p:outputPanel>
						<br/>

						<p:dataTable
								var="itm" value="#{mgrbean.badRsnList}"
								widgetVar="dataTable" id="dataTable" pageLinks="3"
								paginatorPosition="top"
								filteredValue="#{mgrbean.filterBadRsnList}"
								paginator="true" rows="14" emptyMessage="没有数据！"
								selectionMode="single"
								selection="#{mgrbean.selectedMainstd}" rowKey="#{itm.rid}">
							<p:ajax event="rowSelect" process=":mainForm:dataTable"
									listener="#{mgrbean.onRowSelect}"
									update=":mainForm:tableView">
								<p:resetInput target=":mainForm:tableView"></p:resetInput>
							</p:ajax>

							<p:column filterBy="#{itm.tsSimpleCodeByBadrsnId.codeName}"
									  filterMatchMode="contains" headerText="危害因素">
								<h:outputText value="#{itm.tsSimpleCodeByBadrsnId.codeName}" style="color:red;" rendered="#{itm.stopTag==0}"></h:outputText>
								<h:outputText value="#{itm.tsSimpleCodeByBadrsnId.codeName}" rendered="#{itm.stopTag==1}"></h:outputText>
							</p:column>
							<p:column id="tsSimpleCodeByWorkStateid"
									  filterBy="#{itm.tsSimpleCodeByWorkStateid.codeName}"
									  headerText="在岗状态" filterMatchMode="contains">
								<h:outputText value="#{itm.tsSimpleCodeByWorkStateid.codeName}" style="color:red;" rendered="#{itm.stopTag==0}" />
								<h:outputText value="#{itm.tsSimpleCodeByWorkStateid.codeName}"  rendered="#{itm.stopTag==1}" />
							</p:column>
						</p:dataTable>

					</td>
					<td style="vertical-align: top;"><p:tabView id="tableView"
																dynamic="true" cache="true">
						<p:ajax event="tabChange"
								listener="#{mgrbean.onTabChange}" />
						<p:tab title="标准项目组合" id="item">
							<ui:include src="gbz188SchemeItemsComm.xhtml"></ui:include>
						</p:tab>
						<p:tab title="职业禁忌证" id="jjz">
							<ui:include src="gbz188JjzsComm.xhtml"></ui:include>
						</p:tab>
						<p:tab title="疑似职业病" id="occ">
							<ui:include src="gbz188OccdisesComm.xhtml"></ui:include>
						</p:tab>
						<p:tab title="询问症状" id="askItems">
							<ui:include src="gbz188AskItemsComm.xhtml"></ui:include>
						</p:tab>
					</p:tabView></td>
				</tr>
			</table>
			<p:dialog header="添加GBZ 188标准" widgetVar="dlg1" width="500" id="dlg1"
					  modal="true" resizable="false">
				   			<p:panelGrid style="width:98%" id="editGbzGrid">
				<p:row>
					<p:column style="text-align: right;">
						<h:outputText value="*" style="color:red;"></h:outputText>
						<h:outputText value="危害因素："></h:outputText>
					</p:column>
					<p:column>
						<h:panelGrid columns="3"
									 style="border-color: #ffffff;margin: 0px;padding: 0px;">
							<p:inputText id="editBadRsnName"
										 value="#{mgrbean.editBadRsnName}" readonly="true"
										 style="width: 140px;" />
							<p:commandLink styleClass="ui-icon ui-icon-search"
										   id="initTreeLink" process="@this"
										   style="position: relative;left: -40px;top:0px;"
										   oncomplete="PF('overalPanel').show()" />
							<h:inputHidden id="editBadRsnRid" required="true"
										   requiredMessage="请选择危害因素！"
										   value="#{mgrbean.editBadRsnRid}" />
						</h:panelGrid>
						<p:overlayPanel id="overalPanel" for="editBadRsnName"
										style="width:280px;" widgetVar="overalPanel" appendToBody="true"
										dynamic="false">
							<p:tree var="node" selectionMode="single" id="choiceTree"
									value="#{mgrbean.badRsnTree}"
									style="width: 250px;height: 300px;overflow-y: auto;">
								<p:ajax event="select"
										update=":mainForm:editBadRsnName,:mainForm:editBadRsnRid"
										listener="#{mgrbean.onNodeSelect}" process="@this"
										oncomplete="PF('overalPanel').hide();" />
								<p:treeNode>
									<h:outputText value="#{node.codeName}" />
								</p:treeNode>
							</p:tree>
						</p:overlayPanel>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align: right;height: 39px">
						<h:outputText value="*" style="color:red;"></h:outputText>
						<h:outputText value="在岗状态："></h:outputText>
					</p:column>
					<p:column style="text-align: left;padding-left: 9px">
						<p:selectOneMenu value="#{mgrbean.editJobstate}" style="width: 150px"
										 required="true" requiredMessage="请选择在岗状态！">
							<f:selectItem itemLabel="--请选择--" itemValue=""></f:selectItem>
							<f:selectItems value="#{mgrbean.jobStateList}"></f:selectItems>
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row rendered="#{mgrbean.curzwtjMainstd.rid!=null}" >
					<p:column style="text-align: right;height: 39px ">
						<h:outputText value="停用："></h:outputText>
					</p:column>
					<p:column style="text-align: left;padding-left: 9px">
						<p:selectBooleanCheckbox value="#{mgrbean.editStopTag}"></p:selectBooleanCheckbox>
					</p:column>
				</p:row>
			</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" action="#{mgrbean.saveGbz}"
											 process="dlg1,@this" update=":mainForm:dataTable"></p:commandButton>
							<p:spacer width="5" />
							<p:commandButton value="取消" onclick="PF('dlg1').hide();"
											 type="button"></p:commandButton>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
			<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
