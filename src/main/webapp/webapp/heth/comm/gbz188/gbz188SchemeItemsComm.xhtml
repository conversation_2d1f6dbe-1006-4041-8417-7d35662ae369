<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">
	<p:outputPanel styleClass="zwx_toobar_42">
		<table style="width: 100%">
			<tr>
				<td><span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span></td>
				<td style="text-align: left;width: 50%"><p:outputPanel
						rendered="#{mgrbean.selectedMainstd!=null}">
						<h:outputText style="font-weight: bold;color:#2E6E9E;"
							value="危害因素：#{mgrbean.selectedMainstd.tsSimpleCodeByBadrsnId.codeName}；"></h:outputText>
						<p:spacer width="5"></p:spacer>
						<h:outputText style="font-weight: bold;color:#2E6E9E;"
							value="在岗状态：#{mgrbean.selectedMainstd.tsSimpleCodeByWorkStateid.codeName}"></h:outputText>
					</p:outputPanel></td>
				<td style="text-align: right;width: 50% ;"><p:commandButton
						action="#{mgrbean.addSchemeItemsInit}" value="添加"
						update="schemeItemsDialog" icon="ui-icon-plus" process="@this"></p:commandButton></td>
			</tr>
		</table>
	</p:outputPanel>
	<br />
	<p:dataTable id="schemeItemsTable"
		value="#{mgrbean.tbZwtjSchemeItemses}" var="schemeItems"
		 emptyMessage="没有数据！" selectionMode="single"
		selection="#{mgrbean.selecedSchemeItem}"
		rowKey="#{schemeItems.rid}">
		<p:ajax event="rowSelect"
			process=":mainForm:tableView:schemeItemsTable"
			listener="#{mgrbean.onItemRowSelect}"
			update=":mainForm:tableView:fieldset">
		</p:ajax>
		<p:column headerText="项目组合">
			<h:outputText value="#{schemeItems.tsSimpleCode.codeName}@@@#{schemeItems.tsSimpleCode.codeLevelNo}" id="itemCodeName1">
				<f:converter converterId="heth.ItemCodeNameConverterComm" for="itemCodeName1"></f:converter>
			</h:outputText>
		</p:column>
		<p:column headerText="是否必检" style="text-align: center;">
			<h:outputText value="是" rendered="#{schemeItems.isMust==true}"  style="color:#E17009;"></h:outputText>
			<h:outputText value="否" rendered="#{schemeItems.isMust==false}"></h:outputText>
		</p:column>
		<p:column headerText="监测类型" style="text-align: center;">
			<h:outputText value="" rendered="#{schemeItems.jcType == null}"></h:outputText>
			<h:outputText value="常规监测" rendered="#{schemeItems.jcType != null and schemeItems.jcType==1}"></h:outputText>
			<h:outputText value="主动监测" rendered="#{schemeItems.jcType != null and  schemeItems.jcType==2}"></h:outputText>
		</p:column>
		<p:column headerText="是否靶器官" style="text-align: center;">
			<h:outputText value="是" rendered="#{schemeItems.isTargetitem==true}"></h:outputText>
			<h:outputText value="否" rendered="#{schemeItems.isTargetitem==false}"></h:outputText>
		</p:column>
		<p:column headerText="状态" style="text-align: center;">
			<h:outputText value="启用" rendered="#{schemeItems.stopTag==1}"></h:outputText>
			<h:outputText value="停用" rendered="#{schemeItems.stopTag==0}"></h:outputText>
		</p:column>
		<p:column headerText="是否发布" style="text-align: center;">
			<h:outputText value="是" rendered="#{schemeItems.publishTag==1}"></h:outputText>
			<h:outputText value="否" rendered="#{schemeItems.publishTag==0}"></h:outputText>
		</p:column>
		<p:column headerText="操作">
			<p:commandLink  value="启用"
							action="#{mgrbean.powerGbz}"
							update=":mainForm:tableView:schemeItemsTable"
							process="@this"
							rendered="#{schemeItems.stopTag==0 }">
				<p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"></p:confirm>
				<f:setPropertyActionListener target="#{mgrbean.optTableName}"
											 value="TB_ZWTJ_SCHEME_ITEMS"></f:setPropertyActionListener>
				<f:setPropertyActionListener target="#{mgrbean.optRid}"
											 value="#{schemeItems.rid}"></f:setPropertyActionListener>
			</p:commandLink>
			<p:commandLink  value="停用"
							action="#{mgrbean.stopGbz}"
							update=":mainForm:tableView:schemeItemsTable"
							process="@this" rendered="#{schemeItems.stopTag==1}">
				<p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"></p:confirm>
				<f:setPropertyActionListener target="#{mgrbean.optTableName}"
											 value="TB_ZWTJ_SCHEME_ITEMS"></f:setPropertyActionListener>
				<f:setPropertyActionListener target="#{mgrbean.optRid}"
											 value="#{schemeItems.rid}"></f:setPropertyActionListener>
			</p:commandLink>
			<p:spacer rendered="#{schemeItems.stopTag==0 }" width="5"></p:spacer>
			<p:commandLink  value="删除" rendered="#{schemeItems.stopTag==0 }"
							action="#{mgrbean.delGbz}"
							update=":mainForm:tableView:schemeItemsTable"
							process="@this" >
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"></p:confirm>
				<f:setPropertyActionListener target="#{mgrbean.optTableName}"
											 value="TB_ZWTJ_SCHEME_ITEMS"></f:setPropertyActionListener>
				<f:setPropertyActionListener target="#{mgrbean.optRid}"
											 value="#{schemeItems.rid}"></f:setPropertyActionListener>
			</p:commandLink>
		</p:column>
	</p:dataTable>
	<p:fieldset legend="业务提醒" toggleable="true" toggleSpeed="500"
		id="fieldset" style="margin-top: 5px;margin-bottom: 5px;">
		<table style="width: 100%">
			<tr style="height: 80px;">

				<td style="text-align: left;"><p:inputTextarea maxlength="500"
						id="bsWake" widgetVar="bsWake"
						value="#{mgrbean.editBsWake}"
						style="width: 98%;height: 60px;" rows="3"></p:inputTextarea></td>
				<td style="text-align: left;width: 60px;">
					<p:commandButton
						value="保存"
						rendered="#{mgrbean.selecedSchemeItem.publishTag==0 and mgrbean.selecedSchemeItem.stopTag==1}"
						action="#{mgrbean.saveBsWake}" process="@this,bsWake"></p:commandButton>
					<div style="height: 5px;"></div>
				<p:commandButton value="清除"
						rendered="#{mgrbean.selecedSchemeItem.publishTag==0 and mgrbean.selecedSchemeItem.stopTag==1}"
						action="#{mgrbean.cleanBsWork}" process="@this,bsWake" update="bsWake"></p:commandButton></td>
			</tr>
		</table>
	</p:fieldset>
	<p:dialog header="添加项目组合" widgetVar="schemeItemsDialog" width="760"
		id="schemeItemsDialog" modal="true" resizable="false">
		<table style="width: 100%">
			<tr>
				<td style="text-align: right;padding-right: 3px;width: 120px;"
					class="zwx_dialog_font"><h:outputText value="项目组合拼音码："></h:outputText>
				</td>
				<td
					style="text-align: left;padding-left: 3px;vertical-align: middle;width: 120px;">
					<p:inputText size="10" value="#{mgrbean.searchItemCode}">
						<p:ajax event="keyup" update="selectItemListTable"
							process="@this,@parent"
							listener="#{mgrbean.filterSelectItem}" />
					</p:inputText>
				</td>
				<td style="text-align: right;padding-right: 3px;width: 100px;"
					class="zwx_dialog_font"><h:outputText value="项目组合名称："></h:outputText>
				</td>
				<td style="text-align: left;padding-left: 3px"><h:panelGrid
						columns="5">
						<p:inputText value="#{mgrbean.searchItemName}" size="10">
							<p:ajax event="keyup" update="selectItemListTable"
								process="@this,@parent"
								listener="#{mgrbean.filterSelectItem}" />
						</p:inputText>
						<p:spacer width="30" />
						<h:outputText value="您已经选择了 " styleClass="zwx_dialog_font" />
						<h:outputText value="#{mgrbean.selectCount}"
							styleClass="zwx_dialog_font" id="itemSize"
							style="color:blue;font-weight: bold" />
						<h:outputText value=" 条记录！" styleClass="zwx_dialog_font" />
					</h:panelGrid></td>
			</tr>
		</table>
		<p:dataTable var="itm" value="#{mgrbean.filterSelectItemList}"
			widgetVar="selectItemListTable" id="selectItemListTable"
			paginator="true" rows="10" emptyMessage="没有数据！">
			<p:column headerText="选择" style="width:30px;text-align:center">
				<p:commandLink value="选择"
					action="#{mgrbean.selectItemAction}"
					process="@this,selectItemListTable"
					update=":mainForm:tableView:itemSize,:mainForm:tableView:selectItemListTable">
					<f:setPropertyActionListener value="#{itm}"
						target="#{mgrbean.selectTbzwItems}" />
				</p:commandLink>
			</p:column>
			<p:column headerText="项目组合编码">
				<h:outputText value="#{itm.tsSimpleCode.codeNo}"></h:outputText>
			</p:column>
			<p:column headerText="项目组合名称">
				<h:outputText value="#{itm.tsSimpleCode.codeName}@@@#{itm.tsSimpleCode.codeLevelNo}" id="itemCodeName">
					<f:converter converterId="heth.ItemCodeNameConverterComm" for="itemCodeName"></f:converter>
				</h:outputText>
			</p:column>
			<p:column headerText="必检项目" style="text-align: center;">
				<p:selectBooleanCheckbox value="#{itm.isMust}"></p:selectBooleanCheckbox>
			</p:column>
			<p:column headerText="靶器官" style="text-align: center;">
				<p:selectBooleanCheckbox value="#{itm.isTargetitem}"></p:selectBooleanCheckbox>
			</p:column>
			<p:column headerText="监测类型" style="text-align: center;">
				<p:selectOneMenu value="#{itm.jcType}" style="width: 150px;">
					<f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
					<f:selectItem itemValue="1" itemLabel="常规监测"></f:selectItem>
					<f:selectItem itemValue="2" itemLabel="主动监测"></f:selectItem>
				</p:selectOneMenu>
			</p:column>

		</p:dataTable>
		<f:facet name="footer">
			<h:panelGrid style="width: 100%;text-align: center;">
				<h:panelGroup>
					<p:commandButton value="确定"
						action="#{mgrbean.saveSchemeItems}"
						process="schemeItemsDialog,@this"
						update=":mainForm:tableView:schemeItemsTable"></p:commandButton>
					<p:spacer width="5" />
					<p:commandButton value="取消"
						onclick="PF('schemeItemsDialog').hide();" type="button"></p:commandButton>
				</h:panelGroup>
			</h:panelGrid>
		</f:facet>
	</p:dialog>
</ui:composition>