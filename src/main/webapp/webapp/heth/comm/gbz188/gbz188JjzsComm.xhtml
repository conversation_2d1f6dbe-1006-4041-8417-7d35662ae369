<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">
	<p:outputPanel styleClass="zwx_toobar_42">
		<table style="width: 100%">
			<tr>
				<td><span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span></td>
				<td style="text-align: left;width: 50%"><p:outputPanel
						rendered="#{mgrbean.selectedMainstd!=null}">
					<h:outputText style="font-weight: bold;color:#2E6E9E;"
								  value="危害因素：#{mgrbean.selectedMainstd.tsSimpleCodeByBadrsnId.codeName}；"></h:outputText>
					<p:spacer width="5"></p:spacer>
					<h:outputText style="font-weight: bold;color:#2E6E9E;"
								  value="在岗状态：#{mgrbean.selectedMainstd.tsSimpleCodeByWorkStateid.codeName}"></h:outputText>
				</p:outputPanel></td>
				<td style="text-align: right;width: 50% ; font-weight: bold;" ><p:commandButton
						action="#{mgrbean.addJjzInit}" value="添加"
						update="jjzDialog" icon="ui-icon-plus" process="@this"></p:commandButton></td>
			</tr>
		</table>
	</p:outputPanel>
	<br />
	<p:dataTable id="jjzTable" value="#{mgrbean.tbZwtjJjzs}"
				 var="tbzwtjjjz" emptyMessage="没有数据！">
		<p:column headerText="禁忌证编码" style="text-align: center;width:15%;">
			<h:outputText value="#{tbzwtjjjz.tsSimpleCode.codeNo}"></h:outputText>
		</p:column>
		<p:column headerText="禁忌证名称">
			<h:outputText value="#{tbzwtjjjz.tsSimpleCode.codeName}"></h:outputText>
		</p:column>
		<p:column headerText="状态" style="text-align: center;width:10%;">
			<h:outputText value="启用" rendered="#{tbzwtjjjz.stopTag==1}"></h:outputText>
			<h:outputText value="停用" rendered="#{tbzwtjjjz.stopTag==0}"></h:outputText>
		</p:column>
		<p:column headerText="是否发布" style="text-align: center;width:15%;">
			<h:outputText value="是" rendered="#{tbzwtjjjz.publishTag==1}"></h:outputText>
			<h:outputText value="否" rendered="#{tbzwtjjjz.publishTag==0}"></h:outputText>
		</p:column>
		<p:column headerText="操作" style="width:15%;">
			<p:commandLink  value="启用"
							action="#{mgrbean.powerGbz}"
							update=":mainForm:tableView:jjzTable"
							process="@this"
							rendered="#{tbzwtjjjz.stopTag==0 }">
				<p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"></p:confirm>
				<f:setPropertyActionListener target="#{mgrbean.optTableName}"
											 value="TB_ZWTJ_JJZS"></f:setPropertyActionListener>
				<f:setPropertyActionListener target="#{mgrbean.optRid}"
											 value="#{tbzwtjjjz.rid}"></f:setPropertyActionListener>
			</p:commandLink>
			<p:commandLink  value="停用" process="@this"
							update=":mainForm:tableView:jjzTable"
							action="#{mgrbean.stopGbz}"
							rendered="#{tbzwtjjjz.stopTag==1}">
				<p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"></p:confirm>
				<f:setPropertyActionListener target="#{mgrbean.optTableName}"
											 value="TB_ZWTJ_JJZS"></f:setPropertyActionListener>
				<f:setPropertyActionListener target="#{mgrbean.optRid}"
											 value="#{tbzwtjjjz.rid}"></f:setPropertyActionListener>
			</p:commandLink>
			<p:spacer rendered="#{tbzwtjjjz.stopTag==0 }" width="5"></p:spacer>
			<p:commandLink  value="删除" process="@this"
							update=":mainForm:tableView:jjzTable"
							action="#{mgrbean.delGbz}"
							rendered="#{tbzwtjjjz.stopTag==0 }">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"></p:confirm>
				<f:setPropertyActionListener target="#{mgrbean.optTableName}"
											 value="TB_ZWTJ_JJZS"></f:setPropertyActionListener>
				<f:setPropertyActionListener target="#{mgrbean.optRid}"
											 value="#{tbzwtjjjz.rid}"></f:setPropertyActionListener>
			</p:commandLink>
		</p:column>
	</p:dataTable>

	<p:dialog header="添加职业禁忌证" widgetVar="jjzDialog" width="700"
			  id="jjzDialog" modal="true" resizable="false">
		<table style="width: 100%">
			<tr>
				<td style="text-align: right;padding-right: 3px;width: 100px;"
					class="zwx_dialog_font"><h:outputText value="禁忌证拼音码："></h:outputText>
				</td>
				<td
						style="text-align: left;padding-left: 3px;vertical-align: middle;width: 120px;">
					<p:inputText size="10" value="#{mgrbean.searchJjsCode}">
						<p:ajax event="keyup" update="selectJjzListTable"
								process="@this,@parent"
								listener="#{mgrbean.filterSelectJjz}" />
					</p:inputText>
				</td>
				<td style="text-align: right;padding-right: 3px;width: 100px;"
					class="zwx_dialog_font"><h:outputText value="禁忌证名称："></h:outputText>
				</td>
				<td style="text-align: left;padding-left: 3px"><h:panelGrid
						columns="5">
					<p:inputText value="#{mgrbean.searchJjzName}" size="10">
						<p:ajax event="keyup" update="selectJjzListTable"
								process="@this,@parent"
								listener="#{mgrbean.filterSelectJjz}" />
					</p:inputText>
					<p:spacer width="30" />
					<h:outputText value="您已经选择了 " styleClass="zwx_dialog_font" />
					<h:outputText value="#{mgrbean.selectCount}"
								  styleClass="zwx_dialog_font" id="jjzSize"
								  style="color:blue;font-weight: bold" />
					<h:outputText value=" 条记录！" styleClass="zwx_dialog_font" />
				</h:panelGrid></td>
			</tr>
		</table>
		<p:dataTable var="itm" value="#{mgrbean.filterSelectJjzList}"
					 widgetVar="selectJjzListTable" id="selectJjzListTable"
					 paginator="true" rows="10" emptyMessage="没有数据！">
			<p:column headerText="选择" style="width:30px;text-align:center">
				<p:commandLink value="选择" action="#{mgrbean.selectJjzAction}"
							   process="@this,selectJjzListTable"
							   update=":mainForm:tableView:jjzSize,:mainForm:tableView:selectJjzListTable">
					<f:setPropertyActionListener value="#{itm}"
												 target="#{mgrbean.selectTbzwJjs}" />
				</p:commandLink>
			</p:column>
			<p:column headerText="禁忌证编码" style="text-align: center;">
				<h:outputText value="#{itm.tsSimpleCode.codeNo}"></h:outputText>
			</p:column>
			<p:column headerText="禁忌证名称" >
				<h:outputText value="#{itm.tsSimpleCode.codeName}"></h:outputText>
			</p:column>
		</p:dataTable>
		<f:facet name="footer">
			<h:panelGrid style="width: 100%;text-align: center;">
				<h:panelGroup>
					<p:commandButton value="确定" action="#{mgrbean.saveJjzs}"
									 process="jjzDialog,@this" update=":mainForm:tableView:jjzTable"></p:commandButton>
					<p:spacer width="5" />
					<p:commandButton value="取消" onclick="PF('jjzDialog').hide();"
									 type="button"></p:commandButton>
				</h:panelGroup>
			</h:panelGrid>
		</f:facet>
	</p:dialog>
</ui:composition>