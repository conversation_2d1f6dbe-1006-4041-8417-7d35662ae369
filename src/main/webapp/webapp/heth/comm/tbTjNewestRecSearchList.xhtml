<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjNewestRecSearcBean}" />
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false" />

    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }
            function hideStatus() {
                PF('StatusDialog').hide();
            }
        </script>
        <style type="text/css">
            .searchTime input{
                width:77px;
            }
            .searchTime1 input{
                width:88px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检最新档案导出" />
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 onclick="showStatus()" action="#{mgrbean.searchAction}"
                                 oncomplete="hideStatus()" update=":tabView:mainForm:dataTable"
                                 process="@this,mainGrid" />


                <p:commandButton value="导出"  icon="ui-icon-document"
                                 action="#{mgrbean.tjExportItemAction}" id="exportBtn" process="@this" update="exportItemDialog"
                >
                    <f:setPropertyActionListener target="#{mgrbean.exportFlag}" value="1"></f:setPropertyActionListener>
                </p:commandButton>
                <p:commandButton value="导出文件下载" icon="ui-icon-arrowthickstop-1-s" action="#{mgrbean.fileDownloadAction}"
                                 process="@this" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:38px;">
                <p:outputLabel value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;width: 280px;padding-left:9px;">
                <p:inputText value="#{mgrbean.conditionPO.searchPersonName}" maxlength="100"
                             style="width: 180px;" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="证件类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:selectOneMenu value="#{mgrbean.conditionPO.searchPsnType}" style="width:188px;">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.cardTypeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"></f:selectItems>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.conditionPO.searchIDC}"
                             maxlength="25" style="width: 200px;" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <p:outputLabel value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.conditionPO.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.conditionPO.searchCrptName}"
                             maxlength="25" style="width: 180px;" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="体检类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:selectManyCheckbox value="#{mgrbean.conditionPO.searchBhkType}" >
                    <f:selectItem itemLabel="职业健康检查" itemValue="3"/>
                    <f:selectItem itemLabel="放射卫生健康检查" itemValue="4" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;" >
                <p:outputLabel value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.conditionPO.searchStartTime}"
                                              endDate="#{mgrbean.conditionPO.searchEndTime}"  styleClass="searchTime"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="报告出具日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.conditionPO.startRptPrintDate}"
                                              endDate="#{mgrbean.conditionPO.endRptPrintDate}"  styleClass="searchTime"/>
            </p:column>
            <p:column style="text-align:right;padding-right:5px;">
                <p:outputLabel value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.conditionPO.startCreateDate}"
                                              endDate="#{mgrbean.conditionPO.endCreateDate}" styleClass="searchTime1"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="在岗状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.conditionPO.selectOnGuardIds}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="体检危害因素：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.conditionPO.selectBadRsnIds}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        inputWidth="180"
                                        ifTree="true"
                                        ifSelectParent = "false"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="监测类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:4px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.conditionPO.searchJcType}">
                    <f:selectItem itemLabel="常规监测" itemValue="1"/>
                    <f:selectItem itemLabel="主动监测" itemValue="2" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 120px;height:38px;">
                <p:outputLabel value="年龄：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px; ">
                <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchAgeGrid">
                    <p:inputText id="searchAgeName" value="#{mgrbean.selectAgeName}" style="width: 180px;" readonly="true" />
                    <p:commandLink styleClass="ui-icon ui-icon-search" process="@this"  style="position: relative;left: -30px;"
                                   oncomplete="PF('AgeOverlayPanel').show()" type="button" />
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -33px;"
                                   action="#{mgrbean.clearSelectAge}" process="@this"
                                   update="searchAgeGrid,ageOverlayPanel"/>
                </h:panelGrid>
                <p:remoteCommand name="hideAge" process="@this,ageOverlayPanel"
                                 action="#{mgrbean.hideAgeAction}"
                                 update="searchAgeGrid" />
                <p:overlayPanel id="ageOverlayPanel" for="searchAgeName"
                                style="width:280px;" widgetVar="AgeOverlayPanel"
                                showCloseIcon="true" onHide="hideAge();">
                    <p:tree var="node" selectionMode="checkbox"
                            value="#{mgrbean.ageSortTree}"
                            style="width: 250px;height: 200px;overflow-y: auto;"
                            selection="#{mgrbean.selectAges}">
                        <p:treeNode>
                            <p:outputLabel value="#{node.analyItem.codeName}" />
                        </p:treeNode>
                    </p:tree>
                </p:overlayPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="接害工龄：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchWorkGrid">
                    <p:inputText id="searchWorkName" value="#{mgrbean.selectWorkName}" style="width: 180px;" readonly="true" />
                    <p:commandLink styleClass="ui-icon ui-icon-search" process="@this"  style="position: relative;left: -30px;"
                                   oncomplete="PF('WorkOverlayPanel').show()" type="button" />
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -33px;"
                                   action="#{mgrbean.clearSelectWorkAge}" process="@this"
                                   update="searchWorkGrid,workOverlayPanel"/>
                </h:panelGrid>
                <p:remoteCommand name="hideWork" process="@this,searchWorkGrid,workOverlayPanel"
                                 action="#{mgrbean.hideWorkAgeAction}"
                                 update="searchWorkGrid" />
                <p:overlayPanel id="workOverlayPanel" for="searchWorkName"
                                style="width:280px;" widgetVar="WorkOverlayPanel"
                                showCloseIcon="true" onHide="hideWork();">
                    <p:tree var="node" selectionMode="checkbox"
                            value="#{mgrbean.workSortTree}"
                            style="width: 250px;height: 320px;overflow-y: auto;"
                            selection="#{mgrbean.selectWorkAges}">
                        <p:treeNode>
                            <p:outputLabel value="#{node.analyItem.codeName}" />
                        </p:treeNode>
                    </p:tree>
                </p:overlayPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="主检结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:4px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.searchBhkrstName}"
                                        selectedIds="#{mgrbean.conditionPO.searchSelBhkrstIds}"
                                        simpleCodeList="#{mgrbean.searchBhkrstList}"
                                        inputWidth="200" height="120"></zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="体检机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
                                            resetValues="true" update="unitName" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="体检项目：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" colspan="3">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="searchItemNames"
                                         value="#{mgrbean.searchItemNames}"
                                         style="width: 180px;cursor: pointer;"
                                         onclick="document.getElementById('tabView:mainForm:selItemLink').click();"
                                         readonly="true"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search"
                                           id="selItemLink"
                                           action="#{mgrbean.selItemAction}" process="@this"
                                           style="position: relative;left: -28px !important;">
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onItemAction}" process="@this"
                                        resetValues="true" update="searchItemNames" />
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="searchItemNames" action="#{mgrbean.clearSelectedItem}">
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:columnGroup type="header">
            <p:row>
                <p:column headerText="用工单位地区" style="width:180px;text-align: center"/>
                <p:column headerText="人员姓名" style="width:80px;text-align: center;"/>
                <p:column headerText="证件类型" style="width:100px;text-align: center;"/>
                <p:column headerText="证件号码" style="width:120px;text-align: center;"/>
                <p:column headerText="用工单位名称" style="width:400px;text-align: center;"/>
                <p:column headerText="在岗状态" style="width:80px;text-align: center;"/>
                <p:column headerText="监测类型" style="width:80px;text-align: center;"/>
                <p:column headerText="体检日期" style="width:100px;text-align: center;"/>
                <p:column headerText="报告出具日期" style="width:100px;text-align: center;"/>
                <p:column headerText="体检机构" style="width:400px;text-align: center;"/>
            </p:row>
        </p:columnGroup>
        <p:column style="text-align: left">
            <h:outputLabel value="#{itm[1]}" />
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[2]}" />
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[3]}" />
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[4]}" />
        </p:column>
        <p:column style="text-align: left;">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[6]}"/>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[7]}" />
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[8]}" >
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[9]}" >
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align: left;">
            <h:outputLabel value="#{itm[10]}" />
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <!-- 导出项目 -->
        <p:dialog id="exportItemDialog" header="体检项目（不勾选导出所有）"
                  widgetVar="ExportItemDialog" resizable="false" width="350"
                  height="400" modal="true">

            <p:tree value="#{mgrbean.exportTreeNode}" var="node"
                    selectionMode="checkbox"
                    selection="#{mgrbean.selectedExportNodes}" id="exportTree"
                    style="width: 320px;height: 390px;overflow-y: auto;">
                <p:treeNode>
                    <h:outputText value="#{node.itemName}" />
                </p:treeNode>
            </p:tree>

            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="导出"  icon="ui-icon-document" id="menuSaveBtn2"
                                         actionListener="#{mgrbean.exportData}" process="@this,exportTree,:tabView"
                                         onclick="PF('ExportItemDialog').hide()"
                        >
                        </p:commandButton>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="menuBackBtn2"
                                         onclick="PF('ExportItemDialog').hide();" immediate="true" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>