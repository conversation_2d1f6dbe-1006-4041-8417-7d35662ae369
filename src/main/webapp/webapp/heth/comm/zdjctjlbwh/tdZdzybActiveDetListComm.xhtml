<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">

<f:view contentType="text/html">
    <h:head>
    </h:head>
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZdzybActiveDetListBean"-->
    <ui:param name="mgrbean" value="#{tdZdzybActiveDetListBean}"/>
    <ui:param name="editPage" value="/webapp/heth/comm/zdjctjlbwh/tdZdzybActiveDetEditComm.xhtml"/>
    <h:body>
        <h:outputStylesheet name="css/default.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css" />
        <script type="text/javascript" src="/resources/js/namespace.js" />
        <script type="text/javascript"
                src="/resources/js/validate/system/validate.js" />
        <style type="text/css">
            .ui-picklist .ui-picklist-list {
                text-align: left;
                height: 350px;
                width: 340px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }

            .ui-datatable tr.ui-state-highlight a, .ui-datatable tr.ui-state-highlight:hover a{
                color: #2d69d8 !important;
            }
            .ui-datatable-data, .ui-datatable-data > tr {
                border-width: 1px !important;
            }
            .ui-datatable tr.ui-state-highlight a, .ui-datatable tr.ui-state-highlight:hover a {
                color: #ffffff !important;
            }
        </style>
        <p:tabView id="tabView" dynamic="true" cache="true"
                   activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
                                 id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                                    <h:outputText value="主动监测统计类别维护" />
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:dataTable var="itm" value="#{mgrbean.dataModel}"
                                 paginator="true" rows="#{mgrbean.pageSize}"
                                 paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 rowsPerPageTemplate="#{mgrbean.pageSize}" id="dataTable"
                                 lazy="true" emptyMessage="没有您要找的记录！"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                 >
                        <p:column headerText="序号"
                                  style="width: 100px;text-align: center;">
                            <h:outputText value="#{R+1}" />
                        </p:column>
                        <p:column headerText="统计分类"
                                  style="padding-left: 3px;width: 240px;">
                            <h:outputText value="#{itm[1]}" />
                        </p:column>
                        <p:column headerText="统计项目" style="width: 700px;">
                            <h:outputText value="#{itm[2]}" />
                        </p:column>
                        <p:column headerText="操作" style="padding-left: 3px;">
                            <p:commandLink value="修改"
                                           action="#{mgrbean.modInitAction}" update=":tabView"
                                           process="@this">
                                <p:resetInput target=":tabView:editForm:detailTable"></p:resetInput>
                                <f:setPropertyActionListener
                                        target="#{mgrbean.rid}" value="#{itm[3]}" />
                            </p:commandLink>
                            <p:spacer width="5" />
                        </p:column>
                    </p:dataTable>
                </h:form>
            </p:tab>
            <p:tab id="edit" title="edit" titleStyle="display:none;">
                <ui:include src="/webapp/heth/comm/zdjctjlbwh/tdZdzybActiveDetEditComm.xhtml" >
                </ui:include>
            </p:tab>
        </p:tabView>
        <ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    </h:body>
</f:view>
</html>