<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZdzybActiveDetListBean"-->
    <!-- 标题栏 -->
    <h:outputStylesheet name="css/ui-tabs.css" />
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="主动监测统计类别维护" />
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="保存" icon="ui-icon-disk"
                                 action="#{mgrbean.saveAction}" update=":tabView:editForm:detailTable"
                                 process="@this,:tabView:editForm"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView"
                                 immediate="true">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:panelGrid id="editItem" style="width:100%;height:100%;">
            <p:row>
                <p:column
                        style="text-align:right;padding-right:3px;width:150px;height:35px;">
                    <h:outputText value="统计分类：" />
                </p:column>
                <p:column style="text-align:left;padding-left:5px;">
                    <p:outputLabel value="重点行业" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <h:outputText value="备注："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputTextarea rows="4" cols="50" autoResize="false" maxlength="50"
                                     value="#{mgrbean.analyType.rmk}" />
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:fieldset legend="统计类别明细" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid id="registFiles" style="width:100%;">
                <p:row>
                    <p:column style="text-align:left;padding-left:5px;">
                        <p:panelGrid id="psnInfo" style="width:100%;">
                            <p:row>
                                <p:column colspan="4"
                                          style="text-align:left;padding-right:15px;border-color: transparent;">
                                    <p:commandButton value="添加"
                                                     icon="ui-icon-plus" process="@this"
                                                     action="#{mgrbean.addAnalyDetail}" id="addRootBtn">
                                        <p:ajax event="dialogReturn" listener="#{mgrbean.onAnalyDetailSel}" process="@this"
                                                resetValues="true" />
                                    </p:commandButton>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                        <p:dataTable id="detailTable" var="item" styleClass="tableSelect"
                                     value="#{mgrbean.showAllItemsList}" paginator="true"
                                     rows="20" paginatorPosition="bottom" style="width:100%"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                      pageLinks="5" lazy="true" rowsPerPageTemplate="#{'20,50,100'}"
                                     emptyMessage="没有您要找的记录！"
                                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                     rowIndexVar="R">
                            <p:column headerText="序号" style="width:100px;text-align: center;">
                                <p:inputText value="#{item[7]}" style="width:50px" maxlength="5"
                                             onkeyup="SYSTEM.clearNoNum(this)" />
                            </p:column>
                            <p:column headerText="重点行业" style="width:200px;text-align:center;">
                                <h:outputText value="#{item[3]}" />
                            </p:column>
                            <p:column headerText="重点行业的具体中小类行业" style="width:400px;">
                                <h:outputText value="#{item[4]}" />
                            </p:column>

                            <p:column headerText="重点岗位/环节" style="width:400px;">
                                <h:outputText value="#{item[5]}" />
                            </p:column>
                            <p:column headerText="重点职业病危害因素" style="width:400px;">
                                <h:outputText value="#{item[6]}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="删除"
                                               action="#{mgrbean.deleteItemAction}"
                                               update=":tabView:editForm:detailTable" process="@this,:tabView:editForm">
                                    <p:confirm header="消息确认框" message="确定要删除吗？"
                                               icon="ui-icon-alert" />
                                    <f:setPropertyActionListener
                                            target="#{mgrbean.detailRid}"
                                            value="#{item[1]}" />
                                </p:commandLink>
                                <p:spacer width="5" />
                                <p:commandLink value="选择子项目"
                                               update=":tabView:editForm:detailTable,:tabView:editForm:analyDetailSubDialog" process="@this,:tabView:editForm"
                                               action="#{mgrbean.addAnalyDetailSubDialog}">
                                    <f:setPropertyActionListener
                                            target="#{mgrbean.detailRid}"
                                            value="#{item[1]}" />
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <!-- 弹出框 -->
        <p:dialog id="analyDetailSubDialog" header="选择子项目" widgetVar="AnalyDetailSubDialog"
                  resizable="false" width="1250" height="450" modal="true">
            <p:outputPanel styleClass="zwx_toobar_42">
				<span class="ui-separator"><span style="margin-top: 9px;"
                                                 class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <h:panelGrid columns="6"
                             style="border-color:transparent;margin-left: 13px;margin-top: -23px;">
                    <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                      action="#{mgrbean.addIndustryDialog}"
                                     process="@this">
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                process="@this" update="detailTable,selIndustryTable" resetValues="true"/>
                    </p:commandButton>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     process="@this" oncomplete="PF('AnalyDetailSubDialog').hide()"/>
                </h:panelGrid>
            </p:outputPanel>
            <p:dataTable var="itm" style="margin-top: 10px;"
                         value="#{mgrbean.industryList}" id="selIndustryTable"
                         paginator="true" rows="10" emptyMessage="没有数据！" rowIndexVar="R"
                         rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         paginatorPosition="bottom">
                <p:column headerText="序号" style="text-align: center;width: 30px;">
                    <h:outputText value="#{R+1}" escape="false"/>
                </p:column>
                <p:column headerText="重点行业的具体中小类行业" style="width: 180px;">
                    <h:outputText value="#{itm[1]}" escape="false"/>
                </p:column>
                <p:column headerText="重点岗位/环节" style="width: 300px;">
                    <h:outputText value="#{itm[2]}" escape="false"/>
                </p:column>
                <p:column headerText="重点职业病危害因素" style="width: 300px;">
                    <h:outputText value="#{itm[3]}" escape="false"/>
                </p:column>
                <p:column headerText="操作">
                    <p:commandLink value="删除"
                                   action="#{mgrbean.deleteSubAction}"
                                   update=":tabView:editForm:selIndustryTable,:tabView:editForm:detailTable" process="@this,:tabView:editForm">
                        <p:confirm header="消息确认框" message="确定要删除吗？"
                                   icon="ui-icon-alert" />
                        <f:setPropertyActionListener
                                target="#{mgrbean.subRid}"
                                value="#{itm[0]}" />
                    </p:commandLink>
                    <p:spacer width="5"/>
                    <p:commandLink value="选择重点岗位/环节"
                                   update=":tabView:editForm:selIndustryTable,:tabView:editForm:detailTable" process="@this,:tabView:editForm"
                                   action="#{mgrbean.addImportPostlog}">
                        <f:setPropertyActionListener
                                target="#{mgrbean.selPostRids}"
                                value="#{itm[4]}" />
                        <f:setPropertyActionListener
                                target="#{mgrbean.subRid}"
                                value="#{itm[0]}" />
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onImportPostAction}"
                                process="@this" update="selIndustryTable,:tabView:editForm:detailTable" resetValues="true"/>
                    </p:commandLink>
                    <p:spacer width="5"/>
                    <p:commandLink value="选择重点职业病危害因素"
                                   update=":tabView:editForm:selIndustryTable" process="@this,:tabView:editForm"
                                   action="#{mgrbean.addBadRsnDialog}">
                        <f:setPropertyActionListener
                                target="#{mgrbean.selBadRsnRids}"
                                value="#{itm[5]}" />
                        <f:setPropertyActionListener
                                target="#{mgrbean.subRid}"
                                value="#{itm[0]}" />
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onBadRsnAction}"
                                process="@this" update="selIndustryTable,:tabView:editForm:detailTable" resetValues="true"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
        </p:dialog>
    </ui:define>
</ui:composition>