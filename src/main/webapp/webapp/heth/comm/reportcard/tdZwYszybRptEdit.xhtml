<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

    <style>
        .myCalendar input {
            width: 160px;
        }
    </style>
	<script type="text/javascript">
			//<![CDATA[	  
			     function disabledInput(ifView,id){
					if(ifView=="false"){
						return ;
					}
					
					var text;
					var $tabView ;
					if(id){
						$tabView = $("#"+id)
					}else{
						$tabView = $("#tabView\\:editForm\\:tabViewEdit\\:mainTabView");
					}
					$tabView.find("input,textarea").each(function(){
						if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
							$(this).css("pointer-events","none");
						}else{
							$(this).prop("disabled",true);
						}
						$(this).css("opacity","1");
					});
					//单选框label的for标签处理
					$tabView.find("label").each(function(){
						$(this).css("pointer-events","none");
					});
					$tabView.find("a").each(function(){
						text = $(this).text();
						if(!text){
							text = $(this).attr("title");
						}
						if("选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
							$(this).remove();
						}else if("查看"==text||"修改"==text){

						}else{
							$(this).prop("disabled",true);
							$(this).css("pointer-events","none");
							$(this).css("opacity","0.35");
						}

					});
					$tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
						$(this).addClass("ui-state-disabled");
						$(this).css("opacity","1");
						$(this).css("pointer-events","none");
					});
					//下拉
					$tabView.find("div[class*='ui-selectonemenu']").each(function(){
						$(this).addClass("ui-state-disabled");
						$(this).css("pointer-events","none");
						$(this).css("opacity","1");
					});
					//按钮
					$tabView.find("button").each(function(){
						text = $(this).text();
						if("扫描"==text||"预览"==text||"制作"==text||"上传"==text||"添加"==text||"设计"==text||"保存"==text){
							$(this).remove();
						}else if("进入"==text||"查看"==text||"返回"==text||"< 返回"==text||"删除"==text){

						}else{
							$(this).prop("disabled",true);
							$(this).css("pointer-events","none");
							$(this).css("opacity","0.35");
						}
					});
				}

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }
		//]]>
		</script>
    <h:form id="editForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />

        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="疑似职业病报告卡填报"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <div id="btn_panel">
                <h:panelGrid columns="15" style="border-color:transparent;padding:0px;">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;" update=":tabView:editForm"
                                     action="#{tdZwYszybRptListCommBean.saveAction}" process="@this,:tabView:editForm">
                    </p:commandButton>
                    <p:spacer width="5"/>
                    <p:commandButton value="提交" icon="ui-icon-check"
                                     action="#{tdZwYszybRptListCommBean.submitAction}" process="@this,:tabView:editForm">
                        <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                    </p:commandButton>
                    <p:spacer width="5"/>
                    <p:commandButton
                            value="#{tdZwYszybRptListCommBean.tdZwBgkLastSta.state==2 or (tdZwYszybRptListCommBean.tdZwBgkLastSta.state==4 and 1 == tdZwYszybRptListCommBean.rptZone.ifCityDirect)?'初审退回意见':tdZwYszybRptListCommBean.tdZwBgkLastSta.state==4?'复审退回意见':'终审退回意见'}"
                            icon="icon-alert" style="color:red;"
                            process="@this" onclick="PF('ReasonDialog').show();"
                            rendered="#{tdZwYszybRptListCommBean.tdZwBgkLastSta.state==2 or tdZwYszybRptListCommBean.tdZwBgkLastSta.state==4 or tdZwYszybRptListCommBean.tdZwBgkLastSta.state==6}">
                    </p:commandButton>
                    <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                     action="#{tdZwYszybRptListCommBean.backAction}"
                                     update=":tabView" process="@this"/>
                </h:panelGrid>
            </div>
            <p:sticky target="sticky"/>
        </p:outputPanel>
        <div id="writ_yszyb">
            <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
                <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                    <p:row>
                        <p:column colspan="2"><span>本环节文书</span></p:column>
                    </p:row>
                    <p:row>
                        <p:column style="padding: 0;" colspan="2">
                            <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="width: 190px;">
                            <h:outputText value="《疑似职业病报告卡》"/>
                        </p:column>
                        <p:column>
                            <p:outputPanel>
                                <p:spacer width="5"/>
                                <p:commandButton value="设计" icon="ui-icon-pencil"
                                                 action="#{tdZwYszybRptListCommBean.designWritReport}"
                                                 update=":tabView:editForm:writ_yszyb_panel"
                                                 process="@this,:tabView:editForm:writ_yszyb_panel"
                                                 rendered="#{mgrbean.ifDesign == 1}"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="制作" action="#{tdZwYszybRptListCommBean.buildWritReport}"
                                                 rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"
                                                 process="@this,:tabView:editForm:writ_yszyb_panel"/>
                                <p:commandButton value="查看" action="#{tdZwYszybRptListCommBean.toAnnexView}"
                                                 rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"
                                                 process="@this,:tabView:editForm:writ_yszyb_panel"/>
                                <p:spacer width="5"
                                          rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                <p:commandButton value="删除" action="#{tdZwYszybRptListCommBean.delMadedwrit}"
                                                 rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"
                                                 update=":tabView:editForm:writ_yszyb_panel,:tabView:editForm:writeSortPanel"
                                                 process="@this,:tabView:editForm:writ_yszyb_panel"/>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>

                <p:outputPanel id="writ_yszyb_panel">
                    <p:panelGrid styleClass="writeSortInfo" style="margin-top: 30px;" id="writeSortInfo">
                        <p:row>
                            <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                                <h:outputText value="疑似职业病报告卡"
                                              style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="noBorder" style="text-align: left;">
                                <h:outputText value="报告卡编码："/>
                                <p:outputLabel value="自动生成" style="color:gray;"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rid == null}"/>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptNo}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rid != null}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>

                    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;margin-top: 5px">

                        <f:facet name="header">
                            <p:row>
                                <p:column style="height:20px;font-size: 20px;text-align:left;" colspan="4">
                                    <p:outputLabel value="劳动者信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;width:150px">
                                <p:outputLabel value="姓名："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.personnelName}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="证件类型："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByCardTypeId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="证件号码："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.idc}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="性别："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.sex==1?'男':'女'}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="出生日期："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.birthday}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="联系电话："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.linktel}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                    <p:outputLabel value="用人单位信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;width: 150px">
                                <p:outputLabel value="用人单位名称："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.crptName}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="社会信用代码："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.creditCode}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="企业类型："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByEconomyId.codeName}"/>
                            </p:column>
                            <p:column styleClass="column_title">
                                <p:outputLabel value="行业类别："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByIndusTypeId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="企业规模："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByCrptSizeId.codeName}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="用人单位所在区："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByZoneId.fullName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="用人单位详细地址："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.address}"/>
                            </p:column>
                            <p:column styleClass="column_title">
                                <p:outputLabel value="用人单位地址邮编："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.postcode}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="用人单位联系人："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.safeposition}"/>
                            </p:column>
                            <p:column styleClass="column_title">
                                <p:outputLabel value="用人单位联系人电话："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.safephone}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                    <p:outputLabel value="用工单位信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;width: 150px">
                                <p:outputLabel value="用工单位名称："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.empCrptName}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="社会信用代码："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.empCreditCode}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="企业类型："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByEmpEconomyId.codeName}"/>
                            </p:column>
                            <p:column styleClass="column_title">
                                <p:outputLabel value="行业类别："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByEmpIndusTypeId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="企业规模："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByEmpCrptSizeId.codeName}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="用工单位所在区："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByEmpZoneId.fullName}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                    <p:outputLabel value="疑似职业病报告信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;width: 150px">
                                <p:outputLabel value="疑似职业病名称："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.yszybName}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px">
                                <p:outputLabel value="信息来源："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkBySourceId.codeName}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="疑似职业病种类："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;"
                                      colspan="#{tdZwYszybRptListCommBean.ifChemical?1:3}">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.yszybTypeName}"/>
                            </p:column>
                            <p:column styleClass="column_title" rendered="#{tdZwYszybRptListCommBean.ifChemical}">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="职业性化学中毒分类："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;"
                                      rendered="#{tdZwYszybRptListCommBean.ifChemical}">
                                <p:selectOneRadio value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.zyPoisonType}"
                                                  style="width: 120px"
                                                  rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}">
                                    <f:selectItem itemLabel="急性" itemValue="1"/>
                                    <f:selectItem itemLabel="慢性" itemValue="2"/>
                                </p:selectOneRadio>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.zyPoisonType==1?'急性':'慢性'}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="接触的职业性有害因素："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;" colspan="3">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchBadrsns}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="发现日期："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.findDate}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </p:outputLabel>
                            </p:column>
                            <p:column styleClass="column_title">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="接害开始日期："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;">
                                <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                            showOtherMonths="true" size="11" navigator="true"
                                            yearRange="c-200:c" converterMessage="接害开始日期，格式输入不正确！"
                                            showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                            value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.harmStartDate}"
                                            rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.harmStartDate}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </p:outputLabel>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="专业工龄："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;" colspan="3">
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkYear}" maxlength="2"
                                             style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)"
                                             onblur="SYSTEM.clearNoNum(this)"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkYear==null?'':tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkYear}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                <p:spacer width="10"/>
                                <h:outputLabel value="年"/>
                                <p:spacer width="10"/>
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkMonth}" maxlength="2"
                                             style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)"
                                             onblur="SYSTEM.clearNoNum(this)"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkMonth==null?'':tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkMonth}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                <p:spacer width="10"/>
                                <h:outputLabel value="月"/>
                                <p:spacer width="10"/>
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkDay}" maxlength="2"
                                             style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)"
                                             onblur="SYSTEM.clearNoNum(this)"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkDay==null?'':tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkDay}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                <p:spacer width="10"/>
                                <h:outputLabel value="日"/>
                                <p:spacer width="10"/>
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkHour}" maxlength="2"
                                             style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)"
                                             onblur="SYSTEM.clearNoNum(this)"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkHour==null?'':tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkHour}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                <p:spacer width="10"/>
                                <h:outputLabel value="时"/>
                                <p:spacer width="10"/>
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkMinute}"
                                             maxlength="2" style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)"
                                             onblur="SYSTEM.clearNoNum(this)"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel
                                        value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkMinute==null?'':tdZwYszybRptListCommBean.tdZwYszybRpt.tchWorkMinute}"
                                        rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                <p:spacer width="10"/>
                                <h:outputLabel value="分"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="统计工种："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:11px;width: 220px;" colspan="3">
                                <h:panelGrid columns="4" id="work"
                                             style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                                    <p:inputText readonly="true" style="width:160px"
                                                 id="workTypeName"
                                                 value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkByWorkTypeId.codeName}"
                                                 maxlength="25"
                                                 onclick="$('#tabView\\:editForm\\:selWorkLink').click()"/>

                                    <p:commandLink styleClass="ui-icon ui-icon-search"
                                                   id="selWorkLink"
                                                   disabled="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"
                                                   action="#{tdZwYszybRptListCommBean.selectWorkTypeAction}"
                                                   process="@this"
                                                   style="position: relative;left: -30px;">
                                        <p:ajax event="dialogReturn"
                                                listener="#{tdZwYszybRptListCommBean.onWorkTypeSearch}" process="@this"
                                                resetValues="true" update=":tabView:editForm:work"/>
                                    </p:commandLink>
                                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                                   style="position: relative;left: -30px;"
                                                   disabled="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"
                                                   process="@this" update="work"
                                                   action="#{tdZwYszybRptListCommBean.clearWorkType}">
                                    </p:commandLink>
                                    <p:outputLabel style="position: relative;left: -20px;"
                                                   value="（#{tdZwYszybRptListCommBean.tdZwYszybRpt.workOther}）"
                                                   rendered="#{tdZwYszybRptListCommBean.ifOtherWork and tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                                    <p:inputText
                                            rendered="#{tdZwYszybRptListCommBean.ifOtherWork and tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"
                                            id="ifOtherWork" style="width:160px;position: relative;left: -20px;"
                                            value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.workOther}" maxlength="25"/>
                                </h:panelGrid>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <p:outputLabel value="发现单位："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.discoveryUnit}"/>
                            </p:column>
                            <p:column styleClass="column_title">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="发现单位负责人："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.discoveryRespPsn}"
                                             maxlength="25" style="width: 160px;"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.discoveryRespPsn}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                    <p:outputLabel value="填表人信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;width: 150px">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="填表人："/>
                            </p:column>
                            <p:column style="text-align: left;width: 220px;padding-left:11px;">
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fillFormPsn}" maxlength="25"
                                             style="width: 160px;"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fillFormPsn}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="填表人联系电话："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fillLink}" maxlength="25"
                                             style="width: 160px;"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fillLink}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="填表日期："/>
                            </p:column>
                            <p:column style="text-align: left;width: 220px;padding-left:11px;">
                                <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                            showOtherMonths="true" id="happenDate" size="11" navigator="true"
                                            yearRange="c-20:c" converterMessage="填表日期，格式输入不正确！"
                                            showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                            value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fillDate}"
                                            rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fillDate}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </p:outputLabel>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px;">
                                <p:outputLabel value="填表单位："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkRptUnitId.unitname}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                    <p:outputLabel value="报告人信息"/>
                                </p:column>
                            </p:row>
                        </f:facet>

                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;width: 150px">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="报告人："/>
                            </p:column>
                            <p:column style="text-align: left;width: 220px;padding-left:11px;">
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptPsn}" maxlength="25"
                                             style="width: 160px;"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptPsn}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                            </p:column>
                            <p:column styleClass="column_title" style="width: 150px">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="报告人联系电话："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:inputText value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptLink}" maxlength="25"
                                             style="width: 160px;"
                                             rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptLink}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title" style="height:30px;">
                                <h:outputText value="*" style="color:red;"/>
                                <p:outputLabel value="报告日期："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                            showOtherMonths="true" size="11" navigator="true"
                                            yearRange="c-20:c" converterMessage="填表日期，格式输入不正确！"
                                            showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                            value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptDate}"
                                            rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath == null}"/>
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rptDate}"
                                               rendered="#{tdZwYszybRptListCommBean.tdZwYszybRpt.annexPath != null}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </p:outputLabel>
                            </p:column>
                            <p:column styleClass="column_title">
                                <p:outputLabel value="报告单位："/>
                            </p:column>
                            <p:column style="text-align: left;padding-left:11px;">
                                <p:outputLabel value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.fkRptUnitId.unitname}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column styleClass="column_title">
                                <p:outputLabel value="备注："/>
                            </p:column>
                            <p:column style="text-align: left;width:240px;padding-left:11px;" colspan="3">
                                <p:inputTextarea rows="4" cols="65" autoResize="true" maxlength="50"
                                                 value="#{tdZwYszybRptListCommBean.tdZwYszybRpt.rmk}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </p:outputPanel>
            </p:outputPanel>
        </div>
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="#{tdZwYszybRptListCommBean.tdZwBgkLastSta.state==2 or (tdZwYszybRptListCommBean.tdZwBgkLastSta.state==4 and 1 == tdZwYszybRptListCommBean.rptZone.ifCityDirect)?'初审退回意见':tdZwYszybRptListCommBean.tdZwBgkLastSta.state==4?'复审退回意见':'终审退回意见'}" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{tdZwYszybRptListCommBean.tdZwBgkLastSta.state==2?tdZwYszybRptListCommBean.tdZwBgkLastSta.countAuditAdv:tdZwYszybRptListCommBean.tdZwBgkLastSta.state==4?tdZwYszybRptListCommBean.tdZwBgkLastSta.cityAuditAdv:tdZwYszybRptListCommBean.tdZwBgkLastSta.proAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             id="reasonContent" maxlength="100" readonly="true"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         onclick="PF('ReasonDialog').hide();" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <ui:include src="/WEB-INF/templates/system/frpt.xhtml">
            <ui:param name="printBackingBean" value="#{tdZwYszybRptListCommBean}"/>
        </ui:include>
		<ui:include src="/WEB-INF/templates/system/frpt2.xhtml">
			<ui:param name="updateId" value=":tabView:editForm"/>
			<ui:param name="printBackingBean" value="#{tdZwYszybRptListCommBean}"/>
		</ui:include>
    </h:form>
</ui:composition>