<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">


    <h:form id="otherForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="放射卫生技术服务信息报送卡详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
            <ui:include src="tdZwSrvorgCardInfo.xhtml"/>
        </p:outputPanel>
    </h:form>

</ui:composition>