<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.SodReportCardFillInBean"-->
    <ui:define name="insertEditScripts">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput(ifView,id){
                if(ifView=="false"){
                    return ;
                }

                var text;
                var $tabView ;
                if(id){
                    $tabView = $("#"+id)
                }else{
                    $tabView = $("#tabView\\:editForm");
                }
                $tabView.find("input,textarea").each(function(){
                    if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
                        $(this).css("pointer-events","none");
                    }else{
                        $(this).prop("disabled",true);
                    }
                    $(this).css("opacity","1");
                });
                //单选框label的for标签处理
                $tabView.find("label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
                        $(this).remove();
                    }else if("查看"==text||"修改"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("扫描"==text||"预览"==text||"制作"==text||"上传"==text||"添加"==text||"设计"==text||"保存"==text){
                        $(this).remove();
                    }else if("进入"==text||"查看"==text||"返回"==text||"< 返回"==text||"删除"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
        <style>
            .myCalendar1 input{
                width: 160px;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
    	<p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="疑似职业病报告卡填报"/>
            </p:column>
        </p:row>
    </ui:define>
    <!--操作按钮-->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="9" style="border-color:transparent;padding:0px;" id="headButton">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" update=":tabView:editForm:writeSortInfo">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="初审退回意见" icon="icon-alert"  style="color:red;"
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 2 or (mgrbean.tdZwBgkLastSta.state == 4 and mgrbean.tdZwYszybRpt.fkByEmpZoneId.ifCityDirect == 1)}">
                </p:commandButton>
                <p:commandButton value="复审退回意见" icon="icon-alert"  style="color:red;"
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 4 and mgrbean.tdZwYszybRpt.fkByEmpZoneId.ifCityDirect != 1}">
                </p:commandButton>
                <p:commandButton value="终审退回意见" icon="icon-alert"  style="color:red;" styleClass=""
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 6}">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" update=":tabView" oncomplete="PF('ConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:sticky target="sticky"></p:sticky>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:outputPanel styleClass="businessInfo">
            <!--文书-->
            <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                <p:row>
                    <p:column colspan="2" ><span>本环节文书</span></p:column>
                </p:row>
                <p:row>
                    <p:column style="padding: 0;" colspan="2">
                        <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 190px;">
                        <h:outputText value="《疑似职业病报告卡》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:spacer width="5"/>
                            <p:commandButton value="设计" icon="ui-icon-pencil"  action="#{mgrbean.designWritReport}"
                                              process="@this,:tabView:editForm" rendered="#{mgrbean.ifDesign == 1}"/>
                            <p:spacer width="5"/>
                            <p:commandButton value="制作"  action="#{mgrbean.buildWritReport}" rendered="#{mgrbean.tdZwYszybRpt.annexPath == null}"
                                             process="@this,:tabView:editForm"/>
                            <p:commandButton value="查看"  action="#{mgrbean.toAnnexView}" rendered="#{mgrbean.tdZwYszybRpt.annexPath != null}"
                                             process="@this,:tabView:editForm"/>
                            <p:spacer width="5" rendered="#{mgrbean.tdZwYszybRpt.annexPath != null}" />
                            <p:commandButton value="删除"  action="#{mgrbean.delMadedwrit}" rendered="#{mgrbean.tdZwYszybRpt.annexPath != null}"
                                             update=":tabView:editForm" process="@this,:tabView:editForm"/>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <div id="writ_yszyb">
                <!-- 标题 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                            <h:outputText value="疑似职业病报告卡" style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: left;">
                            <h:outputText value="报告卡编码："/>
                            <p:outputLabel value="自动生成" style="color:gray;"
                                           rendered="#{mgrbean.tdZwYszybRpt.rid == null}" />
                            <p:outputLabel
                                    value="#{mgrbean.tdZwYszybRpt.rptNo}"
                                    rendered="#{mgrbean.tdZwYszybRpt.rid != null}" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 劳动者信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
                    <f:facet name="header">
                        <p:row>
                            <p:column style="height:20px;font-size: 20px;text-align:left;" colspan="4">
                                <p:outputLabel value="劳动者信息" />
                            </p:column>
                        </p:row>
                    </f:facet>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;width:150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="姓名："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;width: 220px;">
                            <p:inputText  style="width:160px" value="#{mgrbean.tdZwYszybRpt.personnelName}" maxlength="25"/>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="证件类型："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:selectOneMenu value="#{mgrbean.editCardTypeId}"
                                             id="psnType">
                                <f:selectItems value="#{mgrbean.cardTypeList}" var="cardType" itemLabel="#{cardType.codeName}" itemValue="#{cardType.rid}"/>
                                <p:ajax event="change" process="@this,basicPanel" update="basicPanel"
                                        listener="#{mgrbean.onPsnTypeChangeAction}" />
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="证件号码："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;width: 220px;">
                            <p:inputText id="idc" style="width:160px;float: left;" value="#{mgrbean.tdZwYszybRpt.idc}" maxlength="25" onblur="findFlowByIdc()" disabled="#{!mgrbean.ifIdcAble}"/>
                            <p:remoteCommand name="findFlowByIdc" action="#{mgrbean.findFlowByIdc}" process="@this,idc" update="sex,brth"/>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="性别："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:selectOneRadio id="sex" value="#{mgrbean.tdZwYszybRpt.sex}" >
                                <f:selectItem itemLabel="男" itemValue="1"/>
                                <f:selectItem itemLabel="女" itemValue="2"/>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="出生日期："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;width: 220px;">
                            <p:calendar id="brth" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="出生日期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()"
                                        value="#{mgrbean.tdZwYszybRpt.birthday}"/>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="联系电话："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:inputText  style="width:160px" value="#{mgrbean.tdZwYszybRpt.linktel}" maxlength="25"
                                         id="lnktel"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!--用人单位-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="crptPanel">
                <f:facet name="header">
                    <p:row>
                        <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                            <p:outputLabel value="用人单位信息"/>
                        </p:column>
                    </p:row>
                </f:facet>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="用人单位名称："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:6px;width: 220px;">
                            <h:panelGrid columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                                <p:inputText id="crptName"  readonly="true" style="width:160px;" value="#{mgrbean.tdZwYszybRpt.crptName}" onclick="document.getElementById('tabView:editForm:onCrptSelect').click()"/>
                                <p:commandLink  id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                               action="#{mgrbean.selectCrptList}" process="@this" style="position: relative;left: -30px;"
                                >
                                    <f:setPropertyActionListener value="1" target="#{mgrbean.crpyType}"/>
                                    <p:ajax event="dialogReturn" process="@this,crptPanel" resetValues="true" listener="#{mgrbean.onCrptSelect}" update="crptPanel,empCrptPanel"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <p:outputLabel value="社会信用代码："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.creditCode}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="企业类型："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEconomyId.codeName}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByIndusTypeId.codeName}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="企业规模："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByCrptSizeId.codeName}"/>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <p:outputLabel value="用人单位所在区："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByZoneId.fullName}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="用人单位详细地址："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;width: 220px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.address}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地址邮编："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.postcode}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="用人单位联系人："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;width: 220px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.safeposition}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位联系人电话："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.safephone}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!--用工单位-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="empCrptPanel">
                <f:facet name="header">
                    <p:row>
                        <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                            <p:outputLabel value="用工单位信息"/>
                        </p:column>
                    </p:row>
                </f:facet>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="用工单位名称："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:6px;width: 220px;">
                            <p:inputText readonly="true" style="width:160px;#{mgrbean.selEmpCrpt?'display:none;':''}"
                                         value="#{mgrbean.tdZwYszybRpt.empCrptName}" />
                            <h:panelGrid columns="2" style="border-color: #ffffff;margin: 0;padding: 0;#{mgrbean.selEmpCrpt?'':'display:none;'}">
                                <p:inputText id="empCrptName"  readonly="true" style="width:160px;"
                                             value="#{mgrbean.tdZwYszybRpt.empCrptName}"
                                             onclick="document.getElementById('tabView:editForm:onEmpCrptSelect').click()"/>
                                <p:commandLink  id="onEmpCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                               action="#{mgrbean.selectCrptList}" process="@this" style="position: relative;left: -30px;">
                                    <f:setPropertyActionListener value="2" target="#{mgrbean.crpyType}"/>
                                    <p:ajax event="dialogReturn" process="@this,empCrptPanel" resetValues="true" listener="#{mgrbean.onCrptSelect}" update="empCrptPanel"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <p:outputLabel value="社会信用代码："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.empCreditCode}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="企业类型："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpEconomyId.codeName}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpIndusTypeId.codeName}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="企业规模："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpCrptSizeId.codeName}"/>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <p:outputLabel value="用工单位所在区："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpZoneId.fullName}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!--疑似职业病报告信息-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="yszyb">
                    <f:facet name="header">
                        <p:row>
                            <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                <p:outputLabel value="疑似职业病报告信息" />
                            </p:column>
                        </p:row>
                    </f:facet>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="疑似职业病名称："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:6px;width: 220px;">
                            <h:panelGrid columns="4"
                                         style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                                <p:inputText id="zybType"
                                             value="#{mgrbean.tdZwYszybRpt.yszybName}"
                                             style="width: 150px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:editForm:selZybType').click();"
                                             readonly="true"/>
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selZybType"
                                               action="#{mgrbean.selectZybTypeAction}" process="@this"
                                               style="position: relative;left: -30px;">
                                    <p:ajax event="dialogReturn"
                                            listener="#{mgrbean.onZybTypeSelect}" process="@this,yszyb"
                                            resetValues="true" update="yszyb" />
                                </p:commandLink>
                            </h:panelGrid>
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="信息来源："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:selectOneMenu value="#{mgrbean.sourceId}" style="width: 160px">
                                <f:selectItems value="#{mgrbean.sourceList}" var="source" itemLabel="#{source.codeName}" itemValue="#{source.rid}"/>
                                <p:ajax event="change" process="@this,yszyb" update="yszyb"
                                        listener="#{mgrbean.onSourceChangeAction}" />
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <p:outputLabel value="疑似职业病种类："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;" colspan="#{mgrbean.ifChemical?1:3}">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.yszybTypeName}"/>
                        </p:column>
                        <p:column styleClass="column_title" rendered="#{mgrbean.ifChemical}">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业性化学中毒分类："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;" rendered="#{mgrbean.ifChemical}">
                            <p:selectOneRadio value="#{mgrbean.tdZwYszybRpt.zyPoisonType}"  style="width: 120px">
                                <f:selectItem itemLabel="急性" itemValue="1"/>
                                <f:selectItem itemLabel="慢性" itemValue="2" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="接触的职业性有害因素："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:6px;width: 220px;" colspan="3">
                            <h:panelGrid columns="4" id="badRsnPanel"
                                         style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                                <p:inputText readonly="true" style="width:541px"
                                             id="badrsns" value="#{mgrbean.tdZwYszybRpt.tchBadrsns}" maxlength="1000"
                                             onclick="document.getElementById('tabView:editForm:onBadrsnsSelect').click()"/>
                                <p:commandLink styleClass="ui-icon ui-icon-search" id="onBadrsnsSelect" action="#{mgrbean.selectBadtree}" process="@this"
                                               style="position: relative;left: -30px;">
                                    <p:ajax event="dialogReturn"
                                            listener="#{mgrbean.onBadtreeSelect}" process="@this"
                                            resetValues="true" update="badrsns" />
                                </p:commandLink>
                            </h:panelGrid>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="发现日期："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;width: 220px;">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                        showOtherMonths="true" size="11" navigator="true"
                                        yearRange="c-200:c" converterMessage="发现日期，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.tdZwYszybRpt.findDate}" />
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="接害开始日期："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                        showOtherMonths="true" size="11" navigator="true"
                                        yearRange="c-200:c" converterMessage="接害开始日期，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.tdZwYszybRpt.harmStartDate}" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="专业工龄："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:11px;" colspan="3">
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.tchWorkYear}" maxlength="2" style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"/>
                            <p:spacer width="10" />
                            <h:outputLabel value="年"/>
                            <p:spacer width="10" />
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.tchWorkMonth}" maxlength="2" style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"/>
                            <p:spacer width="10" />
                            <h:outputLabel value="月"/>
                            <p:spacer width="10" />
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.tchWorkDay}" maxlength="2" style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" />
                            <p:spacer width="10" />
                            <h:outputLabel value="日"/>
                            <p:spacer width="10" />
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.tchWorkHour}" maxlength="2" style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" />
                            <p:spacer width="10" />
                            <h:outputLabel value="时"/>
                            <p:spacer width="10" />
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.tchWorkMinute}" maxlength="2" style="width: 20px;" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" />
                            <p:spacer width="10" />
                            <h:outputLabel value="分"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="统计工种："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:6px;width: 220px;" colspan="3">
                            <h:panelGrid columns="4" id="work"
                                         style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                                <p:inputText readonly="true" style="width:160px"
                                             id="workTypeName" value="#{mgrbean.tdZwYszybRpt.workName}" maxlength="25"
                                             onclick="$('#tabView\\:editForm\\:selWorkLink').click()"/>

                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selWorkLink"
                                               action="#{mgrbean.selectWorkTypeAction}" process="@this"
                                               style="position: relative;left: -30px;">
                                    <p:ajax event="dialogReturn"
                                            listener="#{mgrbean.onWorkTypeSearch}" process="@this"
                                            resetValues="true" update=":tabView:editForm:work" />
                                </p:commandLink>
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" style="position: relative;left: -30px;"
                                               process="@this" update="work" action="#{mgrbean.clearWorkType}">
                                </p:commandLink>
                                <p:inputText   rendered="#{mgrbean.ifOtherWork}"
                                               id="ifOtherWork" style="width:160px;position: relative;left: -20px;" value="#{mgrbean.tdZwYszybRpt.workOther}" maxlength="25" />
                            </h:panelGrid>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="发现单位："/>
                        </p:column>
                        <p:column style="text-align: left;padding-left:11px;">
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.discoveryUnit}" maxlength="25" style="width: 160px;" />
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="发现单位负责人："/>
                        </p:column>
                        <p:column  style="text-align: left;padding-left:11px;">
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.discoveryRespPsn}" maxlength="25" style="width: 160px;" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 填表人-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                    <f:facet name="header">
                        <p:row>
                            <p:column  style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                <p:outputLabel value="填表人信息"/>
                            </p:column>
                        </p:row>
                    </f:facet>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人："/>
                        </p:column>
                        <p:column  style="text-align: left;width: 220px;padding-left:11px;" >
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.fillFormPsn}" maxlength="25" style="width: 160px;" />
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人联系电话："/>
                        </p:column>
                        <p:column  style="text-align: left;padding-left:11px;">
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.fillLink}" maxlength="25" style="width: 160px;" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表日期：" />
                        </p:column>
                        <p:column  style="text-align: left;width: 220px;padding-left:11px;">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                        showOtherMonths="true" id="happenDate" size="11" navigator="true"
                                        yearRange="c-20:c" converterMessage="填表日期，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.tdZwYszybRpt.fillDate}" />
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px;">
                            <p:outputLabel value="填表单位：" />
                        </p:column>
                        <p:column  style="text-align: left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkRptUnitId.unitname}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!--报告人-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                    <f:facet name="header">
                        <p:row>
                            <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                                <p:outputLabel value="报告人信息" />
                            </p:column>
                        </p:row>
                    </f:facet>

                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告人：" />
                        </p:column>
                        <p:column  style="text-align: left;width: 220px;padding-left:11px;">
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.rptPsn}" maxlength="25" style="width: 160px;" />
                        </p:column>
                        <p:column styleClass="column_title" style="width: 150px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告人联系电话：" />
                        </p:column>
                        <p:column  style="text-align: left;padding-left:11px;">
                            <p:inputText value="#{mgrbean.tdZwYszybRpt.rptLink}" maxlength="25" style="width: 160px;" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="height:30px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告日期：" />
                        </p:column>
                        <p:column  style="text-align: left;padding-left:11px;">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar"
                                        showOtherMonths="true"  size="11" navigator="true"
                                        yearRange="c-20:c" converterMessage="填表日期，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.tdZwYszybRpt.rptDate}" />
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="报告单位：" />
                        </p:column>
                        <p:column  style="text-align: left;padding-left:11px;">
                            <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkRptUnitId.unitname}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="备注：" />
                        </p:column>
                        <p:column  style="text-align: left;width:240px;padding-left:11px;" colspan="3">
                            <p:inputTextarea rows="4" cols="65" autoResize="true" maxlength="50"
                                             value="#{mgrbean.tdZwYszybRpt.rmk}" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </div>
        </p:outputPanel>
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.countAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 2}"/>
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.cityAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 4}"/>
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.proAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 6}"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         onclick="PF('ReasonDialog').hide();" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <ui:include src="/WEB-INF/templates/system/frpt.xhtml">
            <ui:param name="printBackingBean" value="#{sodReportCardFillInBean}"/>
        </ui:include>
        <ui:include src="/WEB-INF/templates/system/frpt2.xhtml">
            <ui:param name="updateId" value=":tabView:editForm"/>
            <ui:param name="printBackingBean" value="#{sodReportCardFillInBean}"/>
        </ui:include>
    </ui:define>
</ui:composition>
