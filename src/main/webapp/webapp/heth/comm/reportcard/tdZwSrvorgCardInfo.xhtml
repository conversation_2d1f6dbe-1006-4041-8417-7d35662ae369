<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui">
    <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
        <p:row>
            <p:column colspan="2"><span>本环节文书</span></p:column>
        </p:row>
        <p:row>
            <p:column style="padding: 0;" colspan="2">
                <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 260px;">
                <h:outputText value="《放射卫生技术服务信息报送卡》"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.srvorgCard.annexPath}')"
                                     rendered="#{mgrbean.srvorgCard.annexPath != null}">
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 260px;">
                <h:outputText value="《放射卫生技术服务报告首页、签发页》"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{mgrbean.srvorgCard.signAddress}')"
                                     rendered="#{mgrbean.srvorgCard.signAddress != null}">
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 标题 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
        <p:row>
            <p:column styleClass="noBorder" style="text-align: center;">
                <p:outputPanel style="padding-bottom:8px;">
                    <h:outputText value="放射卫生技术服务信息报送卡"
                                  style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                </p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="noBorder" style="text-align: left;">
                <h:outputText value="报告卡编码："/>
                <p:outputLabel value="#{mgrbean.srvorgCard.cardNo}"/>
            </p:column>
        </p:row>
    </p:panelGrid>

    <!-- 机构信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="机构信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="机构名称："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="${mgrbean.srvorgCard.orgName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="法定代表人（或主要负责人）："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <h:outputLabel value="#{mgrbean.srvorgCard.orgFz}"></h:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="注册地址："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.orgAddr}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="机构资质证书编号："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.certNo}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="项目负责人："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean.srvorgCard.proFz}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="联系电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.proLinktel}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="资质业务范围："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;line-height: 26px;" colspan="3">
                <h:outputText value="#{mgrbean.srvorgCard.itemsName}"></h:outputText>
            </p:column>

        </p:row>
    </p:panelGrid>
    <!-- 参与人员信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" >
        <p:row>
            <p:column style="border-bottom-color: transparent;" colspan="2">
                <p:outputLabel value="参与人员信息"
                               style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column colspan="2" >
                <p:dataTable id="srvorgCardPsnList" paginatorPosition="bottom"
                             value="#{mgrbean.srvorgCard.srvorgCardPsnList}"
                             widgetVar="cardRsnTable" var="srvorgCardPsn"
                             emptyMessage="没有数据！" rowIndexVar="R">
                    <p:column headerText="序号" style="width:40px;text-align: center;">
                        <p:outputLabel value="#{R+1}"></p:outputLabel>
                    </p:column>
                    <p:column headerText="姓名" style="width:100px;text-align:center;">
                        <p:outputLabel value="#{srvorgCardPsn.psnName}"></p:outputLabel>
                    </p:column>
                    <p:column headerText="承担的服务事项" style="text-align:center;width: 600px;" >
                        <p:outputLabel value="#{srvorgCardPsn.srvorgCardItemNames}"></p:outputLabel>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 服务的用人单位信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="unitPanel">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="服务的用人单位信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="单位名称："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.unitName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="统一社会信用代码："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.creditCode}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="注册地址："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="#{mgrbean.srvorgCard.address}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="联系人："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean.srvorgCard.linkMan}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="联系电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.linkPhone}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="服务单位类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="#{mgrbean.srvorgCard.fkBySutId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column colspan="4" >
                <p:dataTable id="srvorgCardZoneList" paginatorPosition="bottom"
                             value="#{mgrbean.srvorgCard.srvorgCardZoneList}"
                             widgetVar="srvorgCardZone" var="srvorgCardZone"
                             emptyMessage="没有数据！" rowIndexVar="R">
                    <p:column headerText="技术服务地址" style="width:100px;text-align: center;">
                        <p:outputLabel value="#{srvorgCardZone.fullName}"></p:outputLabel>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 技术服务信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicePanel">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="技术服务信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="技术服务领域："></p:outputLabel>

            </p:column>
            <p:column  style="padding-left: 8px;" colspan="3" >
                <p:outputLabel value="#{mgrbean.srvorgCard.servicesName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="现场采样时间："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <h:outputLabel value="#{mgrbean.srvorgCard.investStartDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
                <p:outputLabel value="~" rendered="#{null != mgrbean.srvorgCard.investEndDate}"></p:outputLabel>
                <h:outputLabel value="#{mgrbean.srvorgCard.investEndDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="现场检测时间："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <h:outputLabel value="#{mgrbean.srvorgCard.jcStartDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
                <p:outputLabel value="~" rendered="#{null != mgrbean.srvorgCard.jcEndDate}"></p:outputLabel>
                <h:outputLabel value="#{mgrbean.srvorgCard.jcEndDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="出具技术服务报告时间："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" >
                <h:outputLabel value="#{mgrbean.srvorgCard.rptDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="技术服务报告编号："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.srvorgCard.rptNo}"></p:outputLabel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 技术服务结果 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="resultPanel">
        <p:row>
            <p:column colspan="2">
                <p:outputLabel value="技术服务结果"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <!--放射卫生防护检测-->
        <p:row rendered="#{mgrbean.srvorgCard.ifFswsFhJc == 1}">
            <p:column colspan="2">
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="放射卫生防护检测"></p:outputLabel>
            </p:column>
        </p:row>
        <!--是否开展放射诊疗工作场所放射防护检测-->
        <p:row rendered="#{mgrbean.srvorgCard.ifFhJc == 1}">
            <p:column colspan="2" style="line-height: 26px;">
                <p:spacer width="20" height="0"/>
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="开展放射诊疗工作场所放射防护检测，共检测点位 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.fhJcPoint}"></p:outputLabel>
                <p:outputLabel value=" 个，"></p:outputLabel>
                <p:outputLabel value="其中，超标点位 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.notHgFhJcPoint}"></p:outputLabel>
                <p:outputLabel value=" 个"></p:outputLabel>
                <p:outputLabel value="。" rendered="#{mgrbean.srvorgCard.srvorgCardFhjcName == null }"></p:outputLabel>
                <p:outputLabel value="，超标点位放射性危害类型：" rendered="#{mgrbean.srvorgCard.srvorgCardFhjcName != null }"></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.srvorgCardFhjcName}。" rendered="#{mgrbean.srvorgCard.srvorgCardFhjcName != null }"></p:outputLabel>
            </p:column>
        </p:row>
        <!--开展放射诊疗设备质量控制检测-->
        <p:row rendered="#{mgrbean.srvorgCard.ifInstZkJc == 1}" colspan="2">
            <p:column style="line-height: 26px;">
                <p:spacer width="20" height="0"/>
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="开展放射诊疗设备质量控制检测"></p:outputLabel>
                <p:outputLabel value="（#{mgrbean.srvorgCard.fkByJcTypeId.codeName}）" rendered="#{null ne mgrbean.srvorgCard.fkByJcTypeId and null ne mgrbean.srvorgCard.fkByJcTypeId.rid}" />
                <p:outputLabel value="，检测设备 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.zkJcInstNum}"></p:outputLabel>
                <p:outputLabel value=" 台（套）"></p:outputLabel>
                <p:outputLabel value="，其中，检测结果有一项以上指标不合格的设备 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.notHgZkJcInstNum}"></p:outputLabel>
                <p:outputLabel value=" 台（套）"></p:outputLabel>
                <p:outputLabel value="。" rendered="#{mgrbean.srvorgCard.notHgZkJcInstName == null}"></p:outputLabel>
                <p:outputLabel value="，不合格设备名称： " rendered="#{mgrbean.srvorgCard.notHgZkJcInstName != null}"></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.notHgZkJcInstName}。" rendered="#{mgrbean.srvorgCard.notHgZkJcInstName != null}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--放射诊疗建设项目评价-->
        <p:row rendered="#{mgrbean.srvorgCard.ifItemPj == 1}">
            <p:column colspan="2">
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="放射诊疗建设项目评价"></p:outputLabel>
            </p:column>
        </p:row>
        <!--预评价-->
        <p:row rendered="#{mgrbean.srvorgCard.ifItemYpj == 1}" >
            <p:column colspan="2" style="line-height: 26px;">
                <p:spacer width="20" height="0"/>
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="预评价，剂量估算超标点位 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.ypjPoint}"></p:outputLabel>
                <p:outputLabel value=" 个"></p:outputLabel>
                <p:outputLabel value="。" rendered="#{mgrbean.srvorgCard.srvorgCardYpjName == null}"></p:outputLabel>
                <p:outputLabel value="，超标点位放射性危害类型：" rendered="#{mgrbean.srvorgCard.srvorgCardYpjName != null}"></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.srvorgCardYpjName}。" rendered="#{mgrbean.srvorgCard.srvorgCardYpjName != null}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--控制效果评价-->
        <p:row rendered="#{mgrbean.srvorgCard.ifItemXgpj == 1}" >
            <p:column  colspan="2" style="line-height: 26px;">
                <p:spacer width="20" height="0"/>
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="控制效果评价，现场共检测点位 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.xgpjJcPoint}"></p:outputLabel>
                <p:outputLabel value=" 个，其中，超标点位 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.notHgXgpjJcPoint}"></p:outputLabel>
                <p:outputLabel value=" 个"></p:outputLabel>
                <p:outputLabel value="。" rendered="#{mgrbean.srvorgCard.srvorgCardXgpjName == null}"></p:outputLabel>
                <p:outputLabel value="，超标点位放射性危害类型：" rendered="#{mgrbean.srvorgCard.srvorgCardXgpjName != null}"></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.srvorgCardXgpjName}。" rendered="#{mgrbean.srvorgCard.srvorgCardXgpjName != null}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--个人剂量监测-->
        <p:row rendered="#{mgrbean.srvorgCard.ifDoseMonit == 1}">
            <p:column colspan="2" style="line-height: 26px;">
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="个人剂量监测人数 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.doseMonitNum}"></p:outputLabel>
                <p:outputLabel value=" 人，其中，5~20mSv人数 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.gt5msv}"></p:outputLabel>
                <p:outputLabel value=" 人，超过20mSv人数 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.gt20msvBhkNum}" id ="gt20msvBhkNum"></p:outputLabel>
                <p:outputLabel value=" 人。"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.srvorgCard.ifDoseMonit == 1 and  mgrbean.srvorgCard.gt20msvBhkNum > 0}">
            <p:column colspan="2" >
                <p:dataTable id="srvorgCardDoseList" paginatorPosition="bottom"
                             value="#{mgrbean.srvorgCard.srvorgCardDoseList}"
                             widgetVar="cardRsnTable" var="srvorgCardDose"
                             emptyMessage="没有数据！" rowIndexVar="R">
                    <p:column headerText="姓名" style="width:100px;text-align: center;">
                        <p:outputLabel value="#{srvorgCardDose.psnName}"></p:outputLabel>
                    </p:column>
                    <p:column headerText="身份证号" style="width:160px;text-align:center;">
                        <p:outputLabel value="#{srvorgCardDose.idc}"></p:outputLabel>
                    </p:column>
                    <p:column headerText="检测日期" style="width:120px;text-align:center;">
                        <h:outputLabel value="#{srvorgCardDose.jcDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                        </h:outputLabel>
                    </p:column>
                    <p:column headerText="X、γ外照射个人剂量当量项目值" style="width:190px;text-align:center;">
                        <p:outputLabel value="#{srvorgCardDose.doseCal}"></p:outputLabel>
                    </p:column>
                    <p:column headerText="计量单位" style="width:60px;text-align:center;">
                        <p:outputLabel value="mSv" maxlength="20"></p:outputLabel>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
        <!--放射防护器材和含放射性产品检测-->
        <p:row rendered="#{mgrbean.srvorgCard.ifFhInstJc == 1}">
            <p:column colspan="2">
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="放射防护器材和含放射性产品检测"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.srvorgCard.ifJcInst == 1}">
            <p:column colspan="2" style="line-height: 26px;">
                <p:spacer width="20" height="0"/>
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="开展放射防护器材检测，共检测样品数量 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.jcInstNum}"></p:outputLabel>
                <p:outputLabel value=" 个，其中，超标样品数量 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.jcNotHgInstNum}"></p:outputLabel>
                <p:outputLabel value=" 个"></p:outputLabel>
                <p:outputLabel value="。" rendered="#{mgrbean.srvorgCard.notHgInstName == null}"></p:outputLabel>
                <p:outputLabel value=" ，超标样品名称： " rendered="#{mgrbean.srvorgCard.notHgInstName != null}"></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.notHgInstName}。" rendered="#{mgrbean.srvorgCard.notHgInstName != null}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.srvorgCard.ifJcUse == 1}">
            <p:column colspan="2" style="line-height: 26px;">
                <p:spacer width="20" height="0"/>
                <p:spacer width="5" height="0"/>
                <p:outputLabel value="开展含放射性产品检测，共检测样品数量 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.jcUseNum}"></p:outputLabel>
                <p:outputLabel value=" 个，其中，超标样品数量 "></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.jcNotHgUseNum}"></p:outputLabel>
                <p:outputLabel value=" 个"></p:outputLabel>
                <p:outputLabel value="。" rendered="#{mgrbean.srvorgCard.notHgUseName == null}"></p:outputLabel>
                <p:outputLabel value=" ，超标样品名称： " rendered="#{mgrbean.srvorgCard.notHgUseName != null}"></p:outputLabel>
                <p:outputLabel value="#{mgrbean.srvorgCard.notHgUseName}。" rendered="#{mgrbean.srvorgCard.notHgUseName != null}"></p:outputLabel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--报告信息-->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="报告信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填报单位："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean.srvorgCard.fillUnitName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="单位负责人："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean.srvorgCard.orgFzMan}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表人："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean.srvorgCard.fillFormPsn}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表人联系电话："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean.srvorgCard.fillLink}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表日期："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;"  colspan="3">
                <h:outputLabel value="#{mgrbean.srvorgCard.fillDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputLabel>
            </p:column>
        </p:row>

    </p:panelGrid>
</ui:composition>
