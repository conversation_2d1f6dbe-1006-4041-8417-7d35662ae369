<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">


    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="放射卫生技术服务信息报送卡详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="撤销" action="#{mgrbean.revokeAction}" rendered="#{mgrbean.ifSupportCancel}" icon="ui-icon-cancel" process="@this,:tabView"
                                 update=":tabView" oncomplete="windowScrollTop();" >
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{mgrbean.backAction}"
                                 update=":tabView" process="@this"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="sticky"></p:sticky>
        <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
            <ui:include src="tdZwSrvorgCardInfo.xhtml"/>
        </p:outputPanel>
    </h:form>

</ui:composition>