<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:t="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwHethChkSmaryListBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwHethChkSmaryListBean}" />
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/tdZwHethChkSmaryEdit.xhtml" />
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwHethChkSmaryView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业性有害因素监测卡填报"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" />
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInitAction}"
                                 process="@this" />
            </h:panelGrid>

        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;" >
                <p:inputText id="searchCrptName" value="#{mgrbean.searchUnitName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="报告卡编码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchRptNo}" style="width: 180px;" maxlength="50" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:remoteCommand name="searchCommand"
                                 process="@this,rcvBdate,rcvEdate"
                                 update="rcvBdate,rcvEdate" />
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvBdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="报告开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="#{mgrbean.searchRcvEdate == null?mgrbean.nowDate:mgrbean.searchRcvEdate}"  styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvBdate}" onmousedown="searchCommand()"/>
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvEdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="报告结束日期，格式输入不正确！"
                            showButtonPanel="true" mindate="#{mgrbean.searchRcvBdate}" maxdate="#{mgrbean.nowDate}" styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvEdate}" onmousedown="searchCommand()"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="报告卡编码" style="width: 200px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[9]}" />
        </p:column>
        <p:column headerText="用工单位地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="用工单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="用工单位社会信用代码" style="width:140px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:100px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>

        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '3'}">
                <h:outputLabel value="待提交" rendered="#{itm[6]==0}"/>
                <h:outputLabel value="待初审" rendered="#{(itm[6]==1) or (itm[7]==1 and itm[6]==3)}"/>
                <h:outputLabel value="已退回" rendered="#{itm[6]==2 or itm[6]==4 or itm[6]==6}" style="color: red"/>
                <h:outputLabel value="待复审" rendered="#{itm[7]==0 and itm[6]==3}"/>
                <h:outputLabel value="待终审" rendered="#{itm[6]==5}"/>
                <h:outputLabel value="终审通过" rendered="#{itm[6]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2'}">
                <h:outputLabel value="待提交" rendered="#{itm[6]==0}"/>
                <h:outputLabel value="待初审" rendered="#{itm[6]==1}"/>
                <h:outputLabel value="已退回" rendered="#{itm[6]==2 or itm[6]==6}" style="color: red"/>
                <h:outputLabel value="待终审" rendered="#{itm[6]==5}"/>
                <h:outputLabel value="终审通过" rendered="#{itm[6]==7}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="修改" rendered="#{mgrbean.checkLevel eq '3' and (itm[6]==0 or itm[6]==2 or itm[6]==4 or itm[6]==6) }" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener target="#{mgrbean.rid}"
                                             value="#{itm[0]}" />
            </p:commandLink>
            <p:commandLink value="修改" rendered="#{mgrbean.checkLevel eq '2' and (itm[6]==0 or itm[6]==2 or itm[6]==6) }" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener target="#{mgrbean.rid}"
                                             value="#{itm[0]}" />
            </p:commandLink>

            <p:commandLink value="详情" rendered="#{mgrbean.checkLevel eq '3' and (itm[6]==1 or itm[6]==3 or itm[6]==5 or itm[6]==7) }" action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{mgrbean.checkLevel eq '2' and (itm[6]==1 or itm[6]==5 or itm[6]==7) }" action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" rendered="#{mgrbean.checkLevel eq '3' and (itm[6]==0 or itm[6]==2 or itm[6]==4 or itm[6]==6) }" action="#{mgrbean.delAction}" process="@this"  update=":tabView:mainForm:dataTable" >
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                <t:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"></t:setPropertyActionListener>
            </p:commandLink>
            <p:commandLink value="删除" rendered="#{mgrbean.checkLevel eq '2' and (itm[6]==0 or itm[6]==2 or itm[6]==6) }" action="#{mgrbean.delAction}" process="@this"  update=":tabView:mainForm:dataTable" >
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                <t:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"></t:setPropertyActionListener>
            </p:commandLink>
        </p:column>
    </ui:define>

</ui:composition>
