<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:t="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwSrvorgCardListBean}" />
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/tdZwSrvorgCardEdit.xhtml" />
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwSrvorgCardView.xhtml"/>
    <!-- 无按钮详情 -->
    <ui:param name="otherPage" value="/webapp/heth/comm/reportcard/tdZwSrvorgCardOther.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
        </style>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="放射卫生技术服务信息报送卡填报"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInit}"
                                 process="@this" />
            </h:panelGrid>

        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="服务单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;" id="zoneCol">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       ifShowTrash="#{true}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="服务单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 280px;" >
                <p:inputText id="searchUnitName" value="#{mgrbean.searchUnitName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="出具技术服务报告时间：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptSDate}" endDate="#{mgrbean.searchRptEDate}" styleClass="myCalendar1"></zwx:CalendarDynamicLimitComp>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;height: 38px" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItem itemLabel="待提交" itemValue="0"/>
                    <f:selectItem itemLabel="已提交" itemValue="1"/>

                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">

        <p:column headerText="服务单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="服务单位名称" style="width: 520px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="出具技术服务报告时间" style="width:160px;text-align: center;">
            <h:outputLabel value="#{itm[3]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>

        <p:column headerText="状态" style=" width:75px;text-align:center;">
            <p:outputPanel >
                <h:outputLabel value="待提交" rendered="#{itm[4] == 0}"/>
                <h:outputLabel value="已提交" rendered="#{itm[4] == 1}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="修改" rendered="#{itm[4] == 0 }" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener target="#{mgrbean.rid}"
                                             value="#{itm[0]}" />
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[4] == 1 }" action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>

            <p:spacer width="5" rendered="#{itm[4] == 0 }"/>
            <p:commandLink value="删除" rendered="#{itm[4] == 0 }" action="#{mgrbean.delAction}" process="@this"  update=":tabView:mainForm:dataTable" >
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                <t:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"></t:setPropertyActionListener>
            </p:commandLink>

        </p:column>
    </ui:define>

</ui:composition>
