<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

    <h:form id="editForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="职业病报告卡审核" />
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <!-- 审核按钮 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
			<h:panelGrid columns="9" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="提交" icon="ui-icon-check"  rendered="#{mgrbean.ifAudit}"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm">
                </p:commandButton>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
					action="#{mgrbean.backAction}" process="@this" oncomplete="datatableOffClick()"
					update=":tabView" />
				<p:inputText style="visibility: hidden;width: 0"/>
			</h:panelGrid>
		</p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.saveAction}" icon="ui-icon-check"  oncomplete="PF('ConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:sticky target="sticky"></p:sticky>
		<p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
			<!--报告卡页面-->
			<ui:include src="tdZwCardInfo.xhtml"/>
			<!--审核意见页面-->
			<c:if test="#{mgrbean.ifAudit}">
				<ui:include src="checkAdvInfo.xhtml"></ui:include>
			</c:if>
			<!--历次审核意见-->
			<p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;margin-bottom: 50px;">
				<f:facet name="header">
					<p:row>
						<p:column colspan="6" style="text-align:left;height: 20px;">
							<p:outputLabel value="历次审核意见"/>
						</p:column>
					</p:row>
				</f:facet>
				<p:row>
					<p:column>
						<p:dataTable id="historyList" paginatorPosition="bottom"
									 value="#{mgrbean.historyList}"
									 widgetVar="badRsnTable" var="item"
									 emptyMessage="没有数据！" rowIndexVar="R">
							<p:column headerText="审核类型"  style="width:130px;text-align: center">
								<p:outputLabel value="#{item[4]}"/>
							</p:column>
							<p:column headerText="审核意见" style="width:450px;">
								<p:outputLabel value="#{item[1]}"/>
							</p:column>
							<p:column headerText="审核人"  style="width:150px;text-align: center">
								<p:outputLabel value="#{item[2]}"/>
							</p:column>
							<p:column headerText="审核日期" style="width:150px;text-align: center">
								<p:outputLabel value="#{item[3]}">
									<f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8"></f:convertDateTime>
								</p:outputLabel>
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:outputPanel>
    </h:form>
</ui:composition>