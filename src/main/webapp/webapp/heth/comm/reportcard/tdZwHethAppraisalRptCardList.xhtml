<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:t="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwHethAppraisalRptCardListBean}" />
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/tdZwHethAppraisalRptCardEdit.xhtml" />
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwHethAppraisalRptCardView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
            .icon-alert{
                background-image: url(/resources/images/alert-tip.png) !important;
                background-size: 12px 12px;
                margin-left: 3px;
                margin-top: -6px !important;
            }
        </style>
    </ui:define>


    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业病鉴定报告卡填报"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" />
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInitAction}"
                                 process="@this" />
            </h:panelGrid>

        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="用人单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 0px;width:280px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"
                                       zoneCodeNew="#{mgrbean.searchZoneCode}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       ifShowTrash="true" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 280px;" >
                <p:inputText value="#{mgrbean.searchUnitName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" >
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchPsnName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" >
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="50" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="鉴定类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
                <p:selectManyCheckbox value="#{mgrbean.searchJdTypes}">
                    <f:selectItems value="#{mgrbean.jdTypeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="鉴定日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchJdBdate}"
                                              endDate="#{mgrbean.searchJdEdate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="首次鉴定结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" >
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectFirstJdResultNames}"
                                        selectedIds="#{mgrbean.selectFirstJdResultIds}"
                                        simpleCodeList="#{mgrbean.firstJdResultList}"
                                        panelWidth="190"
                                        height="200">
                </zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="再次鉴定结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" >
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectNextJdResulNames}"
                                        selectedIds="#{mgrbean.selectNextJdResulIds}"
                                        simpleCodeList="#{mgrbean.nextJdResultList}"
                                        panelWidth="210"
                                        height="200">
                </zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="用人单位地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="用人单位名称" style="width:210px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="姓名" style="width:100px;text-align:center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="证件号码" style="width:120px;text-align:center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width:210px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="鉴定类型" style="width:100px;text-align:center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="鉴定日期" style="width:100px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="首次鉴定结论" style="width:150px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="再次鉴定结论" style="width:150px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:100px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputLabel value="待提交" rendered="#{itm[11]==0}"/>
            <h:outputLabel value="待初审" rendered="#{itm[11]==1}"/>
            <h:outputLabel value="已退回" rendered="#{itm[11]==2 or itm[11]==4 or itm[11]==6}" style="color: red"/>
            <h:outputLabel value="#{null ne mgrbean.isCityDirect and mgrbean.isCityDirect == 1 ? '待初审' :  '待复审'}" rendered="#{itm[11]==3}"/>
            <h:outputLabel value="待终审" rendered="#{itm[11]==5}"/>
            <h:outputLabel value="终审通过" rendered="#{itm[11]==7}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5" rendered="#{itm[11] == 0 or itm[11] == 2 or itm[11] == 4 or itm[11] == 6}" />
            <p:commandLink value="修改" rendered="#{itm[11] == 0 or itm[11] == 2 or itm[11] == 4 or itm[11] == 6}" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="0" target="#{mgrbean.isView}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[11] == 0 or itm[11] == 2 or itm[11] == 4 or itm[11] == 6}" />
            <p:commandLink value="删除" process="@this"  update=":tabView"  action="#{mgrbean.deleteAction}" rendered="#{itm[11] == 0 or itm[11] == 2 or itm[11] == 4 or itm[11] == 6}" >
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5"  rendered="#{itm[11] ne 0 and itm[11] ne 2 and itm[11] ne 4 and itm[11] ne 6}" />
            <p:commandLink value="详情" process="@this"  update=":tabView"  action="#{mgrbean.viewInitAction}" rendered="#{itm[11] ne 0 and itm[11] ne 2 and itm[11] ne 4 and itm[11] ne 6}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="1" target="#{mgrbean.isView}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

</ui:composition>
