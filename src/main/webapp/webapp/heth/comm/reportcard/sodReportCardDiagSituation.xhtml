<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.SodReportCardDiagSituationBean"-->
    <ui:param name="mgrbean" value="#{sodReportCardDiagSituationBean}"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            //<![CDATA[
            function getDownloadFileClick(){
                document.getElementById("mainForm:downloadFileBtn").click();
            }
            //]]>
        </script>
        <style>
            .myCalendar2 input {
                width: 77px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="疑似职业病诊断情况"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"
                                 onclick="zwx_loading_start_pub();" oncomplete="zwx_loading_stop_pub();datatableOffClick();"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportDataBtn"
                                 action="#{mgrbean.exportDataAction}" process="@this,mainGrid" />
                <p:commandButton style="display: none;"  id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start_pub, zwx_loading_stop_pub);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <font color="red">*</font>
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <font color="red">*</font>
                <h:outputText value="疑似职业病报告日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 9px;width:250px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchReportBeginDate}"
                                              endDate="#{mgrbean.searchReportEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="职业危害因素："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnRids}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        ifTree="true" ifSelectParent="false"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertContent">
        <p:dataTable id="dataTable" var="itm" value="#{mgrbean.dataTableList}" emptyMessage="没有您要找的记录！">
            <p:columnGroup type="header">
                <p:row>
                    <p:column headerText="地区" rowspan="2" style="width: 150px;"/>
                    <p:column headerText="疑似职业病报告数" rowspan="2" style="width: 150px;"/>
                    <p:column headerText="报告信息来源" colspan="3" style="width: 120px;"/>
                    <p:column headerText="疑似职业病诊断情况" colspan="5" style="width: 120px;"/>
                </p:row>
                <p:row>
                    <p:column headerText="职业健康检查" style="width: 150px;"/>
                    <p:column headerText="职业病诊断" style="width: 150px;"/>
                    <p:column headerText="医疗卫生机构" style="width: 120px;"/>
                    <p:column headerText="未进入诊断" style="width: 120px;"/>
                    <p:column headerText="诊断中" style="width: 120px;"/>
                    <p:column headerText="确诊职业病" style="width: 120px;"/>
                    <p:column headerText="确诊无职业病" style="width: 120px;"/>
                    <p:column headerText="诊断率（%）" style="width: 120px;"/>
                </p:row>
            </p:columnGroup>

            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[0]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[1]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[2]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[3]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[4]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[5]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[6]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[7]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[8]}"/>
            </p:column>
            <p:column style="padding-left:8px;text-align: center;">
                <h:outputText value="#{itm[9]}"/>
            </p:column>
        </p:dataTable>
    </ui:define>

</ui:composition>