<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:t="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwHethChkSmaryCheckListBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwHethChkSmaryCheckListBean}" />
    <!-- 审核、详情页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/tdZwHethCheckSmaryChkEdit.xhtml" />
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            function  disabledBtn() {
                document.getElementById("tabView:mainForm:reviewBatchOk").setAttribute("disabled", true);
            }
            function downloadFileClick() {
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            };
        </script>
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业性有害因素监测卡审核"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" oncomplete="datatableOffClick();"/>
                <p:commandButton value="批量审核" icon="ui-icon-check"
                                 oncomplete="datatableOffClick()" process="@this,dataTable"
                                 action="#{mgrbean.openReviewConfirmDialog}"/>
                <p:commandButton value="导出" icon="ui-icon-document" process="@this,mainGrid"
                                 action="#{mgrbean.preExport()}" />
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.downloadFile}"/>
                </p:commandButton>

            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <h:panelGrid columns="3"
                             style="border-color: transparent;margin: -6px;padding: 0px;" id="crptZone">
                    <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                        zoneCode="#{mgrbean.searchZoneCode}"
                                        zoneName="#{mgrbean.searchZoneName}"/>
                </h:panelGrid>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;" >
                <p:inputText id="searchCrptName" value="#{mgrbean.searchUnitName}" style="width: 182px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 218px;" maxlength="50" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="报告单位：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
                <h:panelGrid columns="3" style="border-color: transparent;margin: 0px;padding: 0px;">
                    <p:inputText id="unitName"  readonly="true" style="width:180px;"
                                 value="#{mgrbean.fillSysUnitNames}"
                                 onclick="$('#tabView\\:mainForm\\:onOrgSelect').click()"/>
                    <p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                   action="#{mgrbean.selectOrgList}" process="@this" style="position: relative;left: -30px;"
                    >
                        <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onOrgSelect}" update="unitName"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -33px;"
                                   action="#{mgrbean.clearSelectOrg}" process="@this"
                                   update="unitName"/>
                </h:panelGrid>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:remoteCommand name="searchCommand"
                                 process="@this,rptBdate,rptEdate"
                                 update="rptBdate,rptEdate" />
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rptBdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="报告开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="#{mgrbean.searchRptEdate == null ? mgrbean.today:mgrbean.searchRptEdate}"  styleClass="myCalendar1"
                            value="#{mgrbean.searchRptBdate}" onmousedown="searchCommand()"/>
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rptEdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="报告结束日期，格式输入不正确！"
                            showButtonPanel="true" mindate="#{mgrbean.searchRptBdate}" maxdate="#{mgrbean.today}" styleClass="myCalendar1"
                            value="#{mgrbean.searchRptEdate}" onmousedown="searchCommand()"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="接收日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:remoteCommand name="searchCommand1"
                                 process="@this,rcvBdate,rcvEdate"
                                 update="rcvBdate,rcvEdate" />
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvBdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="#{mgrbean.searchRcvEdate == null ? mgrbean.today:mgrbean.searchRcvEdate}"  styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvBdate}" onmousedown="searchCommand1()"/>
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvEdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收结束日期，格式输入不正确！"
                            showButtonPanel="true" mindate="#{mgrbean.searchRcvBdate}" maxdate="#{mgrbean.today}" styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvEdate}" onmousedown="searchCommand1()"/>
            </p:column>

        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="报告卡编码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText value="#{mgrbean.searchRptNo}" style="width: 182px;" maxlength="50" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{!((4==mgrbean.zoneType and itm[9]==1) or (3==mgrbean.zoneType and itm[9]==3) or (2==mgrbean.zoneType and itm[9]==5) or (3==mgrbean.zoneType and itm[9]==5 and mgrbean.checkLevel eq 2))}"/>
        <p:column headerText="报告卡编码" style="width: 220px;padding-left: 8px;word-wrap: break-word;word-break: break-all;">
            <h:outputText value="#{itm[15]}" />
        </p:column>
        <p:column headerText="用工单位地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="用工单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="用工单位社会信用代码" style="width:140px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="报告单位" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[10]}" rendered="#{mgrbean.level == 1}">
            	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            </h:outputText>
            <h:outputText value="#{itm[11]}" rendered="#{mgrbean.level == 2}">
            	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            </h:outputText>
            <h:outputText value="#{itm[12]}" rendered="#{mgrbean.level == 3}">
            	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            </h:outputText>
        </p:column>
        <p:column headerText="审核期限" style="width:80px;padding-left: 8px;text-align: center;">
        	<p:outputPanel rendered="#{itm[13]!=null}">
	            <p:outputLabel rendered="#{itm[13] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
	               <h:outputText value="已超期" style="color:#FFFFFF"/>
	            </p:outputLabel>
	            <p:outputLabel rendered="#{itm[13] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
	               <h:outputText value="当天截止" style="color:#FFFFFF"/>
	            </p:outputLabel>
	            <p:outputLabel rendered="#{itm[13] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
	               <h:outputText value="剩余#{itm[13]}天" style="color:#FFFFFF"/>
	            </p:outputLabel>
        	</p:outputPanel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '3'}">
                <h:outputText value="待提交" rendered="#{itm[9]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[9]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[9]==2}" style="color: red;"/>
                <h:outputText value="市级待审" rendered="#{itm[9]==3}"/>
                <h:outputText value="市级退回" rendered="#{itm[9]==4}" style="color: red;"/>
                <h:outputText value="省级待审" rendered="#{itm[9]==5}"/>
                <h:outputText value="省级退回" rendered="#{itm[9]==6}" style="color: red;"/>
                <h:outputText value="省级通过" rendered="#{itm[9]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2' and mgrbean.platVersion eq '1'}">
                <h:outputText value="待提交" rendered="#{itm[9]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[9]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[9]==2}" style="color: red;"/>
                <h:outputText value="市级待审" rendered="#{itm[9]==5}"/>
                <h:outputText value="市级退回" rendered="#{itm[9]==6}" style="color: red;"/>
                <h:outputText value="市级通过" rendered="#{itm[9]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2' and mgrbean.platVersion eq '2'}">
                <h:outputText value="待提交" rendered="#{itm[9]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[9]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[9]==2}" style="color: red;"/>
                <h:outputText value="省级待审" rendered="#{itm[9]==5}"/>
                <h:outputText value="省级退回" rendered="#{itm[9]==6}" style="color: red;"/>
                <h:outputText value="省级通过" rendered="#{itm[9]==7}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5" rendered="#{((4==mgrbean.zoneType and itm[9]==1) or (3==mgrbean.zoneType and itm[9]==3) or (2==mgrbean.zoneType and itm[9]==5) or (3==mgrbean.zoneType and itm[9]==5 and mgrbean.checkLevel eq 2))}"/>
            <p:commandLink value="审核" rendered="#{((4==mgrbean.zoneType and itm[9]==1) or (3==mgrbean.zoneType and itm[9]==3) or (2==mgrbean.zoneType and itm[9]==5) or (3==mgrbean.zoneType and itm[9]==5 and mgrbean.checkLevel eq 2))}" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="0" target="#{mgrbean.ifIsCheckout}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{!((4==mgrbean.zoneType and itm[9]==1) or (3==mgrbean.zoneType and itm[9]==3) or (2==mgrbean.zoneType and itm[9]==5) or (3==mgrbean.zoneType and itm[9]==5 and mgrbean.checkLevel eq 2))}" />
            <p:commandLink value="详情" process="@this"  update=":tabView"  action="#{mgrbean.modInitAction}"
                           rendered="#{!((4==mgrbean.zoneType and itm[9]==1) or (3==mgrbean.zoneType and itm[9]==3) or (2==mgrbean.zoneType and itm[9]==5) or (3==mgrbean.zoneType and itm[9]==5 and mgrbean.checkLevel eq 2))}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{1}" target="#{mgrbean.ifIsCheckout}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <p:dialog id="reviewConfirmDialog" header="审核意见"
                  widgetVar="ReviewConfirmDialog" resizable="false" width="840"
                  modal="true">
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="审核结果："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:selectOneRadio value="#{mgrbean.checkState}" style="width: 120px">
                            <f:selectItem itemLabel="通过" itemValue="1"/>
                            <f:selectItem itemLabel="退回" itemValue="2"/>
                            <p:ajax event="change" process="@this" listener="#{mgrbean.changeCheckStateBatch}"
                                    update="batchCheckRst" resetValues="true"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="审核意见："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px;">
                        <p:inputTextarea rows="5" autoResize="false" id="batchCheckRst"
                                         style="resize: none;width: 594px;height: 120px;margin-left: 5px"
                                         maxlength="100" value="#{mgrbean.checkRst}"
                        />
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" action="#{mgrbean.reviewBatchAction}"
                                         icon="ui-icon-check" onclick="zwx_loading_start();"
                                         process="@this,reviewConfirmDialog"
                                         oncomplete="zwx_loading_stop()"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('ReviewConfirmDialog').hide();datatableOffClick()"
                                         type="button" resetValues="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>
