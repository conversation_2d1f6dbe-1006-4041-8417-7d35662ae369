<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">

    <ui:define name="insertEditScripts">
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml" />
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="技术服务申报" rendered="#{mgrbean.resetFlag}"/>
                <h:outputText value="技术服务档案详情" rendered="#{!mgrbean.resetFlag}"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="submitBtn" action="#{mgrbean.resetOcchethRpt}" rendered="#{mgrbean.resetFlag}" update=":tabView" process="@this">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:spacer width="5px" rendered="#{mgrbean.ifShowQRCode eq '1' and '0' eq mgrbean.ifView}" />
                <p:commandButton value="二维码" icon="ui-icon-document"  action="#{mgrbean.openQrCodeDiag}" rendered="#{mgrbean.ifShowQRCode eq '1' and mgrbean.tdZwOcchethRpt.state eq 1}"
                                 update=":tabView" process="@this">
                </p:commandButton>
                <p:spacer width="5px" rendered="#{mgrbean.resetFlag}" />
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}"
                                 update=":tabView" process="@this">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <!-- 用人单位信息区 -->
        <p:fieldset legend="用人单位信息"  style="margin-top: 5px;margin-bottom: 5px;" id="viewField1">
            <p:panelGrid style="width:100%;height:100%;">
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="单位名称：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt != null ? mgrbean.tdZwOcchethRpt.crptName : ''}" />
                    </p:column>
                    <p:column style="text-align: right;width:260px;">
                        <p:outputLabel value="用人单位地区：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tmpArea != null ? mgrbean.tmpArea : ''}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="社会信用代码：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt != null ? mgrbean.tdZwOcchethRpt.creditCode : ''}" />
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="单位地址：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt != null ? mgrbean.tdZwOcchethRpt.address : ''}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="行业类别：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.fkByIndusTypeId != null ? mgrbean.tdZwOcchethRpt.fkByIndusTypeId.codeName : ''}" />
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="经济性质：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.fkByEconomyId != null ? mgrbean.tdZwOcchethRpt.fkByEconomyId.codeName : ''}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="企业规模：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt.fkByCrptSizeId != null ? mgrbean.tdZwOcchethRpt.fkByCrptSizeId.codeName : ''}" />
                    </p:column>
                    <p:column style="text-align: right;">
                        <p:outputLabel value="联系人：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.linkMan}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="联系电话：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" colspan="3">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.linkPhone}" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>

        <p:fieldset legend="技术服务申报"  style="margin-top: 5px;margin-bottom: 5px;" id="viewField2">
            <p:panelGrid style="width:100%;height:100%;" id="orgRptPanelExeId">
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="质控编号：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;" colspan="#{mgrbean.ifExtent4Eq2==1?1:3}">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt != null ? (null != mgrbean.tdZwOcchethRpt.manageNo ? mgrbean.tdZwOcchethRpt.manageNo : '') : ''}" />
                    </p:column>
                    <p:column style="text-align: right;" rendered="#{mgrbean.ifExtent4Eq2==1}">
                        <p:outputLabel value="委托协议附件：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;" rendered="#{mgrbean.ifExtent4Eq2==1}">
                        <p:commandButton value="查看" process="@this" rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.entrustFilePath != null and mgrbean.tdZwOcchethRpt.entrustFilePath != ''}"
                                                                                            onclick="window.open('/webFile/#{mgrbean.tdZwOcchethRpt.entrustFilePath}')" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="技术服务类别：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.fkBySortId.codeName}" />
                    </p:column>
                    <p:column style="text-align: right;width:260px;">
                        <p:outputLabel value="报告日期：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.rptDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                        </h:outputText>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="项目名称：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" colspan="3">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.projectName}"  />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="报告编号：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.rptNo}" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;" rendered="#{mgrbean.ifExtent4Eq2==1}">
                        <p:outputLabel value="总结报告附件：" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;" rendered="#{mgrbean.ifExtent4Eq2==0}">
                        <p:outputLabel value="附件：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left: 10px;">
                        <p:commandButton value="查看" process="@this" rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.filePath != null and mgrbean.tdZwOcchethRpt.filePath != ''}"
                                         onclick="window.open('/webFile/#{mgrbean.tdZwOcchethRpt.filePath}')" />
                    </p:column>
                </p:row>
                <p:row rendered="#{null ne mgrbean.tmpExtend1TypeList and mgrbean.tmpExtend1TypeList.contains(mgrbean.sortId)}">
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="体检开展方式：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left:10px;" colspan="3">
                        <p:outputLabel value="#{mgrbean.typeListStr}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifExtent4Eq2==1}">
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="体检日期：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left:10px;" >
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.bhkBeginDate}" rendered="#{mgrbean.tdZwOcchethRpt.bhkBeginDate!=null}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                        </h:outputText>
                        <h:outputText value="~" rendered="#{mgrbean.tdZwOcchethRpt.bhkBeginDate!=null and mgrbean.tdZwOcchethRpt.bhkEndDate!=null}"></h:outputText>
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.bhkEndDate}" rendered="#{mgrbean.tdZwOcchethRpt.bhkEndDate!=null}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                        </h:outputText>
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;">
                        <p:outputLabel value="体检人数：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" >
                        <h:outputText value="#{mgrbean.tdZwOcchethRpt.bhkPsn}" />
                    </p:column>
                </p:row>

                <p:row rendered="#{null ne mgrbean.zybRiskExtend1TypeList and mgrbean.zybRiskExtend1TypeList.contains(mgrbean.sortId)}">
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="职业病危害风险分类：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" colspan="3">
                        <p:outputLabel value="#{mgrbean.zybRiskStr}" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:dialog header="二维码" widgetVar="QrCodeDialog" id="QrCodeDialog" resizable="false" modal="true" minHeight="100" width="380" >
            <table  width="100%" style="text-align: center;">
                <tr>
                    <td style="display: inline-flex;" >
                        <div style="text-align: center;padding-top: 5px;" id="qrcode">
                            <img src="#{mgrbean.qrPicPath!=null?'/webFile/'.concat(mgrbean.qrPicPath):''}" width="150" height="150" />
                        </div>
                    </td>
                </tr>
            </table>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="二维码下载" icon="ui-icon-arrowthickstop-1-s"  onclick="document.getElementById('tabView:viewForm:qrCodeDownLoad').click();"  process="@this"
                                         />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('QrCodeDialog').hide();" process="@this"/>

                        <p:commandLink ajax="false" id="qrCodeDownLoad"  immediate="true" process="@this"
                                       onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                            <f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="/#{mgrbean.qrPicPath}"/>
                            <f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{mgrbean.qrPicName}"/>
                            <p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>

</ui:composition>