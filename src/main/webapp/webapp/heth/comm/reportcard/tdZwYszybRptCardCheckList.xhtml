<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwYszybRptCardCheckListBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwYszybRptCardCheckListBean}" />

    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwYszybRptCardReviewView.xhtml"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="疑似职业病报告卡审核"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" id="plsh" action="#{mgrbean.openReviewConfirmDialog}" process="@this,dataTable"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="提示：" style="color:red;"/>
                <h:outputText value="#{mgrbean.tipInfo}" style="color:blue;"/>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;" >
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width: 180px;" maxlength="50" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="50" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchPersonnelName" value="#{mgrbean.searchPersonnelName}" style="width: 180px;" maxlength="25"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="疑似职业病名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchdiseName}" style="width: 180px;" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="接收日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvBdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="rcvEdate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="接收结束日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchRcvEdate}" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{itm[11]=='1'?false:true}"/>

        <p:column headerText="用工单位地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="人员姓名" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="证件号码" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="疑似职业病名称" style="width:120px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="报告单位" style="width:210px;padding-left: 8px;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="审核期限" style="width:80px;text-align: center;" rendered="#{mgrbean.ifshowdeadline=='true'}">
            <p:outputLabel rendered="#{itm[11]=='1'}">
                <p:outputLabel rendered="#{itm[10] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[10] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
                    <h:outputText value="当天截止" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[10] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
                    <h:outputText value="剩余#{itm[10]}天" style="color:#FFFFFF"/>
                </p:outputLabel>
            </p:outputLabel>
            <h:outputText value="——" rendered="#{itm[11]!='1'}"/>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputLabel value="待提交" rendered="#{itm[9] eq 0}"/>
            <h:outputLabel value="区县级待审" rendered="#{itm[9] eq 1}"/>
            <h:outputLabel value="区县级退回" rendered="#{itm[9] eq 2}" style="color: red"/>
            <h:outputLabel value="市级待审" rendered="#{itm[9] eq 3}"/>
            <h:outputLabel value="市级退回" rendered="#{itm[9] eq 4}" style="color: red"/>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq 2 and mgrbean.platVersion eq 1}">
                <h:outputLabel value="市级待审" rendered="#{itm[9]==5}"/>
                <h:outputLabel value="市级退回" rendered="#{itm[9]==6}" style="color: red"/>
                <h:outputLabel value="市级通过" rendered="#{itm[9]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq 3 or mgrbean.platVersion eq 2}">
                <h:outputLabel value="省级待审" rendered="#{itm[9]==5}"/>
                <h:outputLabel value="省级退回" rendered="#{itm[9]==6}" style="color: red"/>
                <h:outputLabel value="省级通过" rendered="#{itm[9]==7}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="#{itm[11]=='1'?'审核':'详情'}" action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{itm[11]=='1'}" target="#{mgrbean.ifCheck}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <ui:include src="../reviewConfirmDialogComm.xhtml"/>
    </ui:define>
</ui:composition>