<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="职业病鉴定报告卡审核详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="提交" icon="ui-icon-check"  rendered="#{mgrbean.ifIsCheckout == 0}"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:viewForm:check">
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{mgrbean.backAction}"
                                 update=":tabView" process="@this"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="sticky"></p:sticky>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.saveAction}" icon="ui-icon-check" update=":tabView" oncomplete="PF('ConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
            <ui:include src="tdZwHethAppraisalRptCardInfo.xhtml">
                <ui:param name="mgrbean2" value="#{mgrbean.rptCardListBean}"/>
            </ui:include>
            <p:panelGrid styleClass="" id="check"  style="width: 945px;margin: auto;"  rendered="#{mgrbean.ifIsCheckout == 0}">
                <f:facet name="header">
                    <p:row>
                        <p:column colspan="6" style="text-align:left;height: 20px;">
                            <p:outputLabel value="审核意见"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 30px">
                        <p:outputLabel style="color:red;" value="*"/>
                        <p:outputLabel value="审核结果：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:selectOneRadio  value="#{mgrbean.checkResult}">
                            <f:selectItem itemLabel="通过" itemValue="1" />
                            <f:selectItem itemLabel="退回" itemValue="0" />
                            <p:ajax event="change" process="@this,:tabView:viewForm" listener="#{mgrbean.changeCheckState}" update=":tabView:viewForm:check" />
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="审核意见：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputTextarea rows="5" autoResize="false"
                                         style="resize: none;width: 594px;"
                                         value="#{mgrbean.newFlow.proAuditAdv}"
                                         maxlength="100"
                                         rendered="#{mgrbean.level == 3}"/>
                        <p:inputTextarea rows="5" autoResize="false"
                                         style="resize: none;width: 594px;"
                                         value="#{mgrbean.newFlow.countAuditAdv}"
                                         maxlength="100"
                                         rendered="#{mgrbean.level == 1}"/>
                        <p:inputTextarea rows="5" autoResize="false"
                                         style="resize: none;width: 594px;"
                                         value="#{mgrbean.newFlow.cityAuditAdv}"
                                         maxlength="100"
                                         rendered="#{mgrbean.level == 2}"/>
                    </p:column>
                </p:row>
                <!-- 审核人：默认当前单位联系人 -->
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="审核人：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputText value="#{mgrbean.newFlow.proChkPsn}" maxlength="25" style="width: 595px;" rendered="#{mgrbean.level == 3}"/>
                        <p:inputText value="#{mgrbean.newFlow.countyChkPsn}" maxlength="25" style="width: 595px;" rendered="#{mgrbean.level == 1}"/>
                        <p:inputText value="#{mgrbean.newFlow.cityChkPsn}" maxlength="25" style="width: 595px;" rendered="#{mgrbean.level == 2}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;margin-bottom: 50px;">
                <f:facet name="header">
                    <p:row>
                        <p:column colspan="6" style="text-align:left;height: 20px;">
                            <p:outputLabel value="历次审核意见"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:dataTable id="historyList" paginatorPosition="bottom"
                                     value="#{mgrbean.historyList}"
                                     widgetVar="badRsnTable" var="item"
                                     emptyMessage="没有数据！" rowIndexVar="R">
                            <p:column headerText="审核类型"  style="width:100px;text-align: center">
                                <p:outputLabel value="#{item[4]}"/>
                            </p:column>
                            <p:column headerText="审核意见" style="width:510px;">
                                <p:outputLabel value="#{item[1]}"/>
                            </p:column>
                            <p:column headerText="审核人"  style="width:150px;text-align: center">
                                <p:outputLabel value="#{item[2]}"/>
                            </p:column>
                            <p:column headerText="审核日期" style="width:140px;text-align: center">
                                <p:outputLabel value="#{item[3]}">
                                    <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8"></f:convertDateTime>
                                </p:outputLabel>
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:outputPanel>
    </h:form>
</ui:composition>