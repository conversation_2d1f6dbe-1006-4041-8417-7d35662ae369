<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwOcchethRptCommBean}" />
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>

    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwOcchethRptCommView.xhtml"/>
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/tdZwOcchethRptCommEdit.xhtml"></ui:param>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style>
            .myCalendar1 input{
                width: 85px;
            }
            .myCalendar2 input{
                width: 180px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="技术服务申报" />
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
            <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                             process="@this,startDate,endDate,crptNameSearch,manageNoSearch,searchGrid,stateSearch" />
            <p:spacer width="5px" />
            <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addPre}" update=":tabView" process="@this">
                <p:resetInput target=":tabView:editForm:editField1,:tabView:editForm:editField2" />
            </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:45px;padding-top:0px;padding-bottom: 0px;">
                <h:outputText value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:260px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="startDate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="检测（评价）开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.rptDateSearchStart}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="endDate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="检测（评价）结束日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.rptDateSearchEnd}" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 260px;" >
                <p:inputText id="crptNameSearch" value="#{mgrbean.crptNameSearch}" style="width: 196px;" placeholder="模糊查询" maxlength="18"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="质控编号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:inputText id="manageNoSearch" value="#{mgrbean.manageNoSearch}" placeholder="模糊查询" style="width: 196px;" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;padding-top:0px;padding-bottom: 0px;">
                <h:outputText value="技术服务类别：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <h:panelGrid columns="4" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchGrid">
                    <p:inputText id="jsfwTreeName" value="#{mgrbean.jsfwTreeName}" style="width: 196px;" readonly="true" />
                    <h:inputHidden id="jsfwTreeCode" value="#{mgrbean.jsfwTreeCode}" />
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"  style="position: relative;left: -40px;"
                                   oncomplete="PF('DiseaseOverlayPanel').show()" type="button" />
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -41px;"
                                   action="#{mgrbean.clearJsfwlb}"
                                   process="@this,searchGrid,diseaseOverlayPanel" update="searchGrid,diseaseOverlayPanel"/>
                </h:panelGrid>
                <p:overlayPanel id="diseaseOverlayPanel" for="jsfwTreeName"
                                style="width:320px;" widgetVar="DiseaseOverlayPanel"
                                showCloseIcon="true" onHide="Hide();">
                    <p:tree var="node" selectionMode="checkbox" id="diseaseTree"
                            value="#{mgrbean.jsfwlbTree}"
                            style="width: 300px;height: 200px;overflow-y: auto;"
                            selection="#{mgrbean.searchSelJsfw}">
                        <p:treeNode>
                            <p:outputLabel value="#{node.codeName}" />
                        </p:treeNode>
                    </p:tree>
                </p:overlayPanel>
                <p:remoteCommand name="Hide"
                                 process="@this,searchGrid,diseaseOverlayPanel"
                                 action="#{mgrbean.selectJsfwlbAction}"
                                 update="searchGrid" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectManyCheckbox id="stateSearch" value="#{mgrbean.stateSearch}" style="width: 180px;">
                    <f:selectItem itemLabel="待提交" itemValue="0"/>
                    <f:selectItem itemLabel="已提交" itemValue="1"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="质控编号" style="width:240px;text-align: center;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width:240px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="所属区域" style="width:240px;">
            <h:outputText value="#{itm[3]}"  />
        </p:column>
        <p:column headerText="联系人" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            </h:outputText>
        </p:column>
        <p:column headerText="技术服务类别" style="width:240px;">
            <h:outputText value="#{itm[7]}" />
        </p:column>
        <p:column headerText="状态" style="width:80px;text-align: center;">
            <p:outputLabel  rendered="#{itm[8]=='0' or itm[8]==0}">
                <font color="red">待提交</font>
            </p:outputLabel>
            <p:outputLabel rendered="#{itm[8]=='1' or itm[8]==1}">
                <font color="green">已提交</font>
            </p:outputLabel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" update=":tabView" resetValues="true" process="@this" rendered="#{itm[8]=='0' or itm[8]==0}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="6" rendered="#{itm[8]=='0' or itm[8]==0}" />
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}" update="dataTable" process="@this" rendered="#{itm[8]=='0' or itm[8]==0}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <!-- 详情与修改删除是排斥关系 -->
            <p:commandLink value="详情" update=":tabView" resetValues=":tabView" action="#{mgrbean.preView}" rendered="#{itm[8]=='1' or itm[8]==1}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="0"/>
            </p:commandLink>
           <!-- <p:spacer width="6" rendered="#{itm[9]!=null}" />
            <p:commandLink value="查看附件" process="@this"
                           onclick="window.open('/webFile/#{itm[9]}')"
                           rendered="#{itm[9]!=null}">
            </p:commandLink>-->
        </p:column>
    </ui:define>
</ui:composition>