<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwHethAppraisalRptCardInfoBean"-->
        <ui:param name="mgrbean" value="#{tdZwHethAppraisalRptCardInfoBean}"/>
        <p:tabView id="tabView" dynamic="true" cache="true"  style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <p:panelGrid style="width:100%;margin-bottom:5px;" >
                    <f:facet name="header">
                        <p:row>
                            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                                <p:outputLabel value="职业病鉴定报告卡详情"/>
                            </p:column>
                        </p:row>
                    </f:facet>
                </p:panelGrid>
                <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
                    <!--报告卡页面-->
                    <ui:include src="tdZwHethAppraisalRptCardInfo.xhtml">
                        <ui:param name="mgrbean2" value="#{mgrbean}"/>
                    </ui:include>
                </p:outputPanel>
            </p:tab>
        </p:tabView>

    </h:body>
</f:view>
</html>
