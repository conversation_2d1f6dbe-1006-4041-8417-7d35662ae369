<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwZybReportCardReviewListBean"-->
    <ui:param name="mgrbean" value="#{tdZwZybReportCardReviewListBean}"/>
    <!-- 审核页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/tdZwZybReportCardReviewAudit.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style>
            .myCalendar1 input {
                width: 78px;
            }

            table.ui-selectoneradio td label {
                white-space: nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业病报告卡审核"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="批量审核" icon="ui-icon-check" id="batchReview"
                                 action="#{mgrbean.openBatchReviewDialog}" process="@this,dataTable"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="诊断机构地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="人员姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width:280px;">
                <p:inputText value="#{mgrbean.searchPersonnelName}" style="width: 180px;" maxlength="25"
                             placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="证件号码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="20" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;"
                             maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="职业病名称："/>
            </p:column>
            <p:column
                    style="text-align:left;padding-left:9px;height:38px;display:flex;justify-content: flex-start;align-items: center;border: none;">
                <p:inputText readonly="true" style="width:180px" maxlength="1000"
                             id="searchOdName" value="#{mgrbean.searchOdName}"
                             onclick="$('#tabView\\:mainForm\\:onOdSelect').click()"/>
                <p:commandLink styleClass="ui-icon ui-icon-search" style="position: relative;left: -18px;"
                               id="onOdSelect" action="#{mgrbean.initOdSelect}" process="@this">
                    <p:ajax event="dialogReturn" resetValues="true" listener="#{mgrbean.onOdSelect}"
                            process="@this" update="searchOdName"/>
                </p:commandLink>
                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" style="position: relative;left: -15px;"
                               action="#{mgrbean.clearOdName}" process="@this" update="searchOdName"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="报告日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchReportBeginDate}"
                                              endDate="#{mgrbean.searchReportEndDate}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="接收日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchReceiveBeginDate}"
                                              endDate="#{mgrbean.searchReceiveEndDate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column selectionMode="multiple" style="width:2%;text-align:center;"
                  disabledSelection="#{!((4==mgrbean.zoneType and itm[11]==1) or (3==mgrbean.zoneType and itm[11]==3) or (2==mgrbean.zoneType and itm[11]==5) or (3==mgrbean.zoneType and itm[11]==5 and mgrbean.checkLevel eq 2))}"/>
        <p:column headerText="诊断机构地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="人员姓名" style="width:100px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width:230px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="职业病名称" style="width:180px;padding-left: 8px;text-align: center;">
            <h:outputText value="无" rendered="#{'' eq itm[5]}"/>
            <h:outputText value="#{itm[5]}" rendered="#{'' ne itm[5] and '' eq itm[6]}"/>
            <h:outputText value="#{itm[5]}（#{itm[6]}）" rendered="#{'' ne itm[5] and '' ne itm[6]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputLabel value="#{itm[7]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="报告单位" style="width:230px;padding-left: 8px;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputLabel value="#{itm[9]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="处理期限" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputText value="——" rendered="#{null eq itm[10]}"/>
            <p:outputLabel rendered="#{null ne itm[10]}">
                <p:outputLabel rendered="#{itm[10] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
                    <h:outputText value="已超期" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[10] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
                    <h:outputText value="当天截止" style="color:#FFFFFF"/>
                </p:outputLabel>
                <p:outputLabel rendered="#{itm[10] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
                    <h:outputText value="剩余#{itm[10]}天" style="color:#FFFFFF"/>
                </p:outputLabel>
            </p:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width:80px;padding-left: 8px;text-align: center;">
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '3'}">
                <h:outputText value="待提交" rendered="#{itm[11]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[11]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[11]==2}" style="color: red;"/>
                <h:outputText value="市级待审" rendered="#{itm[11]==3}"/>
                <h:outputText value="市级退回" rendered="#{itm[11]==4}" style="color: red;"/>
                <h:outputText value="省级待审" rendered="#{itm[11]==5}"/>
                <h:outputText value="省级退回" rendered="#{itm[11]==6}" style="color: red;"/>
                <h:outputText value="省级审核通过" rendered="#{itm[11]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2' and mgrbean.platVersion eq '1'}">
                <h:outputText value="待提交" rendered="#{itm[11]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[11]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[11]==2}" style="color: red;"/>
                <h:outputText value="市级待审" rendered="#{itm[11]==5}"/>
                <h:outputText value="市级退回" rendered="#{itm[11]==6}" style="color: red;"/>
                <h:outputText value="市级通过" rendered="#{itm[11]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2' and mgrbean.platVersion eq '2'}">
                <h:outputText value="待提交" rendered="#{itm[11]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[11]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[11]==2}" style="color: red;"/>
                <h:outputText value="省级待审" rendered="#{itm[11]==5}"/>
                <h:outputText value="省级退回" rendered="#{itm[11]==6}" style="color: red;"/>
                <h:outputText value="省级通过" rendered="#{itm[11]==7}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink value="审核" action="#{mgrbean.modInitAction}" process="@this" update=":tabView"
                           rendered="#{((4==mgrbean.zoneType and itm[11]==1) or (3==mgrbean.zoneType and itm[11]==3) or (2==mgrbean.zoneType and itm[11]==5) or (3==mgrbean.zoneType and itm[11]==5 and mgrbean.checkLevel eq 2))}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.disCardId}"/>
                <f:setPropertyActionListener value="true" target="#{mgrbean.ifAudit}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" process="@this" update=":tabView"
                           rendered="#{!((4==mgrbean.zoneType and itm[11]==1) or (3==mgrbean.zoneType and itm[11]==3) or (2==mgrbean.zoneType and itm[11]==5) or (3==mgrbean.zoneType and itm[11]==5 and mgrbean.checkLevel eq 2))}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.disCardId}"/>
                <f:setPropertyActionListener value="false" target="#{mgrbean.ifAudit}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <ui:include src="../reviewConfirmDialogComm.xhtml"/>
    </ui:define>
</ui:composition>