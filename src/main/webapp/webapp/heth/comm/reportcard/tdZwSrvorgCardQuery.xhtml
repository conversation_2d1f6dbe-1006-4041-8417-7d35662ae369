<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:t="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwSrvorgCardQueryBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwSrvorgCardQueryBean}" />
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwSrvorgCardView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
        </style>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }

            function windowScrollTop() {
                window.scrollTo(0, 0);
            }
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="放射卫生技术服务信息报送卡查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:menuButton id="importRadhethBtn" value="导入" rendered="#{mgrbean.importJurisdiction}">
                    <p:menuitem value="模板下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                        <p:fileDownload value="#{mgrbean.dosModelFile}"/>
                    </p:menuitem>
                    <p:menuitem value="导入" icon="ui-icon-arrowreturnthick-1-n" update=":tabView:mainForm:importSrvorgCardDialog" process="@this"
                                action="#{mgrbean.openImportSrvorgCardDialog}">
                    </p:menuitem>
                    <p:menuitem value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                process="@this"
                                onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
                                rendered="#{null ne mgrbean.importErrFilePath and '' ne mgrbean.importErrFilePath}">
                        <p:fileDownload value="#{mgrbean.errorImportFile}"/>
                    </p:menuitem>
                </p:menuButton>
            </h:panelGrid>

        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="报告单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:260px;" >
                <zwx:ZoneSingleComp zoneList="#{mgrbean.zoneList}"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="报告单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 260px;">
                <p:inputText id="searchOrgName" value="#{mgrbean.searchOrgName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="服务单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px">
                <p:inputText id="searchUnitName" value="#{mgrbean.searchUnitName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>

        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="技术服务领域："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectServiceNames}"
                                        selectedIds="#{mgrbean.selectServiceRids}" panelWidth="320"
                                        simpleCodeList="#{mgrbean.badServiceList}" ifSelectParent="true" ifContantsParent="true"
                                        ifTree="true" height="200" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="出具技术服务报告时间："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptSDate}"
                                              endDate="#{mgrbean.searchRptEDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
            <p:column style="text-align:right;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItem itemLabel="待提交" itemValue="0"/>
                    <f:selectItem itemLabel="已提交" itemValue="1"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.importJurisdiction}">
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="数据来源："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchSourceList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="0" itemLabel="系统录入"/>
                    <f:selectItem itemValue="1" itemLabel="国家导入"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="报告单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column headerText="报告单位名称" style="width: 360px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="服务单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[3]}" escape="false"/>
        </p:column>
        <p:column headerText="服务单位名称" style="width: 360px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="出具技术服务报告时间" style="width: 160px;text-align: center;">
            <h:outputLabel value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width: 75px;text-align: center;">
            <h:outputText value="待提交" rendered="#{0 eq itm[6]}"/>
            <h:outputText value="已提交" rendered="#{1 eq itm[6]}"/>
        </p:column>
        <p:column headerText="数据来源" style="width: 75px;text-align: center; display:#{mgrbean.importJurisdiction ? '' : 'none'}">
            <h:outputText value="系统录入" rendered="#{0 eq itm[7]}"/>
            <h:outputText value="国家导入" rendered="#{1 eq itm[7]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情"  action="#{mgrbean.viewInitAction}" process="@this,:tabView:mainForm:mainGrid"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>

        </p:column>
    </ui:define>
    
    <ui:define name="insertOtherMainContents">
        <p:dialog header="放射卫生报告卡导入" widgetVar="ImportSrvorgCardDialog" id="importSrvorgCardDialog" resizable="false" modal="true"  width="800">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload  requiredMessage="请选择要文件上传！" styleClass="table-border-none" id="doseFileUploadJs"
                                       fileUploadListener="#{mgrbean.importSrvorgCardAction}" process="@this"
                                       label="选择文件" invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，请重新上传！"
                                       allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                                       invalidFileMessage="只能上传xls、xlsx格式的文件！"
                                       previewWidth="120" cancelLabel="取消"
                                       uploadLabel="导入"  oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                       dragDropSupport="true" mode="advanced" sizeLimit="10485760"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>

</ui:composition>
