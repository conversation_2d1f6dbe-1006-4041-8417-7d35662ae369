<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <script type="text/javascript">
        //<![CDATA[
        function disabledInput(ifView,id){
            if(ifView=="false"){
                return ;
            }

            var text;
            var $tabView ;
            if(id){
                $tabView = $("#"+id)
            }else{
                $tabView = $("#tabView\\:editForm\\:tabViewEdit\\:mainTabView");
            }
            $tabView.find("input,textarea").each(function(){
                if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
                    $(this).css("pointer-events","none");
                }else{
                    $(this).prop("disabled",true);
                }
                $(this).css("opacity","1");
            });
            //单选框label的for标签处理
            $tabView.find("label").each(function(){
                $(this).css("pointer-events","none");
            });
            $tabView.find("a").each(function(){
                text = $(this).text();
                if(!text){
                    text = $(this).attr("title");
                }
                if("选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
                    $(this).remove();
                }else if("查看"==text||"修改"==text){

                }else{
                    $(this).prop("disabled",true);
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","0.35");
                }

            });
            $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
                $(this).addClass("ui-state-disabled");
                $(this).css("opacity","1");
                $(this).css("pointer-events","none");
            });
            //下拉
            $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                $(this).addClass("ui-state-disabled");
                $(this).css("pointer-events","none");
                $(this).css("opacity","1");
            });
            //按钮
            $tabView.find("button").each(function(){
                text = $(this).text();
                if("扫描"==text||"预览"==text||"制作"==text||"上传"==text||"添加"==text||"设计"==text||"保存"==text){
                    $(this).remove();
                }else if("进入"==text||"查看"==text||"返回"==text||"< 返回"==text||"删除"==text){

                }else{
                    $(this).prop("disabled",true);
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","0.35");
                }
            });
        }
        //]]>
    </script>
    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />

        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="疑似职业病报告卡详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdZwYszybRptListCommBean.backAction}"
                                 update=":tabView" process="@this"/>
            </h:panelGrid>
            <p:sticky target="sticky"/>
        </p:outputPanel>

        <ui:param name="mgrbean" value="#{tdZwYszybRptListCommBean}"/>
        <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
            <ui:include src="tdZwYszybRptInfo.xhtml">
                <ui:param name="birthIfShow" value="true"></ui:param>
            </ui:include>
        </p:outputPanel>

    </h:form>
</ui:composition>