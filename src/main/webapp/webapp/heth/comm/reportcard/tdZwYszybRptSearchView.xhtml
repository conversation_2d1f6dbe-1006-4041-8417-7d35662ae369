<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/diagnosisMainComm.css" />

        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="疑似职业病报告卡"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" action="#{tdZwYszybRptListSearchCommBean.backAction}"
                                 update=":tabView" process="@this"/>
            </h:panelGrid>
            <p:sticky target="sticky"/>
        </p:outputPanel>

        <ui:param name="mgrbean" value="#{tdZwYszybRptListSearchCommBean}"/>
        <p:outputPanel styleClass="businessInfo" style="background: rgb(252, 253, 253);">
            <ui:include src="tdZwYszybRptInfo.xhtml">
                <ui:param name="birthIfShow" value="false"></ui:param>
            </ui:include>
            <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;margin-bottom: 50px;">
                <f:facet name="header">
                    <p:row>
                        <p:column colspan="6" style="text-align:left;height: 20px;">
                            <p:outputLabel value="历次审核意见"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:dataTable value="#{mgrbean.bgkFlows}" var="bgk" emptyMessage="暂无数据！">
                            <p:column headerText="审核类型" style="text-align:center;width:100px;">
                                <h:outputText value="初审退回" rendered="#{bgk.operFlag==11 or bgk.operFlag==13}" />
                                <h:outputText value="初审通过" rendered="#{bgk.operFlag==31 or bgk.operFlag==43}" />
                                <h:outputText value="复审退回" rendered="#{bgk.operFlag==22}" />
                                <h:outputText value="#{mgrbean.tdZwYszybRpt.fkByEmpZoneId.ifCityDirect==1?'初审':'复审'}通过" rendered="#{bgk.operFlag==41}" />
                                <h:outputText value="终审退回" rendered="#{bgk.operFlag==32}" />
                                <h:outputText value="终审通过" rendered="#{bgk.operFlag==42}" />
                            </p:column>
                            <p:column headerText="审核意见">
                                <p:outputLabel value="#{bgk.auditAdv}" />
                            </p:column>
                            <p:column headerText="审核人" style="text-align:center;width:150px;">
                                <p:outputLabel value="#{bgk.auditMan}" />
                            </p:column>
                            <p:column headerText="审核日期" style="text-align:center;width:150px;">
                                <h:outputText value="#{bgk.createDate}">
                                    <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="Asia/Shanghai" locale="cn" />
                                </h:outputText>
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:outputPanel>
    </h:form>
</ui:composition>