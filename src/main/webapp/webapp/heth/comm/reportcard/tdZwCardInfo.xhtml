<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
        <p:row>
            <p:column colspan="2" ><span>本环节文书</span></p:column>
        </p:row>
        <p:row>
            <p:column style="padding: 0;" colspan="2">
                <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 190px;">
                <h:outputText value="《职业病报告卡》"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.disCard.annexPath}')" rendered="#{mgrbean.disCard.annexPath != null}">
                    </p:commandButton>
                </p:outputPanel>

            </p:column>
        </p:row>
    </p:panelGrid>

    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;">
        <p:row>
            <p:column styleClass="noBorder"
                      style="text-align: center;padding-bottom: 15px;" colspan="3">
                <h:outputText value="职业病报告卡"
                              style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="noBorder" style="text-align: left;">
                <h:outputText value="报告卡编码："/>
                <p:outputLabel value="#{mgrbean.disCard.rptNo}"/>
            </p:column>
        </p:row>
    </p:panelGrid>

    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;margin-top: 5px">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;text-align:left;" colspan="4">
                    <p:outputLabel value="劳动者信息"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.personnelName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="证件类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByCardTypeId.codeName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="证件号码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.idc}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="性别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="男" rendered="#{mgrbean.disCard.sex==1}"/>
                <p:outputLabel value="女" rendered="#{mgrbean.disCard.sex==2}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="出生日期："/>
            </p:column>
            <p:column style="padding-left: 6px;" >
                <p:outputLabel value="#{mgrbean.disCard.birthday}" rendered="#{ifEncrypt == null}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
                <p:outputLabel value="********" rendered="#{ifEncrypt != null }" />
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="联系电话："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.linktel}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="紧急联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.emergLinkName}" />
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="紧急联系人联系方式："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.emergLinktel}" />
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;text-align:left; " colspan="4">
                    <p:outputLabel value="用人单位信息" />
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.crptName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.creditCode}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="企业类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByEconomyId.codeName}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByCrptSizeId.codeName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="用人单位所在区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByZoneId.fullName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="用人单位详细地址："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.address}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地址邮编："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.postcode}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="用人单位联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.safeposition}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位联系人电话："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.safephone}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                    <p:outputLabel value="用工单位信息"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="用工单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.empCrptName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.empCreditCode}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="企业类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByEmpEconomyId.codeName}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByEmpIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByEmpCrptSizeId.codeName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="用工单位所在区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByEmpZoneId.fullName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;text-align:left; " colspan="4">
                    <p:outputLabel value="职业病报告信息" />
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="申请诊断日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.applyDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px">
                <p:outputLabel value="诊断日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.diagDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.disCard.fkByZybTypeId.extendS4==1}">
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="诊断I期："/>
            </p:column>
            <p:column style="padding-left: 6px;">
                <p:outputLabel value="#{mgrbean.disCard.diag1Date}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px">
                <p:outputLabel value="诊断Ⅱ期："/>
            </p:column>
            <p:column style="padding-left: 6px;">
                <p:outputLabel value="#{mgrbean.disCard.diag2Date}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.disCard.fkByZybTypeId.extendS4==1}">
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="诊断Ⅲ期："/>
            </p:column>
            <p:column style="padding-left: 6px;" colspan="3">
                <p:outputLabel value="#{mgrbean.disCard.diag3Date}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="职业病名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputPanel rendered="#{null != mgrbean.disCard.fkByZybTypeId and null != mgrbean.disCard.fkByZybTypeId.rid}">
                    <p:outputLabel value="#{mgrbean.disCard.fkByZybTypeId.codeName}"/>
                    <p:outputLabel value="（#{mgrbean.disCard.zybDisName}）" rendered="#{mgrbean.disCard.fkByZybTypeId.extendS2==1}"/>
                </p:outputPanel>
                <p:outputLabel value="无" rendered="#{null == mgrbean.disCard.fkByZybTypeId or null == mgrbean.disCard.fkByZybTypeId.rid}"/>
            </p:column>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="职业病种类："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fkByZybDisTypeId.codeName}" rendered="#{null != mgrbean.disCard.fkByZybTypeId and null != mgrbean.disCard.fkByZybTypeId.rid}"/>
                <p:outputLabel value="无" rendered="#{null == mgrbean.disCard.fkByZybTypeId or null == mgrbean.disCard.fkByZybTypeId.rid}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{null != mgrbean.disCard.fkByZybTypeId and null != mgrbean.disCard.fkByZybTypeId.rid and mgrbean.disCard.fkByZybTypeId.extendS4==2}">
            <p:column styleClass="column_title">
                <p:outputLabel value="职业性化学中毒分类："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" colspan="3">
                <p:outputLabel value="#{mgrbean.disCard.zyPoisonType==1?'急性':'慢性'}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{null != mgrbean.disCard.fkByZybTypeId and null != mgrbean.disCard.fkByZybTypeId.rid and mgrbean.disCard.fkByZybTypeId.extendS4==1}">
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="病例类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;" colspan="3">
                <p:outputLabel value="#{mgrbean.disCard.fkByRptTypeId.codeName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="统计工种："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;" colspan="3">
                <p:outputLabel value="#{mgrbean.disCard.fkByWorkTypeId.codeName}"/>
                <p:outputLabel value="（#{mgrbean.disCard.workOther}）" rendered="#{mgrbean.disCard.workOther!=null}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="接触的职业性有害因素："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;padding-bottom: 3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.selectBadRsnNames}"/>

            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="死亡日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.deathDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="死亡原因："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.dieRsn}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="接害开始日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;">
                <p:outputLabel value="#{mgrbean.disCard.begTchDust}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="实际接害工龄："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.tchDustYear}"  />
                <p:spacer width="5" />
                <h:outputLabel value="年"/>
                <p:spacer width="5" />
                <p:outputLabel value="#{mgrbean.disCard.tchDustMonth}" />
                <p:spacer width="5" />
                <h:outputLabel value="月"/>
                <p:spacer width="5" />
                <p:outputLabel value="#{mgrbean.disCard.tchDays}"/>
                <p:spacer width="5" />
                <h:outputLabel value="日"/>
                <p:spacer width="5" />
                <p:outputLabel value="#{mgrbean.disCard.tchHours}" />
                <p:spacer width="5" />
                <h:outputLabel value="时"/>
                <p:spacer width="5" />
                <p:outputLabel value="#{mgrbean.disCard.tchMinutes}" />
                <p:spacer width="5" />
                <h:outputLabel value="分"/>
            </p:column>
        </p:row>
        <p:row rendered="#{null != mgrbean.disCard.fkByZybTypeId and null != mgrbean.disCard.fkByZybTypeId.rid and mgrbean.disCard.fkByZybTypeId.extendS4==1}">
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="合并症："/>
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 220px;" colspan="3">
                <p:outputLabel value="#{mgrbean.hbzName}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="组织诊断单位："/>
            </p:column>
            <p:column style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.diagUnitName}" />
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断单位负责人："/>
            </p:column>
            <p:column  style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.diagRespPsn}" />
            </p:column>
        </p:row>
        <c:if test="#{mgrbean.disCard.cardDocList != null and mgrbean.disCard.cardDocList.size() > 0}">
            <c:forEach var="item" items="#{mgrbean.disCard.cardDocList}">
                <p:row>
                    <p:column styleClass="column_title" style="height:30px;">
                        <p:outputLabel value="诊断医师："/>
                    </p:column>
                    <p:column style="text-align: left;padding-left:6px;">
                        <p:outputLabel value="#{item.docName}" />
                    </p:column>
                    <p:column styleClass="column_title">
                        <p:outputLabel value="诊断医师单位："/>
                    </p:column>
                    <p:column  style="text-align: left;padding-left:6px;">
                        <p:outputLabel value="#{item.unitName}" />
                    </p:column>
                </p:row>
            </c:forEach>
        </c:if>
    </p:panelGrid>

    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;text-align:left; " colspan="4">
                    <p:outputLabel value="填表人信息"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="填表人："/>
            </p:column>
            <p:column  style="text-align: left;width: 220px;padding-left:6px;" >
                <p:outputLabel value="#{mgrbean.disCard.fillFormPsn}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="填表人联系电话："/>
            </p:column>
            <p:column  style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fillLink}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="填表日期：" />
            </p:column>
            <p:column  style="text-align: left;width: 220px;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fillDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="填表单位：" />
            </p:column>
            <p:column  style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.fillUnitName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 15px;">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="4">
                    <p:outputLabel value="报告人信息" />
                </p:column>
            </p:row>
        </f:facet>

        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="报告人：" />
            </p:column>
            <p:column  style="text-align: left;width: 220px;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.rptPsn}" />
            </p:column>
            <p:column styleClass="column_title" style="width: 150px">
                <p:outputLabel value="报告人联系电话：" />
            </p:column>
            <p:column  style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.rptLink}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="报告日期：" />
            </p:column>
            <p:column  style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.rptDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告单位：" />
            </p:column>
            <p:column  style="text-align: left;padding-left:6px;">
                <p:outputLabel value="#{mgrbean.disCard.rptUnitName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="备注：" />
            </p:column>
            <p:column  style="text-align: left;width:240px;padding-left:6px;" colspan="3">
                <p:outputLabel value="#{mgrbean.disCard.rmk}" />
            </p:column>
        </p:row>
    </p:panelGrid>
</ui:composition>