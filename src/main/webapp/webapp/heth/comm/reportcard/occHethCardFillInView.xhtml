<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.OccHethCardFillInListBean"-->
    <style>
        .aloneTitleTable {
            color: #334B9A;
            font-weight: bold;
            padding-left: 6px;
        }

        .column_title {
            width: 15% !important;
        }

        .column_content {
            width: 35% !important;
            padding-left: 8px !important;
            word-wrap: break-word;
            word-break: break-all;
        }
    </style>
    <h:form id="viewForm" styleClass="zwpx-content">
        <link rel="stylesheet" href="/resources/css/reportCard.css"/>
        <!-- 标题 -->
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="职业卫生技术服务信息报送卡详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <!-- 按钮组 -->
        <p:outputPanel styleClass="zwx_toobar_42" id="btnPanel">
            <h:panelGrid columns="15" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel" rendered="#{mgrbean.ifSupportCancel}"  style="float:right;" oncomplete="windowScrollTop();"
                                 action="#{mgrbean.revokeAction}" process="@this,:tabView:editForm">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}"
                                 update=":tabView" process="@this"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:sticky target="btnPanel"/>
        <!-- 正文 -->
        <p:outputPanel styleClass="businessInfo" style="margin-top:40px;background: rgb(252, 253, 253);">
            <!-- 文书 -->
            <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                <p:row>
                    <p:column colspan="2"><span>本环节文书</span></p:column>
                </p:row>
                <p:row>
                    <p:column style="padding: 0;" colspan="2">
                        <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 250px;">
                        <h:outputText value="《职业卫生技术服务信息报送卡》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.occhethCard.annexPath}')"
                                             rendered="#{mgrbean.occhethCard.annexPath != null}">
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 250px;">
                        <h:outputText value="《职业卫生技术服务报告首页、签发页》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.occhethCard.signAddress}')"
                                             rendered="#{mgrbean.occhethCard.signAddress != null}">
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <div id="occHethCard">
                <!-- 标题 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                            <h:outputText value="职业卫生技术服务信息报送卡"
                                          style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: left;">
                            <h:outputText value="报告卡编码："/>
                            <p:outputLabel value="自动生成" style="color:gray;"
                                           rendered="#{mgrbean.occhethCard.rid == null}"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.cardNo}"
                                           rendered="#{mgrbean.occhethCard.rid != null}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 机构信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="orgInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="机构信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="机构名称："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgName}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="法定代表人（或主要负责人）："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgFz}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="注册地址："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgAddr}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="机构资质证书编号："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel
                                    value="#{mgrbean.occhethCard.certNo}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="项目负责人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.proFz}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="联系电话："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.proLinktel}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="资质业务范围："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:outputLabel value="#{mgrbean.occhethCard.occhethCardItemsStr}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 参与人员信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="occhethCardPsnPanel">
                    <p:row>
                        <p:column style="border-bottom-color: transparent;" colspan="2">
                            <p:outputLabel value="参与人员信息"
                                           style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="2">
                            <p:dataTable id="occhethCardPsnTable" paginatorPosition="bottom"
                                         value="#{mgrbean.occhethCard.occhethCardPsnList}"
                                         widgetVar="occhethCardPsnTable" var="occhethCardPsn"
                                         emptyMessage="没有数据！" rowIndexVar="R">
                                <!--@elvariable id="R" type="java.lang.Integer"-->
                                <p:column headerText="序号" style="width:50px;text-align: center;">
                                    <p:outputLabel value="#{R+1}"/>
                                </p:column>
                                <p:column headerText="姓名" style="width:150px;text-align: center;">
                                    <p:outputLabel value="#{occhethCardPsn.psnName}"/>
                                </p:column>
                                <p:column headerText="承担的服务事项" style="text-align: center;">
                                    <p:outputLabel value="#{occhethCardPsn.occhethCardItemStr}"/>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 服务的用人单位信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="crptInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="服务的用人单位信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="单位名称："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <div style="display: flex;align-items: center;">
                                <p:outputLabel value="#{mgrbean.occhethCard.crptName}"/>
                            </div>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="统一社会信用代码："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.creditCode}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="注册地址："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.address}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="企业规模："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.occhethCard.fkByCrptSizeId.codeName}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="联系人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.safeposition}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="联系电话："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.safephone}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" colspan="4" style="text-align: left !important;">
                            <p:dataTable id="occhethCardZoneTable" paginatorPosition="bottom"
                                         value="#{mgrbean.occhethCard.occhethCardZoneList}"
                                         widgetVar="occhethCardZoneTable" var="occhethCardZone"
                                         emptyMessage="没有数据！" rowIndexVar="R">
                                <!--@elvariable id="R" type="java.lang.Integer"-->
                                <p:column headerText="技术服务地址" style="text-align: center;">
                                    <p:outputLabel value="#{occhethCardZone.fullName}"/>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 技术服务信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicesInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="技术服务信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="技术服务领域："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:outputLabel value="#{mgrbean.occhethCard.occhethCardServiceStr}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="现场调查时间："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel
                                    value="#{mgrbean.occhethCard.investStartDate} ~ #{mgrbean.occhethCard.investEndDate}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="现场采样/检测时间："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel
                                    value="#{mgrbean.occhethCard.jcStartDate} ~ #{mgrbean.occhethCard.jcEndDate}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="出具技术服务报告时间："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.rptDate}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="技术服务报告编号："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.rptNo}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="技术服务结果" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasBadrsnJc}">
                        <p:column styleClass="column_title" style="text-align: left;" colspan="4">
                            <p:outputLabel value="" style="padding-left: 6px;"/>
                            <p:outputLabel value="职业病危害因素检测"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasBadrsnJc}">
                        <p:column styleClass="column_title" colspan="4" style="text-align: left;">
                            <div style="padding-right: 3px;">
                                <p:panelGrid style="width:100%;" id="badrsnJcDataTable">
                                    <f:facet name="header">
                                        <p:row>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:220px;">
                                                <p:outputLabel value="职业病危害因素"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:150px;">
                                                <p:outputLabel value="岗位/工种数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:220px;">
                                                <p:outputLabel value="超标岗位/工种数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:240px;">
                                                <p:outputLabel value="超标岗位/工种名称"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:160px;">
                                                <p:outputLabel value="超标检测项目"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:160px;">
                                                <p:outputLabel value="超标检测指标"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:150px;">
                                                <p:outputLabel value="超标值下限"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:150px;">
                                                <p:outputLabel value="超标值上限"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:120px;">
                                                <p:outputLabel value="计量单位"/>
                                            </p:column>
                                        </p:row>
                                    </f:facet>
                                    <c:forEach var="occhethCardJc" items="#{mgrbean.occhethCard.occhethCardJcList}"
                                               varStatus="status">
                                        <c:forEach var="occhethCardJcSub" items="#{occhethCardJc.occhethCardJcSubList}"
                                                   varStatus="status">
                                            <p:row rendered="#{mgrbean.occhethCard.occhethCardJcList.size() ne 0}">
                                                <p:column style="padding-right:3px;text-align: center;"
                                                          rowspan="#{occhethCardJcSub.badrsnRowspan}"
                                                          rendered="#{null ne occhethCardJcSub.badrsnName}">
                                                    <p:outputLabel value="#{occhethCardJcSub.badrsnName}"
                                                                   escape="false"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardJcSub.badrsnRowspan}"
                                                          rendered="#{null ne occhethCardJcSub.postNum}">
                                                    <p:outputLabel
                                                            value="#{occhethCardJcSub.postNum==0?'':occhethCardJcSub.postNum}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardJcSub.badrsnRowspan}"
                                                          rendered="#{null ne occhethCardJcSub.num}">
                                                    <p:outputLabel
                                                            value="#{occhethCardJcSub.num}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardJcSub.postRowspan}"
                                                          rendered="#{null ne occhethCardJcSub.postNameShow}">
                                                    <p:outputLabel value="#{occhethCardJcSub.postNameShow}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardJcSub.itemsRowspan}"
                                                          rendered="#{null ne occhethCardJcSub.itermName}">
                                                    <p:outputLabel value="#{occhethCardJcSub.itermName}" escape="false"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardJcSub.fkByIndexId.codeName}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardJcSub.rstLow}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardJcSub.rstUpper}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardJcSub.fkByMsruntId.codeName}"
                                                                   escape="false"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </c:forEach>
                                    <p:row rendered="#{mgrbean.occhethCard.occhethCardJcList.size() eq 0}">
                                        <p:column style="padding-right:3px;" colspan="10">
                                            <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                            </div>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasStatusPj}">
                        <p:column styleClass="column_title" style="text-align: left;" colspan="4">
                            <p:outputLabel value="" style="padding-left: 6px;"/>
                            <p:outputLabel value="职业病危害现状评价"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasStatusPj}">
                        <p:column styleClass="column_title" colspan="4" style="text-align: left;">
                            <div style="padding-right: 3px;">
                                <p:panelGrid style="width:100%;" id="badrsnPjDataTable">
                                    <f:facet name="header">
                                        <p:row>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:220px;">
                                                <p:outputLabel value="职业病危害因素"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:150px;">
                                                <p:outputLabel value="岗位/工种数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:220px;">
                                                <p:outputLabel value="超标岗位/工种数"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:240px;">
                                                <p:outputLabel value="超标岗位/工种名称"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:160px;">
                                                <p:outputLabel value="超标检测项目"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:160px;">
                                                <p:outputLabel value="超标检测指标"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:150px;">
                                                <p:outputLabel value="超标值下限"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:150px;">
                                                <p:outputLabel value="超标值上限"/>
                                            </p:column>
                                            <p:column styleClass="ui-state-default"
                                                      style="text-align:center;padding-right:3px;width:120px;">
                                                <p:outputLabel value="计量单位"/>
                                            </p:column>
                                        </p:row>
                                    </f:facet>
                                    <c:forEach var="occhethCardPj" items="#{mgrbean.occhethCard.occhethCardPjList}"
                                               varStatus="status">
                                        <c:forEach var="occhethCardPjSub" items="#{occhethCardPj.occhethCardPjSubList}"
                                                   varStatus="status">
                                            <p:row rendered="#{mgrbean.occhethCard.occhethCardPjList.size() ne 0}">
                                                <p:column style="padding-right:3px;text-align: center;"
                                                          rowspan="#{occhethCardPjSub.badrsnRowspan}"
                                                          rendered="#{null ne occhethCardPjSub.badrsnName}">
                                                    <p:outputLabel value="#{occhethCardPjSub.badrsnName}"
                                                                   escape="false"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardPjSub.badrsnRowspan}"
                                                          rendered="#{null ne occhethCardPjSub.postNum}">
                                                    <p:outputLabel
                                                            value="#{occhethCardPjSub.postNum==0?'':occhethCardPjSub.postNum}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardPjSub.badrsnRowspan}"
                                                          rendered="#{null ne occhethCardPjSub.num}">
                                                    <p:outputLabel
                                                            value="#{occhethCardPjSub.num==0?'':occhethCardPjSub.num}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardPjSub.postRowspan}"
                                                          rendered="#{null ne occhethCardPjSub.postNameShow}">
                                                    <p:outputLabel value="#{occhethCardPjSub.postNameShow}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;"
                                                          rowspan="#{occhethCardPjSub.itemsRowspan}"
                                                          rendered="#{null ne occhethCardPjSub.itermName}">
                                                    <p:outputLabel value="#{occhethCardPjSub.itermName}" escape="false"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardPjSub.fkByIndexId.codeName}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardPjSub.rstLow}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardPjSub.rstUpper}"/>
                                                </p:column>
                                                <p:column style="text-align:center;padding-right:3px;">
                                                    <p:outputLabel value="#{occhethCardPjSub.fkByMsruntId.codeName}"
                                                                   escape="false"/>
                                                </p:column>
                                            </p:row>
                                        </c:forEach>
                                    </c:forEach>
                                    <p:row rendered="#{mgrbean.occhethCard.occhethCardPjList.size() eq 0}">
                                        <p:column style="padding-right:3px;" colspan="10">
                                            <p:outputLabel value="没有数据！" style="padding: 4px 10px;"/>
                                        </p:column>
                                    </p:row>
                                </p:panelGrid>
                            </div>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasInstUsePj}">
                        <p:column styleClass="column_title" style="text-align: left;padding: 3px 6px;" colspan="4">
                            <p:outputLabel value="职业病防护设备设施与防护用品的效果评价"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasInstUsePj and mgrbean.occhethCard.hasJcInst}">
                        <p:column styleClass="column_title" colspan="4"
                                  style="text-align: left;line-height: 26px;padding: 3px 6px !important;word-break: break-all;">
                            <p:outputLabel value="" style="padding-left: 24px;"/>
                            <p:outputLabel value="开展职业病防护设备设施防护效果检测，检测设备设施数量"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.jcInstNum}"/>
                            <p:outputLabel value="台（套），"/>
                            <p:outputLabel value="检测结果不合格的设备设施数量"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.jcNotHgInstNum}"/>
                            <p:outputLabel value="台（套）"/>
                            <p:outputLabel value="，不合格的设备设施名称：#{mgrbean.occhethCard.notHgInstName}"
                                           rendered="#{mgrbean.occhethCard.notHgInstName ne null}"/>
                            <p:outputLabel value="。"/>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.occhethCard.hasInstUsePj and mgrbean.occhethCard.hasJcUse}">
                        <p:column styleClass="column_title" colspan="4"
                                  style="text-align: left;line-height: 26px;padding: 3px 6px !important;word-break: break-all;">
                            <p:outputLabel value="" style="padding-left: 24px;"/>
                            <p:outputLabel value="开展职业病防护用品防护效果检测，检测防护用品数量"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.jcUseNum}"/>
                            <p:outputLabel value="个（件），"/>
                            <p:outputLabel value="结果不合格的防护用品数量"/>
                            <p:outputLabel value="#{mgrbean.occhethCard.jcNotHgUseNum}"/>
                            <p:outputLabel value="个（件）"/>
                            <p:outputLabel value="，不合格的防护用品名称：#{mgrbean.occhethCard.notHgUseName}"
                                           rendered="#{mgrbean.occhethCard.notHgUseName ne null}"/>
                            <p:outputLabel value="。"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 报告信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;" id="rptInfoPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="报告信息" styleClass="aloneTitleTable"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="填报单位："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.fillUnitName}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="单位负责人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.orgFzMan}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="填表人："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.fillFormPsn}"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="填表人联系电话："/>
                        </p:column>
                        <p:column styleClass="column_content">
                            <p:outputLabel value="#{mgrbean.occhethCard.fillLink}"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="填表日期："/>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:outputLabel value="#{mgrbean.occhethCard.fillDate}"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </div>
        </p:outputPanel>

    </h:form>
</ui:composition>