<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui">

    <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
        <p:row>
            <p:column colspan="2"><span>本环节文书</span></p:column>
        </p:row>
        <p:row>
            <p:column style="padding: 0;" colspan="2">
                <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 190px;">
                <h:outputText value="《职业病鉴定报告卡》"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看"
                                     onclick="window.open('/webFile/#{mgrbean2.archivesCard.annexPath}')"
                                     rendered="#{mgrbean2.archivesCard.annexPath != null}">
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 标题 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
        <p:row>
            <p:column styleClass="noBorder" style="text-align: center;">
                <p:outputPanel style="padding-bottom:8px;">
                    <h:outputText value="职业病鉴定报告卡"
                                  style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--劳动者信息-->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="workerPanel">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="劳动者信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="姓名："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel style="width: 200px" value="#{mgrbean2.archivesCard.personnelName}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="证件类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByCardTypeId.codeName}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="证件号码："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.idc}" />
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="性别："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="男"  rendered="#{mgrbean2.archivesCard.sex=='1'}"/>
                <p:outputLabel value="女"  rendered="#{mgrbean2.archivesCard.sex=='2'}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="出生日期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.birthday ne null ? (mgrbean2.ifBirthEncrypt ne null and mgrbean2.ifBirthEncrypt == 1 ? '**********' : mgrbean2.archivesCard.birthday) : ''}" />
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="联系电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel style="width: 200px" value="#{mgrbean2.archivesCard.linktel}" />
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 基本信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="用人单位信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位名称："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;width:320px;" >
                 <p:outputLabel value="#{mgrbean2.archivesCard.crptName}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="社会信用代码："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.creditCode}"></p:outputLabel>
            </p:column>
        </p:row>

        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="经济类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByEconomyId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByIndusTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="企业规模："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByCrptSizeId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地区："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByZoneId.fullName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地址："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.address}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地址邮编："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.postcode}"></p:outputLabel>
            </p:column>

        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位联系人："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.safeposition}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位联系人电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.safephone}"></p:outputLabel>
            </p:column>

        </p:row>
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="用工单位信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用工单位名称："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;width: 320px;" >
                 <p:outputLabel  value="#{mgrbean2.archivesCard.empCrptName}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="社会信用代码："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.empCreditCode}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="经济类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByEmpEconomyId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业类别："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByEmpIndusTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="企业规模："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByEmpCrptSizeId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用工单位地区："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByEmpZoneId.fullName}"></p:outputLabel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 职业病鉴定信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="zybjd">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="职业病鉴定信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="是否诊断为职业病："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean2.archivesCard.ifZyb=='0'}"></p:outputLabel>
                <p:outputLabel value="是" rendered="#{mgrbean2.archivesCard.ifZyb=='1'}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row >
            <p:column styleClass="column_title">
                <p:outputLabel value="职业病名称："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width: 320px;" >
                <p:outputLabel
                             value="#{mgrbean2.archivesCard.fkByZybTypeId.codeName}"/>
                <p:outputLabel value="（" rendered="#{mgrbean2.archivesCard.zybDisName!=null}"></p:outputLabel>
                <p:outputLabel style="position: relative;width: 100px;"
                                 value="#{mgrbean2.archivesCard.zybDisName}"  rendered="#{mgrbean2.archivesCard.zybDisName!=null}"/>
                <p:outputLabel value="）" rendered="#{mgrbean2.archivesCard.zybDisName!=null}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="职业病种类："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel id="zybDisTypeId" value="#{mgrbean2.archivesCard.fkByZybDisTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>

        <!--5026的码表扩展字段4为1显示-->
        <p:row rendered="#{mgrbean2.zybjdCode.extendS4 == '1'}" >
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断I期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.diag1Date}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断Ⅱ期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.diag2Date}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean2.zybjdCode.extendS4 == '1'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断Ⅲ期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="#{mgrbean2.archivesCard.diag3Date}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean2.zybjdCode.extendS4 == '1'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="病例类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByRptTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--5026的码表扩展字段4为1显示 结束-->
        <!--5026的码表扩展字段4为2显示-->
        <p:row rendered="#{mgrbean2.zybjdCode.extendS4 == '2'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="职业性化学中毒分类："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="急性" rendered="#{mgrbean2.archivesCard.zyPoisonType=='1'}"></p:outputLabel>
                <p:outputLabel value="慢性" rendered="#{mgrbean2.archivesCard.zyPoisonType=='2'}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--5026的码表扩展字段4为2显示 结束-->
        <p:row>
            <!--大于出生日期-->
            <p:column styleClass="column_title">
                <p:outputLabel value="申请诊断日期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.applyDate}" ></p:outputLabel>
            </p:column>
            <!--大于等于申请诊断日期-->
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断日期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.diagDate}" ></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断机构名称："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;"  colspan="3">
                <p:outputLabel value="#{mgrbean2.archivesCard.diagUnitName}"/>
            </p:column>
        </p:row>
        <p:row>
            <!--大于等于诊断日期-->
            <p:column styleClass="column_title">
                <p:outputLabel value="申请鉴定日期："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.applyJdDate}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="鉴定日期："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;"  colspan="3">
                <p:outputLabel value="#{mgrbean2.archivesCard.aprsCentDate}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="鉴定类型："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" colspan="3" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByJdTypeId.codeName}"/>
            </p:column>
        </p:row>
        <p:row >
            <p:column styleClass="column_title">
                <p:outputLabel value="首次鉴定结论："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByJdRstId.codeName}"/>
            </p:column>
            <!--鉴定类型选择首次鉴定（码表5543扩展字段1为1）灰掉，不可操作，再次鉴定（码表5543扩展字段1为2）时必填-->
            <p:column styleClass="column_title">
                <p:outputLabel value="再次鉴定结论："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByJdAgainRstId.codeName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="鉴定机构名称："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.jdUnitName}" ></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="鉴定机构社会信用代码："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.jdUnitCreditCode}" ></p:outputLabel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 职业病鉴定结果 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="zybjg" rendered="#{mgrbean.ifAgainRst}">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="职业病鉴定结果"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="是否鉴定为职业病："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean2.archivesCard.ifJdZyb=='0'}"></p:outputLabel>
                <p:outputLabel value="是" rendered="#{mgrbean2.archivesCard.ifJdZyb=='1'}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row >
            <p:column styleClass="column_title">
                <p:outputLabel value="职业病名称："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width: 320px;" >
                    <p:outputLabel
                                 value="#{mgrbean2.archivesCard.fkByJdZybTypeId.codeName}"/>
                    <p:outputLabel value="（" rendered="#{mgrbean2.archivesCard.jdZybDisName!=null}"></p:outputLabel>
                    <p:outputLabel style="position: relative;width: 100px;"
                                 value="#{mgrbean2.archivesCard.jdZybDisName}"  rendered="#{mgrbean2.archivesCard.jdZybDisName!=null}" />
                    <p:outputLabel value="）" rendered="#{mgrbean2.archivesCard.jdZybDisName!=null}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="职业病种类："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByJdZybDisTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--5026的码表扩展字段4为1显示-->
        <p:row rendered="#{mgrbean2.zybjgCode.extendS4 == '1'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断I期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.jdDiag1Date}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断Ⅱ期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean2.archivesCard.jdDiag2Date}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean2.zybjgCode.extendS4 == '1'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="诊断Ⅲ期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="#{mgrbean2.archivesCard.jdDiag3Date}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean2.zybjgCode.extendS4 == '1'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="病例类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="#{mgrbean2.archivesCard.fkByJdRptTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--5026的码表扩展字段4为1显示 结束-->
        <!--5026的码表扩展字段4为2显示-->
        <p:row rendered="#{mgrbean2.zybjgCode.extendS4 == '2'}">
            <p:column styleClass="column_title">
                <p:outputLabel value="职业性化学中毒分类："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="3">
                <p:outputLabel value="急性"  rendered="#{mgrbean2.archivesCard.jdZyPoisonType=='1'}"></p:outputLabel>
                <p:outputLabel value="慢性"  rendered="#{mgrbean2.archivesCard.jdZyPoisonType=='2'}"></p:outputLabel>
            </p:column>
        </p:row>
        <!--5026的码表扩展字段4为2显示 结束-->
    </p:panelGrid>
    <!-- 填表人信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="填表人信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表人："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;width: 320px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fillFormPsn}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表人联系电话："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fillLink}" ></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表日期："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.fillDate}" ></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表单位："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel  value="#{mgrbean2.archivesCard.fillUnitName}"></p:outputLabel>
            </p:column>
        </p:row>

    </p:panelGrid>
    <!-- 报告人信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 15px;">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="报告人信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告人："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;width: 320px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.rptPsn}" ></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告人联系电话："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel value="#{mgrbean2.archivesCard.rptLink}" ></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告日期："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:column styleClass="noBorder" style="text-align: left;" colspan="3">
                    <p:outputLabel value="#{mgrbean2.archivesCard.rptDate}" ></p:outputLabel>
                </p:column>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告单位："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" >
                <p:outputLabel  value="#{mgrbean2.archivesCard.fillUnitName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="备注："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" colspan="3">
                <p:outputLabel style="resize: none;width: 600px;"  value="#{mgrbean2.archivesCard.rmk}"></p:outputLabel>
            </p:column>
        </p:row>

    </p:panelGrid>
</ui:composition>