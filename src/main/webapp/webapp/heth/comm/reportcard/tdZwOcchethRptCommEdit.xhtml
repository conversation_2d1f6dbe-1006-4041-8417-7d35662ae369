<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <ui:define name="insertEditScripts">
        <style type="text/css">
            .myCalendar input {
                width: 110px;
            }
        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="技术服务申报" />
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" id="saveBtn" action="#{mgrbean.saveTmpAction}" update=":tabView:mainForm,editField1,editField2"
                                 process="@this,editField1,editField2" />
                <p:spacer width="5px" />
                <p:commandButton value="提交" icon="ui-icon-check" id="submitBtn" action="#{mgrbean.submitAction}"  update=":tabView" process="@this,editField1,editField2">
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:spacer width="5px" />
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}" update=":tabView" process="@this">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <!-- 用人单位信息区 -->
        <p:fieldset legend="用人单位信息"  style="margin-top: 5px;margin-bottom: 5px;" id="editField1">
            <p:panelGrid style="width:100%;height:100%;">
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red">*</font>
                        <p:outputLabel value="单位名称：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;width:360px;">
                        <p:inputText id="crptName" readonly="true" style="width:280px;"
                                     value="#{mgrbean.tdZwOcchethRpt != null ? mgrbean.tdZwOcchethRpt.crptName : ''}"/>
                        <p:spacer width="5" />
                        <p:commandLink value="选择" process="@this"
                                       action="#{mgrbean.selectCrptList}">
                            <p:ajax event="dialogReturn" process="@this" resetValues="true"
                                    listener="#{mgrbean.onUnitSelect}" update="crptName,editField1"/>
                        </p:commandLink>
                    </p:column>
                    <p:column style="text-align: right;width:260px;padding-right: 5px;">
                        <p:outputLabel value="所属区域：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tmpArea != null ? mgrbean.tmpArea : ''}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="社会信用代码：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt != null ? mgrbean.tdZwOcchethRpt.creditCode : ''}" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;">
                        <p:outputLabel value="单位地址：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt != null ? mgrbean.tdZwOcchethRpt.address : ''}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="行业类别：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt.fkByIndusTypeId != null ? mgrbean.tdZwOcchethRpt.fkByIndusTypeId.codeName : ''}" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;">
                        <p:outputLabel value="经济性质：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt.fkByEconomyId != null ? mgrbean.tdZwOcchethRpt.fkByEconomyId.codeName : ''}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-right: 5px;padding-top:0px;padding-bottom: 0px; ">
                        <p:outputLabel value="企业规模：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt.fkByCrptSizeId != null ? mgrbean.tdZwOcchethRpt.fkByCrptSizeId.codeName : ''}" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;">
                        <p:outputLabel value="联系人：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt.linkMan}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="联系电话：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" colspan="3">
                        <p:outputLabel value="#{mgrbean.tdZwOcchethRpt.linkPhone}" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>

        <p:fieldset legend="技术服务申报"  style="margin-top: 5px;margin-bottom: 5px;" id="editField2">
            <p:panelGrid style="width:100%;height:100%;" id="orgRptPanelId">
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <p:outputLabel value="质控编号：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;"  colspan="#{mgrbean.ifExtent4Eq2==1?1:3}">
                        <p:outputLabel rendered="#{mgrbean.tdZwOcchethRpt == null or null == mgrbean.tdZwOcchethRpt.manageNo}" >
                            <font color="gray">自动生成</font>
                        </p:outputLabel>
                        <p:outputLabel rendered="#{mgrbean.tdZwOcchethRpt != null and null != mgrbean.tdZwOcchethRpt.manageNo}" value="#{mgrbean.tdZwOcchethRpt.manageNo}" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;" rendered="#{mgrbean.ifExtent4Eq2==1}">
                        <font color="red">*</font>
                        <p:outputLabel value="委托协议附件：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" rendered="#{mgrbean.ifExtent4Eq2==1}">
                        <h:panelGrid  columns="10" style="border-color: transparent;"  id="entrustBtnUpload">
                            <p:commandButton value="上传"
                                             rendered="#{mgrbean.tdZwOcchethRpt ==null or mgrbean.tdZwOcchethRpt.entrustFilePath == null or mgrbean.tdZwOcchethRpt.entrustFilePath == ''}"
                                             process="@this" onclick="PF('FileDialog').show();" >
                                <f:setPropertyActionListener value="1" target="#{mgrbean.flag}"></f:setPropertyActionListener>
                            </p:commandButton>

                            <p:commandButton value="查看" process="@this" rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.entrustFilePath != null and mgrbean.tdZwOcchethRpt.entrustFilePath != ''}"
                                             onclick="window.open('/webFile/#{mgrbean.tdZwOcchethRpt.entrustFilePath}')" />
                            <p:spacer width="3"
                                      rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.entrustFilePath != null and mgrbean.tdZwOcchethRpt.entrustFilePath != ''}" />
                            <p:commandButton value="删除"
                                             rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.entrustFilePath != null and mgrbean.tdZwOcchethRpt.entrustFilePath != ''}"
                                             update=":tabView:editForm:editField2"
                                             process="@this" action="#{mgrbean.delFilePath}">
                                <f:setPropertyActionListener value="1" target="#{mgrbean.flag}"></f:setPropertyActionListener>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                            </p:commandButton>
                        </h:panelGrid>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red">*</font>
                        <p:outputLabel value="技术服务类别：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left:10px;">
                        <p:selectOneMenu value="#{mgrbean.sortId}" style="width: 260px;" id="totalVerId" >
                            <f:selectItem itemValue="" itemLabel="--请选择--"  />
                            <f:selectItems value="#{mgrbean.jsfwlbList}" />
                            <p:ajax event="change" update="orgRptPanelId" process="@this,orgRptPanelId" listener="#{mgrbean.resetTypeList}" />
                        </p:selectOneMenu>
                    </p:column>
                    <p:column style="text-align: right;width:260px;padding-right: 5px;">
                        <font color="red">*</font>
                        <p:outputLabel value="报告日期：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                    showOtherMonths="true" id="rptDate" size="11" navigator="true"
                                    yearRange="c-10:c+10" converterMessage="检测（评价）日期，格式输入不正确！"
                                    showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar2"
                                    value="#{mgrbean.tdZwOcchethRpt.rptDate}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red" style="display: #{null ne mgrbean.tmpExtend2TypeList and mgrbean.tmpExtend2TypeList.contains(mgrbean.sortId) ? 'inline' : 'none'}" >*</font>
                        <p:outputLabel value="项目名称：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" colspan="3">
                        <p:inputText value="#{mgrbean.tdZwOcchethRpt.projectName}" style="width: 823px;"
                                     maxlength="200"  />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;height:45px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red">*</font>
                        <p:outputLabel value="报告编号：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;">
                        <p:inputText id="rptNo"
                                     value="#{mgrbean.tdZwOcchethRpt.rptNo}" size="30"
                                     maxlength="25"  />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;" rendered="#{mgrbean.ifExtent4Eq2==1}">
                        <font color="red">*</font>
                        <p:outputLabel value="总结报告附件：" />
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;" rendered="#{mgrbean.ifExtent4Eq2==0}">
                        <p:outputLabel value="附件：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" >
                       <h:panelGrid  columns="10" style="border-color: transparent;"  id="btnUpload">
                           <p:commandButton value="上传"
                                            rendered="#{mgrbean.tdZwOcchethRpt ==null or mgrbean.tdZwOcchethRpt.filePath == null or mgrbean.tdZwOcchethRpt.filePath == ''}"
                                            process="@this" onclick="PF('FileDialog').show();" >
                               <f:setPropertyActionListener value="2" target="#{mgrbean.flag}"></f:setPropertyActionListener>
                           </p:commandButton>

                           <p:commandButton value="查看" process="@this" rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.filePath != null and mgrbean.tdZwOcchethRpt.filePath != ''}"
                                            onclick="window.open('/webFile/#{mgrbean.tdZwOcchethRpt.filePath}')" />
                           <p:spacer width="3"
                                     rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.filePath != null and mgrbean.tdZwOcchethRpt.filePath != ''}" />
                           <p:commandButton value="删除"
                                            rendered="#{mgrbean.tdZwOcchethRpt != null and mgrbean.tdZwOcchethRpt.filePath != null and mgrbean.tdZwOcchethRpt.filePath != ''}"
                                            update=":tabView:editForm:btnUpload"
                                            process="@this" action="#{mgrbean.delFilePath}">
                               <f:setPropertyActionListener value="2" target="#{mgrbean.flag}"></f:setPropertyActionListener>
                               <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                           </p:commandButton>
                       </h:panelGrid>
                    </p:column>
                </p:row>

                <p:row rendered="#{null ne mgrbean.tmpExtend1TypeList and mgrbean.tmpExtend1TypeList.contains(mgrbean.sortId)}">
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red">*</font>
                        <p:outputLabel value="体检开展方式：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left:10px;" colspan="3">
                        <p:selectManyCheckbox id="tjExeType" value="#{mgrbean.tjExeTypeList}">
                            <f:selectItem itemLabel="院内体检" itemValue="1" />
                            <f:selectItem itemLabel="外出体检" itemValue="2" />
                        </p:selectManyCheckbox>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifExtent4Eq2==1}">
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red">*</font>
                        <p:outputLabel value="体检日期：" />
                    </p:column>
                    <p:column style="text-align: left;width:360px;padding-left:10px;" >
                        <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.tdZwOcchethRpt.bhkBeginDate}"
                                                      endDate="#{mgrbean.tdZwOcchethRpt.bhkEndDate}"
                                                      styleClass="myCalendar"/>
                    </p:column>
                    <p:column style="text-align: right;padding-right: 5px;">
                        <font color="red">*</font>
                        <p:outputLabel value="体检人数：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" >
                        <p:inputText onblur="SYSTEM.clearNoNumBig0(this)" onkeyup="SYSTEM.clearNoNumBig0(this)"
                                     value="#{mgrbean.tdZwOcchethRpt.bhkPsn}" style="width: 180px;"
                                     maxlength="8"  />
                    </p:column>
                </p:row>

                <p:row rendered="#{null ne mgrbean.zybRiskExtend1TypeList and mgrbean.zybRiskExtend1TypeList.contains(mgrbean.sortId)}">
                    <p:column style="text-align: right;height:45px;width:260px;padding-right: 5px;padding-top:0px;padding-bottom: 0px;">
                        <font color="red">*</font>
                        <p:outputLabel value="职业病危害风险分类：" />
                    </p:column>
                    <p:column style="text-align: left;padding-left:10px;" colspan="3">
                        <p:selectOneRadio value="#{mgrbean.tdZwOcchethRpt.zybRiskId}" style="width:240px;" >
                            <f:selectItems value="#{mgrbean.zybRiskList}" var="zybRisk" itemValue="#{zybRisk.rid}" itemLabel="#{zybRisk.codeName}"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog"
                      resizable="false" modal="true">
                <table>
                    <tr>
                        <td style="text-align: right;"><p:outputLabel
                                value="（支持附件格式为：图片、PDF）"
                                styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 30px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                        </td>
                    </tr>
                    <tr>
                        <td style="position: relative;top: -23px;">
                            <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}"
                                invalidSizeMessage="文件大小不能超过100M!" id="upload"
                                validatorMessage="上传出错啦，请重新上传！" style="width:600px;"
                                previewWidth="120" cancelLabel="取消" update="upload"
                                uploadLabel="上传" dragDropSupport="true" mode="advanced"
                                sizeLimit="104857600" fileLimit="1"
                                fileLimitMessage="最多只能上传1个文件！"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                        /></td>
                    </tr>
                </table>
            </p:dialog>
        </p:fieldset>
    </ui:define>

</ui:composition>