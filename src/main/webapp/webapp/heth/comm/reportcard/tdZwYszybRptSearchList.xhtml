<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwYszybRptListSearchCommBean}"/>


    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwYszybRptSearchView.xhtml"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            } 
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="疑似职业病报告卡查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdZwYszybRptListSearchCommBean.searchAction}" update="dataTable"
                                 process="@this,searchZone,searchCrptName,searchPsnName,searchIdc,searchYszybName,searchFindBdate,searchFindEdate,searchState" />
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputLabel value="提示：" style="color:red;"/>
                <h:outputLabel value="只查询#{tdZwYszybRptListSearchCommBean.receiveDate==1?'报告出具日期':'体检日期'}（#{tdZwYszybRptListSearchCommBean.bhkBeginDate}）之后的数据" style="color:blue;"></h:outputLabel>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:45px;">
                <h:outputText value="用工单位地区：" />
            </p:column>
            <p:column style="text-align:left;width: 300px;">
                <h:panelGrid columns="3" id="searchZone" style="border-color: transparent;margin: -6px;padding: 0px;">
                    <zwx:ZoneSingleComp zoneList="#{tdZwYszybRptListSearchCommBean.zoneList}"
                                        zoneCode="#{tdZwYszybRptListSearchCommBean.searchZoneCode}"
                                        zoneName="#{tdZwYszybRptListSearchCommBean.searchZoneName}"/>

                </h:panelGrid>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchCrptName" value="#{tdZwYszybRptListSearchCommBean.searchCrptName}" style="width: 180px;" maxlength="50"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:160px;height: 33px;">
                <h:outputLabel value="人员姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchPsnName" value="#{tdZwYszybRptListSearchCommBean.searchPsnName}" style="width: 180px;" maxlength="25"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 45px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 300px;">
                <p:inputText id="searchIdc" value="#{tdZwYszybRptListSearchCommBean.searchIdc}" maxlength="18" style="width: 180px;" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <p:outputLabel value="疑似职业病名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 300px;">
                <p:inputText id="searchYszybName" value="#{tdZwYszybRptListSearchCommBean.searchYszybName}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 160px;height: 33px;">
                <p:inputText style="visibility:hidden;height: 0;width: 0;" />
                <h:outputText value="#{tdZwYszybRptListSearchCommBean.receiveDate==1?'接收日期':'体检日期'}：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" colspan="3">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="searchFindBdate" size="9" navigator="true"
                            yearRange="c-10:c+10" converterMessage="查询开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{tdZwYszybRptListSearchCommBean.searchFindBdate}" />
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="searchFindEdate" size="9" navigator="true"
                            yearRange="c-10:c+10" converterMessage="查询结束日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{tdZwYszybRptListSearchCommBean.searchFindEdate}" />
            </p:column>
        </p:row>


        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 160px;height: 45px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="5">
                <p:selectManyCheckbox id="searchState" value="#{tdZwYszybRptListSearchCommBean.states}">
                    <f:selectItems value="#{tdZwYszybRptListSearchCommBean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="用工单位地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="用工单位名称" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="人员姓名" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="证件号码" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[6]}" />
        </p:column>
        <p:column headerText="疑似职业病名称" style="width: 260px;padding-left: 8px;text-align: center">
            <h:outputText value="#{itm[7]}" />
        </p:column>
        <p:column headerText="#{tdZwYszybRptListSearchCommBean.receiveDate==1?'接收日期':'体检日期'}" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            </h:outputText>
        </p:column>


        <p:column headerText="状态" style="width:80px;text-align: center;">
            <h:outputLabel value="待填报" rendered="#{itm[10]==0.5}"/>
            <h:outputLabel value="待提交" rendered="#{itm[10]==0}" style="color:red"/>
            <h:outputLabel value="待初审" rendered="#{itm[10]==1}" />
            <h:outputLabel value="已退回" rendered="#{itm[10]==2 or itm[10]==4 or itm[10]==6}" style="color:red"/>
            <h:outputLabel value="待复审" rendered="#{itm[10]==3}" />
            <h:outputLabel value="待终审" rendered="#{itm[10]==5}" />
            <h:outputLabel value="终审通过" rendered="#{itm[10]==7}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="详情" update=":tabView" process="@this" action="#{tdZwYszybRptListSearchCommBean.viewInitAction}">
                <f:setPropertyActionListener value="#{itm[1]}" target="#{tdZwYszybRptListSearchCommBean.relBhkId}"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{tdZwYszybRptListSearchCommBean.rid}"/>
                <f:setPropertyActionListener value="#{itm[13]}" target="#{tdZwYszybRptListSearchCommBean.occdiseId}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>