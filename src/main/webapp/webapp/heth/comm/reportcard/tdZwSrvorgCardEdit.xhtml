<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput(ifView,id){
                if(ifView=="false"){
                    return;
                }
                var text;
                var $tabView ;
                if(id){
                    $tabView = $("#"+id)
                }else{
                    $tabView = $("#tabView\\:editForm");
                }
                $tabView.find("input,textarea").each(function(){
                    if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
                        $(this).css("pointer-events","none");
                    }else{
                        $(this).prop("disabled",true);
                    }
                    $(this).css("opacity","1");
                });
                //单选框label的for标签处理
                $tabView.find("label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
                        $(this).remove();
                    }else if("查看"==text||"修改"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
                    // 避免已经disable的单选组件 又显示出来
                    if (!$(this).hasClass("ui-state-disabled")) {
                        $(this).addClass("ui-state-disabled");
                        $(this).css("opacity","1");
                        $(this).css("pointer-events","none");
                    }
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("扫描"==text||"预览"==text||"制作"==text||"删除"==text||"上传"==text||"添加"==text||"设计"==text||"汇总"==text){
                        $(this).remove();
                    }else if("查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            function openPDFClick(){
                document.getElementById("tabView:editForm:openPDF").click();
            }
            //]]>
        </script>
        <style>
            .myDate input{
                width: 184px;
            }
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }
            .shadeTip>div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }

            table.ui-selectmanycheckbox tr {
                display: flex;
                align-items: center;
            }

            table.ui-selectmanycheckbox td {
                display: flex;
                align-items: center;
            }

        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
    	<p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="放射卫生技术服务信息报送卡填报"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="9" style="border-color:transparent;padding:0px;" id="headButton">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm"
                                 update=":tabView:editForm:tdZwSrvorgCardCon">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm">
                </p:commandButton>

                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"   oncomplete="PF('FileDialog').hide();"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check"
                             update=":tabView" oncomplete="PF('ConfirmDialog').hide();windowScrollTop();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:sticky target="sticky"></p:sticky>
    </ui:define>
    <ui:define name="insertOtherContents" >
        <p:outputPanel styleClass="businessInfo" id="tdZwSrvorgCardCon">
            <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                <p:row>
                    <p:column colspan="2" ><span>本环节文书</span></p:column>
                </p:row>
                <p:row>
                    <p:column style="padding: 0;" colspan="2">
                        <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 260px;">
                        <h:outputText value="*" style="color:red;"/>
                        <h:outputText value="《放射卫生技术服务信息报送卡》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="预览" onclick="PF('ShadeTip').show();" oncomplete="PF('ShadeTip').hide();"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.buildWritReport}"
                                             rendered="#{mgrbean.srvorgCard.annexPath==null}"
                                             update=":tabView:editForm:tdZwSrvorgCardCon">
                            </p:commandButton>
                            <p:spacer width="5" rendered="#{mgrbean.srvorgCard.annexPath == null}"/>
                            <p:commandButton value="上传"
                                             process="@this,:tabView:editForm" update="fileDialog"
                                             action="#{mgrbean.beforeUpload}"
                                             rendered="#{mgrbean.srvorgCard.annexPath == null}">
                                <f:setPropertyActionListener value="1" target="#{mgrbean.fileType}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.srvorgCard.annexPath}')"
                                             rendered="#{mgrbean.srvorgCard.annexPath != null}">
                            </p:commandButton>
                            <p:spacer width="5" rendered="#{mgrbean.srvorgCard.annexPath != null}"/>
                            <p:commandButton value="删除"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.delMadedwrit}"
                                             rendered="#{mgrbean.srvorgCard.annexPath != null}"
                                             update=":tabView:editForm:tdZwSrvorgCardCon">
                                <f:setPropertyActionListener value="1" target="#{mgrbean.fileType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                            </p:commandButton>
                            <p:commandButton  style="display: none;" id="openPDF"
                                              icon="ui-icon-print"
                                              onclick="window.open('/webFile/#{mgrbean.reportFilePath}')" process="@this"  />
                        </p:outputPanel>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 260px;">
                        <h:outputText value="*" style="color:red;"/>
                        <h:outputText value="《放射卫生技术服务报告首页、签发页》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="上传"
                                             process="@this,:tabView:editForm" update="fileDialog"
                                             action="#{mgrbean.signAddressUpload}"
                                             rendered="#{mgrbean.srvorgCard.signAddress == null}">
                                <f:setPropertyActionListener value="2" target="#{mgrbean.fileType}"/>
                            </p:commandButton>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.srvorgCard.signAddress}')"
                                             rendered="#{mgrbean.srvorgCard.signAddress != null}">
                            </p:commandButton>
                            <p:spacer width="5" rendered="#{mgrbean.srvorgCard.signAddress != null}"/>
                            <p:commandButton value="删除"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.delMadedwrit}"
                                             rendered="#{mgrbean.srvorgCard.signAddress != null}"
                                             update=":tabView:editForm:writeSortPanel">
                                <f:setPropertyActionListener value="2" target="#{mgrbean.fileType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <div id="srvorgCardDiv">
                <!-- 标题 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-top: 15px;" id="writeSortInfo">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;">
                                <h:outputText value="放射卫生技术服务信息报送卡"
                                              style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: left;">
                            <h:outputText value="报告卡编码："/>
                            <p:outputLabel value="自动生成" style="color:gray;"
                                           rendered="#{mgrbean.srvorgCard.rid == null}" />
                            <p:outputLabel value="#{mgrbean.srvorgCard.cardNo}"
                                           rendered="#{mgrbean.srvorgCard.rid != null}" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 机构信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="机构信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="机构名称："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:outputLabel value="${mgrbean.srvorgCard.orgName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="法定代表人（或主要负责人）："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputLabel value="#{mgrbean.srvorgCard.orgFz}"></h:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="注册地址："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:outputLabel value="#{mgrbean.srvorgCard.orgAddr}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="机构资质证书编号："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.srvorgCard.certNo}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="项目负责人："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.srvorgCard.proFz}" maxlength="50" ></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="联系电话："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:inputText value="#{mgrbean.srvorgCard.proLinktel}" maxlength="20"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="资质业务范围："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;line-height: 26px;" colspan="3">
                            <h:outputText value="#{mgrbean.srvorgCard.itemsName}"></h:outputText>
                        </p:column>

                    </p:row>
                </p:panelGrid>
                <!-- 参与人员信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" >
                    <p:row>
                        <p:column style="border-bottom-color: transparent;" colspan="2">
                            <p:outputLabel value="参与人员信息"
                                           style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"></p:outputLabel>
                            <p:commandButton value="添加" icon="ui-icon-plus" action="#{mgrbean.addsrvorgCardPsn}"
                                           process="@this,:tabView:editForm:srvorgCardPsnList" >
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSrvorgCardPsn}" process="@this"
                                        resetValues="true" update=":tabView:editForm:srvorgCardPsnList" />
                            </p:commandButton>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="2" >
                            <p:dataTable id="srvorgCardPsnList" paginatorPosition="bottom"
                                         value="#{mgrbean.srvorgCard.srvorgCardPsnList}"
                                         widgetVar="cardRsnTable" var="srvorgCardPsn"
                                         emptyMessage="没有数据！" rowIndexVar="R">
                                <p:column headerText="序号" style="width:40px;text-align: center;">
                                    <p:outputLabel value="#{R+1}"></p:outputLabel>
                                </p:column>
                                <p:column headerText="姓名" style="width:100px;text-align:center;">
                                    <p:outputLabel value="#{srvorgCardPsn.psnName}"></p:outputLabel>
                                </p:column>
                                <p:column headerText="#{mgrbean.redStar}承担的服务事项" style="text-align:center;width: 600px;" >
                                    <p:selectManyCheckbox columns="8" layout="grid" value="#{srvorgCardPsn.srvorgCardItemIds}" style="width: 460px;" >
                                        <f:selectItems value="#{mgrbean.getTsSimpleCode('5577')}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}" />
                                    </p:selectManyCheckbox>
                                </p:column>
                                <p:column headerText="操作" >
                                    <p:commandLink value="删除"
                                                   update=":tabView:editForm:srvorgCardPsnList"
                                                   process="@this,:tabView:editForm:srvorgCardPsnList"
                                                   action="#{mgrbean.delSrvorgCardPsnAction(srvorgCardPsn)}">
                                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                    </p:commandLink>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 服务的用人单位信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="unitPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="服务的用人单位信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="单位名称："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <table>
                                <tr>
                                    <td style="padding:0;border-color: transparent;">
                                        <p:inputText id="unitName"  readonly="true" style="width: 200px" value="#{mgrbean.srvorgCard.unitName}" onclick="$('#tabView\\:editForm\\:onUnitSelect').click()"/>
                                    </td>
                                    <td style="padding:0;border-color: transparent;position: relative;left: -18px;">
                                        <p:commandLink  id="onUnitSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                                        action="#{mgrbean.selectUnitList}" process="@this,unitPanel" style="position: relative;left: -10px;"
                                        >
                                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onCrptSelect}"
                                                    update="unitPanel"/>
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="统一社会信用代码："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.srvorgCard.creditCode}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="注册地址："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:outputLabel value="#{mgrbean.srvorgCard.address}" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="联系人："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 10px;" >
                            <p:inputText id="linkMan" value="#{mgrbean.srvorgCard.linkMan}"  style="width: 200px"  maxlength="50" ></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="联系电话："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:inputText value="#{mgrbean.srvorgCard.linkPhone}" maxlength="20"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="服务单位类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:selectOneRadio columns="10" layout="grid" value="#{mgrbean.srvorgCard.fkBySutId.rid}"  >
                                <f:selectItems value="#{mgrbean.getTsSimpleCode('5578')}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                    <p:column style="border-bottom-color: transparent;border-right-color: transparent;" colspan="2">
                        <p:commandButton value="添加" icon="ui-icon-plus" action="#{mgrbean.addSrvorgCardZone}"
                                         process="@this," update="srvorgCardZoneList">
                        </p:commandButton>
                    </p:column>
                    <p:column style="border-bottom-color: transparent;border-left-color: transparent;text-align: right" colspan="2">
                        <p:outputLabel value="提示："
                                       style="color: #fc2c2c;line-height: 12px;"></p:outputLabel>
                        <p:outputLabel value="技术服务机构地址与注册地址不一致的，请详细填写服务地址。"
                                       style="color: #3421fc;line-height: 12px;"></p:outputLabel>
                    </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="4" >
                            <p:panelGrid style="width:100%;height:100%;" id="srvorgCardZoneList">
                                <p:row>
                                    <p:column  styleClass="ui-state-default" style="width: 215px;text-align: center;height: 19px;" >
                                        <h:outputText value="技术服务地址"/>
                                    </p:column>
                                    <p:column  styleClass="ui-state-default" style="width: 400px;text-align: center;height: 19px;" >
                                        <h:outputText value="详细地址"/>
                                    </p:column>
                                    <p:column  styleClass="ui-state-default" style="text-align: center;height: 19px;" >
                                        <h:outputText value="操作"/>
                                    </p:column>
                                </p:row>
                                <c:if test="#{mgrbean.srvorgCard.srvorgCardZoneList != null and mgrbean.srvorgCard.srvorgCardZoneList.size() >0}">
                                <c:forEach var="srvorgCardZone" items="#{mgrbean.srvorgCard.srvorgCardZoneList}" varStatus="R">
                                    <p:row>
                                        <p:column style="text-align: left;" >
                                                <zwx:ZoneSingleNewComp  zoneList="#{mgrbean.jsZoneList}"
                                                                        zoneCode="#{srvorgCardZone.zoneGb}"
                                                                        zoneName="#{srvorgCardZone.zoneName}"
                                                                        onchange="onSearchNodeSelect#{R.index}()"
                                                                        choseZoneTypeMin="4" choseZoneTypeMax="4" />
                                                <p:remoteCommand name="onSearchNodeSelect#{R.index}" action="#{mgrbean.searchSrvorgCardZone(srvorgCardZone)}"
                                                                 process="@this,srvorgCardZoneList" update="srvorgCardZoneList"/>
                                        </p:column>
                                        <p:column style="text-align: center;" >
                                            <p:outputLabel value="#{srvorgCardZone.fullName}"></p:outputLabel>
                                        </p:column>
                                        <p:column style="text-align: left;padding-left: 12px;" >
                                            <p:commandLink value="删除"
                                                           update=":tabView:editForm:srvorgCardZoneList"
                                                           process="@this,:tabView:editForm:srvorgCardZoneList"
                                                           action="#{mgrbean.delSrvorgCardZoneAction(srvorgCardZone)}">
                                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                            </p:commandLink>
                                        </p:column>
                                    </p:row>
                                </c:forEach>
                                </c:if>
                                <p:row rendered="#{mgrbean.srvorgCard.srvorgCardZoneList == null or mgrbean.srvorgCard.srvorgCardZoneList.size()==0}">
                                    <p:column style="text-align: left;padding: 4px 10px;height: 33px;" colspan="3">
                                        <h:outputText value="没有数据！"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>

                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 技术服务信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="servicePanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="技术服务信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="技术服务领域："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" colspan="3" >
                            <c:forEach items="#{mgrbean.showServiceList}" var="itm" varStatus="varStatus"  >
                                <p:outputLabel value="#{itm.preTip}" rendered="#{null ne itm.preTip}" />
                                <p:spacer width="5" height="0" rendered="#{null ne itm.preTip}" />
                                <p:selectBooleanCheckbox value="#{itm.ifSelected}" disabled="#{itm.ifDisabled}">
                                    <p:ajax event="change" listener="#{mgrbean.changeSelectServicesAction}" process="@this,servicePanel"
                                            update="servicePanel"/>
                                </p:selectBooleanCheckbox>
                                <p:spacer width="5" height="0"/>
                                <p:outputLabel value="#{itm.simpleCode.codeName}" />
                                <p:spacer width="5" height="0" rendered="#{null ne itm.afterTip}" />
                                <p:outputLabel value="#{itm.afterTip}" rendered="#{null ne itm.afterTip}" />
                                <p:spacer width="5" height="0"/>
                                <!-- 四个换行 -->
                                <h:outputText value="#{mgrbean.brTagStr}" escape="false" rendered="#{varStatus.index ne 0 and varStatus.index%4==3}"/>
                            </c:forEach>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifDate}"/>
                            <p:outputLabel value="现场采样时间："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.srvorgCard.investStartDate}"
                                                          endDate="#{mgrbean.srvorgCard.investEndDate}" styleClass="myCalendar1"></zwx:CalendarDynamicLimitComp>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="现场检测时间："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.srvorgCard.jcStartDate}"
                                                          endDate="#{mgrbean.srvorgCard.jcEndDate}" styleClass="myCalendar1"></zwx:CalendarDynamicLimitComp>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="出具技术服务报告时间："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true"  size="11" navigator="true"
                                        yearRange="c-10:c+10" converterMessage="出具技术服务报告时间格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" styleClass="myDate"
                                        value="#{mgrbean.srvorgCard.rptDate}" />
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="技术服务报告编号："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:inputText value="#{mgrbean.srvorgCard.rptNo}" maxlength="50" style="width: 184px"></p:inputText>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 技术服务结果 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="resultPanel">
                    <p:row>
                        <p:column colspan="2">
                            <p:outputLabel value="技术服务结果"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <!--放射卫生防护检测-->
                    <p:row>
                        <p:column colspan="2">
                            <p:selectBooleanCheckbox value="#{mgrbean.ifFswsFhJc}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="放射卫生防护检测"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <!--是否开展放射诊疗工作场所放射防护检测-->
                    <p:row rendered="#{mgrbean.srvorgCard.ifFswsFhJc == 1}">
                        <p:column colspan="2">
                            <p:spacer width="20" height="0"/>
                            <p:selectBooleanCheckbox value="#{mgrbean.ifFhJc}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="开展放射诊疗工作场所放射防护检测，共检测点位 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.fhJcPoint}" maxlength="6" onkeyup="SYSTEM.clearNoNumBig0(this)"
                                         onblur="SYSTEM.clearNoNumBig0(this)" disabled="#{mgrbean.srvorgCard.ifFhJc != 1}" style="width: 80px;"></p:inputText>
                            <p:outputLabel value=" 个"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifFswsFhJc == 1}">
                        <p:column  colspan="2">
                            <p:panelGrid >
                                <p:row style="border-color: transparent;">
                                    <p:column style="border-color: transparent;">
                                        <p:spacer width="45" height="0"/>
                                        <p:outputLabel value="其中，超标点位 "></p:outputLabel>
                                        <p:inputText value="#{mgrbean.srvorgCard.notHgFhJcPoint}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" style="width: 80px;"
                                                     onblur="checkNotHgFhJcPoint()"  disabled="#{mgrbean.srvorgCard.ifFhJc != 1}" id="notHgFhJcPoint"
                                        ></p:inputText>
                                        <p:remoteCommand name="checkNotHgFhJcPoint"  process="@this,notHgFhJcPoint" update="srvorgCardFhjcs"/>
                                        <p:outputLabel value=" 个，超标点位放射性危害类型："></p:outputLabel>
                                    </p:column>
                                    <p:column style="border-color: transparent;">
                                        <p:selectManyCheckbox columns="4" layout="grid" disabled="#{mgrbean.srvorgCard.ifFhJc != 1 or mgrbean.srvorgCard.notHgFhJcPoint == null or mgrbean.srvorgCard.notHgFhJcPoint == 0}" value="#{mgrbean.srvorgCard.srvorgCardFhjcs}" id="srvorgCardFhjcs">
                                            <f:selectItems value="#{mgrbean.getTsSimpleCode('5579')}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}" />
                                        </p:selectManyCheckbox>
                                    </p:column>

                                </p:row>
                            </p:panelGrid>
                        </p:column>
                    </p:row>
                    <!--开展放射诊疗设备质量控制检测-->
                    <p:row rendered="#{mgrbean.srvorgCard.ifFswsFhJc == 1}" >
                        <p:column colspan="2">
                            <div style="display: table-row;vertical-align: middle;">
                                <div style="display: table-cell;height:28px;vertical-align: middle;">
                                    <p:spacer width="20" height="0"/>
                                    <p:selectBooleanCheckbox value="#{mgrbean.ifInstZkJc}">
                                        <p:ajax event="change"  listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                                update="resultPanel"/>
                                    </p:selectBooleanCheckbox>
                                    <p:spacer width="5" height="0"/>
                                    <p:outputLabel value="开展放射诊疗设备质量控制检测" />
                                    <p:outputLabel value="（" rendered="#{null ne mgrbean.getTsSimpleCode('5646')}" />
                                </div>
                                <div style="display: table-cell;height:28px;vertical-align: middle;">
                                    <p:selectOneRadio value="#{mgrbean.srvorgCard.fkByJcTypeId.rid}" style="height: 24px;" disabled="#{mgrbean.srvorgCard.ifFswsFhJc != 1 or mgrbean.srvorgCard.ifInstZkJc == null or mgrbean.srvorgCard.ifInstZkJc == 0}" >
                                        <f:selectItems value="#{mgrbean.getTsSimpleCode('5646')}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}" />
                                    </p:selectOneRadio>
                                </div>
                                <div style="display: table-cell;height:28px;vertical-align: middle;padding-top: 5px;">
                                    <p:outputLabel value="） " rendered="#{null ne mgrbean.getTsSimpleCode('5646')}" />
                                    <p:outputLabel value="，检测设备 " />
                                    <p:inputText value="#{mgrbean.srvorgCard.zkJcInstNum}" maxlength="6" onkeyup="SYSTEM.clearNoNumBig0(this)" style="width: 80px;"
                                                 onblur="SYSTEM.clearNoNumBig0(this)" disabled="#{mgrbean.srvorgCard.ifInstZkJc != 1}"></p:inputText>
                                    <p:outputLabel value=" 台（套）"></p:outputLabel>
                                </div>
                            </div>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column rendered="#{mgrbean.srvorgCard.ifFswsFhJc == 1}" colspan="2">
                            <p:spacer width="45" height="0"/>
                            <p:outputLabel value="其中，检测结果有一项以上指标不合格的设备 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.notHgZkJcInstNum}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" style="width: 80px;"
                                         onblur="checkNotHgZkJcInstNum()"  disabled="#{mgrbean.srvorgCard.ifInstZkJc != 1}" id="notHgZkJcInstNum"
                            ></p:inputText>
                            <p:remoteCommand name="checkNotHgZkJcInstNum"  process="@this,notHgZkJcInstNum" update="notHgZkJcInstName"/>
                            <p:outputLabel value=" 台（套），不合格设备名称： "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.notHgZkJcInstName}" maxlength="200" style="width: 200px;"
                                         disabled="#{mgrbean.srvorgCard.ifInstZkJc != 1 or mgrbean.srvorgCard.notHgZkJcInstNum == null or mgrbean.srvorgCard.notHgZkJcInstNum == 0}"
                                         id="notHgZkJcInstName"
                            ></p:inputText>
                        </p:column>
                    </p:row>
                <!--放射诊疗建设项目评价-->
                    <p:row>
                        <p:column colspan="2">
                            <p:selectBooleanCheckbox value="#{mgrbean.ifItemPj}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="放射诊疗建设项目评价"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <!--预评价-->
                    <p:row rendered="#{mgrbean.srvorgCard.ifItemPj == 1}" >
                        <p:column colspan="2">
                            <p:spacer width="20" height="0"/>
                            <p:selectBooleanCheckbox value="#{mgrbean.ifItemYpj}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="预评价，剂量估算超标点位 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.ypjPoint}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" id="ypjPoint" style="width: 80px;"
                                         onblur="checkYpjPoint()" disabled="#{mgrbean.srvorgCard.ifItemYpj != 1}"></p:inputText>
                            <p:remoteCommand name="checkYpjPoint"  process="@this,ypjPoint" update="srvorgCardYpjs"/>
                            <p:outputLabel value=" 个"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifItemPj == 1}">
                        <p:column  colspan="2">
                            <p:panelGrid >
                                <p:row style="border-color: transparent;">
                                    <p:column style="border-color: transparent;">
                                        <p:spacer width="45" height="0"/>
                                        <p:outputLabel value="超标点位放射性危害类型："></p:outputLabel>
                                    </p:column>
                                    <p:column style="border-color: transparent;">
                                        <p:selectManyCheckbox columns="8" layout="grid" disabled="#{mgrbean.srvorgCard.ifItemYpj != 1 or mgrbean.srvorgCard.ypjPoint == null or mgrbean.srvorgCard.ypjPoint == 0}"
                                                              value="#{mgrbean.srvorgCard.srvorgCardYpjs}" id="srvorgCardYpjs">
                                            <f:selectItems value="#{mgrbean.getTsSimpleCode('5579')}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}" />
                                        </p:selectManyCheckbox>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </p:column>
                    </p:row>
                    <!--控制效果评价-->
                    <p:row rendered="#{mgrbean.srvorgCard.ifItemPj == 1}" >
                        <p:column  colspan="2">
                            <p:spacer width="20" height="0"/>
                            <p:selectBooleanCheckbox value="#{mgrbean.ifItemXgpj}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="控制效果评价，现场共检测点位 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.xgpjJcPoint}" maxlength="6" onkeyup="SYSTEM.clearNoNumBig0(this)" style="width: 80px;"
                                         onblur="SYSTEM.clearNoNumBig0(this)" disabled="#{mgrbean.srvorgCard.ifItemXgpj != 1}"></p:inputText>
                            <p:outputLabel value=" 个，其中，超标点位 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.notHgXgpjJcPoint}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" id="notHgXgpjJcPoint" style="width: 80px;"
                                         onblur="checkNotHgXgpjJcPoint()" disabled="#{mgrbean.srvorgCard.ifItemXgpj != 1}"></p:inputText>
                            <p:remoteCommand name="checkNotHgXgpjJcPoint"  process="@this,notHgXgpjJcPoint" update="srvorgCardXgpjs"/>
                            <p:outputLabel value=" 个"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifItemPj == 1}">
                        <p:column colspan="2">
                            <p:panelGrid >
                                <p:row style="border-color: transparent;">
                                    <p:column style="border-color: transparent;">
                                        <p:spacer width="45" height="0"/>
                                        <p:outputLabel value="超标点位放射性危害类型："></p:outputLabel>
                                    </p:column>
                                    <p:column style="border-color: transparent;">
                                        <p:selectManyCheckbox columns="8" layout="grid" disabled="#{mgrbean.srvorgCard.ifItemXgpj != 1 or mgrbean.srvorgCard.notHgXgpjJcPoint == null or mgrbean.srvorgCard.notHgXgpjJcPoint == 0}"
                                                              value="#{mgrbean.srvorgCard.srvorgCardXgpjs}" id="srvorgCardXgpjs">
                                            <f:selectItems value="#{mgrbean.getTsSimpleCode('5579')}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}" />
                                        </p:selectManyCheckbox>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                        </p:column>
                    </p:row>
                    <!--个人剂量监测-->
                    <p:row>
                        <p:column colspan="2">
                            <p:selectBooleanCheckbox value="#{mgrbean.ifDoseMonit}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="个人剂量监测，监测人数 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.doseMonitNum}" maxlength="6" onkeyup="SYSTEM.clearNoNumBig0(this)" style="width: 80px;"
                                         onblur="SYSTEM.clearNoNumBig0(this)" disabled="#{mgrbean.srvorgCard.ifDoseMonit != 1}"></p:inputText>
                            <p:outputLabel value=" 人，其中，5~20mSv人数 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.gt5msv}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" style="width: 80px;"
                                         onblur="SYSTEM.clearNoNum(this)" disabled="#{mgrbean.srvorgCard.ifDoseMonit != 1}"></p:inputText>
                            <p:outputLabel value="人，超过20mSv人数 "></p:outputLabel>
                            <u>
                            <p:outputLabel value="#{mgrbean.srvorgCard.gt20msvBhkNum}" id ="gt20msvBhkNum"></p:outputLabel>
                            </u>
                            <p:outputLabel value=" 人。"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifDoseMonit == 1}">
                        <p:column style="border-bottom-color: transparent;border-right-color: transparent;" >
                            <p:commandButton value="添加" icon="ui-icon-plus" action="#{mgrbean.addSrvorgCardDose}"
                                             process="@this,srvorgCardDoseList,gt20msvBhkNum" update="srvorgCardDoseList,gt20msvBhkNum">
                            </p:commandButton>
                        </p:column>
                        <p:column style="border-bottom-color: transparent;border-left-color: transparent;text-align: right" >
                            <p:outputLabel value="提示："
                                           style="color: #fc2c2c;line-height: 12px;"></p:outputLabel>
                            <p:outputLabel value="超过20mSv人数自动计算。"
                                           style="color: #3421fc;line-height: 12px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifDoseMonit == 1}">
                        <p:column colspan="2" >
                            <p:dataTable id="srvorgCardDoseList" paginatorPosition="bottom"
                                         value="#{mgrbean.srvorgCard.srvorgCardDoseList}"
                                         widgetVar="cardRsnTable" var="srvorgCardDose"
                                         emptyMessage="没有数据！" rowIndexVar="R">
                                <p:column headerText="姓名" style="width:80px;text-align: center;">
                                    <p:inputText value="#{srvorgCardDose.psnName}" maxlength="50" style="width: 100px;"></p:inputText>
                                </p:column>
                                <p:column headerText="身份证号" style="width:180px;text-align:center;">
                                    <p:inputText value="#{srvorgCardDose.idc}" maxlength="20"></p:inputText>
                                </p:column>
                                <p:column headerText="检测日期" style="width:120px;text-align:center;">
                                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                                showOtherMonths="true"  size="11" navigator="true"
                                                yearRange="c-10:c+10" converterMessage="检测日期输入不正确！"
                                                showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                                                value="#{srvorgCardDose.jcDate}" />
                                </p:column>
                                <p:column headerText="X、γ外照射个人剂量当量项目值" style="width:195px;text-align:center;">
                                    <p:inputText value="#{srvorgCardDose.doseCal}"  onkeyup="SYSTEM.verifyNum(this,12,6);" onblur="SYSTEM.verifyNum(this,12,6);"></p:inputText>
                                </p:column>
                                <p:column headerText="计量单位" style="width:60px;text-align:center;">
                                    <p:outputLabel value="mSv" maxlength="20"></p:outputLabel>
                                </p:column>
                                <p:column headerText="操作" >
                                    <p:commandLink value="删除"
                                                   update=":tabView:editForm:srvorgCardDoseList,:tabView:editForm:gt20msvBhkNum"
                                                   process="@this,:tabView:editForm:srvorgCardDoseList,:tabView:editForm:gt20msvBhkNum"
                                                   action="#{mgrbean.delSrvorgCardDoseAction(srvorgCardDose)}">
                                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                    </p:commandLink>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                    <!--放射防护器材和含放射性产品检测-->
                    <p:row>
                        <p:column colspan="2">
                            <p:selectBooleanCheckbox value="#{mgrbean.ifFhInstJc}">
                                <p:ajax event="change" listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="放射防护器材和含放射性产品检测"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifFhInstJc == 1}">
                        <p:column colspan="2">
                            <p:spacer width="20" height="0"/>
                            <p:selectBooleanCheckbox value="#{mgrbean.ifJcInst}">
                                <p:ajax event="change"  listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="开展放射防护器材检测，共检测样品数量 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.jcInstNum}" maxlength="6" onkeyup="SYSTEM.clearNoNumBig0(this)" style="width: 80px;"
                                         onblur="SYSTEM.clearNoNumBig0(this)" disabled="#{mgrbean.srvorgCard.ifJcInst != 1}"></p:inputText>
                            <p:outputLabel value=" 个"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column rendered="#{mgrbean.srvorgCard.ifFhInstJc == 1}" colspan="2">
                            <p:spacer width="45" height="0"/>
                            <p:outputLabel value="其中，超标样品数量 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.jcNotHgInstNum}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" style="width: 80px;"
                                         onblur="checkJcNotHgInstNum()"  disabled="#{mgrbean.srvorgCard.ifJcInst != 1}" id="jcNotHgInstNum"
                            ></p:inputText>
                            <p:remoteCommand name="checkJcNotHgInstNum"  process="@this,jcNotHgInstNum" update="notHgInstName"/>
                            <p:outputLabel value=" 个，超标样品名称： "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.notHgInstName}" maxlength="200" style="width: 200px;"
                                         disabled="#{mgrbean.srvorgCard.ifJcInst != 1 or mgrbean.srvorgCard.jcNotHgInstNum == null or mgrbean.srvorgCard.jcNotHgInstNum == 0}"
                                         id="notHgInstName"
                            ></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.srvorgCard.ifFhInstJc == 1}">
                        <p:column colspan="2">
                            <p:spacer width="20" height="0"/>
                            <p:selectBooleanCheckbox value="#{mgrbean.ifJcUse}">
                                <p:ajax event="change"  listener="#{mgrbean.changeResultStateAction}" process="resultPanel"
                                        update="resultPanel"/>
                            </p:selectBooleanCheckbox>
                            <p:spacer width="5" height="0"/>
                            <p:outputLabel value="开展含放射性产品检测，共检测样品数量 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.jcUseNum}" maxlength="6" onkeyup="SYSTEM.clearNoNumBig0(this)" style="width: 80px;"
                                         onblur="SYSTEM.clearNoNumBig0(this)" disabled="#{mgrbean.srvorgCard.ifJcUse != 1}"></p:inputText>
                            <p:outputLabel value=" 个"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column rendered="#{mgrbean.srvorgCard.ifFhInstJc == 1}" colspan="2">
                            <p:spacer width="45" height="0"/>
                            <p:outputLabel value="其中，超标样品数量 "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.jcNotHgUseNum}" maxlength="6" onkeyup="SYSTEM.clearNoNum(this)" style="width: 80px;"
                                         onblur="checkJcNotHgUseNum()"  disabled="#{mgrbean.srvorgCard.ifJcUse != 1}" id="jcNotHgUseNum"
                            ></p:inputText>
                            <p:remoteCommand name="checkJcNotHgUseNum"  process="@this,jcNotHgUseNum" update="notHgUseName"/>
                            <p:outputLabel value=" 个，超标样品名称： "></p:outputLabel>
                            <p:inputText value="#{mgrbean.srvorgCard.notHgUseName}" maxlength="200" style="width: 200px;"
                                         disabled="#{mgrbean.srvorgCard.ifJcUse != 1 or mgrbean.srvorgCard.jcNotHgUseNum == null or mgrbean.srvorgCard.jcNotHgUseNum == 0}"
                                         id="notHgUseName"
                            ></p:inputText>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!--报告信息-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="报告信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="填报单位："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.srvorgCard.fillUnitName}" ></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="单位负责人："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.srvorgCard.orgFzMan}" maxlength="25" style="width: 184px;"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.srvorgCard.fillFormPsn}" maxlength="25" style="width: 184px;"></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人联系电话："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.srvorgCard.fillLink}" maxlength="25" style="width: 184px;"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;"  colspan="3">
                            <p:calendar pattern="yyyy-MM-dd" maxlength="10"
                                        showOtherMonths="true" id="fillDate" size="11" navigator="true"
                                        yearRange="c-10:c" converterMessage="填表日期，格式输入不正确！"
                                        showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                        value="#{mgrbean.srvorgCard.fillDate}" styleClass="myDate" />
                        </p:column>
                    </p:row>

                </p:panelGrid>
            </div>
        </p:outputPanel>
            <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog"
                      resizable="false" modal="true">
                <table>
                    <tr>
                        <td style="text-align: right;"><p:outputLabel
                                value="（支持附件格式为：PDF）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                        </td>
                    </tr>
                    <tr>
                        <td style="position: relative;top: -23px;"><p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}"
                                invalidSizeMessage="文件大小不能超过1M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;" previewWidth="120" cancelLabel="取消"
                                fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="1048576"
                                invalidFileMessage="无效的文件类型！只能上传pdf类型文件"
                                allowTypes="/(\.|\/)(pdf)$/" update="@this"
                        /></td>
                    </tr>
                </table>
            </p:dialog>
        <p:dialog id="shadeTip" widgetVar="ShadeTip" modal="true" height="20" resizable="false" showHeader="false" closeOnEscape="true" styleClass="shadeTip">
            <p:panelGrid >
                <p:row style="border:1px solid transparent !important;">
                    <p:column style="border: transparent !important;">
                        <p:graphicImage url="/resources/images/main/loading5.gif" style="margin-top: 4px;"/>
                    </p:column>
                    <p:column style="border: transparent !important;">
                        <h:outputText style="color: #FFFFFF;font-size: 15px;" value="生成文书中，请等待..." />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
    </ui:define>
</ui:composition>
