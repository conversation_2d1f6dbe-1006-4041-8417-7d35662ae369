<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.TdZwHethChkSmaryListBean"-->
    <ui:define name="insertEditScripts">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <style>
            .op1 {
                opacity: 1 !important;
            }
            .op1 input {
                opacity: 1 !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput(ifView,id){
                if(ifView=="false"){
                    return;
                }
                var text;
                var $tabView ;
                if(id){
                    $tabView = $("#"+id)
                }else{
                    $tabView = $("#tabView\\:editForm");
                }
                $tabView.find("input,textarea").each(function(){
                    if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
                        $(this).css("pointer-events","none");
                    }else{
                        $(this).prop("disabled",true);
                    }
                    $(this).css("opacity","1");
                });
                //单选框label的for标签处理
                $tabView.find("label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
                        $(this).remove();
                    }else if("查看"==text||"修改"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("扫描"==text||"预览"==text||"制作"==text||"删除"==text||"上传"==text||"添加"==text||"设计"==text||"汇总"==text){
                        $(this).remove();
                    }else if("查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
    </ui:define>
    <ui:define name="insertEditTitle">
    	<p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业性有害因素监测卡填报"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="9" style="border-color:transparent;padding:0px;" id="headButton">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"
                                 update=":tabView:editForm:rptNoPanel,badrsnsPanel"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm:rptNoPanel"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="初审退回意见" icon="icon-alert"  style="color:red;"
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 2 or (mgrbean.tdZwBgkLastSta.state == 4 and mgrbean.chkSmary.fkByEmpZoneId.ifCityDirect == 1)}">
                </p:commandButton>
                <p:commandButton value="复审退回意见" icon="icon-alert"  style="color:red;"
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 4 and mgrbean.chkSmary.fkByEmpZoneId.ifCityDirect != 1}">
                </p:commandButton>
                <p:commandButton value="终审退回意见" icon="icon-alert"  style="color:red;" styleClass=""
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 6}">
                </p:commandButton>

                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" update=":tabView" oncomplete="PF('ConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:sticky target="sticky"></p:sticky>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:outputPanel styleClass="businessInfo">
            <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                <p:row>
                    <p:column colspan="2" ><span>本环节文书</span></p:column>
                </p:row>
                <p:row>
                    <p:column style="padding: 0;" colspan="2">
                        <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 190px;">
                        <h:outputText value="《职业性有害因素监测卡》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="设计" icon="ui-icon-pencil" action="#{mgrbean.designWritReport}"
                                             rendered="#{mgrbean.ifShowDesign}"
                                             process="@this,:tabView:editForm" update=":tabView:editForm">
                            </p:commandButton>
                            <p:spacer width="5" rendered="#{mgrbean.chkSmary.annexPath == null}"/>
                            <p:commandButton value="制作"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.buildWritReport}"
                                             rendered="#{mgrbean.chkSmary.annexPath == null}"
                                             update=":tabView">
                            </p:commandButton>
                            <p:spacer width="5"/>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.chkSmary.annexPath}')"
                                             rendered="#{mgrbean.chkSmary.annexPath != null}">
                            </p:commandButton>
                            <p:spacer width="5"/>
                            <p:commandButton value="删除"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.delMadedwrit}"
                                             rendered="#{mgrbean.chkSmary.annexPath != null}"
                                             update=":tabView:editForm">
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <div id="chkSmaryDiv">
                <!-- 标题 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;">
                            <p:outputPanel style="padding-bottom:8px;">
                                <h:outputText value="职业性有害因素监测卡"
                                              style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <p:panelGrid id="rptNoPanel" style="width: 952px;margin: auto;" >
                    <p:row>
                        <p:column styleClass="noBorder" style="width:100px;text-align: left;">
                            <p:outputPanel style="padding-bottom:5px;">
                                <h:outputText value="报告卡编码：" ></h:outputText>
                                <h:outputText value="#{mgrbean.chkSmary.rptNo eq null?'自动生成':mgrbean.chkSmary.rptNo}" style="#{mgrbean.chkSmary.rptNo eq null?'color: gray':''}"></h:outputText>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 基本信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="用人单位信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="用人单位名称："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <table>
                                <tr>
                                    <td style="padding:0;border-color: transparent;">
                                        <p:inputText id="crptName"  readonly="true" style="width: 200px" value="#{mgrbean.chkSmary.crptName}" onclick="$('#tabView\\:editForm\\:onCrptSelect').click()"/>
                                    </td>
                                    <td style="padding:0;border-color: transparent;position: relative;left: -18px;">
                                        <p:commandLink  id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                                        action="#{mgrbean.selectCrptList}" process="@this" style="position: relative;left: -10px;"
                                        >
                                            <f:setPropertyActionListener value="1" target="#{mgrbean.crpyType}"/>
                                            <p:ajax event="dialogReturn" process="@this,jcSubList" resetValues="true" listener="#{mgrbean.onCrptSelect}" update="basicPanel,badrsnsPanel"/>
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="社会信用代码："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText value="#{mgrbean.chkSmary.creditCode}"></h:outputText>
                        </p:column>
                    </p:row>

                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="经济类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:outputLabel value="#{mgrbean.chkSmary.fkByEconomyId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.chkSmary.fkByIndusTypeId.codeName}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="企业规模："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.chkSmary.fkByCrptSizeId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地区："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.fkByZoneId.fullName}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地址："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.address}"></h:outputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地址邮编："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.postcode}"></h:outputText>
                        </p:column>

                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位联系人："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.safeposition}"></h:outputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位联系人电话："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.safephone}"></h:outputText>
                        </p:column>

                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="职工总人数："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.staffNum}"></h:outputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="生产工人数："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.workNum}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="用工单位信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="用工单位名称："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText readonly="true" style="width:200px;#{mgrbean.selEmpCrpt?'display:none;':''}"
                                         value="#{mgrbean.chkSmary.empCrptName}" />
                            <h:panelGrid columns="2" style="border-color: #ffffff;margin: 0;padding: 0;#{mgrbean.selEmpCrpt?'':'display:none;'}">
                                <p:inputText id="empCrptName"  readonly="true" style="width:200px;"
                                             value="#{mgrbean.chkSmary.empCrptName}"
                                             onclick="document.getElementById('tabView:editForm:onEmpCrptSelect').click()"/>
                                <p:commandLink  id="onEmpCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                                action="#{mgrbean.selectCrptList}" process="@this" style="position: relative;left: -30px;">
                                    <f:setPropertyActionListener value="2" target="#{mgrbean.crpyType}"/>
                                    <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onCrptSelect}" update="basicPanel"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="社会信用代码："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText value="#{mgrbean.chkSmary.empCreditCode}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="经济类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpEconomyId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpIndusTypeId.codeName}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="企业规模："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpCrptSizeId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用工单位地区："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.chkSmary.fkByEmpZoneId.fullName}"></h:outputText>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 接害情况 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                    <p:row>
                        <p:column colspan="#{mgrbean.colInt * 2 + 2 }">
                            <p:outputLabel value="接害情况"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" style="width:400px;">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="当年接触职业性有害因素作业人数："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" colspan="#{mgrbean.colInt * 2 + 1 }">
                            <p:inputText value="#{mgrbean.chkSmary.tchBadrsnNum}" onkeyup="SYSTEM.clearNoNum(this)" maxlength="6"></p:inputText>
                        </p:column>
                    </p:row>
                    <c:if test="#{mgrbean.badRsnTypeVoList != null }">
                        <c:forEach items="#{mgrbean.badRsnTypeVoList}" var="itm">
                            <p:row>
                                  <p:column styleClass="column_title" style="width:400px;">
                                      <h:outputText value="*" style="color:red;" />
                                      <p:outputLabel value="#{itm.badrsnType.codeName}："></p:outputLabel>
                                  </p:column>
                                  <p:column  style="padding-left: 8px;width: 80px;" >
                                      <p:inputText value="#{itm.touchNum}" onkeyup="SYSTEM.clearNoNum(this)" maxlength="6" style="width: 60px;"></p:inputText>
                                  </p:column>
                                <c:if test="#{itm.postVos != null }">
                                    <c:forEach items="#{itm.postVos}" var="i" varStatus="status" >
                                        <p:column styleClass="column_title" style="width:210px;" rendered="#{status.first}">
                                            <p:outputLabel value="应检人数：" ></p:outputLabel>
                                            <h:outputText value="*" style="color:red;" />
                                            <p:outputLabel value="#{i.post.codeName}：" ></p:outputLabel>
                                        </p:column>
                                        <p:column styleClass="column_title" style="width:140px;" rendered="#{!status.first}">
                                            <h:outputText value="*" style="color:red;" />
                                            <p:outputLabel value="#{i.post.codeName}：" ></p:outputLabel>
                                        </p:column>
                                        <p:column  style="padding-left: 8px;width: 80px;" >
                                            <p:inputText value="#{i.needChkNum}" onkeyup="SYSTEM.clearNoNum(this)" maxlength="6" style="width: 60px;"></p:inputText>
                                        </p:column>
                                    </c:forEach>
                                </c:if>
                              </p:row>
                        </c:forEach>
                    </c:if>
                </p:panelGrid>

                <p:outputPanel styleClass="writeSortInfo" style="margin-bottom: 10px;display: flex;align-items: center;">
                    一个周期内是否开展职业性有害因素检测：
                    <p:selectOneRadio id="isfunc" value="#{mgrbean.chkSmary.ifOpenOneWeek}" style="width: 90px;">
                        <f:selectItem itemLabel="是" itemValue="1"/>
                        <f:selectItem itemLabel="否" itemValue="0"/>
                        <p:ajax event="change" process="@this,badrsnsPanel" update="badrsnsPanel"/>
                    </p:selectOneRadio>
                </p:outputPanel>
                <!-- 职业性有害因素检测情况-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;#{mgrbean.chkSmary.ifOpenOneWeek eq 1?'':'display:none;'}"
                             id="badrsnsPanel">
                    <p:row>
                        <p:column style="border-bottom-color: transparent;" colspan="2">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业性有害因素检测情况"
                                           style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"></p:outputLabel>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             action="#{mgrbean.addJcSub}"
                                             process="@this,:tabView:editForm:jcSubList"
                                             update=":tabView:editForm:jcSubList">
                            </p:commandButton>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column colspan="2" style="border-bottom-color: transparent;">
                            <p:dataTable id="jcSubList" paginatorPosition="bottom"
                                         value="#{mgrbean.chkSmary.jcSubList}"
                                         widgetVar="badRsnTable" var="jcSub"
                                         emptyMessage="没有数据！" rowIndexVar="R"
                                         paginator="true" rows="10"
										 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
										 rowsPerPageTemplate="10,20" lazy="true"
					                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
					                     pageLinks="5"
					                     >
                                <p:columnGroup type="header">
                                    <p:row>
                                        <p:column rowspan="2" headerText="职业性有害因素" />
                                        <p:column rowspan="2" headerText="工作场所" />
                                        <p:column rowspan="2" headerText="岗位/工种" />
                                        <p:column rowspan="2" headerText="浓/强度类型" />
                                        <p:column colspan="2" headerText="检测值" />
                                        <p:column rowspan="2" headerText="检测日期" />
                                        <p:column rowspan="2" headerText="合格情况" />
                                        <p:column rowspan="2" headerText="操作" />
                                    </p:row>
                                    <p:row>
                                        <p:column  headerText="最小值" />
                                        <p:column  headerText="最大值" />
                                    </p:row>
                                </p:columnGroup>
                                <p:column headerText="职业性有害因素" style="width:120px;">
                                    <table>
                                        <tr>
                                            <td style="padding:0;border-color: transparent;">
                                                <p:inputText id="badrsns" value="#{jcSub.fkByBadrsnId.codeName}"
                                                             readonly="true" style="width: 100px;opacity: 1;"
                                                             onclick="document.getElementById('tabView:editForm:jcSubList:#{R}:selBadrsns').click();"
                                                             disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"/>
                                            </td>
                                            <td style="padding:0;border-color: transparent;position: relative;left: -18px;">
                                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                                               id="selBadrsns"
                                                               style="opacity: 1;"
                                                               action="#{mgrbean.selectBadtree}" process="@this"
                                                               disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}">
                                                    <f:setPropertyActionListener target="#{mgrbean.badrsnIndex}" value="#{R}"></f:setPropertyActionListener>
                                                    <p:ajax event="dialogReturn"
                                                            listener="#{mgrbean.onBadtreeSelect}" process="@this"
                                                            resetValues="true" update="badrsns" />
                                                </p:commandLink>
                                            </td>
                                        </tr>
                                    </table>
                                </p:column>
                                <p:column headerText="工作场所" style="width:80px;text-align:center;">
                                    <p:inputText value="#{jcSub.workPlace}" maxlength="100" style="width: 80px;opacity: 1;"
                                                 disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"/>
                                </p:column>
                                <p:column headerText="岗位/工种" style="text-align:center;width: 100px;" >
                                    <p:inputText value="#{jcSub.workType}" maxlength="25" style="width: 80px;opacity: 1;"
                                                 disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"/>
                                </p:column>
                                <p:column headerText="浓/强度类型" style="text-align:center;width: 100px;">
                                    <p:selectOneMenu value="#{jcSub.thickTypeId}" style="width: 90px;float: left;opacity: 1;"
                                                     disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}">
                                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                        <f:selectItems value="#{mgrbean.fkByThickTypeList}" var="itm"
                                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                    </p:selectOneMenu>
                                </p:column>
                                    <p:column headerText="检测值（最小值）" style="text-align:center;width: 60px;" >
                                        <p:inputText value="#{jcSub.jcValMin}"
                                                     maxlength="20" style="width: 50px;opacity: 1;"
                                                     disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"/>
                                    </p:column>
                                    <p:column headerText="检测值（最大值）" style="text-align:center;width: 60px;" >
                                        <p:inputText value="#{jcSub.jcValMax}"
                                                     maxlength="20" style="width: 50px;opacity: 1;"
                                                     disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"/>
                                    </p:column>

                                <p:column headerText="检测日期" style="text-align:center;width: 80px;" >
                                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                                showOtherMonths="true" size="11" navigator="true"
                                                yearRange="c-10:c+10" converterMessage="检测时间，格式输入不正确！"
                                                showButtonPanel="true" maxdate="new Date()"
                                                value="#{jcSub.jcTime}"
                                                styleClass="op1"
                                                disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"/>
                                </p:column>
                                <p:column headerText="合格情况" style="text-align:center;width: 90px;" >
                                    <p:selectOneMenu value="#{jcSub.hgFlag}" style="width: 90px;float: left;opacity: 1;"
                                                     disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}">
                                        <f:selectItem itemLabel="" itemValue=""/>
                                        <f:selectItem itemLabel="合格" itemValue="1"/>
                                        <f:selectItem itemLabel="不合格" itemValue="0"/>
                                    </p:selectOneMenu>
                                </p:column>
                                <p:column headerText="操作" >
                                    <p:commandLink value="删除"
                                                   update=":tabView:editForm:jcSubList"
                                                   process="@this,:tabView:editForm:jcSubList"
                                                   action="#{mgrbean.delCurBadAction(jcSub)}"
                                                   disabled="#{not empty mgrbean.chkSmary.getAnnexPath()}"
                                                   rendered="#{empty mgrbean.chkSmary.getAnnexPath()}">
                                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                    </p:commandLink>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="border-right-color: transparent;border-top-color:transparent;text-align:right;;padding: 0 10px 3px 3px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="检测单位："></p:outputLabel>
                            <p:inputText value="#{mgrbean.chkSmary.jcUnitName}" maxlength="50" style="width: 250px;" />
                        </p:column>
                        <p:column style="border-top-color:transparent;text-align:center;padding: 0 1px 3px 3px">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="检测单位负责人："></p:outputLabel>
                            <p:inputText value="#{mgrbean.chkSmary.jcUnitCharge}" maxlength="50"  style="width: 190px;" />
                        </p:column>
                    </p:row>
                </p:panelGrid>

                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="填表人信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.chkSmary.fillFormPsn}" maxlength="25"></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人联系电话："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.chkSmary.fillLink}" maxlength="25"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:column styleClass="noBorder" style="text-align: left;" colspan="3">
                                <p:calendar pattern="yyyy-MM-dd" maxlength="10"
                                            showOtherMonths="true" id="fillDate1" size="11" navigator="true"
                                            yearRange="c-10:c" converterMessage="填表日期，格式输入不正确！"
                                            showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                            value="#{mgrbean.chkSmary.fillDate}" />
                            </p:column>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表单位："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText value="#{mgrbean.chkSmary.fkByFillSysUnitId.unitname}" ></h:outputText>
                        </p:column>
                    </p:row>

                </p:panelGrid>
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="报告人信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告人："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.chkSmary.rptPsn}" maxlength="50"></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告人联系电话："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.chkSmary.rptLink}" maxlength="50"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:column styleClass="noBorder" style="text-align: left;" colspan="3">
                                <p:outputLabel value="#{mgrbean.chkSmary.rptDate}" style="font-size: 14px;">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
                                </p:outputLabel>
                            </p:column>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告单位："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText value="#{mgrbean.chkSmary.fkByFillSysUnitId.unitname}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="备注："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" colspan="3">
                            <p:inputTextarea rows="5" autoResize="false"
                                             style="resize: none;width: 786px;"
                                             value="#{mgrbean.chkSmary.rmk}"
                                             maxlength="500"/>
                        </p:column>
                    </p:row>

                </p:panelGrid>
            </div>
        </p:outputPanel>

        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.countAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true" 
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 2}"/>
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.cityAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 4}"/>
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.proAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 6}"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         onclick="PF('ReasonDialog').hide();" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <ui:include src="/WEB-INF/templates/system/frpt.xhtml">
            <ui:param name="printBackingBean" value="#{tdZwHethChkSmaryListBean}"/>
        </ui:include>
        <ui:include src="/WEB-INF/templates/system/frpt2.xhtml">
            <ui:param name="updateId" value=":tabView:editForm"/>
            <ui:param name="printBackingBean" value="#{tdZwHethChkSmaryListBean}"/>
        </ui:include>
    </ui:define>
</ui:composition>
