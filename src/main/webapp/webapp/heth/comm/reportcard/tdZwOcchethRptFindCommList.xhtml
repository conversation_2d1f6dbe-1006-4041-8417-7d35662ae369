<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">


    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwOcchethRptCommFindBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwOcchethRptCommView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml" />
        <script src="#{request.contextPath}/resources/js/namespace.js" type="text/javascript"> </script>
        <script src="#{request.contextPath}/resources/js/validate/system/validate.js" ></script>
        <script type="text/javascript">
            //<![CDATA[
            function getDownloadFileClick(){
                document.getElementById("tabView:mainForm:downloadFile").click();
            }
            //]]>
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="技术服务档案查询"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}"
                                 update="dataTable"
                                 process="@this,:tabView:mainForm:mainGrid"/>
                <p:commandButton value="导出" icon="ui-icon-document"  process="@this,:tabView:mainForm:mainGrid"
                                 action="#{mgrbean.downloadFileCheck}"  />
                <p:commandButton style="display: none;"  id="downloadFile" icon="ui-icon-document" ajax="false" process="@this"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop)">
                    <p:fileDownload value="#{mgrbean.downloadFile}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width: 150px;height: 38px">
                <p:outputLabel value="用人单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 0px;width: 220px;">
                        <zwx:ZoneSingleNewComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                     zoneCode="#{mgrbean.searchZoneCode}"
                                      zoneName="#{mgrbean.searchZoneName}"
                                       zoneType="#{mgrbean.searchZoneType}" ifShowTrash="true"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;">
                    <p:outputLabel value="单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;width: 220px">
                    <p:inputText value="#{mgrbean.orgName}" maxlength="40" size="27" style="width: 200px"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width:150px;">
                    <h:outputText value="报告日期：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                showOtherMonths="true" id="startDate" size="11" navigator="true"
                                yearRange="c-10:c+10" converterMessage="检测（评价）开始日期，格式输入不正确！"
                                showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                                value="#{mgrbean.rptDateSearchStart}" />
                    ~
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                showOtherMonths="true" id="endDate" size="11" navigator="true"
                                yearRange="c-10:c+10" converterMessage="检测（评价）结束日期，格式输入不正确！"
                                showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                                value="#{mgrbean.rptDateSearchEnd}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 150px;height: 38px">
                    <p:outputLabel value="技术服务机构地区："/>
                </p:column>
                <p:column style="text-align:left;padding-left: 0px;width: 220px;">
                    <zwx:ZoneSingleNewComp id="searchUnitZone" zoneList="#{mgrbean.zoneList}"
                                           zoneCode="#{mgrbean.searchUnitZoneCode}"
                                           zoneName="#{mgrbean.searchUnitZoneName}"
                                           zoneType="#{mgrbean.searchUnitZoneType}" ifShowTrash="true"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width:150px;">
                    <p:outputLabel value="技术服务机构：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;width: 220px">
                    <p:inputText value="#{mgrbean.searchUnitName}" maxlength="40" size="27" style="width: 200px"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;">
                    <h:outputText value="技术服务类别：" />
                </p:column>
                <p:column style="text-align:left;">
                    <h:panelGrid columns="4" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchGrid">
                        <p:inputText id="jsfwTreeName" value="#{mgrbean.jsfwTreeName}" style="width: 206px;" readonly="true" />
                        <h:inputHidden id="jsfwTreeCode" value="#{mgrbean.jsfwTreeCode}" />
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"  style="position: relative;left: -40px;"
                                       oncomplete="PF('DiseaseOverlayPanel').show()" type="button" />
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -41px;"
                                       action="#{mgrbean.clearJsfwlb}"
                                       process="@this,searchGrid,diseaseOverlayPanel" update="searchGrid,diseaseOverlayPanel"/>
                    </h:panelGrid>
                    <p:overlayPanel id="diseaseOverlayPanel" for="jsfwTreeName"
                                    style="width:320px;" widgetVar="DiseaseOverlayPanel"
                                    showCloseIcon="true" onHide="Hide();">
                        <p:tree var="node" selectionMode="checkbox" id="diseaseTree"
                                value="#{mgrbean.jsfwlbTree}"
                                style="width: 300px;height: 200px;overflow-y: auto;"
                                selection="#{mgrbean.searchSelJsfw}">
                            <p:treeNode>
                                <p:outputLabel value="#{node.codeName}" />
                            </p:treeNode>
                        </p:tree>
                    </p:overlayPanel>
                    <p:remoteCommand name="Hide"
                                     process="@this,searchGrid,diseaseOverlayPanel"
                                     action="#{mgrbean.selectJsfwlbAction}"
                                     update="searchGrid" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 150px;height: 38px">
                    <p:outputLabel value="质控编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" >
                    <p:inputText value="#{mgrbean.manageNo}" maxlength="40" size="27" style="width: 181px"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width:150px;">
                    <h:outputText value="状态：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="3">
                    <p:selectManyCheckbox value="#{mgrbean.states}">
                        <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                    </p:selectManyCheckbox>
                </p:column>
            </p:row>
        </ui:define>
        <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="质控编号" style="width:240px;padding-left: 3px;text-align: center">
            <h:outputLabel value="#{itm[1]}">
            </h:outputLabel>
        </p:column>
        <p:column headerText="单位名称" style="width:280px;padding-left: 3px;">
            <h:outputLabel value="#{itm[2]}">
            </h:outputLabel>
        </p:column>
        <p:column headerText="用人单位地区" style="width:180px;padding-left: 3px;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="联系人" style="width:80px;padding-left: 3px;text-align: center">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="联系电话" style="width:120px;text-align: center" rendered="false">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="技术服务机构" style="width:240px;">
            <h:outputLabel value="#{itm[6]}" />
        </p:column>
        <p:column headerText="报告日期" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm[7]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="技术服务类别" style="width:240px;">
            <h:outputLabel value="#{itm[8]}" />
        </p:column>
        <p:column headerText="状态" style="width:80px;text-align: center;">
            <h:outputLabel rendered="#{itm[10]==1}">
                <font color="green">已提交</font>
            </h:outputLabel>
            <h:outputLabel  rendered="#{itm[10]==0}">
                <font color="red">待提交</font>
            </h:outputLabel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;" exportable="false">
            <p:spacer width="6px"></p:spacer>
            <p:commandLink value="详情" process="@this" action="#{mgrbean.viewInitAction}" update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="1"/>
            </p:commandLink>
        </p:column>

    </ui:define>


</ui:composition>