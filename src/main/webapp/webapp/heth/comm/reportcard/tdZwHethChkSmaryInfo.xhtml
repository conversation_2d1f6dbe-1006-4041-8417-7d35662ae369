<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui">
    <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
        <p:row>
            <p:column colspan="2"><span>本环节文书</span></p:column>
        </p:row>
        <p:row>
            <p:column style="padding: 0;" colspan="2">
                <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="width: 190px;">
                <h:outputText value="《职业性有害因素监测卡》"/>
            </p:column>
            <p:column>
                <p:outputPanel>
                    <p:commandButton value="查看"
                                     action="#{mgrbean.toAnnexView}"
                                     rendered="#{mgrbean.chkSmary.annexPath != null}">
                    </p:commandButton>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 标题 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
        <p:row>
            <p:column styleClass="noBorder" style="text-align: center;">
                <p:outputPanel style="padding-bottom:8px;">
                    <h:outputText value="职业性有害因素监测卡"
                                  style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid id="rptNoPanel" style="width: 952px;margin: auto;" >
        <p:row>
            <p:column styleClass="noBorder" style="width:100px;text-align: left;">
                <p:outputPanel style="padding-bottom:5px;">
                    <h:outputText value="报告卡编码：" />
                    <h:outputText value="#{mgrbean.chkSmary.rptNo}" />
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 基本信息 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="用人单位信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位名称："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.crptName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="社会信用代码："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.creditCode}"></p:outputLabel>
            </p:column>
        </p:row>

        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="经济类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByEconomyId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业类别："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByIndusTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="企业规模："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByCrptSizeId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地区："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByZoneId.fullName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地址："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.address}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位地址邮编："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.postcode}"></p:outputLabel>
            </p:column>

        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位联系人："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.safeposition}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用人单位联系人电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.safephone}"></p:outputLabel>
            </p:column>

        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="职工总人数："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.staffNum}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="生产工人数："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.workNum}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="用工单位信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="用工单位名称："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.empCrptName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="社会信用代码："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.empCreditCode}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="经济类型："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width:320px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpEconomyId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业类别："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpIndusTypeId.codeName}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="企业规模："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpCrptSizeId.codeName}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="用工单位地区："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByEmpZoneId.fullName}"></p:outputLabel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!-- 职业健康检查情况 -->
    <!-- 接害情况 -->
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <p:row>
            <p:column colspan="#{mgrbean.colInt * 2 + 2 }">
                <p:outputLabel value="接害情况"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="width:350px;">
                <p:outputLabel value="当年接触职业性有害因素作业人数："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;" colspan="#{mgrbean.colInt * 2 + 1 }">
                <p:outputLabel value="#{mgrbean.chkSmary.tchBadrsnNum}"></p:outputLabel>
            </p:column>
        </p:row>
        <c:if test="#{mgrbean.badRsnTypeVoList != null }">
            <c:forEach items="#{mgrbean.badRsnTypeVoList}" var="itm">
                <p:row>
                    <p:column styleClass="column_title" style="width:350px;">
                        <p:outputLabel value="#{itm.badrsnType.codeName}："></p:outputLabel>
                    </p:column>
                    <p:column style="padding-left: 8px;width: 100px;">
                        <p:outputLabel value="#{itm.touchNum}" style="width: 60px;"></p:outputLabel>
                    </p:column>
                    <c:if test="#{itm.postVos != null }">
                        <c:forEach items="#{itm.postVos}" var="i" varStatus="status">
                            <p:column styleClass="column_title" style="width:180px;" rendered="#{status.first}">
                                <p:outputLabel value="应检人数：#{i.post.codeName}：" ></p:outputLabel>
                            </p:column>
                            <p:column styleClass="column_title" style="width:150px;" rendered="#{!status.first}">
                                <p:outputLabel value="#{i.post.codeName}："></p:outputLabel>
                            </p:column>
                            <p:column style="padding-left: 8px;width: 110px;">
                                <p:outputLabel value="#{i.needChkNum}" style="width: 60px;"></p:outputLabel>
                            </p:column>
                        </c:forEach>
                    </c:if>
                </p:row>
            </c:forEach>
        </c:if>
    </p:panelGrid>
    <!-- 职业性有害因素检测情况-->
    <p:outputPanel styleClass="writeSortInfo" style="margin-bottom: 10px;display: flex;align-items: center;">
        一个周期内是否开展职业性有害因素检测：
        <p:outputLabel value="#{mgrbean.chkSmary.ifOpenOneWeek eq 1?'是':'否'}"/>
    </p:outputPanel>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" rendered="#{mgrbean.chkSmary.ifOpenOneWeek eq 1}">
        <p:row>
            <p:column
                    style="border-bottom-color: transparent;" colspan="2">
                <p:outputLabel value="职业性有害因素检测情况"
                               style="color: #334B9A;line-height: 12px;font-weight: bold;padding:0 6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column colspan="2" style="border-bottom-color: transparent;">
                <p:dataTable id="jcSubList" paginatorPosition="bottom"
                             value="#{mgrbean.chkSmary.jcSubList}"
                             widgetVar="badRsnTable" var="jcSub"
                             emptyMessage="没有数据！" rowIndexVar="R"
                             paginator="true" rows="10"
							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							 rowsPerPageTemplate="10,20" lazy="true"
		                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
		                     pageLinks="5"
                             >

                    <p:columnGroup type="header">
                        <p:row>
                            <p:column rowspan="2" headerText="职业性有害因素" />
                            <p:column rowspan="2" headerText="工作场所" />
                            <p:column rowspan="2" headerText="岗位/工种" />
                            <p:column rowspan="2" headerText="浓/强度类型" />
                            <p:column colspan="2" headerText="检测值" />
                            <p:column rowspan="2" headerText="检测日期" />
                            <p:column rowspan="2" headerText="合格情况" />
                        </p:row>
                        <p:row>
                            <p:column  headerText="最小值" />
                            <p:column  headerText="最大值" />
                        </p:row>
                    </p:columnGroup>
                    <p:column headerText="职业性有害因素">
                        <p:outputLabel value="#{jcSub.fkByBadrsnId.codeName}" style="width:120px;"></p:outputLabel>
                    </p:column>
                    <p:column headerText="工作场所" style="width:80px;text-align:center;">
                        <p:outputLabel value="#{jcSub.workPlace}"/>
                    </p:column>
                    <p:column headerText="岗位/工种" style="text-align:center;width: 100px;">
                        <p:outputLabel value="#{jcSub.workType}" style="width: 100px;"/>
                    </p:column>
                    <p:column headerText="浓/强度类型" style="text-align:center;width: 100px;">
                        <p:outputLabel value="#{jcSub.fkByThickTypeId.codeName}" style="width: 100px;"/>
                    </p:column>
                    <p:column headerText="检测值（最小值）" style="text-align:center;width: 60px;">
                        <p:outputLabel value="#{jcSub.jcValMin}" style="width: 100px;"/>
                    </p:column>
                    <p:column headerText="检测值（最大值）" style="text-align:center;width: 60px;">
                        <p:outputLabel value="#{jcSub.jcValMax}" style="width: 100px;"/>
                    </p:column>
                    <p:column headerText="检测日期" style="text-align:center;width: 80px;">
                        <p:outputLabel value="#{jcSub.jcTime}" style="width: 100px;"/>
                    </p:column>
                    <p:column headerText="合格情况" style="text-align:center;width: 90px;">
                        <p:outputLabel value="合格" rendered="#{jcSub.hgFlag=='1'}" style="width: 100px;"/>
                        <p:outputLabel value="不合格" rendered="#{jcSub.hgFlag=='0'}" style="width: 100px;"/>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="border-right-color: transparent;border-top-color:transparent;text-align:center;">
                    <p:outputLabel value="检测单位："></p:outputLabel>
                    <p:outputLabel value="#{mgrbean.chkSmary.jcUnitName}" style="width: 100px;"/>
            </p:column>
            <p:column style="border-top-color:transparent;text-align:center;">
                    <p:outputLabel value="检测单位负责人："></p:outputLabel>
                    <p:outputLabel value="#{mgrbean.chkSmary.jcUnitCharge}" style="width: 100px;"/>
            </p:column>
        </p:row>
    </p:panelGrid>

    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="填表人信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表人："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width: 200px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fillFormPsn}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表人联系电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fillLink}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表日期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:column styleClass="noBorder" style="text-align: left;" colspan="3">
                    <h:outputText value="#{mgrbean.chkSmary.fillDate}"/>
                </p:column>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="填表单位："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByFillSysUnitId.unitname}"></p:outputLabel>
            </p:column>
        </p:row>

    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <p:row>
            <p:column colspan="4">
                <p:outputLabel value="报告人信息"
                               style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告人："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;width: 200px;">
                <p:outputLabel value="#{mgrbean.chkSmary.rptPsn}"></p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告人联系电话："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.rptLink}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告日期："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.rptDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
                </p:outputLabel>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="报告单位："></p:outputLabel>
            </p:column>
            <p:column style="padding-left: 8px;">
                <p:outputLabel value="#{mgrbean.chkSmary.fkByFillSysUnitId.unitname}"></p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title">
                <p:outputLabel value="备注："></p:outputLabel>
            </p:column>
            <p:column  style="padding-left: 8px;" colspan="3">
                <p:outputLabel rows="4" cols="100" autoResize="false" maxlength="50"
                                 value="#{mgrbean.chkSmary.rmk}" />
            </p:column>
        </p:row>
    </p:panelGrid>

</ui:composition>
