<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.SodReportCardFillInBean"-->
    <ui:param name="mgrbean" value="#{sodReportCardFillInBean}"/>
    <!-- 添加页面 -->
    <ui:param name="editPage" value="/webapp/heth/comm/reportcard/sodReportCardEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/sodReportCardView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style>
            .myCalendar1 input {
                width: 78px;
            }

            table.ui-selectoneradio td label {
                white-space: nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="疑似职业病报告卡录入"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInitAction}"
                                 process="@this" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="用工单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchCrptName}" style="width: 180px;"
                             maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="人员姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchPersonnelName}" style="width: 180px;" maxlength="25"
                             placeholder="模糊查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="* " style="color:red;" rendered="#{!mgrbean.thisUnit}"/>
                <h:outputText value="证件号码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="50" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="疑似职业病名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchPersonnelName" value="#{mgrbean.searchSodName}" style="width: 180px;"
                             maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="报告日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="reportBeginDate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="报告开始日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchReportBeginDate}"/>
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="reportEndDate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="报告结束日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar1"
                            value="#{mgrbean.searchReportEndDate}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="本单位："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" colspan="#{mgrbean.thisUnit?1:5}">
                <p:selectBooleanCheckbox value="#{mgrbean.thisUnit}" style="padding-right: 8px;user-select: none;">
                    <p:ajax event="change" update="mainGrid" process="mainGrid"/>
                </p:selectBooleanCheckbox>
                <h:outputLabel value="本单位"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;" rendered="#{mgrbean.thisUnit}">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3" rendered="#{mgrbean.thisUnit}">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="用工单位地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="人员姓名" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="证件号码" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="疑似职业病名称" style="width:120px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="报告单位" style="width:210px;padding-left: 8px;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputText value="#{itm[8]}" style="color: red;" rendered="#{'已退回' eq itm[8]}"/>
            <h:outputText value="#{itm[8]}" rendered="#{'已退回' ne itm[8]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}" process="@this" update=":tabView"
                           rendered="#{'0' eq itm[10]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rptRid}"/>
            </p:commandLink>
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" process="@this" update=":tabView"
                           rendered="#{'1' eq itm[10]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rptRid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{'1' eq itm[10]}"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}" process="@this" update=":tabView"
                           rendered="#{'1' eq itm[10]}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rptRid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
    </ui:define>
</ui:composition>