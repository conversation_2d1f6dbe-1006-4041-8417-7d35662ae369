<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">
      <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
            <p:row>
                <p:column colspan="2" ><span>本环节文书</span></p:column>
            </p:row>
            <p:row>
                <p:column style="padding: 0;" colspan="2">
                    <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width: 190px;">
                    <h:outputText value="《疑似职业病报告卡》"/>
                </p:column>
                <p:column>
                    <p:outputPanel>
                        <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.tdZwYszybRpt.annexPath}')" rendered="#{mgrbean.tdZwYszybRpt.annexPath != null}"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:panelGrid styleClass="writeSortInfo" style="margin-top: 30px;">
            <p:row>
                <p:column styleClass="noBorder" style="text-align: center;" colspan="3">
                    <h:outputText value="疑似职业病报告卡" style="line-height: 30px;font-size: 18px;font-weight: bold;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="noBorder" style="text-align: left;">
                    <h:outputText value="报告卡编码："/>
                    <h:outputText value="#{mgrbean.tdZwYszybRpt.rptNo}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;margin-top: 5px">
            <f:facet name="header">
                <p:row>
                    <p:column  style="height:20px;font-size: 20px;text-align:left;" colspan="4">
                        <p:outputLabel value="劳动者信息"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;width: 150px">
                    <p:outputLabel value="姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.personnelName}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px;">
                    <p:outputLabel value="证件类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByCardTypeId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="证件号码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.idc}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px;">
                    <p:outputLabel value="性别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.sex==1?'男':'女'}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="出生日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <h:outputLabel value="**********" rendered="#{!birthIfShow}"/>
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.birthday}" rendered="#{birthIfShow}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px;">
                    <p:outputLabel value="联系电话："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.linktel}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                    <p:outputLabel value="用人单位信息" />
                </p:column>
            </p:row>
        </f:facet>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;width: 150px">
                    <p:outputLabel value="用人单位名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.crptName}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px;">
                    <p:outputLabel value="社会信用代码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.creditCode}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="企业类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEconomyId.codeName}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="行业类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByIndusTypeId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="企业规模："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByCrptSizeId.codeName}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px;">
                    <p:outputLabel value="用人单位所在区："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByZoneId.fullName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="用人单位详细地址："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.address}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="用人单位地址邮编："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.postcode}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="用人单位联系人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.safeposition}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="用人单位联系人电话："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.safephone}"/>
                </p:column>
            </p:row>

    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                    <p:outputLabel value="用工单位信息" />
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;width: 150px">
                <p:outputLabel value="用工单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;width: 220px;">
                <p:outputLabel value="#{mgrbean.tdZwYszybRpt.empCrptName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;">
                <p:outputLabel value="#{mgrbean.tdZwYszybRpt.empCreditCode}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="企业类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;">
                <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpEconomyId.codeName}"/>
            </p:column>
            <p:column styleClass="column_title">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;">
                <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="column_title" style="height:30px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;">
                <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpCrptSizeId.codeName}"/>
            </p:column>
            <p:column styleClass="column_title" style="width: 150px;">
                <p:outputLabel value="用工单位所在区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;">
                <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByEmpZoneId.fullName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column  style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                    <p:outputLabel value="疑似职业病报告信息" />
                </p:column>
            </p:row>
        </f:facet>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;width: 150px">
                    <p:outputLabel value="疑似职业病名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByOccDiseid.codeName}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px">
                    <p:outputLabel value="信息来源："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkBySourceId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="疑似职业病种类："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="#{mgrbean.ifChemical?1:3}">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.yszybTypeName}"/>
                </p:column>
                <p:column styleClass="column_title" rendered="#{mgrbean.ifChemical}">
                    <p:outputLabel value="职业性化学中毒分类："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" rendered="#{mgrbean.ifChemical}">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.zyPoisonType==1?'急性':'慢性'}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="接触的职业性有害因素："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.tchBadrsns}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="发现日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.findDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
                    </p:outputLabel>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="接害开始日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.harmStartDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
                    </p:outputLabel>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="专业工龄："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.tchWorkYear==null?'':mgrbean.tdZwYszybRpt.tchWorkYear}"/>
                    <p:spacer width="10" />
                    <h:outputLabel value="年"/>
                    <p:spacer width="10" />
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.tchWorkMonth==null?'':mgrbean.tdZwYszybRpt.tchWorkMonth}"/>
                    <p:spacer width="10" />
                    <h:outputLabel value="月"/>
                    <p:spacer width="10" />
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.tchWorkDay==null?'':mgrbean.tdZwYszybRpt.tchWorkDay}"/>
                    <p:spacer width="10" />
                    <h:outputLabel value="日"/>
                    <p:spacer width="10" />
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.tchWorkHour==null?'':mgrbean.tdZwYszybRpt.tchWorkHour}"/>
                    <p:spacer width="10" />
                    <h:outputLabel value="时"/>
                    <p:spacer width="10" />
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.tchWorkMinute==null?'':mgrbean.tdZwYszybRpt.tchWorkMinute}"/>
                    <p:spacer width="10" />
                    <h:outputLabel value="分"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="统计工种："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 220px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkByWorkTypeId.codeName}"/>
                    <p:outputLabel value="（#{mgrbean.tdZwYszybRpt.workOther}）" rendered="#{mgrbean.tdZwYszybRpt.workOther!=null}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="发现单位："/>
                </p:column>
                <p:column style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.discoveryUnit}"/>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="发现单位负责人："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.discoveryRespPsn}"/>
                </p:column>
            </p:row>
    </p:panelGrid>
    <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                    <p:outputLabel value="填表人信息" />
                </p:column>
            </p:row>
        </f:facet>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;width: 150px">
                    <p:outputLabel value="填表人："/>
                </p:column>
                <p:column  style="text-align: left;width: 220px;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fillFormPsn}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px;">
                    <p:outputLabel value="填表人联系电话："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fillLink}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="填表日期："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fillDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
                    </p:outputLabel>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="填表单位："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkRptUnitId.unitname}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
            <f:facet name="header">
                <p:row>
                    <p:column  style="height:20px;font-size: 20px;text-align:left; " colspan="4">
                        <p:outputLabel value="报告人信息" />
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;width: 150px">
                    <p:outputLabel value="报告人："/>
                </p:column>
                <p:column  style="text-align: left;width: 220px;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.rptPsn}"/>
                </p:column>
                <p:column styleClass="column_title" style="width: 150px">
                    <p:outputLabel value="报告人联系电话："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.rptLink}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="报告日期："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.rptDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn" />
                    </p:outputLabel>
                </p:column>
                <p:column styleClass="column_title">
                    <p:outputLabel value="报告单位："/>
                </p:column>
                <p:column  style="text-align: left;padding-left:11px;">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.fkRptUnitId.unitname}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="column_title" style="height:30px;">
                    <p:outputLabel value="备注："/>
                </p:column>
                <p:column  style="text-align: left;width:240px;padding-left:11px;" colspan="3">
                    <p:outputLabel value="#{mgrbean.tdZwYszybRpt.rmk}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
</ui:composition>