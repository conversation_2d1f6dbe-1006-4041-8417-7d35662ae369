<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <link rel="stylesheet" href="/resources/css/reportCard.css" />
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput(ifView,id){
                if(ifView=="false"){
                    return;
                }
                var text;
                var $tabView ;
                if(id){
                    $tabView = $("#"+id)
                }else{
                    $tabView = $("#tabView\\:editForm");
                }
                $tabView.find("input,textarea").each(function(){
                    if($(this).attr("type")=="radio"||$(this).attr("type")=="checkbox"){
                        $(this).css("pointer-events","none");
                    }else{
                        $(this).prop("disabled",true);
                    }
                    $(this).css("opacity","1");
                });
                //单选框label的for标签处理
                $tabView.find("label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"选择"==text||"附件删除"==text||"上传附件"==text||"附件删除"==text){
                        $(this).remove();
                    }else if("查看"==text||"修改"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("扫描"==text||"预览"==text||"制作"==text||"删除"==text||"上传"==text||"添加"==text||"设计"==text||"汇总"==text){
                        $(this).remove();
                    }else if("查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 200px;
            }
        </style>
    </ui:define>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业病鉴定报告卡填报"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="9" style="border-color:transparent;padding:0px;" id="headButton">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" style="float:right;"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.beforeSubmit}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="初审退回意见" icon="icon-alert"  style="color:red;"
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 2 or (mgrbean.tdZwBgkLastSta.state == 4 and mgrbean.tsLocalZone.ifCityDirect == 1)}">
                </p:commandButton>
                <p:commandButton value="复审退回意见" icon="icon-alert"  style="color:red;"
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 4 and mgrbean.tsLocalZone.ifCityDirect != 1}">
                </p:commandButton>
                <p:commandButton value="终审退回意见" icon="icon-alert"  style="color:red;" styleClass=""
                                 process="@this" onclick="PF('ReasonDialog').show();"
                                 rendered="#{mgrbean.tdZwBgkLastSta.state == 6}">
                </p:commandButton>

                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
            <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" update=":tabView" oncomplete="PF('ConfirmDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
        </p:confirmDialog>
        <p:sticky target="sticky"></p:sticky>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:outputPanel styleClass="businessInfo">
            <p:panelGrid styleClass="writeSortPanel panelGrid-none panelGrid-right-first" id="writeSortPanel">
                <p:row>
                    <p:column colspan="2" ><span>本环节文书</span></p:column>
                </p:row>
                <p:row>
                    <p:column style="padding: 0;" colspan="2">
                        <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="width: 190px;">
                        <h:outputText value="《职业病鉴定报告卡》"/>
                    </p:column>
                    <p:column>
                        <p:outputPanel>
                            <p:commandButton value="设计" icon="ui-icon-pencil" action="#{mgrbean.designWritReport}"
                                             rendered="#{mgrbean.ifShowDesign}"
                                             process="@this,:tabView:editForm" update=":tabView:editForm">
                            </p:commandButton>
                            <p:spacer width="5" rendered="#{mgrbean.archivesCard.annexPath == null}"/>
                            <p:commandButton value="制作"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.buildWritReport}"
                                             rendered="#{mgrbean.archivesCard.annexPath == null}"
                                             update=":tabView">
                            </p:commandButton>
                            <p:spacer width="5"/>
                            <p:commandButton value="查看" process="@this"
                                             onclick="window.open('/webFile/#{mgrbean.archivesCard.annexPath}')"
                                             rendered="#{mgrbean.archivesCard.annexPath != null}">
                            </p:commandButton>
                            <p:spacer width="5"/>
                            <p:commandButton value="删除"
                                             process="@this,:tabView:editForm"
                                             action="#{mgrbean.delMadedwrit}"
                                             rendered="#{mgrbean.archivesCard.annexPath != null}"
                                             update=":tabView:editForm">
                            </p:commandButton>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <div id="archivesCardDiv">
                <!-- 标题 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 5px;" id="writeSortInfo">
                    <p:row>
                        <p:column styleClass="noBorder" style="text-align: center;">
                            <p:outputPanel style="padding-bottom:8px;">
                                <h:outputText value="职业病鉴定报告卡"
                                              style="line-height: 30px;font-size: 18px;font-weight: bold;"></h:outputText>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!--劳动者信息-->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="workerPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="劳动者信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="姓名："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:inputText    style="width: 200px" value="#{mgrbean.archivesCard.personnelName}" maxlength="25"/>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="证件类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" styleClass="lineHeight">
                            <p:selectOneMenu value="#{mgrbean.idcType}" style="width: 210px;"
                                             id="psnType">
                                <f:selectItems value="#{mgrbean.cardTypeList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,workerPanel" update="workerPanel"
                                        listener="#{mgrbean.onPsnTypeChangeAction}" />
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title" >
                            <h:outputText value="*" style="color:red;" rendered="#{mgrbean.ifIdcAble}"/>
                            <p:outputLabel value="证件号码："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:inputText  id="idc"  style="width: 200px" value="#{mgrbean.archivesCard.idc}" maxlength="20"  onblur="findFlowByIdc()" disabled="#{!mgrbean.ifIdcAble}"/>
                            <p:remoteCommand name="findFlowByIdc" action="#{mgrbean.findFlowByIdc}" process="@this,idc" update="sex,brth"/>
                        </p:column>
                        <p:column styleClass="column_title" >
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="性别："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" styleClass="lineHeight">
                            <p:selectOneRadio id="sex" value="#{mgrbean.archivesCard.sex}" >
                                <f:selectItem itemLabel="男" itemValue="1"/>
                                <f:selectItem itemLabel="女" itemValue="2"/>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="出生日期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:calendar id="brth" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="出生日期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.birthday}"
                            />
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="联系电话："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:inputText   style="width: 200px" value="#{mgrbean.archivesCard.linktel}" maxlength="20"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 基本信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="basicPanel">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="用人单位信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="用人单位名称："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 7px;" styleClass="lineHeight">
                            <table>
                                <tr>
                                    <td style="padding:0;border-color: transparent;">
                                        <p:inputText id="crptName"  readonly="true" style="width: 200px" value="#{mgrbean.archivesCard.crptName}" onclick="$('#tabView\\:editForm\\:onCrptSelect').click()"/>
                                    </td>
                                    <td style="padding:0;border-color: transparent;position: relative;left: -18px;">
                                        <p:commandLink  id="onCrptSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                                        action="#{mgrbean.selectCrptList}" process="@this" style="position: relative;left: -10px;"
                                        >
                                            <f:setPropertyActionListener value="1" target="#{mgrbean.crpyType}"/>
                                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onCrptSelect}" update="basicPanel"/>
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="社会信用代码："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText value="#{mgrbean.archivesCard.creditCode}"></h:outputText>
                        </p:column>
                    </p:row>

                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="经济类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:outputLabel value="#{mgrbean.archivesCard.fkByEconomyId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.archivesCard.fkByIndusTypeId.codeName}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="企业规模："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.archivesCard.fkByCrptSizeId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地区："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.archivesCard.fkByZoneId.fullName}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地址："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.archivesCard.address}"></h:outputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位地址邮编："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.archivesCard.postcode}"></h:outputText>
                        </p:column>

                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位联系人："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.archivesCard.safeposition}"></h:outputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用人单位联系人电话："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.archivesCard.safephone}"></h:outputText>
                        </p:column>

                    </p:row>
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="用工单位信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="用工单位名称："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 5px;" styleClass="lineHeight">
                            <table>
                                <tr>
                                    <td style="padding:0;border-color: transparent;">
                                        <p:inputText id="empCrptName"  readonly="true" style="width: 200px" value="#{mgrbean.archivesCard.empCrptName}" onclick="$('#tabView\\:editForm\\:onCrptSelect1').click()"/>
                                    </td>
                                    <td style="padding:0;border-color: transparent;position: relative;left: -18px;">
                                        <p:commandLink  id="onCrptSelect1" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                                        action="#{mgrbean.selectCrptList}" process="@this" style="position: relative;left: -10px;"
                                                        rendered="#{mgrbean.archivesCard.fkByCrptId.tsSimpleCodeByIndusTypeId.extendS1==2}"
                                        >
                                            <f:setPropertyActionListener value="2" target="#{mgrbean.crpyType}"/>
                                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onCrptSelect}" update="basicPanel"/>
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="社会信用代码："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText value="#{mgrbean.archivesCard.empCreditCode}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="经济类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;width:320px;">
                            <p:outputLabel value="#{mgrbean.archivesCard.fkByEmpEconomyId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="行业类别："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.archivesCard.fkByEmpIndusTypeId.codeName}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="企业规模："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" >
                            <p:outputLabel value="#{mgrbean.archivesCard.fkByEmpCrptSizeId.codeName}"></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="用工单位地区："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <h:outputText value="#{mgrbean.archivesCard.fkByEmpZoneId.fullName}"></h:outputText>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 职业病鉴定信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="zybjd">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="职业病鉴定信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="是否诊断为职业病："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneRadio id="ifZyb" value="#{mgrbean.archivesCard.ifZyb}">
                                <f:selectItem itemLabel="否" itemValue="0"/>
                                <f:selectItem itemLabel="是" itemValue="1"/>
                                <p:ajax event="change" process="@this,zybjd"  update="zybjd" listener="#{mgrbean.onIfZybSelect}" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row >
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业病名称："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 5px;text-align:left;width: 320px;" styleClass="lineHeight" rendered="#{mgrbean.archivesCard.ifZyb != 0}">
                            <h:panelGrid columns="4"
                                         style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                                <p:inputText id="zybType"
                                             value="#{mgrbean.archivesCard.fkByZybTypeId.codeName}"
                                             style="width: 150px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:editForm:selZybType').click();"
                                             readonly="true"/>
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selZybType"
                                               action="#{mgrbean.selectZybTypeAction}" process="@this"
                                               style="position: relative;left: -30px;" disabled="#{mgrbean.archivesCard.ifZyb !=1}">
                                    <p:ajax event="dialogReturn"
                                            listener="#{mgrbean.onZybTypeSelect}" process="@this,zybjd"
                                            resetValues="true" update="zybjd" />
                                </p:commandLink>
                                <p:inputText style="position: relative;left: -25px;width: 100px;" id="zybDisName"
                                             value="#{mgrbean.archivesCard.zybDisName}" maxlength="100"   rendered="#{mgrbean.zybjdCode.extendS2 ==1}"
                                             placeholder="" />
                            </h:panelGrid>
                        </p:column>
                        <p:column style="padding-left: 8px;" rendered="#{mgrbean.archivesCard.ifZyb == 0}">
                            <p:outputLabel  value="无" ></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业病种类："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel id="zybDisTypeId" value="#{mgrbean.archivesCard.fkByZybDisTypeId.codeName}" rendered="#{mgrbean.archivesCard.ifZyb != 0}"></p:outputLabel>
                            <p:outputLabel  value="无" rendered="#{mgrbean.archivesCard.ifZyb == 0}"></p:outputLabel>
                        </p:column>
                    </p:row>

                    <!--5026的码表扩展字段4为1显示-->
                    <p:row rendered="#{mgrbean.zybjdCode.extendS4 == '1'}" >
                        <p:column styleClass="column_title">
                            <p:outputLabel value="诊断I期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:calendar id="diagDate1" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断I期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.diag1Date}"
                            />
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="诊断Ⅱ期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:calendar id="diagDate2" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断Ⅱ期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.diag2Date}"
                            />
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.zybjdCode.extendS4 == '1'}">
                        <p:column styleClass="column_title">
                            <p:outputLabel value="诊断Ⅲ期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:calendar id="diagDate3" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断Ⅲ期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.diag3Date}"
                            />
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.zybjdCode.extendS4 == '1'}">
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="病例类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneRadio value="#{mgrbean.rptTypeId}" style="width: 520px;" id="rptTypeId">
                                <f:selectItems value="#{mgrbean.rptTypeList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,rptTypeId"  update="rptTypeId" listener="#{mgrbean.onRptTypeId}" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <!--5026的码表扩展字段4为1显示 结束-->
                    <!--5026的码表扩展字段4为2显示-->
                    <p:row rendered="#{mgrbean.zybjdCode.extendS4 == '2'}">
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业性化学中毒分类："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneRadio  value="#{mgrbean.archivesCard.zyPoisonType}" style="width: 150px;" >
                                <f:selectItem itemLabel="急性" itemValue="1"/>
                                <f:selectItem itemLabel="慢性" itemValue="2"/>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <!--5026的码表扩展字段4为2显示 结束-->
                    <p:row>
                        <!--大于出生日期-->
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="申请诊断日期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:calendar id="applyDate" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="申请诊断日期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.applyDate}"
                            />
                        </p:column>
                        <!--大于等于申请诊断日期-->
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="诊断日期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:calendar id="diagDate" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断日期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.diagDate}"
                            />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="诊断机构名称：" ></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 5px;" colspan="3" styleClass="lineHeight">
                            <table>
                                <tr>
                                    <td style="padding:0;border-color: transparent;">
                                        <p:inputText id="diagUnitName"  readonly="true" style="width: 200px" value="#{mgrbean.archivesCard.diagUnitName}" onclick="$('#tabView\\:editForm\\:onDiagUnitSelect').click()"/>
                                    </td>
                                    <td style="padding:0;border-color: transparent;position: relative;left: -18px;">
                                        <p:commandLink  id="onDiagUnitSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                                        action="#{mgrbean.selectDiagUnitList}" process="@this" style="position: relative;left: -10px;"
                                        >
                                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onDiagUnitSelect}" update="diagUnitName"/>
                                        </p:commandLink>
                                    </td>
                                </tr>
                            </table>
                        </p:column>

                    </p:row>
                    <p:row>
                        <!--大于等于诊断日期-->
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="申请鉴定日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:calendar id="applyJdDate" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="申请鉴定日期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.applyJdDate}"
                            />
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="鉴定日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;"  >
                            <p:calendar id="aprsCentDate" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="鉴定日期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.aprsCentDate}"
                            />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="鉴定类型："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneMenu value="#{mgrbean.jdType}" id="jdTypeId" style="width: 210px;">
                                <f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
                                <f:selectItems value="#{mgrbean.jdTypeList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,:tabView:editForm"    listener="#{mgrbean.changeJdType}"/>
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row >
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="首次鉴定结论：" ></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" styleClass="lineHeight">
                            <p:selectOneMenu value="#{mgrbean.jdRst}" id="sc" style="width: 210px;">
                                <f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
                                <f:selectItems value="#{mgrbean.firstJdResultList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,:tabView:editForm"   listener="#{mgrbean.changeJdRst}"/>
                            </p:selectOneMenu>
                        </p:column>
                        <!--鉴定类型选择首次鉴定（码表5543扩展字段1为1）灰掉，不可操作，再次鉴定（码表5543扩展字段1为2）时必填-->
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="再次鉴定结论："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" styleClass="lineHeight" >
                            <p:selectOneMenu value="#{mgrbean.jdAgainRstId}"
                                             disabled="#{mgrbean.archivesCard.fkByJdTypeId.extendS1 != '2'}"
                                             id="zc" style="width: 210px;">
                                <f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
                                <f:selectItems value="#{mgrbean.nextJdResultList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,:tabView:editForm"   listener="#{mgrbean.changeJdAgainRstId}"/>
                            </p:selectOneMenu>

                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="鉴定机构名称："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.archivesCard.jdUnitName}" ></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="鉴定机构社会信用代码："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel value="#{mgrbean.archivesCard.jdUnitCreditCode}" ></p:outputLabel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <!-- 职业病鉴定结果 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;" id="zybjg" rendered="#{mgrbean.ifAgainRst}">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="职业病鉴定结果"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="是否鉴定为职业病："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneRadio id="ifJdZyb" value="#{mgrbean.archivesCard.ifJdZyb}" >
                                <f:selectItem itemLabel="否" itemValue="0"/>
                                <f:selectItem itemLabel="是" itemValue="1"/>
                                <p:ajax event="change" process="@this,zybjg"  update="zybjg" listener="#{mgrbean.onIfJdZybSelect}" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row >
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业病名称："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 5px;text-align:left;width: 320px;" styleClass="lineHeight" rendered="#{mgrbean.archivesCard.ifJdZyb != 0}">
                            <h:panelGrid columns="4"
                                         style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                                <p:inputText id="jdZybType"
                                             value="#{mgrbean.archivesCard.fkByJdZybTypeId.codeName}"
                                             style="width: 150px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:editForm:selJdZybType').click();"
                                             readonly="true"/>
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selJdZybType"
                                               action="#{mgrbean.selectZybTypeAction}" process="@this"
                                               style="position: relative;left: -30px;" disabled="#{mgrbean.archivesCard.ifJdZyb !=1}">
                                    <p:ajax event="dialogReturn"
                                            listener="#{mgrbean.onJdZybTypeSelect}" process="@this,zybjg"
                                            resetValues="true" update="zybjg" />
                                </p:commandLink>
                                <p:inputText style="position: relative;left: -25px;width: 100px;" id="jdZybDisName"
                                             value="#{mgrbean.archivesCard.jdZybDisName}" maxlength="100"   rendered="#{mgrbean.zybjgCode.extendS2 ==1}"
                                             placeholder="" />
                            </h:panelGrid>
                        </p:column>
                        <p:column style="padding-left: 8px;" rendered="#{mgrbean.archivesCard.ifJdZyb == 0}">
                            <p:outputLabel  value="无" ></p:outputLabel>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业病种类："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:outputLabel id="jdZybDisTypeId" value="#{mgrbean.archivesCard.fkByJdZybDisTypeId.codeName}" rendered="#{mgrbean.archivesCard.ifJdZyb != 0}"></p:outputLabel>
                            <p:outputLabel  value="无" rendered="#{mgrbean.archivesCard.ifJdZyb == 0}"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <!--5026的码表扩展字段4为1显示-->
                    <p:row rendered="#{mgrbean.zybjgCode.extendS4 == '1'}">
                        <p:column styleClass="column_title">
                            <p:outputLabel value="诊断I期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:calendar id="jdDiagDate1" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断I期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.jdDiag1Date}"
                            />
                        </p:column>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="诊断Ⅱ期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;">
                            <p:calendar id="jdDiagDate2" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断Ⅱ期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.jdDiag2Date}"
                            />
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.zybjgCode.extendS4 == '1'}">
                        <p:column styleClass="column_title">
                            <p:outputLabel value="诊断Ⅲ期："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3">
                            <p:calendar id="jdDiagDate3" pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                        showOtherMonths="true" size="22" navigator="true" styleClass="myCalendar1"
                                        yearRange="c-70:c" converterMessage="诊断Ⅲ期，格式输入不正确！"
                                        showButtonPanel="true" maxdate="new Date()" value="#{mgrbean.archivesCard.jdDiag3Date}"
                            />
                        </p:column>
                    </p:row>
                    <p:row rendered="#{mgrbean.zybjgCode.extendS4 == '1'}">
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="病例类型："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneRadio value="#{mgrbean.jdRptTypeId}" style="width: 520px;" id="jdRptTypeId">
                                <f:selectItems value="#{mgrbean.rptTypeList}" var="itm"
                                               itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                                <p:ajax event="change" process="@this,jdRptTypeId"  update="jdRptTypeId" listener="#{mgrbean.onJdRptTypeId}" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <!--5026的码表扩展字段4为1显示 结束-->
                    <!--5026的码表扩展字段4为2显示-->
                    <p:row rendered="#{mgrbean.zybjgCode.extendS4 == '2'}">
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="职业性化学中毒分类："></p:outputLabel>
                        </p:column>
                        <p:column style="padding-left: 8px;" colspan="3" styleClass="lineHeight">
                            <p:selectOneRadio  value="#{mgrbean.archivesCard.jdZyPoisonType}" style="width: 150px;" >
                                <f:selectItem itemLabel="急性" itemValue="1"/>
                                <f:selectItem itemLabel="慢性" itemValue="2"/>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <!--5026的码表扩展字段4为2显示 结束-->
                </p:panelGrid>
                <!-- 填表人信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 10px;">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="填表人信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.archivesCard.fillFormPsn}" maxlength="25" style="width: 200px"></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表人联系电话："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.archivesCard.fillLink}" maxlength="25" style="width: 200px"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:column styleClass="noBorder" style="text-align: left;" colspan="3">
                                <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                            showOtherMonths="true" id="fillDate1" size="11" navigator="true"
                                            yearRange="c-10:c" converterMessage="填表日期，格式输入不正确！"
                                            showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                            value="#{mgrbean.archivesCard.fillDate}" />
                            </p:column>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="填表单位："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText  value="#{mgrbean.archivesCard.fillUnitName}"></h:outputText>
                        </p:column>
                    </p:row>

                </p:panelGrid>
                <!-- 报告人信息 -->
                <p:panelGrid styleClass="writeSortInfo" style="margin-bottom: 40px;">
                    <p:row>
                        <p:column colspan="4">
                            <p:outputLabel value="报告人信息"
                                           style="color: #334B9A;font-weight: bold;padding-left:6px;"></p:outputLabel>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告人："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.archivesCard.rptPsn}" maxlength="50" style="width: 200px"></p:inputText>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告人联系电话："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:inputText value="#{mgrbean.archivesCard.rptLink}" maxlength="50" style="width: 200px"></p:inputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告日期："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <p:column styleClass="noBorder" style="text-align: left;" colspan="3">
                                <p:calendar pattern="yyyy-MM-dd" maxlength="10" styleClass="myCalendar1"
                                            showOtherMonths="true" id="rptDate" size="11" navigator="true"
                                            yearRange="c-10:c" converterMessage="报告日期，格式输入不正确！"
                                            showButtonPanel="true" readonlyInput="true" maxdate="new Date()"
                                            value="#{mgrbean.archivesCard.rptDate}" />
                            </p:column>
                        </p:column>
                        <p:column styleClass="column_title">
                            <h:outputText value="*" style="color:red;" />
                            <p:outputLabel value="报告单位："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" >
                            <h:outputText  value="#{mgrbean.archivesCard.rptUnitName}"></h:outputText>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column styleClass="column_title">
                            <p:outputLabel value="备注："></p:outputLabel>
                        </p:column>
                        <p:column  style="padding-left: 8px;" colspan="3">
                            <p:inputTextarea rows="5" autoResize="false"
                                             style="resize: none;width: 600px;"
                                             value="#{mgrbean.archivesCard.rmk}"
                                             maxlength="100"/>
                        </p:column>
                    </p:row>

                </p:panelGrid>
            </div>
        </p:outputPanel>

        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="300" header="退回原因" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.countAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 2}"/>
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.cityAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 4}"/>
            <p:inputTextarea value="#{mgrbean.tdZwBgkLastSta.proAuditAdv}"
                             style="resize:none;width:97%;height:95%;" autoResize="false"
                             maxlength="100" readonly="true"
                             rendered="#{mgrbean.tdZwBgkLastSta.state == 6}"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         onclick="PF('ReasonDialog').hide();" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <ui:include src="/WEB-INF/templates/system/frpt.xhtml">
            <ui:param name="printBackingBean" value="#{tdZwHethAppraisalRptCardListBean}"/>
        </ui:include>
        <ui:include src="/WEB-INF/templates/system/frpt2.xhtml">
            <ui:param name="updateId" value=":tabView:editForm"/>
            <ui:param name="printBackingBean" value="#{tdZwHethAppraisalRptCardListBean}"/>
        </ui:include>
    </ui:define>
</ui:composition>
