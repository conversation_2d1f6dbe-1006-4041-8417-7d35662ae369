<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:t="http://java.sun.com/jsf/core"
                template="/WEB-INF/templates/system/mainTemplate_selection.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdZwjdArchivesCardCheckListBean}" />
    <!-- 审核、详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/reportcard/tdZwjdArchivesCardCheckView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            function  disabledBtn() {
                document.getElementById("tabView:mainForm:reviewBatchOk").setAttribute("disabled", true);
            }
        </script>
        <style>
            .myCalendar1 input{
                width: 78px;
            }
            table.ui-selectoneradio td label{
                white-space:nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业病鉴定报告卡审核"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,mainGrid" oncomplete="datatableOffClick();"/>
                <p:commandButton value="批量审核" icon="ui-icon-check"
                                 action="#{mgrbean.openReviewConfirmDialog}"
                                  />

            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="提示：" style="color:red;"/>
                <h:outputText value="#{mgrbean.tipInfo}" style="color:blue;"/>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="鉴定机构地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 0px;width:280px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.zoneList}"
                                       zoneCodeNew="#{mgrbean.searchZoneCode}"
                                       zoneName="#{mgrbean.searchZoneName}" />
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width: 280px;" >
                <p:inputText value="#{mgrbean.searchUnitName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用工单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" >
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchPsnName}" style="width: 180px;" maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="证件号码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;" >
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="50" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="鉴定类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
            	<p:selectManyCheckbox value="#{mgrbean.searchJdTypes}">
            		<f:selectItems value="#{mgrbean.jdTypeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"></f:selectItems>
            	</p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="鉴定日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
            	<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchJdBdate}"
            		endDate="#{mgrbean.searchJdEdate}"/>
            </p:column>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="首次鉴定结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" >
            	<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectFirstJdResultNames}"
				                        selectedIds="#{mgrbean.selectFirstJdResultIds}"
				                        simpleCodeList="#{mgrbean.firstJdResultList}"
				                        panelWidth="190"
				                        height="200">
                </zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="再次鉴定结论：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;" >
            	<zwx:SimpleCodeManyComp codeName="#{mgrbean.selectNextJdResulNames}"
				                        selectedIds="#{mgrbean.selectNextJdResulIds}"
				                        simpleCodeList="#{mgrbean.nextJdResultList}"
				                        panelWidth="210"
				                        height="200">
                </zwx:SimpleCodeManyComp>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
            	<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptBdate}"
            								  endDate="#{mgrbean.searchRptEdate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="接收日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;">
            	<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRcvBdate}"
            								  endDate="#{mgrbean.searchRcvEdate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column selectionMode="multiple" style="width:2%;text-align:center;" disabledSelection="#{!((4==mgrbean.zoneType and itm[12]==1) or (3==mgrbean.zoneType and itm[12]==3) or (2==mgrbean.zoneType and itm[12]==5) or (3==mgrbean.zoneType and itm[12]==5 and mgrbean.checkLevel eq 2))}"/>
        <p:column headerText="鉴定机构地区" style="width: 180px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="用人单位名称" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="姓名" style="width:80px;text-align:center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="证件号码" style="width:120px;text-align:center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="用工单位名称" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="鉴定类型" style="width:80px;text-align:center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="鉴定日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="首次鉴定结论" style="width:120px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[8]}"/>
        </p:column>
        <p:column headerText="再次鉴定结论" style="width:120px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="报告单位" style="width:180px;padding-left: 8px;">
            <h:outputText value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="报告日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[11]}"/>
        </p:column>
        <p:column headerText="接收日期" style="width:80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[13]}" rendered="#{mgrbean.level == 1}">
            	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"></f:convertDateTime>
            </h:outputText>
            <h:outputText value="#{itm[14]}" rendered="#{mgrbean.level == 2}">
            	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"></f:convertDateTime>
            </h:outputText>
            <h:outputText value="#{itm[15]}" rendered="#{mgrbean.level == 3}">
            	<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"></f:convertDateTime>
            </h:outputText>
        </p:column>
        <p:column headerText="审核期限" style="width:80px;padding-left: 8px;text-align: center;">
        	<p:outputPanel rendered="#{itm[16]!=null}">
	            <p:outputLabel rendered="#{itm[16] lt 0}" style="padding:3px;background:#D0021B;border-radius:2px">
	               <h:outputText value="已超期" style="color:#FFFFFF"/>
	            </p:outputLabel>
	            <p:outputLabel rendered="#{itm[16] eq 1}" style="padding:3px;background:#EB7E10;border-radius:2px;">
	               <h:outputText value="当天截止" style="color:#FFFFFF"/>
	            </p:outputLabel>
	            <p:outputLabel rendered="#{itm[16] gt 1}" style="padding:3px;background:#2F6EA0;border-radius:2px;">
	               <h:outputText value="剩余#{itm[16]}天" style="color:#FFFFFF"/>
	            </p:outputLabel>
        	</p:outputPanel>
        </p:column>
        <p:column headerText="状态" style="width:60px;text-align:center;">
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '3'}">
                <h:outputText value="待提交" rendered="#{itm[12]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[12]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[12]==2}" style="color: red;"/>
                <h:outputText value="市级待审" rendered="#{itm[12]==3}"/>
                <h:outputText value="市级退回" rendered="#{itm[12]==4}" style="color: red;"/>
                <h:outputText value="省级待审" rendered="#{itm[12]==5}"/>
                <h:outputText value="省级退回" rendered="#{itm[12]==6}" style="color: red;"/>
                <h:outputText value="省级审核通过" rendered="#{itm[12]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2' and mgrbean.platVersion eq '1'}">
                <h:outputText value="待提交" rendered="#{itm[12]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[12]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[12]==2}" style="color: red;"/>
                <h:outputText value="市级待审" rendered="#{itm[12]==5}"/>
                <h:outputText value="市级退回" rendered="#{itm[12]==6}" style="color: red;"/>
                <h:outputText value="市级通过" rendered="#{itm[12]==7}"/>
            </p:outputPanel>
            <p:outputPanel rendered="#{mgrbean.checkLevel eq '2' and mgrbean.platVersion eq '2'}">
                <h:outputText value="待提交" rendered="#{itm[12]==0}"/>
                <h:outputText value="区县级待审" rendered="#{itm[12]==1}"/>
                <h:outputText value="区县级退回" rendered="#{itm[12]==2}" style="color: red;"/>
                <h:outputText value="省级待审" rendered="#{itm[12]==5}"/>
                <h:outputText value="省级退回" rendered="#{itm[12]==6}" style="color: red;"/>
                <h:outputText value="省级通过" rendered="#{itm[12]==7}"/>
            </p:outputPanel>
        </p:column>
        <p:column headerText="操作" style="text-align:center;width:50px;">
            <p:commandLink value="审核" rendered="#{((4==mgrbean.zoneType and itm[12]==1) or (3==mgrbean.zoneType and itm[12]==3) or (2==mgrbean.zoneType and itm[12]==5) or (3==mgrbean.zoneType and itm[12]==5 and mgrbean.checkLevel eq 2))}" action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="0" target="#{mgrbean.ifIsCheckout}"/>
            </p:commandLink>
            <p:commandLink value="详情" process="@this"  update=":tabView"  action="#{mgrbean.viewInitAction}" rendered="#{!((4==mgrbean.zoneType and itm[12]==1) or (3==mgrbean.zoneType and itm[12]==3) or (2==mgrbean.zoneType and itm[12]==5) or (3==mgrbean.zoneType and itm[12]==5 and mgrbean.checkLevel eq 2))}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="1" target="#{mgrbean.ifIsCheckout}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <!-- 批量审核弹出框 -->
        <p:confirmDialog id="reviewConfirmDialog" message="确定要批量审核通过吗？" header="消息确认框" widgetVar="ReviewConfirmDialog" >
            <p:commandButton value="确定" action="#{mgrbean.reviewBatchAction}" id="reviewBatchOk"
                             update="@this,dataTable" icon="ui-icon-check" process="@this,mainGrid"
                             oncomplete="PF('ReviewConfirmDialog').hide();datatableOffClick();" onclick="disabledBtn()"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('ReviewConfirmDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>
</ui:composition>
