<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.HethInspectDataTrackBean"-->
    <ui:param name="mgrbean" value="#{hethInspectDataTrackBean}"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/hethInspectDataTrackView.xhtml"/>
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function windowScrollTop() {
                window.scrollTo(0, 0);
            }
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 77px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业健康检查数据上传情况跟踪"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 process="@this,mainGrid" action="#{mgrbean.searchAction}"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
                <p:commandButton value="全部上传" icon="ui-icon-check" id="uploadAllDataBtn"
                                 process="@this,mainGrid" action="#{mgrbean.uploadAllDataAction}"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();">
                    <f:setPropertyActionListener value="#{2}" target="#{mgrbean.uploadActionType}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;width:153px;">
                <h:outputText value="检查机构地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 8px;width:260px;" id="zoneCol">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.searchZoneList}"
                                       zoneCode="#{mgrbean.searchCheckOrgZoneGb}"
                                       zoneName="#{mgrbean.searchCheckOrgZoneName}"
                                       zonePaddingLeft="0" onchange="#{mgrbean.clearCheckOrg()}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;width:153px;">
                <h:outputText value="检查机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;width:260px;">
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="checkOrgName" style="width: 180px;cursor: pointer;"
                                             value="#{mgrbean.searchCheckOrgName}"
                                             onclick="document.getElementById('tabView:mainForm:selCheckOrgLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search" id="selCheckOrgLink"
                                               style="position: relative;left: -28px !important;"
                                               action="#{mgrbean.selCheckOrgAction}"
                                               process="@this,:tabView:mainForm:zoneCol">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectCheckOrgAction}"
                                            process="@this" update="checkOrgName" resetValues="true"/>
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="checkOrgName" action="#{mgrbean.clearCheckOrg}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:153px;">
                <h:outputText value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;">
                <p:inputText id="searchCrptName" style="width: 180px;" maxlength="50"
                             value="#{mgrbean.searchCrptName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="人员姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchPsnName" value="#{mgrbean.searchPsnName}" style="width: 180px;"
                             maxlength="100"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="体检类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectManyCheckbox value="#{mgrbean.searchBhkTypes}">
                    <f:selectItem itemValue="3" itemLabel="职业健康检查"/>
                    <f:selectItem itemValue="4" itemLabel="放射卫生健康检查"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="监测类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectManyCheckbox value="#{mgrbean.searchJcTypes}">
                    <f:selectItem itemValue="1" itemLabel="常规监测"/>
                    <f:selectItem itemValue="2" itemLabel="主动监测"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="在岗状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:2px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectOnGuardNames}"
                                        selectedIds="#{mgrbean.selectOnGuardIds}"
                                        simpleCodeList="#{mgrbean.onGuardList}"
                                        height="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="体检危害因素："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectBadRsnNames}"
                                        selectedIds="#{mgrbean.selectBadRsnIds}"
                                        simpleCodeList="#{mgrbean.badRsnList}"
                                        ifTree="true" ifSelectParent="false"
                                        inputWidth="180"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="报告出具日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:7px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchRptPrintBeginDate}"
                                              endDate="#{mgrbean.searchRptPrintEndDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="体检日期："/>

            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchBhkBeginDate}"
                                              endDate="#{mgrbean.searchBhkEndDate}"
                                              styleClass="myCalendar1"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="是否复检："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectManyCheckbox value="#{mgrbean.searchIfRhks}">
                    <f:selectItem itemValue="1" itemLabel="是"/>
                    <f:selectItem itemValue="0" itemLabel="否"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="是否异常："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectManyCheckbox value="#{mgrbean.searchIfAbnormals}">
                    <f:selectItem itemValue="1" itemLabel="是"/>
                    <f:selectItem itemValue="0" itemLabel="否"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.ifNeedBhkAudit}">
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="审核状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchStates}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="上传国家状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.searchUpCountryStates}">
                    <f:selectItems value="#{mgrbean.upCountryStateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="国家失败原因："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
                <p:inputText id="selectErrMsg" value="#{mgrbean.selectErrMsg}" style="width: 727px;"
                             maxlength="1000"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="检查机构地区" style="width: 150px;">
            <p:outputLabel value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="检查机构" style="width: 200px;">
            <p:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="人员姓名" style="width: 100px;text-align: center;">
            <p:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width: 200px;">
            <p:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="在岗状态" style="width: 70px;text-align: center;">
            <p:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="监测类型" style="width: 80px;text-align: center;">
            <p:outputLabel value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="是否复检" style="width: 70px;text-align: center;">
            <p:outputLabel value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="体检日期" style="width: 90px;text-align: center;">
            <p:outputLabel value="#{itm[8]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="是否异常" style="width: 70px;text-align: center;">
            <p:outputLabel value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="审核状态" style="width: 100px;text-align: center;" rendered="#{mgrbean.ifNeedBhkAudit}">
            <p:outputLabel value="#{itm[10]}"/>
        </p:column>
        <p:column headerText="退回原因" style="width: 200px;" rendered="#{mgrbean.ifNeedBhkAudit}">
            <p:outputLabel id="backRsn" value="#{itm[11]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="backRsn" value="#{itm[11]}"
                       style="max-width:300px;word-break:break-all;word-wrap:break-word;"/>
        </p:column>
        <p:column headerText="上传国家状态" style="width: 100px;text-align: center;">
            <p:outputLabel value="#{itm[14]}"/>
        </p:column>
        <p:column headerText="国家失败原因" style="width: 200px;">
            <p:outputLabel id="errMsg" value="#{itm[12]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="errMsg" value="#{itm[12]}"
                       style="max-width:300px;word-break:break-all;word-wrap:break-word;"/>
        </p:column>
        <p:column headerText="操作" style="width: 110px;">
            <p:commandLink value="重新上传" process="@this" resetValues="true"
                           action="#{mgrbean.reUploadDataBeforeAction()}" rendered="#{itm[16] eq '1'}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <f:setPropertyActionListener value="#{1}" target="#{mgrbean.uploadActionType}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[16] eq '1'}"/>
            <p:commandLink value="详情" process="@this" update=":tabView" resetValues="true"
                           action="#{mgrbean.viewInitAction}" onclick="hideTooltips();" oncomplete="windowScrollTop();">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <!-- 其他内容 -->
    <ui:define name="insertOtherMainContents">
        <p:confirmDialog message="确定要重新上传吗？" header="消息确认框" widgetVar="ReUnloadDataDialog">
            <p:commandButton value="确定" action="#{mgrbean.reUploadData()}"
                             update=":tabView" icon="ui-icon-check" onclick="zwx_loading_start();"
                             oncomplete="zwx_loading_stop();PF('ReUnloadDataDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('ReUnloadDataDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
        <p:confirmDialog message="确定要全部上传吗？" header="消息确认框" widgetVar="AllUnloadDataDialog">
            <p:commandButton value="确定" action="#{mgrbean.allUploadData()}"
                             update=":tabView" icon="ui-icon-check" onclick="zwx_loading_start();"
                             oncomplete="zwx_loading_stop();PF('AllUnloadDataDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('AllUnloadDataDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>
</ui:composition>