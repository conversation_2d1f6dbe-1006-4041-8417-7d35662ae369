<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdTjBhkYszybReportListBean}"/>

    <!-- 编辑页面 -->

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }
            function hideStatus() {
                PF('StatusDialog').hide();
            }
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            //<![CDATA[
            function getDownloadFileClick(){
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }
            //]]>
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
            a:hover {
                color: #25AAE1;
                text-decoration: none;
            }
            .icon-alert{
            	 background-image: url(/resources/images/alert-tip.png) !important;
  				 background-size: 12px 12px;
				 margin-left: 3px;
				 margin-top: -6px !important;
            }
            .ui-chkbox{
                margin-top: 4px;
            }
            .ui-radiobutton-box{
                margin-top:3px;
            }
            .rowBackNone tr{
                background: none;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检疑似职业病报告情况"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportReturnDataBtn"
                                 action="#{mgrbean.exportReturnDataAction}" process="@this,mainGrid" />
                <p:commandButton style="display: none;"  id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="检查机构地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.searchZoneList}"
                                       zoneCodeNew="#{mgrbean.searchZoneCode}"
                                       zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="职业健康检查机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:6px;width:250px;">
                <!-- 弹出框 -->
                <p:outputPanel >
                    <table>
                        <tr>
                            <td style="padding: 0;border-color: transparent;">
                                <p:inputText id="unitName"
                                             value="#{mgrbean.searchUnitName}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('tabView:mainForm:selUnitLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selUnitLink"
                                               action="#{mgrbean.selUnitAction}" process="@this,mainGrid"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}" process="@this"
                                            resetValues="true" update="unitName" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -30px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="unitName" action="#{mgrbean.clearUnit}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height:33px;">
                <h:outputText value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px; " >
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchDateStart}" endDate="#{mgrbean.searchDateEnd}"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="检查机构地区" style="width: 350px;padding-left: 8px;" >
            <h:outputText value="#{itm[0]}" />
        </p:column>
        <p:column headerText="职业健康检查机构" style="padding-left: 8px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="疑似职业病例数" style="width:100px;text-align: center;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="未报告数" style="width:100px;text-align:center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="报告数" style="width:100px;text-align:center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="报告率（%）" style="width:100px;text-align:center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="报告及时率（%）" style="width:130px;text-align:center;" >
            <h:outputText value="#{itm[7]}"/>
        </p:column>
    </ui:define>

</ui:composition>