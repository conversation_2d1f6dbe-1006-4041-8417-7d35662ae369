<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="tjBhkInfoBean" type="com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean"-->
<h:form id="viewForm">
<h:outputStylesheet library="css" name="ui-tabs.css" />
<p:panelGrid style="width:100%;margin-bottom:5px;" id="viewTitleGrid" rendered="#{pageParam == null}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="体检详情"/>
            </p:column>
        </p:row>
    </f:facet>
</p:panelGrid>
<ui:insert name="titleContent"></ui:insert>
<ui:insert name="buttons"></ui:insert>
<p:outputPanel styleClass="zwx_toobar_42" rendered="#{pageParam == null}">
    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
        <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.forwardEditPage}"
                         update=":tabView" immediate="true"/>
    </h:panelGrid>
</p:outputPanel>
<ui:insert name="editContent"></ui:insert>
    <!--用人单位-->
    <p:panelGrid style="margin-top: 10px;width:100%" >
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="用人单位情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="所属地区："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 180px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;">
                <p:outputLabel value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.institutionCode}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="是否分支机构："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="是" rendered="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.ifSubOrg == 1}"/>
                <p:outputLabel value="否" rendered="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.ifSubOrg ne 1}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.tsSimpleCodeByEconomyId.codeName}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.tsSimpleCodeByCrptSizeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndepend ==null ? '' : mgrbean.tjBhkInfoBean.tbTjCrptIndepend.linkman2}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="联系人电话："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndepend == null ?  '' : mgrbean.tjBhkInfoBean.tbTjCrptIndepend.linkphone2}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="职工总人数："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.workForce}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="接触职业病危害因素人数："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.holdCardMan}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="详细地址："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.address}"/>
            </p:column>
        </p:row>

        <p:row rendered="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.ifSubOrg == 1}">
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="上级单位地区："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.fkByUpperUnitId.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="上级单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.fkByUpperUnitId.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="上级单位行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.fkByUpperUnitId.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--用工单位-->
    <p:panelGrid style="margin-top: 10px;width:100%">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; " colspan="6">
                    <p:outputLabel value="用工单位情况"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="所属地区："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 180px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;">
                <p:outputLabel value="用工单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.institutionCode}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="是否分支机构："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="是" rendered="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.ifSubOrg == 1}"/>
                <p:outputLabel value="否" rendered="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.ifSubOrg ne 1}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.tsSimpleCodeByEconomyId.codeName}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.tsSimpleCodeByCrptSizeId.codeName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust ==null ? '' : mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust.linkman2}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="联系人电话："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust == null ?  '' : mgrbean.tjBhkInfoBean.tbTjCrptIndependEntrust.linkphone2}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="职工总人数："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.workForce}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="接触职业病危害因素人数："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.holdCardMan}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="详细地址："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.address}"/>
            </p:column>
        </p:row>

        <p:row rendered="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.ifSubOrg == 1}">
            <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
                <p:outputLabel value="上级单位地区："/>
            </p:column>
            <p:column style="text-align:left;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.fkByUpperUnitId.tsZoneByZoneId.fullName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px;">
                <p:outputLabel value="上级单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;width: 210px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.fkByUpperUnitId.crptName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 180px">
                <p:outputLabel value="上级单位行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-right:3px;">
                <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByEntrustCrptId.fkByUpperUnitId.tsSimpleCodeByIndusTypeId.codeName}"/>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--体检基本信息-->
    <p:panelGrid style="width:100%;margin-top:10px;">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="体检基本信息"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 180px;">
            <h:outputLabel value="体检编号："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 210px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.bhkCode}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 180px;">
            <h:outputLabel value="姓名："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 210px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.personName}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 180px">
            <h:outputLabel value="性别："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.sex}"/>
        </p:column>

    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="证件号码："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.idc}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="出生日期："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="**********" rendered="#{!birthIfShow}">
            </h:outputLabel>
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.brth}" rendered="#{birthIfShow}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="婚姻状况："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.isxmrd=='否'?'未婚':(tjBhkInfoBean.tdTjBhkShow.isxmrd=='是'?'已婚':tjBhkInfoBean.tdTjBhkShow.isxmrd)}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="单位名称："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjCrpt.crptName}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="所属部门："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.dpt}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="在岗状态："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tsSimpleCode.codeName}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="体检类型："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 1}" value="从业体检"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 2}" value="职业普通体检"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 3}" value="职业健康检查"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 4}" value="放射卫生体检"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 5}" value="学校卫生体检"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 6}" value="福利性体检"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="体检机构："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tbTjSrvorg.unitName}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="体检日期："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.bhkDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="工种："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.workName}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="总工龄："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.wrklnt}">
                <f:convertNumber type="number" minFractionDigits="0"/>
            </h:outputLabel>
            <h:outputLabel value="年" rendered="#{null!=tjBhkInfoBean.tdTjBhkShow.wrklnt}"/>
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.wrklntmonth}"/>
            <h:outputLabel value="月" rendered="#{null!=tjBhkInfoBean.tdTjBhkShow.wrklntmonth}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="接害工龄："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tchbadrsntim}">
                <f:convertNumber type="number" minFractionDigits="0"/>
            </h:outputLabel>
            <h:outputLabel value="年" rendered="#{null!=tjBhkInfoBean.tdTjBhkShow.tchbadrsntim}"/>
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tchbadrsnmonth}"/>
            <h:outputLabel value="月" rendered="#{null!=tjBhkInfoBean.tdTjBhkShow.tchbadrsnmonth}"/>
        </p:column>
    </p:row>
    <p:row>
    	<p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="报告出具日期："/>
        </p:column>
         <p:column style="text-align:left;padding-right:3px;">
        	<h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.rptPrintDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
         </p:column>
    	<p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="报告日期："/>
        </p:column>
         <p:column style="text-align:left;padding-right:3px;">
        	<h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.createDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
         </p:column>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="监测类型："/>
        </p:column>
        <p:column  style="text-align:left;padding-right:3px;">
            <h:outputLabel value="常规监测" rendered="#{tjBhkInfoBean.tdTjBhkShow.jcType==1}"/>
            <h:outputLabel value="主动监测" rendered="#{tjBhkInfoBean.tdTjBhkShow.jcType==2}"/>

        </p:column>
    </p:row>
    <p:row>
    	<p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="是否复查："/>
        </p:column>
         <p:column style="text-align:left;padding-right:3px;">
             <h:outputLabel value="是" rendered="#{tjBhkInfoBean.tdTjBhkShow.ifRhk != null and tjBhkInfoBean.tdTjBhkShow.ifRhk==1}"/>
             <h:outputLabel value="否" rendered="#{tjBhkInfoBean.tdTjBhkShow.ifRhk == null or tjBhkInfoBean.tdTjBhkShow.ifRhk!=1}"/>
         </p:column>
    	<p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="联系电话："/>
        </p:column>
         <p:column style="text-align:left;padding-right:3px;" colspan="#{(tjBhkInfoBean.tdTjBhkShow.jcType != null and tjBhkInfoBean.tdTjBhkShow.jcType eq '2') ? '1' : '3'}">
             <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.lnktel}"/>
         </p:column>
        <p:column style="text-align:right;padding-right:3px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.jcType != null and tjBhkInfoBean.tdTjBhkShow.jcType eq '2'}">
            <h:outputLabel value="防护用品佩戴情况："/>
        </p:column>
        <p:column  style="text-align:left;padding-right:3px;"  rendered="#{tjBhkInfoBean.tdTjBhkShow.jcType != null and tjBhkInfoBean.tdTjBhkShow.jcType eq '2'}">
            <p:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.fkByProtectEquId.codeName}"/>
        </p:column>
    </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px">
                <h:outputLabel value="接触危害因素："/>
            </p:column>
            <p:column  style="text-align:left;padding-right:3px;" colspan="#{tjBhkInfoBean.tdTjBhkShow.tchOtherBadrsn != null ? '3' : '5'}">
                <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tchBadrsn}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.tchOtherBadrsn != null}">
                <h:outputLabel value="接触的其他危害因素名称："/>
            </p:column>
            <p:column  style="text-align:left;padding-right:3px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.tchOtherBadrsn != null}" >
                <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.tchOtherBadrsn}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px">
                <h:outputLabel value="体检危害因素："/>
            </p:column>
            <p:column  style="text-align:left;padding-right:3px;" colspan="#{tjBhkInfoBean.tdTjBhkShow.otherBadRsn != null ? '3' : '5'}">
                <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.bhkBadrsn}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.otherBadRsn != null}">
                <h:outputLabel value="体检的其他危害因素名称："/>
            </p:column>
            <p:column  style="text-align:left;padding-right:3px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.otherBadRsn != null}" >
                <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.otherBadRsn}"/>
            </p:column>
        </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="体检结果："/>
        </p:column>
        <p:column colspan="5" style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.bhkrst}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="主检结论："/>
        </p:column>
        <p:column colspan="5" style="text-align:left;padding-right:3px;">
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.tdTjMhkrsts!=null and tjBhkInfoBean.tdTjBhkShow.tdTjMhkrsts.size()>0}" value="#{tjBhkInfoBean.tdTjBhkShow.tdTjMhkrsts.get(0).tsSimpleCode.codeDesc}"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.dataSource == 2}" value="（"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.dataSource == 2}" value="#{tjBhkInfoBean.tdTjBhkShow.ocpBhkrstdes}"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjBhkShow.dataSource == 2}" value="）"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="主检建议："/>
        </p:column>
        <p:column colspan="5" style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjBhkShow.mhkadv}"/>
        </p:column>
    </p:row>
</p:panelGrid>
<p:panelGrid style="width:100%;margin-top:10px;">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="危害因素结论明细"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
    	<p:column style="width: 100%">
    		<p:dataTable var="itm" value="#{tjBhkInfoBean.clusionDTOs}"
                         emptyMessage="没有数据" rowIndexVar="R">
               <p:column headerText="序号" style="width:60px;text-align: center">
                   <h:outputLabel value="#{R+1}"/>
               </p:column>
               <p:column headerText="体检危害因素" style="width:320px;">
               		<h:outputLabel value="#{itm.badRsns}"/>
               </p:column> 
               <p:column headerText="体检结论" style="width:100px;text-align: center">
               		<h:outputLabel value="#{itm.fkByConclusionId.codeName}"/>
               </p:column>  
               <p:column headerText="病种名称" style="width:300px;">
               		<h:outputLabel value="#{null==itm.diseNames?'/':itm.diseNames}"/>
               </p:column>
                <p:column headerText="其他疾病或异常描述" style="width:500px;">
                    <h:outputLabel value="#{null==itm.qtjbName ? '/':itm.qtjbName}"/>
                </p:column>
                <p:column headerText="接触相应职业病危害因素的用人单位名称" style="width:300px;" rendered="#{tjBhkInfoBean.ifShowUnit}">
               		<h:outputLabel value="#{null==itm.unitNames ? '/':itm.unitNames}"/>
               </p:column>
            </p:dataTable>
    	</p:column>
    </p:row>
</p:panelGrid>
<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column style="text-align:left;height: 20px;">
                <p:outputLabel value="人员职业史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="width: 100%">
            <p:dataTable var="Emhistoriesitm" value="#{tjBhkInfoBean.tdTjEmhistoryList}"
                         emptyMessage="没有数据">
                <p:column headerText="起止日期" style="width:140px;text-align: center">
                    <h:outputLabel value="#{Emhistoriesitm.stastpDate}"/>
                </p:column>
                <p:column headerText="工作单位名称" style="width:180px;padding-left: 5px;">
                    <h:outputLabel value="#{Emhistoriesitm.unitName}"/>
                </p:column>
                <p:column headerText="部门车间" style="width:100px;text-align: center">
                    <h:outputLabel value="#{Emhistoriesitm.department}"/>
                </p:column>
                <p:column headerText="工种" style="width:100px;text-align: center">
                    <h:outputLabel value="#{Emhistoriesitm.workType}"/>
                </p:column>
                <p:column headerText="危害因素" style="width:200px;padding-left: 5px;">
                    <h:outputLabel value="#{Emhistoriesitm.prfraysrt}"/>
                </p:column>
                <p:column headerText="防护措施" style="padding-left: 5px;">
                    <h:outputLabel value="#{Emhistoriesitm.defendStep}"/>
                </p:column>
            </p:dataTable>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.bhkType == 4 and !tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="4" style="text-align:left;height: 20px;">
                <p:outputLabel value="人员放射职业史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="width: 100%">
            <p:dataTable var="TjEmhistoriesitm2" value="#{tjBhkInfoBean.tdTjEmhistoryList2}"
                         emptyMessage="没有数据">
                <p:column headerText="起止日期" style="width:120px;text-align: center">
                    <h:outputLabel value="#{TjEmhistoriesitm2.stastpDate}"/>
                </p:column>
                <p:column headerText="工作单位名称" style="width:150px;padding-left: 5px;">
                    <h:outputLabel value="#{TjEmhistoriesitm2.unitName}"/>
                </p:column>
                <p:column headerText="部门车间" style="width:100px;text-align: center">
                    <h:outputLabel value="#{TjEmhistoriesitm2.department}"/>
                </p:column>
                <p:column headerText="工种" style="width:100px;text-align: center">
                    <h:outputLabel value="#{TjEmhistoriesitm2.workType}"/>
                </p:column>
                <p:column headerText="放射线种类" style="width:100px;text-align: center">
                    <h:outputLabel value="#{TjEmhistoriesitm2.fsszl}"/>
                </p:column>
                <p:column headerText="每日工作时数或工作量" style="width:100px;text-align: center">
                    <h:outputLabel value="#{TjEmhistoriesitm2.prfwrklod}"/>
                </p:column>
                <p:column headerText="累积受照剂量" style="width:100px;text-align: center">
                    <h:outputLabel value="#{TjEmhistoriesitm2.prfshnvlu}"/>
                </p:column>
                <p:column headerText="过量照射史" style="padding-left: 5px;">
                    <h:outputLabel value="#{TjEmhistoriesitm2.prfexcshn}"/>
                </p:column>
            </p:dataTable>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="4" style="text-align:left;height: 20px;">
                <p:outputLabel value="既往病史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="width: 100%">
            <p:dataTable var="TjAnamnesisesitm" value="#{tjBhkInfoBean.tdTjBhkShow.tdTjAnamnesises}"
                         emptyMessage="没有数据">
                <p:column headerText="疾病名称" style="width:150px;padding-left: 5px;">
                    <h:outputLabel value="#{TjAnamnesisesitm.hstnam}"/>
                </p:column>
                <p:column headerText="诊断日期" style="width:120px;text-align: center">
                    <h:outputLabel value="#{TjAnamnesisesitm.hstdat}"/>
                </p:column>
                <p:column headerText="诊断单位" style="width:180px;padding-left: 5px;">
                    <h:outputLabel value="#{TjAnamnesisesitm.hstunt}"/>
                </p:column>
                <p:column headerText="治疗经过" style="width:280px;padding-left: 5px;">
                    <h:outputLabel value="#{TjAnamnesisesitm.hstcruprc}"/>
                </p:column>
                <p:column headerText="转归" style="padding-left: 5px;">
                    <h:outputLabel value="#{TjAnamnesisesitm.hstlps}"/>
                </p:column>
            </p:dataTable>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.sex == '女' and !tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="月经史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="初潮(岁)："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width:160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.mnrage}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="经期（天）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width:160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.mns}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="周期（天）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.cyc}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="停经年龄（岁）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.mnlage}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="是否经期："/>
        </p:column>
        <p:column colspan="3" style="text-align:left;padding-right:3px;">
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjExmsdata.isxmns == 0}" value="否"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjExmsdata.isxmns == 1}" value="是"/>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="婚姻史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="结婚日期："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width:160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.mrydat}" />
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="配偶接触放射线情况："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.cplrdtcnd}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="配偶职业及健康状况："/>
        </p:column>
        <p:column colspan="3" style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.cplprfhthcnd}"/>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{tjBhkInfoBean.tdTjBhkShow.sex == '女' and !tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="生育史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="现有子女（人）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.chldqty}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="流产（次）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.abrqty}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="早产（次）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.slnkqty}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="死产（次）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.stlqty}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel value="畸胎（次）："/>
        </p:column>
        <p:column colspan="3" style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.trsqty}"/>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="吸烟史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="目前吸烟情况："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.fkBySmkstaId eq null ? null : tjBhkInfoBean.tdTjExmsdata.fkBySmkstaId.codeName}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="吸烟史（年）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.smkyerqty}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="吸烟史（月）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.smkmthqty}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px">
            <h:outputLabel value="平均每天吸烟量（支）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;" colspan="5">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.smkdayble}"/>
        </p:column>
    </p:row>
</p:panelGrid>
<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="6" style="text-align:left;height: 20px;">
                <p:outputLabel value="饮酒史"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="饮酒情况："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 160px;">
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjExmsdata.winsta == 0}" value="不饮酒"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjExmsdata.winsta == 1}" value="偶尔饮"/>
            <h:outputLabel rendered="#{tjBhkInfoBean.tdTjExmsdata.winsta == 2}" value="经常饮"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="酒量（ml/天）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;width: 160px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.windaymlx}"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width: 150px;">
            <h:outputLabel value="酒龄（年）："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.winyerqty}"/>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="4" style="text-align:left;height: 20px;">
                <p:outputLabel value="其他"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;  ">
            <h:outputLabel value="家族史："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.jzs}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="个人史："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.grs}"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 30px;width: 150px;">
            <h:outputLabel value="其他："/>
        </p:column>
        <p:column style="text-align:left;padding-right:3px;">
            <h:outputLabel value="#{tjBhkInfoBean.tdTjExmsdata.oth}"/>
        </p:column>
    </p:row>
</p:panelGrid>

<p:panelGrid style="width:100%;margin-top:10px;" rendered="#{!tjBhkInfoBean.ifShowBaseOnly}">
    <f:facet name="header">
        <p:row>
            <p:column colspan="4" style="text-align:left;height: 20px;">
                <p:outputLabel value="自觉症状"/>
            </p:column>
        </p:row>
    </f:facet>
    <p:row>
        <p:column style="width: 100%">
            <p:dataTable var="tdTjSymptomItm" value="#{tjBhkInfoBean.tdTjBhkShow.tdTjSymptoms}"
                         emptyMessage="没有数据">
                <p:column headerText="症状" style="width:180px;padding-left: 5px;">
                    <h:outputLabel value="#{tdTjSymptomItm.symId.codeName}"/>
                </p:column>
                <p:column headerText="其他症状" style="padding-left: 5px;">
                    <h:outputLabel value="#{tdTjSymptomItm.othsym}"/>
                </p:column>
            </p:dataTable>
        </p:column>
    </p:row>
</p:panelGrid>

<!--多个体检板块的展示-->
    <c:if test="#{!tjBhkInfoBean.ifShowBaseOnly and tjBhkInfoBean.itemFirstInfos.size() > 0}">
        <p:outputPanel style="margin-top: 10px;width: 100%;margin-top:10px;" id="archivePanel">
            <c:forEach var="t" items="#{tjBhkInfoBean.itemFirstInfos}">
                <p:fieldset legend="#{t.itemName}" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <c:forEach var="t1" items="#{t.itemSecondInfoDTOS}">
                    <p:panelGrid style="width:100%;margin-top:10px;" var="item" >
                        <p:row>
                            <p:column colspan="8" styleClass="ui-widget-header" style="height:22px;text-align: left;">
                                <h:outputText value="#{t1.itemName}" />
                            </p:column>
                        </p:row>
                        <p:row>
                            <!-- 项目列 -->
                            <p:column  style="width:120px; text-align: center; height:20px;" styleClass="ui-state-default">
                                <h:outputText value="项目" />
                            </p:column>

                            <!-- 结果列 -->
                            <p:column  style="width:100px; text-align: center;"  styleClass="ui-state-default">
                                <h:outputText value="结果" />
                            </p:column>

                            <!-- 参考值列 -->
                            <p:column  style="width:100px; text-align: center;"  styleClass="ui-state-default">
                                <h:outputText value="参考值" />
                            </p:column>

                            <!-- 计量单位列 -->
                            <p:column  style="width:100px; text-align: center;"  styleClass="ui-state-default">
                                <h:outputText value="计量单位" />
                            </p:column>

                            <!-- 合格列 -->
                            <p:column style="width:100px; text-align: center;"  styleClass="ui-state-default">
                                <h:outputText value="合格" />
                            </p:column>

                            <!-- 结果判定 -->
                            <p:column  style="width:100px; text-align: center;" rendered="#{t1.ifResultJudgment}"  styleClass="ui-state-default">
                                <h:outputText value="结果判定" />
                            </p:column>

                            <!-- 未检列 -->
                            <p:column  style="width:100px; text-align: center;" styleClass="ui-state-default">
                                <h:outputText value="未检" />
                            </p:column>

                        </p:row>
                        <c:forEach var="itm" items="#{t1.itemTableInfoDTOS}">
                            <p:row>
                            <!-- 项目列 -->
                            <p:column headerText="项目" style="text-align: left;height:20px;">
                                <h:outputText value="#{itm.itemName}" />
                            </p:column>

                            <!-- 结果列 -->
                            <p:column headerText="结果" style="text-align: left;#{itm.resultColour}">
                                <h:outputText value="#{itm.result}" />
                            </p:column>

                            <!-- 参考值列 -->
                            <p:column headerText="参考值" style="text-align: left">
                                <h:outputText value="#{itm.referenceValue}" />
                            </p:column>

                            <!-- 计量单位列 -->
                            <p:column headerText="计量单位" style="text-align: center;">
                                <h:outputText value="#{itm.unit}" />
                            </p:column>

                            <!-- 合格列 -->
                            <p:column headerText="合格" style="text-align: center;#{itm.passColour}">
                                <h:outputText value="#{itm.pass}" />
                            </p:column>

                            <!-- 结果判定 -->
                            <p:column headerText="结果判定" style="text-align: left;#{itm.resultJudgeColor}" rendered="#{t1.ifResultJudgment}">
                                <h:outputText value="#{itm.resultJudge}" />
                            </p:column>

                            <!-- 未检列 -->
                            <p:column headerText="未检" style="text-align: center;#{itm.uncheckedColour}">
                                <h:outputText value="#{itm.unchecked}" />
                            </p:column>

                            </p:row>
                        </c:forEach>
                    </p:panelGrid>
                    </c:forEach>
                </p:fieldset>
            </c:forEach>
        </p:outputPanel>
    </c:if>

<table style="width: 100%">
    <tr>
        <td style="text-align: center;">
            <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn2" action="#{mgrbean.forwardEditPage}"
                             update=":tabView" immediate="true" rendered="#{pageParam == null}"/>
        </td>
    </tr>
</table>

</h:form>
</ui:composition>