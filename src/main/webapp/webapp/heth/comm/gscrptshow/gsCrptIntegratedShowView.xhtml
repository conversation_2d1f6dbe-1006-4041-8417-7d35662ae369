<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.GsCrptIntegratedShowListBean"-->
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用人单位综合展示" />
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="margin: 5px 0;">
            <h:panelGrid columns="10" style="border-color:transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" onclick="hideTooltips();"
                                 action="#{mgrbean.backAction}" process="@this" update=":page_view"
                                 oncomplete="datatableOffClick();hideTooltips();"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <style type="text/css">
            #tabView {
                border-radius: 6px;
                overflow:auto;
            }

            #tabView\:viewForm {
                height: calc(100vh - 5px);
            }

            #tabView\:viewForm\:editTitleGrid, #tabView\:viewForm\:editGrid {
                margin: 0 !important;
                height: 0 !important;
            }

            #tabView\:viewForm, #tabView\:viewForm\:archives, #tabView\:viewForm\:archivesInfo, .da-item, .da-item-b, .left_con, .right_con {
                display: flex;
                flex-direction: column;
            }

            .zwx_toobar_new, .da-panel, .archives_date_sel_panel, .toobar_3 {
                display: flex;
                align-items: center;
            }

            #tabView\:viewForm\:archives, #tabView\:viewForm\:archivesInfo, #tabView\:viewForm\:archivesDetail {
                flex: 1;
                height: 0;
            }

            .full_table {
                width: 100%;
            }

            .none_user_select {
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }

            .zwx_toobar_new {
                border-radius: 6px;
                box-shadow: 0 -1px 0 0 #d5d9e0 inset;
                border: none;
                padding: 7px 0;
            }

            .table_header_column {
                text-align: left;
                padding-left: 5px !important;
                height: 20px;
            }

            .column_title1 {
                text-align: right;
            }

            .column_title2 {
                padding: 3px 6px !important;
            }

            .da-panel {
                padding: 20px 8px 16px 8px;
            }

            .da-item-panel, .da-item {
                margin: 0 8px;
            }

            .da-item, .da-item-b {
                justify-content: space-between;
                padding: 0 10px;
                width: 134px;
                height: 80px;
                border-radius: 4px;
                background: #FCFDFF;
                border: 0.5px solid #FFFFFF00;
                box-shadow: 0 1px 6px -2px rgba(170, 182, 193, 0.50);
            }

            .da-item-panel .da-item-b, .da-item:active {
                border: 0.5px solid #198989;
                background: #faffff;
                box-shadow: 0 3px 5px 0 #dee8f0, 0 1px 6px -2px rgba(170, 182, 193, 0.50);
            }

            .da-item-panel .da-item-a {
                display: none;
                background: #198989 url(/resources/images/crptShow/tick.svg) 9px 5px no-repeat;
            }

            .da-item-panel.sel .da-item-a {
                display: flex;
                width: 24px;
                height: 24px;
                border-radius: 0 4px 0 108px;
                padding: 0;
                margin: 0 0 0 -26px;
                float: right;
            }

            .da-item.block {
                opacity: 0.4;
                background: #DAE4F2;
                color: #191919;
                border: 0.5px solid #FFFFFF00 !important;
                box-shadow: 0 1px 6px -2px rgba(170, 182, 193, 0.50) !important;
                align-items: center;
                justify-content: center;
            }

            .archives_date_sel_panel {
                justify-content: space-between;
                border: none;
                padding: 7px 0 7px 20px;
                box-shadow: 0 1px 0 0 #cdcdcd inset, 0 -1px 0 0 #cdcdcd inset;
            }

            .left_con, .right_con {
                overflow: hidden;
                padding: 2px;
                height: 99%;
            }

            .con {
                display: flex;
                flex-direction: row;
            }

            .left_con {
                width: 45%;
                box-shadow: -1px 0 0 0 #e6e6e6 inset;
            }

            .right_con {
                flex: 1;
            }

            .toobar_2 {
                color: #000000;
                font-weight: bold;
                padding: 8px 10px;
            }

            .toobar_3 {
                padding: 10px;
            }

            .con_sep {
                height: 1px;
                box-shadow: 0 1px 0 0 #e6e6e6 inset;
                padding: 4px 0;
                margin: 5px 0 0 0;
            }

            .left_con1, .right_con1 {
                overflow: auto;
            }

            .left_con1::-webkit-scrollbar, .right_con1::-webkit-scrollbar {
                -webkit-appearance: none;
                background: rgba(255, 0, 0, 0);
                width: 4px;
            }

            .left_con1::-webkit-scrollbar-thumb, .right_con1::-webkit-scrollbar-thumb {
                background-color: #9996;
                border-radius: 10px;
                width: 4px;
                display: none;
            }

            .left_con1:hover::-webkit-scrollbar-thumb, .right_con1:hover::-webkit-scrollbar-thumb {
                display: block;
            }

            .left_con1::-webkit-scrollbar-track, .right_con1::-webkit-scrollbar-track {
                background-color: transparent;
                display: none;
            }

            .left_con1:hover::-webkit-scrollbar-track, .right_con1:hover::-webkit-scrollbar-track {
                display: block;
            }

            .cs-break-word {
                word-wrap: break-word;
                word-break: break-all;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function changeDaPanelItemSel(e) {
                jQuery('.da-item-panel.sel').removeClass('sel');
                jQuery(e).addClass('sel');
            }

            function changeDaPanelItemSelByArchives(type) {
                jQuery('.da-item-panel.sel').removeClass('sel');
                if (type == 1) {
                    jQuery('.da-item-panel.archives1').addClass('sel');
                } else if (type == 2) {
                    jQuery('.da-item-panel.archives2').addClass('sel');
                }
            }

            //]]>
        </script>
        <p:fieldset legend="基本信息" toggleable="true" toggleSpeed="500" id="baseInfo"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid styleClass="full_table">
                <p:row>
                    <p:column styleClass="column_title column_title1" style="width: 150px;height: 30px;">
                        <p:outputLabel value="地区："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" style="width: 300px;">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[0]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1" style="width: 150px;">
                        <p:outputLabel value="单位名称："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" style="width: 300px;">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[1]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1" style="width: 150px;">
                        <p:outputLabel value="社会信用代码："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[2]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="注册地址："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[3]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="作业场所地址："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[4]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="行业类别："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[5]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="经济类型："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[6]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="企业规模："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[7]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="联系人："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[8]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="联系电话："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[9]}"/>
                    </p:column>
                    <p:column styleClass="column_title column_title1">
                        <p:outputLabel value="职业风险分类："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" colspan="3">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[10]}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column styleClass="column_title column_title1" style="height: 30px;">
                        <p:outputLabel value="职业病危害因素种类："/>
                    </p:column>
                    <p:column styleClass="column_title column_title2" style="height: 30px;" colspan="5">
                        <p:outputLabel value="#{mgrbean.crptBaseInfo[11]}"/>
                        <p:commandLink value="查看详情 >>"
                                       style="float: right;margin-right: 25px;color: #194dff;"
                                       process="@this" update="@this,:tabView:viewForm:occRiskClassTablePanel"
                                       action="#{mgrbean.openBadrsnDialog}"
                                       rendered="#{not empty mgrbean.crptBaseInfo[11]}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <p:fieldset legend="监督管理线索信息提醒" toggleable="true" toggleSpeed="500" id="jdglxs"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:outputPanel id="jdglxsPanel">
                <p:dataTable id="jdglxsTable"
                             style="margin-top: 5px;margin-bottom: 5px;" rowIndexVar="R"
                             value="#{mgrbean.tbTjCrptWarnCommList}" var="item" rows="10"
                             paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="10,20,50" pageLinks="5"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             emptyMessage="没有您要找的记录！">
                    <p:column headerText="序号" style="text-align:center;width:4%;">
                        <h:outputText value="#{R + 1}"/>
                    </p:column>
                    <p:column headerText="预警信息" style="width:22%;">
                        <h:outputText value="#{item.fkByWarnId.codeName}"/>
                        <p:outputLabel
                                style="padding:3px;background:red;border-radius:2px;color: white;white-space:nowrap;margin-left: 10px;"
                                rendered="#{item.ifOvertime}">
                            <h:outputText value="已超期"/>
                        </p:outputLabel>
                    </p:column>
                    <p:column headerText="预警日期" style="text-align: center;width:6%;">
                        <h:outputText value="#{item.warnDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8"/>
                        </h:outputText>
                    </p:column>
                    <p:column headerText="#{mgrbean.fontStr}处理结果" style="text-align: center;width:10%" >
                        <div style="background-color: green;width: 185px;height: 25px;padding: 1px;border-radius: 5px;
                        #{item.state eq 1 and ('1' eq item.fkByResultId.extendS1 or '2' eq item.fkByResultId.extendS1 or '4' eq item.fkByResultId.extendS1)?'display:block;':'display:none;'}" >
                            <p:outputLabel value="#{item.fkByResultId.codeName}" style="color:white;vertical-align: sub;"/>
                        </div>
                        <div style="background-color: orange;width: 185px;height: 25px;padding: 1px;border-radius: 5px;
                        #{item.state eq 1 and ('3' eq item.fkByResultId.extendS1)?'display:block;':'display:none;'}" >
                            <p:outputLabel value="#{item.fkByResultId.codeName}" style="color:white;vertical-align: sub;"/>
                        </div>
                        <p:outputLabel value="#{item.fkByResultId.codeName}" style="color:black;vertical-align: sub;" rendered="#{item.state eq 1 and !('1' eq item.fkByResultId.extendS1 or '2' eq item.fkByResultId.extendS1 or '3' eq item.fkByResultId.extendS1 or '4' eq item.fkByResultId.extendS1)}"/>
                        <p:selectOneMenu style="width: 187px; text-align: left;margin-top: 3px;" editable="false" rendered="#{item.state eq 0}"
                                         value="#{item.resultId}">
                            <f:selectItem itemValue="" itemLabel="--请选择--"/>
                            <f:selectItems value="#{mgrbean.warnResults}"
                                           var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column headerText="处理时间" style="text-align: center;width:10%;">
                        <h:outputText value="#{item.dealDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8"/>
                        </h:outputText>
                    </p:column>
                    <p:column headerText="处理人" style="text-align: center;width:5%;">
                        <h:outputText value="#{item.fkByDealPsnId.username}" rendered="#{'4' ne item.fkByResultId.extendS1}"/>
                        <h:outputText value="系统" rendered="#{'4' eq item.fkByResultId.extendS1}"/>
                    </p:column>
                    <p:column headerText="状态" style="text-align: center;width:6%;">
                        <h:outputText value="待处置" rendered="#{item.state eq 0}" style="#{item.ifOvertime?'color: red;':''}" />
                        <h:outputText value="已处置" rendered="#{item.state eq 1}" />
                    </p:column>
                    <p:column headerText="备注" style="width:15%;">
                        <p:inputText style="width:280px;" value="#{item.rmk}" maxlength="500"
                                     rendered="#{item.state eq 0}"/>
                        <h:outputText id="rmk" value="#{item.rmk}" rendered="#{item.state eq 1}"
                                       styleClass="zwx-tooltip" />
                        <p:tooltip for="rmk" style="max-width:300px;">
                            <p:outputLabel value="#{item.rmk}" escape="false" />
                        </p:tooltip>
                    </p:column>
                    <p:column headerText="操作">
                        <p:commandLink value="提交" action="#{mgrbean.openWarnDialog}"
                                       rendered="#{item.state eq 0}" process="@this,:tabView:viewForm:jdglxsPanel"
                                       oncomplete="hideTooltips();"
                                       update=":tabView:viewForm:jdglxsPanel" resetValues="true">
                            <f:setPropertyActionListener value="#{item}" target="#{mgrbean.crptWarnComm}"/>
                        </p:commandLink>
                        <p:commandLink value="撤销" action="#{mgrbean.warnCancelAction()}"
                                       rendered="#{item.state eq 1 and '4' ne item.fkByResultId.extendS1}" process="@this,:tabView:viewForm:jdglxsPanel"
                                       oncomplete="hideTooltips();"
                                       update=":tabView:viewForm:jdglxsPanel" resetValues="true">
                            <f:setPropertyActionListener value="#{item}" target="#{mgrbean.crptWarnComm}"/>
                            <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
                <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
                    <p:commandButton value="确定" action="#{mgrbean.warnSubmitAction}" icon="ui-icon-check"
                                     oncomplete="PF('ConfirmDialog').hide();" update=":tabView"/>
                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
                </p:confirmDialog>
            </p:outputPanel>
        </p:fieldset>
        <p:fieldset legend="职业病危害项目申报数据" toggleable="true" toggleSpeed="500" id="zybwh"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable id="zybwhTable"
                         style="margin-top: 5px;margin-bottom: 5px;" rowIndexVar="R"
                         value="#{mgrbean.unitBasicInfoList}" var="item" rows="10"
                         paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="10,20,50" pageLinks="5"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         emptyMessage="没有您要找的记录！">
                <p:column headerText="申报年份" style="text-align:center;width:5%;">
                    <h:outputText value="#{item[1]}"/>
                </p:column>
                <p:column headerText="申报日期" style="text-align:center;width:6%;">
                    <h:outputText value="#{item[2]}" >
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8"/>
                    </h:outputText>
                </p:column>
                <p:column headerText="申报类型" style="text-align: center;width:6%;">
                    <h:outputText value="初次申报" rendered="#{0 eq item[3]}"/>
                    <h:outputText value="变更申报" rendered="#{1 eq item[3]}"/>
                    <h:outputText value="年度更新" rendered="#{2 eq item[3]}"/>
                </p:column>
                <p:column headerText="职工人数" style="text-align: center;width:5%;" >
                    <h:outputText value="#{item[4]}" />
                </p:column>
                <p:column headerText="接害人数" style="text-align: center;width:5%;">
                    <h:outputText value="#{item[5]}" />
                </p:column>
                <p:column headerText="外委人数" style="text-align: center;width:5%;">
                    <h:outputText value="#{item[6]}" />
                </p:column>
                <p:column headerText="负责人是否培训" style="text-align: center;width:4%;">
                    <h:outputText value="已培训" rendered="#{1 eq item[7]}"/>
                    <h:outputText value="未培训" rendered="#{0 eq item[7]}"/>
                </p:column>
                <p:column headerText="职业卫生管理人员培训" style="text-align: center;width:6%;">
                    <h:outputText value="已培训" rendered="#{1 eq item[8]}"/>
                    <h:outputText value="未培训" rendered="#{0 eq item[8]}"/>
                </p:column>
                <p:column headerText="本年度检测情况" style="text-align: center;width:4%;">
                    <h:outputText value="已检测" rendered="#{1 eq item[9]}"/>
                    <h:outputText value="未检测" rendered="#{0 eq item[9]}"/>
                </p:column>
                <p:column headerText="本年度岗中开展职业健康检查情况" style="text-align: center;width:8%;">
                    <h:outputText value="已开展" rendered="#{1 eq item[10]}"/>
                    <h:outputText value="未开展" rendered="#{0 eq item[10]}"/>
                </p:column>
                <p:column headerText="有无粉尘因素" style="text-align: center;width:6%;">
                    <h:outputText value="有" rendered="#{1 eq item[11]}"/>
                    <h:outputText value="无" rendered="#{0 eq item[11]}"/>
                </p:column>
                <p:column headerText="有无化学毒物" style="text-align: center;width:6%;">
                    <h:outputText value="有" rendered="#{1 eq item[12]}"/>
                    <h:outputText value="无" rendered="#{0 eq item[12]}"/>
                </p:column>
                <p:column headerText="有无物理因素" style="text-align: center;width:6%;">
                    <h:outputText value="有" rendered="#{1 eq item[13]}"/>
                    <h:outputText value="无" rendered="#{0 eq item[13]}"/>
                </p:column>
                <p:column headerText="有无放射因素" style="text-align: center;width:6%;">
                    <h:outputText value="有" rendered="#{1 eq item[14]}"/>
                    <h:outputText value="无" rendered="#{0 eq item[14]}"/>
                </p:column>
                <p:column headerText="有无生物因素" style="text-align: center;width:6%;">
                    <h:outputText value="有" rendered="#{1 eq item[15]}"/>
                    <h:outputText value="无" rendered="#{0 eq item[15]}"/>
                </p:column>
                <p:column headerText="有无其他因素" style="text-align: center;width:6%;">
                    <h:outputText value="有" rendered="#{1 eq item[16]}"/>
                    <h:outputText value="无" rendered="#{0 eq item[16]}"/>
                </p:column>
                <p:column headerText="操作">
                    <p:commandLink value="详情"
                                   style="margin-right: 25px;"
                                   process="@this" action="#{mgrbean.viewUnitBasicInfo}">
                        <f:setPropertyActionListener value="#{item[0]}" target="#{mgrbean.unitBasicInfoRid}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>

        </p:fieldset>
        <p:fieldset legend="工作场所职业病危害因素监测数据" toggleable="true" toggleSpeed="500" id="csjc"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable id="csjcTable"
                         style="margin-top: 5px;margin-bottom: 5px;" rowIndexVar="R"
                         value="#{mgrbean.unitBasicInfoJcList}" var="item" rows="10"
                         paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="10,20,50" pageLinks="5"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         emptyMessage="没有您要找的记录！">
                <p:column headerText="监测年份" style="text-align:center;width:5%;">
                    <h:outputText value="#{item[1]}"/>
                </p:column>
                <p:column headerText="职工人数" style="text-align:center;width:5%;">
                    <h:outputText value="#{item[2]}" />
                </p:column>
                <p:column headerText="接害人数" style="text-align: center;width:5%;">
                    <h:outputText value="#{item[4]}"/>
                </p:column>
                <p:column headerText="外委人数" style="text-align: center;width:5%;">
                    <h:outputText value="#{item[3]}" />
                </p:column>
                <p:column headerText="负责人培训情况" style="text-align: center;width:7%;">
                    <h:outputText value="已培训" rendered="#{1 eq item[5]}"/>
                    <h:outputText value="未培训" rendered="#{0 eq item[5]}"/>
                </p:column>
                <p:column headerText="职业卫生管理人员培训情况" style="text-align: center;width:11%;">
                    <h:outputText value="已培训" rendered="#{1 eq item[6]}"/>
                    <h:outputText value="未培训" rendered="#{0 eq item[6]}"/>
                </p:column>
                <p:column headerText="上一年度职业病危害因素检测情况" style="text-align: center;width:13%;">
                    <h:outputText value="已检测" rendered="#{1 eq item[7]}"/>
                    <h:outputText value="未检测" rendered="#{0 eq item[7]}"/>
                </p:column>
                <p:column headerText="上一年度在岗期间职业健康检查情况" style="text-align: center;width:14%;">
                    <h:outputText value="已体检" rendered="#{1 eq item[8]}"/>
                    <h:outputText value="未体检" rendered="#{0 eq item[8]}"/>
                </p:column>
                <p:column headerText="是否存在超标岗位" style="text-align: center;width:7%;">
                    <h:outputText value="是" rendered="#{item[9]>0}"/>
                    <h:outputText value="否" rendered="#{0 eq item[9]}"/>
                </p:column>
                <p:column headerText="操作">
                    <p:commandLink value="详情"
                                   style="margin-right: 25px;"
                                   process="@this" action="#{mgrbean.csjcViewAction}">
                        <f:setPropertyActionListener value="#{item[0]}" target="#{mgrbean.unitBasicInfoJcRid}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>

        </p:fieldset>
        <p:fieldset legend="职业健康检查数据" toggleable="true" toggleSpeed="500" id="zyjk"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable var="bhkData" value="#{mgrbean.bhkDataList}" id="bhkDataTable"
                         paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,20,50"  lazy="true" emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column style="width: 70px;text-align: center;" headerText="体检年份">
                    <h:outputText value="#{bhkData[0]}"/>
                </p:column>
                <p:column style="width: 70px;text-align: center;" headerText="体检人数">
                    <h:outputText value="#{bhkData[1]}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="有无粉尘因素">
                    <h:outputText value="有" rendered="#{bhkData[2] > 0}"/>
                    <h:outputText value="无" rendered="#{bhkData[2] == 0}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="有无化学毒物">
                    <h:outputText value="有" rendered="#{bhkData[3] > 0}"/>
                    <h:outputText value="无" rendered="#{bhkData[3] == 0}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="有无物理因素">
                    <h:outputText value="有" rendered="#{bhkData[4] > 0}"/>
                    <h:outputText value="无" rendered="#{bhkData[4] == 0}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="有无放射因素">
                    <h:outputText value="有" rendered="#{bhkData[5] > 0}"/>
                    <h:outputText value="无" rendered="#{bhkData[5] == 0}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="有无生物因素">
                    <h:outputText value="有" rendered="#{bhkData[6] > 0}"/>
                    <h:outputText value="无" rendered="#{bhkData[6] == 0}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="有无其他因素">
                    <h:outputText value="有" rendered="#{bhkData[7] > 0}"/>
                    <h:outputText value="无" rendered="#{bhkData[7] == 0}"/>
                </p:column>
                <p:column style="width: 130px;text-align: center;" headerText="职业禁忌证检出数">
                    <h:outputText value="#{bhkData[8]}"/>
                </p:column>
                <p:column style="width: 130px;text-align: center;" headerText="疑似职业病检出数">
                    <h:outputText value="#{bhkData[9]}" style="#{bhkData[9] > 0 ? 'color: red;':''}"/>
                </p:column>
                <p:column headerText="操作">
                    <p:commandLink value="详情" process="@this"
                                   action="#{mgrbean.openBhkDataDialogAction(bhkData[0])}"/>
                </p:column>
            </p:dataTable>
        </p:fieldset>
        <p:fieldset legend="职业病诊断数据" toggleable="true" toggleSpeed="500" id="zybzd"
                    style="margin-top: 5px;margin-bottom: 5px;" rendered="#{not empty mgrbean.diagDataList}">
            <p:dataTable var="diagData" value="#{mgrbean.diagDataList}" id="diagDataTable"
                         paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,20,50"  lazy="true" emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column style="width: 50px;text-align: center;" headerText="年份">
                    <h:outputText value="#{diagData.get(0)}"/>
                </p:column>
                <p:column style="width: 70px;text-align: center;" headerText="申请诊断数">
                    <h:outputText value="#{diagData.get(1)}"/>
                </p:column>
                <p:column style="width: 100px;text-align: center;" headerText="确诊职业病数">
                    <h:outputText value="#{diagData.get(2)}"/>
                </p:column>
                <c:forEach items="#{mgrbean.diagDataHeaderList}" var="diagDataHeader" varStatus="diagDataHeaderIndex">
                    <p:column style="width: #{diagDataHeader.length()*15}px;text-align: center;" headerText="#{diagDataHeader}">
                        <h:outputText value="#{diagData.get(diagDataHeaderIndex.index + 3)}"/>
                    </p:column>
                </c:forEach>
                <p:column headerText="操作" style="">
                    <p:commandLink value="详情" process="@this"
                                   action="#{mgrbean.openDiagDataDialogAction(diagData.get(0))}"/>
                </p:column>
            </p:dataTable>
        </p:fieldset>
        <p:fieldset legend="职业卫生技术服务报告卡数据" toggleable="true" toggleSpeed="500" id="bgk"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable var="occhethCard" value="#{mgrbean.occhethCardList}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,20,50"  lazy="true" emptyMessage="没有您要找的记录！" id="occhethCard"
                          currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column headerText="报告日期"  width="10%" style="text-align: center;">
                    <h:outputText value="#{occhethCard[1]}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                    </h:outputText>
                </p:column>
                <p:column headerText="技术服务类别" width="15%" style="text-align: center;">
                    <h:outputText value="职业病危害因素检测"/>
                </p:column>
                <p:column headerText="岗位数" width="10%" style="text-align: center;" >
                    <h:outputText value="#{occhethCard[2]}"/>
                </p:column>
                <p:column headerText="超标岗位数" width="10%" style="text-align: center;" >
                    <h:outputText value="#{occhethCard[3]}"/>
                </p:column>
                <p:column headerText="粉尘是否超标" width="15%" style="text-align: center;" >
                    <h:outputText value="#{occhethCard[4]}"/>
                </p:column>
                <p:column headerText="物理因素是否超标" width="15%" style="text-align: center;" >
                    <h:outputText value="#{occhethCard[5]}"/>
                </p:column>
                <p:column headerText="化学毒物是否超标" width="15%" style="text-align: center;" >
                    <h:outputText value="#{occhethCard[6]}"/>
                </p:column>
                <p:column headerText="操作" width="10%" >
                    <p:commandLink value="详情" process="@this"  action="#{mgrbean.viewOcchethCardAction}">
                        <f:setPropertyActionListener target="#{mgrbean.cardRid}" value="#{occhethCard[0]}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>

        </p:fieldset>
        <p:fieldset legend="放射卫生技术服务报告卡数据" toggleable="true" toggleSpeed="500" id="fsbgk"
                    style="margin-top: 5px;margin-bottom: 5px;" rendered="#{mgrbean.srvorgCardList ne null and mgrbean.srvorgCardList.size() > 0 }">
            <p:dataTable var="srvorgCard" value="#{mgrbean.srvorgCardList}" paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,20,50"  lazy="true" emptyMessage="没有您要找的记录！" id="srvorgCard"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column headerText="报告日期"  width="15%" style="text-align: center;">
                    <h:outputText value="#{srvorgCard[1]}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                    </h:outputText>
                </p:column>
                <p:column headerText="技术服务类别" width="70%" style="text-align: center;">
                    <h:outputText value="#{srvorgCard[2]}"/>
                </p:column>
                <p:column headerText="操作" width="15%" >
                    <p:commandLink value="详情" process="@this"  action="#{mgrbean.viewSrvorgCardAction}">
                        <f:setPropertyActionListener target="#{mgrbean.cardRid}" value="#{srvorgCard[0]}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
        </p:fieldset>
        <p:dialog id="occRiskClassDialog" header="职业病危害因素" widgetVar="OccRiskClassDialog"
                  resizable="false" width="850" height="400" modal="true">
            <p:inputText style="visibility: hidden;width: 0;position: absolute;"/>
            <p:scrollPanel id="occRiskClassTablePanel" mode="native" style="border:0;padding-bottom:5px;margin-top:5px">
                <p:dataTable id="occRiskClassTable"
                             style="margin-top: 5px;margin-bottom: 5px;"
                             value="#{mgrbean.occRiskClassList}" var="occRiskClass" rows="10"
                             paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="10,20,50" pageLinks="5"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             emptyMessage="没有您要找的记录！">
                    <p:column headerText="职业病危害因素种类" style="text-align:center;width:150px;">
                        <h:outputText value="#{occRiskClass[0]}"/>
                    </p:column>
                    <p:column headerText="职业病危害因素">
                        <h:outputText value="#{occRiskClass[1]}"/>
                        <p:outputLabel
                                style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;color: white;white-space:nowrap;"
                                rendered="#{occRiskClass[3] eq '1'}">
                            <h:outputText value="高毒"/>
                        </p:outputLabel>
                        <p:outputLabel
                                style="margin-left:8px;padding:3px;background:#E15F34;border-radius:2px;color: white;white-space:nowrap;"
                                rendered="#{occRiskClass[4] eq '1'}">
                            <h:outputText value="致癌"/>
                        </p:outputLabel>
                    </p:column>
                </p:dataTable>
            </p:scrollPanel>
        </p:dialog>
        <p:dialog id="bhkDataDialog" header="职业健康检查数据" widgetVar="BhkDataDialog"
                  resizable="false" width="1050" height="400" modal="true">
            <p:dataTable var="bhkData" value="#{mgrbean.crptIntegratedShowBhkDataBean.dataModel}" id="bhkPsnDataTable"
                         paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,20,50"  lazy="true" emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column styleClass="cs-break-word" style="width: 120px;" headerText="体检编号">
                    <h:outputText value="#{bhkData[0]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 60px;text-align: center;" headerText="姓名">
                    <h:outputText value="#{bhkData[1]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 120px;text-align: center;" headerText="证件号码">
                    <h:outputText value="#{bhkData[2]}"/>
                </p:column>
                <p:column style="width: 40px;text-align: center;" headerText="性别">
                    <h:outputText value="#{bhkData[3]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 150px;text-align: center;" headerText="工种">
                    <h:outputText value="#{bhkData[4]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" headerText="体检危害因素">
                    <h:outputText value="#{bhkData[5]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 80px;text-align: center;" headerText="体检日期">
                    <h:outputText value="#{bhkData[6]}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8"/>
                    </h:outputText>
                </p:column>
            </p:dataTable>
        </p:dialog>
        <p:dialog id="diagDataDialog" header="职业病诊断数据" widgetVar="DiagDataDialog"
                  resizable="false" width="1050" height="400" modal="true">
            <p:dataTable var="diagData" value="#{mgrbean.diagYearDataList}" id="diagPsnDataTable"
                         paginator="true" rows="10" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="10,20,50"  lazy="true" emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  >
                <p:column styleClass="cs-break-word" headerText="档案编号">
                    <h:outputText value="#{diagData[0]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 120px;text-align: center;" headerText="身份证号">
                    <h:outputText value="#{diagData[1]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 60px;text-align: center;" headerText="姓名">
                    <h:outputText value="#{diagData[2]}"/>
                </p:column>
                <p:column style="width: 40px;text-align: center;" headerText="性别">
                    <h:outputText value="男" rendered="#{diagData[3] eq '1'}"/>
                    <h:outputText value="女" rendered="#{diagData[3] eq '2'}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 80px;text-align: center;" headerText="接诊日期">
                    <h:outputText value="#{diagData[4]}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8"/>
                    </h:outputText>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 275px;" headerText="申请诊断的职业病">
                    <h:outputText value="#{diagData[5]}"/>
                </p:column>
                <p:column styleClass="cs-break-word" style="width: 110px;text-align: center;" headerText="是否诊断为职业病">
                    <h:outputText value="是" rendered="#{diagData[7] eq 1}"/>
                    <h:outputText value="否" rendered="#{diagData[7] eq 0}"/>
                </p:column>
            </p:dataTable>
        </p:dialog>
    </ui:define>
</ui:composition>