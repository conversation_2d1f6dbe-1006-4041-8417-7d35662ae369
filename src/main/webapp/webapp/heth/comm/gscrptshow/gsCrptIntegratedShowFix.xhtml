<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.GsCrptIntegratedShowListBean"-->
    <ui:insert name="insertEditScripts"/>
    <h:form id="otherForm">
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <h:outputText value="用人单位综合展示"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>
        <p:outputPanel styleClass="zwx_toobar_42" style="margin: 5px 0;">
            <h:panelGrid columns="10" style="border-color:transparent;padding: 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="修订" icon="ui-icon-check" id="fixBtn"
                                 action="#{mgrbean.beforeFixAction}" process="@this,fixPanel"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" onclick="hideTooltips();"
                                 action="#{mgrbean.backAction}" process="@this" update=":page_view"
                                 oncomplete="datatableOffClick()"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:confirmDialog message="所有业务数据的所属用人单位将变更为选择的单位，一旦修订无法撤销，是否确认修订？"
                             header="消息确认框" widgetVar="FixConfirmDialog" styleClass="fixConfirmDialog">
                <p:commandButton value="确定" action="#{mgrbean.fixAction}" icon="ui-icon-check"
                                 oncomplete="PF('FixConfirmDialog').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('FixConfirmDialog').hide();"
                                 type="button"/>
            </p:confirmDialog>
        </p:outputPanel>
        <style type="text/css">
            .fixConfirmDialog .ui-dialog-buttonpane.ui-widget-content.ui-helper-clearfix {
                text-align: center;
            }
            
            #tabView {
                border-radius: 6px;
            }

            #table1 {
                /*使用Flex布局*/
                display: flex;
                /*垂直方向布局*/
                flex-direction: column;
            }

            #table1 .tbl:first-child {
                /*border-top-width: 1px;*/
                /*border-top-style: solid;*/
                border-top-color: inherit;
            }

            .tbl {
                /*width: 50%;*/
                /*height: 100px;*/
                min-height: 35px;
                /*使用Flex布局，默认为水平方向布局*/
                display: flex;
                border-left-width: 1px;
                /*border-left-style: solid;*/
                border-left-color: inherit;
                border-right-width: 1px;
                /*border-right-style: solid;*/
                border-right-color: inherit;
                border-bottom-width: 1px;
                border-bottom-style: solid;
                border-bottom-color: inherit;
            }

            .cell {
                /*其实这里也可以将宽度设置为2%、5%、10%等，因为页面要展示7列，100 / 7 约等于14%，所以这里的宽度最好不超过14%(不过超过了，在页面展示上貌似也没什么问题，不知道会不会有潜在的问题)*/
                /*因为是约等于14%，所以还会剩余一些空间，我在下面定义了flex-grow的值为1，即剩余的空间，会被这7列平分*/
                /*如果这里没有设置宽度，那么当文本内容过多时，该列的宽度会自动扩大*/
                width: 1%;
                /*height: 100%;*/
                /*定义项目的放大比例，如果值为1，则所有的项目将等分剩余的空间*/
                /*因为当前是水平方向布局，所以等分的是剩余的width*/
                flex-grow: 3;
                display: flex;
                /*水平居中、垂直居中*/
                /*justify-content: center;*/
                align-items: center;
                /*换行*/
                word-break: break-all;
                word-wrap: break-word;
                white-space: normal;
                border-right-width: 1px;
                border-right-style: solid;
                border-right-color: inherit;
                padding: 3px;
            }

            .cell.first_cell {
                flex-grow: 1;
                font-weight: bold;
                padding-left: 6px;
            }

            .cell.center_cell {
                font-weight: bold;
                justify-content: center;
            }

            .tbl:last-child:not(.ui-chkbox-box) {
                border-bottom-style: none;
            }

            .tbl div:last-child:not(.ui-chkbox-box) {
                border-right: 0;
            }

            .cs-required:before {
                content: '*';
                color: red;
            }

            .content_checkbox {
                padding-right: 3px;
            }
        </style>
        <p:outputPanel id="fixPanel" style="padding: 5px;">
            <div id="table1" class="ui-widget-content">
                <div class="tbl">
                    <div class="cell first_cell center_cell cs-required">单位信息</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell center_cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.crptSel}" rendered="#{not empty crptVO.crptSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(0, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.crptName}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">地区</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.zoneSel}" rendered="#{not empty crptVO.zoneSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(1, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.tsZoneByZoneId.zoneName}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">单位名称</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.nameSel}" rendered="#{not empty crptVO.nameSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(2, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.crptName}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">社会信用代码</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.institutionCodeSel}"
                                                     rendered="#{not empty crptVO.institutionCodeSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(3, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.institutionCode}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">单位地址</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.addressSel}"
                                                     rendered="#{not empty crptVO.addressSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(4, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.address}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">作业场所地址</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.enrolAddressSel}"
                                                     rendered="#{not empty crptVO.enrolAddressSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(5, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.enrolAddress}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">行业类别</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.indusTypeSel}"
                                                     rendered="#{not empty crptVO.indusTypeSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(6, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.tsSimpleCodeByIndusTypeId.codeName}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">经济类型</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.economySel}"
                                                     rendered="#{not empty crptVO.economySel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(7, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.tsSimpleCodeByEconomyId.codeName}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">企业规模</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.crptSizeSel}"
                                                     rendered="#{not empty crptVO.crptSizeSel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(8, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.tsSimpleCodeByCrptSizeId.codeName}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">联系人</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.linkMan2Sel}"
                                                     rendered="#{not empty crptVO.linkMan2Sel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(9, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.linkman2}
                        </div>
                    </c:forEach>
                </div>
                <div class="tbl">
                    <div class="cell first_cell">联系电话</div>
                    <c:forEach items="#{mgrbean.fixCrptList}"
                               var="crptVO" varStatus="crptStatus">
                        <div class="ui-panelgrid cell">
                            <p:selectBooleanCheckbox styleClass="content_checkbox"
                                                     value="#{crptVO.linkPhone2Sel}"
                                                     rendered="#{not empty crptVO.linkPhone2Sel}">
                                <p:ajax event="change" process="@this,fixPanel" update="fixPanel"
                                        listener="#{mgrbean.fixChange(10, crptStatus.index)}"/>
                            </p:selectBooleanCheckbox>
                            #{crptVO.crpt.linkphone2}
                        </div>
                    </c:forEach>
                </div>
            </div>
        </p:outputPanel>
    </h:form>
</ui:composition>