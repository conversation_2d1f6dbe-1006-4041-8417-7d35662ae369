<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection_new.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.GsCrptIntegratedShowListBean"-->
    <ui:param name="mgrbean" value="#{gsCrptIntegratedShowListBean}"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/comm/gscrptshow/gsCrptIntegratedShowView.xhtml"/>
    <!-- 修订页面 -->
    <ui:param name="otherPage" value="/webapp/heth/comm/gscrptshow/gsCrptIntegratedShowFix.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript" src="#{request.contextPath}/resources/echarts/3.0/echarts.min.js"></script>
        <script type="text/javascript" src="#{request.contextPath}/resources/echarts/3.0/macarons.js"></script>
        <script type="text/javascript">
            //<![CDATA[
            function getDownloadFileClick() {
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }

            var chart1;
            var timeOut = null, json1, option;
            var fromWidth = 1400;
            // 1888 1033
            var autoSize = fromWidth >= 1800 ? 20 : (fromWidth >= 1400 ? 15 : 10);

            function buildChart() {
                stop();
                chart1 = echarts.init(document
                    .getElementById("chartShow"), "macarons");
                json1 = document.getElementById("chartForm:chartJson").value;
                if (json1 != '') {
                    option = eval("(" + json1 + ")");
                    if (4 != parseInt(document.getElementById("chartForm:chartType").value) &&
                        autoSize < parseInt(document.getElementById("chartForm:chartDataSize").value)) {
                        option.dataZoom = [
                            {
                                show: false, //为true滚动条出现
                                type: 'slider', //type:'inside'，滚动条在最下面，鼠标点击滚动
                                startValue: 0, // 从头开始。
                                endValue: autoSize - 1, //endValue具体显示几个数值 下标
                            }
                        ];
                        chart1.setOption(option);
                        autoMove();
                        if (timeOut) {
                            chart1.on('mouseover', stop);
                            chart1.on('mouseout', goMove);
                        }
                    } else {
                        chart1.setOption(option);
                    }
                }
            }

            window.onresize = function () {
                if (null != chart1 && undefined != chart1) {
                    chart1.resize();
                }
            };

            function autoMove() {
                stop();
                timeOut = setInterval(() => {
                    if (null != document.getElementById("chartForm:chartType") &&
                        undefined != document.getElementById("chartForm:chartType")) {
                        if (4 != parseInt(document.getElementById("chartForm:chartType").value) &&
                            parseInt(document.getElementById("chartForm:chartDataSize").value) > autoSize) {
                            var chartSize = parseInt(document.getElementById("chartForm:chartDataSize").value);
                            if (null != option && undefined != option) {
                                // 每次向后滚动一个，最后一个从头开始。
                                if (Number(option.dataZoom[0].endValue) == chartSize - 1) {
                                    option.dataZoom[0].endValue = autoSize - 1;
                                    option.dataZoom[0].startValue = 0;
                                } else {
                                    option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
                                    option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
                                }
                            }
                        }
                        if (null != chart1 && undefined != chart1 && null != option && undefined != option) {
                            chart1.setOption(option);
                        }
                    }
                }, 3000);
            }

            //停止滚动
            function stop() {
                if (timeOut != null && undefined != timeOut) {
                    clearInterval(timeOut)
                }
            }

            //继续滚动
            function goMove() {
                autoMove()
            }

            jQuery(document).ready(function () {
                //进入详情也会走这里
                if (null != document.getElementById("chartForm:chartType") &&
                    undefined != document.getElementById("chartForm:chartType")) {
                    fromWidth = null == document.getElementById("chartForm") || undefined == document.getElementById("chartForm") ? 1030 :
                        document.getElementById("chartForm").clientWidth;
                    autoSize = fromWidth >= 1800 ? 20 : (fromWidth >= 1400 ? 15 : 10);
                    buildChart();
                }
            });

            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            //]]>
        </script>
    </ui:define>
    <ui:define name="insertOutsideMainContents">
        <div style="height: 30%; background: #ffffff; border-radius: 2px; box-shadow: 0 2px 4px 0 rgba(0,0,0,0.12), 0 0 6px 0 rgba(0,0,0,0.04);margin: 0 10px 15px 10px;">
            <h:form id="chartForm">
                <p:commandButton process="@this" id="flushBtn" onclick="reflushChart()" style="display: none"/>
                <h:inputHidden id="chartJson" value="#{mgrbean.chartJson}"/>
                <h:inputHidden id="chartDataSize" value="#{mgrbean.chartDataSize}"/>
                <h:inputHidden id="chartType" value="#{mgrbean.chartType}"/>
                <p:remoteCommand name="chartChange" action="#{mgrbean.changeChartType}" process="@this,:chartForm"
                                 update="chartForm:outputPanel,chartJson,chartDataSize,chartType"
                                 oncomplete="buildChart()"/>
                <p:outputPanel id="overviewPanel">
                    <p:panel id="chartPanel">
                        <div style="width:100%;height:30px;display:flex;">
                            <div style="width:10%;height:100%;text-align: left;padding-left: 12px; align-items: center; display: flex;">
                                <h:outputText value="用人单位综合展示" style="font-weight: bold;letter-spacing:2px;"/>
                            </div>
                            <div style="height:100%;text-align:left;padding-left: 3px;">
                                <p:selectOneMenu style="width: 120px;" value="#{mgrbean.chartType}">
                                    <f:selectItem itemValue="1" itemLabel="地区分布"/>
                                    <f:selectItem itemValue="2" itemLabel="行业类别"/>
                                    <f:selectItem itemValue="3" itemLabel="经济类型"/>
                                    <f:selectItem itemValue="4" itemLabel="企业规模"/>
                                    <f:selectItem itemValue="5" itemLabel="高毒分布"/>
                                    <f:selectItem itemValue="6" itemLabel="致癌分布"/>
                                    <p:ajax event="change" oncomplete="chartChange()" process="@this"/>
                                </p:selectOneMenu>
                            </div>
                        </div>
                        <p:outputPanel id="outputPanel">
                            <table style="width: 100%;">
                                <tr valign="top">
                                    <td width="100%">
                                        <div id="chartShow" style="height:210px;padding:0px;"/>
                                    </td>
                                </tr>
                            </table>
                        </p:outputPanel>
                    </p:panel>
                </p:outputPanel>
            </h:form>
        </div>
    </ui:define>
    <ui:define name="insertUpMainContents">
    </ui:define>
    <ui:define name="insertTitle">
    </ui:define>
    <ui:define name="insertButtons">
        <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" styleClass="btn1"
                         action="#{mgrbean.searchAction}" oncomplete="datatableOffClick()"
                         process="@this,mainGridNew" update=":tabView:mainForm:dataTable"/>
        <p:commandButton value="导出" icon="ui-icon-document" id="exportBtn" styleClass="btn2"
                         action="#{mgrbean.exportBefore}" process="@this,:tabView:mainForm:mainGridNew"/>
        <p:commandButton value="修订" icon="ui-icon-check" id="fixBtn" onstart="stop()"
                         action="#{mgrbean.openFixPageAction}" process="@this,:tabView:mainForm:dataTable"
                         rendered="#{mgrbean.hasFix}"/>
        <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document"
                         ajax="false" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
            <p:fileDownload value="#{mgrbean.export()}"/>
        </p:commandButton>
    </ui:define>
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:280px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}" panelHeight="350" height="330"
                                    zoneCode="#{mgrbean.searchZoneCode}" zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="用人单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:inputText value="#{mgrbean.searchCrptName}" style="width: 180px;" maxlength="100"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchInstitutionCode}" style="width: 180px;" maxlength="100"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="行业类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 6px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="indusTypeName" style="width: 180px;cursor: pointer;"
                                         value="#{mgrbean.selectIndusTypeNames}" readonly="true"
                                         onclick="document.getElementById('tabView:mainForm:selIndusTypeLink').click();"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="selIndusTypeLink"
                                           style="position: relative;left: -28px !important;"
                                           process="@this" action="#{mgrbean.selSimpleCodeAction}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                        process="@this" update="indusTypeName" resetValues="true"/>
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="indusTypeName"
                                           action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5002"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="经济类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 6px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="encomyName" style="width: 180px;cursor: pointer;"
                                         value="#{mgrbean.selectEconomyNames}" readonly="true"
                                         onclick="document.getElementById('tabView:mainForm:selEconomyLink').click();"
                            />
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="selEconomyLink"
                                           style="position: relative;left: -28px !important;"
                                           process="@this" action="#{mgrbean.selSimpleCodeAction}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                        process="@this"
                                        resetValues="true" update="encomyName"/>
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="encomyName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5003"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="企业规模："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;">
                <zwx:SimpleCodeManyComp selectedIds="#{mgrbean.selectCrptSizeIds}"
                                        simpleCodeList="#{mgrbean.crptSizeList}"
                                        height="200"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="职业病危害因素："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 6px;">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <p:inputText id="badRsnName" style="width: 180px;cursor: pointer;"
                                         value="#{mgrbean.selectBadRsnNames}" readonly="true"
                                         onclick="document.getElementById('tabView:mainForm:selBadRsnLink').click();"/>
                        </td>
                        <td style="border-color: transparent;">
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="selBadRsnLink"
                                           style="position: relative;left: -28px !important;"
                                           process="@this" action="#{mgrbean.selSimpleCodeAction}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5007"/>
                                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                                        process="@this" resetValues="true" update="badRsnName"/>
                            </p:commandLink>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -30px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this" update="badRsnName" action="#{mgrbean.clearSimpleCode}">
                                <f:setPropertyActionListener target="#{mgrbean.simpleCodeOpType}" value="5007"/>
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="启用状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 6px;">
                <p:selectManyCheckbox value="#{mgrbean.enabledState}">
                    <f:selectItem itemValue="0" itemLabel="启用"/>
                    <f:selectItem itemValue="1" itemLabel="停用"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="修订状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 6px;">
                <p:selectManyCheckbox value="#{mgrbean.fixState}">
                    <f:selectItem itemValue="1" itemLabel="修订中"/>
                    <f:selectItem itemValue="2" itemLabel="已修订"/>
                    <f:selectItem itemValue="3" itemLabel="修订失败"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="异常信息："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;" colspan="5">
                <zwx:SimpleCodeManyComp selectedIds="#{mgrbean.selectAbnormalInfoIds}"
                                        simpleCodeList="#{mgrbean.abnormalInfoList}"
                                        height="200"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column selectionMode="multiple" style="width:30px;text-align:center;padding: 0;"
                  disabledSelection="#{itm[14] eq 1}" rendered="#{mgrbean.hasFix}"/>
        <p:column headerText="地区" style="width: 200px;height:25px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="用人单位名称" style="width: 320px;padding-left: 8px;line-height: 20px;">
            <h:outputText value="#{itm[2]}" style="padding-right:8px;"/>
            <p:outputLabel
                    style="padding:3px;background:#E15F34;border-radius:2px;color: white;white-space:nowrap;"
                    rendered="#{itm[9] eq '1'}">
                <h:outputText value="分支机构"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="社会信用代码" style="width: 180px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="经济类型" style="width: 200px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="企业规模" style="width: 70px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="行业类别" style="width: 200px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
        <p:column headerText="预警数" style="width: 150px;padding-left: 8px;text-align: center;">
            <p:outputPanel style="display: flex;align-items: center;justify-content: center;">
                <div style="background-color: white;margin-left: 5px;width: 20px;height: 20px;padding: 1px;border-radius: 3px;border: #000000FF 1px solid;" title="待处理">
                    <p:outputLabel value="#{itm[16]}" style="color:black;vertical-align: sub;"/>
                </div>
                <div style="background-color: green;margin-left: 10px;width: 20px;height: 20px;padding: 1px;border-radius: 3px;border: green 1px solid;" title="已核实/已整改">
                    <p:outputLabel value="#{itm[17]}" style="color:white;vertical-align: sub;"/>
                </div>
                <div style="background-color: orange;margin-left: 10px;width: 20px;height: 20px;padding: 1px;border-radius: 3px;border: orange 1px solid;" title="正在整改">
                    <p:outputLabel value="#{itm[18]}" style="color:white;vertical-align: sub;"/>
                </div>
                <div style="background-color: red;margin-left: 10px;width: 20px;height: 20px;padding: 1px;border-radius: 3px;border: red 1px solid;" title="超期未处理">
                    <p:outputLabel value="#{itm[19]}" style="color:white;vertical-align: sub;"/>
                </div>
            </p:outputPanel>
        </p:column>
        <p:column headerText="启用状态" style="width: 70px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[11]}"/>
        </p:column>
        <p:column headerText="修订状态" style="width: 80px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[13]}"/>
        </p:column>
        <p:column headerText="操作">
            <p:commandLink value="详情" process="@this,:tabView:mainForm:mainGridNew" update=":page_view"
                           onclick="stop();" action="#{mgrbean.viewInitAction}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{mgrbean.hasFix}"/>
            <p:commandLink value="启用" process="@this" update="dataTable"
                           action="#{mgrbean.enableCrpt}" rendered="#{mgrbean.hasFix and itm[12] eq 1}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="停用" resetValues="true" process="@this" update=":tabView:mainForm:stopRsnDialog"
                           oncomplete="PF('StopRsnDialog').show();"
                           rendered="#{mgrbean.hasFix and itm[12] ne 1}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.stopRsn}" value=""/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[12] eq 1}"/>
            <p:commandLink value="停用原因" process="@this" update=":tabView:mainForm:stopRsnViewDialog"
                           oncomplete="PF('StopRsnViewDialog').show();" rendered="#{itm[12] eq 1}">
                <f:setPropertyActionListener target="#{mgrbean.stopRsn}" value="#{itm[15]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertDownMainContents">
        <p:dialog id="stopRsnDialog" header="停用原因" widgetVar="StopRsnDialog"
                  resizable="false" width="500" height="300" modal="true">
            <p:inputTextarea value="#{mgrbean.stopRsn}" style="resize:none;width:95%;height:95%;"
                             id="stopRsnContent" maxlength="200"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('StopRsnDialog').hide();"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         process="@this,stopRsnContent" update="dataTable"
                                         action="#{mgrbean.stopCrpt}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <p:dialog id="stopRsnViewDialog" header="停用原因" widgetVar="StopRsnViewDialog"
                  resizable="false" width="500" height="300" modal="true">
            <h:outputText value="#{mgrbean.stopRsn}" styleClass="cs-tal cs-fs-14 cs-break-word" escape="false"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="关闭" process="@this" onclick="PF('StopRsnViewDialog').hide();"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>