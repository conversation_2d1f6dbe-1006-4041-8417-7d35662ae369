<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.SiteMonitoringDataBean"-->
    <ui:param name="mgrbean" value="#{siteMonitoringDataBean}"/>
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
            function getDownloadFileClick(){
                document.getElementById("mainForm:downloadFileBtn").click();
            }
        </script>
        <style type="text/css">
            .myCalendar1 input {
                width: 78px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="场所监测数据导入"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="导入" icon="ui-icon-arrowreturnthick-1-n" id="importDialogBtn"
                                 process="@this" action="#{mgrbean.openImportDialog}"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:153px;height:38px;">
                <h:outputText value="年份："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 8px;width:248px;">
                <p:selectOneMenu style="width: 187px; text-align: left;margin-top: 3px;" editable="false"
                                 value="#{mgrbean.searchYear}">
                    <f:selectItem itemValue="" itemLabel="--请选择--"/>
                    <f:selectItems value="#{mgrbean.searchYearList}"
                                   var="itm" itemLabel="#{itm}" itemValue="#{itm}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:153px;height:38px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 0;width:246px;" id="zoneCol">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.searchZoneList}"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="8"
                                       ifShowTrash="#{true}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:153px;">
                <h:outputText value="企业ID："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchUuId" value="#{mgrbean.searchUuId}" style="width: 180px;" maxlength="32"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchUnitName" value="#{mgrbean.searchUnitName}" style="width: 180px;"
                             maxlength="300"/>
            </p:column>
            <p:column style="text-align:right;height:38px;">
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchCreditCode" value="#{mgrbean.searchCreditCode}" style="width: 180px;"
                             maxlength="18"/>
            </p:column>
            <p:column style="text-align:right;height:38px;">
                <h:outputText value="导入数据："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectManyCheckbox value="#{mgrbean.searchDataTypeList}" converter="javax.faces.Integer">
                    <f:selectItem itemValue="1" itemLabel="调查信息"/>
                    <f:selectItem itemValue="2" itemLabel="结果信息"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="年份" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column headerText="地区" style="width: 222px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width: 350px;padding-left: 8px;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="社会信用代码" style="width: 222px;text-align: center;">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="企业ID" style="width: 222px;text-align: center;">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="调查信息" style="width: 120px;text-align: center;">
            <h:outputLabel value="未导入" rendered="#{itm[6] eq 0}"/>
            <h:outputLabel value="已导入" rendered="#{itm[6] ne 0}"/>
        </p:column>
        <p:column headerText="结果信息" style="width: 120px;text-align: center;">
            <h:outputLabel value="未导入" rendered="#{itm[7] eq 0}"/>
            <h:outputLabel value="已导入" rendered="#{itm[7] ne 0}"/>
        </p:column>
    </ui:define>
    <!-- 其他内容 -->
    <ui:define name="insertDialogs">
        <p:dialog header="场所监测数据导入" id="importDialog" widgetVar="ImportDialog" modal="true" width="500"
                  height="150"
                  resizable="false" closeOnEscape="true">
            <p:panelGrid id="importTable">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="数据类型："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left: 9px;width:302px;">
                        <p:selectOneMenu style="width: 187px; text-align: left;margin-top: 3px;" editable="false"
                                         value="#{mgrbean.importDataType}">
                            <f:selectItem itemValue="" itemLabel="--请选择--"/>
                            <f:selectItem itemValue="0" itemLabel="基本信息"/>
                            <f:selectItem itemValue="1" itemLabel="调查信息"/>
                            <f:selectItem itemValue="2" itemLabel="结果信息"/>
                            <p:ajax event="change" process="@this"
                                    update="importTable"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                        <h:outputText value="*" style="color:red;" rendered="#{mgrbean.importDataType == 0}"/>
                        <p:outputLabel value="年份："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left: 9px;width:302px;">
                        <p:selectOneMenu style="width: 187px; text-align: left;margin-top: 3px;" editable="false"
                                         value="#{mgrbean.importYear}" rendered="#{mgrbean.importDataType == 0}">
                            <f:selectItems value="#{mgrbean.searchYearList}"
                                           var="itm" itemLabel="#{itm}" itemValue="#{itm}"/>
                        </p:selectOneMenu>
                        <p:selectOneMenu style="width: 187px; text-align: left;margin-top: 3px;" editable="false"
                                         value="#{mgrbean.importYear}" disabled="true"
                                         rendered="#{mgrbean.importDataType != 0}">
                            <f:selectItem itemValue="" itemLabel=""/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                        <p:outputLabel value="导入："/>
                    </p:column>
                    <p:column style="text-align:left;width:160px;">
                        <p:menuButton  value="导入" style="margin-left: 6px;">
                            <p:menuitem value="模板下载" icon="ui-icon-arrowthickstop-1-s" action="#{mgrbean.templateImportBefore}" />
                            <p:menuitem value="导入" icon="ui-icon-arrowreturnthick-1-n" id="importBtn"
                                        update="uploadFileDialog"
                                        action="#{mgrbean.importBefore}">
                            </p:menuitem>
                        </p:menuButton>
                        <p:commandButton style="display: none;"  id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                         onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);hideTooltips();">
                            <p:fileDownload value="#{mgrbean.getTemplateFiles()}"/>
                        </p:commandButton>
                        <p:spacer width="5"/>
                        <p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                         process="@this"
                                         onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
                                         rendered="#{null ne mgrbean.importErrFilePath}">
                            <p:fileDownload value="#{mgrbean.errorImportFile}"/>
                        </p:commandButton>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
        <p:dialog header="导入" widgetVar="UploadFileDialog" id="uploadFileDialog" resizable="false"
                  modal="true" width="600">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持文件格式为：xlsx）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要文件上传！" styleClass="table-border-none"
                                      id="fileUpload"
                                      process="@this" fileUploadListener="#{mgrbean.importDataAction}"
                                      label="选择文件" invalidSizeMessage="文件大小不能超过100M!"
                                      validatorMessage="上传出错啦，请重新上传！"
                                      allowTypes="/(\.|\/)(xlsx)$/" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！"
                                      invalidFileMessage="只能上传xlsx格式的文件！"
                                      previewWidth="120" cancelLabel="取消"
                                      uploadLabel="导入" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                      dragDropSupport="true" mode="advanced" sizeLimit="104857600"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>