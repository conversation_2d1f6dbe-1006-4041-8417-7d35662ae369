<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <p:confirmDialog id="reviewConfirmDialog" message="确定要批量审核通过吗？" header="消息确认框" widgetVar="ReviewConfirmDialog" >
        <p:commandButton value="确定" action="#{mgrbean.reviewBatchAction}"
                         update="dataTable" icon="ui-icon-check"
                         oncomplete="PF('ReviewConfirmDialog').hide();datatableOffClick();"/>
        <p:commandButton value="取消" icon="ui-icon-close"
                         onclick="PF('ReviewConfirmDialog').hide();datatableOffClick()"
                         type="button"/>
    </p:confirmDialog>

</ui:composition>