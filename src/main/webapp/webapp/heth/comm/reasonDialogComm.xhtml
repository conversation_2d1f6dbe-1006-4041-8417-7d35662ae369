<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

    <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
              height="300" header="退回原因" resizable="false" modal="true">
        <h:inputText
                style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
        <p:inputTextarea value="#{mgrbean.backRsn}"
                         style="resize:none;width:97%;height:95%;" autoResize="false"
                         id="reasonContent" maxlength="100" readonly="#{mgrbean.readOnly}"/>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: right;">
                <h:panelGroup>
                    <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                     process="@this" immediate="true" />
                    <p:spacer width="5" />
                    <p:commandButton value="确定" styleClass="submit_btn"
                                     process="@this,reasonContent" oncomplete="datatableOffClick()"
                                     action="#{mgrbean.returnAction}"
                                     rendered="#{!mgrbean.readOnly}" />
                    <p:commandButton value="确定" styleClass="submit_btn" oncomplete="datatableOffClick()"
                                     onclick="PF('ReasonDialog').hide();"
                                     rendered="#{mgrbean.readOnly}" />
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:composition>