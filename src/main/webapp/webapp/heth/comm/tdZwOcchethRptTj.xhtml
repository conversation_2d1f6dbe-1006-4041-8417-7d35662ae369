<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 78px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="技术服务申报统计"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" oncomplete="datatableOffClick()"
                                 action="#{tdZwOcchethRptTjBean.searchAction}"
                                 process="@this,mainForm" update=":mainForm:dataTablePanel"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="用人单位地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;">
                <p:outputPanel id="searchCrptZoneCode">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <zwx:ZoneSingleNewComp  zoneList="#{tdZwOcchethRptTjBean.zoneList}"
                                                zoneCode="#{tdZwOcchethRptTjBean.searchCrptZoneCode}"
                                                zoneName="#{tdZwOcchethRptTjBean.searchCrptZoneName}"
                                                   onchange="document.getElementById('mainForm:clearCrptLink').click();"/>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -26px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this,searchCrptZoneCode" update="searchCrptZoneCode" action="#{tdZwOcchethRptTjBean.clearCrptZoneCode}"
                            oncomplete="document.getElementById('mainForm:clearCrptLink').click();">
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="用人单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="border-color: transparent;">
                                <p:inputText id="searchCrptNames"
                                             value="#{tdZwOcchethRptTjBean.selectCrptNames}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('mainForm:selCrptLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selCrptLink"
                                               action="#{tdZwOcchethRptTjBean.selCrptAction}" process="@this,searchCrptZoneCode"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{tdZwOcchethRptTjBean.onSelectCrptAction}" process="@this"
                                            resetValues="true" update="searchCrptNames" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -33px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" id="clearCrptLink"
                                               process="@this" update="searchCrptNames" action="#{tdZwOcchethRptTjBean.clearCrptNames}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="行业类别：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="border-color: transparent;">
                            <p:inputText id="indusTypeName"
                                         value="#{tdZwOcchethRptTjBean.selectIndusTypeNames}"
                                         style="width: 180px;cursor: pointer;"
                                         onclick="document.getElementById('mainForm:selIndusTypeLink').click();"
                                         readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selIndusTypeLink"
                                               action="#{tdZwOcchethRptTjBean.selSimpleCodeAction}" process="@this"
                                               style="position: relative;left: -28px !important;">
                                    <f:setPropertyActionListener target="#{tdZwOcchethRptTjBean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                                    <p:ajax event="dialogReturn" listener="#{tdZwOcchethRptTjBean.onSimpleCodeAction}" process="@this"
                                            resetValues="true" update="indusTypeName" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -33px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="indusTypeName" action="#{tdZwOcchethRptTjBean.clearSimpleCode}">
                                    <f:setPropertyActionListener target="#{tdZwOcchethRptTjBean.simpleCodeType}" value="5002"></f:setPropertyActionListener>
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="经济性质：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="border-color: transparent;position: relative;left:7px;">
                                <p:inputText id="economyName"
                                             value="#{tdZwOcchethRptTjBean.selectEconomyNames}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('mainForm:selEconomyLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selEconomyLink"
                                               action="#{tdZwOcchethRptTjBean.selSimpleCodeAction}" process="@this"
                                               style="position: relative;left: -21px !important;">
                                    <f:setPropertyActionListener target="#{tdZwOcchethRptTjBean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                                    <p:ajax event="dialogReturn" listener="#{tdZwOcchethRptTjBean.onSimpleCodeAction}" process="@this"
                                            resetValues="true" update="economyName" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -26px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               process="@this" update="economyName" action="#{tdZwOcchethRptTjBean.clearSimpleCode}">
                                    <f:setPropertyActionListener target="#{tdZwOcchethRptTjBean.simpleCodeType}" value="5003"></f:setPropertyActionListener>
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="企业规模：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px">
                <zwx:SimpleCodeManyComp selectedIds="#{tdZwOcchethRptTjBean.selectCrptSizeIds}"
                                        simpleCodeList="#{tdZwOcchethRptTjBean.crptSizeList}"
                                        height="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="职业病危害风险分类：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <zwx:SimpleCodeManyComp selectedIds="#{tdZwOcchethRptTjBean.selectZybRiskIds}"
                                        simpleCodeList="#{tdZwOcchethRptTjBean.zybRiskList}"
                                        height="200"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="技术服务机构地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:250px;">
                <p:outputPanel id="searchOccZoneCode">
                <table>
                    <tr>
                        <td style="padding: 0;border-color: transparent;">
                            <zwx:ZoneSingleNewComp  zoneList="#{tdZwOcchethRptTjBean.orgZoneList}"
                                                zoneCode="#{tdZwOcchethRptTjBean.searchOccZoneCode}"
                                                zoneName="#{tdZwOcchethRptTjBean.searchOccZoneName}"
                                                   onchange="document.getElementById('mainForm:clearOccLink').click();"/>
                        </td>
                        <!-- 清空 -->
                        <td style="border-color: transparent;position: relative;left: -26px;">
                            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                           process="@this,searchOccZoneCode" update="searchOccZoneCode" action="#{tdZwOcchethRptTjBean.clearOccZoneCode}"
                            oncomplete="document.getElementById('mainForm:clearOccLink').click();">
                            </p:commandLink>
                        </td>
                    </tr>
                </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;;">
                <h:outputText value="技术服务机构名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px">
                <!-- 弹出框 -->
                <p:outputPanel>
                    <table>
                        <tr>
                            <td style="border-color: transparent;">
                                <p:inputText id="searchOccNames"
                                             value="#{tdZwOcchethRptTjBean.selectOccNames}"
                                             style="width: 180px;cursor: pointer;"
                                             onclick="document.getElementById('mainForm:selOccLink').click();"
                                             readonly="true"/>
                            </td>
                            <td style="border-color: transparent;">
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selOccLink"
                                               action="#{tdZwOcchethRptTjBean.selOccAction}" process="@this,searchOccZoneCode"
                                               style="position: relative;left: -28px !important;">
                                    <p:ajax event="dialogReturn" listener="#{tdZwOcchethRptTjBean.onSelectOccAction}" process="@this"
                                            resetValues="true" update="searchOccNames" />
                                </p:commandLink>
                            </td>
                            <!-- 清空 -->
                            <td style="border-color: transparent;position: relative;left: -33px;">
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" id="clearOccLink"
                                               process="@this" update="searchOccNames" action="#{tdZwOcchethRptTjBean.clearOccNames}">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="报告日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:outputPanel style="padding-left: 6px;">
                <zwx:CalendarDynamicLimitComp startDate="#{tdZwOcchethRptTjBean.searchStartDate}"
                                              endDate="#{tdZwOcchethRptTjBean.searchEndDate}"  styleClass="myCalendar1"/>
                </p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="统计维度：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
                <p:selectOneRadio id="searchDimension" value="#{tdZwOcchethRptTjBean.searchDimension}" style="width: 850px;">
                    <f:selectItem itemLabel="用人单位地区" itemValue="1" />
                    <f:selectItem itemLabel="用人单位名称" itemValue="2" />
                    <f:selectItem itemLabel="行业类别" itemValue="3" />
                    <f:selectItem itemLabel="经济性质" itemValue="4" />
                    <f:selectItem itemLabel="企业规模" itemValue="5" />
                    <f:selectItem itemLabel="技术服务机构地区" itemValue="6" />
                    <f:selectItem itemLabel="技术服务机构名称" itemValue="7" />
                </p:selectOneRadio>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 内容列 -->
    <ui:define name="insertContent">
        <p:outputPanel id="dataTablePanel" styleClass="ui-panel ui-widget ui-widget-content ui-corner-all">
            <p:dataTable id="recoveryTable" styleClass="ui-panel-content ui-widget-content"
                         value="#{tdZwOcchethRptTjBean.dataList}" var="dataItem" paginator="false"
                         emptyMessage="没有您要找的记录！" >
                <c:forEach items="#{tdZwOcchethRptTjBean.headerValueList}" var="headerValue" varStatus="varIndex">
                    <p:column headerText="#{headerValue}" style="#{varIndex.index==0?'width:100px;':'width:70px;'}text-align:center;">
                        <h:outputText value="#{dataItem[varIndex.index]}" style="#{'合计'.equals(dataItem[varIndex.index])?'font-weight: bold;':''}"/>
                    </p:column>
                </c:forEach>
            </p:dataTable>
        </p:outputPanel>
    </ui:define>

</ui:composition>