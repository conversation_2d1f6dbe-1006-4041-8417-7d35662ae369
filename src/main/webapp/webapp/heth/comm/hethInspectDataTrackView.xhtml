<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate_new.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.comm.web.HethInspectDataTrackBean"-->
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="职业健康检查数据上传情况跟踪"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="buttons" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 process="@this" update=":tabView" action="#{mgrbean.backAction}"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();windowScrollTop();"/>
            </h:panelGrid>
            <p:sticky target="buttons"/>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <!--国家失败原因-->
        <p:panelGrid style="margin-top: 10px;width:100%" rendered="#{mgrbean.showNationErrorMsg}">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="国家失败原因"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column
                        style="text-align:left;height: 30px;padding: 5px 0 0 53px;line-height: 30px;line-break: anywhere;border-right: 0;">
                    <p:outputLabel value="#{mgrbean.nationErrorMsg}"/>
                </p:column>
                <p:column style="text-align:left;height: 30px;border-left: 0;width: 30%;">
                </p:column>
            </p:row>
        </p:panelGrid>
        <!--历次审核意见-->
        <p:panelGrid style="margin-top: 10px;width:100%" rendered="#{mgrbean.ifNeedBhkAudit}">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="历次审核意见"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column>
                    <p:dataTable value="#{mgrbean.historyList}" var="item" emptyMessage="暂无数据！">
                        <p:column headerText="审核类型" style="width:100px;text-align: center">
                            <p:outputLabel value="#{item[4]}"/>
                        </p:column>
                        <p:column headerText="审核意见" style="width:510px;">
                            <p:outputLabel value="#{item[1]}"/>
                        </p:column>
                        <p:column headerText="审核人" style="width:150px;text-align: center">
                            <p:outputLabel value="#{item[2]}"/>
                        </p:column>
                        <p:column headerText="审核日期" style="width:140px;text-align: center">
                            <p:outputLabel value="#{item[3]}">
                                <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8"/>
                            </p:outputLabel>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!--异常情况-->
        <p:panelGrid style="margin-top: 10px;width:100%" rendered="#{mgrbean.ifNeedBhkAudit}">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; " colspan="6">
                        <p:outputLabel value="异常情况"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row rendered="#{mgrbean.unAbnormalsList.size() == 0}">
                <p:column style="text-align:left;height: 30px;padding: 5px 0 5px 53px;">
                    <p:outputLabel value="无"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.unAbnormalsList.size() > 0}">
                <p:column style="text-align:left;height: 30px;padding: 5px 0 0 53px;" colspan="3">
                    <p:outputLabel value="提示：" style="color: red"/>
                </p:column>
            </p:row>
            <c:forEach items="#{mgrbean.unAbnormalsList}" varStatus="unAbnormal" var="itm">
                <p:row>
                    <p:column
                            style="text-align:left;height: 30px; padding-left: 83px;border: 0;width: 20px;vertical-align: top">
                        <h:outputText style="line-height: 30px;" value="#{unAbnormal.index+1}、"/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;border: 0;">
                        <h:outputText value="#{itm}" style="line-height: 30px;"/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;border-left: 0; width: 30%">
                    </p:column>
                </p:row>
            </c:forEach>
        </p:panelGrid>
        <!--公用界面：体检基本信息-->
        <ui:include src="/webapp/heth/comm/tbTjBhkInfo.xhtml">
            <ui:param name="tjBhkInfoBean" value="#{mgrbean.tjBhkInfoBean}"/>
            <ui:param name="pageParam" value="1"/>
            <ui:param name="birthIfShow" value="true"/>
        </ui:include>
    </ui:define>
</ui:composition>