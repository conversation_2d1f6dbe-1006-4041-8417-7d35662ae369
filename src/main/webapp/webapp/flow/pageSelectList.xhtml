<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>流程页面选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>

    <h:body style="overflow-y:hidden;">
        <h:form id="mainForm">
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5">
                            <p:outputLabel value="系统类型：" styleClass="zwx_dialog_font" />
                            <p:selectOneMenu id="itmSort" value="#{pageSelectBean.selectItmSort}" style="width: 160px;" onchange="startSearch()">
                                <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                <f:selectItems value="#{pageSelectBean.itmSortMap}"/>
                            </p:selectOneMenu>
                            <p:remoteCommand name="startSearch" action="#{pageSelectBean.searchAction}" process="@this,itmSort" update="dataTable"/>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{pageSelectBean.displayList}" id="dataTable" paginator="true" rows="10"
                         emptyMessage="没有数据！" paginatorPosition="top"  >
                <p:column headerText="选择" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{pageSelectBean.selectAction}" process="@this" >
                        <f:setPropertyActionListener value="#{itm}" target="#{pageSelectBean.tdFlowNodePage}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="系统类型" style="width: 120px;padding-left: 3px;">
                    <h:outputText value="#{itm.systemType.typeCN}" />
                </p:column>
                <p:column headerText="页面描述" style="padding-left: 3px;">
                    <h:outputText value="#{itm.pageDesc}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
    </h:body>
</f:view>
</html>
