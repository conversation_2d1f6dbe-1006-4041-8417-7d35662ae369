<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title>拟办选人</title>
		<style type="text/css">
            .ui-picklist .ui-picklist-list {
                text-align: left;
                height: 200px;
                width: 345px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
</style>
        <script type="text/javascript">
        //<![CDATA[
        function selectComp() {
        		var arr = new Array();
        		arr[0] =document.getElementById("personForm:selectedManIds").value;
        		arr[1] =document.getElementById("personForm:selectedManNames").value;
        		 window.returnValue=arr;
                 window.close();
        }           
        //]]>
        </script>
	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:form id="personForm">
			<p:outputPanel styleClass="zwx_toobar_42" style="margin-top:5px;">
				<h:panelGrid columns="3">
					<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
			        <p:commandButton value="保存" icon="ui-icon-check" id="personSaveBtn" action="#{tdFlowPersonDialogSelectBean.personSelectAction}" 
			        	process="@form"  update="selectedManIds,selectedManNames" oncomplete="selectComp();"/>
	                  <p:commandButton value="取消" icon="ui-icon-close" id="personBackBtn"  process="@this"  onclick="window.close();"/>
				</h:panelGrid>
			</p:outputPanel>		
			<h:inputHidden id="selectedManIds" value="#{tdFlowPersonDialogSelectBean.selectedManIds}"/>
        	<h:inputHidden id="selectedManNames" value="#{tdFlowPersonDialogSelectBean.selectedManNames}"/>
        	<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<table width="100%">
					<tr>
						<td width="60" style="text-align: right;padding-right: 3px">科室：</td>
						<td style="text-align: left;padding-right: 3px">
							<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
								<p:inputText id="userOfficeName" value="#{tdFlowPersonDialogSelectBean.userOfficeName}" readonly="true" style="width: 120px;" /> 
								<h:inputHidden id="userOfficeRid" value="#{tdFlowPersonDialogSelectBean.userOfficeRid}" /> 
								<p:commandLink styleClass="ui-icon ui-icon-search" id="initOfficeTreeLink" process="@this"
									style="position: relative;left: -30px;top:0px;" oncomplete="PF('UserOfficePanel').show()" /> 
							</h:panelGrid>	
							<p:overlayPanel id="userOfficePanel" for="userOfficeName" dynamic="false" style="width:280px;"
								widgetVar="UserOfficePanel">
								<p:tree value="#{tdFlowPersonDialogSelectBean.userOfficeTreeNode}" var="node" selectionMode="single" id="userOfficeTree" style="width: 250px;height: 200px;overflow-y: auto;">

									<p:ajax event="select" update=":personForm:userOfficeName,:personForm:userOfficeRid, :personForm:userPickList" listener="#{tdFlowPersonDialogSelectBean.onOfficeNodeSelect}"
										oncomplete="PF('UserOfficePanel').hide();" process="@this, :personForm:userPickList" />

									<p:treeNode>
										<h:outputText value="#{node.officename}" />
									</p:treeNode>
								</p:tree>
							</p:overlayPanel>
						</td>
					</tr>
				</table>
			</p:fieldset>
			<p:pickList id="userPickList" value="#{tdFlowPersonDialogSelectBean.dualListModel}" var="user" itemValue="#{user}" itemLabel="#{user.username}" converter="system.UserOfficeFilterConvert"
				showSourceControls="false" showTargetControls="false" showCheckbox="true" showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains" effect="drop">

				<f:facet name="sourceCaption">可选用户</f:facet>
				<f:facet name="targetCaption">已选用户</f:facet>

				<p:column style="width:50%;text-align: left;" >#{user.username}</p:column>
				<p:column style="width:50%;text-align: left;" >#{user.officeNames}</p:column>
			</p:pickList>
		</h:form>
	</h:body>
</f:view>
</html>