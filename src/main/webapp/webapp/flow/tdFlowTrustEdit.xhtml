<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
	</h:head>
	<h:body>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<h:form id="mainForm">
			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
				id="titleGrid">
				<f:facet name="header">
					<p:row>
						<p:column colspan="4"
							style="text-align:left;padding-left:5px;height: 20px;">
							<h:outputText value="流程委托" />
						</p:column>
					</p:row>
				</f:facet>
			</p:panelGrid>
			<p:outputPanel id="buttonsPanel">
				<p:outputPanel styleClass="zwx_toobar_42">
					<h:panelGrid columns="3"
						style="border-color:transparent;padding:0px;">
						<span class="ui-separator"> <span
							class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
							action="#{tdFlowTrustEditBean.saveAction}" process="@form" />
						<p:commandButton value="返回" icon="ui-icon-close"
							action="#{tdFlowTrustEditBean.backAction}" process="@this" />
					</h:panelGrid>
				</p:outputPanel>
			</p:outputPanel>
			<p:sticky target="buttonsPanel" margin="0" />

			<p:panelGrid style="width:100%">
				<p:row>
					<p:column
						style="width:120px; text-align:right;padding-right:0px; height:28px">
						<font color="red">*</font>
						<p:outputLabel value="委托给：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<h:panelGrid columns="4"
							style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText id="username"
								value="#{tdFlowTrustEditBean.user==null?'':tdFlowTrustEditBean.user.username}"
								style="width: 120px;cursor:pointer;"
								onclick="document.getElementById('mainForm:selUserLink').click();"
								required="true" requiredMessage="请选择委托人" readonly="true" />
							<p:commandLink styleClass="ui-icon ui-icon-search"
								id="selUserLink"
								action="#{tdFlowTrustEditBean.selectUserInitAction()}"
								process="@this" style="position: relative;left: -30px;">
								<p:ajax event="dialogReturn"
									listener="#{tdFlowTrustEditBean.onUserSelect}" process="@this"
									resetValues="true" update="username" />
							</p:commandLink>
							<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn"
								title="清空" update="username"
								action="#{tdFlowTrustEditBean.clearUser()}" process="@this"
								style="position: relative;left: -25px;">
							</p:commandLink>
						</h:panelGrid>
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="width:120px; text-align:right;padding-right:3px; height:28px">
						<font color="red">*</font>
						<p:outputLabel value="委托时间：" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;">
						<p:calendar pattern="yyyy-MM-dd" maxlength="10"
							showOtherMonths="true" id="beginTime" navigator="true"
							yearRange="c-10:c+10" size="11" showButtonPanel="true"
							converterMessage="开始时间，格式输入不正确！" required="true"
							requiredMessage="请选择开始时间！" mindate="new Date()"
							value="#{tdFlowTrustEditBean.editBean.beginTime}" />
							~<p:calendar pattern="yyyy-MM-dd" maxlength="10"
							showOtherMonths="true" id="endTime" navigator="true"
							yearRange="c-10:c+10" size="11" showButtonPanel="true"
							converterMessage="结束时间，格式输入不正确！" required="true"
							requiredMessage="请选择结束时间！" mindate="new Date()"
							value="#{tdFlowTrustEditBean.editBean.endTime}" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<p:fieldset legend="委托流程">
				<p:dataTable var="itm" value="#{tdFlowTrustEditBean.displayList}"
					emptyMessage="暂无流程定义!" id="flowTable">
					<p:columnGroup type="header">
						<p:row>
							<p:column>
								<f:facet name="header">
									<h:outputText value="流程名称" />
								</f:facet>
							</p:column>
							<p:column>
								<f:facet name="header">
									<h:outputText value="流程委托" />
								</f:facet>
							</p:column>
						</p:row>
					</p:columnGroup>
					<p:column
						style="width:20%;padding-left:#{itm.level*15}px;background:#{itm.level == 0 ? '#A5FFC5':'transparent'}">
						<h:outputText value="#{itm.name}" />
					</p:column>
					<p:column
						style="background:#{itm.level  == 0 ? '#A5FFC5':'transparent'}">
						<table style="border-color: transparent;margin: 0px;padding: 0px;">
							<tr>
								<td style="width: 10px"><p:selectBooleanCheckbox
										value="#{itm.selected}">
										<p:ajax process="@this,:mainForm:flowTable"
											update=":mainForm:flowTable"
											listener="#{tdFlowTrustEditBean.onTypeSelect(itm.rid,itm.level,itm.selected)}" />
									</p:selectBooleanCheckbox></td>
							</tr>
						</table>
					</p:column>
				</p:dataTable>
			</p:fieldset>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
<!-- 带转向、真分页的模板 -->