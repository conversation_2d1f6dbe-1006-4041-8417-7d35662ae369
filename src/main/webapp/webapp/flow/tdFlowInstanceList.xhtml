<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:form id="mainForm">
            <h:outputStylesheet name="css/default.css"/>
            <!-- 无查询条件 -->
            <ui:param name="condition" value="1"/>

            <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                <f:facet name="header">
                    <p:row>
                        <p:column  style="text-align:left;padding-left:5px;height: 20px;">
                            <h:outputText value="发起流程"/>
                        </p:column>
                    </p:row>
                </f:facet>
            </p:panelGrid>

            <p:outputPanel id="buttonsPanel">
                <ui:insert name="insertButtons"/>
            </p:outputPanel>

            <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{condition==null}">
                <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                    <ui:insert name="insertSearchConditons"/>
                </p:panelGrid>
            </p:fieldset>

            <table style="width:100%;">
                <tr style="vertical-align: top">
                    <td width="50%">
                        <ui:repeat var="v"  value="#{tdFlowInstanceBean.leftList}">
                            <ui:include src="/WEB-INF/templates/flow/flowList.xhtml">
                                <ui:param name="tdFlowType" value="#{v}"/>
                            </ui:include>
                        </ui:repeat>
                    </td>
                    <td width="50%">
                        <ui:repeat var="v"  value="#{tdFlowInstanceBean.rightList}">
                            <ui:include src="/WEB-INF/templates/flow/flowList.xhtml">
                                <ui:param name="tdFlowType" value="#{v}"/>
                            </ui:include>
                        </ui:repeat>
                    </td>
                </tr>
            </table>
        </h:form>
        <!-- 引入用户自定义皮肤 -->
        <link type="text/css" rel="stylesheet" href="/javax.faces.resource/theme.css.faces?ln=primefaces-#{facesContext.externalContext.sessionMap['SESSION_DATA'].skinName}" />
    </h:body>
</f:view>
</html>






