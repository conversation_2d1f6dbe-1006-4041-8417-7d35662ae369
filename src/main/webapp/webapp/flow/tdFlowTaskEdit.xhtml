<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<style type="text/css">
#dialogBoxId{
 top: 20% !important;
 left:50% !important;
}		
		</style>
		<script type="text/javascript">
            //<![CDATA[
            function sendShortMsg() {
            		var url = "#{request.scheme}" + "://" + "#{request.serverName}"+":"+"#{request.serverPort}"+"#{request.contextPath}"+"/webapp/system/msgDialogSend.faces";
            		window.showModalDialog(url,"","dialogWidth=940px;dialogHeight=460px;center:yes;scroll:0;");
            }     
            
			/**保存按钮点击执行的脚本，用于被重写*/
			function zwx_flow_save_before() {}
			/**保存按钮执行完成的脚本，用于被重写*/
			function zwx_flow_save_complete(xhr, status, args){}
			/**提交按钮点击执行的脚本，用于被重写*/
			function zwx_flow_submit_before() {}
			/**提交按钮执行完成的脚本，用于被重写*/
			function zwx_flow_submit_complete(xhr, status, args){}
			/**提交按钮执行的弹出框弹出之前的脚本，用于被重写*/
			function zwx_flow_submit_dialogbefore() {}    
			/**提交按钮执行的弹出框返回事件执行后的脚本，用于被重写*/
			function zwx_flow_submit_dialogrtn() {}    
			/**驳回按钮点击执行的脚本，用于被重写*/
			function zwx_flow_notAgree_before() {}
			/**驳回按钮执行的弹出框返回事件执行后的脚本，用于被重写*/
			function zwx_flow_notAgree_dialogrtn() {}   
			/**驳回按钮关闭执行的脚本，用于被重写*/
			function zwx_flow_notAgree_complete(xhr, status, args) {}
			/**流程图按钮点击执行的脚本，用于被重写*/
			function zwx_flow_processPic_before() {}
	        /**pf dialog framework, icon close event listener*/
	        function zwx_dialog_close_listener() {}			
			
			/**回首界面*/
			function zwx_flow_back2list() {
				var as=document.getElementById("tabView:editForm:actionState").value;
				if(as==1 || as==2) {
					document.getElementById('tabView:editForm:backBtn1').click();
//				}else if(as==2)	{
// 					top.win_TabMenu.OpenTabWin("m01", '信息查看', 'webapp/system/tdMsgMainSearchList.faces?param1=1', 'm03');
// 					top.win_TabMenu.OpenTabWin("m01", '待办任务', 'webapp/flow/tdFlowTaskList.faces?param1=1', 'm03');
				}
			}
					function close_current_menu() {
						var current_tab_id = 'tab-button-' + top.win_TabMenu.iTabIndex;
						top.CloseTabWin(current_tab_id);
						}
			
			jQuery(window).load(function () {
				PF('TestButton').getJQ().click();
			});
			
			function WebOpenPrint(){
			}
            //]]>
        </script>


	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<p:tabView id="tabView" dynamic="true" style="border:1px; padding:0px;">
			<p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<h:form id="editForm">
					<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="mainTitleGrid">
						<f:facet name="header">
							<p:row>
								<p:column style="text-align:left;padding-left:5px;height: 20px;">
									<h:outputText value="任务处理" />
									<p:graphicImage name="flow/processed.png" library="images" rendered="#{(tdFlowTaskEditBean.readOnly) and (!tdFlowTaskEditBean.ff)}"
										style="position: absolute;left: 250px;top: 115px;-webkit-transform:rotate(340deg); -webkit-transform-origin:10px 20px;"/>
								</p:column>
							</p:row>
						</f:facet>
					</p:panelGrid>

					<p:outputPanel styleClass="zwx_toobar_h_auto" id="editBtnsPanel">
						<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
						<!-- 保存 -->
						<p:commandButton icon="ui-icon-check" id="saveBtn" action="#{tdFlowTaskEditBean.saveAction}" process="@this,businessPanel" 
							value="#{tdFlowTaskEditBean.btnDispMap['0'].flowBtnName}" update="editBtnsPanel,businessPanel"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['0'].disp)}"
							onclick="zwx_flow_save_before()" oncomplete="zwx_flow_save_complete(xhr, status, args)">
							<f:setPropertyActionListener target="#{tdFlowTaskEditBean.actionType}" value="saveAction" />
						</p:commandButton>
						
						<!-- 提交 -->
						<p:commandButton icon="ui-icon-circle-triangle-e" id="submitBtn" action="#{tdFlowTaskEditBean.submitAction}" process="@this,businessPanel"
							value="#{tdFlowTaskEditBean.btnDispMap['1'].flowBtnName}" update="editBtnsPanel"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['1'].disp)}"
							onclick="zwx_flow_submit_before()" oncomplete="zwx_flow_submit_complete(xhr, status, args);zwx_dialog_close_listener();">
							<f:setPropertyActionListener target="#{tdFlowTaskEditBean.actionType}" value="submitAction" />
							<p:ajax event="dialogReturn" listener="#{tdFlowTaskEditBean.onPersonSelect}" onstart="zwx_flow_submit_dialogbefore();"
								oncomplete="zwx_flow_submit_dialogrtn();zwx_flow_back2list();" update="actionState" />
						</p:commandButton>
						
						<!-- 驳回 -->
						<p:commandButton icon="ui-icon-circle-triangle-w" id="notAgreeBtn" action="#{tdFlowTaskEditBean.backProcessInit}" 
							value="#{tdFlowTaskEditBean.btnDispMap['2'].flowBtnName}" onclick="zwx_flow_notAgree_before();"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.tdFlowNode.nums > 1) and (tdFlowTaskEditBean.btnDispMap['2'].disp)}"
							process="@this" oncomplete="zwx_dialog_close_listener();">
							<p:ajax event="dialogReturn" listener="#{tdFlowTaskEditBean.backProcessAction}" oncomplete="zwx_flow_notAgree_dialogrtn();zwx_flow_back2list();" update="actionState"/>
						</p:commandButton>
						
						<!-- 删除 -->
						<p:commandButton icon="ui-icon-trash" id="deleteBtn" actionListener="#{tdFlowTaskEditBean.deleteAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['3'].flowBtnName}"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.tdFlowNode.nums == 1) and (tdFlowTaskEditBean.btnDispMap['3'].disp)}"								
							process="@this">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
						</p:commandButton>	
						
						<!-- 终止 -->
						<p:commandButton icon="ui-icon-arrowthickstop-1-e" id="terminateBtn" actionListener="#{tdFlowTaskEditBean.terminateAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['11'].flowBtnName}"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['11'].disp)}"								
							process="@this,businessPanel">
							<p:confirm header="消息确认框" message="确定要#{tdFlowTaskEditBean.btnDispMap['11'].flowBtnName}吗？" icon="ui-icon-alert"/>
						</p:commandButton>
						
						<!-- 办结 -->
						<p:commandButton icon="ui-icon-circle-check" id="finishBtn" actionListener="#{tdFlowTaskEditBean.finishAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['12'].flowBtnName}"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['12'].disp)}"								
							process="@this,businessPanel">
							<p:confirm header="消息确认框" message="确定要#{tdFlowTaskEditBean.btnDispMap['12'].flowBtnName}吗？" icon="ui-icon-alert"/>
						</p:commandButton>
						
						<!-- 完成 -->
						<p:commandButton icon="ui-icon-circle-check" id="finishSelfBtn" actionListener="#{tdFlowTaskEditBean.finishSelfAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['13'].flowBtnName}"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['13'].disp)}"								
							process="@this,businessPanel">
							<p:ajax event="dialogReturn" listener="#{tdFlowTaskEditBean.onFinishSelf}" onstart="zwx_flow_submit_dialogbefore();"
								oncomplete="zwx_flow_submit_dialogrtn();zwx_flow_back2list();" update="actionState" />
						</p:commandButton>
							
						<!-- 分发 -->
						<p:commandButton icon="ui-icon-person" id="ffBtn" actionListener="#{tdFlowTaskEditBean.ffInitAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['10'].flowBtnName}" process="@this"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['10'].disp)}">
							<p:ajax event="dialogReturn" listener="#{tdFlowTaskEditBean.onFfSelect}" />
						</p:commandButton>	
						
						<!-- 流程图 -->
						<p:commandButton icon="ui-icon-image" id="processPicBtn" actionListener="#{tdFlowTaskEditBean.processPicAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['4'].flowBtnName}" onclick="zwx_flow_processPic_before();"
							rendered="#{tdFlowTaskEditBean.btnDispMap['4'].disp}"								
							process="@this" oncomplete="zwx_dialog_close_listener();">
						</p:commandButton>	
						<!-- 已阅 -->
						<p:commandButton icon="ui-icon-comment" id="haveReadBtn" action="#{tdFlowTaskEditBean.readAction}" process="@this,businessPanel"
							value="#{tdFlowTaskEditBean.btnDispMap['16'].flowBtnName}" update="editBtnsPanel"
							rendered="#{(!tdFlowTaskEditBean.readOnly) and (tdFlowTaskEditBean.btnDispMap['16'].disp)}"
							onclick="zwx_flow_submit_before()" oncomplete="zwx_flow_submit_complete(xhr, status, args);zwx_dialog_close_listener();">
							<f:setPropertyActionListener target="#{tdFlowTaskEditBean.actionType}" value="readAction" />
							<p:ajax event="dialogReturn" listener="#{tdFlowTaskEditBean.onPersonSelect}" onstart="zwx_flow_submit_dialogbefore();"
								oncomplete="zwx_flow_submit_dialogrtn();zwx_flow_back2list();" update="actionState" />
						</p:commandButton>
						
						<!-- 打印 -->	
					    <p:menuButton value="打印选项" rendered="#{tdFlowTaskEditBean.printable  and ( tdFlowTaskEditBean.btnDispMap['6'].disp or  tdFlowTaskEditBean.btnDispMap['7'].disp  or  tdFlowTaskEditBean.btnDispMap['8'].disp  or  tdFlowTaskEditBean.btnDispMap['9'].disp  ) }">
					        <p:menuitem value="#{tdFlowTaskEditBean.btnDispMap['6'].flowBtnName}" icon="ui-icon-document" action="#{tdFlowTaskEditBean.preViewAction}"
					        	rendered="#{tdFlowTaskEditBean.btnDispMap['6'].disp}"	
					        	process="@this,:tabView:editForm:hiddenPanel" update=":tabView:editForm:hiddenPanel" oncomplete="frpt_show();"/>
					        	
					        <p:menuitem value="#{tdFlowTaskEditBean.btnDispMap['7'].flowBtnName}" icon="ui-icon-print" action="#{tdFlowTaskEditBean.printAction}"
					        	rendered="#{tdFlowTaskEditBean.btnDispMap['7'].disp}"
					        	process="@this,:tabView:editForm:hiddenPanel" update=":tabView:editForm:hiddenPanel" oncomplete="frpt_show();"/>
					        	
					        <p:menuitem value="#{tdFlowTaskEditBean.btnDispMap['8'].flowBtnName}" icon="ui-icon-pencil" 
					        	rendered="#{tdFlowTaskEditBean.btnDispMap['8'].disp}" action="#{tdFlowTaskEditBean.designAction}" 
					        	process="@this,:tabView:editForm:hiddenPanel" update=":tabView:editForm:hiddenPanel" oncomplete="frpt_design();"/>
					        	
					        <p:separator rendered="#{tdFlowTaskEditBean.btnDispMap['9'].disp}"/>
					        <p:menuitem value="#{tdFlowTaskEditBean.btnDispMap['9'].flowBtnName}" icon="ui-icon-wrench" url="javascript:frpt_printer_set();" 
					        	process="@this" rendered="#{tdFlowTaskEditBean.btnDispMap['9'].disp}"	/>
					    </p:menuButton>		
							
						<!-- 用户自定义按钮 -->	
						<c:forEach items="#{tdFlowTaskEditBean.buttonList}" var="btn">
							<p:commandButton id="#{btn.btnId}" value="#{btn.btnName}" binding="#{btn.btnObject}"/>	
						</c:forEach>
						
						<!-- 测试 -->
						<p:commandButton style="display:none;" value="测试" process="@this" update="editBtnsPanel" widgetVar="TestButton"/>
						
						<!-- 返回 -->
						<p:commandButton icon="ui-icon-close" id="backBtn" action="#{tdFlowTaskEditBean.backAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['5'].flowBtnName}"
							rendered="#{tdFlowTaskEditBean.btnDispMap['5'].disp}"								
						 	process="@this" immediate="true"/>
					 	<p:commandButton icon="ui-icon-close" id="backBtn1" action="#{tdFlowTaskEditBean.backAction}" 
							value="#{tdFlowTaskEditBean.btnDispMap['5'].flowBtnName}" style="display:none;"
							rendered="#{tdFlowTaskEditBean.btnDispMap['5'].disp}"								
						 	process="@this" immediate="true">
						 	<f:setPropertyActionListener target="#{tdFlowTaskEditBean.flag}" value="true"></f:setPropertyActionListener>
						 	</p:commandButton>
						 	
						<!-- 文档打印 -->
						<p:commandButton icon="ui-icon-document" id="wordPrintBtn" 
							value="#{tdFlowTaskEditBean.btnDispMap['15'].flowBtnName}" onclick="WebOpenPrint();"
							rendered="#{tdFlowTaskEditBean.btnDispMap['15'].disp}" process="@this" >
						</p:commandButton>
					</p:outputPanel>
					
					<p:fieldset legend="流程信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" id="flowTitleGridUp"
						rendered="#{tdFlowTaskEditBean.flowInfoShow}">
						<p:panelGrid style="width:100%;" rendered="#{!tdFlowTaskEditBean.dispPro}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowTaskEditBean.businessKey}"/>
								</p:column>
							</p:row>
							<c:forEach items="#{tdFlowTaskEditBean.lastNodeAdvice}" var="itm">
								<p:row>
									<p:column style="text-align:right;padding-right:3px;height: 28px;width:200px;">
										上一步处理情况：
									</p:column>
									<p:column style="text-align:left;padding-left:3px;">
										#{itm[0]}(#{itm[1]})：#{itm[2]} 
									</p:column>
								</p:row>
							</c:forEach>
						</p:panelGrid>	
						<p:panelGrid style="width:100%;" rendered="#{tdFlowTaskEditBean.dispPro}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowTaskEditBean.businessKey}"/>
								</p:column>
							</p:row>
							<p:row>
								<p:column colspan="2">
							        <p:dataTable var="itm" value="#{tdFlowTaskEditBean.advDataList}" emptyMessage="没有您要找的记录！">
							            <p:column headerText="节点名称" style="width: 80px;text-align: center;">
							                <h:outputText value="#{itm[0]}" />
							            </p:column>
							            <p:column headerText="处理人" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[1]}" />
							            </p:column>
							            <p:column headerText="选择的处理人" style="width: 150px;">
							                <h:outputText value="#{itm[12]}" />
							            </p:column>
							            <p:column headerText="通知方式" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[6]}" />
							            </p:column>
							            <p:column headerText="处理时间" style="width: 140px;text-align: center; ">
							                <h:outputText value="#{itm[2]}" />
							            </p:column>
							            <p:column headerText="处理意见" style="padding-left: 3px;font-weight:bold;">
							                <h:outputText value="#{itm[3]}" />
							            </p:column>
							            <p:column headerText="发送状态" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[7]}" />
							            </p:column>
							            <p:column headerText="接收状态" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[8]}" />
							            </p:column>
							            <p:column headerText="接收时间" style="width: 140px;text-align: center; ">
							                <h:outputText value="#{itm[9]}" />
							            </p:column>
							        </p:dataTable>	
								</p:column>
							</p:row>
						</p:panelGrid>	
					</p:fieldset>
					
					
					<p:outputPanel id="businessPanel">
						<ui:include src="#{tdFlowTaskEditBean.tdFlowNode.tdFlowNodePage.pageUrl}" >
                            <ui:param name="pageParameter" value="#{tdFlowTaskEditBean.tdFlowNode.tdFlowNodePage.pageParm}"/>
						</ui:include>
					</p:outputPanel>
					
					<p:fieldset legend="流程信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" id="flowTitleGrid"
						rendered="#{!tdFlowTaskEditBean.flowInfoShow}">
						<p:panelGrid style="width:100%;" rendered="#{!tdFlowTaskEditBean.dispPro}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowTaskEditBean.businessKey}"/>
								</p:column>
							</p:row>
							<c:forEach items="#{tdFlowTaskEditBean.lastNodeAdvice}" var="itm">
								<p:row>
									<p:column style="text-align:right;padding-right:3px;height: 28px;width:200px;">
										上一步处理情况：
									</p:column>
									<p:column style="text-align:left;padding-left:3px;">
										#{itm[0]}(#{itm[1]})：#{itm[2]} 
									</p:column>
								</p:row>
							</c:forEach>
						</p:panelGrid>	
						<p:panelGrid style="width:100%;" rendered="#{tdFlowTaskEditBean.dispPro}">
							<p:row>
								<p:column style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowTaskEditBean.businessKey}"/>
								</p:column>
							</p:row>
							<p:row>
								<p:column colspan="2">
							        <p:dataTable var="itm" value="#{tdFlowTaskEditBean.advDataList}" emptyMessage="没有您要找的记录！">
							            <p:column headerText="节点名称" style="width: 80px;text-align: center;">
							                <h:outputText value="#{itm[0]}" />
							            </p:column>
							            <p:column headerText="处理人" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[1]}" />
							            </p:column>
							            <p:column headerText="选择的处理人" style="width: 150px;">
							                <h:outputText value="#{itm[12]}" />
							            </p:column>
							            <p:column headerText="通知方式" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[6]}" />
							            </p:column>
							            <p:column headerText="处理时间" style="width: 140px;text-align: center; ">
							                <h:outputText value="#{itm[2]}" />
							            </p:column>
							            <p:column headerText="处理意见" style="padding-left: 3px;font-weight:bold;">
							                <h:outputText value="#{itm[3]}" />
							            </p:column>
							            <p:column headerText="发送状态" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[7]}" />
							            </p:column>
							            <p:column headerText="接收状态" style="width: 60px;text-align: center; ">
							                <h:outputText value="#{itm[8]}" />
							            </p:column>
							            <p:column headerText="接收时间" style="width: 140px;text-align: center; ">
							                <h:outputText value="#{itm[9]}" />
							            </p:column>
							        </p:dataTable>	
								</p:column>
							</p:row>
						</p:panelGrid>	
					</p:fieldset>
					
					 <!-- FastReport引入 -->
					<c:if test="#{tdFlowTaskEditBean.printable}">
						<ui:include src="/WEB-INF/templates/system/frpt.xhtml">
							<ui:param name="printBackingBean" value="#{tdFlowTaskEditBean.businessService}"/>
						</ui:include>
					</c:if>
					
					<h:inputHidden id="actionState" value="#{tdFlowTaskEditBean.actionState}"/>
					
                    <ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
				</h:form>
			</p:tab>
		</p:tabView>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		<c:if test="#{tdFlowTaskEditBean.def.ifDynaForm==1}">
			<ui:include src="/WEB-INF/templates/system/primeui.xhtml" />
		</c:if>
	</h:body>
</f:view>
</html>
