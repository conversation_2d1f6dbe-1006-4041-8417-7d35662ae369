<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<title>请选择人员</title>
		<style type="text/css">

		.30width {
			width: 30%;
		}
		
		.40width {
			width: 40%;
		}
		
		.ui-selectmanycheckbox label, .ui-selectoneradio label {
			display: block;
			margin-top: 0px !important;
			margin-right: 10px !important;
		}
		
		.ui-panelgrid td {
			border-width: 1px;
			border-style: solid;
			border-color: inherit;
			padding: 0px 1px 0px 0px;
		}
		</style>
		
		<script type="text/javascript">
		 //<![CDATA[
		 //]]>           
		</script>
	</h:head>
	<h:body style="overflow-y:hidden;">
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:form id="manForm">
			<p:scrollPanel id="scrollPanel" style="width:825px;height:460px;border:0px;" mode="native">
				<p:panelGrid id="mainGrid" style="width:805px;">
					<c:forEach items="#{tdFlowSubmitNBTZBean.dataMap}" var="entry" varStatus="inx">
						<p:row>
							<p:column style="width:120px;text-align:right;padding-right:3px;height:30px;">
								<h:outputText value="#{entry.key.nodeName}"/>
								<h:outputText value="："/>
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height:30px;">
								<p:selectManyCheckbox value="#{tdFlowSubmitNBTZBean.dataMap[entry.key].selectUserIdsList}" layout="grid" columns="5">
									<f:selectItems value="#{tdFlowSubmitNBTZBean.dataMap[entry.key].userList}" var="user" itemLabel="#{user.username}" itemValue="#{user.rid}"/>
								</p:selectManyCheckbox>
							</p:column>
						</p:row>
					</c:forEach>
					<p:row>
						<p:column colspan="2">
							<table style="width: 100%;border-color:white;">
								<p:outputPanel rendered="#{tdFlowSubmitNBTZBean.txtypeList.size() > 0}">
									<tr>
										<td style="width:90px; text-align:right;padding-right:3px;"><h:outputText value="通讯方式：" styleClass="zwx_dialog_font" /></td>
										<td style="padding-left:3px;" colspan="2">
											<p:selectManyCheckbox id="selectedTx" value="#{tdFlowSubmitNBTZBean.selectedTxtypeList}">
												<f:selectItems value="#{tdFlowSubmitNBTZBean.txtypeList}" var="t" itemLabel="#{t.txType.typeCN}" itemValue="#{t.rid}" />
											</p:selectManyCheckbox></td>
									</tr>
								</p:outputPanel>
								<tr>
									<td style="width:90px; text-align:right;padding-right:3px;"><h:outputText value="*" style="color: red" rendered="#{tdFlowSubmitNBTZBean.tdFlowNode.ifAdv==1}" /> <h:outputText
											value="处理意见：" styleClass="zwx_dialog_font" /></td>
									<td style="padding-left:3px;">
										<p:inputTextarea id="advice" value="#{tdFlowSubmitNBTZBean.advice}" style="height:50px;width:550px;" maxlength="1000" widgetVar="advice"/>
									</td>
									<td style="width: 67px;padding-left: 0px;">
										<p:commandButton id="templateBtn" value="生成意见" 
											style="height:50px;width:65px;" action="#{tdFlowSubmitNBTZBean.generatorAdvice}" update="advice" process="@form">
										</p:commandButton>	
											 
										<p:dialog header="意见模版" widgetVar="IdeaModule" resizable="false" width="500" height="400" modal="true">
											<p:dataTable var="itm" style="width:470px;" value="#{tdFlowSubmitNBTZBean.advTemplateList}" id="ideaTable" emptyMessage="没有数据！" scrollable="true" scrollHeight="370">
												<p:column headerText="选择" style="width: 40px;text-align:center;">
													<p:commandLink value="选择" oncomplete="PF('IdeaModule').hide();" process="@this" update=":manForm:advice">
														<f:setPropertyActionListener target="#{tdFlowSubmitNBTZBean.advice}" value="#{itm.afterAnalyContent}" />
													</p:commandLink>
												</p:column>
												<p:column headerText="意见内容">
													<h:outputText value="#{itm.afterAnalyContent}" />
												</p:column>
											</p:dataTable>
										</p:dialog></td>
									<td style="width: 67px;padding-left: 0px;"><p:commandButton id="cyyBtn" value="常用用语" type="button" onclick="PF('cyyModule').show();" style="height:50px;width:65px;" />
										<p:dialog header="常用语" widgetVar="cyyModule" resizable="false" width="500" height="400" modal="true">
											<p:dataTable var="itm" style="width:470px;" value="#{tdFlowSubmitNBTZBean.cyyList}" id="cyyTable" emptyMessage="没有数据！" scrollable="true" scrollHeight="370">
												<p:column headerText="选择" style="width: 40px;text-align:center;">
													<p:commandLink value="选择" oncomplete="PF('cyyModule').hide();" process="@this" update=":manForm:advice">
														<f:setPropertyActionListener target="#{tdFlowSubmitNBTZBean.advice}" value="#{itm.codeName}" />
													</p:commandLink>
												</p:column>
												<p:column headerText="意见内容">
													<h:outputText value="#{itm.codeName}" />
												</p:column>
											</p:dataTable>
										</p:dialog></td>
								</tr>
								<tr>
									<td style="width:90px; text-align:right;padding-right:3px;"></td>
									<td style="padding-left:3px;" colspan="2"><h:outputText id="display" styleClass="zwx_dialog_font" /></td>
									<td style="width: 50px;padding: 0px;"></td>
								</tr>
							</table>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:scrollPanel>
			<p:outputPanel style="text-align:center;margin-top: 10px;">
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e" id="personSaveBtn" action="#{tdFlowSubmitNBTZBean.personSelectAction}" process="@form" />
				<p:spacer width="5" />
				<p:commandButton value="取消" icon="ui-icon-close" id="personBackBtn" action="#{tdFlowSubmitNBTZBean.dialogClose}" process="@this" />
			</p:outputPanel>
			<ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
			<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
		</h:form>
	</h:body>
</f:view>
</html>
