<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
	
	    <script type="text/javascript">
        //<![CDATA[
/**保存按钮点击执行的脚本，用于被重写*/
function zwx_flow_save_before() {
	var dynaData={};
	//非复选框获取Id判断即可
	jQuery("[id*=DYNA_MAIN]").each(function(){
		if(jQuery(this).attr("type") != 'checkbox' && jQuery(this).attr("cbHide") != 1)	{
			if(typeof(jQuery(this).attr("required"))!="undefined" && jQuery(this).attr("required") == "required") {
				if(jQuery(this).val()=="") {
					dyna_add_msg([{severity: 'error', summary: jQuery(this).attr("requiredMessage")}]); 
					throw {severity: 'error', summary: jQuery(this).attr("requiredMessage")};
				}
			}
			jQuery(dynaData).attr(jQuery(this).attr("id"), jQuery(this).val());
		}
	});
	jQuery("[id*=DYNA_SUB]").each(function(){
		if(jQuery(this).attr("type") != 'checkbox'  && jQuery(this).attr("cbHide") != 1 )	{
			if(typeof(jQuery(this).attr("required"))!="undefined" && jQuery(this).attr("required") == "required") {
				if(jQuery(this).val()=="") {
					dyna_add_msg([{severity: 'error', summary: jQuery(this).attr("requiredMessage")}]); 
					throw {severity: 'error', summary: jQuery(this).attr("requiredMessage")};
				}
			}
			jQuery(dynaData).attr(jQuery(this).attr("id"), jQuery(this).val());
		}
	});
	//复选框获取name判断
	jQuery("[name*=DYNA_MAIN]").each(function(){
		if(jQuery(this).attr("type") == 'checkbox')	{
			var chName = jQuery(this).attr("name");
			var val = "";
			jQuery('input[name='+chName+']:checked').each(function(){ 
				val+= "@#@"+jQuery(this).val();
			});
			jQuery("#"+chName).val(val);
			if(val != ""){
				val = val.substring(3);
			}else{
				if(typeof(jQuery("#"+chName).attr("required"))!="undefined" && jQuery("#"+chName).attr("required") == "required") {
					dyna_add_msg([{severity: 'error', summary: jQuery("#"+chName).attr("requiredMessage")}]); 
					throw {severity: 'error', summary: jQuery("#"+chName).attr("requiredMessage")};
				}
			}
			jQuery(dynaData).attr(chName, val);
		}
	});
	jQuery("[name*=DYNA_SUB]").each(function(){
		if(jQuery(this).attr("type") == 'checkbox')	{
			var chName = jQuery(this).attr("name");
			var val = "";
			jQuery('input[name='+chName+']:checked').each(function(){ 
				val+= "@#@"+jQuery(this).val();
			});
			jQuery("#"+chName).val(val);
			if(val != ""){
				val = val.substring(3);
			}else{
				if(typeof(jQuery("#"+chName).attr("required"))!="undefined" && jQuery("#"+chName).attr("required") == "required") {
					dyna_add_msg([{severity: 'error', summary: jQuery("#"+chName).attr("requiredMessage")}]); 
					throw {severity: 'error', summary: jQuery("#"+chName).attr("requiredMessage")};
				}
			}
			jQuery(dynaData).attr(chName, val);
		}
	});
	
	jQuery("select").removeAttr("disabled");
	jQuery("#tabView\\:editForm\\:dataJson").val(JSON.stringify(dynaData));
	
}
/**保存按钮执行完成的脚本，用于被重写*/
function zwx_flow_save_complete(xhr, status, args){
	dyna_init();
}   

/**提交按钮点击执行的脚本，用于被重写*/
function zwx_flow_submit_before() {
	zwx_flow_save_before();
}

function zwx_flow_submit_dialogbefore() {
	dyna_init();
}

jQuery(function() {
	initUpLoad();
	dyna_init();
	
	maxRow=parseInt(jQuery("#tabView\\:editForm\\:rows").val());
});

function dyan_add_row(container, row, tpl) {
	var addHtml=Mustache.render(tpl, {
		row: row
	});
	jQuery(container).append(addHtml);
	dyna_dom_style_init();
}

function dyna_href_del(row) {
	var r=$("#subTableDiv").find("tr").length;
	if(r<=1) {
		alert("不允许删除！");
	}else {
		if(confirm("确定要删行吗？")) {
			jQuery("#tabView\\:editForm\\:rows").val(r-1);
			jQuery("#subTableRowDiv"+row).remove();
		}
	}
}

function dyna_add_msg(msg) {
	jQuery("#defaultMsg").puigrowl('show', msg);
}


var maxRow;
var DataChildTpl = "";
function dyna_init() {
	jQuery("#dynaDiv").html(jQuery("#tabView\\:editForm\\:html").val());
	dyna_dom_style_init();
	var formType=jQuery("#tabView\\:editForm\\:formType").val();
	if(DataChildTpl == "" && formType==2) {
		if(jQuery("#DataChildTpl").html() != undefined)	{
			DataChildTpl = jQuery("#DataChildTpl").html().replace(/(\/\/\<!\-\-)|(\/\/\-\->)/g,"");
		}
	}
	var dataStr=jQuery("#tabView\\:editForm\\:dataJson").val();
	if(dataStr != "") {
		var dataJSON=JSON.parse(dataStr);
		for(var prop in dataJSON) {
			//多选框 需要特殊处理
			if(typeof(jQuery("input[name='"+prop+"']"))!="undefined" && jQuery("input[name='"+prop+"']").attr("type") == 'checkbox'){
				var dataArr = dataJSON[prop].split('@#@');
				for(var i = 0 ; i < dataArr.length ; i++)	{
					jQuery("input[name='"+prop+"'][value='"+dataArr[i]+"'] ").puicheckbox('check');
				}
			}else if(typeof(jQuery("#"+prop))!="undefined") {
				if(jQuery("#"+prop).is('select')){//如果为下拉框，则需要用下拉的特殊赋值方式
					jQuery("#"+prop).puidropdown('selectValue', dataJSON[prop]);
				}else if(jQuery("#"+prop).is('span')){
					jQuery("#"+prop).text(dataJSON[prop]);
				}else {
					if(typeof(jQuery("#"+prop).attr("fieldTyp"))!="undefined" && jQuery("#"+prop).attr("fieldTyp") == "fj") {
						var fjId = jQuery("#"+prop).attr("id");
						//截取到Id中附件类控件的前缀
						var fjTemp = fjId.substring(0,fjId.lastIndexOf('NAME')); //这样就获取到了前面的字符串。
						
						jQuery("#" + fjTemp + "NAME").val(dataJSON[prop]);
						jQuery("#" + fjTemp + "_SPAN").text(dataJSON[prop]);

						jQuery("#" + fjTemp + "_UP").css('display', 'none');
						jQuery("#" + fjTemp + "_DOWN").css('display', '');
						jQuery("#" + fjTemp + "_DEL").css('display', '');
					}else{
						jQuery("#"+prop).val(dataJSON[prop]);
					}
				}
			}
		}
	}
	jQuery("#DYNA_BTN_TH").off().click(function() {
		beforeClick();
		jQuery("#tabView\\:editForm\\:rows").val(jQuery("#tabView\\:editForm\\:rows").val()+1);
		dyan_add_row("#subTableDiv",maxRow, DataChildTpl);
		maxRow=maxRow+1;
		afterClick();
	});
	
}

function beforeClick(){
	
}

function afterClick(){
}
        //]]>
    </script>
	
	<p:panelGrid style="width:100%;height:100%;" id="dynaFormGrid">
		<f:facet name="header">
			<p:row>
				<p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
					<h:outputText value="#{dynaFormBean.tdFormDef.formName}" />
				</p:column>
			</p:row>
		</f:facet>
	</p:panelGrid>
	<br/>
	<h:inputHidden id="formType" value="#{dynaFormBean.formType}"/>
	<h:inputHidden id="dataJson" value="#{dynaFormBean.dataJson}"/>
	<h:inputHidden id="unitId" value="#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"/>
	
	<h:inputHidden id="html" value="#{dynaFormBean.html}"/>
	<p:remoteCommand name="dynaSearchData" process="@this" action="#{dynaFormBean.dynaSearchData()}" oncomplete="dynaHandleComplete(xhr, status, args);"></p:remoteCommand>
	<h:inputHidden id="rows" value="#{dynaFormBean.tdFormDef.rows}"/>
	<div id="defaultMsg" /> 

	<div id="dynaDiv">
	</div>
	
	<ui:include src="uploadJq.xhtml"/>
	
	



</ui:composition>

