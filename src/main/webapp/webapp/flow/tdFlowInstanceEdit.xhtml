<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<script type="text/javascript">
			/**保存按钮点击执行的脚本，用于被重写*/
			function zwx_flow_save_before() {}
			/**保存按钮执行完成的脚本，用于被重写*/
			function zwx_flow_save_complete(xhr, status, args){}
			/**提交按钮点击执行的脚本，用于被重写*/
			function zwx_flow_submit_before() {}
			/**提交按钮执行的弹出框弹出之前的脚本，用于被重写*/
			function zwx_flow_submit_dialogbefore() {}    			
			/**提交按钮执行完成的脚本，用于被重写*/
			function zwx_flow_submit_complete(xhr, status, args){}
			/**提交按钮执行的弹出框返回事件执行后的脚本，用于被重写*/
			function zwx_flow_submit_dialogrtn() {}
	        /**pf dialog framework, icon close event listener*/
	        function zwx_dialog_close_listener() {}	
			
			function close_current_menu() {
				var current_tab_id = 'tab-button-' + top.win_TabMenu.iTabIndex;
				top.CloseTabWin(current_tab_id);
			}
			
			function WebOpenPrint(){
			}
			//
			jQuery(window).load(function () {
				PF('TestButton').getJQ().click();
			});
		</script>


	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css"/>
		<h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<p:tabView id="tabView" dynamic="true" style="border:1px; padding:0px;">
			<p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<h:form id="editForm">
					<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="mainTitleGrid">
						<f:facet name="header">
							<p:row>
								<p:column style="text-align:left;padding-left:5px;height:23px;">
									<h:outputText value="#{tdFlowInstanceEditBean.tdFlowNode.nodeName}" />
								</p:column>
							</p:row>
						</f:facet>
					</p:panelGrid>

					<p:outputPanel styleClass="zwx_toobar_h_auto" id="mainGrid">
						<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
						
						<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" onclick="zwx_flow_save_before()" 
							oncomplete="zwx_flow_save_complete(xhr, status, args);" action="#{tdFlowInstanceEditBean.saveAction}"
							process="@form" update="editForm"  rendered="#{tdFlowInstanceEditBean.noSubmit}">
							<f:setPropertyActionListener target="#{tdFlowInstanceEditBean.actionType}"  value="saveAction"/>
						</p:commandButton>	
						<p:commandButton value="提交" icon="ui-icon-circle-triangle-e" id="submitBtn" action="#{tdFlowInstanceEditBean.submitAction}" 
								onclick="zwx_flow_submit_before()"
								oncomplete="zwx_flow_submit_complete(xhr, status, args);zwx_dialog_close_listener();" process="@form" rendered="#{tdFlowInstanceEditBean.noSubmit}">
								<p:ajax event="dialogReturn" listener="#{tdFlowInstanceEditBean.onPersonSelect}" update="editForm" 
									oncomplete="zwx_flow_submit_dialogrtn()" onstart="zwx_flow_submit_dialogbefore();"/>
						</p:commandButton>
						
						<!-- 新增 -->
						<p:commandButton icon="ui-icon-plus" id="addAgainBtn" action="#{tdFlowInstanceEditBean.addAgainAction}"
						    rendered="#{tdFlowInstanceEditBean.btnDispMap['14'].disp and (!tdFlowInstanceEditBean.noSubmit)}"
							value="#{tdFlowInstanceEditBean.btnDispMap['14'].flowBtnName}" ajax="false" immediate="true">
						</p:commandButton>						
						
						<!-- 打印 -->	
					    <p:menuButton value="打印选项" rendered="#{tdFlowInstanceEditBean.printable  and( tdFlowInstanceEditBean.btnDispMap['6'].disp or  tdFlowInstanceEditBean.btnDispMap['7'].disp  or  tdFlowInstanceEditBean.btnDispMap['8'].disp  or  tdFlowInstanceEditBean.btnDispMap['9'].disp  ) }">
					        <p:menuitem value="#{tdFlowInstanceEditBean.btnDispMap['6'].flowBtnName}" icon="ui-icon-document" action="#{tdFlowInstanceEditBean.preViewAction}"
					        	rendered="#{tdFlowInstanceEditBean.btnDispMap['6'].disp}"	
					        	process="@this,:tabView:editForm:hiddenPanel" update=":tabView:editForm:hiddenPanel" oncomplete="frpt_show();"/>
					        	
					        <p:menuitem value="#{tdFlowInstanceEditBean.btnDispMap['7'].flowBtnName}" icon="ui-icon-print" action="#{tdFlowInstanceEditBean.printAction}"
					        	rendered="#{tdFlowInstanceEditBean.btnDispMap['7'].disp}"
					        	process="@this,:tabView:editForm:hiddenPanel" update=":tabView:editForm:hiddenPanel" oncomplete="frpt_show();"/>
					        	
					        <p:menuitem value="#{tdFlowInstanceEditBean.btnDispMap['8'].flowBtnName}" icon="ui-icon-pencil" 
					        	rendered="#{tdFlowInstanceEditBean.btnDispMap['8'].disp}" action="#{tdFlowInstanceEditBean.designAction}" 
					        	process="@this,:tabView:editForm:hiddenPanel" update=":tabView:editForm:hiddenPanel" oncomplete="frpt_design();"/>
					        	
					        <p:separator rendered="#{tdFlowInstanceEditBean.btnDispMap['9'].disp}"/>
					        <p:menuitem value="#{tdFlowInstanceEditBean.btnDispMap['9'].flowBtnName}" icon="ui-icon-wrench" url="javascript:frpt_printer_set();" 
					        	process="@this" rendered="#{tdFlowInstanceEditBean.btnDispMap['9'].disp}"	/>
					    </p:menuButton>	
					    
					    <!-- 用户自定义按钮 -->	
						<c:forEach items="#{tdFlowInstanceEditBean.buttonList}" var="btn">
							<p:commandButton id="#{btn.btnId}" value="#{btn.btnName}" binding="#{btn.btnObject}"/>	
						</c:forEach>
						
						<!-- 测试 -->
						<p:commandButton style="display:none;" value="测试" process="@this" update="mainGrid" widgetVar="TestButton"/>
						
						<!-- 文档打印 -->
						<p:commandButton icon="ui-icon-document" id="wordPrintBtn" onclick="WebOpenPrint();"
						    rendered="#{tdFlowInstanceEditBean.btnDispMap['15'].disp}"
							value="#{tdFlowInstanceEditBean.btnDispMap['15'].flowBtnName}" immediate="true" process="@this">
						</p:commandButton>
						
						<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" 
							action="#{tdFlowInstanceEditBean.backAction}"  process="@this" rendered="#{tdFlowInstanceEditBean.turn==1 or tdFlowInstanceEditBean.turn==2}" />
						<p:commandButton value="关闭" icon="ui-icon-close" id="closeBtn" type="button"
							rendered="#{tdFlowInstanceEditBean.turn==3}" onclick="close_current_menu()"/>
					</p:outputPanel>

					<p:panelGrid style="width:100%;" id="flowTitleGrid" rendered="false">
						<f:facet name="header">
							<p:row>
								<p:column style="text-align:left;padding-left:5px;height: 20px;" colspan="2">
									<h:outputText value="流程信息" />
								</p:column>
							</p:row>
						</f:facet>
						<p:row>
							<p:column style="text-align:right;padding-right:3px;height: 28px;width:150px;">
								流程标题：
							</p:column>
							<p:column style="text-align:left;padding-left:3px;">
								<p:inputText id="businessKey" value="#{tdFlowInstanceEditBean.businessKey}"  
									required="true" requiredMessage="流程标题不允许为空！" style="width:450px;" maxlength="100"/>
							</p:column>
						</p:row>
					</p:panelGrid>					

					<ui:include src="#{tdFlowInstanceEditBean.tdFlowNode.jspUrl}" >
                        <ui:param name="pageParameter" value="#{tdFlowInstanceEditBean.tdFlowNode.tdFlowNodePage.pageParm}"/>
                    </ui:include>
					
					<!-- FastReport引入 -->
					<c:if test="#{tdFlowInstanceEditBean.printable}">
						<ui:include src="/WEB-INF/templates/system/frpt.xhtml">
							<ui:param name="printBackingBean" value="#{tdFlowInstanceEditBean.businessService}"/>
						</ui:include>
					</c:if>
						
					<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
				</h:form>
			</p:tab>
		</p:tabView>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		<c:if test="#{tdFlowInstanceEditBean.tdFlowDef.ifDynaForm==1}">
			<ui:include src="/WEB-INF/templates/system/primeui.xhtml" />
		</c:if>
	</h:body>
</f:view>
</html>

