<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<style type="text/css">
</style>
		<script type="text/javascript">
			//<![CDATA[
				function WebOpenPrint(){
				}
			//]]>
		</script>


	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<p:tabView id="tabView" dynamic="true"
			style="border:1px; padding:0px;">
			<p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<h:form id="editForm">
					<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
						id="mainTitleGrid">
						<f:facet name="header">
							<p:row>
								<p:column style="text-align:left;padding-left:5px;height: 20px;">
									<h:outputText value="详情" />
								</p:column>
							</p:row>
						</f:facet>
					</p:panelGrid>

					<p:outputPanel styleClass="zwx_toobar_h_auto" id="editBtnsPanel">
						<span class="ui-separator"
							style="float: left;margin: 7px 3px auto 5px;"><span
							class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:commandButton value="流程图" icon="ui-icon-image"
							id="processPicBtn"
							actionListener="#{tdFlowDetailBean.processPicAction}"
							process="@this" />
										<!-- 分发 -->
						<p:commandButton icon="ui-icon-person" id="ffBtn" actionListener="#{tdFlowDetailBean.ffInitAction}" 
							value="#{tdFlowDetailBean.btnDispMap['10'].flowBtnName}" process="@this"
							rendered="#{tdFlowDetailBean.btnDispMap['10'].disp}">
							<p:ajax event="dialogReturn" listener="#{tdFlowDetailBean.onFfSelect}" />
						</p:commandButton>	
						<p:menuButton value="打印选项"
							rendered="#{tdFlowDetailBean.printable}">
							<p:menuitem value="预览" icon="ui-icon-document"
								action="#{tdFlowDetailBean.preViewAction}"
								process="@this,:tabView:editForm:hiddenPanel"
								update=":tabView:editForm:hiddenPanel" oncomplete="frpt_show();" />
							<p:menuitem value="打印" icon="ui-icon-print"
								action="#{tdFlowDetailBean.printAction}"
								process="@this,:tabView:editForm:hiddenPanel"
								update=":tabView:editForm:hiddenPanel" oncomplete="frpt_show();" />
							<p:menuitem value="设计" icon="ui-icon-pencil"
								action="#{tdFlowDetailBean.designAction}"
								process="@this,:tabView:editForm:hiddenPanel"
								update=":tabView:editForm:hiddenPanel"
								oncomplete="frpt_design();" />
							<p:separator />
							<p:menuitem value="打印机" icon="ui-icon-wrench"
								url="javascript:frpt_printer_set();" process="@this" />
						</p:menuButton>
						<p:commandButton icon="ui-icon-document" id="wordPrintBtn" onclick="WebOpenPrint();"
							value="#{tdFlowDetailBean.btnDispMap['15'].flowBtnName}" process="@this"
							rendered="#{tdFlowDetailBean.btnDispMap['15'].disp}">
						</p:commandButton>
						<c:forEach items="#{tdFlowDetailBean.buttonList}" var="btn">
							<p:commandButton id="#{btn.btnId}" value="#{btn.btnName}"
								binding="#{btn.btnObject}" />
						</c:forEach>
						<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
							action="#{tdFlowDetailBean.backAction}" process="@this"
							immediate="true" />
					</p:outputPanel>
					
					<p:fieldset legend="流程信息" toggleable="true" toggleSpeed="500"
						style="margin-top: 5px;margin-bottom: 5px;" id="flowTitleGridUp"
						rendered="#{tdFlowDetailBean.flowInfoShow}">
						<p:panelGrid style="width:100%;"
							rendered="#{!tdFlowDetailBean.dispPro}">
							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowDetailBean.businessKey}" />
								</p:column>
							</p:row>
							<c:forEach items="#{tdFlowDetailBean.lastNodeAdvice}" var="itm">
								<p:row>
									<p:column
										style="text-align:right;padding-right:3px;height: 28px;width:200px;">
										上一步处理情况：
									</p:column>
									<p:column style="text-align:left;padding-left:3px;">
										#{itm[0]}(#{itm[1]})：#{itm[2]} 
									</p:column>
								</p:row>
							</c:forEach>
						</p:panelGrid>
						<p:panelGrid style="width:100%;"
							rendered="#{tdFlowDetailBean.dispPro}">
							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowDetailBean.businessKey}" />
								</p:column>
							</p:row>
							<p:row>
								<p:column colspan="2">
									<p:dataTable var="itm"
										value="#{tdFlowDetailBean.advDataList}"
										emptyMessage="没有您要找的记录！">
										<p:column headerText="节点名称"
											style="width: 80px;text-align: center;">
											<h:outputText value="#{itm[0]}" />
										</p:column>
										<p:column headerText="处理人"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[1]}" />
										</p:column>
										<p:column headerText="选择的处理人" style="width: 150px; max-width:150px">
							                <h:outputText value="#{itm[12]}" />
							            </p:column>
										<p:column headerText="通知方式"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[6]}" />
										</p:column>
										<p:column headerText="处理时间"
											style="width: 140px;text-align: center; ">
											<h:outputText value="#{itm[2]}" />
										</p:column>
										<p:column headerText="处理意见" style="padding-left: 3px;font-weight:bold;">
											<h:outputText value="#{itm[3]}" />
										</p:column>
										<p:column headerText="发送状态"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[7]}" />
										</p:column>
										<p:column headerText="接收状态"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[8]}" />
										</p:column>
										<p:column headerText="接收时间"
											style="width: 140px;text-align: center; ">
											<h:outputText value="#{itm[9]}" />
										</p:column>
									</p:dataTable>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>

					<ui:include src="#{tdFlowDetailBean.tdFlowNode.jspUrl}" />

					<p:fieldset legend="流程信息" toggleable="true" toggleSpeed="500"
						style="margin-top: 5px;margin-bottom: 5px;" id="flowTitleGrid"
						rendered="#{!tdFlowDetailBean.flowInfoShow}">
						<p:panelGrid style="width:100%;"
							rendered="#{!tdFlowDetailBean.dispPro}">
							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowDetailBean.businessKey}" />
								</p:column>
							</p:row>
							<c:forEach items="#{tdFlowDetailBean.lastNodeAdvice}" var="itm">
								<p:row>
									<p:column
										style="text-align:right;padding-right:3px;height: 28px;width:200px;">
										上一步处理情况：
									</p:column>
									<p:column style="text-align:left;padding-left:3px;">
										#{itm[0]}(#{itm[1]})：#{itm[2]} 
									</p:column>
								</p:row>
							</c:forEach>
						</p:panelGrid>
						<p:panelGrid style="width:100%;"
							rendered="#{tdFlowDetailBean.dispPro}">
							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;height: 28px;width:200px;">
									流程标题：
								</p:column>
								<p:column style="text-align:left;padding-left:3px;">
									<h:outputText value="#{tdFlowDetailBean.businessKey}" />
								</p:column>
							</p:row>
							<p:row>
								<p:column colspan="2">
									<p:dataTable var="itm"
										value="#{tdFlowDetailBean.advDataList}"
										emptyMessage="没有您要找的记录！">
										<p:column headerText="节点名称"
											style="width: 80px;text-align: center;">
											<h:outputText value="#{itm[0]}" />
										</p:column>
										<p:column headerText="处理人"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[1]}" />
										</p:column>
										<p:column headerText="选择的处理人" style="width: 150px; max-width:150px">
							                <h:outputText value="#{itm[12]}" />
							            </p:column>
										<p:column headerText="通知方式"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[6]}" />
										</p:column>
										<p:column headerText="处理时间"
											style="width: 140px;text-align: center; ">
											<h:outputText value="#{itm[2]}" />
										</p:column>
										<p:column headerText="处理意见" style="padding-left: 3px;font-weight:bold;">
											<h:outputText value="#{itm[3]}" />
										</p:column>
										<p:column headerText="发送状态"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[7]}" />
										</p:column>
										<p:column headerText="接收状态"
											style="width: 60px;text-align: center; ">
											<h:outputText value="#{itm[8]}" />
										</p:column>
										<p:column headerText="接收时间"
											style="width: 140px;text-align: center; ">
											<h:outputText value="#{itm[9]}" />
										</p:column>
									</p:dataTable>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>

					<c:if test="#{tdFlowDetailBean.printable}">
						<ui:include src="/WEB-INF/templates/system/frpt.xhtml">
							<ui:param name="printBackingBean"
								value="#{tdFlowDetailBean.businessService}" />
						</ui:include>
					</c:if>

					<ui:include src="/WEB-INF/templates/system/confirm.xhtml" />
				</h:form>
			</p:tab>
		</p:tabView>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		<c:if test="#{tdFlowDetailBean.def.ifDynaForm==1}">
			<ui:include src="/WEB-INF/templates/system/primeui.xhtml"/>
		</c:if>
	</h:body>
</f:view>
</html>
