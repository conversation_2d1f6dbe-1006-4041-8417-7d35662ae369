<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<title>请填写意见</title>
		<script type="text/javascript">
	    //<![CDATA[
		function allselectAll() {
		    var checked = document.getElementById("manForm:all_select_input").checked; 
		    jQuery(':checkbox[id*="node_"]').attr('checked', checked);

		    if (checked) {
		    	jQuery('div[id*="node_"] > div').each(function() {
		    		jQuery(this).addClass('ui-state-active');
		    		jQuery(this).children('span').addClass('ui-icon ui-icon-check');
		        });
		    } else {
		    	jQuery('div[id*="node_"] > div').each(function() {
		    		jQuery(this).removeClass('ui-state-active');
		    		jQuery(this).children('span').removeClass('ui-icon ui-icon-check');
		        });
		    }
		}
		
		function nodeselectAll(eid) {
			var whole_id = "manForm:" + eid + "_input";
			var checked = document.getElementById(whole_id).checked; 
		    jQuery(':checkbox[id*="manForm:'+eid+'_user"]').attr('checked', checked);

		    if (checked) {
		    	jQuery('div[id*="manForm:'+eid+'_user"] > div').each(function() {
		    		jQuery(this).addClass('ui-state-active');
		    		jQuery(this).children('span').addClass('ui-icon ui-icon-check');
		        });
		    } else {
		    	jQuery('div[id*="manForm:'+eid+'_user"] > div').each(function() {
		    		jQuery(this).removeClass('ui-state-active');
		    		jQuery(this).children('span').removeClass('ui-icon ui-icon-check');
		        });
		    }
		}		
		
		function beforeSubmit() {
			var userIds="";
			jQuery(':checkbox[id*="_user_"]').each(function() {
				if(jQuery(this).is(':checked')) {
					userIds = userIds + ",'" + jQuery(this).attr('data-p-label')+"'";
				}
			});
			jQuery("#manForm\\:selectUserIds").attr('value', userIds);
		}
        //]]>		
		
		</script>
	</h:head>
	<h:body style="overflow-y:hidden;">
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:form id="manForm">
			<p:panelGrid id="mainGrid" style="width:100%;height:400px;">
				<p:row rendered="#{tdFlowSubmitZYTZBean.txtypeList.size() > 0}">
					<p:column style="width:100px; text-align:right;padding-right:3px;height:28px;">
						<h:outputText value="通讯方式:" />
					</p:column>
					<p:column style="padding-left:3px;">
						<p:selectManyCheckbox id="selectedTx" value="#{tdFlowSubmitZYTZBean.selectedTxtypeList}">
							<f:selectItems value="#{tdFlowSubmitZYTZBean.txtypeList}" var="t" itemLabel="#{t.txType.typeCN}" itemValue="#{t.rid}" />
						</p:selectManyCheckbox>						
					</p:column>
				</p:row>												
				<p:row >
					<p:column colspan="2" style="vertical-align:top;height:70px;" >
						<table style="width: 100%;border-color:white; ">
							<tr>
								<td style="width:90px; text-align:right;padding-right:3px;"><h:outputText value="*" style="color: red" rendered="#{tdFlowSubmitZYTZBean.tdFlowNode.ifAdv==1}" /> <h:outputText
										value="处理意见：" styleClass="zwx_dialog_font" /></td>
								<td style="padding-left:3px;">
									<p:inputTextarea id="advice" value="#{tdFlowSubmitZYTZBean.advice}" style="height:50px;width:520px;" maxlength="1000" />
								</td>
								<td style="width: 67px;padding-left: 0px;padding-bottom: 8px;" valign="bottom" >
									<p:commandButton id="templateBtn" value="意见模板" type="button" onclick="PF('IdeaModule').show();"
										style="height:50px;width:65px;" /> 
									<p:dialog header="意见模版" widgetVar="IdeaModule" resizable="false" width="500" height="400" modal="true">
										<p:dataTable var="itm" style="width:470px;" value="#{tdFlowSubmitZYTZBean.advTemplateList}" id="ideaTable" emptyMessage="没有数据！" scrollable="true" scrollHeight="370">
											<p:column headerText="选择" style="width: 40px;text-align:center;">
												<p:commandLink value="选择" oncomplete="PF('IdeaModule').hide();" process="@this" update=":manForm:advice">
													<f:setPropertyActionListener target="#{tdFlowSubmitZYTZBean.advice}" value="#{itm.afterAnalyContent}" />
												</p:commandLink>
											</p:column>
											<p:column headerText="意见内容">
												<h:outputText value="#{itm.afterAnalyContent}" />
											</p:column>
										</p:dataTable>
									</p:dialog>
								</td>
								<td style="width: 67px;padding-left: 0px;padding-bottom: 8px;" valign="bottom">
									<p:commandButton id="cyyBtn" value="常用用语" type="button" onclick="PF('cyyModule').show();" style="height:50px;width:65px;" />
									<p:dialog header="常用语" widgetVar="cyyModule" resizable="false" width="500" height="400" modal="true">
										<p:dataTable var="itm" style="width:470px;" value="#{tdFlowSubmitZYTZBean.cyyList}" id="cyyTable" emptyMessage="没有数据！" scrollable="true" scrollHeight="370">
											<p:column headerText="选择" style="width: 40px;text-align:center;">
												<p:commandLink value="选择" oncomplete="PF('cyyModule').hide();" process="@this" update=":manForm:advice">
													<f:setPropertyActionListener target="#{tdFlowSubmitZYTZBean.advice}" value="#{itm.codeName}" />
												</p:commandLink>
											</p:column>
											<p:column headerText="意见内容">
												<h:outputText value="#{itm.codeName}" />
											</p:column>
										</p:dataTable>
									</p:dialog></td>
							</tr>
							<tr>
								<td style="width:90px; text-align:right;padding-right:3px;"></td>
								<td style="padding-left:3px;" colspan="2"><h:outputText id="display" styleClass="zwx_dialog_font" /></td>
								<td style="width: 50px;padding: 0px;"></td>
							</tr>
						</table>
					</p:column>
				</p:row>
				<p:row rendered="#{tdFlowSubmitZYTZBean.ffMsg}">
					<p:column colspan="2" style="height:30px;padding-left:3px;">
						<p:selectBooleanCheckbox itemLabel="全选" id="all_select" onchange="allselectAll();" widgetVar="all_select"/>
						<p:spacer width="10"/> 
						<span style="color: blue;font-weight: bold;font-size: 13px;">（通知以下流程参与者！）</span>
					</p:column>
				</p:row>	
				
				<p:row rendered="#{tdFlowSubmitZYTZBean.ffMsg}">
					<p:column colspan="2" style="vertical-align:top;padding-left:3px;">
						<p:scrollPanel mode="native" style="height:235px; width:810px;padding-top:10px;">
							<c:forEach items="#{tdFlowSubmitZYTZBean.allUserMap}" var="itm" varStatus="inx">
								<div style="text-align: left;padding-left: 3px;height: 28px;">
									<p:selectBooleanCheckbox itemLabel="#{itm.key}-" id="node_#{inx.index}" onchange="nodeselectAll('node_#{inx.index}')"/> 
								</div>
								<div style="padding-left: 110px;height: 28px;">
									<c:forEach items="#{itm.value}" var="user_itm" varStatus="user_inx">
										<p:selectBooleanCheckbox itemLabel="#{user_itm.username}" id="node_#{inx.index}_user_#{user_inx.index}" label="#{user_itm.rid}"/>
										<p:spacer width="10"/>
									</c:forEach>
								</div>		
							</c:forEach>
						</p:scrollPanel>
					</p:column>
				</p:row>					
			</p:panelGrid>
			<p:outputPanel style="text-align:center;margin-top: 10px;">
				<h:inputHidden id="selectUserIds" value="#{tdFlowSubmitZYTZBean.selectUserIds}"/>
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e" id="personSaveBtn" onclick="beforeSubmit()" 
					action="#{tdFlowSubmitZYTZBean.personSelectAction}" process="@form" />
				<p:spacer width="5" />
				<p:commandButton value="取消" icon="ui-icon-close" id="personBackBtn" action="#{tdFlowSubmitZYTZBean.dialogClose}" process="@this" />
			</p:outputPanel>
			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		</h:form>
	</h:body>
</f:view>
</html>
