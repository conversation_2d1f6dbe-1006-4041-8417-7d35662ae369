<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdFlowHandledBean}" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<style type="text/css">
.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 0px;
	padding-right: 0px;
}

.ui-picklist .ui-picklist-list {
	text-align: left;
	height: 300px;
	width: 345px;
	overflow: auto;
}

.ui-picklist .ui-picklist-filter {
	padding-right: 0px;
	width: 98%;
}

.ui-picklist td {
	border-width: 0px;
	border-style: solid;
	border-color: inherit;
	padding: 4px 10px;
}

.30width {
	width: 30%;
}

.40width {
	width: 40%;
}
</style>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="已办任务" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{tdFlowHandledBean.searchAction}" update="dataTable"
					process="@this,:mainForm:mainGrid" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:180px;height:35px;">
				<h:outputText value="流程类型：" />
			</p:column>
			<p:column style="text-align:left;width:250px;">
				<div style="display: table-cell;padding-left: 3px;">
					<p:inputText id="searchFlowTypeName"
						value="#{tdFlowHandledBean.condition.searchFlowTypeName}"
						style="width: 180px;" readonly="true" />
				</div>
				<div style="display: table-cell;">
					<p:commandLink styleClass="ui-icon ui-icon-search"
						id="initTreeLink" process="@this"
						style="position: relative;left: -20px;top:5px;"
						oncomplete="PF('SearchFlowTypeName').show()" />
				</div>
				<p:overlayPanel id="flowTypePanel" for="searchFlowTypeName"
					style="width:280px;" widgetVar="SearchFlowTypeName">
					<p:tree var="node" selectionMode="single" id="flowTypeTree"
						value="#{tdFlowHandledBean.searchTreeNode}"
						style="width: 250px;height: 400px;overflow-y: auto;">
						<p:ajax event="select" update=":mainForm:searchFlowTypeName"
							listener="#{tdFlowHandledBean.onSearchNodeSelect}"
							process="@this,@parent"
							oncomplete="PF('SearchFlowTypeName').hide();" />
						<p:treeNode>
							<h:outputText value="#{node.typeName}" />
						</p:treeNode>
					</p:tree>
				</p:overlayPanel>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="任务名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<p:inputText id="searchTaskName"
					value="#{tdFlowHandledBean.condition.searchTaskName}"
					style="width: 180px;" />
			</p:column>
		</p:row>

		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:180px;height:35px;">
				<h:outputText value="流程状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;width:250px;">
				<p:selectOneRadio id="searchFlowState"
					value="#{tdFlowHandledBean.condition.searchFlowState}"
					style="width: 220px;">
					<f:selectItem itemLabel="未结束" itemValue="0" />
					<f:selectItem itemLabel="结束" itemValue="1" />
					<f:selectItem itemLabel="全部" itemValue="2" />
				</p:selectOneRadio>
			</p:column>
			<p:column style="text-align:left;padding-left:10px;" colspan="2">
				<p:selectBooleanCheckbox id="searchMyFlow"
					value="#{tdFlowHandledBean.condition.searchMyFlow}"
					itemLabel="我发起的" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="流程类型" style="width: 100px;text-align: center;">
			<h:outputText value="#{itm[1]}" />
		</p:column>
		<p:column headerText="任务名称" style="width: 250px;padding-left: 3px;">
			<h:outputText value="#{itm[2]}" />
		</p:column>
		<p:column headerText="当前节点" style="width: 120px;padding-left: 3px;">
			<h:outputText value="#{itm[6]}" />
		</p:column>
		<p:column headerText="发起人" style="width: 120px;text-align: center;">
			<h:outputText value="#{itm[4]}" />
		</p:column>
		<p:column headerText="状态" style="width: 100px;text-align: center;">
			<h:outputText value="#{itm[5]=='1'?'结束':'未结束' }" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="详情" action="#{tdFlowHandledBean.modInitAction}"
				process="@this">
				<f:setPropertyActionListener target="#{tdFlowHandledBean.processId}"
					value="#{itm[0]}" />
				<f:setPropertyActionListener
					target="#{tdFlowHandledBean.businesskey}" value="#{itm[2]}" />
				<f:setPropertyActionListener target="#{tdFlowHandledBean.businessId}" value="#{itm[9]}"/>
				<f:setPropertyActionListener target="#{tdFlowHandledBean.taskId}" value="#{itm[10]}"/>
				<f:setPropertyActionListener target="#{tdFlowHandledBean.noteId}" value="#{itm[11]}"/>
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="流程图"
				action="#{tdFlowHandledBean.processPicAction}" process="@this">
				<f:setPropertyActionListener target="#{tdFlowHandledBean.processId}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5"
				rendered="#{itm[5]=='0' and tdFlowHandledBean.ifCanCx}" />
			<p:commandLink value="撤销" action="#{tdFlowHandledBean.replevyAction}"
				process="@this"
				rendered="#{itm[5]=='0' and tdFlowHandledBean.ifCanCx}">
				<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tdFlowHandledBean.processId}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5"
				rendered="#{itm[5]=='0' and tdFlowHandledBean.ifCanJjq}" />
			<p:commandLink value="加减处理人"
				action="#{tdFlowHandledBean.jjqInitAction}"
				rendered="#{itm[5]=='0' and tdFlowHandledBean.ifCanJjq}"
				process="@this" update=":mainForm:userDialog"
				oncomplete="PF('UserDialog').show()">
				<f:setPropertyActionListener target="#{tdFlowHandledBean.processId}"
					value="#{itm[0]}" />
				<f:setPropertyActionListener target="#{tdFlowHandledBean.proDefId}"
					value="#{itm[7]}" />
			</p:commandLink>
			<p:spacer width="5"
				rendered="#{itm[5]=='0' and itm[3]==tdFlowHandledBean.curUserId}" />
			<p:commandLink value="催办" action="#{tdFlowHandledBean.saveAction}"
				process="@this"
				rendered="#{itm[5]=='0' and itm[3]==tdFlowHandledBean.curUserId}">
				<p:confirm header="消息确认框" message="确定要催办吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tdFlowHandledBean.processId}"
					value="#{itm[0]}" />
				<f:setPropertyActionListener
					target="#{tdFlowHandledBean.taskNodeName}" value="#{itm[6]}" />
				<f:setPropertyActionListener target="#{tdFlowHandledBean.taskName}"
					value="#{itm[2]}" />
			</p:commandLink>
		</p:column>
	</ui:define>

	<!-- 弹出框 -->
	<ui:define name="insertDialogs">
		<!-- 用户分配 -->
		<p:dialog id="userDialog" header="加减处理人" widgetVar="UserDialog"
			resizable="false" width="800" height="450" modal="true">
			<p:dataTable var="itm" value="#{tdFlowHandledBean.actNodeUserList}"
				id="jjqUserTable" emptyMessage="没有数据！" rowIndexVar="R">
				<p:column headerText="节点名称" style="width: 150px;padding-left:3px;">
					<h:outputText value="#{itm[1]}" />
				</p:column>
				<p:column headerText="处理人" style="padding-left:3px;">
					<h:outputText value="#{itm[2]}" />
				</p:column>
				<p:column headerText="操作" style="text-align:center;width:120px;">
					<p:commandLink value="加减处理人" process="@this"
						action="#{tdFlowHandledBean.jjqInitAction2}"
						update=":mainForm:userChooseDialog"
						oncomplete="PF('UserChooseDialog').show();">
						<f:setPropertyActionListener target="#{tdFlowHandledBean.editRow}"
							value="#{R}" />
					</p:commandLink>
				</p:column>
			</p:dataTable>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="userSaveBtn"
							action="#{tdFlowHandledBean.jjqAction}"
							process="@this,:mainForm:userDialog" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="userBackBtn"
							onclick="PF('UserDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 用户待办人修改 -->
		<p:dialog id="userChooseDialog" header="选择待办人"
			widgetVar="UserChooseDialog" resizable="false" width="850"
			height="450" modal="true">
			<p:panelGrid id="mainUserChooseGrid" style="width:100%;">
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:12%;height:30px;">
						<h:outputText value="单位：" styleClass="zwx_dialog_font" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:30%;">
						<p:selectOneMenu id="searchUnitId"
							value="#{tdFlowHandledBean.searchUnitId}" style="width:200px;">
							<f:selectItem itemLabel="--全部--" itemValue="" />
							<f:selectItems value="#{tdFlowHandledBean.searchUnitMap}" />
							<p:ajax event="change" process="@form"
								listener="#{tdFlowHandledBean.unitChangeAction}"
								update="searchOfficeId,userPickList" />
						</p:selectOneMenu>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:12%;">
						<h:outputText value="科室：" styleClass="zwx_dialog_font" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:100px;">
						<p:selectOneMenu id="searchOfficeId"
							value="#{tdFlowHandledBean.searchOfficeId}" style="width:200px;">
							<f:selectItem itemLabel="--全部--" itemValue="" />
							<f:selectItems value="#{tdFlowHandledBean.searchOfficeMap}" />
							<p:ajax event="change" process="@form"
								listener="#{tdFlowHandledBean.officeChangeAction}"
								update="userPickList" />
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="4">
						<p:pickList id="userPickList"
							value="#{tdFlowHandledBean.dualListModel}" var="user"
							itemValue="#{user}" itemLabel="#{user.username}"
							converter="system.UserOfficeUnitConvert"
							showSourceControls="false" showTargetControls="false"
							showCheckbox="true" showSourceFilter="true"
							showTargetFilter="true" filterMatchMode="contains" effect="drop">
							<f:facet name="sourceCaption">
								<h:panelGrid columns="3" columnClasses="30width,30width,40width">
									<h:outputText value="姓名" />
									<h:outputText value="科室" />
									<h:outputText value="单位" />
								</h:panelGrid>
							</f:facet>
							<f:facet name="targetCaption">
								<h:panelGrid columns="3" columnClasses="30width,30width,40width">
									<h:outputText value="姓名" />
									<h:outputText value="科室" />
									<h:outputText value="单位" />
								</h:panelGrid>
							</f:facet>

							<p:column style="width:30%;text-align: center;">#{user.username}</p:column>
							<p:column style="width:30%;text-align: center;">#{user.officeNames}</p:column>
							<p:column style="width:40%;text-align: center;">#{user.tsUnit.unitSimpname}</p:column>
						</p:pickList>
					</p:column>
				</p:row>
			</p:panelGrid>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check"
							id="userChooseSaveBtn" action="#{tdFlowHandledBean.jjqAction2}"
							process="@this,:mainForm:userChooseDialog" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							id="userChooseBackBtn" onclick="PF('UserChooseDialog').hide();"
							type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</ui:define>
</ui:composition>











