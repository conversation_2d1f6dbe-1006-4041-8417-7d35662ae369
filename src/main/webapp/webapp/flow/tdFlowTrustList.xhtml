<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdFlowTrustListBean}" />
	<ui:param name="onfocus" value="false" />
	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">

		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<style type="text/css">
.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 0px;
	padding-right: 0px;
}
</style>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="2"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="流程委托" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{tdFlowTrustListBean.searchAction}" update="dataTable"
					process="@this,:mainForm:mainGrid" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
					action="#{tdFlowTrustListBean.forwardEditPage}" process="@this">
					<f:setPropertyActionListener target="#{tdFlowTrustListBean.rid}"
						value="0" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column
				style="width:120px; text-align:right;padding-right:3px; height:28px">
				<p:outputLabel value="开始时间：" />
			</p:column>
			<p:column style="width:220px; text-align:left;padding-left:5px;">
				<p:calendar pattern="yyyy-MM-dd" maxlength="10"
					showOtherMonths="true" id="searchPreBeginDate" navigator="true"
					yearRange="c-10:c+10" size="11" showButtonPanel="true"
					converterMessage="开始时间前区间，格式输入不正确！"
					value="#{tdFlowTrustListBean.searchPreBeginDate}" />~
							<p:calendar pattern="yyyy-MM-dd" maxlength="10"
					showOtherMonths="true" id="searchBackBeginDate" navigator="true"
					yearRange="c-10:c+10" size="11" showButtonPanel="true"
					converterMessage="开始时间后区间，格式输入不正确！"
					value="#{tdFlowTrustListBean.searchBackBeginDate}" />
			</p:column>
			<p:column
				style="width:120px; text-align:right;padding-right:3px; height:28px">
				<p:outputLabel value="结束时间：" />
			</p:column>
			<p:column style="width:220px; text-align:left;padding-left:5px;">
				<p:calendar pattern="yyyy-MM-dd" maxlength="10"
					showOtherMonths="true" id="searchPreEndDate" navigator="true"
					yearRange="c-10:c+10" size="11" showButtonPanel="true"
					converterMessage="结束时间前区间，格式输入不正确！"
					value="#{tdFlowTrustListBean.searchPreEndDate}" />~
							<p:calendar pattern="yyyy-MM-dd" maxlength="10"
					showOtherMonths="true" id="searchBackEndDate" navigator="true"
					yearRange="c-10:c+10" size="11" showButtonPanel="true"
					converterMessage="结束时间后区间，格式输入不正确！"
					value="#{tdFlowTrustListBean.searchBackEndDate}" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:120px;">
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:selectManyCheckbox value="#{tdFlowTrustListBean.searchStates}"
					id="searchStates">
					<f:selectItem itemLabel="有效" itemValue="1" />
					<f:selectItem itemLabel="无效" itemValue="0" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="委托给" style="width: 60px;padding-left: 3px;">
			<h:outputText value="#{itm[2]}" />
		</p:column>
		<p:column headerText="委托内容" style="width:500px;padding-left: 3px;">
			<h:outputText value="#{itm[6]}" />
		</p:column>
		<p:column headerText="委托时间"
			style="width:200px;padding-left: 3px;text-align: center;">
			<h:outputText value="#{itm[3]}" />~<h:outputText value="#{itm[4]}" />
		</p:column>
		<p:column headerText="是否有效" style="width: 60px;text-align: center;">
			<h:outputText value="#{itm[5]==0?'无效':'有效'}" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="修改" rendered="#{itm[5]==1}"
				action="#{tdFlowTrustListBean.forwardEditPage}" process="@this">
				<f:setPropertyActionListener target="#{tdFlowTrustListBean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="删除" rendered="#{itm[5]==1}"
				action="#{tdFlowTrustListBean.deleteAction}" process="@this"
				update="dataTable">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tdFlowTrustListBean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="失效" rendered="#{itm[5]==1}"
				action="#{tdFlowTrustListBean.updateStateAction}" process="@this"
				update="dataTable">
				<p:confirm header="消息确认框" message="确定要失效吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tdFlowTrustListBean.rid}"
					value="#{itm[0]}" />
				<f:setPropertyActionListener target="#{tdFlowTrustListBean.state}"
					value="0" />
			</p:commandLink>
		</p:column>
	</ui:define>

</ui:composition>











