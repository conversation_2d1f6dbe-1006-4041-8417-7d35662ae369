<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdFlowDebugBean}" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<style type="text/css">
.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 0px;
	padding-right: 0px;
}
</style>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="流程追踪" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{tdFlowDebugBean.searchAction}" update="dataTable"
					process="@this,:mainForm:mainGrid" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:180px;height:35px;">
				<h:outputText value="流程类型：" />
			</p:column>
			<p:column style="text-align:left;width:250px;">
				<div style="display: table-cell;padding-left: 3px;">
					<p:inputText id="searchFlowTypeName"
						value="#{tdFlowDebugBean.condition.searchFlowTypeName}"
						style="width: 180px;" readonly="true" />
				</div>
				<div style="display: table-cell;">
					<p:commandLink styleClass="ui-icon ui-icon-search"
						id="initTreeLink" process="@this"
						style="position: relative;left: -20px;top:5px;"
						oncomplete="PF('SearchFlowTypeName').show()" />
				</div>
				<p:overlayPanel id="flowTypePanel" for="searchFlowTypeName"
					style="width:280px;" widgetVar="SearchFlowTypeName">
					<p:tree var="node" selectionMode="single" id="flowTypeTree"
						value="#{tdFlowDebugBean.searchTreeNode}"
						style="width: 250px;height: 400px;overflow-y: auto;">
						<p:ajax event="select" update=":mainForm:searchFlowTypeName"
							listener="#{tdFlowDebugBean.onSearchNodeSelect}"
							process="@this,@parent"
							oncomplete="PF('SearchFlowTypeName').hide();" />
						<p:treeNode>
							<h:outputText value="#{node.typeName}" />
						</p:treeNode>
					</p:tree>
				</p:overlayPanel>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="任务名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<p:inputText id="searchTaskName"
					value="#{tdFlowDebugBean.condition.searchTaskName}"
					style="width: 180px;" />
			</p:column>
		</p:row>

		<p:row>
			<p:column
				style="text-align:right;padding-right:3px;width:180px;height:35px;">
				<h:outputText value="流程状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;width:250px;">
				<p:selectOneRadio id="searchFlowState"
					value="#{tdFlowDebugBean.condition.searchFlowState}"
					style="width: 220px;">
					<f:selectItem itemLabel="未结束" itemValue="0" />
					<f:selectItem itemLabel="结束" itemValue="1" />
					<f:selectItem itemLabel="全部" itemValue="2" />
				</p:selectOneRadio>
			</p:column>
			<p:column style="text-align:left;padding-left:10px;" colspan="2">
				<p:selectBooleanCheckbox id="searchMyFlow"
					value="#{tdFlowDebugBean.condition.searchMyFlow}" itemLabel="我发起的" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="流程类型" style="width: 100px;text-align: center;">
			<h:outputText value="#{itm[1]}" />
		</p:column>
		<p:column headerText="任务名称" style="width: 250px;padding-left: 3px;">
			<h:outputText value="#{itm[2]}" />
		</p:column>
		<p:column headerText="当前节点" style="width: 120px;padding-left: 3px;">
			<h:outputText value="#{itm[3]}" />
		</p:column>
		<p:column headerText="发起人" style="width: 120px;text-align: center;">
			<h:outputText value="#{itm[4]}" />
		</p:column>
		<p:column headerText="状态" style="width: 100px;text-align: center;">
			<h:outputText value="#{itm[5]}" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="详情" action="#{tdFlowDebugBean.modInitAction}"
				process="@this" immediate="true">
				<f:setPropertyActionListener target="#{tdFlowDebugBean.processId}"
					value="#{itm[0]}" />
				<f:setPropertyActionListener target="#{tdFlowDebugBean.userId}"
					value="#{itm[7]}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="流程图"
				action="#{tdFlowDebugBean.processPicAction}" process="@this">
				<f:setPropertyActionListener target="#{tdFlowDebugBean.processId}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="催办" action="#{tdFlowDebugBean.saveAction}"
				process="@this" rendered="#{itm[6]=='0'}">
				<p:confirm header="消息确认框" message="确定要催办吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tdFlowDebugBean.processId}"
					value="#{itm[0]}" />
				<f:setPropertyActionListener
					target="#{tdFlowDebugBean.taskNodeName}" value="#{itm[3]}" />
				<f:setPropertyActionListener target="#{tdFlowDebugBean.taskName}"
					value="#{itm[2]}" />
			</p:commandLink>
		</p:column>
	</ui:define>

</ui:composition>











