<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdFlowDefBean}"/>
    <!-- 节点配置编辑页面:无树形 -->
    <ui:param name="editPage" value="/webapp/flow/tdFlowNodeList.xhtml"/>

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<script type="text/javascript">
        //<![CDATA[
            function settingCheckboxSingle(obj) {
        		if(obj.checked) {
		        	var v2=document.getElementsByName("templateSelect");
		        	for(var i=0; i<v2.length; i++) {
		        		v2[i].checked = false;
		        	}
		        	obj.checked	= true;
        		}
        	}       
            var clickRepeat;
            var clickCount=0;
            var clickDelay=200;
            var rid;
            function checkClick(id) {
            	rid = id;
            	clearTimeout(clickRepeat);
            	clickCount++;
            	if(clickCount==1) {
            		clickRepeat=setTimeout('function1(); clickCount=0',clickDelay);
            	} else {
            		function2();
            		clickCount=0;
            	}
            }

            //单击事件
            function function1() {
            	refreshMetaDemo([{name:'param1', value:rid}]);
            }

            //双击事件
            function function2() {
            	addTitle([{name:'param1', value:rid}]);
            }      
        //]]>
		</script>
	    <style type="text/css">
	        .ui-picklist .ui-picklist-list{
	            text-align:left;
	            height: 340px;
	            width: 340px;
	            overflow: auto;
	        }
	
	        .ui-picklist .ui-picklist-filter {
	            padding-right: 0px;
	            width: 98%;
	        }
	        
	    </style>
	</ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="流程部署"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdFlowDefBean.searchAction}" update="dataTable" process="@this,searchFlowTypeId,searchFlowName" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" update="defEditDialog" process="@this" action="#{tdFlowDefBean.deployInit}" oncomplete="PF('DefEditDialog').show()">
					<f:setPropertyActionListener target="#{tdFlowDefBean.actionType}" value="deployAddInit"></f:setPropertyActionListener>
					<p:resetInput target="defEditDialog" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="流程类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 350px;vertical-align: middle;">
            	<div style="display: table-cell;">
            		<p:inputText id="searchFlowTypeName" value="#{tdFlowDefBean.searchFlowTypeName}"  style="width: 180px;" readonly="true"/>
            	</div>
            	<div style="display: table-cell;vertical-align: middle;">
            		<p:commandLink styleClass="ui-icon ui-icon-search"  id="initTreeLink"  process="@this" style="position: relative;left: -18px;"
			                       oncomplete="PF('SearchFlowTypeName').show()"/>
            	</div>
             	<h:inputHidden id="searchFlowTypeId" value="#{tdFlowDefBean.searchFlowTypeId}"/>
		        <p:overlayPanel id="flowTypePanel" for="searchFlowTypeName" style="width:280px;" widgetVar="SearchFlowTypeName">
		            <p:tree var="node" selectionMode="single" id="flowTypeTree"  value="#{tdFlowDefBean.searchTreeNode}"
		                    style="width: 250px;height: 400px;overflow-y: auto;">
		                <p:ajax event="select" update=":tabView:mainForm:searchFlowTypeName,:tabView:mainForm:searchFlowTypeId" listener="#{tdFlowDefBean.onSearchNodeSelect}"
		                        process="@this,@parent" oncomplete="PF('SearchFlowTypeName').hide();"/>
		                <p:treeNode>
		                    <h:outputText value="#{node.typeName}"/>
		                </p:treeNode>
		            </p:tree>
		        </p:overlayPanel>   		                       
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="流程名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText id="searchFlowName" value="#{tdFlowDefBean.searchFlowName}" maxlength="15"/>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="流程类型" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="流程名称" style="width: 180px;padding-left: 3px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="备注" style="padding-left: 3px;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="版本" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="操作" style="width: 250px;padding-left: 3px;">
             <p:commandLink value="修改" action="#{tdFlowDefBean.deployInit}" update=":tabView:mainForm:defEditDialog"
                           process="@this" oncomplete="PF('DefEditDialog').show()">
                <f:setPropertyActionListener target="#{tdFlowDefBean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{tdFlowDefBean.actionType}" value="deployModInit"></f:setPropertyActionListener>
                <p:resetInput target=":tabView:mainForm:defEditDialog"/>                
            </p:commandLink>
	        <p:spacer width="5" />        
            <p:commandLink value="节点配置" action="#{tdFlowDefBean.addInitAction}" update=":tabView" process="@this" >
                <f:setPropertyActionListener target="#{tdFlowDefBean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{tdFlowDefBean.defName}" value="#{itm[2]}"/>
            </p:commandLink>        
            <p:spacer width="5" />
             <p:commandLink value="角色分配" action="#{tdFlowDefBean.fpRoleInitAction}" update=":tabView:mainForm:grantRoleDialog"
                           process="@this" oncomplete="PF('GrantRole').show();">
                <f:setPropertyActionListener target="#{tdFlowDefBean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
	        <p:spacer width="5" />
	        <p:commandLink value="用户分配" action="#{tdFlowDefBean.yhfpInitAction}" update=":tabView:mainForm:userDialog"
	                       oncomplete="PF('UserDialog').show()" process="@this">
	            <f:setPropertyActionListener target="#{tdFlowDefBean.rid}" value="#{itm[0]}"/>
	        </p:commandLink>            
        </p:column>
    </ui:define>

	<!-- 弹出框内容 -->
	<ui:define name="insertOtherMainContents">
	    <!-- 新增流程定义 -->
	    <p:dialog id="defEditDialog" header="流程定义部署" widgetVar="DefEditDialog" resizable="false" width="650" height="300" modal="true">
	        <p:panelGrid style="width:100%;" id="defEditGrid">
	            <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;">
	                    <font color="red">*</font>
	                    <h:outputText value="流程类型："/>
	                </p:column>
	                <p:column style="text-align:left;">
		            	<div style="display: table-cell;">
		            		<p:inputText id="flowTypeName" value="#{tdFlowDefBean.flowTypeName}"  style="width: 180px;margin-left:0px;" readonly="true"/>
		            	</div>
		            	<div style="display: table-cell;vertical-align: middle;">
		            		<p:commandLink styleClass="ui-icon ui-icon-search"  id="initFlowTreeLink"  process="@this" style="position: relative;left: -18px;"
					                       oncomplete="PF('FlowTypeName').show()"/>
		            	</div>	                
	                	<h:inputHidden id="flowTypeId" value="#{tdFlowDefBean.flowTypeId}"/>
				        <p:overlayPanel id="addflowTypePanel" for="flowTypeName" style="width:280px;" widgetVar="FlowTypeName" appendToBody="true" dynamic="false">
				            <p:tree var="node" selectionMode="single" id="addFlowTypeTree"  value="#{tdFlowDefBean.searchTreeNode}"
				                    style="width: 250px;height: 400px;overflow-y: auto;">
				                <p:ajax event="select" update=":tabView:mainForm:flowTypeName,:tabView:mainForm:flowTypeId" listener="#{tdFlowDefBean.onNodeSelect}"
				                        process="@this,@parent" oncomplete="PF('FlowTypeName').hide();"/>
				                <p:treeNode>
				                    <h:outputText value="#{node.typeName}"/>
				                </p:treeNode>
				            </p:tree>
				        </p:overlayPanel>         	                    
	                </p:column>
	            </p:row>
	            
	            <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                	<font color="red">*</font>
	                    <h:outputText value="流程名称："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                    <p:inputText id="defName" value="#{tdFlowDefBean.tdFlowDef.defName}" maxlength="50" size="20"/>
	                    <p:spacer width="20"></p:spacer>
	                    <p:commandLink value="标题维护" process="@this" action="#{tdFlowDefBean.searchTitleTemplateList}"
	                    	oncomplete="PF('TemplateDialog').show()" update="templateDialog"/>   
	                </p:column>
	            </p:row>
	            
	            <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <h:outputText value="备注："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                    <p:inputText id="defDesc" value="#{tdFlowDefBean.tdFlowDef.defDesc}" maxlength="50" size="50"/>
	                </p:column>
	            </p:row>
	            
	            <p:row rendered="#{tdFlowDefBean.tdFlowDef.rid == null}">
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                	<font color="red">*</font>
	                    <h:outputText value="流程zip包："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                	<h:panelGroup id="uploadGroup">
							<p:commandButton value="上传" oncomplete="PF('fileUIdVar').show();"  
								rendered="#{tdFlowDefBean.tdFlowDef.zipPath==null}"  process="@this" update="fileUId"/>
					        <h:panelGroup rendered="#{tdFlowDefBean.tdFlowDef.zipPath!=null}">
					            <b><p:outputLabel value="#{tdFlowDefBean.tdFlowDef.zipName}"/></b>
					            <p:spacer width="5"/>
					            <p:commandButton value="删除" update="uploadGroup"  process="uploadGroup" action="#{tdFlowDefBean.deleteDiskFile}" />
					        </h:panelGroup>	                
	                	</h:panelGroup>
	                </p:column>
	            </p:row>
	            
	            <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <h:outputText value="序号："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                    <p:inputText id="nums" value="#{tdFlowDefBean.tdFlowDef.nums}" maxlength="2" size="4"  converterMessage="序号只能输入数字"/>
	                </p:column>
	            </p:row>	  
	            <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <font color="red">*</font>
	                    <h:outputText value="是否动态表单："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                   	<p:selectOneRadio value="#{tdFlowDefBean.tdFlowDef.ifDynaForm}" id="ifDynaForm"  style="width:130px;" required="true" 
	                   		requiredMessage="是否动态表单不允许为空！" >
	                   		<f:selectItem itemValue="0" itemLabel="否" />
	                   		<f:selectItem itemValue="1" itemLabel="是" />
	                   		<p:ajax event="change" process="@this,defEditGrid" update="defEditGrid" />
	                   	</p:selectOneRadio>
	                </p:column>
	            </p:row>	  
	            <p:row rendered="false"  >
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <h:outputText value="动态表单："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                    	<h:panelGrid columns="4" style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText id="formName" value="#{tdFlowDefBean.tdFlowDef.tdFormDef == null ? null : tdFlowDefBean.tdFlowDef.tdFormDef.formName}"
										size="50" style="cursor: pointer" onclick="document.getElementById('tabView:mainForm:selPageLink').click();"
										 readonly="true"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="selPageLink"
										   action="#{tdFlowDefBean.selectFormAction}" process="@this" style="position: relative;left: -32px;">
								<p:ajax event="dialogReturn" listener="#{tdFlowDefBean.onFormSelect}"
										process="@this" resetValues="true" update="formName,formVersion"/>
							</p:commandLink>
							<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn" title="清空" update="formName,formVersion"
										   action="#{tdFlowDefBean.clearFormName}" process="@this" style="position: relative;left: -28px;">
							</p:commandLink>
						</h:panelGrid>
						
						<h:outputLabel style="color:blue;" id="formVersion" value="#{tdFlowDefBean.tdFlowDef.tdFormDef != null ? '  当前版本号：' : ''} #{tdFlowDefBean.tdFlowDef.tdFormDef != null ? tdFlowDefBean.tdFlowDef.tdFormDef.formVersion : ''}"  ></h:outputLabel>
	                </p:column>
	            </p:row>	
	            <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <h:outputText value="是否在移动端显示："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                   	<p:selectOneRadio value="#{tdFlowDefBean.tdFlowDef.isDispapp}" id="isDispapp"  style="width:130px;">
	                   		<f:selectItem itemValue="0" itemLabel="否" />
	                   		<f:selectItem itemValue="1" itemLabel="是" />
	                   	</p:selectOneRadio>
	                </p:column>
	            </p:row>  
	            <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
                        <h:outputText value="流程意见是否全部显示："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                           <p:selectOneRadio value="#{tdFlowDefBean.tdFlowDef.isDispPro}" id="isDispPro"  style="width:130px;">
                               <f:selectItem itemValue="0" itemLabel="否" />
                               <f:selectItem itemValue="1" itemLabel="是" />
                           </p:selectOneRadio>
                    </p:column>
                </p:row>
	            
	
	        </p:panelGrid>
	        <f:facet name="footer">
	            <h:panelGrid style="width: 100%;text-align: center;">
	                <h:panelGroup>
	                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{tdFlowDefBean.saveAction}" 
	                    	onstart="PF('StatusDialog').show()" onsuccess="PF('StatusDialog').hide()"
	                    	process="@this,flowTypeId,defName,defDesc,nums,ifDynaForm,isDispapp,isDispPro" update="defEditGrid,dataTable"/>
	                    <p:spacer width="5" />
	                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('DefEditDialog').hide();" process="@this"/>
	                </h:panelGroup>
	            </h:panelGrid>
	        </f:facet>
	    </p:dialog>		
	    
	    <!-- 文件上传弹出框 -->
        <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false">
	           <p:fileUpload allowTypes="/(\.|\/)(zip)$/"  requiredMessage="请选择文件上传！"
	                          fileUploadListener="#{tdFlowDefBean.handleFileUpload}" fileLimit="1"
	                          fileLimitMessage="最多只能上传1个文件！" invalidFileMessage="无效的文件类型！只能上传zip类型文件"  label="选择文件"
	                          invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
	                          style="width:550px;" previewWidth="60" process="@this" update="uploadGroup"
	                          cancelLabel="取消"   uploadLabel="上传" dragDropSupport="true" mode="advanced"
	                          sizeLimit="10485760" />	   
        </p:dialog>	    
	    
        <!-- 角色授权 -->
        <p:dialog id="grantRoleDialog" header="角色分配" widgetVar="GrantRole" resizable="false" width="350" height="400" modal="true">
            <p:selectManyCheckbox id="roleCheckbox" value="#{tdFlowDefBean.selectedRoles}" layout="pageDirection">
                <f:selectItems value="#{tdFlowDefBean.roleMap}"/>
            </p:selectManyCheckbox>

            <f:facet name="footer">
                <h:panelGrid style="width:
                 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="roleSaveBtn" action="#{tdFlowDefBean.fpRoleAction}" process="@this,roleCheckbox" oncomplete="PF('GrantRole').hide();"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="roleBackBtn" onclick="PF('GrantRole').hide();" process="@this"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>	    
        
        
	    <!-- 用户分配 -->
	    <p:dialog id="userDialog" header="用户分配" widgetVar="UserDialog" resizable="false" width="800" height="450" modal="true" dynamic="true">
	
	        <table width="100%" >
	            <tr>
	                <td width="60" style="text-align: right;padding-right: 3px">科室：</td>
	                <td  style="text-align: left;">
	                	<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
		                    <p:inputText id="userOfficeName" value="#{tdFlowDefBean.userOfficeName}" readonly="true" style="width: 120px;"/>
		                    <h:inputHidden id="userOfficeRid" value="#{tdFlowDefBean.userOfficeRid}"/>
		                    <p:commandLink styleClass="ui-icon ui-icon-search"  id="initOfficeTreeLink"  process="@this" style="position: relative;left: -30px;"
					                       oncomplete="PF('UserOfficePanel').show()"/>
						</h:panelGrid>
	                    <p:overlayPanel id="userOfficePanel" for="userOfficeName" dynamic="false" style="width:280px;" widgetVar="UserOfficePanel" >
	                        <p:tree value="#{tdFlowDefBean.userOfficeTreeNode}" var="node" selectionMode="single"   id="userOfficeTree"
	                                style="width: 250px;height: 400px;overflow-y: auto;">
	
	                            <p:ajax event="select" update=":tabView:mainForm:userOfficeName,:tabView:mainForm:userOfficeRid, :tabView:mainForm:userPickList"
	                                    listener="#{tdFlowDefBean.onOfficeNodeSelect}" oncomplete="PF('UserOfficePanel').hide();"  process="@this, :tabView:mainForm:userPickList"/>
	
	                            <p:treeNode>
	                                <h:outputText value="#{node.officename}" />
	                            </p:treeNode>
	                        </p:tree>
	                    </p:overlayPanel>
	                </td>
	            </tr>
	        </table>
	
	        <p:pickList id="userPickList" value="#{tdFlowDefBean.dualListModel}" var="user"
	                    itemValue="#{user}" itemLabel="#{user.username}" converter="system.UserOfficeConvert"
	                    showSourceControls="false" showTargetControls="false" showCheckbox="true"
	                    showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
	                    effect="drop">
	
	            <f:facet name="sourceCaption">可选用户</f:facet>
	            <f:facet name="targetCaption">已选用户</f:facet>
	
	            <p:column style="width:50%;text-align: left;">#{user.username}</p:column>
	            <p:column style="width:50%;text-align: left;">#{user.officeName}</p:column>
	        </p:pickList>
	
	        <f:facet name="footer">
	            <h:panelGrid style="width: 100%;text-align: center;">
	                <h:panelGroup>
	                    <p:commandButton value="保存" icon="ui-icon-check" id="userSaveBtn" action="#{tdFlowDefBean.yhfpAction}" process="@this,userPickList" oncomplete="PF('UserDialog').hide();"/>
	                    <p:spacer width="5" />
	                    <p:commandButton value="取消" icon="ui-icon-close" id="userBackBtn" onclick="PF('UserDialog').hide();" process="@this"/>
	                </h:panelGroup>
	            </h:panelGrid>
	        </f:facet>
	    </p:dialog>  
	    
	    
	    <!--模板标题 -->
        <p:dialog id="templateDialog" header="设置标题模板" widgetVar="TemplateDialog" resizable="false" width="710" height="370" modal="true">
        	<p:outputPanel id="buttonPanel" style="width:100%;text-align:right;padding-right:3px;">
        		<p:commandButton id="addTemplateBtn" value="添加模板" action="#{tdFlowDefBean.addTitleTemplateInit}" 
        			resetValues="true" process="@this" oncomplete="TemplateMgrDialog.show();" update="templateMgrDialog"/>
        	</p:outputPanel>
        	<p:dataTable id="templateTable" value="#{tdFlowDefBean.titleTemplateList}" var="itm" style="padding-top:5px;width:680px;" 
        		emptyMessage="没有数据." scrollable="true" scrollHeight="300">
        		<p:column headerText="选择" style="width:30px;text-align:center">
        			<input type="checkbox" name="templateSelect" checked="#{itm.isDefault==1 ? 'checked':''}" onclick="settingCheckboxSingle(this)" value="#{itm.rid}"/>
        		</p:column>
        		<p:column headerText="模板名称" style="width:80px;text-align:center;">
        			<p:commandLink value="#{itm.templateName}" action="#{tdFlowDefBean.modTemplateInit}"
        				process="@this,:tabView:mainForm:templateTable" resetValues="true" oncomplete="TemplateMgrDialog.show();" 
        				update=":tabView:mainForm:templateMgrDialog">
        				<f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowTitle}" value="#{itm}"/>
        			</p:commandLink>
        		</p:column>
        		<p:column headerText="模板内容">
        			<h:outputText value="#{itm.templateContent}"/>
        		</p:column>
        	</p:dataTable>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="chooseTemplateBtn" action="#{tdFlowDefBean.chooseTitleTemplate}" 
                        	process="@this,templateTable" oncomplete="TemplateDialog.hide();"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="cancelChooseTemplateBtn"
                        	 onclick="TemplateDialog.hide();" type="button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>          	
        </p:dialog>
        
        <!--模板意见 -->
        <p:dialog id="templateMgrDialog" header="模板管理" widgetVar="TemplateMgrDialog" resizable="false" width="950" height="425" modal="true">
        	<table style="width: 100%;">
        		<tr>
        			<td style="vertical-align: top;">
        				<p:panel style="height:411px;" header="模板信息">
	        				<h:panelGrid columns="2">
	        					<span style="margin-top:5px;"><font style='color:red'>*</font>模板名称：</span>
	        					<p:inputText id="templateName" value="#{tdFlowDefBean.tdFlowTitle.templateName}" maxlength="25" size="20" 
	        						required="true" requiredMessage="模板名称不允许为空！" style="margin-top:5px;"/>
	        				
		        				<h:outputText value="模板内容：" style="margin-top:5px;"/>
		        				<p:inputTextarea id="templateContent" value="#{tdFlowDefBean.tdFlowTitle.templateContent}" rows="6" cols="50"
		        					counter="display" maxlength="1000" counterTemplate="还可以输入{0}个字!" style="margin-top:5px;"/>
		        				
		        				<p:spacer/>	
	        				 	<h:outputText id="display" style="margin-top:5px;"/>
	        				 		
		        				<p:spacer/>	
								<p:commandButton id="resolveBtn" value="测试" action="#{tdFlowDefBean.resolveTitleTemplate}" 
	        						process="@this,templateContent,resolvedContent" update="resolvedContent" style="margin-top:5px;"/>
	        				 		
	        				 	<h:outputText value="测试内容：" style="margin-top:5px;"/>	
		        				<p:inputTextarea id="resolvedContent" value="#{tdFlowDefBean.resolvedContent}" rows="8" cols="50" readonly="true" style="margin-top:5px;"/>
	        				</h:panelGrid>
        				</p:panel>
        			</td>
        			<td style="vertical-align: top;padding: 1px;">
        				<p:panel style="height:300px;width:350px;padding-right:5px;" header="模板元素">
	        				<p:dataTable id="defineList" value="#{tdFlowDefBean.defineList}" var="define" filterDelay="1000" widgetVar="defineList"
	        					scrollable="true" scrollHeight="220" style="width:340px;" filteredValue="#{tdFlowDefBean.filterDefineList}" emptyMessage="没有数据.">
	        					<p:column headerText="" filterOptions="#{tdFlowDefBean.filterDefineSelectList}" filterMatchMode="exact" 
	        						filterBy="#{define.systemType.typeCN}" style="text-align:center;width:120px;">
	        						<h:outputText value="#{define.systemType.typeCN}" converter="system.EmTempateNameConvert" title="#{define.systemType.typeCN}"/>
	        					</p:column>
	        					
	        					<p:column filterMatchMode="contains" style="text-align:center;" filterBy="metaName" headerText="">
	        						<a onmouseup="checkClick('#{define.rid}')" title="单击显示内容，双击添加！" 
	        							style="cursor: pointer" href="javascript:;">#{define.metaName}</a>
	        					</p:column>
	        				</p:dataTable>
        				</p:panel>
					    <p:panel id="metaDemo" header="示例" style="margin-top:5px;height:100px;width:350px;">
					   		<h:outputText id="metaDemoText" value="#{tdFlowDefBean.tbTempmetaDefine.demoDesc}"/>
					    </p:panel>         				
        			</td>
        		</tr>
        	</table>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="saveTemplateBtn" action="#{tdFlowDefBean.saveTitleTemplate}" 
                        	process="@this,templateMgrDialog"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="cancelSaveTemplateBtn"
                        	 onclick="TemplateMgrDialog.hide();" type="button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>        	
        </p:dialog>
	    <p:remoteCommand name="refreshMetaDemo" process="@this" action="#{tdFlowDefBean.refreshMetaDemo}" update="metaDemo"/>
	    <p:remoteCommand name="addTitle" process="@this,templateContent" action="#{tdFlowDefBean.addTitle}" update="templateContent"/>
	    <!-- 保存时状态 -->
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>     
	</ui:define>
</ui:composition>











