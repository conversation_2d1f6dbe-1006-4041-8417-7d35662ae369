<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title>请选择人员</title>
		<style type="text/css">
.ui-picklist .ui-picklist-list {
	text-align: left;
	height: 175px;
	width: 350px;
	overflow: auto;
}

.ui-picklist .ui-picklist-filter {
	padding-right: 0px;
	width: 98%;
}

.ui-picklist td {
	border-width: 0px;
	border-style: solid;
	border-color: inherit;
	padding: 4px 10px;
}

.30width {
	width: 30%;
}

.40width {
	width: 40%;
}
</style>
	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:form id="manForm">
			<p:panelGrid id="mainGrid" style="width:100%;">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:12%;height:30px;">
						<h:outputText value="单位：" styleClass="zwx_dialog_font" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:30%;">
						<p:selectOneMenu id="searchUnitId" value="#{tdFlowSubmitGDXRBean.searchUnitId}" style="width:200px;">
							<f:selectItem itemLabel="--全部--" itemValue="" />
							<f:selectItems value="#{tdFlowSubmitGDXRBean.searchUnitMap}" />
							<p:ajax event="change" process="@form" listener="#{tdFlowSubmitGDXRBean.unitChangeAction}" update="searchOfficeId,userPickList" />
						</p:selectOneMenu>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:12%;">
						<h:outputText value="科室：" styleClass="zwx_dialog_font" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:100px;">
						<p:selectOneMenu id="searchOfficeId" value="#{tdFlowSubmitGDXRBean.searchOfficeId}" style="width:200px;">
							<f:selectItem itemLabel="--全部--" itemValue="" />
							<f:selectItems value="#{tdFlowSubmitGDXRBean.searchOfficeMap}" />
							<p:ajax event="change" process="@form" listener="#{tdFlowSubmitGDXRBean.officeChangeAction}" update="userPickList" />
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="4" style="height:30px;">
						<p:pickList id="userPickList" value="#{tdFlowSubmitGDXRBean.dualListModel}" var="user" itemValue="#{user}" itemLabel="#{user.username}"
							converter="system.UserOfficeUnitConvert" showSourceControls="false" showTargetControls="false" showCheckbox="true" showSourceFilter="true"
							showTargetFilter="true" filterMatchMode="contains" effect="drop">

							<f:facet name="sourceCaption">
								<h:panelGrid columns="3" columnClasses="30width,30width,40width">
									<h:outputText value="姓名" />
									<h:outputText value="科室" />
									<h:outputText value="单位" />
								</h:panelGrid>
							</f:facet>
							<f:facet name="targetCaption">
								<h:panelGrid columns="3" columnClasses="30width,30width,40width">
									<h:outputText value="姓名" />
									<h:outputText value="科室" />
									<h:outputText value="单位" />
								</h:panelGrid>
							</f:facet>
							<p:column style="width:30%;text-align: center;">#{user.username}</p:column>
							<p:column style="width:30%;text-align: center;">#{user.officeNames}</p:column>
							<p:column style="width:40%;text-align: center;">#{user.tsUnit.unitSimpname}</p:column>
						</p:pickList>
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="4">
						<table style="width: 100%;border-color:white;">
							<p:outputPanel rendered="#{tdFlowSubmitGDXRBean.txtypeList.size() > 0}">
							<tr>
								<td style="width:90px; text-align:right;padding-right:3px;"><h:outputText value="通讯方式：" styleClass="zwx_dialog_font" /></td>
								<td style="padding-left:3px;" colspan="2"><p:selectManyCheckbox id="selectedTx" value="#{tdFlowSubmitGDXRBean.selectedTxtypeList}">
										<f:selectItems value="#{tdFlowSubmitGDXRBean.txtypeList}" var="t" itemLabel="#{t.txType.typeCN}" itemValue="#{t.rid}" />
									</p:selectManyCheckbox></td>
							</tr>
							</p:outputPanel>
							<tr>
								<td style="width:90px; text-align:right;padding-right:3px;"><h:outputText value="*" style="color: red" rendered="#{tdFlowSubmitGDXRBean.curNode.ifAdv==1}" /> <h:outputText
										value="处理意见：" styleClass="zwx_dialog_font" /></td>
								<td style="padding-left:3px;"><p:inputTextarea id="advice" value="#{tdFlowSubmitGDXRBean.advice}" style="height:50px;width:520px;" maxlength="1000"/></td>
								<td style="width: 67px;padding-left: 0px;"><p:commandButton id="templateBtn" value="意见模板" type="button" onclick="PF('IdeaModule').show();"
										style="height:50px;width:65px;" /> <p:dialog header="意见模版" widgetVar="IdeaModule" resizable="false" width="500" height="400" modal="true">
										<p:dataTable var="itm" style="width:470px;" value="#{tdFlowSubmitGDXRBean.advTemplateList}" id="ideaTable" emptyMessage="没有数据！" scrollable="true" scrollHeight="370">
											<p:column headerText="选择" style="width: 40px;text-align:center;">
												<p:commandLink value="选择" oncomplete="PF('IdeaModule').hide();" process="@this" update=":manForm:advice">
													<f:setPropertyActionListener target="#{tdFlowSubmitGDXRBean.advice}" value="#{itm.afterAnalyContent}" />
												</p:commandLink>
											</p:column>
											<p:column headerText="意见内容">
												<h:outputText value="#{itm.afterAnalyContent}" />
											</p:column>
										</p:dataTable>
									</p:dialog></td>
								<td style="width: 67px;padding-left: 0px;"><p:commandButton id="cyyBtn" value="常用用语" type="button" onclick="PF('cyyModule').show();" style="height:50px;width:65px;" />
									<p:dialog header="常用语" widgetVar="cyyModule" resizable="false" width="500" height="400" modal="true">
										<p:dataTable var="itm" style="width:470px;" value="#{tdFlowSubmitGDXRBean.cyyList}" id="cyyTable" emptyMessage="没有数据！" scrollable="true" scrollHeight="370">
											<p:column headerText="选择" style="width: 40px;text-align:center;">
												<p:commandLink value="选择" oncomplete="PF('cyyModule').hide();" process="@this" update=":manForm:advice">
													<f:setPropertyActionListener target="#{tdFlowSubmitGDXRBean.advice}" value="#{itm.codeName}" />
												</p:commandLink>
											</p:column>
											<p:column headerText="意见内容">
												<h:outputText value="#{itm.codeName}" />
											</p:column>
										</p:dataTable>
									</p:dialog></td>
							</tr>
							<tr>
								<td style="width:90px; text-align:right;padding-right:3px;"></td>
								<td style="padding-left:3px;" colspan="2"><h:outputText id="display" styleClass="zwx_dialog_font" /></td>
								<td style="width: 50px;padding: 0px;"></td>
							</tr>
						</table>
					</p:column>
				</p:row>
			</p:panelGrid>
			<p:outputPanel style="text-align:center;margin-top: 10px;">
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e" id="personSaveBtn" action="#{tdFlowSubmitGDXRBean.personSelectAction}" process="@form" />
				<p:spacer width="5" />
				<p:commandButton value="取消" icon="ui-icon-close" id="personBackBtn" action="#{tdFlowSubmitGDXRBean.dialogClose}" process="@this" />
			</p:outputPanel>
			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		</h:form>
	</h:body>
</f:view>
</html>
