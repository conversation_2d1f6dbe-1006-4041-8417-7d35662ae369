<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>动态表单页面选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>

    <h:body style="overflow-y:hidden;">
        <h:form id="mainForm">
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5">
                            <p:outputLabel value="表单名称：" styleClass="zwx_dialog_font" />
                            <p:inputText value="#{formSelectBean.searchName}" >
                            	<p:ajax event="keyup" process="@this" listener="#{formSelectBean.searchAction}" update="dataTable" />
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{formSelectBean.displayList}" id="dataTable" paginator="true" rows="10"
                         emptyMessage="没有数据！" paginatorPosition="top"  >
                <p:column headerText="选择" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{formSelectBean.selectAction}" process="@this" >
                        <f:setPropertyActionListener value="#{itm}" target="#{formSelectBean.tdFormDef}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="表单编号" style="width: 120px;padding-left: 3px;">
                    <h:outputText value="#{itm.formCode}" />
                </p:column>
                <p:column headerText="表单名称" style="padding-left: 3px;">
                    <h:outputText value="#{itm.formName}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
    </h:body>
</f:view>
</html>
