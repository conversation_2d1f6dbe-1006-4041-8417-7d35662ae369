<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
    <ui:define name="insertScripts">
        <style type="text/css">
            .ui-panelgrid td {
                padding-top: 2px;
                padding-bottom: 2px;
                padding-left: 5px;
                padding-right: 0px;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            /**
             *小图标赋值
             * @param imgsrc
             */
            function menuIcoSet(imgsrc){
                document.getElementById("mainForm:menuIcon").value=imgsrc;
                document.getElementById("mainForm:menuIconView").value=imgsrc;
                document.getElementById("imgDiv").innerHTML="<img height='24' width='24' src='/resources/images/flowType/"+imgsrc+"'/>";
                PF('MenuPanel').hide();
            }
            //]]>
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column style="text-align:left;padding-left:5px;height: 20px;" colspan="2">
                <h:outputText value="流程类型维护" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" >
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="backBtn" action="#{tdFlowTypeBean.searchAction}" update="typeTreeDataTable" process="@this,searchTypeName" />
				<p:commandButton value="添加根节点" update="typeEditDialog" icon="ui-icon-plus" process="@this,typeTreeDataTable" oncomplete="PF('TypeEditDialog').show()"
					action="#{tdFlowTypeBean.addInit}" id="addRootBtn">
					<p:resetInput target="typeEditDialog" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 10%">
                <h:outputText value="类型名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchTypeName"
                             value="#{tdFlowTypeBean.searchTypeName}" style="width: 180px;" />
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格 -->
    <ui:define name="insertContent">
        <p:contextMenu for="typeTreeDataTable">
            <p:menuitem value="添加子节点" update="typeEditDialog" icon="ui-icon-plus" process="@this,typeTreeDataTable"
                        oncomplete="PF('TypeEditDialog').show()" actionListener="#{tdFlowTypeBean.addInit}">
                <f:setPropertyActionListener target="#{tdFlowTypeBean.isCode}" value="true"/>
            </p:menuitem>
            <p:menuitem value="修改" update="typeEditDialog" icon="ui-icon-pencile" process="@this,typeTreeDataTable"
                        oncomplete="PF('TypeEditDialog').show()" actionListener="#{tdFlowTypeBean.modInit}"/>
            <p:menuitem value="删除" update="typeTreeDataTable" icon="ui-icon-close" process="@this,typeTreeDataTable"
                        action="#{tdFlowTypeBean.deleteAction}">
            </p:menuitem>
        </p:contextMenu>

        <p:treeTable value="#{tdFlowTypeBean.treeNode}" var="itm"
                     id="typeTreeDataTable" emptyMessage="没有您要找的记录！"
                     selectionMode="single" selection="#{tdFlowTypeBean.selectedNode}">
            <p:column headerText="类型名称" style="width: 80px;">
                <h:outputText value="#{itm.typeName}" />
            </p:column>
            <p:column headerText="上级类型名称" style="width: 80px;">
                <h:outputText value="#{itm.parent.typeName}" />
            </p:column>
            <p:column headerText="说明" style="width: 80px;">
                <h:outputText value="#{itm.typeDesc}" />
            </p:column>
            <p:column headerText="图标地址" style="width: 80px;">
                <img height='24' width='24' src='#{itm.picPath}' style="display: #{itm.picPath == null ? 'none' : ''}"/>
            </p:column>
        </p:treeTable>

        <!-- 新增、修改 -->
        <p:dialog id="typeEditDialog" header="流程类型维护" widgetVar="TypeEditDialog"
                  resizable="false" width="600" height="230" modal="true">
            <p:panelGrid style="width:100%;" id="typeEditGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputText value="类型名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="typeName" value="#{tdFlowTypeBean.tdFlowType.typeName}" size="24" maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="序号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="nums" value="#{tdFlowTypeBean.tdFlowType.nums}" size="24" maxlength="4"
                                converterMessage="请输入正确的序号！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="说明："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="typeDesc" value="#{tdFlowTypeBean.tdFlowType.typeDesc}" size="50" maxlength="100"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputLabel for="menuIconView" value="图标选择："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                        <h:inputHidden   id="menuIcon"  value="#{tdFlowTypeBean.tdFlowType.picPath}" />
                        <p:inputText   id="menuIconView" value="#{tdFlowTypeBean.tdFlowType.picPath}"  readonly="true"  style="width:160px;">
                        </p:inputText> &#160; &#160; &#160;<span id="imgDiv">
                    <h:panelGroup rendered="#{tdFlowTypeBean.tdFlowType.picPath!=null}">
                        <img height='24' width='24' src='/resources/images/flowType/#{tdFlowTypeBean.tdFlowType.picPath}'/></h:panelGroup></span>
                        <p:overlayPanel   id="menuPanel" for="menuIconView"  style="width:300px;"
                                          widgetVar="MenuPanel"  >
                            <p:scrollPanel style="width: 280px;height:120px;overflow-x: hidden;" mode="native">
                                <ui:repeat value="#{tdFlowTypeBean.imgUrlList}" var="menu" varStatus="ind" >
                                    &#160;
                                    <img onclick="menuIcoSet('#{menu}')" height="24" width="24" style="cursor:pointer" src="/resources/images/flowType/#{menu}">
                                    </img>
                                    &#160;
                                </ui:repeat>
                            </p:scrollPanel>
                        </p:overlayPanel>
                    </p:column>
                </p:row>
                 <p:row>
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <h:outputText value="移动端图标："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;vertical-align: middle;">
	                    <h:panelGroup id="uploadGroup2">
                            <p:commandButton value="上传" oncomplete="PF('filePicUIdVar').show();"
                                             rendered="#{tdFlowTypeBean.tdFlowType.mobilePicUrl==null}"
                                             process="@this" update="filePicUId"/>
                            <h:panelGroup rendered="#{tdFlowTypeBean.tdFlowType.mobilePicUrl!=null}">
                                <h:graphicImage url="/webFile#{tdFlowTypeBean.tdFlowType.mobilePicUrl}"  style="width:28px;height:28px;margin-bottom:-10px;"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="删除" update="uploadGroup2"  process="@this"
                                                 action="#{tdFlowTypeBean.deleteMobilePic}" />
                            </h:panelGroup>
                            <h:outputText style="color:blue;" value="　[推荐像素：40*40]" />
                        </h:panelGroup>
	                </p:column>
	            </p:row>  
                <p:row>
                    <p:column style="text-align:center;" colspan="2">
                        <p:commandButton value="保存" icon="ui-icon-check" id="typeSaveBtn" action="#{tdFlowTypeBean.saveAction}"
                                         process="@this,@form" update="typeTreeDataTable,typeEditGrid"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="typeBackBtn" onclick="PF('TypeEditDialog').hide();" immediate="true"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog> 
        
        <!--图标上传 -->
		<p:dialog header="文件上传" widgetVar="filePicUIdVar" id="filePicUId"
			resizable="false" modal="true">
			<p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" 
				previewWidth="50" fileUploadListener="#{tdFlowTypeBean.handlePicUpload}"
				fileLimit="1" fileLimitMessage="最多只能上传1个文件！" label="选择文件"
				uploadLabel="上传" cancelLabel="取消" sizeLimit="1048576"
				invalidSizeMessage="文件大小不能超过2M!" validatorMessage="上传出错啦，重新上传！"
				invalidFileMessage="无效的文件类型！只能上传jpg,bmp,png,gif等图片类型文件！"
				process="@this" update="uploadGroup2" mode="advanced"
				dragDropSupport="true" allowTypes="/(\.|\/)(jpe?g|bmp|png|gif)$/" />
		</p:dialog>
    </ui:define>
</ui:composition>