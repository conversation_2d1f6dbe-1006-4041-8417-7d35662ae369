<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdFlowTaskBean}"/>

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
	
    	<!--引入中文日期-->
    	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
    
	    <style type="text/css">
		.ui-panelgrid td {
            padding-top: 2px;
            padding-bottom: 2px;
            padding-left: 0px;
            padding-right: 0px;
        }
		table.ui-selectoneradio td label{
			 white-space:nowrap;
	    	 overflow: hidden;
	    	 padding: 5px;
		}        	        
	    </style>	
	</ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="待办任务"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdFlowTaskBean.searchAction}" 
					update="dataTable" process="@this,:mainForm:mainGrid" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:120px;height:35px;">
                <h:outputText value="流程类型：" />
            </p:column>
            <p:column style="text-align:left;width:250px;">
            
            	<div style="display: table-cell;padding-left: 3px;">
            		<p:inputText id="searchFlowTypeName" value="#{tdFlowTaskBean.condition.searchFlowTypeName}"  style="width: 180px;" readonly="true"/>
            	</div>
            	<div style="display: table-cell;">
			        <p:commandLink styleClass="ui-icon ui-icon-search"  id="initTreeLink"  process="@this" style="position: relative;left: -20px;top:5px;"
			                       oncomplete="PF('SearchFlowTypeName').show()"/>
            	</div>
		        <p:overlayPanel id="flowTypePanel" for="searchFlowTypeName" style="width:280px;" widgetVar="SearchFlowTypeName">
		            <p:tree var="node" selectionMode="single" id="flowTypeTree"  value="#{tdFlowTaskBean.searchTreeNode}"
		                    style="width: 250px;height: 400px;overflow-y: auto;">
		                <p:ajax event="select" update=":mainForm:searchFlowTypeName" listener="#{tdFlowTaskBean.onSearchNodeSelect}"
		                        process="@this,@parent" oncomplete="PF('SearchFlowTypeName').hide();"/>
		                <p:treeNode>
		                    <h:outputText value="#{node.typeName}"/>
		                </p:treeNode>
		            </p:tree>
		        </p:overlayPanel>               
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:100px;">
            	<h:outputText value="任务名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
            	<p:inputText id="searchTaskName" value="#{tdFlowTaskBean.condition.searchTaskName}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:120px;">
                <h:outputText value="流程发起时间：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:250px;">
            	<p:calendar id="searchStartTime" value="#{tdFlowTaskBean.condition.searchStartTime}" size="11" navigator="true" yearRange="c-10:c"
                                converterMessage="转正日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                <h:outputText value=" ~ " />                
            	<p:calendar id="searchEndTime" value="#{tdFlowTaskBean.condition.searchEndTime}" size="11" navigator="true" yearRange="c-10:c"
                                converterMessage="转正日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;" colspan="2">
            	<p:selectOneRadio id="searchTrust" value="#{tdFlowTaskBean.searchTrust}">
            		<f:selectItem itemLabel="包含委托" itemValue="0"/>
            		<f:selectItem itemLabel="不包含委托" itemValue="1"/>
            		<f:selectItem itemLabel="仅委托" itemValue="2"/>
            	</p:selectOneRadio>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="流程类型" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="流程名称" style="width: 180px;padding-left: 3px;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="任务名称" style="width: 280px;padding-left: 3px;">
        	<h:graphicImage rendered="#{itm[11]=='99'}" url="/resources/images/flow/cb.png" height="16" width="16"/>
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="当前节点" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[7]}" />
        </p:column>        
        <p:column headerText="发起人" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[8]}" />
        </p:column>        
        <p:column headerText="发起时间" style="width: 150px;text-align: center;">
            <h:outputText value="#{itm[9]}" />
        </p:column>        
        <p:column headerText="操作" style="padding-left: 3px;">   
             <p:commandLink value="处理" action="#{tdFlowTaskBean.taskDealInitAction}" process="@this" immediate="true">
                <f:setPropertyActionListener target="#{tdFlowTaskBean.taskId}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{tdFlowTaskBean.nodeId}" value="#{itm[6]}"/>
                <f:setPropertyActionListener target="#{tdFlowTaskBean.businessId}" value="#{itm[10]}"/>
            </p:commandLink>       	
        </p:column>
    </ui:define>

</ui:composition>











