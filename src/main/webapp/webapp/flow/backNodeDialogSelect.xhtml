<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:f="http://java.sun.com/jsf/core" 
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title>退回节点选择</title>
		<style type="text/css">
		</style>
        <script type="text/javascript">
        //<![CDATA[
        jQuery(function(){
        	ifOnlyOneNode();
        })
                   
        //只有一个时默认勾选           
        function ifOnlyOneNode(){
        	var nodes=jQuery(':checkbox[id*="ActNode_"]');
        	if(nodes.length==1){
        		jQuery(nodes).attr('checked', true); 
        		jQuery('div[id*="ActNode_"]').each(function() {
      					jQuery(this).children('div.ui-chkbox-box').each(function() {
            	    		jQuery(this).addClass('ui-state-active');
            	    		jQuery(this).children('span').addClass('ui-icon ui-icon-check');
        				});
        		});
        	}
        }           
                   
        function node_select(id) {
        	var wholeId = "selectForm:"+id+"_input";
        	var checked = document.getElementById(wholeId).checked;
        	if(checked) {
        		var groupId="selectForm:"+id.substring(0,id.indexOf('_Node_'));
        		jQuery(':checkbox[id*="ActNode_"]').each(function() {
        			if(this.id.indexOf(groupId) < 0) {
        				jQuery(this).attr('checked', false); 
        			}
        		});
        		
        		jQuery('div[id*="ActNode_"]').each(function() {
        			if(this.id.indexOf(groupId) < 0) {
      					jQuery(this).children('div').each(function() {
            	    		jQuery(this).removeClass('ui-state-active');
            	    		jQuery(this).children('span').removeClass('ui-icon ui-icon-check');
        				});

        			}
        		});
        	}			
        } 
        
		function beforeSubmit() {
			var nodeIds="";
			jQuery(':checkbox[id*="ActNode_"]').each(function() {
				if(jQuery(this).is(':checked')) {
					nodeIds = nodeIds + "," + jQuery(this).attr('data-p-label');
				}
			});
			jQuery("#selectForm\\:backNodeId").attr('value', nodeIds);
		}        
        //]]>
        </script>
	</h:head>

	<h:body style="margin:0px;padding:0px;">
		<h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:form id="selectForm"  style="margin:0px;padding:0px;">
			<p:panelGrid style="width:690px;margin-top:5px;">
				<div id="nodeDiv">
					<c:forEach items="#{backNodeSelectBean.backNodeList}" var="entry" varStatus="nodeInx">
						<p:row>
							<p:column style="width:140px;">
								<c:forEach items="#{entry}" var="itm">
									<div style="display: table-row;vertical-align: middle;">
										<div style="display: table-cell;height:28px;vertical-align: middle;">
											<p:selectBooleanCheckbox itemLabel="#{itm.activitiNodeName}" id="ActNode_#{nodeInx.index}_Node_#{itm.activitiNodeKey}" 
												onchange="node_select('ActNode_#{nodeInx.index}_Node_#{itm.activitiNodeKey}')" label="#{itm.activitiNodeKey}"/> 
										</div>
									</div>
								</c:forEach>
							</p:column>
							<p:column style="padding-left:3px;">
								<c:forEach items="#{entry}" var="itm">
									<div style="display: table-row;vertical-align: middle;">
										<div style="display: table-cell;height:28px;padding-left:20px;vertical-align: middle;">
											<c:forEach items="#{itm.assigeeList}" var="user" varStatus="inx">
												<c:if test="#{inx.index > 0}">，</c:if>#{user.username} 
											</c:forEach>
										</div>
									</div>
								</c:forEach>
							</p:column>	
						</p:row>					
					</c:forEach>
				</div>
				<p:row rendered="#{backNodeSelectBean.txtypeList.size() > 0}">
					<p:column style="width:140px; text-align:right;padding-right:3px;height:28px;">
						<h:outputText value="通讯方式：" />
					</p:column>
					<p:column style="padding-left:3px;">
						<p:selectManyCheckbox id="selectedTx" value="#{backNodeSelectBean.selectedTxtypeList}">
							<f:selectItems value="#{backNodeSelectBean.txtypeList}" var="t" itemLabel="#{t.txType.typeCN}" itemValue="#{t.rid}" />
						</p:selectManyCheckbox>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="width:140px; text-align:right;padding-right:3px;">
						<h:outputText value="退回意见：" />
					</p:column>
					<p:column style="padding-left:3px;">
						<div style="display: table-row;">
							<div style="display:table-cell;">
								<p:inputTextarea id="backAdvice" value="#{backNodeSelectBean.advice}" style="height:50px;width:450px;" counter="display" maxlength="1000"
									counterTemplate="还可以输入{0}个字!" />	
							</div>
							<div style="display:table-cell;vertical-align: top;">
								<p:commandButton id="cyyBtn" value="常用用语" type="button" onclick="PF('cyyModule').show();" style="height:60px;width:65px;" />			
							</div>
						</div>
						<div style="display: table-row;">
							<h:outputText id="display" styleClass="zwx_dialog_font" />	
						</div>
    					<p:overlayPanel id="cyyPanel" for="cyyBtn" hideEffect="fade" dynamic="false" style="width:300px;" 
    						showCloseIcon="true" widgetVar="CyyPanel" my="left bottom" appendToBody="true">
    						<p:dataList id="cyyDataTable" value="#{backNodeSelectBean.cyyList}" var="itm" paginator="false" emptyMessage="没有数据">
    							<p:commandLink value="#{itm.codeName}" process="@this" update=":selectForm:backAdvice"
    								oncomplete="PF('CyyPanel').hide();">
    								<f:setPropertyActionListener target="#{backNodeSelectBean.advice}" value="#{itm.codeName}"/>
    							</p:commandLink>
    						</p:dataList>
    					</p:overlayPanel>							
					</p:column>
				</p:row>
			</p:panelGrid>		
			<p:outputPanel style="text-align:center;position: absolute;top:350px;width:100%;">
				<h:inputHidden id="backNodeId" value="#{backNodeSelectBean.backNodeId}"/>
				<p:commandButton value="确定" icon="ui-icon-check" id="saveBtn" onclick="beforeSubmit()" 
					action="#{backNodeSelectBean.selectAction}" process="@form" update="selectForm"/>
				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{backNodeSelectBean.dialogClose}" process="@this" />
			</p:outputPanel>
			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>			
		</h:form>
	</h:body>
</f:view>
</html>