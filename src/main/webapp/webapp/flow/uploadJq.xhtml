<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

	<link rel="stylesheet" type="text/css"
		href="/resources/upload/css/default.css" />
	<link rel="stylesheet" type="text/css"
		href="/resources/upload/css/uploadify.css" />
	<script type="text/javascript"
		src="/resources/upload/js/jquery.popWin.min.js"></script>
	<script type="text/javascript"
		src="/resources/upload/js/jquery.uploadify.v2.1.0.js"></script>
	<script type="text/javascript" src="/resources/upload/js/swfobject.js"></script>

	<div id="downLoadDia" style="display:none;" title="上传附件">
		<table width="100%" cellpadding="0" cellspacing="1">
			<tr>
				<td style="text-align: left; "><input type="file"
					name="uploadify" id="uploadify" /></td>
			</tr>
		</table>
		<div id="fileQueue"></div>
		<input type="hidden" id="filename" name="filename" />
	</div>


	<script type="text/javascript">
		//<![CDATA[
		//上传文件ID
		var gobalFileUpId = "";

		function dialogInit() {
			jQuery('#downLoadDia').puidialog({
				showEffect : 'fade',
				hideEffect : 'fade',
				minimizable : false,
				maximizable : false,
				responsive : false,
				resizable : false,
				width : 450,
				height : 180,
				closable : true,
				draggable : false,
				modal : true
			});
		}

		/**
		 * 下载
		 */
		function dynaDownLoadFile(filePutId) {
			if ('' != filePutId) {
				var path = jQuery("#" + filePutId + "PATH").val();
				var name = jQuery("#" + filePutId + "NAME").val();
				var url = '/DownLoad?path=' + path + '&name=' + name;
				url = encodeURI(url);
				var form = jQuery("<form>");
				form.attr('style', 'display:none');
				form.attr('target', '');
				form.attr('method', 'post');
				form.attr('encoding', 'multipart/form-data');
				form.attr('action', url);
				jQuery('body').append(form);
				form.submit();
				form.remove();
			}
		}

		function dynaDelFile(filePutId) {
			if ('' != filePutId) {
				jQuery("#" + filePutId + "PATH").val("");
				jQuery("#" + filePutId + "NAME").val("");
				jQuery("#" + filePutId + "_SPAN").text("");

				jQuery("#" + filePutId + "_UP").css('display', '');
				jQuery("#" + filePutId + "_DOWN").css('display', 'none');
				jQuery("#" + filePutId + "_DEL").css('display', 'none');
			}
		}

		function dynaUploadFile(filePutId) {
			if (null != filePutId) {
				gobalFileUpId = filePutId;
				jQuery('#downLoadDia').puidialog('show');
			}
		}

		function initUpLoad() {
			dialogInit();
			jQuery("#uploadify")
					.uploadify(
							{
								'uploader' : '/resources/upload/swf/uploadify.swf?var='
										+ Math.random(),
								'script' : '/Upload?var=' + Math.random(),
								'cancelImg' : '/resources/upload/images/cancel.png',
								'queueID' : 'fileQueue',
								'auto' : true,
								'multi' : true,
								'fileDesc' : '*.*',
								'fileExt' : '*.*',
								'height' : 35,
								'width' : 118,
								'buttonImg' : '/resources/upload/images/picture.jpg',
								'simUploadLimit' : 3,
								//'queueSizeLimit' :1, //可上传的文件个数 
								'wmode' : 'transparent',
								'onComplete' : function(event, queueID,
										fileObj, response, data) {
									document.getElementById("filename").value = response;
									console.log("response====" + response);
									if (null != response
											&& "undefined" != response) {
										jQuery("#" + gobalFileUpId + "_UP")
												.css('display', 'none');
										jQuery("#" + gobalFileUpId + "_DOWN")
												.css('display', '');
										jQuery("#" + gobalFileUpId + "_DEL")
												.css('display', '');

										var filePath = response.substr(1);
										var pathArr = filePath.split("@#@");
										var fiName = pathArr[0];
										var fiPath = pathArr[1];
										jQuery("#" + gobalFileUpId + "_SPAN")
												.text(fiName);
										jQuery("#" + gobalFileUpId + "NAME")
												.val(fiName);
										jQuery("#" + gobalFileUpId + "PATH")
												.val(fiPath);
									}
									jQuery('#downLoadDia').puidialog('hide');
								},
								'onError' : function(event, queueID, fileObj) {
									alert("文件:" + fileObj.name + " 上传失败");
								},
								'sizeLimit' : 104857600
							});
		}
		//]]>
	</script>
</ui:composition>

