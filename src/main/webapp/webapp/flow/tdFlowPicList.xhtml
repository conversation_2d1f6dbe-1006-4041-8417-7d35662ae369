<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>流程图</title>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:form id="mainForm">
	        <p:dataTable var="itm" value="#{tdFlowHisTaskBean.dataList}" id="dataList" emptyMessage="没有您要找的记录！">
	            <p:column headerText="节点名称" style="width: 80px;text-align: center;">
	                <h:outputText value="#{itm[0]}" />
	            </p:column>
	            <p:column headerText="处理人" style="width: 60px;text-align: center; ">
	                <h:outputText value="#{itm[1]}" />
	            </p:column>
       			<p:column headerText="选择的处理人" style="min-width: 80px;">
	                <h:outputText value="#{itm[12]}" />
	            </p:column>
	            <p:column headerText="通知方式" style="width: 60px;text-align: center; ">
	                <h:outputText value="#{itm[6]}" />
	            </p:column>
	            <p:column headerText="处理时间" style="width: 140px;text-align: center; ">
	                <h:outputText value="#{itm[2]}" />
	            </p:column>
	            <p:column headerText="处理意见" style="padding-left: 3px;font-weight:bold;">
	                <h:outputText value="#{itm[3]}" />
	            </p:column>
	            <p:column headerText="发送状态" style="width: 60px;text-align: center; ">
	                <h:outputText value="#{itm[7]}" />
	            </p:column>
	            <p:column headerText="接收状态" style="width: 60px;text-align: center; ">
	                <h:outputText value="#{itm[8]}" />
	            </p:column>
	            <p:column headerText="接收时间" style="width: 140px;text-align: center; ">
	                <h:outputText value="#{itm[9]}" />
	            </p:column>
	            
	        </p:dataTable>		
	        <img src="#{tdFlowHisTaskBean.servletUrl}" />	
        </h:form>
    </h:body>
</f:view>
</html>
