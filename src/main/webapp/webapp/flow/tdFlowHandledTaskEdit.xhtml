<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
       <!-- 编辑页面的script -->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
    </ui:define>

       <!-- 标题栏 -->
       <ui:define name="insertEditTitle">
           <p:row>
               <p:column  style="text-align:left;padding-left:5px;height: 20px;">
                   <h:outputText value="详情"/>
               </p:column>
           </p:row>
       </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="流程图" icon="ui-icon-image" id="processPicBtn" actionListener="#{tdFlowHandledBean.processPicAction}" process="@this" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tdFlowHandledBean.backAction}" process="@this" update=":tabView" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
       <ui:include src="#{tdFlowHandledBean.tdFlowNode.jspUrl}"/>
    </ui:define>
</ui:composition>

