<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/editTemplate.xhtml">

	<!-- 脚本 -->
	<ui:define name="insertEditScripts">
		<script type="text/javascript">
        //<![CDATA[
        	function settingCheckboxSingle(obj) {
        		if(obj.checked) {
		        	var v2=document.getElementsByName("templateSelect");
		        	for(var i=0; i<v2.length; i++) {
		        		v2[i].checked = false;
		        	}
		        	obj.checked	= true;
        		}
        	}
			function settingCheckboxAll(obj,num){
				var btns;
				if(num == 1){
					btns=document.getElementsByName("btnSelect");
				}else if(num == 2){
					btns=document.getElementsByName("txSelect");
				}
				for(var i = 0 ; i < btns.length ; i++) {
					if (obj.checked) {
						btns[i].checked = true;
					} else {
						btns[i].checked = false;
					}
				}
			}
        //]]>
		</script>
		<script type="text/javascript">
        //<![CDATA[
var clickRepeat;
var clickCount=0;
var clickDelay=200;
var rid;
function checkClick(id) {
	rid = id;
	clearTimeout(clickRepeat);
	clickCount++;
	if(clickCount==1) {
		clickRepeat=setTimeout('function1(); clickCount=0',clickDelay);
	} else {
		function2();
		clickCount=0;
	}
}

//单击事件
function function1() {
	refreshMetaDemo([{name:'param1', value:rid}]);
}

//双击事件
function function2() {
	addMeta([{name:'param1', value:rid}]);
} 
        //]]>		
		</script>
		
		<style type="text/css">
	table.ui-selectoneradio td label{
		 white-space:nowrap;
    	 overflow: hidden;
	}		
		</style>
	</ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="节点配置"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="按钮初始化" icon="ui-icon-plus" id="btnInit" action="#{tdFlowDefBean.buttonInitAction}" process="@this" update="nodeDataTable">
					<p:confirm header="消息确认框" message="确定要初始化吗？" icon="ui-icon-alert"/>
				</p:commandButton>
				<p:commandButton value="通讯方式初始化" icon="ui-icon-check" id="txbtnInit" action="#{tdFlowDefBean.txTypeInitAction}" process="@this" update="nodeDataTable">
					<p:confirm header="消息确认框" message="确定要初始化吗？" icon="ui-icon-alert"/>
				</p:commandButton>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tdFlowDefBean.backAction}" update=":tabView" immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertOtherContents">
        <p:dataTable var="itm" value="#{tdFlowDefBean.flowNodeList}" id="nodeDataTable" emptyMessage="没有您要找的记录！">
            <f:facet name="header"><h:outputText value="#{tdFlowDefBean.defName}"/></f:facet>
            <p:column headerText="节点名称" style="width: 200px;padding-left: 3px;">
                <h:outputText value="#{itm.nodeName}" />
            </p:column>
            <p:column headerText="跳转方式" style="width: 160px;text-align: center;">
                <h:outputText value="#{itm.flowInType.typeCN}" />
            </p:column>
            <p:column headerText="是否被拟办" style="width: 100px;text-align: center;">
                <h:outputText value="#{itm.ifNbNode==1?'是':'否'}" />
            </p:column>
            <p:column headerText="意见是否必输" style="width: 100px;text-align: center;">
                <h:outputText value="#{itm.ifAdv==1?'是':'否'}" />
            </p:column>
            <p:column headerText="任务处理类型" style="width: 100px;text-align: center;">
                <h:outputText value="#{itm.dealType.typeCN}" />
            </p:column>
            <p:column headerText="操作" style="padding-left: 3px;">
                <p:commandLink value="修改" action="#{tdFlowDefBean.nodeAddInit}" update=":tabView:editForm:nodeEditDialog"
                               oncomplete="PF('NodeEditDialog').show()" process="@this">
                    <f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowNode}" value="#{itm}"/>
                    <p:resetInput target=":tabView:editForm:nodeEditDialog"/>
                </p:commandLink>
                <p:spacer width="5"/>
                <p:commandLink value="节点脚本" action="#{tdFlowDefBean.nodeScriptInitAction}" update=":tabView:editForm:nodeScriptDialog"
                               oncomplete="PF('NodeScriptDialog').show()" process="@this" >
                    <f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowNode}" value="#{itm}"/>
                    <p:resetInput target=":tabView:editForm:nodeScriptDialog"/>
                </p:commandLink>
                <p:spacer width="5" rendered="#{itm.nums != 1}"/>
                <p:commandLink value="设置待办人" action="#{tdFlowDefBean.nodeSqInit}" update=":tabView:editForm:assigneeEditDialog"
                               oncomplete="PF('AssigneeEditDialog').show()" process="@this" rendered="#{itm.nums != 1}">
                    <f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowNode}" value="#{itm}"/>
                    <p:resetInput target=":tabView:editForm:assigneeEditDialog"/>
                </p:commandLink>   
                <p:spacer width="5"/>
                <p:commandLink value="意见模板" action="#{tdFlowDefBean.searchTemplateList}" update=":tabView:editForm:templateDialog"
                               oncomplete="PF('TemplateDialog').show()" process="@this">
                    <f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowNode}" value="#{itm}"/>
                    <p:resetInput target=":tabView:editForm:templateDialog"/>
                </p:commandLink>
				<p:spacer width="5"/>
				<p:commandLink value="按钮配置" action="#{tdFlowDefBean.buttonCfgInit}" process="@this" update=":tabView:editForm:buttonDialog"
							   oncomplete="PF('ButtonDialog').show()" resetValues="true">
					<f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowNode}" value="#{itm}"/>
					<p:resetInput target=":tabView:editForm:buttonDialog"/>
				</p:commandLink>
				<p:spacer width="5"/>
				<p:commandLink value="通讯方式配置" action="#{tdFlowDefBean.txTypeCfgInit}" process="@this" update=":tabView:editForm:txTypeDataTable"
							   oncomplete="PF('TxTypeDialog').show()" resetValues="true">
					<f:setPropertyActionListener target="#{tdFlowDefBean.tdFlowNode}" value="#{itm}"/>
					<p:resetInput target=":tabView:editForm:txTypeDataTable"/>
				</p:commandLink>
            </p:column>
        </p:dataTable>

        <!--节点脚本-->
        <p:dialog id="nodeScriptDialog" header="节点脚本" widgetVar="NodeScriptDialog" resizable="false" width="700" height="340" modal="true">
            <p:panelGrid style="width:100%;margin-top:-1px;" id="nodeScriptGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:100px;">
                        <h:outputLabel value="*" style="color: red"/>
                        <h:outputText value="执行脚本："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputTextarea value="#{tdFlowDefBean.tdFlowNodeScript.script}" rows="17" cols="60"
                                         maxlength="2000"  style="margin-top:5px;" autoResize="false"
                                required="true" requiredMessage="执行脚本不允许为空"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height:30px;">
                        <h:outputLabel value="*" style="color: red"/>
                        <h:outputText value="执行顺序："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneRadio value="#{tdFlowDefBean.tdFlowNodeScript.scriptType}" style="width:230px;"
                                          required="true" requiredMessage="执行顺序不允许为空">
                            <f:selectItem itemLabel="进入节点前"  itemValue="0"/>
                            <f:selectItem itemLabel="离开节点前"  itemValue="1"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" action="#{tdFlowDefBean.nodeScriptSaveAction}"
                                         process="@this,nodeScriptGrid" update="nodeScriptGrid" />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('NodeScriptDialog').hide();" process="@this"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

		<!--按钮配置-->
		<p:dialog id="txTypeDialog" header="通讯方式配置" widgetVar="TxTypeDialog" resizable="false" width="450" height="250" modal="true">
			<p:dataTable id="txTypeDataTable" value="#{tdFlowDefBean.txtypeList}" var="txitm" style="padding-top:5px"
						 emptyMessage="没有数据."   >
				<p:columnGroup type="header">
					<p:row>
						<p:column style="width:30px;text-align:center" >
							<f:facet name="header" >
								<input type="checkbox" id="txAllSelect" checked="#{tdFlowDefBean.txselected==1 ? 'checked':''}" onclick="settingCheckboxAll(this,2)" />
							</f:facet>
						</p:column>
						<p:column >
							<f:facet name="header">
								<h:outputText value="编码" />
							</f:facet>
						</p:column>
						<p:column >
							<f:facet name="header">
								<h:outputText value="通讯类型" />
							</f:facet>
						</p:column>
						<p:column >
							<f:facet name="header">
								<h:outputText value="实现类" />
							</f:facet>
						</p:column>
					</p:row>
				</p:columnGroup>
				<p:column style="text-align: center">
					<input type="checkbox" name="txSelect" checked="#{txitm.selected == 1 ? 'checked':''}" value="#{txitm.rid}"/>
				</p:column>
				<p:column style="text-align: center;width:18%;" >
					<p:outputLabel value="#{txitm.typeCode}"/>
				</p:column>
				<p:column style="text-align: center;width:28%" >
					<h:outputLabel value="#{txitm.txType.typeCN}"/>
				</p:column>
				<p:column style="width:50%" >
					<p:outputLabel value="#{tdFlowDefBean.getImpl(txitm.implClass)}"/>
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" action="#{tdFlowDefBean.txTypeCfgSaveAction}" oncomplete="PF('TxTypeDialog').hide();"
										 process="@this,txTypeDataTable" update="txTypeDataTable" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" onclick="PF('TxTypeDialog').hide();" process="@this"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!--按钮配置-->
		<p:dialog id="buttonDialog" header="按钮配置" widgetVar="ButtonDialog" resizable="false" width="360" height="400" modal="true">
			<p:dataTable id="buttonTable" value="#{tdFlowDefBean.buttonList}" var="btnitm" style="padding-top:5px"
						 emptyMessage="没有数据."  >
				<p:columnGroup type="header">
					<p:row>
						<p:column style="width:30px;text-align:center" >
							<f:facet name="header" >
								<input type="checkbox" id="btnAllSelect" checked="#{tdFlowDefBean.allSelect==1 ? 'checked':''}" onclick="settingCheckboxAll(this,1)" />
							</f:facet>
						</p:column>
						<p:column style="text-align:center;width: 100px;">
							<f:facet name="header">
								<h:outputText value="按钮类型" />
							</f:facet>
						</p:column>
						<p:column >
							<f:facet name="header">
								<h:outputText value="按钮名称" />
							</f:facet>
						</p:column>
					</p:row>
				</p:columnGroup>
				<p:column style="text-align: center">
					<input type="checkbox" name="btnSelect" checked="#{btnitm.rid!=null ? 'checked':''}" value="#{btnitm.flowBtnType.typeNo}"/>
				</p:column>
				<p:column style="text-align: center">
					<h:outputText value="#{btnitm.flowBtnType.typeCN}"/>
				</p:column>
				<p:column >
					<p:inputText maxlength="10" size="15" value="#{btnitm.flowBtnName}"/>
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" action="#{tdFlowDefBean.buttonCfgSaveAction}"
										 process="@this,buttonTable" update="buttonTable" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ButtonDialog').hide();" process="@this"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>


		<!--节点编辑 -->
        <p:dialog id="nodeEditDialog" header="流程节点编辑" widgetVar="NodeEditDialog" resizable="false" width="600" height="300" modal="true">
            <p:panelGrid style="width:100%;" id="nodeEditGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height:30px;">
                        <h:outputText value="节点名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <h:outputText  value="#{tdFlowDefBean.tdFlowNode.nodeName}"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                    	<font color="red">*</font>
                        <h:outputText value="跳转方式："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
		                <p:selectOneRadio id="flowInType" value="#{tdFlowDefBean.tdFlowNode.flowInType}" 
		                		required="true" requiredMessage="请选择跳转方式">
		                	<f:selectItems value="#{tdFlowDefBean.flowInTypeMap}"/>
		                	<p:ajax event="change" process="@this,@parent"  listener="#{tdFlowDefBean.flowInTypeChgAction}"  update="nodeEditGrid2"/>
		                </p:selectOneRadio>                    
                    </p:column>
                </p:row>
                
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                    	<font color="red">*</font>
                        <h:outputText value="任务处理类型："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
		                <p:selectOneRadio id="dealType" value="#{tdFlowDefBean.tdFlowNode.dealType}" 
		                		required="true" requiredMessage="请选择任务处理类型">
		                	<f:selectItems value="#{tdFlowDefBean.dealTypeMap}"/>
		                </p:selectOneRadio>                    
                    </p:column>
                </p:row>
                
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                    	<font color="red">*</font>
                        <h:outputText value="意见是否必输："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
		                <p:selectOneRadio id="ifAdv" value="#{tdFlowDefBean.tdFlowNode.ifAdv}">
		                	<f:selectItem itemLabel="是"  itemValue="1"/>
		                	<f:selectItem itemLabel="否"  itemValue="0"/>
		                </p:selectOneRadio>                    
                    </p:column>
                </p:row>
                
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="驳回时首节点是否显示："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
		                <p:selectOneRadio id="fstNodeDisp" value="#{tdFlowDefBean.tdFlowNode.fstNodeDisp}">
		                	<f:selectItem itemLabel="是"  itemValue="1"/>
		                	<f:selectItem itemLabel="否"  itemValue="0"/>
		                </p:selectOneRadio>                    
                    </p:column>
                </p:row>
                
                <p:row rendered="#{tdFlowDefBean.tdFlowNode.tdFlowDef.ifDynaForm != 1}" >
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                    	<font color="red">*</font>
                        <h:outputText value="表单地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
						<h:panelGrid columns="4" style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText id="pageUrl" value="#{tdFlowDefBean.tdFlowNode.tdFlowNodePage == null ? null : tdFlowDefBean.tdFlowNode.tdFlowNodePage.pageDesc}"
										 style="width: 260px;cursor: pointer" onclick="document.getElementById('tabView:editForm:selPageLink').click();"
										 readonly="true"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="selPageLink"
										   action="#{tdFlowDefBean.selectPageAction}" process="@this" style="position: relative;left: -32px;">
								<p:ajax event="dialogReturn" listener="#{tdFlowDefBean.onPageSelect}"
										process="@this" resetValues="true" update="pageUrl"/>
							</p:commandLink>
							<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn" title="清空" update="pageUrl"
										   action="#{tdFlowDefBean.clearPage}" process="@this" style="position: relative;left: -28px;">
							</p:commandLink>
						</h:panelGrid>
                    </p:column>
                </p:row>
                
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height:30px;">
                        <h:outputText value="多节点时待办人规则："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
		                <p:selectOneRadio id="mulNodeRule" value="#{tdFlowDefBean.tdFlowNode.mulNodeRule}">
		                	<f:selectItem itemLabel="都必选"  itemValue="0"/>
		                	<f:selectItem itemLabel="至少选择1个"  itemValue="1"/>
		                </p:selectOneRadio> 	     
                    </p:column>
                </p:row>
                
                <p:row rendered="#{tdFlowDefBean.tdFlowNode.tdFlowDef.ifDynaForm == 1}"  >
	                <p:column style="text-align:right;padding-right:3px;width:30%;height:35px;">
	                    <font color="red">*</font>
	                    <h:outputText value="动态表单："/>
	                </p:column>
	                <p:column style="text-align:left;padding-left:3px;">
	                    	<h:panelGrid columns="4" style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
							<p:inputText id="formName" value="#{tdFlowDefBean.tdFlowNode.tdFormDef == null ? null : tdFlowDefBean.tdFlowNode.tdFormDef.formName}"
										size="50" style="cursor: pointer" onclick="document.getElementById('tabView:editForm:selPageLink2').click();"
										 readonly="true"/>
							<p:commandLink styleClass="ui-icon ui-icon-search" id="selPageLink2"
										   action="#{tdFlowDefBean.selectFormAction}" process="@this" style="position: relative;left: -32px;">
								<p:ajax event="dialogReturn" listener="#{tdFlowDefBean.onFormSelect}"
										process="@this" resetValues="true" update="formName"/>
							</p:commandLink>
							<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn2" title="清空" update="formName"
										   action="#{tdFlowDefBean.clearFormName}" process="@this" style="position: relative;left: -28px;">
							</p:commandLink>
						</h:panelGrid>
	                </p:column>
	            </p:row>	  
	            
                
            </p:panelGrid>
            <p:panelGrid style="width:100%;margin-top:-1px;" id="nodeEditGrid2">    
               	<!-- 拟办跳转 -->
                <p:row rendered="#{tdFlowDefBean.tdFlowNode.flowInType.typeNo == '2'}">
                    <p:column style="text-align:right;padding-right:3px;width:30%;height:30px;">
                        <h:outputText value="被拟办节点："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                    	<p:selectManyCheckbox value="#{tdFlowDefBean.bnbList}"  id="bnbList"  layout="pageDirection">
                    		<f:selectItems value="#{tdFlowDefBean.bnbNodesMap}"/>
                    	</p:selectManyCheckbox>      
                    </p:column>
                </p:row>
                
                <!-- 自动跳转 -->
                <p:row rendered="#{tdFlowDefBean.tdFlowNode.flowInType.typeNo == '0'}">
                    <p:column style="text-align:right;padding-right:3px;width:30%;height:30px;">
                        <h:outputText value="是否需要分发消息："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
		                <p:selectOneRadio id="ffMsg" value="#{tdFlowDefBean.tdFlowNode.ffMsg}">
		                	<f:selectItem itemLabel="是"  itemValue="1"/>
		                	<f:selectItem itemLabel="否"  itemValue="0"/>
		                </p:selectOneRadio> 	     
                    </p:column>
                </p:row>
                
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="nodeSaveBtn" action="#{tdFlowDefBean.nodeSaveAction}"
                                         process="@this,nodeEditGrid,nodeEditGrid2" update="nodeDataTable,nodeEditGrid,nodeEditGrid2" />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="nodeBackBtn" onclick="PF('NodeEditDialog').hide();" process="@this"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>        
        
         <!--设置待办人 -->
        <p:dialog id="assigneeEditDialog" header="设置待办人" widgetVar="AssigneeEditDialog" resizable="false" width="600" height="410" modal="true">
        	<p:outputPanel id="assigneePanel">
        		<table style="width:100%;">
        				<tr>
        					<td width="50%" style="height: 150px;padding-right: 3px;">
						        <p:selectOneListbox id="ruleCode" value="#{tdFlowDefBean.ruleCode}" style="width:100%; height:150px;">
						            <f:selectItems value="#{tdFlowDefBean.ruleMap}"/>
						            
						             <p:ajax event="change" update="dictoryList" process="@this,@parent" listener="#{tdFlowDefBean.onRuleChgAction}"/>
						        </p:selectOneListbox>
        					</td>
        					<td>
						      <p:selectManyMenu id="dictoryList" value="#{tdFlowDefBean.dictoryList}" 
						                          filter="true" filterMatchMode="contains" showCheckbox="true" style="width:100%; height:150px;">
						            <f:selectItems value="#{tdFlowDefBean.dictoryMap}" />
						       </p:selectManyMenu>        						
        					</td>
        				</tr>
         				<tr height="30">
        					<td>
        						已选角色：
        					</td>
        					<td style="text-align: right;padding-right: 3px;">
				                 <p:commandLink value="添加" action="#{tdFlowDefBean.nodeSqAdd}"  process="@this,dictoryList,selectDictoryList,ruleCode"  
				                 		update="dictoryList,selectDictoryList" >
				                </p:commandLink>       				
				                <p:spacer width="5" />		
				                 <p:commandLink value="删除" action="#{tdFlowDefBean.nodeSqDelete}"  process="@this,dictoryList,selectDictoryList,ruleCode"  
				                 		update="dictoryList,selectDictoryList" >
				                </p:commandLink>       						
        					</td>
						</tr>        
        				<tr>
        					<td style="height: 200px;padding-left: 3px;" colspan="2">
							      <p:selectManyMenu id="selectDictoryList" value="#{tdFlowDefBean.selectDictoryList}"  
							                          filter="true" filterMatchMode="contains" showCheckbox="true" style="width:100%;height:200px;">
							            <f:selectItems value="#{tdFlowDefBean.selectDictoryMap}"  />
							       </p:selectManyMenu>       
        					</td>
        				</tr>											       				
        		</table>
        	</p:outputPanel>
             <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="nodeSqSaveBtn" action="#{tdFlowDefBean.nodeSqAction}"
                                         process="@this,assigneePanel" update="nodeDataTable,assigneePanel" />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="nodeSqBackBtn" onclick="PF('AssigneeEditDialog').hide();" process="@this"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>       	
        </p:dialog>
        
        <!--模板意见 -->
        <p:dialog id="templateDialog" header="设置意见模板" widgetVar="TemplateDialog" resizable="false" width="710" height="370" modal="true">
        	<p:outputPanel id="buttonPanel" style="width:100%;text-align:right;padding-right:3px;">
        		<p:commandButton id="addTemplateBtn" value="添加模板" action="#{tdFlowDefBean.addTemplateInit}" 
        			resetValues="true" process="@this" oncomplete="TemplateMgrDialog.show();" update="templateMgrDialog"/>
        	</p:outputPanel>
        	<p:dataTable id="templateTable" value="#{tdFlowDefBean.templateList}" var="itm" style="padding-top:5px;width:680px;" 
        		emptyMessage="没有数据." scrollable="true" scrollHeight="300">
        		<p:column headerText="选择" style="width:30px;text-align:center">
        			<input type="checkbox" name="templateSelect" checked="#{itm.isDefault==1 ? 'checked':''}" onclick="settingCheckboxSingle(this)" value="#{itm.rid}"/>
        		</p:column>
        		<p:column headerText="模板名称" style="width:80px;text-align:center;">
        			<p:commandLink value="#{itm.templateName}" action="#{tdFlowDefBean.modTemplateInit}"
        				process="@this,:tabView:editForm:templateTable" resetValues="true" oncomplete="TemplateMgrDialog.show();" 
        				update=":tabView:editForm:templateMgrDialog">
        				<f:setPropertyActionListener target="#{tdFlowDefBean.tbFlowAdvtemplate}" value="#{itm}"/>
        			</p:commandLink>
        		</p:column>
        		<p:column headerText="模板内容">
        			<h:outputText value="#{itm.templateContent}"/>
        		</p:column>
        	</p:dataTable>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="chooseTemplateBtn" action="#{tdFlowDefBean.chooseTemplate}" 
                        	process="@this,templateDialog" oncomplete="TemplateDialog.hide();"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="cancelChooseTemplateBtn"
                        	 onclick="TemplateDialog.hide();" type="button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>          	
        </p:dialog>
        
        <!--模板意见 -->
        <p:dialog id="templateMgrDialog" header="模板管理" widgetVar="TemplateMgrDialog" resizable="false" width="950" height="425" modal="true">
        	<table style="width: 100%;">
        		<tr>
        			<td style="vertical-align: top;">
        				<p:panel style="height:411px;" header="模板信息">
	        				<h:panelGrid columns="2">
	        					<span style="margin-top:5px;"><font style='color:red'>*</font>模板名称：</span>
	        					<p:inputText id="templateName" value="#{tdFlowDefBean.tbFlowAdvtemplate.templateName}" maxlength="25" size="20" 
	        						required="true" requiredMessage="模板名称不允许为空！" style="margin-top:5px;"/>
	        				
		        				<h:outputText value="模板内容：" style="margin-top:5px;"/>
		        				<p:inputTextarea id="templateContent" value="#{tdFlowDefBean.tbFlowAdvtemplate.templateContent}" rows="6" cols="50"
		        					counter="display" maxlength="1000" counterTemplate="还可以输入{0}个字!" style="margin-top:5px;"/>
		        				
		        				<p:spacer/>	
	        				 	<h:outputText id="display" style="margin-top:5px;"/>
	        				 		
		        				<p:spacer/>	
								<p:commandButton id="resolveBtn" value="测试" action="#{tdFlowDefBean.resolveTemplate}" 
	        						process="@this,templateContent,resolvedContent" update="resolvedContent" style="margin-top:5px;"/>
	        				 		
	        				 	<h:outputText value="测试内容：" style="margin-top:5px;"/>	
		        				<p:inputTextarea id="resolvedContent" value="#{tdFlowDefBean.resolvedContent}" rows="8" cols="50" readonly="true" style="margin-top:5px;"/>
	        				</h:panelGrid>
        				</p:panel>
        			</td>
        			<td style="vertical-align: top;padding: 1px;">
        				<p:panel style="height:300px;width:350px;padding-right:5px;" header="模板元素">
	        				<p:dataTable id="defineList" value="#{tdFlowDefBean.defineList}" var="define" filterDelay="1000" widgetVar="defineList"
	        					scrollable="true" scrollHeight="220" style="width:340px;" filteredValue="#{tdFlowDefBean.filterDefineList}" emptyMessage="没有数据.">
	        					<p:column headerText="" filterOptions="#{tdFlowDefBean.filterDefineSelectList}" filterMatchMode="exact" 
	        						filterBy="#{define.systemType.typeCN}" style="text-align:center;width:120px;">
	        						<h:outputText value="#{define.systemType.typeCN}" converter="system.EmTempateNameConvert" title="#{define.systemType.typeCN}"/>
	        					</p:column>
	        					
	        					<p:column filterMatchMode="contains" style="text-align:center;" filterBy="metaName" headerText="">
	        						<a onmouseup="checkClick('#{define.rid}')" title="单击显示内容，双击添加！" 
	        							style="cursor: pointer" href="javascript:;">#{define.metaName}</a>
	        					</p:column>
	        				</p:dataTable>
        				</p:panel>
					    <p:panel id="metaDemo" header="示例" style="margin-top:5px;height:100px;width:350px;">
					   		<h:outputText id="metaDemoText" value="#{tdFlowDefBean.tbTempmetaDefine.demoDesc}"/>
					    </p:panel>         				
        			</td>
        		</tr>
        	</table>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="saveTemplateBtn" action="#{tdFlowDefBean.saveTemplate}" 
                        	process="@this,templateMgrDialog"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="cancelSaveTemplateBtn"
                        	 onclick="TemplateMgrDialog.hide();" type="button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>        	
        </p:dialog>
        
		<p:remoteCommand name="refreshMetaDemo" process="@this" action="#{tdFlowDefBean.refreshMetaDemo}" update="metaDemo"/>
		<p:remoteCommand name="addMeta" process="@this,templateContent" action="#{tdFlowDefBean.addMeta}" update="templateContent"/>
    </ui:define>
</ui:composition>

