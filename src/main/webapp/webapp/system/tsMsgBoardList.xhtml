<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tsMsgBoardBean}"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

        <style type="text/css">
            .ui-picklist .ui-picklist-list{
                text-align:left;
                height: 350px;
                width: 340px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="留言板查看"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tsMsgBoardBean.searchAction}" update="dataTable"
					process="@this,searchZone,searchUnitListOneMenu,beginDate,endDate,searchUserName" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:100px;" >
                <h:outputText value="地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 200px;">
                <zwx:ZoneSingleNewComp zoneList="#{tsMsgBoardBean.zoneList}"  zoneCodeNew="#{tsMsgBoardBean.searchZoneCode}" 
                						zoneName="#{tsMsgBoardBean.searchZoneName}"
                	id="searchZone" onchange="onSearchNodeSelect()"  zoneType="#{tsMsgBoardBean.searchZoneType}"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{tsMsgBoardBean.onSearchNodeSelect}" process="@this,searchZone"
                                 update=":tabView:mainForm:searchUnitListOneMenu"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:100px;">
                <h:outputText value="单位：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 230px;">
                <p:selectOneMenu id="searchUnitListOneMenu" value="#{tsMsgBoardBean.searchUnitId}" style="width: 210px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{tsMsgBoardBean.searchUnitMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:100px;">
                <h:outputText value="留言人：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;">
                <p:inputText  style="width:180px" value="#{tsMsgBoardBean.searchUserName}" id="searchUserName"/>
            </p:column> 
        </p:row>
        <p:row>        
            <p:column style="text-align:right;padding-right:3px;width:100px;height: 35px;">
                <h:outputText value="留言时间段：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
               <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
							showOtherMonths="true" id="beginDate" size="11" navigator="true"
							yearRange="c-10:c+10" converterMessage="留言开始日期，格式输入不正确！"
							showButtonPanel="true" maxdate="new Date()"
							value="#{tsMsgBoardBean.searchBeginDate}" />
				~
				<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
							showOtherMonths="true" id="endDate" size="11" navigator="true"
							yearRange="c-10:c+10" converterMessage="留言结束日期，格式输入不正确！"
							showButtonPanel="true" maxdate="new Date()"
							value="#{tsMsgBoardBean.searchEndDate}" />
            </p:column>                      
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width: 80px;text-align: center; " remander="">
            <h:outputText value="#{itm[0]}" />
        </p:column>
        <p:column headerText="单位" style="padding-left: 3px;width: 310px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="联系人" style="width: 80px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="联系电话" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="留言时间" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="留言人" style="width: 80px;">
            <h:outputText value="#{itm[6]}" />
        </p:column>
        <p:column headerText="留言内容">
            <h:outputText value="#{itm[4]}" />
        </p:column>
    </ui:define>
</ui:composition>











