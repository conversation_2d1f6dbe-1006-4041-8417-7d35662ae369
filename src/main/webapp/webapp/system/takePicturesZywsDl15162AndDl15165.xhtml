<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--suppress SpellCheckingInspection -->
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
    <h:head>
        <title>
            <span id="dialogSelfTitle">文件扫描</span>
            <p:spacer width="10"/>
            <span id="pageNum">共0张</span>
        </title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <h:outputStylesheet name="css/gpy/DL15162AndDL15165.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <script type='text/javascript' src='/resources/js/gpy/DL15162AndDL15165.js'>
        </script>
    </h:head>
    <h:body style="overflow-y:hidden;">
        <h:form id="mainForm">
            <p:layout id="contentLay" style="height:430px">
                <p:layoutUnit position="west" size="410" id="leftLay">
                    <div class="video_panel">
                        <img id="zwxVedioInput" class="zwxVedioInput" alt=""/>
                    </div>
                </p:layoutUnit>
                <p:layoutUnit position="center" size="200" id="centerLay">
                    <p:outputPanel id="board" styleClass="center-container" style="margin-left: 40px;">
                        <p:dashboard model="#{takePicturesZywsBean.model}">
                            <p:ajax event="reorder" listener="#{takePicturesZywsBean.handleReorder}"
                                    oncomplete="initVedioImgsEvents()"/>
                            <c:forEach items="#{takePicturesZywsBean.imgList}" var="imgBean">
                                <p:panel id="p#{imgBean.xh}" header="第#{imgBean.xh}张" closable="true"
                                         style="width: 150px;">
                                    <p:ajax event="close" onstart="deletePicture#{imgBean.xh}()"/>
                                    <p:remoteCommand resetValues="true" name="deletePicture#{imgBean.xh}"
                                                     action="#{takePicturesZywsBean.deletePicture}"
                                                     oncomplete="showPanel()">
                                        <f:setPropertyActionListener value="#{imgBean}"
                                                                     target="#{takePicturesZywsBean.imgBeanTemp}"/>
                                    </p:remoteCommand>
                                    <p:outputPanel styleClass="vedio-photo">
                                        <p:graphicImage style="width: 100%;height: 100%;cursor: pointer;"
                                                        value="/webFile#{imgBean.path}"/>
                                    </p:outputPanel>
                                </p:panel>
                            </c:forEach>
                        </p:dashboard>
                        <div class="clear">
                        </div>
                    </p:outputPanel>
                </p:layoutUnit>
            </p:layout>
            <p:outputPanel rendered="#{takePicturesZywsBean.type=='2'}"
                           style="margin:5px 20px;padding-bottom: 5px;border-bottom: 1px solid #A6C9E2;">
                <h:outputText value="*" style="color: #ff0000"/>附件名称：<p:inputText
                    value="#{takePicturesZywsBean.showFileName}" id="showFileName" style="width: 350px;"
                    maxlength="50"/>
            </p:outputPanel>
            <p:outputPanel styleClass="line-cutoff" rendered="#{takePicturesZywsBean.type=='1'}">&#160;</p:outputPanel>
            <p:outputPanel style="text-align:center" id="btnPanel">
                <p:spacer width="5"/>
                <p:commandButton value="拍照" icon="ui-icon-video" id="saveBtn"
                                 type="button" onclick="vedioInputTakePhoto()"/>
                <p:spacer width="5"/>
                <p:commandButton value="插入" icon="ui-icon-seek-end" id="insertBtn"
                                 type="button" onclick="vedioInputInsert()"/>
                <p:spacer width="5"/>
                <p:commandButton value="预览" icon="ui-icon-image" id="viewBtn"
                                 type="button" onclick="viewImgDetail()"/>
                <p:spacer width="5"/>
                <p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
                                 id="submitBtn" type="button" onclick="vedioInputSubmit()"/>

                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
                                 action="#{takePicturesZywsBean.dialogClose}" widgetVar="BackBtn"
                                 process="@this" style="display:none"/>
                <p:remoteCommand name="savePictureAction" process="@this,binaryStr"
                                 action="#{takePicturesZywsBean.savePicture}" update="imgsJson"
                                 oncomplete="initVedioImgsEvents();showPanel();"/>
                <p:remoteCommand name="deletePictureAction" process="@this"
                                 action="#{takePicturesZywsBean.deletePicture}" update="imgsJson"
                                 oncomplete="initVedioImgsEvents();showPanel();"/>
                <p:remoteCommand name="submitPictureAction" process="@this,showFileName"
                                 action="#{takePicturesZywsBean.confirmAction}"/>

                <h:inputHidden id="binaryStr" value="#{takePicturesZywsBean.binaryStr}"/>
                <h:inputHidden id="imgsJson" value="#{takePicturesZywsBean.imgsJson}"/>
            </p:outputPanel>
            <a href="/resources/files/chiscdcServerDL15162AndDL15165.exe" id="crxId"
               style="display: none">crxId</a>
            <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
            <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
        </h:form>
        <script type="text/javascript">
            //<![CDATA[
            function viewImgDetail() {
                var imgsJson = document.getElementById("mainForm:imgsJson").value;
                var grobalUrl = "#{request.scheme}" + "://"
                    + "#{request.serverName}" + ":"
                    + "#{request.serverPort}" + "#{request.contextPath}";
                var url = grobalUrl + "/webapp/system/viewImgShow.faces?param="
                    + encodeURI(imgsJson);
                var htmlStr = '<body scroll="no" style="margin: 0;padding: 0;border:0;overflow:hidden;">' +
                    '<iframe style="margin: 0;padding: 0;border: 0;width:100%;height:100%;" src="' + url +
                    '"></iframe>' +
                    '</body>';
                var newWin = window.open('','newwindow','toolbar =no, menubar=no, scrollbars=no, resizeable=no, location=no, status=no');
                newWin.document.write(htmlStr);
            }

            //]]>
        </script>
        <p:dialog id="installDialog" widgetVar="InstallDialog" header="提示"
                  width="510" height="210" resizable="false" modal="true"
                  styleClass="frpt_diag">
            <div class="frpt_panel">
                <div class="frpt_img" style="">
                    <img alt="" src="/resources/images/read-idc-icon.png"/>
                </div>
                <div class="frpt_tip">
                    <div class="frpt_tip_head">
                        <span>服务访问失败，请依次检查以下步骤：</span>
                    </div>
                    <div class="frpt_tip_body">
                        <span>
                            1、检查是否已安装高拍仪相关服务，<a onclick="document.getElementById('crxId').click();">点击下载服务</a>；
                        </span>
                    </div>
                    <div class="frpt_tip_body">
                        <span>
                            2、检查是否已打开并启动服务；
                        </span>
                    </div>
                    <div class="frpt_tip_body">
                        <span>
                            3、检查服务设置中监听端口是否设置为9000、9443；
                        </span>
                    </div>
                    <div class="frpt_tip_body">
                        <span>
                            4、检查无误后<a onclick="PF('InstallDialog').hide();vedioInputInit();">点击此处刷新</a>。
                        </span>
                    </div>
                </div>
            </div>
        </p:dialog>
    </h:body>
</f:view>
</html>
