<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
   			 <h:outputStylesheet name="css/default.css"/>
   			 <h:outputStylesheet name="css/ui-tabs.css" />
            <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
            
        <style type="text/css">
            .ui-selectmanycheckbox.ui-widget td, .ui-selectoneradio.ui-widget td{
                padding-left:0px; padding-right: 0px !important;
            }
            table.ui-selectmanycheckbox td label{
                padding-left: 5px;
                padding-right:20px;
                white-space:nowrap;
                overflow: hidden;
            }

        </style>
        <h:outputStylesheet library="css" name="ui-tabs.css"/>
    </h:head>

    <h:body>
        <h:form id="mainForm">
            

            <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:left;padding-left:5px;height: 22px;">
                            <h:outputText value="角色管理"/>
                        </p:column>
                    </p:row>
                </f:facet>
            </p:panelGrid>
            <p:outputPanel styleClass="zwx_toobar_h_auto">
                <span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" process="@this,searchMenuName,searchCode,searchOption,dataTable" action="#{roleMenuEditBean.searchAllAction}" update="dataTable"/>
                <p:spacer width="5" />
                <p:commandButton value="保存" icon="ui-icon-disk"  action="#{roleMenuEditBean.saveAction}"  process="@this,dataTable" update="dataTable" />
                <p:spacer width="5" />
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" process="@this" action="#{roleMenuEditBean.backAction}" >
                </p:commandButton>
            </p:outputPanel>
            <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" >
                <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;width:120px;">
                            <h:outputLabel for="searchMenuName" value="菜单名称："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;width:180px;">
                            <p:inputText id="searchMenuName" value="#{roleMenuEditBean.searchMenuName}" maxlength="10"/>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;width:130px;">
                            <h:outputLabel value="结构层次编码："/>
                        </p:column>
                        <p:column style="text-align:left;width:180px;padding-left: 3px">
                            <p:inputText id="searchCode" value="#{roleMenuEditBean.searchCode}" maxlength="10"/>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;width:100px;">
                            <h:outputLabel value="选项："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left: 3px">
                            <p:selectOneRadio id="searchOption" value="#{roleMenuEditBean.searchOption}" style="width: 210px;">
                                <f:selectItem itemLabel="全部" itemValue="0"/>
                                <f:selectItem itemLabel="已分配权限" itemValue="1"/>
                                <p:ajax event="change" process="@this,:mainForm:mainGrid"
                                        listener="#{roleMenuEditBean.searchAction()}"
                                        update=":mainForm:dataTable" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </p:fieldset>
            <p:dataTable var="itm" value="#{roleMenuEditBean.displayMenuList}" paginator="true" rows="20" paginatorPosition="bottom"
                    id="dataTable" emptyMessage="没有您要找的记录！" >
                <p:columnGroup  type="header">
                    <p:row>
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="菜单名称"/>
                            </f:facet>
                        </p:column>
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="菜单编码"/>
                            </f:facet>
                        </p:column>
                        <p:column>
                            <f:facet name="header">
                                <h:outputText value="菜单授权"/>
                            </f:facet>
                        </p:column>
                    </p:row>
                </p:columnGroup>
                <p:column style="width:20%;padding-left:#{itm.levelNum*15}px;">
                    <h:outputText value="#{itm.menuCn}"/>
                </p:column>
                <p:column style="width:10%;">
                    <h:outputText value="#{itm.menuLevelNo}"/>
                </p:column>
                <p:column>
                    <table style="border-color: transparent;margin: 0px;padding: 0px;">
                        <tr>
                            <td style="width: 10px">
                                <p:selectBooleanCheckbox value="#{itm.selected}" >
                                    <p:ajax process="@this,dataTable"  update="dataTable" listener="#{roleMenuEditBean.menuSelectAction(itm.rid,itm.selected)}" />
                                </p:selectBooleanCheckbox>
                            </td>
                            <td style="width: 30px;padding-left: 0px;padding-left: 0px;">查看</td>
                            <td>
                                <p:selectManyCheckbox value="#{itm.btns}" style="width: #{itm.allbtns.size()*110}px;"
                                        disabled="#{!itm.selected}">
                                    <f:selectItems value="#{itm.allbtns}"/>
                                </p:selectManyCheckbox>
                            </td>
                        </tr>
                    </table>

                </p:column>

            </p:dataTable>

        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    </h:body>
</f:view>
</html>
