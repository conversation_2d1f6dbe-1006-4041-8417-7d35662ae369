<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<title><span id="dialogSelfTitle">文件扫描</span> <p:spacer
				width="10" /> <span id="pageNum">共0张</span></title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:form id="mainForm">
			<p:layout id="contentLay" style="height:378px">
				<p:layoutUnit position="west" size="410" id="leftLay" style="text-align:center;">
					<object id="zwxVedioInput" type="application/x-hanvoncap" style="z-index:99999" 
						class="zwxVedioInput"> </object>
				</p:layoutUnit>
				<p:layoutUnit position="center" size="200" id="centerLay">
					<p:outputPanel id="board" styleClass="center-container"
						style="margin-left: 40px;">
						<p:dashboard model="#{takePicturesZywsBean.model}">
							<p:ajax event="reorder"
								listener="#{takePicturesZywsBean.handleReorder}"
								oncomplete="initVedioImgsEvents()" />
							<c:forEach items="#{takePicturesZywsBean.imgList}" var="imgBean">
								<p:panel id="p#{imgBean.xh}" header="第#{imgBean.xh}张"
									closable="true" style="width: 150px;">
									<p:ajax event="close" onstart="deletePicture#{imgBean.xh}()" />
									<p:remoteCommand resetValues="true"
										name="deletePicture#{imgBean.xh}"
										action="#{takePicturesZywsBean.deletePicture}"
										oncomplete="showPanel()">
										<f:setPropertyActionListener value="#{imgBean}"
											target="#{takePicturesZywsBean.imgBeanTemp}" />
									</p:remoteCommand>
									<p:outputPanel styleClass="vedio-photo">
										<p:graphicImage
											style="width: 100%;height: 100%;cursor: pointer;"
											value="/webFile#{imgBean.path}" />
									</p:outputPanel>
								</p:panel>
							</c:forEach>
						</p:dashboard>
						<div class="clear"></div>
					</p:outputPanel>
				</p:layoutUnit>
			</p:layout>
			<p:outputPanel rendered="#{takePicturesZywsBean.type=='2'}"
				style="margin:5px 20px;padding-bottom: 5px;border-bottom: 1px solid #A6C9E2;">
				<h:outputText value="*" style="color: #ff0000" />附件名称：<p:inputText
					value="#{takePicturesZywsBean.showFileName}" id="showFileName"
					style="width: 350px;" maxlength="50" />
			</p:outputPanel>
			<p:outputPanel styleClass="line-cutoff"
				rendered="#{takePicturesZywsBean.type=='1'}">&#160;</p:outputPanel>
			<p:outputPanel style="text-align:center" id="btnPanel">
				<p:spacer width="5" />
				<p:commandButton value="拍照" icon="ui-icon-video" id="saveBtn"
					type="button" onclick="vedioInputTakePhoto()" />
				<p:spacer width="5" />
				<p:commandButton value="插入" icon="ui-icon-seek-end" id="insertBtn"
					type="button" onclick="vedioInputInsert()" />
				<p:spacer width="5" />
				<p:commandButton value="预览" icon="ui-icon-image" id="viewBtn"
					type="button" onclick="viewImgDetail()" />
				<p:spacer width="5" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
					id="submitBtn" type="button" onclick="vedioInputSubmit()" />

				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
					action="#{takePicturesZywsBean.dialogClose}" widgetVar="BackBtn"
					process="@this" style="display:none" />
				<p:remoteCommand name="savePictureAction" process="@this,binaryStr"
					action="#{takePicturesZywsBean.savePicture}" update="imgsJson"
					oncomplete="initVedioImgsEvents();showPanel();" />
				<p:remoteCommand name="deletePictureAction" process="@this"
					action="#{takePicturesZywsBean.deletePicture}" update="imgsJson"
					oncomplete="initVedioImgsEvents();showPanel();" />
				<p:remoteCommand name="submitPictureAction"
					process="@this,showFileName"
					action="#{takePicturesZywsBean.confirmAction}" />

				<h:inputHidden id="binaryStr"
					value="#{takePicturesZywsBean.binaryStr}" />
				<h:inputHidden id="imgsJson"
					value="#{takePicturesZywsBean.imgsJson}" />
			</p:outputPanel>

			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		</h:form>
		<script type="text/javascript">
			//<![CDATA[
			var idx = 0;
			function plugin() {
				return document.getElementById('zwxVedioInput');
			}
			function init() {
				try {
					var count = plugin().GetCameraCount();
					if (count <= 0) {
						alert("获取设备列表失败！");
						PF("BackBtn").getJQ().click();
						return;
					}
					idx = plugin().GetMainCameraIndex();
					plugin().SetVideoRotate(idx, 1);//旋转90度
					plugin().SetCropType(idx, 2);//自动裁切
					plugin().OpenCamera(idx);
				//	setTimeout(initDevRes, 10);
				} catch (e) {
					PF('InstallDialog').show();
				}

			}
			function initDevRes() {
				plugin().SetDevRes(idx, 4);
			}
			/**
			 * 拍照
			 */
			function vedioInputTakePhoto() {
				try {
					var tval = plugin().CaptureBase64(idx);
					document.getElementById("mainForm:binaryStr").value = tval;
					savePictureAction();
				} catch (e) {
					PF('InstallDialog').show();
				}
			}

			function vedioInputInsert() {
				var selectLen = jQuery(".vedio-photo-choosen").length;
				if (selectLen == 0) {
					alert("请先选择要插入的页码！");
					return;
				} else {
					var find = jQuery("#mainForm\\:board").find(
							"div.vedio-photo-choosen").find("img").eq(0);
					var index = jQuery("#mainForm\\:board").find("img").index(
							find);
					var tval = plugin().CaptureBase64(idx);
					document.getElementById("mainForm:binaryStr").value = tval;
					savePictureAction([ {
						name : 'inx',
						value : index + 1
					} ]);
				}
			}

			function vedioInputSubmit() {
				var imgsJson = document.getElementById("mainForm:imgsJson").value;
				if (imgsJson == "" || imgsJson == "[]") {
					alert("请先拍照！");
					return;
				}
				submitPictureAction();
			}

			/**
			 * 释放
			 */
			function vedioInputClear() {
				if (plugin().valid) {
					plugin().CloseCamera(idx);
				}
			}

			window.onload = function() {
				//给弹出框绑定关闭时间
			//	setTimeout(diaAddCloseListener, 1000);
				init();
			}

			function diaAddCloseListener() {
				jQuery(window.parent.document).find("#dialogSelfTitle")
						.parent().next().children("span").bind("click",
								function() {
									vedioInputClear();
								});
			}

			/**
			 * 图片查看详情
			 */
			function viewImgDetail() {
				var imgsJson = document.getElementById("mainForm:imgsJson").value;
				var grobalUrl = "#{request.scheme}" + "://"
						+ "#{request.serverName}" + ":"
						+ "#{request.serverPort}" + "#{request.contextPath}";
				var url = grobalUrl + "/webapp/system/viewImgShowE1100.faces?param="
						+ encodeURI(imgsJson);
				var htmlStr = '<body scroll="no" style="margin: 0;padding: 0;border:0;overflow:hidden;">' +
						'<iframe style="margin: 0;padding: 0;border: 0;width:100%;height:100%;" src="' + url +
						'"></iframe>' +
						'</body>';
				var newWin = window.open('','newwindow','toolbar =no, menubar=no, scrollbars=no, resizeable=no, location=no, status=no');
				newWin.document.write(htmlStr);
			}

			function initVedioImgsEvents() {
				jQuery(".img-close-icon").hide();
				jQuery(".vedio-photo").bind("click", function() {
					jQuery(".img-close-icon").hide();
					jQuery(".vedio-photo-choosen").each(function() {
						jQuery(this).removeClass("vedio-photo-choosen");
					});
					jQuery(this).addClass("vedio-photo-choosen");
					jQuery(this).parent().find(".img-close-icon").show();
				});

				jQuery(".img-close-icon").bind("click", function() {
					var inx = jQuery(this).prev(".page").html();
					deletePictureAction([ {
						name : 'inx',
						value : inx
					} ]);
				});
			}

			/**
			 * 显示隐藏的panel
			 */
			function showPanel() {
				jQuery("#mainForm\\:board").find("div.ui-panel").each(
						function() {
							if ($(this).hasClass("ui-helper-hidden")) {
								$(this).removeClass("ui-helper-hidden");
							}
						});
				var length = jQuery("#mainForm\\:board").find("img").length;
				$("#pageNum", window.parent.document).html("共" + length + "张");
				initVedioImgsEvents()
			}
			//]]>
		</script>
		<style type="text/css">
.ui-dashboard .ui-dashboard-column {
	padding-bottom: 10px !important;
}

.clear {
	clear: both;
}

#mainForm\:contentLay .ui-corner-all {
	border-radius: 0px !important;
}

#mainForm\:leftLay,#mainForm\:centerLay,#mainForm\:rightLay .ui-widget-content
	{
	border: 1px solid #666666;
}

#mainForm\:leftLay {
	left: 20px !important;
	width: 410px !important;
}

#mainForm\:centerLay {
	left: 472px !important;
	width: 250px !important;
	text-align: center;
}

#mainForm\:centerLay .ui-helper-hidden {
	display: block;
}

#mainForm\:rightLay {
	border: 1px solid #666666 !important;
	width: 280px !important;
}

#mainForm\:rightLay .ui-layout-unit-content {
	border: none !important;
}

.vedio-photo-choosen {
	border: 3px solid red;
}

.zwxVedioInput {
	width: 270px;
	height: 360px;
}

.line-cutoff {
	height: 20px;
	border-bottom: 1px solid #A6C9E2;
	clear: both;
	margin-bottom: 15px;
}

#mainForm\:leftLay .ui-layout-unit-content {
	overflow-y: hidden !important;
}

#mainForm\:centerLay .ui-layout-unit-content {
	overflow-x: hidden !important;
}

.ui-layout-unit .ui-layout-unit-content {
	padding: 0.2em 0em;
	border: 0px none;
	overflow: auto;
}

#mainForm\:dataList_content {
	border: none !important;
}

.ui-datalist-item {
	height: 30px;
	border-bottom: dotted 1px #b2b2b2;
	margin-bottom: 10px;
}

.frpt_diag .ui-dialog-content {
	background-color: #FFFFFF;
}

.frpt_diag .ui-dialog-content {
	padding: 0em 0em;
}

.frpt_img {
	width: 150px;
	text-align: center;
	vertical-align: top;
	padding-top: 10px;
}

.frpt2_img {
	width: 150px;
	text-align: center;
	vertical-align: top;
	padding-top: 27px;
}

.frpt_tip_head {
	height: 18px;
	font-size: 12px;
}

.frpt_tip_body {
	height: 27px;
	font-size: 13px;
}

.frpt_bottom {
	text-align: right;
	padding-right: 24px;
	padding-bottom: 20px;
	vertical-align: bottom;
}
</style>
		<a href="/resources/files/HWDriver.zip" id="eHwCameraPlugin"
			style="display: none">E高拍仪插件</a>
		<p:dialog id="installDialog" widgetVar="InstallDialog" header="插件安装  （汉王E1100 Air）"
			width="480" height="209" resizable="false" modal="true"
			styleClass="frpt_diag">
			<table style="width: 100%;height: 100%;">
				<tr>
					<td style="height: 24px;"></td>
				</tr>
				<tr>
					<td style="text-align: left;vertical-align: top;">
						<table style="width: 100%;height: 100%;">
							<tr>
								<td rowspan="5" class="frpt_img"><img
									style="margin-top: 20px;" alt="身份证读卡器图标"
									src="/resources/images/read-idc-icon.png" /></td>
								<td class="frpt_tip_head"><span>操作信息：</span></td>
							</tr>
							<tr>
								<td class="frpt_tip_body"><span>1、点击下载按钮下载高拍仪相关控件；</span></td>
							</tr>
							<tr>
								<td class="frpt_tip_body"><span>2、根据下载的压缩包内的文件进行安装；</span>
								</td>
							</tr>
							<tr>
								<td class="frpt_tip_body"><span>3、安装完成之后刷新界面。</span></td>
							</tr>
							<tr>
								<td class="frpt_bottom"><p:commandButton process="@this"
										value="下载" style="width:80px"
										onclick="document.getElementById('eHwCameraPlugin').click();PF('InstallDialog').hide()" />
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</p:dialog>
	</h:body>
</f:view>
</html>
