<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	xmlns:p="http://primefaces.org/ui">

	<!--引入中文日期-->
	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
	<h:outputScript library="js" name="namespace.js" />
	<h:outputScript name="js/validate/system/validate.js" />
	<style>
[title=jumpList] {
	border: 0px none;
	margin: 1px 0;
	padding: 3px 5px;
	text-align: left;
	white-space: nowrap;
	width: 340px;
}
</style>
	<script type="text/javascript">
		//<![CDATA[
		//]]>
	</script>
	<h:form id="subEditForm">
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
			id="editTitleGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="题目详情" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					process="@this,editGrid,optLayoutGrid" update=":tabView"
					action="#{genQueBean.subSaveAction()}" />

				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					update=":tabView" process="@this"
					action="#{genQueBean.backEditAction()}" />
			</h:panelGrid>
		</p:outputPanel>
		<p:panelGrid style="width:100%;height:100%;margin-top:5px;"
			id="editGrid">
			<p:row>
				<p:column
					style="text-align:right;padding-right:3px;width:150px;height:30px">
					<font color="red">*</font>
					<h:outputText value="题目标题：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<h:outputText value="#{genQueBean.sub.titleDesc}"
						rendered="#{genQueBean.sub.questTypeValue!=5 }" />
					<p:inputTextarea value="#{genQueBean.sub.titleDesc}"
						maxlength="100" cols="85" rows="3" autoResize="false"
						required="true" requiredMessage="请输入题目标题"
						rendered="#{genQueBean.sub.questTypeValue==5 }" />
				</p:column>
			</p:row>
			<p:row>
				<p:column
					style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<h:outputText value="显示题号：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px;">
					<p:inputText id="showCode" value="#{genQueBean.sub.showCode}"
						maxlength="10" size="10" />
				</p:column>
				<p:column
						style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<h:outputText value="对照编码：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText value="#{genQueBean.sub.rightCode}"
								 maxlength="20" size="10" />
				</p:column>
			</p:row>
			<p:row rendered="#{genQueBean.sub.questTypeValue!=5 }">
				<p:column
					style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<font color="red">*</font>
					<h:outputText value="是否必答：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px;">
					<p:selectOneRadio value="#{genQueBean.sub.mustAsk}" id="muskAsk">
						<f:selectItem itemLabel="是" itemValue="1" />
						<f:selectItem itemLabel="否" itemValue="0" />
					</p:selectOneRadio>
				</p:column>
				<p:column
					style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<h:outputText value="分值：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText id="optionScore" value="#{genQueBean.sub.optionScore}"
						maxlength="5" size="10" converterMessage="请输入整数！" />
				</p:column>
			</p:row>
			<p:row>
				<p:column
					style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<h:outputText value="类型：" />
				</p:column>
				<p:column style="text-align:left;"
					colspan="#{(genQueBean.sub.questTypeValue==4 or genQueBean.sub.questTypeValue==7 or genQueBean.sub.questTypeValue==9)?'1':'3'}">
					<h:panelGrid columns="10" id="questTypeGrid"
						style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
						<h:outputText value="#{genQueBean.sub.questType.typeCN}" />
						<p:spacer width="10" />
						<h:outputText value="多选必答至少选几个：" rendered="false" />
						<p:inputText value="#{genQueBean.sub.minSelectNum}" size="5"
							rendered="false" maxlength="5" converterMessage="请输入数字!" />
					</h:panelGrid>
				</p:column>
				<p:column
					rendered="#{genQueBean.sub.questTypeValue==4 or genQueBean.sub.questTypeValue==7 or genQueBean.sub.questTypeValue==9}"
					style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<h:outputText value="是否多项填空：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;"
					rendered="#{genQueBean.sub.questTypeValue==4 or genQueBean.sub.questTypeValue==7 or genQueBean.sub.questTypeValue==9}">
					<h:outputText value="#{genQueBean.sub.isMulti==1?'是':'否'}" />
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:panelGrid id="optLayoutGrid"
			style="width:100%;height:100%;top:-1px;">
			<p:row rendered="false">
				<p:column
					style="text-align:right;padding-right:3px;height:30px;width:180px;">
					<h:outputText value="选项布局：" />
				</p:column>
				<p:column style="text-align:left;" colspan="3">
					<h:panelGrid columns="10" id="layoutGrid"
						style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
						<p:selectOneMenu value="#{genQueBean.sub.optLayout}">
							<f:selectItem itemValue="" itemLabel="请选择..." />
							<f:selectItem itemValue="0" itemLabel="竖向" />
							<f:selectItem itemValue="1" itemLabel="横向" />
							<f:selectItem itemValue="2" itemLabel="随题目显示（多用于填空）" />
							<p:ajax event="change" process="@this" resetValues="true"
								listener="#{genQueBean.onSubOptLayoutChange}"
								update=":tabView:subEditForm" />
						</p:selectOneMenu>
						<p:spacer width="10" />
						<h:outputText value="横向列数："
							rendered="#{genQueBean.sub.optLayout==1}" />
						<p:inputText value="#{genQueBean.sub.cols}" size="2" maxlength="2"
							rendered="#{genQueBean.sub.optLayout==1}"
							converterMessage="请输入数字!" />
					</h:panelGrid>
				</p:column>
			</p:row>
			<p:row rendered="#{genQueBean.sub.questTypeValue==2 }">
				<p:column style="text-align:right;padding-right:3px;">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最小值:" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px;">
					<p:inputText value="#{genQueBean.sub.slideMinval}" size="5"
						maxlength="5" converterMessage="滑动题最小值请输入数字!" required="true"
						requiredMessage="滑动题最小值不能为空" />
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最大值:" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText value="#{genQueBean.sub.slideMaxval}" size="5"
						maxlength="5" converterMessage="滑动题最大值请输入数字!" required="true"
						requiredMessage="滑动题最小值不能为空" />
				</p:column>
			</p:row>
			<p:row rendered="#{genQueBean.sub.questTypeValue==2 }">
				<p:column style="text-align:right;padding-right:3px;">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最小值描述:" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText value="#{genQueBean.sub.slideMinvalDesc}" size="20"
						maxlength="250" required="true" requiredMessage="滑动题最小值描述不能为空" />
				</p:column>
				<p:column style="text-align:right;padding-right:3px;">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最大值描述:" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText value="#{genQueBean.sub.slideMaxvalDesc}" size="20"
						maxlength="250" required="true" requiredMessage="滑动题最大值描述不能为空" />
				</p:column>
			</p:row>
			<p:row rendered="false">
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="显示脚本：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<p:inputTextarea value="#{genQueBean.sub.showScript}"
						maxlength="250" cols="85" rows="5" autoResize="false" />
				</p:column>
			</p:row>
			<p:row rendered="false">
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="单位：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<p:inputText value="#{genQueBean.sub.questUnit}" maxlength="10"
						size="10" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="填写提示：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<p:inputTextarea value="#{genQueBean.sub.otherDesc}"
						maxlength="1000" cols="85" rows="8" autoResize="false" />
				</p:column>
			</p:row>
			<p:row rendered="false">
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="提示图片：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<h:panelGroup id="subOtherImgGroup">
						<p:commandButton value="上传" oncomplete="PF('fileUpVar').show();"
							rendered="#{genQueBean.sub.otherImg==null}" process="@this"
							update=":tabView:subEditForm:fileUp">
							<f:setPropertyActionListener target="#{genQueBean.uploadTag}"
								value="2" />
						</p:commandButton>
						<h:panelGroup rendered="#{genQueBean.sub.otherImg!=null}">
							<h:graphicImage url="/webFile#{genQueBean.sub.otherImg}"
								width="100px" height="65px" />
							<p:spacer width="5" />
							<p:commandButton value="删除" update="subOtherImgGroup"
								process="@this" action="#{genQueBean.deleteDiskFile}">
								<f:setPropertyActionListener target="#{genQueBean.uploadTag}"
									value="2" />
							</p:commandButton>
						</h:panelGroup>
						<h:outputText style="color:blue;" value="[推荐像素：124*94]" />
					</h:panelGroup>
				</p:column>
			</p:row>
			<p:row>
				<p:column
					style="text-align:right;padding-right:3px;width:180px;height:30px">
					<h:outputLabel value="显示方式：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<h:outputLabel value="直接显示"
						rendered="#{genQueBean.sub.jumpType!=2 and genQueBean.sub.questTypeValue!=5}" />
					<h:outputLabel value="依赖父题目"
						rendered="#{genQueBean.sub.jumpType==2 and genQueBean.sub.questTypeValue!=5}" />
					<h:panelGrid columns="10" id="relQesGrid" cellpadding="0"
						cellspacing="0" style="border-color: #FFFFFF;padding: 0 ;"
						rendered="#{genQueBean.sub.questTypeValue==5}">
						<p:selectOneMenu value="#{genQueBean.sub.jumpType}">
							<f:selectItem itemValue="0" itemLabel="直接显示" />
							<f:selectItem itemValue="2" itemLabel="依赖上面的题目" />
							<p:ajax event="change" update="relQesGrid" process="@this" />
						</p:selectOneMenu>
						<p:spacer width="10" />
						<h:outputLabel value="依赖哪题:"
							rendered="#{genQueBean.sub.jumpType==2}" />
						<p:selectOneMenu value="#{genQueBean.sub.relQueCode}"
							rendered="#{genQueBean.sub.jumpType==2}" style="width:350px">
							<f:selectItem itemLabel="请选择...." itemValue="" />
							<f:selectItems value="#{genQueBean.relOptions}" var="que"
								id="relList" itemDescription="relList"
								itemLabel="#{que.showCode} #{que.titleDesc}"
								itemValue="#{que.qesCode}" />
							<p:ajax event="change"
								listener="#{genQueBean.onRelQueCodeChange}" update="relQesGrid"
								process="@this,relQesGrid" />
						</p:selectOneMenu>
						<p:spacer width="10" />
						<h:outputLabel value="依赖选项:"
							rendered="#{genQueBean.sub.relQueCode != null}" />
						<p:selectManyCheckbox value="#{genQueBean.sub.relQueOptNums}"
							layout="grid" columns="5"
							rendered="#{genQueBean.sub.relQueCode != null}">
							<f:selectItems value="#{genQueBean.relQueOpts}" />
						</p:selectManyCheckbox>
					</h:panelGrid>
				</p:column>
			</p:row>
		</p:panelGrid>
		<!-- 表格题 -->
		<h:panelGrid columns="1" id="tablePanel" style="width:100%">
			<!-- 行标题 -->
			<p:fieldset id="rowtitleField" legend="行标题" toggleable="true"
				rendered="#{genQueBean.sub.questTypeValue==11}" toggleSpeed="500"
				style="margin-top: 5px;margin-bottom: 5px;">
				<p:dataTable var="itm"
					value="#{genQueBean.sub.fkByTableId.rowtitles}" editingRow="true"
					id="rowtitleTable" emptyMessage="暂无行标题" rows="10" paginator="true"
					rowIndexVar="R" paginatorPosition="bottom">
					<p:column headerText="名称" style="width:180px;">
						<h:outputLabel value="#{itm.title}" />
					</p:column>
					<p:column headerText="行序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.rowIndex}" />
					</p:column>
					<p:column headerText="行合并" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.rowspan}" />
					</p:column>
					<p:column headerText="列序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.colIndex}" />
					</p:column>
					<p:column headerText="列合并" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.colspan}" />
					</p:column>
					<p:column headerText="状态" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.state==1?'启用':'停用'}" />
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="查看" process="@this" resetValues="true"
							update=":tabView:subEditForm:rowtitleDialog"
							oncomplete="PF('RowtitleDialog').show()">
							<f:setPropertyActionListener target="#{genQueBean.rowtitle}"
								value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:fieldset>
			<!--列定义 -->
			<p:fieldset id="colsdefineField" legend="列定义" toggleable="true"
				rendered="#{genQueBean.sub.questTypeValue==11}" toggleSpeed="500"
				style="margin-top: 5px;margin-bottom: 5px;">
				<p:dataTable var="itm"
					value="#{genQueBean.sub.fkByTableId.colsdefines}" editingRow="true"
					id="colsdefineTable" emptyMessage="暂无列定义" rows="10"
					paginator="true" rowIndexVar="R" paginatorPosition="bottom">
					<p:column headerText="序号" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.num}" />
					</p:column>
					<p:column headerText="列名" style="width:180px;">
						<h:outputLabel value="#{itm.colDesc}" />
					</p:column>
					<p:column headerText="列类型" style="width:60px;text-align: center">
						<h:outputLabel value="文本" rendered="#{itm.colType==1}" />
						<h:outputLabel value="整数" rendered="#{itm.colType==2}" />
						<h:outputLabel value="小数" rendered="#{itm.colType==3}" />
						<h:outputLabel value="日期" rendered="#{itm.colType==4}" />
						<h:outputLabel value="计算" rendered="#{itm.colType==5}" />
						<h:outputLabel value="下拉" rendered="#{itm.colType==6}" />
						<h:outputLabel value="合并列头" rendered="#{itm.colType==9}" />
						<h:outputLabel value="标题列" rendered="#{itm.colType==0}" />
					</p:column>
					<p:column headerText="行序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.rowIndex}" />
					</p:column>
					<p:column headerText="列序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.colIndex}" />
					</p:column>
					<p:column headerText="行合并" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.rowspan}" />
					</p:column>
					<p:column headerText="列合并" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.cols}" />
					</p:column>
					<p:column headerText="是否必填" style="width:60px;text-align: center">
						<h:outputLabel value="是" rendered="#{itm.colMust==1}" />
						<h:outputLabel value="否" rendered="#{itm.colMust==0}" />
					</p:column>
					<p:column headerText="状态" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.state==1?'启用':'停用'}" />
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="查看" process="@this" resetValues="true"
							update=":tabView:subEditForm:colsdefineDialog"
							oncomplete="PF('ColsdefineDialog').show()">
							<f:setPropertyActionListener target="#{genQueBean.colsdefine}"
								value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:fieldset>
			<!-- 行标题添加、修改 -->
			<p:dialog width="430" widgetVar="RowtitleDialog" id="rowtitleDialog"
				resizable="false" header="行标题">
				<p:panelGrid style="width:100%;" id="rowtitleEditGrid">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:90px;">
							<h:outputLabel value="名称:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<h:outputText value="#{genQueBean.rowtitle.title}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="行序号:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width:90px;">
							<h:outputText value="#{genQueBean.rowtitle.rowIndex}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:90px;">
							<h:outputLabel value="行合并:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.rowtitle.rowspan}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="列序号:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.rowtitle.colIndex}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="列合并:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.rowtitle.colspan}" />
						</p:column>
					</p:row>
				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="关闭" icon="ui-icon-close"
								id="rowtitleCancelBtn" process="@this"
								oncomplete="PF('RowtitleDialog').hide()">
							</p:commandButton>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
			<p:dialog width="800" widgetVar="ColsdefineDialog"
				id="colsdefineDialog" resizable="false" header="列定义">
				<p:panelGrid style="width:100%;" id="colsdefineEditGrid">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="序号:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width:180px;">
							<h:outputText value="#{genQueBean.colsdefine.num}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="列名:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.colDesc}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="行序号:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.rowIndex}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="行合并:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.rowspan}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="列序号:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.colIndex}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="列合并:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.cols}" />
						</p:column>
					</p:row>
				</p:panelGrid>
				<p:panelGrid id="colDetailGrid"
					style="width:100%;height:100%;position: relative;top: -1px;">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="列类型:" />
						</p:column>
						<p:column
							style="text-align:left;padding-left:3px;#{genQueBean.colsdefine.colType!=0?'width:180px;':''}"
							colspan="#{genQueBean.colsdefine.colType!=0?'1':'3'}">
							<h:outputText value="标题列"
								rendered="#{genQueBean.colsdefine.colType==0}" />
							<h:outputText value="文本"
								rendered="#{genQueBean.colsdefine.colType==1}" />
							<h:outputText value="整数"
								rendered="#{genQueBean.colsdefine.colType==2}" />
							<h:outputText value="小数"
								rendered="#{genQueBean.colsdefine.colType==3}" />
							<h:outputText value="日期"
								rendered="#{genQueBean.colsdefine.colType==4}" />
							<h:outputText value="计算"
								rendered="#{genQueBean.colsdefine.colType==5}" />
							<h:outputText value="下拉"
										  rendered="#{genQueBean.colsdefine.colType==6}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:120px;"
							rendered="#{genQueBean.colsdefine.colType!=0}">
							<h:outputLabel value="是否必填:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;"
							rendered="#{genQueBean.colsdefine.colType!=0}">
							<h:outputText value="是"
								rendered="#{genQueBean.colsdefine.colMust==1}" />
							<h:outputText value="否"
								rendered="#{genQueBean.colsdefine.colMust==0}" />
						</p:column>
					</p:row>
					<p:row rendered="#{genQueBean.colsdefine.colType==5}">
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="表达式:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<h:outputText value="#{genQueBean.colsdefine.colExpr}" />
						</p:column>
					</p:row>
					<p:row
						rendered="#{genQueBean.colsdefine.colType!=null and genQueBean.colsdefine.colType!=0 and genQueBean.colsdefine.colType!=5}">
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="列长度:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.colLenth}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="默认值:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:outputText value="#{genQueBean.colsdefine.colDefvalue}" />
						</p:column>
					</p:row>
					<p:row
						rendered="#{genQueBean.colsdefine.colType ==2 or genQueBean.colsdefine.colType==3}">
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="是否范围约束:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;"
							colspan="#{genQueBean.colsdefine.colType==3?'1':'3'}">
							<h:outputText value="是"
								rendered="#{genQueBean.colsdefine.scopeCons==1}" />
							<h:outputText value="否"
								rendered="#{genQueBean.colsdefine.scopeCons==0}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;"
							rendered="#{genQueBean.colsdefine.colType==3}">
							<h:outputLabel value="列小数点保留位数:" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;"
							rendered="#{genQueBean.colsdefine.colType==3}">
							<h:outputText value="#{genQueBean.colsdefine.colPrec}" />
						</p:column>
					</p:row>
				</p:panelGrid>
				<p:outputPanel id="scopePanel">
					<p:panelGrid id="scopeGrid"
						style="width:100%;height:100%;position: relative;top: -2px;"
						rendered="#{genQueBean.colsdefine.scopeCons==1}">
						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputLabel value="最小值:" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;width:180px">
								<h:outputText value="#{genQueBean.colsdefine.minValue}" />
							</p:column>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputLabel value="最大值:" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;">
								<h:outputText value="#{genQueBean.colsdefine.maxValue}" />
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:outputPanel>
				<p:outputPanel id="dsTypePanel">
					<p:panelGrid id="dsTypeGrid"
						style="width:100%;height:100%;position: relative;top: -2px;"
						rendered="#{genQueBean.colsdefine.colType!=null and genQueBean.colsdefine.colType!=0 and genQueBean.colsdefine.colType!=5}">
						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputLabel value="取值类型:" />
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;#{genQueBean.colsdefine.dsType==2?'width:180px;':''}"
								colspan="#{genQueBean.colsdefine.dsType==2?'1':'3'}">
								<h:outputText value="一般"
									rendered="#{genQueBean.colsdefine.dsType==1}" />
								<h:outputText value="码表"
									rendered="#{genQueBean.colsdefine.dsType==2}" />
								<h:outputText value="SQL"
									rendered="#{genQueBean.colsdefine.dsType==3}" />
							</p:column>
							<p:column style="text-align:right;padding-right:3px;width:120px;"
								rendered="#{genQueBean.colsdefine.dsType==2}">
								<h:outputLabel value="码表编码:" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;"
								rendered="#{genQueBean.colsdefine.dsType==2}">
								<h:outputText value="#{genQueBean.colsdefine.dsCdcode}" />
							</p:column>
						</p:row>
						<p:row rendered="#{genQueBean.colsdefine.dsType==3}">
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputLabel value="取值SQL:" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;" colspan="3">
								<h:outputText value="#{genQueBean.colsdefine.dsSql}" />
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:outputPanel>
				<p:outputPanel id="dsScriptPanel">
					<p:panelGrid id="dsScriptGrid"
								 style="width:100%;height:100%;position: relative;top: -3px;" >
						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputText value="脚本：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;" colspan="3">
								<p:commandButton value="查看" onclick="PF('TableLibScrDialog').show()" process="@this"
												 update=":tabView:subEditForm:tableLibScript"
												 rendered="#{null ne genQueBean.colsdefine and null ne genQueBean.colsdefine.execScript }" />
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:outputPanel>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="关闭" icon="ui-icon-close"
								id="colsdefineCancelBtn" process="@this"
								oncomplete="PF('ColsdefineDialog').hide()">
							</p:commandButton>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
		</h:panelGrid>
		<!-- 选项 -->
		<h:panelGrid columns="1" id="optListPanel" style="width:100%">
			<p:fieldset id="optListField" legend="选项" toggleable="true"
				rendered="#{genQueBean.sub.questTypeValue==0 or genQueBean.sub.questTypeValue==1 or genQueBean.sub.questTypeValue==8 or genQueBean.sub.questTypeValue==10}"
				toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<p:dataTable var="itm" value="#{genQueBean.sub.proOptList}"
					id="optInfoTable" emptyMessage="暂无选项" rows="50" paginator="true"
					paginatorPosition="bottom">
					<p:column headerText="序号" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.num}" />
					</p:column>
					<p:column headerText="选项文字" style="width:320px;">
						<h:outputLabel value="#{itm.optionDesc}" />
					</p:column>
					<p:column headerText="选项值" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.optionValue}" />
					</p:column>
					<p:column headerText="是否需要填空" style="width:55px;text-align: center">
						<h:outputLabel value="#{itm.needFill==1?'是':'否'}" />
					</p:column>
					<p:column headerText="是否多项填空" style="width:55px;text-align: center">
						<h:outputLabel value="#{itm.isMulti==1?'是':'否'}" />
					</p:column>
					<p:column headerText="是否正确答案" style="width:55px;text-align: center">
						<h:outputLabel value="#{itm.isCorrect==1?'是':'否'}" />
					</p:column>
					<p:column headerText="分值" style="width:55px;text-align: center">
						<h:outputLabel value="#{itm.optionScore}" />
					</p:column>
					<p:column headerText="说明" style="width:240px;">
						<h:outputLabel value="#{itm.otherDesc}" />
					</p:column>
					<p:column headerText="状态" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.state==1?'启用':'停用'}" />
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="删除" process="@this" update="optInfoTable"
							action="#{genQueBean.optDeleteAction}" rendered="false">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{genQueBean.opt}"
								value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" process="@this"
							oncomplete="PF('OptDialog').show()"
							update=":tabView:subEditForm:optDialog">
							<f:setPropertyActionListener target="#{genQueBean.opt}"
								value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:fieldset>
		</h:panelGrid>
		<p:fieldset id="fillMaxRangeField" legend="填空值范围" toggleable="true"
			rendered="#{genQueBean.ranges != null}" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:dataTable var="itm" value="#{genQueBean.ranges}" id="rangeTable"
				emptyMessage="暂无数据" rows="50">
				<p:column headerText="选项序号" style="text-align: center"
					rendered="#{genQueBean.sub.questTypeValue==0 or genQueBean.sub.questTypeValue==1 or genQueBean.sub.questTypeValue==8}">
					<h:outputLabel value="#{itm.optNum}" />
				</p:column>
				<p:column headerText="填空序号" style="text-align: center">
					<h:outputLabel value="#{itm.num}" />
				</p:column>
				<p:column headerText="最小值" style="text-align: center">
					<h:outputLabel value="#{itm.fillMin}" />
				</p:column>
				<p:column headerText="最大值" style="text-align: center">
					<h:outputLabel value="#{itm.fillMax}" />
				</p:column>
			</p:dataTable>
		</p:fieldset>
		<p:dialog width="800" widgetVar="OptDialog" id="optDialog"
			resizable="false" minHeight="400" header="选项" maximizable="true">
			<p:panelGrid style="width:100%;" id="optEditGrid">
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;">
						<h:outputLabel value="序号:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;">
						<h:outputText value="#{genQueBean.opt.num}" />
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="是否正确答案:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;">
						<h:outputLabel value="#{genQueBean.opt.isCorrect==1?'是':'否'}" />
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
						rendered="false">
						<h:outputLabel value="选项图片:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;"
						rendered="false">
						<h:panelGroup id="optionImgGroup">
							<h:panelGroup rendered="#{genQueBean.opt.optionImg!=null}">
								<h:graphicImage url="/webFile#{genQueBean.opt.optionImg}"
									width="100px" height="65px" />
							</h:panelGroup>
						</h:panelGroup>
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;">
						<h:outputLabel value="选项文字:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;" colspan="3">
						<h:outputText value="#{genQueBean.opt.optionDesc}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;">
						<h:outputLabel value="说明:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;" colspan="3">
						<h:outputText value="#{genQueBean.opt.otherDesc}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;">
						<h:outputLabel value="选项值:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;"
						colspan="#{genQueBean.sub.questTypeValue==1?'1':'3'}">
						<h:outputText value="#{genQueBean.opt.optionValue}" />
					</p:column>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;"
						rendered="#{genQueBean.sub.questTypeValue==1}">
						<h:outputLabel value="是否互斥:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;"
						rendered="#{genQueBean.sub.questTypeValue==1}">
						<h:outputText value="#{genQueBean.opt.isAlter==1?'是':'否'}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;">
						<h:outputLabel value="是否需要填空:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;width:220px;"
						colspan="#{genQueBean.opt.needFill==1?'1':'3'}">
						<h:outputText value="#{genQueBean.opt.needFill==1?'是':'否'}" />
					</p:column>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;"
						rendered="#{genQueBean.opt.needFill==1}">
						<h:outputLabel value="是否多项填空:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;"
						rendered="#{genQueBean.opt.needFill==1}">
						<h:outputText value="#{genQueBean.opt.isMulti==1?'是':'否'}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:120px;height:30px;">
						<h:outputLabel value="分值:" />
					</p:column>
					<p:column style="text-align:left;padding-left:5px;" colspan="3">
						<p:inputText value="#{genQueBean.opt.optionScore}" maxlength="5"
							size="5" converterMessage="请输入数字" />
					</p:column>
				</p:row>
				<p:row rendered="false">
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="备注:" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" colspan="3">
						<p:inputText value="#{genQueBean.opt.rmk}" maxlength="250"
							size="67" />
					</p:column>
				</p:row>
				<p:row rendered="false">
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="说明图片:" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" colspan="3">
						<h:panelGroup id="otherImgGroup">
							<p:commandButton value="上传" oncomplete="PF('fileUpVar').show();"
								rendered="#{genQueBean.opt.otherImg==null}" process="@this"
								update=":tabView:subEditForm:fileUp">
								<f:setPropertyActionListener target="#{genQueBean.uploadTag}"
									value="0" />
							</p:commandButton>
							<h:panelGroup rendered="#{genQueBean.opt.otherImg!=null}">
								<h:graphicImage url="/webFile#{genQueBean.opt.otherImg}"
									width="100px" height="65px" />
								<p:spacer width="5" />
								<p:commandButton value="删除" update="otherImgGroup"
									process="@this" action="#{genQueBean.deleteDiskFile}">
									<p:confirm header="消息确认框" message="确定要删除吗？"
										icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{genQueBean.uploadTag}"
										value="0" />
								</p:commandButton>
							</h:panelGroup>
							<h:outputText style="color:blue;" value="[推荐像素：124*94]" />
						</h:panelGroup>
					</p:column>
				</p:row>
				<p:row rendered="#{genQueBean.sub.jumpType!=2}">
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="跳转类型:" />
					</p:column>
					<p:column style="text-align:left;padding: 0;" colspan="3">
						<h:panelGrid columns="10" id="jumpTypeGrid"
							style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
							<p:selectOneMenu value="#{genQueBean.opt.jumpType}">
								<f:selectItem itemValue="0" itemLabel="无跳转" />
								<f:selectItem itemValue="3" itemLabel="跳转到哪个题目" />
								<p:ajax event="change" update="jumpTypeGrid" process="@this" />
							</p:selectOneMenu>
							<p:spacer width="10" />
							<h:outputLabel value="跳转至哪题:"
								rendered="#{genQueBean.opt.jumpType==3}" />
							<p:selectOneMenu value="#{genQueBean.opt.jumpQuestCode}"
								rendered="#{genQueBean.opt.jumpType==3}" style="width:350px">
								<f:selectItem itemLabel="请选择...." itemValue="" />
								<f:selectItems value="#{genQueBean.jumpOptions}" var="que"
									id="jumpList" itemDescription="jumpList"
									itemLabel="#{que.showCode} #{que.titleDesc}"
									itemValue="#{que.qesCode}" />
							</p:selectOneMenu>
						</h:panelGrid>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check" id="optSubmitBtn"
							process="@this,optEditGrid" update="optInfoTable"
							action="#{genQueBean.optSaveAction}" />
						<p:commandButton value="关闭" icon="ui-icon-close" id="optCancelBtn"
							immediate="true" oncomplete="PF('OptDialog').hide()" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>


		<!-- 表格列脚本 -->
		<p:dialog header="脚本" widgetVar="TableLibScrDialog" id="tableLibScrDialog"
				  maximizable="true" resizable="false">
			<p:inputTextarea value="#{null == genQueBean.colsdefine ? '' : genQueBean.colsdefine.execScript}" cols="85" readonly="true"
							 rows="18" id="tableLibScript" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
										 id="tableLibScrDialogCancelBtn" onclick="PF('TableLibScrDialog').hide();"
										 immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 文件上传 -->
		<p:dialog header="文件上传" widgetVar="fileUpVar" id="fileUp"
			resizable="false" modal="true">
			<p:fileUpload requiredMessage="请选择上传图标！" style="width:700px;"
				previewWidth="50"
				fileUploadListener="#{genQueBean.handleFileUpload}" fileLimit="1"
				fileLimitMessage="最多只能上传1个文件！" label="选择文件" uploadLabel="上传"
				cancelLabel="取消" sizeLimit="10485760"
				invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
				invalidFileMessage="无效的文件类型！只能上传gif,jpg,png等图片类型文件！" process="@this"
				mode="advanced" dragDropSupport="true"
				allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
		</p:dialog>
	</h:form>
</ui:composition>