<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" 
	xmlns:a4j="http://java.sun.com/jsf/html" 
	template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdMsgMainSearchBean}" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
		<script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/NewPopMenu.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/CreatePopup.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/index.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/zwx.system.js"></script>

		<style type="text/css">
.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 5px;
	padding-right: 0px;
}
</style>
		<script type="text/javascript">
            //<![CDATA[
            function forwordPage(name,adr){
                if("" != name && "" != adr){
                    top.ShortcutMenuClick("01",name,adr,"");
                }
            }
            //]]>
        </script>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6" style="text-align:left;padding-left:5px; height: 20px;">
				<h:outputText value="我的消息" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdMsgMainSearchBean.searchAction}" update="dataTable"
					process="@this,dataTable,searchTitle,searchState,searchType" />
				<p:commandButton value="全部已阅" icon="ui-icon-search" id="qbyy" action="#{tdMsgMainSearchBean.editSubStateAll}" update="dataTable" process="@this,dataTable">
					<f:setPropertyActionListener value="1" target="#{tdMsgMainSearchBean.state}" />
					<p:confirm header="消息确认框" message="确定要全部已阅吗？" icon="ui-icon-alert" />
				</p:commandButton>
				<p:commandButton value="批量已阅" icon="ui-icon-search" id="plyy" action="#{tdMsgMainSearchBean.boforeEditSubStatePl}" process="@this,dataTable">
				</p:commandButton>
				<p:confirmDialog message="确定要批量已阅吗？" header="消息确认框" widgetVar="ConfirmDialog">
			        <p:commandButton value="确定" action="#{tdMsgMainSearchBean.editSubStatePl}" update="dataTable" icon="ui-icon-check">
			        	<f:setPropertyActionListener value="1" target="#{tdMsgMainSearchBean.state}" />
			        </p:commandButton>
			        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
			    </p:confirmDialog>
				<p:commandButton value="批量收藏" icon="ui-icon-search" id="plsc" action="#{tdMsgMainSearchBean.editSubStatePl}" update="dataTable" process="@this,dataTable" rendered="false">
					<f:setPropertyActionListener value="2" target="#{tdMsgMainSearchBean.state}" />
					<p:confirm header="消息确认框" message="确定要批量收藏吗？" icon="ui-icon-alert" />
				</p:commandButton>
				<p:commandButton value="批量删除" icon="ui-icon-search" id="pldel" action="#{tdMsgMainSearchBean.delSubStatePl}" update="dataTable" process="@this,dataTable" rendered="false">
					<p:confirm header="消息确认框" message="确定要批量删除吗？" icon="ui-icon-alert" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<p:outputLabel value="标题：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;width:400px;">
				<p:inputText id="searchTitle" size="58" value="#{tdMsgMainSearchBean.searchInfoTitle}" maxlength="100" style="width:380px;" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<p:outputLabel value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left: 3px">
				<p:selectManyCheckbox id="searchState" value="#{tdMsgMainSearchBean.searchState}">
					<f:selectItem itemLabel="未阅" itemValue="0" />
					<f:selectItem itemLabel="已阅" itemValue="1" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
		
		<p:row rendered="false">
			<p:column style="text-align:right;padding-right:3px">
				<p:outputLabel value="消息类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left: 3px" colspan="3">
				<p:selectManyCheckbox id="searchType" value="#{tdMsgMainSearchBean.type}">
					<f:selectItems value="#{tdMsgMainSearchBean.searchTypeName}"/>
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertContent">
		<p:dataTable id="dataTable" var="v" value="#{tdMsgMainSearchBean.dataModel}"
		 		paginator="true" rows="#{tdMsgMainSearchBean.pageSize}" paginatorPosition="bottom" rowsPerPageTemplate="#{tdMsgMainSearchBean.perPageSize}" lazy="true"  
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
				currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
			 	style="text-align: left" selection="#{tdMsgMainSearchBean.selResultList}" rowKey="#{v[0]}"
				emptyMessage="没有您要找的记录！">
			<p:column selectionMode="multiple" style="width:50px;text-align:center;" disabledSelection="#{v[2] == 1}"/>
			<p:column headerText="消息类型" style="text-align:center;width: 7%" rendered="false">
				<p:outputLabel value="#{v[1].typeCN}" />
			</p:column>
			<p:column headerText="标题">
				<p:graphicImage  url="/resources/images/map/alarm.gif" width="16" height="21" rendered="#{v[1].typeCN == '4'}"  />
				<p:commandLink rendered="#{v[3] != null}" id="linkAlarm" value="#{v[6]}" process="@this"
					action="#{tdMsgMainSearchBean.openLinkAction}">
					<f:setPropertyActionListener value="#{v[0]}" target="#{tdMsgMainSearchBean.rid}" />
				</p:commandLink>	
				<p:outputLabel rendered="#{v[3] == null}" value="#{v[6]}" />
			</p:column>
			<p:column headerText="接收时间" style="width:150px;text-align:center">
				<p:outputLabel value="#{v[5]}"/>
			</p:column>
			<p:column headerText="状态" style="text-align:center;width: 60px;">
				<p:outputLabel rendered="#{v[2] == 0}" value="未阅" />
				<p:outputLabel rendered="#{v[2] == 1}" value="已阅" />
				<p:outputLabel rendered="#{v[2] == 2}" value="收藏" />
			</p:column>
			<p:column headerText="操作" style="width:150px;">
				<p:commandLink value="已阅" id="yy" rendered="#{v[2] == 0}" action="#{tdMsgMainSearchBean.editSubState}" update="dataTable"
					process="@this,dataTable">
					<f:setPropertyActionListener value="1" target="#{tdMsgMainSearchBean.state}" />
					<f:setPropertyActionListener value="#{v[0]}" target="#{tdMsgMainSearchBean.rid}" />
					<p:confirm header="消息确认框" message="确定要已阅吗？" icon="ui-icon-alert" />
				</p:commandLink>
				<p:spacer width="5" rendered="#{v[2] == 0}" />
				<p:commandLink value="收藏" id="sc" rendered="#{v[2] == 0 and false}" action="#{tdMsgMainSearchBean.editSubState}" update="dataTable"
					process="@this,dataTable">
					<f:setPropertyActionListener value="2" target="#{tdMsgMainSearchBean.state}" />
					<f:setPropertyActionListener value="#{v[0]}" target="#{tdMsgMainSearchBean.rid}" />
					<p:confirm header="消息确认框" message="确定要收藏吗？" icon="ui-icon-alert" />
				</p:commandLink>
				<p:spacer width="5"/>
				<p:commandLink value="删除" id="del" action="#{tdMsgMainSearchBean.delSubState}" update="dataTable"
					process="@this,dataTable" rendered="false">
					<f:setPropertyActionListener value="#{v[7]}" target="#{tdMsgMainSearchBean.rid}" />
					<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				</p:commandLink>
			</p:column>
		</p:dataTable>
	</ui:define>
</ui:composition>