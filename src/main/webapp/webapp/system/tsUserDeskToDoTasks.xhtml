<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
	<!--@elvariable id="mgrbean" type="com.chis.modules.system.web.TsUserDeskShowBean"-->
  <!-- 门户-待办任务 -->
  <p:outputPanel styleClass="panelDiv">
	 <p:outputLabel value="待办任务" styleClass="panelTitle"></p:outputLabel>
  </p:outputPanel>
  <p:outputPanel style="padding: 12px 20px 10px 20px;" rendered="#{mgrbean.toDoTasks.size()>0}">
  	 <p:panelGrid styleClass="panelGrid">
  	 	<c:forEach items="#{mgrbean.toDoTasks}" var="task">
	  	 	<p:row>
	  	 		<p:column>
  	 				<p:outputPanel style="padding:10px 0px;">
		  	 			<p:outputLabel value="#{task.infoMsg}" escape="false" onclick="top.menuclick('#{task.menuVO.menuCn}','#{task.menuVO.menuCn}','#{task.menuVO.menuUri}','#{task.menuVO.menuRid}')"
		  	 				style="color:#444444 !important;text-decoration: none;cursor: pointer;">
		  	 			</p:outputLabel>
  	 				</p:outputPanel>
	  	 		</p:column>
	  	 		<p:column styleClass="dateCol">
	  	 			<p:outputLabel value="#{task.date}" style="color: #707879 !important;">
	  	 				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN"></f:convertDateTime>
	  	 			</p:outputLabel>
	  	 		</p:column>
	  	 	</p:row>
  	 	</c:forEach>
  	 </p:panelGrid>
  </p:outputPanel>
  <p:outputPanel styleClass="imgNoDataDiv" rendered="#{mgrbean.toDoTasks.size()==0}">
 	<!-- 暂无数据 -->
	<p:outputPanel>
		<p:graphicImage url="/resources/images/portal/img_no_data.svg"></p:graphicImage>
		<p:outputPanel style="text-align: center;">
			<p:outputLabel value="暂无数据"></p:outputLabel>
		</p:outputPanel>
	</p:outputPanel>
 </p:outputPanel>    
</ui:composition>