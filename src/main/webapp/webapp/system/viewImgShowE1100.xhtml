<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://xmlns.jcp.org/jsf/html"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:f="http://xmlns.jcp.org/jsf/core"
      xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view>
    <h:head>
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />

        <link rel="stylesheet" href="/resources/raxusSlider/css/raxus.css" media="screen" type="text/css"/>
        <script type="text/javascript" src="/resources/raxusSlider/js/raxus-slider.min.js"></script>

        <link rel="stylesheet" href="/resources/raxusSlider/css/bootstrap.min.css" media="screen" type="text/css"/>
        <link rel="stylesheet" href="/resources/raxusSlider/documentation/css/documentation.css" media="screen" type="text/css"/>
        <script type="text/javascript" src="/resources/raxusSlider/documentation/js/document.js"></script>

        <script type="text/javascript" src="/resources/raxusSlider/documentation/js/highlight.pack.js"/>

        <script type="text/javascript">
            //<![CDATA[
            function rotate(obj,arr){
                obj = obj + document.getElementById('mySlider').getAttribute('data-show');
                var img = document.getElementById(obj);
                if(!img || !arr) return false;
                var n = img.getAttribute('step');
                if(n== null) n=0;
                if(arr=='left'){
                    (n==0)? n=3:n--;
                }else if(arr=='right'){
                    (n==3)? n=0:n++;
                }
                img.setAttribute('step',n);
                var c = document.getElementById('canvas_'+obj);
                if(c== null){
                    img.style.visibility = 'hidden';
                    img.style.position = 'absolute';
                    c = document.createElement('canvas');
                    c.setAttribute("id",'canvas_'+obj);
                    img.parentNode.appendChild(c);
                }
                var canvasContext = c.getContext('2d');
                switch (n) {
                    default :
                    case 0 :
                        c.setAttribute('width', img.width);
                        c.setAttribute('height', img.height);
                        canvasContext.rotate(0 * Math.PI / 180);
                        canvasContext.drawImage(img, 0, 0,img.width,img.height);
                        break;
                    case 1 :
                        c.setAttribute('width', img.height);
                        c.setAttribute('height', img.width);
                        canvasContext.rotate(90 * Math.PI / 180);
                        canvasContext.drawImage(img, 0,-img.height,img.width,img.height);
                        break;
                    case 2 :
                        c.setAttribute('width', img.width);
                        c.setAttribute('height', img.height);
                        canvasContext.rotate(180 * Math.PI / 180);
                        canvasContext.drawImage(img, -img.width, -img.height,img.width,img.height);
                        break;
                    case 3 :
                        c.setAttribute('width', img.height);
                        c.setAttribute('height', img.width);
                        canvasContext.rotate(270 * Math.PI / 180);
                        canvasContext.drawImage(img, -img.width, 0,img.width,img.height);
                        break;
                }
            }

            //]]>
        </script>

        <style type="text/css">
            #mySlider {
                width: 100%;
                height: 1200px;
                margin: auto; /* for center alignment */
            }
            #mySlider .mini-images li {
                width: 100px;
                height: 50px;
            }

            /* for tablet */
            @media screen and (max-width: 980px) {
                #mySlider {
                    width: 100%;
                    height: 450px;
                }
            }
            /* for mobile */
            @media screen and (max-width: 640px) {
                #mySlider {
                    width: 100%;
                    height: 250px;
                }
            }

            .mainbox{

                padding-top: 20px;
                margin:auto;
                width: 85%;
            }

            #tool {
                height:28px;
                line-height:24px
            }
            #tool a {
                display:block;
                float:right;
                width:50px;
                height:20px;
                background:url(../../resources/raxusSlider/img/arr.gif) no-repeat;
                font-size:14px;
                text-indent:16px
            }
            #tool a#arr_left {
                background-position:2px 6px
            }
            #tool a#arr_right {
                background-position:2px -21px
            }

        </style>
    </h:head>

    <h:body>
        <h:form id="mainForm">
            <div class="mainbox">
                <div id="tool">
                    <a href="#" id="arr_left" onclick="rotate('myimg','left')">向左</a>
                    <a href="#" id="arr_right" onclick="rotate('myimg','right')">向右</a>
                </div>

                <div class="row">
                    <div class="col-md-12 a">

                        <div id="mySlider" class="raxus-slider"
                             data-arrows="show" data-fullscreen="show" data-dots="show"
                             data-keypress="true" data-thumbnail="bottom">
                            <ul class="slider-relative" id="relative">
                                <c:forEach items="#{viewImgShowBean.relist}" var="item" varStatus="varstatus">
                                    <li class="slide">
                                        <img src="/webFile#{item.path}" alt="" id="myimg#{varstatus.index}" />
                                            <span class="text">
                                                <small>#{item.desc}</small>
                                            </span>
                                    </li>
                                </c:forEach>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        </h:form>
    </h:body>
</f:view>
</html>
