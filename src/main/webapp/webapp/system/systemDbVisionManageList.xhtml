<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage2.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{dbVisionBean}"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="数据库版本管理"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertContent">
        <p:dataTable id="dbVisionTable" style="margin-top: 5px;margin-bottom: 5px;"
                     value="#{dbVisionBean.dbVisionConvertList}" var="dbVision"
                     paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="20,50" pageLinks="5"
                     rows="20"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     emptyMessage="没有您要找的记录！">
            <p:column headerText="系统类型" style="text-align:center;width:100px;">
                <h:outputText value="#{dbVision.systemType.typeNo}"/>
            </p:column>
            <p:column headerText="系统类型名称" style="width:200px;">
                <h:outputText value="#{dbVision.systemTypeNameCn}"/>
            </p:column>
            <p:column headerText="系统版本号" style="text-align:center;width:120px;">
                <h:outputText value="#{dbVision.latestVision}"/>
            </p:column>
            <p:column headerText="数据库版本号" style="text-align:center;width:120px;">
                <h:outputText value="#{dbVision.tsSystemUpdate.curVersion}"
                              style="color: #{dbVision.tsSystemUpdate.curVersion == dbVision.latestVision?'green':'red'};"/>
            </p:column>
            <p:column headerText="操作" style="">
                <p:commandLink value="手动升级" style="padding-right: 5px;"
                               action="#{dbVisionBean.updateDbVision}"
                               update=":mainForm:detailDialog, :mainForm:dbVisionTable"
                               process="@this, :mainForm:dbVisionTable"
                               rendered="#{!dbVision.visionIdentical}">
                    <f:setPropertyActionListener target="#{dbVisionBean.dbVisionConvert}" value="#{dbVision}"/>
                </p:commandLink>
                <p:commandLink value="查看升级语句"
                               action="#{dbVisionBean.showAllSql}"
                               update=":mainForm:allLatestSqlTable"
                               process="@this, :mainForm:dbVisionTable">
                    <f:setPropertyActionListener target="#{dbVisionBean.dbVisionConvert}" value="#{dbVision}"/>
                </p:commandLink>
            </p:column>
        </p:dataTable>

        <p:dialog id="detailDialog" header="日志详情" widgetVar="DetailDialog"
                  resizable="false" width="850" height="450" modal="true">
            <p:scrollPanel mode="native" style="border:0;padding-bottom:5px;margin-top:5px">
                <h:outputText value="#{dbVisionBean.dbVisionConvert.errors}"
                              style="text-align:left;font-size:14px;word-break: break-word;word-wrap:break-word;"
                              escape="false"/>
            </p:scrollPanel>
        </p:dialog>
        <p:dialog id="allLatestSqlDialog" header="升级语句" widgetVar="AllLatestSqlDialog"
                  resizable="false" width="850" height="350" modal="true">
            <p:scrollPanel id="allLatestSqlTablePanel" mode="native" style="border:0;padding-bottom:5px;margin-top:5px">
                <p:dataTable id="allLatestSqlTable" style="margin-top: 5px;margin-bottom: 5px;"
                             value="#{dbVisionBean.dbVisionConvert.sqlSentenceList}" var="sqlSentence"
                             paginator="true" paginatorPosition="bottom" rowsPerPageTemplate="10,20,50" pageLinks="5"
                             rows="10"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                             emptyMessage="没有您要找的记录！">
                    <p:column headerText="版本" style="text-align:center;width:100px;">
                        <h:outputText value="#{sqlSentence.ver}"/>
                    </p:column>
                    <p:column headerText="SQL">
                        <h:outputText id="sqlSentenceText" style="overflow: hidden; word-break: break-all; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1;"
                                      value="#{sqlSentence.sql}" escape="false"/>
                    </p:column>
                    <p:column headerText="操作" style="text-align:center;width:100px;">
                        <p:commandLink value="详情"
                                       action="#{dbVisionBean.showSql}"
                                       process="@this, :mainForm:allLatestSqlTable"
                                       update=":mainForm:showSqlPanel">
                            <f:setPropertyActionListener target="#{dbVisionBean.viewSqlSentence}"
                                                         value="#{sqlSentence}"/>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </p:scrollPanel>
        </p:dialog>

        <p:dialog id="showSql" header="SQL" widgetVar="ShowSql"
                  resizable="false" width="800" modal="true" maximizable="true">
            <p:outputPanel id="showSqlPanel">
                <div style="max-width:800px;word-wrap: break-word;padding: 10px;">
                    <p:outputLabel value="#{dbVisionBean.viewSql}" escape="false"/>
                </div>
            </p:outputPanel>
        </p:dialog>
    </ui:define>

</ui:composition>