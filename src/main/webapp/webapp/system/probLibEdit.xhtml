<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/editTemplate.xhtml">

	<ui:define name="insertEditScripts">
		<style type="text/css">
.ui-treetable thead th,.ui-treetable tbody td,.ui-treetable tfoot td {
	white-space: normal;
}

.ui-orderlist .ui-orderlist-list {
	list-style-type: none;
	margin: 0;
	padding: 0;
	overflow: auto;
	height: 400px;
	width: 680px;
}

.orderList {
	padding-left: 13px;
}
</style>
		<script>
			//<![CDATA[

			//]]>
		</script>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="2"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="问卷编辑" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 编辑页面的按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42" id="editPageButtonPanel">
			<h:panelGrid columns="7"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{genQueBean.saveAction}"
					process="@this,editGrid,subjectInfoTable" update="@form" />
				<p:commandButton value="生成" icon="ui-icon-disk" id="generateBtn"
					action="#{genQueBean.genHtmlAction}" process="@this,editGrid"
					update="editGrid">
					<f:setPropertyActionListener target="#{genQueBean.ifEdit}"
						value="1" />
				</p:commandButton>
				<p:menuButton value="预览">
					<p:menuitem value="WEB版" icon="ui-icon-extlink" id="webPreviewBtn"
						action="#{genQueBean.forwardPreviewAction}" process="@this">
						<f:setPropertyActionListener target="#{genQueBean.htmlName}"
							value="#{genQueBean.lib.htmlName}" />
						<f:setPropertyActionListener target="#{genQueBean.previewTag}"
							value="1" />
					</p:menuitem>
					<p:menuitem value="移动版" icon="ui-icon-newwin" id="mobilePreviewBtn"
						action="#{genQueBean.forwardPreviewAction}" process="@this">
						<f:setPropertyActionListener target="#{genQueBean.htmlName}"
							value="#{genQueBean.lib.htmlName}" />
						<f:setPropertyActionListener target="#{genQueBean.previewTag}"
							value="2" />
					</p:menuitem>
				</p:menuButton>
				<p:commandButton icon="ui-icon-search" value="题库选择"
					action="#{genQueBean.subAddInitAction}"
					process="@this,subjectInfoTable">
					<p:ajax event="dialogReturn" listener="#{genQueBean.onSubSelect}"
						process="@this" />
				</p:commandButton>
				<p:commandButton icon="ui-icon-plus" value="添加标题"
					action="#{genQueBean.titleAddInit}"
					process="@this,subjectInfoTable">
				</p:commandButton>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{genQueBean.backAction}" update=":tabView" process="@this"
					immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="editPageButtonPanel" margin="0"></p:sticky>
	</ui:define>

	<!-- 编辑页面的内容-->
	<ui:define name="insertEditContent">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="所属单位：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width:280px;">
				<h:outputText value="#{genQueBean.lib.tsUnitByUnitId.unitname}"
					rendered="#{genQueBean.ifAdmin==false}" />
				<p:selectOneMenu value="#{genQueBean.selectedUnitID}"
					rendered="#{genQueBean.ifAdmin}">
					<f:selectItem itemValue="" itemLabel="请选择..." />
					<f:selectItems value="#{genQueBean.unitList}" />
				</p:selectOneMenu>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<font color="red">*</font>
				<h:outputText value="问卷类别：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:selectOneMenu value="#{genQueBean.selectedQueTypeID}"
					required="true" requiredMessage="请选择问卷类别！">
					<f:selectItem itemValue="" itemLabel="请选择..." />
					<f:selectItems value="#{genQueBean.typeList}" />
				</p:selectOneMenu>
			</p:column>
		</p:row>
		<p:row rendered="#{false and genQueBean.ifAdmin }">
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<font color="red">*</font>
				<h:outputText value="系统类别：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;" colspan="3">
				<p:selectOneMenu value="#{genQueBean.systemTypeEdit}"
					required="true" requiredMessage="系统类型不允许为空！" style="width: 180px;">
					<f:selectItem itemLabel="--请选择--" itemValue="" />
					<f:selectItems value="#{genQueBean.systemTypeMap}" />
				</p:selectOneMenu>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="序号：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width:280px;">
				<p:inputText value="#{genQueBean.lib.num}" maxlength="5" size="5"
					converterMessage="请输入数字" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<font color="red">*</font>
				<h:outputText value="问卷名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText value="#{genQueBean.lib.questName}" maxlength="50"
					required="true" requiredMessage="请输入问卷名称！" size="35" />
			</p:column>
		</p:row>
		<p:row rendered="#{genQueBean.ifAdmin}">
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="基础资料脚本：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:commandButton value="编辑" id="libScrBtn" process="@this"
					oncomplete="PF('LibScrDialog').show();" update="libScrDialog" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="初始化脚本：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:commandButton value="编辑" id="libInitScrBtn" process="@this"
					oncomplete="PF('LibInitScrDialog').show();"
					update="libInitScrDialog" />
			</p:column>
		</p:row>
		<p:row rendered="#{genQueBean.ifAdmin}">
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="验证脚本：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;" colspan="3">
				<p:commandButton value="编辑" id="verifyScriptBtn" process="@this"
					oncomplete="PF('VerifyScrDialog').show();" update="verifyScrDialog" />
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="备注：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;" colspan="3">
				<p:inputTextarea value="#{genQueBean.lib.rmk}" maxlength="1000"
					cols="85" rows="4" />
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<font color="red">*</font>
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:selectOneRadio value="#{genQueBean.lib.state}"
					style="width:150px">
					<f:selectItem itemLabel="启用" itemValue="1" />
					<f:selectItem itemLabel="停用" itemValue="0" />
				</p:selectOneRadio>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputText value="背景图：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:commandButton value="选择背景" oncomplete="PF('ThemeDialog').show()"
					action="#{genQueBean.themeSelectedInit}" process="@this"
					update=":tabView:editForm:themeDialog">
					<p:resetInput target=":tabView:editForm:themeDialog" />
				</p:commandButton>
			</p:column>
		</p:row>
	</ui:define>

	<ui:define name="insertOtherContents">
		<p:fieldset id="subjectListField" legend="题目列表" toggleable="true"
			toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
			<p:treeTable value="#{genQueBean.root}" var="itm"
				id="subjectInfoTable" emptyMessage="暂无题目" style="width:100%;">
				<p:column headerText="题目标题">
					<h:outputLabel value="#{itm.titleDesc}" />
				</p:column>
				<p:column headerText="题目编码" rendered="#{genQueBean.ifAdmin}"
					style="width:60px;text-align:center;">
					<h:outputLabel value="#{itm.qesCode}"
						rendered="#{itm.questType.typeNo != 5}" />
				</p:column>
				<p:column headerText="显示题号" style="width:80px;padding-left:3px;">
					<p:inputText value="#{itm.showCode}" maxlength="10"
						size="10" />
				</p:column>
				<p:column headerText="对照编码" style="width:80px;padding-left:3px;">
					<p:inputText value="#{itm.rightCode}" maxlength="20"
								 size="10" />
				</p:column>
				<p:column headerText="类型" style="width:80px;text-align: center">
					<h:outputLabel value="#{itm.questType.typeCN}" />
				</p:column>
				<p:column headerText="是否必答" style="width:60px;text-align: center">
					<h:outputLabel value="#{itm.mustAsk==1?'是':'否'}" />
				</p:column>
				<p:column headerText="操作" style="width:100px;padding-left: 3px;">
					<p:spacer width="5" />
					<p:commandLink value="修改" action="#{genQueBean.subModInitAction}"
						process="@this,subjectInfoTable,:tabView:editForm:editGrid"
						update=":tabView" resetValues="true">
						<f:setPropertyActionListener value="#{itm}"
							target="#{genQueBean.sub}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="排序" action="#{genQueBean.initSubOrder}"
						process="@this,subjectInfoTable"
						update=":tabView:editForm:subOrderDialog"
						oncomplete="PF('SubOrderDialog').show();">
						<f:setPropertyActionListener value="#{itm}"
							target="#{genQueBean.sub}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="删除" action="#{genQueBean.subDeleteAction}"
						process="@this" update="subjectInfoTable">
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener value="#{itm}"
							target="#{genQueBean.sub}" />
					</p:commandLink>
				</p:column>
			</p:treeTable>
		</p:fieldset>
		<p:dialog width="700" widgetVar="ThemeDialog" id="themeDialog"
			resizable="false" minHeight="400" header="选择背景">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="3"
					style="border-color:transparent;padding:0px;">
					<span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="确定" icon="ui-icon-check"
						id="themeSubmitBtn" process="@this"
						action="#{genQueBean.themeSelectedConfirm}"
						oncomplete="PF('ThemeDialog').hide()" />
					<p:commandButton value="关闭" icon="ui-icon-close"
						id="themeCancelBtn" immediate="true"
						oncomplete="PF('ThemeDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<p:dataGrid var="theme" id="themeGrid"
				value="#{genQueBean.themeList}" columns="3" layout="grid">
				<p:panel style="text-align:center">
					<h:panelGrid columns="1" style="width:100%">
						<p:graphicImage value="#{theme.thumbPath}" />
						<p:selectBooleanCheckbox value="#{theme.selected}">
							<p:ajax listener="#{genQueBean.onThemeSelected(theme.value)}"
								update="themeGrid" />
						</p:selectBooleanCheckbox>
					</h:panelGrid>
				</p:panel>
			</p:dataGrid>
		</p:dialog>
		<p:dialog width="700" widgetVar="TitleAddDialog" id="titleAddDialog"
			resizable="false" header="添加标题">
			<table style="width:100%;margin-top:5px;">
				<tr>
					<td style="width:20%;text-align: right;">显示题号：</td>
					<td><p:inputText id="showCode"
							value="#{genQueBean.sub.showCode}" maxlength="10" size="10" /></td>
				</tr>
				<tr>
					<td style="width:20%;text-align: right;"><font color="red">*</font>标题：</td>
					<td><p:inputTextarea value="#{genQueBean.sub.titleDesc}"
							maxlength="100" cols="55" rows="3" autoResize="false"
							required="true" requiredMessage="请输入标题！" /></td>
				</tr>
			</table>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
							id="titleSubmitBtn" process="@this,titleAddDialog"
							action="#{genQueBean.titleSaveAction}" />
						<p:commandButton value="关闭" icon="ui-icon-close"
							id="titleCancelBtn" immediate="true"
							oncomplete="PF('TitleAddDialog').hide()" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 排序题目  -->
		<p:dialog id="subOrderDialog" header="题目排序" widgetVar="SubOrderDialog"
			resizable="false" width="780" height="440" modal="true">
			<p:orderList value="#{genQueBean.orderList}" id="orderList"
				var="order" style="width:650px;text-align:center;"
				styleClass="orderList" itemValue="#{order}"
				converter="system.ProbSubOrderConvert" controlsLocation="right">
				<f:facet name="caption">题目名称</f:facet>
				<p:column style="text-align:left;bottom:2px;">
					<h:outputText value="#{order.showCode} #{order.titleDesc}" />
				</p:column>
			</p:orderList>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check"
							id="subOrderSaveBtn" action="#{genQueBean.saveSubOrderList}"
							process="@this,orderList" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							id="subOrderBackBtn" onclick="PF('SubOrderDialog').hide();"
							immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<p:dialog header="基础资料脚本" widgetVar="LibScrDialog" id="libScrDialog"
			maximizable="true" resizable="false">
			<p:inputTextarea value="#{genQueBean.lib.libScript}" cols="85"
				rows="18" id="libScript" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
							id="LibScrDialogSaveBtn" oncomplete="PF('LibScrDialog').hide();"
							process="@this,libScript" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							id="LibScrDialogCancelBtn" onclick="PF('LibScrDialog').hide();"
							immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<p:dialog header="初始化脚本" widgetVar="LibInitScrDialog"
			id="libInitScrDialog" maximizable="true" resizable="false">
			<p:inputTextarea value="#{genQueBean.lib.libInitSrc}" cols="85"
				rows="18" id="libInitSrc" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
							id="LibInitScrDialogSaveBtn"
							oncomplete="PF('LibInitScrDialog').hide();"
							process="@this,libInitSrc" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							id="LibInitScrDialogCancelBtn"
							onclick="PF('LibInitScrDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<p:dialog header="验证脚本" widgetVar="VerifyScrDialog"
			id="verifyScrDialog" maximizable="true" resizable="false">
			<p:inputTextarea value="#{genQueBean.lib.verifyScript}" cols="85"
				rows="18" id="verifyScript" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
							id="VerifyScrDialogSaveBtn"
							oncomplete="PF('VerifyScrDialog').hide();"
							process="@this,verifyScript" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							id="VerifyScrDialogCancelBtn"
							onclick="PF('VerifyScrDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</ui:define>
</ui:composition>

