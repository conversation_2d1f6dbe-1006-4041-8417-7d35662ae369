<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/editTemplate_new.xhtml">
	<ui:define name="insertEditScripts">
		<style>
		</style>
		<script>
			//<![CDATA[	  

			//]]>
		</script>
	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="文书类型维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{tbZwWritsortBean.saveAction}" update=":tabView"
					process="@this,:tabView:editForm:editGrid" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{tbZwWritsortBean.backAction}" update=":tabView"
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 编辑页面内容 -->
	<ui:define name="insertEditContent">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:220px;">
				<font color="red">*</font>
				<h:outputText value="文书编号：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="writCode"
					value="#{tbZwWritsortBean.tbZwWritsort.writCode}" maxlength="20"
					style="width: 180px;" />
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:220px;">
				<font color="red">*</font>
				<h:outputText value="文书名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="writName"
					value="#{tbZwWritsortBean.tbZwWritsort.writName}" maxlength="50"
					style="width: 180px;" />
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:220px;">
				<h:outputText value="文书字头：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText id="writShortname"
					value="#{tbZwWritsortBean.tbZwWritsort.writShortname}"
					maxlength="25" style="width: 180px;" />
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:220px;">
				<h:outputText value="报表模板：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<div style="width: 220px;">
					<p:inputText id="fkByRptTemplId" style="width: 180px;"
						value="#{tbZwWritsortBean.tbZwWritsort.fkByRptTemplId.rptnam}"
						readonly="true" onclick="initRptDialog()" />
					<p:remoteCommand name="initRptDialog" process="@this"
						action="#{tbZwWritsortBean.initRptDialog}"
						update="tsRptListDialog"
						oncomplete="PF('TsRptListDialog').show();" />
					<p:commandLink styleClass="ui-icon  ui-icon-trash"
						action="#{tbZwWritsortBean.clearRptTemp}" process="@this"
						style="position:relative;left:-10px;top:4px;float:right;"
						update="fkByRptTemplId" />
				</div>
			</p:column>
		</p:row>

	</ui:define>

	<ui:define name="insertOtherContents">
		<p:dialog id="tsRptListDialog" widgetVar="TsRptListDialog"
			header="报表模板" resizable="false" width="700" modal="true"
			maximizable="true">
			<p:panelGrid id="rptSearch">
				<p:row style=" border: none;">
					<p:column style="text-align: right;border: hidden;">
						<h:outputText value="系统类型：" />
					</p:column>
					<p:column style="border: hidden;">
						<p:selectOneMenu value="#{tbZwWritsortBean.paramType}"
							style="width: 140px;">
							<f:selectItems value="#{tbZwWritsortBean.systemTypeMap}" />
						</p:selectOneMenu>
					</p:column>
					<p:column style="border: hidden;">
						<h:outputText value="报表名称：" />
					</p:column>
					<p:column style="border: hidden;">
						<p:inputText maxlength="50"
							value="#{tbZwWritsortBean.searchRptName}" />
					</p:column>
					<p:column style="border: hidden;">
						<p:commandButton action="#{tbZwWritsortBean.searchRptAction}"
							value="查询" icon="ui-icon-search"
							process="@this,:tabView:editForm:rptSearch"
							update=":tabView:editForm:dataTable" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<p:dataTable var="itm" value="#{tbZwWritsortBean.searchRptList}"
				emptyMessage="没找到记录！" paginator="true" rows="10"
				selectionMode="single" rowKey="#{itm.rid}"
				paginatorPosition="bottom" rowIndexVar="R"
				selection="#{tbZwWritsortBean.searchRpt}"
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
				rowsPerPageTemplate="#{mgrbean.pageSize}" id="dataTable" lazy="true"
				currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
				<p:column headerText="报表名称" style="width:300px;">
					<h:outputText value="#{itm.rptnam}" />
				</p:column>
				<p:column headerText="报表编码" style="width:300px;">
					<h:outputText value="#{itm.rptCod}" />
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width:100%;text-align:center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check" id="confirmBtn"
							process="@this,tsRptListDialog"
							update=":tabView:editForm:fkByRptTemplId"
							action="#{tbZwWritsortBean.checkTemp}"
							oncomplete="PF('TsRptListDialog').hide();" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="closeBtn"
							onclick="PF('TsRptListDialog').hide();" process="@this"
							immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

	</ui:define>

</ui:composition>
