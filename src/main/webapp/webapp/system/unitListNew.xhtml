<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.system.web.UnitNewBean"-->
    <ui:param name="mgrbean" value="#{unitNewBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/unitNewEdit.xhtml"/>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="单位管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchInit}" update="dataTable, :tabView:mainForm:mainGrid"
					process="@this,searchUnitname,searchSortId,searchSubOrges,searchState,searchZone,socialCode" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;height: 35px;">
                <h:outputText value="行政区划所属地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 250px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneBsList}" zoneCodeNew="#{mgrbean.searchZoneCode}" zoneName="#{mgrbean.searchZoneName}" id="searchZone"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;width: 250px;">
                <p:inputText id="searchUnitname" value="#{mgrbean.searchUnitname}" maxlength="15"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:inputText style="width:200px;" id="socialCode" value="#{mgrbean.searchCreditCode}" maxlength="18" />
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 35px;">
                <h:outputText value="单位属性：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:selectOneMenu id="searchSortId" value="#{mgrbean.searchSortId}" style="width: 180px;">
                    <f:selectItem itemLabel="--全部--"/>
                    <f:selectItems value="#{mgrbean.bsSortMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;" rendered="#{mgrbean.ifSubOrg == 1 or mgrbean.ifSubOrg == '1'}">
                <h:outputText value="是否分支机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 250px;" rendered="#{mgrbean.ifSubOrg == 1 or mgrbean.ifSubOrg == '1'}">
                <p:selectManyCheckbox id="searchSubOrges" value="#{mgrbean.searchSubOrges}">
                    <f:selectItem itemLabel="是" itemValue="1"/>
                    <f:selectItem itemLabel="否" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="#{(mgrbean.ifSubOrg == 1 or mgrbean.ifSubOrg == '1')?1:3}">
                <p:selectManyCheckbox id="searchState" value="#{mgrbean.searchStates}">
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="行政区划所属地区" style="width: 200px;padding-left: 10px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="业务管辖地区" style="width: 200px;padding-left: 10px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="单位" style="padding-left: 3px;width: 300px;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="是否分支机构" style="width: 100px;text-align: center;" rendered="#{mgrbean.ifSubOrg == 1 or mgrbean.ifSubOrg == '1'}">
            <h:outputText value="#{itm[7] != null?(itm[7] == '0'?'否':'是'):''}" />
        </p:column>
        <p:column headerText="社会信用代码" style="padding-left: 3px;width: 180px;text-align: center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="单位属性" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="状态" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[6] =='0'?'停用':'启用'}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;">
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" update=":tabView" process="@this">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <p:resetInput target=":tabView:editForm:editGrid"/>
            </p:commandLink>
            <p:spacer width="5" />
            
            <p:commandLink value="停用" action="#{mgrbean.stopAction}" update="dataTable" process="@this" rendered="#{itm[6]=='1'}">
                <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="启用" action="#{mgrbean.startAction}" update="dataTable" process="@this" rendered="#{itm[6]!='1'}">
                <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{mgrbean.ifDel}"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}" update="dataTable" process="@this"
                           rendered="#{mgrbean.ifDel}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" />
        </p:column>
    </ui:define>

</ui:composition>











