<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdPublishNoticeQueryListBean}"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/system/tdPublishNoticeQueryView.xhtml"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <ui:param name="onfocus" value="false" />
        <script type="text/javascript">
            function direTop(){
                // 页面回到顶部
                document.body.scrollTop=document.documentElement.scrollTop=0;
            };
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 80px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知查阅"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="发布类型：" />
            </p:column>
            <p:column style="text-align:left;width:240px;">
                <p:outputPanel id="zonearea">
                    <div style="border-color: #ffffff;margin: 0px;padding: 0px;display: table-cell;" >
                        <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectTypeNames}"
                                                selectedIds="#{mgrbean.selectTypeIds}"
                                                simpleCodeList="#{mgrbean.typeList}"
                                                panelWidth="190" ifTree = "true"
                                                height="300"></zwx:SimpleCodeManyComp>
                    </div>
                </p:outputPanel>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="标题：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;width: 240px;" >
                <p:inputText value="#{mgrbean.searchTitle}" style="width: 180px;" maxlength="50" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="发布日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchBdate}" endDate="#{mgrbean.searchEdate}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItem itemValue="0" itemLabel="待阅"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="已阅"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="发布类型" style="text-align:center;width: 15%;" >
            <h:outputText value="#{itm[1]}" />
            <h:outputText value="（#{itm[3]}）" rendered="#{null ne itm[2] and 1 == itm[2] and null ne itm[3]}"/>
        </p:column>
        <p:column headerText="标题" style="width: 43%;">
            <h:outputLabel value="#{itm[4]}" id="titleId" styleClass="zwx-tooltip" />
            <p:tooltip  for="titleId" value="#{itm[4]}" style="max-width: 320px;"  />
        </p:column>
        <p:column headerText="是否需要回执" style="width: 7%;text-align:center;">
            <h:outputLabel value="#{null ne itm[9] and itm[9]== 1 ? '是' : '否' }"  />
        </p:column>
        <p:column headerText="发布日期" style="text-align:center;width: 10%;">
            <h:outputLabel value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="发布机构" style="width: 15%;">
            <h:outputText value="#{itm[6]}" />
        </p:column>
        <p:column headerText="状态" style="width:5%;text-align:center;">
            <h:outputLabel value="待阅" rendered="#{itm[7]==0}" style="color: red;"/>
            <h:outputLabel value="已阅" rendered="#{itm[7]==1}" style="color: green;"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="详情"  action="#{mgrbean.preView}" process="@this"  update=":tabView" resetValues="true" onclick="hideTooltips();direTop();">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>


</ui:composition>
