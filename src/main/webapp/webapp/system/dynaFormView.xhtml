<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
	
	<h:form id="viewForm">
		<p:panelGrid style="width:100%;height:100%;" id="dynaFormGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="#{mgrbean.formDef.formName}" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent; padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-close" update=":tabView"
						action="#{mgrbean.backAction}" process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<h:inputHidden id="formType" value="#{mgrbean.formType}"/>
		<h:inputHidden id="dataJson" value="#{mgrbean.dataJson}"/>
		<h:inputHidden id="html" value="#{mgrbean.html}"/>
		<h:inputHidden id="rows" value="#{mgrbean.formDef.rows}"/>
		<div id="defaultMsg" /> 
	
		<div id="dynaDiv">
			
		</div>
		
	</h:form>
	<script type="text/javascript"
		src="/resources/upload/js/jquery.popWin.min.js"></script>
	<script type="text/javascript"
		src="/resources/upload/js/jquery.uploadify.v2.1.0.js"></script>
	<script type="text/javascript" src="/resources/upload/js/swfobject.js"></script>
    <script type="text/javascript">
	//<![CDATA[
	    jQuery(function() {
	    	dyna_init();
	    	maxRow=parseInt(jQuery("#tabView\\:viewForm\\:rows").val());
	    });
	
	    function dyan_add_row(container, row, tpl) {
	    	var addHtml=Mustache.render(tpl, {
	    		row: row
	    	});
	    	jQuery(container).append(addHtml);
	    	dyna_dom_style_init();
	    }
	
	    function dyna_href_del(row) {
	    	var r=parseInt(jQuery("#tabView\\:viewForm\\:rows").val());
	    	if(r<=1) {
	    		alert("不允许删除！");
	    	}else {
	    		if(confirm("确定要删行吗？")) {
	    			jQuery("#tabView\\:viewForm\\:rows").val(r-1);
	    			jQuery("#subTableRowDiv"+row).remove();
	    		}
	    	}
	    }
	
	    function dyna_add_msg(msg) {
	    	jQuery("#defaultMsg").puigrowl('show', msg);
	    }
	
	    var maxRow;
	    var DataChildTpl;
	    function dyna_init() {
	    	jQuery("#dynaDiv").html(jQuery("#tabView\\:viewForm\\:html").val());
	    	dyna_dom_style_init();
	
	    	var formType=jQuery("#tabView\\:viewForm\\:formType").val();
	    	if((DataChildTpl == null || DataChildTpl=="")  && formType=='2') {
	    		DataChildTpl = jQuery("#DataChildTpl").html().replace(/(\/\/\<!\-\-)|(\/\/\-\->)/g,"");
	    	}
	    	
	    	var dataStr=jQuery("#tabView\\:viewForm\\:dataJson").val();
	    	if(dataStr != "") {
	    		var dataJSON=JSON.parse(dataStr);
	    		console.log(dataJSON);
	    		for(var prop in dataJSON) {
	    			if(typeof(jQuery("#"+prop))!="undefined") {
	    				if(jQuery("#"+prop).is('select')){//如果为下拉框，则需要用下拉的特殊赋值方式
	    					jQuery("#"+prop).puidropdown('selectValue', dataJSON[prop]);
	    				}else if(jQuery("#"+prop).is('span')){
	    					jQuery("#"+prop).text(dataJSON[prop]);
	    				}else {
	    					if(typeof(jQuery("#"+prop).attr("fieldTyp"))!="undefined" && jQuery("#"+prop).attr("fieldTyp") == "fj") {
	    						var fjId = jQuery("#"+prop).attr("id");
	    						//截取到Id中附件类控件的前缀
	    						var fjTemp = fjId.substring(0,fjId.lastIndexOf('NAME')); //这样就获取到了前面的字符串。
	    						jQuery("#" + fjTemp + "NAME").val(dataJSON[prop]);
	    						jQuery("#" + fjTemp + "_SPAN").text(dataJSON[prop]);

//	    						jQuery("#" + fjTemp + "_UP").css('display', 'none');
	    						jQuery("#" + fjTemp + "_DOWN").css('display', '');
//	    						jQuery("#" + fjTemp + "_DEL").css('display', '');
	    					}else{
	    						jQuery("#"+prop).val(dataJSON[prop]);
	    					}
	    				}
	    			}
	    		}
	    	}
	    	jQuery("#DYNA_BTN_TH").off().click(function() {
	    		dyan_add_row("#subTableDiv",maxRow, DataChildTpl);
	    		jQuery("#tabView\\:viewForm\\:rows").val(parseInt(jQuery("#tabView\\:viewForm\\:rows").val())+1);
	    		maxRow=maxRow+1;
	    	});
	    }
	    
	    
	    /**
		 * 下载
		 */
		function dynaDownLoadFile(filePutId) {
			if ('' != filePutId) {
				var path = jQuery("#" + filePutId + "PATH").val();
				var name = jQuery("#" + filePutId + "NAME").val();
				var url = '/DownLoad?path=' + path + '&name=' + name;
				url = encodeURI(url);
				var form = jQuery("<form>");
				form.attr('style', 'display:none');
				form.attr('target', '');
				form.attr('method', 'post');
				form.attr('encoding', 'multipart/form-data');
				form.attr('action', url);
				jQuery('body').append(form);
				form.submit();
				form.remove();
			}
		}
     //]]>
    </script>
    
</ui:composition>

