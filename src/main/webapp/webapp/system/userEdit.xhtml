<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
       <!-- 编辑页面的script -->
    <ui:define name="insertEditScripts">
        <h:outputScript library="js" name="namespace.js" />
	    <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            //<![CDATA[

            //控制是否提交职工信息
            function blockEmpInfo() {
                if(UserType.inputs[0].checked) {
                    PF('baseInfoBlock').hide();
                    PF('careerInfoBlock').hide();
                    PF('parttimeBlock').hide()
                } else {
                    PF('baseInfoBlock').show();
                    PF('careerInfoBlock').show();
                    PF('parttimeBlock').show();
                }
            }

            function handleUserSave(xhr, status, args) {
                if(args.validationFailed) {
                    blockEmpInfo();
                }
            }
            //]]>
            
        </script>

    </ui:define>

       <!-- 标题栏 -->
       <ui:define name="insertEditTitle">
           <p:row>
               <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                   <h:outputText value="用户管理"/>
               </p:column>
           </p:row>
       </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{userBean.saveAction}"
                                 process="@this,:tabView:editForm:editGrid,:tabView:editForm:baseInfoGrid,:tabView:editForm:careerInfoGrid" update=":tabView"
                                 oncomplete="handleUserSave(xhr, status, args);" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{userBean.backAction}"
                                 update=":tabView" immediate="true" />
				<p:commandButton value="密码初始化" icon="ui-icon-check" id="pwdInit" action="#{userBean.pwdInitAction}"
                                 process="@this" rendered="#{userBean.rid != null}">
					<p:confirm header="消息确认框" message="确定要密码初始化吗？"  icon="ui-icon-alert" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertEditContent">
        <p:row>
            <p:column style="text-align:right;padding-right:0px;width:180px;">
                <font color="red">*</font>
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:280px;">
                <zwx:ZoneSingleNewComp zoneList="#{userBean.zoneList}"  zoneCodeNew="#{userBean.editZoneCode}" zoneName="#{userBean.editZoneName}"
                                       id="editZone" onchange="onNodeSelect()"  zoneType="#{userBean.editZoneType}"/>
                <p:remoteCommand name="onNodeSelect" action="#{userBean.onNodeSelect}" process="@this,editZone"
                                 update=":tabView:editForm:editUnitListOneMenu,:tabView:editForm:editOfficeListOneMenu"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <font color="red">*</font>
                <h:outputText value="单位："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectOneMenu id="editUnitListOneMenu" value="#{userBean.editUnitId}" style="width: 180px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{userBean.editUnitMap}"/>
                    <p:ajax event="change" update="editOfficeListOneMenu" process="@this" listener="#{userBean.onUnitChange}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="科室："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:280px;">
                <p:selectOneMenu id="editOfficeListOneMenu" value="#{userBean.editOfficeId}" style="width: 180px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{userBean.editOfficeMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <font color="red">*</font>
                <h:outputText value="用户类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <h:panelGrid columns="4" style="border-color: #ffffff">
                    <p:selectOneRadio id="userType" value="#{userBean.tsUserInfo.userType}" style="width: 180px;" onchange="blockEmpInfo()" widgetVar="UserType">
                        <f:selectItem itemLabel="内部用户" itemValue="1"/>
                        <f:selectItem itemLabel="外部用户" itemValue="2"/>
                    </p:selectOneRadio>

                    <p:spacer width="5" rendered="#{userBean.ifAdmin}"/>
                    <p:selectBooleanCheckbox id="useradmin" value="#{userBean.tsUserInfo.useradmin}" rendered="#{userBean.ifAdmin}"/>
                    <h:outputText value="管理员" rendered="#{userBean.ifAdmin}"/>
                </h:panelGrid>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <font color="red">*</font>
                <h:outputText value="用户登录名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width:280px;">
                <p:inputText id="userNo" value="#{userBean.tsUserInfo.userNo}" maxlength="15"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <font color="red">*</font>
                <h:outputText value="用户姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="username" value="#{userBean.tsUserInfo.username}" maxlength="15"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <font color="red">*</font>
                <h:outputText value="手机号码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="mbNum" value="#{userBean.tsUserInfo.mbNum}" maxlength="13"
                        required="true" requiredMessage="手机号码不允许为空！"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel for="rmk" value="备注：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:inputText id="rmk" value="#{userBean.tsUserInfo.rmk}" maxlength="50" style="width: 80%"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <font color="red">*</font>
                <h:outputText value="邮箱："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3">
                <p:inputText id="email" value="#{userBean.tsUserInfo.email}"
                required="true" requiredMessage="邮箱不允许为空！"/>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:fieldset id="baseInfoField" legend="人员信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
           <p:remoteCommand name="findFlowByIdc" action="#{mgrbean.findFlowByIdc}" process="@this,:tabView:editForm:baseInfoGrid" update=":tabView">
			</p:remoteCommand>
            <p:panelGrid style="width:100%;" id="baseInfoGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width: 180px;">
                        <h:outputText value="身份证号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;width: 280px;">
                        <p:inputText id="idc" value="#{userBean.tsUserInfo.tbSysEmp.idc}" onblur="findFlowByIdc();" maxlength="18"/>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width: 180px;">
                        <font color="red">*</font>
                        <h:outputText value="性别："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneRadio id="empSex" value="#{userBean.tsUserInfo.tbSysEmp.empSex}" style="width: 100px;">
                            <f:selectItem itemLabel="男" itemValue="1"/>
                            <f:selectItem itemLabel="女" itemValue="2"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <font color="red">*</font>
                        <h:outputText value="民族："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu id="empNation" value="#{userBean.tsUserInfo.tbSysEmp.empNation}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.empNationMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="出生日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:calendar id="birthday" value="#{userBean.tsUserInfo.tbSysEmp.birthday}" size="15" navigator="true" yearRange="c-100:c"
                                    converterMessage="出生日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="曾用名："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:inputText id="usedName" value="#{userBean.tsUserInfo.tbSysEmp.usedName}" maxlength="25"/>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="学历："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu id="eduDegree" value="#{userBean.tsUserInfo.tbSysEmp.eduDegree}" style="width: 120px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.eduDegreeMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>

                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="政治面貌："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu id="politics" value="#{userBean.tsUserInfo.tbSysEmp.politics}" style="width: 146px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.politicsMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="宗教信仰："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu id="religion" value="#{userBean.tsUserInfo.tbSysEmp.religion}" style="width: 120px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.religionMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="婚姻状况："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;" colspan="3">
                        <p:selectOneMenu id="maritalStatus" value="#{userBean.tsUserInfo.tbSysEmp.maritalStatus}" style="width: 146px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.maritalStatusMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputLabel value="电子签名："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;" colspan="3">
                        <h:panelGroup id="uploadGroup">
                            <p:commandButton value="上传" oncomplete="PF('fileUIdVar').show();"
                                             rendered="#{userBean.tsUserInfo.tbSysEmp.psnSign==null}"
                                             process="@this" update="fileUId"/>
                            <h:panelGroup rendered="#{userBean.tsUserInfo.tbSysEmp.psnSign!=null}">
                                <h:graphicImage url="/webFile#{userBean.tsUserInfo.tbSysEmp.psnSign}" width="150px" height="60px" style="margin-bottom: -10px;"/>
                                <p:spacer width="5"/>
                                <p:commandButton value="删除" update="uploadGroup"  process="@this"
                                                 action="#{userBean.deleteSysEmpPsnsign}" />
                            </h:panelGroup>
                            <h:outputText style="color:blue;" value="　[推荐像素：150*60]" />
                        </h:panelGroup>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <!-- 文件上传 -->
            <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false" modal="true">
                <p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="50"
                              fileUploadListener="#{userBean.handleFileUpload}"
                              fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                              label="选择文件" uploadLabel="上传" cancelLabel="取消"
                              sizeLimit="1048576" invalidSizeMessage="文件大小不能超过2M!"
                              validatorMessage="上传出错啦，重新上传！"
                              process="@this" update="uploadGroup"
                              mode="advanced" dragDropSupport="true"
                              invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png类型文件"
							  allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
            </p:dialog>
        </p:fieldset>

        <p:fieldset id="careerInfoField" legend="职工信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid style="width:100%;" id="careerInfoGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <font color="red">*</font>
                        <h:outputText value="职工编号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;width:280px;">
                        <p:inputText id="empCode" value="#{userBean.tsUserInfo.tbSysEmp.empCode}" maxlength="10"/>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="是否领导："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneRadio id="isLeader" value="#{userBean.tsUserInfo.tbSysEmp.isLeader}" style="width: 100px;">
                            <f:selectItem itemLabel="是" itemValue="1"/>
                            <f:selectItem itemLabel="否" itemValue="0"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="职务："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;width:280px;">
                        <p:selectOneMenu id="duty" value="#{userBean.tsUserInfo.tbSysEmp.duty}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.dutyMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="职称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneMenu id="position" value="#{userBean.tsUserInfo.tbSysEmp.position}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.positionMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="入职日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;width:280px;">
                        <p:calendar id="workDay" value="#{userBean.tsUserInfo.tbSysEmp.workDay}" size="11" navigator="true" yearRange="c-100:c"
                                    converterMessage="入职日期，格式输入不正确！" pattern="yyyy-MM-dd" showButtonPanel="true" showOtherMonths="true"/>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="转正日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:calendar id="regularDay" value="#{userBean.tsUserInfo.tbSysEmp.regularDay}" size="11" navigator="true" yearRange="c-100:c"
                                    converterMessage="转正日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="人员属性："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;width:280px;">
                        <p:selectOneMenu id="psnPropId" value="#{userBean.psnPropId}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItems value="#{userBean.psnPropIdList}"/>
                        </p:selectOneMenu>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="在职状态："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <p:selectOneRadio id="onduty" value="#{userBean.tsUserInfo.tbSysEmp.onduty}" style="width: 180px;">
                            <f:selectItem itemLabel="在职" itemValue="1"/>
                            <f:selectItem itemLabel="离职" itemValue="0"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:180px;">
                        <h:outputText value="序号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;" colspan="3">
                        <p:inputText id="num" value="#{userBean.tsUserInfo.tbSysEmp.num}" size="4" maxlength="4" converterMessage="序号只能输入数字"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>

        <p:blockUI block="baseInfoGrid" widgetVar="baseInfoBlock" />
        <p:blockUI block="careerInfoGrid" widgetVar="careerInfoBlock" />

        <p:fieldset id="parttimeFiltset" legend="兼职信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <table style="width: 100%">
                <tr>
                    <td>
                        <p:outputPanel styleClass="zwx_toobar_42">
                            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                <p:commandButton icon="ui-icon-plus" value="添加" process="@this,editOfficeListOneMenu" action="#{userBean.addParttimeinit}"
                                                 update="parttimeInfoListDialog"
                                                 oncomplete="PF('ParttimeInfoListDialog').show()"/>
                            </h:panelGrid>
                        </p:outputPanel>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm" value="#{userBean.tsParttimeInfoList}" id="parttimeInfoTable" emptyMessage="暂无兼职信息">
                <p:column headerText="兼职科室" style="width:250px;text-align: center">
                    <h:outputLabel value="#{itm.tsOffice.officename}"/>
                </p:column>
                <p:column headerText="兼职职务" style="width:250px;text-align: center">
                    <h:outputLabel value="#{itm.dutyId.codeName}"/>
                </p:column>
                <p:column headerText="是否领导" style="width:100px;text-align: center">
                    <h:outputLabel rendered="#{itm.isLeader == '0'}" value="否" />
                    <h:outputLabel rendered="#{itm.isLeader == '1'}" value="是" />
                </p:column>
                <p:column headerText="操作" style="padding-left: 3px;">
                    <p:commandLink value="修改" process="@this" action="#{userBean.modifyParttimeAction}"
                                    update=":tabView:editForm:parttimeInfoListDialog" oncomplete="PF('ParttimeInfoListDialog').show()">
                        <f:setPropertyActionListener value="#{itm}" target="#{userBean.tempParttimeInfo}"/>
                    </p:commandLink>
                    <p:spacer width="5" />
                    <p:commandLink value="删除" process="@this"
                                   action="#{userBean.deleteParttimeAction}" update="parttimeInfoTable">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        <f:setPropertyActionListener value="#{itm}" target="#{userBean.tempParttimeInfo}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
            <p:dialog id="parttimeInfoListDialog" widgetVar="ParttimeInfoListDialog" header="兼职信息" resizable="false"
                      width="500" modal="true">
                <p:panelGrid id="parttimePanel" style="width: 100%">
                    <p:row>
                        <p:column style="width: 30%;padding-right:3px;text-align: right">
                            <p:outputLabel value="*" style="color: red"/>
                            <p:outputLabel value="兼职科室：" />
                        </p:column>
                        <p:column  style="text-align:left;padding-left:5px;">
                            <p:selectOneMenu value="#{userBean.parttimeOfficeId}" style="width: 180px;">
                                <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                <f:selectItems value="#{userBean.parttimeOfficeMap}"/>
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;width:30%;">
                            <p:outputLabel value="*" style="color: red"/>
                            <h:outputText value="兼职职务："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:8px;width:280px;">
                            <p:selectOneMenu value="#{userBean.parttimeDutyId}" style="width: 180px;">
                                <f:selectItem itemLabel="--请选择--" itemValue=""/>
                                <f:selectItems value="#{userBean.dutyMap}"/>
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;width:30%">
                            <h:outputText value="是否领导："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:5px;">
                            <p:selectOneRadio value="#{userBean.tsParttimeInfo.isLeader}" style="width: 120px;">
                                <f:selectItem itemLabel="是" itemValue="1"/>
                                <f:selectItem itemLabel="否" itemValue="0"/>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                </p:panelGrid>
                <f:facet name="footer">
                    <h:panelGrid style="width: 100%;text-align: center;">
                        <h:panelGroup>
                            <p:commandButton value="确定" action="#{userBean.saveParttimeAction}"
                                             process="@this,parttimePanel" update="parttimeInfoTable"/>
                            <p:spacer width="5"/>
                            <p:commandButton value="取消" onclick="PF('ParttimeInfoListDialog').hide();"
                                             type="button"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
            </p:dialog>
        </p:fieldset>

        <p:blockUI block="parttimeFiltset" widgetVar="parttimeBlock" />
    </ui:define>
</ui:composition>

