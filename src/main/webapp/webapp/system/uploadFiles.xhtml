<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title><span id="dialogSelfTitle">#{uploadFilesBean.title}</span></title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<style>
.ui-orderlist .ui-orderlist-list {
	list-style-type: none;
	margin: 0;
	padding: 0;
	overflow: auto;
	height: 280px;
	width: 450px;
}

.orderList {
	padding-left: 13px;
}
</style>
	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:form id="mainForm">
			<div class="container">
				<p:dataTable id="dataTable" value="#{uploadFilesBean.dataList}"
					var="itm" emptyMessage="没有数据！" paginator="true" rows="15"
					lazy="false" paginatorPosition="top" rowIndexVar="R"
					paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
					currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
					<p:column headerText="序号" style="width:60px;text-align:center">
						<h:outputText value="#{itm.xh}" />
					</p:column>
					<p:column headerText="文件名">
						<h:outputText value="#{itm.fileName}" />
					</p:column>
					<p:column headerText="操作" style="width:60px;">
						<p:commandLink value="删除" action="#{uploadFilesBean.deleteAction}"
							update="dataTable" process="@this">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{uploadFilesBean.filePo}"
								value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</div>

			<div class="line-cutoff">&#160;</div>
			<p:outputPanel style="text-align:center">
				<p:commandButton value="上传" icon="ui-icon-folder-open"
					id="uploadBtn" type="button" onclick="PF('fileUIdVar').show();" />
				<p:spacer width="5" />
				<p:commandButton value="排序" icon="ui-icon-transferthick-e-w"
					id="orderBtn" update="orderDialog" process="@this,dataTable"
					actionListener="#{uploadFilesBean.initOrderAction}"
					 />
				<p:spacer width="5" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
					id="submitBtn" action="#{uploadFilesBean.confirmAction}" process="@this,dataTable"/>
				<p:spacer width="5" />
				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
					action="#{uploadFilesBean.dialogClose}" widgetVar="BackBtn"
					process="@this" style="display:none" />
			</p:outputPanel>

			<p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId"
				resizable="false" modal="true">
				<p:fileUpload requiredMessage="请选择要上传的文件！" auto="true"
					invalidFileMessage="请上传格式为#{uploadFilesBean.fileTypes}的文件！"
					fileUploadListener="#{uploadFilesBean.handleFileUpload}"
					fileLimit="1" fileLimitMessage="一次最多只能上传1个文件！" label="选择文件"
					invalidSizeMessage="文件大小不能超过#{uploadFilesBean.fileSize}M!"
					validatorMessage="网络异常，请重新上传！" style="width:700px;"
					previewWidth="120" cancelLabel="取消" uploadLabel="上传"
					allowTypes="#{uploadFilesBean.allowTypes}"
					update="dataTable,uploadPF" dragDropSupport="true" id="uploadPF"
					onstart="zwx_loading_start();" oncomplete="zwx_loading_stop()"
					mode="advanced" sizeLimit="#{uploadFilesBean.allowSize}" />
			</p:dialog>

			<!-- 排序题目  -->
			<p:dialog id="orderDialog" header="文件排序" widgetVar="OrderDialog"
				resizable="false" width="550" height="330" modal="true">
				<p:orderList value="#{uploadFilesBean.dataList}" id="fileList"
					var="file" style="width:480px;text-align:center;"
					styleClass="orderList" itemValue="#{file}"
					converter="system.FilePoOrderConvert" controlsLocation="right">
					<f:facet name="caption">文件名称</f:facet>
					<p:column style="text-align:left;bottom:2px;padding-left:8px;">
						<h:outputText value="#{file.fileName}" />
					</p:column>
				</p:orderList>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check"
								action="#{uploadFilesBean.orderSaveAction}" id="orderSaveBtn"
								process="@this,fileList" update=":mainForm:dataTable"
								oncomplete="PF('OrderDialog').hide();" />
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close"
								id="orderBackBtn" onclick="PF('OrderDialog').hide();"
								immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
		<script type="text/javascript">
			//<![CDATA[

			//]]>
		</script>
		<style type="text/css">
body {
	
}

.container {
	width: 100%;
	height: 425px;
}

.line-cutoff {
	height: 20px;
	width: 850px;
	border-bottom: 1px solid #A6C9E2;
	clear: both;
	margin-bottom: 15px;
}
</style>
	</h:body>
</f:view>
</html>
