<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{genQueBean}" />
	<!-- 是否启用光标定位功能 -->
	<ui:param name="onfocus" value="true" />
	<!-- 编辑页面 -->
	<ui:param name="editPage" value="/webapp/system/probLibEdit.xhtml" />
	<!-- 编辑页面 -->
	<ui:param name="otherPage" value="/webapp/system/probSubjectEdit.xhtml" />
	<ui:define name="insertScripts">
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="问卷管理" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{genQueBean.searchAction}" update="dataTable"
					process="@this,mainGrid" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
					action="#{genQueBean.addInitAction}" update=":tabView"
					process="@this">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:120px;">
				<h:outputText value="问卷类别：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;width:220px;">
				<p:selectOneMenu value="#{genQueBean.searchTypeId}">
					<f:selectItem itemValue="" itemLabel="--全部--" />
					<f:selectItems value="#{genQueBean.typeList}" />
				</p:selectOneMenu>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:120px;">
				<h:outputText value="问卷名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;width:220px;">
				<p:inputText value="#{genQueBean.searchQueName}" maxlength="15" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:120px;">
				<h:outputText value="状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;">
				<p:selectManyCheckbox value="#{genQueBean.searchState}">
					<f:selectItem itemLabel="启用" itemValue="1" />
					<f:selectItem itemLabel="停用" itemValue="0" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>

	<ui:define name="insertDataTable">
		<p:column headerText="序号" style="width: 40px;text-align: center;">
			<h:outputText value="#{R+1}">
			</h:outputText>
		</p:column>
		<p:column headerText="单位名称" style="width: 120px;">
			<h:outputText value="#{itm.tsUnitByUnitId.unitSimpname}" />
		</p:column>
		<p:column headerText="问卷类别" style="width: 280px;">
			<h:outputText value="#{itm.tsSimpleCodeByQuestSortid.codeName}" />
		</p:column>
		<p:column headerText="问卷名称" style="width: 300px;">
			<h:outputText value="#{itm.questName}" />
		</p:column>
		<p:column headerText="状态" style="width: 50px;text-align: center;">
			<h:outputText value="#{itm.state==1?'启用':'停用'}" />
		</p:column>
		<p:column headerText="操作">
			<p:commandLink value="生成" action="#{genQueBean.genHtmlAction}"
				process="@this">
				<f:setPropertyActionListener target="#{genQueBean.ifEdit}" value="0" />
				<f:setPropertyActionListener value="#{itm.rid}"
					target="#{genQueBean.rid}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="Web版预览"
				action="#{genQueBean.forwardPreviewAction}" process="@this">
				<f:setPropertyActionListener value="#{itm.htmlName}"
					target="#{genQueBean.htmlName}" />
				<f:setPropertyActionListener target="#{genQueBean.previewTag}"
					value="1" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="移动版预览"
				action="#{genQueBean.forwardPreviewAction}" process="@this">
				<f:setPropertyActionListener value="#{itm.htmlName}"
					target="#{genQueBean.htmlName}" />
				<f:setPropertyActionListener target="#{genQueBean.previewTag}"
					value="2" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="修改" action="#{genQueBean.modInitAction}"
				process="@this" update=":tabView">
				<p:resetInput target=":tabView:editForm:editGrid" />
				<f:setPropertyActionListener value="#{itm.rid}"
					target="#{genQueBean.rid}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="启用" action="#{genQueBean.updateState}"
				process="@this" update="dataTable" rendered="#{itm.state==0}">
				<p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener value="#{itm.rid}"
					target="#{genQueBean.rid}" />
				<f:setPropertyActionListener value="1" target="#{genQueBean.state}" />
			</p:commandLink>
			<p:commandLink value="停用" action="#{genQueBean.updateState}"
				process="@this" update="dataTable" rendered="#{itm.state==1}">
				<p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener value="#{itm.rid}"
					target="#{genQueBean.rid}" />
				<f:setPropertyActionListener value="0" target="#{genQueBean.state}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="删除" action="#{genQueBean.deleteAction}"
				process="@this" update="dataTable" rendered="false">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener value="#{itm.rid}"
					target="#{genQueBean.rid}" />
			</p:commandLink>
		</p:column>
	</ui:define>
</ui:composition>