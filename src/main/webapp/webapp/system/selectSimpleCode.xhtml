<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>码表数据选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet library="css" name="ui-tabs.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <style type="text/css">
            .ui-picklist .ui-picklist-list{
                text-align:left;
                height: 340px;
                width: 400px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
        </style>
    </h:head>

    <h:body style="overflow:hidden;">
        <h:form id="artForm">
            <table width="100%">
	            <tr>
	                <td width="100" style="text-align: right;padding-right: 3px;font-size:13px !important;color:#222222">
	                	系统类型：
	                	
	                </td>
	                <td style="text-align: left;padding-right: 3px">
	                    <p:selectOneMenu  value="#{selectCodeTypeBean.selectedSysTypeId}" style="width: 160px;">
	                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
	                        <f:selectItems value="#{selectCodeTypeBean.systemTypeList}" var="itm"
	                        itemLabel="#{itm.typeCN}" itemValue="#{itm.typeNo}"/>
	                        <p:ajax event="change" update="pickList,codeTypeMenu" process="@this,pickList" listener="#{selectCodeTypeBean.onSysChange}"/>
	                    </p:selectOneMenu>
	                </td>
	                <td width="100" style="text-align: right;padding-right: 3px;font-size:13px !important;color:#222222">
	                	码表类型：
	                </td>
	                <td style="text-align: left;padding-right: 3px">
	                    <p:selectOneMenu id="codeTypeMenu" value="#{selectCodeTypeBean.selectedCodeTypeId}" style="width: 160px;">
	                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
	                        <f:selectItems value="#{selectCodeTypeBean.showTypeCodeList}" var="itm"
	                        itemLabel="#{itm.codeTypeDesc}" itemValue="#{itm.rid}"/>
	                        <p:ajax event="change" update="pickList" process="@this,pickList" listener="#{selectCodeTypeBean.onCodeTypeChange}"/>
	                    </p:selectOneMenu>
	                </td>
	            </tr>
	        </table> 
            <p:pickList id="pickList" value="#{selectCodeTypeBean.dualListModel}" var="unit" 
                        itemValue="#{unit}" itemLabel="#{unit[2]}" converter="system.SimpleCodeObjConvert"
                        showSourceControls="false" showTargetControls="false" showCheckbox="true"
                        showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains" 
                        effect="drop" >
 
                <f:facet name="sourceCaption">可选码表数据</f:facet>
                <f:facet name="targetCaption">已选码表数据</f:facet>

                <p:column style="width:30%;text-align: left;left-padding:#{unit[3]*2}px">#{unit[0]}</p:column>
                <p:column style="width:30%;text-align: left;left-padding:#{unit[3]*2}px">#{unit[1]}</p:column>
                <p:column style="width:40%;text-align: left;left-padding:#{unit[3]*2}px">#{unit[2]}</p:column>
            </p:pickList>
            <br/> 
            <p:outputPanel style="text-align:center">
                <p:commandButton value="确定" icon="ui-icon-check" id="saveBtn" action="#{selectCodeTypeBean.selectAction}" process="@form" />
                <p:spacer width="5" />
                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectCodeTypeBean.dialogClose}" process="@this"/>
            </p:outputPanel>
        </h:form>
    </h:body>
</f:view>
</html>
