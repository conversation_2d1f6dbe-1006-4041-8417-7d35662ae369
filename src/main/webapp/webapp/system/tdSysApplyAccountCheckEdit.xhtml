<ui:composition xmlns="http://www.w3.org/1999/xhtml"
       xmlns:ui="http://java.sun.com/jsf/facelets"
       xmlns:h="http://java.sun.com/jsf/html"
       xmlns:f="http://java.sun.com/jsf/core"
       xmlns:p="http://primefaces.org/ui"
       xmlns:c="http://java.sun.com/jsp/jstl/core"
       xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
       template="/WEB-INF/templates/system/editTemplate.xhtml">
       <ui:define name="insertEditScripts">
       		<style type="text/css">
       			.checkboxVisible .ui-state-disabled{
       				opacity: 1;
       			}
				.icon-alert{
					background-image: url(/resources/images/alert-tip.png) !important;
					background-size: 12px 12px;
					margin-left: 3px;
					margin-top: -6px !important;
				}
				.ui-state-hover .icon-alert{
					background-image: url(/resources/images/alert-tip-w.png) !important;
					background-size: 12px 12px;
					margin-left: 3px;
					margin-top: -6px !important;
				}
				.ui-state-hover .icon-alert{
					color : #fff;
				}
			</style>
       </ui:define>
       
       <ui:define name="insertEditTitle">
       		<p:row>
	            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
	                <h:outputText value="注册账号审核" />
	            </p:column>
	        </p:row>
       </ui:define>
       <ui:define name="insertEditButtons">
       		<p:outputPanel styleClass="zwx_toobar_42">
			    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
			        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="审核通过" icon="ui-icon-check"
						action="#{mgrbean.beforeSaveAction}"
						process="@this,:tabView:editForm" rendered="#{mgrbean.applyAccount.stateMark==0}">
					</p:commandButton>
					<p:commandButton value="退回" icon="ui-icon-cancel"
						 process="@this" oncomplete="PF('ReasonDialog').show();"
						 update="reasonDialog" rendered="#{mgrbean.applyAccount.stateMark==0}">
					</p:commandButton>
					<p:commandButton value="退回原因" icon="icon-alert" style="color:red;"
									 oncomplete="PF('SysApplyAccountReasonDialog').show();"
									 process="@this" rendered="#{mgrbean.applyAccount.stateMark==2}">
					</p:commandButton>
					<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn3"
						action="#{mgrbean.backAction}" process="@this"
						update=":tabView" />
					<p:inputText style="visibility: hidden;width: 0"/>
			    </h:panelGrid>
			</p:outputPanel>
			<p:confirmDialog message="确定要审核通过吗？" header="消息确认框" widgetVar="ReviewConfirmDialog" >
		       	<p:commandButton value="确定" action="#{mgrbean.saveAction}" 
		        	update=":tabView" icon="ui-icon-check"
		        	oncomplete="PF('ReviewConfirmDialog').hide();"/>
		       	<p:commandButton value="取消" icon="ui-icon-close" 
		        	onclick="PF('ReviewConfirmDialog').hide();" 
		        	type="button"/>
		    </p:confirmDialog>
       </ui:define>
       <ui:define name="insertOtherContents">
       		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
			   	<f:facet name="header">
			        <p:row>
			            <p:column colspan="6" style="text-align:left;height: 20px;">
			                <p:outputLabel value="单位信息"/>
			            </p:column>
			        </p:row>
			    </f:facet>
			    <p:row>
			        <p:column style="text-align:right;padding-right:3px;height: 30px;width:220px;">
			            <p:outputLabel value="行政区划所属地区：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.zoneName}"></p:outputLabel>
		        	</p:column>
		        </p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 30px;">
						<p:outputLabel value="社会信用代码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;">
						<p:outputLabel value="#{mgrbean.applyAccount.creditCode}"></p:outputLabel>
					</p:column>
				</p:row>
		        <p:row>
		        	<p:column style="text-align:right;padding-right:3px;height: 30px;">
			            <p:outputLabel value="单位名称：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.applyAccount.unitname}"></p:outputLabel>
		        	</p:column>
		        </p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height: 30px;">
						<p:outputLabel value="是否分支机构：" />
					</p:column>
					<p:column style="text-align:left;padding-left:6px;">
						<p:outputLabel value="是" rendered="#{mgrbean.applyAccount.ifSubOrg==1}"></p:outputLabel>
						<p:outputLabel value="否" rendered="#{mgrbean.applyAccount.ifSubOrg!=1}"></p:outputLabel>
					</p:column>
				</p:row>
			    <p:row>
			        <p:column style="text-align:right;padding-right:3px;height: 30px;">
			            <p:outputLabel value="单位联系电话：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.applyAccount.unitTel}"></p:outputLabel>
		        	</p:column>
		        </p:row>
		        <p:row>
		        	<p:column style="text-align:right;padding-right:3px;height: 30px;">
			            <p:outputLabel value="单位属性：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.sortNames}"></p:outputLabel>
		        	</p:column>
			    </p:row>
			    <p:row>
			    	<p:column style="text-align:right;padding-right:3px;height: 30px;width:150px;">
			            <p:outputLabel value="申请材料：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:dataTable value="#{mgrbean.annexs}" var="annex" emptyMessage="未查询到数据！">
			        		<p:column headerText="证书资料" style="width:320px;">
			        			<p:outputLabel value="#{annex.fkByAnnexId.annexName}"></p:outputLabel>
			        		</p:column>
			        		<p:column headerText="操作">
			        			<p:commandLink value="查看" onclick="window.open('/webFile/#{annex.annexPath}')"/>
			        		</p:column>
			        	</p:dataTable>
		        	</p:column>
			    </p:row>
		    </p:panelGrid>
		    <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
			   	<f:facet name="header">
			        <p:row>
			            <p:column colspan="6" style="text-align:left;height: 20px;">
			                <p:outputLabel value="用户信息"/>
			            </p:column>
			        </p:row>
			    </f:facet>
			    <p:row>
			        <p:column style="text-align:right;padding-right:3px;height: 30px;width:220px;">
			            <p:outputLabel value="用户姓名：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.applyAccount.username}"></p:outputLabel>
		        	</p:column>
		        </p:row>
			    <p:row>
		        	<p:column style="text-align:right;padding-right:3px;height: 30px;">
			            <p:outputLabel value="手机号码：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.applyAccount.mobileNum}"></p:outputLabel>
		        	</p:column>
		        </p:row>
			    <p:row>
		        	<p:column style="text-align:right;padding-right:3px;height: 30px;">
			            <p:outputLabel value="身份证号：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.applyAccount.tempIdc}"></p:outputLabel>
		        	</p:column>
			    </p:row>
		    </p:panelGrid>
		    <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
			   	<f:facet name="header">
			        <p:row>
			            <p:column colspan="6" style="text-align:left;height: 20px;">
			                <p:outputLabel value="账号信息"/>
			            </p:column>
			        </p:row>
			    </f:facet>
			    <p:row>
			        <p:column style="text-align:right;padding-right:3px;height: 30px;width:220px;">
			            <p:outputLabel value="账号名称：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<p:outputLabel value="#{mgrbean.applyAccount.userNo}"></p:outputLabel>
		        	</p:column>
		        </p:row>
			    <p:row>
		        	<p:column style="text-align:right;padding-right:3px;height: 30px;">
		        		<h:outputText value="*" style="color: red;" rendered="#{mgrbean.applyAccount.stateMark!=1}"/>
			            <p:outputLabel value="账号有效期：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
			        	<h:outputLabel value="#{mgrbean.validBegDate}">
			                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			            </h:outputLabel>
						~
						<p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
									showOtherMonths="true" size="11" navigator="true"
									yearRange="c:c+10" converterMessage="结束日期，格式输入不正确！"
									showButtonPanel="true" mindate="new Date()"
									value="#{mgrbean.validEndDate}" 
									rendered="#{mgrbean.applyAccount.stateMark==0}"/>
						<h:outputLabel value="#{mgrbean.validEndDate}" rendered="#{mgrbean.applyAccount.stateMark==1}">
			                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
			            </h:outputLabel>
		        	</p:column>
			    </p:row>
			    <p:row>
			    	<p:column style="text-align:right;padding-right:3px;height: 30px;">
			            <p:outputLabel value="角色授权：" />
			        </p:column>
			        <p:column style="text-align:left;padding-left:6px;">
						<p:outputLabel value="#{mgrbean.allRoleName}"/>
		        	</p:column>
			    </p:row>
		    </p:panelGrid>
       		<p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
				height="300" header="退回原因" resizable="false" modal="true">
				<h:inputText
					style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
				<p:inputTextarea value="#{mgrbean.backRsn}"
					style="resize:none;width:97%;height:95%;" autoResize="false"
					id="reasonContent" maxlength="50"/>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: right;">
						<h:panelGroup>
							<p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
								process="@this" immediate="true" />
							<p:spacer width="5" />
							<p:commandButton value="确定" styleClass="submit_btn"
								process="@this,reasonContent"
								action="#{mgrbean.returnAction}" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
		   <p:dialog id="sysApplyAccountReasonDialog" widgetVar="SysApplyAccountReasonDialog" width="500"
					 height="300" header="退回原因" resizable="false" modal="true">
			   <h:inputText
					   style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
			   <p:inputTextarea value="#{mgrbean.applyAccount == null ? '' : mgrbean.applyAccount.backRsn}"
								style="resize:none;width:97%;height:95%;" readonly="true" autoResize="false"
								maxlength="50"/>
		   </p:dialog>
       </ui:define>
</ui:composition>
