<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
		    xmlns:ui="http://java.sun.com/jsf/facelets" 
		    xmlns:f="http://java.sun.com/jsf/core" 
		    xmlns:h="http://java.sun.com/jsf/html" 
		    xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title>发送消息</title>
		<style type="text/css">
.ui-picklist .ui-picklist-list {
	text-align: left;
	height: 300px;
	width: 300px;
	overflow: auto;
}

.ui-picklist .ui-picklist-filter {
	padding-right: 0px;
	width: 98%;
}

.msgBtnClass {
	width:64px;
	height:64px;
	background-image: url("/resources/images/msg.png");
	background-size:64px 64px; 
}
</style>
        <script type="text/javascript">
        //<![CDATA[
        function selectComp() {
        		var arr = new Array();
        		arr[0] =document.getElementById("personForm:selectedManIds").value;
        		arr[1] =document.getElementById("personForm:selectedManNames").value;
        		 window.returnValue=arr;
                 window.close();
        }           
        //]]>
        </script>
	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css"/>
		<h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:form id="msgForm">
			<table style="width: 100%; height: 380px;margin: 0px;padding: 0px;">
				<tr	style="height: 100%;" valign="top">
					<td style="width: 260px;">
						<p:accordionPanel style="width:260px;height:100%;" id="conPanel" >
	                        <p:tab title="按科室">
									<p:tree value="#{msgDialogSendBean.userOfficeTreeNode}" var="node" selectionMode="single" id="userOfficeTree" 
											style="width: 200px;height: 320px;overflow-y: auto;">
											
			                            <p:ajax event="select"  update=":msgForm:userPickList"  listener="#{msgDialogSendBean.onOfficeNodeSelect}" />
			                                    														
										<p:treeNode>
											<h:outputText value="#{node.officename}" />
										</p:treeNode>
									</p:tree>
	                        </p:tab>
	
	                        <p:tab title="按职务">
									<p:tree value="#{msgDialogSendBean.dutyTree}" var="node" selectionMode="single"  id="dutyTree" 
											style="width: 200px;height: 320px;overflow-y: auto;">
											
										<p:ajax event="select"  update=":msgForm:userPickList"  listener="#{msgDialogSendBean.onDutyNodeSelect}" />	
											
										<p:treeNode>
											<h:outputText value="#{node.codeName}" />
										</p:treeNode>
									</p:tree>
	                        </p:tab>
	
	                        <p:tab title="按领导">
								<p:selectBooleanCheckbox id="ifManager"  value="#{msgDialogSendBean.ifManager}">
										<p:ajax event="change" update=":msgForm:userPickList" 
                                    				listener="#{msgDialogSendBean.onBossChgAction}" />										
								</p:selectBooleanCheckbox>领导                            
	                        </p:tab>
	                    </p:accordionPanel>				
					</td>
					<td valign="top" align="left">
							<table style="width: 100%; margin: 0px;padding: 0px;">
								<tr valign="top">
									<td colspan="2">
										<p:pickList id="userPickList" value="#{msgDialogSendBean.dualListModel}" var="user" itemValue="#{user}" itemLabel="#{user.username}" converter="system.UserMsgConvert"
											showSourceControls="false" showTargetControls="false" showCheckbox="true" showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains" effect="drop">
											<p:ajax event="transfer" listener="#{msgDialogSendBean.handleTransfer}"  update=":msgForm:userPickList" process="@this"/>
						
											<f:facet name="sourceCaption">可选用户</f:facet>
											<f:facet name="targetCaption">已选用户</f:facet>
						
											<p:column style="width:75%;text-align: left;">#{user.username}</p:column>
											<p:column style="width:25%;text-align: left;">#{user.officeName}</p:column>
										</p:pickList>										
									</td>
								</tr>
								<tr>	
									<td width="580">
										<p:inputTextarea maxlength="100" id="msgCont"  value="#{msgDialogSendBean.msgCont}"
												required="true" requiredMessage="消息内容不允许为空!" 
												autoResize="true" rows="2"  style="width:570px;" title="消息内容"/><br/>
										<p:selectBooleanCheckbox id="mobileMsg"  value="#{msgDialogSendBean.mobileMsg}"/>
										<p:spacer width="2"/>手机短信
									</td>
									<td style="padding-left: 3px">
										 <p:commandButton  id="msgBtn" action="#{msgDialogSendBean.sendAction}"  
										 	styleClass="msgBtnClass" icon=""  update=":msgForm:msgCont,:msgForm:userPickList"/>
									</td>
								</tr>
							</table>
					</td>
				</tr>
			</table>
			<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		</h:form>
		 <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>