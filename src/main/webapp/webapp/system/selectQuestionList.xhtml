<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<title>题目选择</title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
		<style type="text/css">
</style>

	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:form id="mainForm">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="4">
					<span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
						update=":mainForm:selectedGdsTable"
						action="#{selectQuestionBean.searchAction}" process="@form" />
					<p:commandButton value="确定" icon="ui-icon-check" id="saveBtn"
						update=":mainForm:selectedGdsTable"
						action="#{selectQuestionBean.saveAction}" process="@form" />
					<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
						update=":mainForm:selectedGdsTable"
						action="#{selectQuestionBean.closeAction}" process="@this" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr style="height:35px;">
					<td style="text-align: left;padding-left: 3px;height:35px;"
						colspan="2"><h:panelGrid columns="8">
							<p:outputLabel value="标题：" styleClass="zwx_dialog_font" />
							<p:inputText id="title" value="#{selectQuestionBean.searchTitle}"
								maxlength="100" size="60">
								<p:ajax event="keyup" update="selectedGdsTable"
									process="@this,@parent"
									listener="#{selectQuestionBean.searchAction}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
				<tr style="height:35px;">
					<td style="text-align: left;padding-left: 3px;height:35px;"
						colspan="2"><h:panelGrid columns="8">
							<p:outputLabel value="题型：" styleClass="zwx_dialog_font" />
							<p:selectManyCheckbox
								value="#{selectQuestionBean.selectQueTypeList}">
								<f:selectItems value="#{selectQuestionBean.showTypeList}" />
							</p:selectManyCheckbox>
						</h:panelGrid></td>
				</tr>
				<tr style="height:35px;">
					<td
						style="text-align: left;padding-left: 3px;height:35px;width:400px"><h:panelGrid
							columns="8">
							<p:outputLabel value="类别：" styleClass="zwx_dialog_font" />
							<h:panelGrid columns="3"
								style="border-color: #ffffff;margin: 0px;padding: 0px;">
								<p:inputText id="examTypeName"
									value="#{selectQuestionBean.examName}" style="width: 180px;"
									onclick="document.getElementById('mainForm:initTreeLink').click();"
									readonly="true" />
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="initTreeLink" partialSubmit="true"
									action="#{selectQuestionBean.initExamTree}" process="@this"
									style="position: relative;left: -30px;"
									oncomplete="PF('ExamPanel').show()" update="examTree">
								</p:commandLink>
								<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
									update=":mainForm:examTypeName"
									action="#{selectQuestionBean.clearExamName}" process="@this"
									style="position: relative;left: -20px;">
								</p:commandLink>
							</h:panelGrid>
							<p:overlayPanel id="examPanel" for="examTypeName"
								style="width:280px;height:300px;" widgetVar="ExamPanel" showEvent="dblclick"
								showCloseIcon="true">
								<p:tree value="#{selectQuestionBean.examNode}" var="node"
									selectionMode="single" id="examTree"
									style="width: 250px;height: 280px;overflow-y: auto;"
									selection="#{selectQuestionBean.selectedNode}">
									<p:treeNode>
										<h:outputText value="#{node.codeName}" />
									</p:treeNode>
									<p:ajax event="select" update=":mainForm:examTypeName"
										listener="#{selectQuestionBean.onNodeSelect}" process="@this"
										oncomplete="PF('ExamPanel').hide();" />
								</p:tree>
							</p:overlayPanel>
						</h:panelGrid></td>

					<td style="text-align: left;padding-left: 3px;height:35px;"><h:panelGrid
							columns="8">
							<p:outputLabel value="状态：" styleClass="zwx_dialog_font" />
							<p:selectManyCheckbox value="#{selectQuestionBean.selectState}">
								<f:selectItem itemLabel="已选择" itemValue="1" />
								<f:selectItem itemLabel="未选择" itemValue="2" />
							</p:selectManyCheckbox>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable var="itm" value="#{selectQuestionBean.queDataModel}"
				id="selectedGdsTable" paginator="true" rows="10"
				rowsPerPageTemplate="10,20,50"
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
				emptyMessage="没有数据！" lazy="true"
				currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
				paginatorPosition="bottom" rowIndexVar="R" scrollable="true"
				scrollHeight="250">
				<p:column headerText="选择" style="width:50px;text-align:center">
					<p:selectBooleanCheckbox value="#{itm.selected}">
						<p:ajax event="change" listener="#{selectQuestionBean.selectAction(itm)}" process="@this"></p:ajax>
					</p:selectBooleanCheckbox>
				</p:column>
				<p:column headerText="类别" style="width: 80px;padding-left: 3px;">
					<h:outputText value="#{itm.tsSimpleCodeByTypeId.codeName}" />
				</p:column>
				<p:column headerText="编码" style="width: 80px;padding-left: 3px;"
					rendered="false">
					<h:outputText value="#{itm.qesCode}" />
				</p:column>
				<p:column headerText="标题" style="padding-left: 3px;">
					<h:outputText value="#{itm.titleDesc}" />
				</p:column>
				<p:column headerText="题型" style="width: 100px;padding-left: 3px; ">
					<h:outputText value="单选题" rendered="#{itm.questType.typeNo=='0'}" />
					<h:outputText value="多项选择题" rendered="#{itm.questType.typeNo=='1'}" />
					<h:outputText value="滑动题" rendered="#{itm.questType.typeNo=='2'}" />
					<h:outputText value="评价题" rendered="#{itm.questType.typeNo=='3'}" />
					<h:outputText value="文本填空题" rendered="#{itm.questType.typeNo=='4'}" />
					<h:outputText value="日期填空题" rendered="#{itm.questType.typeNo=='6'}" />
					<h:outputText value="整数填空" rendered="#{itm.questType.typeNo=='7'}" />
					<h:outputText value="数字填空题" rendered="#{itm.questType.typeNo=='9'}" />
					<h:outputText value="是非题" rendered="#{itm.questType.typeNo=='10'}" />
					<h:outputText value="表格题" rendered="#{itm.questType.typeNo=='11'}" />
					<h:outputText value="只读显示" rendered="#{itm.questType.typeNo=='12'}" />
				</p:column>
				<!-- <f:facet name="footer">
					<p:column style="text-align: center;">
						<h:outputText value="您已经选择了 " styleClass="zwx_dialog_font" />
						<h:outputText value="#{selectQuestionBean.selectList.size()}"
							styleClass="zwx_dialog_font" id="displaySize"
							style="color:blue;font-weight: bold" />
						<h:outputText value=" 条记录！" styleClass="zwx_dialog_font" />
					</p:column>
				</f:facet> -->
			</p:dataTable>
			<br />
			<br />
		</h:form>
	</h:body>
</f:view>
</html>
