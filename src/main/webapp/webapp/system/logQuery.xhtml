<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{logQueryBean}" />
	<!-- 是否启用光标定位功能 -->
	<ui:param name="onfocus" value="true" />

	<ui:define name="insertScripts">
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

	</ui:define>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="日志查询" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{logQueryBean.searchAction}" update="dataTable"
					process="@this,searchUserNo,searchClientIP,searchState" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:100px;">
				<h:outputText value="用户账号：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;width:200px;">
				<p:inputText id="searchUserNo" value="#{logQueryBean.searchUserNo}"
					maxlength="15" placeholder="精确查询"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:100px;">
				<h:outputText value="登录IP：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;width:200px;">
				<p:inputText id="searchClientIP" placeholder="精确查询"
					value="#{logQueryBean.searchClientIP}" maxlength="15" />
			</p:column>
            <p:column style="text-align:right;width:100px;">
                <h:outputText value="类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:selectManyCheckbox id="searchState" value="#{logQueryBean.searchState}">
                    <f:selectItem itemLabel="登录系统" itemValue="1001" />
                    <f:selectItem itemLabel="退出系统" itemValue="1002" />
                    <f:selectItem itemLabel="保存" itemValue="2001" />
                    <f:selectItem itemLabel="删除" itemValue="2002" />
                    <f:selectItem itemLabel="异常" itemValue="3001" />
                </p:selectManyCheckbox>
            </p:column>
		</p:row>
	</ui:define>

	<ui:define name="insertDataTable">
		<!--<p:column headerText="序号"
			style="padding-left: 3px;width: 80px;text-align: center;">
			<h:outputText value="#{R+1}">
			</h:outputText>
		</p:column>-->
		<p:column headerText="事件时间" style="padding-left: 3px;width: 180px;text-align: center;">
			<h:outputText value="#{itm[1]}">
				<f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn" />
			</h:outputText>
		</p:column>
        <p:column headerText="类型" style="width: 80px;text-align: center;">
            <h:outputText value="登录系统" rendered="#{itm[3]=='1001'}"/>
            <h:outputText value="退出系统" rendered="#{itm[3]=='1002'}"/>
            <h:outputText value="保存" rendered="#{itm[3]=='2001'}"/>
            <h:outputText value="删除" rendered="#{itm[3]=='2002'}"/>
            <h:outputText value="异常" rendered="#{itm[3]=='3001'}"/>
        </p:column>
		<p:column headerText="用户账号" style="width: 80px;text-align: center;">
			<h:outputText value="#{itm[2]}" />
		</p:column>
        <p:column headerText="登录IP" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[5]}" />
        </p:column>
        <p:column headerText="操作模块" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[6]}" />
        </p:column>
		<p:column headerText="日志简要" style="width: 600px;">
			<h:outputText value="#{itm[4]}" />
		</p:column>

		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="详情" action="#{logQueryBean.detailAction}"
				update=":mainForm:detailDialog" process="@this"
				oncomplete="PF('DetailDialog').show();">
				<f:setPropertyActionListener value="#{itm[0]}"
					target="#{logQueryBean.rid}" />
			</p:commandLink>
		</p:column>
	</ui:define>

	<ui:define name="insertDialogs">
		<p:dialog id="detailDialog" header="日志详情" widgetVar="DetailDialog"
			resizable="false" width="850" height="450" modal="true" maximizable="true">
			<p:scrollPanel mode="native" style="border:0px;padding-bottom:5px;margin-top:5px">
			<h:outputText value="#{logQueryBean.logDetail}"
				style="text-align:left;font-size:14px;word-break: break-word;word-wrap:break-word;" escape="false" />
				</p:scrollPanel>
		</p:dialog>

	</ui:define>
</ui:composition>