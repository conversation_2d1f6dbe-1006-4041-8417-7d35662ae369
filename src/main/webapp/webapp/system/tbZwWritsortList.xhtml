<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管bean -->
	<ui:param name="mgrbean" value="#{tbZwWritsortBean}"></ui:param>

	<!-- 编辑页面 -->
	<ui:param name="editPage" value="/webapp/system/tbZwWritsortEdit.xhtml" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<p:outputLabel value="文书类型维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" id="searchBtn"
					icon="ui-icon-search" action="#{mgrbean.searchAction}"
					update="dataTable" process="@this,mainGrid" />
				<p:commandButton value="添加" id="addBtn" icon="ui-icon-plus"
					action="#{mgrbean.addInitAction}" update=":tabView" process="@this">
					<p:resetInput target=":tabView:editForm"></p:resetInput>
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>


	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="width:180px;text-align:right;padding-right:5px;">
				<h:outputText value="文书编号：" />
			</p:column>
			<p:column style="width:220px;text-align:left;padding-left:5px;">
				<p:inputText value="#{mgrbean.searchWritCode}" style="width:168px"
					maxlength="10" />
			</p:column>
			<p:column style="width:180px;text-align:right;padding-right:5px;">
				<h:outputText value="文书名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:5px;">
				<p:inputText value="#{mgrbean.searchWritName}" style="width:168px"
					maxlength="50" />
			</p:column>
		</p:row>
	</ui:define>



	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="文书编号" style="width: 190px;">
			<p:outputLabel value="#{itm[1]}" />
		</p:column>
		<p:column headerText="文书名称" style="width: 190px;">
			<p:outputLabel value="#{itm[2]}" />
		</p:column>
		<p:column headerText="文书字头" style="width: 190px;">
			<h:outputText value="#{itm[3]}" />
		</p:column>
		<p:column headerText="文书报表编码" style="width:190px;">
			<h:outputText value="#{itm[4]}" />
		</p:column>


		<p:column headerText="操作">
			<p:commandLink value="修改" action="#{mgrbean.modInitAction}"
				update=":tabView" process="@this">
				<f:setPropertyActionListener target="#{mgrbean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="删除" action="#{mgrbean.deleteAction}"
				update="dataTable" process="@this">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{mgrbean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
		</p:column>
	</ui:define>

	<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
	<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
	<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
	<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
</ui:composition>