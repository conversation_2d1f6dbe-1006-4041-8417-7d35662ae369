<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

    <!-- 通知发布 -->
    <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left; padding-left: 5px; " colspan="3">
                    <p:outputLabel value="通知发布内容"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height: 39px;">
                <p:outputLabel value="发布类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:outputLabel value="#{childbean.selectedPublishType.codePath} #{null == childbean.curPublishNotice.otherType ? '' : '（'}#{null == childbean.curPublishNotice.otherType ? '' : childbean.curPublishNotice.otherType}#{null == childbean.curPublishNotice.otherType ? '' : '）'}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:8px;width: 150px;height: 39px;">
                <p:outputLabel value="标题："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:outputLabel value="#{childbean.curPublishNotice.title}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:8px;width: 150px;height: 39px;">
                <p:outputLabel value="是否需要回执："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" >
                <p:outputLabel value="是" rendered="#{null ne childbean.curPublishNotice.ifNeedFeedback and 1 == childbean.curPublishNotice.ifNeedFeedback}"/>
                <p:outputLabel value="否" rendered="#{null == childbean.curPublishNotice.ifNeedFeedback or 0 == childbean.curPublishNotice.ifNeedFeedback}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 30px;">
                <p:outputLabel value="内容："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="3" >
                <div style="width: 100%; max-height:600px;overflow:auto;" >
                    <p:outputLabel value="#{childbean.curPublishNotice.content}" escape="false" />
                </div>
            </p:column>
        </p:row>
    </p:panelGrid>
    <!--附件区域 -->
    <p:panelGrid  style="margin-bottom: 10px;width:100%" id="noticeAnnexPanel" >
        <f:facet name="header">
            <p:row>
                <p:column style="height:20px;text-align:left;padding-left: 5px; " >
                    <p:outputLabel value="通知发布附件"/>
                </p:column>
            </p:row>
        </f:facet>
        <p:row>
            <p:column>
                <p:dataTable value="#{childbean.publishNoticeAnnexList}" var="itm" id="noticeAnnexViewDataTable" rows="#{childbean.noticeUnitPageSize}" paginatorPosition="bottom" rowIndexVar="R"
                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                             rowsPerPageTemplate="#{'10,20,50'}"  emptyMessage="没有您要找的记录！" paginator="true"
                             currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                    <p:column headerText="序号" style="width: 80px;text-align: center;" >
                        <p:outputLabel value="#{R+1}" />
                    </p:column>
                    <p:column headerText="附件名称" style="width: 380px;" >
                        <p:outputLabel value="#{itm.annexName}" />
                    </p:column>
                    <p:column headerText="操作" >
                        <p:commandLink value="下载" process="@this" immediate="true"
                                       ajax="false" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);" >
                            <f:setPropertyActionListener value="#{itm}" target="#{childbean.curNoticeAnnex}"/>
                            <p:fileDownload value="#{childbean.noiceAnnexStreamedContent}" />
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
    </p:panelGrid>
</ui:composition>
