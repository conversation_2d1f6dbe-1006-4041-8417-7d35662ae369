<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
	<h:form id="mainform">
		<p:panelGrid style="width:100%;" id="zz">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="第三方系统注册" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent; padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" id="saveBtn" icon="ui-icon-check"
					action="#{sysBean.saveAction}" process="@this,mainGrid1"
					update=":tabView">
				</p:commandButton>

				<p:commandButton value="返回" id="backBtn" icon="ui-icon-close"
					action="#{sysBean.backAction()}" immediate="true" update=":tabView"
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<!-- 第三方系统注册 -->
		<p:fieldset legend="第三方系统注册" toggleable="true" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:panelGrid style="width:100%;height:100%;" id="mainGrid1">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:180px;height:25px">
						<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="系统名称："  />
					</p:column>
					<p:column style="width:400px;">
						<p:inputText value="#{sysBean.tsDsfSys.sysName}" maxlength="50" style="width:230px"
							 />
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:180px;">
					
						<p:outputLabel value="系统简称：" />
					</p:column>
					<p:column>
						<p:inputText value="#{sysBean.tsDsfSys.sysJc}"  style="width:230px" maxlength="25"
							 />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:180px;">
						
						<p:outputLabel value="系统图标：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
                    <h:panelGroup id="uploadGroup">
                        <p:commandButton value="上传" oncomplete="PF('fileUIdVar').show();"
                                         rendered="#{sysBean.tsDsfSys.sysIcon==null}"
                                         process="@this" update="fileUId"/>
                        <h:panelGroup rendered="#{sysBean.tsDsfSys.sysIcon!=null}">
                            <h:graphicImage url="/webFile#{sysBean.tsDsfSys.sysIcon}" width="64px" height="64px"/>
                            <p:spacer width="5"/>
                            <p:commandButton value="删除" update="uploadGroup"  process="@this"
                                             action="#{sysBean.deleteDiskFile}" />
                        </h:panelGroup>
                        
                        <h:outputText style="color:blue;" value="　[推荐像素：64*64]" />
                    </h:panelGroup>
                </p:column>
					<p:column style="text-align:right;padding-right:3px;width:180px;">
						<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="系统类型：" />
					</p:column>
					<p:column>
                        <p:selectOneRadio value="#{sysBean.tsDsfSys.xtType}"  >
                            <f:selectItem itemLabel="BS" itemValue="1"  />
                            <f:selectItem itemLabel="CS" itemValue="2" />
                            <p:ajax event="change" process="@this,mainGrid1"  update="mainGrid1" />
                        </p:selectOneRadio>
                       
                    </p:column>     
				</p:row>
				<p:row rendered="#{sysBean.tsDsfSys.xtType=='1'}">
					<p:column style="text-align:right;padding-right:3px;width:180px;height:25px;" id="address">
					
						<p:outputLabel value="程序地址：" />
					</p:column>
					<p:column colspan="3"
						style="text-align:left;padding-left:3px;width:180px;">
						<p:inputText value="#{sysBean.tsDsfSys.sysUrl}"  style="width:300px;" maxlength="100"
							  />
					</p:column>
				</p:row>

			</p:panelGrid>
		</p:fieldset>
		
		 <!-- 文件上传 -->
    <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false" modal="true">
        <p:fileUpload requiredMessage="请选择上传图标！" style="width:700px;" previewWidth="50"
                      fileUploadListener="#{sysBean.handleFileUpload}"
                      fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                      label="选择文件" uploadLabel="上传" cancelLabel="取消"
                      sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                      validatorMessage="上传出错啦，重新上传！"
                      invalidFileMessage="无效的文件类型！只能上传gif,jpg,png等图片类型文件！"
                      process="@this" update="uploadGroup"
                      mode="advanced" dragDropSupport="true"
                      allowTypes="/(\.|\/)(gif|jpe?g|png)$/" />
    </p:dialog>
		

		<!--第三方系统注册参数表 -->

		<p:fieldset legend="第三方系统注册参数" toggleable="true" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:panelGrid style="width:100%;height:100%;" id="mainGrid2">
				<p:row>
					<p:column colspan="4">
						<p:commandButton value="添加"  icon="ui-icon-plus" resetValues="true"
							oncomplete="PF('Dlg2').show();" update=":tabView:mainform:dlg2"  action="#{sysBean.sysAdd}"
							process="@this">
							<f:setPropertyActionListener target="#{sysBean.tig}" value="1" />
						</p:commandButton>
					</p:column>
				</p:row>
				<!-- 第三方系统注册参数表 -->
				<p:row>
					<p:column>
						<p:dataTable value="#{sysBean.list}" var="item" id="oo"
							emptyMessage="没有您要找的记录！">
							<p:column headerText="参数名">
								<h:outputText value="#{item.paramCn}" />
							</p:column>
							<p:column headerText="参数字段名">
								<h:outputText value="#{item.paramEn}" />
							</p:column>
							<p:column headerText="参数描述">
								<h:outputText value="#{item.paramDesc}" />
							</p:column>
							<!--参数修改  -->
							<p:column headerText="操作">
								<p:commandLink value="修改" process="@this"
									oncomplete="PF('Dlg2').show();" update=":tabView:mainform:dlg2">
									<f:setPropertyActionListener target="#{sysBean.tig}" value="2" />

									<f:setPropertyActionListener target="#{sysBean.tsDsfSysParam}"
										value="#{item}" />
								</p:commandLink>
								<p:spacer width="5" />
								<!--参数删除  -->
								<p:commandLink value="删除" action="#{sysBean.deleteSys}"
									update="oo" process="@this">
									<p:confirm header="消息确认框" message="确定要删除吗？"
										icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{sysBean.tsDsfSysParam}"
										value="#{item}" />
								</p:commandLink>
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
			</p:panelGrid>
			</p:fieldset>
			<!--第三方系统注册参数表插入  -->
		<p:dialog header="第三方系统注册参数表" widgetVar="Dlg2" showEffect="explode"
			resizable="false" width="500" height="180" id="dlg2">
			<p:panelGrid style="width:100%;">
				<p:row>
					<p:column style="text-align:right;width:100px" >
					<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="参数名：" />
					</p:column>
					<p:column>
						<p:inputText value="#{sysBean.tsDsfSysParam.paramCn}" required="true"
				maxlength="25"			requiredMessage="参数名不能为空" style="width:300px" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;">
					<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="参数字段名：" />
					</p:column>
					<p:column>
						<p:inputText value="#{sysBean.tsDsfSysParam.paramEn}" required="true"
				maxlength="25"			requiredMessage="参数字段名不能为空" style="width:300px" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;">
					
						<p:outputLabel value="参数描述：" />
					</p:column>
					<p:column>
						<p:inputTextarea  value="#{sysBean.tsDsfSysParam.paramDesc}" maxlength="50" style="width:300px" />
					</p:column>
				</p:row>
	<!-- 按钮操作 -->
				<p:row>
					<p:column colspan="2" style="text-align:center;">
						<p:commandButton value="确定" action="#{sysBean.tdspSave}" icon="ui-icon-check"
							update="oo" process="@this,dlg2"  />

						<p:commandButton type="button" value="关闭"  icon="ui-icon-close"
							onclick="PF('Dlg2').hide();" />
					</p:column>
				</p:row>
			</p:panelGrid>

		</p:dialog>
	</h:form>	
</ui:composition>