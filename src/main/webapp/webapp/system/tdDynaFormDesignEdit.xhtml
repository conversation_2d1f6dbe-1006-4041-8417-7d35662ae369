<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdDynaFormDesignBean}" />
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />

	<!-- 样式或javascripts -->
	<ui:define name="insertEditScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
		<style type="text/css">
body {
	overflow-x: hidden;
}
</style>

	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="动态表单维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;" id="btnPanel">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{tdDynaFormDesignBean.saveAction}"
					rendered="#{tdDynaFormDesignBean.bindTdFormDef.state == 0}"
					process="@this,editPanelInfo" update="btnPanel" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
					rendered="#{tdDynaFormDesignBean.bindTdFormDef.state == 0}"
					id="submitBtn" update="btnPanel"
					action="#{tdDynaFormDesignBean.confirmEditRecord(1)}"
					process="@this,editPanelInfo">
					<p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert" />
				</p:commandButton>
				<p:commandButton value="撤销" icon="ui-icon-circle-triangle-e"
					rendered="#{tdDynaFormDesignBean.bindTdFormDef.state == 1}"
					id="cancelBtn" update="btnPanel"
					action="#{tdDynaFormDesignBean.confirmEditRecord(0)}"
					process="@this,editPanelInfo">
					<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert" />
				</p:commandButton>

				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{tdDynaFormDesignBean.backAction}" update=":tabView"
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
	</ui:define>

	<!-- 其余内容 -->
	<ui:define name="insertOtherContents">
		<p:outputPanel id="editPanelInfo">
			<p:panel header="基本信息" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="表单编号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:outputLabel value="自动生成" style="color:gray;"
								rendered="#{tdDynaFormDesignBean.bindTdFormDef.rid == null}" />
							<p:outputLabel
								value="#{tdDynaFormDesignBean.bindTdFormDef.formCode}"
								rendered="#{tdDynaFormDesignBean.bindTdFormDef.rid != null}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="表单类别：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<h:panelGrid columns="4"
								style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:inputText id="tdFormTypeName"
									value="#{ null != tdDynaFormDesignBean.bindTdFormDef.tdFormTypeByTypeId ?tdDynaFormDesignBean.bindTdFormDef.tdFormTypeByTypeId.typeName:''}"
									style="width: 182px;cursor: pointer"
									onclick="document.getElementById('tabView:editForm:selorgLink').click();"
									readonly="true" />
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="selorgLink" process="@this"
									style="position: relative;left: -28px;"
									action="#{tdDynaFormDesignBean.showSingleAction}"
									update="singleSelDialog"
									oncomplete="PF('SingleSelDialog').show()">
								</p:commandLink>
								<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn"
									title="清空" update="tdFormTypeName"
									action="#{tdDynaFormDesignBean.clearSingleAciton}"
									process="@this" style="position: relative;left: -25px;">
								</p:commandLink>
							</h:panelGrid>
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="表单名称：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:inputText
								value="#{tdDynaFormDesignBean.bindTdFormDef.formName}" size="20"
								maxlength="100" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="数据表：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<h:panelGrid columns="4"
								style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:inputText id="tdTabName"
									value="#{tdDynaFormDesignBean.dynaFormName}"
									style="width: 182px;cursor: pointer"
									onclick="document.getElementById('tabView:editForm:tdTabLink').click();"
									readonly="true" />
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="tdTabLink" process="@this"
									style="position: relative;left: -28px;"
									action="#{tdDynaFormDesignBean.showTableAction}"
									update="dataStructureDialog"
									oncomplete="PF('DataStructureDialog').show()">
								</p:commandLink>
								<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn3"
									title="清空" update="tdTabName"
									action="#{tdDynaFormDesignBean.clearTableAciton}"
									process="@this" style="position: relative;left: -25px;">
								</p:commandLink>
							</h:panelGrid>
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="报表模版：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<h:panelGrid columns="5"
								style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:inputText id="prtTplCode"
									value="#{tdDynaFormDesignBean.selRpt==null?null:tdDynaFormDesignBean.selRpt.rptnam}"
									style="width: 182px;cursor: pointer"
									onclick="document.getElementById('tabView:editForm:selRptLink').click();"
									readonly="true" />
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="selRptLink" process="@this"
									style="position: relative;left: -28px;"
									action="#{tdDynaFormDesignBean.showRptModel}"
									update="rptDialog" oncomplete="PF('RptDialog').show()">
								</p:commandLink>
								<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn2"
									title="清空" update="prtTplCode"
									action="#{tdDynaFormDesignBean.clearRptAciton}" process="@this"
									style="position: relative;left: -25px;">
								</p:commandLink>

								<p:outputLabel value="是否需要报表打印，如需要请选择模版！" style="color:blue;" />
							</h:panelGrid>
						</p:column>
					</p:row>
				</p:panelGrid>

			</p:panel>

			<p:panel header="HTML代码" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成表单HTML代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="htmlCode"
								value="#{tdDynaFormDesignBean.bindTdFormDef.html}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成列表HTML代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="searchHtmlCode"
								value="#{tdDynaFormDesignBean.bindTdFormDef.searchHtml}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成列表SQL代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="searchSqlCode"
								value="#{tdDynaFormDesignBean.bindTdFormDef.searchSql}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:panel>
		</p:outputPanel>

		<p:dialog id="dataStructureDialog" widgetVar="DataStructureDialog"
			header="数据表选择" resizable="false" width="750" height="400"
			modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="关闭" icon="ui-icon-close" process="@this"
						oncomplete="PF('DataStructureDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							id="searchPanelD" columns="10">
							<p:outputLabel value="表　名：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{tdDynaFormDesignBean.tableNameDiag}"
								style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this,searchPanelD"
									update=":tabView:editForm:tableDatatable"
									listener="#{tdDynaFormDesignBean.filterTableSel}" />
							</p:inputText>
							<p:spacer width="10" />
							<p:outputLabel value="表模式：" styleClass="zwx_dialog_font" />
							<p:selectOneMenu value="#{tdDynaFormDesignBean.tableModelDiag}"
								style="width:150px;">
								<f:selectItem itemValue="" itemLabel="--请选择--" />
								<f:selectItem itemValue="1" itemLabel="单表" />
								<f:selectItem itemValue="2" itemLabel="主子表" />
								<p:ajax event="change" process="@this,searchPanelD"
									update=":tabView:editForm:tableDatatable"
									listener="#{tdDynaFormDesignBean.filterTableSel}" />
							</p:selectOneMenu>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{tdDynaFormDesignBean.showTableList}" var="typ"
				emptyMessage="暂无记录" paginator="true" rows="10"
				paginatorPosition="bottom" id="tableDatatable">
				<p:column headerText="操作" style="width:80px;text-align:center;">
					<p:commandLink value="选择" process="@this"
						action="#{tdDynaFormDesignBean.selTableResult}"
						update=":tabView:editForm:tdTabName,:tabView:editForm:htmlCode,:tabView:editForm:searchHtmlCode,:tabView:editForm:searchSqlCode"
						oncomplete="PF('DataStructureDialog').hide()">
						<f:setPropertyActionListener
							target="#{tdDynaFormDesignBean.dynaFormId}" value="#{typ[2]}" />
						<f:setPropertyActionListener
							target="#{tdDynaFormDesignBean.dynaFormName}" value="#{typ[0]}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="表名" style="padding-left: 3px;">
					<h:outputLabel value="#{typ[0]}" />
				</p:column>
				<p:column headerText="表模式" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ[1]=='1'?'单表':'主子表'}" />
				</p:column>
			</p:dataTable>
		</p:dialog>
		<p:dialog id="singleSelDialog" widgetVar="SingleSelDialog"
			header="表单类型选择" resizable="false" width="750" height="400"
			modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="添加" icon="ui-icon-plus" process="@this"
						action="#{tdDynaFormDesignBean.addSingleSel}"
						update="singleAddDialog" oncomplete="PF('SingleAddDialog').show()" />
					<p:commandButton value="返回" icon="ui-icon-close" process="@this"
						oncomplete="PF('SingleSelDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							columns="3">
							<p:outputLabel value="名　称：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{tdDynaFormDesignBean.formTypeDiag}"
								style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this"
									update=":tabView:editForm:singleDatatable"
									listener="#{tdDynaFormDesignBean.filterSingelSel}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{tdDynaFormDesignBean.showTypeList}" var="typ"
				emptyMessage="暂无记录" paginator="true" rows="10"
				paginatorPosition="bottom" id="singleDatatable">
				<p:column headerText="操作" style="width:120px;text-align:center;">
					<p:commandLink value="选择" process="@this"
						action="#{tdDynaFormDesignBean.selSingleResult}"
						update=":tabView:editForm:tdFormTypeName"
						oncomplete="PF('SingleSelDialog').hide()">
						<f:setPropertyActionListener
							target="#{tdDynaFormDesignBean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="修改" process="@this"
						update=":tabView:editForm:singleAddDialog"
						oncomplete="PF('SingleAddDialog').show()">
						<f:setPropertyActionListener
							target="#{tdDynaFormDesignBean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="删除" process="@this"
						action="#{tdDynaFormDesignBean.deleteSingleAction}"
						update="singleDatatable">
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener
							target="#{tdDynaFormDesignBean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="类型编码" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ.typeCode}" />
				</p:column>
				<p:column headerText="类型名称" style="padding-left: 3px;">
					<h:outputLabel value="#{typ.typeName}" />
				</p:column>

			</p:dataTable>
		</p:dialog>
		<p:dialog id="singleAddDialog" widgetVar="SingleAddDialog"
			header="表单类型维护" resizable="false" width="500" modal="true">
			<p:panelGrid style="width:100%;" id="singleAddGrid">
				<p:row>
					<p:column
						style="width:30%;height: 25px;text-align: right;padding-right: 3px;">
						<p:outputLabel value="*" style="color: red" />
						<p:outputLabel value="类型编码：" />
					</p:column>
					<p:column style="padding-left: 3px;">
						<p:inputText
							value="#{tdDynaFormDesignBean.bindTdFormType.typeCode}"
							style="width: 160px;" maxlength="25" required="true"
							requiredMessage="类型编码不允许为空！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="height: 25px;text-align: right;padding-right: 3px;">
						<p:outputLabel value="*" style="color: red" />
						<p:outputLabel value="类型名称：" />
					</p:column>
					<p:column style="padding-left: 3px;">
						<p:inputText
							value="#{tdDynaFormDesignBean.bindTdFormType.typeName}"
							style="width: 160px;" maxlength="50" required="true"
							requiredMessage="类型名称不允许为空！" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check"
							action="#{tdDynaFormDesignBean.saveSingleAction}"
							process="@this,singleAddGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							onclick="PF('SingleAddDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>



		<p:dialog id="rptDialog" widgetVar="RptDialog" header="报表模版选择"
			resizable="false" width="780" modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="关闭" icon="ui-icon-close" process="@this"
						oncomplete="PF('RptDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							id="searchRptDiag" columns="6">
							<p:outputLabel value="系统类型：" styleClass="zwx_dialog_font" />
							<p:selectOneMenu id="msgType" style="width:200px"
								value="#{tdDynaFormDesignBean.sysType}">
								<f:selectItem itemLabel="--全部--" itemValue="" />
								<f:selectItems value="#{tdDynaFormDesignBean.sysTypeMap}" />
								<p:ajax event="change" process="@this,searchRptDiag"
									update=":tabView:editForm:rptDatatable"
									listener="#{tdDynaFormDesignBean.filterRptList}" />
							</p:selectOneMenu>
							<p:spacer width="5" />
							<p:outputLabel value="名　称：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{tdDynaFormDesignBean.searchRptName}"
								style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this,searchRptDiag"
									update=":tabView:editForm:rptDatatable"
									listener="#{tdDynaFormDesignBean.filterRptList}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{tdDynaFormDesignBean.showRptList}" var="typ"
				emptyMessage="暂无记录" paginator="true" rows="10"
				paginatorPosition="bottom" id="rptDatatable">
				<p:column headerText="操作" style="width:60px;text-align:center;">
					<p:commandLink value="选择" process="@this"
						update=":tabView:editForm:prtTplCode"
						oncomplete="PF('RptDialog').hide()">
						<f:setPropertyActionListener
							target="#{tdDynaFormDesignBean.selRpt}" value="#{typ}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="报表编号" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ.rptCod}" />
				</p:column>
				<p:column headerText="报表名称" style="padding-left: 3px;">
					<h:outputLabel value="#{typ.rptnam}" />
				</p:column>

			</p:dataTable>
		</p:dialog>

		<p:dialog widgetVar="ThemeSelect" id="themeSelect" width="460"
			height="100" header="动态表单样式选择" resizable="false">
			<p:panelGrid style="width:100%;" id="themePanel">
				<p:row>
					<p:column style="width:30%;padding-right:8px;text-align:right;">
       					样式选择：
       				</p:column>
					<p:column>
						<p:selectOneMenu id="styleId"
							value="#{tdDynaFormDesignBean.styleId}" style="width:180px;"
							required="true" requiredMessage="样式不能为空！">
							<f:selectItems value="#{tdDynaFormDesignBean.styleList}" />
						</p:selectOneMenu>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check" id="saveBtnThe"
							action="#{tdDynaFormDesignBean.genHtmlCode}"
							process="@this,editPanelInfo,themePanel" update="htmlCode" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="backBtnThe"
							onclick="PF('ThemeSelect').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</ui:define>
</ui:composition>











