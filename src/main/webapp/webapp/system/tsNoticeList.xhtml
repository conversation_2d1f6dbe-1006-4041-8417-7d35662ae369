<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tsNoticeListBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/tsNoticeEdit.xhtml"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知通告管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable"
                                 process="@this,searchDate,searchNoticTitle,searchIfPublish,searchStateMark" />
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this">
                    <p:resetInput target=":tabView:editForm:editGrid" />
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;" >
                <h:outputText value="通知日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 260px;">
<!--                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="searchStart"-->
<!--                            navigator="true" yearRange="c-20:c+20" maxdate="new Date()"-->
<!--                            converterMessage="通知日期，格式输入不正确！"-->
<!--                            showButtonPanel="true" size="11"-->
<!--                            value="#{mgrbean.searchStartDate}"/>-->
<!--                <p:outputLabel value="～"/>-->
<!--                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="searchEnd"-->
<!--                            navigator="true" yearRange="c-20:c+20"-->
<!--                            showButtonPanel="true"  maxdate="new Date()"-->
<!--                            converterMessage="通知日期，格式输入不正确！" size="11"-->
<!--                            value="#{mgrbean.searchEndDate}"/>-->
                <p:outputPanel id="searchDate">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchStartDate}" converterMessageB="通知日期，格式输入不正确！" converterMessageE="通知日期，格式输入不正确！"
                                              yearRange="20"
                                              endDate="#{mgrbean.searchEndDate}"/>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="通知标题：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 260px;">
                <p:inputText id="searchNoticTitle" value="#{mgrbean.searchNoticTitle}" maxlength="100" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="是否发布：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectManyCheckbox id="searchIfPublish" value="#{mgrbean.searchIfPublish}">
                    <f:selectItem itemLabel="是" itemValue="1"/>
                    <f:selectItem itemLabel="否" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:120px;height: 33px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column colspan="5">
                <p:selectManyCheckbox id="searchStateMark" value="#{mgrbean.searchStateMark}">
                    <f:selectItem itemLabel="待提交" itemValue="0"/>
                    <f:selectItem itemLabel="已提交" itemValue="2" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="序号" style="width: 60px;text-align: center;">
            <p:outputLabel value="#{R+1}" />
        </p:column>
        <p:column headerText="通知标题" style="padding-left: 3px;width: 420px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="通知日期" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[2]}" >
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
            </h:outputText>
        </p:column>
        <p:column headerText="是否发布" style="width: 60px;text-align: center;">
            <h:outputText value="#{itm[3]=='1'?'是':'否'}" />
        </p:column>
        <p:column headerText="状态" style="width: 60px;text-align: center;">
            <h:outputText value="待提交" rendered="#{itm[4]=='0'}" />
            <h:outputText value="已提交" rendered="#{itm[4]=='2'}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" update=":tabView" process="@this" resetValues="true" rendered="#{itm[4]=='0'}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.modInitAction}" update=":tabView" process="@this" resetValues="true" rendered="#{itm[4]=='2'}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="取消发布" action="#{mgrbean.publichAction}" update="dataTable" process="@this" rendered="#{itm[4]=='2' and itm[3]=='1'}">
                <p:confirm header="消息确认框" message="确定要取消发布吗？" icon="ui-icon-alert" />
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="发布" action="#{mgrbean.publichAction}" update="dataTable" process="@this" rendered="#{itm[4]=='2' and itm[3]=='0'}">
                <p:confirm header="消息确认框" message="确定要发布吗？" icon="ui-icon-alert" />
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" />
        </p:column>
    </ui:define>
</ui:composition>











