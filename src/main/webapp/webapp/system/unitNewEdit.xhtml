<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
	<ui:param name="mgrbean" value="#{unitNewBean}"/>
 	<ui:define name="insertEditScripts">
	 	<script>
			function newBlank(){
				window.parent.open("http://api.map.baidu.com/lbsapi/getpoint/index.html");
			}
		</script>
	 </ui:define>
       <!-- 标题栏 -->
       <ui:define name="insertEditTitle">
           <p:row>
               <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                   <h:outputText value="单位管理"/>
               </p:column>
           </p:row>
       </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-disk" id="saveBtn" action="#{mgrbean.BeforSaveAction}" process="@this,editGrid" update=":tabView" />
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}" update=":tabView" immediate="true" />
				<p:confirmDialog message="#{mgrbean.message}" header="消息确认框" widgetVar="ConfirmDialog" style="text-align: center">
					<p:outputPanel style="text-align: right;">
				        <p:commandButton value="确定" action="#{mgrbean.saveAction}" icon="ui-icon-check" process="@this,editGrid" oncomplete="PF('ConfirmDialog').hide();" update=":tabView" />
				        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
			        </p:outputPanel>
			    </p:confirmDialog>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertEditContent">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="行政区划所属地区："/>
            </p:column>
            <p:column style="text-align:left;width:180px;border-right:none" id="xzZone">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneBsList}" zoneId="#{mgrbean.editZoneId}" zoneName="#{mgrbean.editZoneName}" />
            </p:column>
            <p:column style="text-align:left;border-left:none">
            	<h:outputText value="提示：" style="color: red"/>
            	<h:outputText value="必须选择到区县及以下" style="color: blue"/>
            </p:column>
        </p:row>
		<p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="业务管辖地区："/>
            </p:column>
            <p:column style="text-align:left;width:180px;border-right:none;" id="bsZone" >
                <zwx:ZoneSingleNewComp  zoneList="#{mgrbean.zoneBsList}"  zoneId="#{mgrbean.editBsZoneId}" zoneName="#{mgrbean.editBsZoneName}" />
            </p:column>
            <p:column style="text-align:left;border-left:none">
            	<p:commandLink  value="同行政区划所属地区" action="#{mgrbean.copyZone}" process="@this,editGrid" update="@this,editGrid"></p:commandLink>
            </p:column>
        </p:row>
		<p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="社会信用代码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="creditCode" value="#{mgrbean.tsUnit.creditCode}" maxlength="50" style="width: 180px;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unitname" value="#{mgrbean.tsUnit.unitname}" maxlength="50" style="width: 180px;"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="单位简称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unitSimpname" value="#{mgrbean.tsUnit.unitSimpname}" maxlength="50" style="width: 180px;"/>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.ifSubOrg=='1'}">
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="是否分支机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
            	<p:selectOneRadio value="#{mgrbean.tsUnit.ifSubOrg}">
            		<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
            		<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
            	</p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;height:25px">
                <h:outputText value="单位编码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
            	<h:outputText value="#{mgrbean.tsUnit.unitCode==null?'自动生成':mgrbean.tsUnit.unitCode}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="邮编："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unitzip" value="#{mgrbean.tsUnit.unitzip}" maxlength="6" size="10"/>
            </p:column>
        </p:row>
		<p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
            	<!-- <font color="red">*</font> -->
                <h:outputText value="百度坐标："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
            	<p:outputPanel id="bdPanel">
	            	<h:outputText value="X " style="padding-left:5px"/>
	            	<p:inputText value="#{mgrbean.tsUnit.lng}" maxlength="25" onchange="changeX();" >
	            	</p:inputText>
	            	<h:outputText value="Y " style="padding-left:5px"/>
	            	<p:inputText value="#{mgrbean.tsUnit.lat}" onchange="changeX();" maxlength="25"/>
	            	<p:menuButton value="百度地图坐标" style="margin-left:5px">
	            		 	<p:menuitem value="百度地图查询" icon="ui-icon-search" onclick="newBlank()" process="@this"/>
	       				    <p:menuitem value="GPS坐标转换" icon="ui-icon-transferthick-e-w" oncomplete="PF('GpsDialog').show()" 
	       				    	resetValues="true" process="@this" update="gpsDialog"/>
	            	</p:menuButton>	
	            	<p:remoteCommand name="changeX" action="#{mgrbean.changeX}" update=":tabView:editForm:bdPanel" process=":tabView:editForm:bdPanel">
					</p:remoteCommand>
            	</p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="单位地址："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unitaddr" value="#{mgrbean.tsUnit.unitaddr}" maxlength="50" size="50"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="联系电话："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unittel" value="#{mgrbean.tsUnit.unittel}" maxlength="20" style="width: 180px;"/>
                <h:outputText value="（格式：0510-85373786）" style="color: red"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="单位传真："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unitfax" value="#{mgrbean.tsUnit.unitfax}" maxlength="20" style="width: 180px;"/>
                <h:outputText value="（格式：0510-85373786）" style="color: red"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="单位邮箱："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="unitemail" value="#{mgrbean.tsUnit.unitemail}" maxlength="60" size="40"/>
            </p:column>
        </p:row>
		<p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <!-- <h:outputText value="*" style="color: #ff0000;"/> -->
                <h:outputText value="法定代表人："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2"> 
                <p:inputText id="orgFz" value="#{mgrbean.tsUnit.orgFz}" maxlength="25" style="width: 180px;" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="法定代表人职务："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:selectOneMenu value="#{mgrbean.tsUnit.orgFzzw}" editable="true" maxlength="25"
                                 style="width: 188px;" >
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.careerList}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
             <!--    <h:outputText value="*" style="color: #ff0000;"/> -->
                <h:outputText value="联系人："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="linkMan" value="#{mgrbean.tsUnit.linkMan}" maxlength="50"  style="width: 180px;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
               <!--  <h:outputText value="*" style="color: #ff0000;"/> -->
                <h:outputText value="联系人电话："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="orgTel" value="#{mgrbean.tsUnit.orgTel}" maxlength="25" style="width: 180px;"/>
            </p:column>
        </p:row>
        <p:row rendered="false">
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="医疗机构执业许可证号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="mediLic" value="#{mgrbean.tsUnit.mediLic}" maxlength="100" size="50"/>
            </p:column>
        </p:row>
        <p:row rendered="false">
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="放射诊疗许可证号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:inputText id="radLic" value="#{mgrbean.tsUnit.radLic}" maxlength="100" size="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="机构性质："/>
            </p:column>
            <p:column style="text-align:left;padding-left:12px;" colspan="2">
                <p:selectOneMenu value="#{mgrbean.tsUnit.unitTypeRid}" editable="false"
                                 style="width: 188px;" >
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.unitTypeList}" var="unitType"
                                   itemValue="#{unitType.rid}" itemLabel="#{unitType.codeName}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
            	<font color="red">*</font>
                <h:outputText value="单位属性："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:selectManyCheckbox id="selectSortList" value="#{mgrbean.selectSortList}" layout="grid" columns="5">
                    <f:selectItems value="#{mgrbean.bsSortMap}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    
     <ui:define name="insertOtherContents">
     <p:dialog width="400" resizable="false" modal="true" id="gpsDialog"
			widgetVar="GpsDialog" header="GPS坐标转换" minHeight="200">
			<p:panelGrid style="width:100%" id="gpsGrid">
				<p:row>
					<p:column style="width:100px;text-align:right;padding-right:3px;">
						<p:outputLabel value="东经："/>
					</p:column>
					<p:column>
						<p:inputText size="6" value="#{mgrbean.degreeLong}" maxlength="6" 
							required="true" requiredMessage="东经数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">°</font>
						<p:inputText size="6" value="#{mgrbean.minuteLong}" maxlength="6"
							required="true" requiredMessage="东经数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">′</font>
						<p:inputText size="6" value="#{mgrbean.secondLong}" maxlength="6"
							required="true" requiredMessage="东经数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">″</font>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="width:100px;text-align:right;padding-right:3px;">
						<p:outputLabel value="北纬："/>
					</p:column>
					<p:column>
						<p:inputText size="6" value="#{mgrbean.degreeLat}" maxlength="6"
							required="true" requiredMessage="北纬数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">°</font>
						<p:inputText size="6" value="#{mgrbean.minuteLat}"
							required="true" requiredMessage="北纬数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">′</font>
						<p:inputText size="6" value="#{mgrbean.secondLat}"
							required="true" requiredMessage="北纬数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">″</font>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
    			<h:panelGrid style="width: 100%;text-align: center;">
	                <h:panelGroup>
	                    <p:commandButton value="转换" icon="ui-icon-disk" action="#{mgrbean.convertLatOrLon}"
	                    	 process="@this,gpsGrid" update="bdPanel"/>
	                    <p:spacer width="5" />
	                    <p:commandButton value="关闭" icon="ui-icon-close" process="@this" oncomplete="PF('GpsDialog').hide()" />
	                </h:panelGroup>
	            </h:panelGrid>
    		</f:facet>
		</p:dialog>
	</ui:define>
</ui:composition>

