<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:c="http://java.sun.com/jsp/jstl/core"  
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
<h:head>
	<script type="text/javascript">
		//<![CDATA[ 
		function beforeSearchAction(){
			var dynaData={};
			jQuery("[id*=DYNA_SEARCH]").each(function(){
				var value = jQuery(this).val();
				if(value==null || value=='')
					return;
				jQuery(dynaData).attr(jQuery(this).attr("id"), value);
			});
			jQuery("#searchJson").val(JSON.stringify(dynaData));
			searchAction();
		}
		//]]>
	</script>
</h:head>

<h:body>
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{dynaFormStatisticsBean}"/>
	<ui:param name="onfocus" value="false"></ui:param>
    <h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	<h:outputStylesheet name="css/ui-tabs.css"/>
	
	<h:inputHidden id="searchJson" value="#{mgrbean.searchJson}"/>
	<h:inputHidden id="dataListJson" value="#{mgrbean.dataListJson}"/>
	<h:inputHidden id="rid" value="#{mgrbean.rid}"/>
	<h:inputHidden id="searchHtml" value="#{mgrbean.searchHtml}"/>
	<h:inputHidden id="tableTopTitleJson" value="#{mgrbean.tableTopTitleJson}"/>
	
	<p:tabView id="tabView" dynamic="true" cache="true" activeIndex="#{mgrbean.activeTab}" 
	 style="border:1px; padding:0px;" widgetVar="tab-veiw" >
	 	 <p:tab id="list" title="mainTitle" titleStyle="display:none;">
			<h:form id="mainForm">
				<script type="text/javascript">
					//<![CDATA[ 
					function appendSearchHtml(){
						jQuery("#listDiv").html(jQuery("#searchHtml").val());
						jQuery("#tableTopTitleJson").val(jQuery("#topTitle").val());
						uploadTopTitleJson();
					}
					
					jQuery(function(){
						appendSearchHtml();
					});
					//]]>
				</script>
				<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                    <f:facet name="header">
                        <p:row>
					        <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
					            <h:outputText value="#{mgrbean.statisticsDef.formName}"/>
					        </p:column>
					    </p:row>
                    </f:facet>
				</p:panelGrid>	
				<p:outputPanel id="buttonsPanel">
					<p:outputPanel styleClass="zwx_toobar_42">
						<h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
							<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" onclick="beforeSearchAction()"/>
							<p:remoteCommand name="searchAction" action="#{mgrbean.searchAction}" update=":dataListJson" process="@this,:searchJson" oncomplete="dyna_search();"></p:remoteCommand>
							<p:remoteCommand name="uploadTopTitleJson" process="@this,:tableTopTitleJson"></p:remoteCommand>
							<p:commandButton value="导出" icon="ui-icon-arrowthickstop-1-s" id="dwnloadBtn" 
								onclick="PrimeFaces.monitorDownload(showStatus, hideStatus);"
								ajax="false" process="@this">
								<p:fileDownload value="#{mgrbean.downloadFile}"/>
							</p:commandButton>
						</h:panelGrid>
					</p:outputPanel>
				</p:outputPanel>
				<div id="defaultMsg" /> 
				<div id="listDiv" style="margin-top:5px;">
				</div>
			</h:form>
	 	 </p:tab>
	 	 <p:tab id="edit" title="edit" titleStyle="display:none;">
	 	 </p:tab>
	 	 <p:tab id="view" title="view" titleStyle="display:none;">
	 	 </p:tab>
	 	 <p:tab id="other" title="other" titleStyle="display:none;">
	 	 </p:tab>
	 </p:tabView>
	<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/primeui.xhtml"/>
    <ui:include src="/WEB-INF/templates/system/echarts.xhtml"></ui:include>
</h:body>
</f:view>	
</html>
<!-- 带转向、真分页的模板 -->