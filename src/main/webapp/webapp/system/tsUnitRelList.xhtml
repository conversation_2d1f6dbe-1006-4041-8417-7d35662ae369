<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tsUnitRelBean}" />
	<!-- 编辑页面 -->
	<ui:param name="editPage" value="/webapp/system/tsUnitRelEdit.xhtml" />
	<ui:param name="viewPage" value="/webapp/system/tsUnitRelView.xhtml" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

		<style type="text/css">
.ui-picklist .ui-picklist-list {
	text-align: left;
	height: 350px;
	width: 340px;
	overflow: auto;
}

.ui-picklist .ui-picklist-filter {
	padding-right: 0px;
	width: 98%;
}
</style>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="单位关系" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{tsUnitRelBean.searchAction}" update="dataTable"
					process="@this,searchZone,searchUnitListOneMenu" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
					action="#{tsUnitRelBean.addInitAction}" update=":tabView"
					process="@this">

				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>
      
	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:10%;">
				<h:outputText value="地区：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;width: 20%;">

				<zwx:ZoneSingleNewComp zoneList="#{tsUnitRelBean.zoneList}" zoneCodeNew="#{tsUnitRelBean.searchZoneCode}"
									   zoneName="#{tsUnitRelBean.searchZoneName}" id="searchZone"
									   onchange="onSearchNodeSelect()" zoneType="#{tsUnitRelBean.searchZoneType}"/>

				<p:remoteCommand name="onSearchNodeSelect"
					action="#{tsUnitRelBean.onSearchNodeSelect}"
					process="@this,searchZone"
					update=":tabView:mainForm:searchUnitListOneMenu " />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:10%;">
				<h:outputText value="单位：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 200px;">
				<p:selectOneMenu id="searchUnitListOneMenu"
					value="#{tsUnitRelBean.searchUnitId}" style="width: 190px;">
					<f:selectItem itemLabel="--请选择--" itemValue="" />
					<f:selectItems value="#{tsUnitRelBean.searchUnitMap}" />
				</p:selectOneMenu>
			</p:column>
		</p:row>


	</ui:define>

	<!-- 表格列 -->
<ui:define name="insertDataTable">
		
		<p:column headerText="单位" style="width: 200px;padding-left: 3px;text-align:center;">
			<h:outputText value="#{itm[3]}" />
		</p:column>
		<p:column headerText="关系类型" style="width: 80px;padding-left: 3px;text-align:center;">
			<h:outputText value="委托"  rendered="#{itm[1]==1}" />
			<h:outputText value="代管"  rendered="#{itm[1]==2}" />
		</p:column>
		<p:column headerText="操作" style="width: 80px;padding-left: 3px;text-align:center;">
		<p:commandLink value="修改" action="#{tsUnitRelBean.modInitAction}"
				id="modBtn" process="@this" update=":tabView" >

				<f:setPropertyActionListener target="#{tsUnitRelBean.rid}"
					value="#{itm[0]}" />
						<f:setPropertyActionListener target="#{tsUnitRelBean.relEditType}"
					value="#{itm[1]}" />
				
			</p:commandLink>
			<p:spacer width="5px"></p:spacer>
			<p:commandLink value="删除" action="#{tsUnitRelBean.deleteAction}"
				id="deleteBtn" process="@this" update=":tabView" >
               <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tsUnitRelBean.rid}"
					value="#{itm[0]}" />
						<f:setPropertyActionListener target="#{tsUnitRelBean.relEditType}"
					value="#{itm[1]}" />
				
			</p:commandLink>
			<p:spacer width="5px"></p:spacer>
			
		<p:commandLink value="查看" action="#{tsUnitRelBean.viewInitAction}"
				id="viewBtn" process="@this" update=":tabView" >	
			<f:setPropertyActionListener target="#{tsUnitRelBean.rid}"
					value="#{itm[0]}" />
						<f:setPropertyActionListener target="#{tsUnitRelBean.relEditType}"
					value="#{itm[1]}" />
				
			</p:commandLink>
		</p:column>
</ui:define>
	<!-- 其它内容 -->
	<ui:define name="insertOtherMainContents">
		
	</ui:define>

</ui:composition>











