<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/viewTemplate.xhtml">
	<ui:define name="insertScripts">
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
	</ui:define>
	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="节假日配置" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 编辑页面的按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			<span class="ui-separator"
				style="float: left;margin: 7px 3px auto 5px;"><span
				class="ui-icon ui-icon-grip-dotted-vertical" /></span>
			<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
				action="#{tsHolidayBean.saveAddListAction}"
				process="@this,:tabView:viewForm:editGrid" update=":tabView" />
			<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
				action="#{tsHolidayBean.backAction}" update=":tabView"
				immediate="true" />
		</p:outputPanel>
	</ui:define>

	<ui:define name="insertEditContent">
		<p:row>
			<p:column style="height:38px;width:180px">
				<p:outputLabel style="color:red">*</p:outputLabel>
				<h:outputText value="日期类型："></h:outputText>
			</p:column>
			<p:column style="height:38px;">
				<p:selectOneRadio value="#{tsHolidayBean.addListtypes}"
					style="width:200px;">
					<f:selectItem itemLabel="工作日" itemValue="0" />
					<f:selectItem itemLabel="休息日" itemValue="1" />
					<f:selectItem itemLabel="节假日" itemValue="2" />
				</p:selectOneRadio>
			</p:column>

		</p:row>
		<p:row>
			<p:column colspan="2">
				<p:commandButton value="添加日期" icon="ui-icon-plus" id="addBtn"
					resetValues="true" action="#{tsHolidayBean.addAction}"
					update="subTab" process="@this,subTab">
				</p:commandButton>
			</p:column>
		</p:row>
		<p:row>
			<p:column colspan="2">
				<p:dataTable var="itm" value="#{tsHolidayBean.holidayList}"
					id="subTab" emptyMessage="没有数据！">
					<p:column headerText="开始日期 ~结束日期"
						style="text-align: center;width:300px;padding-left:3px;word-wrap: break-word;word-break: break-all;">
						<p:calendar navigator="true" yearRange="c-10:c"
							value="#{itm.searchBeginDate}" size="11"
							converterMessage="日期格式输入不正确！" pattern="yyyy-MM-dd"
							showButtonPanel="true" showOtherMonths="true" />
							~
							<p:calendar navigator="true" yearRange="c-10:c"
							value="#{itm.searchEndDate}" size="11"
							converterMessage="日期格式输入不正确！" pattern="yyyy-MM-dd"
							showButtonPanel="true" showOtherMonths="true" />
					</p:column>
					<p:column headerText="删除" 	>
						<p:commandLink update=":tabView:viewForm:subTab"
							action="#{tsHolidayBean.delTsHoliday}" process="@this,:tabView:viewForm:subTab" value="删除"
							>
							<f:setPropertyActionListener target="#{tsHolidayBean.tsHoliday}"
								value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:column>
		</p:row>

	</ui:define>
	<!-- 其它内容 -->

</ui:composition>


