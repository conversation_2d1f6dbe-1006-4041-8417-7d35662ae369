<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <h:outputScript name="js/datatable.js" />
        <style type="text/css">
            .table-border-none tr{
                border: none transparent !important;
            }
            .table-border-none tr td{
                border: transparent !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
                function removeExtraZonePael(){
                    //移除掉多余的地区框
                    var el = jQuery('#tabView\\:editForm\\:searchNoticeZone\\:zonePanel');
                    if(el.length>1){
                        el.each(function(index){
                            if(index>0){
                                $(this).remove();
                            }
                        });
                    }
                    //移除多余的单位属性框
                    el = jQuery('#tabView\\:editForm\\:searchNoticeUnitSort\\:dataOverPanel');
                    if(el.length>1){
                        el.each(function(index){
                            if(index>0){
                                $(this).remove();
                            }
                        });
                    }
                }

                /**
                 * 工具栏提示汉化
                 * */
                function transferChineseTitle(){
                    //获取所有的工具栏标签
                    var elements = $("#tabView\\:editForm\\:editor").find("div");
                    if(undefined != elements && elements.length > 0){
                        elements.each(function(){
                            var title = $(this).attr("title");
                            if($(this).hasClass("ui-editor-button") && undefined != title){
                                if(title == "Bold"){
                                    $(this).attr("title","加粗");
                                }else if(title == "Italic"){
                                    $(this).attr("title","倾斜");
                                }else if(title == "Underline"){
                                    $(this).attr("title","下划线");
                                }else if(title == "Strikethrough"){
                                    $(this).attr("title","删除线");
                                }else if(title == "Subscript"){
                                    $(this).attr("title","下标");
                                }else if(title == "Superscript"){
                                    $(this).attr("title","上标");
                                }else if(title == "Font"){
                                    $(this).attr("title","字体");
                                }else if(title == "Font Size"){
                                    $(this).attr("title","字号");
                                }else if(title == "Style"){
                                    $(this).attr("title","字体样式");
                                }else if(title == "Font Color"){
                                    $(this).attr("title","字体颜色");
                                }else if(title == "Text Highlight Color"){
                                    $(this).attr("title","字体背景颜色");
                                }else if(title == "Bullets"){
                                    $(this).attr("title","项目符号");
                                }else if(title == "Numbering"){
                                    $(this).attr("title","编号");
                                }else if(title == "Align Text Left"){
                                    $(this).attr("title","左对齐");
                                }else if(title == "Center"){
                                    $(this).attr("title","居中对齐");
                                }else if(title == "Align Text Right"){
                                    $(this).attr("title","右对齐");
                                }else if(title == "Justify"){
                                    $(this).attr("title","两端对齐");
                                }else if(title == "Undo"){
                                    $(this).attr("title","撤销");
                                }else if(title == "Redo"){
                                    $(this).attr("title","恢复");
                                }else if(title == "Insert Horizontal Rule"){
                                    $(this).attr("title","水平线");
                                }else if(title == "Cut"){
                                    $(this).attr("title","剪切");
                                }else if(title == "Copy"){
                                    $(this).attr("title","复制");
                                }else if(title == "Paste"){
                                    $(this).attr("title","粘贴");
                                }
                            }
                        });
                    }
                }
            //]]>
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知发布" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0px;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk"
                                 action="#{mgrbean.savePublishNotice}" update=":tabView" oncomplete="transferChineseTitle();"
                                 process="@this,:tabView:editForm"></p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.submitPublishNotice}" update=":tabView"
                                 process="@this,:tabView:editForm"></p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView" immediate="true" >
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <p:sticky target="sticky" />
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:confirmDialog message="切换发布类型将清空通知对象，是否继续？" header="消息确认框" widgetVar="NoticeConfirmDialog">
            <p:outputPanel style="text-align:right;">
                <p:commandButton value="确定" action="#{mgrbean.sureChangeCheckType}" icon="ui-icon-check" process="@this,:tabView:editForm" update=":tabView:editForm" oncomplete="PF('NoticeConfirmDialog').hide();PF('PublishTypePanel').hide();"/>
                <p:commandButton value="取消" icon="ui-icon-close" action="#{mgrbean.cancelChangeCheckType}" process="@this" oncomplete="PF('NoticeConfirmDialog').hide();" update=":tabView:editForm:publishTypeName,:tabView:editForm:publishTypeId"/>
            </p:outputPanel>
        </p:confirmDialog>
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom: 10px;" id="publishNoticeHeadId">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left; padding-left: 5px; " colspan="3">
                        <p:outputLabel value="通知发布内容"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:150px;height: 39px;">
                    <h:outputText value="*" style="color:red;" />
                    <p:outputLabel value="发布类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px; " colspan="2">
                    <h:panelGrid columns="4" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                        <p:inputText id="publishTypeName"
                                     value="#{mgrbean.selectedPublishType.codeName}" readonly="true"
                                     style="width: 203px;"  />
                        <p:commandLink styleClass="ui-icon ui-icon-search"
                                       id="initTreeLink2" process="@this"
                                       style="position: relative;left: -30px;top:0px;"
                                       oncomplete="PF('PublishTypePanel').show()" />
                        <h:inputHidden id="publishTypeId"  rendered="true"
                                       requiredMessage="请选择发布类型！"
                                       value="#{mgrbean.selectedPublishType.rid}" />
                        <p:inputText value="#{mgrbean.curPublishNotice.otherType}" maxlength="6" style="width: 240px;position: relative;left: -35px;" rendered="#{mgrbean.ifOtPublishType}" />
                    </h:panelGrid>
                    <p:overlayPanel id="publishTypePanel" for="publishTypeName"
                                    style="width:210px;" widgetVar="PublishTypePanel"
                                    dynamic="false">
                        <p:tree var="node" selectionMode="single" id="choiceTree2"
                                value="#{mgrbean.publishTypeTree}"
                                style="width: 180px;height: 300px;overflow-y: auto;">
                            <p:ajax event="select"
                                    update=":tabView:editForm:publishTypeName,:tabView:editForm:publishTypeId"
                                    listener="#{mgrbean.onPublishTypeNodeSelect}" process="@this,:tabView:editForm"
                            />
                            <p:treeNode>
                                <h:outputText value="#{node.codeName}" />
                            </p:treeNode>
                        </p:tree>
                    </p:overlayPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 39px;">
                    <h:outputText value="*" style="color:red;" />
                    <p:outputLabel value="标题："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="2">
                    <p:inputText value="#{mgrbean.curPublishNotice.title}" maxlength="50" style="width: 300px;" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 39px;">
                    <h:outputText value="*" style="color:red;" />
                    <p:outputLabel value="是否需要回执："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="2">
                    <p:selectOneRadio value="#{mgrbean.curPublishNotice.ifNeedFeedback}"  >
                        <f:selectItem itemLabel="否" itemValue="0" />
                        <f:selectItem itemLabel="是" itemValue="1" />
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;">
                    <p:outputLabel value="内容："/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;" colspan="2">
                    <!-- 详情disabled="true" -->
                    <p:editor id="editor" widgetVar="editorWidget" value="#{mgrbean.curPublishNotice.content}" width="1200" height="600" onchange="transferChineseTitle();"
                              controls="bold italic underline strikethrough subscript superscript font size style color highlight bullets numbering alignleft center alignright justify undo redo rule cut copy paste"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <script type="text/javascript">
            //<![CDATA[
            transferChineseTitle();
            //]]>
        </script>
        <!-- 参考tdYsjcLaborWorkPlaceEdit -->
        <p:panelGrid  style="margin-bottom: 10px;width:100%" id="noticeAnnexPanel" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left;padding-left: 5px; " >
                        <p:outputLabel value="通知发布附件"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:left;" >
                    <p:commandButton value="上传" icon="ui-icon-plus"  process="@this" oncomplete="PF('FileDialog').show();"
                                     update=":tabView:editForm:fileDialog"
                                     style="margin:3px 10px;">
                    </p:commandButton>
                    <p:dataTable value="#{mgrbean.publishNoticeAnnexList}" var="itm" id="noticeAnnexDataTable" rows="#{mgrbean.noticeUnitPageSize}" paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{'10,20,50'}"  emptyMessage="没有您要找的记录！" paginator="true"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                        <p:column headerText="序号" style="width: 80px;text-align: center;" >
                            <p:outputLabel value="#{R+1}" />
                        </p:column>
                        <p:column headerText="附件名称" style="width: 380px;" >
                            <p:outputLabel value="#{itm.annexName}" />
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="下载" process="@this" immediate="true"
                                           ajax="false" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);" >
                                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.curNoticeAnnex}"/>
                                <p:fileDownload value="#{mgrbean.noiceAnnexStreamedContent}" />
                            </p:commandLink>
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" action="#{mgrbean.removePublishNoticeAnnex}" process="@this" update=":tabView:editForm:noticeAnnexPanel">
                                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.curNoticeAnnex}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" id="noticeUnitPanel" >
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left;padding-left: 5px; ">
                        <p:outputLabel value="通知对象"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:left;">
                    <p:commandButton value="添加" icon="ui-icon-plus"
                                     action="#{mgrbean.ifShowPublishNoticeUnitDialog}" process="@this,:tabView:editForm"
                                     oncomplete="removeExtraZonePael();"
                                     style="margin:3px 10px;">
                    </p:commandButton>
                    <p:dataTable value="#{mgrbean.publishNoticeUnitList}" id="noticeUnitDataTable" var="itm" rows="#{mgrbean.noticeUnitPageSize}" paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{'10,20,50'}"  emptyMessage="没有您要找的记录！" paginator="true"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                        <p:column headerText="序号" style="width: 80px;text-align: center;" >
                            <p:outputLabel value="#{R+1}" />
                        </p:column>
                        <p:column headerText="地区" style="width: 260px;" >
                            <p:outputLabel value="#{itm.fkByUnitId.tsZone.zoneType>3?itm.fkByUnitId.tsZone.fullName.substring(itm.fkByUnitId.tsZone.fullName.indexOf('_')+1,itm.fkByUnitId.tsZone.fullName.length()):itm.fkByUnitId.tsZone.zoneName}" />
                        </p:column>
                        <p:column headerText="单位名称" style="width: 380px;" >
                            <p:outputLabel value="#{itm.fkByUnitId.unitname}" />
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="删除" action="#{mgrbean.removePublishNoticeUnit}" process="@this" update=":tabView:editForm:noticeUnitPanel">
                                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.curNoticeUnit}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 附件上传 -->
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true" >
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：doc、docx、xls、xlsx、pdf、zip、rar、7z）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 60px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要上传的文件！"
                                      label="文件选择" fileUploadListener="#{mgrbean.uploadPublishNoticeAnnex}"
                                      invalidSizeMessage="文件大小不能超过500M!" validatorMessage="上传出错啦，请重新上传！"
                                      style="width:800px;" previewWidth="120" cancelLabel="取消" multiple="true"
                                      uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="524288000"
                                      invalidFileMessage="无效的文件类型！只能上传doc,docx,xls,xlsx,pdf,zip,rar,7z类型文件"
                                      allowTypes="/(\.|\/)(docx?|xlsx?|pdf|zip|rar|7z)$/" oncomplete="PF('FileDialog').hide();"
                        />
                    </td>
                </tr>
            </table>
        </p:dialog>
        <!-- 通知对象选择 -->
        <p:dialog header="通知对象" widgetVar="PublishNoticeUnitDialog" id="publishNoticeUnitDialog"
                  resizable="false" modal="true"  width="800" height="530">
            <p:outputPanel styleClass="zwx_toobar_42" style="display: flex;">
                <h:panelGrid columns="6">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                    <p:commandButton value="查询" icon="ui-icon-search" action="#{mgrbean.searchShowUnitObjList}"
                                     process="@this,publishNoticeUnitSearchPanel,publishNoticeUnitDataTable" update="publishNoticeUnitDataTable"/>
                    <p:commandButton value="确定" icon="ui-icon-check" action="#{mgrbean.sureUnitObjSelect}"
                                     process="@this,publishNoticeUnitDataTable"  update="publishNoticeUnitDataTable"  />
                    <p:commandButton value="全部选择" icon="ui-icon-check" action="#{mgrbean.selectAllUnitObjAction}"
                                     process="@this,publishNoticeUnitDataTable"  update="publishNoticeUnitDataTable"  />
                    <p:commandButton value="取消" icon="ui-icon-close" process="@this" oncomplete="PF('PublishNoticeUnitDialog').hide();" />
                </h:panelGrid>
            </p:outputPanel>
            <p:panelGrid style="width:100%;" id="publishNoticeUnitSearchPanel" styleClass="table-border-none">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 38px;width: 300px; border: transparent;">
                        <div style="display: table-row; height: 38px;">
                            <div style="display:table-cell;vertical-align: middle;padding-left: 5px;width: 85px;">
                                <h:outputText value="地区：" />
                            </div>
                            <!-- 地区组件列表是否重新初始化 resetList true 并且 resetTimes > setedTimes -->
                            <div style="display:table-cell;vertical-align: middle;padding-right: 5px;">
                                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.publishNoticeShowZoneList}"
                                                       zoneCodeNew="#{mgrbean.noticeZoneCode}" zoneId="#{mgrbean.noticeZoneId}"
                                                       ifShowTrash="false"  zonePaddingLeft="4" resetList="true" resetTimes="100000" setedTimes="0"
                                                       zoneName="#{mgrbean.noticeZoneName}" id="searchNoticeZone"/>
                            </div>
                        </div>
                    </p:column>
                    <p:column style="text-align:left;padding-left:9px;height: 38px;border: transparent;">
                        <div style="display: table-row; height: 38px;">
                            <div style="display:table-cell;vertical-align: middle;padding-left: 5px;width: 65px;">
                                <h:outputText value="单位名称：" />
                            </div>
                            <div style="display:table-cell;vertical-align: middle;padding-left: 5px;padding-right: 5px;">
                                <p:inputText value="#{mgrbean.noticeUnitName}"  style="width: 180px;" />
                            </div>
                        </div>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;height: 38px;border: transparent;" colspan="2">
                        <div style="display: table-row; height: 38px;">
                            <div style="display:table-cell;vertical-align: middle;padding-left: 5px;width: 80px;">
                                <h:outputText value="单位属性：" />
                            </div>
                            <div style="display:table-cell;vertical-align: middle;padding-right: 5px;">
                                <zwx:SelectManyMenuNewComp id="searchNoticeUnitSort" dataMap="#{mgrbean.bsSortMap}" height="200" zonePaddingLeft="0"
                                                           dataValue="#{mgrbean.sortSelectRids}" dataLabel="#{mgrbean.sortSelectNames}"/>
                            </div>
                        </div>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:dataTable var="itm" value="#{mgrbean.showUnitObjList}"   id="publishNoticeUnitDataTable" paginator="true"
                         rows="#{mgrbean.unitSelectPageSize}" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         emptyMessage="没有您要找的记录！" paginatorPosition="bottom" pageLinks="5" rowIndexVar="R"
                         rowsPerPageTemplate="#{'10,20,50'}"
                         selection="#{mgrbean.selectedUnitObjList}"  rowKey="#{itm[0]}" rowSelectMode="add">
                <p:ajax event="page" process="@this,publishNoticeUnitDataTable"/>
                <p:ajax event="rowSelect" process="@this,publishNoticeUnitDataTable" listener="#{mgrbean.rowSelectListener}" immediate="true" />
                <p:ajax event="rowUnselect" process="@this,publishNoticeUnitDataTable" listener="#{mgrbean.rowUnselectListener}" immediate="true" />
                <p:ajax event="rowSelectCheckbox" process="@this,publishNoticeUnitDataTable" listener="#{mgrbean.rowSelectListener}" immediate="true" />
                <p:ajax event="rowUnselectCheckbox" process="@this,publishNoticeUnitDataTable" listener="#{mgrbean.rowUnselectListener}" immediate="true" />
                <p:ajax event="toggleSelect" process="@this,publishNoticeUnitDataTable" listener="#{mgrbean.toggleSelectListener}" immediate="true" />

                <!-- 固定列宽 所有列都按百分比设置宽度 -->
                <p:column style="width: 6%;text-align: center;" selectionMode="multiple" />
                <p:column style="width: 37%;" headerText="地区">
                    <h:outputText value="#{itm[1]}" />
                </p:column>
                <p:column  headerText="单位名称" style="width: 57%;">
                    <h:outputText value="#{itm[2]}" />
                </p:column>
            </p:dataTable>
        </p:dialog>
    </ui:define>
</ui:composition>