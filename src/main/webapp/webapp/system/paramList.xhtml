<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <!-- 脚本 -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <style type="text/css">
            .ui-selectmanycheckbox.ui-widget td, .ui-selectoneradio.ui-widget td {
                border: 0 none;
                padding: 2px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="参数管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{paramBean.searchAction}" update="businessPanel" process="@this,searchParamName,searchParamDesc" />
				<p:commandButton value="保存" icon="ui-icon-check" id="addBtn" action="#{paramBean.saveAction}" update="businessPanel" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="参数名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 250px;">
                <p:inputText id="searchParamName" value="#{paramBean.searchParamName}" maxlength="15"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="参数描述：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText id="searchParamDesc" value="#{paramBean.searchParamDesc}" maxlength="20"/>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 具体内容 -->
    <ui:define name="insertContent">
        <!--多个业务模块的参数-->
        <p:outputPanel style="margin-top: 5px;" id="businessPanel" binding="#{paramBean.businessPanel}">

        </p:outputPanel>
    </ui:define>

</ui:composition>











