<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

	<style type="text/css">
body {
	overflow-x: hidden;
}
</style>
	<h:form id="subViewForm">
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
			id="viewTitleGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="字段信息详情" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					update=":tabView" process="@this">
					<f:setPropertyActionListener
						target="#{tdDynaFormStructureBean.activeTab}" value="2"></f:setPropertyActionListener>
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
		<p:outputPanel id="editGrid">
			<p:fieldset toggleable="true" legend="基本信息" style="margin-top:5px;">

				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="字段名：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.fdEnname}" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:150px;">
							<p:outputLabel value="字段说明：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="3">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.fdCnname}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="字段类型：" />
						</p:column>
						<p:column
							style="text-align:left;padding-left:8px;padding:0 0 0 0;"
							colspan="5">
							<h:panelGrid columns="10" id="fieldGrid"
								style="border-color:#FFFFFF;">
								<p:outputLabel
									value="#{tdDynaFormStructureBean.selTdFormField.fdDbtypeStr}" />
								<p:outputLabel value="字符串长度："
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NVARCHAR2' }" />
								<p:outputLabel
									value="#{tdDynaFormStructureBean.selTdFormField.lenChar}"
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NVARCHAR2' }" />
								<p:outputLabel value="整数长度："
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='INTEGER' or tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }" />
								<p:outputLabel
									value="#{tdDynaFormStructureBean.selTdFormField.lenInt}"
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='INTEGER'  or tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }" />
								<p:outputLabel value="小数长度："
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }" />
								<p:outputLabel
									value="#{tdDynaFormStructureBean.selTdFormField.lenDemi}"
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }" />
							</h:panelGrid>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>
			<p:fieldset toggleable="true" legend="扩展信息" style="margin-top:5px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column style="text-align:right;padding-right:8px;width:150px;">
							<p:outputLabel value="是否列表显示：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.listBol?'是':'否'}" />
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="编辑时显示类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.isShow=='0'?'不显示':(tdDynaFormStructureBean.selTdFormField.isShow=='1'?'正常显示':(tdDynaFormStructureBean.selTdFormField.isShow=='2'?'只读显示':''))}" />
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="是否必输：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.reqBol?'是':'否'}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="编辑时行号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.rowNum}" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:150px;">
							<p:outputLabel value="编辑时列号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.colNum}" />
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="所占列数：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.colSpan}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="展示类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="3">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.dataSrcStr}" />
							<p:spacer width="5" />
							<p:outputLabel
								value="码表类型：#{tdDynaFormStructureBean.selCode==null?null:tdDynaFormStructureBean.selCode.codeTypeDesc}"
								rendered="#{tdDynaFormStructureBean.selCode!=null}" />
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="是否流程变量：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.proValBol?'是':'否'}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="脚本类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.selTdFormField.scriptType=='0'?'页面初始化脚本':(tdDynaFormStructureBean.selTdFormField.scriptType=='1'?'保存前执行脚本':'')}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="数据设置脚本：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:inputTextarea readonly="true"
								value="#{tdDynaFormStructureBean.selTdFormField.dataScript}"
								autoResize="false" rows="5" cols="100" maxlength="1000" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>
		</p:outputPanel>
	</h:form>
</ui:composition>
