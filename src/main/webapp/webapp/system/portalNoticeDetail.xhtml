<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
    <h:head>
    </h:head>
    <link rel="stylesheet" href="/resources/css/portal/notice.css" />
    <style type="text/css">

    </style>
    <!--    <ui:param name="mgrbean" value="#{tsUserDeskShowBean}"/>-->
    <h:form id="myform" style="padding-top: 38px;">
        <p:outputPanel styleClass="notification-detail-card">
            <p:outputPanel styleClass="notification-detail-header">
                <p:outputPanel styleClass="notification-detail-title">
                    <h:outputText value="#{tsUserDeskShowBean.publishNotice.title}"/>
                </p:outputPanel>
                <p:outputPanel styleClass="notification-subtitle">
                    <h:outputLabel value="发布日期：" />
                    <h:outputLabel style="margin-right: 20px;" value="#{tsUserDeskShowBean.publishNotice.publishDate}" />
                    <h:outputLabel value="发布机构：" />
                    <h:outputLabel style="margin-right: 20px;" value="#{tsUserDeskShowBean.publishNotice.fkByPublishUnitId.unitname}" />
                    <h:outputLabel value="发布类型：" />
                    <h:outputLabel value="#{tsUserDeskShowBean.publishNotice.fkByPublishTypeId.codeName}" rendered="#{tsUserDeskShowBean.publishNotice.fkByPublishTypeId.extendS1!='1'}"/>
                    <h:outputLabel value="#{tsUserDeskShowBean.publishNotice.otherType}" rendered="#{tsUserDeskShowBean.publishNotice.fkByPublishTypeId.extendS1=='1'}"/>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel style="height: 1px;background: #D9DEE4;"/>
            <div style="padding: 10px 30px;">
                <h:outputText style="word-break: break-word;" id="display" value="#{tsUserDeskShowBean.publishNotice.content}" escape="false" />
            </div>
            <p:outputPanel style="height: 1px;background: #D9DEE4;" rendered="#{tsUserDeskShowBean.publishNotice.content!=null}"/>
            <p:outputPanel  styleClass="notification-detail-body">
                <p:outputPanel styleClass="notification-detail-footer">
                    <p:outputPanel styleClass="notification-detail-footer-text">

                        <div style="display: flex;min-width: 70px;">
                            <h:outputText value="相关附件：" />
                        </div>
                        <div style="display: flex;flex-direction: column;align-items: flex-start;">
                            <c:forEach var="item" items="#{tsUserDeskShowBean.publishNotice.noticeAnnexList}" varStatus="itemStatus">
                                <p:commandLink value="#{item.annexName}" process="@this"
                                               style="padding-left: 5px;text-decoration: none;color: #0073ea;padding-bottom: 20px;"
                                               ajax="false"  styleClass="linktext">
                                    <f:setPropertyActionListener value="#{item}" target="#{tsUserDeskShowBean.curNoticeAnnex}"/>
                                    <p:fileDownload value="#{tsUserDeskShowBean.noiceAnnexStreamedContent}" />
                                </p:commandLink>
                            </c:forEach>
                        </div>
                    </p:outputPanel>
                </p:outputPanel>
            </p:outputPanel >
        </p:outputPanel>

    </h:form>
    <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
</f:view>
</html>

