<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">
	<h:form id="viewForm">
		<p:panelGrid style="width:100%;margin-bottom:5px;" id="viewTitleGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
						<p:outputLabel value="基本信息" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>

		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tdInfoMainBean.backAction}" update=":tabView" immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
		<p:panelGrid style="width:100%;margin-top:5px;" id="viewGrid">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:13%;">
					<p:outputLabel value="标题：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;">
					<p:outputLabel value="#{tdInfoMainBean.tdInfoMain.infoTitle}" size="100" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:13%;">
					<p:outputLabel value="内容：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;">
					<p:inputTextarea rows="10" cols="120" autoResize="false" readonly="true" value="#{tdInfoMainBean.tdInfoMain.infoContent}" />
					<h:outputText id="display" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:13%;">
					<p:outputLabel value="附件：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;">
					<p:dataTable value="#{tdInfoMainBean.annexList}" var="v" style="width:60%" type="ordered" emptyMessage="没有上传的文件记录！">
						<p:column headerText="文件名称" style="width: 45%;">
							<p:outputLabel value="#{v.annexName}" />
						</p:column>
						<p:column headerText="操作" style="width: 15%;">
							<p:commandLink ajax="false" value="下载" immediate="true" process="@this"
								onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
								<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
								<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
								<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
							</p:commandLink>
						</p:column>
					</p:dataTable>
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:panelGrid style="width:100%;" id="empGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
						<p:outputLabel value="人员信息" />
					</p:column>
				</p:row>
			</f:facet>
			<p:row>
				<p:column style="text-align:left;padding-left:3px;">
					<p:dataTable value="#{tdInfoMainBean.viewUserList}" var="v" style="width:100%" type="ordered" emptyMessage="没有人员接收记录！">
						<p:column headerText="科室" style="width: 15%;">
							<p:outputLabel value="#{v[0]}" />
						</p:column>
						<p:column headerText="人员" style="width: 15%;">
							<p:outputLabel value="#{v[1]}" />
						</p:column>
						<p:column headerText="接收时间" style="width: 15%;">
							<p:outputLabel value="#{v[2]}" />
						</p:column>
					</p:dataTable>
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:blockUI block="viewGrid" widgetVar="empBlock" />
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
	</h:form>
</ui:composition>

