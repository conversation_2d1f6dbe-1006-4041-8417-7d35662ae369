<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdInfoMainBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>

    <!-- 信息发布 -->
    <ui:param name="editPage" value="/webapp/system/tdInfoMainEdit.xhtml"/>
    <!-- 信息发布 -->
    <ui:param name="viewPage" value="/webapp/system/tdInfoMainView.xhtml"/>

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
	</ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="我的寻呼"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tdInfoMainBean.searchAction}" update="dataTable" process="@this,date4,date5,searchTitle" />
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{tdInfoMainBean.addInitAction}" update=":tabView" process="@this">
                    <p:resetInput target=":tabView:editForm:editGrid,:tabView:editForm:empGrid"/>
                </p:commandButton>               
			</h:panelGrid>
		</p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
         	<p:column style="text-align:right;padding-right:3px;width:15%">
            	<p:outputLabel value="标题："/>
            </p:column>
            <p:column style="text-align:left;width:25%">
            	<p:inputText value="#{tdInfoMainBean.searchInfoTitle}" id="searchTitle"
                             size="20" maxlength="100" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:15%">
            	<p:outputLabel value="发布日期："/>
            </p:column>
            <p:column style="text-align:left;">
            	<p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="date4"
                            navigator="true" yearRange="c-10:c+10"
                            converterMessage="发布日期，格式输入不正确！" 
                            showButtonPanel="true"
                            value="#{tdInfoMainBean.searchBeginDate}"/>
                <p:outputLabel value="～"/>
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="date5"
                            navigator="true" yearRange="c-10:c+10"
                            showButtonPanel="true"
                            converterMessage="发布日期，格式输入不正确！"  
                            value="#{tdInfoMainBean.searchEndDate}"/>
            </p:column>
           
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="标题" style="width: 70%;">
            <p:outputLabel value="#{itm.infoTitle}" />
        </p:column>
        <p:column headerText="发布时间" style="width: 11%;text-align: center;">
            <p:outputLabel value="#{itm.publishTime}" >
                <f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;width: 10%;">
        	<p:commandLink value="详情"  id="xq" action="#{tdInfoMainBean.viewInitAction}" update=":tabView" process="@this" >
                <f:setPropertyActionListener value="#{itm.rid}" target="#{tdInfoMainBean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>