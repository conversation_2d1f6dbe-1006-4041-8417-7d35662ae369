<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdSysApplyAccountCheckListBean}"/>

    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/tdSysApplyAccountCheckEdit.xhtml" />
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/system/tdSysApplyAccountCheckView.xhtml" />

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <script type="text/javascript">
        </script>
        <style type="text/css">
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText rendered="#{mgrbean.tag == null or mgrbean.tag == '1'}" value="注册账号审核"/>
                <h:outputText rendered="#{mgrbean.tag != null and mgrbean.tag == '2'}" value="注册账号查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;width:240px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}" />
            </p:column>

			<p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="社会信用代码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 240px;" >
                <p:inputText value="#{mgrbean.searchCreditCode}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchUnitName}" style="width: 180px;" maxlength="50"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="单位属性：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 240px;">
                <p:selectOneMenu id="searchSystemType" value="#{mgrbean.sortId}" style="width: 188px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.sortTypeMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:38px;">
                <h:outputText value="姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText value="#{mgrbean.searchPersonName}" style="width: 180px;" maxlength="25"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="身份证号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" >
                <p:inputText value="#{mgrbean.searchIdc}" style="width: 180px;" maxlength="50"/>
            </p:column>

        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 160px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItem itemValue="0" itemLabel="待审核"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="审核通过"></f:selectItem>
                    <f:selectItem itemValue="2" itemLabel="已退回"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width: 180px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="社会信用代码" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="单位名称" style="width:210px;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="单位属性" style="width:180px;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="姓名" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="身份证号" style="width:120px;text-align: center;">
            <h:outputText value="#{itm[6]}"/>
        </p:column>
         <p:column headerText="手机号码" style="width:100px;text-align: center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputLabel value="待审核" rendered="#{itm[8]==0}" style="color:red;"/>
            <h:outputLabel value="审核通过" rendered="#{itm[8]==1}" style="color:green;"/>
            <h:outputLabel value="已退回" rendered="#{itm[8]==2}" style="color:red;"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="#{itm[8]=='0'?'审核':'详情'}" rendered="#{mgrbean.tag == null or mgrbean.tag == '1'}"
                           action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>

            <p:commandLink value="详情" rendered="#{mgrbean.tag != null and mgrbean.tag == '2'}"
                           action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

</ui:composition>