<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <ui:param name="onfocus" value="false" />
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            ul li {
                list-style-type: none;
                margin-left: 16px;
            }

            .flex-item:not(:last-child) {
                margin-right: 5px;
            }
        </style>
        <script type="text/javascript">
            function updateState(){
                var timer = setInterval(function() {
                    var downloadComplete = PrimeFaces.getCookie("primefaces.download");
                    if (downloadComplete === null) {//下载完成
                        $("#tabView\\:mainForm\\:searchBtn").click();
                        clearInterval(timer);
                    }else{
                        //未下载完成，继续
                    }
                }, 1000)

            }
        </script>
    </ui:define>
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdTjExportListBean}"/>
    <!-- 编辑页面 -->
    <h:outputStylesheet name="css/default.css"/>
    <h:outputScript library="js" name="namespace.js"/>
    <h:outputScript name="js/validate/system/validate.js"/>
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="导出文件下载"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;"
                           rendered="#{mgrbean.exportFileLivemonth != null and mgrbean.exportFileLivemonth > 0}">
                <h:outputLabel value="注意：" style="color:red;"/>
                <h:outputLabel value="导出文件仅保留#{mgrbean.exportFileLivemonth}月，请及时下载！" style="color:blue;"></h:outputLabel>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 35px;">
                <h:outputText value="类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;width: 260px;">
                <p:selectOneMenu id="exportType" value="#{tdTjExportListBean.searchExportTypeId}"
                                 style="width: 200px;">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItems value="#{tdTjExportListBean.exportTypeList}" var="simItm" itemValue="#{simItm.rid}" itemLabel="#{simItm.codeName}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="导出时间：" />
            </p:column>
            <p:column  style="text-align:left;padding-left:12px;width: 260px;">
                <zwx:CalendarDynamicLimitComp startDate="#{tdTjExportListBean.searchExportSDate}"
                                              styleClass="myCalendar11"  endDate="#{tdTjExportListBean.searchExportEDate}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column>
                <p:selectManyCheckbox value="#{tdTjExportListBean.searchState}" id="searchHolidayState" >
                    <f:selectItem itemLabel="导出中" itemValue="0"/>
                    <f:selectItem itemLabel="导出成功" itemValue="1" />
                    <f:selectItem itemLabel="导出失败" itemValue="2" />
                    <f:selectItem itemLabel="已下载" itemValue="3" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 内容-->
    <ui:define name="insertDataTable">
        <p:column headerText="类型" style="padding-left: 3px;width: 120px;text-align: center;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="导出文件名称" style="width: 170px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="操作单位" style="width: 190px;" rendered="#{tdTjExportListBean.ifAdmin}">
            <h:outputText value="#{itm[10]}" />
        </p:column>
        <p:column headerText="操作人" style="width: 100px; text-align: center;" rendered="#{tdTjExportListBean.ifAdmin}">
            <h:outputText value="#{itm[11]}" />
        </p:column>
        <p:column headerText="导出时间" style="width: 130px;text-align: center;">
            <h:outputText value="#{itm[3]}" >
                <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn" />
            </h:outputText>
        </p:column>
        <p:column headerText="导出文件时间" style="width: 120px;text-align: center;" rendered="#{tdTjExportListBean.ifAdmin}">
            <h:outputText value="#{itm[4]}" >
                <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn"></f:convertDateTime>
            </h:outputText>
        </p:column>
        <p:column headerText="状态" style="width: 60px;text-align: center;">
            <h:outputText value="导出中" rendered="#{itm[5]==0}"/>
            <h:outputText value="导出成功" rendered="#{itm[5]==1}"/>
            <h:outputText value="导出失败" rendered="#{itm[5]==2}"/>
            <h:outputText value="已下载" rendered="#{itm[5]==3}"/>
        </p:column>
        <p:column headerText="导出条件" style="width: 300px;">
            <p:commandLink value="#{itm[6]}" action="#{tdTjExportListBean.showAction}"
                           update=":tabView:mainForm:detailDialog" process="@this"
                           oncomplete="PF('DetailDialog').show();" styleClass="zwx-tooltip">
                <f:setPropertyActionListener value="#{itm[0]}"
                                             target="#{tdTjExportListBean.rid}" />
            </p:commandLink>
        </p:column>
        <p:column headerText="导出失败原因" style="width: 200px;" rendered="#{tdTjExportListBean.ifAdmin}">
            <h:outputText id="exportFalseReason" value="#{itm[7]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="exportFalseReason" style="width:450px;">
                <p:outputLabel value="#{itm[7]}" escape="false"></p:outputLabel>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作">
            <p:outputPanel style="display: flex;">
                <p:commandLink value="下载" process="@this" immediate="true" rendered="#{(itm[5]==1 or itm[5]==3) and itm[8] != null}"
                               ajax="false" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop); updateState();"
                               styleClass="flex-item">
                    <f:setPropertyActionListener target="#{tdTjExportListBean.rid}" value="#{itm[0]}"/>
                    <f:setPropertyActionListener target="#{tdTjExportListBean.filePath}" value="#{itm[8]}"/>
                    <f:setPropertyActionListener target="#{tdTjExportListBean.fileName}" value="#{itm[2]}"/>
                    <p:fileDownload value="#{tdTjExportListBean.downLoadExport}" />
                </p:commandLink>
                <p:commandLink value="删除" process="@this" update="dataTable" action="#{tdTjExportListBean.deleteAction()}"
                               rendered="#{itm[13] and itm[5] eq 3}"
                               styleClass="flex-item">
                    <f:setPropertyActionListener target="#{tdTjExportListBean.rid}" value="#{itm[0]}"/>
                    <f:setPropertyActionListener target="#{tdTjExportListBean.filePath}" value="#{itm[8]}"/>
                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                </p:commandLink>
            </p:outputPanel>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog id="detailDialog" header="导出条件" widgetVar="DetailDialog"
                  resizable="false" width="850" height="450" modal="true" maximizable="true" style="overflow: auto">
            <p:panelGrid style="width: 100%;">
                <c:forEach var="con" items="#{tdTjExportListBean.conditionList}">
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;width:17%;height:40px">
                            <p:outputLabel value="#{con.split('@\\*@')[0]}："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:15px;">
                            <p:outputLabel value="#{con.split('@\\*@')[1]}"/>
                        </p:column>
                    </p:row>
                </c:forEach>
            </p:panelGrid>
        </p:dialog>

    </ui:define>
</ui:composition>
