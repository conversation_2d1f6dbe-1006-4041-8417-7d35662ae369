<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:p="http://primefaces.org/ui"  
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" >
<f:view contentType="text/html">
	
    <h:head>
        <title>#{selectMutilUnitBean.title}</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
    	<style type="text/css">
    	 html{
    		overflow:hidden;
    	} 
    	 .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 296px;
            width: 390px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
        
        .ui-dialog .ui-dialog-content{
         	overflow: hidden;
        }
    	</style>
    </h:head>
    
    <h:body>
        <h:form id="selectForm" style="overflow:hidden">
        	<p:panel  style="width:860px;margin-top:10px;">
		        <table width="100%">
		            <tr>
		                <td width="40" style="text-align: right;padding-right: 3px">地区：</td>
		                <td style="text-align: left;padding-right: 3px">
		                	<zwx:ZoneSingleComp zoneList="#{selectMutilUnitBean.zoneList}" zoneCode="#{selectMutilUnitBean.zoneCode}"
		                		zoneName="#{selectMutilUnitBean.zoneName}" id="editZone" onchange="onNodeSelect()" zoneType="#{selectMutilUnitBean.zoneLevel}"/>
		                	<p:remoteCommand name="onNodeSelect" action="#{selectMutilUnitBean.onNodeSelect}" process="@this,editZone,unitPickList"
		                                 update=":selectForm:unitPickList"/>  
		                </td>
		            </tr>
		        </table>
		        <p:pickList id="unitPickList" value="#{selectMutilUnitBean.unitDualListModel}" var="unit"
		                    itemValue="#{unit}" itemLabel="#{unit.unitName}" converter="system.UnitConvert"
		                    showSourceControls="false" showTargetControls="false" showCheckbox="true"
		                    showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
		                    effect="drop">
		
		            <f:facet name="sourceCaption">可选单位</f:facet>
		            <f:facet name="targetCaption">已选单位</f:facet>
		
		            <p:column style="width:80%;text-align: left;">#{unit.unitName}</p:column>
		        </p:pickList>
		
		        <f:facet name="footer">
		            <h:panelGrid style="width: 100%;text-align: center;">
		                <h:panelGroup>
		                    <p:commandButton value="保存" icon="ui-icon-check" id="unitSaveBtn" action="#{selectMutilUnitBean.selectAction}" process="@this,unitPickList" />
		                    <p:spacer width="5" />
		                    <p:commandButton value="取消" icon="ui-icon-close" id="unitBackBtn" action="#{selectMutilUnitBean.dialogClose}" process="@this"/>
		                </h:panelGroup>
		            </h:panelGrid>
		        </f:facet>
		    </p:panel>
        </h:form>
    </h:body>
</f:view>
</html>
