<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title><span id="dialogSelfTitle">文件扫描</span></title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:form id="mainForm">
			<p:layout id="contentLay" style="height:510px">
				<p:layoutUnit position="west" size="410" id="leftLay">
					<object id="zwxVedioInput"
						type="application/x-chiscdc-VideoInput-plugin"
						class="zwxVedioInput"> </object>
				</p:layoutUnit>
				<p:layoutUnit position="center" size="200" id="centerLay">
					<div class="center-container">
						<div class="img-info">
							共<label id="pageNum">0</label>页
						</div>
						<div class="clear"></div>
						<ul>
						</ul>
					</div>
				</p:layoutUnit>
				<p:layoutUnit position="east" size="300" id="rightLay">
					<p:dataList value="#{takePicturesBean.fileList}" var="itm"
						id="dataList" type="ordered" itemType="disc" paginator="false"
						styleClass="paginated" emptyMessage="无">
					     <f:facet name="header">
          					  当前：#{takePicturesBean.filePo.fileName}
       					 </f:facet>
						<p:commandLink update=":mainForm:imgsJson,dataList"
							oncomplete="buildVedioImgsHtml();initVedioImgsEvents();"
							process="@this" action="#{takePicturesBean.fileChangeAction}"
							value="#{itm.fileName}">
							<f:setPropertyActionListener value="#{itm}"
								target="#{takePicturesBean.filePo}" />
						</p:commandLink>
					</p:dataList>
				</p:layoutUnit>
			</p:layout>
			<div class="line-cutoff">&#160;</div>
			<p:outputPanel style="text-align:center" id="btnPanel">
				<p:spacer width="5" />
				<p:commandButton value="拍照" icon="ui-icon-video" id="saveBtn"
					type="button" onclick="vedioInputTakePhoto()" />
				<p:spacer width="5" />
				<p:commandButton value="插入" icon="ui-icon-seek-end" id="insertBtn"
					type="button" onclick="vedioInputInsert()" />
				<p:spacer width="5" />
				<p:commandButton value="预览" icon="ui-icon-image" id="viewBtn"
					type="button" onclick="viewImgDetail()" />
				<p:spacer width="5" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
					id="submitBtn" type="button" onclick="vedioInputSubmit()" />

				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
					action="#{takePicturesBean.dialogClose}" widgetVar="BackBtn"
					process="@this" style="display:none" />
				<p:remoteCommand name="savePictureAction" process="@this,binaryStr"
					action="#{takePicturesBean.savePicture}" update="imgsJson,dataList"
					oncomplete="buildVedioImgsHtml();initVedioImgsEvents();" />
				<p:remoteCommand name="deletePictureAction" process="@this"
					action="#{takePicturesBean.deletePicture}" update="imgsJson"
					oncomplete="buildVedioImgsHtml();initVedioImgsEvents();" />
				<p:remoteCommand name="submitPictureAction" process="@this"
					action="#{takePicturesBean.confirmAction}" />

				<h:inputHidden id="binaryStr" value="#{takePicturesBean.binaryStr}" />
				<h:inputHidden id="imgsJson" value="#{takePicturesBean.imgsJson}" />
			</p:outputPanel>

			<a href="/resources/files/chiscdcHWVideoInput.crx" id="crxId"
				style="display: none">crxId</a>
			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		</h:form>
		<script type="text/javascript">
			//<![CDATA[
			/**
			 * 检查插件是否安装
			 */
			var vedioAble = 0;
			var zwxVedioInput = document.getElementById("zwxVedioInput");

			function onVedioInputLoad() {
				var checkVedioInputOcx = zwxVedioInput.CheckDevice;
				if (checkVedioInputOcx == undefined) {
					vedioAble = 0;
					PF("InstallDialog").show();
				} else if (checkVedioInputOcx == "OK") {
					zwxVedioInput.ChiscdcHwPenSign;
					vedioAble = 1;
				} else {
					alert(checkVedioInputOcx);
					PF("BackBtn").getJQ().click();
				}
			}

			/**
			 * 设备初始化
			 */
			function vedioInputInit() {
				onVedioInputLoad();
				if (vedioAble == 1) {
					zwxVedioInput.DeviceFormatIndex = 2;
					var openDeviceRst = zwxVedioInput.OpenDevice;
					if (openDeviceRst != "OK") {
						zwxVedioInput.CloseDevice;
						zwxVedioInput.OpenDevice;
					}
				}
			}

			/**
			 * 拍照
			 */
			function vedioInputTakePhoto() {
				zwxVedioInput.TakePicture;
				document.getElementById("mainForm:binaryStr").value = zwxVedioInput.JpgData;
				savePictureAction();
			}

			function vedioInputInsert() {
				var selectLen = jQuery(".vedio-photo-choosen").length;
				if (selectLen == 0) {
					alert("请先选择要插入的页码！");
					return;
				} else {
					var inx = jQuery(".vedio-photo-choosen").next().html();
					zwxVedioInput.TakePicture;
					document.getElementById("mainForm:binaryStr").value = zwxVedioInput.JpgData;
					savePictureAction([ {
						name : 'inx',
						value : inx
					} ]);
				}
			}

			function vedioInputSubmit() {
				var imgsJson = document.getElementById("mainForm:imgsJson").value;
				if (imgsJson == "" || imgsJson == "[]") {
					alert("请先拍照！");
					return;
				}
				submitPictureAction();
			}

			/**
			 * 释放
			 */
			function vedioInputClear() {
				zwxVedioInput.CloseDevice;
			}

			window.onload = function() {
				//给弹出框绑定关闭时间
				setTimeout(diaAddCloseListener, 1000);
				vedioInputInit();
				buildVedioImgsHtml();
				initVedioImgsEvents();
			}

			function diaAddCloseListener() {
				jQuery(window.parent.document).find("#dialogSelfTitle")
						.parent().next().children("span").bind("click",
								function() {
									vedioInputClear();
								});
			}

			/**
			 * 图片查看详情
			 */
			function viewImgDetail() {
				var imgsJson = document.getElementById("mainForm:imgsJson").value;
				var grobalUrl = "#{request.scheme}" + "://"
						+ "#{request.serverName}" + ":"
						+ "#{request.serverPort}" + "#{request.contextPath}";
				var url = grobalUrl + "/webapp/system/viewImgShow.faces?param="
						+ encodeURI(imgsJson);
				window
						.open(
								url,
								"",
								"height=650,width=1200,top=100,left=100,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no");
			}

			function buildVedioImgsHtml() {
				var imgsJson = jQuery("#mainForm\\:imgsJson").val();
				if (imgsJson != "" && imgsJson != "[]") {
					var imgsObj = eval(imgsJson);
					var h = "";
					var pageNum = imgsObj.length;
					for (var i = 0; i < pageNum; i++) {
						h = h + "<li>";
						h = h + "<div class='vedio-photo'>";
						h = h
								+ "<img class='vedio-image' src='/webFile"+imgsObj[i].path+"'/>";
						h = h + "</div>";
						h = h + "<div class='page'>" + (i + 1) + "</div>";
						h = h
								+ "<img class='img-close-icon' src='/resources/images/close-red.png' />";
						h = h + "</li>";
					}
					jQuery(".center-container > ul").html(h);
					jQuery("#pageNum").html(pageNum);
				} else {
					jQuery(".center-container > ul").html("");
				}
			}

			function initVedioImgsEvents() {
				jQuery(".img-close-icon").hide();
				jQuery(".vedio-photo").bind("click", function() {
					jQuery(".img-close-icon").hide();
					jQuery(".vedio-photo-choosen").each(function() {
						jQuery(this).removeClass("vedio-photo-choosen");
					});
					jQuery(this).addClass("vedio-photo-choosen");
					jQuery(this).parent().find(".img-close-icon").show();
				});

				jQuery(".img-close-icon").bind("click", function() {
					var inx = jQuery(this).prev(".page").html();
					deletePictureAction([ {
						name : 'inx',
						value : inx
					} ]);
				});
			}
			//]]>
		</script>
		<style type="text/css">
.clear {
	clear: both;
}

#mainForm\:contentLay .ui-corner-all {
	border-radius: 0px !important;
}

#mainForm\:leftLay,#mainForm\:centerLay,#mainForm\:rightLay .ui-widget-content
	{
	border: 1px solid #666666;
}

#mainForm\:leftLay {
	width: 410px !important;
}

#mainForm\:centerLay {
	left: 410px !important;
	width: 250px !important;
	text-align: center;
}

#mainForm\:rightLay {
	border: 1px solid #666666 !important;
	width: 280px !important;
}

#mainForm\:rightLay .ui-layout-unit-content {
	border: none !important;
}

.vedio-photo-choosen {
	border: 3px solid red;
}

.zwxVedioInput {
	width: 400px;
	height: 500px;
}

.line-cutoff {
	height: 20px;
	width: 940px;
	border-bottom: 1px solid #A6C9E2;
	clear: both;
	margin-bottom: 15px;
}

#mainForm\:leftLay .ui-layout-unit-content {
	overflow-y: hidden !important;
}

.center-container .img-info {
	margin-top: 3px;
	font-family: Microsoft YaHei;
	font-size: 11px;
	color: #333333;
}

.center-container ul {
	width: 205px;
	height: 458px;
	margin-top: 3px;
	margin-left: 20px;
	padding-left: 0px;
	padding-top: 10px;
	overflow-y: auto;
	border: 1px solid #666666;
	background: #F5F5F5;
}

.center-container ul li {
	list-style: none;
	height: 145px;
	margin-left: 0px; //
	border: 1px solid red;
}

.center-container ul li div.vedio-photo {
	width: 90px;
	height: 120px;
	-webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px
		rgba(0, 0, 0, .5);
	-moz-box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px
		rgba(0, 0, 0, .5);
	-o-box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px rgba(0, 0, 0, .5);
	box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px rgba(0, 0, 0, .5);
	margin-left: 50px;
	margin-bottom: 5px;
}

.ui-layout-unit .ui-layout-unit-content {
	padding: 0.2em 0em;
	border: 0px none;
	overflow: hidden;
}

.center-container ul li div.photo:before {
	background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(rgba(255, 255, 255, .15)),
		to(rgba(0, 0, 0, .25))),
		-webkit-gradient(linear, left top, right bottom, color-stop(0, rgba(255,
		255, 255, 0)), color-stop(0.5, rgba(255, 255, 255, .1)),
		color-stop(0.501, rgba(255, 255, 255, 0)),
		color-stop(1, rgba(255, 255, 255, 0)));
	background: -moz-linear-gradient(top, rgba(255, 255, 255, .15),
		rgba(0, 0, 0, .25)),
		-moz-linear-gradient(left top, rgba(255, 255, 255, 0),
		rgba(255, 255, 255, .1) 50%, rgba(255, 255, 255, 0) 50%,
		rgba(255, 255, 255, 0));
	background: linear-gradient(top, rgba(255, 255, 255, .15),
		rgba(0, 0, 0, .25)), linear-gradient(left top, rgba(255, 255, 255, 0),
		rgba(255, 255, 255, .1) 50%, rgba(255, 255, 255, 0) 50%,
		rgba(255, 255, 255, 0));
}

.center-container ul li img.vedio-image {
	width: 100%;
	height: 100%;
	cursor: pointer;
}

.center-container ul li img.img-close-icon {
	width: 20px;
	height: 20px;
	position: relative;
	top: -155px;
	left: 53px;
	cursor: pointer;
}

#mainForm\:dataList_content {
	border: none !important;
}

.ui-datalist-item {
	height: 30px;
	border-bottom: dotted 1px #b2b2b2;
	margin-bottom: 10px;
}
</style>
		<p:dialog id="installDialog" widgetVar="InstallDialog" header="插件安装"
			width="350" height="150" resizable="false" modal="true">
			<table style="width: 100%">
				<tr>
					<td style="height: 5px;"></td>
				</tr>
				<tr>
					<td style="height: 30px;text-align: left"><span
						style="font-size: large;font-weight: 600">提示信息：</span></td>
				</tr>
				<tr>
					<td style="width: 100%;height: 60px;"><span
						style="font-size: 22px;font-weight: 700;color: blue">请下载插件，将下载的文件拖入到浏览器中，并刷新页面！</span>
					</td>
				</tr>
				<tr>
					<td style="height: 5px;"></td>
				</tr>
				<tr>
					<td style="text-align: center"><a
						onclick="document.getElementById('crxId').click();PF('InstallDialog').hide()"
						class="button large classic-orange">插件下载</a></td>
				</tr>
			</table>
		</p:dialog>
	</h:body>
</f:view>
</html>
