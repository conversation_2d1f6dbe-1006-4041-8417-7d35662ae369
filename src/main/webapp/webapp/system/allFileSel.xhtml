<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
    <ui:define name="insertScripts">
        <script type="text/javascript" src="/resources/component/pager/kkpager.js"></script>
        <link rel="stylesheet" type="text/css" href="/resources/component/pager/kkpager.css"/>
        <script type="text/javascript">
            //<![CDATA[
            //下载
            function downLoad(path, name) {
                var form = jQuery("<form>");
                form.attr('style', 'display:none');
                form.attr('target', '');
                form.attr('method', 'post');
                form.attr('action', '/DownLoad?path=' + path + '&name=' + name);
                jQuery('body').append(form);
                form.submit();
                form.remove();
            }


            //init
            function showPager() {
                var totalPage = parseInt(document.getElementById("mainForm:totalPage").value, 10);
                var totalRecords = parseInt(document.getElementById("mainForm:totalRecords").value, 10);
                var pageNo = parseInt(document.getElementById("mainForm:pageNo").value, 10);
                //生成分页
                //有些参数是可选的，比如lang，若不传有默认值
                kkpager.init({
                    pno: pageNo,
                    //总页码
                    total: totalPage,
                    //总数据条数
                    totalRecords: totalRecords,
                    getLink: function (n) {
                        return "javascript:pageClick(" + n + ");";
                    },
                    lang: {
                        firstPageText: '|<',
                        lastPageText: '>|',
                        prePageText: '<',
                        nextPageText: '>',
                        totalPageBeforeText: '共',
                        totalPageAfterText: '页',
                        totalRecordsAfterText: '条数据',
                        gopageBeforeText: '转到',
                        gopageButtonOkText: '确定',
                        gopageAfterText: '页',
                        buttonTipBeforeText: '第',
                        buttonTipAfterText: '页'
                    }
                });
                kkpager.generPageHtml();
            }
            function pageClick(pageno) {
                document.getElementById("mainForm:pageNo").value = pageno;
                kkpager.selectPage(pageno);
                pageSeachJS();
            }
            //]]>
        </script>
    </ui:define>
    <ui:param name="condition" value="no"/>
    <ui:define name="insertContent">
        <table width="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td width="40%" style="text-align: right">
                    <img src="../../resources/component/quickDesktop/image/Folder.png"/>
                </td>
                <td style="text-align: left;vertical-align: middle;" width="70%">

                    <p:inputText id="descriptionSearch" value="#{allSearchBean.descriptionSearch}" maxlength="500"
                                 style="width: 350px;height: 25px"/>
                    <p:commandButton value="搜索一下" oncomplete="showPager();"
                                     update="searchPanel"
                                     action="#{allSearchBean.searchAction}"
                                     process="@this,descriptionSearch"/>
                    <p:remoteCommand name="pageSeachJS" action="#{allSearchBean.pageSearchAction}"
                                     process="@this,pageNo" update="searchPanel"/>
                </td>

            </tr>

        </table>
        <p:outputPanel id="searchPanel">
            <h:inputHidden value="#{allSearchBean.totalPage}" id="totalPage"/>
            <h:inputHidden value="#{allSearchBean.totalRecords}" id="totalRecords"/>
            <h:inputHidden value="#{allSearchBean.pageNo}" id="pageNo"/>
            <b><h:outputText value="#{allSearchBean.searchMess}" style="color:blueviolet;font-size: 12px"/></b>
            <ui:repeat value="#{allSearchBean.searchList}" var="itm">
                <br/> <br/>
                <a href="javascript:;" onclick="downLoad('#{itm[0]}','#{itm[2]}')">
                    <h:outputText value="#{itm[2]}" escape="false"/>
                    <h:outputText value="　　【文件大小：#{itm[3]}】" escape="false"/>
                </a>
                <br/>
                <h:outputText value="#{itm[1]}" escape="false"/>
            </ui:repeat>
        </p:outputPanel>

        <div id="kkpager"></div>
    </ui:define>
</ui:composition>