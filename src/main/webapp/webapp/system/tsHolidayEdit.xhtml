<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<ui:define name="insertScripts">
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
	</ui:define>
	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="节假日配置" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 编辑页面的按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			  <span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{tsHolidayBean.saveAction}" 
					process="@this,:tabView:editForm:editGrid" update=":tabView" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{tsHolidayBean.backAction}" update=":tabView"
					immediate="true" />
		</p:outputPanel>
	</ui:define>

	<!-- 编辑页面的内容-->
	<ui:define name="insertEditContent">

		<p:row >
			<p:column style="text-align:right;height:38px;width:180px">
				<p:outputLabel style="color:red" rendered="#{tsHolidayBean.rid==null}">*</p:outputLabel>
				<h:outputText value="日期："></h:outputText>
			</p:column>
			<p:column style="padding-left: 8px" >
				<p:calendar navigator="true" yearRange="c-10:c" rendered="#{tsHolidayBean.rid==null}"
					value="#{tsHolidayBean.addDate}" size="11"
					converterMessage="日期格式输入不正确！" pattern="yyyy-MM-dd"
					showButtonPanel="true" showOtherMonths="true">
					<p:ajax event="dateSelect" listener="#{tsHolidayBean.changeDate}" resetValues="true"
						process="@this"></p:ajax>
				</p:calendar>
				<h:outputText value="#{tsHolidayBean.date}" rendered="#{tsHolidayBean.rid!=null}"></h:outputText>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;height:38px;width:180px">
				<h:outputText value="日期类型："></h:outputText>
			</p:column>
			<p:column style="padding-left: 8px">
				<p:selectOneRadio value="#{tsHolidayBean.tsHoliday.dateType}" id="dtype"
					style="width:200px;">
					<f:selectItem itemLabel="工作日" itemValue="0" />
					<f:selectItem itemLabel="休息日" itemValue="1" />
					<f:selectItem itemLabel="节假日" itemValue="2" />
				</p:selectOneRadio>
			</p:column>
		</p:row>


	</ui:define>
	<!-- 其它内容 -->
	<ui:define name="insertOtherContents">

	</ui:define>
</ui:composition>


