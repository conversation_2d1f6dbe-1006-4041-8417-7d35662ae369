<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">

    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知通告编辑"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="保存" icon="ui-icon-disk" style="float:right;" rendered="#{mgrbean.tsNotice.stateMark==0}"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" rendered="#{mgrbean.tsNotice.stateMark==0}"
                                 action="#{mgrbean.boforeSubmit}" process="@this,:tabView:editForm">
                </p:commandButton>
                <p:commandButton value="撤销" action="#{mgrbean.revokeAction}" icon="ui-icon-cancel" rendered="#{mgrbean.tsNotice.stateMark==2}">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}"
                                 update=":tabView" immediate="true" />
                <p:confirmDialog message="确定要提交吗？" header="消息确认框" widgetVar="ConfirmDialog">
                    <p:commandButton value="确定" action="#{mgrbean.submitAction}" icon="ui-icon-check" />
                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();" type="button"/>
                </p:confirmDialog>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertEditContent">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 33px;">
                <h:outputText value="*" style="color:red;" rendered="#{mgrbean.tsNotice.stateMark==0}"/>
                <h:outputText value="通知标题："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputTextarea value="#{mgrbean.tsNotice.noticeTitle}" maxlength="100" rendered="#{mgrbean.tsNotice.stateMark==0}"
                                 style="width:450px;resize: none;height:60px;" autoResize="false" />
                <h:outputText value="#{mgrbean.tsNotice.noticeTitle}" rendered="#{mgrbean.tsNotice.stateMark==2}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="*" style="color:red;" rendered="#{mgrbean.tsNotice.stateMark==0}"/>
                <h:outputText value="通知日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:calendar navigator="true" yearRange="c-10:c" rendered="#{mgrbean.tsNotice.stateMark==0}"
                            value="#{mgrbean.tsNotice.noticeDate}" size="11" maxdate="new Date();"
                            converterMessage="通知日期格式输入不正确！" pattern="yyyy-MM-dd"
                            showButtonPanel="true" showOtherMonths="true"/>
                <h:outputText value="#{mgrbean.tsNotice.noticeDate}" rendered="#{mgrbean.tsNotice.stateMark==2}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputText>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="*" style="color:red;" rendered="#{mgrbean.tsNotice.stateMark==0}"/>
                <h:outputText value="是否发布："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectOneRadio value="#{mgrbean.tsNotice.ifPublish}" rendered="#{mgrbean.tsNotice.stateMark==0}">
                    <f:selectItem itemValue="0" itemLabel="否"/>
                    <f:selectItem itemValue="1" itemLabel="是"/>
                </p:selectOneRadio>

                <h:outputText value="是" rendered="#{mgrbean.tsNotice.stateMark==2 and mgrbean.tsNotice.ifPublish==1}"/>
                <h:outputText value="否" rendered="#{mgrbean.tsNotice.stateMark==2 and mgrbean.tsNotice.ifPublish==0}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 33px;">
                <h:outputText value="*" style="color:red;" rendered="#{mgrbean.tsNotice.stateMark==0}"/>
                <h:outputText value="附件："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <h:panelGroup id="uploadGroup" rendered="#{mgrbean.tsNotice.stateMark==0}">
                    <p:commandButton value="上传" oncomplete="PF('FileDialog').show();"
                                     process="@this" update=":tabView:editForm:fileDialog"
                                     rendered="#{mgrbean.tsNotice.filePath==null}"/>
                    <h:panelGroup rendered="#{mgrbean.tsNotice.filePath!=null}">
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.tsNotice.filePath}')"
                                         rendered="#{mgrbean.tsNotice.filePath!=null}"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="删除" update="uploadGroup" process="@this" rendered="#{mgrbean.tsNotice.filePath!=null}"
                                         action="#{mgrbean.delAnnex}"/>
                    </h:panelGroup>
                </h:panelGroup>

                <p:commandButton value="查看" process="@this"
                                 onclick="window.open('/webFile/#{mgrbean.tsNotice.filePath}')"
                                 rendered="#{mgrbean.tsNotice.stateMark==2 and mgrbean.tsNotice.filePath!=null}"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:dialog header="附件上传" styleClass="fileDialogClass" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true" >
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：PDF）" styleClass="diagTextClass" 
                                       style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要上传的文件！"
                                      label="文件选择" fileUploadListener="#{mgrbean.fileUpload}"
                                      invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                      style="width:600px;" previewWidth="120" cancelLabel="取消" styleClass="fileUpload"
                                      fileLimit="1" fileLimitMessage="只能选择一个文件！"
                                      process="@this" update="uploadGroup"
                                      uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                                      invalidFileMessage="无效的文件类型！只能上传pdf类型文件"
                                      allowTypes="/(\.|\/)(pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>