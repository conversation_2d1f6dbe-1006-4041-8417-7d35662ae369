<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" 
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{tsGroupBean}"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <style type="text/css">
        .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 340px;
            width: 340px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
    </style>
    <script type="text/javascript">

    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="组管理"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tsGroupBean.searchAction}" update="dataTable" process="@this,searchGroupName" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" update="groupAddDialog" process="@this" action="#{tsGroupBean.addInit}" oncomplete="PF('GroupAddDialog').show()">
					<p:resetInput target="groupAddDialog" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchGroupName" value="组名称：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <p:inputText id="searchGroupName" value="#{tsGroupBean.searchGroupName}"
                         maxlength="100"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="序号" style="width: 80px;text-align: center">
        <h:outputText value="#{itm.xh}" />
    </p:column>
    <p:column headerText="名称" style="width: 350px;text-align: center;">
        <h:outputText value="#{itm.groupName}" />
    </p:column>
    <p:column headerText="状态" style="width:120px;text-align: center;">
        <h:outputText value="启用" rendered="#{itm.ifReveal==1}"/>
        <h:outputText value="停用" rendered="#{itm.ifReveal==0}"/>
    </p:column>
    <p:column headerText="操作" style="padding-left: 3px;">
        <p:commandLink value="修改" rendered="#{itm.ifReveal==0}"
                       action="#{tsGroupBean.modInit}" update=":mainForm:groupAddDialog"
                       oncomplete="PF('GroupAddDialog').show()" process="@this">
            <f:setPropertyActionListener target="#{tsGroupBean.tsGroup}" value="#{itm}"/>
            <p:resetInput target=":mainForm:groupAddDialog"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.ifReveal==0}"/>
        <p:commandLink value="删除" rendered="#{itm.ifReveal==0}" action="#{tsGroupBean.deleteAction}"
                       update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{tsGroupBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.ifReveal==0}" />
        <p:commandLink rendered="#{itm.ifReveal==1}" value="停用"
                       action="#{tsGroupBean.changeStateAction}"
                       update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{tsGroupBean.rid}" value="#{itm.rid}"/>
            <f:setPropertyActionListener target="#{tsGroupBean.changeState}" value="0"/>
        </p:commandLink>
        <p:commandLink rendered="#{itm.ifReveal==0}" value="启用"
                       action="#{tsGroupBean.changeStateAction}"
                       update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{tsGroupBean.rid}" value="#{itm.rid}"/>
            <f:setPropertyActionListener target="#{tsGroupBean.changeState}" value="1"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink value="成员管理" action="#{tsGroupBean.userManagerInit}" update=":mainForm:userDialog"
                       oncomplete="PF('UserDialog').show()" process="@this">
            <f:setPropertyActionListener target="#{tsGroupBean.rid}" value="#{itm.rid}"/>
            <f:setPropertyActionListener target="#{tsGroupBean.tsGroup}" value="#{itm}"/>
        </p:commandLink>
    </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
    <!-- 新增、修改组 -->
    <p:dialog id="groupAddDialog" header="组维护" widgetVar="GroupAddDialog"
              resizable="false" width="500" height="80" modal="true">
        <p:panelGrid style="width:100%;" id="groupAddGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputLabel value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="xh" value="#{tsGroupBean.tsGroup.xh}" maxlength="2" size="25"
                                 converterMessage="序号格式错误，请输入0~99的序号！"
                                 required="true" requiredMessage="序号不允许为空！"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputLabel value="组名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="groupDesc" value="#{tsGroupBean.tsGroup.groupName}"
                                 maxlength="100" size="25" required="true" requiredMessage="组名称不允许为空！"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
                                     action="#{tsGroupBean.saveAction}"
                                     process="@this,xh,groupDesc"
                                     update="dataTable,groupAddGrid"/>
                                     <!--oncomplete="PF('GroupAddDialog').hide();" -->
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
                                     oncomplete="PF('GroupAddDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!-- 成员管理 -->
    <p:dialog id="userDialog" header="请选择人员" widgetVar="UserDialog" resizable="false" width="800"
              height="450" modal="true" dynamic="true">
        <table width="100%" >
            <tr>
                <td width="60" style="text-align: right;padding-right: 3px">科室：</td>
                <td  style="text-align: left;">
                    <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                        <p:inputText id="userOfficeName" value="#{tsGroupBean.userOfficeName}"
                                     readonly="true" style="width: 120px;"/>
                        <h:inputHidden id="userOfficeRid" value="#{tsGroupBean.userOfficeRid}"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search"  id="initOfficeTreeLink"
                                       process="@this" style="position: relative;left: -30px;"
                                       oncomplete="PF('UserOfficePanel').show()"/>
                    </h:panelGrid>
                    <p:overlayPanel id="userOfficePanel" for="userOfficeName" dynamic="false"
                                    style="width:280px;" widgetVar="UserOfficePanel" >
                        <p:tree value="#{tsGroupBean.userOfficeTreeNode}" var="node" selectionMode="single"
                                id="userOfficeTree"
                                style="width: 250px;height: 400px;overflow-y: auto;">
                            <p:ajax event="select" update=":mainForm:userOfficeName,:mainForm:userOfficeRid, :mainForm:userPickList"
                                    listener="#{tsGroupBean.onOfficeNodeSelect}"
                                    oncomplete="PF('UserOfficePanel').hide();"
                                    process="@this, :mainForm:userPickList"/>
                            <p:treeNode>
                                <h:outputText value="#{node.officename}" />
                            </p:treeNode>
                        </p:tree>
                    </p:overlayPanel>
                </td>
            </tr>
        </table>

        <p:pickList id="userPickList" value="#{tsGroupBean.userInfoDualListModel}"
                    var="user" itemValue="#{user}" itemLabel="#{user.username}"
                    converter="system.UserOfficeConvert"
                    showSourceControls="false"
                    showTargetControls="false"
                    showCheckbox="true"
                    showSourceFilter="true"
                    showTargetFilter="true"
                    filterMatchMode="contains"
                    effect="drop">
            <f:facet name="sourceCaption">可选用户</f:facet>
            <f:facet name="targetCaption">已选用户</f:facet>

            <p:column style="width:45%;text-align: left;">#{user.username}</p:column>
            <p:column style="width:55%;text-align: left;">#{user.officeName}</p:column>
        </p:pickList>

        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="userSaveBtn"
                                     action="#{tsGroupBean.userManagerAction}"
                                     process="@this,userPickList" oncomplete="PF('UserDialog').hide();"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="userBackBtn"
                                     onclick="PF('UserDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:define>
</ui:composition>











