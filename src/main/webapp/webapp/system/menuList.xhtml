<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{menuBean}"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <h:outputStylesheet name="css/ui-cs.css"/>
    <style type="text/css">
        .ui-panelgrid td {
            padding-top: 2px;
            padding-bottom: 2px;
            padding-left: 5px;
            padding-right: 0px;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[
        /**
         *小图标赋值
         * @param imgsrc
         */
        function menuIcoSet(imgsrc){
            document.getElementById("mainForm:menuIcon").value=imgsrc;
            document.getElementById("mainForm:menuIconView").value=imgsrc;
            document.getElementById("imgDiv").innerHTML="<img height='16' width='16' src='/resources/component/quickDesktop/image/16px/"+imgsrc+"'/>";
            PF('MenuPanel').hide();
        }
        /**
         * 大图标赋值
         * @param imgsrc
         */
        function bigmenuIcoSet(imgsrc){
            document.getElementById("mainForm:bigIcon").value=imgsrc;
            document.getElementById("mainForm:bigIconView").value=imgsrc;
            document.getElementById("bigImgDiv").innerHTML="<img height='16' width='16' src='/resources/component/quickDesktop/image/64px/"+imgsrc+"'/>";
            PF('BigMenuPanel').hide();
        }
        //]]>
    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
            <h:outputText value="菜单管理"/>
        </p:column>
    </p:row>

</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>

				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{menuBean.searchAction}" update="dataTable" process="@this,mainGrid" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{menuBean.addInitAction}" update=":mainForm:menuEditDialog" oncomplete="PF('MenuEditDialog').show()"
					process="@this"  >
					<p:resetInput target=":mainForm:menuEditDialog" />
				</p:commandButton>
                <p:commandButton value="自检" icon="ui-icon-check"  action="#{mgrbean.selfInspectionAction}" process="@this" update=":mainForm"
                                 onclick="zwx_loading_start()" oncomplete="zwx_loading_stop()" >
                    <p:confirm header="消息确认框" message="确定要自检吗？" icon="ui-icon-alert"/>
                </p:commandButton>
			</h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputText value="提示：" style="color:red;"/>
                <h:outputText value="修改记录后请重新【自检】！" style="color:blue;"/>
            </p:outputPanel>
		</p:outputPanel>
	</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputText value="菜单名称/简称："/>
        </p:column>
        <p:column styleClass="cs-scv-w" >
            <p:inputText value="#{mgrbean.searchMenuName}" maxlength="30"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputText value="菜单编码："/>
        </p:column>
        <p:column styleClass="cs-scv-w">
            <p:inputText  value="#{mgrbean.searchMenuEn}" maxlength="15"/>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputText value="结构层次编码："/>
        </p:column>
        <p:column styleClass="cs-scv">
            <p:inputText  value="#{mgrbean.searchCode}" maxlength="15"/>
        </p:column>
    </p:row>
    <p:row>
        <p:column styleClass="cs-scl-first">
            <h:outputText value="自检状态："/>

        </p:column>
        <p:column styleClass="cs-scv-w" >
            <p:selectManyCheckbox value="#{mgrbean.searchStates}" >
                <f:selectItem itemLabel="成功" itemValue="1" />
                <f:selectItem itemLabel="失败" itemValue="2" />
            </p:selectManyCheckbox>
        </p:column>
        <p:column styleClass="cs-scl-w">
            <h:outputText value="链接地址："/>
        </p:column>
        <p:column styleClass="cs-scv" colspan="3" >
            <p:inputText value="#{mgrbean.searchMenuUri}" maxlength="100" style="width:50%;"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="菜单名称" style="width:180px;padding-left:#{itm.levelNum*15}px; ">
        <h:outputText value="#{itm.menuCn}"/>
    </p:column>
    <p:column headerText="菜单简称" style="width:160px;">
        <h:outputText value="#{itm.menuSimple}"/>
    </p:column>
    <p:column headerText="结构层次编码" style="width:90px;">
        <h:outputText value="#{itm.menuLevelNo}"/>
    </p:column>
    <p:column headerText="是否功能菜单" style="width:80px;text-align:center;">
        <h:outputText value="是" rendered="#{itm.isfunc==1}"/>
        <h:outputText value="否" rendered="#{itm.isfunc==0}"/>
    </p:column>
    <p:column headerText="菜单编码" style="width:120px;white-space: pre-wrap;word-wrap: break-word;word-break: break-all;">
        <h:outputText value="#{itm.menuEn}"/>
    </p:column>
    <p:column headerText="链接地址" style="width:320px;white-space: pre-wrap;word-wrap: break-word;word-break: break-all;">
        <h:outputText value="#{itm.menuUri}"/>
    </p:column>
    <p:column headerText="序号" style="text-align:center;width:50px;">
        <h:outputText value="#{itm.num}"/>
    </p:column>
    <p:column headerText="是否打开新的浏览窗口" style="text-align:center;width:90px;">
        <h:outputText value="否" rendered="#{itm.ifPop ne '1'}"/>
        <h:outputText value="是" rendered="#{itm.ifPop eq '1'}"/>
    </p:column>
    <p:column headerText="自检状态" style="text-align:center;width:60px;">
        <h:outputText value="成功" rendered="#{itm.state eq '1'}"/>
        <h:outputText value="失败" rendered="#{itm.state eq '2'}" style="color: red;"/>
    </p:column>
    <p:column headerText="失败原因" style="width:180px;">
        <p:outputLabel id="errMsg" value="#{itm.errRsn}" styleClass="zwx-tooltip"/>
        <p:tooltip for="errMsg" value="#{itm.errRsn}"
                   style="max-width:300px;word-break:break-all;word-wrap:break-word;"/>
    </p:column>
    <p:column headerText="操作" >
        <p:commandLink value="修改" action="#{menuBean.modInitAction}" update=":mainForm:menuEditDialog"
                       oncomplete="PF('MenuEditDialog').show();" process="@this"  >
            <f:setPropertyActionListener target="#{menuBean.tsMenu}" value="#{itm}"/>
            <f:setPropertyActionListener target="#{menuBean.rid}" value="#{itm.rid}"/>
            <p:resetInput target=":mainForm:menuEditDialog"/>
        </p:commandLink>
        <p:spacer width="5"/>
        <p:commandLink value="删除" action="#{menuBean.deleteAction}" update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{menuBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5"/>
        <p:commandLink value="详情"   update=":mainForm:menuViewDialog"
                       oncomplete="PF('MenuViewDialog').show();" process="@this">
            <f:setPropertyActionListener target="#{menuBean.tsMenu}" value="#{itm}"  />
        </p:commandLink>
    </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
    <!-- 新增、修改菜单 -->
    <p:dialog id="menuEditDialog" header="菜单管理" dynamic="true" widgetVar="MenuEditDialog" resizable="false" width="500" height="340"
              modal="true">
        <p:panelGrid style="width:100%;" id="menuEditGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="menuName" value="菜单名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="menuName" value="#{menuBean.tsMenu.menuCn}" maxlength="30" style="width:80%;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="menuSimple" value="菜单简称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="menuSimple" value="#{menuBean.tsMenu.menuSimple}" maxlength="30" style="width:80%;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="menuLevelNo" value="结构层次编码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="menuLevelNo" value="#{menuBean.tsMenu.menuLevelNo}" maxlength="15" style="width:80%;"/>
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="isfunc" value="是否功能菜单："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                   <p:selectOneRadio id="isfunc" value="#{menuBean.tsMenu.isfunc}">
                       <f:selectItem itemLabel="是" itemValue="1"/>
                       <f:selectItem itemLabel="否" itemValue="0"/>
                       <p:ajax event="change" process="@this,isfunc" listener="#{mgrbean.changeIsfunc}" update="isfunc,menuUriLab,menuUriValue" resetValues="true"/>

                   </p:selectOneRadio>
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText converterMessage="序号，只能输入数字！" maxlength="2"  id="num" value="#{menuBean.tsMenu.num}" style="width:80%;"/>
                </p:column>
            </p:row>

            <p:row >
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;" >
                    <h:outputLabel value="链接地址：" id="menuUriLab" styleClass="#{menuBean.tsMenu.isfunc ne '0' ? 'cs-required' : ''}" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText  id="menuUriValue" value="#{menuBean.tsMenu.menuUri}" readonly="#{menuBean.tsMenu.isfunc ne '0' ? 'false' : 'true'}" maxlength="100"  style="width:80%;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="menuIconView" value="小图标选择："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                    <h:inputHidden   id="menuIcon"  value="#{menuBean.tsMenu.menuIcon}" />
                    <p:inputText   id="menuIconView" value="#{menuBean.tsMenu.menuIcon}"  readonly="true"  style="width:80%;">
                    </p:inputText> &#160; &#160; &#160;<span id="imgDiv">
                    <h:panelGroup rendered="#{menuBean.tsMenu.menuIcon!=null}">
                    <img height='16' width='16' src='/resources/component/quickDesktop/image/16px/#{menuBean.tsMenu.menuIcon}'/></h:panelGroup></span>
                    <p:overlayPanel   id="menuPanel" for="menuIconView"  style="width:300px;"
                                      widgetVar="MenuPanel"  >
                        <p:scrollPanel style="width: 280px;height:120px;overflow-x: hidden;" mode="native">
                            <ui:repeat value="#{menuBean.imgUrlList}" var="menu" varStatus="ind" >
                                &#160;
                                <img onclick="menuIcoSet('#{menu}')" height="16" width="16" style="cursor:pointer" src="/resources/component/quickDesktop/image/16px/#{menu}">
                                </img>
                                &#160;
                            </ui:repeat>
                        </p:scrollPanel>
                    </p:overlayPanel>
                </p:column>
            </p:row>


            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel for="menuIconView" value="大图标选择："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                    <h:inputHidden   id="bigIcon"  value="#{menuBean.tsMenu.bigIcon}" />
                    <p:inputText   id="bigIconView" value="#{menuBean.tsMenu.bigIcon}"  readonly="true"  style="width:80%;">
                    </p:inputText> &#160; &#160; &#160;<span id="bigImgDiv">
                    <h:panelGroup rendered="#{menuBean.tsMenu.bigIcon!=null}">
                        <img height='16' width='16' src='/resources/component/quickDesktop/image/64px/#{menuBean.tsMenu.bigIcon}'/></h:panelGroup></span>
                    <p:overlayPanel   id="bigmenuPanel" for="bigIconView"  style="width:300px;"
                                      widgetVar="BigMenuPanel"  >
                        <p:scrollPanel style="width: 280px;height:120px;overflow-x: hidden;" mode="native">
                            <ui:repeat value="#{menuBean.bigImgUrlList}" var="menu" varStatus="ind" >
                                &#160;
                                <img onclick="bigmenuIcoSet('#{menu}')" height="32" width="32" style="cursor:pointer" src="/resources/component/quickDesktop/image/64px/#{menu}">
                                </img>
                                &#160;
                            </ui:repeat>
                        </p:scrollPanel>
                    </p:overlayPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel for="menuIconView" value="是否打开新的浏览窗口："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                    <p:selectOneRadio  value="#{menuBean.tsMenu.ifPop}">
                        <f:selectItem itemLabel="是" itemValue="1"/>
                        <f:selectItem itemLabel="否" itemValue="0"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:center;" colspan="2">
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{menuBean.saveAction}"
                                     process="@this,menuEditGrid"
                                     update="dataTable,mainForm:menuEditGrid"/>
                    &#160;&#160;
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" style="cursor: hand;" onclick="PF('MenuEditDialog').hide();"
                                     immediate="true"/>&#160;&#160;
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:dialog>

    <p:dialog id="menuViewDialog" header="菜单管理" dynamic="true" widgetVar="MenuViewDialog" resizable="false" width="500" height="390"
              modal="true">
        <p:panelGrid style="width:100%;" id="menuViewGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel  value="菜单名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel value="#{menuBean.tsMenu.menuCn}" />
                </p:column>
            </p:row>
            <p:row>
            <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                <h:outputLabel  value="菜单编码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:outputLabel value="#{menuBean.tsMenu.menuEn}" />
            </p:column>
        </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel value="菜单简称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel value="#{menuBean.tsMenu.menuSimple}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel value="结构层次编码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel value="#{menuBean.tsMenu.menuLevelNo}" />
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel value="是否功能菜单："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel rendered="#{menuBean.tsMenu.isfunc==1}" value="是"/>
                    <p:outputLabel rendered="#{menuBean.tsMenu.isfunc==0}" value="否"/>
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel  value="#{menuBean.tsMenu.num}"/>
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel value="链接地址："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;white-space: pre-wrap;word-wrap: break-word;word-break: break-all;">
                    <p:outputLabel value="#{menuBean.tsMenu.menuUri}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel value="小图标选择："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                    <p:outputLabel   value="#{menuBean.tsMenu.menuIcon}" />
                     &#160; &#160; &#160;<img height='16' width='16' src='/resources/component/quickDesktop/image/16px/#{menuBean.tsMenu.menuIcon}'/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel  value="大图标选择："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                    <p:outputLabel   value="#{menuBean.tsMenu.bigIcon}" />
                <span>
                    <h:panelGroup rendered="#{menuBean.tsMenu.bigIcon!=null}">
                        &#160; &#160; &#160; <img height='32' width='32' src='/resources/component/quickDesktop/image/64px/#{menuBean.tsMenu.bigIcon}'/>
                  </h:panelGroup>
                </span>

                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                    <h:outputLabel  value="是否打开新的浏览窗口："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;nowrap:nowrap;">
                    <p:outputLabel rendered="#{menuBean.tsMenu.ifPop==1}" value="是"/>
                    <p:outputLabel rendered="#{menuBean.tsMenu.ifPop!=1}" value="否"/>

                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:center;" colspan="2">
                    <p:commandButton value="关闭" icon="ui-icon-close"  style="cursor: hand;" onclick="PF('MenuViewDialog').hide();"
                                     immediate="true"/>&#160;&#160;
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:dialog>


</ui:define>

</ui:composition>