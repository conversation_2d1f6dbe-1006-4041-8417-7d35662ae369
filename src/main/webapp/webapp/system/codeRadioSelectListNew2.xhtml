<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body style="overflow-y:hidden;"  onload="document.getElementById('codeForm:pym').focus();">
    	<title>#{codeRadioSelectNewBean2.titleName}选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <style type="text/css">
        </style>
        <h:form id="codeForm">
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                        	<p:outputLabel value="#{codeRadioSelectNewBean2.titleName}大类：" styleClass="zwx_dialog_font" rendered="#{codeRadioSelectNewBean2.ifShowFirstCode}"/>
                        	<p:selectOneMenu value="#{codeRadioSelectNewBean2.firstCodeNo}" 
                        		rendered="#{codeRadioSelectNewBean2.ifShowFirstCode}" id="firstCodeNo"
                        		style="width:200px;">
                        		<f:selectItem itemValue="" itemLabel="--全部--"></f:selectItem>
                        		<f:selectItems value="#{codeRadioSelectNewBean2.firstList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"></f:selectItems>
                        		<p:ajax event="change" listener="#{codeRadioSelectNewBean2.searchAction}" process="@this,searchPanel" update="selectedIndusTable"></p:ajax>
                        	</p:selectOneMenu>
                            <p:outputLabel value="#{codeRadioSelectNewBean2.searchName==null?'名称/拼音码':codeRadioSelectNewBean2.searchName}：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{codeRadioSelectNewBean2.searchNamOrPy}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{codeRadioSelectNewBean2.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{codeRadioSelectNewBean2.displayList}" id="selectedIndusTable"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorPosition="bottom">
                <p:column headerText="选择" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{codeRadioSelectNewBean2.selectAction}" process="@this" rendered="#{codeRadioSelectNewBean2.ifAllSelect?'true':(itm.levelIndex != '0' and itm.lastCodeName != '0')}">
                        <f:setPropertyActionListener value="#{itm}" target="#{codeRadioSelectNewBean2.selectPro}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="#{codeRadioSelectNewBean2.colName==null?'名称':codeRadioSelectNewBean2.colName}" style="padding-left: 3px;">
                	<h:outputText value="#{(itm.levelIndex == '4') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '3') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
    </h:body>
</f:view>
</html>
