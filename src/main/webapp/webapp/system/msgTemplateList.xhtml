<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{msgTemplateBean}"/>
    <ui:define name="insertScripts">
        <script type="text/javascript">
            //<![CDATA[
            var clickRepeat;
            var clickCount=0;
            var clickDelay=200;
            var rid;
            function checkClick(id) {
                rid = id;
                clearTimeout(clickRepeat);
                clickCount++;
                if(clickCount==1) {
                    clickRepeat=setTimeout('function1(); clickCount=0',clickDelay);
                } else {
                    function2();
                    clickCount=0;
                }
            }

            //单击事件
            function function1() {
                refreshMetaDemo([{name:'param1', value:rid}]);
            }

            //双击事件
            function function2() {
                addMeta([{name:'param1', value:rid}]);
            }
            //]]>
        </script>
    </ui:define>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="项目模板维护"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4"
                         style="border-color:transparent;padding:0;">
                    <span class="ui-separator"><span
                            class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{msgTemplateBean.searchAction}" update="dataTable"
                                 process="@this,msgType"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{msgTemplateBean.addInitAction}"
                                 update="editDialog" process="@this" resetValues="true" oncomplete="PF('EditDialog').show()" >
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="系统类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 3px;">
                <p:selectOneMenu id="msgType" style="width:200px" value="#{msgTemplateBean.sysType}">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItems value="#{msgTemplateBean.sysTypeMap}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>
    </ui:define>

    <ui:define name="insertDataTable">
        <p:column headerText="序号" style="width:60px;text-align: center">
            <h:outputLabel value="#{R+1}"/>
        </p:column>
        <p:column headerText="系统类型" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm.systemType.typeCN}"/>
        </p:column>
        <p:column headerText="编码" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm.tempCode}"/>
        </p:column>
        <p:column headerText="备注" style="width:300px;padding-left: 3px">
            <h:outputLabel  value="#{itm.rmk}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;width: 200px;">
            <p:commandLink value="修改" process="@this" action="#{msgTemplateBean.modInitAction}"
                           update=":mainForm:editDialog" oncomplete="PF('EditDialog').show()"  >
                <f:setPropertyActionListener target="#{msgTemplateBean.tdTempmetaType}" value="#{itm}"/>
                <p:resetInput target=":mainForm:editDialog"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="模板维护" process="@this" action="#{msgTemplateBean.tempmetaDefineAction}" update=":mainForm:templateTable"
                           oncomplete="PF('TemplateDialog').show()" >
                <f:setPropertyActionListener target="#{msgTemplateBean.tdTempmetaType}" value="#{itm}"/>
                <f:setPropertyActionListener target="#{msgTemplateBean.tdTempmetaTypeId}" value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{msgTemplateBean.tempSysType}" value="#{itm.systemType}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertDialogs">
        <p:dialog id="editDialog" header="模板类型维护" widgetVar="EditDialog" resizable="false" width="550" modal="true">
            <p:panelGrid style="width:100%;" id="editGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 25%;">
                    <h:outputText style="color: red" value="*"/><h:outputText value="系统类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;">
                    <p:selectOneMenu style="width:200px" value="#{msgTemplateBean.editSystType}" required="true" requiredMessage="系统类型不允许为空！">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{msgTemplateBean.sysTypeMap}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 25%;">
                    <h:outputText style="color: red" value="*"/><h:outputText value="编码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;">
                    <p:inputText style="width: 192px;" value="#{msgTemplateBean.tdTempmetaType.tempCode}" maxlength="25"
                                 required="true" requiredMessage="编码不允许为空！" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 25%;">
                    <h:outputText style="color: red" value="*"/><h:outputText value="备注："/>
                </p:column>
                <p:column style="text-align:left;padding-left:5px;">
                    <p:inputTextarea rows="4" cols="40" autoResize="false"  value="#{msgTemplateBean.tdTempmetaType.rmk}" maxlength="100"
                                 required="true" requiredMessage="备注不允许为空！"
                                 counter="display" counterTemplate="还可以输入{0}个字" />
                    <br />
                    <h:outputText id="display" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:center;" colspan="2">
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
                                     action="#{msgTemplateBean.saveAction}"
                                     process="@this,editGrid" update="dataTable,editGrid"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
                                     onclick="PF('EditDialog').hide();" immediate="true"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        </p:dialog>

        <p:dialog id="templateDialog" header="模板内容" widgetVar="TemplateDialog" resizable="false" width="850" height="380" modal="true">
            <p:outputPanel id="buttonPanel" style="width:100%;text-align:right;padding-right:3px;">
                <p:commandButton id="addTemplateBtn" value="添加模板" action="#{msgTemplateBean.addTemplateInit}"
                                 resetValues="true" process="@this" oncomplete="EditTemplateDialog.show();" update="editTemplateDialog"/>
            </p:outputPanel>
            <p:dataTable id="templateTable" value="#{msgTemplateBean.tdTempmetaDefineList}" var="itm" style="padding-top:5px;width:828px;"
                         emptyMessage="没有数据." scrollable="true" scrollHeight="300" rowIndexVar="var">
                <p:column headerText="序号" style="text-align: center;width: 6%;">
                    <p:outputLabel value="#{var+1}"/>
                </p:column>
                <p:column headerText="模板内容" style="width:67%;padding-left: 3px;">
                    <h:outputText value="#{itm.tempContent}"/>
                </p:column>
                <p:column headerText="是否默认" style="text-align: center;width: 7%;" >
                    <h:outputLabel value="默认" rendered="#{itm.isDefault == 1}"/>
                </p:column>
                <p:column headerText="操作" style="padding-left: 3px;width: 20%;">
                    <p:commandLink value="修改" process="@this" action="#{msgTemplateBean.modTemplateAction}"
                                   update=":mainForm:editTemplateDialog" oncomplete="PF('EditTemplateDialog').show()"  >
                        <f:setPropertyActionListener target="#{msgTemplateBean.tdTempmetaDefine}" value="#{itm}"/>
                    </p:commandLink>
                    <p:spacer width="5" />
                    <p:commandLink rendered="#{itm.isDefault == 0}" value="设为默认" process="@this"
                                   action="#{msgTemplateBean.defaultTempSetAction}" update="templateTable" >
                        <p:confirm header="消息确认框" message="确定要设为默认吗？" icon="ui-icon-alert"/>
                        <f:setPropertyActionListener target="#{msgTemplateBean.tdTempmetaDefineId}" value="#{itm.rid}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="关闭" icon="ui-icon-close" id="cancelChooseTemplateBtn"
                                         onclick="TemplateDialog.hide();" type="button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <p:dialog id="editTemplateDialog" header="模板维护" widgetVar="EditTemplateDialog" resizable="false" width="950" height="425" modal="true">
            <table style="width: 100%;">
                <tr>
                    <td style="vertical-align: top;">
                        <p:panel style="height:411px;" header="模板信息">
                            <h:panelGrid columns="2">
                                <span style="margin-top:5px;"><font style='color:red'>*</font>模板内容：</span>
                                <p:inputTextarea id="templateContent" value="#{msgTemplateBean.templateContent}" rows="6" cols="50"
                                                 counter="tempContentDisplay" maxlength="1000" counterTemplate="还可以输入{0}个字!"
                                                 style="margin-top:5px;"/>
                                <p:spacer/>
                                <h:outputText id="tempContentDisplay" style="margin-top:5px;"/>
                                <p:spacer/>
                                <p:commandButton id="resolveBtn" value="测试" action="#{msgTemplateBean.resolveTemplateAction}"
                                                 process="@this,templateContent" update="resolvedContent" style="margin-top:5px;"/>
                                <h:outputText value="测试内容：" style="margin-top:5px;"/>
                                <p:inputTextarea id="resolvedContent" value="#{msgTemplateBean.resolvedContent}" rows="8" cols="50" readonly="true" style="margin-top:5px;"/>
                            </h:panelGrid>
                        </p:panel>
                    </td>
                    <td style="vertical-align: top;padding: 1px;">
                        <p:panel style="height:300px;width:350px;padding-right:5px;" header="模板元素">
                            <p:dataTable value="#{msgTemplateBean.defineList}" var="define" style="width:340px;" emptyMessage="没有数据."
                                         scrollable="true" scrollHeight="220" filteredValue="#{msgTemplateBean.filterDefineList}" >
                                <p:column headerText="" style="text-align:center;"
                                          filterOptions="#{msgTemplateBean.filterDefineSelectList}" filterMatchMode="exact" filterBy="#{define.systemType.typeCN}" >
                                    <h:outputText value="#{define.systemType.typeCN}" converter="system.EmTempateNameConvert" title="#{define.systemType.typeCN}"/>
                                </p:column>
                                <p:column style="text-align:center;"  headerText=""
                                          filterMatchMode="contains" filterBy="metaName">
                                    <a onmouseup="checkClick('#{define.rid}')" title="单击显示内容，双击添加！"
                                       style="cursor: pointer" href="javascript:;">#{define.metaName}</a>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                        <p:panel id="metaDemo" header="示例" style="margin-top:5px;height:100px;width:350px;">
                            <h:outputText id="metaDemoText" value="#{msgTemplateBean.tbTempmetaDefine.demoDesc}"/>
                        </p:panel>
                    </td>
                </tr>
            </table>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" action="#{msgTemplateBean.saveTemplateAction}"
                                         process="@this,editTemplateDialog" update="templateTable"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="EditTemplateDialog.hide();" type="button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <p:remoteCommand name="refreshMetaDemo" process="@this" action="#{msgTemplateBean.refreshMetaDemo}" update="metaDemo"/>
        <p:remoteCommand name="addMeta" process="@this,templateContent" action="#{msgTemplateBean.addMeta}" update="templateContent"/>
    </ui:define>



</ui:composition>














