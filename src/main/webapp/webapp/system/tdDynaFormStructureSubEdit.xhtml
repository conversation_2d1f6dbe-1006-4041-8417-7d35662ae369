<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

	<style type="text/css">
body {
	overflow-x: hidden;
}
</style>
	<h:form id="subForm">
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
			id="editTitleGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="字段信息维护" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{tdDynaFormStructureBean.saveSubAction}"
					process="@this,editGrid" update=":tabView" />

				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					update=":tabView" process="@this">
					<f:setPropertyActionListener
						target="#{tdDynaFormStructureBean.activeTab}" value="1"></f:setPropertyActionListener>
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
		<p:outputPanel id="editGrid">
			<p:fieldset toggleable="true" legend="基本信息" style="margin-top:5px;">

				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="字段说明：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:inputText
								value="#{tdDynaFormStructureBean.selTdFormField.fdCnname}"
								rendered="#{tdDynaFormStructureBean.selTdFormField.rid==null}"
								onblur="document.getElementById('tabView:subForm:data1').click();"
								id="fdCnname" maxlength="50" />

							<p:inputText
								value="#{tdDynaFormStructureBean.selTdFormField.fdCnname}"
								rendered="#{tdDynaFormStructureBean.selTdFormField.rid!=null}" />

							<p:commandLink style="display:none;" id="data1"
								action="#{tdDynaFormStructureBean.genAutoPy}"
								oncomplete="clearNoABC123_(document.getElementById('tabView:subForm:fdEnname'));"
								process="@this,fdCnname,fdEnname" update="fdEnname">
								<f:setPropertyActionListener value="field"
									target="#{tdDynaFormStructureBean.pyMode}" />
							</p:commandLink>
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:150px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="字段名：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="3">
							<p:inputText
								value="#{tdDynaFormStructureBean.selTdFormField.fdEnname}"
								rendered="#{tdDynaFormStructureBean.selTdFormField.rid==null}"
								id="fdEnname" onkeyup="clearNoABC123_(this)"
								onblur="clearNoABC123_(this)" maxlength="10" />
							<p:outputLabel
								rendered="#{tdDynaFormStructureBean.selTdFormField.rid!=null}"
								value="#{tdDynaFormStructureBean.selTdFormField.fdEnname}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="字段类型：" />
						</p:column>
						<p:column
							style="text-align:left;padding-left:8px;padding:0 0 0 0;"
							colspan="5">
							<h:panelGrid columns="10" id="fieldGrid"
								style="border-color:#FFFFFF;">
								<p:selectOneMenu
									value="#{tdDynaFormStructureBean.selTdFormField.fdDbtype}"
									rendered="#{tdDynaFormStructureBean.selTdFormField.rid==null}">
									<f:selectItem itemLabel="--请选择--" itemValue="" />
									<f:selectItems value="#{tdDynaFormStructureBean.fieldTypeList}" />
									<p:ajax event="change" process="@this" update="fieldGrid" />
								</p:selectOneMenu>

								<p:outputLabel
									rendered="#{tdDynaFormStructureBean.selTdFormField.rid!=null}"
									value="#{tdDynaFormStructureBean.selTdFormField.fdDbtypeStr}" />

								<p:outputLabel value="字符串长度："
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NVARCHAR2' }" />
								<p:inputText
									value="#{tdDynaFormStructureBean.selTdFormField.lenChar}"
									size="4"
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NVARCHAR2' }"
									onkeyup="SYSTEM.clearNoNum(this)"
									onblur="SYSTEM.clearNoNum(this)" maxlength="4" />
								<p:outputLabel value="整数长度："
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='INTEGER' or tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }" />
								<p:inputText
									value="#{tdDynaFormStructureBean.selTdFormField.lenInt}"
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='INTEGER'  or tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }"
									onkeyup="SYSTEM.clearNoNum(this)"
									onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
								<p:outputLabel value="小数长度："
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }" />
								<p:inputText
									value="#{tdDynaFormStructureBean.selTdFormField.lenDemi}"
									rendered="#{tdDynaFormStructureBean.selTdFormField.fdDbtype=='NUMBER' }"
									onkeyup="SYSTEM.clearNoNum(this)"
									onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
							</h:panelGrid>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>
			<p:fieldset toggleable="true" legend="扩展信息" style="margin-top:5px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column style="text-align:right;padding-right:8px;width:150px;">
							<p:outputLabel value="列表显示：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:selectOneRadio style="width:160px"
								value="#{tdDynaFormStructureBean.selTdFormField.listBol}">
								<f:selectItem itemValue="true" itemLabel="是" />
								<f:selectItem itemValue="false" itemLabel="否" />
							</p:selectOneRadio>
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="查询条件：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:selectOneRadio style="width:160px"
								value="#{tdDynaFormStructureBean.selTdFormField.ifSearch}">
								<f:selectItem itemValue="true" itemLabel="是" />
								<f:selectItem itemValue="false" itemLabel="否" />
								<p:ajax event="change" process="@this" update="searchType"/>
							</p:selectOneRadio>
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="匹配方式：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:selectOneMenu style="width:160px"
								value="#{tdDynaFormStructureBean.selTdFormField.searchType}" id="searchType" disabled="#{!tdDynaFormStructureBean.selTdFormField.ifSearch}">
								<f:selectItem itemLabel="--请选择--" itemValue=""/>
								<f:selectItems value="#{tdDynaFormStructureBean.searchTypeList}" />
							</p:selectOneMenu>
						</p:column>
					</p:row>
					
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="显示类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:selectOneMenu
								value="#{tdDynaFormStructureBean.selTdFormField.isShow}">
								<f:selectItem itemValue="0" itemLabel="不显示" />
								<f:selectItem itemValue="1" itemLabel="正常显示" />
								<f:selectItem itemValue="2" itemLabel="只读显示" />
							</p:selectOneMenu>
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="是否必输：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="3">
							<p:selectOneRadio style="width:160px"
								value="#{tdDynaFormStructureBean.selTdFormField.reqBol}">
								<f:selectItem itemValue="true" itemLabel="是" />
								<f:selectItem itemValue="false" itemLabel="否" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="编辑时行号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:inputText
								value="#{tdDynaFormStructureBean.selTdFormField.rowNum}"
								onkeyup="SYSTEM.clearNoNum(this)"
								onblur="SYSTEM.clearNoNum(this)" maxlength="2" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:150px;">
							<p:outputLabel value="编辑时列号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:inputText
								value="#{tdDynaFormStructureBean.selTdFormField.colNum}"
								onkeyup="SYSTEM.clearNoNum(this)"
								onblur="SYSTEM.clearNoNum(this)" maxlength="2" />
						</p:column>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="所占列数：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:inputText
								value="#{tdDynaFormStructureBean.selTdFormField.colSpan}"
								onkeyup="SYSTEM.clearNoNum(this)"
								onblur="SYSTEM.clearNoNum(this)" maxlength="2" />
							<p:spacer width="5" />
							<p:outputLabel value="如不需要合并，则默认填1" style="color:blue;" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="是否流程变量：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:selectOneRadio style="width:160px"
								value="#{tdDynaFormStructureBean.selTdFormField.proValBol}">
								<f:selectItem itemValue="true" itemLabel="是" />
								<f:selectItem itemValue="false" itemLabel="否" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="展示类型：" />
						</p:column>
						<p:column
							style="text-align:left;padding-left:8px;padding: 0px 0px 0px 0px;vertical-align:middle;"
							colspan="5">
							<h:panelGrid columns="20"
								style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:selectOneMenu
									value="#{tdDynaFormStructureBean.selTdFormField.dataSrc}"
									style="width:120px;">
									<f:selectItem itemLabel="--请选择--" itemValue="" />
									<f:selectItems value="#{tdDynaFormStructureBean.fieldShowList}" />
									<p:ajax event="change" process="@this"
										update="ifDateSel,ifCodeSel,ifCodeSel2" />
								</p:selectOneMenu>


								<p:outputPanel id="ifDateSel">
									<h:panelGrid columns="5"
										style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;"
										rendered="#{tdDynaFormStructureBean.selTdFormField.dataSrc=='dict_select_one' or tdDynaFormStructureBean.selTdFormField.dataSrc=='dict_select_many'}">
										<h:outputText value="　加载数据方式：" />
										<p:selectOneMenu
											value="#{tdDynaFormStructureBean.selTdFormField.dataFrom}"
											style="width:120px;">
											<f:selectItem itemLabel="码表" itemValue="1" />
											<f:selectItem itemLabel="SQL语句" itemValue="2" />
											<p:ajax event="change" process="@this"
												update="ifCodeSel,ifCodeSel2" />
										</p:selectOneMenu>
									</h:panelGrid>
								</p:outputPanel>

								<p:outputPanel id="ifCodeSel">
									<h:panelGrid columns="5"
										style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;"
										rendered="#{(tdDynaFormStructureBean.selTdFormField.dataSrc=='dict_select_one' or tdDynaFormStructureBean.selTdFormField.dataSrc=='dict_select_many'  ) and tdDynaFormStructureBean.selTdFormField.dataFrom==1}">
										<p:spacer width="5" />
										<p:outputLabel value="码表类型：" />
										<p:inputText id="simCode"
											value="#{tdDynaFormStructureBean.selCode==null?null:tdDynaFormStructureBean.selCode.codeTypeDesc}"
											style="width: 182px;cursor: pointer"
											onclick="document.getElementById('tabView:subForm:selCodeLink').click();"
											readonly="true" />
										<p:commandLink styleClass="ui-icon ui-icon-search"
											id="selCodeLink" process="@this"
											style="position: relative;left: -28px;"
											action="#{tdDynaFormStructureBean.showCodeModel}"
											update="codeDialog" oncomplete="PF('CodeDialog').show()">
										</p:commandLink>
										<p:commandLink styleClass="ui-icon ui-icon-trash"
											id="clearBtn2" title="清空" update="simCode"
											action="#{tdDynaFormStructureBean.clearCodeAciton}"
											process="@this" style="position: relative;left: -25px;">
										</p:commandLink>
									</h:panelGrid>
								</p:outputPanel>
							</h:panelGrid>
							<p:outputLabel id="ifCodeSel2">
								<h:panelGrid columns="20"
									style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;"
									rendered="#{(tdDynaFormStructureBean.selTdFormField.dataSrc=='dict_select_one' or tdDynaFormStructureBean.selTdFormField.dataSrc=='dict_select_many' ) and tdDynaFormStructureBean.selTdFormField.dataFrom==2}">
									<p:outputPanel>
										<p:outputPanel id="textSel">
											<p:inputTextarea counterTemplate="还可以输入{0}个字!"
												counter="querySql"
												value="#{tdDynaFormStructureBean.selTdFormField.querySql}"
												autoResize="false" rows="5" cols="100" maxlength="500" />
											<br />
											<p:outputLabel id="querySql" />
										</p:outputPanel>
									</p:outputPanel>
								</h:panelGrid>
							</p:outputLabel>
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="脚本类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:selectOneMenu
								value="#{tdDynaFormStructureBean.selTdFormField.scriptType}">
								<f:selectItem itemValue="" itemLabel="--请选择--" />
								<f:selectItem itemValue="0" itemLabel="页面初始化脚本" />
								<f:selectItem itemValue="1" itemLabel="保存前执行脚本" />
							</p:selectOneMenu>
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="数据设置脚本：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:inputTextarea counterTemplate="还可以输入{0}个字!"
								counter="dataScriptD"
								value="#{tdDynaFormStructureBean.selTdFormField.dataScript}"
								autoResize="false" rows="5" cols="100" maxlength="1000" />
							<br />
							<p:outputLabel id="dataScriptD" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>

		</p:outputPanel>


		<p:dialog id="codeDialog" widgetVar="CodeDialog" header="码表类型选择"
			resizable="false" width="780" modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="关闭" icon="ui-icon-close" process="@this"
						oncomplete="PF('CodeDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							id="searchCodeDiag" columns="6">
							<p:outputLabel value="系统类型：" styleClass="zwx_dialog_font" />
							<p:selectOneMenu id="msgType" style="width:200px"
								value="#{tdDynaFormStructureBean.sysTypeC}">
								<f:selectItem itemLabel="--全部--" itemValue="" />
								<f:selectItems value="#{tdDynaFormStructureBean.sysTypeMap}" />
								<p:ajax event="change" process="@this,searchCodeDiag"
									update=":tabView:subForm:codeDatatable"
									listener="#{tdDynaFormStructureBean.filterCodeList}" />
							</p:selectOneMenu>
							<p:spacer width="5" />
							<p:outputLabel value="名　称：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{tdDynaFormStructureBean.searchCodeName}"
								style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this,searchCodeDiag"
									update=":tabView:subForm:codeDatatable"
									listener="#{tdDynaFormStructureBean.filterCodeList}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{tdDynaFormStructureBean.showCodeList}"
				var="typ" emptyMessage="暂无记录" paginator="true" rows="10"
				paginatorPosition="bottom" id="codeDatatable">
				<p:column headerText="操作" style="width:60px;text-align:center;">
					<p:commandLink value="选择" process="@this"
						update=":tabView:subForm:simCode"
						oncomplete="PF('CodeDialog').hide()">
						<f:setPropertyActionListener
							target="#{tdDynaFormStructureBean.selCode}" value="#{typ}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="类型编码" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ.codeTypeName}" />
				</p:column>
				<p:column headerText="类型名称" style="padding-left: 3px;">
					<h:outputLabel value="#{typ.codeTypeDesc}" />
				</p:column>
			</p:dataTable>
		</p:dialog>
	</h:form>
</ui:composition>
