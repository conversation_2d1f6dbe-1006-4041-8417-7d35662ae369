<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage2.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.system.web.ContrastManageBean"-->
    <ui:param name="mgrbean" value="#{contrastManageBean}"/>
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>

    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertContent">
        <div style="display: flex;align-items: flex-start;justify-content: space-between;">
            <div style="display: flex;flex-direction: column;width: 30%;">
                <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                    <f:facet name="header">
                        <p:row>
                            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                                <h:outputText value="业务类型"/>
                                <p:inputText style="visibility: hidden;width: 0;"/>
                            </p:column>
                        </p:row>
                    </f:facet>
                </p:panelGrid>
                <p:outputPanel styleClass="zwx_toobar_42" style="display: flex;">
                    <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                        <p:commandButton value="添加" icon="ui-icon-plus"
                                         update=":mainForm:contraDialog"
                                         action="#{mgrbean.addContra}" resetValues="true"/>
                    </h:panelGrid>
                </p:outputPanel>
                <div style="display: table-cell;vertical-align: middle;width: 100%;padding-top: 5px;">
                    <div style="display: table-row;">
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:outputLabel value="业务类型/业务说明："/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:inputText style="width: 180px;" onkeydown="if(event.keyCode===13){return false;}"
                                         value="#{mgrbean.contraCodeSearch}" maxlength="100">
                                <p:ajax event="keyup" update=":mainForm:dataTable,:mainForm:subPanel"
                                        process="@this,:mainForm"
                                        listener="#{mgrbean.searchMainAction}"/>

                            </p:inputText>
                        </div>
                    </div>

                    <p:dataTable var="itm" value="#{mgrbean.contraTableList}"
                                 id="dataTable" lazy="true" emptyMessage="没有您要找的记录！"
                                 rowIndexVar="R" rowKey="#{itm[0]}" style="margin-top: 5px;"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="20,50,100" pageLinks="5"
                                 paginator="true" rows="20" paginatorPosition="bottom"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                 selectionMode="single" selection="#{mgrbean.selRecord}">
                        <p:ajax event="rowSelect" process=":mainForm:dataTable"  oncomplete="zwx_loading_stop();"
                                listener="#{mgrbean.onRowSelect}"  onstart="zwx_loading_start();"
                                update=":mainForm:subPanel" resetValues="true">
                            <p:resetInput target=":mainForm"/>
                        </p:ajax>
                        <p:column headerText="业务类型" style="width:60px;line-height: 24px;text-align: center;">
                            <h:outputText value="#{itm[1]}"/>
                        </p:column>
                        <p:column headerText="业务说明" style="width:130px;line-height: 24px;">
                            <h:outputText value="#{itm[2]}"/>
                        </p:column>
                        <p:column headerText="备注" style="line-height: 24px;">
                            <h:outputText id="contraRmk" value="#{itm[3]}" styleClass="zwx-tooltip"/>
                            <p:tooltip for="contraRmk" style="max-width:450px;">
                                <p:outputLabel styleClass="cs-break-word" value="#{itm[3]}" escape="false"/>
                            </p:tooltip>
                        </p:column>
                        <p:column headerText="操作" style="width: 60px;line-height: 24px;">
                            <p:commandLink value="修改" resetValues="true"
                                           process="@this" update=":mainForm:contraDialog"
                                           action="#{mgrbean.modContraAction}">
                                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                            </p:commandLink>
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" resetValues="true"
                                           process="@this,:mainForm" update="dataTable,:mainForm:subPanel"
                                           action="#{mgrbean.delContraAction}">
                                <p:confirm header="消息确认框" message="将删除当前业务类型的所有对照数据，是否删除？"
                                           icon="ui-icon-alert"/>
                                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </div>
            </div>
            <div style="display: flex;flex-direction: column;width: 68%;">
                <p:outputPanel id="subPanel"
                               style="width: 100%;display: #{mgrbean.selRecord!=null?'block;':'none;'}">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                                    <h:outputText value="#{mgrbean.subTitle}"/>
                                    <p:inputText style="visibility: hidden;width: 0;"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel styleClass="zwx_toobar_42" style="display: flex;">
                        <h:panelGrid columns="6" style="border-color:transparent;padding:0px;" id="right_btn">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" resetValues="true" icon="ui-icon-plus"
                                             update=":mainForm:contraSubDialog"
                                             action="#{mgrbean.addContraSub}"/>
                            <p:menuButton id="importRadhethBtn" value="导入" >
                                <p:menuitem value="模板下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                            onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                                    <p:fileDownload value="#{mgrbean.getTemplateFile()}"/>
                                </p:menuitem>
                                <p:menuitem value="导入" icon="ui-icon-arrowreturnthick-1-n" update=":mainForm:uploadFileDialog" process="@this"
                                            action="#{mgrbean.openImportDialog}">
                                </p:menuitem>
                            </p:menuButton>
                            <p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"
                                             process="@this"
                                             onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"
                                             rendered="#{null ne mgrbean.importErrFilePath}">
                                <p:fileDownload value="#{mgrbean.getErrorImportFile()}"/>
                            </p:commandButton>
                            <p:commandButton value="全部删除"  resetValues="true"
                                             update=":mainForm:delContraSubDialog,:mainForm:busiTypePane"
                                             action="#{mgrbean.delSubByType}"/>
                        </h:panelGrid>
                    </p:outputPanel>
                    <div style="display: table-cell;vertical-align: middle;width: inherit;padding-top: 5px;">
                        <div style="display: flex;align-items: center;">
                            <div style="display: table-cell;vertical-align: middle;">
                                <h:outputLabel value="对照类型/对照类型说明："/>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;">
                                <p:inputText style="width: 180px;" value="#{mgrbean.busiTypeSearch}"
                                             maxlength="100" onkeydown="if(event.keyCode===13){return false;}">
                                    <p:ajax event="keyup" update=":mainForm:subTable" process="@this"
                                            listener="#{mgrbean.searchSubAction}"/>
                                </p:inputText>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;margin-left: 10px;">
                                <h:outputLabel value="LEFT_CODE/LEFT_DESC：" />
                            </div>
                            <div style="display: table-cell;vertical-align: middle;">
                                <p:inputText style="width: 180px;" value="#{mgrbean.leftCodeSearch}"
                                             maxlength="50" onkeydown="if(event.keyCode===13){return false;}">
                                    <p:ajax event="keyup" update=":mainForm:subTable" process="@this"
                                            listener="#{mgrbean.searchSubAction}"/>
                                </p:inputText>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;margin-left: 10px;">
                                <h:outputLabel value="RIGHT_CODE/RIGHT_DESC："/>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;">
                                <p:inputText style="width: 180px;" value="#{mgrbean.rightCodeSearch}"
                                             maxlength="50" onkeydown="if(event.keyCode===13){return false;}">
                                    <p:ajax event="keyup" update=":mainForm:subTable" process="@this"
                                            listener="#{mgrbean.searchSubAction}"/>
                                </p:inputText>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;">

                            </div>
                        </div>
                    </div>
                    <p:dataTable var="item" value="#{mgrbean.dataModel}" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{mgrbean.perPageSize}" id="subTable" lazy="true" emptyMessage="没有您要找的记录！" style="margin-top: 5px;"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                        <p:columnGroup type="header">
                            <p:row>
                                <p:column headerText="对照类型"
                                          style="width:60px;line-height: 24px;text-align: center"/>
                                <p:column headerText="对照类型说明" style="width:160px;text-align: center;"/>
                                <p:column headerText="LEFT_CODE" style="width:80px;text-align: center;"/>
                                <p:column headerText="LEFT_DESC" style="width:160px;text-align: center;"/>
                                <p:column headerText="RIGHT_CODE" style="width:80px;text-align: center;"/>
                                <p:column headerText="RIGHT_DESC" style="width:160px;text-align: center;"/>
                                <p:column headerText="特殊标记" style="width:80px;text-align: center;"/>
                                <p:column headerText="备注" style="width:150px;text-align: center;"/>
                                <p:column headerText="操作" style="text-align: center;"/>
                            </p:row>
                        </p:columnGroup>
                        <p:column style="text-align: center;line-height: 24px;">
                            <h:outputLabel value="#{item[1]}"/>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputText id="busDesc" value="#{item[2]}" styleClass="zwx-tooltip" escape="false"/>
                            <p:tooltip for="busDesc" style="max-width:450px;">
                                <p:outputLabel styleClass="cs-break-word" value="#{item[2]}" escape="false"/>
                            </p:tooltip>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputLabel value="#{item[3]}" />
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputText id="leftDesc" value="#{item[4]}" styleClass="zwx-tooltip" escape="false"/>
                            <p:tooltip for="leftDesc" style="max-width:450px;">
                                <p:outputLabel styleClass="cs-break-word" value="#{item[4]}" escape="false"/>
                            </p:tooltip>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputLabel value="#{item[5]}" escape="false"/>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputText id="descr" value="#{item[6]}" styleClass="zwx-tooltip" escape="false"/>
                            <p:tooltip for="descr" style="max-width:450px;">
                                <p:outputLabel styleClass="cs-break-word" value="#{item[6]}" escape="false"/>
                            </p:tooltip>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputLabel value="#{item[7]}"/>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <h:outputText id="dsfSpecialDesc" value="#{item[8]}" styleClass="zwx-tooltip"
                                          escape="false"/>
                            <p:tooltip for="dsfSpecialDesc" style="max-width:450px;">
                                <p:outputLabel styleClass="cs-break-word" value="#{item[8]}" escape="false"/>
                            </p:tooltip>
                        </p:column>
                        <p:column style="line-height: 24px;">
                            <p:commandLink value="修改" resetValues="true"
                                           process="@this" update=":mainForm:contraSubDialog"
                                           action="#{mgrbean.modContraSubAction}">
                                <f:setPropertyActionListener target="#{mgrbean.subRid}" value="#{item[0]}"/>
                            </p:commandLink>
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" resetValues="true"
                                           process="@this" update=":mainForm:subTable"
                                           action="#{mgrbean.delContraSubAction}">
                                <p:confirm header="消息确认框" message="确定要删除吗？"
                                           icon="ui-icon-alert"/>
                                <f:setPropertyActionListener target="#{mgrbean.subRid}" value="#{item[0]}"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:outputPanel>
            </div>
        </div>

        <p:dialog header="业务类型" widgetVar="ContraDialog" id="contraDialog"
                  resizable="false" width="450" height="185" modal="true">
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="业务类型："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 180px;" maxlength="15" onkeyup="SYSTEM.clearNoNumBig0(this)"
                                     onblur="SYSTEM.clearNoNumBig0(this)"
                                     onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraMain.contraCode}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="业务说明："/>
                    </p:column>
                    <p:column style="text-align:left;">
                        <p:inputText style="width: 300px;" onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraMain.descr}" maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <p:outputLabel value="备注："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputTextarea value="#{mgrbean.contraMain.rmk}" maxlength="1000"
                                         style="width:300px;resize: none;height:50px;" autoResize="false"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check"
                                         update=":mainForm:dataTable,:mainForm:subPanel" process="@this,contraDialog"
                                         action="#{mgrbean.saveContra}"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close" update=":mainForm:dataTable,:mainForm:subTable"
                                         onclick="PF('ContraDialog').hide();"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <p:dialog header="对照维护" widgetVar="ContraSubDialog" id="contraSubDialog"
                  resizable="false" width="470" height="325" modal="true" >
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%">
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="对照类型："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 180px;" maxlength="3"
                                     onkeydown="SYSTEM.verifyNum3(this, 3, 0, false);if(event.keyCode===13){return false;}"
                                     onkeyup="SYSTEM.verifyNum3(this, 3, 0, false)"
                                     onblur="SYSTEM.verifyNum3(this, 3, 0, true)"
                                     value="#{mgrbean.contraSub.busiType}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <p:outputLabel value="对照类型说明："/>
                    </p:column>
                    <p:column style="text-align:left;">
                        <p:inputText style="width: 300px;" onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraSub.busDesc}" maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="LEFT_CODE："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 300px;" onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraSub.leftCode}" maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <p:outputLabel value="LEFT_DESC："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 300px;" onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraSub.leftDesc}" maxlength="100"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="RIGHT_CODE："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 300px;" onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraSub.rightCode}" maxlength="50"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <p:outputLabel value="RIGHT_DESC："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 300px;" onkeydown="if(event.keyCode===13){return false;}"
                                     value="#{mgrbean.contraSub.descr}" maxlength="100"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <p:outputLabel value="特殊标记："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputText style="width: 300px;"
                                     maxlength="1"   onkeydown="SYSTEM.verifyNum3(this, 1, 0, false);if(event.keyCode===13){return false;}"
                                     onkeyup="SYSTEM.verifyNum3(this, 1, 0, false)"
                                     onblur="SYSTEM.verifyNum3(this, 1, 0, true)"
                                     value="#{mgrbean.contraSub.dsfTag}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <p:outputLabel value="备注："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:inputTextarea value="#{mgrbean.contraSub.dsfSpecialDesc}" maxlength="50"
                                         style="width:300px;resize: none;height:50px;" autoResize="false"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check"
                                         update=":mainForm:subTable" process="@this,contraSubDialog"
                                         action="#{mgrbean.saveSubContra}"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close" update=":mainForm:dataTable,:mainForm:subTable"
                                         onclick="PF('ContraSubDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <p:dialog header="全部删除" widgetVar="DelContraSubDialog" id="delContraSubDialog"
                  resizable="false" width="450" height="60" modal="true"  >
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%" id="busiTypePane">
                <p:row>
                    <p:column style="text-align: right;width: 100px">
                        <h:outputText value="*" style="color:red;"/>
                        <p:outputLabel value="类型："/>
                    </p:column>
                    <p:column style="text-align:left;width: 300px">
                        <p:selectOneMenu value="#{mgrbean.delType}" style="width: 100px;" id="busiType">
                            <f:selectItems value="#{mgrbean.busiTypeList}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="全部删除" icon="ui-icon-check"
                                         update=":mainForm:subTable" process="@this,:mainForm:busiTypePane"
                                         onclick="zwx_loading_start();"
                                         action="#{mgrbean.delAllSubByType}">
                            <p:confirm header="消息确认框" message="确定要删除该类型下所有的记录吗？"
                                       icon="ui-icon-alert"/>
                        </p:commandButton>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close" update=":mainForm:dataTable,:mainForm:subTable"
                                         onclick="PF('DelContraSubDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <p:dialog header="对照维护导入" widgetVar="UploadFileDialog"
                  id="uploadFileDialog" resizable="false"
                  modal="true" width="800">
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要文件上传！" styleClass="table-border-none"
                                      id="fileUpload"
                                      process="@this" fileUploadListener="#{mgrbean.importDataAction}"
                                      label="选择文件" invalidSizeMessage="文件大小不能超过200M!"
                                      validatorMessage="上传出错啦，请重新上传！"
                                      allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1"
                                      fileLimitMessage="最多只能上传1个文件！"
                                      invalidFileMessage="只能上传xls、xlsx格式的文件！"
                                      previewWidth="120" cancelLabel="取消"
                                      uploadLabel="导入" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                      dragDropSupport="true" mode="advanced" sizeLimit="209715200"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
        <ui:include src="/WEB-INF/templates/system/hideTooltips.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml" />
    </ui:define>
</ui:composition>