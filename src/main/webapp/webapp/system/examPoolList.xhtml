<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
	<style type="text/css">
.ui-treetable thead th,.ui-treetable tbody td,.ui-treetable tfoot td {
	white-space: normal;
}
</style>
	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<p:tabView id="tabView" dynamic="true" cache="true"
			activeIndex="#{examPoolBean.activeTab}"
			style="border:1px; padding:0px;">
			<p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<h:form id="mainForm">
					<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
						id="titleGrid">
						<f:facet name="header">
							<p:row>
								<p:column colspan="6"
									style="text-align:left;padding-left:5px;height: 20px;">
									<h:outputText value="题库管理" />
								</p:column>
							</p:row>
						</f:facet>
					</p:panelGrid>

					<p:outputPanel id="buttonsPanel">
						<p:outputPanel styleClass="zwx_toobar_42">
							<h:panelGrid columns="3"
								style="border-color:transparent;padding:0px;">
								<span class="ui-separator"><span
									class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
									action="#{examPoolBean.searchAction}" update="dataTable"
									process="@this,:tabView:mainForm:proTypeTree,searchQusetType,searchTitleDesc,searchState" />
								<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
									action="#{examPoolBean.addInitAction}" update=":tabView"
									process="@this">
									<p:resetInput target=":tabView:editForm:editGrid" />
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel>
					</p:outputPanel>


					<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
						style="margin-top: 5px;margin-bottom: 5px;"
						rendered="#{condition==null}">
						<p:panelGrid style="width:100%;height:100%;" id="mainGrid">
							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;width:180px;height:35px; ">
									<h:outputText value="题库类型：" />
								</p:column>


								<p:column style="text-align:left;padding-left:3px; width:280px">
									<h:panelGrid columns="5"
										style="border-color: #ffffff;margin: 0px;padding: 0px;">
										<p:inputText id="searchProType"
											value="#{examPoolBean.searchProTypeName}" readonly="true"
											style="width:180px;" />
										<p:commandLink styleClass="ui-icon ui-icon-search"
											id="initTreeLink" partialSubmit="true" process="@this"
											style="position: relative;left: -30px;"
											oncomplete="PF('searchProTypePanel').show()">
										</p:commandLink>
										<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
											style="position: relative;left: -35px;"
											update=":tabView:mainForm:searchProType"
											action="#{examPoolBean.clearSearchNodeSelected}"
											process="@this">
										</p:commandLink>

										<p:overlayPanel id="searchProTypePanel" for="searchProType"
											style="width:300px;" widgetVar="searchProTypePanel">

											<p:tree dynamic="true"
												value="#{examPoolBean.searchTsProbExamtypeTree}" var="node"
												selectionMode="single"
												selection="#{examPoolBean.selectedSearchProTypeNode}"
												id="proTypeTree"
												style="width: 280px;height: 220px;overflow-y: auto;">
												<p:ajax event="select"
													update="@this,:tabView:mainForm:searchProType,:tabView:mainForm:searchTsProbExamtypeId"
													partialSubmit="true"
													listener="#{examPoolBean.onSearchProtypeNodeSelect}"
													oncomplete="PF('searchProTypePanel').hide();" />
												<p:treeNode>
													<h:outputText value="#{node.codeName}" />
												</p:treeNode>
											</p:tree>

										</p:overlayPanel>

										<h:inputHidden id="searchTsProbExamtypeId"
											value="#{examPoolBean.searchTsProbExamtypeId}" />
									</h:panelGrid>
								</p:column>
								
								<p:column
									style="text-align:right;padding-right:3px;height:35px;width:180px">
									<h:outputText value="题目标题：" />
								</p:column>
								<p:column style="text-align:left;padding-left:8px;;">
									<p:inputText id="searchTitleDesc" size="50"
										value="#{examPoolBean.searchTitleDesc}" maxlength="50" />
								</p:column>
							</p:row>

							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;width:180px;height:35px;">
									<h:outputText value="题型：" />
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width:280px">
									<p:selectOneMenu id="searchQusetType"
										value="#{examPoolBean.searchQusetType}" style="width: 120px;">
										<f:selectItem itemLabel="--请选择--" itemValue="" />
										<f:selectItems value="#{examPoolBean.questTypeList}" var="itm"
											itemLabel="#{itm.typeCN}" itemValue="#{itm.typeNo}" />

									</p:selectOneMenu>
								</p:column>
								<p:column style="text-align:right;padding-right:3px;">
									<h:outputText value="状态：" />
								</p:column>
								<p:column>
									<p:selectManyCheckbox value="#{examPoolBean.searchState}"
										id="searchState">
										<f:selectItem itemLabel="启用" itemValue="1" />
										<f:selectItem itemLabel="停用" itemValue="0" />
									</p:selectManyCheckbox>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>
					<p:dataTable var="item" value="#{examPoolBean.dataModel}" paginator="true" rows="#{examPoolBean.pageSize}" paginatorPosition="bottom"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="#{examPoolBean.perPageSize}" pageLinks="5" id="dataTable" lazy="true" emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" rowIndexVar="R" >
				
						<p:column headerText="题库类型" size="30"
							style="width: 120px;text-align: center;padding-left:#{item.examTypeDot*20}px;">
							<h:outputText value="#{item.tsSimpleCodeByTypeId.codeName}" />
						</p:column>
						<p:column headerText="题型" style="width: 70px;text-align: center;">
							<h:outputText value="#{item.questType.typeCN}" />
						</p:column>
						<p:column headerText="题目标题"
							style="width: 700px;padding-left:#{item.poolDot*20}px;">
							<h:outputText value="#{item.titleDesc}" />
						</p:column>
						<p:column headerText="题目编码" style="width: 30px;text-align: center;">
							<h:outputText value="#{item.qesCode}" />
						</p:column>
						<p:column headerText="状态" style="width: 30px;text-align: center;">
							<h:outputText value="#{item.state==0?'停用':'启用'}" />
						</p:column>
						<p:column headerText="操作" style="padding-left: 3px;width:180px">
							<p:commandLink value="修改" action="#{examPoolBean.modInitAction}"
								update=":tabView,:tabView:editForm:optLayoutGrid" process="@this" resetValues="true">
								<f:setPropertyActionListener target="#{examPoolBean.rid}"
									value="#{item.rid}" />
								<f:setPropertyActionListener target="#{examPoolBean.tsProbExampool}"
									value="#{item}" />	
								<p:resetInput target=":tabView:editForm:editGrid" />
							</p:commandLink>
							<p:spacer width="5" />
							<p:commandLink value="删除" action="#{examPoolBean.deleteAction}"
								update="dataTable" process="@this">
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
								<f:setPropertyActionListener
									target="#{examPoolBean.qesLevelCode}"
									value="#{item.qesLevelCode}" />
							</p:commandLink>
							<p:spacer width="5" />
							<p:commandLink value="停用"
								action="#{examPoolBean.changeStateAction}" update="dataTable"
								process="@this" rendered="#{item.state=='1'}">
								<p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert" />
								<f:setPropertyActionListener
									target="#{examPoolBean.qesLevelCode}"
									value="#{item.qesLevelCode}" />
									<f:setPropertyActionListener target="#{examPoolBean.tsProbExampool}"
									value="#{item}" />
							</p:commandLink>
							<p:commandLink value="启用"
								action="#{examPoolBean.changeStateAction}" update="dataTable"
								process="@this" rendered="#{item.state!='1'}">
								<p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert" />
								<f:setPropertyActionListener
									target="#{examPoolBean.qesLevelCode}"
									value="#{item.qesLevelCode}" />
									<f:setPropertyActionListener target="#{examPoolBean.tsProbExampool}"
									value="#{item}" />
							</p:commandLink>
							<p:spacer width="5" />

							<p:commandLink value="添加子题目"
								action="#{examPoolBean.addInitAction}" update=":tabView"
								process="@this">
								<f:setPropertyActionListener
									target="#{examPoolBean.qesLevelCode}"
									value="#{item.qesLevelCode}" />
								<f:setPropertyActionListener target="#{examPoolBean.rid}"
									value="#{item.rid}" />
								<p:resetInput target=":tabView:editForm:editGrid" />
							</p:commandLink>
							<p:spacer width="5" />
						</p:column>
					</p:dataTable>

				</h:form>
			</p:tab>
			<p:tab id="edit" title="edit" titleStyle="display:none;">
				<ui:include src="/webapp/system/examPoolEdit.xhtml"></ui:include>
			</p:tab>
			<p:tab id="view" title="view" titleStyle="display:none;">
				<ui:include src="#{viewPage}"></ui:include>
			</p:tab>
			<p:tab id="other" title="other" titleStyle="display:none;">
				<ui:include src="#{otherPage}"></ui:include>
			</p:tab>
		</p:tabView>
		<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
