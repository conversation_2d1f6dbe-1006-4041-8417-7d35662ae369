<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:pe="http://primefaces.org/ui/extensions"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdDynaFormStructureBean}" />
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />

	<!-- 样式或javascripts -->
	<ui:define name="insertEditScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
		<style type="text/css">
body {
	overflow-x: hidden;
}
</style>

	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="动态表单数据结构维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10" style="border-color:transparent;padding:0px;" id="btnPanel" >
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{tdDynaFormStructureBean.saveAction}" rendered="#{tdDynaFormStructureBean.mTdFormTable.state != 1}"
					process="@this,editPanelInfo" update="btnPanel,editPanelInfo" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e" rendered="#{tdDynaFormStructureBean.mTdFormTable.state != 1}" id="submitBtn"
				 update="btnPanel,editPanelInfo"
					action="#{tdDynaFormStructureBean.confirmEditRecord(1)}" process="@this,editPanelInfo">
					<p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert" />
				</p:commandButton>
				<p:commandButton value="撤销" icon="ui-icon-circle-triangle-e" rendered="#{tdDynaFormStructureBean.mTdFormTable.state == 1}" id="cancelBtn"
				 update="btnPanel"
					action="#{tdDynaFormStructureBean.confirmEditRecord(0)}" process="@this">
					<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert" />
				</p:commandButton>

				<p:commandButton value="添加表单字段" icon="ui-icon-plus" id="addMain1"
					rendered="#{tdDynaFormStructureBean.formModel == '1' and tdDynaFormStructureBean.mTdFormTable.state != 1}" action="#{tdDynaFormStructureBean.addSub}"
					update=":tabView:editForm:fieldList1" process="@this,editPanelInfo">
					<f:setPropertyActionListener value="1" target="#{tdDynaFormStructureBean.opType}" />
				</p:commandButton> 
				<p:commandButton value="添加主表字段" icon="ui-icon-plus" id="addMain2"
					rendered="#{tdDynaFormStructureBean.formModel == '2' and tdDynaFormStructureBean.mTdFormTable.state != 1}" action="#{tdDynaFormStructureBean.addSub}"
					update=":tabView:editForm:fieldList2" process="@this,editPanelInfo">
					<f:setPropertyActionListener value="2" target="#{tdDynaFormStructureBean.opType}" />
				</p:commandButton>
				<p:commandButton value="添加子表字段" icon="ui-icon-plus" id="addSub2"
					rendered="#{tdDynaFormStructureBean.formModel == '2' and tdDynaFormStructureBean.mTdFormTable.state != 1}" action="#{tdDynaFormStructureBean.addSub}"
					update=":tabView:editForm:fieldList3" process="@this,editPanelInfo">
					<f:setPropertyActionListener value="3" target="#{tdDynaFormStructureBean.opType}" />
				</p:commandButton>
				
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tdDynaFormStructureBean.backAction}" update=":tabView" process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
	</ui:define>

	<!-- 其余内容 -->
	<ui:define name="insertOtherContents">
		<p:outputPanel id="editPanelInfo">
			<p:panel header="基本信息" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="表模式：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:selectOneRadio style="width:180px" value="#{tdDynaFormStructureBean.formModel}" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}">
								<f:selectItem itemLabel="单表" itemValue="1" />
								<f:selectItem itemLabel="主子表" itemValue="2" />
								<p:ajax event="change" process="@this" listener="#{tdDynaFormStructureBean.changeTableModel}" update="formDetail,btnPanel" />
							</p:selectOneRadio>
							<p:outputLabel value="#{tdDynaFormStructureBean.formModel=='1'?'单表':'主子表'}" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}" />
						</p:column>
					</p:row>
				</p:panelGrid>

			</p:panel>

			<p:outputPanel id="formDetail" style="width:100%;">
				<p:fieldset toggleable="true" legend="数据结构" rendered="#{tdDynaFormStructureBean.formModel == '1'}" style="margin-top:5px;">
					<table>
						<tr style="height: 30px;">
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText value="表中文名：" /></td>
							<td style="text-align: left;padding-left: 3px;width: 150px;">
								<p:inputText value="#{tdDynaFormStructureBean.mTdFormTable.cnName}" 
									maxlength="50"  rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}"/> 
								
								<p:inputText value="#{tdDynaFormStructureBean.mTdFormTable.cnName}"
								   maxlength="50" id="MTabCn" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}"
								   onblur="document.getElementById('tabView:editForm:pym1').click();" /> 
									
								<p:commandLink style="display:none;" id="pym1" action="#{tdDynaFormStructureBean.genAutoPy}" 
									oncomplete="clearNoABC123_(document.getElementById('tabView:editForm:MTName'));"
									process="@this,MTabCn,MTName" update="MTName">
									<f:setPropertyActionListener value="MT" target="#{tdDynaFormStructureBean.pyMode}" />
								</p:commandLink>
							</td>

							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText value="表名：" /></td>
							<td style="text-align: left;padding-left: 3px;">
								<p:outputLabel value="TZ_ " rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}" /> 
								<p:inputText onkeyup="clearNoABC123_(this)" id="MTName" onblur="clearNoABC123_(this)" 
									rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}"
									value="#{tdDynaFormStructureBean.mTdFormTable.enName}" maxlength="10" />
								
								<p:outputLabel value="TZ_#{tdDynaFormStructureBean.mTdFormTable.enName}"
									 rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}" /></td>
						</tr>
					</table>
				</p:fieldset>

				<p:dataTable style="margin-top:5px;" value="#{tdDynaFormStructureBean.mTdFormTable.fieldList}"
					rendered="#{tdDynaFormStructureBean.formModel == '1'}" rowIndexVar="R" id="fieldList1" var="field" scrollable="true" emptyMessage="没有记录！" scrollWidth="100%">
					<f:facet name="header">
						<p:outputLabel value="字段信息" style="text-align: left;" />
					</f:facet>
					<p:column headerText="操作" style="text-align: center;width: 80px;">
						<p:commandLink value="删除"  rendered="#{field.rid==null}" action="#{tdDynaFormStructureBean.removeFieldSub}" update="fieldList1" process="@this">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="1" target="#{tdDynaFormStructureBean.opType}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" action="#{tdDynaFormStructureBean.modFieldSub}" update=":tabView" process="@this,:tabView:editForm:editPanelInfo">
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="1" target="#{tdDynaFormStructureBean.opType}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段说明" style="text-align: center;width: 150px;">
						<p:inputText value="#{field.fdCnname}" rendered="#{field.rid==null}"  id="fdCnname" maxlength="50" size="20" onblur="document.getElementById('tabView:editForm:fieldList1:#{R}:data1').click();">
						</p:inputText>
						<p:inputText value="#{field.fdCnname}" rendered="#{field.rid!=null}"  maxlength="50" size="20"  >
						</p:inputText>

						<p:commandLink style="display:none;" id="data1" action="#{tdDynaFormStructureBean.genAutoPy}"
							oncomplete="clearNoABC123_(document.getElementById('tabView:editForm:fieldList1:#{R}:fdEnname'));" process="@this,fdCnname,fdEnname" update="fdEnname">
							<f:setPropertyActionListener value="field" target="#{tdDynaFormStructureBean.pyMode}" />
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段名" style="text-align: center;width: 100px;">
						<p:inputText value="#{field.fdEnname}" rendered="#{field.rid==null}"  id="fdEnname" onkeyup="clearNoABC123_(this)" onblur="clearNoABC123_(this)" maxlength="10" size="10" />
						
						<p:outputLabel rendered="#{field.rid!=null}" value="#{field.fdEnname}" />
					</p:column>
					<p:column headerText="字段类型" style="width: 120px;">
						<p:selectOneMenu value="#{field.fdDbtype}" rendered="#{field.rid==null}" >
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{tdDynaFormStructureBean.fieldTypeList}" />
							<p:ajax event="change" process="@this,lenCharsss" update="lenCharsss" />
						</p:selectOneMenu>
						
						<p:outputLabel  value="#{field.fdDbtypeStr}" rendered="#{field.rid!=null}"   />
					</p:column>
					<p:column headerText="精度" style="padding-left:8px;width: 100px;">
						<p:outputPanel id="lenCharsss">
							<p:inputText value="#{field.lenChar}" onkeyup="SYSTEM.clearNoNum(this)" rendered="#{field.fdDbtype== 'NVARCHAR2'}" onblur="SYSTEM.clearNoNum(this)" maxlength="3" size="3" />
							<p:outputLabel value="整数：" rendered="#{field.fdDbtype=='NUMBER' }" />
							<p:inputText value="#{field.lenInt}" rendered="#{field.fdDbtype=='INTEGER' or field.fdDbtype=='NUMBER' }" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"
								maxlength="3" size="3" /> <br/>
							<p:outputLabel value=" 小数：" rendered="#{field.fdDbtype== 'NUMBER'}" />
							<p:inputText value="#{field.lenDemi}" onkeyup="SYSTEM.clearNoNum(this)" rendered="#{field.fdDbtype=='NUMBER' }" onblur="SYSTEM.clearNoNum(this)" maxlength="3" size="3" />
						</p:outputPanel>
					</p:column>
					<p:column headerText="列表显示" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.listBol}" />
					</p:column>
					<p:column headerText="查询" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.ifSearch}" >
							<p:ajax event="change" process="@this" update="searchType"/>
						</p:selectBooleanCheckbox>
					</p:column>
					<p:column headerText="匹配方式" style="width: 90px;">
						<p:selectOneMenu value="#{field.searchType}" disabled="#{!field.ifSearch}" id="searchType">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdDynaFormStructureBean.searchTypeList}" />
						</p:selectOneMenu>	
					</p:column>
					<p:column headerText="显示类型" style="width: 85px;">
						<p:selectOneMenu value="#{field.isShow}">
							<f:selectItem itemValue="0" itemLabel="不显示" />
							<f:selectItem itemValue="1" itemLabel="正常显示" />
							<f:selectItem itemValue="2" itemLabel="只读显示" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="是否必输" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.reqBol}" />
					</p:column>
					<p:column headerText="编辑时行号" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.rowNum}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="编辑时列号" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.colNum}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="所占列数" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.colSpan}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="展示类型" style="width: 100px;">
						<p:selectOneMenu value="#{field.dataSrc}">
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{tdDynaFormStructureBean.fieldShowList}" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="是否流程变量" style="text-align: center;width: 90px;">
						<p:selectBooleanCheckbox value="#{field.proValBol}" />
					</p:column>
				</p:dataTable>

				<p:fieldset toggleable="true" legend="主表数据结构" rendered="#{tdDynaFormStructureBean.formModel == '2'}" style="margin-top:5px;">
					<table>
						<tr style="height: 30px;">
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText value="表中文名：" /></td>
							<td style="text-align: left;padding-left: 3px;width: 150px;">
								<p:inputText value="#{tdDynaFormStructureBean.mTdFormTable.cnName}" 
									maxlength="50"  rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}"/> 
								
								<p:inputText value="#{tdDynaFormStructureBean.mTdFormTable.cnName}"
								   maxlength="50" id="mainTabCn" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}"
								   onblur="document.getElementById('tabView:editForm:pym2').click();" /> 
									
								<p:commandLink style="display:none;" id="pym2" action="#{tdDynaFormStructureBean.genAutoPy}" 
									oncomplete="clearNoABC123_(document.getElementById('tabView:editForm:mainTableName'));"
									process="@this,mainTabCn,mainTableName" update="mainTableName">
									<f:setPropertyActionListener value="mainTB" target="#{tdDynaFormStructureBean.pyMode}" />
								</p:commandLink>
							</td>
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText value="表名：" /></td>
							<td style="text-align: left;padding-left: 3px;"><p:outputLabel value="TZ_ " rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}" /> <p:inputText
									id="mainTableName" onkeyup="clearNoABC123_(this)" onblur="clearNoABC123_(this)" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}"
									value="#{tdDynaFormStructureBean.mTdFormTable.enName}" maxlength="10" /> <p:outputLabel value="TZ_#{tdDynaFormStructureBean.mTdFormTable.enName}"
									rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}" /></td>
						</tr>
					</table>
				</p:fieldset>

				<p:dataTable style="margin-top:5px;" value="#{tdDynaFormStructureBean.mTdFormTable.fieldList}" rendered="#{tdDynaFormStructureBean.formModel == '2'}" id="fieldList2"
					var="field" scrollable="true" emptyMessage="没有记录！"  rowIndexVar="R"  scrollWidth="100%">
					<f:facet name="header">
						<p:outputLabel value="字段信息" style="text-align: left;" />
					</f:facet>
					<p:column headerText="操作" style="text-align: center;width: 60px;">
						<p:commandLink value="删除" rendered="#{field.rid==null}"  action="#{tdDynaFormStructureBean.removeFieldSub}" update="fieldList2" process="@this">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="2" target="#{tdDynaFormStructureBean.opType}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" action="#{tdDynaFormStructureBean.modFieldSub}" update=":tabView" process="@this,:tabView:editForm:editPanelInfo">
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="1" target="#{tdDynaFormStructureBean.opType}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段说明" style="width: 150px;">
						<p:inputText value="#{field.fdCnname}" id="fdCnname" maxlength="50" rendered="#{field.rid==null}"
							size="20" onblur="document.getElementById('tabView:editForm:fieldList2:#{R}:data1').click();">
						</p:inputText>
						
						<p:inputText value="#{field.fdCnname}"  rendered="#{field.rid !=  null}" ></p:inputText>
						
						<p:commandLink style="display:none;" id="data1" action="#{tdDynaFormStructureBean.genAutoPy}"
							oncomplete="clearNoABC123_(document.getElementById('tabView:editForm:fieldList2:#{R}:fdEnname'));" process="@this,fdCnname,fdEnname" update="fdEnname">
							<f:setPropertyActionListener value="field" target="#{tdDynaFormStructureBean.pyMode}" />
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段名" style="width: 100px;">
						<p:inputText value="#{field.fdEnname}" id="fdEnname" rendered="#{field.rid ==  null}"  onkeyup="clearNoABC123_(this)" onblur="clearNoABC123_(this)" maxlength="10" size="10" />
						<p:outputLabel value="#{field.fdEnname}" rendered="#{field.rid !=  null}"   />
					</p:column>
					<p:column headerText="字段类型" style="text-align: center;width: 120px;">
						<p:selectOneMenu value="#{field.fdDbtype}" style="text-align:left" rendered="#{field.rid ==  null}" >
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{tdDynaFormStructureBean.fieldTypeList}" />
							<p:ajax event="change" process="@this,lenCharsss" update="lenCharsss" />
						</p:selectOneMenu>
						
						<p:outputLabel value="#{field.fdDbtypeStr}" rendered="#{field.rid !=  null}"   />
					</p:column>
					<p:column headerText="精度" style="padding-left:8px;width: 100px;">
						<p:outputPanel id="lenCharsss">
							<p:inputText value="#{field.lenChar}" onkeyup="SYSTEM.clearNoNum(this)" rendered="#{field.fdDbtype== 'NVARCHAR2'}" onblur="SYSTEM.clearNoNum(this)" maxlength="3" size="3" />
							<p:outputLabel value="整数：" rendered="#{field.fdDbtype=='NUMBER' }" />
							<p:inputText value="#{field.lenInt}" rendered="#{field.fdDbtype=='INTEGER' or field.fdDbtype=='NUMBER' }" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"
								maxlength="3" size="3" /><br/>
							<p:outputLabel value=" 小数：" rendered="#{field.fdDbtype== 'NUMBER'}" />
							<p:inputText value="#{field.lenDemi}" onkeyup="SYSTEM.clearNoNum(this)" rendered="#{field.fdDbtype=='NUMBER' }" onblur="SYSTEM.clearNoNum(this)" maxlength="3" size="3" />
						</p:outputPanel>
					</p:column>
					<p:column headerText="列表显示" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.listBol}" />
					</p:column>
					<p:column headerText="查询" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.ifSearch}" >
							<p:ajax event="change" process="@this" update="searchType"/>
						</p:selectBooleanCheckbox>
					</p:column>
					<p:column headerText="匹配方式" style="width: 90px;">
						<p:selectOneMenu value="#{field.searchType}"  disabled="#{!field.ifSearch}" id="searchType">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdDynaFormStructureBean.searchTypeList}" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="显示类型" style="width: 85px;">
						<p:selectOneMenu value="#{field.isShow}" >
							<f:selectItem itemValue="0" itemLabel="不显示" />
							<f:selectItem itemValue="1" itemLabel="正常显示" />
							<f:selectItem itemValue="2" itemLabel="只读显示" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="是否必输" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.reqBol}" />
					</p:column>
					<p:column headerText="编辑时行号" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.rowNum}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="编辑时列号" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.colNum}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="所占列数" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.colSpan}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="展示类型" style="width: 100px;">
						<p:selectOneMenu value="#{field.dataSrc}">
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{tdDynaFormStructureBean.fieldShowList}" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="是否流程变量" style="text-align: center;width: 90px;">
						<p:selectBooleanCheckbox value="#{field.proValBol}" />
					</p:column>
				</p:dataTable>


				<p:fieldset toggleable="true" legend="子表数据结构" rendered="#{tdDynaFormStructureBean.formModel == '2'}" style="margin-top:5px;">
					<table>
						<tr style="height: 30px;">
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText value="表中文名：" /></td>
							<td style="text-align: left;padding-left: 3px;width: 150px;">
								<p:inputText value="#{tdDynaFormStructureBean.sTdFormTable.cnName}" 
									maxlength="50"  rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}"/> 
								
								<p:inputText value="#{tdDynaFormStructureBean.sTdFormTable.cnName}"
								   maxlength="50" id="subTabCn" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}"
								   onblur="document.getElementById('tabView:editForm:pym3').click();" /> 
									
								<p:commandLink style="display:none;" id="pym3" action="#{tdDynaFormStructureBean.genAutoPy}" 
									oncomplete="clearNoABC123_(document.getElementById('tabView:editForm:subTableName'));"
									process="@this,subTabCn,subTableName" update="subTableName">
									<f:setPropertyActionListener value="subTB" target="#{tdDynaFormStructureBean.pyMode}" />
								</p:commandLink>
							</td>
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText value="表名：" /></td>
							<td style="text-align: left;padding-left: 3px;"><p:outputLabel value="TZ_ " rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}" /> <p:inputText
									onkeyup="clearNoABC123_(this)" id="subTableName" onblur="clearNoABC123_(this)" rendered="#{tdDynaFormStructureBean.mTdFormTable.rid == null}"
									value="#{tdDynaFormStructureBean.sTdFormTable.enName}" maxlength="10" /> <p:outputLabel value="TZ_#{tdDynaFormStructureBean.sTdFormTable.enName}"
									rendered="#{tdDynaFormStructureBean.mTdFormTable.rid != null}" /></td>
						</tr>
					</table>
				</p:fieldset>

				<p:dataTable style="margin-top:5px;" value="#{tdDynaFormStructureBean.sTdFormTable.fieldList}" rendered="#{tdDynaFormStructureBean.formModel == '2'}" id="fieldList3"
					var="field" scrollable="true" emptyMessage="没有记录！" rowIndexVar="R" scrollWidth="100%">
					<f:facet name="header">
						<p:outputLabel value="字段信息" style="text-align: left;" />
					</f:facet>
					<p:column headerText="操作" style="text-align: center;width: 60px;">
						<p:commandLink value="删除" rendered="#{field.rid==null}"  action="#{tdDynaFormStructureBean.removeFieldSub}" update="fieldList3" process="@this">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="3" target="#{tdDynaFormStructureBean.opType}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" action="#{tdDynaFormStructureBean.modFieldSub}" update=":tabView" process="@this,:tabView:editForm:editPanelInfo">
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="3" target="#{tdDynaFormStructureBean.opType}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段说明" style="text-align: center;width: 150px;">
						<p:inputText value="#{field.fdCnname}" id="fdCnname" rendered="#{field.rid==null}"
						 maxlength="50" size="20" onblur="document.getElementById('tabView:editForm:fieldList3:#{R}:data1').click();">
						</p:inputText>
						<p:inputText value="#{field.fdCnname}" rendered="#{field.rid!=null}" >
						</p:inputText>

						<p:commandLink style="display:none;" id="data1" action="#{tdDynaFormStructureBean.genAutoPy}"
							oncomplete="clearNoABC123_(document.getElementById('tabView:editForm:fieldList3:#{R}:fdEnname'));" process="@this,fdCnname,fdEnname" update="fdEnname">
							<f:setPropertyActionListener value="#{field}" target="#{tdDynaFormStructureBean.selTdFormField}" />
							<f:setPropertyActionListener value="field" target="#{tdDynaFormStructureBean.pyMode}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段名" style="text-align: center;width: 100px;">
						<p:inputText value="#{field.fdEnname}" id="fdEnname" rendered="#{field.rid==null}"
						 onkeyup="clearNoABC123_(this)" onblur="clearNoABC123_(this)" maxlength="10" size="10" />
						
						<p:outputLabel value="#{field.fdEnname}" rendered="#{field.rid!=null}"   />
					</p:column>
					<p:column headerText="字段类型" style="text-align: center;width: 120px;">
						<p:selectOneMenu value="#{field.fdDbtype}" style="text-align:left" rendered="#{field.rid==null}">
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{tdDynaFormStructureBean.fieldTypeList}" />
							<p:ajax event="change" process="@this,lenCharsss" update="lenCharsss" />
						</p:selectOneMenu>
						
						<p:outputLabel value="#{field.fdDbtypeStr}" rendered="#{field.rid!=null}"   />
					</p:column>
					<p:column headerText="精度" style="padding-left:8px;width: 100px;">
						<p:outputPanel id="lenCharsss">
							<p:inputText value="#{field.lenChar}" onkeyup="SYSTEM.clearNoNum(this)" rendered="#{field.fdDbtype== 'NVARCHAR2'}" onblur="SYSTEM.clearNoNum(this)" maxlength="3" size="3" />
							<p:outputLabel value="整数：" rendered="#{field.fdDbtype=='NUMBER' }" />
							<p:inputText value="#{field.lenInt}" rendered="#{field.fdDbtype=='INTEGER' or field.fdDbtype=='NUMBER' }" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"
								maxlength="3" size="3" /><br/>
							<p:outputLabel value=" 小数：" rendered="#{field.fdDbtype== 'NUMBER'}" />
							<p:inputText value="#{field.lenDemi}" onkeyup="SYSTEM.clearNoNum(this)" rendered="#{field.fdDbtype=='NUMBER' }" onblur="SYSTEM.clearNoNum(this)" maxlength="3" size="3" />
						</p:outputPanel>
					</p:column>
					<p:column headerText="列表显示" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.listBol}" />
					</p:column>
					
					<p:column headerText="查询" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.ifSearch}" >
							<p:ajax event="change" process="@this" update="searchType"/>
						</p:selectBooleanCheckbox>
					</p:column>
					<p:column headerText="匹配方式" style="width: 90px;">
						<p:selectOneMenu value="#{field.searchType}" id="searchType" disabled="#{!field.ifSearch}">
							<f:selectItem itemLabel="--请选择--" itemValue=""/>
							<f:selectItems value="#{tdDynaFormStructureBean.searchTypeList}" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="显示类型" style="width: 85px;">
						<p:selectOneMenu value="#{field.isShow}">
							<f:selectItem itemValue="0" itemLabel="不显示" />
							<f:selectItem itemValue="1" itemLabel="正常显示" />
							<f:selectItem itemValue="2" itemLabel="只读显示" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="是否必输" style="text-align: center;width: 80px;">
						<p:selectBooleanCheckbox value="#{field.reqBol}" />
					</p:column>
					<p:column headerText="编辑时行号" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.rowNum}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="编辑时列号" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.colNum}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="所占列数" style="text-align: center;width: 80px;">
						<p:inputText value="#{field.colSpan}" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" maxlength="2" size="2" />
					</p:column>
					<p:column headerText="展示类型" style="width: 100px;">
						<p:selectOneMenu value="#{field.dataSrc}">
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{tdDynaFormStructureBean.fieldShowList}" />
						</p:selectOneMenu>
					</p:column>
					<p:column headerText="是否流程变量" style="text-align: center;width: 90px;">
						<p:selectBooleanCheckbox value="#{field.proValBol}" />
					</p:column>
				</p:dataTable>
			</p:outputPanel>

		</p:outputPanel>



		<p:dialog id="singleSelDialog" widgetVar="SingleSelDialog" header="表单类型选择" resizable="false" width="750" height="400" modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="添加" icon="ui-icon-plus" process="@this" action="#{tdDynaFormStructureBean.addSingleSel}" update="singleAddDialog"
						oncomplete="PF('SingleAddDialog').show()" />
					<p:commandButton value="返回" icon="ui-icon-close" process="@this" oncomplete="PF('SingleSelDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid columns="3">
							<p:outputLabel value="名　称：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{tdDynaFormStructureBean.formTypeDiag}" style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this" update=":tabView:editForm:singleDatatable" listener="#{tdDynaFormStructureBean.filterSingelSel}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{tdDynaFormStructureBean.showTypeList}" var="typ" emptyMessage="暂无记录" paginator="true" rows="10" paginatorPosition="bottom" id="singleDatatable">
				<p:column headerText="操作" style="width:120px;text-align:center;">
					<p:commandLink value="选择" process="@this" action="#{tdDynaFormStructureBean.selSingleResult}" update=":tabView:editForm:tdFormTypeName" oncomplete="PF('SingleSelDialog').hide()">
						<f:setPropertyActionListener target="#{tdDynaFormStructureBean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="修改" process="@this" update=":tabView:editForm:singleAddDialog" oncomplete="PF('SingleAddDialog').show()">
						<f:setPropertyActionListener target="#{tdDynaFormStructureBean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="删除" process="@this" action="#{tdDynaFormStructureBean.deleteSingleAction}" update="singleDatatable">
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{tdDynaFormStructureBean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="类型编码" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ.typeCode}" />
				</p:column>
				<p:column headerText="类型名称" style="padding-left: 3px;">
					<h:outputLabel value="#{typ.typeName}" />
				</p:column>

			</p:dataTable>
		</p:dialog>
		<p:dialog id="singleAddDialog" widgetVar="SingleAddDialog" header="表单类型维护" resizable="false" width="500" modal="true">
			<p:panelGrid style="width:100%;" id="singleAddGrid">
				<p:row>
					<p:column style="width:30%;height: 25px;text-align: right;padding-right: 3px;">
						<p:outputLabel value="*" style="color: red" />
						<p:outputLabel value="类型编码：" />
					</p:column>
					<p:column style="padding-left: 3px;">
						<p:inputText value="#{tdDynaFormStructureBean.bindTdFormType.typeCode}" style="width: 160px;" maxlength="25" required="true" requiredMessage="类型编码不允许为空！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="height: 25px;text-align: right;padding-right: 3px;">
						<p:outputLabel value="*" style="color: red" />
						<p:outputLabel value="类型名称：" />
					</p:column>
					<p:column style="padding-left: 3px;">
						<p:inputText value="#{tdDynaFormStructureBean.bindTdFormType.typeName}" style="width: 160px;" maxlength="50" required="true" requiredMessage="类型名称不允许为空！" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" action="#{tdDynaFormStructureBean.saveSingleAction}" process="@this,singleAddGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" onclick="PF('SingleAddDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>



		<p:dialog id="rptDialog" widgetVar="RptDialog" header="报表模版选择" resizable="false" width="780" modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="关闭" icon="ui-icon-close" process="@this" oncomplete="PF('RptDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid id="searchRptDiag" columns="6">
							<p:outputLabel value="系统类型：" styleClass="zwx_dialog_font" />
							<p:selectOneMenu id="msgType" style="width:200px" value="#{tdDynaFormStructureBean.sysType}">
								<f:selectItem itemLabel="--全部--" itemValue="" />
								<f:selectItems value="#{tdDynaFormStructureBean.sysTypeMap}" />
								<p:ajax event="change" process="@this,searchRptDiag" update=":tabView:editForm:rptDatatable" listener="#{tdDynaFormStructureBean.filterRptList}" />
							</p:selectOneMenu>
							<p:spacer width="5" />
							<p:outputLabel value="名　称：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{tdDynaFormStructureBean.searchRptName}" style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this,searchRptDiag" update=":tabView:editForm:rptDatatable" listener="#{tdDynaFormStructureBean.filterRptList}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{tdDynaFormStructureBean.showRptList}" var="typ" emptyMessage="暂无记录" paginator="true" rows="10" paginatorPosition="bottom" id="rptDatatable">
				<p:column headerText="操作" style="width:60px;text-align:center;">
					<p:commandLink value="选择" process="@this" update=":tabView:editForm:prtTplCode" oncomplete="PF('RptDialog').hide()">
						<f:setPropertyActionListener target="#{tdDynaFormStructureBean.selRpt}" value="#{typ}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="报表编号" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ.rptCod}" />
				</p:column>
				<p:column headerText="报表名称" style="padding-left: 3px;">
					<h:outputLabel value="#{typ.rptnam}" />
				</p:column>

			</p:dataTable>
		</p:dialog>
	</ui:define>
</ui:composition>











