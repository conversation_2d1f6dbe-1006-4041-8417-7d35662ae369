<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <ui:param name="onfocus" value="false" />
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            ul li {
                list-style-type: none;
                margin-left: 16px;
            }
        </style>
    </ui:define>
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{systemHolidayConfigBean}"/>
    <!-- 编辑页面 -->
    <h:outputStylesheet name="css/default.css"/>
    <h:outputScript library="js" name="namespace.js"/>
    <h:outputScript name="js/validate/system/validate.js"/>
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="节假日配置"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{systemHolidayConfigBean.addInitAction}"
                                 update=":tabView:mainForm:holidayComfigDialog" process="@this,mainGrid">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 35px;">
                <h:outputText value="日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;width: 260px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchHolidaySDate}"
                                              endDate="#{mgrbean.searchHolidayEDate}" ifNotMaxDate="true"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="日期类型：" />
            </p:column>
            <p:column  style="text-align:left;padding-left:12px;width: 260px;">
                <p:selectManyCheckbox value="#{mgrbean.searchHolidayType}" id="searchHolidayType" >
                    <f:selectItem itemLabel="节假日" itemValue="1"/>
                    <f:selectItem itemLabel="补班" itemValue="2" />
                </p:selectManyCheckbox>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column>
                <p:selectManyCheckbox value="#{mgrbean.searchHolidayState}" id="searchHolidayState" >
                    <f:selectItem itemLabel="停用" itemValue="0"/>
                    <f:selectItem itemLabel="启用" itemValue="1" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 内容-->
    <ui:define name="insertDataTable">
        <p:column headerText="序号" style="width: 80px;text-align: center;">
            <h:outputText value="#{R+1}" />
        </p:column>
        <p:column headerText="日期类型" style="padding-left: 3px;width: 120px;text-align: center;">
            <h:outputText value="节假日" rendered="#{itm[1]==1}"/>
            <h:outputText value="补班" rendered="#{itm[1]==2}"/>
        </p:column>
        <p:column headerText="日期段" style="width: 200px;text-align: center;">
            <h:outputText value="#{itm[2]}" >
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8" locale="cn"></f:convertDateTime>
            </h:outputText>~
            <h:outputText value="#{itm[3]}" >
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8" locale="cn"></f:convertDateTime>
            </h:outputText>
        </p:column>
        <p:column headerText="说明" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="状态" style="width: 120px;text-align: center;">
            <h:outputText value="停用" rendered="#{itm[5]==0}"/>
            <h:outputText value="启用" rendered="#{itm[5]==1}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;">
            <p:commandLink value="修改" action="#{systemHolidayConfigBean.modInit}" update=":tabView:mainForm:holidayComfigDialog" process="@this">
                <f:setPropertyActionListener target="#{systemHolidayConfigBean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog header="节假日配置" widgetVar="HolidayComfigDialog" id="holidayComfigDialog"
                  resizable="false" modal="true"  width="500">
            <p:panelGrid style="width:100%" id="holidayGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:120px;height: 35px;" >
                        <font color="red">*</font>
                        <p:outputLabel value="日期类型："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneRadio value="#{systemHolidayConfigBean.tsSysHoliday.holiType}" style="width: 120px;">
                            <f:selectItem itemLabel="节假日" itemValue="1"/>
                            <f:selectItem itemLabel="补班" itemValue="2"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:120px;height: 35px;">
                        <font color="red">*</font>
                        <p:outputLabel value="配置时间段："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">

                        <zwx:CalendarDynamicLimitComp startDate="#{systemHolidayConfigBean.tsSysHoliday.startDate}"
                                                      endDate="#{systemHolidayConfigBean.tsSysHoliday.endDate}" ifNotMaxDate="true"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:120px;height: 35px;">
                        <font color="red">*</font>
                        <p:outputLabel value="说明："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText value="#{systemHolidayConfigBean.tsSysHoliday.holiDesc}" maxlength="25" style="width: 186px;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:120px;height: 35px;">
                        <font color="red">*</font>
                        <p:outputLabel value="状态："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneRadio value="#{systemHolidayConfigBean.tsSysHoliday.state}" style="width: 120px;">
                            <f:selectItem itemLabel="启用" itemValue="1"/>
                            <f:selectItem itemLabel="停用" itemValue="0"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:center;" colspan="2">
                        <p:commandButton value="保存" update="dataTable" process="@this,holidayGrid" icon="ui-icon-check"
                                         action="#{systemHolidayConfigBean.saveAction}"/>
                        &#160;&#160;
                        <p:commandButton value="取消" icon="ui-icon-close" oncomplete="PF('HolidayComfigDialog').hide()"/>
                    </p:column>
                </p:row>

            </p:panelGrid>
        </p:dialog>
    </ui:define>
</ui:composition>
