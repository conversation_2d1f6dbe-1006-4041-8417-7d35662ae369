<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{unitBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/unitEdit.xhtml"/>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="单位管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{unitBean.searchAction}" update="dataTable"
					process="@this,searchUnitname,searchSortId,searchState,searchZone" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{unitBean.addInitAction}" update=":tabView" process="@this">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;height: 35px;">
                <h:outputText value="地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;width: 450px;">
                <zwx:ZoneSingleNewComp zoneList="#{unitBean.zoneList}" zoneCodeNew="#{unitBean.searchZoneCode}" zoneName="#{unitBean.searchZoneName}" id="searchZone"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchUnitname" value="#{unitBean.searchUnitname}" maxlength="15"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 35px;">
                <h:outputText value="单位属性：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectOneMenu id="searchSortId" value="#{unitBean.searchSortId}" style="width: 180px;">
                    <f:selectItem itemLabel="--全部--"/>
                    <f:selectItems value="#{unitBean.bsSortMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectOneRadio id="searchState" value="#{unitBean.searchState}" style="width: 180px;">
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width: 100px;padding-left: 10px;">
            <h:outputText value="#{itm.tsZone.zoneType gt unitBean.currentMaxZoneType?'　':''}#{itm.tsZone.zoneName}" />
        </p:column>
        <p:column headerText="单位" style="padding-left: 3px;">
            <h:outputText value="#{itm.unitname}" />
        </p:column>
        <p:column headerText="单位编码" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm.unitCode}" />
        </p:column>
        <p:column headerText="注册码" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm.regCode}" />
        </p:column>        
        <p:column headerText="单位属性" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm.unitSortNames}" />
        </p:column>
        <p:column headerText="操作" style="width: 250px;padding-left: 3px;">
            <p:commandLink value="修改" action="#{unitBean.modInitAction}" update=":tabView" process="@this">
                <f:setPropertyActionListener target="#{unitBean.rid}" value="#{itm.rid}"/>
                <p:resetInput target=":tabView:editForm:editGrid"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="删除" action="#{unitBean.deleteAction}" update="dataTable" process="@this">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{unitBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="停用" action="#{unitBean.stopAction}" update="dataTable" process="@this" rendered="#{itm.ifReveal=='1'}">
                <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{unitBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
            <p:commandLink value="启用" action="#{unitBean.startAction}" update="dataTable" process="@this" rendered="#{itm.ifReveal!='1'}">
                <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{unitBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
            <p:spacer width="5" />
        </p:column>
    </ui:define>

</ui:composition>











