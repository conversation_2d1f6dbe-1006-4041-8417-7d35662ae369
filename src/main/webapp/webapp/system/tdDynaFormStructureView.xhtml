<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	template="/WEB-INF/templates/system/viewTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdDynaFormStructureBean}" />
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />

	<!-- 样式或javascripts -->
	<ui:define name="insertEditScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
		<style type="text/css">
body {
	overflow-x: hidden;
}
</style>

	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="动态表单数据结构维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;" id="btnPanel">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{tdDynaFormStructureBean.backAction}" update=":tabView"
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
	</ui:define>

	<!-- 其余内容 -->
	<ui:define name="insertOtherContents">
		<p:outputPanel id="editPanelInfo">
			<p:panel header="基本信息" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="表模式：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:outputLabel
								value="#{tdDynaFormStructureBean.formModel==1?'单表':'主子表'}" />
						</p:column>
					</p:row>
				</p:panelGrid>

			</p:panel>

			<p:outputPanel id="formDetail" style="width:100%;">
				<p:fieldset toggleable="true" legend="数据结构"
					rendered="#{tdDynaFormStructureBean.formModel == '1'}"
					style="margin-top:5px;">
					<table>
						<tr style="height: 30px;">
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText
									value="表中文名：" /></td>
							<td style="text-align: left;padding-left: 3px;width: 150px;">
								<p:outputLabel
									value="#{tdDynaFormStructureBean.mTdFormTable.cnName}" />
							</td>
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText
									value="表名：" /></td>
							<td style="text-align: left;padding-left: 3px;"><p:outputLabel
									value="TZ_#{tdDynaFormStructureBean.mTdFormTable.enName}" />
							</td>
						</tr>
					</table>
				</p:fieldset>

				<p:dataTable style="margin-top:5px;"
					value="#{tdDynaFormStructureBean.mTdFormTable.fieldList}"
					rendered="#{tdDynaFormStructureBean.formModel == '1'}"
					rowIndexVar="R" id="fieldList1" var="field" scrollable="true"
					emptyMessage="没有记录！" scrollWidth="100%">
					<f:facet name="header">
						<p:outputLabel value="字段信息" style="text-align: left;" />
					</f:facet>
					<p:column headerText="操作" style="text-align: center;width: 80px;">
						<p:commandLink value="详情"
							action="#{tdDynaFormStructureBean.viewFieldSub}" update=":tabView"
							process="@this">
							<f:setPropertyActionListener value="#{field}"
								target="#{tdDynaFormStructureBean.selTdFormField}" />
						</p:commandLink>
					</p:column>
					<p:column headerText="字段说明"
						style="text-align: center;width: 180px;">
						<p:outputLabel value="#{field.fdCnname}" />
					</p:column>
					<p:column headerText="字段名" style="text-align: center;width: 100px;">
						<p:outputLabel value="#{field.fdEnname}" />
					</p:column>
					<p:column headerText="字段类型"
						style="text-align: center;width: 120px;">
						<p:outputLabel value="#{field.fdDbtypeStr}" />
					</p:column>
					<p:column headerText="精度" style="text-align: center;width: 180px;">
						<p:outputPanel id="lenCharsss">
							<p:outputLabel value="#{field.lenChar}"
								rendered="#{field.fdDbtype== 'NVARCHAR2'}" />
							<p:outputLabel value="整数："
								rendered="#{field.fdDbtype=='NUMBER' }" />
							<p:outputLabel value="#{field.lenInt}"
								rendered="#{field.fdDbtype=='INTEGER' or field.fdDbtype=='NUMBER' }" />
							<p:outputLabel value=" 小数："
								rendered="#{field.fdDbtype== 'NUMBER'}" />
							<p:outputLabel value="#{field.lenDemi}"
								rendered="#{field.fdDbtype=='NUMBER' }" />
						</p:outputPanel>
					</p:column>
					<p:column headerText="是否列表显示"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.listBol?'是':'否'}" />
					</p:column>
					<p:column headerText="编辑时显示类型"
						style="text-align: center;width: 80px;">
						<p:outputLabel
							value="#{field.isShow=='0'?'不显示':(field.isShow=='1'?'正常显示':(field.isShow=='2'?'只读显示':''))}" />
					</p:column>
					<p:column headerText="是否必输" style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.reqBol?'是':'否'}" />
					</p:column>
					<p:column headerText="编辑时行号"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.rowNum}" />
					</p:column>
					<p:column headerText="编辑时列号"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.colNum}" />
					</p:column>
					<p:column headerText="所占列数" style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.colSpan}" />
					</p:column>
					<p:column headerText="展示类型"
						style="text-align: center;width: 120px;">
						<p:outputLabel value="#{field.dataSrcStr}" />
					</p:column>
					<p:column headerText="是否流程变量"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.proValBol?'是':'否'}" />
					</p:column>
				</p:dataTable>



				<p:fieldset toggleable="true" legend="主表数据结构"
					rendered="#{tdDynaFormStructureBean.formModel == '2'}"
					style="margin-top:5px;">
					<table>
						<tr style="height: 30px;">
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText
									value="表中文名：" /></td>
							<td style="text-align: left;padding-left: 3px;width: 150px;">
								<p:outputLabel
									value="#{tdDynaFormStructureBean.mTdFormTable.cnName}" />
							</td>
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText
									value="表名：" /></td>
							<td style="text-align: left;padding-left: 3px;"><p:outputLabel
									value="TZ_#{tdDynaFormStructureBean.mTdFormTable.enName}" /></td>
						</tr>
					</table>
				</p:fieldset>

				<p:dataTable style="margin-top:5px;"
					value="#{tdDynaFormStructureBean.mTdFormTable.fieldList}"
					rendered="#{tdDynaFormStructureBean.formModel == '2'}"
					id="fieldList2" var="field" scrollable="true" emptyMessage="没有记录！"
					rowIndexVar="R" scrollWidth="100%">
					<f:facet name="header">
						<p:outputLabel value="字段信息" style="text-align: left;" />
					</f:facet>
					<p:column headerText="操作" style="text-align: center;width: 80px;">
						<p:column headerText="操作" style="text-align: center;width: 60px;">
							<p:commandLink value="详情"
								action="#{tdDynaFormStructureBean.viewFieldSub}" update=":tabView"
								process="@this">
								<f:setPropertyActionListener value="#{field}"
									target="#{tdDynaFormStructureBean.selTdFormField}" />
							</p:commandLink>
						</p:column>
					</p:column>
					<p:column headerText="字段说明"
						style="text-align: center;width: 180px;">
						<p:outputLabel value="#{field.fdCnname}" />
					</p:column>
					<p:column headerText="字段名" style="text-align: center;width: 100px;">
						<p:outputLabel value="#{field.fdEnname}" />
					</p:column>
					<p:column headerText="字段类型"
						style="text-align: center;width: 120px;">
						<p:outputLabel value="#{field.fdDbtypeStr}" />
					</p:column>
					<p:column headerText="精度" style="text-align: center;width: 180px;">
						<p:outputPanel id="lenCharsss">
							<p:outputLabel value="#{field.lenChar}"
								rendered="#{field.fdDbtype== 'NVARCHAR2'}" />
							<p:outputLabel value="整数："
								rendered="#{field.fdDbtype=='NUMBER' }" />
							<p:outputLabel value="#{field.lenInt}"
								rendered="#{field.fdDbtype=='INTEGER' or field.fdDbtype=='NUMBER' }" />
							<p:outputLabel value=" 小数："
								rendered="#{field.fdDbtype== 'NUMBER'}" />
							<p:outputLabel value="#{field.lenDemi}"
								rendered="#{field.fdDbtype=='NUMBER' }" />
						</p:outputPanel>
					</p:column>
					<p:column headerText="是否列表显示"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.listBol?'是':'否'}" />
					</p:column>
					<p:column headerText="编辑时显示类型"
						style="text-align: center;width: 80px;">
						<p:outputLabel
							value="#{field.isShow=='0'?'不显示':(field.isShow=='1'?'正常显示':(field.isShow=='2'?'只读显示':''))}" />
					</p:column>
					<p:column headerText="是否必输" style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.reqBol?'是':'否'}" />
					</p:column>
					<p:column headerText="编辑时行号"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.rowNum}" />
					</p:column>
					<p:column headerText="编辑时列号"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.colNum}" />
					</p:column>
					<p:column headerText="所占列数" style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.colSpan}" />
					</p:column>
					<p:column headerText="展示类型"
						style="text-align: center;width: 120px;">
						<p:outputLabel value="#{field.dataSrcStr}" />
					</p:column>
					<p:column headerText="是否流程变量"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.proValBol?'是':'否'}" />
					</p:column>
				</p:dataTable>


				<p:fieldset toggleable="true" legend="子表数据结构"
					rendered="#{tdDynaFormStructureBean.formModel == '2'}"
					style="margin-top:5px;">
					<table>
						<tr style="height: 30px;">
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText
									value="表中文名：" /></td>
							<td style="text-align: left;padding-left: 3px;width: 150px;">
								<p:outputLabel
									value="#{tdDynaFormStructureBean.sTdFormTable.cnName}" />
							</td>
							<td style="text-align: right;padding-right: 3px;width: 100px;"><h:outputText
									value="表名：" /></td>
							<td style="text-align: left;padding-left: 3px;"><p:outputLabel
									value="TZ_#{tdDynaFormStructureBean.sTdFormTable.enName}" />
							</td>
						</tr>
					</table>
				</p:fieldset>

				<p:dataTable style="margin-top:5px;"
					value="#{tdDynaFormStructureBean.sTdFormTable.fieldList}"
					rendered="#{tdDynaFormStructureBean.formModel == '2'}"
					id="fieldList3" var="field" scrollable="true" emptyMessage="没有记录！"
					rowIndexVar="R" scrollWidth="100%">
					<f:facet name="header">
						<p:outputLabel value="字段信息" style="text-align: left;" />
					</f:facet>
					<p:column headerText="操作" style="text-align: center;width: 80px;">
						<p:column headerText="操作" style="text-align: center;width: 60px;">
							<p:commandLink value="详情"
								action="#{tdDynaFormStructureBean.viewFieldSub}" update=":tabView"
								process="@this">
								<f:setPropertyActionListener value="#{field}"
									target="#{tdDynaFormStructureBean.selTdFormField}" />
							</p:commandLink>
						</p:column>
					</p:column>

					<p:column headerText="字段说明"
						style="text-align: center;width: 180px;">
						<p:outputLabel value="#{field.fdCnname}" />
					</p:column>
					<p:column headerText="字段名" style="text-align: center;width: 100px;">
						<p:outputLabel value="#{field.fdEnname}" />
					</p:column>
					<p:column headerText="字段类型"
						style="text-align: center;width: 120px;">
						<p:outputLabel value="#{field.fdDbtypeStr}" />
					</p:column>
					<p:column headerText="精度" style="text-align: center;width: 180px;">
						<p:outputPanel id="lenCharsss">
							<p:outputLabel value="#{field.lenChar}"
								rendered="#{field.fdDbtype== 'NVARCHAR2'}" />
							<p:outputLabel value="整数："
								rendered="#{field.fdDbtype=='NUMBER' }" />
							<p:outputLabel value="#{field.lenInt}"
								rendered="#{field.fdDbtype=='INTEGER' or field.fdDbtype=='NUMBER' }" />
							<p:outputLabel value=" 小数："
								rendered="#{field.fdDbtype== 'NUMBER'}" />
							<p:outputLabel value="#{field.lenDemi}"
								rendered="#{field.fdDbtype=='NUMBER' }" />
						</p:outputPanel>
					</p:column>
					<p:column headerText="是否列表显示"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.listBol?'是':'否'}" />
					</p:column>
					<p:column headerText="编辑时显示类型"
						style="text-align: center;width: 80px;">
						<p:outputLabel
							value="#{field.isShow=='0'?'不显示':(field.isShow=='1'?'正常显示':(field.isShow=='2'?'只读显示':''))}" />
					</p:column>
					<p:column headerText="是否必输" style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.reqBol?'是':'否'}" />
					</p:column>
					<p:column headerText="编辑时行号"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.rowNum}" />
					</p:column>
					<p:column headerText="编辑时列号"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.colNum}" />
					</p:column>
					<p:column headerText="所占列数" style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.colSpan}" />
					</p:column>
					<p:column headerText="展示类型"
						style="text-align: center;width: 120px;">
						<p:outputLabel value="#{field.dataSrcStr}" />
					</p:column>
					<p:column headerText="是否流程变量"
						style="text-align: center;width: 80px;">
						<p:outputLabel value="#{field.proValBol?'是':'否'}" />
					</p:column>
				</p:dataTable>
			</p:outputPanel>
		</p:outputPanel>

	</ui:define>
</ui:composition>











