<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>码表类型选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <style type="text/css">
            .ui-picklist .ui-picklist-list{
                text-align:left;
                height: 340px;
                width: 400px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
        </style>
    </h:head>

    <h:body style="overflow:hidden;">
        <h:form id="artForm">
            <p:pickList id="unitPickList" value="#{selectCodeTypeBean.dualListModel}" var="unit"
                        itemValue="#{unit}" itemLabel="#{unit.codeTypeDesc}" converter="system.CodeTypeConvert"
                        showSourceControls="false" showTargetControls="false" showCheckbox="true"
                        showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
                        effect="drop">

                <f:facet name="sourceCaption">可选码表</f:facet>
                <f:facet name="targetCaption">已选码表</f:facet>

                <p:column style="width:20%;text-align: left;">#{unit.codeTypeName}</p:column>
                <p:column style="width:30%;text-align: left;">#{unit.systemType.typeCN}</p:column>
                <p:column style="width:50%;text-align: left;">#{unit.codeTypeDesc}</p:column>
            </p:pickList>
            <br/>
            <p:outputPanel style="text-align:center">
                <p:commandButton value="确定" icon="ui-icon-check" id="saveBtn" action="#{selectCodeTypeBean.selectAction}" process="@form" />
                <p:spacer width="5" />
                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectCodeTypeBean.dialogClose}" process="@this"/>
            </p:outputPanel>
        </h:form>
    </h:body>
</f:view>
</html>
