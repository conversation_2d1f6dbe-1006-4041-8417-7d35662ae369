<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<script type="text/javascript">
			//<![CDATA[

			function clearNoABC123_(obj) {
				var reg = /^[A-Za-z0-9_]+$/;
				var val = obj.value;
				var tempVal = "";
				if (val.length > 0) {
					for (var i = 0; i < val.length; i++) {
						var tempStr = val.substr(i, 1);
						if (tempStr.match(reg)) {
							tempVal += tempStr;
						}
					}
					tempVal = tempVal.toUpperCase();
					if (obj.value != tempVal) {
						obj.value = tempVal;
					}
				}
			}

			//]]>
		</script>
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<!-- 托管Bean -->
		<ui:param name="mgrbean" value="#{tdDynaFormDesignBean}" />

		<p:tabView id="tabView" dynamic="true" cache="true"
			activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0px;">
			<p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<h:form id="mainForm">
					<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
						id="titleGrid">
						<f:facet name="header">
							<p:row>
								<p:column colspan="6"
									style="text-align:left;padding-left:5px;height: 20px;">
									<h:outputText value="动态表单维护" />
								</p:column>
							</p:row>
						</f:facet>
					</p:panelGrid>
					<p:outputPanel id="buttonsPanel">
						<p:outputPanel styleClass="zwx_toobar_42">
							<h:panelGrid columns="3"
								style="border-color:transparent;padding:0px;">
								<span class="ui-separator"><span
									class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
									action="#{tdDynaFormDesignBean.searchAction}"
									update="dataTable" process="@this,@form" />
								<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
									action="#{tdDynaFormDesignBean.addInitAction}"
									update=":tabView" process="@this">
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel>
					</p:outputPanel>
					<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
						style="margin-top: 5px;margin-bottom: 5px;"
						rendered="#{condition==null}">
						<p:panelGrid style="width:100%;height:100%;" id="mainGrid">
							<p:row>
								<p:column
									style="text-align:right;padding-right:3px;width:120px;">
									<h:outputText value="表单类别：" />
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 200px;">
									<p:selectOneMenu value="#{tdDynaFormDesignBean.formTypeId}"
										style="width:180px;">
										<f:selectItem itemLabel="--全部--" itemValue="" />
										<f:selectItems value="#{tdDynaFormDesignBean.formTypeList}"
											var="typ" itemLabel="#{typ.typeName}" itemValue="#{typ.rid}" />
									</p:selectOneMenu>
								</p:column>
								<p:column
									style="text-align:right;padding-right:3px;width:120px;">
									<h:outputText value="表单名称：" />
								</p:column>
								<p:column style="text-align:left;padding-left:8px;width: 200px;">
									<p:inputText value="#{tdDynaFormDesignBean.formName}"
										maxlength="20" />
								</p:column>
								<p:column
									style="text-align:right;padding-right:3px;width:120px;">
									<h:outputText value="状态：" />
								</p:column>
								<p:column style="text-align:left;padding-left:8px;">
									<p:selectManyCheckbox
										value="#{tdDynaFormDesignBean.searchState}">
										<f:selectItem itemLabel="待提交" itemValue="0" />
										<f:selectItem itemLabel="已提交" itemValue="1" />
									</p:selectManyCheckbox>
								</p:column>
							</p:row>
						</p:panelGrid>
					</p:fieldset>

					<p:dataTable var="itm" value="#{mgrbean.dataModel}"
						paginator="true" rows="#{mgrbean.pageSize}"
						paginatorPosition="bottom" rowIndexVar="R"
						paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
						rowsPerPageTemplate="#{mgrbean.pageSize}" id="dataTable"
						lazy="true" emptyMessage="没有您要找的记录！" widgetVar="DATA"
						currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
						<p:column headerText="序号" style="text-align: center;width: 60px;">
							<h:outputText value="#{R+1}" />
						</p:column>
						<p:column headerText="表单类型" style="text-align:center;width:150px;">
							<h:outputText value="#{itm[0]}">
							</h:outputText>
						</p:column>
						<p:column headerText="表单编号"
							style="text-align:center;width: 120px;">
							<h:outputText value="#{itm[1]}" />
						</p:column>
						<p:column headerText="表单名称"
							style="padding-left: 3px;width: 200px;">
							<h:outputText value="#{itm[2]}" />
						</p:column>
						<p:column headerText="对应表名"
							style="padding-left: 3px;width: 200px;">
							<h:outputText value="#{itm[5]}" />
						</p:column>
						<p:column headerText="状态" style="text-align: center;width: 80px;">
							<h:outputText value="#{itm[3]==1?'已提交':'待提交'}" />
						</p:column>
						<p:column headerText="操作" style="padding-left: 8px;">
							<p:commandLink value="查看"
								action="#{tdDynaFormDesignBean.viewInitAction}"
								update=":tabView" process="@this">
								<f:setPropertyActionListener
									target="#{tdDynaFormDesignBean.formId}" value="#{itm[4]}" />
							</p:commandLink>
							<p:spacer width="5" />

							<p:commandLink value="修改"
								action="#{tdDynaFormDesignBean.modInitAction}" update=":tabView"
								process="@this">
								<f:setPropertyActionListener
									target="#{tdDynaFormDesignBean.formId}" value="#{itm[4]}" />
							</p:commandLink>
							<p:spacer width="5" />

							<p:commandLink value="删除" rendered="#{itm[3]==0}"
								action="#{tdDynaFormDesignBean.deleteAction}" update=":tabView"
								process="@this">
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
								<f:setPropertyActionListener
									target="#{tdDynaFormDesignBean.formId}" value="#{itm[4]}" />
							</p:commandLink>
							<p:spacer width="5" rendered="#{itm[4]==0}" />
						</p:column>
					</p:dataTable>

					<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
					<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
				</h:form>
			</p:tab>
			<p:tab id="edit" title="edit" titleStyle="display:none;">
				<ui:include src="/webapp/system/tdDynaFormDesignEdit.xhtml" /> 
			</p:tab>
			<p:tab id="view" title="view" titleStyle="display:none;">
				<ui:include src="/webapp/system/tdDynaFormDesignView.xhtml" />
			</p:tab>
			<p:tab id="subEdit" title="subEdit" titleStyle="display:none;">
				<!-- <ui:include src="/webapp/system/tdFlowFormDesignSubEdit.xhtml" /> -->
			</p:tab>
			<p:tab id="subView" title="viewEdit" titleStyle="display:none;">
				<!-- <ui:include src="/webapp/system/tdFlowFormDesignSubView.xhtml" /> -->
			</p:tab>
		</p:tabView>

		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
	</h:body>
</f:view>
</html>










