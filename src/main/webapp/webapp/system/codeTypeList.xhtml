<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<!--@elvariable id="mgrbean" type="com.chis.modules.system.web.CodeBean"-->
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{codeBean}"/>
    <!-- 码表编辑页面:无树形 -->
    <ui:param name="editPage" value="/webapp/system/codeList.xhtml"/>
    <!-- 码表编辑页面 -->
    <ui:param name="viewPage" value="/webapp/system/codeTreeList.xhtml"/>
	<ui:define name="insertScripts">
		<h:outputScript library="js" name="namespace.js"/>
		<h:outputScript name="js/validate/system/validate.js"/>
	</ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="码表维护"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{codeBean.searchAction}"
								 update="dataTable" process="@this,mainGrid"
								 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();" />
				<p:commandButton value="新增" icon="ui-icon-plus" id="codeAddBtn" action="#{codeBean.addCodeType}" process="@this" update="codeEditDialog"
					oncomplete="PF('CodeTypeEditDialog').show()">
					<p:resetInput target="codeEditDialog" />
				</p:commandButton>
				<p:commandButton value="自检" icon="ui-icon-check" action="#{codeBean.autoCheck}" update="dataTable" process="@this"
								 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();" />
			</h:panelGrid>
			<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
				<h:outputLabel value="提示：" style="color:red;"/>
				<h:outputLabel value="仅对树形码表进行自检，修改记录后请重新【自检】！" style="color:blue;"/>
			</p:outputPanel>
		</p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width: 160px;">
				<h:outputText value="系统类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:9px;width: 300px;">
				<p:selectOneMenu id="searchSystemType" value="#{codeBean.searchSystemType}" style="width: 187px;">
					<f:selectItem itemLabel="--全部--" itemValue=""/>
					<f:selectItems value="#{codeBean.systemTypeMap}"/>
				</p:selectOneMenu>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;">
				<h:outputText value="码表类型：" />
			</p:column>
			<p:column style="text-align:left;padding-left:9px;width: 300px;">
				<p:inputText id="searchTypeName" value="#{codeBean.searchTypeName}" maxlength="100"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:160px;height: 35px;">
				<h:outputText value="编码：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;">
				<p:inputText  value="#{codeBean.searchTypeCode}" maxlength="25"/>
			</p:column>
		</p:row>
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width: 160px;height: 35px;">
				<h:outputText value="自检状态：" />
			</p:column>
			<p:column style="text-align:left;padding-left:9px;" colspan="5">
				<p:selectManyCheckbox value="#{mgrbean.searchTypeState}" >
					<f:selectItem itemValue="1" itemLabel="成功"/>
					<f:selectItem itemValue="2" itemLabel="失败"/>
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="系统类型" style="width: 200px;text-align: center;">
            <h:outputText value="#{itm[1].typeCN}" />
        </p:column>
        <p:column headerText="编码" style="width: 80px; ">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="码表类型" style="width: 300px;padding-left: 3px;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="是否树形" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[4]=='1'?'是':'否'}" />
        </p:column>
		<p:column headerText="维护说明" style="padding-left: 3px;">
			<h:outputText id="rmk" value="#{itm[7]}" styleClass="zwx-tooltip" />
			<p:tooltip for="rmk"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" >
				<h:outputText value="#{itm[9]}" escape="false" />
			</p:tooltip>
		</p:column>
		<p:column headerText="自检状态" style="width: 80px;text-align: center;">
			<h:outputText value="成功" rendered="#{itm[5]=='1'}" />
			<h:outputText value="失败" rendered="#{itm[5]=='2'}" style="color: red;" />
		</p:column>
		<p:column headerText="失败原因" style="width: 300px;padding-left: 3px;">
			<h:outputText id="failRsn" value="#{itm[6]}" styleClass="zwx-tooltip" />
			<p:tooltip for="failRsn"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" >
				<h:outputText value="#{itm[8]}" escape="false" />
			</p:tooltip>
		</p:column>
        <p:column headerText="操作" style="width: 100px;padding-left: 3px;">
            <p:commandLink value="维护" action="#{codeBean.modInitAction}" onclick="hideTooltips();"
						   update=":tabView" process="@this" rendered="#{itm[4]=='0'}">
                <f:setPropertyActionListener target="#{codeBean.codeTypeId}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{codeBean.curCodeTypeName}" value="#{itm[3]}"/>
                <f:setPropertyActionListener target="#{codeBean.curCodeTypeNo}" value="#{itm[2]}"/>
                <f:setPropertyActionListener target="#{codeBean.codeTypeRmk}" value="#{itm[7]}"/>
                <f:setPropertyActionListener target="#{codeBean.checkState}" value="#{itm[5]}"/>
                <f:setPropertyActionListener target="#{codeBean.checkFailRsn}" value="#{itm[6]}"/>
            </p:commandLink>
            <p:commandLink value="维护" action="#{codeBean.viewInitAction}" onclick="hideTooltips();"
						   update=":tabView" process="@this" rendered="#{itm[4]=='1'}">
                <f:setPropertyActionListener target="#{codeBean.codeTypeId}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{codeBean.curCodeTypeName}" value="#{itm[3]}"/>
				<f:setPropertyActionListener target="#{codeBean.curCodeTypeNo}" value="#{itm[2]}"/>
				<f:setPropertyActionListener target="#{codeBean.codeTypeRmk}" value="#{itm[7]}"/>
				<f:setPropertyActionListener target="#{codeBean.checkState}" value="#{itm[5]}"/>
				<f:setPropertyActionListener target="#{codeBean.checkFailRsn}" value="#{itm[6]}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="修改" action="#{codeBean.modCodeType}"
						   update=":tabView:mainForm:codeEditDialog" oncomplete="PF('CodeTypeEditDialog').show()" process="@this"  >
                <f:setPropertyActionListener target="#{codeBean.codeTypeId}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" />
        </p:column>
    </ui:define>
	<ui:define name="insertOtherMainContents">
		<!-- 新增、修改码表类别 -->
		<p:dialog id="codeEditDialog" header="码表类别维护" widgetVar="CodeTypeEditDialog" resizable="false" width="600" height="350" modal="true">
			<p:panelGrid style="width:100%;" id="codeEditGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;height:27px;">
						<font color="red">*</font>
						<h:outputText value="系统类型：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:selectOneMenu value="#{codeBean.systemTypeEdit}" required="true" requiredMessage="系统类型不允许为空！" style="width: 180px;">
							<f:selectItem itemLabel="--请选择--" itemValue="" />
							<f:selectItems value="#{codeBean.systemTypeMap}" />
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;height:27px;">
						<h:outputText value="编码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:outputLabel value="#{codeBean.addCodeType.codeTypeName}"  />
					</p:column>
				</p:row>

				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;height:27px;">
						<font color="red">*</font>
						<h:outputText value="名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="codeName" value="#{codeBean.addCodeType.codeTypeDesc}" maxlength="100"  required="true" requiredMessage="名称不允许为空！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="是否树形：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:selectOneRadio style="width:160px;" value="#{codeBean.addCodeType.treeTag}" required="true" requiredMessage="是否树形不允许为空！" >
							<f:selectItem itemValue="0" itemLabel="否" />
							<f:selectItem itemValue="1" itemLabel="是" />
						</p:selectOneRadio>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="序号：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="xh" value="#{codeBean.addCodeType.num}" maxlength="8"
									 onblur="SYSTEM.verifyNum4(this, 8,0, true)"
									 onkeyup="SYSTEM.verifyNum4(this, 8,0, false)"
									 converterMessage="序号只能输入整数！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="维护说明：" />
					</p:column>
					<p:column style="text-align:left;vertical-align: middle;padding-left:3px;height: 150px;">
						<p:inputTextarea value="#{codeBean.addCodeType.rmk}" style="resize:none;width:97%;height:90%;"
										 autoResize="false" maxlength="2000"/>
					</p:column>
				</p:row>

			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn" action="#{codeBean.saveCodeType}" process="@this,codeEditGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn" onclick="PF('CodeTypeEditDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>


	</ui:define>

</ui:composition>











