<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
		    	<style type="text/css">
		    table.ui-selectmanycheckbox td {
		    	padding-left:0px;
		    	padding-right:5px;
		    	margin-left:0px;
		    	margin-right:5px;
		    }	
			table.ui-selectmanycheckbox td label{
				 white-space:nowrap !important;
		    	 overflow: hidden !important;
		    	 margin-top:0px;
			}  
		    table.ui-selectoneradio td {
		    	padding-left:0px;
		    	padding-right:5px;
		    	margin-left:0px;
		    	margin-right:5px;
		    }				  
			table.ui-selectoneradio td label{
				 white-space:nowrap;
		    	 overflow: hidden;
			}
		    	</style>    
		    	<script type="text/javascript">
		    	//<![CDATA[
		    	function seasonalChg() {
		    		if(typeof PeriodType != "undefined") {
		    			PeriodType.selectValue(4);
		    		}
		    	}           
		      //]]>
		    	</script>
    </h:head>

    <h:body>
        <h:form id="mainForm">
            <h:outputStylesheet name="css/default.css"/>
            <h:outputStylesheet name="css/ui-tabs.css" />
            <!--引入中文日期-->
        	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

            <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                <f:facet name="header">
                    <p:row>
                        <p:column  style="text-align:left;padding-left:5px;height: 20px;">
                            <h:outputText value="我的日程"/>
                        </p:column>
                    </p:row>
                </f:facet>
            </p:panelGrid>

			<!-- month,agendaWeek,agendaDay -->
			<p:schedule id="mySchedule" value="#{scheduleBean.defaultScheduleModel}" locale="tr" rightHeaderTemplate="" widgetVar="mySchedule" timeFormat="HH:mm">
		        <p:ajax event="dateSelect" listener="#{scheduleBean.onDateSelect}" update="EditDialog" oncomplete="PF('EditDialog').show()" resetValues="true"/>
		        <p:ajax event="eventSelect" listener="#{scheduleBean.onEventSelect}" update="EditDialog" oncomplete="PF('EditDialog').show()" resetValues="true"/>		
			</p:schedule>
			
		    <p:dialog id="EditDialog" header="我的日程" widgetVar="EditDialog" resizable="false" width="700" height="300" 
		    	modal="true" maximizable="true">
		        <p:panelGrid style="width:100%;" id="editGrid">
		            <p:row>
		                <p:column style="text-align:right;padding-right:3px;width:30%;">
		                    <font color="red">*</font>
		                    <h:outputText value="周期性日程："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
		                	<p:selectOneRadio value="#{scheduleBean.tdOaSchedule.seasonal}" id="seasonal">
		                		<f:selectItem itemLabel="否" itemValue="0"/>
		                		<f:selectItem itemLabel="是" itemValue="1"/>
		                		<p:ajax event="change" process="@this,@parent" update="editGrid1,editGrid2" oncomplete="seasonalChg()"/>
		                	</p:selectOneRadio>
		                </p:column>
		            </p:row>
		            
		            <p:row>
		                <p:column style="text-align:right;padding-right:3px;width:30%;">
		                    <font color="red">*</font>
		                    <h:outputText value="标题："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
		                    <p:inputText id="scheduleTitle" value="#{scheduleBean.tdOaSchedule.scheduleTitle}" 
		                    	maxlength="100" required="true" requiredMessage="标题不允许为空！" size="50"/>
		                </p:column>
		            </p:row>
		
		            <p:row>
		                <p:column style="text-align:right;padding-right:3px;width:30%;">
		                    <h:outputText value="内容："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
		                	<p:inputTextarea id="scheduleTxt" value="#{scheduleBean.tdOaSchedule.scheduleTxt}" cols="50" rows="3"/>
		                </p:column>
		            </p:row>
		
		        </p:panelGrid>    
		        <p:panelGrid style="width:100%;margin-top:-1px;" id="editGrid1">    
		            <p:row rendered="#{scheduleBean.tdOaSchedule.seasonal=='0'}">
		                <p:column style="text-align:right;padding-right:3px;width:30%;">
		                	<font color="red">*</font>
		                    <h:outputText value="执行时间："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
	                        <p:calendar  id="beginTime" value="#{scheduleBean.tdOaSchedule.beginTime}" size="22" navigator="true" yearRange="c:c+1" 
	                                     converterMessage="执行开始时间，格式输入不正确！" pattern="yyyy-MM-dd HH:mm"  showButtonPanel="true" showOtherMonths="true"/>
	                        ~
	                        <p:calendar   id="endTime" value="#{scheduleBean.tdOaSchedule.endTime}" size="22" navigator="true" yearRange="c:c+1" 
	                                     converterMessage="执行结束时间，格式输入不正确！" pattern="yyyy-MM-dd HH:mm"  showButtonPanel="true" showOtherMonths="true"/>
		                </p:column>
		            </p:row>		        
		        
		            <p:row rendered="#{scheduleBean.tdOaSchedule.seasonal=='1'}">
		                <p:column style="text-align:right;padding-right:3px;width:30%;">
		                	<h:outputText value="周期设置："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
			                <div style="display: table-cell;vertical-align: middle;">
			                    <p:selectOneMenu id="periodType" value="#{scheduleBean.tdOaSchedule.periodType}" widgetVar="PeriodType">
			                        <f:selectItem itemLabel="日" itemValue="2"/>
			                        <f:selectItem itemLabel="周" itemValue="3"/>
			                        <f:selectItem itemLabel="月" itemValue="4"/>
			                        <p:ajax event="change" process="@this,@parent" update="periodPanel,editGrid2"/>
			                    </p:selectOneMenu>
			                </div>
			                <div style="display: table-cell;vertical-align: middle;">
			                    <p:outputPanel id="periodPanel">
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="：" rendered="#{scheduleBean.tdOaSchedule.periodType != '0'}"/>
			                        </div>
			
			                        <!-- 每天几点几分执行一次 -->
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="每天" rendered="#{scheduleBean.tdOaSchedule.periodType=='2'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.hourOfDay}"
			                                             rendered="#{scheduleBean.tdOaSchedule.periodType=='2'}">
			                                <c:forEach begin="0" end="23" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="点" rendered="#{scheduleBean.tdOaSchedule.periodType=='2'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.minOfDay}" rendered="#{scheduleBean.tdOaSchedule.periodType=='2'}">
			                                <c:forEach begin="0" end="59" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx}" itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="分开始执行" rendered="#{scheduleBean.tdOaSchedule.periodType=='2'}"/>
			                        </div>
			
			                        <!-- 每周几的几点几分执行一次 -->
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="每星期" rendered="#{scheduleBean.tdOaSchedule.periodType=='3'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.dayOfWeek}" rendered="#{scheduleBean.tdOaSchedule.periodType=='3'}">
			                                <f:selectItem itemLabel="一" itemValue="1"/>
			                                <f:selectItem itemLabel="二" itemValue="2"/>
			                                <f:selectItem itemLabel="三" itemValue="3"/>
			                                <f:selectItem itemLabel="四" itemValue="4"/>
			                                <f:selectItem itemLabel="五" itemValue="5"/>
			                                <f:selectItem itemLabel="六" itemValue="6"/>
			                                <f:selectItem itemLabel="日" itemValue="7"/>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.hourOfDay}" rendered="#{scheduleBean.tdOaSchedule.periodType=='3'}">
			                                <c:forEach begin="0" end="23" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="点" rendered="#{scheduleBean.tdOaSchedule.periodType=='3'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.minOfDay}" rendered="#{scheduleBean.tdOaSchedule.periodType=='3'}">
			                                <c:forEach begin="0" end="59" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx}" itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="分开始执行" rendered="#{scheduleBean.tdOaSchedule.periodType=='3'}"/>
			                        </div>
			
			                        <!-- 每月几号的几点几分执行一次 -->
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="每月" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.dayOfMon}" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}">
			                                <c:forEach begin="1" end="31" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="日" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.hourOfDay}" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}">
			                                <c:forEach begin="0" end="23" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="点" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}"/>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <p:selectOneMenu value="#{scheduleBean.tdOaSchedule.minOfDay}" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}">
			                                <c:forEach begin="0" end="59" step="1" var="inx">
			                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx}" itemValue="#{inx}"/>
			                                </c:forEach>
			                            </p:selectOneMenu>
			                        </div>
			                        <div style="display: table-cell;vertical-align: middle;">
			                            <h:outputText value="分" rendered="#{scheduleBean.tdOaSchedule.periodType=='4'}"/>
			                        </div>
			                    </p:outputPanel>
			                </div>		                
		                </p:column>
		            </p:row>
		      </p:panelGrid>      
		      <p:panelGrid style="width:100%;margin-top:-1px;" id="editGrid2">  
		            <p:row>
		                <p:column style="text-align:right;padding-right:3px;width:30%;">
		                	<h:outputText value="提醒："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
                        	<p:selectOneMenu value="#{scheduleBean.tdOaSchedule.remindTime}" id="remindTime" widgetVar="RemindTime" >
                        		<f:selectItem itemLabel="无提醒" itemValue="0"/>
                        		<f:selectItem itemLabel="5 分钟 前" itemValue="300000"/>
                        		<f:selectItem itemLabel="10 分钟 前" itemValue="600000"/>
                        		<f:selectItem itemLabel="15 分钟 前" itemValue="900000"/>
                        		<f:selectItem itemLabel="30 分钟 前" itemValue="1800000"/>
                        		<f:selectItem itemLabel="1 小时 前" itemValue="3600000"/>
                        		<f:selectItem itemLabel="2 小时 前" itemValue="7200000"/>
                        		<f:selectItem itemLabel="6 小时 前" itemValue="21600000"/>
                        		<f:selectItem itemLabel="12 小时 前" itemValue="43200000"/>
                        		<f:selectItem itemLabel="18 小时 前" itemValue="64800000"/>
                        		<f:selectItem itemLabel="1 天 前" itemValue="86400000"/>
                        		<f:selectItem itemLabel="2 天 前" itemValue="172800000"/>
                        		<f:selectItem itemLabel="1 星期 前" itemValue="604800000"/>
                        		<p:ajax event="change" process="@this,@parent" update="editGrid2"/>
                        	</p:selectOneMenu>
                        	<p:selectOneRadio id="isRemind" value="#{scheduleBean.tdOaSchedule.isRemind}" rendered="false">
                        		<f:selectItem itemLabel="不提醒" itemValue="0"/>
                        		<f:selectItem itemLabel="提醒" itemValue="1"/>
                        	</p:selectOneRadio>
		                </p:column>
		            </p:row>	
		            	              
		            <p:row rendered="#{scheduleBean.tdOaSchedule.remindTime != '0'}">
		                <p:column style="text-align:right;padding-right:3px;width:30%;height:25px;">
		                	<h:outputText value="提醒方式："/>
		                </p:column>
		                <p:column style="text-align:left;padding-left:3px;">
							<p:selectManyCheckbox id="remindMtdList" value="#{scheduleBean.tdOaSchedule.remindMtdList}">
								<f:selectItems value="#{scheduleBean.txtypeList}" var="t" itemLabel="#{t.txType.typeCN}" itemValue="#{t.rid}" />
							</p:selectManyCheckbox>		                
		                </p:column>
		            </p:row>
		        </p:panelGrid>
		        
		        <f:facet name="footer">
		            <h:panelGrid style="width: 100%;text-align: center;">
		                <h:panelGroup>
		                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{scheduleBean.saveAction}" process="@this,:mainForm:editGrid,:mainForm:editGrid1,:mainForm:editGrid2"/>
		                    <p:spacer width="5" />
		                    <p:commandButton value="移除" icon="ui-icon-trash" id="deleteBtn" process="@this" action="#{scheduleBean.deleteScheduleAction}" rendered="#{scheduleBean.tdOaSchedule.rid!=null}">
		                    	<p:confirm header="消息确认框" message="确定要移除吗？" icon="ui-icon-alert"/>
		                    </p:commandButton>
		                    <p:spacer width="5" />
		                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('EditDialog').hide();" type="button"/>
		                </h:panelGroup>
		            </h:panelGrid>
		        </f:facet>
		    </p:dialog>		
		    
		    <ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>	            
        </h:form>
		<script type="text/javascript">
		//<![CDATA[
	   PrimeFaces.locales['tr'] = {
	        closeText: '关闭',
	        prevText: '上月',
	        nextText: '下月',
	        currentText: '今天',
	        monthNames: ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
	        monthNamesShort: ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
	        dayNames: ['星期天','星期一','星期二','星期三','星期四','星期五','星期六'],
	        dayNamesShort: ['周日','周一','周二','周三','周四','周五','周六'],
	        dayNamesMin: ['周日','周一','周二','周三','周四','周五','周六'],
	        weekHeader: 'Hf',
	        firstDay: 1,
	        isRTL: false,
	        showMonthAfterYear: true,
	        yearSuffix: '',
	        month: '月',
	        week: '周',
	        day: '日',
	        allDayText : '全天'
	    };	           
		//]]>	
		</script> 
        <!-- 引入用户自定义皮肤 -->
        <link type="text/css" rel="stylesheet" href="/javax.faces.resource/theme.css.faces?ln=primefaces-#{facesContext.externalContext.sessionMap['SESSION_DATA'].skinName}" />
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
    	<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
    </h:body>
</f:view>
</html>






