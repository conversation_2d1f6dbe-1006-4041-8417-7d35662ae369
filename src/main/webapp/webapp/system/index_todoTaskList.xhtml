<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<ui:param name="mgrbean" value="#{indexTodoTaskListBean}" />
		<style type="text/css">
</style>
		<script type="text/javascript">
			//<![CDATA[
			function forwordPage(name, adr) {
				top.ShortcutMenuClick("01", name, adr, "");
			}

			function zwx_socket_update() {
				updateTodoView();
			}
			//]]>
		</script>
		<style>
.refresh {
	position: absolute;
	width: 25px;
	height: 25px;
	right: 5px;
	top: 8px;
	z-index: 100;
}
</style>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />
	</h:head>
	<h:body>
		<h:form id="dbForm">
			<p:remoteCommand name="updateTodoView"
				action="#{indexTodoTaskListBean.initTabView}" update="dbForm"
				process="@this" />
			<p:tabView id="dbTab" widgetVar="DbTab" dynamic="true" cache="false"
				style="border:0px;height:#{indexTodoTaskListBean.lanHeight-57}px;padding:0px;"
				rendered="#{indexTodoTaskListBean.tabMenuList.size()>0}"
				activeIndex="#{indexTodoTaskListBean.tabIndex}">
				<p:ajax event="tabChange" process="@this,dbTab"
					listener="#{indexTodoTaskListBean.onTabChange}" update="dbTab">
				</p:ajax>
				<c:forEach items="#{indexTodoTaskListBean.tabMenuList}" var="tabItm">
					<p:tab title="#{tabItm[0]}(#{tabItm[1]})" id="#{tabItm[0]}">
						<table cellpadding="0" cellspacing="0" border="0"
							style="width: 100%;">
							<c:forEach items="#{indexTodoTaskListBean.dbMsgList}" var="itm">
								<tr>
									<td style="width:70%;padding-top:5px;"><p:commandLink
											value="#{itm[3]}" process="@this"
											rendered="#{indexTodoTaskListBean.ifFlow}"
											style="color:#222222;font-family:Lucida Grande, Lucida Sans, Arial, sans-serif;font-size:13px;"
											action="#{indexTodoTaskListBean.openLinkAction}">
											<f:setPropertyActionListener value="#{itm[0]}"
												target="#{indexTodoTaskListBean.taskId}" />
											<f:setPropertyActionListener value="#{itm[5]}"
												target="#{indexTodoTaskListBean.nodeId}" />
											<f:setPropertyActionListener value="#{itm[6]}"
												target="#{indexTodoTaskListBean.businessId}" />
										</p:commandLink> <p:commandLink value="#{itm[3]}" process="@this"
											rendered="#{!indexTodoTaskListBean.ifFlow}"
											style="color:#222222;font-family:Lucida Grande, Lucida Sans, Arial, sans-serif;font-size:13px;"
											action="#{indexTodoTaskListBean.openLinkAction}">
											<f:setPropertyActionListener value="#{itm[0]}"
												target="#{indexTodoTaskListBean.rid}" />
										</p:commandLink></td>
									<td style="text-align:right;width:30%;"><span
										style="color:#999;font-size:13px;font-family:Lucida Grande, Lucida Sans, Arial, sans-serif;">#{itm[4]}</span>
									</td>
								</tr>
							</c:forEach>
						</table>
					</p:tab>
				</c:forEach>
			</p:tabView>
			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px; border-collapse:inherit;"
				id="titleGrid" styleClass="ui-corner-all"
				rendered="#{indexTodoTaskListBean.tabMenuList.size()==0}">
				<f:facet name="header">
					<p:row>
						<p:column colspan="4"
							style="text-align:left;padding-left:5px;height: 20px;"
							styleClass="ui-corner-all">
							<h:outputText value="没有内容" />
						</p:column>
					</p:row>
				</f:facet>
			</p:panelGrid>
			<div class="refresh">
				<p:commandLink
					styleClass="ui-panel-titlebar-icon ui-corner-all ui-state-default ui-icon ui-icon-refresh"
					process="@this" action="#{indexTodoTaskListBean.initTabView}"
					update="dbForm">
					<f:setPropertyActionListener
						target="#{indexTodoTaskListBean.tabIndex}" value="0" />
				</p:commandLink>
			</div>
		</h:form>
		<ui:insert name="/WEB-INF/templates/system/insertPoll"></ui:insert>
		<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>