<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	   template="/WEB-INF/templates/system/mainTemplate.xhtml">
	
  <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{sysBean}" />
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/sysLoginEdit.xhtml" />
    <!-- 查看页面 -->
    <ui:param name="viewPage" value="/webapp/system/sysLoginView.xhtml" />


	 <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="第三方系统注册" />
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3"
                style="border-color:transparent; padding:0px;">
                <span class="ui-separator"><span
                    class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" action="#{sysBean.searchAction}" process="@this,mainGrid"
                update=":tabView"     />
                <p:commandButton value="添加" icon="ui-icon-plus" id="searchBtn"
            action="#{sysBean.addInitAction}" update=":tabView"
					process="@this" resetValues="true"> 
                </p:commandButton>

            </h:panelGrid>
        </p:outputPanel>
    </ui:define>


    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;width:120px;padding-left:3px;">
                <p:outputLabel value="系统名称：" />
            </p:column>
            <p:column style="width: 200px;padding-left: 3px;">
                <p:inputText value="#{sysBean.searchSysName}" maxlength="50" />
            </p:column>
            <p:column style="width: 120px;padding-left: 3px;text-align:right">
                <p:outputLabel value="系统类型："  />
            </p:column>
            <p:column>
                  <p:selectManyCheckbox value="#{sysBean.listType}">
                            <f:selectItem itemLabel="BS" itemValue="1" />
                            <f:selectItem itemLabel="CS" itemValue="2" />
                  </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!--数据库主表和探头的几条基本信息 -->
    <ui:define name="insertDataTable">
        <p:column headerText="序号" style="width: 80px;text-align: center;">
            <h:outputText value="#{R+1}" />
        </p:column>
        <p:column headerText="系统名称" style="width:200px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
         
        <p:column headerText="系统类型" style="width:80px;text-align: center;">
            <h:outputText value="#{itm[3]==1?'BS':'CS'}"/>
        </p:column>
        <p:column headerText="程序地址">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <!--增删改查按钮的实现  -->
        <p:column headerText="操作" style="width: 250px;padding-left: 3px;">
			<p:commandLink value="查看" process="@this"
				action="#{sysBean.viewInitAction}" update=":tabView">

				<!--传值用的  -->
				<f:setPropertyActionListener target="#{sysBean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
						
			<p:spacer width="5" />
			<p:commandLink value="修改" process="@this" update=":tabView"
			 action="#{sysBean.modInitAction}">

				<f:setPropertyActionListener target="#{sysBean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="删除" action="#{sysBean.delete}"
				update=":tabView" process="@this">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{sysBean.rid}"
					value="#{itm[0]}" />
			</p:commandLink>
			
			<p:spacer width="5" />
		</p:column>
        <!-- 主表查询 -->
    </ui:define>


</ui:composition>
