<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <ui:param name="onfocus" value="false" />
    <script type="text/javascript">
    </script>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知发布详情" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0px;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView" immediate="true" >
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <!-- 引入通知发布与附件区域 -->
        <ui:include src="tdPublishNoticeViewBase.xhtml">
            <ui:param name="childbean" value="#{mgrbean}" />
        </ui:include>
        <!--通知对象区域-->
            <p:panelGrid  style="margin-bottom: 10px;width:100%" id="noticeUnitPanel" >
                <f:facet name="header">
                    <p:row>
                        <p:column style="height:20px;text-align:left;padding-left: 5px; ">
                            <p:outputLabel value="通知对象"/>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:dataTable value="#{mgrbean.publishNoticeUnitList}" id="noticeUnitViewDataTable" var="itm" rows="#{mgrbean.noticeUnitPageSize}" paginatorPosition="bottom" rowIndexVar="R"
                                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                     rowsPerPageTemplate="#{'10,20,50'}"  emptyMessage="没有您要找的记录！" paginator="true"
                                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                            <p:column headerText="序号" style="width: 50px;text-align: center;" >
                                <p:outputLabel value="#{R+1}" />
                            </p:column>
                            <p:column headerText="地区" style="width: 260px;" >
                                <p:outputLabel value="#{itm.fkByUnitId.tsZone.zoneType>3?itm.fkByUnitId.tsZone.fullName.substring(itm.fkByUnitId.tsZone.fullName.indexOf('_')+1,itm.fkByUnitId.tsZone.fullName.length()):itm.fkByUnitId.tsZone.zoneName}" />
                            </p:column>
                            <p:column headerText="单位名称" style="width: 380px;" >
                                <p:outputLabel value="#{itm.fkByUnitId.unitname}" />
                            </p:column>
                            <p:column headerText="状态" style="width: 60px;text-align: center" >
                                <p:outputLabel value="待阅" rendered="#{itm.state==0}" style="color: red"/>
                                <p:outputLabel value="已阅" rendered="#{itm.state==1}" style="color: green"/>
                            </p:column>
                            <p:column headerText="操作" >
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
            </p:panelGrid>
    </ui:define>
</ui:composition>
