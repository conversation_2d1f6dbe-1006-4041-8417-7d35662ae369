<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title><h:outputText value="#{selectOfficeBean.officeTitle}" /></title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	</h:head>
	<h:body>
		<!-- style="overflow-y:hidden;" -->
		<h:form id="selectForm">
			<p:tree value="#{selectOfficeBean.treeNode}" var="node" selectionMode="checkbox" selection="#{selectOfficeBean.selectedNodes}" id="officeTree"
				style="width: 300px;height: 330px;overflow-y: auto;">
				<p:treeNode>
					<h:outputText value="#{node.officename}" />
				</p:treeNode>
			</p:tree>
			<h:panelGrid style="width: 100%;text-align: center;position: relative;top:10px;">
				<h:panelGroup>
					<p:commandButton value="保存" icon="ui-icon-check" id="officeSaveBtn" action="#{selectOfficeBean.selectAction}" process="@this,officeTree" />
					<p:spacer width="5" />
					<p:commandButton value="取消" icon="ui-icon-close" id="officeBackBtn" action="#{selectOfficeBean.dialogClose}" immediate="true" />
				</h:panelGroup>
			</h:panelGrid>

			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		</h:form>
	</h:body>
</f:view>
</html>
