<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body onload="document.getElementById('codeForm:userName').focus();">
    	<title>#{userRadioSelectListBean.titleName}选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <style type="text/css">
        </style>
        <h:form id="codeForm">
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                            <p:outputLabel value="姓名：" styleClass="zwx_dialog_font" />
                            <p:inputText id="userName" value="#{userRadioSelectListBean.searchName}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update=":codeForm:selectedUserTable" process="@this,searchPanel" listener="#{userRadioSelectListBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"  value="#{userRadioSelectListBean.displayList}" id="selectedUserTable"
                         rowsPerPageTemplate="#{'10,20,50'}"  pageLinks="5"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorPosition="bottom"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                <p:column headerText="选择" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{userRadioSelectListBean.selectAction}" process="@this" >
                        <f:setPropertyActionListener value="#{itm}" target="#{userRadioSelectListBean.selectPro}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="单位名称" style="padding-left: 3px;width:260px;">
                	<h:outputText value="#{itm.tsUnit.unitname}" />
                </p:column>
                <p:column headerText="姓名" style="padding-left: 3px;width:150px;text-align: center;">
                    <h:outputText value="#{itm.username}" />
                </p:column>
                <p:column headerText="手机号码" style="padding-left: 3px;text-align: center;">
                    <h:outputText value="#{itm.mbNum}" />
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
    </h:body>
</f:view>
</html>
