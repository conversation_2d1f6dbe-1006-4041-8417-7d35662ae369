<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>
    <h:body onload="document.getElementById('codeForm:pym').focus();">
        <title>行业类别选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <h:form id="codeForm">
            <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
                <h:panelGrid columns="3"
                             style="border-color:transparent;padding:0;">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{indusTypeCodeMulitySelectListBean.submitAction}"
                                     process="@this,selectedIndusTable"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     action="#{indusTypeCodeMulitySelectListBean.dialogClose}" process="@this"/>
                </h:panelGrid>
                <p:outputPanel
                        style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;"
                        rendered="#{!indusTypeCodeMulitySelectListBean.selectSameLevel and indusTypeCodeMulitySelectListBean.selectNum!=null}">
                    <h:outputText value="提示：" style="color:red;position: relative;"/>
                    <h:outputText style="color:blue;position: relative;"
                                  value="最多只支持选择#{indusTypeCodeMulitySelectListBean.selectNum}个类别"/>
                </p:outputPanel>
                <p:outputPanel
                        rendered="#{indusTypeCodeMulitySelectListBean.selectSameLevel and indusTypeCodeMulitySelectListBean.selectNum==null}"
                        style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                    <h:outputText value="提示：" style="color:red;position: relative;"/>
                    <h:outputText value="只支持选择同级类别" style="color:blue;position: relative;"/>
                </p:outputPanel>
                <p:outputPanel
                        rendered="#{indusTypeCodeMulitySelectListBean.selectSameLevel and indusTypeCodeMulitySelectListBean.selectNum!=null}"
                        style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                    <h:outputText value="提示：" style="color:red;position: relative;"/>
                    <h:outputText value="最多只支持选择#{indusTypeCodeMulitySelectListBean.selectNum}个同级类别"
                                  style="color:blue;position: relative;"/>
                </p:outputPanel>
            </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                            <p:selectOneMenu value="#{indusTypeCodeMulitySelectListBean.selectTopCodeNo}" id="topCodeNo"
                                             style="width: 180px;">
                                <f:selectItem itemValue="" itemLabel="--门类--"/>
                                <f:selectItems value="#{indusTypeCodeMulitySelectListBean.topTypeList}"
                                               var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                                <p:ajax event="change"
                                        listener="#{indusTypeCodeMulitySelectListBean.onTopCodeTypeSelect}"
                                        process="@this"
                                        update="firstIndusCodeNo,middleCodeNo,selectedIndusTable"/>
                            </p:selectOneMenu>
                            <p:selectOneMenu value="#{indusTypeCodeMulitySelectListBean.selectFatherCodeNo}"
                                             id="firstIndusCodeNo"
                                             style="width: 180px;">
                                <f:selectItem itemValue="" itemLabel="--大类--"/>
                                <f:selectItems value="#{indusTypeCodeMulitySelectListBean.fatherTypeList}"
                                               var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                                <p:ajax event="change"
                                        listener="#{indusTypeCodeMulitySelectListBean.onFatherCodeTypeSelect}"
                                        process="@this"
                                        update="middleCodeNo,selectedIndusTable"/>
                            </p:selectOneMenu>
                            <p:selectOneMenu value="#{indusTypeCodeMulitySelectListBean.selectMiddleCodeNo}"
                                             id="middleCodeNo"
                                             style="width: 180px;">
                                <f:selectItem itemValue="" itemLabel="--中类--"/>
                                <f:selectItems value="#{indusTypeCodeMulitySelectListBean.middleTypeList}"
                                               var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                                <p:ajax event="change" listener="#{indusTypeCodeMulitySelectListBean.searchAction}"
                                        process="@this,searchPanel" update="selectedIndusTable"/>
                            </p:selectOneMenu>
                            <p:inputText id="pym" value="#{indusTypeCodeMulitySelectListBean.searchNamOrPy}"
                                         maxlength="20"
                                         style="width: 180px;height: 19px;margin: 2px 0 4px 0;" placeholder="小类名称/拼音码">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel"
                                        listener="#{indusTypeCodeMulitySelectListBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm" value="#{indusTypeCodeMulitySelectListBean.displayList}" id="selectedIndusTable"
                         paginator="true" rows="10" emptyMessage="没有数据！" rowsPerPageTemplate="10,20" pageLinks="5"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         paginatorPosition="bottom" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                <p:column headerText="选择" style="text-align:center;width:30px;">
                    <p:selectBooleanCheckbox value="#{itm.ifSelected}" disabled="#{itm.ifDisabled}">
                        <p:ajax event="change" listener="#{indusTypeCodeMulitySelectListBean.selectAction(itm)}"
                                process="@this"
                                update="selectedIndusTable"/>
                    </p:selectBooleanCheckbox>
                </p:column>
                <p:column headerText="门类" style="padding-left: 3px;width: 180px;">
                    <h:outputText value="#{indusTypeCodeMulitySelectListBean.splitCodePath(itm.codePath, 0)}"/>
                </p:column>
                <p:column headerText="大类" style="padding-left: 3px;width: 180px;">
                    <h:outputText value="#{indusTypeCodeMulitySelectListBean.splitCodePath(itm.codePath, 1)}"/>
                </p:column>
                <p:column headerText="中类" style="padding-left: 3px;width: 180px;">
                    <h:outputText value="#{indusTypeCodeMulitySelectListBean.splitCodePath(itm.codePath, 2)}"/>
                </p:column>
                <p:column headerText="小类" style="padding-left: 3px;width: 180px;">
                    <h:outputText value="#{indusTypeCodeMulitySelectListBean.splitCodePath(itm.codePath, 3)}"/>
                </p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
    </h:body>
</f:view>
</html>
