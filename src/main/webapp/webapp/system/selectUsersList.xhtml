<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>#{selectUsersBean.title}</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
    </h:head>
    <h:body>
        <h:form id="selectForm" style="overflow:hidden">
        	  <p:outputPanel styleClass="zwx_toobar_42" >
				<h:panelGrid columns="3">
					<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="确定" icon="ui-icon-check" id="saveBtn" action="#{selectUsersBean.selectAction}" process="@form" />
					<p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectUsersBean.dialogClose}" process="@this" />
				</h:panelGrid>
			</p:outputPanel>
			<p:panel id="offPanel" style="width:860px;margin-top:8px;">
				<f:facet name="header">
					<p:row>
	                    <p:column colspan="2"  style="text-align:left;padding-left:5px;">
	                        <p:selectBooleanCheckbox value="#{selectUsersBean.officeWholeSelect}" itemLabel=" &#160;全部科室">
	                        	<p:ajax listener="#{selectUsersBean.officeWholeSelectAction}" process="@this,:selectForm:offPanel" 
	                        		event="change" update=":selectForm:offPanel"/>
	                        </p:selectBooleanCheckbox>
	                    </p:column>
	                </p:row>
				</f:facet>
				<p:panelGrid style="width:840px;" id="empOffGrid">
					<c:forEach items="#{selectUsersBean.userOfficeMap}" var="v">
						<p:row>
						    <p:column style="text-align:right;padding-right:3px;width: 120px;">
	                            <p:selectBooleanCheckbox itemLabel="#{v.key.officename}"  value="#{v.key.selected}">
	                            		<p:ajax listener="#{selectUsersBean.officeSelectAction}" process="@this,:selectForm:offPanel,:selectForm:officeId" 
	                            			update=":selectForm:offPanel" event="change" 
	                            			onstart="document.getElementById('selectForm:officeId').value = #{v.key.rid}"/>
	                            </p:selectBooleanCheckbox>
	                        </p:column>
	                        <p:column style="text-align:left;padding-left:3px;">
	                        		<c:forEach items="#{v.value}" var="emp"  varStatus="ind">
	                        				<p:selectBooleanCheckbox itemLabel="#{emp.username}" value="#{emp.selected}" style="margin-left:10px;"/>
	                        				<c:if test="#{(ind.index+1) % 5 == 0}">
	                        					<br/>
	                        				</c:if>
	                        		</c:forEach>
	                        </p:column>
						</p:row>
					</c:forEach>
				</p:panelGrid>
			</p:panel>
			<p:panel id="groupPanel" style="width:860px;margin-top:10px;" rendered="#{selectUsersBean.ifShowGroup and selectUsersBean.userGroupMap.size() > 0}">
				<f:facet name="header">
					<p:row>
	                    <p:column colspan="2"  style="text-align:left;padding-left:5px;">
	                        <p:selectBooleanCheckbox value="#{selectUsersBean.groupWholeSelect}" itemLabel=" &#160;全部组">
	                        	<p:ajax listener="#{selectUsersBean.groupWholeSelectAction}" process="@this,:selectForm:groupPanel" 
	                        		event="change" update=":selectForm:groupPanel"/>
	                        </p:selectBooleanCheckbox>
	                    </p:column>
	                </p:row>
				</f:facet>
				<p:panelGrid style="width:840px;" id="empGroupGrid">
					<c:forEach items="#{selectUsersBean.userGroupMap}" var="v">
						<p:row>
						    <p:column style="text-align:right;padding-right:3px;width: 120px;">
	                            <p:selectBooleanCheckbox itemLabel="#{v.key.groupName}"  value="#{v.key.selected}">
	                            		<p:ajax listener="#{selectUsersBean.groupSelectAction}" process="@this,:selectForm:groupPanel,:selectForm:groupId" 
	                            			update=":selectForm:groupPanel" event="change" 
	                            			onstart="document.getElementById('selectForm:groupId').value = #{v.key.rid}"/>
	                            </p:selectBooleanCheckbox>
	                        </p:column>
	                        <p:column style="text-align:left;padding-left:3px;">
	                        		<c:forEach items="#{v.value}" var="emp"  varStatus="ind">
	                        				<p:selectBooleanCheckbox itemLabel="#{emp.username}" value="#{emp.selected}" style="margin-left:10px;"/>
	                        				<c:if test="#{(ind.index+1) % 5 == 0}">
	                        					<br/>
	                        				</c:if>
	                        		</c:forEach>
	                        </p:column>
						</p:row>
					</c:forEach>
				</p:panelGrid>
			</p:panel>            
            <br/>
            <h:inputHidden id="officeId" value="#{selectUsersBean.officeId}"/>
           	<h:inputHidden id="groupId" value="#{selectUsersBean.groupId}"/>
        </h:form>
    </h:body>
</f:view>
</html>
