<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">
	<h:form id="editTreeForm">
		<ui:include src="/WEB-INF/templates/system/focus.xhtml" />
			<style type="text/css">
				.ui-orderlist .ui-orderlist-list {
					list-style-type: none;
					margin: 0;
					padding: 0;
					overflow: auto;
					height: 400px;
					width: 250px;
				}
				
				.orderList{
					padding-left:13px;	
				}
				
				
				.ui-treetable tbody td{
				   white-space: normal;
				}
			</style>
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editGrid">
			<f:facet name="header">
				<p:row>
					<p:column style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="码表维护" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" action="#{codeBean.searchTreeSimpleCode}"
								 update="codeTreeDataTable"
								 process="@this,searchCodeName,searchMixCode" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{codeBean.backAction}"
								 onclick="hideTooltips();"
								 update=":tabView" immediate="true" />
				<p:commandButton value="添加根节点" update="codeEditDialog" icon="ui-icon-plus" process="@this" oncomplete="PF('CodeEditDialog').show()"
					action="#{codeBean.codeAddInitAction}" id="addRootBtn">
					<f:setPropertyActionListener target="#{codeBean.ifRoot}" value="true" />
					<p:resetInput target="codeEditDialog" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>

		<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
			<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:180px;">
						<h:outputText value="名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width: 250px;">
						<p:inputText id="searchCodeName" value="#{codeBean.searchCodeName}" maxlength="15" />
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:180px;">
						<h:outputText value="编码/结构层次编码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="searchMixCode" value="#{codeBean.searchMixCode}" maxlength="100" />
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:fieldset>

		<p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputText value="维护说明：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;">
					<p:inputTextarea value="#{codeBean.codeTypeRmk}"
									 style="width:99%;height: 150px;" autoResize="false"
									 readonly="true"/>
				</p:column>
			</p:row>
			<p:row rendered="#{codeBean.checkState == '2'}">
				<p:column style="text-align:right;padding-right:3px;height: 30px;">
					<h:outputText value="自检失败原因：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;">
					<h:outputText value="#{codeBean.checkFailRsn}" style="width: 80%;" />
				</p:column>
			</p:row>
		</p:panelGrid>

		<p:treeTable value="#{codeBean.treeNode}" var="itm" id="codeTreeDataTable" emptyMessage="没有您要找的记录！" selectionMode="single" selection="#{codeBean.selectedNode}"
			style="margin-top:5px;">
			<p:ajax event="expand" process="@this,codeTreeDataTable" listener="#{codeBean.expandNode}"
					immediate="true"/>
			<p:ajax event="collapse" process="@this,codeTreeDataTable" listener="#{codeBean.collapseNode}"
					immediate="true"/>
			<f:facet name="header">
				<h:outputText value="#{codeBean.curCodeTypeNo}    #{codeBean.curCodeTypeName}" />
			</f:facet>
			<p:column headerText="编码" style="width: 180px;word-wrap: break-word;word-break: break-all;padding-left:3px;">
				<h:outputText value="#{itm.codeNo}" />
			</p:column>
			<p:column headerText="结构层次编码" style="width: 180px;word-wrap: break-word;word-break: break-all;padding-left:3px;">
				<h:outputText value="#{itm.codeLevelNo}" />
			</p:column>
			<p:column headerText="名称" style="padding-left: 3px;width: 300px; ">
				<h:outputText value="#{itm.codeName}"  />
			</p:column>
			<p:column headerText="序号" style="width: 60px;text-align: center; ">
				<h:outputText value="#{itm.num}" />
			</p:column>
			<p:column headerText="状态" style="width: 80px;text-align: center; ">
				<h:outputText value="#{itm.ifReveal=='1'?'启用':'停用'}" />
			</p:column>
			<p:column headerText="说明" style="padding-left: 3px;width: 80px;">
				<h:outputText id="desc" value="#{itm.codeDesc}" styleClass="zwx-tooltip" />
				<p:tooltip for="desc" value="#{itm.codeDesc}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" />
			</p:column>
			<p:column headerText="扩展字段1" style="width: 50px;text-align: center;">
				<h:outputText value="#{itm.extendS1}" />
			</p:column>
			<p:column headerText="扩展字段2" style="width: 50px;text-align: center;">
				<h:outputText value="#{itm.extendS2}" />
			</p:column>
			<p:column headerText="扩展字段3" style="width: 80px;padding-left: 3px;">
				<h:outputText id="ext3" value="#{itm.extendS3}" styleClass="zwx-tooltip" />
				<p:tooltip for="ext3" value="#{itm.extendS3}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" />
			</p:column>
			<p:column headerText="扩展字段4" style="width: 50px;text-align: center;">
				<h:outputText value="#{itm.extendS4}" />
			</p:column>
			<p:column headerText="扩展字段5" style="width: 80px;padding-left: 3px;">
				<h:outputText id="ext5" value="#{itm.extendS5}" styleClass="zwx-tooltip" />
				<p:tooltip for="ext5" value="#{itm.extendS5}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" />
			</p:column>
			<p:column headerText="扩展字段6" style="width: 80px;padding-left: 3px;">
				<h:outputText id="ext6" value="#{itm.extendS6}" styleClass="zwx-tooltip" />
				<p:tooltip for="ext6" value="#{itm.extendS6}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" />
			</p:column>
			<p:column headerText="扩展字段7" style="width: 80px;padding-left: 3px;">
				<h:outputText id="ext7" value="#{itm.extendS7}" styleClass="zwx-tooltip" />
				<p:tooltip for="ext7" value="#{itm.extendS7}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" />
			</p:column>
			<p:column headerText="扩展字段8" style="width: 80px;padding-left: 3px;">
				<h:outputText id="ext8" value="#{itm.extendS8}" styleClass="zwx-tooltip" />
				<p:tooltip for="ext8" value="#{itm.extendS8}"  style="max-width:300px;word-break:break-all;word-wrap:break-word;" />
			</p:column>
			<p:column headerText="操作" style="padding-left: 3px;">
				<p:spacer width="5" rendered="#{itm.ifCanAddSub}" />
				<p:commandLink value="添加子节点" action="#{codeBean.codeAddInitAction}" rendered="#{itm.ifCanAddSub}" oncomplete="PF('CodeEditDialog').show()"
					update=":tabView:editTreeForm:codeEditDialog" process="@this">
					<f:setPropertyActionListener target="#{codeBean.ifRoot}" value="false" />
					<f:setPropertyActionListener target="#{codeBean.tsSimpleCode}" value="#{itm}" />
				</p:commandLink>
				<p:spacer width="5" />
				<p:commandLink value="修改" action="#{codeBean.codeModInitAction}" update=":tabView:editTreeForm:codeEditDialog" process="@this,:tabView:editTreeForm:codeTreeDataTable">
					<f:setPropertyActionListener target="#{codeBean.codeId}" value="#{itm.rid}" />
					<f:setPropertyActionListener target="#{codeBean.tsSimpleCode}" value="#{itm}" />
				</p:commandLink>
				<p:spacer width="5" rendered="#{itm.ifCanAddSub}" />
				<p:commandLink value="停用" action="#{codeBean.stopAction}"  update=":tabView:editTreeForm:codeTreeDataTable" process="@this,:tabView:editTreeForm:codeTreeDataTable" rendered="#{itm.ifReveal=='1' and itm.ifCanAddSub}">
					<p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert" />
					<f:setPropertyActionListener target="#{codeBean.codeId}" value="#{itm.rid}" />
				</p:commandLink>
				<p:commandLink value="启用" action="#{codeBean.startAction}" update=":tabView:editTreeForm:codeTreeDataTable" process="@this,:tabView:editTreeForm:codeTreeDataTable"
					rendered="#{itm.ifReveal!='1' and itm.ifCanAddSub}">
					<p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert" />
					<f:setPropertyActionListener target="#{codeBean.codeId}" value="#{itm.rid}" />
				</p:commandLink>
				<p:spacer width="5" rendered="#{itm.ifCanAddSub}" />
				<p:commandLink value="排序" action="#{codeBean.orderHasTreeAction}" rendered="#{itm.ifCanAddSub}" process="@this,:tabView:editTreeForm:codeTreeDataTable">
					<f:setPropertyActionListener target="#{codeBean.tsSimpleCode}" value="#{itm}" />
				</p:commandLink>
				
				
			</p:column>
		</p:treeTable>

		<!-- 新增、修改码表 -->
		<p:dialog id="codeEditDialog" header="码表维护" widgetVar="CodeEditDialog" resizable="false" width="600" height="620" modal="true">
			<p:panelGrid style="width:100%;" id="codeEditGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="编码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="codeNo" value="#{codeBean.tsSimpleCode.codeNo}" maxlength="10" style="width: 90%" />
					</p:column>
				</p:row>
				<p:row rendered="#{null ne codeBean.tsSimpleCode.rid}">
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="结构层次编码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText value="#{codeBean.tsSimpleCode.codeLevelNo}" maxlength="100" style="width: 90%" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="codeName" value="#{codeBean.tsSimpleCode.codeName}" maxlength="250"  style="width: 90%" />
					</p:column>
				</p:row>

				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputText value="序号：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="xh" value="#{codeBean.tsSimpleCode.num}"
									 onblur="SYSTEM.verifyNum4(this, 8,0, true)"
									 onkeyup="SYSTEM.verifyNum4(this, 8,0, false)" style="width: 90%"
									 maxlength="8" converterMessage="序号只能输入整数！" />
					</p:column>
				</p:row>

				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="说明：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;height: 60px; vertical-align: middle;">
						<p:inputTextarea value="#{codeBean.tsSimpleCode.codeDesc}"
										 style="resize:none;width:90%;height:80%;"
										 autoResize="false" maxlength="100"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段1：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" >
						<p:inputText id="extends1" value="#{codeBean.tsSimpleCode.extendS1}"
									 onblur="SYSTEM.verifyNum4(this, 2,0, true)"
									 onkeyup="SYSTEM.verifyNum4(this, 2,0, false)"
									 maxlength="2" style="width: 90%" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段2：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" >
						<p:inputText id="extends2" value="#{codeBean.tsSimpleCode.extendS2}"
									 onblur="SYSTEM.verifyNum4(this, 8,0, true)"
									 onkeyup="SYSTEM.verifyNum4(this, 8,0, false)"
									 converterMessage="扩展字段2只能输入整数！" style="width: 90%" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段3：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;height: 40px; vertical-align: middle;" >
						<p:inputTextarea value="#{codeBean.tsSimpleCode.extendS3}"
										 style="resize:none;width:90%;height:80%;"
										 autoResize="false" maxlength="50"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段4：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" >
						<p:inputText id="extends4" value="#{codeBean.tsSimpleCode.extendS4}"
									 onblur="SYSTEM.verifyNum4(this, 1,0, true)"
									 onkeyup="SYSTEM.verifyNum4(this, 1,0, false)"
									 maxlength="1" style="width: 90%" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段5：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;height: 40px; vertical-align: middle;" >
						<p:inputTextarea value="#{codeBean.tsSimpleCode.extendS5}"
										 style="resize:none;width:90%;height:80%;"
										 autoResize="false" maxlength="50"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段6：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;height: 40px; vertical-align: middle;" >
						<p:inputTextarea value="#{codeBean.tsSimpleCode.extendS6}"
										 style="resize:none;width:90%;height:80%;"
										 autoResize="false" maxlength="50"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段7：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;height: 40px; vertical-align: middle;" >
						<p:inputTextarea value="#{codeBean.tsSimpleCode.extendS7}"
										 style="resize:none;width:90%;height:80%;"
										 autoResize="false" maxlength="2000"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<h:outputText value="扩展字段8：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;height: 40px; vertical-align: middle;">
						<p:inputTextarea value="#{codeBean.tsSimpleCode.extendS8}"
										 style="resize:none;width:90%;height:80%;"
										 autoResize="false" maxlength="2000"/>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn" action="#{codeBean.saveAction}" process="@this,codeEditGrid"
							update=":tabView:editTreeForm:codeTreeDataTable,codeEditGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn" onclick="PF('CodeEditDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		
		<!-- 排序码表 -->
		<p:dialog id="codeOrderDialog" header="码表排序" widgetVar="CodeOrderDialog" resizable="false" width="340" height="440" modal="true">
			<p:orderList value="#{codeBean.orderList}" id="orderL" var="code" style="width:250px;text-align:center;" styleClass="orderList" itemValue="#{code}" converter="system.SimpleCodeConvert"
				controlsLocation="right">
				<f:facet name="caption">码表名称</f:facet>

				<p:column>
					<h:outputText value="#{code.codeName}" />
				</p:column>
			</p:orderList>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn2" action="#{codeBean.saveNoTreeOrder}" process="@this,orderL" oncomplete="PF('CodeOrderDialog').hide();" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn2" onclick="PF('CodeOrderDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</h:form>

</ui:composition>

