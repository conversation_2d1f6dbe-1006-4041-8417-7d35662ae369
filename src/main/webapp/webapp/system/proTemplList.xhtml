<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{proTemplBean}" />

	<!-- 样式或javascripts -->
	<ui:define name="insertScripts">
		<style type="text/css">
.ui-picklist .ui-picklist-list {
	text-align: left;
	height: 340px;
	width: 360px;
	overflow: auto;
}

.ui-picklist .ui-picklist-filter {
	padding-right: 0px;
	width: 98%;
}
</style>
		<script type="text/javascript">
			//<![CDATA[

			//]]>
		</script>
	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="2"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="选项模版维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			<span class="ui-separator"
				style="float: left;margin: 7px 3px auto 5px;"><span
				class="ui-icon ui-icon-grip-dotted-vertical" /></span>
			<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
				action="#{proTemplBean.searchAction}" update="dataTable"
				process="@this,mainGrid" />
			<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
				update="templEditDialog" process="@this"
				action="#{proTemplBean.addInit}"
				oncomplete="PF('TemplEditDialog').show()">
				<p:resetInput target="templEditDialog" />
			</p:commandButton>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputLabel for="searchName" value="模版名称：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;width:280px;">
				<p:inputText id="searchName" value="#{proTemplBean.searchName}"
					maxlength="50" />
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<h:outputLabel for="searchCode" value="模版编码：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<p:inputText id="searchCode" value="#{proTemplBean.searchCode}"
					maxlength="25" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 表格列 -->
	<ui:define name="insertDataTable">
		<p:column headerText="模版名称" style="width: 120px;padding-left: 3px;">
			<h:outputText value="#{itm.tmplName}" />
		</p:column>
		<p:column headerText="模版编码" style="width: 80px;padding-left: 3px;">
			<h:outputText value="#{itm.tmplCode}" />
		</p:column>
		<p:column headerText="模版选项" style="width: 500px;padding-left: 3px;">
			<h:outputText value="#{itm.tmplOpts}" />
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="修改" action="#{proTemplBean.modInit}"
				update=":mainForm:templEditDialog"
				oncomplete="PF('TemplEditDialog').show()" process="@this">
				<f:setPropertyActionListener target="#{proTemplBean.rid}"
					value="#{itm.rid}" />
				<p:resetInput target=":mainForm:templEditDialog" />
			</p:commandLink>
			<p:spacer width="5" />
			<p:commandLink value="删除" action="#{proTemplBean.deleteAction}"
				update="dataTable" process="@this">
				<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{proTemplBean.rid}"
					value="#{itm.rid}" />
			</p:commandLink>
		</p:column>
	</ui:define>

	<!-- 弹出框 -->
	<ui:define name="insertDialogs">
		<!-- 新增、修改角色 -->
		<p:dialog id="templEditDialog" header="选项模版维护"
			widgetVar="TemplEditDialog" resizable="false" width="700"
			modal="true">
			<p:panelGrid style="width:100%;" id="templEditGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputLabel for="tmplName" value="模版名称：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="tmplName"
							value="#{proTemplBean.proTempl.tmplName}" maxlength="50"
							required="true" requiredMessage="模版名称不能为空" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputLabel for="tmplCode" value="模版编码：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText id="tmplCode"
							value="#{proTemplBean.proTempl.tmplCode}" maxlength="25"
							size="25" required="true" requiredMessage="模版编码不能为空" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:30%;">
						<font color="red">*</font>
						<h:outputLabel for="tmplOpts" value="模版选项：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputTextarea id="tmplOpts"
							value="#{proTemplBean.proTempl.tmplOpts}" maxlength="500"
							required="true" requiredMessage="模版选项不能为空" cols="60" rows="7" />
						<h:outputText id="display"
							style="text-align:right;padding-right:3px;color:blue;"
							value="格式为：选项描述,选项描述" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
							action="#{proTemplBean.saveAction}" process="@this,templEditGrid"
							update="dataTable,templEditGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
							onclick="PF('TemplEditDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		
	</ui:define>

</ui:composition>











