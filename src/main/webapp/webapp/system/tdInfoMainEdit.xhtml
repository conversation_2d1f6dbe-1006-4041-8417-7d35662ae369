<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 编辑页面的script -->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
                    function selectUser(user){
                        document.getElementById('tabView:editForm:selOfficeId').value = user.checked + '_' + user.value;
                    	
                    }
                    
                    function selectAll(allCheck){
                    	jQuery('div[id*="unit_"] :checkbox').attr('checked', allCheck);
             		    if (allCheck) {
             		    	jQuery('div[id*="unit_"] > div').each(function() {
             		    		jQuery(this).addClass('ui-state-active');
             		    		jQuery(this).children('span').addClass('ui-icon ui-icon-check');
             		        });
             		    } else {
             		    	jQuery('div[id*="unit_"] > div').each(function() {
             		    		jQuery(this).removeClass('ui-state-active');
             		    		jQuery(this).children('span').removeClass('ui-icon ui-icon-check');
             		        });
             		    }
                    }
                  	
            //]]>
        </script>
        <style type="text/css">
            .ui-fileupload-files td {
                border: 0 none;
                padding: 2px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="基本信息" />
            </p:column>
        </p:row>
    </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="发布" icon="ui-icon-check" id="saveBtn" action="#{tdInfoMainBean.saveAction}" process="@form" update=":tabView" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{tdInfoMainBean.backSearchPageAction}" update=":tabView" immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
                <h:inputHidden id="selOfficeId" value="#{tdInfoMainBean.selOfficeId}" />
                 <h:inputHidden id="selUnitId" value="#{tdInfoMainBean.selUnitId}" />
    </ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertEditContent">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:8%;">
                <font color="red">*</font>
                <p:outputLabel value="标题：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText value="#{tdInfoMainBean.tdInfoMain.infoTitle}" 
                             size="100" maxlength="100" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <font color="red">*</font>
                <p:outputLabel value="内容：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputTextarea rows="10" cols="100" autoResize="false" 
                                 value="#{tdInfoMainBean.tdInfoMain.infoContent}" />
                <h:outputText id="display" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="附件：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <h:panelGroup id="downId">
                    <p:commandButton value="上传" onclick="PF('fileUIdVar').show();"
                                     process="@this" />
                    <p:dataTable value="#{tdInfoMainBean.annexList}" var="v"
                                 style="width:60%" type="ordered" emptyMessage="没有上传的文件记录！">
                        <p:column headerText="文件名称" style="width: 45%;">
                            <p:outputLabel value="#{v.annexName}" />
                        </p:column>
                        <p:column headerText="操作" style="width: 15%;">
                            <p:commandLink ajax="false" value="下载"  immediate="true" process="@this"
								onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
								<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
								<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
								<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
							</p:commandLink>
                            <p:spacer width="5" />
                            <p:commandLink value="删除" update=":tabView:editForm:downId"
                                           partialSubmit="true" process="@this"
                                           action="#{tdInfoMainBean.deleteDiskFile}">
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                <f:setPropertyActionListener target="#{tdInfoMainBean.tdInfoAnnex}" value="#{v}"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                    <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId"
                              resizable="false">
                        <p:fileUpload requiredMessage="请选择要文件上传！"
                                      fileUploadListener="#{tdInfoMainBean.handleFileUpload}"
                                      fileLimit="5" fileLimitMessage="最多只能上传5个文件！" label="选择文件"
                                      invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
                                      style="width:600px;" previewWidth="120" cancelLabel="取消"
                                      update=":tabView:editForm:downId" uploadLabel="上传"
                                      dragDropSupport="true" mode="advanced" sizeLimit="10485760"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
							  		  allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                                       />
                    </p:dialog>
                </h:panelGroup>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:panelGrid style="width:100%;" id="empGrid">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4"
                              style="text-align:left;padding-left:5px;height: 20px;">
                        <font color="red">*</font>
                        <p:outputLabel value="人员信息" />
                    </p:column>
                </p:row>
            </f:facet>

 				<p:row>
                <p:column style="text-align:left;padding-left:50px;width: 5%;height: 27px;">
                    <p:outputLabel value="全部单位" />
                </p:column>
                <p:column style="text-align:left;padding-left:20px;width: 20%">
                 <p:selectBooleanCheckbox value="#{tdInfoMainBean.ifAll}" >
                		 <p:ajax event="change" listener="#{tdInfoMainBean.changeAll}" process="@this,:tabView:editForm:empGrid"  update=":tabView:editForm:empGrid"/>
                </p:selectBooleanCheckbox> 
                	<p:outputLabel value="  " />
                	<p:outputLabel value="全选" />
                </p:column>	
                </p:row>
            <c:forEach items="#{tdInfoMainBean.unitUserMap}" var="v1">
            	
                <p:row>
                <p:column style="text-align:left;padding-left:50px;width: 5%;height: 27px;">
                    <p:outputLabel value="#{v1.key}" />
                </p:column>
                <p:column style="text-align:left;padding-left:20px;width: 20%">
                <p:selectBooleanCheckbox  id="unit_#{v1.key}" >
                		 <p:ajax event="change" listener="#{tdInfoMainBean.selectUnit()}" process="@this,selUnitId" update=":tabView:editForm:empGrid"
                             onstart="document.getElementById('tabView:editForm:selUnitId').value =('#{v1.key}_'+document.getElementById('tabView:editForm:unit_#{v1.key}_input').checked)"/>
                </p:selectBooleanCheckbox>
                	<p:outputLabel value="  " />
                	<p:outputLabel value="全选" />
                </p:column>	
                </p:row>
                <c:forEach items="#{v1.value}" var="v">
                    <p:row>
                        <p:column style="text-align:right;padding-right:15px;width: 5%">
                            <p:selectBooleanCheckbox value="#{v.select}" id="office_#{v.officeRid}" >
                                <p:ajax event="change" listener="#{tdInfoMainBean.selectOffice}" process="@this,selOfficeId" update="user_#{v.officeRid}"
                                        onstart="document.getElementById('tabView:editForm:selOfficeId').value = #{v.officeRid}"/>
                            </p:selectBooleanCheckbox>
                            <p:outputLabel value="  " />
                            <p:outputLabel value="#{v.officeName}" />
                        </p:column>
                        <p:column style="text-align:left;padding-left:15px;width: 20%">
                            <p:selectManyCheckbox value="#{v.selUserRids}" id="user_#{v.officeRid}" columns="6" onchange="selectUser(this)">
                                <f:selectItems value="#{v.userInfo}" var="user" itemLabel="#{user.userName}" itemValue="#{user.userRid}">
                                </f:selectItems>
                                <p:ajax event="change" listener="#{tdInfoMainBean.selectOffice}" process="@this,selOfficeId"/>
                            </p:selectManyCheckbox>
                        </p:column>
                    </p:row>
                </c:forEach>
              
            </c:forEach>
        </p:panelGrid>
        <p:blockUI block="empGrid" widgetVar="empBlock" />
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
    </ui:define>
</ui:composition>

