<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:p="http://primefaces.org/ui"  
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" >
<f:view contentType="text/html">
	
    <h:head>
        <title>#{selectMutilUserBean.title}</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
    	<style type="text/css">
    	 html{
    		overflow:hidden;
    	} 
    	 .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 296px;
            width: 390px;
            overflow: hidden;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
        
         .ui-dialog .ui-dialog-content{
         	overflow: hidden;
        }
    	</style>
    </h:head>
    
    <h:body>
        <h:form id="selectForm" style="overflow:hidden">
        	<p:panel  style="width:860px;margin-top:10px;">
		        <table width="100%">
		            <tr>
		                <td width="40" style="text-align: right;padding-right: 3px">地区：</td>
		                <td style="text-align: left;padding-right: 3px">
		                	<zwx:ZoneSingleComp zoneList="#{selectMutilUserBean.zoneList}" zoneCode="#{selectMutilUserBean.userZoneCode}"
		                		zoneName="#{selectMutilUserBean.userZoneName}" id="editZone" onchange="onNodeSelect()" zoneType="#{selectMutilUserBean.userZoneLevel}"/>
		                	<p:remoteCommand name="onNodeSelect" action="#{selectMutilUserBean.onNodeSelect}" process="@this,editZone,userPickList"
		                                 update=":selectForm:unitListOneMenu,:selectForm:userPickList"/>  
		                </td>
		                <td width="40" style="text-align: right;padding-right: 3px">单位：</td>
		                <td style="text-align: left;padding-right: 3px">
		                    <p:selectOneMenu id="unitListOneMenu" value="#{selectMutilUserBean.userUnitId}" style="width: 200px;">
		                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
		                        <f:selectItems value="#{selectMutilUserBean.unitMap}"/>
		                        <p:ajax event="change" update="userPickList,officeListOneMenu" process="@this,userPickList" listener="#{selectMutilUserBean.onUnitChange}"/>
		                    </p:selectOneMenu>
		                </td>
		                <td width="40" style="text-align: right;padding-right: 3px">科室：</td>
		                <td style="text-align: left;padding-right: 3px">
		                    <p:selectOneMenu id="officeListOneMenu" value="#{selectMutilUserBean.userUnitOfficeId}" style="width: 160px;">
		                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
		                        <f:selectItems value="#{selectMutilUserBean.unitOfficeMap}"/>
		                        <p:ajax event="change" update="userPickList" process="@this,userPickList" listener="#{selectMutilUserBean.onofficeChange}"/>
		                    </p:selectOneMenu>
		                </td>
		            </tr>
		        </table>
		        <p:pickList id="userPickList" value="#{selectMutilUserBean.userDualListModel}" var="user"
		                    itemValue="#{user}" itemLabel="#{user.username}" converter="system.UserConvert"
		                    showSourceControls="false" showTargetControls="false" showCheckbox="true"
		                    showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
		                    effect="drop">
		
		            <f:facet name="sourceCaption">可选用户</f:facet>
		            <f:facet name="targetCaption">已选用户</f:facet>
		
		            <p:column style="width:40%;text-align: left;">#{user.username}</p:column>
		            <p:column style="width:60%;text-align: left;">#{selectMutilUserBean.getOfficeName(user.rid)}</p:column>
		        </p:pickList>
		
		        <f:facet name="footer">
		            <h:panelGrid style="width: 100%;text-align: center;">
		                <h:panelGroup>
		                    <p:commandButton value="保存" icon="ui-icon-check" id="userSaveBtn" action="#{selectMutilUserBean.selectAction}" process="@this,userPickList" />
		                    <p:spacer width="5" />
		                    <p:commandButton value="取消" icon="ui-icon-close" id="userBackBtn" action="#{selectMutilUserBean.dialogClose}" process="@this"/>
		                </h:panelGroup>
		            </h:panelGrid>
		        </f:facet>
		    </p:panel>
        </h:form>
    </h:body>
</f:view>
</html>
