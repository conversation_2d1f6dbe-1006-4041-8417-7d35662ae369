<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
<!-- 托管Bean -->
<!--@elvariable id="mgrbean" type="com.chis.modules.system.web.RoleNewBean"-->
<ui:param name="mgrbean" value="#{roleNewBean}"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <style type="text/css">
        .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 340px;
            width: 360px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[

        //保存的oncomplete
        function handleRoleSave(xhr, status, args) {
            if(!args.validationFailed) {
                 PF('RoleEditDialog').hide();
            }
        }
        //]]>
    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="角色管理"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
			<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update="dataTable" process="@this,searchRoleName,searchRoleType" />
			<p:spacer width="5" />
			<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" update="roleEditDialog" process="@this" action="#{mgrbean.addInitAction}" oncomplete="PF('RoleEditDialog').show()">
               	<p:resetInput target="roleEditDialog"/>
           	</p:commandButton>
		</p:outputPanel>
</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchRoleName" value="角色名称：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;width: 200px;">
            <p:inputText id="searchRoleName" value="#{mgrbean.searchRoleName}" maxlength="10"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel value="角色类型：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <zwx:SimpleCodeManyComp selectedIds="#{mgrbean.roleTypeRids}" id="searchRoleType"
                                    simpleCodeList="#{mgrbean.roleTypeList}"
                                    height="200" panelWidth="200" inputWidth="165"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="角色名称" style="width: 280px;padding-left: 3px;">
        <h:outputText value="#{itm.roleName}" />
    </p:column>
    <p:column headerText="角色类型" style="width: 150px;padding-left: 3px;text-align: center">
        <h:outputText value="#{itm.typeName}" />
    </p:column>
    <p:column headerText="仅超管可见角色" style="width: 150px;padding-left: 3px;text-align: center" rendered="#{mgrbean.ifAdmin}">
        <h:outputText value="#{itm.ifSuperManageRole=='1'?'是':'否'}" />
    </p:column>
    <p:column headerText="角色编码" style="width: 150px;padding-left: 3px;text-align: center">
        <h:outputText value="#{itm.roleCode}" />
    </p:column>
    <p:column headerText="角色描述" style="width: 400px;padding-left: 3px;">
        <h:outputText value="#{itm.roleDesc}" />
    </p:column>
    <p:column headerText="操作" style="padding-left: 3px;">
        <p:commandLink value="修改" action="#{mgrbean.modInitAction}" update=":mainForm:roleEditDialog"
                       oncomplete="PF('RoleEditDialog').show()" process="@this"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}">
            <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
            <p:resetInput target=":mainForm:roleEditDialog"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"/>
        <p:commandLink value="菜单分配" action="#{mgrbean.roleMenuEdit}" immediate="true"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"
                        process="@this">
            <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"/>
        <p:commandLink value="删除" action="#{mgrbean.beforDelAction}" update="dataTable" process="@this"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}">
            <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
       
    </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
    <!--删除-->
    <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="ConfirmDialog">
        <p:commandButton value="确定" action="#{mgrbean.deleteAction}" icon="ui-icon-check"
                         update="dataTable"  process="@this"
                         oncomplete="PF('ConfirmDialog').hide();"/>
        <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('ConfirmDialog').hide();"
                         type="button"/>
    </p:confirmDialog>
    <!-- 新增、修改角色 -->
    <p:dialog id="roleEditDialog" header="角色维护" widgetVar="RoleEditDialog" resizable="false" width="500" height="300!important" >
        <p:panelGrid style="width:100%;" id="roleEditGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height:40px">
                    <font color="red">*</font>
                    <h:outputLabel for="roleName" value="角色名称：" />
                </p:column>
                <p:column style="text-align:left;padding-left:15px;">
                    <p:inputText id="roleName" value="#{mgrbean.tsRole.roleName}" maxlength="10"/>
                </p:column>
            </p:row>
            <p:row>
	            <p:column style="text-align:right;padding-right:3px;width:220px;">
	            	<font color="red">*</font>
	                <h:outputText value="角色类型："/>
	            </p:column>
	            <p:column style="text-align:left;padding-left:8px;">
	               <p:selectOneRadio value="#{mgrbean.type}"  id="roleType" columns="3" style="width:280px;height:42px"  layout="grid">
						<f:selectItems value="#{mgrbean.roleTypeList}" var="itm" 
						itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"></f:selectItems>
					</p:selectOneRadio>
	            </p:column>
	        </p:row>
            <p:row rendered="#{mgrbean.ifAdmin}">
				<p:column style="text-align:right;padding-right:3px;height:40px">
						<font color="red">*</font>
                     <h:outputLabel value="仅超管可见角色："/>
                 </p:column>
                 <p:column style="text-align:left;padding-left: 8px">
                     <p:selectOneRadio id="ifSee" value="#{mgrbean.tsRole.ifSuperManageRole}" style="width: 100px;">
                         <f:selectItem itemLabel="否" itemValue="0"/>
                         <f:selectItem itemLabel="是" itemValue="1"/>
                     </p:selectOneRadio>
                  </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height:40px">
                    <h:outputLabel for="roleCode" value="角色编码：" />
                </p:column>
                <p:column style="text-align:left;padding-left:15px;">
                    <p:inputText id="roleCode" value="#{mgrbean.tsRole.roleCode}" maxlength="50"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel for="roleDesc" value="角色描述：" />
                </p:column>
                <p:column style="text-align:left;padding-left:15px;">
                    <p:inputTextarea id="roleDesc" value="#{mgrbean.tsRole.roleDesc}" 
                    			  style="width:270px"	maxlength="50" rows="4" autoResize="false"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-disk" id="saveBtn" action="#{mgrbean.saveAction}" process="@this,roleEditGrid"
                                     update="dataTable"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('RoleEditDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!-- 菜单授权 -->
    <p:dialog id="menuDialog" header="菜单授权" widgetVar="MenuDialog" resizable="false" width="350" height="400" modal="true">

        <p:tree value="#{mgrbean.treeNode}" var="node" selectionMode="checkbox" selection="#{mgrbean.selectedNodes}" id="menuTree"
                style="width: 320px;height: 390px;overflow-y: auto;">
            <p:treeNode>
                <h:outputText value="#{node.menuCn}" />
            </p:treeNode>
        </p:tree>

        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="menuSaveBtn" action="#{mgrbean.fpMenuAction}" process="@this,menuTree" oncomplete="PF('MenuDialog').hide();"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="menuBackBtn" onclick="PF('MenuDialog').hide();" immediate="true"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

    
</ui:define>

</ui:composition>











