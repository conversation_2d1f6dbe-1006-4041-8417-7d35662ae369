<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
     <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tsProbExamtypeBean}" />
    	<ui:define name="insertScripts">
		<script type="text/javascript">
			//<![CDATA[
	
			//]]>
		</script>
		 <h:outputScript name="js/calendar_zh.js"/>
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
	</ui:define>
    <!-- 标题 -->
   <ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="问卷题目类型维护"/>
        </p:column>
    </p:row>
 </ui:define>  
  
<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
			<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" update="dataTable"  action="#{tsProbExamtypeBean.searchAction}" process="@this,searchTypeName"/>
			<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"  process="@this" update="factoryDailogs"
							 oncomplete="PF('FactoryDailogs').show()" action="#{tsProbExamtypeBean.addInit}">
           	</p:commandButton>
		</p:outputPanel>
</ui:define>    

<!-- 查询条件-->
<ui:define name="insertSearchConditons">
	<p:row>
		<p:column style="text-align:right;padding-right:3px;width:120px;">
            <h:outputLabel  value="类型名称：" />
        </p:column>
        <p:column style="text-align:left;padding-right:8px;">
           <p:inputText id="searchTypeName" value="#{tsProbExamtypeBean.searchTypeName}" style="width: 150px;" maxlength="40"></p:inputText>
        </p:column>
	</p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
        <p:column headerText="编码" style="padding-left: 5px;width:80px;text-align: center">
            <h:outputText value="#{itm.typeCode}" />
        </p:column>
        <p:column headerText="类型名称" style="width:300px;padding-left: #{itm.levelNum*15}px;">
            <h:outputText  value="#{itm.typeName}" />
        </p:column>
        <p:column headerText="备注" style="width: 400px; padding-left: 5px;text-align: center">
            <h:outputText value="#{itm.rmk}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 5px;">
          <p:spacer width="5" />
            <p:commandLink value="添加子节点" process="@this" oncomplete="PF('FactoryDailogs').show()" 
            				 action="#{tsProbExamtypeBean.add}" update=":mainForm:factoryGrid">
            				 <f:setPropertyActionListener target="#{tsProbExamtypeBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
          <p:spacer width="5" />
            <p:commandLink value="修改" process="@this" oncomplete="PF('FactoryDailogs').show()" 
            				 action="#{tsProbExamtypeBean.modInit}" update=":mainForm:factoryGrid">
             	<f:setPropertyActionListener target="#{tsProbExamtypeBean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
             <p:spacer width="5" />
                <p:commandLink value="删除" process="@this" 
            				 action="#{tsProbExamtypeBean.delete}" update="dataTable">
            				  <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
             	<f:setPropertyActionListener target="#{tsProbExamtypeBean.tsProbExamtype}" value="#{itm}"/>
            </p:commandLink>
       </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
	<p:dialog id="factoryDailogs" header="问卷题目类型维护" widgetVar="FactoryDailogs" resizable="false" width="400" height="110" modal="true">
		<p:panelGrid style="width:100%;" id="factoryGrid">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
					<font color="red">*</font>
                    <h:outputLabel value="编码：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 100px;">
				  <p:inputText value="#{tsProbExamtypeBean.tsProbExamtype.typeCode}" maxlength="20"
				 onkeyup="SYSTEM.clearNoNum(this);"></p:inputText>
                 </p:column>
           </p:row>
           <p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
					<font color="red">*</font>
                    <h:outputLabel value="类型名称：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 300px;">
				  <p:inputText value="#{tsProbExamtypeBean.tsProbExamtype.typeName}" maxlength="20"/>
                 </p:column>
           </p:row>
           <p:row>
                <p:column style="text-align:right;padding-right:3px;">
                   <h:outputLabel value="备注："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
				  <p:inputText value="#{tsProbExamtypeBean.tsProbExamtype.rmk}" maxlength="100"/>
                 </p:column>
            </p:row>  
		</p:panelGrid>
		<f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check"  id="saveBtn" process="@this,factoryGrid"
                                     update="dataTable"  action="#{tsProbExamtypeBean.saveAction}"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('FactoryDailogs').hide();" process="@this" immediate="true"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
	</p:dialog>
</ui:define>
                
</ui:composition>
