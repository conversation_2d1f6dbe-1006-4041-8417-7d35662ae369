<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.system.web.UserNewBean"-->
    <ui:param name="mgrbean" value="#{userNewBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/userNewEdit.xhtml"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <script>
            function downloadFileClick() {
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            };
        </script>
        <style type="text/css">
            .ui-picklist .ui-picklist-list{
                text-align:left;
                height: 350px;
                width: 340px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用户管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" update=":tabView:mainForm:dataTable"
					process="@this,searchZone,searchUnitListOneMenu,searchUsername,searchUserNo,:tabView:mainForm" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this" onclick="hideTooltips();">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
                <p:commandButton value="导出" icon="ui-icon-document" process="@this"
                                 action="#{mgrbean.preExport()}" rendered="#{mgrbean.ifShowExportBtn}" />
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document"
                                 ajax="false" process="@this"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:40px" >
                <h:outputText value="行政区划所属地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;width: 250px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}"  zoneCodeNew="#{mgrbean.searchZoneCode}" zoneName="#{mgrbean.searchZoneName}"
                	id="searchZone" onchange="onSearchNodeSelect()"  zoneType="#{mgrbean.searchZoneType}"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{mgrbean.onSearchNodeSelect}" process="@this,searchZone"
                                 update=":tabView:mainForm:searchUnitListOneMenu"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="单位：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px;">
                <p:selectOneMenu id="searchUnitListOneMenu" value="#{mgrbean.searchUnitId}" filter="true" filterMatchMode="contains" 
                				style="width: 190px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.searchUnitMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="用户姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="searchUsername" value="#{mgrbean.searchUsername}" maxlength="15"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 40px;">
                <h:outputText value="用户账号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px;">
                <p:inputText id="searchUserNo" value="#{mgrbean.searchUserNo}" maxlength="15"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 40px;">
                <h:outputText value="角色名称：" />
            </p:column>
            <p:column style="text-align:left;width: 250px;">
                <zwx:SimpleCodeManyComp id="roleNameSelectId" codeName="#{mgrbean.selectOnRoleNames}"
                                        selectedIds="#{mgrbean.selectOnRoleIds}"
                                        simpleCodeList="#{mgrbean.tsSimpleCodes}" ifTree = "true"
                                        height="380"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 40px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.searchUserState}" id="searchUserState" >
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="行政区划所属地区" style="width: 180px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="单位" style="padding-left: 3px;width: 220px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="用户姓名" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="用户账号" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="角色类型" style="width: 160px;text-align: center;">
            <h:outputText value="#{itm[20]}" />
        </p:column>
        <p:column headerText="角色名称" style="width: 260px;">
            <h:outputText id="roleName" value="#{itm[19]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="roleName" style="max-width:450px;">
                <p:outputLabel value="#{itm[19]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="状态" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[5]=='1'?'启用':'停用'}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;">
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" update=":tabView" process="@this"  resetValues="true" onclick="hideTooltips();">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <p:resetInput target=":tabView:editForm:editGrid"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{mgrbean.ifDel}"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}" update="dataTable" process="@this"
            rendered="#{mgrbean.ifDel}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="停用" action="#{mgrbean.stopAction}" update="dataTable" process="@this" rendered="#{itm[5]=='1'}">
                <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:commandLink value="启用" action="#{mgrbean.startAction}" update="dataTable" process="@this" rendered="#{itm[5]!='1'}">
                <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
            <p:spacer width="5" />
        </p:column>
    </ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherMainContents">
       
    </ui:define>

</ui:composition>











