<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tdPublishNoticeListBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/tdPublishNoticeEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/system/tdPublishNoticeView.xhtml"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
        <ui:param name="onfocus" value="false" />
        <script type="text/javascript">
           function direTop(){
                // 页面回到顶部
                document.body.scrollTop=document.documentElement.scrollTop=0;
            };
           function generateClick(){
               document.getElementById("tabView:mainForm:generateReportId").click();
           }
        </script>
        <style type="text/css">
            .myCalendar1 input{
                width: 80px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知发布"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="6" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 update="dataTable" process="@this,mainGrid"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}" update=":tabView" process="@this">
                </p:commandButton>
                <p:commandButton style="display: none;"  id="generateReportId" ajax="false" icon="ui-icon-print"
                                 process="@this" action="#{mgrbean.finishDownloadDelCacheFile}"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);"  >
                    <p:fileDownload value="#{mgrbean.noticeUnitAnnexStreamedContent}" />
                </p:commandButton>
            </h:panelGrid>

        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height:38px;">
                <h:outputText value="发布类型：" />
            </p:column>
            <p:column style="text-align:left;width:240px;">
                <p:outputPanel id="zonearea">
                    <div style="border-color: #ffffff;margin: 0px;padding: 0px;display: table-cell;" >
                        <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectTypeNames}"
		                        selectedIds="#{mgrbean.selectTypeIds}"
		                        simpleCodeList="#{mgrbean.typeList}"
		                        panelWidth="190" ifTree = "true"
		                        height="300"></zwx:SimpleCodeManyComp>
                    </div>
                </p:outputPanel>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="标题：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;width: 240px;" >
            	<p:inputText value="#{mgrbean.searchTitle}" style="width: 180px;" maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="发布日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;">
            	<zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchBdate}" endDate="#{mgrbean.searchEdate}"/>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;height: 38px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:9px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                	<f:selectItem itemValue="0" itemLabel="待发布"></f:selectItem>
                	<f:selectItem itemValue="1" itemLabel="已发布"></f:selectItem>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="发布类型" style="text-align:center;width: 180px;">
            <h:outputText value="#{itm[1]}" />
            <h:outputText value="（#{itm[2]}）" rendered="#{itm[2]!=null}"/>
        </p:column>
    	<p:column headerText="标题" style="width: 320px;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="发布日期" style="text-align:center;width: 100px;">
            <h:outputLabel value="#{itm[4]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="查阅进度" style="width: 240px;">
        	<p:progressBar value="#{itm[8]}" labelTemplate="#{itm[5]}/#{itm[6]}" displayOnly="true" rendered="#{itm[7]==1}"/>
        </p:column>
        <p:column headerText="状态" style="width:80px;text-align:center;">
            <h:outputLabel value="待发布" rendered="#{itm[7]==0}"/>
            <h:outputLabel value="已发布" rendered="#{itm[7]==1}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:spacer width="5"/>
            <p:commandLink value="修改" rendered="#{itm[7]==0}" action="#{mgrbean.modInitAction}" process="@this"  update=":tabView" resetValues="true">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:commandLink value="详情"  rendered="#{itm[7]==1}"  action="#{mgrbean.viewInitAction}" process="@this"  update=":tabView" resetValues="true" onclick="direTop();">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" rendered="#{itm[7]==0}" action="#{mgrbean.deleteAction}" process="@this" update=":tabView:mainForm:dataTable">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            </p:commandLink>
            <p:commandLink value="取消发布" rendered="#{itm[7]==1 and itm[5]==0}" action="#{mgrbean.cancelAction}" process="@this" update=":tabView:mainForm:dataTable">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
                <p:confirm header="消息确认框" message="确定要取消发布吗？" icon="ui-icon-alert"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{itm[7]==1 and itm[5] ne 0 and itm[10]==1}" />
            <p:commandLink value="下载回执附件" rendered="#{itm[7]==1 and itm[5] ne 0 and itm[10]==1}"
                           process="@this" action="#{mgrbean.preDownloadNoticeUnitAnnex}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

</ui:composition>
