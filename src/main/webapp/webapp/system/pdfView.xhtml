<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	</h:head>

	<h:body >
		<h:form id="mainForm">
			<h:outputStylesheet name="css/default.css" />
			<h:outputStylesheet name="css/ui-tabs.css" />
		<style type="text/css">
		.ui-datalist-item {
			height: 50px;
			border-bottom: dotted 1px #b2b2b2;
			margin-bottom: 10px;
		} 
		
		#mainForm\:rightLay .ui-layout-unit-content {
			overflow-y:hidden !important; 
		}
		
		#mainForm\:dataList_content {
			border: none !important; 
		}
		</style>			
			<script type="text/javascript">
				//<![CDATA[
				window.onload = function() {
					showPDFJs();
				}
				
				function showPDFJs() {
					var dynHtml = document.getElementById("mainForm:pdfhtml").value;
					document.getElementById("pdfCol").innerHTML = dynHtml;
					jQuery("#mainForm\\:rightLay").find("span[class*='ui-layout-unit-header-title']").each(function() {
						jQuery(this).html(jQuery("#mainForm\\:fileName").val())
					});
				}
				//]]>
			</script>
			<p:layout id="contentLay" fullPage="true">
				<p:layoutUnit position="west" size="300" resizable="true" header="文件列表" id="leftLay">
				 	<p:dataList value="#{pdfViewBean.pdfList}" var="itm" id="dataList"
				 		type="ordered" itemType="disc" paginator="false" styleClass="paginated">
				        <p:commandLink update=":mainForm:buttonsPanel" oncomplete="showPDFJs()" title="点击浏览" 
				        	process="@this" action="#{pdfViewBean.viewAction}" value="#{itm.fileName}">
				            <f:setPropertyActionListener value="#{itm}" target="#{pdfViewBean.filePo}" />
				        </p:commandLink>
				    </p:dataList>					
				</p:layoutUnit>
				<p:layoutUnit position="center" header="#{pdfViewBean.filePo.fileName}" id="rightLay">
					<p:outputPanel id="buttonsPanel">
						<p:outputPanel styleClass="zwx_toobar_h_auto">
							<span class="ui-separator"
								style="float: left;margin: 7px 3px auto 5px;"><span
								class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="上一个" icon="ui-icon-arrowthick-1-n"
								process="@this" update="buttonsPanel" oncomplete="showPDFJs()"
								disabled="#{(pdfViewBean.totalPdfPage gt 0 and pdfViewBean.currPdfPage gt 1)?false:true}"
								action="#{pdfViewBean.upPdf}" />
							<p:spacer width="5" />
							<p:commandButton value="下一个" icon="ui-icon-arrowthick-1-s"
								process="@this" update="buttonsPanel" oncomplete="showPDFJs()"
								disabled="#{(pdfViewBean.totalPdfPage gt 0 and pdfViewBean.currPdfPage lt pdfViewBean.totalPdfPage)?false:true}"
								action="#{pdfViewBean.downPdf}" />
							<p:spacer width="5" />
							<p:outputLabel value="当前数：" />
							<p:outputLabel value="#{pdfViewBean.currPdfPage}" />
							<p:spacer width="5" />
							<p:outputLabel value="文件数：" />
							<p:outputLabel value="#{pdfViewBean.totalPdfPage}" />
						</p:outputPanel>
						<h:inputHidden id="pdfhtml" value="#{pdfViewBean.pdfhtml}" />
						<h:inputHidden id="fileName" value="#{pdfViewBean.filePo.fileName}" />
					</p:outputPanel>
					<div id="pdfCol" style="height: 100%;"></div>					
				</p:layoutUnit>
			</p:layout>					
		</h:form>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>






