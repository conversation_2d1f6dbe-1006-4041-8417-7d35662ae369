<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

	<!-- 脚本 -->
	<ui:define name="insertScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<style type="text/css">
.ui-selectmanycheckbox.ui-widget td,.ui-selectoneradio.ui-widget td {
	border: 0 none;
	padding: 2px;
}
</style>
	</ui:define>
	<ui:param name="onfocus" value="false"></ui:param>
	<!-- 标题栏 -->
	<ui:define name="insertTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="报表查询" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
					action="#{tsRptBean.searchAction}" update="dataTable"
					process="@this,mainGrid" />
				<p:commandButton value="新增" icon="ui-icon-plus" id="codeAddBtn"
					action="#{tsRptBean.addAction}" process="@this"
					update=":mainForm:codeEditDialog"
					oncomplete="PF('CodeTypeEditDialog').show()">
					<p:resetInput target=":mainForm:codeEditDialog" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

	<!-- 查询条件 -->
	<ui:define name="insertSearchConditons">
		<p:row>
			<p:column style="width:200px;text-align: right;padding-right: 10px;">
				<p:outputLabel value="系统类型：" />
			</p:column>
			<p:column>
				<p:selectOneMenu value="#{tsRptBean.tempCn}" id="types">
					<f:selectItems value="#{tsRptBean.systemTypeMap}" />
				</p:selectOneMenu>
			</p:column>
			<p:column style="width:200px;text-align: right;padding-right: 10px;">
				<p:outputLabel value="报表名称：" />
			</p:column>
			<p:column>
				<p:inputText value="#{tsRptBean.searchRptName}" />
			</p:column>

		</p:row>

	</ui:define>

	<!-- 具体内容 -->
	<ui:define name="insertContent">
		<!--多个业务模块的参数-->
			<p:dataTable var="obj" value="#{tsRptBean.tsRptList}"
				paginator="true" rows="#{tsRptBean.pageSize}" paginatorPosition="bottom"
				rowIndexVar="index"
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
				rowsPerPageTemplate="#{tsRptBean.perPageSize}" id="dataTable" lazy="true"
				emptyMessage="没有您要找的记录！"
				currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
				rendered="#{dataTable==null}">
				<p:columnGroup type="header">
					<p:row>
						<p:column width="30px" headerText="序号" />
						<p:column width="70px" headerText="系统类型" />
						<p:column width="70px" headerText="报表编码" />
						<p:column width="200px" headerText="报表名称" />
						<p:column width="180px" headerText="报表模板地址" />
						<p:column width="50px" headerText="报表版本" />
						<p:column headerText="备注" />
						<p:column headerText="操作" width="100px" />
					</p:row>
				</p:columnGroup>
				<p:column style="text-align: center">
					<p:outputLabel value="#{index+1}" />
				</p:column>
				<p:column style="text-align: center">
					<p:outputLabel value="#{obj.systemType.typeCN}" />
				</p:column>
				<p:column style="padding-left: 10px;">
					<p:outputLabel value="#{obj.rptCod}" />
				</p:column>
				<p:column style="padding-left: 10px;">
					<p:outputLabel value="#{obj.rptnam}" />
				</p:column>
				<p:column style="padding-left: 10px;">
					<p:outputLabel value="#{obj.rptpath}" />
				</p:column>
				<p:column style="text-align: center;">
					<p:outputLabel value="#{obj.rptver}" />
				</p:column>
				<p:column style="padding-left: 10px;">
					<p:outputLabel value="#{obj.rmk}" />
				</p:column>
				<p:column style="padding-left: 10px;">
					<p:commandLink value="设计" update=":mainForm"
						action="#{tsRptBean.designAction()}" process="@this">
						<f:setPropertyActionListener target="#{tsRptBean.editPo}"
							value="#{obj}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="修改" action="#{tsRptBean.modAction}"
						update=":mainForm:codeEditDialog"
						oncomplete="PF('CodeTypeEditDialog').show()" process="@this">
						<f:setPropertyActionListener target="#{tsRptBean.rptId}"
							value="#{obj.rid}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="删除" action="#{tsRptBean.delAction}"
						update="dataTable" process="@this">
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{tsRptBean.rptId}"
							value="#{obj.rid}" />
					</p:commandLink>
					<p:spacer width="5" />
				</p:column>
			</p:dataTable>

		<!-- 新增、修改报表查询 -->
			<p:dialog id="codeEditDialog" header="报表维护"
				widgetVar="CodeTypeEditDialog" resizable="false" width="600"
				height="200" modal="true">
				<p:panelGrid style="width:100%;" id="codeEditGrid">
					<p:row>
						<p:column
							style="text-align:right;padding-right:3px;width:30%;height:27px;">
							<font color="red">*</font>
							<h:outputText value="系统类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:selectOneMenu value="#{tsRptBean.systemTypeEdit}"
								required="true" requiredMessage="系统类型不允许为空！"
								style="width: 180px;">
								<f:selectItems value="#{tsRptBean.systemTypeMap}" />
							</p:selectOneMenu>
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:3px;width:30%;height:27px;">
							<font color="red">*</font>
							<h:outputText value="报表编码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							TZ_ <p:inputText value="#{tsRptBean.addTsRpt.rptCod}"
								required="true" requiredMessage="报表编码不允许为空！" maxlength="25" />
						</p:column>
					</p:row>

					<p:row>
						<p:column
							style="text-align:right;padding-right:3px;width:30%;height:27px;">
							<font color="red">*</font>
							<h:outputText value="报表名称：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{tsRptBean.addTsRpt.rptnam}" maxlength="100"
								required="true" requiredMessage="报表名称不允许为空！" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:30%;">
							<font color="red">*</font>
							<h:outputText value="报表模版地址：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{tsRptBean.addTsRpt.rptpath}"
								maxlength="250" required="true" requiredMessage="报表模版地址不允许为空！" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:30%;">
							<font color="red">*</font>
							<h:outputText value="备注：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{tsRptBean.addTsRpt.rmk}" maxlength="100"
								required="true" requiredMessage="备注不允许为空！" />
						</p:column>
					</p:row>

				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn"
								action="#{tsRptBean.saveAction}" process="@this,codeEditGrid" />
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn"
								onclick="PF('CodeTypeEditDialog').hide();" immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>

			<ui:include src="/WEB-INF/templates/system/frpt.xhtml">
				<ui:param name="printBackingBean" value="#{tsRptBean}" />
			</ui:include>
			<p:remoteCommand  name="updateFastReport" process="@this,mainGrid" action="#{tsRptBean.updateFastReport}"
							 update="dataTable" />
			<ui:include src="/WEB-INF/templates/system/frpt2.xhtml">
				<ui:param name="updateId" value=":mainForm" />
				<ui:param name="printBackingBean" value="#{tsRptBean}" />
			</ui:include>
	</ui:define>

</ui:composition>

