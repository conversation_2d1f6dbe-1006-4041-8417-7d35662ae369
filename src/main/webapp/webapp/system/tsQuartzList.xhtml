<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

<!-- 脚本 -->
<ui:define name="insertScripts">
    <!--引入中文日期-->
    <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="定时任务"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
    <p:outputPanel styleClass="zwx_toobar_42">
        <h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
            <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{tsQuartzBean.searchAction}" update="dataTable"
                             process="@this,mainGrid" />
        </h:panelGrid>
    </p:outputPanel>
</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:180px;">
            <h:outputText value="系统类型：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;width: 250px;">
            <p:selectOneMenu id="searchSystemType" value="#{tsQuartzBean.searchSystemType}" style="width: 180px;">
                <f:selectItem itemLabel="--全部--" itemValue=""/>
                <f:selectItems value="#{tsQuartzBean.systemTypeMap}"/>
            </p:selectOneMenu>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:180px;">
            <h:outputText value="任务描述：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <p:inputText id="searchBsDesc" value="#{tsQuartzBean.searchBsDesc}" maxlength="20"/>
        </p:column>
    </p:row>

</ui:define>

<!-- 具体内容 -->
<ui:define name="insertContent">
<!-- 系统自动编号 -->
<p:dataTable var="itm" value="#{tsQuartzBean.dataList}" id="dataTable" emptyMessage="没有您要找的记录！" >
    <p:column headerText="系统类型" style="width: 120px;text-align:center;">
        <h:outputText value="#{itm.systemType.typeCN}" />
    </p:column>
    <p:column headerText="任务编码" style="width: 100px;text-align:center;">
        <h:outputText value="#{itm.taskCode}" />
    </p:column>
    <p:column headerText="任务描述" style="width: 350px;padding-left: 3px;">
        <h:outputText value="#{itm.taskDescr}" />
    </p:column>
    <p:column headerText="执行周期" style="width: 250px;padding-left: 3px; ">
        <h:outputText value="#{itm.periodDesc}" />
    </p:column>
    <p:column headerText="状态" style="width: 80px;text-align:center;">
        <h:outputText value="#{itm.ifReveal=='1'?'启动中':'暂停'}" />
    </p:column>
    <p:column headerText="操作">
        <p:commandLink value="暂停" update=":mainForm:dataTable" rendered="#{itm.ifReveal=='1'}"
                       process="@this" action="#{tsQuartzBean.stopTask}">
            <p:confirm header="消息确认框" message="确定要暂停吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{tsQuartzBean.tsQuartz}" value="#{itm}"/>
        </p:commandLink>
        <p:commandLink value="修改" update=":mainForm:codeEditDialog" oncomplete="PF('CodeEditDialog').show()"
                       process="@this" resetValues="true" action="#{tsQuartzBean.modInit}" rendered="#{itm.ifReveal=='0'}">
            <f:setPropertyActionListener target="#{tsQuartzBean.tsQuartz}" value="#{itm}"/>
        </p:commandLink>
        <p:spacer width="5"/>
        <p:commandLink value="启动" update=":mainForm:dataTable" rendered="#{itm.ifReveal=='0'}"
                       process="@this" action="#{tsQuartzBean.startTask}">
            <p:confirm header="消息确认框" message="确定要启动吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{tsQuartzBean.tsQuartz}" value="#{itm}"/>
        </p:commandLink>
    </p:column>
    <f:facet name="footer">
        <p:outputPanel style="text-align: center;">
            <h:outputText value="查询到#{tsQuartzBean.dataList.size()}条记录"/>
        </p:outputPanel>
    </f:facet>
</p:dataTable>

<!-- 新增、修改码表 -->
<p:dialog id="codeEditDialog" header="定时任务配置" widgetVar="CodeEditDialog" resizable="false" width="700" height="150" modal="true">
    <p:panelGrid style="width:100%;" id="codeEditGrid">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:80px;height: 30px;">
                <h:outputText value="任务编码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <h:outputText value="#{tsQuartzBean.tsQuartz.taskCode}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:80px;height: 30px;">
                <h:outputText value="任务描述："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <h:outputText value="#{tsQuartzBean.tsQuartz.taskDescr}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:80px;height: 30px;">
                <h:outputText value="执行周期："/><div style="vertical-align: middle;"></div>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;vertical-align: middle;">
                <div style="display: table-cell;vertical-align: middle;">
                    <p:selectOneMenu id="period" value="#{tsQuartzBean.periodtype}" >
                        <f:selectItem itemLabel="分" itemValue="0"/>
                        <f:selectItem itemLabel="时" itemValue="1"/>
                        <f:selectItem itemLabel="日" itemValue="2"/>
                        <f:selectItem itemLabel="周" itemValue="3"/>
                        <f:selectItem itemLabel="月" itemValue="4"/>
                        <p:ajax event="change" process="@this,@parent" update="periodPanel"/>
                    </p:selectOneMenu>
                </div>
                <div style="display: table-cell;vertical-align: middle;">
                    <p:outputPanel id="periodPanel">
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="："/>
                        </div>

                        <div style="display: table-cell;vertical-align: middle;">
                            <!-- 几分钟执行一次 -->
                            <h:outputText value="间隔" rendered="#{tsQuartzBean.periodtype=='0'}"/>
                            <p:inputText value="#{tsQuartzBean.tsQuartz.intervalMin}" rendered="#{tsQuartzBean.periodtype=='0'}"
                                         size="6" maxlength="5" required="true" requiredMessage="请填写间隔分钟!"
                                         converterMessage="请输入正确的间隔分钟"/>
                            <h:outputText value="分钟执行一次" rendered="#{tsQuartzBean.periodtype=='0'}"/>
                        </div>


                        <div style="display: table-cell;vertical-align: middle;">
                            <!-- 几点钟执行一次 -->
                            <h:outputText value="间隔" rendered="#{tsQuartzBean.periodtype=='1'}"/>
                            <p:inputText value="#{tsQuartzBean.tsQuartz.intervalHour}" rendered="#{tsQuartzBean.periodtype=='1'}"
                                         size="6" maxlength="5" required="true" requiredMessage="请填写间隔小时！"
                                         converterMessage="请输入正确的间隔小时！"/>
                            <h:outputText value="小时执行一次" rendered="#{tsQuartzBean.periodtype=='1'}"/>
                        </div>

                        <!-- 每天几点几分执行一次 -->
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="每天" rendered="#{tsQuartzBean.periodtype=='2'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.hourOfDay}"
                                             rendered="#{tsQuartzBean.periodtype=='2'}">
                                <c:forEach begin="0" end="23" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="点" rendered="#{tsQuartzBean.periodtype=='2'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.minOfDay}" rendered="#{tsQuartzBean.periodtype=='2'}">
                                <c:forEach begin="0" end="59" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx}" itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="分开始执行" rendered="#{tsQuartzBean.periodtype=='2'}"/>
                        </div>

                        <!-- 每周几的几点几分执行一次 -->
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="每星期" rendered="#{tsQuartzBean.periodtype=='3'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.dayOfWeek}" rendered="#{tsQuartzBean.periodtype=='3'}">
                                <f:selectItem itemLabel="一" itemValue="1"/>
                                <f:selectItem itemLabel="二" itemValue="2"/>
                                <f:selectItem itemLabel="三" itemValue="3"/>
                                <f:selectItem itemLabel="四" itemValue="4"/>
                                <f:selectItem itemLabel="五" itemValue="5"/>
                                <f:selectItem itemLabel="六" itemValue="6"/>
                                <f:selectItem itemLabel="日" itemValue="7"/>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.hourOfDay}" rendered="#{tsQuartzBean.periodtype=='3'}">
                                <c:forEach begin="0" end="23" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="点" rendered="#{tsQuartzBean.periodtype=='3'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.minOfDay}" rendered="#{tsQuartzBean.periodtype=='3'}">
                                <c:forEach begin="0" end="59" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx}" itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="分开始执行" rendered="#{tsQuartzBean.periodtype=='3'}"/>
                        </div>

                        <!-- 每月几号的几点几分执行一次 -->
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="每月" rendered="#{tsQuartzBean.periodtype=='4'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.dayOfMon}" rendered="#{tsQuartzBean.periodtype=='4'}">
                                <c:forEach begin="1" end="31" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="日" rendered="#{tsQuartzBean.periodtype=='4'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.hourOfDay}" rendered="#{tsQuartzBean.periodtype=='4'}">
                                <c:forEach begin="0" end="23" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx} " itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="点" rendered="#{tsQuartzBean.periodtype=='4'}"/>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <p:selectOneMenu value="#{tsQuartzBean.tsQuartz.minOfDay}" rendered="#{tsQuartzBean.periodtype=='4'}">
                                <c:forEach begin="0" end="59" step="1" var="inx">
                                    <f:selectItem itemLabel="#{inx lt 10?('0'.concat(inx)):inx}" itemValue="#{inx}"/>
                                </c:forEach>
                            </p:selectOneMenu>
                        </div>
                        <div style="display: table-cell;vertical-align: middle;">
                            <h:outputText value="分开始执行" rendered="#{tsQuartzBean.periodtype=='4'}"/>
                        </div>
                    </p:outputPanel>
                </div>
            </p:column>
        </p:row>
    </p:panelGrid>
    <f:facet name="footer">
        <h:panelGrid style="width: 100%;text-align: center;">
            <h:panelGroup>
                <p:commandButton value="保存" icon="ui-icon-check" id="ruleSaveBtn" action="#{tsQuartzBean.saveAction}"
                                 process="@this,codeEditGrid" update="dataTable,codeEditGrid"/>
                <p:spacer width="5" />
                <p:commandButton value="取消" icon="ui-icon-close" id="ruleBackBtn" onclick="PF('CodeEditDialog').hide();" immediate="true" type="button"/>
            </h:panelGroup>
        </h:panelGrid>
    </f:facet>
</p:dialog>
</ui:define>

</ui:composition>











