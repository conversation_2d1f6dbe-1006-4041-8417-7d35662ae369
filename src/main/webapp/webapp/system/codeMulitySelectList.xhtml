<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	  xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body   onload="document.getElementById('codeForm:pym').focus();">
    <title>#{codeMulitySelectListBean.titleName}选择</title>
    <h:outputStylesheet name="css/default.css"/>
	<ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	<style type="text/css" >
	</style>
	<h:outputStylesheet name="css/ui-tabs.css"/>
        <h:form id="codeForm">
        	<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
		        <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
					<span class="ui-separator"><span
		                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
		            <p:commandButton value="确定" icon="ui-icon-check"
		                             action="#{codeMulitySelectListBean.submitAction}"
		                             process="@this,selectedIndusTable"/>
		            <p:commandButton value="全部选择" icon="ui-icon-check"
									 update="selectedIndusTable"
		                             action="#{codeMulitySelectListBean.showAllSelAction}"
		                             process="@this" rendered="#{codeMulitySelectListBean.showAllSel}"/>
		             <p:commandButton value="取消" icon="ui-icon-close"
		                             action="#{codeMulitySelectListBean.dialogClose}" process="@this"/>
		        </h:panelGrid>
		        <p:outputPanel rendered="#{!codeMulitySelectListBean.selectSameLevel and codeMulitySelectListBean.selectNum!=null}"
		        	style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
			        <h:outputText value="提示：" style="color:red;position: relative;"></h:outputText>
					<h:outputText value="最多只支持选择#{codeMulitySelectListBean.selectNum}个类别" style="color:blue;position: relative;"></h:outputText>
		        </p:outputPanel>
		        <p:outputPanel rendered="#{codeMulitySelectListBean.selectSameLevel and codeMulitySelectListBean.selectNum==null}"
		        	style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
			        <h:outputText value="提示：" style="color:red;position: relative;"></h:outputText>
					<h:outputText value="只支持选择同级类别" style="color:blue;position: relative;"></h:outputText>
		        </p:outputPanel>
		        <p:outputPanel rendered="#{codeMulitySelectListBean.selectSameLevel and codeMulitySelectListBean.selectNum!=null}"
		        	style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
			        <h:outputText value="提示：" style="color:red;position: relative;"></h:outputText>
					<h:outputText value="最多只支持选择#{codeMulitySelectListBean.selectNum}个同级类别" style="color:blue;position: relative;"></h:outputText>
		        </p:outputPanel>
				<p:outputPanel rendered="#{codeMulitySelectListBean.ifMainDust !=null and '1'.equals(codeMulitySelectListBean.ifMainDust)}"
							   style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
					<h:outputText value="提示：" style="color:red;position: relative;"></h:outputText>
					<h:outputText value="红色字体标识的为主要粉尘，只能选择一种！" style="color:blue;position: relative;"></h:outputText>
				</p:outputPanel>
				<p:outputPanel rendered="#{codeMulitySelectListBean.ifShowExpiryDate !=null and '1'.equals(codeMulitySelectListBean.ifShowExpiryDate)}"
							   style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
					<h:outputText value="账号有效期：" style="position: relative;"></h:outputText>
					<zwx:CalendarDynamicLimitComp startDate="#{codeMulitySelectListBean.validBDate}"
												  endDate="#{codeMulitySelectListBean.validEDate}" ifNotMaxDate="true"/>
				</p:outputPanel>
		    </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="10" id="searchPanel">
                            <p:outputLabel value="#{codeMulitySelectListBean.titleName}大类：" styleClass="zwx_dialog_font" rendered="#{codeMulitySelectListBean.ifShowFirstCode}"/>
								<zwx:SimpleCodeManyComp selectedIds="#{codeMulitySelectListBean.selectIds}"
														simpleCodeList="#{codeMulitySelectListBean.firstList}"
														height="200" panelWidth="200" inputWidth="120" onchange="onSearchSelect()" clientWidth="23" clearWidth="23"
														rendered="#{codeMulitySelectListBean.ifShowFirstCode}" />
								<p:remoteCommand name="onSearchSelect" action="#{codeMulitySelectListBean.searchAction}"
												 process="@this,searchPanel" update="selectedIndusTable"/>
                            <p:spacer width="5"/>
                            <p:outputLabel value="#{codeMulitySelectListBean.searchName==null?'名称/拼音码':codeMulitySelectListBean.searchName}：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{codeMulitySelectListBean.searchNamOrPy}" style="width: 120px;height: 18px;margin: 2px 0 3px 0;" maxlength="20">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{codeMulitySelectListBean.searchAction}"/>
                            </p:inputText>
                            <p:spacer width="5" rendered="#{codeMulitySelectListBean.showSelectCodeNo}"/>
                            <p:outputLabel value="编码：" styleClass="zwx_dialog_font"
										   rendered="#{codeMulitySelectListBean.showSelectCodeNo}"/>
                            <p:inputText id="codeNo" value="#{codeMulitySelectListBean.searchCodeNo}"
										 style="width: 120px;height: 18px;margin: 2px 0 3px 0;" maxlength="20"
										 rendered="#{codeMulitySelectListBean.showSelectCodeNo}">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel"
										listener="#{codeMulitySelectListBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm"   value="#{codeMulitySelectListBean.displayList}" id="selectedIndusTable"
						 rowsPerPageTemplate="#{'10,20,50'}"  pageLinks="5"
                         paginator="true" rows="10" emptyMessage="没有数据！"
                         paginatorPosition="bottom"
						 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
                <p:column headerText="选择" style="text-align:center;width:30px;">
                	<p:selectBooleanCheckbox value="#{itm.ifSelected}" disabled="#{itm.ifDisabled}">
                		<p:ajax event="change" listener="#{codeMulitySelectListBean.selectAction(itm)}" process="@this" update="selectedIndusTable" />
                	</p:selectBooleanCheckbox>
                </p:column>
				<p:column headerText="编码" style="padding-left: 3px;width:100px;"
						  rendered="#{codeMulitySelectListBean.showSelectCodeNo}">
					<h:outputText escape="false"  value="#{itm.codeNo}"/>
				</p:column>
                <p:column headerText="#{codeMulitySelectListBean.colName==null?'名称':codeMulitySelectListBean.colName}" style="padding-left: 3px;">
                    <h:outputText escape="false"  value="#{(itm.levelIndex == '3') ? '&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '2') ? '&#160;&#160;&#160;&#160;&#160;&#160;' :''}#{(itm.levelIndex == '1') ? '&#160;&#160;&#160;' :''}#{itm.codeName}"
                    	style="#{itm.ifDisabled?('1' eq codeMulitySelectListBean.ifMainDust and '1' eq itm.extendS5?'color:red':'color:gray'):('1' eq codeMulitySelectListBean.ifMainDust and '1' eq itm.extendS5?'color:red':'')};"/>
                </p:column>
				<p:column headerText="#{codeMulitySelectListBean.columnName}" style="padding-left: 3px;text-align: center;" rendered="#{null ne codeMulitySelectListBean.columnName}">
					<h:outputText escape="false"  value="#{itm.encryptStr1}"/>
				</p:column>
            </p:dataTable>
            <br/><br/>
        </h:form>
		<!-- <ui:include src="/WEB-INF/templates/system/focus.xhtml"/> -->
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
    </h:body>
</f:view>
</html>
