<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">

 	<ui:define name="insertEditScripts">
	 	<script>
			function newBlank(){
				window.parent.open("http://api.map.baidu.com/lbsapi/getpoint/index.html");
			}
		</script>
	 </ui:define>
       <!-- 标题栏 -->
       <ui:define name="insertEditTitle">
           <p:row>
               <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
                   <h:outputText value="单位管理"/>
               </p:column>
           </p:row>
       </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{unitBean.saveAction}" process="@this,editGrid" update=":tabView" />
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{unitBean.backAction}" update=":tabView" immediate="true" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertEditContent">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="所属地区："/>
            </p:column>
            <p:column style="text-align:left;">
                <zwx:ZoneSingleNewComp zoneList="#{unitBean.zoneList}" zoneId="#{unitBean.editZoneId}" zoneName="#{unitBean.editZoneName}" />
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unitname" value="#{unitBean.tsUnit.unitname}" maxlength="50"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <font color="red">*</font>
                <h:outputText value="单位简称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unitSimpname" value="#{unitBean.tsUnit.unitSimpname}" maxlength="50"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;height:25px">
                <h:outputText value="单位编码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
            	<h:outputText value="#{unitBean.tsUnit.unitCode==null?'自动生成':unitBean.tsUnit.unitCode}"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="邮编："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unitzip" value="#{unitBean.tsUnit.unitzip}" maxlength="6" size="10"/>
            </p:column>
        </p:row>
		<p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="百度坐标："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
            	<p:outputPanel id="bdPanel">
	            	<h:outputText value="X " style="padding-left:5px"/><p:inputText value="#{unitBean.tsUnit.lng}" maxlength="10"/>
	            	<h:outputText value="Y " style="padding-left:5px"/><p:inputText value="#{unitBean.tsUnit.lat}" maxlength="10"/>
	            	<p:menuButton value="百度地图坐标" style="margin-left:5px">
	            		 	<p:menuitem value="百度地图查询" icon="ui-icon-search" onclick="newBlank()" process="@this"/>
	       				    <p:menuitem value="GPS坐标转换" icon="ui-icon-transferthick-e-w" oncomplete="PF('GpsDialog').show()" 
	       				    	resetValues="true" process="@this" update="gpsDialog"/>
	            	</p:menuButton>	
            	</p:outputPanel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="单位地址："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unitaddr" value="#{unitBean.tsUnit.unitaddr}" maxlength="50" size="50"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="联系电话："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unittel" value="#{unitBean.tsUnit.unittel}" maxlength="20"/>
                <h:outputText value="（格式：0510-85373786）" style="color: red"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="单位传真："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unitfax" value="#{unitBean.tsUnit.unitfax}" maxlength="20"/>
                <h:outputText value="（格式：0510-85373786）" style="color: red"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="单位邮箱："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:inputText id="unitemail" value="#{unitBean.tsUnit.unitemail}" maxlength="60" size="40"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:220px;">
                <h:outputText value="单位属性："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectManyCheckbox id="selectSortList" value="#{unitBean.selectSortList}" layout="grid" columns="5">
                    <f:selectItems value="#{unitBean.bsSortMap}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    
     <ui:define name="insertOtherContents">
     <p:dialog width="400" resizable="false" modal="true" id="gpsDialog"
			widgetVar="GpsDialog" header="GPS坐标转换" minHeight="200">
			<p:panelGrid style="width:100%" id="gpsGrid">
				<p:row>
					<p:column style="width:100px;text-align:right;padding-right:3px;">
						<p:outputLabel value="东经："/>
					</p:column>
					<p:column>
						<p:inputText size="6" value="#{unitBean.degreeLong}" maxlength="6" 
							required="true" requiredMessage="东经数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">°</font>
						<p:inputText size="6" value="#{unitBean.minuteLong}" maxlength="6"
							required="true" requiredMessage="东经数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">′</font>
						<p:inputText size="6" value="#{unitBean.secondLong}" maxlength="6"
							required="true" requiredMessage="东经数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">″</font>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="width:100px;text-align:right;padding-right:3px;">
						<p:outputLabel value="北纬："/>
					</p:column>
					<p:column>
						<p:inputText size="6" value="#{unitBean.degreeLat}" maxlength="6"
							required="true" requiredMessage="北纬数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">°</font>
						<p:inputText size="6" value="#{unitBean.minuteLat}"
							required="true" requiredMessage="北纬数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">′</font>
						<p:inputText size="6" value="#{unitBean.secondLat}"
							required="true" requiredMessage="北纬数据不能为空！"
							onblur="SYSTEM.verifyNum(this,9,2)" onkeyup="SYSTEM.verifyNum(this,9,2)"/> <font size="5">″</font>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
    			<h:panelGrid style="width: 100%;text-align: center;">
	                <h:panelGroup>
	                    <p:commandButton value="转换" icon="ui-icon-disk" action="#{unitBean.convertLatOrLon}"
	                    	 process="@this,gpsGrid" update="bdPanel"/>
	                    <p:spacer width="5" />
	                    <p:commandButton value="关闭" icon="ui-icon-close" process="@this" oncomplete="PF('GpsDialog').hide()" />
	                </h:panelGroup>
	            </h:panelGrid>
    		</f:facet>
		</p:dialog>
	</ui:define>
</ui:composition>

