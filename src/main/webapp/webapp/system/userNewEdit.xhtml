<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
       <!-- 编辑页面的script -->
    <ui:define name="insertEditScripts">
    	<ui:param name="mgrbean" value="#{userNewBean}"/>
        <h:outputScript library="js" name="namespace.js" />
	    <h:outputScript name="js/validate/system/validate.js" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
            
        </script>

    </ui:define>

       <!-- 标题栏 -->
       <ui:define name="insertEditTitle">
           <p:row>
               <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                   <h:outputText value="用户管理"/>
               </p:column>
           </p:row>
       </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-disk" id="saveBtn" action="#{mgrbean.saveAction}"
                                 process="@this,:tabView:editForm:other1,:tabView:editForm:other2" update=":tabView"/>
				<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}"
                                 update=":tabView" immediate="true" />
				<p:commandButton value="密码初始化" icon="ui-icon-check" id="pwdInit" action="#{mgrbean.pwdInitAction}"
                                 process="@this" rendered="#{mgrbean.rid != null}">
					<p:confirm header="消息确认框" message="确定要密码初始化吗？" icon="ui-icon-alert" />
				</p:commandButton>
			</h:panelGrid>
			<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;" rendered="#{mgrbean.rid != null}">
            	<h:outputLabel value="提示：" style="color:red;"></h:outputLabel>
             	<h:outputLabel value="初始化密码为：#{mgrbean.initPassword}" style="color:blue;"></h:outputLabel>
            </p:outputPanel>
		</p:outputPanel>
	</ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertOtherContents">
      <p:fieldset legend="基本信息"  style="margin-top: 5px;margin-bottom: 5px;">
      <p:remoteCommand name="findFlowByIdc" action="#{mgrbean.findFlowByIdc}" process="@this,:tabView:editForm:other1,:tabView:editForm:other2" update=":tabView">
			</p:remoteCommand>
       <p:panelGrid style="width:100%;" id="other1">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;height:40px">
                <font color="red">*</font>
                <h:outputText value="行政区划所属地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:0px;border-right:none;width:180px;">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" zoneId="#{mgrbean.editZoneId}"  zoneCode="#{mgrbean.editZoneCode}" zoneName="#{mgrbean.editZoneName}"
                                       id="editZone" onchange="onNodeSelect()"  zoneType="#{mgrbean.editZoneType}"/>
                <p:remoteCommand name="onNodeSelect" action="#{mgrbean.onNodeSelect}" process="@this,editZone"
                                 update=":tabView:editForm:editUnitListOneMenu"/>
            </p:column>
            <p:column style="text-align:left;border-left:none">
            	<h:outputText value="提示：" style="color: red"/>
            	<h:outputText value="必须选择到区县及以下" style="color: blue"/>
            </p:column>
        </p:row>
        <p:row>  
            <p:column style="text-align:right;padding-right:3px;height:40px">
                <font color="red">*</font>
                <h:outputText value="单位："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:selectOneMenu id="editUnitListOneMenu" value="#{mgrbean.editUnitId}" style="width: 190px;" filter="true" filterMatchMode="contains">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.editUnitMap}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:40px">
                <font color="red">*</font>
                <h:outputText value="用户账号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:inputText id="userNo" value="#{mgrbean.tsUserInfo.userNo}" maxlength="15"  style="width: 180px"/>
            </p:column>
        </p:row>
        <p:row>    
            <p:column style="text-align:right;padding-right:3px;height:40px">
                <font color="red">*</font>
                <h:outputText value="用户姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:inputText id="username" value="#{mgrbean.tsUserInfo.username}" maxlength="15" style="width: 180px"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:40px">
                <font color="red">*</font>
                <h:outputText value="手机号码："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:inputText id="mbNum" value="#{mgrbean.tsUserInfo.mbNum}" maxlength="13" style="width: 180px" 
                				onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" />
            </p:column>
         </p:row>
        <p:row>   
            <p:column style="text-align:right;padding-right:3px;height:40px">
            	<font color="red">*</font>
                <h:outputLabel for="idc" value="身份证：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:inputText id="idc" value="#{mgrbean.tsUserInfo.idc}" maxlength="18"  onblur="findFlowByIdc();" style="width: 180px"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:40px">
                <font color="red">*</font>
                <h:outputText value="账号有效期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;" colspan="2">
                <p:calendar id="validBegDate" value="#{mgrbean.tsUserInfo.validBegDate}" size="15" navigator="true" yearRange="c-100:c" readonly="true"
                          style="pointer-events:none"   converterMessage="账号有效期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                       ~
                <p:calendar id="validEndDate" value="#{mgrbean.tsUserInfo.validEndDate}" size="15" navigator="true" yearRange="c-5:c+20" mindate="new Date()"
               			   readonlyInput="true"  converterMessage="账号有效期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            </p:column>
        </p:row>
        </p:panelGrid>
      </p:fieldset>
      <p:fieldset legend="角色授权" toggleable="true" toggleSpeed="500">  
        <p:panelGrid style="width:100%;" id="other2" >
		 <p:row>
            <p:column style="text-align:left;padding-left:3px;height: 25px;border-color:transparent">
                 <p:panelGrid id="roleObjTable" style="width:100%;">
                   <f:facet name="header">
                 	<p:row>
                 		<p:column  style="padding-left:3px;width: 200px;text-align:center">
                 			<p:outputLabel value="角色类型"/>
                 		</p:column>
                 		<p:column  style="padding-left:3px;text-align:center">
                 			<p:outputLabel value="角色名称"/>
                 		</p:column>
                 	</p:row>
                 	</f:facet>
                 	<c:forEach items="#{mgrbean.roleTypeList}" var="roleType">
                 	 <p:row>
                 		<p:column>
                 			<table style="height:100%">
                    		<tr>
                    			<td style="width:24px;padding:0;border:0px;">
                    				<p:selectBooleanCheckbox value="#{roleType.ifSelected}" disabled="#{roleType.disabled}">
			                            <p:ajax event="change" listener="#{mgrbean.roleChangeAction}" process="@this,@parent,roleObjTable" update="roleObjTable"/>
			                        </p:selectBooleanCheckbox>
                    			</td>
                    			<td style="padding:0;border:0px;">
                    				#{roleType.codeName}
                    			</td>
                    			</tr>
                    		</table>
                 		</p:column>
                 		<p:column>
                 			<p:selectManyCheckbox columns="5" layout="grid" value="#{roleType.itemSubs}">
                            <f:selectItems value="#{roleType.rolePoList}" var="sub" itemValue="#{sub.rid}" itemLabel="#{sub.roleName}" 
                            				itemDisabled="#{sub.disabled}"/>
                        	</p:selectManyCheckbox>
                 		</p:column>
                 	  </p:row>	
                 	</c:forEach>	
                 </p:panelGrid>            
            </p:column>
        </p:row>
        </p:panelGrid>
       </p:fieldset> 
    </ui:define>
    
</ui:composition>

