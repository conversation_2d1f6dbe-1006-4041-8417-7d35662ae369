<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
                
      <!-- 没有查询条件 -->          
     <ui:param name="condition" value="1"/>
                
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="基本信息" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 编辑页面的内容-->
    <ui:define name="insertContent">
        <p:panelGrid style="width:100%;height:100%;" id="viewGrid">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:13%;height: 30px;">
                <p:outputLabel value="标题：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{tdMsgViewBean.tdInfoMain.infoTitle}"
                               size="100" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:5px;width:13%;">
                <p:outputLabel value="内容：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:inputTextarea rows="10" cols="120" autoResize="false"
                                 readonly="true" value="#{tdMsgViewBean.tdInfoMain.infoContent}" />
                <h:outputText id="display" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:13%;">
                <p:outputLabel value="附件：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:dataTable value="#{tdMsgViewBean.annexList}" var="v"
                             style="width:60%" type="ordered" emptyMessage="没有上传的文件记录！">
                    <p:column headerText="文件名称" style="width: 45%;">
                        <p:outputLabel value="#{v.annexName}" />
                    </p:column>
                    <p:column headerText="操作" style="width: 15%;">
                        <p:commandLink ajax="false" value="下载" immediate="true" process="@this"
							onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
							<f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{v.annexAddr}"/>
							<f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{v.annexName}"/>
							<p:fileDownload value="#{downLoadPreBean.streamedContent}" ></p:fileDownload>		
						</p:commandLink>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:13%;height: 30px;">
                <p:outputLabel value="发起人：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;width:100px;">
                <p:outputLabel
                        value="#{tdMsgViewBean.tdInfoMain.tsUserInfo.username}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 100px;">
                <p:outputLabel value="发起时间：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:outputLabel value="#{tdMsgViewBean.tdInfoMain.publishTime}">
                    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" timeZone="GMT+8"
                                       locale="cn" />
                </p:outputLabel>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 30px;">
                <p:outputLabel value="操作：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="3">
             	<p:outputLabel value="已阅" rendered="#{tdMsgViewBean.state != 0}"/>
             	<p:commandLink value="已阅" id="yy" rendered="#{tdMsgViewBean.state == 0}" action="#{tdMsgViewBean.editSubState}" update="viewGrid"
					process="@this,viewGrid">
					<f:setPropertyActionListener value="1" target="#{tdMsgViewBean.state}" />
					<p:confirm header="消息确认框" message="确定要已阅吗？" icon="ui-icon-alert" />
				</p:commandLink>
				<p:spacer width="5" rendered="#{tdMsgViewBean.state == 0}" />
				<p:commandLink value="收藏" id="sc" rendered="#{tdMsgViewBean.state == 0}" action="#{tdMsgViewBean.editSubState}" update="viewGrid"
					process="@this,viewGrid">
					<f:setPropertyActionListener value="2" target="#{tdMsgViewBean.state}" />
					<p:confirm header="消息确认框" message="确定要收藏吗？" icon="ui-icon-alert" />
				</p:commandLink> 
            </p:column>
           
        </p:row>
        </p:panelGrid>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
    </ui:define>
    
</ui:composition>

