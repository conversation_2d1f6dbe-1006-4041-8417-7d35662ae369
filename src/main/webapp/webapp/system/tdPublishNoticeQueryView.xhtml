<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <ui:param name="onfocus" value="false" />
    <script type="text/javascript">
    </script>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通知查阅详情" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="6"
                         style="border-color:transparent;padding:0px;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView" immediate="true" >
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <!-- 引入通知发布与附件区域 -->
        <ui:include src="tdPublishNoticeViewBase.xhtml">
            <ui:param name="childbean" value="#{mgrbean.noticeListBean}" />
        </ui:include>
        <p:panelGrid  style="margin-bottom: 10px;width:100%" rendered="#{null ne mgrbean.noticeListBean.curPublishNotice and mgrbean.noticeListBean.curPublishNotice.ifNeedFeedback == 1}">
            <f:facet name="header">
                <p:row>
                    <p:column style="height:20px;text-align:left;padding-left: 5px; " >
                        <p:outputLabel value="上传回执附件"/>
                    </p:column>
                </p:row>
            </f:facet>
            <p:row>
                <p:column style="text-align:left;" >
                    <p:commandButton value="上传" icon="ui-icon-plus"  process="@this" oncomplete="PF('FileDialog').show();"
                                     update=":tabView:viewForm:fileDialog"
                                     style="margin:3px 10px;">
                    </p:commandButton>
                    <p:dataTable value="#{mgrbean.noticeUnitAnnexList}" var="itm" id="noticeAnnexDataTable" rows="#{mgrbean.noticeAnnexPageSize}" paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{'10,20,50'}"  emptyMessage="没有您要找的记录！" paginator="true"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
                        <p:column headerText="序号" style="width: 80px;text-align: center;" >
                            <p:outputLabel value="#{R+1}" />
                        </p:column>
                        <p:column headerText="附件名称" style="width: 380px;" >
                            <p:outputLabel value="#{itm.annexName}" />
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="下载" process="@this" immediate="true"
                                           ajax="false" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);" >
                                <f:setPropertyActionListener target="#{downLoadPreBean.filePath}" value="#{itm.annexAddr}"/>
                                <f:setPropertyActionListener target="#{downLoadPreBean.fileName}" value="#{itm.annexName}"/>
                                <p:fileDownload value="#{downLoadPreBean.streamedContent}" />
                            </p:commandLink>
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" action="#{mgrbean.removeNoticeUnitAnnex}" process="@this"
                                           update=":tabView:viewForm:noticeAnnexDataTable">
                                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.curUnitAnnex}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- 附件上传 -->
        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true">
            <table>
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel
                                value="（支持附件格式为：图片、PDF、zip或rar压缩文件）" styleClass="blueColorStyle"
                                style="position: relative;bottom: -6px;padding-right: 30px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload
                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                fileUploadListener="#{mgrbean.fileUpload}" multiple="true"
                                invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                style="width:600px;max-height: 270px;" previewWidth="120" cancelLabel="取消"
                                uploadLabel="上传"
                                dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf,zip,rar类型文件"
                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf|zip|rar)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>
