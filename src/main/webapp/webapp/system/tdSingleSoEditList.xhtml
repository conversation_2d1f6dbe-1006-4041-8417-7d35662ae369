<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html" 
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
<h:head>
</h:head>

<h:body>
	

	<h:form id="mainform">
	<h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	<h:outputStylesheet name="css/ui-tabs.css"/>
		<p:panelGrid style="width:100%;" id="zz">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="第三方系统登录维护" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent; padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" id="saveBtn" icon="ui-icon-check"
					action="#{tdSingleSoBean.saveAction}" process="@this,mainGrid1">
				</p:commandButton>

				<p:commandButton value="返回" id="backBtn" icon="ui-icon-close"
					action="#{tdSingleSoBean.backAction()}" immediate="true" 
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<!-- 第三方系统登录信息 -->
		<p:fieldset legend="第三方系统登录信息" toggleable="true" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:panelGrid style="width:100%;height:100%;" id="mainGrid1">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:180px;height:25px">
						<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="系统名称：" />
					</p:column>
					<p:column style="width:400px;">
						<p:selectOneMenu disabled="false" id="systemMenu" value="#{tdSingleSoBean.tsDsfSys.rid}" style="width: 180px;">
                    		<f:selectItem itemLabel="--请选择--" itemValue=""/>
                    		<f:selectItems value="#{tdSingleSoBean.systemMap}"/>
                    		<p:ajax event="change" update="sysUrl" process="@this,systemMenu" listener="#{tdSingleSoBean.onSystemChange}" />
                	 	</p:selectOneMenu>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:180px;height:25px;" id="address">
						<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="程序地址：" />
					</p:column>
					<p:column style="width:400px;">
						<p:inputText id="sysUrl" value="#{tdSingleSoBean.sysUrl}" style="width:300px;"/>
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:fieldset>

		<!--第三方系统登录参数 -->

		<p:fieldset legend="第三方系统登录参数" toggleable="true" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:panelGrid style="width:100%;height:100%;" id="mainGrid2">
				<p:row>
					<p:column colspan="4">
						<p:commandButton value="添加"  icon="ui-icon-plus"
							oncomplete="PF('Dlg2').show();" update=":mainform:dlg2"  action="#{tdSingleSoBean.sysAdd}"
							process="@this">
							<f:setPropertyActionListener target="#{tdSingleSoBean.tig}" value="1" />
						</p:commandButton>
					</p:column>
				</p:row>
				<p:row>
					<p:column>
						<p:dataTable value="#{tdSingleSoBean.list}" var="item" id="oo"
							emptyMessage="没有您要找的记录！">
							<p:column headerText="参数字段名">
								<h:outputText value="#{item.paramEn}" />
							</p:column>
							<p:column headerText="参数值">
								<h:outputText value="#{item.paramValue}" />
							</p:column>
							<!--参数修改  -->
							<p:column headerText="操作">
								<p:commandLink value="修改" process="@this"
									oncomplete="PF('Dlg2').show();" update=":mainform:dlg2">
									<f:setPropertyActionListener target="#{tdSingleSoBean.tig}" value="2" />

									<f:setPropertyActionListener target="#{tdSingleSoBean.tsDsfLoginfParam}"
										value="#{item}" />
								</p:commandLink>
								<p:spacer width="5" />
								<!--参数删除  -->
								<p:commandLink value="删除" action="#{tdSingleSoBean.delOneMsg}"
									update="oo" process="@this">
									<p:confirm header="消息确认框" message="确定要删除吗？"
										icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{tdSingleSoBean.tsDsfLoginfParam}"
										value="#{item}" />
								</p:commandLink>
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
			</p:panelGrid>
			</p:fieldset>
			<!--第三方系统注册参数表插入  -->
		<p:dialog header="第三方系统注册参数表" widgetVar="Dlg2" 
			resizable="false" width="420" height="150" id="dlg2">
			<p:panelGrid style="width:100%;">
				<p:row>
					<p:column style="text-align:right;">
					<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="参数字段名：" />
					</p:column>
					<p:column>
						<p:inputText value="#{tdSingleSoBean.tsDsfLoginfParam.paramEn}" required="true"
							requiredMessage="参数字段名不能为空" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;">
					<p:outputLabel value="*" style="color:red;" />
						<p:outputLabel value="参数值：" />
					</p:column>
					<p:column>
						<p:inputText value="#{tdSingleSoBean.tsDsfLoginfParam.paramValue}" required="true"
							requiredMessage="参数值不能为空" />
					</p:column>
				</p:row>
	<!-- 按钮操作 -->
				<p:row>
					<p:column colspan="2" style="text-align:center;">
						<p:commandButton value="确定" action="#{tdSingleSoBean.tdspSave}"
							update="oo" process="@this,dlg2"  />

						<p:commandButton type="button" value="关闭"
							onclick="PF('Dlg2').hide();" />
					</p:column>
				</p:row>
			</p:panelGrid>
		</p:dialog>
		
		<p:confirmDialog global="true" showEffect="fade" >
        <p:commandButton value="是" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
        <p:commandButton value="否" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
    </p:confirmDialog>
	</h:form>
</h:body>
</f:view>	
</html>