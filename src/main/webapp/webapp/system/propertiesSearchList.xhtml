<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
	  xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
<h:head>
</h:head>

<h:body>
	<h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	<h:outputStylesheet name="css/ui-tabs.css"/>
	<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
        <f:facet name="header">
            <p:row>
	            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
	                <h:outputText value="配置文件查询"/>
	            </p:column>
	        </p:row>
        </f:facet>
	</p:panelGrid>
	
	<c:forEach items="#{propertiesSearchListBean.panels}" var="panel">
       <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
		   	<f:facet name="header">
		        <p:row>
		            <p:column colspan="6" style="text-align:left;height: 20px;">
		                <p:outputLabel value="#{panel.fileName}"/>
		            </p:column>
		        </p:row>
		    </f:facet>
		    <p:row>
		    	<p:column>
		    		<p:dataTable value="#{panel.propertiesTables}" var="data" emptyMessage="暂无数据！">
		    			<p:column headerText="属性名" style="width:300px;">
		    				<p:outputLabel value="#{data.attrName}"></p:outputLabel>
		    			</p:column>
		    			<p:column headerText="属性值">
		    				<p:outputLabel value="#{data.attrValue}"></p:outputLabel>
		    			</p:column>
		    		</p:dataTable>
		    	</p:column>
	        </p:row>
	    </p:panelGrid>
     </c:forEach>
</h:body>
</f:view>	
</html>