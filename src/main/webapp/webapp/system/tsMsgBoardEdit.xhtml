<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
	<h:head>
	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet name="css/ui-tabs.css" />

		<!-- 托管bean-->
		<ui:param name="mgrbean" value="#{perfectUnitInfoBean}" />

		<script src="#{request.contextPath}/resources/js/namespace.js"
			type="text/javascript">
			
		</script>
		<script
			src="#{request.contextPath}/resources/js/validate/system/validate.js">
			
		</script>
		<script>
			function newBlank() {
				window.parent
						.open("http://api.map.baidu.com/lbsapi/getpoint/index.html");
			}
		</script>
		<!-- 标题栏 -->
		<h:form id="editForm">
			<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
				id="editTitleGrid">
				<f:facet name="header">
					<p:row>
						<p:column colspan="2"
							style="text-align:left;padding-left:5px;height: 20px;">
							<h:outputText value="留言板" />
						</p:column>
					</p:row>
				</f:facet>
			</p:panelGrid>

			<!-- 编辑页面的按钮 -->
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="3"
					style="border-color:transparent;padding:0px;">
					<span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
						action="#{tsMsgBoardBean.saveAction}" process="@this,editGrid"
						update="editForm" />
				</h:panelGrid>
			</p:outputPanel>

			<!-- 编辑页面的内容-->
			<p:panelGrid
				style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;"
				id="editGrid">
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;height:30px;width:300px">
						<font color="red">*</font>
						<h:outputText value="联系人：" />
					</p:column>
					<p:column style="text-align:left;padding-left:12px;">
						<p:inputText id="editLinkMan" maxlength="50"
							value="#{tsMsgBoardBean.editLinkMan}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height:30px">
					<font color="red">*</font>
						<h:outputText value="联系电话：" />
					</p:column>
					<p:column style="text-align:left;padding-left:12px;">
						<p:inputText id="editLinkTel" maxlength="25"
							value="#{tsMsgBoardBean.editLinkTel}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;height:30px">
					<font color="red">*</font>
						<h:outputText value="留言内容：" />
					</p:column>
					<p:column style="text-align:left;padding-left:12px; ">
						<p:inputTextarea maxlength="1000" rows="15" cols="100" id="editFeedback"
							autoResize="false"  value="#{tsMsgBoardBean.editFeedback}" />
					</p:column>
				</p:row>
			</p:panelGrid>
		</h:form>
	    <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
	    <ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
   		<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
   		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
<!-- 带转向、真分页的模板 -->

