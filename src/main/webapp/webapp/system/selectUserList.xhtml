<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>人员选择</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
    </h:head>

    <h:body style="overflow-y:hidden;">
        <h:form id="selectForm">
            <table width="100%">
                <tr>
                    <td width="100" style="text-align: right;padding-right: 3px" class="zwx_dialog_font">科室：</td>
                    <td style="text-align: left;padding-left: 3px;vertical-align: middle;" width="200" >
                        <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                            <p:inputText id="userOfficeName" value="#{selectUserBean.userOfficeName}" readonly="true" style="width: 120px;"/>
                            <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"
                                           style="position: relative;left: -20px;top:0px;" oncomplete="PF('OveralPanel').show()"/>
                            <h:inputHidden id="userOfficeRid" value="#{selectUserBean.userOfficeRid}"/>
                        </h:panelGrid>
                        <p:overlayPanel id="overalPanel" for="userOfficeName" dynamic="false" style="width:280px;" widgetVar="OveralPanel">
                            <p:tree value="#{selectUserBean.userOfficeTreeNode}" var="node"
                                    selectionMode="single" id="choiceTree"
                                    style="width: 250px;height: 300px;overflow-y: auto;">

                                <p:ajax event="select" oncomplete="PF('OveralPanel').hide();" process="@this"
                                        listener="#{selectUserBean.onOfficeNodeSelect}"
                                        update=":selectForm:userOfficeName,:selectForm:userOfficeRid,:selectForm:selectedTable" />

                                <p:treeNode>
                                    <h:outputText value="#{node.officename}" />
                                </p:treeNode>
                            </p:tree>
                        </p:overlayPanel>
                    </td>
                    <td width="140" style="text-align: right;padding-right: 3px" class="zwx_dialog_font">姓名：</td>
                    <td style="text-align: left;padding-left: 3px">
                        <p:inputText id="userName" value="#{selectUserBean.userName}" style="width: 120px;" maxlength="20">
                            <p:ajax event="keyup" update="selectedTable" process="@this,@parent" listener="#{selectUserBean.searchAction}"/>
                        </p:inputText>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm" value="#{selectUserBean.userList}" id="selectedTable" paginator="true" rows="10"
                         emptyMessage="没有数据！" selection="#{selectUserBean.selectedUser}" rowKey="#{itm.rid}">
                <p:column selectionMode="single" style="width:10px;text-align:center"/>
                <p:column headerText="科室" style="padding-left: 3px; ">
                    <h:outputText value="#{itm.officeName}" />
                </p:column>
                <p:column headerText="姓名" style="width: 120px;text-align: center;">
                    <h:outputText value="#{itm.username}" />
                </p:column>
            </p:dataTable>
            <br/>
            <p:outputPanel style="text-align:center">
                <p:commandButton value="确定" icon="ui-icon-check" id="saveBtn" action="#{selectUserBean.selectAction}" process="@form" />
                <p:spacer width="5" />
                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectUserBean.dialogClose}" process="@this"/>
            </p:outputPanel>
            <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
            <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
        </h:form>
    </h:body>
</f:view>
</html>
