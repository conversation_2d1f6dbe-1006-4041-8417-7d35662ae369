<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdDynaFormDesignStatisticsBean}" />
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />

	<!-- 样式或javascripts -->
	<!--引入中文日期-->
	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

	<script type="text/javascript">
		//<![CDATA[
		//]]>
	</script>
	<style type="text/css">
	body {
		overflow-x: hidden;
	}
	</style>


	<!-- 标题栏 -->
	<h:form id="assistForm">
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="editTitleGrid">
            <f:facet name="header">
				<p:row>
					<p:column colspan="6"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="动态表单统计辅助编辑" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<!-- 按钮 -->
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;" id="btnPanel">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
					action="#{mgrbean.forwardEditPage}" update=":tabView"
					process="@this" >
					<p:confirm header="消息确认框" message="确定要取消编辑吗？" icon="ui-icon-alert" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>

		<!-- 其余内容 -->
		<p:wizard flowListener="#{mgrbean.onFlowProcess}" nextLabel="下一步" backLabel="上一步" widgetVar="wizard"
			showNavBar="true" showStepStatus="false" id="wizard" step="#{mgrbean.wizardIndex}">
	        <p:tab id="step_filters" title="字段配置">
	            <p:panel header="字段配置">
	            	<!-- 按钮 -->
					<p:outputPanel styleClass="zwx_toobar_42">
						<h:panelGrid columns="10"
							style="border-color:transparent;padding-top:3px;">
							<span class="ui-separator"><span
								class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="添加" icon="ui-icon-plus" id="filterAddBtn"
								action="#{mgrbean.addFilterField}" update="filterFieldList"
								process="@this,filterFieldList" >
							</p:commandButton>
						</h:panelGrid>
					</p:outputPanel>
	            	<p:dataTable style="margin-top:5px;" value="#{mgrbean.assistFieldList}"
						rowIndexVar="R" id="filterFieldList" var="field" scrollable="false" emptyMessage="没有记录！" scrollWidth="100%">
						<p:column headerText="操作" style="text-align: center;width: 80px;">
							<p:commandLink value="删除" action="#{mgrbean.removeFilterField}" update="filterFieldList" process="@this,filterFieldList">
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
								<f:setPropertyActionListener value="#{field}" target="#{mgrbean.filterField}" />
							</p:commandLink>
						</p:column>
						<p:column headerText="字段信息" style="text-align: center;width: 240px;">
							<p:selectOneMenu value="#{field.fieldSql}" style="width: 180px;">
								<f:selectItems value="#{mgrbean.fieldSelect}" />
							</p:selectOneMenu>
						</p:column>
						<p:column headerText="配置类型" style="text-align: center;width: 240px;">
							<p:selectOneMenu value="#{field.fieldType}" style="width: 180px;">
								<p:ajax event="change" update="filterFieldList" process="@this,filterFieldList"/>
								<f:selectItems value="#{mgrbean.fieldConfigList}" />
							</p:selectOneMenu>
						</p:column>
						<p:column headerText="查询方式" style="text-align: center;width: 180px;">
							<p:selectOneMenu value="#{field.searchType}" style="width: 150px;" id="searchType" disabled="#{!(field.fieldType==1)}" >
								<f:selectItems value="#{mgrbean.searchTypeList}" />
							</p:selectOneMenu>
						</p:column>
						<p:column headerText="聚合方式" style="text-align: center;width: 180px;">
							<p:selectOneMenu value="#{field.convergeType}" style="width: 150px;" id="convergeType" disabled="#{!(field.fieldType==3)}">
								<f:selectItems value="#{mgrbean.convergeTypeList}" />
							</p:selectOneMenu>
						</p:column>
						<p:column headerText="排序方式" style="text-align: center;">
							<p:selectOneMenu value="#{field.orderType}" style="width: 150px;" id="orderType" disabled="#{!(field.fieldType==4)}">
								<f:selectItems value="#{mgrbean.orderTypeList}" />
							</p:selectOneMenu>
						</p:column>
	            	</p:dataTable>
	            </p:panel>
	        </p:tab>
	 
	        <p:tab id="step_sql" title="生成SQL">
	            <p:panel header="生成SQL">
	            	<p:panelGrid style="width:100%;">
						<p:row>
							<p:column
								style="text-align:right;padding-right:8px;height:30px;width:150px;">
								<p:outputLabel value="生成统计SQL：" />
							</p:column>
							<p:column style="text-align:left;padding-left:8px;width:200px;"
								colspan="5">
								<p:inputTextarea id="assistStatisticsSql"
									value="#{mgrbean.statisticsSql}"
									autoResize="false" style="width:98%;" rows="20" />
							</p:column>
						</p:row>
					</p:panelGrid>
	            </p:panel>
	        </p:tab>
	 
	        <p:tab id="step_data" title="数据处理">
	            <p:panel header="数据处理">
	            	<!-- 按钮 -->
					<p:outputPanel styleClass="zwx_toobar_42">
						<h:panelGrid columns="10"
							style="border-color:transparent;padding-top:3px;">
							<span class="ui-separator"><span
								class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="添加" icon="ui-icon-plus" id="shellAddBtn"
								action="#{mgrbean.addShellInit}" update="shellDialog"
								process="@this" oncomplete="PF('shellDialog').show();">
							</p:commandButton>
						</h:panelGrid>
					</p:outputPanel>
	            	<p:panelGrid style="width:100%;margin-top:5px;">
						<p:row>
							<p:column
								style="text-align:right;padding-right:8px;height:30px;width:150px;">
								<p:outputLabel value="统计sql：" />
							</p:column>
							<p:column style="text-align:left;padding-left:8px;">
								<p:outputLabel value="#{mgrbean.statisticsSql}"></p:outputLabel>
							</p:column>
						</p:row>
						<p:row>
							<p:column
								style="text-align:right;padding-right:8px;height:30px;width:150px;">
								<p:outputLabel value="生成统计脚本：" />
							</p:column>
							<p:column style="text-align:left;padding-left:8px;">
								<p:inputTextarea id="assistStatisticsShell"
									value="#{mgrbean.statisticsShell}"
									autoResize="false" style="width:98%;" rows="20" />
							</p:column>
						</p:row>
					</p:panelGrid>
	            </p:panel>
	        </p:tab>
	        
	        <p:tab id="step_title" title="HTML生成设置">
	            <p:panel header="HTML生成设置">
	            	<!-- 按钮 -->
					<p:outputPanel styleClass="zwx_toobar_42">
						<h:panelGrid columns="10"
							style="border-color:transparent;padding-top:3px;">
							<span class="ui-separator"><span
								class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="添加" icon="ui-icon-plus" id="topTitleAddBtn"
								action="#{mgrbean.addTopTitle}" update="topTitles"
								process="@this,topTitles" immediate="true">
							</p:commandButton>
						</h:panelGrid>
					</p:outputPanel>
					
					<p:panelGrid style="margin-top:5px;width:100%;">
					<p:row>
						<p:column style="width:180px;text-align:right;"><p:outputLabel value="统计图配置"/></p:column>
						<p:column>
							<p:selectOneMenu value="#{mgrbean.echartsType}" style="width: 180px;">
					            <f:selectItems value="#{mgrbean.echartsTypeList}" />
					        </p:selectOneMenu>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="width:180px;text-align:right;"><p:outputLabel value="数据表头配置"/></p:column>
						<p:column>
							<p:dataTable  value="#{mgrbean.topTitles}"
								rowIndexVar="R" id="topTitles" var="toptitle" scrollable="false" emptyMessage="没有记录！" scrollWidth="100%">
								<p:column headerText="操作" style="text-align: center;width: 80px;">
									<p:commandLink value="删除" action="#{mgrbean.removeTopTitle}" update="topTitles" 
										process="@this,topTitles" immediate="true">
										<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
										<f:setPropertyActionListener value="#{toptitle}" target="#{mgrbean.topTitlePo}" />
									</p:commandLink>
								</p:column>
								<p:column headerText="表头名称" style="text-align: center;width: 240px;">
									<p:inputText value="#{toptitle.titleName}" required="true" requiredMessage="表头名称不可为空！"></p:inputText>
								</p:column>
								<p:column headerText="是否统计图数据" style="text-align: center;width: 240px;">
									<p:selectBooleanCheckbox value="#{toptitle.isEcharts}" >
										<p:ajax event="change" process="@this" update="echartDataType" ></p:ajax>
									</p:selectBooleanCheckbox>
								</p:column>
								<p:column headerText="统计图数据类型" style="text-align: center;width: 240px;">
									<p:selectOneRadio id="echartDataType" value="#{toptitle.colType}" style="width: 180px;" disabled="#{!toptitle.isEcharts}">
							            <f:selectItem itemLabel="数据" itemValue="0" />
							            <f:selectItem itemLabel="描述" itemValue="1" />
							        </p:selectOneRadio>
								</p:column>
								<p:column headerText="数据索引" style="text-align: center;">
									<p:inputText value="#{toptitle.dataIndex}" onkeyup="SYSTEM.clearNoNum(this)" 
										required="true" requiredMessage="数据索引不可为空！"
										onblur="SYSTEM.clearNoNum(this)" maxlength="2"></p:inputText>
								</p:column>
		            		</p:dataTable>
	            		</p:column>
					</p:row>
	            	</p:panelGrid>
	            </p:panel>
	        </p:tab>
	 
	        <p:tab id="step_html" title="HTML编辑">
	            <p:panel header="HTML编辑">
	            	<!-- 按钮 -->
					<p:outputPanel styleClass="zwx_toobar_42">
						<h:panelGrid columns="10"
							style="border-color:transparent;padding-top:3px;">
							<span class="ui-separator"><span
								class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="确定" icon="ui-icon-plus" id="assistSubmitBtn"
								action="#{mgrbean.assistSubmit}" update=":tabView"
								process="@this,statisticsHtmlCode" >
								<p:confirm header="消息确认框" message="确定覆盖已有配置信息吗？" icon="ui-icon-alert" />
							</p:commandButton>
						</h:panelGrid>
					</p:outputPanel>
	            	<p:panelGrid style="width:100%;margin-top:5px;" id="statisticsHtmlCode">
						<p:row>
							<p:column
								style="text-align:right;padding-right:8px;height:30px;width:150px;">
								<p:outputLabel value="生成HTML：" />
							</p:column>
							<p:column style="text-align:left;padding-left:8px;">
								<p:inputTextarea id="assistStatisticsHtmlCode"
									value="#{mgrbean.statisticsHtmlCode}"
									autoResize="false" style="width:98%;" rows="20" />
							</p:column>
						</p:row>
					</p:panelGrid>
	            </p:panel>
	        </p:tab>
	    </p:wizard>
	    
	    <p:dialog widgetVar="shellDialog" id="shellDialog" width="460"
			height="120" header="动态统计脚本添加" resizable="false">
			<p:panelGrid style="width:100%;" id="themePanel">
				<p:row>
					<p:column style="width:30%;padding-right:8px;text-align:right;">
       					字段一编号：
       				</p:column>
					<p:column>
						<p:inputText value="#{mgrbean.fieldOne}" onkeyup="SYSTEM.clearNoNum(this)" 
							required="true" requiredMessage="字段一编号不可为空！"
							onblur="SYSTEM.clearNoNum(this)" maxlength="2"></p:inputText>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="width:30%;padding-right:8px;text-align:right;">
       					字段二编号：
       				</p:column>
					<p:column>
						<p:inputText value="#{mgrbean.fieldTwo}" onkeyup="SYSTEM.clearNoNum(this)" 
							required="true" requiredMessage="字段二编号不可为空！"
							onblur="SYSTEM.clearNoNum(this)" maxlength="2"></p:inputText>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="width:30%;padding-right:8px;text-align:right;">
       					处理方式：
       				</p:column>
					<p:column>
						<p:selectOneMenu 
							value="#{mgrbean.dealMethod}" style="width:180px;"
							required="true" requiredMessage="处理方式不可为空！">
							<f:selectItems value="#{mgrbean.dealMethods}" />
						</p:selectOneMenu>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check" id="shellSaveBtn"
							action="#{mgrbean.addStatisticsShell}"
							process="@this,shellDialog" onsuccess="PF('shellDialog').hide();"
							update=":tabView:assistForm:assistStatisticsShell"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="shellBackBtn"
							onclick="PF('shellDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	</h:form>
</ui:composition>











