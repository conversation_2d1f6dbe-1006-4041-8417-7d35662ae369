<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
		<title>签名</title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:form id="mainForm">
			<div class="pen-container">
				<object id="zwxPenSign"
					type="application/x-chiscdc-HWPenSign-plugin" class="zwxPenSign">
				</object>
			</div>
			<br />
			<p:outputPanel style="text-align:center">
				<p:spacer width="5" />
				<p:commandButton value="确定" icon="ui-icon-check" id="saveBtn"
					type="button" onclick="penSignSave()" />
				<p:spacer width="5" />
				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
					action="#{penSignBean.dialogClose}" widgetVar="BackBtn"
					process="@this" style="display:none" />
				<p:remoteCommand name="psnSingConfirmAction" process="@form"
					action="#{penSignBean.confirmAction}" />
				<h:inputHidden id="signBinary" value="#{penSignBean.signBinary}" />
			</p:outputPanel>

			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		</h:form>
		<script type="text/javascript">
			//<![CDATA[
			/**
			 * 检查插件是否安装
			 */
			var penSignAble = 0;
			var zwxPenSign = document.getElementById("zwxPenSign");
			function onPenSignLoad() {
				var checkPenSignOcx = zwxPenSign.CheckOcx;
				if (checkPenSignOcx == undefined) {
					penSignAble = 0;
					PF("InstallDialog").show();
				} else if (checkPenSignOcx == "OK") {
					zwxPenSign.ChiscdcHwPenSign;
					penSignAble = 1;
				} else {
					alert(checkPenSignOcx);
					PF("BackBtn").getJQ().click();
				}
			}

			/**
			 * 设备初始化
			 */
			function penSignInit() {
				onPenSignLoad();
				if (penSignAble == 1) {
					zwxPenSign.ChiscdcHwPenSign;
				}
			}

			function penSignSave() {
				document.getElementById("mainForm:signBinary").value = zwxPenSign.FPhotoData;
				psnSingConfirmAction();
			}

			window.onload = function() {
				penSignInit();
			}
			//]]>
		</script>
		<style type="text/css">
body {
	width: 420px;
	text-align: center;
}

.pen-container {
	width: 420px;
	height: 170px;
	margin: 0 auto;
}

.zwxPenSign {
	width: 400px;
	height: 150px;
	margin-top: 10px;
	margin-left: 10px;
	margin-right:10px;
	border: 1px solid;
}

#tipDiv a:link {
	color: blue
}

/* 未被访问的链接     蓝色 */
#tipDiv a:visited {
	color: red
}

/* 已被访问过的链接   蓝色 */
#tipDiv a:hover {
	color: darkorange;
	text-decoration: none;
}

/* 鼠标悬浮在上的链接 蓝色 */
#tipDiv a:active {
	color: blue
}
</style>
		<p:dialog id="installDialog" widgetVar="InstallDialog" header="插件安装"
			width="300" height="120" resizable="false" modal="true">
			<p:outputPanel id="tipDiv" style="text-align: left;margin-top:10px;">
				<a href="/resources/files/HWSetUp.exe"
					style="font-weight: bold;font-size: 12px;">驱动下载 </a>
				<a href="/resources/files/chiscdcHWPenSign.crx"
					style="font-weight: bold;font-size: 12px;">插件下载 </a>
				<p:separator />
				安装步骤<br />
				 1、下载驱动程序并安装<br />2、下载插件并拖入浏览器安装<br />3、刷新浏览器！
			</p:outputPanel>
			<!-- 	<table style="width: 100%">
				<tr>
					<td style="height: 5px;"></td>
				</tr>
				<tr>
					<td style="height: 30px;text-align: left">
						<span style="font-size: large;font-weight: 600">提示信息：</span>
					</td>
				</tr>
				<tr>
					<td style="width: 100%;height: 60px;">
						<span style="font-size: 22px;font-weight: 700;color: blue">请下载插件，将下载的文件拖入到浏览器中，并刷新页面！</span>
					</td>
				</tr>
				<tr>
					<td style="height: 5px;"></td>
				</tr>
				<tr>
					<td style="text-align: center">
						<a onclick="document.getElementById('crxId').click();PF('InstallDialog').hide()"
						   class="button large classic-orange" >插件下载</a>
					</td>
				</tr>
			</table> -->
		</p:dialog>
	</h:body>
</f:view>
</html>
