<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
	<f:view contentType="text/html">
		<h:head>
			<script type="text/javascript" src="/resources/echarts/echarts-all.js"></script>
		    <script type="text/javascript" src="/resources/echarts/macarons.js"></script>
			<script>
				function showTable(src, tableId) {
					var obj = document.getElementById("mainForm:table"+ tableId);
					$(obj).toggle();
					var show = $(obj).css('display');
					if (show == 'table') {
						$(src).parent().css('background-color', '#FFFCDB');
						$(src).parent().css('border', '1px #F9960A solid');
					} else {
						$(src).parent().css('background-color', '#EEEEEE');
						$(src).parent().css('border', '1px #98b3e2 solid');
					}
				}

				function buildChart(src, tableId) {
					var macarons = theme();
					var obj = document.getElementById("chart" + tableId);
					$(obj).toggle();
					var chart1 = echarts.init(obj, macarons);
					var json1 = document.getElementById("mainForm:chartJson"
							+ tableId).value;
					chart1.setOption(eval("(" + json1 + ")"));
					var show = $(obj).css('display');
					if (show == 'block') {
						$(src).parent().css('background-color', '#FFFCDB');
						$(src).parent().css('border', '1px #F9960A solid');
					} else {
						$(src).parent().css('background-color', '#EEEEEE');
						$(src).parent().css('border', '1px #98b3e2 solid');
					}
				}
			</script>
		
			<style>
				#btnDiv ul {
					list-style: none;
				}
				
				#btnDiv ul li {
					border:1px #98b3e2 solid;  
					background: #eeeeee;  
					line-height: 24px; 
					float:left; 
					margin-left: 12px
				}
				
				#btnDiv ul li a, #btnDiv ul li a:link, #btnDiv ul li a:visited, #btnDiv ul li a:active{
					line-height: 25px;
					padding:4px 4px 4px 22px;
					text-decoration: none;
					color: #1f376d;  
					height: 16px;
				}
				
				#btnDiv ul li a:hover {
				    color: #ff9900;
				}
				
				.bgTClass {
					BACKGROUND: url(../../resources/component/quickDesktop/image/juzhenti.gif) no-repeat 4px 4px;
				}
				
				.bgChartClass {
					BACKGROUND: url(../../resources/component/quickDesktop/image/column3d.gif) no-repeat 4px 4px;
				}
			</style>
		</h:head>
		<h:body>
			<h:outputStylesheet library="css" name="default.css" />
			<h:form id="mainForm">
				<div style="margin:0px auto; width:980px;padding:5px 0px">
				<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                    <f:facet name="header">
                   	  <p:row>
	                      <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
				                <h:outputText value="问卷答案数统计"/>
				          </p:column>
			          </p:row>
                    </f:facet>
				</p:panelGrid>	
				<p:outputPanel styleClass="zwx_toobar_42" rendered="false">
					<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
						<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" 
							action="#{slowQueAnalyBean.backAction}"  process="@this" />
					</h:panelGrid>
				</p:outputPanel>
				<p:fieldset style="margin-top:5px; width:950px">
					<c:forEach items="#{slowQueAnalyBean.resultList}" var="itm">
						<p:outputPanel style="margin:5px auto;background-color:#CCEEFF; height:40px;padding-left:10px;"
							rendered="#{itm.topicType == 5}">
							<p:outputLabel value="#{itm.showCode}" style="line-height: 40px; font-weight: bold;"/>
							&#160;
							<p:outputLabel value="#{itm.topicDesc}" style="line-height: 40px;"/>
						</p:outputPanel>
						<p:outputPanel style="margin:5px auto; padding:0px 10px " rendered="#{itm.topicType != 5}">
							<p:outputPanel style="width:100%;margin:5px auto;float:left">
								<p:outputPanel>
									<p:outputLabel value="#{itm.showCode}" style="color:#3d81ee;font-weight: bold;line-height: 25px;"/>
									&#160;
									<p:outputLabel value="#{itm.topicDesc}"/> &#160;
									<p:outputLabel value="[单选题]" style="color:#3d81ee;" rendered="#{itm.topicType == 0}"/>
									<p:outputLabel value="[多选题]" style="color:#3d81ee;" rendered="#{itm.topicType == 1}"/>
									<p:outputLabel value="[滑动条]" style="color:#3d81ee;" rendered="#{itm.topicType == 2}"/>
									<p:outputLabel value="[下拉单选]" style="color:#3d81ee;" rendered="#{itm.topicType == 8}"/>
									<div id="btnDiv" style="float: right">
										<ul>
											<li style="background-color:#FFFCDB;border:1px #F9960A solid">
												<p:commandLink value="表格" process="@this" onclick="showTable(this, '#{itm.topicId}')" styleClass="bgTClass"/>
											</li>
											<li>
												<p:commandLink value="柱状图" process="@this" onclick="buildChart(this, '#{itm.topicId}')" styleClass="bgChartClass"/>
											</li>
										</ul>
									</div>
								</p:outputPanel>
							</p:outputPanel>
							<p:panelGrid style="margin:10px auto; width:100%;" id="table#{itm.topicId}">
								<p:row>
									<p:column style="text-align:center;width:45%;" styleClass="ui-state-default">
										<p:outputLabel value="选项" />
									</p:column>
									<p:column style="width:10%;text-align:center"  styleClass="ui-state-default">
										<p:outputLabel value="小计"/>
									</p:column>
									<p:column style="text-align:center;width:45%"  styleClass="ui-state-default">
										<p:outputLabel value="比例"/>
									</p:column>
								</p:row>
								<c:forEach items="#{itm.optionList}" var="sub">
									<p:row>
										<p:column>
											<p:outputLabel value="#{sub.optionDesc}" rendered="#{sub.optionDesc != null}"/>
											<p:outputLabel value="本题有效填写人次" style="font-weight: bold;" rendered="#{sub.optionDesc == null}"/>
										</p:column>
										<p:column style="text-align:center">
											<p:outputLabel value="#{sub.replyCnt}"/>
										</p:column>
										<p:column>
											<p:progressBar value="#{sub.replyPat}" style="width:60%" labelTemplate="#{sub.replyPat}%" rendered="#{sub.optionDesc != null}"/>
										</p:column>
									</p:row>
								</c:forEach>
							</p:panelGrid>
							<div id="chart#{itm.topicId}" style="width:98%;height:300px;display:none;float: left"></div>
							<h:inputHidden value="#{itm.charJson}" id="chartJson#{itm.topicId}"/>
						</p:outputPanel>
						<p:separator style="height:1px;border:none;border-top:1px dashed #555555;width:100%; margin:10px auto"
							 rendered="#{itm.topicType != 5}"/>
					</c:forEach>
				</p:fieldset>
				</div>
			</h:form>
		</h:body>
	</f:view>
</html>
