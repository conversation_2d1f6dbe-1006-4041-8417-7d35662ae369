<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection_mongo.xhtml">

<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{logBhkServiceCrptBean3}"/>
<ui:param name="editPage" value="/webapp/system/mongo3/tsMongoLogEdit_crptNew.xhtml"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <!--引入中文日期-->
    <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
    <style type="text/css">
        .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 340px;
            width: 360px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
        .ui-custom-zone input {
            width: 200px !important;
        }
        .ui-zwx-calendar input{
            width: 80px !important;
        }
        .log-panelgrid td{
            line-height: 30px;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[


        //]]>
    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="企业日志管理"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto" style="display: flex;align-items: center">
            <p:outputPanel style="flex:1;display: flex;align-items: center">
                <span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{logBhkServiceCrptBean3.searchAction}" update="dataTable,mongoPageInfo" process="@this,:tabView:mainForm:mainGrid" />
                <p:spacer width="5"/>
                <p:commandButton value="导出" ajax="false" icon="ui-icon-document" id="exportBtn" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{logBhkServiceCrptBean3.downloadFile}"/>
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="批量已阅" icon="ui-icon-document" action="#{logBhkServiceCrptBean3.batchUpdateStateAction}" update="dataTable" process="@this,dataTable">
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="显示总记录数" action="#{logBhkServiceCrptBean3.showTotalAction}" update="mongoPageInfo" process="@this">
                </p:commandButton>
                <p:spacer width="10"/>
                <p:outputPanel id="mongoPageInfo">
                    <p:outputLabel value="总记录数：#{logBhkServiceCrptBean3.total}条" style="color:blue;" rendered="#{logBhkServiceCrptBean3.showInfo}"/>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel style="padding-right: 8px;">
                <p:outputLabel value="说明：" style="color:red;"/>
                <p:outputLabel value="日志模块数据量大，只显示前#{logBhkServiceCrptBean3.searcchLimit}条数据。" style="color:blue;"/>
            </p:outputPanel>
		</p:outputPanel>
</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row rendered="#{logBhkServiceCrptBean3.ifAdmin}">
        <p:column style="text-align:right;padding-right:3px;width:120px;height: 18px;">
            <h:outputLabel  value="地区：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;width:250px">
            <div class="ui-custom-zone">
            <zwx:ZoneSingleNewComp zoneList="#{logBhkServiceCrptBean3.zoneList}"  zoneCodeNew="#{logBhkServiceCrptBean3.searchEntity.zone_gb}" zoneName="#{logBhkServiceCrptBean3.searchZoneName}"
                                   id="searchZone" onchange="onSearchNodeSelect()"  zoneType="#{logBhkServiceCrptBean3.searchZoneType}"/>
            <p:remoteCommand name="onSearchNodeSelect" action="#{logBhkServiceCrptBean3.onSearchNodeSelect}" process="@this,searchZone"
                             update="searchUnitListOneMenu"/>
            </div>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:150px">
            <h:outputLabel  value="上传机构：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;width:250px">
            <p:selectOneMenu id="searchUnitListOneMenu" value="#{logBhkServiceCrptBean3.searchEntity.unit_code}" style="width: 210px;" filter="true" filterMatchMode="contains">
                <f:selectItem itemLabel="--请选择--" itemValue=""/>
                <f:selectItems value="#{logBhkServiceCrptBean3.searchUnitMap}"/>
            </p:selectOneMenu>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:120px">
            <h:outputLabel  value="上传日期：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;">
            <div class="ui-zwx-calendar">
            <p:calendar value="#{logBhkServiceCrptBean3.searchEntity.visit_time_start}" size="11" navigator="true" yearRange="c-20:c"
                 readonlyInput="true" maxdate="new Date()"  converterMessage="上传开始日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            ~
            <p:calendar value="#{logBhkServiceCrptBean3.searchEntity.visit_time_end}" size="11" navigator="true" yearRange="c-20:c"
                 readonlyInput="true" maxdate="new Date()"  converterMessage="上传结束日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            </div>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;height: 18px;width:120px;">
            <h:outputLabel  value="企业名称：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;width:250px">
            <p:inputText style="width: 200px;" value="#{logBhkServiceCrptBean3.searchEntity.crpt_name}" maxlength="50" placeholder="精确查询"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:120px">
            <h:outputLabel  value="社会信用代码：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;width:250px">
            <p:inputText style="width:200px;" value="#{logBhkServiceCrptBean3.searchEntity.credit_code}" maxlength="50" placeholder="精确查询"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:120px;">
            <h:outputLabel  value="上传标记：" />
        </p:column>
        <p:column style="text-align:left;padding-left:5px;">
            <p:selectManyCheckbox  value="#{logBhkServiceCrptBean3.uploadTags}">
                <f:selectItem itemValue="1" itemLabel="成功"/>
                <f:selectItem itemValue="0" itemLabel="失败"/>
            </p:selectManyCheckbox>
        </p:column>
    </p:row>
    <p:row>
    	<p:column style="text-align:right;padding-right:3px;width:120px" 
    		rendered="#{!logBhkServiceCrptBean3.ifAdmin}">
            <h:outputLabel  value="上传日期：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;"
        	rendered="#{!logBhkServiceCrptBean3.ifAdmin}">
            <div class="ui-zwx-calendar">
            <p:calendar value="#{logBhkServiceCrptBean3.searchEntity.visit_time_start}" size="11" navigator="true" yearRange="c-20:c"
                 readonlyInput="true" maxdate="new Date()"  converterMessage="上传开始日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            ~
            <p:calendar value="#{logBhkServiceCrptBean3.searchEntity.visit_time_end}" size="11" navigator="true" yearRange="c-20:c"
                 readonlyInput="true" maxdate="new Date()"  converterMessage="上传结束日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            </div>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;height: 18px;">
            <h:outputLabel  value="状态：" />
        </p:column>
        <p:column style="text-align:left;padding-left:5px;" colspan="#{logBhkServiceCrptBean3.ifAdmin?'5':'3'}">
            <p:selectManyCheckbox  value="#{logBhkServiceCrptBean3.dealStates}">
                <f:selectItem itemValue="1" itemLabel="已阅"/>
                <f:selectItem itemValue="0" itemLabel="未阅"/>
            </p:selectManyCheckbox>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="地区" style="width: 150px;padding-left: 3px;text-align: center">
        <h:outputText value="#{logBhkServiceCrptBean3.uploadZoneMap.get(itm.zone_gb)}" />
    </p:column>
    <p:column headerText="上传机构名称" style="width: 250px;padding-left: 3px;">
        <h:outputText value="#{logBhkServiceCrptBean3.uploadUnitMap.get(itm.unit_code)}" />
    </p:column>
    <p:column headerText="企业名称" style="width: 250px;padding-left: 3px;">
        <h:outputText value="#{itm.crpt_name}" />
    </p:column>
    <p:column headerText="社会信用代码" style="width: 150px;padding-left: 3px;text-align: center;">
        <h:outputText value="#{itm.credit_code}" />
    </p:column>
    <p:column headerText="上传时间" style="width: 150px;padding-left: 3px;text-align: center;">
        <h:outputText value="#{itm.visit_time}">
            <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn" />
        </h:outputText>
    </p:column>
    <p:column headerText="上传标记" style="width: 80px;padding-left: 3px;text-align: center;">
        <h:outputText value="失败" rendered="#{itm.upload_tag=='0'}"/>
        <h:outputText value="成功" rendered="#{itm.upload_tag=='1'}"/>
    </p:column>
    <p:column headerText="错误原因" style="width: 150px;padding-left: 3px;text-align: center;">
        <h:outputText value="——" rendered="#{itm.upload_tag=='1'}"/>
        <h:outputLink id="R_#{R}"  value="#">
            <h:outputText value="#{itm.error_msg_temp}" />
            <p:tooltip  for="@parent" value="#{itm.error_msg}" style="width: 300px;"
                        showEffect="clip" hideEffect="explode" position="left" rendered="#{itm.upload_tag=='0'}"/>
        </h:outputLink>
    </p:column>
    <p:column headerText="状态" style="width: 80px;padding-left: 3px;text-align: center;">
        <h:outputText value="已阅" rendered="#{itm.deal_state=='1'}"/>
        <h:outputText value="未阅" rendered="#{itm.deal_state=='0'}"/>
    </p:column>
    <p:column headerText="操作" style="padding-left: 10px;">
        <p:commandLink value="查看" action="#{logBhkServiceCrptBean3.modInitAction}" process="@this" update=":tabView"  >
            <f:setPropertyActionListener value="#{itm.rid}" target="#{logBhkServiceCrptBean3.searchEntity.rid}"/>
            <f:setPropertyActionListener value="#{itm}" target="#{logBhkServiceCrptBean3.entity}"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink rendered="#{itm.deal_state=='0'}" value="已阅"  action="#{logBhkServiceCrptBean3.updateStateAction}"
                         process="@this" update=":tabView">
            <f:setPropertyActionListener value="#{itm.rid}" target="#{logBhkServiceCrptBean3.searchEntity.rid}"/>
            <f:setPropertyActionListener value="#{itm}" target="#{logBhkServiceCrptBean3.entity}"/>
            <p:confirm header="消息确认框" message="确定标记为已阅吗？" icon="ui-icon-alert"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{not empty itm.need_update_name and logBhkServiceCrptBean3.useNewCrptSelect}"/>
        <p:commandLink rendered="#{itm.need_update_name=='0' and logBhkServiceCrptBean3.useNewCrptSelect}" value="更新单位名称"
                       action="#{logBhkServiceCrptBean3.updateCompanyNameAction}" process="@this">
            <f:setPropertyActionListener value="#{itm.rid}" target="#{logBhkServiceCrptBean3.searchEntity.rid}"/>
            <f:setPropertyActionListener value="#{itm}" target="#{logBhkServiceCrptBean3.entity}"/>
        </p:commandLink>
        <p:commandLink rendered="#{itm.need_update_name=='1' and logBhkServiceCrptBean3.useNewCrptSelect}" value="查看单位名称"
                        process="@this" update=":tabView:mainForm:showCrptName" oncomplete="PF('CrptNameConfirmDialog').show();">
            <f:setPropertyActionListener value="#{itm.update_name}" target="#{logBhkServiceCrptBean3.showCrptName}"/>
        </p:commandLink>
    </p:column>
</ui:define>
<ui:define name="insertOtherMainContents">
    <p:dialog header="单位名称" widgetVar="CrptNameConfirmDialog" width="500" modal="true" resizable="false">
        <script type="application/javascript">
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
        </script>
        <p:panelGrid id="showCrptName" style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;width: 160px;">
                    <h:outputText value="单位名称：" />
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <h:outputText value="#{logBhkServiceCrptBean3.showCrptName}" />
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup style="text-align: center;">
                    <p:commandButton value="关闭" type="button" onclick="PF('CrptNameConfirmDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!--上级单位弹出框-->
    <p:dialog header="查询单位" widgetVar="SearchCompanyDialog" width="700" height="200" modal="true"
              resizable="false">
        <script type="application/javascript">
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                }
            });
        </script>
        <!-- 表格 -->
        <p:dataTable id="searchCompanyTable" value="#{logBhkServiceCrptBean3.companyList}" var="company"
                     paginator="true" rows="20" paginatorPosition="bottom" rowIndexVar="R"
                     paginatorTemplate="{CurrentPageReport}"
                     currentPageReportTemplate="查询到{totalRecords}条记录"
                     emptyMessage="没有您要找的记录！" lazy="true">
            <p:column headerText="操作" style="width: 40px;text-align:center;">
                <p:commandLink value="选择"
                               action="#{logBhkServiceCrptBean3.updateCompanyName(company)}"
                               onsuccess="PF('SearchCompanyDialog').hide();"/>
            </p:column>
            <p:column headerText="单位名称">
                <h:outputText value="#{company.name}"/>
            </p:column>
            <p:column headerText="社会信用代码" style="width: 200px;text-align: center;">
                <h:outputText value="#{company.creditCode}"/>
            </p:column>
        </p:dataTable>
    </p:dialog>
</ui:define>
</ui:composition>











