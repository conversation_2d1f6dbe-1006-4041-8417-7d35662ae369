<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_selection_mongo.xhtml">

<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{logBhkServicePersonBean}"/>
<ui:param name="editPage" value="/webapp/system/mongo3/tsMongoLogEdit_personNew.xhtml"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <!--引入中文日期-->
    <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
    <style type="text/css">
        .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 340px;
            width: 360px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
        .ui-custom-zone input {
            width: 200px !important;
        }
        .ui-zwx-calendar input{
            width: 80px !important;
        }
        .log-panelgrid td{
            line-height: 30px;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[


        //]]>
    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="体检人员日志管理"/>
        </p:column>
    </p:row>
</ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_h_auto" style="display: flex;align-items: center">
            <p:outputPanel style="flex:1;display: flex;align-items: center">
                <span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{logBhkServicePersonBean.searchAction}" update="dataTable,mongoPageInfo" process="@this,:tabView:mainForm:mainGrid" />
                <p:spacer width="5"/>
                <p:commandButton value="导出" ajax="false" icon="ui-icon-document" id="exportBtn" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{logBhkServicePersonBean.downloadFile}"/>
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="批量已阅" icon="ui-icon-document" action="#{logBhkServicePersonBean.batchUpdateStateAction}" update="dataTable" process="@this,dataTable">
                </p:commandButton>
                <p:spacer width="5"/>
                <p:commandButton value="显示总记录数" action="#{logBhkServicePersonBean.showTotalAction}" update="mongoPageInfo" process="@this">
                </p:commandButton>
                <p:spacer width="10"/>
                <p:outputPanel id="mongoPageInfo">
                    <p:outputLabel value="总记录数：#{logBhkServicePersonBean.total}条" style="color:blue;" rendered="#{logBhkServicePersonBean.showInfo}"/>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel style="padding-right: 8px;">
                <h:outputText value="说明：" style="color:red;"/>
                <h:outputText value="日志模块数据量大，只显示前${logBhkServicePersonBean.searcchLimit}条数据。" style="color:blue;"/>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row rendered="#{logBhkServicePersonBean.ifAdmin}">
            <p:column style="text-align:right;padding-right:3px;width:120px;height: 18px;">
                <h:outputLabel  value="地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:250px">
                <div class="ui-custom-zone">
                    <zwx:ZoneSingleNewComp zoneList="#{logBhkServicePersonBean.zoneList}"  zoneCodeNew="#{logBhkServicePersonBean.searchEntity.zone_gb}" zoneName="#{logBhkServicePersonBean.searchZoneName}"
                                           id="searchZone" onchange="onSearchNodeSelect()"  zoneType="#{logBhkServicePersonBean.searchZoneType}"/>
                    <p:remoteCommand name="onSearchNodeSelect" action="#{logBhkServicePersonBean.onSearchNodeSelect}" process="@this,searchZone"
                                     update="searchUnitListOneMenu"/>
                </div>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:120px">
                <h:outputLabel  value="上传机构：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;width:250px">
                <p:selectOneMenu id="searchUnitListOneMenu" value="#{logBhkServicePersonBean.searchEntity.unit_code}" style="width: 210px;" filter="true" filterMatchMode="contains">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{logBhkServicePersonBean.searchUnitMap}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 18px;width:120px">
                <h:outputLabel  value="企业名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:inputText style="width: 200px;" value="#{logBhkServicePersonBean.searchEntity.bhk_crpt_name}" maxlength="50" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:120px;height: 32px;">
                <h:outputLabel  value="人员姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;width:250px">
                <p:inputText style="width:200px;" value="#{logBhkServicePersonBean.searchEntity.bhk_psn_name}" maxlength="50" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 18px;width:120px;">
                <h:outputLabel  value="身份证号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;width:250px;">
                <p:inputText style="width:200px;" value="#{logBhkServicePersonBean.searchEntity.bhk_idc}" maxlength="32" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height: 18px;width:120px;">
                <h:outputLabel  value="体检编号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <p:inputText style="width:200px;" value="#{logBhkServicePersonBean.searchEntity.bhk_code}" maxlength="25" placeholder="精确查询"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:120px;height: 32px;">
                <h:outputLabel  value="上传日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <div class="ui-zwx-calendar">
                    <p:calendar value="#{logBhkServicePersonBean.searchEntity.visit_time_start}" size="11" navigator="true" yearRange="c-20:c"
                                readonlyInput="true" maxdate="new Date()"  converterMessage="上传开始日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                    ~
                    <p:calendar value="#{logBhkServicePersonBean.searchEntity.visit_time_end}" size="11" navigator="true" yearRange="c-20:c"
                                readonlyInput="true" maxdate="new Date()"  converterMessage="上传结束日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                </div>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel  value="报告出具日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <zwx:CalendarDynamicLimitComp startDate="#{logBhkServicePersonBean.searchEntity.rpt_date_start}"
                                              endDate="#{logBhkServicePersonBean.searchEntity.rpt_date_end}"  styleClass="ui-zwx-calendar"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;">
                <h:outputLabel  value="体检日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <div class="ui-zwx-calendar">
                    <p:calendar value="#{logBhkServicePersonBean.searchEntity.bhk_date_start}" size="11" navigator="true" yearRange="c-20:c"
                                readonlyInput="true" maxdate="new Date()"   converterMessage="体检开始日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                    ~
                    <p:calendar value="#{logBhkServicePersonBean.searchEntity.bhk_date_end}" size="11" navigator="true" yearRange="c-20:c"
                                readonlyInput="true" maxdate="new Date()"   converterMessage="体检结束日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
                </div>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 18px;width:120px" rendered="#{!logBhkServicePersonBean.ifAdmin}">
                <h:outputLabel  value="企业名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:10px;" rendered="#{!logBhkServicePersonBean.ifAdmin}">
                <p:inputText style="width: 200px;" value="#{logBhkServicePersonBean.searchEntity.bhk_crpt_name}" maxlength="50" placeholder="精确查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;;">
                <h:outputLabel  value="上传标记：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:selectManyCheckbox  value="#{logBhkServicePersonBean.uploadTags}">
                    <f:selectItem itemValue="1" itemLabel="成功"/>
                    <f:selectItem itemValue="0" itemLabel="失败"/>
                </p:selectManyCheckbox>
            </p:column>
        	<p:column style="text-align:right;padding-right:3px;" >
                <h:outputLabel  value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="5">
                <p:selectManyCheckbox  value="#{logBhkServicePersonBean.dealStates}">
                    <f:selectItem itemValue="1" itemLabel="已阅"/>
                    <f:selectItem itemValue="0" itemLabel="未阅"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width: 150px;padding-left: 3px;text-align: center">
            <h:outputText value="#{logBhkServicePersonBean.uploadZoneMap.get(itm.zone_gb)}" />
        </p:column>
        <p:column headerText="上传机构" style="width: 250px;padding-left: 3px;">
            <h:outputText value="#{logBhkServicePersonBean.uploadUnitMap.get(itm.unit_code)}" />
        </p:column>
        <p:column headerText="人员姓名" style="width: 100px;padding-left: 3px;text-align: center;">
            <h:outputText value="#{itm.bhk_psn_name}" />
        </p:column>
        <p:column headerText="身份证号" style="width: 150px;padding-left: 3px;text-align: center;">
            <h:outputText value="#{itm.bhk_idc}" />
        </p:column>
        <p:column headerText="体检编号" style="width: 150px;padding-left: 3px;text-align: center;">
            <h:outputText value="#{itm.bhk_code}" />
        </p:column>
        <p:column headerText="企业名称" style="width: 250px;padding-left: 3px;">
            <h:outputText value="#{itm.bhk_crpt_name}" />
        </p:column>
        <p:column headerText="社会信用代码" style="width: 0px;padding-left: 0px;text-align: center;" rendered="false">
            <h:outputText value="#{itm.bhk_credit_code}" />
        </p:column>
        <p:column headerText="体检日期" style="width: 100px;padding-left: 3px;text-align: center;">
            <h:outputText value="#{itm.bhk_date}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8" locale="cn" />
            </h:outputText>
        </p:column>
        <p:column headerText="报告出具日期" style="width: 100px;padding-left: 3px;text-align: center;">
            <h:outputText value="#{itm.rpt_date}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8" locale="cn" />
            </h:outputText>
        </p:column>
        <p:column headerText="上传时间" style="width: 150px;padding-left: 3px;text-align: center;">
            <h:outputText value="#{itm.visit_time}">
                <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn" />
            </h:outputText>
        </p:column>
        <p:column headerText="上传标记" style="width: 80px;padding-left: 3px;text-align: center;">
            <h:outputText value="失败" rendered="#{itm.upload_tag=='0'}"/>
            <h:outputText value="成功" rendered="#{itm.upload_tag=='1'}"/>
        </p:column>
        <p:column headerText="错误原因" style="width: 150px;padding-left: 3px;text-align: center;">
            <h:outputText value="——" rendered="#{itm.upload_tag=='1'}"/>
            <h:outputLink id="R_#{R}"  value="#">
                <h:outputText value="#{itm.error_msg_temp}" />
                <p:tooltip  for="@parent" value="#{itm.error_msg}" style="width: 300px;"
                            showEffect="clip" hideEffect="explode" position="left" rendered="#{itm.upload_tag=='0'}"/>
            </h:outputLink>
        </p:column>
        <p:column headerText="状态" style="width: 80px;padding-left: 3px;text-align: center;">
            <h:outputText value="未阅" rendered="#{itm.deal_state=='0'}"/>
            <h:outputText value="已阅" rendered="#{itm.deal_state=='1'}"/>
            <h:outputText value="无需处理" rendered="#{itm.deal_state=='2'}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 10px;width: 80px;">
            <p:commandLink value="查看" action="#{logBhkServicePersonBean.modInitAction}" process="@this" update=":tabView"  >
                <f:setPropertyActionListener value="#{itm.rid}" target="#{logBhkServicePersonBean.searchEntity.rid}"/>
                <f:setPropertyActionListener value="#{itm}" target="#{logBhkServicePersonBean.entity}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink rendered="#{itm.deal_state=='0'}" value="已阅"  action="#{logBhkServicePersonBean.updateStateAction}"
                           process="@this" update=":tabView">
                <f:setPropertyActionListener value="#{itm.rid}" target="#{logBhkServicePersonBean.searchEntity.rid}"/>
                <f:setPropertyActionListener value="#{itm.rid}" target="#{logBhkServicePersonBean.searchEntity.rid}"/>
                <f:setPropertyActionListener value="#{itm}" target="#{logBhkServicePersonBean.entity}"/>
                <p:confirm header="消息确认框" message="确定标记为已阅吗？" icon="ui-icon-alert"/>
            </p:commandLink>
        </p:column>
    </ui:define>
</ui:composition>

