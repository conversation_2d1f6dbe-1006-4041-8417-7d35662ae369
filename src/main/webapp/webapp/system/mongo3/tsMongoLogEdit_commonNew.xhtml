<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

        <p:fieldset legend="调用信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid style="width:100%;"  styleClass="log-panelgrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width: 300px">
                        <h:outputText value="上传机构编码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <h:outputText value="#{mgrbean.entity.unit_code}"/>
                        <p:inputText style="visibility: hidden;width: 1px;"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="上传机构名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <h:outputText value="#{mgrbean.uploadUnitMap.get(mgrbean.entity.unit_code)}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="调用时间："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:8px;">
                        <h:outputText value="#{mgrbean.entity.visit_time}">
                            <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn" />
                        </h:outputText>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifAdmin}">
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="客户端IP："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <h:outputText value="#{mgrbean.entityMain.client_ip}"/>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifAdmin}">
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="调用地址："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <h:outputText value="#{mgrbean.entityMain.visit_adr}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>

</ui:composition>

