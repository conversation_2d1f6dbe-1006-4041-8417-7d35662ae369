<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 编辑页面的script -->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[

            //]]>

        </script>

    </ui:define>


    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="体检人员日志管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton rendered="#{bhkDataServiceBean.dealState=='0'}" value="已阅" icon="ui-icon-check"  action="#{bhkDataServiceBean.updateStateAction}"
                                 process="@this" update=":tabView">
                    <p:confirm header="消息确认框" message="确定标记为已阅吗？" icon="ui-icon-alert"/>
                    <f:setPropertyActionListener value="1" target="#{bhkDataServiceBean.dealState}"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-close" id="backBtn" action="#{bhkDataServiceBean.backAction}"
                                 update=":tabView" immediate="true" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 编辑页面的内容-->
    <ui:define name="insertEditContent">

    </ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <ui:include src="./tsMongoLogEdit_common.xhtml"/>
        <p:fieldset  legend="传输信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <c:if test="#{bhkDataServiceBean.tdZwlogUpload.subs.size() gt 0}">
            <c:forEach items="#{bhkDataServiceBean.tdZwlogUpload.subs}" var="oneSub" >
                <c:if test="#{oneSub.rid==bhkDataServiceBean.searchEntity.subs.rid}">
                    <p:panelGrid style="width:100%;" styleClass="log-panelgrid">
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;width: 300px">
                                <h:outputText value="请求唯一标识："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="#{oneSub.uid}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="操作标识："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="新增" rendered="#{oneSub.opt_tag=='1'}"/>
                                <h:outputText value="修改" rendered="#{oneSub.opt_tag=='2'}"/>
                                <h:outputText value="删除" rendered="#{oneSub.opt_tag=='3'}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="人员姓名："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="#{oneSub.bhk_psn_name}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="身份证号："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:8px;">
                                <h:outputText value="#{oneSub.bhk_idc}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="体检编号："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:8px;">
                                <h:outputText value="#{oneSub.bhk_code}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="企业名称："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                #{oneSub.bhk_crpt_name}
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="社会信用代码："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:8px;">
                                <h:outputText value="#{oneSub.bhk_credit_code}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="体检机构编号："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:8px;">
                                <h:outputText value="#{oneSub.bhk_org_code}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="体检机构名称："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="#{bhkDataServiceBean.uploadUnitMap.get(oneSub.bhk_org_code)}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="体检日期："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="#{oneSub.bhk_date}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8" locale="cn" />
                                </h:outputText>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="请求内容："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <p:commandLink value="查看" action="#{bhkDataServiceBean.showContentAction}" process="@this" update="respTextDialog">
                                    <f:setPropertyActionListener  value="#{bhkDataServiceBean.subText.req_text}" target="#{bhkDataServiceBean.showContent}"/>
                                </p:commandLink>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="响应内容："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="成功时无响应内容" rendered="#{oneSub.upload_tag=='1'}"/>
                                <p:commandLink rendered="#{oneSub.upload_tag=='0'}" value="查看" action="#{bhkDataServiceBean.showContentAction}" process="@this" update="respTextDialog">
                                    <f:setPropertyActionListener  value="#{bhkDataServiceBean.subText.resp_text}" target="#{bhkDataServiceBean.showContent}"/>
                                </p:commandLink>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="上传标记："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="失败" rendered="#{oneSub.upload_tag=='0'}"/>
                                <h:outputText value="成功" rendered="#{oneSub.upload_tag=='1'}"/>
                            </p:column>
                        </p:row>
                        <p:row  rendered="#{oneSub.upload_tag=='0'}">
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="失败原因："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <p:inputTextarea value="#{bhkDataServiceBean.showErrorContent}" autoResize="false" readonly="true" style="width: 600px;resize: none;height: 100px;margin-top: 10px;"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <h:outputText value="状态："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:10px;">
                                <h:outputText value="未阅" rendered="#{oneSub.deal_state=='0'}"/>
                                <h:outputText value="已阅" rendered="#{oneSub.deal_state=='1'}"/>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </c:if>
            </c:forEach>
            </c:if>
        </p:fieldset>
        <ui:include src="./tsMongoLogEdit_common2.xhtml"/>
        <div style="margin-top: 20px;">
        <p:dialog style="" id="respTextDialog" widgetVar="RespTextDialog" header="信息查看" resizable="false"
                  width="800"  modal="true" >
            <p:inputTextarea readonly="true"  value="#{bhkDataServiceBean.showContent}" autoResize="false"  style="resize: none;width: 98%;height: 600px;overflow-x: hidden"/>
        </p:dialog>
        </div>
    </ui:define>
</ui:composition>

