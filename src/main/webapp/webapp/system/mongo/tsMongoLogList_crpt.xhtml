<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_simple_mongo.xhtml">
<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{bhkDataServiceBean}"/>
    <ui:param name="editPage" value="/webapp/system/mongo/tsMongoLogEdit_crpt.xhtml"/>
<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <!--引入中文日期-->
    <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
    <style type="text/css">
        .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 340px;
            width: 360px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
        .ui-custom-zone input {
            width: 200px !important;
        }
        .ui-zwx-calendar input{
            width: 80px !important;
        }
        .log-panelgrid td{
            line-height: 30px;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[


        //]]>
    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="企业日志管理"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
			<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{bhkDataServiceBean.searchAction}" update="dataTable" process="@this,:tabView:mainForm:mainGrid" />
            <p:commandButton value="导出" ajax="false" icon="ui-icon-document" id="exportBtn" onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                <p:dataExporter target="dataTable" type="xls" fileName="#{bhkDataServiceBean.exportFileName}" postProcessor="#{bhkDataServiceBean.postProcessXLS}" preProcessor="#{bhkDataServiceBean.preProcessXLS}"/>
            </p:commandButton>
		</p:outputPanel>
</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:150px;">
            <h:outputLabel  value="地区：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;width:250px">
            <div class="ui-custom-zone">
            <zwx:ZoneSingleNewComp zoneList="#{bhkDataServiceBean.zoneList}"  zoneCodeNew="#{bhkDataServiceBean.searchEntity.zone_gb}" zoneName="#{bhkDataServiceBean.searchZoneName}"
                                   id="searchZone" onchange="onSearchNodeSelect()"  zoneType="#{bhkDataServiceBean.searchZoneType}"/>
            <p:remoteCommand name="onSearchNodeSelect" action="#{bhkDataServiceBean.onSearchNodeSelect}" process="@this,searchZone"
                             update="searchUnitListOneMenu"/>
            </div>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:200px">
            <h:outputLabel  value="上传机构：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;width:250px">
            <p:selectOneMenu id="searchUnitListOneMenu" value="#{bhkDataServiceBean.searchEntity.unit_code}" style="width: 210px;">
                <f:selectItem itemLabel="--请选择--" itemValue=""/>
                <f:selectItems value="#{bhkDataServiceBean.searchUnitMap}"/>
            </p:selectOneMenu>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:200px">
            <h:outputLabel  value="上传日期：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;">
            <div class="ui-zwx-calendar">
            <p:calendar value="#{bhkDataServiceBean.searchEntity.visit_time_start}" size="11" navigator="true" yearRange="c-20:c"
                 readonlyInput="true" maxdate="new Date()"   converterMessage="上传开始日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            ~
            <p:calendar value="#{bhkDataServiceBean.searchEntity.visit_time_end}" size="11" navigator="true" yearRange="c-20:c"
                 readonlyInput="true" maxdate="new Date()"   converterMessage="上传结束日期，格式输入不正确！" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
            </div>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;;">
            <h:outputLabel  value="企业名称：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;">
            <p:inputText style="width: 200px;" value="#{bhkDataServiceBean.searchEntity.subs.crpt_name}" maxlength="50"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel  value="社会信用代码：" />
        </p:column>
        <p:column style="text-align:left;padding-left:10px;">
            <p:inputText style="width:200px;" value="#{bhkDataServiceBean.searchEntity.subs.credit_code}" maxlength="50"/>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;;">
            <h:outputLabel  value="上传标记：" />
        </p:column>
        <p:column style="text-align:left;padding-left:5px;">
            <p:selectManyCheckbox  value="#{bhkDataServiceBean.uploadTags}">
                <f:selectItem itemValue="1" itemLabel="成功"/>
                <f:selectItem itemValue="0" itemLabel="失败"/>
            </p:selectManyCheckbox>
        </p:column>
    </p:row>
    <p:row>
        <p:column style="text-align:right;padding-right:3px;">
            <h:outputLabel  value="状态：" />
        </p:column>
        <p:column style="text-align:left;padding-left:5px;" colspan="5">
            <p:selectManyCheckbox  value="#{bhkDataServiceBean.dealStates}">
                <f:selectItem itemValue="1" itemLabel="已阅"/>
                <f:selectItem itemValue="0" itemLabel="未阅"/>
            </p:selectManyCheckbox>
        </p:column>


    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="地区" style="width: 150px;padding-left: 3px;text-align: center">
        <h:outputText value="#{bhkDataServiceBean.uploadZoneMap.get(itm.zone_gb)}" />
    </p:column>
    <p:column headerText="上传机构名称" style="width: 250px;padding-left: 3px;">
        <h:outputText value="#{bhkDataServiceBean.uploadUnitMap.get(itm.unit_code)}" />
    </p:column>
    <p:column headerText="企业名称" style="width: 250px;padding-left: 3px;">
        <h:outputText value="#{itm.subs.crpt_name}" />
    </p:column>
    <p:column headerText="社会信用代码" style="width: 150px;padding-left: 3px;text-align: center;">
        <h:outputText value="#{itm.subs.credit_code}" />
    </p:column>
    <p:column headerText="上传时间" style="width: 150px;padding-left: 3px;text-align: center;">
        <h:outputText value="#{itm.visit_time}">
            <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="GMT+8" locale="cn" />
        </h:outputText>
    </p:column>
    <p:column headerText="上传标记" style="width: 80px;padding-left: 3px;text-align: center;">
        <h:outputText value="失败" rendered="#{itm.subs.upload_tag=='0'}"/>
        <h:outputText value="成功" rendered="#{itm.subs.upload_tag=='1'}"/>
    </p:column>
    <p:column headerText="状态" style="width: 80px;padding-left: 3px;text-align: center;">
        <h:outputText value="已阅" rendered="#{itm.subs.deal_state=='1'}"/>
        <h:outputText value="未阅" rendered="#{itm.subs.deal_state=='0'}"/>
    </p:column>
    <p:column headerText="失败原因" style="width: 150px;padding-left: 3px;text-align: center;display: none">
        <h:outputText value="#{itm.subs.resp_text}" />
    </p:column>
    <p:column headerText="操作" style="padding-left: 10px;">
        <p:commandLink value="查看" action="#{bhkDataServiceBean.modInitAction}" process="@this" update=":tabView"  >
            <f:setPropertyActionListener value="#{itm.rid}" target="#{bhkDataServiceBean.searchEntity.rid}"/>
            <f:setPropertyActionListener value="#{itm.subs.rid}" target="#{bhkDataServiceBean.searchEntity.subs.rid}"/>
            <f:setPropertyActionListener value="#{itm.subs.deal_state}" target="#{bhkDataServiceBean.dealState}"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink rendered="#{itm.subs.deal_state=='0'}" value="已阅"  action="#{bhkDataServiceBean.updateStateAction}"
                         process="@this" update=":tabView">
            <f:setPropertyActionListener value="#{itm.rid}" target="#{bhkDataServiceBean.searchEntity.rid}"/>
            <f:setPropertyActionListener value="#{itm.subs.rid}" target="#{bhkDataServiceBean.searchEntity.subs.rid}"/>
            <f:setPropertyActionListener value="0" target="#{bhkDataServiceBean.dealState}"/>
            <p:confirm header="消息确认框" message="确定标记为已阅吗？" icon="ui-icon-alert"/>
        </p:commandLink>
    </p:column>
</ui:define>


</ui:composition>











