<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml"
>
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            ul li {
                list-style-type: none;
                margin-left: 16px;
            }
        </style>
    </ui:define>
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{systemPropertiesGetListBean}"/>
    <!-- 编辑页面 -->
    <h:outputStylesheet name="css/default.css"/>
    <h:outputScript library="js" name="namespace.js"/>
    <h:outputScript name="js/validate/system/validate.js"/>
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="配置文件查询"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{systemPropertiesGetListBean.searchList}" update="dataList"
                                 process="@this,searchfileName">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="width: 180px;height: 35px; text-align: right; padding-right: 3px;"><p:outputLabel
                    value="属性名："/></p:column>
            <p:column style="text-align: left; padding-left: 8px;"><p:inputText
                    value="#{systemPropertiesGetListBean.searchKeySet}" id="searchfileName"/></p:column>
        </p:row>
    </ui:define>
    <!-- 内容-->
    <ui:define name="insertContent">
        <p:outputPanel id="dataList" style="margin: 0 3px 0 3px">
            <c:forEach items="#{systemPropertiesGetListBean.propertiesNameList}" var="name">
                <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;">
                    <f:facet name="header">
                        <p:row>
                            <p:column colspan="6" style="text-align:left;height: 20px;">
                                <p:outputLabel value="#{name.fileName}"/>
                            </p:column>
                        </p:row>
                    </f:facet>
                    <p:row>
                        <p:column>
                            <p:dataTable value="#{name.propertiesContentList}" var="content" emptyMessage="暂无数据！">
                                <p:column headerText="属性名" style="margin: 5px 10px 5px 0;width: 200px;">
                                    <p:outputLabel value="#{content.attributeName}"/>
                                </p:column>
                                <p:column headerText="属性值">
                                    <p:outputLabel value="#{content.attributeValue}"/>
                                </p:column>
                            </p:dataTable>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </c:forEach>
        </p:outputPanel>
    </ui:define>
</ui:composition>