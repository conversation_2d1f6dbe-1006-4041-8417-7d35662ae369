<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{userBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/system/userEdit.xhtml"/>

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

        <style type="text/css">
            .ui-picklist .ui-picklist-list{
                text-align:left;
                height: 350px;
                width: 340px;
                overflow: auto;
            }

            .ui-picklist .ui-picklist-filter {
                padding-right: 0px;
                width: 98%;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="用户管理"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{userBean.searchAction}" update="dataTable"
					process="@this,searchZone,searchUnitListOneMenu,searchOfficeListOneMenu,searchUsername,searchUserType,searchUserState" />
				<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{userBean.addInitAction}" update=":tabView" process="@this">
					<p:resetInput target=":tabView:editForm:editGrid" />
				</p:commandButton>
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 35px;" >
                <h:outputText value="地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;width: 250px;">
                <zwx:ZoneSingleNewComp zoneList="#{userBean.zoneList}"  zoneCodeNew="#{userBean.searchZoneCode}" zoneName="#{userBean.searchZoneName}"
                	id="searchZone" onchange="onSearchNodeSelect()"  zoneType="#{userBean.searchZoneType}"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{userBean.onSearchNodeSelect}" process="@this,searchZone"
                                 update=":tabView:mainForm:searchUnitListOneMenu,:tabView:mainForm:searchOfficeListOneMenu"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="单位：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px;">
                <p:selectOneMenu id="searchUnitListOneMenu" value="#{userBean.searchUnitId}" style="width: 190px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{userBean.searchUnitMap}"/>
                    <p:ajax event="change" update="searchOfficeListOneMenu" process="@this" listener="#{userBean.onSearchUnitChange}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="科室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectOneMenu id="searchOfficeListOneMenu" value="#{userBean.searchOfficeId}" style="width: 180px;">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{userBean.searchOfficeMap}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:160px;height: 35px;">
                <h:outputText value="用户姓名：" />
            </p:column>
            <p:column style="text-align:left;padding-left:12px;width: 250px;">
                <p:inputText id="searchUsername" value="#{userBean.searchUsername}" maxlength="15"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectOneRadio id="searchUserType" value="#{userBean.searchUserType}" style="width: 180px;">
                    <f:selectItem itemLabel="内部用户" itemValue="1"/>
                    <f:selectItem itemLabel="外部用户" itemValue="2"/>
                </p:selectOneRadio>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="状态：" />
            </p:column>
            <p:column>
                <p:selectManyCheckbox value="#{userBean.searchUserState}" id="searchUserState" >
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0" />
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width: 80px;text-align: center;">
            <h:outputText value="#{itm[0]}" />
        </p:column>
        <p:column headerText="单位" style="padding-left: 3px;width: 220px;">
            <h:outputText value="#{itm[1]}" />
        </p:column>
        <p:column headerText="科室" style="width: 180px;">
            <h:outputText value="#{itm[2]}" />
        </p:column>
        <p:column headerText="用户登录名" style="width: 100px;text-align: center;">
            <h:outputText value="#{itm[3]}" />
        </p:column>
        <p:column headerText="用户姓名" style="width: 120px;text-align: center;">
            <h:outputText value="#{itm[4]}" />
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;">
            <p:commandLink value="修改" action="#{userBean.modInitAction}" update=":tabView" process="@this"
                    oncomplete="blockEmpInfo();">
                <f:setPropertyActionListener target="#{userBean.rid}" value="#{itm[5]}"/>
                <p:resetInput target=":tabView:editForm:editGrid"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="删除" action="#{userBean.deleteAction}" update="dataTable" process="@this">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{userBean.rid}" value="#{itm[5]}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="角色分配" action="#{userBean.fpRoleInitAction}" update=":tabView:mainForm:grantRoleDialog"
                           process="@this" oncomplete="PF('GrantRole').show();">
                <f:setPropertyActionListener target="#{userBean.rid}" value="#{itm[5]}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="菜单授权" action="#{userBean.menuRedirectAction}" immediate="true" process="@this">
                <f:setPropertyActionListener target="#{userBean.rid}" value="#{itm[5]}"/>
            </p:commandLink>
            <p:spacer width="5" />
            <p:commandLink value="停用" action="#{userBean.stopAction}" update="dataTable" process="@this" rendered="#{itm[6]=='1'}">
                <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{userBean.rid}" value="#{itm[5]}"/>
            </p:commandLink>
            <p:commandLink value="启用" action="#{userBean.startAction}" update="dataTable" process="@this" rendered="#{itm[6]!='1'}">
                <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{userBean.rid}" value="#{itm[5]}"/>
            </p:commandLink>
            <p:spacer width="5" />
        </p:column>
    </ui:define>

    <!-- 其它内容 -->
    <ui:define name="insertOtherMainContents">
        <!-- 角色授权 -->
        <p:dialog id="grantRoleDialog" header="角色分配" widgetVar="GrantRole" resizable="false" width="350" height="400" modal="true">

            <p:selectManyCheckbox id="roleCheckbox" value="#{userBean.selectedRoles}" layout="pageDirection">
                <f:selectItems value="#{userBean.roleMap}"/>
            </p:selectManyCheckbox>

            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="roleSaveBtn" action="#{userBean.fpRoleAction}" process="@this,roleCheckbox" oncomplete="PF('GrantRole').hide();"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="roleBackBtn" onclick="PF('GrantRole').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

        <!-- 菜单授权 -->
        <p:dialog id="menuDialog" header="菜单授权" widgetVar="MenuDialog" resizable="false" width="350" height="400" modal="true">

            <p:tree value="#{userBean.treeNode}" var="node" selectionMode="checkbox" selection="#{userBean.selectedNodes}" id="menuTree"
                    style="width: 320px;height: 390px;overflow-y: auto;">
                <p:treeNode>
                    <h:outputText value="#{node.menuCn}" />
                </p:treeNode>
            </p:tree>

            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="menuSaveBtn" action="#{userBean.menuAction}" process="@this,menuTree" oncomplete="PF('MenuDialog').hide();"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="menuBackBtn" onclick="PF('MenuDialog').hide();" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>

    </ui:define>

</ui:composition>











