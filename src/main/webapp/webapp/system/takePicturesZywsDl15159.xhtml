<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<title><span id="dialogSelfTitle">文件扫描</span> <p:spacer
				width="10" /> <span id="pageNum">共0张</span></title>
		<h:outputStylesheet name="css/default.css" />
		<h:outputStylesheet name="css/ui-tabs.css" />
		<ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<script type="text/javascript" src="/resources/js/WebCamera_EL_D_V1.2.3/javascript_method.js"></script>
	</h:head>

	<h:body style="overflow-y:hidden;">
		<h:form id="mainForm">
			<p:layout id="contentLay" style="height:378px">
				<p:layoutUnit position="west" size="410" id="leftLay" style="text-align:center;">
					<!-- 摄像头 -->
					<div id="Chrome" >
						<!-- 摄像头 -->
						<img id="chrome_img" alt="" src="" class="zwxVedioInput"/>
					</div>
				</p:layoutUnit>
				<p:layoutUnit position="center" size="200" id="centerLay">
					<p:outputPanel id="board" styleClass="center-container"
						style="margin-left: 40px;">
						<p:dashboard model="#{takePicturesZywsBean.model}">
							<p:ajax event="reorder"
								listener="#{takePicturesZywsBean.handleReorder}"
								oncomplete="initVedioImgsEvents()" />
							<c:forEach items="#{takePicturesZywsBean.imgList}" var="imgBean">
								<p:panel id="p#{imgBean.xh}" header="第#{imgBean.xh}张"
									closable="true" style="width: 150px;">
									<p:ajax event="close" onstart="deletePicture#{imgBean.xh}()" />
									<p:remoteCommand resetValues="true"
										name="deletePicture#{imgBean.xh}"
										action="#{takePicturesZywsBean.deletePicture}"
										oncomplete="showPanel()">
										<f:setPropertyActionListener value="#{imgBean}"
											target="#{takePicturesZywsBean.imgBeanTemp}" />
									</p:remoteCommand>
									<p:outputPanel styleClass="vedio-photo">
										<p:graphicImage
											style="width: 100%;height: 100%;cursor: pointer;"
											value="/webFile#{imgBean.path}" />
									</p:outputPanel>
								</p:panel>
							</c:forEach>
						</p:dashboard>
						<div class="clear"></div>
					</p:outputPanel>
				</p:layoutUnit>
			</p:layout>
			<p:outputPanel rendered="#{takePicturesZywsBean.type=='2'}"
				style="margin:5px 20px;padding-bottom: 5px;border-bottom: 1px solid #A6C9E2;">
				<h:outputText value="*" style="color: #ff0000" />附件名称：<p:inputText
					value="#{takePicturesZywsBean.showFileName}" id="showFileName"
					style="width: 350px;" maxlength="50" />
			</p:outputPanel>
			<p:outputPanel styleClass="line-cutoff"
				rendered="#{takePicturesZywsBean.type=='1'}">&#160;</p:outputPanel>
			<p:outputPanel style="text-align:center" id="btnPanel">
				<p:spacer width="5" />
				<p:commandButton value="拍照" icon="ui-icon-video" id="saveBtn"
					type="button" onclick="vedioInputTakePhoto()" />
				<p:spacer width="5" />
				<p:commandButton value="插入" icon="ui-icon-seek-end" id="insertBtn"
					type="button" onclick="vedioInputInsert()" />
				<p:spacer width="5" />
				<p:commandButton value="预览" icon="ui-icon-image" id="viewBtn"
					type="button" onclick="viewImgDetail()" />
				<p:spacer width="5" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
					id="submitBtn" type="button" onclick="vedioInputSubmit()" />

				<p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
					action="#{takePicturesZywsBean.dialogClose}" widgetVar="BackBtn"
					process="@this" style="display:none" />
				<p:remoteCommand name="savePictureAction" process="@this,binaryStr"
					action="#{takePicturesZywsBean.savePicture}" update="imgsJson"
					oncomplete="initVedioImgsEvents();showPanel();" />
				<p:remoteCommand name="deletePictureAction" process="@this"
					action="#{takePicturesZywsBean.deletePicture}" update="imgsJson"
					oncomplete="initVedioImgsEvents();showPanel();" />
				<p:remoteCommand name="submitPictureAction"
					process="@this,showFileName"
					action="#{takePicturesZywsBean.confirmAction}" />

				<h:inputHidden id="binaryStr"
					value="#{takePicturesZywsBean.binaryStr}" />
				<h:inputHidden id="imgsJson"
					value="#{takePicturesZywsBean.imgsJson}" />
			</p:outputPanel>

			<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
			<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		</h:form>
		<script type="text/javascript">
			//<![CDATA[

			var url="http://127.0.0.1:38088";
			var data;
			//用户使用的浏览器版本数据
			var IE_data;
			// 摄像头
			var DevIndex=0;
			//纠偏 默认关闭
			var rectifying = "0";
			//模式
			var ModelNumIndex = "0";
			//画质
			var QualityNumindex = "0";
			//1：服务异常  2：高拍仪接入异常  其他：拍照服务丢失
			var ifOpenCamera=0;

			window.onload = function() {
				init();
			}

			function init() {
				IE_data = IEVersion();
				if (IE_data == "Chrome" || IE_data == "edge" || IE_data == "Firefox") {
					var imgdata = document.getElementById("Chrome");
					imgdata.style.display = "none";
					imgdata.style.visibility = "hidden";
				}
				if (IE_data <= 7) {
					if (!window.JSON) {
						window.JSON = {
							parse: function (jsonStr) {
								return eval('(' + jsonStr + ')');
							}
						}
					}
				}
				//判断是否能链接
				Getdevice();
			}

			//判断浏览器
			function IEVersion() {
				var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
				var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
				var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
				var isChrome = userAgent.indexOf("Chrome") > -1 && !isIE; //判断是否IE的Chrome浏览器
				var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
				var Firefox = userAgent.indexOf("Firefox") > -1 && !isIE;

				if (isIE) {
					var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
					reIE.test(userAgent);
					var fIEVersion = parseFloat(RegExp["$1"]);
					if (fIEVersion == 7) {
						return 7;
					} else if (fIEVersion == 8) {
						return 8;
					} else if (fIEVersion == 9) {
						return 9;
					} else if (fIEVersion == 10) {
						return 10;
					} else {
						return 6;//IE版本<=7
					}
				} else if (isEdge) {
					return 'edge';//edge
				} else if (isIE11) {
					return 11; //IE11
				} else if (isChrome) {
					return 'Chrome'  //Chrome
				} else if (Firefox) {
					return 'Firefox'  //Firefox
				} else {
					return -1;//不是ie浏览器
				}
			}

			//判断设备是否在线，请求发送失败，提示下载插件，
			function Getdevice() {
				Ajax("POST", url+"/device=isconnect", data, function gettingData(res) {
					if (res.code === "0") {
						if (res.data >= 1) {
							OpenCamera();
							var RotationT = setTimeout(function () {
								selectResolution();
								clearTimeout(RotationT);
							}, 1500);
						} else {
							ifOpenCamera=2;
							alert("请检查是否接入高拍仪！");
						}
					} else {
						ifOpenCamera=1;
						PF('InstallDialog').show();
					}
				})
			}

			function selectResolution() {

				var data = {
					camidx: JSON.stringify(DevIndex),
					mode: "0",
					width: "2045",
					height: "1536"
				};
				Ajax("post", url+"/device=setresolution", JSON.stringify(data), function gettingData(res) {
							if (res.code === "0") {
								console.log("分辨率设置成功：")
								CloseCamera();
								var Gettion = setTimeout(function () {
									OpenCamera();
									var imgdata = document.getElementById("Chrome");
									imgdata.style.display = "block";
									imgdata.style.visibility = "visible";
									clearTimeout(Gettion);
								}, 1500);
							} else {
								console.log("分辨率设置失败");
							}

						}
				)
			}

			//关闭摄像头
			function CloseCamera() {
				if (IE_data == "Chrome" || IE_data == "edge" || IE_data == "Firefox") {
					var data = {
						camidx: JSON.stringify(DevIndex)
					};
					Ajax("post", url+"/video=close", JSON.stringify(data), function gettingData(res) {
								if (res.code === "0") {
									document.getElementById("chrome_img").src = "";
									console.log("摄像头关闭成功");
								} else {
									console.log("摄像头关闭失败");
								}
							}
					)
				} else {
					if (showVideoOcx.StopPreview()) {
						console.log("关闭成功")
					} else {
						console.log("关闭失败")
					}
				}
			}


			// 图片旋转，旋转完，拍照哪里可以不需要跟着动态旋转
			function Rotation() {
				var data = {
					camidx: JSON.stringify(DevIndex), // 摄像头，0:主头；1:副头
					rotate: "90", // 角度，参数：90、180、270
				};
				Ajax("post", url+"/video=rotate", JSON.stringify(data), function gettingData(res) {
							if (res.code === "0") {

								console.info("顺时针旋转90°成功");
							} else {
								console.info("旋转90°失败");
							}
						}
				)
			}

			//打开摄像头
			function OpenCamera() {
				if (IE_data == "Chrome" || IE_data == "edge" || IE_data == "Firefox") {
					var random = Math.random(); //创建随机数
					document.getElementById("chrome_img").src = "";
					document.getElementById("chrome_img").src = "http://127.0.0.1:38088/video=stream&camidx=0"  + "?" + random;
				} else {
					showVideoOcx.StartPreview(DevIndex);
				}
			}

			/**
			 * 拍照
			 */
			function vedioInputTakePhoto() {
					var data = {
						"filepath": "base64",  //"D://test11.jpg"  指定路径  "base64" 不保存本地，返回base64  ""   保存默认路径下
						"rotate": "0",      // 图像旋转角度，90的整数倍，默认："0"
						"deskew": rectifying,      // 纠偏（主头有效），参数：0:不纠偏；1:纠偏
						"deskewval": "10",       // 纠偏像素值：正常给0，正数时多裁，负数时少裁
						"camidx": JSON.stringify(DevIndex),      // 摄像头索引，参数：0:主头；1:副头
						"quality": QualityNumindex, //图片质量，图片保存本地调用。0:默认质量；1:高质量；2:较高质量；3:中质量；4:较低质量；5:低质量
						"ColorMode": ModelNumIndex, // 色彩模式，图片保存本地时调用。0：彩色 1：灰色 2：黑白 3：白纸印章 4：去背景色(普通文件) 5:去背景色(身份证)
						"bIsPrint1to1": "0",   // 1:1打印有一点点偏差 是适用于身份证卡片之类  1:开启;  0 :关闭
					};

					Ajax("POST", url+"/video=grabimage", JSON.stringify(data), async function gettingData(res) {
						if (res.code === "0") {
							var imgBase64 = await rotateImgByBase64(res.photoBase64);
							imgBase64 = imgBase64.substring("data:image/png;base64, ".length);
							console.log(imgBase64);
							document.getElementById("mainForm:binaryStr").value = imgBase64;
							savePictureAction();
						}else {
							if(ifOpenCamera===1){
								PF('InstallDialog').show();
							}else if(ifOpenCamera===2){
								alert("请检查是否接入高拍仪！");
							}else{
								alert("拍照失败,请重试！");
							}
						}
					})
			}

			function vedioInputInsert() {
				var selectLen = jQuery(".vedio-photo-choosen").length;
				if (selectLen == 0) {
					alert("请先选择要插入的页码！");
					return;
				} else {
					var find = jQuery("#mainForm\\:board").find(
							"div.vedio-photo-choosen").find("img").eq(0);
					var index = jQuery("#mainForm\\:board").find("img").index(
							find);
					var data = {
						"filepath": "base64",  //"D://test11.jpg"  指定路径  "base64" 不保存本地，返回base64  ""   保存默认路径下
						"rotate": "0",      // 图像旋转角度，90的整数倍，默认："0"
						"deskew": rectifying,      // 纠偏（主头有效），参数：0:不纠偏；1:纠偏
						"deskewval": "10",       // 纠偏像素值：正常给0，正数时多裁，负数时少裁
						"camidx": JSON.stringify(DevIndex),      // 摄像头索引，参数：0:主头；1:副头
						"quality": QualityNumindex, //图片质量，图片保存本地调用。0:默认质量；1:高质量；2:较高质量；3:中质量；4:较低质量；5:低质量
						"ColorMode": ModelNumIndex, // 色彩模式，图片保存本地时调用。0：彩色 1：灰色 2：黑白 3：白纸印章 4：去背景色(普通文件) 5:去背景色(身份证)
						"bIsPrint1to1": "0",   // 1:1打印有一点点偏差 是适用于身份证卡片之类  1:开启;  0 :关闭
					};
					Ajax("get", url+"/video=grabimage", JSON.stringify(data), async function gettingData(res) {
								if (res.code === "0") {
									var imgBase64 = await rotateImgByBase64(res.photoBase64);
									imgBase64 = imgBase64.substring("data:image/png;base64, ".length);
									document.getElementById("mainForm:binaryStr").value = imgBase64;
									savePictureAction([ {
										name : 'inx',
										value : index + 1
									} ]);
								} else {
									alert("拍照失败");
								}
							}
					)
				}
			}

			function vedioInputSubmit() {
				var imgsJson = document.getElementById("mainForm:imgsJson").value;
				if (imgsJson == "" || imgsJson == "[]") {
					alert("请先拍照！");
					return;
				}
				submitPictureAction();
			}

			/**
			 * 释放
			 */
			function vedioInputClear() {

			}


			/**
			 * 图片查看详情
			 */
			function viewImgDetail() {
				var imgsJson = document.getElementById("mainForm:imgsJson").value;
				var grobalUrl = "#{request.scheme}" + "://"
						+ "#{request.serverName}" + ":"
						+ "#{request.serverPort}" + "#{request.contextPath}";
				var url = grobalUrl + "/webapp/system/viewImgShowE1100.faces?param="
						+ encodeURI(imgsJson);
				var htmlStr = '<body scroll="no" style="margin: 0;padding: 0;border:0;overflow:hidden;">' +
						'<iframe style="margin: 0;padding: 0;border: 0;width:100%;height:100%;" src="' + url +
						'"></iframe>' +
						'</body>';
				var newWin = window.open('','newwindow','toolbar =no, menubar=no, scrollbars=no, resizeable=no, location=no, status=no');
				newWin.document.write(htmlStr);
			}

			function initVedioImgsEvents() {
				jQuery(".img-close-icon").hide();
				jQuery(".vedio-photo").bind("click", function() {
					jQuery(".img-close-icon").hide();
					jQuery(".vedio-photo-choosen").each(function() {
						jQuery(this).removeClass("vedio-photo-choosen");
					});
					jQuery(this).addClass("vedio-photo-choosen");
					jQuery(this).parent().find(".img-close-icon").show();
				});

				jQuery(".img-close-icon").bind("click", function() {
					var inx = jQuery(this).prev(".page").html();
					deletePictureAction([ {
						name : 'inx',
						value : inx
					} ]);
				});
			}

			/**
			 * 显示隐藏的panel
			 */
			function showPanel() {
				jQuery("#mainForm\\:board").find("div.ui-panel").each(
						function() {
							if ($(this).hasClass("ui-helper-hidden")) {
								$(this).removeClass("ui-helper-hidden");
							}
						});
				var length = jQuery("#mainForm\\:board").find("img").length;
				$("#pageNum", window.parent.document).html("共" + length + "张");
				initVedioImgsEvents()
			}


			/**
			 * 图片旋转90度并压缩
			 * @param base64Str
			 * @returns {Promise<string>}
			 */
			async function rotateImgByBase64(base64Str) {
				const base64 = "data:image/png;base64," + base64Str;
				const img = await getImage(base64);
				const canvas = document.createElement("canvas");
				const ctx = canvas.getContext("2d");
				// 旋转前，重置画布（因为旋转会改变画布的大小）
				canvas.width = img.height;
				canvas.height = img.width;
				// 将图像居中
				ctx.translate(img.height / 2, img.width / 2);
				// 旋转 90 度
				ctx.rotate(Math.PI / 2);
				// 将图像移回原点
				ctx.translate(-img.width / 2, -img.height / 2);
				// 绘制图像
				ctx.drawImage(img, 0, 0);
				return canvas.toDataURL("image/jpeg", 0.5);
			}

			function getImage(base64) {
				return new Promise((resolve, reject) => {
					const img = new Image();
					img.onload = function () {
						resolve(this);
					};
					img.onerror = reject;
					img.src = base64;
				});
			}

			//]]>
		</script>
		<style type="text/css">
.ui-dashboard .ui-dashboard-column {
	padding-bottom: 10px !important;
}

.clear {
	clear: both;
}

#mainForm\:contentLay .ui-corner-all {
	border-radius: 0px !important;
}

#mainForm\:leftLay,#mainForm\:centerLay,#mainForm\:rightLay .ui-widget-content
	{
	border: 1px solid #666666;
}

#mainForm\:leftLay {
	left: 20px !important;
	width: 410px !important;
}

#mainForm\:centerLay {
	left: 472px !important;
	width: 250px !important;
	text-align: center;
}

#mainForm\:centerLay .ui-helper-hidden {
	display: block;
}

#mainForm\:rightLay {
	border: 1px solid #666666 !important;
	width: 280px !important;
}

#mainForm\:rightLay .ui-layout-unit-content {
	border: none !important;
}

.vedio-photo-choosen {
	border: 3px solid red;
}

.zwxVedioInput {
	width: 400px;
	height: 300px;
	margin-top: 34px;
}

.line-cutoff {
	height: 20px;
	border-bottom: 1px solid #A6C9E2;
	clear: both;
	margin-bottom: 15px;
}

#mainForm\:leftLay .ui-layout-unit-content {
	overflow-y: hidden !important;
}

#mainForm\:centerLay .ui-layout-unit-content {
	overflow-x: hidden !important;
}

.ui-layout-unit .ui-layout-unit-content {
	padding: 0.2em 0em;
	border: 0px none;
	overflow: auto;
}

#mainForm\:dataList_content {
	border: none !important;
}

.ui-datalist-item {
	height: 30px;
	border-bottom: dotted 1px #b2b2b2;
	margin-bottom: 10px;
}

.frpt_diag .ui-dialog-content {
	background-color: #FFFFFF;
	margin-bottom: 25px;
}

.frpt_diag .ui-dialog-content {
	padding: 0em 0em;
}

.frpt_img {
	width: 150px;
	text-align: center;
	vertical-align: top;
	padding-top: 10px;
}

.frpt2_img {
	width: 150px;
	text-align: center;
	vertical-align: top;
	padding-top: 27px;
}

.frpt_tip_head {
	height: 18px;
	font-size: 12px;
}

.frpt_tip_body {
	height: 27px;
	font-size: 13px;
}

.frpt_bottom {
	text-align: right;
	padding-right: 24px;
	padding-bottom: 20px;
	vertical-align: bottom;
}
</style>
		<a href="/resources/files/WebCamera_EL_D_V1.2.3.exe" id="eHwCameraPlugin"
			style="display: none">高拍仪插件</a>
		<p:dialog id="installDialog" widgetVar="InstallDialog" header="提示"
			width="510" height="155" resizable="false" modal="true"
			styleClass="frpt_diag">
			<table style="width: 100%;height: 100%;">
				<tr>
					<td style="height: 24px;"></td>
				</tr>
				<tr>
					<td style="text-align: left;vertical-align: top;">
						<table style="width: 100%;height: 100%;">
							<tr>
								<td rowspan="5" class="frpt_img"><img
									style="margin-top: 20px;" alt="身份证读卡器图标"
									src="/resources/images/read-idc-icon.png" /></td>
								<td class="frpt_tip_head"><span>服务访问失败，请依次检查以下步骤：</span></td>
							</tr>
							<tr>
								<td class="frpt_tip_body"><span>1、检查是否已安装高拍仪相关服务，<a href="javascript:void(0);" onclick="document.getElementById('eHwCameraPlugin').click();PF('InstallDialog').hide()">点击下载服务</a>；</span></td>
							</tr>
							<tr>
								<td class="frpt_tip_body"><span>2、检查是否已启动并启用服务；</span>
								</td>
							</tr>
							<tr>
								<td class="frpt_tip_body"><span>3、检查服务设置中监听端口是否设置为38088；</span>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</p:dialog>
	</h:body>
</f:view>
</html>
