<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{roleBean}"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <style type="text/css">
        .ui-picklist .ui-picklist-list{
            text-align:left;
            height: 340px;
            width: 360px;
            overflow: auto;
        }

        .ui-picklist .ui-picklist-filter {
            padding-right: 0px;
            width: 98%;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[

        //保存的oncomplete
        function handleRoleSave(xhr, status, args) {
            if(!args.validationFailed) {
                 PF('RoleEditDialog').hide();
            }
        }
        //]]>
    </script>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
            <h:outputText value="角色管理"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
			<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
			<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{roleBean.searchAction}" update="dataTable" process="@this,searchRoleName" />
			<p:spacer width="5" />
			<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" update="roleEditDialog" process="@this" action="#{roleBean.addInitAction}" oncomplete="PF('RoleEditDialog').show()">
               	<p:resetInput target="roleEditDialog"/>
           	</p:commandButton>
		</p:outputPanel>
</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchRoleName" value="角色名称：" />
        </p:column>
        <p:column style="text-align:left;padding-left:3px;">
            <p:inputText id="searchRoleName" value="#{roleBean.searchRoleName}" maxlength="10"/>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="角色名称" style="width: 280px;padding-left: 3px;">
        <h:outputText value="#{itm.roleName}" />
    </p:column>
    <p:column headerText="备注" style="width: 400px;padding-left: 3px;">
        <h:outputText value="#{itm.roleDesc}" />
    </p:column>
    <p:column headerText="操作" style="padding-left: 3px;">
        <p:commandLink value="修改" action="#{roleBean.modInitAction}" update=":mainForm:roleEditDialog"
                       oncomplete="PF('RoleEditDialog').show()" process="@this"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}">
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
            <p:resetInput target=":mainForm:roleEditDialog"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"/>
        <p:commandLink value="删除" action="#{roleBean.deleteAction}" update="dataTable" process="@this"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}">
            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"/>
        <p:commandLink value="菜单分配" action="#{roleBean.roleMenuEdit}" immediate="true"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"
                        process="@this">
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}"/>
        <p:commandLink value="机构分配" action="#{roleBean.jgfpInitAction}" update=":mainForm:unitDialog"
                       rendered="#{roleBean.ifAdmin}"
                       oncomplete="PF('UnitDialog').show()" process="@this">
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5" rendered="#{roleBean.ifAdmin}"/>
        <p:commandLink value="用户分配" action="#{roleBean.yhfpInitAction}" update=":mainForm:userDialog"
                       oncomplete="PF('UserDialog').show()" process="@this">
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink value="码表类型授权" action="#{roleBean.codeTypeInitAction}" process="@this"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}">
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
            <p:ajax event="dialogReturn"  listener="#{roleBean.onCodeTypeSelect}" process="@this"/>
        </p:commandLink>
        <p:spacer width="5" />
        <p:commandLink value="码表数据授权" action="#{roleBean.simpleCodeInitAction}" process="@this"
                       rendered="#{itm.unitId==facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.rid}">
            <f:setPropertyActionListener target="#{roleBean.rid}" value="#{itm.rid}"/>
            <p:ajax event="dialogReturn"  listener="#{roleBean.onSimpleCodeSelect}" process="@this"/>
        </p:commandLink>
    </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
    <!-- 新增、修改角色 -->
    <p:dialog id="roleEditDialog" header="角色维护" widgetVar="RoleEditDialog" resizable="false" width="500" height="80" modal="true">
        <p:panelGrid style="width:100%;" id="roleEditGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="roleName" value="角色名称：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="roleName" value="#{roleBean.tsRole.roleName}" maxlength="10"/>
                </p:column>
            </p:row>

            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel for="roleDesc" value="角色描述：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="roleDesc" value="#{roleBean.tsRole.roleDesc}" maxlength="50" size="50"/>
                </p:column>
            </p:row>

        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{roleBean.saveAction}" process="@this,roleName,roleDesc"
                                     update="dataTable,roleEditGrid" oncomplete="handleRoleSave(xhr, status, args);"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('RoleEditDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!-- 菜单授权 -->
    <p:dialog id="menuDialog" header="菜单授权" widgetVar="MenuDialog" resizable="false" width="350" height="400" modal="true">

        <p:tree value="#{roleBean.treeNode}" var="node" selectionMode="checkbox" selection="#{roleBean.selectedNodes}" id="menuTree"
                style="width: 320px;height: 390px;overflow-y: auto;">
            <p:treeNode>
                <h:outputText value="#{node.menuCn}" />
            </p:treeNode>
        </p:tree>

        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="menuSaveBtn" action="#{roleBean.fpMenuAction}" process="@this,menuTree" oncomplete="PF('MenuDialog').hide();"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="menuBackBtn" onclick="PF('MenuDialog').hide();" immediate="true"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

    <!-- 机构分配 -->
    <p:dialog id="unitDialog" header="机构分配" widgetVar="UnitDialog" resizable="false" width="780" height="410" modal="true">

        <p:pickList id="unitPickList" value="#{roleBean.dualListModel}" var="unit"
                    itemValue="#{unit}" itemLabel="#{unit.unitName}" converter="system.UnitConvert"
                    showSourceControls="false" showTargetControls="false" showCheckbox="true"
                    showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
                    effect="drop">

            <f:facet name="sourceCaption">可选单位</f:facet>
            <f:facet name="targetCaption">已选单位</f:facet>

            <p:column style="width:25%;text-align: left;">#{unit.zoneName}</p:column>
            <p:column style="width:75%;text-align: left;">#{unit.unitName}</p:column>
        </p:pickList>

        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="unitSaveBtn" action="#{roleBean.jgfpAction}" process="@this,unitPickList" oncomplete="PF('UnitDialog').hide();"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="unitBackBtn" onclick="PF('UnitDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

    <!-- 用户分配 -->
    <p:dialog id="userDialog" header="用户分配" widgetVar="UserDialog" resizable="false" width="800" height="450" modal="true" >
    
        <table width="100%">
            <tr>
                <td width="40" style="text-align: right;padding-right: 3px">地区：</td>
                <td style="text-align: left;padding-right: 3px">
                	<zwx:ZoneSingleComp zoneList="#{roleBean.zoneList}" zoneCode="#{roleBean.userZoneCode}"
                		zoneName="#{roleBean.userZoneName}" id="editZone" onchange="onNodeSelect()" zoneType="#{roleBean.userZoneLevel}"/>
                	<p:remoteCommand name="onNodeSelect" action="#{roleBean.onNodeSelect}" process="@this,editZone,userPickList"
                                 update=":mainForm:unitListOneMenu,:mainForm:userPickList"/>  
                </td>
                <td width="40" style="text-align: right;padding-right: 3px">单位：</td>
                <td style="text-align: left;padding-right: 3px">
                    <p:selectOneMenu id="unitListOneMenu" value="#{roleBean.userUnitId}" style="width: 160px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{roleBean.unitMap}"/>
                        <p:ajax event="change" update="userPickList,officeListOneMenu" process="@this,userPickList" listener="#{roleBean.onUnitChange}"/>
                    </p:selectOneMenu>
                </td>
                <td width="40" style="text-align: right;padding-right: 3px">科室：</td>
                <td style="text-align: left;padding-right: 3px">
                    <p:selectOneMenu id="officeListOneMenu" value="#{roleBean.userUnitOfficeId}" style="width: 160px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{roleBean.unitOfficeMap}"/>
                        <p:ajax event="change" update="userPickList" process="@this,userPickList" listener="#{roleBean.onofficeChange}"/>
                    </p:selectOneMenu>
                </td>
            </tr>
        </table>

        <p:pickList id="userPickList" value="#{roleBean.userDualListModel}" var="user"
                    itemValue="#{user}" itemLabel="#{user.username}" converter="system.UserConvert"
                    showSourceControls="false" showTargetControls="false" showCheckbox="true"
                    showSourceFilter="true" showTargetFilter="true" filterMatchMode="contains"
                    effect="drop">

            <f:facet name="sourceCaption">可选用户</f:facet>
            <f:facet name="targetCaption">已选用户</f:facet>

            <p:column style="width:40%;text-align: left;">#{user.username}</p:column>
            <p:column style="width:60%;text-align: left;">#{roleBean.getOfficeName(user.rid)}</p:column>
        </p:pickList>

        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="userSaveBtn" action="#{roleBean.yhfpAction}" process="@this,userPickList" oncomplete="PF('UserDialog').hide();"/>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="userBackBtn" onclick="PF('UserDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:define>

</ui:composition>











