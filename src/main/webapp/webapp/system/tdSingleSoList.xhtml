<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
    xmlns:p="http://primefaces.org/ui"
    xmlns:c="http://java.sun.com/jsp/jstl/core">
    
  <ui:param name="mgrbean" value="#{tdSingleSoBean}" />
  
	<h:head>
		<title></title>
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
	</h:head>
	 <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
	<f:view>
	<body class="deskbody">
	<h:form id="mainForm">
	<div style="text-align:center;height:30px;padding-top:10px;">
		<p:commandButton icon="ui-icon-circle-plus" action="#{tdSingleSoBean.addAction}" 
					oncomplete="PF('SystemDialog').show();"	update="systemGrid">
		</p:commandButton>
	</div>
	<div id="main" style="padding-top:80px;padding-left:100px;padding-right:100px;height:90%;">
		<c:forEach items="#{tdSingleSoBean.tableTypeList}" var="v">
		<p:row>
			<p:column style="padding-left:5px;padding-right:5px;padding-top:5px;">
				<p:graphicImage style="width:64px;height:64px;" value="/files/dsfLogo/#{v.tsDsfSys.sysIcon}" />
				<p:commandLink action="#{tdSingleSoBean.onClick(v)}" value="#{v.tsDsfSys.sysName}"
							 process="@this" update="systemGrid"/>	
				<p:spacer width="20" />
			</p:column>
		</p:row>
		</c:forEach>		
	</div>
	<p:dialog id="systemDg" header="系统维护" widgetVar="SystemDialog" resizable="false" width="400" height="190">
		<p:panelGrid style="width:100%;" id="systemGrid">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="用户：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 160px;">
					<h:outputText id="username" value="#{tdSingleSoBean.tsUserInfo.username}"/>
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="系统：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 160px;">
					 <p:selectOneMenu disabled="false" id="systemMenu" value="#{tdSingleSoBean.tsDsfSys.rid}" style="width: 180px;">
                    	<f:selectItem itemLabel="--请选择--" itemValue=""/>
                    	<f:selectItems value="#{tdSingleSoBean.systemMap}"/>
                	 </p:selectOneMenu>
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:250px;">
                    <h:outputLabel value="程序地址：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 250px;">
           			<p:inputText id="messages" value="#{tdSingleSoBean.sysUrl}" style="width:250px;"/>
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="参数名：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 160px;">
					<p:inputText id="paramEn" value="#{tdSingleSoBean.tsDsfLoginfParam.paramEn}" style="width:160px" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="参数值：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 160px;">
					<p:inputText id="paramValue" value="#{tdSingleSoBean.tsDsfLoginfParam.paramValue}" style="width:160px"/>
				</p:column>
			</p:row>
		</p:panelGrid>
		<f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{tdSingleSoBean.saveAction}" process="@this,username,systemMenu,messages,paramEn,paramValue" update=":mainForm"/>
                	<p:spacer width="5" />
                	<p:commandButton value="删除" id="delBtn" action="#{tdSingleSoBean.delAction}" process="@this,username,systemMenu,messages,paramEn,paramValue" update=":mainForm">
                		<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                	</p:commandButton>
                	<p:spacer width="5" />
                	<p:commandButton value="取消" icon="ui-icon-close" id="backBtn" type="button" onclick="PF('SystemDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
	</p:dialog>	
	<p:confirmDialog global="true" showEffect="fade" >
        <p:commandButton value="是" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
        <p:commandButton value="否" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
    </p:confirmDialog>
	</h:form>
	</body>
	</f:view>
</ui:composition>