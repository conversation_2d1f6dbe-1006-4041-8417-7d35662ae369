<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
<!-- 托管Bean -->
<ui:param name="mgrbean" value="#{officeBean}"/>

<!-- 样式或javascripts -->
<ui:define name="insertScripts">
    <style type="text/css">
        .ui-tree .ui-tree-container{
            overflow-x:hidden;
        }

        .ui-panelgrid td {
            padding-top: 2px;
            padding-bottom: 2px;
            padding-left: 5px;
            padding-right: 0px;
        }
    </style>
</ui:define>

<!-- 标题栏 -->
<ui:define name="insertTitle">
    <p:row>
        <p:column colspan="#{officeBean.ifAdmin?6:4}" style="text-align:left;padding-left:5px; height: 20px;">
            <h:outputText value="科室管理"/>
        </p:column>
    </p:row>

</ui:define>

<!-- 按钮 -->
<ui:define name="insertButtons">

		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{officeBean.searchAction}" update="dataTable"
                             process="@this,dataTable,searchOfficeName,searchState,searchUnit"/>
            <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{officeBean.addInitAction}"
                             update=":mainForm:manageMan,:mainForm:deptLeader,:mainForm:officetype,:mainForm:officeCode,
                       :mainForm:unitrid,:mainForm:officeName,:mainForm:officeSimple,:mainForm:officetel,:mainForm:isYjOffice,
                       :mainForm:officefax,:mainForm:upUnitTree,:mainForm:upOfficeName,:mainForm:upOfficeRid,:mainForm:num"
                              oncomplete="PF('OfficeEditDialog').show()" process="@this">
                <p:resetInput target=":mainForm:officeEditDialog"/>
             </p:commandButton>
				<p:commandButton value="归并记录查询" icon="ui-icon-clock" id="searchRefBtn" action="#{officeBean.searchRefAction}" process="@this" update=":mainForm:dataRefTableDialog"
                             oncomplete="PF('DataRefTableDialog').show()"/>
			</h:panelGrid>
		</p:outputPanel>

</ui:define>

<!-- 查询条件 -->
<ui:define name="insertSearchConditons">
    <p:row>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchOfficeName" value="科室名称："/>
        </p:column>
        <p:column style="text-align:left;padding-left:3px;width:20%;">
            <p:inputText id="searchOfficeName" value="#{officeBean.searchOfficeName}" maxlength="25"/>
        </p:column>
        <p:column rendered="#{officeBean.ifAdmin}" style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel for="searchUnit" value="单位选择："/>
        </p:column>
        <p:column rendered="#{officeBean.ifAdmin}"  style="text-align:left;padding-left:3px;padding-top:6px;width:20%;">
            <p:selectOneMenu id="searchUnit" value="#{officeBean.unitId}" filter="true" filterMatchMode="contains">
                <f:selectItems value="#{officeBean.untiList}"/>
            </p:selectOneMenu>
        </p:column>
        <p:column style="text-align:right;padding-right:3px;width:10%;">
            <h:outputLabel value="状态："/>
        </p:column>
        <p:column style="text-align:left;padding-left: 3px;width:30%;">
            <p:selectOneRadio  style="width: 240px" id="searchState" value="#{officeBean.searchState}">
                <f:selectItem itemLabel="启用" itemValue="1"/>
                <f:selectItem itemLabel="停用" itemValue="0"/>
                <f:selectItem itemLabel="已归并" itemValue="2"/>
            </p:selectOneRadio>
        </p:column>
    </p:row>
</ui:define>

<!-- 表格列 -->
<ui:define name="insertDataTable">
    <p:column headerText="序号" style="width: 40px;text-align: center;">
        <p:outputLabel value="#{R+1}"/>
    </p:column>
    <p:column headerText="科室名称" style="padding-left:#{itm.paddingLeftSize*10}px;width:140px; ">
        <h:outputText value="#{itm.officename}"/>
    </p:column>
    <p:column headerText="科室编码" style="width: 80px;">
        <h:outputText value="#{itm.officecode}"/>
    </p:column>
    <p:column headerText="科室负责人" style="width: 80px; text-align: center;">
        <h:outputText value="#{itm.tbSysEmpByDeptLeaderId.empName}"/>
    </p:column>
    <p:column headerText="办公室电话" >
        <h:outputText value="#{itm.officetel}"/>
    </p:column>
    <p:column headerText="科室类型" style="width: 100px; text-align: center;">
        <h:outputText value="行政科室" rendered="#{itm.officetype==1}"/>
        <h:outputText value="业务科室" rendered="#{itm.officetype==2}"/>
    </p:column>
    <p:column headerText="状态" style="width:40px;text-align: center;">
        <h:outputText value="启用" rendered="#{itm.ifReveal==1}"/>
        <h:outputText value="停用" rendered="#{itm.ifReveal==0}"/>
        <h:outputText value="已归并" rendered="#{itm.ifReveal==2}"/>
    </p:column>
    <p:column headerText="所属单位" style="width: 80px; text-align: center;">
        <h:outputText value="#{itm.tsUnit.unitSimpname}"/>
    </p:column>
    <p:column headerText="操作" style="width: 160px;text-align: center;">
        <p:commandLink value="修改" rendered="#{itm.ifReveal!=2}" action="#{officeBean.modInitAction}"
                       update=":mainForm:manageMan,:mainForm:deptLeader,:mainForm:officetype,:mainForm:officeCode,
                       :mainForm:unitrid,:mainForm:officeName,:mainForm:officeSimple,:mainForm:officetel,:mainForm:isYjOffice,
                       :mainForm:officefax,:mainForm:upUnitTree,:mainForm:upOfficeName,:mainForm:upOfficeRid,:mainForm:num"
                       oncomplete="PF('OfficeEditDialog').show()" process="@this">
            <f:setPropertyActionListener target="#{officeBean.rid}" value="#{itm.rid}"/>
            <f:setPropertyActionListener target="#{officeBean.tsOffice}" value="#{itm}"/>
            <f:setPropertyActionListener target="#{officeBean.oldOfficecode}" value="#{itm.officecode}"/>
            <p:resetInput target=":mainForm:officeEditDialog"/>
        </p:commandLink>
        <p:spacer width="5"/>
        <p:commandLink value="删除" rendered="#{itm.ifReveal!=2}" action="#{officeBean.deleteAction}" update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{officeBean.rid}" value="#{itm.rid}"/>
        </p:commandLink>
        <p:spacer width="5"/>
			<p:commandLink rendered="#{itm.ifReveal==1}" value="停用" action="#{officeBean.stateChangeAction}" update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{officeBean.rid}" value="#{itm.rid}"/>
            <f:setPropertyActionListener target="#{officeBean.ifReveal}" value="0"/>
        </p:commandLink>

			<p:commandLink rendered="#{itm.ifReveal==0}" value="启用" action="#{officeBean.stateChangeAction}" update="dataTable" process="@this">
            <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
            <f:setPropertyActionListener target="#{officeBean.rid}" value="#{itm.rid}"/>
            <f:setPropertyActionListener target="#{officeBean.ifReveal}" value="1"/>
        </p:commandLink>

        <p:spacer width="5" rendered="#{itm.ifReveal==1}"/>
        <p:commandLink value="归并" rendered="#{itm.ifReveal==1}"  update=":mainForm:officeTree,:mainForm:userOfficeName,:mainForm:userOfficeId,:mainForm:oldOfficeId,:mainForm:officename"
                       oncomplete="PF('OfficeRefDialog').show()" action="#{officeBean.modRefInnitAction}" process="@this">
            <f:setPropertyActionListener target="#{officeBean.tsOffice}" value="#{itm}"/>
        </p:commandLink>
    </p:column>
</ui:define>

<!-- 弹出框 -->
<ui:define name="insertDialogs">
    <!-- 新增、修改地区 -->
		<p:dialog id="officeEditDialog" dynamic="true" header="科室管理" widgetVar="OfficeEditDialog" resizable="false" width="500" modal="true">
        <p:panelGrid style="width:100%;" id="officeEditGrid">
            <p:row rendered="#{officeBean.ifAdmin}">
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="unitrid" value="单位选择："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneMenu id="unitrid" style="width: 220px" value="#{officeBean.unitEditRid}" filter="true" filterMatchMode="contains">
                        <f:selectItems value="#{officeBean.unitEditList}"/>
                        <p:ajax process="@this"  event="change" listener="#{officeBean.changeUnitRefOffice}" update="manageMan,deptLeader,upUnitTree,upOfficeName,upOfficeRid"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="officeName" value="科室名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="officeName" value="#{officeBean.tsOffice.officename}" maxlength="25"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="officeSimple" value="科室简称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="officeSimple" value="#{officeBean.tsOffice.simplName}" maxlength="25"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="officeCode" value="科室编码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText id="officeCode" value="#{officeBean.tsOffice.officecode}" maxlength="10"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <font color="red">*</font>
                    <h:outputLabel for="officetype" value="科室类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio style="width:240px;" id="officetype" value="#{officeBean.tsOffice.officetype}">
                        <f:selectItem itemLabel="行政科室" itemValue="1"/>
                        <f:selectItem itemLabel="业务科室" itemValue="2"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel  value="上级科室选择："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText   id="upOfficeName"  value="#{officeBean.upOfficeName}" readonly="true"  style="width:160px;">
                    </p:inputText>
						<p:overlayPanel id="upOfficePanel" for="upOfficeName" style="width:300px;" widgetVar="upOfficePanel">

							<p:tree dynamic="true" value="#{officeBean.upUnitTreeNode}" var="node" selectionMode="single" selection="#{officeBean.selectedUpNote}" id="upUnitTree"
                                 style="width:280px;height:220px;overflow-y: auto;">
								<p:ajax event="select" update=":mainForm:upOfficeName,:mainForm:upOfficeRid" partialSubmit="true" listener="#{officeBean.onNodeUpSelect}"
									oncomplete="PF('upOfficePanel').hide();" />
                            <p:treeNode>
                                <h:outputText value="#{node.simplName}"/>
                            </p:treeNode>
                        </p:tree>

                    </p:overlayPanel>
                    <h:inputHidden id="upOfficeRid" value="#{officeBean.upOfficeRid}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText converterMessage="序号，只能输入数字！" maxlength="2"  id="num" value="#{officeBean.tsOffice.num}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel  value="科室负责人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneMenu id="deptLeader" value="#{officeBean.tbSysEmpByDeptLeaderId}">
                        <f:selectItems value="#{officeBean.empList}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel  value="分管领导："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneMenu id="manageMan" value="#{officeBean.tbSysEmpByManageManid}">
                        <f:selectItems value="#{officeBean.empList2}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel for="officetype" value="是否应急管理部门："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio style="width:160px;" id="isYjOffice" value="#{officeBean.tsOffice.isYjOffice}">
                        <f:selectItem itemLabel="是" itemValue="1"/>
                        <f:selectItem itemLabel="否" itemValue="0"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel  value="办公室电话："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText maxlength="25" id="officetel" value="#{officeBean.tsOffice.officetel}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel  value="办公室传真："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText maxlength="25" id="officefax" value="#{officeBean.tsOffice.officefax}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:center;" colspan="2">
                    <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{officeBean.saveAction}"
							process="@this,num,manageMan,deptLeader,upOfficeRid,officetype,officeCode,unitrid,officeName,officeSimple,officetel,officefax,isYjOffice" update="dataTable,officeEditGrid,foucs" />
                    <p:spacer width="5"/>
						<p:commandButton value="取消" icon="ui-icon-close" id="backBtn" onclick="PF('OfficeEditDialog').hide();" immediate="true" />
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:dialog>


		<p:dialog id="officeRefDialog" header="科室归并" widgetVar="OfficeRefDialog" resizable="false" width="500" height="140" modal="true" dynamic="true">
        <p:panelGrid style="width:100%;" id="officeRefGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 30px">
                    <h:outputLabel value="原科室名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel id="officename" value="#{officeBean.tsOffice.officecode}　　#{officeBean.tsOffice.officename}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:30%;height: 30px">
                    <font color="red">*</font>
                    <h:outputLabel for="userOfficeId" value="新科室名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px; width: 100%">
                    <p:inputText   id="userOfficeName"  value="#{officeBean.userOfficeName}" readonly="true"  style="width:160px;">
                    </p:inputText>
						<p:overlayPanel id="officePanel" for="userOfficeName" style="width:300px;" widgetVar="OfficePanel">

							<p:tree dynamic="true" value="#{officeBean.treeNode}" var="node" selectionMode="single" selection="#{officeBean.selectedNode}" id="officeTree"
                                style="width: 280px;height: 220px;overflow-y: auto;">

								<p:ajax event="select" update=":mainForm:userOfficeName,:mainForm:userOfficeId" partialSubmit="true" listener="#{officeBean.onNodeSelect}"
									oncomplete="PF('OfficePanel').hide();" />
                            <p:treeNode>
                                <h:outputText value="#{node.officename}"/>
                            </p:treeNode>
                        </p:tree>

                    </p:overlayPanel>
                    <h:inputHidden id="userOfficeId" value="#{officeBean.userOfficeId}"/>
                    <h:inputHidden id="oldOfficeId" value="#{officeBean.tsOffice.rid}"/>
                </p:column>

            </p:row>
            <p:row>
                <p:column style="text-align:center;" colspan="2">
						<p:commandButton value="保存" icon="ui-icon-check" id="saveRefBtn" action="#{officeBean.saveRefAction}" process="@this,userOfficeId,oldOfficeId"
                                     update=":mainForm:dataTable,:mainForm:officeRefGrid" >
                        <p:confirm header="消息确认框" message="确定要科室归并吗？" icon="ui-icon-alert"/>
                    </p:commandButton>
                    <p:spacer width="5"/>
						<p:commandButton value="取消" icon="ui-icon-close" id="backRefBtn" onclick="PF('OfficeRefDialog').hide();" immediate="true" />
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:dialog>

		<p:dialog id="dataRefTableDialog" header="科室归并记录查询" widgetVar="DataRefTableDialog" resizable="false" width="800" height="400" modal="true">
        <table border="0" width="100%" style="height: 100%">
				<tr>
					<td style="text-align:left;padding-left: 3px; vertical-align: top;height: 30px;">
                <table border="0" width="100%" style="height: 100%">
                    <tr>
								<td width="30%" style="text-align: left"><h:outputLabel value="科室名称：" /> <p:spacer width="5" /> <p:inputText value="#{officeBean.selRefOffceName}" id="selRefOffceName" /></td>
								<td width="#{officeBean.ifAdmin?'10%':'0'}" style="text-align: right"><h:outputLabel rendered="#{officeBean.ifAdmin}" value="单位选择：" /></td>
								<td width="#{officeBean.ifAdmin?'30%':'0'}" style="text-align: left"><p:selectOneMenu rendered="#{officeBean.ifAdmin}" id="searchUnit2" value="#{officeBean.refunitId}">
                                <f:selectItems value="#{officeBean.untiList}"/>
                            </p:selectOneMenu></td>
								<td style="text-align: left"><p:commandButton value="查询" rendered="#{!officeBean.ifAdmin}" icon="ui-icon-search" id="searchRefBtn2"
										action="#{officeBean.searchRefAction}" update="dataRefTable" process="@this,selRefOffceName" /> <p:commandButton value="查询" rendered="#{officeBean.ifAdmin}"
										icon="ui-icon-search" id="searchRefBtn3" action="#{officeBean.searchRefAction}" update="dataRefTable" process="@this,selRefOffceName,searchUnit2" /></td>
							</tr>
						</table>
                        </td>
				</tr>
				<tr>
					<td style="text-align:center; vertical-align: top;"><p:dataTable var="itm" value="#{officeBean.refList}" paginator="true" rows="10" paginatorPosition="bottom"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}" rowsPerPageTemplate="10,20,50" id="dataRefTable" lazy="true"
							emptyMessage="没有您要找的记录！" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" rowIndexVar="R">
                    <p:column headerText="序号" style="width: 80px;text-align: center;">
                        <p:outputLabel value="#{R+1}"/>
                    </p:column>
                    <p:column headerText="原科室名称" style="text-align: center; ">
                        <h:outputText value="#{itm.tsOfficeOld.officecode}　#{itm.tsOfficeOld.officename}"/>
                    </p:column>
                    <p:column headerText="归并后科室名称" style="text-align: center; ">
                        <h:outputText value="#{itm.tsOfficeNew.officecode}　#{itm.tsOfficeNew.officename}"/>
                    </p:column>
                    <p:column headerText="创建日期" style="text-align: center; ">
                        <h:outputText  value="#{itm.createDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8"/>
                        </h:outputText>
                    </p:column>
                    <p:column headerText="操作" style="text-align: center;display: none; ">
                        <p:spacer width="5"/>
                        <p:commandLink value="取消归并" rendered="#{itm.tsOfficeNew.ifReveal==1}" action="#{officeBean.cancelRefAction}" update=":mainForm:dataRefTable,:mainForm:dataTable"
                                       process="@this">
                            <p:confirm header="消息确认框" message="确定要取消归并吗？" icon="ui-icon-alert"/>
                            <f:setPropertyActionListener target="#{officeBean.tsDeptMdref}" value="#{itm}"/>
                        </p:commandLink>
                    </p:column>
						</p:dataTable></td>
				</tr>
				<tr>
					<td style="text-align:center;vertical-align: bottom;"><p:commandButton value="关闭" icon="ui-icon-close" id="backRefBtn2" onclick="PF('DataRefTableDialog').hide();"
							immediate="true" /></td>
				</tr>
        </table>
    </p:dialog>
</ui:define>

</ui:composition>