<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="通讯方式维护"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="4"
                         style="border-color:transparent;padding:0;">
                    <span class="ui-separator"><span
                            class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"  process="@this,searchState"
                                 action="#{tbTxTypeBean.searchAction}" update=":mainForm:dataTable"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 5px;">
                <p:selectManyCheckbox  style="width: 180px" id="searchState" value="#{tbTxTypeBean.searchStates}">
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <ui:define name="insertContent">
        <p:dataTable id="dataTable" value="#{tbTxTypeBean.resultList}" var="itm" emptyMessage="没有您要找的记录！">
            <p:column headerText="序号" style="width:80px;text-align: center">
                <h:outputLabel value="#{itm.xh}"/>
            </p:column>
            <p:column headerText="通讯方式" style="width:120px;text-align: center">
                <h:outputLabel value="#{itm.txType.typeCN}"/>
            </p:column>
            <p:column headerText="实现类" style="width:150px;text-align: center">
                <h:outputLabel value="#{tbTxTypeBean.getImpl(itm.implClass)}"/>
            </p:column>
            <p:column headerText="状态" style="width:80px;text-align: center">
                <h:outputLabel rendered="#{itm.status == 0}" value="停用"/>
                <h:outputLabel rendered="#{itm.status == 1}" value="启用"/>
            </p:column>
            <p:column headerText="操作" style="padding-left: 3px;width: 300px;">
                <p:commandLink rendered="#{itm.status==1}" value="停用" process="@this" action="#{tbTxTypeBean.stateChangeAction}" update="dataTable" >
                    <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                    <f:setPropertyActionListener target="#{tbTxTypeBean.txTyperid}" value="#{itm.rid}"/>
                    <f:setPropertyActionListener target="#{tbTxTypeBean.changeStateValue}" value="0"/>
                </p:commandLink>
                <p:commandLink rendered="#{itm.status==0}" value="启用" process="@this" action="#{tbTxTypeBean.stateChangeAction}" update="dataTable" >
                    <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                    <f:setPropertyActionListener target="#{tbTxTypeBean.txTyperid}" value="#{itm.rid}"/>
                    <f:setPropertyActionListener target="#{tbTxTypeBean.changeStateValue}" value="1"/>
                </p:commandLink>
                <p:spacer width="5" rendered="#{itm.status==0}"/>
                <p:commandLink rendered="#{itm.status==0}" value="修改" process="@this"
                               update=":mainForm:editDialog" oncomplete="PF('EditDialog').show()"  >
                    <f:setPropertyActionListener target="#{tbTxTypeBean.tsTxtype}" value="#{itm}"/>
                    <p:resetInput target=":mainForm:editDialog"/>
                </p:commandLink>
            </p:column>
        </p:dataTable>
        <p:dialog id="editDialog" header="通讯方式维护" widgetVar="EditDialog" resizable="false" width="400" modal="true">
            <p:panelGrid style="width:100%;" id="editGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width: 30%;height: 30px;">
                        <h:outputText value="通讯方式："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <h:outputText value="#{tbTxTypeBean.tsTxtype.txType.typeCN}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width: 30%;height: 30px;">
                        <h:outputText value="实现类："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneMenu id="impClass" value="#{tbTxTypeBean.tsTxtype.implClass}" style="width: 200px;">
                            <f:selectItems value="#{tbTxTypeBean.implClassMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width: 30%;height: 30px;">
                        <h:outputText value="序号："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText value="#{tbTxTypeBean.tsTxtype.xh}" maxlength="2" size="10" converterMessage="请输入正确的序号！"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:center;" colspan="2">
                        <p:commandButton value="保存" icon="ui-icon-check"
                                         action="#{tbTxTypeBean.saveAction}"
                                         process="@this,editGrid" update="dataTable,editGrid"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close"
                                         onclick="PF('EditDialog').hide();" immediate="true"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
    </ui:define>

</ui:composition>