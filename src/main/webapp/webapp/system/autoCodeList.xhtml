<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/mainTemplate_noPage.xhtml">

    <!-- 脚本 -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <style type="text/css">
            .ui-selectmanycheckbox.ui-widget td, .ui-selectoneradio.ui-widget td {
                border: 0 none;
                padding: 2px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="编号规则配置"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{autoCodeBean.searchAction}" update="businessTable"
					process="@this,searchIdCode,searchBsDesc,searchSystemType" />
			</h:panelGrid>
		</p:outputPanel>
	</ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="编码：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 250px;">
                <p:inputText id="searchIdCode" value="#{autoCodeBean.searchIdCode}" maxlength="15"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="业务描述：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText id="searchBsDesc" value="#{autoCodeBean.searchBsDesc}" maxlength="20"/>
            </p:column>
        </p:row>

        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:180px;">
                <h:outputText value="系统类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneMenu id="searchSystemType" value="#{autoCodeBean.searchSystemType}" style="width: 180px;">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItems value="#{autoCodeBean.systemTypeMap}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>

    </ui:define>

    <!-- 具体内容 -->
    <ui:define name="insertContent">
        <!-- 系统自动编号 -->
        <p:dataTable var="itm" value="#{autoCodeBean.businessList}" id="businessTable" emptyMessage="没有您要找的记录！" >
            <p:column headerText="编码" style="width: 120px;padding-left: 3px;">
                <h:outputText value="#{itm.idcode}" />
            </p:column>
            <p:column headerText="业务描述" style="width: 350px;padding-left: 3px;">
                <h:outputText value="#{itm.bsDesc}" />
            </p:column>
            <p:column headerText="前缀" style="width: 150px;padding-left: 3px;">
                <h:outputText value="#{itm.pfx}" />
            </p:column>
            <p:column headerText="分隔符" style="width: 80px;text-align: center; ">
                <h:outputText value="#{itm.splitStr}" />
            </p:column>
            <p:column headerText="后缀" style="width: 80px;text-align: center; ">
                <h:outputText value="#{itm.suf}" />
            </p:column>
            <p:column headerText="流水号长度" style="width: 80px;text-align: center; ">
                <h:outputText value="#{itm.codeLenth}" />
            </p:column>
            <p:column headerText="操作">
                <p:commandLink value="修改" update=":mainForm:codeEditDialog" action="#{autoCodeBean.editInitAction}"
                               oncomplete="PF('CodeEditDialog').show()" process="@this">
                    <f:setPropertyActionListener target="#{autoCodeBean.tsCodeRule}" value="#{itm}"/>
                    <p:resetInput target=":mainForm:codeEditDialog"/>
                </p:commandLink>
            </p:column>
            <f:facet name="footer">
                <p:outputPanel style="text-align: right;">
                    <h:outputText value="查询到#{autoCodeBean.businessList.size()}条记录"/>
                </p:outputPanel>
            </f:facet>
        </p:dataTable>

        <!-- 新增、修改码表 -->
        <p:dialog id="codeEditDialog" header="编号规则配置" widgetVar="CodeEditDialog" resizable="false" width="600" height="300" modal="true">
            <p:panelGrid style="width:100%;" id="codeEditGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 25px;">
                        <h:outputText value="编码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <h:outputText value="#{autoCodeBean.tsCodeRule.idcode}"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 25px;">
                        <h:outputText value="业务描述："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <h:outputText value="#{autoCodeBean.tsCodeRule.bsDesc}"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="实现类："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneMenu id="impClass" value="#{autoCodeBean.tsCodeRule.impClass}">
                            <f:selectItems value="#{autoCodeBean.implClassMap}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="前缀："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="pfx" value="#{autoCodeBean.tsCodeRule.pfx}" maxlength="10"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="分割符："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="splitStr" value="#{autoCodeBean.tsCodeRule.splitStr}" maxlength="10"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="后缀："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="suf" value="#{autoCodeBean.tsCodeRule.suf}" maxlength="10"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font style="color: red">*</font>
                        <h:outputText value="流水号长度："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="codeLenth" value="#{autoCodeBean.tsCodeRule.codeLenth}" maxlength="2"
                                     converterMessage="流水号长度格式不正确！" size="5" required="true" requiredMessage="流水长度不允许为空！"/>
                    </p:column>
                </p:row>

                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputText value="测试结果："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                        <h:outputText id="testCode" value="#{autoCodeBean.testCode}" style="color: #FF0000"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="保存" icon="ui-icon-check" id="ruleSaveBtn" action="#{autoCodeBean.saveAction}"
                                         process="@this,codeEditGrid" update="businessTable"/>
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="ruleBackBtn" onclick="PF('CodeEditDialog').hide();" immediate="true"/>
                        <p:spacer width="5" />
                        <p:commandButton value="测试按钮" icon="ui-icon-search" id="testBtn" action="#{autoCodeBean.testAction}"
                                         process="@this,codeEditGrid" update="testCode"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>

</ui:composition>











