<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	template="/WEB-INF/templates/system/viewTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdDynaFormDesignBean}" />
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />

	<!-- 样式或javascripts -->
	<ui:define name="insertEditScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
		<style type="text/css">
body {
	overflow-x: hidden;
}
</style>

	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="动态表单维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;" id="btnPanel">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{tdDynaFormDesignBean.backAction}" update=":tabView"
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
	</ui:define>

	<!-- 其余内容 -->
	<ui:define name="insertOtherContents">
		<p:outputPanel id="editPanelInfo">
			<p:panel header="基本信息" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="表单编号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:outputLabel value="自动生成" style="color:gray;"
								rendered="#{tdDynaFormDesignBean.bindTdFormDef.rid == null}" />
							<p:outputLabel
								value="#{tdDynaFormDesignBean.bindTdFormDef.formCode}"
								rendered="#{tdDynaFormDesignBean.bindTdFormDef.rid != null}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="表单类别：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormDesignBean.bindTdFormDef.tdFormTypeByTypeId.typeName}" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:100px;">
							<p:outputLabel value="表单名称：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:outputLabel
								value="#{tdDynaFormDesignBean.bindTdFormDef.formName}" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:100px;">
							<p:outputLabel value="数据表：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:outputLabel value="#{tdDynaFormDesignBean.dynaFormName}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="报表模版：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:outputLabel value="#{tdDynaFormDesignBean.selRpt.rptnam}" />
						</p:column>
					</p:row>
				</p:panelGrid>

			</p:panel>

			<p:panel header="HTML代码" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成表单HTML代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="htmlCode" readonly="true"
								value="#{tdDynaFormDesignBean.bindTdFormDef.html}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成列表HTML代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="searchHtmlCode" readonly="true"
								value="#{tdDynaFormDesignBean.bindTdFormDef.searchHtml}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成列表SQL代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="searchSqlCode" readonly="true"
								value="#{tdDynaFormDesignBean.bindTdFormDef.searchSql}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:panel>
		</p:outputPanel>

	</ui:define>
</ui:composition>











