<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
	<h:form id="mainform1">
		<p:panelGrid style="width:100%;" id="zz">
			<f:facet name="header">
				<p:row>
					<p:column colspan="4"
						style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="第三方系统注册详情表" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
				style="border-color:transparent; padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="返回" action="#{sysBean.backAction}"  icon="ui-icon-close"
					immediate="true" update=":tabView" process="@this" />
			</h:panelGrid>
		</p:outputPanel>

		<p:fieldset legend="第三方系统注册表" toggleable="true" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:panelGrid style="width:100%;height:100%;" id="mainGrid4">
				<p:row>
					<p:column
						style="text-align:right;padding-right:3px;width:180px;height:25px">
						<p:outputLabel value="系统名称" />
					</p:column>
					<p:column style="width:450px;">
						<p:outputLabel value="#{sysBean.tsDsfSys.sysName}" />
					</p:column>

					<p:column
						style="text-align:right;padding-right:3px;width:180px;height:25px">
						<p:outputLabel value="系统简称" />
					</p:column>
					<p:column>
						<p:outputLabel value="#{sysBean.tsDsfSys.sysJc}" />
					</p:column>
				</p:row>
				<p:row>
					
				<p:column style="text-align:right;padding-right:3px;width:180px;height:25px">
						<p:outputLabel value="系统图标：" />
					</p:column>
					<p:column >
						<h:panelGroup id="uploadGroup">
                       
                        <h:panelGroup rendered="#{sysBean.tsDsfSys.sysIcon!=null}">
                            <h:graphicImage url="/webFile#{sysBean.tsDsfSys.sysIcon}" width="64px" height="64px"/>
                            <p:spacer width="5"/>                          
                        </h:panelGroup>                       
                    </h:panelGroup>
					</p:column>

				
					<p:column
						style="text-align:right;padding-right:3px;width:180px;height:25px">
						<p:outputLabel value="系统类型：" />
					</p:column>
					<p:column>
						<p:outputLabel value="#{sysBean.tsDsfSys.xtType==1?'BS':'CS'}" />
					</p:column>
				</p:row>
				<p:row rendered="#{sysBean.tsDsfSys.xtType=='1'}">
				<p:column style="text-align:right;padding-right:3px;width:180px;height:25px" >
						<p:outputLabel value="程序地址：" />
					</p:column>
					<p:column colspan="3">
						<p:outputLabel value="#{sysBean.tsDsfSys.sysUrl}" />
					</p:column>

					
				</p:row>

			</p:panelGrid>
		</p:fieldset>
		<!--子表查询  -->
		<p:fieldset legend="第三方系统注册参数表" toggleable="true" toggleSpeed="500"
			style="margin-top: 5px;margin-bottom: 5px;">
			<p:dataTable value="#{sysBean.list}" var="item" id="oo" >
				<p:column headerText="参数名" style="height:25px">
					<h:outputText value="#{item.paramCn}" />
				</p:column>
				<p:column headerText="参数字段名">
					<h:outputText value="#{item.paramEn}" />
				</p:column>
				<p:column headerText="参数描述">
					<h:outputText value="#{item.paramDesc}" />
				</p:column>
			</p:dataTable>

		</p:fieldset>
	</h:form>

</ui:composition>