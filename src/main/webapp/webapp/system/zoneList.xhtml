<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.system.web.ZoneBean"-->
    <ui:param name="mgrbean" value="#{zoneBean}"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <h:outputStylesheet library="css" name="ui-cs.css"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <style type="text/css">
            .ui-panelgrid td {
                padding: 2px 0 2px 5px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="地区管理"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.addInitAction}"
                                 process="@this" update=":mainForm:zoneEditDialog"
                                 oncomplete="PF('ZoneEditDialog').show()">
                    <p:resetInput target=":mainForm:zoneEditDialog"/>
                </p:commandButton>
                <p:commandButton value="自检" icon="ui-icon-check" process="@this" update="dataTable" action="#{mgrbean.checkAllTsZone}"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputLabel value="提示：" style="color:red;"/>
                <h:outputLabel value="修改记录后请重新【自检】！" style="color:blue;"/>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputLabel value="全称/地区名称："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <p:inputText value="#{mgrbean.searchZoneName}" maxlength="100"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="地区编码："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <p:inputText value="#{mgrbean.searchZoneGb}" maxlength="10"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="地区级别："/>
            </p:column>
            <p:column style="padding-left: 3px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox style="" value="#{mgrbean.searchZoneLevel}">
                    <f:selectItem itemLabel="国家" itemValue="1"/>
                    <f:selectItem itemLabel="省级" itemValue="2"/>
                    <f:selectItem itemLabel="市级" itemValue="3"/>
                    <f:selectItem itemLabel="区县" itemValue="4"/>
                    <f:selectItem itemLabel="街道" itemValue="5"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputLabel value="是否市直属："/>
            </p:column>
            <p:column style="padding-left: 3px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox style="" value="#{mgrbean.searchIfCityDirect}">
                    <f:selectItem itemLabel="是" itemValue="1"/>
                    <f:selectItem itemLabel="否" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputLabel value="是否省直属："/>
            </p:column>
            <p:column style="padding-left: 3px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox style="" value="#{mgrbean.searchIfProvDirect}">
                    <f:selectItem itemLabel="是" itemValue="1"/>
                    <f:selectItem itemLabel="否" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
            <p:column styleClass="cs-scl">
                <h:outputLabel value="状态："/>
            </p:column>
            <p:column style="padding-left: 3px !important;" styleClass="cs-scv">
                <p:selectManyCheckbox style="" value="#{mgrbean.searchIfReveal}">
                    <f:selectItem itemLabel="启用" itemValue="1"/>
                    <f:selectItem itemLabel="停用" itemValue="0"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column styleClass="cs-scl-h">
                <h:outputLabel value="自检状态："/>
            </p:column>
            <p:column style="padding-left: 3px !important;" styleClass="cs-scv" colspan="5">
                <p:selectManyCheckbox style="" value="#{mgrbean.searchState}">
                    <f:selectItem itemLabel="成功" itemValue="1"/>
                    <f:selectItem itemLabel="失败" itemValue="2"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="R" type="java.lang.Integer"-->
        <!--@elvariable id="itm" type="com.chis.modules.system.entity.TsZone"-->
        <p:column headerText="序号" style="text-align: center;width: 40px;">
            <p:outputLabel value="#{R+1}"/>
        </p:column>
        <p:column headerText="全称" style="width: 425px;">
            <h:outputText value="#{itm.fullName}"/>
        </p:column>
        <p:column headerText="地区名称" style="width: 150px;">
            <h:outputText value="#{itm.zoneName}"/>
        </p:column>
        <p:column headerText="10位地区编码" style="text-align: center;width: 120px;">
            <h:outputText value="#{itm.zoneGb}"/>
        </p:column>
        <p:column headerText="地区级别" style="text-align: center;width: 60px;">
            <h:outputText value="国家" rendered="#{itm.zoneType eq 1}"/>
            <h:outputText value="省级" rendered="#{itm.zoneType eq 2}"/>
            <h:outputText value="市级" rendered="#{itm.zoneType eq 3}"/>
            <h:outputText value="区县" rendered="#{itm.zoneType eq 4}"/>
            <h:outputText value="街道" rendered="#{itm.zoneType eq 5}"/>
        </p:column>
        <p:column headerText="第三方编码" style="text-align: center;width: 120px;">
            <h:outputText value="#{itm.dsfCode}"/>
        </p:column>
        <p:column headerText="真实地区级别" style="text-align: center;width: 80px;">
            <h:outputText value="国家" rendered="#{itm.realZoneType eq 1}"/>
            <h:outputText value="省级" rendered="#{itm.realZoneType eq 2}"/>
            <h:outputText value="市级" rendered="#{itm.realZoneType eq 3}"/>
            <h:outputText value="区县" rendered="#{itm.realZoneType eq 4}"/>
            <h:outputText value="街道" rendered="#{itm.realZoneType eq 5}"/>
        </p:column>
        <p:column headerText="是否市直属" style="text-align: center;width: 80px;">
            <h:outputText value="是" rendered="#{itm.ifCityDirect eq 1}"/>
            <h:outputText value="否" rendered="#{itm.ifCityDirect ne 1}"/>
        </p:column>
        <p:column headerText="是否省直属" style="text-align: center;width: 80px;">
            <h:outputText value="是" rendered="#{itm.ifProvDirect eq 1}"/>
            <h:outputText value="否" rendered="#{itm.ifProvDirect ne 1}"/>
        </p:column>
        <p:column headerText="状态" style="text-align: center;width: 40px;">
            <h:outputText value="启用" rendered="#{itm.ifReveal ne 0}"/>
            <h:outputText value="停用" rendered="#{itm.ifReveal eq 0}"/>
        </p:column>
        <p:column headerText="自检状态" style="text-align: center;width: 60px;">
            <h:outputText value="成功" rendered="#{itm.state eq 1}"/>
            <h:outputText value="失败" style="color: red;" rendered="#{itm.state eq 2}"/>
        </p:column>
        <p:column headerText="失败原因" style="width: 250px;">
            <h:outputText id="errRsn" value="#{itm.errRsn}" styleClass="zwx-tooltip"/>
            <p:tooltip for="errRsn" style="max-width: 450px;">
                <p:outputLabel value="#{itm.errRsn}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作" style="">
            <p:commandLink value="修改" process="@this" update=":mainForm:zoneEditDialog"
                           action="#{mgrbean.modInitAction}" oncomplete="PF('ZoneEditDialog').show()">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{mgrbean.oldZone}" value="#{itm}"/>
                <p:resetInput target=":mainForm:zoneEditDialog"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="停用" process="@this" update="dataTable"
                           action="#{mgrbean.stateChangeAction}" rendered="#{itm.ifReveal ne 0}">
                <p:confirm header="消息确认框" message="确定要停用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{mgrbean.stateSwitch}" value="0"/>
            </p:commandLink>
            <p:commandLink value="启用" process="@this" update="dataTable"
                           action="#{mgrbean.stateChangeAction}" rendered="#{itm.ifReveal eq 0}">
                <p:confirm header="消息确认框" message="确定要启用吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{mgrbean.stateSwitch}" value="1"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" process="@this" update="dataTable"
                           action="#{mgrbean.deleteAction}" rendered="#{itm.ifReveal eq 0}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm.rid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <!-- 弹出框 -->
    <ui:define name="insertDialogs">
        <!-- 新增、修改地区 -->
        <p:dialog id="zoneEditDialog" header="地区管理" widgetVar="ZoneEditDialog"
                  resizable="false" width="500" height="370" modal="true">
            <p:panelGrid style="width:100%;" id="zoneEditGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel value="10位地区编码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText value="#{mgrbean.editZone.zoneGb}" maxlength="10">
                            <p:ajax event="change" listener="#{zoneBean.fillInfoByGb}"
                                    process="zoneEditGrid" update="zoneEditGrid"/>
                        </p:inputText>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel for="zoneCode" value="12位地区编码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="zoneCode" value="#{mgrbean.editZone.zoneCode}" maxlength="12"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel for="roleName" value="地区名称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="roleName" value="#{mgrbean.editZone.zoneName}" maxlength="25">
                            <p:ajax event="change" listener="#{zoneBean.fillInfoByName}"
                                    process="zoneEditGrid" update="zoneEditGrid"/>
                        </p:inputText>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel for="roleShortName" value="地区简称："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="roleShortName" value="#{mgrbean.editZone.zoneShortName}" maxlength="30"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel for="zoneType" value="地区级别："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneMenu id="zoneType" value="#{mgrbean.editZone.zoneType}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItem itemLabel="国家" itemValue="1"/>
                            <f:selectItem itemLabel="省级" itemValue="2"/>
                            <f:selectItem itemLabel="市级" itemValue="3"/>
                            <f:selectItem itemLabel="县区" itemValue="4"/>
                            <f:selectItem itemLabel="街道" itemValue="5"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel for="dsfCode" value="第三方编码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText id="dsfCode" value="#{mgrbean.editZone.dsfCode}" maxlength="20"
                                     onkeyup="SYSTEM.clearChinese(this)">
                            <p:ajax event="change" listener="#{zoneBean.fillInfoByDsfCode()}"
                                    process="zoneEditGrid" update="zoneEditGrid"/>
                        </p:inputText>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <font color="red">*</font>
                        <h:outputLabel value="真实地区级别："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneMenu value="#{mgrbean.editZone.realZoneType}">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItem itemLabel="国家" itemValue="1"/>
                            <f:selectItem itemLabel="省级" itemValue="2"/>
                            <f:selectItem itemLabel="市级" itemValue="3"/>
                            <f:selectItem itemLabel="县区" itemValue="4"/>
                            <f:selectItem itemLabel="街道" itemValue="5"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputLabel value="是否市直属："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneRadio value="#{mgrbean.editZone.ifCityDirect}">
                            <f:selectItem itemLabel="是" itemValue="1"/>
                            <f:selectItem itemLabel="否" itemValue="0"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;">
                        <h:outputLabel value="是否省直属："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneRadio value="#{mgrbean.editZone.ifProvDirect}">
                            <f:selectItem itemLabel="是" itemValue="1"/>
                            <f:selectItem itemLabel="否" itemValue="0"/>
                        </p:selectOneRadio>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:center;" colspan="2">
                        <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" action="#{mgrbean.saveAction}"
                                         process="@this,zoneEditGrid"
                                         update="dataTable,zoneEditGrid"/>
                        <p:spacer width="5"/>
                        <p:commandButton value="取消" icon="ui-icon-close" id="backBtn"
                                         onclick="PF('ZoneEditDialog').hide();"
                                         immediate="true"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
        <p:dialog id="showErrMsg" header="自检结果" widgetVar="ShowErrMsg"
                  resizable="false" width="800" modal="true" maximizable="true" style="height: auto;">
            <p:outputPanel id="showErrMsgPanel">
                <div style="max-width:800px;word-wrap: break-word;padding: 10px;">
                    <c:forEach items="#{mgrbean.allMsgList}" var="msg">
                        <p>#{msg}</p>
                    </c:forEach>
                </div>
            </p:outputPanel>
        </p:dialog>
    </ui:define>
</ui:composition>