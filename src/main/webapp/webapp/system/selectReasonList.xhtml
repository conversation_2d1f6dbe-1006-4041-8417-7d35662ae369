<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title><h:outputText value="#{selectReasonBean.codeTypeName}选择"/></title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet name="css/ui-tabs.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
    </h:head>

    <h:body style="overflow-y:hidden;">
        <h:form id="selectForm">
        	<p:scrollPanel mode="native" style="height:350px;width:680px;">
	            <p:dataTable var="itm" value="#{selectReasonBean.dataList}" id="selectedTable" paginator="false"
	                         emptyMessage="没有数据！" selection="#{selectReasonBean.tsSimpleCode}" rowKey="#{itm.rid}">
	                <p:column selectionMode="single" style="width:10px;text-align:center"/>
	                <p:column headerText="#{selectReasonBean.codeTypeName}" style="padding-left: 3px; ">
	                    <h:outputText value="#{itm.codeName}" />
	                </p:column>
	            </p:dataTable>
            </p:scrollPanel>
            <p:outputPanel style="text-align:center">
                <p:commandButton value="确定" icon="ui-icon-check" id="saveBtn" action="#{selectReasonBean.selectAction}" process="@form" />
                <p:spacer width="5" />
                <p:commandButton value="取消" icon="ui-icon-close" id="backBtn" action="#{selectReasonBean.dialogClose}" process="@this"/>
            </p:outputPanel>
            <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
            <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
        </h:form>
    </h:body>
</f:view>
</html>
