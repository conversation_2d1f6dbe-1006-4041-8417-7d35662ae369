<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<!-- 编辑页面的script -->
	<ui:define name="insertEditScripts">
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<script type="text/javascript">
			//<![CDATA[

			//]]>
		</script>

	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="4"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="单位关系管理" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 编辑页面的按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="4"
				style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{tsUnitRelBean.saveAction}" process="@this,mainUnitListOneMenu,rel,dataTable"
					update=":tabView" />
					<p:commandButton value="添加关系单位" icon="ui-icon-plus" id="plusBtn"   process="@this"
					action="#{tsUnitRelBean.addRelInit}"  update="editRelDialog"/>
				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{tsUnitRelBean.backAction}" update=":tabView"
					immediate="true" />

			</h:panelGrid>
		</p:outputPanel>
	</ui:define>


	<!-- 编辑页面的内容-->
	<ui:define name="insertEditContent">





	</ui:define>

	<!-- 其它内容 -->
	<ui:define name="insertOtherContents">
		<p:outputPanel id="editPanelInfo">
			<p:panel header="选择单位" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:160px;">
							<h:outputText value="地区：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width: 250px;">

							<zwx:ZoneSingleNewComp zoneList="#{tsUnitRelBean.zoneList}" zoneCodeNew="#{tsUnitRelBean.mainZoneCode}"
												   zoneName="#{tsUnitRelBean.mainZoneName}" id="mainZone" rendered="#{tsUnitRelBean.rid==null}"
												   onchange="onMainNodeSelect()" zoneType="#{tsUnitRelBean.mainZoneType}"/>

							<p:remoteCommand name="onMainNodeSelect"
								action="#{tsUnitRelBean.onMainNodeSelect}"
								process="@this,mainZone"
								update=":tabView:editForm:mainUnitListOneMenu " />
							<h:outputText value="#{tsUnitRelBean.mainZoneName}"
								rendered="#{tsUnitRelBean.rid!=null}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:160px;">
							<h:outputText value="单位：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width: 250px;">
							<p:selectOneMenu id="mainUnitListOneMenu"
								value="#{tsUnitRelBean.mainUnitId}" style="width: 190px;"
								rendered="#{tsUnitRelBean.rid==null}">
								<f:selectItem itemLabel="--请选择--" itemValue="" />
								<f:selectItems value="#{tsUnitRelBean.mainUnitMap}" />
								<p:ajax event="change"
									listener="#{tsUnitRelBean.checkMainAction}" process="@this"
									update="rel,relUnitListOneMenu,dataTable" />
							</p:selectOneMenu>
							<h:outputText value="#{tsUnitRelBean.mainUnitName}"
								rendered="#{tsUnitRelBean.rid!=null}" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:160px;">
							<h:outputText value="选择关系类型：" />
						</p:column>

						<p:column style="text-align:left;padding-left:8px;width: 250px;">
							<p:selectOneRadio id="rel" value="#{tsUnitRelBean.relEditType}"
								style="width: 120px;">
								<f:selectItems value="#{tsUnitRelBean.relMap}" />


							</p:selectOneRadio>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:panel>

			
			<p:panel header="添加的关系单位" style="text-align:left;margin-top:3px;">

				<p:dataTable var="itm" rowIndexVar="index" lazy="true"
					value="#{tsUnitRelBean.relTsUnitList}" paginator="true" rows="5"
					paginatorPosition="bottom" id="dataTable" emptyMessage="">
					<p:columnGroup type="header">
						<p:row>
							<p:column style="width: 80px;text-align:center;">
								<f:facet name="header">
									<h:outputText value="单位名称" />
								</f:facet>
							</p:column >
							<p:column style="width: 80px;text-align:center;">
								<f:facet name="header">
									<h:outputText value="关系单位名称" />
								</f:facet>
							</p:column>
							<p:column style="width: 80px;text-align:center;">
								<f:facet name="header">
									<h:outputText value="关系" />
								</f:facet>
							</p:column>
							<p:column style="width: 80px;text-align:center;">
								<f:facet name="header">
									<h:outputText value="开始时间" />
								</f:facet>
							</p:column>
							<p:column style="width: 80px;text-align:center;">
								<f:facet name="header">
									<h:outputText value="结束时间" />
								</f:facet>
							</p:column>
							<p:column style="width: 80px;text-align:center;">
								<f:facet name="header">
									<h:outputText value="操作" />
								</f:facet>
							</p:column>
						</p:row>
					</p:columnGroup>


					<p:column style="width: 80px;text-align:center;">

						<h:outputText value="#{itm.fkByUnitId.unitname}" />

					</p:column>

					<p:column style="width: 80px;text-align:center;">
						<h:outputText value="#{itm.fkByRelUnitId.unitname}" />

					</p:column>


					<p:column style="width: 80px;text-align:center;">
						<h:outputText value="委托" rendered="#{itm.relType=='1'}" />
						<h:outputText value="代管" rendered="#{itm.relType=='2'}" />
					</p:column>
					<p:column style="width: 80px;text-align:center;">


						<h:outputText value="#{itm.beginTime}">
							<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"/> 
						</h:outputText>
					</p:column>

					<p:column style="width: 80px;text-align:center;">


						<h:outputText value="#{itm.endTime}">
							<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"/> 
						</h:outputText>

					</p:column>
					<p:column style="width: 80px;text-align:center;">
						<p:commandLink value="删除"
							action="#{tsUnitRelBean.deleteRelAction}" id="deleteRelBtn"
							process="@this" update="dataTable">
							<f:setPropertyActionListener target="#{tsUnitRelBean.tsUnitRel}"
								value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>



			</p:panel>
		</p:outputPanel>
        <p:dialog id="editRelDialog" header="添加关系单位" widgetVar="editRelDialog" resizable="false" width="500" height="200" modal="true">
      <p:panelGrid style="width:100%;" id="editRelEditGrid">
           
               <p:row>
               <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="关系单位：" />
            </p:column>
            <p:column style="text-align:left;padding-left:8px;width: 250px;">
                <p:selectOneMenu id="relUnitListOneMenu" value="#{tsUnitRelBean.relUnitId}" style="width: 190px;" required="true" requiredMessage="关系单位不能为空">
                    <f:selectItem itemLabel="--请选择--" itemValue=""/>
                    <f:selectItems value="#{tsUnitRelBean.relUnitMap}"/> 
                         
                </p:selectOneMenu>
            </p:column> 
                </p:row>
                <p:row>
               <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="开始时间:" />
            </p:column> 
               <p:column style="text-align:left;padding-right:3px;width:160px;">
               <p:calendar navigator="true" yearRange="c-10:c" id="beginTime"
					value="#{tsUnitRelBean.beginDate}" size="11"  
					 pattern="yyyy-MM-dd"
					showButtonPanel="true" showOtherMonths="true"   required="true"  requiredMessage="开始时间不能为空"/>
					</p:column>
                </p:row>
                <p:row>
                <p:column style="text-align:right;padding-right:3px;width:160px;">
                <h:outputText value="结束时间:" />
            </p:column> 
               <p:column style="text-align:left;padding-right:3px;width:160px;">
               <p:calendar navigator="true" yearRange="c-10:c"
					value="#{tsUnitRelBean.endDate}" size="11" id="endTime"
					 pattern="yyyy-MM-dd"
					showButtonPanel="true" showOtherMonths="true"   required="true"  requiredMessage="结束时间不能为空"/>
					</p:column>
                </p:row>
            </p:panelGrid>
                   <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确认" icon="ui-icon-check" id="saveDialogBtn" action="#{tsUnitRelBean.addRelAction}" process="@this,relUnitListOneMenu,mainUnitListOneMenu,beginTime,endTime,rel"
                               update="editRelEditGrid,dataTable"  />
                        <p:spacer width="5" />
                        <p:commandButton value="取消" icon="ui-icon-close" id="backDialogBtn" onclick="PF('editRelDialog').hide();" process="@this"/>
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            </p:dialog>
	</ui:define>
</ui:composition>

