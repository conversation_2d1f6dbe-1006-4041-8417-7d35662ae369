<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	template="/WEB-INF/templates/system/editTemplate.xhtml">
	<!-- 托管Bean -->
	<ui:param name="mgrbean" value="#{tdDynaFormDesignStatisticsBean}" />
	<!-- 焦点不显示-->
	<ui:param name="onfocus" value="1" />

	<!-- 样式或javascripts -->
	<ui:define name="insertEditScripts">
		<!--引入中文日期-->
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />

		<script type="text/javascript">
			//<![CDATA[
			//]]>
		</script>
		<style type="text/css">
body {
	overflow-x: hidden;
}
</style>

	</ui:define>

	<!-- 标题栏 -->
	<ui:define name="insertEditTitle">
		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="动态表单统计维护" />
			</p:column>
		</p:row>
	</ui:define>

	<!-- 按钮 -->
	<ui:define name="insertEditButtons">
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="10"
				style="border-color:transparent;padding:0px;" id="btnPanel">
				<span class="ui-separator"><span
					class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
					action="#{mgrbean.saveAction}"
					rendered="#{mgrbean.bindTdFormStatisticsDef.state == 0}"
					process="@this,editPanelInfo" update="btnPanel" />
				<p:commandButton value="提交" icon="ui-icon-circle-triangle-e"
					rendered="#{mgrbean.bindTdFormStatisticsDef.state == 0}"
					id="submitBtn" update="btnPanel"
					action="#{mgrbean.confirmEditRecord(1)}"
					process="@this,editPanelInfo">
					<p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert" />
				</p:commandButton>
				<p:commandButton value="撤销" icon="ui-icon-circle-triangle-e"
					rendered="#{mgrbean.bindTdFormStatisticsDef.state == 1}"
					id="cancelBtn" update="btnPanel"
					action="#{mgrbean.confirmEditRecord(0)}"
					process="@this,editPanelInfo">
					<p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert" />
				</p:commandButton>

				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
					action="#{mgrbean.backAction}" update=":tabView"
					process="@this" />
			</h:panelGrid>
		</p:outputPanel>
		<p:sticky target="btns" margin="1" />
	</ui:define>

	<!-- 其余内容 -->
	<ui:define name="insertOtherContents">
		<p:outputPanel id="editPanelInfo">
			<p:panel header="基本信息" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="表单编号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;" colspan="5">
							<p:outputLabel value="自动生成" style="color:gray;"
								rendered="#{mgrbean.bindTdFormStatisticsDef.rid == null}" />
							<p:outputLabel
								value="#{mgrbean.bindTdFormStatisticsDef.formCode}"
								rendered="#{mgrbean.bindTdFormStatisticsDef.rid != null}" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="表单类别：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<h:panelGrid columns="4"
								style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:inputText id="tdFormTypeName"
									value="#{ null != mgrbean.bindTdFormStatisticsDef.tdFormTypeByTypeId ?mgrbean.bindTdFormStatisticsDef.tdFormTypeByTypeId.typeName:''}"
									style="width: 182px;cursor: pointer"
									onclick="document.getElementById('tabView:editForm:selorgLink').click();"
									readonly="true" />
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="selorgLink" process="@this"
									style="position: relative;left: -28px;"
									action="#{mgrbean.showSingleAction}"
									update="singleSelDialog"
									oncomplete="PF('SingleSelDialog').show()">
								</p:commandLink>
								<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn"
									title="清空" update="tdFormTypeName"
									action="#{mgrbean.clearSingleAciton}"
									process="@this" style="position: relative;left: -25px;">
								</p:commandLink>
							</h:panelGrid>
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="表单名称：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;">
							<p:inputText id="statisticsFormName"
								value="#{mgrbean.bindTdFormStatisticsDef.formName}" size="20"
								maxlength="100" />
						</p:column>
						<p:column style="text-align:right;padding-right:8px;width:100px;">
							<p:outputLabel value="*" style="color:red;" />
							<p:outputLabel value="数据表：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<h:panelGrid columns="4"
								style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:inputText id="tdTabName"
									value="#{mgrbean.dynaFormName}"
									style="width: 182px;cursor: pointer"
									onclick="document.getElementById('tabView:editForm:tdTabLink').click();"
									readonly="true" />
								<p:commandLink styleClass="ui-icon ui-icon-search"
									id="tdTabLink" process="@this"
									style="position: relative;left: -28px;"
									action="#{mgrbean.showTableAction}"
									update="dataStructureDialog"
									oncomplete="PF('DataStructureDialog').show()">
								</p:commandLink>
								<p:commandLink styleClass="ui-icon ui-icon-trash" id="clearBtn3"
									title="清空" update="tdTabName"
									action="#{mgrbean.clearTableAciton}"
									process="@this" style="position: relative;left: -25px;">
								</p:commandLink>
							</h:panelGrid>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:panel>

			<p:outputPanel id="assistBtns" styleClass="zwx_toobar_42" style="margin-top:3px;">
				<h:panelGrid columns="10"
					style="border-color:transparent;padding-top:4px;" id="assistBtnPanel">
					<span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="辅助编辑" icon="ui-icon-gear" id="assistkBtn"
						action="#{mgrbean.toAssistPage}" update=":tabView,:tabView:assistForm:wizard"
						process="@this,statisticsFormName" />
				</h:panelGrid>
			</p:outputPanel>

			<p:panel header="脚本、代码" style="text-align:left;margin-top:3px;">
				<p:panelGrid style="width:100%;">
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成统计SQL：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="statisticsSql"
								value="#{mgrbean.bindTdFormStatisticsDef.statisticsSql}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成统计脚本：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="dataShell"
								value="#{mgrbean.bindTdFormStatisticsDef.dataShell}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
					<p:row>
						<p:column
							style="text-align:right;padding-right:8px;height:30px;width:150px;">
							<p:outputLabel value="生成统计HTML代码：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;width:200px;"
							colspan="5">
							<p:inputTextarea id="statisticsHtml"
								value="#{mgrbean.bindTdFormStatisticsDef.statisticsHtml}"
								autoResize="false" style="width:98%;" rows="20" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:panel>
		</p:outputPanel>

		<p:dialog id="dataStructureDialog" widgetVar="DataStructureDialog"
			header="数据表选择" resizable="false" width="750" height="400"
			modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="关闭" icon="ui-icon-close" process="@this"
						oncomplete="PF('DataStructureDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							id="searchPanelD" columns="10">
							<p:outputLabel value="表　名：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{mgrbean.tableNameDiag}"
								style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this,searchPanelD"
									update=":tabView:editForm:tableDatatable"
									listener="#{mgrbean.filterTableSel}" />
							</p:inputText>
							<p:spacer width="10" />
							<p:outputLabel value="表模式：" styleClass="zwx_dialog_font" />
							<p:selectOneMenu value="#{mgrbean.tableModelDiag}"
								style="width:150px;">
								<f:selectItem itemValue="" itemLabel="--请选择--" />
								<f:selectItem itemValue="1" itemLabel="单表" />
								<f:selectItem itemValue="2" itemLabel="主子表" />
								<p:ajax event="change" process="@this,searchPanelD"
									update=":tabView:editForm:tableDatatable"
									listener="#{mgrbean.filterTableSel}" />
							</p:selectOneMenu>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{mgrbean.showTableList}" var="typ"
				emptyMessage="暂无记录" paginator="true" rows="10"
				paginatorPosition="bottom" id="tableDatatable">
				<p:column headerText="操作" style="width:80px;text-align:center;">
					<p:commandLink value="选择" process="@this"
						action="#{mgrbean.selTableResult}"
						update=":tabView:editForm:tdTabName,:tabView:editForm:statisticsSql,:tabView:editForm:dataShell,:tabView:editForm:statisticsHtml"
						oncomplete="PF('DataStructureDialog').hide()">
						<f:setPropertyActionListener
							target="#{mgrbean.dynaFormId}" value="#{typ[2]}" />
						<f:setPropertyActionListener
							target="#{mgrbean.dynaFormName}" value="#{typ[0]}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="表名" style="padding-left: 3px;">
					<h:outputLabel value="#{typ[0]}" />
				</p:column>
				<p:column headerText="表模式" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ[1]=='1'?'单表':'主子表'}" />
				</p:column>
			</p:dataTable>
		</p:dialog>
		<p:dialog id="singleSelDialog" widgetVar="SingleSelDialog"
			header="表单类型选择" resizable="false" width="750" height="400"
			modal="true">
			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="6" style="border-color:transparent;padding:0;">
					<span class="ui-separator"> <span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="添加" icon="ui-icon-plus" process="@this"
						action="#{mgrbean.addSingleSel}"
						update="singleAddDialog" oncomplete="PF('SingleAddDialog').show()" />
					<p:commandButton value="返回" icon="ui-icon-close" process="@this"
						oncomplete="PF('SingleSelDialog').hide()" />
				</h:panelGrid>
			</p:outputPanel>
			<table width="100%">
				<tr>
					<td style="text-align: left;padding-left: 3px"><h:panelGrid
							columns="3">
							<p:outputLabel value="名　称：" styleClass="zwx_dialog_font" />
							<p:inputText value="#{mgrbean.formTypeDiag}"
								style="width: 120px;" maxlength="20">
								<p:ajax event="keyup" process="@this"
									update=":tabView:editForm:singleDatatable"
									listener="#{mgrbean.filterSingelSel}" />
							</p:inputText>
						</h:panelGrid></td>
				</tr>
			</table>
			<p:dataTable value="#{mgrbean.showTypeList}" var="typ"
				emptyMessage="暂无记录" paginator="true" rows="10"
				paginatorPosition="bottom" id="singleDatatable">
				<p:column headerText="操作" style="width:120px;text-align:center;">
					<p:commandLink value="选择" process="@this"
						action="#{mgrbean.selSingleResult}"
						update=":tabView:editForm:tdFormTypeName"
						oncomplete="PF('SingleSelDialog').hide()">
						<f:setPropertyActionListener
							target="#{mgrbean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="修改" process="@this"
						update=":tabView:editForm:singleAddDialog"
						oncomplete="PF('SingleAddDialog').show()">
						<f:setPropertyActionListener
							target="#{mgrbean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
					<p:spacer width="5" />
					<p:commandLink value="删除" process="@this"
						action="#{mgrbean.deleteSingleAction}"
						update="singleDatatable">
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener
							target="#{mgrbean.bindTdFormType}" value="#{typ}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="类型编码" style="width:100px;text-align: center">
					<h:outputLabel value="#{typ.typeCode}" />
				</p:column>
				<p:column headerText="类型名称" style="padding-left: 3px;">
					<h:outputLabel value="#{typ.typeName}" />
				</p:column>

			</p:dataTable>
		</p:dialog>
		<p:dialog id="singleAddDialog" widgetVar="SingleAddDialog"
			header="表单类型维护" resizable="false" width="500" modal="true">
			<p:panelGrid style="width:100%;" id="singleAddGrid">
				<p:row>
					<p:column
						style="width:30%;height: 25px;text-align: right;padding-right: 3px;">
						<p:outputLabel value="*" style="color: red" />
						<p:outputLabel value="类型编码：" />
					</p:column>
					<p:column style="padding-left: 3px;">
						<p:inputText
							value="#{mgrbean.bindTdFormType.typeCode}"
							style="width: 160px;" maxlength="25" required="true"
							requiredMessage="类型编码不允许为空！" />
					</p:column>
				</p:row>
				<p:row>
					<p:column
						style="height: 25px;text-align: right;padding-right: 3px;">
						<p:outputLabel value="*" style="color: red" />
						<p:outputLabel value="类型名称：" />
					</p:column>
					<p:column style="padding-left: 3px;">
						<p:inputText
							value="#{mgrbean.bindTdFormType.typeName}"
							style="width: 160px;" maxlength="50" required="true"
							requiredMessage="类型名称不允许为空！" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check"
							action="#{mgrbean.saveSingleAction}"
							process="@this,singleAddGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
							onclick="PF('SingleAddDialog').hide();" process="@this" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

	</ui:define>
</ui:composition>











