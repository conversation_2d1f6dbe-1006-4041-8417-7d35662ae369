<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
				xmlns:p="http://primefaces.org/ui">

	<!--引入中文日期-->
	<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
	<h:outputScript library="js" name="namespace.js" />
	<h:outputScript name="js/validate/system/validate.js" />
	<style type="text/css">
		 table.ui-selectoneradio td label {
			 white-space: nowrap;
			 overflow: hidden;
		 }
	</style>
	<script type="text/javascript">
		//<![CDATA[

		//]]>
	</script>
	<h:form id="editForm">
		<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;"
					 id="editTitleGrid">
			<f:facet name="header">
				<p:row>
					<p:column colspan="6"
							  style="text-align:left;padding-left:5px;height: 20px;">
						<h:outputText value="题目管理" />
					</p:column>
				</p:row>
			</f:facet>
		</p:panelGrid>
		<p:outputPanel id="btns" styleClass="zwx_toobar_42">
			<h:panelGrid columns="3"
						 style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
				<p:commandButton value="保存" icon="ui-icon-check" id="saveBtn"
								 process="@this,editGrid,optLayoutGrid,:tabView:editForm:numFillTable"
								 update=":tabView" action="#{examPoolBean.saveAction()}" />

				<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
								 update=":tabView" process="@this"
								 action="#{examPoolBean.backAction()}" />
			</h:panelGrid>
		</p:outputPanel>

		<p:panelGrid style="width:100%;height:100%;margin-top:5px;"
					 id="editGrid">
			<p:row rendered="#{examPoolBean.ifHaveFather==true}">
				<p:column
						style="text-align:right;padding-right:3px;height:35px;width:150px;">
					<font color="red">*</font>
					<h:outputText value="父题目：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<h:outputText value="#{examPoolBean.fatherTitleDesc}" />
				</p:column>
			</p:row>

			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:150px;">
					<font color="red">*</font>
					<h:outputText value="题目标题：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<p:inputTextarea value="#{examPoolBean.tsProbExampool.titleDesc}"
									 maxlength="500" cols="85" rows="2" autoResize="false" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="附加文字：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<p:inputTextarea value="#{examPoolBean.tsProbExampool.otherDesc}"
									 maxlength="1000" cols="85" rows="2" autoResize="false" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="参考答案：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<p:inputTextarea value="#{examPoolBean.tsProbExampool.ansDesc}"
									 maxlength="500" cols="85" rows="2" autoResize="false" />
				</p:column>
			</p:row>
			<p:row rendered="false" >
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="附加图片：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;" colspan="3">
					<h:panelGroup id="otherImgGroup">
						<p:commandButton value="上传" oncomplete="PF('fileUpVar').show();"
										 rendered="#{examPoolBean.tsProbExampool.otherImg==null}"
										 process="@this" update=":tabView:editForm:fileUp">
							<f:setPropertyActionListener target="#{examPoolBean.uploadTag}"
														 value="2" />
						</p:commandButton>
						<h:panelGroup
								rendered="#{examPoolBean.tsProbExampool.otherImg!=null}">
							<h:graphicImage
									url="/webFile#{examPoolBean.tsProbExampool.otherImg}"
									width="100px" height="65px" />
							<p:spacer width="5" />
							<p:commandButton value="删除" update="otherImgGroup"
											 process="@this" action="#{examPoolBean.deleteDiskFile}">
								<f:setPropertyActionListener target="#{examPoolBean.uploadTag}"
															 value="2" />
							</p:commandButton>
						</h:panelGroup>
						<h:outputText style="color:blue;" value="[推荐像素：124*94]" />
					</h:panelGroup>
				</p:column>
			</p:row>
			<p:row>
				<p:column
						style="text-align:right;padding-right:3px;height:46px;width:180px;">
					<font color="red">*</font>
					<h:outputText value="是否必答：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px;">
					<p:selectOneRadio value="#{examPoolBean.tsProbExampool.mustAsk}"
									  id="muskAsk">
						<f:selectItem itemLabel="是" itemValue="1" />
						<f:selectItem itemLabel="否" itemValue="0" />
						<p:ajax event="change" process="@this,questTypeGrid"
								update="questTypeGrid" />
					</p:selectOneRadio>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<font color="red">*</font>
					<h:outputText value="状态：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;height:28px;">
					<p:selectOneRadio value="#{examPoolBean.tsProbExampool.state}"
									  style="width:150px">
						<f:selectItem itemLabel="启用" itemValue="1" />
						<f:selectItem itemLabel="停用" itemValue="0" />
					</p:selectOneRadio>
				</p:column>
			</p:row>
			<p:row>
				<p:column
						style="text-align:right;padding-right:3px;width:180px;height:46px; ">
					<font color="red">*</font>
					<h:outputText value="题库类型：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px; width:280px">
					<h:panelGrid columns="5"
								 style="border-color: #ffffff;margin: 0px;padding: 0px;">
						<p:inputText id="tsProbExamtypeName"
									 value="#{examPoolBean.tsProbExamtypeName}" readonly="true"
									 style="width:180px;" />
						<p:commandLink styleClass="ui-icon ui-icon-search"
									   id="initTreeLink" partialSubmit="true" process="@this"
									   style="position: relative;left: -30px;"
									   oncomplete="PF('proTypePanel').show()">
						</p:commandLink>
						<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
									   style="position: relative;left: -35px;"
									   update=":tabView:editForm:tsProbExamtypeName"
									   action="#{examPoolBean.clearNodeSelected}" process="@this">
						</p:commandLink>
						<p:overlayPanel id="proTypePanel" for="tsProbExamtypeName"
										style="width:300px;" widgetVar="proTypePanel">
							<p:tree dynamic="true" value="#{examPoolBean.tsProbExamtypeTree}"
									var="node" selectionMode="single"
									selection="#{examPoolBean.selectedProTypeNode}" id="proTypeTree"
									style="width: 280px;height: 220px;overflow-y: auto;">
								<p:ajax event="select"
										update="@this,:tabView:editForm:tsProbExamtypeName,:tabView:editForm:tsProbExamtypeId,:tabView:editForm:relEditGrid"
										partialSubmit="true"
										listener="#{examPoolBean.onProtypeNodeSelect}"
										oncomplete="PF('proTypePanel').hide();" />
								<p:treeNode>
									<h:outputText value="#{node.codeName}" />
								</p:treeNode>
							</p:tree>
						</p:overlayPanel>
						<h:inputHidden id="tsProbExamtypeId"
									   value="#{examPoolBean.tsProbExamtypeId}" />
					</h:panelGrid>
				</p:column>
				<p:column
						style="text-align:right;padding-right:3px;height:46px;width:180px;">
					<font color="red">*</font>
					<h:outputText value="题型：" />
				</p:column>
				<p:column style="text-align:left;">
					<h:panelGrid columns="10" id="questTypeGrid"
								 style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
						<p:selectOneMenu value="#{examPoolBean.tsProbExampool.questType}">
							<f:selectItem itemValue="" itemLabel="请选择..." />
							<f:selectItems value="#{examPoolBean.questTypeList}" var="itm"
										   itemLabel="#{itm.typeCN}" itemValue="#{itm}" />
							<p:ajax event="change"
									process="@this,muskAsk,optLayoutGrid,editGrid"
									listener="#{examPoolBean.onQuestTypeChange}"
									update="questTypeGrid,:tabView:editForm:optLayoutGrid,optListPanel,numFillPanel,tablePanel" />
						</p:selectOneMenu>

					</h:panelGrid>
				</p:column>
			</p:row>
			<p:row>
				<p:column
						style="text-align:right;padding-right:3px;height:46px;width:180px;">
					<h:outputText value="分值：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px;">
					<p:inputText style="width: 180px;" autoResize="false"
								 value="#{examPoolBean.tsProbExampool.optionScore}"
								 onkeyup="SYSTEM.verifyNum3(this, 3, 3);" onblur="SYSTEM.verifyNum3(this, 3, 3);"/>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputText value="得分脚本：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;height:28px;">
					<p:commandButton value="编辑" onclick="PF('LibScrDialog').show()" process="@this"/>
				</p:column>
			</p:row>

		</p:panelGrid>
		<p:panelGrid id="optLayoutGrid"
					 style="width:#{(examPoolBean.tsProbExampool.questType.typeNo==4 or examPoolBean.tsProbExampool.questType.typeNo==7
			 or examPoolBean.tsProbExampool.questType.typeNo==9)?'99.99%':'100%'};height:100%;position: relative;top: -1px;">
			<p:row rendered="false">
				<p:column
						style="text-align:right;padding-right:3px;height:46px;width:180px;">
					<h:outputText value="选项布局：" />
				</p:column>
				<p:column style="text-align:left;" colspan="3">
					<h:panelGrid columns="10" id="layoutGrid"
								 style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
						<p:selectOneMenu value="#{examPoolBean.tsProbExampool.optLayout}">
							<f:selectItem itemValue="0" itemLabel="竖向" />
							<f:selectItem itemValue="1" itemLabel="横向" />
							<f:selectItem itemValue="2" itemLabel="随题目显示（多用于填空）" />
							<p:ajax event="change" process="@this" resetValues="true"
									listener="#{examPoolBean.onOptLayoutChange}"
									update=":tabView:editForm" />
						</p:selectOneMenu>
						<p:spacer width="10" />
						<h:outputText value="横向列数："
									  rendered="#{examPoolBean.tsProbExampool.optLayout==1}" />
						<p:inputText value="#{examPoolBean.tsProbExampool.cols}" size="2"
									 maxlength="2"
									 rendered="#{examPoolBean.tsProbExampool.optLayout==1}"
									 converterMessage="请输入数字!" />
					</h:panelGrid>
				</p:column>
			</p:row>
			<p:row rendered="#{examPoolBean.tsProbExampool.questType.typeNo==2 }">
				<p:column
						style="text-align:right;padding-right:3px;width:180px;height:46px">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最小值：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px">
					<p:inputText value="#{examPoolBean.tsProbExampool.slideMinval}"
								 id="slideMinval" size="5" maxlength="5"
								 converterMessage="滑动题最小值请输入整数!" />
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最大值：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText value="#{examPoolBean.tsProbExampool.slideMaxval}"
								 id="slideMaxval" size="5" maxlength="5"
								 converterMessage="滑动题最大值请输入整数!" />
				</p:column>
			</p:row>
			<p:row rendered="#{examPoolBean.tsProbExampool.questType.typeNo==2 }">
				<p:column
						style="text-align:right;padding-right:3px;width:180px;height:46px">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最小值描述：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px">
					<p:inputText value="#{examPoolBean.tsProbExampool.slideMinDesc}"
								 id="slideMinDesc" size="20" maxlength="250" />
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px">
					<font color="red">*</font>
					<h:outputLabel value="滑动题最大值描述：" />
				</p:column>
				<p:column style="text-align:left;padding-left:8px;">
					<p:inputText value="#{examPoolBean.tsProbExampool.slideMaxDesc}"
								 id="slideMaxDesc" size="20" maxlength="250" />
				</p:column>
			</p:row>
			<p:row
					rendered="#{examPoolBean.tsProbExampool.questType.typeNo==4 or examPoolBean.tsProbExampool.questType.typeNo==7
			 or examPoolBean.tsProbExampool.questType.typeNo==9}">
				<p:column style="text-align:right;padding-right:3px;width:180px;height: 46px;">
					<h:outputLabel value="是否为多项填空：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;margin:0 0 0 0">
					<h:panelGrid columns="4"
								 style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;margin:0 0 0 0">
						<p:selectOneRadio value="#{examPoolBean.tsProbExampool.isMulti}"
										  style="width:100px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
						</p:selectOneRadio>

						<h:outputLabel style="color:blue; "
									   value="注意： 多项填空时，题目标题中填空用“_”表示；
							连续多个“_”默认为一个填空。" />
					</h:panelGrid>
				</p:column>
			</p:row>
			<p:row rendered="#{examPoolBean.ifHaveFather}">
				<p:column style="text-align:right;padding-right:3px;width:180px;height: 46px;">
					<h:outputLabel value="显示规则：" />
				</p:column>
				<p:column style="text-align:left;padding-left:5px;width:280px;"
						  colspan="3">
					<h:panelGrid columns="10" id="jumpTypeGrid" cellpadding="0"
								 cellspacing="0" style="border-color: #ffffff;padding: 0;">
						<p:selectOneMenu value="#{examPoolBean.tsProbExampool.jumpType}">
							<f:selectItem itemValue="0" itemLabel="直接显示" />
							<f:selectItem itemValue="2" itemLabel="依赖父题目" />
							<p:ajax event="change" update="jumpTypeGrid" process="@this" />
						</p:selectOneMenu>

						<p:spacer width="10" />
						<!-- <font color="red" >*</font> -->
						<h:outputLabel value="*"
									   style="color:red;position: relative;right:-10px;"
									   rendered="#{examPoolBean.tsProbExampool.jumpType==2}" />
						<h:outputLabel value="依赖选项："
									   rendered="#{examPoolBean.tsProbExampool.jumpType==2}" />
						<p:selectManyCheckbox value="#{examPoolBean.dependNum}"
											  layout="grid" columns="5"
											  rendered="#{examPoolBean.tsProbExampool.jumpType==2}">
							<f:selectItems value="#{examPoolBean.fatherNumList}" />
						</p:selectManyCheckbox>
					</h:panelGrid>
				</p:column>
			</p:row>


			<p:row rendered="#{!examPoolBean.ifHaveFather}">
				<p:column style="text-align:right;padding-right:3px;width:180px;height: 46px;">
					<h:outputLabel value="显示规则：" />
				</p:column>
				<p:column style="text-align:left;padding-left:5px;width:280px;"
						  colspan="3">
					<h:panelGrid columns="10" id="relEditGrid" cellpadding="0"
								 cellspacing="0" style="border-color: #ffffff;padding: 0 ;">
						<p:selectOneMenu value="#{examPoolBean.tsProbExampool.jumpType}">
							<f:selectItem itemValue="0" itemLabel="直接显示" />
							<f:selectItem itemValue="2" itemLabel="依赖其他题目" />
							<p:ajax event="change" update="relEditGrid" process="@this"
									listener="#{examPoolBean.onJumpTypeChange}" />
						</p:selectOneMenu>
						<p:spacer width="10" />
						<p:selectOneMenu value="#{examPoolBean.selectedRelQueCode}" style="width: 100px;"
										 rendered="#{examPoolBean.tsProbExampool.jumpType==2}">
							<f:selectItems value="#{examPoolBean.relQueList}" var="relQue"
										   itemLabel="#{relQue.titleDesc}" itemValue="#{relQue.qesCode}" />
							<p:ajax event="change" update="relEditGrid" process="@this"
									listener="#{examPoolBean.onRelQueSelect}" />
						</p:selectOneMenu>
						<p:spacer width="10" />
						<h:outputLabel value="依赖选项："
									   rendered="#{examPoolBean.selectedRelQueCode!=null}" />
						<p:selectManyCheckbox value="#{examPoolBean.selectedRelQueOpts}"
											  layout="grid" columns="5"
											  rendered="#{examPoolBean.selectedRelQueCode!=null}">
							<f:selectItems value="#{examPoolBean.relQueOptList}" />
						</p:selectManyCheckbox>
					</h:panelGrid>
				</p:column>
			</p:row>

			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:180px;height: 46px;">
					<h:outputLabel value="序号："/>
				</p:column>
				<p:column style="text-align:left;padding-left:8px;width:280px;">
					<p:inputText style="width: 180px;" value="#{examPoolBean.tsProbExampool.num}" maxlength="9"
								 onkeyup="SYSTEM.verifyNumClear0(this, 9);"
								 onblur="SYSTEM.verifyNumClear0(this, 9);"/>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="难易程度："/>
				</p:column>
				<p:column style="text-align:left;padding-left:5px;">
					<p:selectOneRadio value="#{examPoolBean.tsProbExampool.fkByHardLevelId.rid}" >
						<f:selectItems value="#{examPoolBean.hardLevelList}" var="itm"
									   itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
					</p:selectOneRadio>
				</p:column>
			</p:row>

			<p:row >
				<p:column style="text-align:right;padding-right:3px;width:180px;height: 46px;">
					<h:outputLabel value="附件：" />
				</p:column>
				<p:column style="text-align:left;padding-left:9px;width:280px;" >
					<p:commandButton value="上传" id="attachment" process="@this" oncomplete="PF('fileUpVar').show();"
									 update=":tabView:editForm:fileUp"
									 rendered="#{examPoolBean.tsProbExampool.annexAddr == null}">
						<f:setPropertyActionListener target="#{examPoolBean.isImg}" value="1"/>
					</p:commandButton>
					<h:panelGroup rendered="#{examPoolBean.tsProbExampool.annexAddr!=null}" id="attachmentDel">
						<p:commandButton value="查看" action="#{examPoolBean.previewAction}" process="@this,:tabView:editForm:optLayoutGrid" update=":tabView:editForm:optLayoutGrid"/>
						<p:spacer width="15"/>
						<p:commandButton value="删除"  process="@this,:tabView:editForm:optLayoutGrid" update=":tabView:editForm:optLayoutGrid"
										 action="#{examPoolBean.deleteAnnexAddrAction}" />
					</h:panelGroup>
				</p:column>
				<p:column style="text-align:right;padding-right:3px;width:180px;">
					<h:outputLabel value="页面脚本："/>
				</p:column>
				<p:column style="text-align:left;padding-left:5px;">
					<p:commandButton value="编辑" onclick="PF('PageScrDialog').show()" process="@this"/>
				</p:column>
			</p:row>
			<p:row
					rendered="#{examPoolBean.tsProbExampool.questType.typeNo==11 }">
				<p:column
						style="text-align:right;padding-right:3px;width:180px;height:46px">
					<font color="red">*</font>
					<h:outputLabel value="是否固定行数：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;margin:0 0 0 0">
					<h:panelGrid columns="6" id="rowFixedPanel"
								 style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;margin:0 0 0 0">
						<p:selectOneRadio
								value="#{examPoolBean.tsProbExampool.fkByTableId.rowFixed}"
								style="width:100px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
							<p:ajax event="change" process="@this" update="rowFixedPanel" />
						</p:selectOneRadio>
						<p:spacer width="10" />
						<h:outputText value="*" style="color:red"
									  rendered="#{examPoolBean.tsProbExampool.fkByTableId.rowFixed==0}" />
						<h:outputText value="默认行数："
									  rendered="#{examPoolBean.tsProbExampool.fkByTableId.rowFixed==0}" />
						<p:spinner min="2" stepFactor="1" max="99"
								   value="#{examPoolBean.tsProbExampool.fkByTableId.defaultLineNum}"
								   size="5" maxlength="2"
								   rendered="#{examPoolBean.tsProbExampool.fkByTableId.rowFixed==0}"
								   converterMessage="默认行数请输入数字!" />
					</h:panelGrid>
				</p:column>
			</p:row>
		</p:panelGrid>
		<!-- 选项 -->
		<h:panelGrid columns="1" id="optListPanel" style="width:100%">
			<p:fieldset id="optListField" legend="选项" toggleable="true"
						rendered="#{examPoolBean.tsProbExampool.questType.typeNo==0 or examPoolBean.tsProbExampool.questType.typeNo==1
				or examPoolBean.tsProbExampool.questType.typeNo==8 or examPoolBean.tsProbExampool.questType.typeNo==10}"
						toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<table style="width: 100%">
					<tr>
						<td><p:outputPanel styleClass="zwx_toobar_42">
							<h:panelGrid columns="5"
										 style="border-color:transparent;padding:0px;">
									<span class="ui-separator"><span
											class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton icon="ui-icon-plus" value="手动添加"
												 process="@this" action="#{examPoolBean.optAddInitAction}"
												 oncomplete="PF('OptDialog').show()"
												 disabled="#{examPoolBean.tsProbExampool.questType.typeNo==10}"
												 update=":tabView:editForm:optDialog">
									<p:resetInput target=":tabView:editForm:optDialog"></p:resetInput>
								</p:commandButton>
								<p:commandButton icon="ui-icon-plus" value="从常规添加"
												 disabled="#{examPoolBean.tsProbExampool.questType.typeNo==10}"
												 process="@this" oncomplete="PF('TemplChooseDialog').show()"
												 update=":tabView:editForm:templChooseDialog">
									<f:setPropertyActionListener
											target="#{examPoolBean.templOpts}" value="" />
								</p:commandButton>
								<p:commandButton icon="ui-icon-plus" value="从码表添加"
												 disabled="#{examPoolBean.tsProbExampool.questType.typeNo==10}"
												 process="@this" oncomplete="PF('CodeChooseDialog').show()"
												 action="#{examPoolBean.initCodeTypeList}"
												 update=":tabView:editForm:codeChooseDialog">
									<f:setPropertyActionListener
											target="#{examPoolBean.selectedCodeType}" value="" />
									<f:setPropertyActionListener
											target="#{examPoolBean.selecedSystemType}" value="" />
								</p:commandButton>
								<p:commandButton icon="ui-icon-close" value="清空"
												 disabled="#{examPoolBean.tsProbExampool.questType.typeNo==10}"
												 process="@this" action="#{examPoolBean.optClear}"
												 update="optInfoTable">
									<p:confirm header="消息确认框" message="确定要清空吗？"
											   icon="ui-icon-alert" />
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel></td>
					</tr>
				</table>
				<p:dataTable var="itm"
							 value="#{examPoolBean.tsProbExampool.tsProPoolOpts}"
							 editingRow="true" id="optInfoTable" emptyMessage="暂无选项" rows="50"
							 paginator="true" rowIndexVar="R" paginatorPosition="bottom">
					<p:column headerText="序号" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.num}" />
					</p:column>
					<p:column headerText="选项文字" style="width:220px;">
						<h:outputLabel value="#{itm.optionDesc}" />
					</p:column>
					<p:column headerText="选项值" style="width:60px;">
						<h:outputLabel value="#{itm.optionValue}" />
					</p:column>
					<p:column headerText="分值" style="width:40px;text-align: center"
							  rendered="fasle">
						<h:outputLabel value="#{itm.optionScore}" />
					</p:column>
					<p:column headerText="是否正确答案" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.isCorrect==1?'是':'否'}" />
					</p:column>
					<p:column headerText="是否需要填空" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.needFill==1?'是':'否'}" />
					</p:column>
					<p:column headerText="状态" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.state==1?'启用':'停用'}" />
					</p:column>
					<p:column headerText="附加文字" style="width:180px;">
						<h:outputLabel value="#{itm.otherDesc}" />
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="删除" process="@this" update="optInfoTable"
									   rendered="#{examPoolBean.tsProbExampool.questType.typeNo!=10}"
									   action="#{examPoolBean.optDeleteAction}">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener
									target="#{examPoolBean.tsProPoolOpt}" value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" process="@this"
									   action="#{examPoolBean.optModInitAction}"
									   oncomplete="PF('OptDialog').show()" resetValues="true"
									   update=":tabView:editForm:optDialog">
							<f:setPropertyActionListener
									target="#{examPoolBean.tsProPoolOpt}" value="#{itm}" />
							<f:setPropertyActionListener target="#{examPoolBean.num}"
														 value="#{itm.num}" />
							<f:setPropertyActionListener target="#{examPoolBean.optValue}"
														 value="#{itm.optionValue}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="危险因素配置" process="@this"
									   rendered="false"
									   action="#{examPoolBean.configAction}">
							<p:ajax event="dialogReturn"
									listener="#{examPoolBean.onFactorSelect}" process="@this"></p:ajax>
							<f:setPropertyActionListener target="#{examPoolBean.num}"
														 value="#{itm.num}" />
							<f:setPropertyActionListener
									target="#{examPoolBean.tsProPoolOpt}" value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
					</p:column>
				</p:dataTable>
			</p:fieldset>
		</h:panelGrid>

		<!-- 数字填空题，触发危险因素 -->
		<h:panelGrid columns="1" id="numFillPanel" style="width:100%">
			<p:fieldset id="numFillField" legend="填空值范围配置" toggleable="true"
						rendered="#{examPoolBean.tsProbExampool.questType.typeNo==7 or examPoolBean.tsProbExampool.questType.typeNo==9}"
						toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<table style="width: 100%">
					<tr>
						<td><p:outputPanel styleClass="zwx_toobar_42">
							<h:panelGrid columns="5"
										 style="border-color:transparent;padding:0px;">
									<span class="ui-separator"><span
											class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton icon="ui-icon-plus" value="添加填空数"
												 process="@this,:tabView:editForm:numFillPanel"
												 action="#{examPoolBean.numFillAddInitAction}"
												 update=":tabView:editForm:numFillPanel">
								</p:commandButton>
								<p:commandButton icon="ui-icon-close" value="清空"
												 disabled="#{examPoolBean.tsProbExampool.questType.typeNo==10}"
												 process="@this" action="#{examPoolBean.numFillClear}"
												 update="numFillTable,optLayoutGrid">
									<p:confirm header="消息确认框" message="确定要清空吗？"
											   icon="ui-icon-alert" />
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel></td>
					</tr>
				</table>
				<p:dataTable var="itm" editMode="cell"
							 value="#{examPoolBean.numFillList}" id="numFillTable"
							 emptyMessage="暂无选项" rows="50" paginator="true" rowIndexVar="R"
							 paginatorPosition="bottom">
					<p:column headerText="填空序号" style="width:100px;text-align: center">

						<p:spinner value="#{itm.num}" style="text-align: center" min="1"
								   stepFactor="1" size="6" />

					</p:column>
					<p:column headerText="填空值最小值"
							  style="width:200px;text-align: center">
						<p:spinner value="#{itm.minVerifyVal}" size="6"
								   stepFactor="#{examPoolBean.tsProbExampool.questType.typeNo==7?1:0.01}"
								   style="text-align: center">
							<f:convertNumber
									pattern="#{examPoolBean.tsProbExampool.questType.typeNo==7?'###0':'###0.00'}"
									type="number" />
						</p:spinner>
					</p:column>
					<p:column headerText="填空值最大值"
							  style="width:200px;text-align: center">
						<p:spinner value="#{itm.maxVerifyVal}" size="6"
								   stepFactor="#{examPoolBean.tsProbExampool.questType.typeNo==7?1:0.01}"
								   style="text-align: center">
							<f:convertNumber
									pattern="#{examPoolBean.tsProbExampool.questType.typeNo==7?'###0':'###0.00'}"
									type="number" />
						</p:spinner>
					</p:column>
					<p:column headerText="触发危险因素最小值" rendered="false"
							  style="width:150px;text-align: center">
						<p:spinner value="#{itm.minVal}" size="6"
								   stepFactor="#{examPoolBean.tsProbExampool.questType.typeNo==7?1:0.01}"
								   style="text-align: center">
							<f:convertNumber
									pattern="#{examPoolBean.tsProbExampool.questType.typeNo==7?'###0':'###0.00'}"
									type="number" />
						</p:spinner>
					</p:column>
					<p:column headerText="触发危险因素最大值" rendered="false"
							  style="width:150px;text-align: center">
						<p:spinner value="#{itm.maxVal}" size="6"
								   stepFactor="#{examPoolBean.tsProbExampool.questType.typeNo==7?1:0.01}"
								   style="text-align: center">
							<f:convertNumber
									pattern="#{examPoolBean.tsProbExampool.questType.typeNo==7?'###0':'###0.00'}"
									type="number" />
						</p:spinner>
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="删除"
									   process="@this,:tabView:editForm:numFillTable"
									   update="numFillTable"
									   action="#{examPoolBean.numFillDeleteAction}">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{examPoolBean.numFill}"
														 value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="危险因素配置"
									   process="@this,:tabView:editForm:numFillTable"
									   rendered="false"
									   action="#{examPoolBean.numFillConfigAction}">
							<p:ajax event="dialogReturn"
									listener="#{examPoolBean.numFillOnFactorSelect}" process="@this"></p:ajax>
							<f:setPropertyActionListener target="#{examPoolBean.numFill}"
														 value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
					</p:column>
				</p:dataTable>
			</p:fieldset>
		</h:panelGrid>

		<!-- 表格题 -->
		<h:panelGrid columns="1" id="tablePanel" style="width:100%">
			<!-- 行标题 -->
			<p:fieldset id="rowtitleField" legend="行标题" toggleable="true"
						rendered="#{examPoolBean.tsProbExampool.questType.typeNo==11}"
						toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<table style="width: 100%">
					<tr>
						<td><p:outputPanel styleClass="zwx_toobar_42">
							<h:panelGrid columns="5"
										 style="border-color:transparent;padding:0px;">
									<span class="ui-separator"><span
											class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton icon="ui-icon-plus" value="添加" process="@this"
												 action="#{examPoolBean.rowtitleAddInitAction}"
												 oncomplete="PF('RowtitleDialog').show()"
												 update=":tabView:editForm:rowtitleDialog">
									<p:resetInput target=":tabView:editForm:rowtitleDialog" />
								</p:commandButton>
								<p:commandButton icon="ui-icon-close" value="清空"
												 process="@this" action="#{examPoolBean.rowtitleClearAction}"
												 update="rowtitleTable">
									<p:confirm header="消息确认框" message="确定要清空吗？"
											   icon="ui-icon-alert" />
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel></td>
					</tr>
				</table>
				<p:dataTable var="itm"
							 value="#{examPoolBean.tsProbExampool.fkByTableId.rowtitles}"
							 editingRow="true" id="rowtitleTable" emptyMessage="暂无行标题" rows="10"
							 paginator="true" rowIndexVar="R" paginatorPosition="bottom">
					<p:column headerText="名称" style="width:180px;">
						<h:outputLabel value="#{itm.title}" />
					</p:column>
					<p:column headerText="行序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.rowIndex}" />
					</p:column>
					<p:column headerText="行合并" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.rowspan}" />
					</p:column>
					<p:column headerText="列序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.colIndex}" />
					</p:column>
					<p:column headerText="列合并" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.colspan}" />
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="删除" process="@this"
									   action="#{examPoolBean.rowtitleDeleteAction()}"
									   update="rowtitleTable">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{examPoolBean.rowtitle}"
														 value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" process="@this" resetValues="true"
									   update=":tabView:editForm:rowtitleDialog"
									   oncomplete="PF('RowtitleDialog').show()">
							<f:setPropertyActionListener target="#{examPoolBean.rowtitle}"
														 value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:fieldset>
			<!--列定义 -->
			<p:fieldset id="colsdefineField" legend="列定义" toggleable="true"
						rendered="#{examPoolBean.tsProbExampool.questType.typeNo==11}"
						toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<table style="width: 100%">
					<tr>
						<td><p:outputPanel styleClass="zwx_toobar_42">
							<h:panelGrid columns="5"
										 style="border-color:transparent;padding:0px;">
									<span class="ui-separator"><span
											class="ui-icon ui-icon-grip-dotted-vertical" /></span>
								<p:commandButton icon="ui-icon-plus" value="添加" process="@this"
												 action="#{examPoolBean.colsdefineAddInitAction}"
												 oncomplete="PF('ColsdefineDialog').show()"
												 update=":tabView:editForm:colsdefineDialog">
									<p:resetInput target=":tabView:editForm:colsdefineDialog" />
								</p:commandButton>
								<p:commandButton icon="ui-icon-close" value="清空"
												 process="@this" action="#{examPoolBean.colsdefineClearAction}"
												 update="colsdefineTable">
									<p:confirm header="消息确认框" message="确定要清空吗？"
											   icon="ui-icon-alert" />
								</p:commandButton>
							</h:panelGrid>
						</p:outputPanel></td>
					</tr>
				</table>
				<p:dataTable var="itm"
							 value="#{examPoolBean.tsProbExampool.fkByTableId.colsdefines}"
							 editingRow="true" id="colsdefineTable" emptyMessage="暂无列定义"
							 rows="10" paginator="true" rowIndexVar="R"
							 paginatorPosition="bottom">
					<p:column headerText="序号" style="width:40px;text-align: center">
						<h:outputLabel value="#{itm.num}" />
					</p:column>
					<p:column headerText="列名" style="width:180px;">
						<h:outputLabel value="#{itm.colDesc}" />
					</p:column>
					<p:column headerText="列类型" style="width:40px;">
						<h:outputLabel value="文本" rendered="#{itm.colType==1}" />
						<h:outputLabel value="整数" rendered="#{itm.colType==2}" />
						<h:outputLabel value="小数" rendered="#{itm.colType==3}" />
						<h:outputLabel value="日期" rendered="#{itm.colType==4}" />
						<h:outputLabel value="计算" rendered="#{itm.colType==5}" />
						<h:outputLabel value="下拉" rendered="#{itm.colType==6}" />
						<h:outputLabel value="合并列头" rendered="#{itm.colType==9}" />
						<h:outputLabel value="标题列" rendered="#{itm.colType==0}" />
					</p:column>
					<p:column headerText="行序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.rowIndex}" />
					</p:column>
					<p:column headerText="列序号" style="width:50px;text-align: center">
						<h:outputLabel value="#{itm.colIndex}" />
					</p:column>
					<p:column headerText="行合并" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.rowspan}" />
					</p:column>
					<p:column headerText="列合并" style="width:60px;text-align: center">
						<h:outputLabel value="#{itm.cols}" />
					</p:column>
					<p:column headerText="是否必填" style="width:60px;text-align: center">
						<h:outputLabel value="是" rendered="#{itm.colMust==1}" />
						<h:outputLabel value="否" rendered="#{itm.colMust==0}" />
					</p:column>
					<p:column headerText="操作" style="padding-left: 3px;">
						<p:spacer width="5" />
						<p:commandLink value="删除" process="@this"
									   action="#{examPoolBean.colsdefineDeleteAction()}"
									   update="colsdefineTable">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener target="#{examPoolBean.colsdefine}"
														 value="#{itm}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="修改" process="@this" resetValues="true"
									   update=":tabView:editForm:colsdefineDialog"
									   action="#{examPoolBean.colsdefineModInitAction()}"
									   oncomplete="PF('ColsdefineDialog').show()">
							<f:setPropertyActionListener target="#{examPoolBean.colsdefine}"
														 value="#{itm}" />
						</p:commandLink>
					</p:column>
				</p:dataTable>
			</p:fieldset>
			<!-- 行标题添加、修改 -->
			<p:dialog width="430" widgetVar="RowtitleDialog" id="rowtitleDialog"
					  resizable="false" header="行标题">
				<p:panelGrid style="width:100%;" id="rowtitleEditGrid">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:90px;">
							<font color="red">*</font>
							<h:outputLabel value="名称：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<p:inputText value="#{examPoolBean.rowtitle.title}" size="18"
										 maxlength="25" required="true" requiredMessage="请输入名称" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<font color="red">*</font>
							<h:outputLabel value="行序号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width:90px;">
							<p:inputText value="#{examPoolBean.rowtitle.rowIndex}" size="5"
										 maxlength="2" required="true" requiredMessage="请输入行序号"
										 converterMessage="行序号请输入数字" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:90px;">
							<h:outputLabel value="行合并：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.rowtitle.rowspan}" size="5"
										 maxlength="2" converterMessage="行合并请输入数字" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<font color="red">*</font>
							<h:outputLabel value="列序号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.rowtitle.colIndex}" size="5"
										 maxlength="2" required="true" requiredMessage="请输入列序号"
										 converterMessage="列序号请输入数字" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="列合并：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.rowtitle.colspan}" size="5"
										 maxlength="2" converterMessage="列合并请输入数字" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="状态：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<p:selectOneRadio value="#{examPoolBean.rowtitle.state}"
											  style="width:120px;">
								<f:selectItem itemLabel="启用" itemValue="1" />
								<f:selectItem itemLabel="停用" itemValue="0" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="确定" icon="ui-icon-check"
											 id="rowtitleSubmitBtn" process="@this,rowtitleEditGrid"
											 update="rowtitleTable,rowtitleEditGrid"
											 action="#{examPoolBean.rowtitleSaveAction}" />
							<p:commandButton value="关闭" icon="ui-icon-close"
											 id="rowtitleCancelBtn" process="@this"
											 oncomplete="PF('RowtitleDialog').hide()">
							</p:commandButton>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
			<p:dialog width="800" widgetVar="ColsdefineDialog"
					  id="colsdefineDialog" resizable="false" header="列定义">
				<p:panelGrid style="width:100%;" id="colsdefineEditGrid">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="序号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width:180px;">
							<p:inputText value="#{examPoolBean.colsdefine.num}" size="5"
										 maxlength="5" converterMessage="序号请输入数字" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<font color="red">*</font>
							<h:outputLabel value="列名：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.colDesc}" size="25"
										 maxlength="50" required="true" requiredMessage="列名不能为空" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<font color="red">*</font>
							<h:outputLabel value="行序号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.rowIndex}" size="5"
										 maxlength="2" converterMessage="行序号请输入数字" required="true"
										 requiredMessage="行序号不能为空" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="行合并：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.rowspan}" size="5"
										 maxlength="3" converterMessage="行合并请输入数字" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;">
							<font color="red">*</font>
							<h:outputLabel value="列序号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.colIndex}" size="5"
										 maxlength="2" required="true" requiredMessage="列序号不能为空"
										 converterMessage="列序号请输入数字" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="列合并：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.cols}" size="5"
										 maxlength="3" converterMessage="列合并请输入数字" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="状态：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<p:selectOneRadio value="#{examPoolBean.colsdefine.state}"
											  style="width:120px;">
								<f:selectItem itemLabel="启用" itemValue="1" />
								<f:selectItem itemLabel="停用" itemValue="0" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
				</p:panelGrid>
				<p:panelGrid id="colDetailGrid"
							 style="width:100%;height:100%;position: relative;top: -1px;">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<font color="red">*</font>
							<h:outputLabel value="列类型：" />
						</p:column>
						<p:column
								style="text-align:left;padding-left:3px;#{examPoolBean.colsdefine.colType!=0?'width:180px;':''}"
								colspan="#{examPoolBean.colsdefine.colType!=0?'1':'3'}">
							<p:selectOneMenu value="#{examPoolBean.colsdefine.colType}"
											 required="true" requiredMessage="请选择列类型">
								<f:selectItem itemLabel="请选择..." itemValue="" />
								<f:selectItem itemLabel="标题列" itemValue="0" />
								<f:selectItem itemLabel="文本" itemValue="1" />
								<f:selectItem itemLabel="整数" itemValue="2" />
								<f:selectItem itemLabel="小数" itemValue="3" />
								<f:selectItem itemLabel="日期" itemValue="4" />
								<f:selectItem itemLabel="计算" itemValue="5" />
								<f:selectItem itemLabel="下拉" itemValue="6" />
								<p:ajax process="@this" update="colDetailGrid,dsTypePanel"
										event="change" />
							</p:selectOneMenu>
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:120px;"
								  rendered="#{examPoolBean.colsdefine.colType!=0}">
							<font color="red">*</font>
							<h:outputLabel value="是否必填：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;"
								  rendered="#{examPoolBean.colsdefine.colType!=0}">
							<p:selectOneRadio value="#{examPoolBean.colsdefine.colMust}"
											  required="true" requiredMessage="是否必填不能为空" style="width:100px;"
											  rendered="#{examPoolBean.colsdefine.colType != 0}">
								<f:selectItem itemLabel="是" itemValue="1" />
								<f:selectItem itemLabel="否" itemValue="0" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row rendered="#{examPoolBean.colsdefine.colType==5}">
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<font color="red">*</font>
							<h:outputLabel value="表达式：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<p:inputTextarea value="#{examPoolBean.colsdefine.colExpr}"
											 maxlength="200" cols="65" rows="2" required="true"
											 requiredMessage="表达式不能为空" autoResize="false" />
						</p:column>
					</p:row>
					<p:row
							rendered="#{examPoolBean.colsdefine.colType!=null and examPoolBean.colsdefine.colType!=0 and examPoolBean.colsdefine.colType!=5}">
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="列长度：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.colLenth}"
										 converterMessage="列长度请输入数字" size="5" maxlength="5" />
						</p:column>
						<p:column style="text-align:right;padding-right:3px;">
							<h:outputLabel value="默认值：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{examPoolBean.colsdefine.colDefvalue}"
										 size="25" maxlength="25" />
						</p:column>
					</p:row>
					<p:row
							rendered="#{examPoolBean.colsdefine.colType ==2 or examPoolBean.colsdefine.colType==3}">
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputLabel value="是否范围约束：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;"
								  colspan="#{examPoolBean.colsdefine.colType==3?'1':'3'}">
							<p:selectOneRadio value="#{examPoolBean.colsdefine.scopeCons}"
											  style="width:100px;">
								<f:selectItem itemLabel="是" itemValue="1" />
								<f:selectItem itemLabel="否" itemValue="0" />
								<p:ajax event="change" process="@this" update="scopePanel" />
							</p:selectOneRadio>
						</p:column>
						<p:column style="text-align:right;padding-right:3px;"
								  rendered="#{examPoolBean.colsdefine.colType==3}">
							<h:outputLabel value="列小数点保留位数：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;"
								  rendered="#{examPoolBean.colsdefine.colType==3}">
							<p:inputText value="#{examPoolBean.colsdefine.colPrec}" size="5"
										 maxlength="2" />
						</p:column>
					</p:row>
				</p:panelGrid>
				<p:outputPanel id="scopePanel">
					<p:panelGrid id="scopeGrid"
								 style="width:100%;height:100%;position: relative;top: -2px;"
								 rendered="#{examPoolBean.colsdefine.scopeCons==1}">
						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputLabel value="最小值：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;width:180px">
								<p:inputText value="#{examPoolBean.colsdefine.minValue}"
											 size="25" maxlength="50" />
							</p:column>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputLabel value="最大值：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;">
								<p:inputText value="#{examPoolBean.colsdefine.maxValue}"
											 size="25" maxlength="50" />
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:outputPanel>
				<p:outputPanel id="dsTypePanel">
					<p:panelGrid id="dsTypeGrid"
								 style="width:100%;height:100%;position: relative;top: -2px;"
								 rendered="#{examPoolBean.colsdefine.colType!=null and examPoolBean.colsdefine.colType!=0 and examPoolBean.colsdefine.colType!=5}">
						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<font color="red">*</font>
								<h:outputLabel value="取值类型：" />
							</p:column>
							<p:column
									style="text-align:left;padding-left:3px;#{examPoolBean.colsdefine.dsType==2?'width:180px;':''}"
									colspan="#{examPoolBean.colsdefine.dsType==2?'1':'3'}">
								<p:selectOneMenu value="#{examPoolBean.colsdefine.dsType}"
												 required="true" requiredMessage="请选择列类型">
									<f:selectItem itemLabel="请选择..." itemValue="" />
									<f:selectItem itemLabel="一般" itemValue="1" />
									<f:selectItem itemLabel="码表" itemValue="2" />
									<f:selectItem itemLabel="SQL" itemValue="3" />
									<p:ajax process="@this" update="dsTypeGrid" event="change" />
								</p:selectOneMenu>
							</p:column>
							<p:column style="text-align:right;padding-right:3px;width:120px;"
									  rendered="#{examPoolBean.colsdefine.dsType==2}">
								<font color="red">*</font>
								<h:outputLabel value="码表编码：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;"
									  rendered="#{examPoolBean.colsdefine.dsType==2}">
								<p:inputText value="#{examPoolBean.colsdefine.dsCdcode}"
											 required="true" requiredMessage="请输入码表编码" size="25"
											 maxlength="50" />
							</p:column>
						</p:row>
						<p:row rendered="#{examPoolBean.colsdefine.dsType==3}">
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<font color="red">*</font>
								<h:outputLabel value="取值SQL：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;" colspan="3">
								<p:inputTextarea value="#{examPoolBean.colsdefine.dsSql}"
												 maxlength="200" cols="65" rows="2" required="true"
												 requiredMessage="取值SQL不能为空" autoResize="false" />
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:outputPanel>
				<p:outputPanel id="dsScriptPanel">
					<p:panelGrid id="dsScriptGrid"
								 style="width:100%;height:100%;position: relative;top: -3px;" >
						<p:row>
							<p:column style="text-align:right;padding-right:3px;width:120px;">
								<h:outputText value="脚本：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;" colspan="3">
								<p:commandButton value="编辑" onclick="PF('TableLibScrDialog').show()" process="@this"
												 update=":tabView:editForm:tableLibScript" />
							</p:column>
						</p:row>
					</p:panelGrid>
				</p:outputPanel>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="确定" icon="ui-icon-check"
											 id="colsdefineSubmitBtn"
											 process="@this,colsdefineEditGrid,dsTypePanel,colDetailGrid,scopeGrid,dsScriptPanel"
											 update="colsdefineTable,colsdefineEditGrid,dsTypePanel,colDetailGrid,scopeGrid,dsScriptPanel"
											 action="#{examPoolBean.colsdefineSaveAction}" />
							<p:commandButton value="关闭" icon="ui-icon-close"
											 id="colsdefineCancelBtn" process="@this"
											 action="#{examPoolBean.colsdefineModCancelAction()}"
											 oncomplete="PF('ColsdefineDialog').hide()">
							</p:commandButton>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
		</h:panelGrid>

		<!--编辑初始化脚本-->
		<p:dialog header="得分脚本" widgetVar="LibScrDialog" id="libScrDialog"
				  maximizable="true" resizable="false">
			<p:inputTextarea value="#{examPoolBean.tsProbExampool.scoreScript}" cols="85"
							 rows="18" id="libScript" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
										 id="LibScrDialogSaveBtn" oncomplete="PF('LibScrDialog').hide();"
										 process="@this,libScript" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
										 id="LibScrDialogCancelBtn" onclick="PF('LibScrDialog').hide();"
										 immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!-- 表格列脚本 -->
		<p:dialog header="脚本" widgetVar="TableLibScrDialog" id="tableLibScrDialog"
				  maximizable="true" resizable="false">
			<p:inputTextarea value="#{examPoolBean.colsdefine.execScript}" cols="85"
							 rows="18" id="tableLibScript" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
										 id="tableLibScrDialogSaveBtn" oncomplete="PF('TableLibScrDialog').hide();"
										 process="@this,tableLibScript" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
										 id="tableLibScrDialogCancelBtn" onclick="PF('TableLibScrDialog').hide();"
										 immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!-- 页面脚本 -->
		<p:dialog header="页面脚本" widgetVar="PageScrDialog" id="pageScrDialog"
				  maximizable="true" resizable="false">
			<p:inputTextarea value="#{examPoolBean.tsProbExampool.execScript}" cols="85"
							 rows="18" id="pageLibScript" autoResize="false" />
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
										 id="pageScrDialogSaveBtn" oncomplete="PF('PageScrDialog').hide();"
										 process="@this,pageLibScript" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
										 id="pageScrDialogCancelBtn" onclick="PF('PageScrDialog').hide();"
										 immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!-- 选项添加、修改 -->
		<p:dialog width="800" widgetVar="OptDialog" id="optDialog"
				  resizable="false" minHeight="400" header="选项" maximizable="true">
			<p:panelGrid style="width:100%;" id="optEditGrid">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputLabel value="序号：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:220px">
						<p:inputText value="#{examPoolBean.tsProPoolOpt.num}" size="5"
									 maxlength="5" required="true" requiredMessage="请输入序号"
									 converterMessage="序号请输入数字！" />
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="是否正确答案：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:selectOneRadio value="#{examPoolBean.tsProPoolOpt.isCorrect}"
										  style="width:100px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
						</p:selectOneRadio>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
							  rendered="false">
						<h:outputLabel value="选项图片：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;"
							  rendered="false">
						<h:panelGroup id="optionImgGroup">
							<p:commandButton value="上传" oncomplete="PF('fileUpVar').show();"
											 rendered="#{examPoolBean.optImg==null}" process="@this"
											 update=":tabView:editForm:fileUp">
								<f:setPropertyActionListener target="#{examPoolBean.uploadTag}" value="1" />
								<f:setPropertyActionListener target="#{examPoolBean.isImg}" value="0" />
							</p:commandButton>
							<h:panelGroup rendered="#{examPoolBean.optImg!=null}">
								<h:graphicImage url="/webFile#{examPoolBean.optImg}"
												width="100px" height="65px" />
								<p:spacer width="5" />
								<p:commandButton value="删除" update="optionImgGroup"
												 process="@this" action="#{examPoolBean.deleteDiskFile}">
									<f:setPropertyActionListener target="#{examPoolBean.uploadTag}"
																 value="1" />
								</p:commandButton>
							</h:panelGroup>
							<h:outputText style="color:blue;" value="[推荐像素：124*94]" />
						</h:panelGroup>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputLabel value="选项文字：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" colspan="3">
						<p:inputTextarea value="#{examPoolBean.tsProPoolOpt.optionDesc}"
										 maxlength="250" cols="65" rows="2" required="true"
										 requiredMessage="请输入选项文字" autoResize="false" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="说明：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" colspan="3">
						<p:inputTextarea value="#{examPoolBean.tsProPoolOpt.otherDesc}"
										 maxlength="1000" cols="65" rows="2" autoResize="false" />
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputLabel value="选项值：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:220px;"
							  colspan="#{examPoolBean.tsProbExampool.questType.typeNo==1?1:3}">
						<p:inputText value="#{examPoolBean.tsProPoolOpt.optionValue}"
									 required="true" requiredMessage="请输入选项值" size="10" maxlength="9" />
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
							  rendered="#{examPoolBean.tsProbExampool.questType.typeNo==1}">
						<h:outputLabel value="是否互斥：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" colspan="3"
							  rendered="#{examPoolBean.tsProbExampool.questType.typeNo==1}">
						<p:selectOneRadio value="#{examPoolBean.tsProPoolOpt.isAlter}">
							<f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
							<f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
						</p:selectOneRadio>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
							  rendered="false">
						<h:outputLabel value="分值：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;"
							  rendered="false">
						<p:inputText value="#{examPoolBean.tsProPoolOpt.optionScore}"
									 size="5" maxlength="5" converterMessage="分值请输入整数！" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<p:panelGrid id="optFillGrid"
						 style="width:100%;height:100%;position: relative;top: -1px;">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="是否需要填空：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;width:220px;"
							  id="isNeedFill"
							  colspan="#{examPoolBean.tsProPoolOpt.needFill==1?'1':'3'}">
						<p:selectOneRadio value="#{examPoolBean.tsProPoolOpt.needFill}"
										  style="width:100px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
							<p:ajax process="@this" update="optFillGrid" event="change"></p:ajax>
						</p:selectOneRadio>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
							  rendered="#{examPoolBean.tsProPoolOpt.needFill==1}">
						<h:outputLabel value="是否多项填空：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;"
							  rendered="#{examPoolBean.tsProPoolOpt.needFill==1}">
						<p:selectOneRadio value="#{examPoolBean.tsProPoolOpt.isMulti}"
										  style="width:100px;">
							<f:selectItem itemLabel="是" itemValue="1" />
							<f:selectItem itemLabel="否" itemValue="0" />
						</p:selectOneRadio>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
							  rendered="#{examPoolBean.tsProbExampool.questType.typeNo!=10}">
						<h:outputLabel value="状态：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;"
							  rendered="#{examPoolBean.tsProbExampool.questType.typeNo!=10}"
							  colspan="#{examPoolBean.tsProPoolOpt.needFill==1?'1':'3'}">
						<p:selectOneRadio value="#{examPoolBean.tsProPoolOpt.state}"
										  style="width:120px;">
							<f:selectItem itemLabel="启用" itemValue="1" />
							<f:selectItem itemLabel="停用" itemValue="0" />
						</p:selectOneRadio>
					</p:column>
					<p:column style="text-align:right;padding-right:3px;width:120px;"
							  rendered="#{examPoolBean.tsProPoolOpt.needFill==1}">
						<h:outputLabel value="是否有数字填空：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;"
							  colspan="#{examPoolBean.tsProbExampool.questType.typeNo==10?'3':'1'}"
							  rendered="#{examPoolBean.tsProPoolOpt.needFill==1}">
						<h:panelGrid columns="4"
									 style="border-color: #ffffff;padding: 0;margin:0;cellpadding:0;cellmargin:0"
									 id="isNumFillCol">
							<p:selectOneRadio value="#{examPoolBean.tsProPoolOpt.isNumFill}"
											  style="width:100px;">
								<f:selectItem itemLabel="是" itemValue="1" />
								<f:selectItem itemLabel="否" itemValue="0" />
								<p:ajax process="isNumFillCol" update="isNumFillCol"
										event="change"></p:ajax>
							</p:selectOneRadio>
							<p:commandButton icon="ui-icon-plus" value="设置选项数字填空"
											 process="@this" action="#{examPoolBean.optNumFillAction}"
											 oncomplete="PF('optNumFillDialog').show()"
											 rendered="#{examPoolBean.tsProPoolOpt.isNumFill==1}"
											 update=":tabView:editForm:optNumFillDialog">
								<p:resetInput target="optNumFillDialog"></p:resetInput>
							</p:commandButton>
						</h:panelGrid>
					</p:column>
				</p:row>
				<p:row rendered="false">
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputLabel value="备注：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" colspan="3">
						<p:inputText value="#{examPoolBean.tsProPoolOpt.rmk}"
									 maxlength="250" size="67" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check" id="optSubmitBtn"
										 process="@this,optEditGrid,optFillGrid" update="optInfoTable"
										 action="#{examPoolBean.optSaveAction}" />
						<p:commandButton value="关闭" icon="ui-icon-close" id="optCancelBtn"
										 process="@this" action="#{examPoolBean.optCancelAction}">
						</p:commandButton>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!--添加常规选项-->
		<p:dialog id="templChooseDialog" widgetVar="TemplChooseDialog"
				  width="600" resizable="false" header="添加常规选项" modal="true">
			<table style="width:100%">
				<tr valign="top">
					<td style="width:80%;padding: 0 0 0 0;"><p:inputTextarea
							value="#{examPoolBean.templOpts}" cols="50" rows="15"
							id="templOpts" required="true" requiredMessage="请输入选项内容！" /></td>
					<td style="width:20%"><p:scrollPanel
							style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;height:240px;">
						<h:panelGrid columns="1"
									 style="border-color: #ffffff;padding: 0 0 0 0;cellpadding:0;">
							<c:forEach var="itm" items="#{examPoolBean.templList}">
								<p:commandButton value="#{itm.tmplName}" process="@this"
												 style="width:80px;" update="templOpts">
									<f:setPropertyActionListener
											target="#{examPoolBean.templOpts}" value="#{itm.tmplOpts}" />
								</p:commandButton>
							</c:forEach>
						</h:panelGrid>
					</p:scrollPanel></td>
				</tr>
				<tr valign="top">
					<td colspan="2"><h:outputLabel style="color:blue;"
												   value="格式为： 选项文字,选项文字" /></td>
				</tr>
			</table>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
										 process="@this,templOpts" action="#{examPoolBean.optCreateBatch}"
										 update="optInfoTable" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
										 onclick="PF('TemplChooseDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!-- 选项设置数字填空 -->
		<p:dialog id="optNumFillDialog" widgetVar="optNumFillDialog"
				  width="600" resizable="false" header="设置选项数字填空" modal="true">
			<table style="width: 100%">
				<tr>
					<td><p:outputPanel styleClass="zwx_toobar_42">
						<h:panelGrid columns="5"
									 style="border-color:transparent;padding:0px;">
								<span class="ui-separator"><span
										class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton icon="ui-icon-plus" value="添加填空数"
											 process="@this,:tabView:editForm:optNumFillTable"
											 action="#{examPoolBean.optNumFillAddInitAction}"
											 update=":tabView:editForm:optFillGrid,optNumFillTable">
							</p:commandButton>
							<p:commandButton icon="ui-icon-close" value="清空" process="@this"
											 action="#{examPoolBean.optNumFillClear}"
											 update=":tabView:editForm:optFillGrid,optNumFillTable">
								<p:confirm header="消息确认框" message="确定要清空吗？"
										   icon="ui-icon-alert" />
							</p:commandButton>
						</h:panelGrid>
					</p:outputPanel></td>
				</tr>
			</table>
			<p:dataTable var="itm" editMode="cell"
						 value="#{examPoolBean.optNumFillList}" id="optNumFillTable"
						 emptyMessage="暂无选项" rows="5" paginator="true" rowIndexVar="R"
						 paginatorPosition="bottom">
				<p:column headerText="选项填空序号" style="width:100px;text-align: center">
					<p:spinner value="#{itm.num}" style="text-align: center" min="1"
							   stepFactor="1" size="6" />
				</p:column>
				<p:column headerText="填空值最小值" style="width:150px;text-align: center">
					<p:spinner value="#{itm.minVerifyVal}" size="6" stepFactor="0.01"
							   style="text-align: center" />
				</p:column>
				<p:column headerText="填空值最大值" style="width:150px;text-align: center">
					<p:spinner value="#{itm.maxVerifyVal}" size="6" stepFactor="0.01"
							   style="text-align: center" />
				</p:column>
				<p:column headerText="操作" style="padding-left: 3px;">
					<p:spacer width="5" />
					<p:commandLink value="删除" process="@this,optNumFillTable"
								   update="optNumFillTable"
								   action="#{examPoolBean.optNumFillDeleteAction}">
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{examPoolBean.numFill}"
													 value="#{itm}" />
					</p:commandLink>
					<p:spacer width="5" />
				</p:column>
			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
										 onsuccess="PF('OptDialog').show();"
										 process="@this,optNumFillTable"
										 action="#{examPoolBean.optNumFillSave}"
										 update=":tabView:editForm:optFillGrid" />
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close"
										 onclick="PF('optNumFillDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!--添加码表选项-->
		<p:dialog width="400" widgetVar="CodeChooseDialog" height="300"
				  id="codeChooseDialog" resizable="false" header="添加码表选项">
			<table style="width:100%">
				<tr>
					<td style="width:25%;text-align: right;padding-right: 3px;"><font
							color="red">*</font> <h:outputLabel value="码表名称：" /></td>
					<td style="width:75%;"><p:selectOneMenu id="selectedCodeType"
															value="#{examPoolBean.selectedCodeType}" required="true"
															requiredMessage="请选择码表！">
						<f:selectItem itemValue="" itemLabel="请选择..." />
						<f:selectItems value="#{examPoolBean.codeTypeList}" />
						<p:ajax event="change" process="@this"
								listener="#{examPoolBean.optShowBatchByCode}"
								update="optCodeDataTable" />
					</p:selectOneMenu></td>
				</tr>
			</table>
			<p:dataTable id="optCodeDataTable" var="itm"
						 value="#{examPoolBean.simpleCodeList}" rowIndexVar="R"
						 emptyMessage="没有您要找的记录！ ">
				<p:column headerText="序号" size="30"
						  style="width: 20px;text-align: center;">
					<h:outputText value="#{R+1}" />
				</p:column>
				<p:column headerText="选项文字" style="width: 100px;text-align: center;">
					<h:outputText value="#{itm.codeName}" />
				</p:column>

			</p:dataTable>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="确定" icon="ui-icon-check"
										 id="codeSubmitBtn" process="@this,selectedCodeType"
										 update="optInfoTable"
										 action="#{examPoolBean.optCreateBatchByCode}" />
						<p:commandButton value="关闭" icon="ui-icon-close"
										 id="codeCancelBtn" immediate="true"
										 oncomplete="PF('CodeChooseDialog').hide()" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		<!-- 文件上传 -->
		<p:dialog header="#{examPoolBean.isImg == 1?'附件上传':'文件上传'}" widgetVar="fileUpVar" id="fileUp"
				  resizable="false" modal="true">

			<p:fileUpload requiredMessage="请选择上传图片！" style="width:700px;"
						  previewWidth="50"
						  fileUploadListener="#{examPoolBean.handleFileUpload}" fileLimit="1"
						  fileLimitMessage="最多只能上传1个文件！" label="选择文件" uploadLabel="上传"
						  cancelLabel="取消" sizeLimit="10485760" update="otherImgGroup"
						  invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，重新上传！"
						  invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png图片类型文件！" process="@this,optLayoutGrid"
						  mode="advanced" dragDropSupport="true"
						  allowTypes="/(\.|\/)(gif|jpe?g|png)$/" >
				<h:outputText value="（ 支持附件格式为：gif、jpeg、jpg、png图片类型文件 ）" styleClass="blueColorStyle" style="position: absolute;top: 13px;right: 50px; font-weight: bold;color: #ffffff;z-index: 10;"/>
			</p:fileUpload>
		</p:dialog>

	</h:form>
	<script>
		var preview = function (url) {
			window.open('/webFile'+url)
		}
	</script>
</ui:composition>
