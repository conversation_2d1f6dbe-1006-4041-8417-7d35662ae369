<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	template="/WEB-INF/templates/system/mainTemplate.xhtml">
	<ui:param name="onfocus" value="1"></ui:param>
	<ui:param name="mgrbean" value="#{tsHolidayBean}"></ui:param>
	<ui:param name="editPage" value="/webapp/system/tsHolidayEdit.xhtml"></ui:param>
	<ui:param name="viewPage" value="/webapp/system/tsHolidayAddList.xhtml"></ui:param>
	<ui:define name="insertScripts">
		<h:outputScript library="js" name="namespace.js" />
		<h:outputScript name="js/validate/system/validate.js" />
		<ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
		<style type="text/css">
table.ui-selectmanycheckbox td label {
	white-space: nowrap;
	overflow: hidden;
}

.ui-panelgrid td {
	padding-top: 2px;
	padding-bottom: 2px;
	padding-left: 5px;
	padding-right: 0px;
}

.ui-panelgrid td {
	border-width: 1px;
}
</style>
	</ui:define>
	<ui:define name="insertTitle">

		<p:row>
			<p:column colspan="6"
				style="text-align:left;padding-left:5px;height: 20px;">
				<h:outputText value="节假日配置"></h:outputText>
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertButtons">
		<p:outputPanel styleClass="zwx_toobar_h_auto">
				<span class="ui-separator" style="float: left;margin: 7px 3px auto 5px;"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
				<p:commandButton value="查询" process="@this,mainGrid"
					update="dataTable" action="#{tsHolidayBean.searchAction}"
					icon="ui-icon-search"></p:commandButton>
				<p:commandButton value="添加" process="@this" update=":tabView"
					resetValues="true" icon="ui-icon-plus"
					action="#{tsHolidayBean.addInitAction}">
				</p:commandButton>
				<p:commandButton value="批量添加" process="@this" update=":tabView"
					resetValues="true" icon="ui-icon-plus"
					action="#{tsHolidayBean.viewInitAction}">
				</p:commandButton>
		</p:outputPanel>
	</ui:define>
	<ui:define name="insertSearchConditons">
		
		<p:row>
			<p:column style="text-align:right;padding-left:8px;width:150px;">
				<h:outputText value="日期："></h:outputText>
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width:300px;">
				<p:calendar navigator="true" yearRange="c-10:c"
					value="#{tsHolidayBean.searchBeginDate}" size="11"
					converterMessage="日期格式输入不正确！" pattern="yyyy-MM-dd"
					showButtonPanel="true" showOtherMonths="true" />
				                ~
				                <p:calendar navigator="true" yearRange="c-10:c"
					value="#{tsHolidayBean.searchEndDate}" size="11"
					converterMessage="日期格式输入不正确！" pattern="yyyy-MM-dd"
					showButtonPanel="true" showOtherMonths="true" />
			</p:column>
			<p:column style="text-align:right;height:38px;width:150px;">
				<h:outputText value="日期类型："></h:outputText>
			</p:column>
			<p:column style="padding-left: 8px;">
				<p:selectManyCheckbox
					value="#{tsHolidayBean.searchtypes}">
					<f:selectItem itemLabel="工作日" itemValue="0" />
					<f:selectItem itemLabel="休息日" itemValue="1" />
					<f:selectItem itemLabel="节假日" itemValue="2" />
				</p:selectManyCheckbox>
			</p:column>
		</p:row>
	</ui:define>
	<ui:define name="insertDataTable">
		<p:column headerText="序号" style="width:30px;text-align: center;">
			<h:outputLabel value="#{R+1}" />
		</p:column>
		<p:column headerText="日期" style="width: 380px; ">
			<h:outputText value="#{itm[1]}">
				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="GMT+8"></f:convertDateTime>
			</h:outputText>
		</p:column>
		<p:column headerText="日期类型" style="width: 150px;text-align:center;">
			<h:outputText value="工作日" rendered="#{itm[2]==0}"></h:outputText>
			<h:outputText value="休息日" rendered="#{itm[2]==1}"></h:outputText>
			<h:outputText value="节假日" rendered="#{itm[2]==2}"></h:outputText>
		</p:column>
		<p:column headerText="操作" style="padding-left: 3px;">
			<p:commandLink value="修改" process="@this"
				action="#{tsHolidayBean.modInitAction}" resetValues="true"
				update=":tabView">
				<f:setPropertyActionListener target="#{tsHolidayBean.rid}"
					value="#{itm[0]}"></f:setPropertyActionListener>
			</p:commandLink>

			<p:spacer width="5"></p:spacer>
			<p:commandLink value="删除" action="#{tsHolidayBean.deleteAction}"
				update="dataTable" process="@this">
				<p:confirm header="消息框" message="确认删除吗？" icon="ui-icon-alert" />
				<f:setPropertyActionListener target="#{tsHolidayBean.rid}"
					value="#{itm[0]}"></f:setPropertyActionListener>
			</p:commandLink>
		</p:column>
	</ui:define>
</ui:composition>
