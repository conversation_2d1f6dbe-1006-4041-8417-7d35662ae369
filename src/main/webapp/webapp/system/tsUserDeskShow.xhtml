<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>
    <link rel="stylesheet" href="/resources/css/portal/portal.css" />
    <script type="text/javascript">

        if (self === top) {
            window.stop();
        }
        function openNewNoticeTab(rid){
            top.ShortcutMenuClick("01","通知公告详情", "/webapp/system/portalNoticeDetail.faces?rid="+rid,rid);
        }

    </script>
    <style type="text/css">
        /*通知公告，文件中心tab高度*/
        .ui-tabs-panel.ui-widget-content.ui-corner-bottom{
            height: 100%;
        }
        .redText{
            color: red;
        }
    </style>
    <ui:param name="mgrbean" value="#{tsUserDeskShowBean}"/>
    <h:body style="overflow-y:hidden;">
        <title>首页</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <h:form id="formView">
        <p:remoteCommand action="#{tsUserDeskShowBean.addQuickMenu}" name="addQuickMenuFun" />
        <div class="el-scrollbar ms el-scrollbar" style="height: 100%;">
            <div class="el-scrollbar__wrap">
                <div class="el-scrollbar__view">
                    <div class="ms-fragment-in" style="height: 100vh;">
                        <div style="height: 100%;">
                            <div id="one" style="height: 160px;">
                                <div style="height:163px;overflow: hidden; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px; background: rgb(255, 255, 255);">
                                    <div class="panelDiv">
                                        <span class="panelTitle">快速开始</span>
                                        <span class="ms-declare-link-header" onclick="addQuickMenuFun();" >
                                            <p:graphicImage url="/resources/images/portal/plus.png"/>
                                            添加快捷入口
                                        </span>
                                    </div>
                                    <div style="margin-left: 30px;">
                                        <c:forEach items="#{tsUserDeskShowBean.ownerMenuList}" var="itm" varStatus="status">
                                            <a href="javascript:top.menuclick('#{itm[4]}','#{itm[4]}','#{itm[3]}','#{itm[0]}');" style="color:#5a666c">
                                            <p:outputPanel styleClass="card-table">
                                                <p:outputPanel><p:graphicImage url="/resources/component/quickDesktop/image/64px/#{itm[2]}"/></p:outputPanel>
                                                <p:outputPanel style="margin-top: 5px; font-size: 13px;">#{itm[1]}</p:outputPanel>
                                            </p:outputPanel>
                                            </a>
                                        </c:forEach>
                                        <c:if test="#{tsUserDeskShowBean.ownerMenuList==null or tsUserDeskShowBean.ownerMenuList.size()==0}">
                                            <p:outputPanel styleClass="card-table-tip">
                                                <p:outputPanel><p:graphicImage url="/resources/images/portal/img_no_data.svg"/></p:outputPanel>
                                                <p:outputPanel style="margin-top: 5px;">暂无快捷入口</p:outputPanel>
                                            </p:outputPanel>
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                            <div id="two">
                                <div class="two_content" style="width: 50%;" id="notice">

                                    <p:tabView id="tabView"  cache="false" dynamic="false" >
                                        <c:if test="#{tsUserDeskShowBean.noticePojoList!=null and tsUserDeskShowBean.noticePojoList.size() > 0}">
                                            <c:forEach items="#{tsUserDeskShowBean.noticePojoList}" var="noticePojo">
                                                <p:tab title="#{noticePojo.codeName}">
                                                    <c:if test="#{noticePojo.tdPublishNoticeUnits!=null and noticePojo.tdPublishNoticeUnits.size() > 0}">
                                                        <table style="padding: 20px;" class="panelGrid">
                                                            <c:forEach items="#{noticePojo.tdPublishNoticeUnits}" var="notice">
                                                                <tr style="width: 100%;line-height: 14px;" onclick="alink_#{notice.fkByMainId.rid}();" >
                                                                    <td style="height: 25px;width: 25px;">
                                                                        <p:graphicImage  url="#{notice.fkByMainId.fkByPublishTypeId.extendS5 == null ? '#' : notice.fkByMainId.fkByPublishTypeId.extendS5}" width="18" height="18"
                                                                                         style="position: relative;" />
                                                                    </td>
                                                                    <td style="text-align: left;">
                                                                        <h:outputText value="#{notice.fkByMainId.title}"  styleClass="myTooltip"  id="title_#{notice.fkByMainId.rid}" />
                                                                        <p:tooltip for="title_#{notice.fkByMainId.rid}" value="#{notice.fkByMainId.title}" style="max-width: 400px;"  />
                                                                    </td>
                                                                    <td class="dateCol">
                                                                        <h:outputLabel styleClass="textStyle"
                                                                                       value="#{notice.fkByMainId.publishDate}" >
                                                                            <f:convertDateTime pattern="yyyy-MM-dd"
                                                                                               timeZone="Asia/Shanghai" locale="cn" />
                                                                        </h:outputLabel>
                                                                        <p:remoteCommand name="alink_#{notice.fkByMainId.rid}"
                                                                            action="#{tsUserDeskShowBean.clickNewNoticeLink}" oncomplete="openNewNoticeTab(#{notice.fkByMainId.rid})" >
                                                                            <f:setPropertyActionListener value="#{notice.fkByMainId.rid}" target="#{tsUserDeskShowBean.noticerRid}"></f:setPropertyActionListener>
                                                                        </p:remoteCommand>
                                                                    </td>
                                                                </tr>
                                                                <tr style="line-height: 14px;">
                                                                    <td style="padding-bottom: 20px;height: 25px;">
                                                                    </td>
                                                                    <td colspan="2" style="padding-bottom: 20px;">
                                                                        <p:outputLabel value="#{notice.fkByMainId.fkByPublishTypeId.codeName}" rendered="#{notice.fkByMainId.fkByPublishTypeId.extendS1 != '1'}" styleClass="textStyle"/>
                                                                        <p:outputLabel value="#{notice.fkByMainId.otherType}"  rendered="#{notice.fkByMainId.fkByPublishTypeId.extendS1 == '1'}" styleClass="textStyle"/>
                                                                        <p:spacer width="5"/>
                                                                        <p:outputLabel value="|" styleClass="textStyle" />
                                                                        <p:spacer width="5"/>
                                                                        <p:outputLabel value="#{notice.fkByMainId.fkByPublishUnitId.unitname}" styleClass="textStyle"/>
                                                                    </td>
                                                                </tr>
                                                            </c:forEach>
                                                        </table>
                                                    </c:if>
                                                    <p:outputPanel styleClass="imgNoDataDiv" rendered="#{noticePojo.tdPublishNoticeUnits == null or noticePojo.tdPublishNoticeUnits.size() == 0}">
                                                        <!-- 暂无数据 -->
                                                        <p:outputPanel>
                                                            <p:graphicImage url="/resources/images/portal/img_no_data.svg"></p:graphicImage>
                                                            <p:outputPanel style="text-align: center;">
                                                                <p:outputLabel value="暂无数据"></p:outputLabel>
                                                            </p:outputPanel>
                                                        </p:outputPanel>
                                                    </p:outputPanel>
                                                </p:tab>
                                            </c:forEach>
                                        </c:if>
                                    </p:tabView>

                                </div>
                                <div class="two_content" style="margin-left: 20px; width: calc(50% - 20px);">
                                    <ui:include src="tsUserDeskToDoTasks.xhtml"></ui:include>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <p:dialog header="菜单选择" widgetVar="MenuSelectDialog" width="800" height="450" modal="true" resizable="false" >
            <p:outputPanel styleClass="zwx_toobar_42">
                <h:panelGrid columns="4"
                             style="border-color:transparent;padding:0;">
					<span class="ui-separator">
                        <span class="ui-icon ui-icon-grip-dotted-vertical"/>
                    </span>
                    <p:commandButton value="查询" icon="ui-icon-search"
                                     action="#{mgrbean.filterQuickMenu}"
                                     process="@this,searchPanel"/>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{mgrbean.sureUserDesk}"
                                     process="@this"/>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     oncomplete="PF('MenuSelectDialog').hide();" process="@this"/>
                </h:panelGrid>
            </p:outputPanel>
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                            <p:outputLabel value="菜单名称：" styleClass="zwx_dialog_font" />
                            <p:inputText id="searchMenuName" value="#{mgrbean.searchMenuName}" style="width: 180px;" />
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <p:dataTable var="itm" value="#{mgrbean.showDeskMenuList}" id="dataTable"
                         rowsPerPageTemplate="#{'10,20,50'}"
                         paginator="true" rows="10" emptyMessage="没有数据！" rowKey="#{itm}" pageLinks="5"
                         paginatorPosition="bottom" lazy="true" selection="#{mgrbean.curPageSelectedDeskMenuList}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowSelectMode="add">
                <p:ajax event="page" process="@this,dataTable" listener="#{mgrbean.pageListener}" update="dataTable" />
                <!-- 注意没有rowUnselect 都是执行的 rowSelect 调用rowToggleSelectListener -->
                <p:ajax event="rowSelect" process="@this,dataTable" listener="#{mgrbean.rowToggleSelectListener}"
                        immediate="true" update="dataTable"/>
                <p:ajax event="rowSelectCheckbox" process="@this,dataTable" listener="#{mgrbean.rowSelectListener}"
                        immediate="true" update="dataTable"/>
                <p:ajax event="rowUnselectCheckbox" process="@this,dataTable" listener="#{mgrbean.rowUnselectListener}"
                        immediate="true" update="dataTable"/>
                <p:ajax event="toggleSelect" process="@this,dataTable" listener="#{mgrbean.toggleSelectListener}"
                        immediate="true"/>

                <p:column selectionMode="multiple" style="width:40px;text-align:center"/>
                <p:column headerText="菜单名称" style="padding-left: 10px;">
                    <h:outputText value="#{itm[2]}" escape="false" />
                </p:column>
                <p:column headerText="菜单简称" style="padding-left: 10px;">
                    <h:outputText value="#{itm[3]}" escape="false" />
                </p:column>
            </p:dataTable>
        </p:dialog>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/confirm.xhtml"/>
    </h:body>
</f:view>
</html>
