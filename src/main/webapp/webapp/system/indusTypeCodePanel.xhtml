<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <table width="100%" >
        <tr>
            <td style="text-align: left;padding-left: 3px;border-color:transparent" >
                <h:panelGrid columns="5" id="searchIndusPanel" style="border-color:transparent">
                    <p:selectOneMenu value="#{indusbean.selectTopCodeNo}" id="topCodeNo" style="width: 180px;">
                        <f:selectItem itemValue="" itemLabel="--门类--"/>
                        <f:selectItems value="#{indusbean.topTypeList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                        <p:ajax event="change" listener="#{indusbean.onTopCodeTypeSelect}" process="@this" update="firstIndusCodeNo,middleCodeNo,selectedIndusCodeTable"/>
                    </p:selectOneMenu>
                    <p:selectOneMenu value="#{indusbean.selectFatherCodeNo}" id="firstIndusCodeNo" style="width: 180px;">
                        <f:selectItem itemValue="" itemLabel="--大类--"/>
                        <f:selectItems value="#{indusbean.fatherTypeList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                        <p:ajax event="change" listener="#{indusbean.onFatherCodeTypeSelect}" process="@this" update="middleCodeNo,selectedIndusCodeTable"/>
                    </p:selectOneMenu>
                    <p:selectOneMenu value="#{indusbean.selectMiddleCodeNo}" id="middleCodeNo" style="width: 180px;">
                        <f:selectItem itemValue="" itemLabel="--中类--"/>
                        <f:selectItems value="#{indusbean.middleTypeList}" var="itm" itemValue="#{itm.codeNo}" itemLabel="#{itm.codeName}"/>
                        <p:ajax event="change" listener="#{indusbean.searchAction}" process="@this,searchIndusPanel" update="selectedIndusCodeTable"/>
                    </p:selectOneMenu>
                    <p:inputText id="pymIndus" value="#{indusbean.searchContext}" maxlength="20" style="width: 180px;" placeholder="小类名称/拼音码">
                        <p:ajax event="keyup" update="selectedIndusCodeTable" process="@this,searchIndusPanel" listener="#{indusbean.searchAction}"/>
                    </p:inputText>
                </h:panelGrid>
            </td>
        </tr>
    </table>
    <p:dataTable var="itm"   value="#{indusbean.showIndusList}" id="selectedIndusCodeTable"
                 paginator="true" rows="10" emptyMessage="没有数据！" rowsPerPageTemplate="10,20" pageLinks="5"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 paginatorPosition="bottom" currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页" >
        <p:column headerText="选择" style="width:50px; height:22px;text-align:center">
            <p:commandLink value="选择" action="#{mgrbean.selectIndusTypeAction}" process="@this" rendered="#{itm.ifDisabled}">
                <f:setPropertyActionListener value="#{itm}" target="#{mgrbean.selectPro}"/>
            </p:commandLink>
        </p:column>
        <p:column headerText="门类" style="padding-left: 3px;width: 180px;">
            <h:outputText value="#{indusbean.splitCodePath(itm.codePath, 0)}" />
        </p:column>
        <p:column headerText="大类" style="padding-left: 3px;width: 180px;">
            <h:outputText value="#{indusbean.splitCodePath(itm.codePath, 1)}" />
        </p:column>
        <p:column headerText="中类" style="padding-left: 3px;width: 180px;">
            <h:outputText value="#{indusbean.splitCodePath(itm.codePath, 2)}" />
        </p:column>
        <p:column headerText="小类" style="padding-left: 3px;width: 180px;">
            <h:outputText value="#{indusbean.splitCodePath(itm.codePath, 3)}" />
        </p:column>
    </p:dataTable>
</ui:composition>