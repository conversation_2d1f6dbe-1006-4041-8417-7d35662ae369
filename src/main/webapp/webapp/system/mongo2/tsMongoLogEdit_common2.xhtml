<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">

        <p:fieldset  legend="总包原始信息" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
            <p:panelGrid style="width:100%;" styleClass="log-panelgrid">
                <p:row rendered="#{mgrbean.ifAdmin}">
                    <p:column style="text-align:right;padding-right:3px;width: 300px">
                        <h:outputText value="请求内容："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <p:commandLink value="查看" action="#{mgrbean.showContentAction}" process="@this" update="respTextDialog">
                            <f:setPropertyActionListener  value="#{mgrbean.mainText.req_text}" target="#{mgrbean.showContent}"/>
                        </p:commandLink>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifAdmin}">
                    <p:column style="text-align:right;padding-right:3px;width: 300px">
                        <h:outputText value="原始报文："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <p:commandLink value="查看" action="#{mgrbean.showContentAction}" process="@this" update="respTextDialog">
                            <f:setPropertyActionListener  value="#{mgrbean.mainText.req_encrypt_text}" target="#{mgrbean.showContent}"/>
                        </p:commandLink>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrbean.ifAdmin}">
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="响应内容："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <p:commandLink value="查看"  action="#{mgrbean.showContentAction}"  process="@this" update="respTextDialog">
                            <f:setPropertyActionListener value="#{mgrbean.mainText.resp_text}" target="#{mgrbean.showContent}"/>
                        </p:commandLink>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width: 300px">
                        <h:outputText value="上传数据条数："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <h:outputText value="#{mgrbean.entityMain.sub_count}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;">
                        <h:outputText value="成功条数："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:10px;">
                        <h:outputText value="#{mgrbean.entityMain.success_count}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>

</ui:composition>

