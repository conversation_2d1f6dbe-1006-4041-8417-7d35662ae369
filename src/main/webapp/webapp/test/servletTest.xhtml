<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>servlet测试</title>
        <h:outputStylesheet library="css" name="default.css"/>
        <style type="text/css">
            
        </style>
	    <script type="text/javascript">
	        //<![CDATA[
	        var fileContent='H4sIAAAAAAAAAK1XWbIbIQy8EoNAYj79Zrn/kdItmHnMZjuVxFUxRo1Qa+XJJKsVnXWKwVSzRRMdddVF5hhliIuEGGPQrIsK9hOkixqlli2pxlkKkKss80umjB3oKDlCV9AV6xRDvmpO0CzUnBJQKc06WLKo4/EssVA56ITT2fG8f8TeqJNmyvOUaZnivEELTueJZ0z87k6b+Y05EGPUkM3SasFStQzyJTkv7PjvaqsEGSV/PptfWK06N99teFgMhmfsLqFveGJOrzSNS/n2XMIOmBfEJldP1Vj1jC/shPbT15rhef9uHAeJkqFhwbpyVuyBt+hHreQAuMdvl8BW7pdq20cdU41YIqOBOdZZxn/mlgUpYvh/gF0Rq+qt7m7q4WkDy01HDtSN/EUe9djz2c3ek+UaxW34aasgP0ovGuNwJ709wQqC5j264N8iMNTYgR3YwxyaBKOcbXS2AboyfqP82m+PlXsjcR932P5ddfA318HvDzWHXTMxEd+jr5XrtJ3p8RkfaXrGHdPksBgVH0MMv1W/8aG/6XtU64LVXKOwSb2KmbUjMM3H250uQ12zmruMZo8I3W96njVaTmfXq019zt3bte1uFQsrA2vydIvfc0RcWET4yU5a0PXs1XJ57Pl5z4s1izYerUqazkeE++NJmphbtYPNjxh2zAk1MqOHHHle5fQeWfRd6FTZqSRWHPSwE9xUvr6Tb1F7E7nk90+X2N1i5D7H8EEPYQUZ/FA+5hoxCyoMMWXm32SeNczgOWh9Bp4qY67dzbNmq4NjddwgHngUdOQFVjHbhmNOPfAIzjy0jjB8xeSvciRx8vYVyhw0IiBftho/RvriIWpac5utF++4VOKFbfJXSu13pb4pOu7RfZW889ktc++87p3RO2h8yJ2LnsR8E2QfJxdfJMhC9XdMnYjK987cZg6zlJXIuK513tdZwHP0EGczJ5+/k/Kvx04IeHw/P3v3Nv+QRTmhTxwcLydM7+PBXxaw94iBa5Ptr62uB3IG8h3SkNe3Wl/lHF1e+5caieu7KnnoDa1n+91rPXHf1XvEf+oLzLnRsyF5dyjf1NM/Z/5wsUv5UvPcZ/SHLyaloYsRP/J19zCHLz0PvR0xdZTPEGbp74w6xzn/7FNW8vwu2rcR2ud3h7uZ4PWvjXsMZX8AlUEjx9gMAAA=';
	        function test() {
				var frpt=document.getElementById("frpt");
				
				logInfo('label0');
				
				//var bool=frpt.GetRptFile('1001', '1');
				///logInfo(bool);
				
				frpt.SetRptFile('1001', '1', fileContent, '12');
				logInfo('label1');
				
				//frpt.DesignRptFile('1001', '1', '12');
				//logInfo('label2');
	        }       
	        
	        function logInfo(content) {
	        	var v1=document.getElementById("log").innerHTML;
	        	document.getElementById("log").innerHTML=v1+'<h3>'+content+'</h3>';
	        }
	        
	        function printf() {
	        	var frpt=document.getElementById("frpt");
	        	var xmlrpt=document.getElementById("mainForm:xmlData").value;
	        	//frpt.SetRptFile('1001', '1', fileContent, '12');
	        	//alert(xmlrpt);
	        	var v1=frpt.ShowReport('1001', '1',xmlrpt, '12');
	        	//alert(v1);
	        	//alert(frpt.GetErrorMsg());
	        }
	        
	        //]]>
	    </script>
    </h:head>

    <h:body onload="test()">
		<OBJECT ID="frpt" WIDTH="0" HEIGHT="0" CLASSID="CLSID:E2C2FBA6-871A-4FD8-90D3-ABC66B1D674C">
		</OBJECT>
		<a href="/webapp/flow/tdFlowInstanceList.faces">发起流程</a>
		
		<h:form id="mainForm">
			<h:inputHidden id="xmlData" value="#{fastReportTestBean.xmlData}"/>
			<p:commandButton value="测试" type="button" onclick="printf()"/>
		</h:form>
		
		<div id="log" style="width: 100%;background: gray;">
		</div>
		
		
    </h:body>
</f:view>
</html>
