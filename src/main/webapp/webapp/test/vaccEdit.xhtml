<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

	<p:panelGrid style="width:100%;height:100%;" id="vaccGrid">
		<f:facet name="header">
			<p:row>
				<p:column colspan="2" style="text-align:left;padding-left:5px;height: 20px;">
					<h:outputText value="请假单" />
				</p:column>
			</p:row>
		</f:facet>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<font color="red">*</font>
				<h:outputText value="请假天数：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<p:inputText id="vaccDay" value="#{vaccBean.tdTestVacc.vaccDay}" required="true" requiredMessage="请假天数不允许为空" maxlength="3" />
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;">
				<font color="red">*</font>
				<h:outputText value="请假原因：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<p:inputText id="vaccReason" value="#{vaccBean.tdTestVacc.vaccReason}" required="true" requiredMessage="请假原因不允许为空" maxlength="100" />
			</p:column>
		</p:row>

		<p:row>
			<p:column style="text-align:right;padding-right:3px;width:180px;height:27px;">
				<h:outputText value="请假人：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;">
				<h:outputText value="#{vaccBean.tdTestVacc.vaccMan}" />
			</p:column>
		</p:row>
	</p:panelGrid>
	
	<p:commandButton value="测试签名版" action="#{vaccBean.penSignAction}" process="@this">
		<p:ajax event="dialogReturn" listener="#{vaccBean.onPenSignCallback}"/>	
	</p:commandButton>
	
	<p:commandButton value="测试快拍" action="#{vaccBean.vedioInputAction}" process="@this">
		<p:ajax event="dialogReturn" listener="#{vaccBean.onVedioInputCallback}"/>	
	</p:commandButton>
	
	<p:commandButton value="PDF在线阅读" action="#{vaccBean.pdfViewAction}" process="@this">
	</p:commandButton>
	
	<p:commandLink value="下载测试" ajax="false" immediate="true" process="@this" action="#{vaccBean.downLoadDiskFile}"
		onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
		<f:setPropertyActionListener target="#{vaccBean.downloadFilePath}" value="/temp/aaa.pdf"/>
		<p:fileDownload value="#{vaccBean.streamedContent}" ></p:fileDownload>		
	</p:commandLink>	
	
	<p:commandButton value="通用上传" action="#{vaccBean.commonUploadAction}" process="@this">
		<p:ajax event="dialogReturn" listener="#{vaccBean.onCommonUploadCallback}"/>	
	</p:commandButton>	
	
	<p:outputPanel id="signPanel">
		<ui:repeat var="itm" value="#{vaccBean.signList}">
			<img src="/webFile/#{itm}"/>
		</ui:repeat>
		
		
	</p:outputPanel>
	
	<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
</ui:composition>

