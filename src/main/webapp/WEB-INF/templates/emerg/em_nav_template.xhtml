<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
		<script type="text/javascript" src="/resources/component/pager/kkpager.js"></script>
		<link rel="stylesheet" type="text/css" href="/resources/component/pager/kkpager.css" />
		
		<style type="text/css">
body {
	margin: 0;
	padding: 0;
	width:100%;
	height: 100%; 
}	

#head_div {
	width:100%;
	height: 90px;
	padding: 0;
	background: url(/resources/images/emerg/lxbxdc_bg.png) repeat-x;

}

#head_cont_div{
	width:100%;
	height: 90px;
	display: table-cell;
	vertical-align: middle;	
}

#footer{
 position:absolute;
 bottom:0;
 width:100%;
 height:1px;
 line-height:1px;
 text-align:center;
}	

#content_div {
 position: absolute!important;
 top:90px!important;
 height:auto!important;
 
 position: relative;
 top:-90px; 
 height:100%;
  
 bottom:1px;
 width:100%;
 background:url(/resources/images/emerg/bg.png) repeat-x;
 text-align:center;
 overflow: auto;
}

#nav_div {
	width:100%;
	height:50px;
	padding-left:50px;
	background: url(/resources/images/emerg/line.png) repeat-x;
}

#nav_div .nav_cont_div {
	height: 50px;
	display: table-cell;
	vertical-align: middle;	
}

#nav_div .nav_cont_div a {
	text-decoration: none;
}

.nav_href_cont {
	font-size: 20px;
	font-weight: bold;
	font-family: Sim Hei;
	color: blue;	
}
		</style>
		<script type="text/javascript">
		//<![CDATA[
		function forwardtoevent() {
			var url = "#{request.scheme}" + "://" + "#{request.serverName}"+":"+"#{request.serverPort}"+"#{request.contextPath}"+"/webapp/emerg/tdEmEpiEventList.faces";
        	this.location.href=url;			
		}    
		
		function forward_between_nav(type) {
			forwardToPage([{"name":"turnURL", "value":type}]);
		}
		
		function forward_from_tableType(type, tableType) {
			forwardToPage([{"name":"turnURL", "value":type}, {"name":"tableType", "value":tableType}]);
		}
 		//]]>		           
		</script>
	</h:head>

    <h:body>
    	<h:form id="mainForm">
    		<ui:insert name="insertScripts"/>
	       	<div id="head_div">
	       		<div id="head_cont_div">
	        		<img src="/resources/images/emerg/lxbxdc_logo.png" style="cursor: pointer;" onclick="forwardtoevent();"/>
	       		</div>
	       	</div>
	       	<div id="content_div#{noSeeCol}">
	       		<ui:insert name="insertContent"/>
	       	</div>
	       	
	       	<p:remoteCommand name="forwardToPage" process="@this" action="#{mgrbean.forwardToPage}" immediate="true"/>
	       	<p:remoteCommand name="forwardToFlow" process="@this" action="#{mgrbean.forwardToFlow}" immediate="true"/>
            <ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
            <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>	       	
    	</h:form>
	    <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
	    <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>    	
    </h:body>
</f:view>
</html>



