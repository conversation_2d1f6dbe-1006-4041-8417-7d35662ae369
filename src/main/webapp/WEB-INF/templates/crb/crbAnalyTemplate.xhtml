<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<f:view contentType="text/html">
    <h:head>
        <!-- 页面表格样式 -->
        <h:outputScript name="js/dataTable.js" rendered="#{dataTableStyle == null}"/>
        <script type="text/javascript" src="/resources/component/fusioncharts/FusionCharts.js"></script>
        <style type="text/css">
            .ui-panelgrid td {
                padding-top: 2px;
                padding-bottom: 2px;
                padding-left: 5px;
                padding-right: 0px;
            }

            .ui-panelgrid td {
                border-width: 1px;
            }
        </style>
    </h:head>

    <h:body>
        <h:form id="mainForm">
            <h:outputStylesheet name="css/default.css"/>
            <ui:insert name="insertScripts"/>

            <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                <f:facet name="header">
                    <ui:insert name="insertTitle"/>
                </f:facet>
            </p:panelGrid>

            <p:outputPanel id="buttonsPanel">
                <p:outputPanel styleClass="zwx_toobar_42">
                    <h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
                        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                        <p:row>
                            <p:column colspan="6" style="text-align:left;padding-left:5px;">
                                <p:commandButton value="分析" icon="ui-icon-search" id="searchBtn"
                                                 action="#{mgrbean.searchAction}"
                                                 update="xml1,datatable"
                                                 process="@this,@form"/>
                                <ui:insert name="insertButtons"/>
                            </p:column>
                        </p:row>
                    </h:panelGrid>
                </p:outputPanel>
            </p:outputPanel>
            <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                        rendered="#{condition==null}">
                <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;width:125px;height: 25px;">
                            <h:outputText value="年份："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:8px;height: 25px;width: 220px;">
                            <p:selectOneMenu value="#{mgrbean.searchYear}">
                                <f:selectItems value="#{mgrbean.yearList}"/>
                            </p:selectOneMenu>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;width:120px;">
                            <h:outputText value="地区："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;width: 220px;">
                            <zwx:ZoneSingleComp zoneList="#{mgrbean.zoneList}" zoneCode="#{mgrbean.searchZoneCode}"
                                                zoneType="#{mgrbean.searchZoneType}" zoneName="#{mgrbean.searchZoneName}"/>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;width:80px;">
                            <h:outputText value="统计类型："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;">
                            <p:selectOneRadio value="#{mgrbean.analyType}" style="width: 180px;">
                                <f:selectItem itemLabel="发病数" itemValue="0"/>
                                <f:selectItem itemLabel="死亡数" itemValue="1"/>
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;">
                            <h:outputText value="疾病分类："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;">
                            <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;"
                                         id="searchDiseaseTypeGrid">
                                <p:inputText id="searchDiseaseTypeName" value="#{mgrbean.searchDiseaseTypeName}"
                                             style="width: 180px;" readonly="true"/>
                                <h:inputHidden id="searchDiseaseTypeCode" value="#{mgrbean.searchDiseaseTypeCode}"/>
                                <p:commandLink styleClass="ui-icon ui-icon-search" id="initTreeLink" process="@this"
                                               style="position: relative;left: -40px;"
                                               oncomplete="PF('DiseaseOverlayPanel').show()" type="button"/>
                            </h:panelGrid>
                            <p:overlayPanel id="diseaseOverlayPanel" for="searchDiseaseTypeName" style="width:280px;"
                                            widgetVar="DiseaseOverlayPanel"
                                            showCloseIcon="true" onHide="diseaseTypeHide();">
                                <p:tree var="node" selectionMode="checkbox" id="diseaseTree"
                                        value="#{mgrbean.searchDiseaseTypeNode}"
                                        style="width: 250px;height: 400px;overflow-y: auto;"
                                        selection="#{mgrbean.searchSelectedDiseaseType}">
                                    <p:treeNode>
                                        <h:outputText value="#{node.codeName}"/>
                                    </p:treeNode>
                                </p:tree>
                            </p:overlayPanel>
                            <p:remoteCommand name="diseaseTypeHide"
                                             process="@this,searchDiseaseTypeGrid,diseaseOverlayPanel"
                                             action="#{mgrbean.diseaseTypeChgAction}"
                                             update="searchDiseaseTypeGrid,searchDiseaseGrid,disOverlayPanel"/>
                        </p:column>

                        <p:column style="text-align:right;padding-right:3px;">
                            <h:outputText value="仅重点传染病："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;">
                            <p:selectOneRadio value="#{mgrbean.ifImport}" style="width: 120px;">
                                <f:selectItem itemLabel="否" itemValue="0"></f:selectItem>
                                <f:selectItem itemLabel="是" itemValue="1"></f:selectItem>
                                <p:ajax event="change" process="@this,searchDiseaseTypeCode"
                                        listener="#{mgrbean.diseaseTypeChgAction}"
                                        update="searchDiseaseCode,searchDiseaseGrid,disOverlayPanel"></p:ajax>
                            </p:selectOneRadio>
                        </p:column>
                        <p:column style="text-align:right;padding-right:3px;width:120px;height: 25px;">
                            <h:outputText value="疾病名称："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;height: 25px;">
                            <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;"
                                         id="searchDiseaseGrid">
                                <p:inputText id="searchDiseaseName" value="#{mgrbean.searchDiseaseName}"
                                             style="width: 180px;" readonly="true"/>
                                <h:inputHidden id="searchDiseaseCode" value="#{mgrbean.searchDiseaseCode}"/>
                                <p:commandLink styleClass="ui-icon ui-icon-search" id="initDisTreeLink" process="@this"
                                               style="position: relative;left: -40px;"
                                               oncomplete="PF('DisOverlayPanel').show()" type="button"/>
                            </h:panelGrid>
                            <p:overlayPanel id="disOverlayPanel" for="searchDiseaseName" style="width:280px;"
                                            widgetVar="DisOverlayPanel"
                                            showCloseIcon="true" onHide="diseaseHide();">
                                <p:tree var="node" selectionMode="checkbox" id="disTree"
                                        value="#{mgrbean.searchDiseaseNode}"
                                        style="width: 250px;height: 400px;overflow-y: auto;"
                                        selection="#{mgrbean.searchSelectedDisease}">
                                    <p:treeNode>
                                        <h:outputText value="#{node.diseName}"/>
                                    </p:treeNode>
                                </p:tree>
                            </p:overlayPanel>
                            <p:remoteCommand name="diseaseHide" process="@this,searchDiseaseGrid,disOverlayPanel"
                                             action="#{mgrbean.diseaseChgAction}" update="searchDiseaseGrid"/>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </p:fieldset>

            <h:inputHidden id="xml1" value="#{mgrbean.xml1}"/>
            <table style="width: 100%">
            	<tr>
                    <td style="width: 100%">
                        <div id="chartImage1" align="center" style="vertical-align: middle;"/>
                    </td>
                </tr>
                <tr>
                    <td style="width: 100%">
                        <ui:insert name="insertContent"/>
                    </td>
                </tr>
            </table>
            <ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
            <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
        </h:form>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    </h:body>
</f:view>
</html>