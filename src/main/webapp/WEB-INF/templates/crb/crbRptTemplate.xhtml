<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html" 
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
	  xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
		<style type="text/css">
            .ui-panelgrid td {
                padding-top: 2px;
                padding-bottom: 2px;
                padding-left: 5px;
                padding-right: 0px;
            }
            .ui-panelgrid td {
                border-width: 1px;
            }		
		</style>
		<h:outputScript name="js/dataTable.js"/>
	</h:head>

	<h:body>
		<h:outputStylesheet name="css/default.css"/>
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />	
        <ui:insert name="insertScripts"/>
		<h:form id="mainForm">
			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                   <f:facet name="header">
                       <ui:insert name="insertTitle"/>
                   </f:facet>
			</p:panelGrid>
            <!-- 按钮 -->
            <p:outputPanel styleClass="zwx_toobar_42" style="margin-top:5px;">
                <h:panelGrid columns="3">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                    <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}" process="@this,mainGrid" update="dataTablePanel,exportBtn"/>
                    <p:commandButton value="导出" ajax="false" icon="ui-icon-document" id="exportBtn"  >
                        <p:dataExporter target="dataTable" type="xlsx" fileName="#{mgrbean.exportFileName}"
                                        preProcessor="#{mgrbean.preProcessXLS}" postProcessor="#{mgrbean.postProcessXLS}"/>
                    </p:commandButton>
                </h:panelGrid>
            </p:outputPanel>
        	<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;height:100%;" id="mainGrid">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="年份："/>
						</p:column>
						<p:column style="text-align:left;padding-left:11px;width:200px;">
							<p:selectOneMenu id="searchYear" value="#{mgrbean.searchYear}">
								<f:selectItems value="#{mgrbean.searchYearMap}"/>
								<p:ajax event="change" listener="#{mgrbean.yearChangeAction}" process="@this,@parent,searchTimePanel" update="searchTimePanel"/>
							</p:selectOneMenu>
						</p:column>	
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="地区："/>
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}" zoneCode="#{mgrbean.searchZoneCode}" zoneName="#{mgrbean.searchZoneName}"/>
						</p:column>	
					</p:row>	
					
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="统计规则："/>
						</p:column>
						<p:column style="text-align:left;padding-left:11px;width:200px;">
							<p:selectOneMenu id="searchRule" value="#{mgrbean.searchRule}">
								<f:selectItems value="#{mgrbean.searchRuleMap}"/>
							</p:selectOneMenu>
						</p:column>	
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="疾病分类："/>
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width:220px;">
			            	<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchDiseaseTypeGrid">
						        <p:inputText id="searchDiseaseTypeName" value="#{mgrbean.searchDiseaseTypeName}"  style="width: 180px;" readonly="true"/>
						        <h:inputHidden id="searchDiseaseTypeCode" value="#{mgrbean.searchDiseaseTypeCode}"/>
						        <p:commandLink styleClass="ui-icon ui-icon-search"  id="initTreeLink"  process="@this" style="position: relative;left: -40px;"
						                       oncomplete="PF('DiseaseOverlayPanel').show()" type="button"/>
					        </h:panelGrid>
					        <p:overlayPanel id="diseaseOverlayPanel" for="searchDiseaseTypeName" style="width:280px;" widgetVar="DiseaseOverlayPanel" 
					        	showCloseIcon="true" onHide="diseaseTypeHide();">
					            <p:tree var="node" selectionMode="checkbox" id="diseaseTree"  value="#{mgrbean.searchDiseaseTypeNode}"
					                    style="width: 250px;height: 400px;overflow-y: auto;" selection="#{mgrbean.searchSelectedDiseaseType}">
					                <p:treeNode>
					                    <h:outputText value="#{node.codeName}"/>
					                </p:treeNode>
					            </p:tree>
					        </p:overlayPanel>   
					        <p:remoteCommand name="diseaseTypeHide" process="@this,searchDiseaseTypeGrid,diseaseOverlayPanel"
					        	action="#{mgrbean.diseaseTypeChgAction}" update="searchDiseaseTypeGrid,searchDiseaseGrid,disOverlayPanel"/>						
						</p:column>	
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="疾病名称："/>
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
			            	<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchDiseaseGrid">
						        <p:inputText id="searchDiseaseName" value="#{mgrbean.searchDiseaseName}"  style="width: 180px;" readonly="true"/>
						        <h:inputHidden id="searchDiseaseCode" value="#{mgrbean.searchDiseaseCode}"/>
						        <p:commandLink styleClass="ui-icon ui-icon-search"  id="initDisTreeLink"  process="@this" style="position: relative;left: -40px;"
						                       oncomplete="PF('DisOverlayPanel').show()" type="button"/>
					        </h:panelGrid>
					        <p:overlayPanel id="disOverlayPanel" for="searchDiseaseName" style="width:280px;" widgetVar="DisOverlayPanel" 
					        	showCloseIcon="true" onHide="diseaseHide();">
					            <p:tree var="node" selectionMode="checkbox" id="disTree"  value="#{mgrbean.searchDiseaseNode}"
					                    style="width: 250px;height: 400px;overflow-y: auto;" selection="#{mgrbean.searchSelectedDisease}">
					                <p:treeNode>
					                    <h:outputText value="#{node.diseName}"/>
					                </p:treeNode>
					            </p:tree>
					        </p:overlayPanel> 	
					        <p:remoteCommand name="diseaseHide" process="@this,searchDiseaseGrid,disOverlayPanel"
					        	action="#{mgrbean.diseaseChgAction}" update="searchDiseaseGrid"/>				        
						</p:column>	
				        					
					</p:row>						

					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="报告属地："/>
						</p:column>
						<p:column style="text-align:left;padding-left:3px;width:200px;">
							<zwx:SelectManyMenu id="searchReportAddr" dataMap="#{mgrbean.searchReportAddrMap}" 
								dataValue="#{mgrbean.searchReportAddrValue}" dataLabel="#{mgrbean.searchReportAddrLabel}"/>
						</p:column>	
						<p:column style="text-align:right;padding-right:3px;width:80px;">
							<h:outputText value="病例分型："/>
						</p:column>
						<p:column style="text-align:left;padding-left:3px;" colspan="3">
							<zwx:SelectManyMenu id="searchDiseaseType" dataMap="#{mgrbean.searchDiseaseTypeMap}" 
								dataValue="#{mgrbean.searchDiseaseTypeValue}" dataLabel="#{mgrbean.searchDiseaseTypeLabel}"/>		
						</p:column>	
					</p:row>
					
					<p:row>
						<p:column style="text-align:left;padding-left:3px;height:33px;" colspan="6">
							<p:outputPanel id="searchTimePanel">
								<p:selectOneRadio id="searchTimeType" value="#{mgrbean.searchTimeType}" layout="custom">
									<f:selectItems value="#{mgrbean.searchTimeTypeMap}" />
								</p:selectOneRadio>
								
						   		<h:panelGrid columns="23" cellpadding="0" style="border-color: #ffffff;margin: 0px;padding: 0px;">
						            <p:radioButton id="opt1" for="searchTimeType" itemIndex="0"/>
						   			<h:outputText value="年季月报"/>
									<p:selectOneMenu id="searchNjyb" value="#{mgrbean.searchNjyb}">
										<f:selectItems value="#{mgrbean.searchNjybMap}"/>
									</p:selectOneMenu>
									<p:spacer width="5"/>
									<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
									<p:spacer width="5"/>
						 
						            <p:radioButton id="opt2" for="searchTimeType" itemIndex="1" />
						 			<h:outputText value="旬报"/>
									<p:selectOneMenu id="searchXunMon" value="#{mgrbean.searchXunMon}">
										<f:selectItems value="#{mgrbean.searchXunMonMap}"/>
									</p:selectOneMenu>
									<p:selectOneMenu id="searchXun" value="#{mgrbean.searchXun}">
										<f:selectItems value="#{mgrbean.searchXunMap}"/>
									</p:selectOneMenu>
									<p:spacer width="5"/>
									<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
									<p:spacer width="5"/>
						 
						            <p:radioButton id="opt3" for="searchTimeType" itemIndex="2" />
						 			<h:outputText value="周报"/>
									<p:selectOneMenu id="searchWeek" value="#{mgrbean.searchWeek}">
										<f:selectItems value="#{mgrbean.searchWeekMap}"/>
									</p:selectOneMenu>
									<p:spacer width="5"/>
									<span class="ui-separator" style="display: #{mgrbean.IF_DISPLAY_DAY?'':'none'}"  >
                                        <span class="ui-icon ui-icon-grip-dotted-vertical"   style="display: #{mgrbean.IF_DISPLAY_DAY?'':'none'}" /></span>
									<p:spacer width="5"/>
									
						            <p:radioButton id="opt4" for="searchTimeType" itemIndex="3" rendered="#{mgrbean.IF_DISPLAY_DAY}" />
									<h:outputText value="日报"  rendered="#{mgrbean.IF_DISPLAY_DAY}" />
									<p:calendar id="searchDay" value="#{mgrbean.searchDay}"   rendered="#{mgrbean.IF_DISPLAY_DAY}"
										size="11" navigator="true" yearRange="c-10:c"
	                                	converterMessage="日期格式输入不正确！" 
	                                	pattern="yyyy-MM-dd"  showButtonPanel="true"/>
	                                <p:spacer width="5"/>
						        </h:panelGrid>
					        </p:outputPanel>
						</p:column>
					</p:row>	
				</p:panelGrid>
			</p:fieldset>
			<p:outputPanel id="dataTablePanel">
				<p:scrollPanel mode="native" rendered="#{mgrbean.headSecondRowList.size() > 0}" style="border:0px">
                    <!-- 表格列 -->
                        <p:dataTable var="itm" value="#{mgrbean.dataList}" paginator="no"  emptyMessage="没有您要找的记录！"
                                     id="dataTable" style="width:#{mgrbean.headFirstRowList.size() * 140}px;margin-bottom:5px;">
                            <p:columnGroup type="header">
                                <p:row>
                                    <c:forEach items="#{mgrbean.headFirstRowList}" var="hed">
                                        <p:column rowspan="#{hed.rowSpan}" colspan="#{hed.colSpan}">
                                            <f:facet name="header">
                                                <h:outputText value="#{hed.colName}"/>
                                            </f:facet>
                                        </p:column>
                                    </c:forEach>
                                </p:row>
                                <p:row>
                                    <c:forEach items="#{mgrbean.headSecondRowList}" var="hed">
                                        <p:column rowspan="#{hed.rowSpan}" colspan="#{hed.colSpan}">
                                            <f:facet name="header">
                                                <h:outputText value="#{hed.colName}"/>
                                            </f:facet>
                                        </p:column>
                                    </c:forEach>
                                </p:row>
                            </p:columnGroup>
                            <c:forEach begin="0" end="#{mgrbean.headSecondRowList.size()}" varStatus="inx">
                                <p:column>
                                    <h:outputText value="#{itm[inx.index]}"/>
                                </p:column>
                            </c:forEach>
                        </p:dataTable>
				</p:scrollPanel>
			</p:outputPanel>
		</h:form>
		<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
	</h:body>
</f:view>
</html>