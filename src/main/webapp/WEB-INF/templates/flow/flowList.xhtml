<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">
  <style type="text/css">
	.flowHrefClass{
		font-size: 13px;
		color: blue;
	}
	.flowSeperate {
		color: gray;
		line-height: 0px;
		height: 30px;
	}
  </style>   
  
 <br/>
		<table style="width:98%;">
			<tr>
				<td rowspan="3" style="text-align:right;padding-right:5px;width: 100px;">
					<img src="#{tdFlowType.picPath}" style="width:48px;height:48px;"/>
				</td>
				<td><h:outputText value="#{tdFlowType.typeName}" style="color: black;font-weight: bold;font-family: Arial,'宋体';"/></td>
			</tr>
			<tr>
				<td>
					<ui:repeat var="temVar" value="#{tdFlowType.tdFlowDefList}"  varStatus="inx">
						<p:commandLink action="#{tdFlowInstanceBean.startProcess}"  value="#{temVar.defName}"  styleClass="flowHrefClass" process="@this" ajax="false" >
							<f:setPropertyActionListener target="#{tdFlowInstanceBean.tdFlowDef}" value="#{temVar}"/>
						</p:commandLink>
						<span class="flowSeperate"><h:outputText value="  |  "  rendered="#{inx.index != (tdFlowType.tdFlowDefList.size() -1) }" /></span>
					</ui:repeat>
				</td>
			</tr>
			<tr>
				<td><hr class="flow_separate"/></td>
			</tr>
		   </table>
 <br/>
</ui:composition>
<!--流程定义的列表-->
