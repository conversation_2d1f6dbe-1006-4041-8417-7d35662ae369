<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Pragma" content="no-cache" />
<meta http-equiv="Cache-Control" content="no-cache" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="Access-Control-Allow-Origin" content="*" />

<link rel="stylesheet" href="/resources/css/que/default.css" />
<link rel="stylesheet" href="/resources/css/que/public.css" />
<link rel="stylesheet" href="/resources/css/que/private.css" />
<link rel="stylesheet"
	href="/resources/css/que/newsolid_###backImage###.css" />
<link rel="stylesheet" href="/resources/css/slowque/private.css" />
<link rel="stylesheet" href="/resources/css/slowque/sendDialog.css" />
<link rel="stylesheet" href="/resources/css/que/table.css" />
<title>###title###</title>
</head>

<body>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-3.5.1.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-migrate-1.4.1.js"></script>
	<script type="text/javascript"
		src="/resources/js/que/jquery.json-2.4.js"></script>
	<script type="text/javascript" src="/resources/js/que/calendar.js"></script>
	<script type="text/javascript" src="/resources/js/que/que_utils.js"></script>
	<script type="text/javascript" src="/resources/js/que/que_common.js"></script>
	<script type="text/javascript" src="/resources/js/que/scroll.js"></script>

	<script type="text/javascript"
		src="/resources/js/que/validate_common.js"></script>
	<script type="text/javascript"
		src="/resources/js/###systemType###/questionNare_validate.js"></script>
	<script type="text/javascript"
		src="/resources/js/slowque/slider_extras.js"></script>

	<div id="divNotRun"
		style="height:100px; text-align:center; display:none;"></div>
	<div id="jqContent" class="" style="text-align: left; ">
		<div id="headerCss" style="overflow-x: hidden; overflow-y: hidden; ">
			<div id="ctl00_header"></div>
		</div>
		<div id="mainCss">
			<div id="mainInner">
				<div id="box">
					<!-- 标题 -->
					<div class="survey" style="margin:0px auto;">
						<div class="surveyhead" style="border: 0px;">
							<h1 style="position:relative;">
								<span>###TITLE###</span>
							</h1>
						</div>
						<form method="post" onsubmit="validator(this);return false;"
							id="form">
							<div class="surveycontent">
								<div class="que_userinfo">
									姓名：<span class="que_userinfo_name">张三</span> 性别：<span
										class="que_userinfo_sex">男</span> 身份证：<span
										class="que_userinfo_code">320923195612061234</span>
								</div>

								<div>
									<fieldset class="fieldset" id="fieldset1">
										<legend>
											<span style="font:14px"></span>
										</legend>

										###content###

									</fieldset>
									<div id="divMaxTime" maxTime="30"
										style="text-align: center; width: 80px; position: fixed; top: 105px; border: 1px solid rgb(219, 219, 219); padding: 8px; z-index: 10; left: 132px; background: white;display:none">
										<div id="spanTimeTip"
											style="border-bottom:1px solid #dbdbdb;height:30px; line-height:30px;"></div>
										<div
											style="color: Red;font-size:16px; height:30px; line-height:30px;"
											id="spanMaxTime"></div>
									</div>
								</div>
								<div style="padding-top: 6px;clear:both; padding-bottom:10px;"
									id="submit_div">
									<table id="submit_table" style="margin: 20px auto;">
										<tbody>
											<tr>
												<td><input type="button" onclick="saveForm()"
													class="submitbutton" value="保存"
													onmouseout="this.className='submitbutton';"
													id="save_button" style="padding:0 24px;height:32px;" /> <input
													type="submit" class="submitbutton" value="提交"
													onmouseout="this.className='submitbutton';"
													id="submit_button" style="padding:0 24px;height:32px;" />
													<input type="button" class="submitbutton" value="返回"
													onclick="javascript:window.location.href=$('#fromUrl').val();"
													onmouseout="this.className='submitbutton';"
													id="back_button"
													style="padding:0 24px;height:32px;display: none;" /></td>
												<td align="right" valign="bottom"></td>
											</tr>
										</tbody>
									</table>
									<div style="clear:both;"></div>
								</div>
							</div>


							<div id="fullbg"></div>
							<div id="dialog">
								<p class="close">
									发送处方 <span class="closeRight"><a href="#"
										onclick="closeBg();">关闭</a></span>
								</p>


								<table style="width:100%;">
									<tr style="vertical-align: top;">
										<td style="height:320px;border-right:1px dashed;">
											<div style="width:100%;overflow-y:auto;height:300px;">

												<table class="gridtable">
													<tr>
														<th style="width:40px;text-align:center;">选择</th>
														<th style="width:220px;">处方名称</th>
													</tr>
													<tr>
														<td><input type="checkbox" class="checkCf"
															name="cfCheck" value="1" /></td>
														<td style="text-align: left;">高血压</td>
													</tr>
												</table>
											</div>
										</td>
										<td style="width:480px;">
											<table style="color:black;">
												<tr>
													<td>
														<div
															style="width:100%;height:25px;float:left;font-size:12px;text-align: left;">短信内容预览：</div>
														<div
															style="width:100%;overflow:auto;float:left;height:220px;font-size:12px;text-align: left; word-wrap:break-word;word-break:break-all;"
															id="contentDx"></div>

														<div
															style="width:100%;height:25px;float:left;font-size:12px;text-align: left;">备注：</div>
														<div style="width:100%;float:left;">
															<textarea class="area" name="textarea2" id="textarea2"
																cols="65" onpropertychange="replaceCheckTemp()"
																oninput="replaceCheckTemp()" rows="3"></textarea>
														</div>
													</td>

												</tr>
												<tr>
													<td style="text-align:center;"><input type="checkbox"
														id="ifSend" value="1" checked="checked" />发送处方
														&nbsp;&nbsp;<input type="button" class="dialogbutton"
														value="发送" onclick="sendMsg();" /> <input type="button"
														class="dialogbutton" value="关闭" style="margin-left:10px;"
														onclick="closeBg();" /></td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</form>
					</div>
				</div>
				<div style="clear: both;"></div>
			</div>
			<div id="footercss">
				<div id="footerLeft"></div>
				<div id="footerCenter"></div>
				<div id="footerRight"></div>

				<input type="hidden" id="urlParams" /> <input type="hidden"
					id="fromUrl" /><input type="hidden" id="ansQueId" /><input
					type="hidden" id="userId" /><input type="hidden" id="mainId" /><input
					type="hidden" id="zoneId" /><input type="hidden" id="queId" /> <input
					type="hidden" id="startTime" /><input type="hidden" id="nowTime" />
			</div>
			<div style="clear: both; height: 10px;"></div>
			<div style="height: 20px;">&nbsp;</div>
		</div>
		<div style="clear:both;"></div>
	</div>
	<div id="msgDialog">
		<p class="close">
			<span class="closeRight"><a href="#" onclick="closeBg();">关闭</a></span>
		</p>
		<table style="width:100%;">
			<tr>
				<td>
					<div
						style="width:100%;height:25px;padding-left:8px;padding-right:8px;font-size:12px;text-align: left;"
						id="message">提示信息：</div>
					<div style="width:100%;height:50px;">
				</td>
			</tr>
			<tr>
				<td style="text-align:center;">
					<!--<input type="button" class="dialogbutton" value="提交" onclick="sendMsg();" />-->
				</td>
			</tr>
		</table>
	</div>
	<div id="infoDialog">
		<form method="post" onsubmit="begin();return false;" id="beginForm">
			<table
				style="color:black;text-align: center;width: 100%;padding-top: 10px;">
				<tr>
					<td>
						<div
							style="width:25%;height:25px;float:left;font-size:12px;text-align: right;">姓名：</div>
						<div style="width:75%;float:right;text-align: left;">
							<input type="text" style="width:60px;" id="psnName"
								name="psnName" />
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div
							style="width:25%;height:15px;float:left;font-size:12px;text-align: right;"></div>
						<div style="width:75%;float:right;text-align: left;">
							<div style="color: red;" id="psnNameError"></div>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div
							style="width:25%;height:25px;float:left;font-size:12px;text-align: right;">身份证号：</div>
						<div style="width:75%;float:right;text-align: left;">
							<input type="text" style="width:180px;" id="psnIdc" name="psnIdc" />
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div
							style="width:25%;height:15px;float:left;font-size:12px;text-align: right;"></div>
						<div style="width:75%;float:right;text-align: left;">
							<div style="color: red;" id="psnIdcError"></div>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div
							style="width:25%;height:25px;float:left;font-size:12px;text-align: right;">单位：</div>
						<div style="width:75%;float:right;text-align: left;">
							<input type="text" style="width:210px;" id="psnUnit"
								name="psnUnit" />
						</div>
					</td>
				</tr>
				<tr>
					<td style="height:15px;"></td>
				</tr>
				<tr>
					<td>
						<div
							style="width:25%;height:25px;float:left;font-size:12px;text-align: right;">性别：</div>
						<div style="width:75%;float:right;text-align: left;">
							<input type="radio" value="1" name="psnSex" checked="checked" />男<input
								type="radio" value="2" name="psnSex" />女
						</div>
					</td>
				</tr>
				<tr>
					<td style="height:15px;"><div style="text-align: center;"
							id="infoMess"></div></td>
				</tr>
				<tr>
					<td style="text-align:center;"><input type="submit"
						class="dialogbutton" value="开始答题" /></td>
				</tr>
			</table>
		</form>
	</div>
</body>
</html>
