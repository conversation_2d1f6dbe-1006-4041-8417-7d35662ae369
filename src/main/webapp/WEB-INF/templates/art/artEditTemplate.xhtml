<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

    <ui:insert name="insertEditScripts"/>
	<h:form id="editForm">
		<p:panelGrid style="width:99.8%;height:100%;margin-bottom:5px;" id="editGrid">
            <f:facet name="header">
                <ui:insert name="insertEditTitle"/>
            </f:facet>
		</p:panelGrid>
		
		<p:outputPanel styleClass="zwx_toobar_42">
			<h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
				<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" rendered="#{mgrbean.showSave}"
                                 action="#{mgrbean.saveAction}" process="@form" update=":tabView"/>
                <p:commandButton value="提交" icon="ui-icon-circle-triangle-e" id="submitBtn" rendered="#{mgrbean.showSubmit}"
                                 action="#{mgrbean.submitAction}" process="@form" update=":tabView">
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="撤销" icon="ui-icon-circle-triangle-w" id="cancelBtn" rendered="#{mgrbean.showCancel}"
                                 action="#{mgrbean.cancelAction}" process="@form" update=":tabView">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <!-- 其它按钮 -->
                <ui:insert name="insertOtherBtns"/>
                <p:commandButton value="返回" icon="ui-icon-close" id="backBtn" rendered="#{mgrbean.showExit}"
                                 action="#{mgrbean.backAction}" update=":tabView" process="@this"/>
			</h:panelGrid>
		</p:outputPanel>
		
				
        <!-- 主表信息 -->
        <ui:insert name="insertEditContent"/>
        
        <p:outputPanel id="artListPanel">
        	<ui:include src="#{artListPage}"></ui:include>
        </p:outputPanel>

		<ui:insert name="insertOtherContents"/>

	</h:form>
</ui:composition>
<!--出入库的编辑页面的和mainTemplate是一组，是mainTemplate的编辑、查看页面的模板-->
