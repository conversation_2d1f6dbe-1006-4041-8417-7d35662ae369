<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent" xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
	<h:head>
	</h:head>
	<h:body>
		<h:outputStylesheet library="css" name="default.css" />
		<h:outputStylesheet library="css" name="ui-tabs.css" />
		<script type="text/javascript" src="/resources/component/fusioncharts/FusionCharts.js"></script>
		<ui:insert name="insertScripts" />
		<h:form id="mainForm">
			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
				<f:facet name="header">
					<p:row>
						<p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
							<ui:insert name="insertTitle" />
						</p:column>
					</p:row>
				</f:facet>
			</p:panelGrid>

			<p:outputPanel id="buttonsPanel">
				<p:outputPanel styleClass="zwx_toobar_42">
					<h:panelGrid columns="2" style="border-color:transparent;padding:0px;">
						<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:row>
							<p:column colspan="6" style="text-align:left;padding-left:5px;">
								<p:commandButton value="分析" icon="ui-icon-search" id="searchBtn" action="#{mgrBean.searchAction}" update="hiddenPG,dataTableGro" process="@this,mainGrid" />
							</p:column>
						</p:row>
					</h:panelGrid>
				</p:outputPanel>
			</p:outputPanel>

			<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
				<p:panelGrid style="width:100%;height:100%;" id="mainGrid">
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:125px;height: 25px;">
							<h:outputText value="年份：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;height: 25px;width: 220px;">
							<div style="display: table-row;">
								<div style="display: table-cell;">
									<p:selectOneMenu value="#{mgrBean.searchYear}">
										<f:selectItems value="#{mgrBean.yearList}" />
										<p:ajax event="change" process="@this" oncomplete="document.getElementById('mainForm:searchBtn').click();" />
									</p:selectOneMenu>
								</div>
								<div style="display: table-cell;vertical-align: middle;">年</div>
							</div>
						</p:column>
						<p:column style="text-align:right;padding-right:3px;width:120px;">
							<h:outputText value="地区：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<zwx:ZoneSingleNewComp zoneList="#{mgrBean.zoneList}" zoneCodeNew="#{mgrBean.searchZoneCode}" zoneType="#{mgrBean.searchZoneType}" zoneName="#{mgrBean.searchZoneName}" />
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:fieldset>

			<h:panelGroup id="hiddenPG">
				<ui:insert name="hiddenSpace" />
			</h:panelGroup>

			<ui:insert name="insertCharts" />

			<h:panelGroup id="dataTableGro">
				<ui:insert name="insertDataTable" />
				<br/>
			</h:panelGroup>

			<ui:include src="../system/confirm.xhtml"></ui:include>
			<ui:include src="../system/focus.xhtml"></ui:include>
		</h:form>
		<ui:include src="../system/growl.xhtml"></ui:include>
		<ui:include src="../system/ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>