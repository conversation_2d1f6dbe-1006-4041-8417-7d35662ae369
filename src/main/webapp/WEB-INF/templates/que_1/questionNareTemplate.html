<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Pragma" content="no-cache" />
<meta http-equiv="Cache-Control" content="no-cache" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="Access-Control-Allow-Origin" content="*" />

<link rel="stylesheet" href="/resources/css/que/default.css" />
<link rel="stylesheet" href="/resources/css/que/public.css" />
<link rel="stylesheet" href="/resources/css/que/private.css" />
<link rel="stylesheet" href="/resources/css/que/table.css" />
<link rel="stylesheet"
	href="/resources/css/que/newsolid_###backImage###.css" />
<link rel="stylesheet" href="/resources/css/slowque/sendDialog.css" />
<title>###title###</title>
</head>

<body>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-3.7.1.min.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-migrate-1.4.1.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-migrate-3.4.0.min.js" />
	<script type="text/javascript"
		src="/resources/js/que/jquery.json-2.4.js"></script>
	<script type="text/javascript" src="/resources/js/que/calendar.js"></script>
	<script type="text/javascript" src="/resources/js/que/que_utils.js"></script>
	<script type="text/javascript" src="/resources/js/que/que_common.js"></script>
	<script type="text/javascript" src="/resources/js/que/scroll.js"></script>
    <script type="text/javascript"
            src="/resources/js/que/jquery-resize.js"></script>
	<script type="text/javascript"
		src="/resources/js/que/validate_common.js"></script>
	<script type="text/javascript"
		src="/resources/js/que/questionNare_validate.js"></script>
	<script type="text/javascript"
		src="/resources/js/slowque/slider_extras.js"></script>
	<script type="text/javascript"
			src="/resources/js/que-aes.js"></script>
	<script type="text/javascript" >

		###javaScript###

	</script>
	<div id="divNotRun"
		style="height:100px; text-align:center; display:none;"></div>
	<div id="jqContent" class="" style="text-align: left; ">
		<div id="headerCss" style="overflow-x: hidden; overflow-y: hidden; ">
			<div id="ctl00_header"></div>
		</div>
		<div id="mainCss" style="width: ###mainCssWidth###px;">
			<div id="mainInner">
				<div id="box">
					<!-- 标题 -->
					<div class="survey" style="margin:0px auto;width: ###subjectShowWidth###px; ">
						<div class="surveyhead" style="border: 0px;">
							<h1 style="position:relative;">
								<span>###TITLE###</span>
							</h1>
						</div>
						<form method="post" onsubmit="validator(this,null);return false;"
							id="form">
							<div class="surveycontent" style="padding-bottom: 100px">
								<div>
									<fieldset class="fieldset" id="fieldset1">
										<legend>
											<span style="font:14px"></span>
										</legend>
										###content###
									</fieldset>
									<div id="divMaxTime" maxTime="30"
										style="text-align: center; width: 80px; position: fixed; top: 105px; border: 1px solid rgb(219, 219, 219); padding: 8px; z-index: 10; left: 132px; background: white;display:none">
										<div id="spanTimeTip"
											style="border-bottom:1px solid #dbdbdb;height:30px; line-height:30px;"></div>
										<div
											style="color: Red;font-size:16px; height:30px; line-height:30px;"
											id="spanMaxTime"></div>
									</div>
								</div>
								<div style="padding-top: 6px;clear:both; position: fixed;background: #efefef;width: ###mainCssWidth###px;bottom: 0; margin-left: -70px;display: none"
									id="submit_div">
									<table id="submit_table" style="margin: 15px auto;">
										<tbody>
											<tr>
												<td><input type="button" class="submitbutton"
													value="返回" onmouseout="this.className='submitbutton';"
													onclick="javascript:window.back();" id="return_button"
													style="padding:0 24px;height:32px;width:120px;display: none;"> </input></td>

												<td><input
														type="submit" class="submitbutton" id="save"
														onclick="setOpType(0)" value="保存"
														onmouseout="this.className='submitbutton';"
														style="padding:0 24px;height:32px;width:120px;display: none;"></input>
													<input type="button" class="submitbutton"
													value="审核通过" onmouseout="this.className='submitbutton';"
													onclick="javascript:window.showMsgDialog();"
													id="submit_button"
													style="padding:0 24px;height:32px;width:120px;display: none;"></input> </td>

												<td align="right" valign="bottom"><input type="button"
													class="submitbutton" value="退回"
													onclick="javascript:window.showBg();"
													onmouseout="this.className='submitbutton';"
													id="back_button"
													style="padding:0 24px;height:32px; width:120px;display: none;" /> <input
													type="submit" class="submitbutton" id="ctlNext"
													onmouseout="this.className='submitbutton';" value="提交"
													onclick="setOpType(1)"
													style="padding:0 24px;height:32px;width:120px;display: none;"></input></td>
											</tr>

										</tbody>
									</table>
									<div style="clear:both;"></div>
								</div>
							</div>


							<div id="fullbg"></div>
							<div id="dialog"
								style="width:510px;height:280px;margin-left:-280px">
								<p class="close">
									<span>退回原因</span>
								</p>
								<table style="width:100%;">
									<tr>
										<td style="height:200px;"><textarea rows="12" cols="80"
												name="textarea" id="queTextArea" style="resize:none;width: 99%;"
												maxlength="200"></textarea></td>
									</tr>
									<tr>
										<td style="text-align:center;">&nbsp;&nbsp;<input
											type="button" class="dialogbutton" id="dialogbutton"
											value="确认" onclick="backAction();" /> <input type="button"
											id="closeButton" class="dialogbutton" value="关闭"
											style="margin-left:10px;" onclick="closeBg();" /></td>
									</tr>
								</table>
							</div>

							<div id="msgDialog" style="width:250px;height:130px">
								<p class="close">
									<span>消息确认框</span>
								</p>
								<table style="width:100%;">
									<tr>
										<td
											style="text-align: center;font-size: medium;padding-top: 16px">
											确认要审核通过吗？</td>
									</tr>
									<tr>
										<td style="text-align:center;padding-top:20px">
											&nbsp;&nbsp;<input type="button" class="dialogbutton"
											value="确认" onclick="saveSubMitAction();" /><input
											type="button" class="dialogbutton" value="关闭"
											style="margin-left:10px;" onclick="closeBg();" />
										</td>
									</tr>
								</table>
							</div>
						</form>
					</div>
				</div>
				<div style="clear: both;"></div>
			</div>
			<div>
				<input type="hidden" id="urlParams" /> <input type="hidden"
					id="fromUrl" /><input type="hidden" id="ansQueId" /><input
					type="hidden" id="userId" /><input type="hidden" id="mainId" /><input
					type="hidden" id="zoneId" /><input type="hidden" id="queId" /> <input
					type="hidden" id="startTime" /><input type="hidden" id="nowTime" />
				<input type="hidden" id="opType" />
				<input type="hidden" id="middleCompareVal" />
			</div>
		</div>
	</div>
</body>
</html>
