<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="viewport"
	content="width=device-width, minimum-scale=1, maximum-scale=1" />
<meta name="format-detection" content="telephone=no" />
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<title>###title###</title>
<link rel="stylesheet" href="/resources/css/queM/jqmobo.css" />
<link rel="stylesheet" href="/resources/css/queM/rangeslider.css" />
<link rel="stylesheet" href="/resources/css/que/table.css" />
<link
	href="/resources/component/mobiscroll/css/mobiscroll.animation.css"
	rel="stylesheet" type="text/css" />
<link href="/resources/component/mobiscroll/css/mobiscroll.icons.css"
	rel="stylesheet" type="text/css" />
<link href="/resources/component/mobiscroll/css/mobiscroll.frame.css"
	rel="stylesheet" type="text/css" />
<link href="/resources/component/mobiscroll/css/mobiscroll.scroller.css"
	rel="stylesheet" type="text/css" />
</head>
<body>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-3.5.1.js"></script>
	<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery-migrate-1.4.1.js"></script>
	<script type="text/javascript"
		src="/resources/js/queM/jquery.json-2.4.js"></script>
	<script type="text/javascript" src="/resources/js/queM/que_utils.js"></script>
	<script type="text/javascript" src="/resources/js/queM/hintinfo.js"></script>
	<script type="text/javascript" src="/resources/js/queM/jqmobo2.js"></script>
	<script type="text/javascript"
		src="/resources/js/queM/questionNareMobile_validate.js"></script>
	<!--mobiscroll-->
	<script src="/resources/component/mobiscroll/js/mobiscroll.core.js"></script>
	<script src="/resources/component/mobiscroll/js/mobiscroll.frame.js"></script>
	<script src="/resources/component/mobiscroll/js/mobiscroll.scroller.js"></script>
	<script
		src="/resources/component/mobiscroll/js/mobiscroll.util.datetime.js"></script>
	<script
		src="/resources/component/mobiscroll/js/mobiscroll.datetimebase.js"></script>
	<script src="/resources/component/mobiscroll/js/mobiscroll.datetime.js"></script>
	<script
		src="/resources/component/mobiscroll/js/i18n/mobiscroll.i18n.zh.js"></script>

	<!--mobiscroll-->
	<script>
		var hasSlider = 1;
	</script>
	<script src="/resources/js/queM/rangeslider.js?v=1"
		type="text/javascript"></script>
	<form id="form" method="post">
		<div id="toptitle">
			<h1 class="htitle">###TITLE###</h1>
		</div>
		<div id="divContent">
			<div id="divQuestion" class="fieldset">###content###</div>
			<div class="footer">
				<div class="ValError"></div>
				<div id="divSubmit" style="padding: 10px;">
					<input type="hidden" id="urlParams" /> <input type="hidden"
						id="sfMtdId" name="sfMtdId" value="3" />
					<div
						style=" float: left;width: 50%;position: relative;min-height: 1px;padding-left: 15px;padding-right: 15px;">
						<a id="save" href="javascript:; " class="button blue "> 保存</a>
					</div>
					<div
						style=" float: left;width: 50%;position: relative;min-height: 1px;padding-left: 15px;padding-right: 15px; ">
						<a id="ctlNext" href="javascript:;" class="button blue"> 提交</a>
					</div>
					<div style="margin:0px 0 10px; padding-top:10px;">
						<div style="clear: both; "></div>
					</div>
				</div>
			</div>
		</div>
	</form>
	<script type="text/javascript">
		var activityId = 5711036;
		var isPub = 1;
		var sojumpParm = '';
		var lastTopic = 0;
		var Password = "";
		var guid = "";
		var udsid = '';
		var langVer = 0;
		var cProvince = "";
		var cCity = "";
		var cIp = "";
		var divTip = document.getElementById("divTip");
		var displayPrevPage = "none";
		var inviteid = '';
		var access_token = "";
		var openid = "";
		var wxthird = 0;
		var hashb = 0;
		var sjUser = '';
		var sourceurl = '';
		var jiFenBao = 0;
		var cAlipayAccount = "";
		var isRunning = 1;
		var SJBack = '';
		var jiFen = "0";
		var ItemDicData = "";
		var rndnum = "**********";
		var totalPage = 1;
		var cepingCandidate = "";
		var cpid = "";
		var submitWithGet = 1;
		var needAvoidCrack = 0;

		var needAvoidCrack = 0;
		var tdCode = "tdCode";
		var imgCode = $("#imgCode")[0];
		var submit_text = $("#yucinput")[0];
		var tCode = $("#" + tdCode)[0];

		$('input[data-role="datebox"]').mobiscroll().date({
			theme : 'mobiscroll',
			mode : 'scroller',
			display : 'modal',
			showLabel : true,
			lang : 'zh',
			dateOrder : 'ddmmyy',
			dateFormat : 'yy-mm-dd',
			rtl : true
		});
	</script>

	<div class="dw-hidden" role="alert"></div>
</body>
</html>