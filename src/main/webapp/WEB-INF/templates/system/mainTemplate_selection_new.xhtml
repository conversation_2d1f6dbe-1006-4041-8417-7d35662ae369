<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<!--showOutsideMainContents：用于查询页面，是否始终显示insertOutsideMainContents区域，需要设置参数计算页面表格高度偏移量（类型：Boolean；单位px）-->
<!--<ui:param name="showOutsideMainContents" value="true"/>-->
<!--tableOffset：用于查询页面，由于查询条件高度不固定，需要设置参数计算页面表格高度偏移量（类型：Integer；单位：px）-->
<!--<ui:param name="tableOffset" value=""/>-->
<f:view contentType="text/html">
    <h:head>
        <script type="text/javascript">
            function datatableOffClick(){
                $(document).off("click.datatable","#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
    </h:head>
    <h:body onload="datatableOffClick();">
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <style type="text/css">
            body, html {
                height: 100%;
                width: 100%;
                margin: 0 0 0 0;
                overflow: hidden hidden;
            }

            #tabView {
                overflow: auto;
                background: #ffffff;
                border: unset !important;
                border-radius: 2px;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
                margin: 0 10px 5px 10px;
                flex: 1;
            }

            #tabView {
                overflow: #{mgrbean.activeTab eq 0 ? 'hidden' : 'auto'};
            }
        </style>
        <p:outputPanel id="page_view" style="display: flex;flex-direction: column;height: 100vh;width: 100%;">
            <ui:insert name="insertScripts"/>
            <p:outputPanel rendered="#{mgrbean.activeTab eq 0 or showOutsideMainContents}">
                <ui:insert name="insertOutsideMainContents"/>
            </p:outputPanel>
            <p:tabView id="tabView" dynamic="true" cache="true" activeIndex="#{mgrbean.activeTab}"
                       style="border:1px; padding:0;">
                <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                    <h:form id="mainForm">
                        <style type="text/css">
                            #tabView .ui-tabs-panels, #tabView .ui-tabs-panels .ui-tabs-panel, #tabView .ui-tabs-panels .ui-tabs-panel form {
                                height: 100%;
                            }

                            #tabView .ui-tabs-panels .ui-tabs-panel form, .data_panel, #tabView\:mainForm\:dataTable {
                                display: flex;
                                flex-direction: column;
                            }

                            .data_panel, #tabView\:mainForm\:dataTable {
                                flex: 1;
                                height: 0;
                            }

                            .main_table .ui-datatable-scrollable-body {
                                max-height: calc(100% - 64px);
                                margin-right: 0 !important;
                                overflow-y: scroll !important;
                                overflow-x: auto !important;
                            }
                        </style>
                        <div style="display: flex;flex-direction: column;">
                            <ui:insert name="insertTitle"/>
                            <div style="padding: 12px 0 6px 12px;border-top: 1px solid #d5d9e0;display: flex;gap: 5px;">
                                <ui:insert name="insertButtons"/>
                            </div>
                            <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
                                        style="margin-top: 5px;margin-bottom: 5px;" rendered="#{condition==null}">
                                <p:panelGrid style="width:100%;height:100%;" id="mainGridNew" styleClass="search-table">
                                    <ui:insert name="insertSearchConditons"/>
                                </p:panelGrid>
                            </p:fieldset>
                            <ui:insert name="insertDownMainContents"/>
                        </div>
                        <div class="data_panel">
                            <script type="text/javascript">
                                //<![CDATA[
                                function tableBodyScrollTop() {
                                    jQuery("#tabView\\:mainForm\\:dataTable > div.ui-datatable-scrollable-body").scrollTop(0);
                                }

                                //]]>
                            </script>
                            <p:dataTable var="itm" value="#{mgrbean.dataModel}" paginator="true"
                                         rows="#{mgrbean.pageSize}"
                                         paginatorPosition="bottom" rowIndexVar="R"
                                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                         rowsPerPageTemplate="#{mgrbean.perPageSize}" id="dataTable" lazy="true"
                                         emptyMessage="没有您要找的记录！" scrollable="true" scrollHeight=""
                                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                         styleClass="main_table" selection="#{mgrbean.selectEntitys}" rowKey="#{itm}"
                                         rendered="#{dataTable==null}">
                                <p:ajax event="page" process="@this" oncomplete="tableBodyScrollTop();"/>
                                <ui:insert name="insertDataTable"/>
                            </p:dataTable>
                        </div>
                    </h:form>
                </p:tab>
                <p:tab id="edit" title="edit" titleStyle="display:none;">
                    <ui:include src="#{editPage}"/>
                </p:tab>
                <p:tab id="view" title="view" titleStyle="display:none;">
                    <ui:include src="#{viewPage}"/>
                </p:tab>
                <p:tab id="other" title="other" titleStyle="display:none;">
                    <ui:include src="#{otherPage}"/>
                </p:tab>
                <p:tab id="otherView" title="otherView" titleStyle="display:none;">
                    <ui:include src="#{otherViewPage}"/>
                </p:tab>
                <p:tab id="edit2" title="edit2" titleStyle="display:none;">
                    <ui:include src="#{edit2Page}"/>
                </p:tab>
                <p:tab id="edit3" title="edit3" titleStyle="display:none;">
                    <ui:include src="#{edit3Page}"/>
                </p:tab>
                <p:tab id="edit4" title="edit3" titleStyle="display:none;">
                    <ui:include src="#{edit4Page}"/>
                </p:tab>
            </p:tabView>
        </p:outputPanel>
        <ui:insert name="insertPoll"/>
        <ui:include src="confirm.xhtml"/>
        <ui:include src="focus.xhtml"/>
        <ui:include src="growl.xhtml"/>
        <ui:include src="ajaxStatus.xhtml"/>
        <ui:include src="ajaxLoading.xhtml"/>
        <!--解决页面跳转tooltips不消失问题-->
        <ui:include src="/WEB-INF/templates/system/hideTooltips.xhtml"/>
    </h:body>
</f:view>
</html>
        <!-- 带转向、真分页的模板 -->