<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

		<p:dialog widgetVar="StatusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
			<p:graphicImage url="/resources/images/main/loading2.gif"  />
		</p:dialog>	 
		
	<script type="text/javascript">
	//<![CDATA[
	function zwx_loading_start() {
		PF('StatusDialog').show();
	}	           
	function zwx_loading_stop() {
		PF('StatusDialog').hide();
	}
	function showStatus() {
		PF('StatusDialog').show();
	}
	function hideStatus() {
		PF('StatusDialog').hide();
	}
	//]]>           
	</script>
		
</ui:composition>
<!--等待框的模板-->
