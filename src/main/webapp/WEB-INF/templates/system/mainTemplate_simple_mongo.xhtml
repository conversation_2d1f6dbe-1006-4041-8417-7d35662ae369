<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <ui:insert name="insertScripts"/>
        <p:tabView id="tabView" dynamic="true" cache="true" activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <ui:insert name="insertTitle"/>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <ui:insert name="insertButtons"></ui:insert>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{condition==null}">
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <ui:insert name="insertSearchConditons"/>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:dataTable var="itm" value="#{mgrbean.dataModel}" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                 rowsPerPageTemplate="#{mgrbean.perPageSize}" id="dataTable" lazy="true" emptyMessage="没有您要找的记录！"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  rendered="#{dataTable==null}">
                        <ui:insert name="insertDataTable"></ui:insert>
                    </p:dataTable>
                    <ui:insert name="insertOtherMainContents"></ui:insert>

                </h:form>
            </p:tab>
            <p:tab id="edit" title="edit" titleStyle="display:none;">
                <ui:include src="#{editPage}"></ui:include>
            </p:tab>
            <p:tab id="view" title="view" titleStyle="display:none;">
                <ui:include src="#{viewPage}"></ui:include>
            </p:tab>
            <p:tab id="other" title="other" titleStyle="display:none;">
                <ui:include src="#{otherPage}"></ui:include>
            </p:tab>
            <p:tab id="otherView" title="otherView" titleStyle="display:none;">
                <ui:include src="#{otherViewPage}"></ui:include>
            </p:tab>
        </p:tabView>
        <ui:insert name="insertPoll"></ui:insert>
        <ui:include src="confirm.xhtml"></ui:include>
        <ui:include src="focus.xhtml"></ui:include>
        <ui:include src="growl.xhtml"></ui:include>
        <ui:include src="ajaxStatus.xhtml"></ui:include>
    </h:body>
</f:view>
</html>