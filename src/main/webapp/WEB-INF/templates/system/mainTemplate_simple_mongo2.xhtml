<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
    </h:head>

    <h:body>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <ui:insert name="insertScripts"/>
        <p:tabView id="tabView" dynamic="true" cache="true" activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0px;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <ui:insert name="insertTitle"/>
                        </f:facet>
                    </p:panelGrid>
                    <p:outputPanel id="buttonsPanel">
                        <ui:insert name="insertButtons"></ui:insert>
                    </p:outputPanel>
                    <p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{condition==null}">
                        <p:panelGrid style="width:100%;height:100%;" id="mainGrid">
                            <ui:insert name="insertSearchConditons"/>
                        </p:panelGrid>
                    </p:fieldset>
                    <p:remoteCommand name="previousPageAction" action="#{mgrbean.previousPageAction}" process="@this" update="dataTable"/>
                    <p:remoteCommand name="nextPageAction" action="#{mgrbean.nextPageAction}" process="@this" update="dataTable"/>
                    <p:dataTable var="itm" value="#{mgrbean.mongoPage.records}" paginator="false" rows="#{mgrbean.mongoPage.size}"  rowIndexVar="R"
                                 rowsPerPageTemplate="#{mgrbean.mongoPage.size}" id="dataTable" lazy="true" emptyMessage="没有您要找的记录！"
                                  rendered="#{dataTable==null}" selection="#{mgrbean.selectEntitys}" rowKey="#{itm.rid}">

                        <p:column selectionMode="multiple" style="width:16px;text-align:center"/>
                        <ui:insert name="insertDataTable"></ui:insert>
                        <f:facet name="footer">
                            <p:outputPanel styleClass="ui-paginator ui-paginator-bottom ui-corner-bottom">
                                <!--分页信息-->
                                <p:outputLabel id="mongoPageInfo">
                                    <p:outputLabel  rendered="#{mgrbean.mongoPage.showInfo}"  styleClass="ui-paginator-current" value="查询到#{mgrbean.mongoPage.total}条记录，共#{mgrbean.mongoPage.getPages()}页"/>
                                    <p:commandLink value="点击显示总记录数" style="color:blue" styleClass="ui-paginator-current"
                                                   actionListener="#{mgrbean.showTotalAction}" process="@this" update="mongoPageInfo"
                                                   rendered="#{!mgrbean.mongoPage.showInfo}"
                                    />
                                </p:outputLabel>
                                <p:spacer width="15"/>
                                <!--上一页 下一页-->
                                <p:outputLabel rendered="#{!mgrbean.mongoPage.hasPrevious()}" styleClass="ui-paginator-prev ui-state-default ui-corner-all ui-state-disabled"><span class="ui-icon ui-icon-seek-prev">p</span></p:outputLabel>
                                <p:outputLabel rendered="#{mgrbean.mongoPage.hasPrevious()}" onclick="previousPageAction()" styleClass="ui-paginator-prev ui-state-default ui-corner-all "><span class="ui-icon ui-icon-seek-prev">p</span></p:outputLabel>
                                <p:outputLabel rendered="#{!mgrbean.mongoPage.hasNext()}" styleClass="ui-paginator-next ui-state-default ui-corner-all ui-state-disabled"><span class="ui-icon ui-icon-seek-next">p</span></p:outputLabel>
                                <p:outputLabel rendered="#{mgrbean.mongoPage.hasNext()}" onclick="nextPageAction()" styleClass="ui-paginator-next ui-state-default ui-corner-all "><span class="ui-icon ui-icon-seek-next">p</span></p:outputLabel>
                            </p:outputPanel>
                        </f:facet>
                    </p:dataTable>
                    <ui:insert name="insertOtherMainContents"></ui:insert>


                </h:form>
            </p:tab>
            <p:tab id="edit" title="edit" titleStyle="display:none;">
                <ui:include src="#{editPage}"></ui:include>
            </p:tab>
            <p:tab id="view" title="view" titleStyle="display:none;">
                <ui:include src="#{viewPage}"></ui:include>
            </p:tab>
            <p:tab id="other" title="other" titleStyle="display:none;">
                <ui:include src="#{otherPage}"></ui:include>
            </p:tab>
            <p:tab id="otherView" title="otherView" titleStyle="display:none;">
                <ui:include src="#{otherViewPage}"></ui:include>
            </p:tab>
        </p:tabView>
        <ui:insert name="insertPoll"></ui:insert>
        <ui:include src="confirm.xhtml"></ui:include>
        <ui:include src="focus.xhtml"></ui:include>
        <ui:include src="growl.xhtml"></ui:include>
        <ui:include src="ajaxStatus.xhtml"></ui:include>
        <ui:include src="ajaxLoading.xhtml"></ui:include>
    </h:body>
</f:view>
</html>