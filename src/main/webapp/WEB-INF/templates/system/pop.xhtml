<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">
	<script type="text/javascript">
		//<![CDATA[
		function zwx_grobal_showInfo(data) {
			if (typeof (data) != "undefined" && data != '') {
				var msgBean;
				try {
					msgBean = eval('(' + data + ')');
				} catch (e) {
					console.log("websocket json解析出错");
				}
				if (typeof (msgBean) != "undefined" && msgBean.msgType != null) {
					if (msgBean.msgType == "1") {
						jQuery('.globalInfoStyle').html(msgBean.globalInfo);
						jQuery('.globalInfoStyle').html(
								jQuery('.globalInfoStyle').text());
						PF('GlobalInfoDialog').show();
					}
					try {
						if (msgBean.msgType == "1" || msgBean.msgType == "2") {
							var iframes = jQuery('iframe');
							if (typeof (iframes) != "undefined"
									&& iframes != null) {
								for (var i = 0; i < iframes.length; i++) {
									if (iframes[i].src.indexOf("portalCenter") > 0) {
										var outIframe = document
												.getElementById(iframes[i].id);
										var iframeList = jQuery(
												outIframe.contentWindow.document)
												.find('iframe');
										if (typeof (iframeList) != "undefined"
												&& iframeList != null) {
											for (var j = 0; j < iframeList.length; j++) {
												if (iframeList[j].name == "need_socket_update") {
													iframeList[j].contentWindow
															.zwx_socket_update();
												}
											}
										}
									}
								}
							}
						}
					} catch (e) {
						console.log("待办任务刷新失败");
					}
				}
			}
			showMsg();
		}
		//]]>
	</script>
	<p:remoteCommand name="showMsg" action="#{headBean.showMsgAction}"
		update="dialogDeskForm:newImg" />
	<p:socket onMessage="zwx_grobal_showInfo"
		channel="/MsgChanel#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.rid}"
		/>
	<!--  <p:socket onMessage="updateMsgInfo" channel="/updateMsgInfo" /> -->
	<p:dialog id="globalInfoDialog" widgetVar="GlobalInfoDialog"
		resizable="false" showEffect="fade" width="200" height="80"
		modal="false" position="right bottom" draggable="false">
		<f:facet name="header">
			<h:panelGroup>
				<h:graphicImage name="/images/message.png" />
				<h:outputText value="消息" style="margin-left:2px;" />
			</h:panelGroup>
		</f:facet>
		<h:panelGrid columns="2" style="margin-top:10px;margin-left:10px;">
			<h:graphicImage name="/images/icon_info.png" />
			<h:outputText styleClass="globalInfoStyle" escape="true"
				style="margin-left:5px;" />
		</h:panelGrid>
	</p:dialog>
</ui:composition>
<!--消息弹出框-->
