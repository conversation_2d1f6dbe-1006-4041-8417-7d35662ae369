<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

	<EMBED id="frpt" type="application/x-chiscdc-FasterReport-plugin" width="0" height="0">
	</EMBED>
	<p:remoteCommand  name="updateFastReport" process="@this" action="#{printBackingBean.fastReportBean.updateFastReport}"/>
    <p:remoteCommand  name="returnSignResult" process="@this" action="#{printBackingBean.returnSignResult}"/>
	<p:remoteCommand  name="deleteTempPDF" process="@this" action="#{printBackingBean.fastReportBean.deleteTempPDF}"/>
    <p:outputPanel id="hiddenPanel">
    	<input type="hidden" id="rptCod" value="#{printBackingBean.fastReportBean.tsRpt.rptCod}"/>
    	<input type="hidden" id="rptver" value="#{printBackingBean.fastReportBean.tsRpt.rptver}"/>
    	<input type="hidden" id="rptnam" value="#{printBackingBean.fastReportBean.tsRpt.rptnam}"/>
    	<input type="hidden" id="rpttpl" value="#{printBackingBean.fastReportBean.tsRpt.fileContent}"/>
    	<input type="hidden" id="xmlData" value="#{printBackingBean.fastReportBean.tsRpt.xmlData}"/>
    	<input type="hidden" id="businessVersion" value="#{printBackingBean.fastReportBean.tsRpt.businessVersion}"/>
    	<input type="hidden" id="fastReportVersion" value="#{printBackingBean.fastReportBean.version}"/>
    	
    	<!--  -->
    	<input type="hidden" id="sealServerAdr" value="#{printBackingBean.fastReportBean.signRptPO.sealServerAdr}"/>
    	<input type="hidden" id="sealPrintAdr" value="#{printBackingBean.fastReportBean.signRptPO.sealPrintAdr}"/>
    	<input type="hidden" id="sealtokenId" value="#{printBackingBean.fastReportBean.signRptPO.sealtokenId}"/>
    	<input type="hidden" id="sealifSign" value="#{printBackingBean.fastReportBean.signRptPO.sealifSign}"/>
    	<input type="hidden" id="sealsignParam" value="#{printBackingBean.fastReportBean.signRptPO.sealsignParam}"/>
    	<input type="hidden" id="filePath" value="#{printBackingBean.fastReportBean.signRptPO.filePath}"/>
    	<input type="hidden" id="tempPDFPath" value="#{printBackingBean.fastReportBean.tempPDFPath}"/>
    </p:outputPanel>
    <a href="/resources/files/chiscdcReportPlus.crx" id="crxId" style="display: none">crxId</a>
    <script type="text/javascript">
        //<![CDATA[
            /**
             * 检查FastReport是否安装
             */
			var able = 0;
            function onFastReportLoad(){
				if(frpt.GetNewFileTpl == undefined){
                    able = 0;
                    InstallDialog.show();
				}else{
					//版本号
					var version = frpt.GetVersion;
					var fastReportVersion = document.getElementById("fastReportVersion").value;
					if (fastReportVersion != null && version != fastReportVersion) {
						PF("UpdateDialog").show();
						$("#curVersion").text(version);
						$("#sysVersion").text(fastReportVersion);
					} else {
						able = 1;
					}
				}
            }
            /**
			 * <p>oss预览报表</p>
			 *
			 */
	        function frpt_showPDF() {
			   var tempPDFPath=document.getElementById("tempPDFPath").value;
			   deleteTempPDF([{name:'tempPDFPath',value:tempPDFPath}]);
	        }
			   /**
	            * 导出PDF报表
	            */
		        function frpt_export() {
	                onFastReportLoad();
				    if(able == 1){
						var frpt=document.getElementById("frpt");
						var rptcod=document.getElementById("rptCod").value;
						var rptvre=document.getElementById("rptver").value;
						var rptnam=document.getElementById("rptnam").value;
						var rpttpl=document.getElementById("rpttpl").value;
						var xmlData=document.getElementById("xmlData").value;
						var businessVersion=document.getElementById("businessVersion").value;
						var newrptcod=rptcod;
						if(businessVersion!="") {
							newrptcod = newrptcod+"zwx"+businessVersion;
						}
						var bool=frpt.GetRptFile(newrptcod, rptvre);
						if(!bool) {
							frpt.SetRptFile(newrptcod, rptvre, rpttpl);
						}
						
						frpt.ShowReport(newrptcod, rptvre,xmlData, rptnam+'.pdf');
					}
		        }
		        
		       /**
	            * 电子签章
	            */
		        function frpt_sign() {
	                onFastReportLoad();
				    if(able == 1){
						var rptcod=document.getElementById("rptCod").value;
						var rptvre=document.getElementById("rptver").value;
						var rpttpl=document.getElementById("rpttpl").value;
						var xmlData=document.getElementById("xmlData").value;
						var businessVersion=document.getElementById("businessVersion").value;
						var newrptcod=rptcod;
						if(businessVersion!="") {
							newrptcod = newrptcod+"zwx"+businessVersion;
						}
						var bool=frpt.GetRptFile(newrptcod, rptvre);
						if(!bool) {
							frpt.SetRptFile(newrptcod, rptvre, rpttpl);
						}
						
						var sealServerAdr=document.getElementById("sealServerAdr").value;
						var sealPrintAdr=document.getElementById("sealPrintAdr").value;
						var sealtokenId=document.getElementById("sealtokenId").value;
						var sealifSign=document.getElementById("sealifSign").value;
						var sealsignParam=document.getElementById("sealsignParam").value;
						var filePath=document.getElementById("filePath").value;
						
						frpt.SealServerAdr=sealServerAdr;
					  	frpt.SealPrintAdr=sealPrintAdr;
			        	frpt.SealtokenId=sealtokenId;
			        	frpt.SealRid="1";
			        	frpt.SealifSign=sealifSign;
			        	frpt.SealsignParam=sealsignParam;
			        	frpt.filePath=filePath;
				        frpt.RptSeal(newrptcod, rptvre, xmlData,"");
				        var msg = frpt.SealReturn; 
				        //返回JSON需要解析
				        returnSignResult([{name:'returnMsg', value:msg}]);
					}
		        }
		        
           /**
            * 设计报表
            */
	        function frpt_design() {
                onFastReportLoad();
			    if(able == 1){
					var frpt=document.getElementById("frpt");
					var rptcod=document.getElementById("rptCod").value;
					var rptvre=document.getElementById("rptver").value;
					var rpttpl=document.getElementById("rpttpl").value;
					var xmlData=document.getElementById("xmlData").value;
					var businessVersion=document.getElementById("businessVersion").value;
					var newrptcod=rptcod;
					if(businessVersion!="") {
						newrptcod = newrptcod+"zwx"+businessVersion;
					}
					var bool=frpt.GetRptFile(newrptcod, rptvre);
					if(!bool) {
						frpt.SetRptFile(newrptcod, rptvre, rpttpl);
					}
					//最后一个参数是pdf导出路径
					frpt.ShowReport(newrptcod, rptvre,xmlData, '');
					updateFastReport([{name:'frptCode', value:rptcod}, {name:'frptContent', value:frpt.GetNewFileTpl}]);
				}
	        }
           
           /**
            * 预览报表
            */
	        function frpt_show() {
                onFastReportLoad();
			    if(able == 1){
					var frpt=document.getElementById("frpt");
					var rptcod=document.getElementById("rptCod").value;
					var rptvre=document.getElementById("rptver").value;
					var rpttpl=document.getElementById("rpttpl").value;
					var xmlData=document.getElementById("xmlData").value;
					var businessVersion=document.getElementById("businessVersion").value;
					var newrptcod=rptcod;
					if(businessVersion!="") {
						newrptcod = newrptcod+"zwx"+businessVersion;
					}

					var bool=frpt.GetRptFile(newrptcod, rptvre);
					if(!bool) {
						frpt.SetRptFile(newrptcod, rptvre, rpttpl);
					}
					frpt.ShowReport(newrptcod, rptvre,xmlData, '');
				}
	        }

           /**
            * 打印机设置
            */           
            function frpt_printer_set() {
            	onFastReportLoad();
			    if(able == 1){
				   var frpt=document.getElementById("frpt");
				   var rptcod=document.getElementById("rptCod").value;
				   var businessVersion=document.getElementById("businessVersion").value;
					var newrptcod=rptcod;
					if(businessVersion!="") {
						newrptcod = newrptcod+"zwx"+businessVersion;
					}
				   frpt.SetPrinter(newrptcod);
			    }
            }
        //]]>
    </script>

	<style type="text/css">
		.frpt_diag .ui-dialog-content{
			background-color:#FFFFFF;
		}
		.frpt_diag .ui-dialog-content{
			padding: 0em 0em;
		}
		.frpt_img{
			width: 150px;
			text-align: center;
			vertical-align: top;
			padding-top: 10px;
		}
		.frpt2_img{
			width: 150px;
			text-align: center;
			vertical-align: top;
			padding-top: 27px;
		}
		.frpt_tip_head{
			height: 18px;
			font-size:12px;
		}
		.frpt_tip_body{
			height: 27px;
			font-size:13px;
		}
		.frpt_bottom{
			text-align: right;
		    padding-right: 24px;
		    padding-bottom: 20px;
		    vertical-align: bottom;
		}

	</style>

	<p:dialog id="installDialog" widgetVar="InstallDialog" header="插件安装"
			  width="480" height="209" resizable="false" modal="true"  styleClass="frpt_diag">
		<table style="width: 100%;height: 100%;">
			<tr>
				<td style="height: 24px;"></td>
			</tr>
			<tr>
				<td style="text-align: left;vertical-align: top;">
					<table style="width: 100%;height: 100%;">
						<tr>
							<td rowspan="5" class="frpt_img" >
								<img alt="报表图标" src="/resources/images/frpt_icon.png" />
							</td>
							<td class="frpt_tip_head">
								<span >操作信息：</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >1、点击下载按钮下载报表插件；</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >2、将下载的文件拖入浏览器中安装；</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >3、安装之后刷新界面。</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_bottom" >
								<p:commandButton process="@this" value="下载" style="width:80px" onclick="document.getElementById('crxId').click();PF('InstallDialog').hide()" />
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</p:dialog>
	<p:dialog id="updateDialog" widgetVar="UpdateDialog" header="插件更新"
			  width="500" height="250" resizable="false" modal="true" styleClass="frpt_diag" >
		<table style="width: 100%;height: 100%;">
			<tr>
				<td style="height: 24px;" ></td>
			</tr>
			<tr>
				<td style="text-align: left;vertical-align: top;">
					<table style="width: 100%;height: 100%;">
						<tr>
							<td rowspan="5" class="frpt2_img" >
								<img alt="报表图标"  src="/resources/images/frpt_icon.png" />
							</td>
							<td class="frpt_tip_head">
								<span >操作信息：</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >1、请至扩展中心卸载报表插件；</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >2、点击下载按钮下载报表插件；</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >3、将下载的文件拖入浏览器中安装；</span>
							</td>
						</tr>
						<tr>
							<td class="frpt_tip_body">
								<span >4、安装之后刷新界面。</span>
							</td>
						</tr>
						<tr>
							<td colspan="3">
								<hr />
							</td>
						</tr>
						<tr>
							<td style="padding-bottom: 14px;padding-left: 10px;">
								当前版本号：<span id="curVersion"></span><br/>
								系统版本号：<span id="sysVersion" style="color: #ff0000"></span>
							</td>
							<td style="padding-left: 153px;padding-bottom: 12px;">
								<a style="padding-right: 10px;" target="_blank" href="/resources/images/rptTeach.gif">
									<font style="color: #808080;">查看教程</font>
								</a>
							</td>
							<td class="frpt_bottom"  style="padding-bottom: 10px;padding-bottom: 17px;">
								<p:commandButton process="@this"  value="下载" style="width:80px" onclick="document.getElementById('crxId').click();PF('UpdateDialog').hide()" />
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</p:dialog>
</ui:composition>
<!--FastReport-->
