<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">

   <ul class="secondUl">
   	   <c:forEach items="#{menuList}" var="menu">
   	   		<li>
   	   			<a href="javascript:zwx_newMenu_open('#{menu.menuUri}', '#{menu.menuCn}', '#{menu.menuRid}')  ">#{menu.menuCn}
   	   				<c:if test="#{null != menu.childrenList and menu.childrenList.size() > 0}">
   	   					<span class="submenu-icon">&#8801;</span>
   	   				</c:if> 
   	   				
   	   			</a>
				<c:if test="#{null != menu.childrenList and menu.childrenList.size() > 0}">
    				<ui:include src="/WEB-INF/templates/system/menu.xhtml">
    					<ui:param name="menuList" value="#{menu.childrenList}"/>
    				</ui:include>
			    </c:if>   	   			
   	   		</li>
   	   </c:forEach>
   </ul>   
</ui:composition>
<!--Menu-->
