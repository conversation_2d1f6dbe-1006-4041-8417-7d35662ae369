<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

	<EMBED id="embed1" type="application/x-chiscdc-Login-plugin" width="0" height="0">
	</EMBED>
	<p:remoteCommand  name="showappPath" process="@this,appPath" action="#{tdSingleSoBean.showappPath}"/>
    <h:inputHidden id="appPath" value="#{tdSingleSoBean.app_path}"/>
    <a href="/resources/files/chiscdcLoginPlus.crx" id="crxId" style="display: none">crxId</a>
    <script type="text/javascript">
        //<![CDATA[
            /**
             * 检查cslogin控件是否安装
             */
			var able = 0;
			var embed1 = document.getElementById('embed1');
            var app_path;
            function onCsloginLoad(){
				if(embed1.text != "OK"){
                    able = 0;
                    InstallDialog.show();
				}else{
                    able = 1;
				}
            }
           /**
            * 选择程序
            */
	        function action_xzcx() {
	        	onCsloginLoad();
			    if(able == 1){
					var embed1=document.getElementById("embed1");
					app_path=embed1.ExeFileName;
					document.getElementById('mainForm:app_path').value=app_path;
					document.getElementById('mainForm:appPath').value=app_path;
					showappPath();
				}
	        }
           
	        function action_login() {
	        	embed1.chiscdcLogin;
	        }
        //]]>
    </script>

	<style type="text/css">
		.button i {
			padding:1px 5px 0px 0px;
		}

		.button {
			font-family: 'kalingaregular';
			text-decoration:none !important;
		}

		.large {
			padding-top:10px;
			padding-bottom: 10px;
			padding-left: 35px;
			padding-right: 35px;
			font-size:22px;
			font-weight: 600;
		}

		.button a {
			display:block;
			text-decoration:none;
			cursor:pointer;
		}

		a {
			cursor:pointer;
		}

		.classic-orange {
			background:#FCA600;
			color:#FFF !important;
		}

		.classic-orange:hover {
			background:#EE8900;
		}

	</style>

	<h:form id="csloginForm">
		<p:dialog id="installDialog" widgetVar="InstallDialog" header="插件安装"
				  width="500" height="230" resizable="false" modal="true">
			<table style="width: 100%">
				<tr>
					<td style="height: 20px;"></td>
				</tr>
				<tr>
					<td style="height: 30px;text-align: left">
						<span style="font-size: large;font-weight: 600">提示信息：</span>
					</td>
				</tr>
				<tr>
					<td style="width: 100%;height: 60px;">
						<span style="font-size: 22px;font-weight: 700;color: blue">请下载选择程序插件，将下载的文件拖入到浏览器中，并刷新页面！</span>
					</td>
				</tr>
				<tr>
					<td style="height: 20px;"></td>
				</tr>
				<tr>
					<td style="text-align: center">
						<a onclick="document.getElementById('crxId').click();PF('InstallDialog').hide()"
						   class="button large classic-orange" >插件下载</a>
					</td>
				</tr>
			</table>
		</p:dialog>
	</h:form>

</ui:composition>
<!--FastReport-->
