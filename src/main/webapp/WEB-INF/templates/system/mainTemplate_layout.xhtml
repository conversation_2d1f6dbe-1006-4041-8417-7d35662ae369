<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
	<h:head>
	</h:head>

	<h:body>
		<h:outputStylesheet library="css" name="default.css" />
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
		<h:outputStylesheet library="css" name="ui-tabs.css" />
		<ui:insert name="insertScripts" />
		<p:tabView id="tabView" dynamic="true"
			activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0px;">
			<p:tab id="list" title="mainTitle" titleStyle="display:none;">
				<h:form id="mainForm">
					<p:layout style="width:100%;height: 682px;" id="contentLay">
						<p:layoutUnit position="west" size="221" style="border:0px;"
							collapsible="false" header="#{mgrbean.leftTitle}">
							<ui:insert name="insertLeftLayout" />
						</p:layoutUnit>
						<p:layoutUnit position="center" style="border:0px;"
							header="#{mgrbean.rightTitle}">
							<ui:insert name="insertButtons"></ui:insert>
							<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;" rendered="#{condition==null}">
								<p:panelGrid style="width:100%;padding-top: 5px;" id="mainGrid">
									<ui:insert name="insertSearchConditons"></ui:insert>
								</p:panelGrid>
							</p:fieldset>
							<p:dataTable var="itm" value="#{mgrbean.dataModel}"
								paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="#{mgrbean.perPageSize}" id="dataTable" lazy="true"
								emptyMessage="没有您要找的记录！"
								currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
								<ui:insert name="insertDataTable"></ui:insert>
							</p:dataTable>
							<ui:insert name="insertOtherMainContents"></ui:insert>
						</p:layoutUnit>
					</p:layout>
					<ui:include src="confirm.xhtml"></ui:include>
					<ui:include src="focus.xhtml"></ui:include>
				</h:form>
			</p:tab>
			<p:tab id="edit" title="edit" titleStyle="display:none;">
				<ui:include src="#{editPage}"></ui:include>
			</p:tab>
			<p:tab id="view" title="view" titleStyle="display:none;">
				<ui:include src="#{viewPage}"></ui:include>
			</p:tab>
		</p:tabView>

		<ui:include src="growl.xhtml"></ui:include>
		<ui:include src="ajaxStatus.xhtml"></ui:include>
	</h:body>
</f:view>
</html>
<!-- 带转向、真分页的模板 -->