<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">

    <style type="text/css">

        .frpt_diag .ui-dialog-content{
            background-color:#FFFFFF;
        }
        .frpt_diag .ui-dialog-content{
            padding: 0em 0em;
        }
        .frpt_img{
            width: 150px;
            text-align: center;
            vertical-align: top;
            padding-top: 10px;
        }
        .frpt2_img{
            width: 150px;
            text-align: center;
            vertical-align: top;
            padding-top: 27px;
        }
        .frpt_tip_head{
            height: 18px;
            font-size:12px;
        }
        .frpt_tip_body{
            height: 27px;
            font-size:13px;
        }
        .frpt_bottom{
            text-align: right;
            padding-right: 24px;
            padding-bottom: 20px;
            vertical-align: bottom;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[

        /**
         * 检测身份证读卡器是否安装
         */
        function checkReaderIDCDeviceExist() {
            if (idc.FCardNo == undefined) {
                return false;
            }
            return true;
        }

        function readcard() {
            try {
               if(!checkReaderIDCDeviceExist()){
                    PF('InstallReadIDCDialog').show();
                   return ;
               }
                idc.ReadCard();
                var fCardNo = idc.FCardNo;
                var idcInfo={
                    readerIdc:fCardNo,
                    readerName:idc.FName,
                    readerAdd:idc.FAddress
                };
                var ifShowPhoto ="#{ifShowPhoto}" ;
                if(ifShowPhoto){
                    idcInfo.readerBSAE64 =idc.FPhotoData;
                }
                var stringify = JSON.stringify(idcInfo);
                cardReaderAction([{name:'readIDCBean',value:stringify}]);
            } catch (e) {
                console.log(e);
            }
        }

        //]]>
    </script>

    <EMBED id="idc" type="application/x-chiscdc-ReadIdc-plugin" width="0" height="0"></EMBED>

    <p:remoteCommand  name="cardReaderAction" process="@this" action="#{readIDCManageBean.cardReaderAction}"/>

    <a href="/resources/files/readerIdc.rar" id="readIDCPlugin" style="display: none">身份证读卡插件</a>

    <p:dialog id="installReadIDCDialog" widgetVar="InstallReadIDCDialog" header="插件安装"
              width="480" height="209" resizable="false" modal="true"  styleClass="frpt_diag">
        <table style="width: 100%;height: 100%;">
            <tr>
                <td style="height: 24px;"></td>
            </tr>
            <tr>
                <td style="text-align: left;vertical-align: top;">
                    <table style="width: 100%;height: 100%;">
                        <tr>
                            <td rowspan="5" class="frpt_img" >
                                <img style="margin-top: 20px;" alt="身份证读卡器图标" src="/resources/images/read-idc-icon.png" />
                            </td>
                            <td class="frpt_tip_head">
                                <span >操作信息：</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="frpt_tip_body">
                                <span >1、点击下载按钮下载读卡器相关插件；</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="frpt_tip_body">
                                <span >2、根据下载的压缩包内的文件名称提示进行安装；</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="frpt_tip_body">
                                <span >3、安装完成之后刷新界面。</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="frpt_bottom" >
                                <p:commandButton process="@this" value="下载" style="width:80px" onclick="document.getElementById('readIDCPlugin').click();PF('InstallReadIDCDialog').hide()" />
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </p:dialog>
</ui:composition>
