<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui">

	<EMBED id="WebOffice" width="100%" height="100%"  type="application/x-chiscdc-WebOffice-plugin"/>

    <a href="/resources/files/chiscdcWebOfficePlus.crx" class="extensionPluginClass" style="display: none">crxId</a>
    <script type="text/javascript">
        //<![CDATA[
            /**
             * 检查插件是否安装
             */
            function officePluginInstallCheck(){
                try{
                	if(WebOffice.Version =="undefined" || WebOffice.Version==null){
                		jQuery(".extensionPluginClass").get(0).click();
                        alert("安装完后请重启浏览器！");
                        return "0";
                	}else{
                		return "1";
                	}
                } catch (e){
                	jQuery(".extensionPluginClass").get(0).click();
                    alert("安装完后请重启浏览器！");
                    return "0";
                }
            }
           
            /**
             * 隐藏控件
             */
            function hideOffice() {
                if (null != document.getElementById("WebOffice")) {
                    document.getElementById("WebOffice").style.width = "0px";
                    document.getElementById("WebOffice").style.height = "0px";
                }
            }
            
            /**
             * 显示控件
             */            
            function showOffice() {
                if (null != document.getElementById("WebOffice")) {
                    document.getElementById("WebOffice").style.width = "100%";
                    document.getElementById("WebOffice").style.height = "100%";
                }
            }  
            
            /**
            * 全屏显示
            */
    		function fullScreenScript() {
    			WebOffice.FullSize;
    		}
            
            /**
            * 替换标签
            */
            function loadBookmarks() {
            	WebOffice.WebLoadBookmarks;        	
            }            
        //]]>
    </script>	
</ui:composition>
<!--FastReport-->
