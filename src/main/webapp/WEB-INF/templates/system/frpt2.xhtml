<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:p="http://primefaces.org/ui" xmlns:h="http://xmlns.jcp.org/jsf/html">
	<p:remoteCommand  name="deleteTempPDF" process="@this" action="#{printBackingBean.fastReportBean.deleteTempPDF}"/>
	<p:remoteCommand  name="buildWritReportActionDo" process="@this"  onstart="PF('shadeTip').show();" update="#{updateId}"
					  action="#{printBackingBean.tobuildWritReport}" oncomplete="PF('shadeTip').hide();"/>
	<p:remoteCommand  name="buildWritReportActionDoNoUpdate" process="@this"  onstart="PF('shadeTip').show();"
					  action="#{printBackingBean.tobuildWritReport}" oncomplete="PF('shadeTip').hide();"/>
    <p:outputPanel id="hiddenPanel2">
    	<input type="hidden" id="tempPDFPath" value="#{printBackingBean.fastReportBean.tempPDFPath}"/>
    </p:outputPanel>
    <script type="text/javascript">
        //<![CDATA[
			/**
			 * <p>oss预览报表</p>
			 *
			 */
	        function frpt_showPDF() {
			   var tempPDFPath=document.getElementById("tempPDFPath").value;
			   deleteTempPDF([{name:'tempPDFPath',value:tempPDFPath}]);
	        }
			/**
			 * 文书等待的提示信息显示
			 */
	        function showShadeTip() {
				buildWritReportActionDo();
	        }
			/**
			 * 文书等待的提示信息显示
			 */
	        function showShadeTipNoUpdate() {
				buildWritReportActionDoNoUpdate();
	        }

        //]]>
    </script>
	<style type="text/css">
		.shadeTip {
			border-radius: 5px;
			padding: 10px;
			background: #4D4D4D !important;
			text-align: left;
			color: white !important;
			word-wrap: break-word;
			border: none;
		}

		.shadeTip .ui-dialog-content {
			border: 1px solid transparent;
			background: #4D4D4D !important;
		}

		.shadeTip .ui-widget-content {
			border: 1px solid transparent;
			background: #4D4D4D !important;
		}
		.shadeTip>div:first-child {
			overflow: hidden;
			margin-top: -10px;
		}

	</style>
	<p:dialog id="shadeTip" widgetVar="shadeTip" modal="true" height="20" resizable="false" showHeader="false" closeOnEscape="true" styleClass="shadeTip">
		<p:panelGrid >
			<p:row style="border:1px solid transparent !important;">
				<p:column style="border: transparent !important;">
						<p:graphicImage url="/resources/images/main/loading5.gif" style="margin-top: 4px;"/>
				</p:column>
				<p:column style="border: transparent !important;">
					<h:outputText style="color: #FFFFFF;font-size: 15px;" value="文书生成中，请等待..." />
				</p:column>
			</p:row>
		</p:panelGrid>


	</p:dialog>
</ui:composition>
<!--FastReport-->
