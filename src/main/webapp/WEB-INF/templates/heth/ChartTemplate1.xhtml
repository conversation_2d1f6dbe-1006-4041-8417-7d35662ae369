<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
		<h:outputStylesheet library="css" name="default.css" />
		<h:outputStylesheet library="css" name="ui-tabs.css" />
		<script type="text/javascript" src="/resources/component/fusioncharts/FusionCharts.js"></script>
		<ui:insert name="insertCharScripts" />

		<h:form id="chartForm">

			<p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
				<f:facet name="header">
					<ui:insert name="insertCharTitle" />
				</f:facet>
			</p:panelGrid>

			<ui:insert name="insertCharButtons"/>

			<p:fieldset legend="查询条件" toggleable="true" toggleSpeed="500"
				style="margin-top: 5px;margin-bottom: 5px;" rendered="#{condition==null}">
				<p:panelGrid style="width:100%;height:100%;" id="mainGrid">
					<ui:insert name="insertCharSearchConditons" />
				</p:panelGrid>
			</p:fieldset>

			<p:fieldset legend="统计图表" toggleable="true" toggleSpeed="500"
				style="margin-top: 5px;margin-bottom: 5px;">
				<ui:insert name="insertChart"></ui:insert>
			</p:fieldset>

			<ui:insert name="insertCharDataTable"></ui:insert>
			<ui:insert name="insertCharOtherMainContents"></ui:insert>
			<ui:include src="../system/confirm.xhtml"></ui:include>
			<ui:include src="../system/focus.xhtml"></ui:include>

		</h:form>

		<ui:include src="../system/growl.xhtml"></ui:include>
		<ui:include src="../system/ajaxStatus.xhtml"></ui:include>
</ui:composition>
<!-- 带转向、真分页的模板 -->