<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  
        xmlns="http://java.sun.com/xml/ns/javaee"  
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
        version="3.0">  	
	<display-name></display-name>
	<context-param>
		<param-name>javax.faces.PROJECT_STAGE</param-name>
		<param-value>Production</param-value>
	</context-param>
	<context-param>
	    <param-name>com.sun.faces.numberOfViewsInSession</param-name>
	    <param-value>500</param-value>
	</context-param>
	<context-param>
	    <param-name>com.sun.faces.numberOfLogicalViews</param-name>
	    <param-value>500</param-value>
	</context-param>	
    <context-param>
    	<param-name>com.sun.faces.serializeServerState</param-name>
    	<param-value>false</param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.STATE_SAVING_METHOD</param-name>
        <param-value>server</param-value>
    </context-param>
    	
	<context-param>
		<param-name>primefaces.THEME</param-name>
		<param-value>redmond</param-value>
	</context-param>
	<context-param>
		<param-name>primefaces.CLIENT_SIDE_VALIDATION</param-name>
		<param-value>true</param-value>
	</context-param>
    <context-param>
        <param-name>javax.faces.DEFAULT_SUFFIX</param-name>
        <param-value>.xhtml</param-value>
    </context-param>
	<context-param>
		<param-name>primefaces.SUBMIT</param-name>
		<param-value>partial</param-value>
	</context-param>
	<context-param>
        <param-name>primefaces.UPLOADER</param-name>
        <param-value>commons</param-value>
    </context-param>
	<context-param>
		<param-name>javax.faces.FACELETS_SKIP_COMMENTS</param-name>
		<param-value>true</param-value>
	</context-param>     
    
    <!-- 直接访问页面 -->
     <filter>  
	    <filter-name>ToOtherPageFilter</filter-name>
	    <filter-class>com.chis.modules.system.filter.ToOtherPageFilter</filter-class>
	</filter>	
	<filter-mapping>
		    <filter-name>ToOtherPageFilter</filter-name>
			<url-pattern>/*</url-pattern>
	</filter-mapping>

    <!-- 解决上传乱码问题 -->
	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>    
    
    <filter>
        <filter-name>PrimeFaces FileUpload Filter</filter-name>
        <filter-class>org.primefaces.webapp.filter.FileUploadFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>PrimeFaces FileUpload Filter</filter-name>
        <servlet-name>Faces Servlet</servlet-name>
    </filter-mapping>

	<servlet>
		<servlet-name>springServlet</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath*:/spring/spring-context.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>springServlet</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

    <!--定义Spring的配置的位置，可以定义多个配置文件，可以使用通配符。  -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:/spring/spring-*.xml</param-value>
	</context-param>    
    
    <!-- 对Spring容器进行实例化 设置一启动当前的Web应用，就加载Spring，让Spring管理Bean-->
    <listener>  
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>  
    </listener>  

	<!-- 
    <filter>
        <filter-name>SpringOpenEntityManagerInViewFilter</filter-name>
        <filter-class>org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>SpringOpenEntityManagerInViewFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
     -->

	<servlet>
		<servlet-name>Faces Servlet</servlet-name>
		<servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>Faces Servlet</servlet-name>
		<url-pattern>*.faces</url-pattern>
	</servlet-mapping>
    <servlet>
        <servlet-name>Push Servlet</servlet-name>
        <servlet-class>org.primefaces.push.PushServlet</servlet-class>
        <load-on-startup>0</load-on-startup>
        <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
        <servlet-name>Push Servlet</servlet-name>
        <url-pattern>/primepush/*</url-pattern>
    </servlet-mapping>
	<welcome-file-list>
		<welcome-file>login.faces</welcome-file>
	</welcome-file-list>
	<servlet>
		<servlet-name>SSOServlet</servlet-name>
		<servlet-class>com.chis.modules.system.web.SSOServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>SSOServlet</servlet-name>
		<url-pattern>/SSOServlet</url-pattern>
	</servlet-mapping>
	<servlet>
		<servlet-name>ZzpxServlet</servlet-name>
		<servlet-class>com.chis.modules.system.web.ZzpxServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>ZzpxServlet</servlet-name>
		<url-pattern>/zzpx</url-pattern>
	</servlet-mapping>
	<servlet>
		<servlet-name>ChisWaySsoServlet</servlet-name>
		<servlet-class>com.chis.modules.system.web.ChisWaySsoServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>ChisWaySsoServlet</servlet-name>
		<url-pattern>/ChisWaySsoServlet</url-pattern>
	</servlet-mapping>
	<!-- 
    <servlet>
        <display-name>监控查看服务</display-name>
        <servlet-name>VedioLookService</servlet-name>
        <servlet-class>com.chis.web.servlet.VedioLookService</servlet-class>
    </servlet>
    <servlet>
		<servlet-name>AuthImage</servlet-name>
		<servlet-class>com.chis.web.servlet.AuthImage</servlet-class>
	</servlet>
    <servlet>
		<servlet-name>SSOServlet</servlet-name>
		<servlet-class>com.chis.web.servlet.SSOServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>SSOServlet</servlet-name>
		<url-pattern>/SSOServlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>AuthImage</servlet-name>
		<url-pattern>/authImage</url-pattern>
	</servlet-mapping>
    <servlet-mapping>
        <servlet-name>VedioLookService</servlet-name>
        <url-pattern>/VedioLookService</url-pattern>
    </servlet-mapping>

    <servlet>
		<servlet-name>FlowPicServlet</servlet-name>
		<servlet-class>com.chis.web.servlet.FlowPicServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>FlowPicServlet</servlet-name>
		<url-pattern>/FlowPicServlet</url-pattern>
	</servlet-mapping>		
	<servlet>
		<servlet-name>newShortcutDesktopAjax</servlet-name>
		<servlet-class>com.chis.web.servlet.ShortcutDesktopAjax</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>newShortcutDesktopAjax</servlet-name>
		<url-pattern>/newShortcutDesktopAjax</url-pattern>
	</servlet-mapping>
    <servlet>
        <servlet-name>OfficeServlet</servlet-name>
        <servlet-class>com.chis.web.servlet.ZwxBaseFileServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OfficeServlet</servlet-name>
        <url-pattern>/OfficeServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MsOfficeServlet</servlet-name>
        <servlet-class>com.chis.web.servlet.ZwxMsFileServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MsOfficeServlet</servlet-name>
        <url-pattern>/MsOfficeServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>EmOfficeServlet</servlet-name>
        <servlet-class>com.chis.web.servlet.EmOfficeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EmOfficeServlet</servlet-name>
        <url-pattern>/EmOfficeServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>DownLoad</servlet-name>
        <servlet-class>com.chis.web.servlet.ZwxDownLoad</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DownLoad</servlet-name>
        <url-pattern>/DownLoad</url-pattern>
    </servlet-mapping>
     -->

	<!-- DruidStatView 
	<servlet>
		<servlet-name>DruidStatView</servlet-name>
		<servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
		<init-param>
			<param-name>allow</param-name>
			<param-value>127.0.0.1</param-value>
		</init-param>
	</servlet>
	<servlet-mapping>
		<servlet-name>DruidStatView</servlet-name>
		<url-pattern>/druid/*</url-pattern>
	</servlet-mapping> 
	  
	
	<servlet>
        <servlet-name>Connector</servlet-name>
        <servlet-class>com.chis.modules.system.web.ZwxConnectorServlet</servlet-class>
        <load-on-startup>2</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>Connector</servlet-name>
        <url-pattern>/resources/js/fckeditor/editor/filemanager/connectors/*</url-pattern>
    </servlet-mapping>
	-->
    
  	<session-config>
        <session-timeout>500</session-timeout>
    </session-config>
</web-app>