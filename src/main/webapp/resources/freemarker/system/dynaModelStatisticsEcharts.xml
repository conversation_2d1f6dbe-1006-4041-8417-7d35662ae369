<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>动态表单列表框架样式</name>
	<content>
	<![CDATA[
	<#if gen_type??>
		<#if gen_type==0>
		//柱状图
		var legendValue = [<#if gen_data?size gt 0><#list gen_data as data><#if data_index!=0>,</#if>'${data.titleName!''}'</#list></#if>];
		var xAxisData = new Array();
		var xAxisIndex = <#if gen_title?size gt 0><#list gen_title as title><#if title_index==0>${title.dataIndex}</#if></#list><#else>0</#if>;
		for(var i=0;i<datas.length;i++){
			var tempItemData = datas[i][xAxisIndex];
			if(tempItemData == null )
				tempItemData = "-";
			xAxisData.push(tempItemData);
		}
		var dataIndexs = [<#if gen_data?size gt 0><#list gen_data as data><#if data_index!=0>,</#if>${data.dataIndex!''}</#list></#if>];
		var data = new Array();
		for (var i=0;i<dataIndexs.length;i++){
			var itemData = new Array();
			for(var j=0;j<datas.length;j++){
				var tempItemData = datas[j][dataIndexs[i]];
				if(tempItemData == null )
					tempItemData = 0;
				itemData.push(tempItemData);
			}
			data.push(itemData);
		}
		
		var option = {
			title: {
        		text: '${gen_form_name!''}统计图',
        		x:'center'
    		},
    		tooltip : {
        		trigger: 'axis'
    		},
    		legend: {
        		x: 'left',
        		data: legendValue
    		},
    		toolbox: {
        		show : true,
        		feature : {
            		magicType : {show: true, type: ['line', 'bar']},
            		restore : {show: true},
            		saveAsImage : {show: true}
       			}
    		},
			xAxis : [
        		{
            		type : 'category',
            		data : xAxisData
       			}
    		],
			yAxis : [
        		{
            		type : 'value'
        		}
    		],
    		series : [
    		<#if gen_data?size gt 0>
    		<#list gen_data as data>
        		{
            		name:'${data.titleName}',
            		type:'bar',
            		stack: 'bar',
            		areaStyle: {normal: {}},
            		data:data[${data_index}]
        		}
        		<#if data_has_next>,</#if>
        	</#list>
        	</#if>
        	]
    	};
		<#elseif gen_type==1>
		//折线图
		var legendValue = [<#if gen_data?size gt 0><#list gen_data as data><#if data_index!=0>,</#if>'${data.titleName!''}'</#list></#if>];
		var xAxisData = new Array();
		var xAxisIndex = <#if gen_title?size gt 0><#list gen_title as title><#if title_index==0>${title.dataIndex}</#if></#list><#else>0</#if>;
		for(var i=0;i<datas.length;i++){
			var tempItemData = datas[i][xAxisIndex];
			if(tempItemData == null )
				tempItemData = "-";
			xAxisData.push(tempItemData);
		}
		var dataIndexs = [<#if gen_data?size gt 0><#list gen_data as data><#if data_index!=0>,</#if>${data.dataIndex!''}</#list></#if>];
		var data = new Array();
		for (var i=0;i<dataIndexs.length;i++){
			var itemData = new Array();
			for(var j=0;j<datas.length;j++){
				var tempItemData = datas[j][dataIndexs[i]];
				if(tempItemData == null )
					tempItemData = 0;
				itemData.push(tempItemData);
			}
			data.push(itemData);
		}
		
		var option = {
			title: {
        		text: '${gen_form_name!''}统计图',
        		x:'center'
    		},
    		tooltip : {
        		trigger: 'axis'
    		},
    		legend: {
        		x: 'left',
        		data: legendValue
    		},
    		toolbox: {
        		show : true,
        		feature : {
            		magicType : {show: true, type: ['line', 'bar']},
            		restore : {show: true},
            		saveAsImage : {show: true}
       			}
    		},
			xAxis : [
        		{
            		type : 'category',
            		boundaryGap : false,
            		data : xAxisData
       			}
    		],
			yAxis : [
        		{
            		type : 'value'
        		}
    		],
    		series : [
    		<#if gen_data?size gt 0>
    		<#list gen_data as data>
        		{
            		name:'${data.titleName}',
            		type:'line',
            		stack: 'line',
            		areaStyle: {normal: {}},
            		data:data[${data_index}]
        		}
        		<#if data_has_next>,</#if>
        	</#list>
        	</#if>
        	]
    	};
		<#elseif gen_type==2>
		//饼图
		var xAxisData = new Array();
		var xAxisIndex = <#if gen_title?size gt 0><#list gen_title as title><#if title_index==0>${title.dataIndex}</#if></#list><#else>0</#if>;
		for(var i=0;i<datas.length;i++){
			var tempItemData = datas[i][xAxisIndex];
			if(tempItemData == null )
				tempItemData = "-";
			xAxisData.push(tempItemData);
		}
		var dataIndex = <#if gen_data?size gt 0><#list gen_data as data><#if data_index==0>${data.dataIndex}</#if></#list><#else>0</#if>;
		var data = new Array();
		for (var i=0;i<datas.length;i++){
			var itemDatas = datas[i];
			var itemData = {};
			var itemDataValue = itemDatas[dataIndex];
			var itemDataName = itemDatas[xAxisIndex];
			if(itemDataValue==null)
				itemDataValue = 0;
			if(itemDataName==null)
				itemDataName = "-";
			itemData.value = itemDataValue;
			itemData.name = itemDataName;
			data.push(itemData);
		}
		
		var option = {
			title: {
        		text: '${gen_form_name!''}统计图',
        		x:'center'
    		},
    		tooltip : {
        		trigger: 'axis'
    		},
    		legend: {
    			orient : 'vertical',
        		x: 'left',
        		data: xAxisData
    		},
    		toolbox: {
        		show : true,
        		feature : {
            		restore : {show: true},
            		saveAsImage : {show: true}
       			}
    		},
			calculable : true,
    		series : [
        		{
            		name:'${gen_form_name!''}',
            		type:'pie',
            		radius : '55%',
            		center: ['50%', '60%'],
            		data:data
        		}
        	]
    	};
		</#if>
		
	<#else>
		var option = "";
	</#if>
	]]>
	</content>
</template>