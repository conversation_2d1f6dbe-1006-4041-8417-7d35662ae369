<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>框架样式</name>
	<content>
	<![CDATA[
<#if gen_Main_Fields??>
<table class="ui-panelgrid ui-widget"
	style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" role="grid">
	<tbody>
		<#list gen_Main_Fields as field>
		<tr class="ui-widget-content ui-panelgrid-even" role="row">
			<td role="gridcell" style="text-align:right;padding-right:3px;width:180px;height:30px;">
				<#if field.isReq == 1>
				<font color="red">*</font>
				</#if>
				${field.fdCnname!''}：
			</td>
			<td role="gridcell" style="text-align:left;padding-left:8px;">
				${field.freeMarkerStr!''}
			</td>
		</tr>
		</#list>
	</tbody>
</table>
</#if>

<#if gen_Sub_Fields??>
${r'<#if rows?? && rows gt 0>'}

<button id="DYNA_BTN_TH" type="button">添行</button>

<table style="width:100%;height:100%;">
	<tbody>
		<tr>
			<td>
				<div class="ui-datatable ui-widget">
					<div class="ui-datatable-tablewrapper">
						<table role="grid">
							<thead>
								<tr role="row">
									<#list gen_Sub_Fields as subField>
									<th class="ui-state-default" role="columnheader" style="text-align:center;">
										<span>${subField.fdCnname!''}</span>
									</th>
									</#list>
								</tr>
							</thead>

							<tbody id="subTableDiv"  class="ui-datatable-data ui-widget-content">
								${r'<#list 1..rows as t>'}
								<tr id="subTableRowDiv${r'${t-1}'}" class="ui-widget-content"
									role="row">
									<#list gen_Sub_Fields as subField>
									<td role="gridcell" style="width: 100px;text-align:center ;">
										${subField.freeMarkerStr!''}
									</td>
									</#list>
								</tr>
								${r'</#list>'}
							</tbody>
						</table>
					</div>
				</div>
			</td>
		</tr>
	</tbody>
</table>

<script type="text/template" id="DataChildTpl">
	<tr id="subTableRowDiv{{row}}" class="ui-widget-content" role="row">
		<#list gen_Sub_Fields as subField>
		<td role="gridcell" style="text-align:center ;">
			${subField.freeMarkerModelStr!''}
		</td>
		</#list>
	</tr>
</script>
</div>
${r'</#if>'}
</#if>

<#if gen_Js??>
${gen_Js!''}
</#if>
	]]>
	</content>
</template>



