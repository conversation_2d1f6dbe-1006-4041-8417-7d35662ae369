<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>动态表单列表框架样式</name>
	<content>
	<![CDATA[
<script type="text/javascript">
//生成统计图表方法
function dyna_dom_genCharts(datas){
	if(datas==null)
		datas={};
	${echartsOption!''}
	if(option==null || option=='')
		return;
	var myChart = echarts.init(document.getElementById("result_main"));
	myChart.setOption(option);
}
 
//primeui样式引用
function dyna_dom_style_init() {
    jQuery("[id*=DYNA_SEARCH]").puiinputtext();
}
//过滤条件传值
function search_filters_init() {
	var search = jQuery("#searchJson").val();
	if(search==null || search == '')
		return;
	var searchs = JSON.parse(search);
	jQuery("[id*=DYNA_SEARCH]").each(function(){
		jQuery(this).val(eval('searchs.'+jQuery(this).attr("id")));
	});
}
//FIELDSET初始化方法
function dyna_fieldset_init(){
	jQuery("[id*=FIELDSET_]").puifieldset({
        toggleable: true
    });
}

//查询方法
function dyna_search(){
	dyna_search_init();
}
//查询初始化方法
function dyna_search_init(){
	$('#DATA_TABLE').puidatatable();
	jQuery("#DATA_TABLE_BODY").html("");
	var dataListJson = jQuery("#dataListJson").val();
	var dataTable = jQuery("#DATA_TABLE");
	var thCount = jQuery(dataTable).find("th").length;
	if(dataListJson==null || dataListJson == '' || dataListJson == '[]'){
		var noDataHtml = "<tr class=\"ui-widget-content ui-datatable-odd\"><td colspan=\""+thCount+"\">没有查询到相关数据！</td></tr>";
		jQuery("#DATA_TABLE_BODY").html(noDataHtml);
		dyna_dom_genCharts(datas);
		return;
	}
	var datas = JSON.parse(dataListJson);
	var trHtml = "<tr class=\"ui-widget-content ui-datatable-even\">";
	var trEndHtml = "</tr>";
	var tdHtml = "<td>";
	var tdEndHtml = "</td>";
	for(var i=0;i<datas.length;i++){
		var tr = trHtml+"";
		<#if gen_title_fields?size gt 0>
		<#list gen_title_fields as titleField>
		tr+=tdHtml +dyna_pack_table_data(datas[i],${titleField.dataIndex})+ tdEndHtml;
		</#list>
		</#if>
		jQuery("#DATA_TABLE_BODY").append(tr);
	}
	dyna_dom_genCharts(datas);
}

function dyna_pack_table_data(data,j){
	var value = data[j];
	if(value == null || value == "null")
		value = "-";
	return value;
}

jQuery(function(){
	console.info("page_init");
	dyna_dom_style_init();
	search_filters_init();
	dyna_fieldset_init();
	beforeSearchAction();
});


</script>
<#if gen_search_fields?size gt 0>
<fieldset id="FIELDSET_SEARCH">
	<legend>查询条件</legend>
	<table class="ui-panelgrid ui-widget"
		style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" role="grid" id="mainGrid">
		<tbody>
			<tr class="ui-widget-content ui-panelgrid-even" role="row">
			<#list gen_search_fields as searchField>
				<#if searchField_index!=0 && searchField_index%2==0>
					</tr><tr class='ui-widget-content ui-panelgrid-even' role='row'>
				</#if>
				<td role="gridcell" style="text-align:right;padding-right:3px;width:160px;height:30px;">
					${searchField.fdCnname}：
				</td>
				
				<#if gen_search_fields?size%2==1 && searchField_index+1 == gen_search_fields?size>
					<td role="gridcell" style="text-align:left;padding-left:8px;width: 370px;" colspan="3">
						${searchField.freeMarkerStr}
					</td>
				<#elseif searchField_index%2==0>
					<td role="gridcell" style="text-align:left;padding-left:8px;width: 370px;">
						${searchField.freeMarkerStr}
					</td>
				<#else>
					<td role="gridcell" style="text-align:left;padding-left:8px;">
						${searchField.freeMarkerStr}
					</td>
				</#if>
			</#list>
			</tr>
		</tbody>
	</table>
</fieldset>
</#if>
<#if echartsOption??>
<div id="result_main"
	style="height:400px;border:1px solid #ccc;padding:10px;margin-top:5px"></div>
</#if>
<div id="DATA_TABLE" class="" style="margin-top: 5px;">
	<input id="topTitle" type="hidden" value='${gen_top_title!''}'/>
	<table>
		<thead>
			<tr class="ui-state-default">
				<#if gen_title_fields?size gt 0>
				<#list gen_title_fields as titleField>
				<th class="ui-state-default"><span class="ui-column-title">${titleField.titleName}</span></th>
				</#list>
				</#if>
			</tr>
		</thead>
		<tbody id="DATA_TABLE_BODY">
		</tbody>
	</table>
</div>
<div id="PAGINATOR_DATA_TABLE"></div>
	]]>
	</content>
</template>



