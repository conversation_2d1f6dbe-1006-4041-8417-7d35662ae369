<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>经典样式</name>
	<content>
	<![CDATA[
<#if gen_Main_Fields??>
<div class="pui-grid pui-grid-responsive">
<#list gen_Main_Fields as field>
	<div class="pui-grid-row">
		<div class="pui-grid-col-1 right middle">
			<#if field.isReq == 1>
			<font color="red">*</font>
			</#if>
			${field.fdCnname!''}：
		</div>
		<div class="pui-grid-col-2 left">
			${field.freeMarkerStr!''}
		</div>
	</div>
</#list>
</div>
</#if>

<#if gen_Sub_Fields??>
	${r'<#if rows?? && rows gt 0>'}
	<button id="DYNA_BTN_TH" type="button">添行</button>
	<div class="pui-grid pui-grid-responsive" id="subTableDiv">
		<div class="pui-grid-row">
			<#list gen_Sub_Fields as subField>
			<div class="pui-grid-col-1 center middle ui-state-default">
				${subField.fdCnname!''}
			</div>
			</#list>
		</div>
	
		${r'<#list 1..rows as t>'}
		<div class="pui-grid-row sub-grid-row" id="subTableRowDiv${r'${t-1}'}">
		
		<#list gen_Sub_Fields as subField>
	    	<div class="pui-grid-col-1 center middle table_border">
	  			${subField.freeMarkerStr!''}
	  		</div>
		</#list>
		</div>
		${r'</#list>'}
		
		<script type="text/template" id="DataChildTpl" >
		<div class="pui-grid-row sub-grid-row" id="subTableRowDiv{{row}}">
		<#list gen_Sub_Fields as subField>
	    	<div class="pui-grid-col-1 center middle table_border">
	  			${subField.freeMarkerModelStr!''}
	  		</div>
		</#list>
		</div>
		</script>
	</div>
	${r'</#if>'}
</#if>

<#if gen_Js??>
${gen_Js!''}
</#if>
	]]>
	</content>
</template>
