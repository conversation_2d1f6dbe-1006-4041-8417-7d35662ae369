<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>动态表单列表框架样式</name>
	<content>
	<![CDATA[
<script type="text/javascript">
 
//primeui样式引用
function dyna_dom_style_init() {
    jQuery("[id*=DYNA_SEARCH]").puiinputtext();
}
//过滤条件传值
function search_filters_init() {
	var search = jQuery("#searchJson").val();
	if(search==null || search == '')
		return;
	var searchs = JSON.parse(search);
	jQuery("[id*=DYNA_SEARCH]").each(function(){
		jQuery(this).val(eval('searchs.'+jQuery(this).attr("id")));
	});
}
//FIELDSET初始化方法
function dyna_fieldset_init(){
	jQuery("[id*=FIELDSET_]").puifieldset({
        toggleable: true
    });
}
//分页
//为了抑制递归查询，在每次查询时重置flag
var flag = true;
function dyna_paginator_init(){
	var pageSize = jQuery("#pageSize").val();
	if(pageSize==null || pageSize=="")
		pageSize = "18";
	var pageCount = jQuery("#pageCount").val();
	var first = jQuery("#page").val();
	var page = first/pageSize;
	console.info(first);
	jQuery("#PAGINATOR_DATA_TABLE").puipaginator({
        totalRecords: pageCount,
        rows: pageSize,
        page: page,
        paginate: function(event, state) {
        	/* first: First record,
            rows: Number of rows,
            page: Current page,
            pageCount: Total number of pages,
            pageLinks: Number of page links */
            jQuery("#page").val(state.first);
			if(flag){
            	searchAction();
            	flag = false;
			}else{
				flag = true;
			}
        }
    });
}
//查询方法
function dyna_search(){
	flag=false;
	dyna_search_init();
}
//查询初始化方法
function dyna_search_init(){
	dyna_paginator_init();
	$('#DATA_TABLE').puidatatable();
	jQuery("#DATA_TABLE_BODY").html("");
	var dataListJson = jQuery("#dataListJson").val();
	var dataTable = jQuery("#DATA_TABLE");
	var thCount = jQuery(dataTable).find("th").length;
	if(dataListJson==null || dataListJson == '' || dataListJson == '[]'){
		var noDataHtml = "<tr class=\"ui-widget-content ui-datatable-odd\"><td colspan=\""+thCount+"\">没有查询到相关数据！</td></tr>";
		jQuery("#DATA_TABLE_BODY").html(noDataHtml);
		return;
	}
	var datas = JSON.parse(dataListJson);
	var trHtml = "<tr class=\"ui-widget-content ui-datatable-even\">";
	var trEndHtml = "</tr>";
	var tdHtml = "<td>";
	var tdEndHtml = "</td>";
	for(var i=0;i<datas.length;i++){
		var tr = trHtml+"";
		for(var j=1;j<datas[i].length-1;j++){
			var value = datas[i][j];
			if(value == null || value == "null")
				value = "-";
			tr += tdHtml + value + tdEndHtml;
		}
		var optionTd = tdHtml + "";
		optionTd += "<a  href=\"#\" class=\"ui-commandlink ui-widget\" onclick=\"dyna_to_detail("+datas[i][0]+")\">详情</a>"+tdEndHtml;
		tr += optionTd + trEndHtml;
		jQuery("#DATA_TABLE_BODY").append(tr);
	}
}

//前往详情页面
function dyna_to_detail(rid){
	jQuery("#rid").val(rid);
	viewInit();
}

jQuery(function(){
	console.info("page_init");
	dyna_dom_style_init();
	search_filters_init();
	dyna_fieldset_init();
	dyna_search_init();
	
});


</script>
<#if gen_search_fields?size gt 0>
<fieldset id="FIELDSET_SEARCH">
	<legend>查询条件</legend>
	<table class="ui-panelgrid ui-widget"
		style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" role="grid" id="mainGrid">
		<tbody>
			<tr class="ui-widget-content ui-panelgrid-even" role="row">
			<#list gen_search_fields as searchField>
				<#if searchField_index!=0 && searchField_index%2==0>
					</tr><tr class='ui-widget-content ui-panelgrid-even' role='row'>
				</#if>
				<td role="gridcell" style="text-align:right;padding-right:3px;width:160px;height:30px;">
					${searchField.fdCnname}：
				</td>
				
				<#if gen_search_fields?size%2==1 && searchField_index+1 == gen_search_fields?size>
					<td role="gridcell" style="text-align:left;padding-left:8px;width: 280px;" colspan="3">
						${searchField.freeMarkerStr}
					</td>
				<#elseif searchField_index%2==0>
					<td role="gridcell" style="text-align:left;padding-left:8px;width: 280px;">
						${searchField.freeMarkerStr}
					</td>
				<#else>
					<td role="gridcell" style="text-align:left;padding-left:8px;">
						${searchField.freeMarkerStr}
					</td>
				</#if>
			</#list>
			</tr>
		</tbody>
	</table>
</fieldset>
</#if>
<div id="DATA_TABLE" class="" style="margin-top: 5px;">
	<table>
		<thead>
			<tr class="ui-state-default">
				<#if gen_title_fields?size gt 0>
				<#list gen_title_fields as titleField>
				<th class="ui-state-default"><span class="ui-column-title">${titleField.fdCnname}</span></th>
				</#list>
				</#if>
			</tr>
		</thead>
		<tbody id="DATA_TABLE_BODY">
		</tbody>
	</table>
</div>
<div id="PAGINATOR_DATA_TABLE"></div>
	]]>
	</content>
</template>



