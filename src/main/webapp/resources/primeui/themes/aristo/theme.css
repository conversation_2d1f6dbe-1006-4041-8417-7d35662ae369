/**
 * Aristo Theme
 * http://wijmo.com/
 *
 * Based on the Aristo theme concept created by 280 North and Pinvoke (https://github.com/280north/aristo).
 *
*/

a {
    outline: none;
}

.ui-icon {
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

/*
 * jQuery UI CSS Framework @VERSION
 *
 * Copyright 2010, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 *
 * To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Arial,sans-serif&fwDefault=bold&fsDefault=1.1em&cornerRadius=3px&bgColorHeader=c4c4c4&bgTextureHeader=03_highlight_soft.png&bgImgOpacityHeader=100&borderColorHeader=a8a8a8&fcHeader=4f4f4f&iconColorHeader=898989&bgColorContent=ffffff&bgTextureContent=01_flat.png&bgImgOpacityContent=100&borderColorContent=a8a8a8&fcContent=4f4f4f&iconColorContent=616161&bgColorDefault=c4c4c4&bgTextureDefault=04_highlight_hard.png&bgImgOpacityDefault=80&borderColorDefault=a8a8a8&fcDefault=4f4f4f&iconColorDefault=ffffff&bgColorHover=c4c4c4&bgTextureHover=04_highlight_hard.png&bgImgOpacityHover=80&borderColorHover=a8a8a8&fcHover=4f4f4f&iconColorHover=ffffff&bgColorActive=c4c4c4&bgTextureActive=06_inset_hard.png&bgImgOpacityActive=65&borderColorActive=aaaaaa&fcActive=4f4f4f&iconColorActive=ffffff&bgColorHighlight=fbf9ee&bgTextureHighlight=02_glass.png&bgImgOpacityHighlight=55&borderColorHighlight=fcefa1&fcHighlight=363636&iconColorHighlight=2e83ff&bgColorError=fef1ec&bgTextureError=05_inset_soft.png&bgImgOpacityError=95&borderColorError=cd0a0a&fcError=cd0a0a&iconColorError=cd0a0a&bgColorOverlay=aaaaaa&bgTextureOverlay=01_flat.png&bgImgOpacityOverlay=0&opacityOverlay=30&bgColorShadow=4f4f4f&bgTextureShadow=01_flat.png&bgImgOpacityShadow=0&opacityShadow=30&thicknessShadow=8px&offsetTopShadow=-8px&offsetLeftShadow=-8px&cornerRadiusShadow=8px
 */


/* Component containers
----------------------------------*/
.ui-widget
{
    font-family: Arial,sans-serif;
    font-size: 1em;
}
.ui-widget .ui-widget
{
    font-size: 1em;
}
.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button
{
    font-family: Arial,sans-serif;
    font-size: 1em;
}
.ui-widget-content
{
    border: 1px solid #a8a8a8;
    background: #ffffff;
    color: #4f4f4f;
}
.ui-widget-content a
{
    color: #4f4f4f;
}

.ui-datatable-odd {
    background: none repeat scroll 0 0 #F2F5F9;
}

.ui-widget-header
{
    border: 1px solid #a8a8a8;
    background: #c4c4c4 url(images/ui-bg_highlight-soft_100_c4c4c4_1x100.png) 50% 50% repeat-x;
    background: #c4c4c4 linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #c4c4c4 -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)"; */
    color: #333;
    font-weight: bold;
    text-shadow: 0px 1px 0px rgba(255,255,255,0.7);
}
.ui-widget-header a
{
    color: #4f4f4f;
}

/* Interaction states
----------------------------------*/
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default
{
    border: 1px solid #a8a8a8;
    background: #c4c4c4 url(images/ui-bg_highlight-hard_80_c4c4c4_1x100.png) 50% 50% repeat-x;
    background: #c4c4c4 linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #c4c4c4 -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)"; */
    font-weight: bold;
    color: #4f4f4f;
    text-shadow: 0px 1px 0px rgba(255,255,255,0.7);
}
.ui-state-default
{
    -moz-box-shadow: inset 0px 1px 0px #fff;
    -webkit-box-shadow: inset 0px 1px 0px #fff;
    box-shadow: inset 0px 1px 0px #fff;
}
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited
{
    color: #4f4f4f;
    text-decoration: none;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus
{
    border: 1px solid #7096ab;
    background: #85b2cb url(images/ui-bg_highlight-hard_80_85b2cb_1x100.png) 50% 50% repeat-x;
    background: #85b2cb linear-gradient(to bottom, rgba(255,255,255,0.6), rgba(255,255,255,0));
    background: #85b2cb -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.6)), to(rgba(255,255,255,0)));
    background: #85b2cb -moz-linear-gradient(to bottom, rgba(255,255,255,0.6), rgba(255,255,255,0)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#CCFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#CCFFFFFF, endColorstr=#00FFFFFF)"; */
    font-weight: bold;
    color: #313131;
    -moz-box-shadow: 0 0 5px #85b2cb;
    -webkit-box-shadow: 0px 0px 8px #85b2cb;
    box-shadow: 0px 0px 8px #85b2cb;
}
.ui-state-hover
{
    -moz-box-shadow: 0px 0px 8px #85b2cb, inset 0px 1px 0px #fff;
    -webkit-box-shadow: 0px 0px 8px #85b2cb, inset 0px 1px 0px #fff;
    box-shadow: 0px 0px 8px #85b2cb, inset 0px 1px 0px #fff;
}
.ui-state-hover a, .ui-state-hover a:hover
{
    color: #2f556a;
    text-decoration: none;
}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active
{
    border: 1px solid #7096ab;
    background: #85b2cb url(images/ui-bg_inset-hard_65_85b2cb_1x100.png) 50% 50% repeat-x;
    background: #85b2cb linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,0.4));
    background: #85b2cb -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0)), to(rgba(255,255,255,0.4)));
    background: #85b2cb -moz-linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,0.4)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF, endColorstr=#CCFFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF, endColorstr=#CCFFFFFF)"; */
    font-weight: bold;
    color: #1C4257;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
}
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited
{
    color: #2f556a;
    text-decoration: none;
}
.ui-widget :active
{
    outline: none;
}
.ui-state-active
{
    -moz-box-shadow: inset 0px -1px 0px #fff;
    -webkit-box-shadow: inset 0px 1px 0px #fff;
    box-shadow: inset 0px 1px 0px #fff;
}
/* Interaction Cues
----------------------------------*/
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight
{
    border: 1px solid #666666;
    background: #aaaaaa;
    background: #aaaaaa linear-gradient(to bottom, rgba(0,0,0,0.25), rgba(0,0,0,0));
    background: #aaaaaa -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,0.25)), to(rgba(0,0,0,0)));
    background: #aaaaaa -moz-linear-gradient(to bottom, rgba(0,0,0,0.25), rgba(0,0,0,0)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#33000000, endColorstr=#00000000);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#33000000, endColorstr=#00000000)"; */
    color: #ffffff;
    text-shadow: 1px 1px 1px #333333;
}

.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a
{
    color: #363636;
}
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error
{
    border: 1px solid #cd0a0a;
    background: #fef1ec url(images/ui-bg_inset-soft_95_fef1ec_1x100.png) 50% bottom repeat-x;
    background: #fef1ec linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #fef1ec -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #fef1ec -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)";*/
    color: #cd0a0a;
}
.ui-state-error a, .ui-widget-content .ui-state-error a, .ui-widget-header .ui-state-error a
{
    color: #cd0a0a;
}
.ui-state-error-text, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error-text
{
    color: #cd0a0a;
}
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary
{
    font-weight: bold;
}
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary
{
    opacity: .7;
    filter: Alpha(Opacity=70);
    font-weight: normal;
}
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled
{
    opacity: .35;
    filter: Alpha(Opacity=35);
    background-image: none;
}

/* Icons
----------------------------------*/

/* states and images */
.ui-icon
{
    width: 16px;
    height: 16px;
    background-image: url(images/ui-icons_616161_256x240.png);
}
.ui-widget-content .ui-icon
{
    background-image: url(images/ui-icons_616161_256x240.png);
}
.ui-widget-header .ui-icon
{
    background-image: url(images/ui-icons_898989_256x240.png);
}
.ui-state-default .ui-icon
{
    background-image: url(images/ui-icons_38667f_256x240.png);
}
.ui-state-hover .ui-icon, .ui-state-focus .ui-icon
{
    background-image: url(images/ui-icons_38667f_256x240.png);
}
.ui-state-active .ui-icon
{
    background-image: url(images/ui-icons_38667f_256x240.png);
}
.ui-state-highlight .ui-icon
{
    background-image: url(images/ui-icons_2e83ff_256x240.png);
}
.ui-state-error .ui-icon, .ui-state-error-text .ui-icon
{
    background-image: url(images/ui-icons_cd0a0a_256x240.png);
}

/* positioning */
.ui-icon-carat-1-n
{
    background-position: 0 0;
}
.ui-icon-carat-1-ne
{
    background-position: -16px 0;
}
.ui-icon-carat-1-e
{
    background-position: -32px 0;
}
.ui-icon-carat-1-se
{
    background-position: -48px 0;
}
.ui-icon-carat-1-s
{
    background-position: -64px 0;
}
.ui-icon-carat-1-sw
{
    background-position: -80px 0;
}
.ui-icon-carat-1-w
{
    background-position: -96px 0;
}
.ui-icon-carat-1-nw
{
    background-position: -112px 0;
}
.ui-icon-carat-2-n-s
{
    background-position: -128px 0;
}
.ui-icon-carat-2-e-w
{
    background-position: -144px 0;
}
.ui-icon-triangle-1-n
{
    background-position: 0 -16px;
}
.ui-icon-triangle-1-ne
{
    background-position: -16px -16px;
}
.ui-icon-triangle-1-e
{
    background-position: -32px -16px;
}
.ui-icon-triangle-1-se
{
    background-position: -48px -16px;
}
.ui-icon-triangle-1-s
{
    background-position: -64px -16px;
}
.ui-icon-triangle-1-sw
{
    background-position: -80px -16px;
}
.ui-icon-triangle-1-w
{
    background-position: -96px -16px;
}
.ui-icon-triangle-1-nw
{
    background-position: -112px -16px;
}
.ui-icon-triangle-2-n-s
{
    background-position: -128px -16px;
}
.ui-icon-triangle-2-e-w
{
    background-position: -144px -16px;
}
.ui-icon-arrow-1-n
{
    background-position: 0 -32px;
}
.ui-icon-arrow-1-ne
{
    background-position: -16px -32px;
}
.ui-icon-arrow-1-e
{
    background-position: -32px -32px;
}
.ui-icon-arrow-1-se
{
    background-position: -48px -32px;
}
.ui-icon-arrow-1-s
{
    background-position: -64px -32px;
}
.ui-icon-arrow-1-sw
{
    background-position: -80px -32px;
}
.ui-icon-arrow-1-w
{
    background-position: -96px -32px;
}
.ui-icon-arrow-1-nw
{
    background-position: -112px -32px;
}
.ui-icon-arrow-2-n-s
{
    background-position: -128px -32px;
}
.ui-icon-arrow-2-ne-sw
{
    background-position: -144px -32px;
}
.ui-icon-arrow-2-e-w
{
    background-position: -160px -32px;
}
.ui-icon-arrow-2-se-nw
{
    background-position: -176px -32px;
}
.ui-icon-arrowstop-1-n
{
    background-position: -192px -32px;
}
.ui-icon-arrowstop-1-e
{
    background-position: -208px -32px;
}
.ui-icon-arrowstop-1-s
{
    background-position: -224px -32px;
}
.ui-icon-arrowstop-1-w
{
    background-position: -240px -32px;
}
.ui-icon-arrowthick-1-n
{
    background-position: 0 -48px;
}
.ui-icon-arrowthick-1-ne
{
    background-position: -16px -48px;
}
.ui-icon-arrowthick-1-e
{
    background-position: -32px -48px;
}
.ui-icon-arrowthick-1-se
{
    background-position: -48px -48px;
}
.ui-icon-arrowthick-1-s
{
    background-position: -64px -48px;
}
.ui-icon-arrowthick-1-sw
{
    background-position: -80px -48px;
}
.ui-icon-arrowthick-1-w
{
    background-position: -96px -48px;
}
.ui-icon-arrowthick-1-nw
{
    background-position: -112px -48px;
}
.ui-icon-arrowthick-2-n-s
{
    background-position: -128px -48px;
}
.ui-icon-arrowthick-2-ne-sw
{
    background-position: -144px -48px;
}
.ui-icon-arrowthick-2-e-w
{
    background-position: -160px -48px;
}
.ui-icon-arrowthick-2-se-nw
{
    background-position: -176px -48px;
}
.ui-icon-arrowthickstop-1-n
{
    background-position: -192px -48px;
}
.ui-icon-arrowthickstop-1-e
{
    background-position: -208px -48px;
}
.ui-icon-arrowthickstop-1-s
{
    background-position: -224px -48px;
}
.ui-icon-arrowthickstop-1-w
{
    background-position: -240px -48px;
}
.ui-icon-arrowreturnthick-1-w
{
    background-position: 0 -64px;
}
.ui-icon-arrowreturnthick-1-n
{
    background-position: -16px -64px;
}
.ui-icon-arrowreturnthick-1-e
{
    background-position: -32px -64px;
}
.ui-icon-arrowreturnthick-1-s
{
    background-position: -48px -64px;
}
.ui-icon-arrowreturn-1-w
{
    background-position: -64px -64px;
}
.ui-icon-arrowreturn-1-n
{
    background-position: -80px -64px;
}
.ui-icon-arrowreturn-1-e
{
    background-position: -96px -64px;
}
.ui-icon-arrowreturn-1-s
{
    background-position: -112px -64px;
}
.ui-icon-arrowrefresh-1-w
{
    background-position: -128px -64px;
}
.ui-icon-arrowrefresh-1-n
{
    background-position: -144px -64px;
}
.ui-icon-arrowrefresh-1-e
{
    background-position: -160px -64px;
}
.ui-icon-arrowrefresh-1-s
{
    background-position: -176px -64px;
}
.ui-icon-arrow-4
{
    background-position: 0 -80px;
}
.ui-icon-arrow-4-diag
{
    background-position: -16px -80px;
}
.ui-icon-extlink
{
    background-position: -32px -80px;
}
.ui-icon-newwin
{
    background-position: -48px -80px;
}
.ui-icon-refresh
{
    background-position: -64px -80px;
}
.ui-icon-shuffle
{
    background-position: -80px -80px;
}
.ui-icon-transfer-e-w
{
    background-position: -96px -80px;
}
.ui-icon-transferthick-e-w
{
    background-position: -112px -80px;
}
.ui-icon-folder-collapsed
{
    background-position: 0 -96px;
}
.ui-icon-folder-open
{
    background-position: -16px -96px;
}
.ui-icon-document
{
    background-position: -32px -96px;
}
.ui-icon-document-b
{
    background-position: -48px -96px;
}
.ui-icon-note
{
    background-position: -64px -96px;
}
.ui-icon-mail-closed
{
    background-position: -80px -96px;
}
.ui-icon-mail-open
{
    background-position: -96px -96px;
}
.ui-icon-suitcase
{
    background-position: -112px -96px;
}
.ui-icon-comment
{
    background-position: -128px -96px;
}
.ui-icon-person
{
    background-position: -144px -96px;
}
.ui-icon-print
{
    background-position: -160px -96px;
}
.ui-icon-trash
{
    background-position: -176px -96px;
}
.ui-icon-locked
{
    background-position: -192px -96px;
}
.ui-icon-unlocked
{
    background-position: -208px -96px;
}
.ui-icon-bookmark
{
    background-position: -224px -96px;
}
.ui-icon-tag
{
    background-position: -240px -96px;
}
.ui-icon-home
{
    background-position: 0 -112px;
}
.ui-icon-flag
{
    background-position: -16px -112px;
}
.ui-icon-calendar
{
    background-position: -32px -112px;
}
.ui-icon-cart
{
    background-position: -48px -112px;
}
.ui-icon-pencil
{
    background-position: -64px -112px;
}
.ui-icon-clock
{
    background-position: -80px -112px;
}
.ui-icon-disk
{
    background-position: -96px -112px;
}
.ui-icon-calculator
{
    background-position: -112px -112px;
}
.ui-icon-zoomin
{
    background-position: -128px -112px;
}
.ui-icon-zoomout
{
    background-position: -144px -112px;
}
.ui-icon-search
{
    background-position: -160px -112px;
}
.ui-icon-wrench
{
    background-position: -176px -112px;
}
.ui-icon-gear
{
    background-position: -192px -112px;
}
.ui-icon-heart
{
    background-position: -208px -112px;
}
.ui-icon-star
{
    background-position: -224px -112px;
}
.ui-icon-link
{
    background-position: -240px -112px;
}
.ui-icon-cancel
{
    background-position: 0 -128px;
}
.ui-icon-plus
{
    background-position: -16px -128px;
}
.ui-icon-plusthick
{
    background-position: -32px -128px;
}
.ui-icon-minus
{
    background-position: -48px -128px;
}
.ui-icon-minusthick
{
    background-position: -64px -128px;
}
.ui-icon-close
{
    background-position: -80px -128px;
}
.ui-icon-closethick
{
    background-position: -96px -128px;
}
.ui-icon-key
{
    background-position: -112px -128px;
}
.ui-icon-lightbulb
{
    background-position: -128px -128px;
}
.ui-icon-scissors
{
    background-position: -144px -128px;
}
.ui-icon-clipboard
{
    background-position: -160px -128px;
}
.ui-icon-copy
{
    background-position: -176px -128px;
}
.ui-icon-contact
{
    background-position: -192px -128px;
}
.ui-icon-image
{
    background-position: -208px -128px;
}
.ui-icon-video
{
    background-position: -224px -128px;
}
.ui-icon-script
{
    background-position: -240px -128px;
}
.ui-icon-alert
{
    background-position: 0 -144px;
}
.ui-icon-info
{
    background-position: -16px -144px;
}
.ui-icon-notice
{
    background-position: -32px -144px;
}
.ui-icon-help
{
    background-position: -48px -144px;
}
.ui-icon-check
{
    background-position: -64px -144px;
}
.ui-icon-bullet
{
    background-position: -80px -144px;
}
.ui-icon-radio-off
{
    background-position: -96px -144px;
}
.ui-icon-radio-on
{
    background-position: -112px -144px;
}
.ui-icon-pin-w
{
    background-position: -128px -144px;
}
.ui-icon-pin-s
{
    background-position: -144px -144px;
}
.ui-icon-play
{
    background-position: 0 -160px;
}
.ui-icon-pause
{
    background-position: -16px -160px;
}
.ui-icon-seek-next
{
    background-position: -32px -160px;
}
.ui-icon-seek-prev
{
    background-position: -48px -160px;
}
.ui-icon-seek-end
{
    background-position: -64px -160px;
}
.ui-icon-seek-start
{
    background-position: -80px -160px;
}
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.ui-icon-seek-first
{
    background-position: -80px -160px;
}
.ui-icon-stop
{
    background-position: -96px -160px;
}
.ui-icon-eject
{
    background-position: -112px -160px;
}
.ui-icon-volume-off
{
    background-position: -128px -160px;
}
.ui-icon-volume-on
{
    background-position: -144px -160px;
}
.ui-icon-power
{
    background-position: 0 -176px;
}
.ui-icon-signal-diag
{
    background-position: -16px -176px;
}
.ui-icon-signal
{
    background-position: -32px -176px;
}
.ui-icon-battery-0
{
    background-position: -48px -176px;
}
.ui-icon-battery-1
{
    background-position: -64px -176px;
}
.ui-icon-battery-2
{
    background-position: -80px -176px;
}
.ui-icon-battery-3
{
    background-position: -96px -176px;
}
.ui-icon-circle-plus
{
    background-position: 0 -192px;
}
.ui-icon-circle-minus
{
    background-position: -16px -192px;
}
.ui-icon-circle-close
{
    background-position: -32px -192px;
}
.ui-icon-circle-triangle-e
{
    background-position: -48px -192px;
}
.ui-icon-circle-triangle-s
{
    background-position: -64px -192px;
}
.ui-icon-circle-triangle-w
{
    background-position: -80px -192px;
}
.ui-icon-circle-triangle-n
{
    background-position: -96px -192px;
}
.ui-icon-circle-arrow-e
{
    background-position: -112px -192px;
}
.ui-icon-circle-arrow-s
{
    background-position: -128px -192px;
}
.ui-icon-circle-arrow-w
{
    background-position: -144px -192px;
}
.ui-icon-circle-arrow-n
{
    background-position: -160px -192px;
}
.ui-icon-circle-zoomin
{
    background-position: -176px -192px;
}
.ui-icon-circle-zoomout
{
    background-position: -192px -192px;
}
.ui-icon-circle-check
{
    background-position: -208px -192px;
}
.ui-icon-circlesmall-plus
{
    background-position: 0 -208px;
}
.ui-icon-circlesmall-minus
{
    background-position: -16px -208px;
}
.ui-icon-circlesmall-close
{
    background-position: -32px -208px;
}
.ui-icon-squaresmall-plus
{
    background-position: -48px -208px;
}
.ui-icon-squaresmall-minus
{
    background-position: -64px -208px;
}
.ui-icon-squaresmall-close
{
    background-position: -80px -208px;
}
.ui-icon-grip-dotted-vertical
{
    background-position: 0 -224px;
}
.ui-icon-grip-dotted-horizontal
{
    background-position: -16px -224px;
}
.ui-icon-grip-solid-vertical
{
    background-position: -32px -224px;
}
.ui-icon-grip-solid-horizontal
{
    background-position: -48px -224px;
}
.ui-icon-gripsmall-diagonal-se
{
    background-position: -64px -224px;
}
.ui-icon-grip-diagonal-se
{
    background-position: -80px -224px;
}


/* Misc visuals
----------------------------------*/

/* Corner radius */
.ui-corner-tl
{
    -moz-border-radius-topleft: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
}
.ui-corner-tr
{
    -moz-border-radius-topright: 3px;
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
}
.ui-corner-bl
{
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.ui-corner-br
{
    -moz-border-radius-bottomright: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-corner-top
{
    -moz-border-radius-topleft: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
    -moz-border-radius-topright: 3px;
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
}
.ui-corner-bottom
{
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-corner-right
{
    -moz-border-radius-topright: 3px;
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-corner-left
{
    -moz-border-radius-topleft: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.ui-corner-all
{
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.ui-round-all
{
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

/* Overlays */
.ui-widget-overlay
{
    background: #2d5972 url(images/ui-bg_flat_0_2d5972_40x100.png) 50% 50% repeat-x;
    opacity: .30;
    filter: Alpha(Opacity=30);
}
.ui-widget-shadow
{
    margin: -8px 0 0 -8px;
    padding: 8px;
    background: #4f4f4f url(images/ui-bg_flat_0_4f4f4f_40x100.png) 50% 50% repeat-x;
    opacity: .30;
    filter: Alpha(Opacity=30);
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
}


/**
 * Widget Extensions
 */
.ui-accordion .ui-accordion-header {
    margin: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.ui-accordion .ui-accordion-content {
    margin-bottom: 0;
}

.ui-dialog .ui-dialog-titlebar {
    border-top: none;
    border-right: none;
    border-left: none;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.ui-slider {
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: #cbcbcb;
    -moz-box-shadow: inset 0 2px 2px #8f8f8f;
    -webkit-box-shadow: inset 0 2px 2px #8f8f8f;
    box-shadow: inset 0 2px 2px #8f8f8f;
}
.ui-slider .ui-slider-handle {
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}
.ui-slider .ui-slider-range {
    background: #a3cae0;
    -moz-box-shadow: inset 0 2px 2px #7d9aab;
    -webkit-box-shadow: inset 0 2px 2px #7d9aab;
    box-shadow: inset 0 2px 2px #7d9aab;
}

.ui-slider-vertical {
    background: #cbcbcb;
    -moz-box-shadow: inset 0 2px 2px #8f8f8f;
    -webkit-box-shadow: inset 0 2px 2px #8f8f8f;
    box-shadow: inset 0 2px 2px #8f8f8f;
}

.ui-slider-vertical .ui-slider-range {
    background: #a3cae0;
    -moz-box-shadow: inset 2px 0 2px #7d9aab;
    -webkit-box-shadow: inset 2px 0 2px #7d9aab;
    box-shadow: inset 2px 0 2px #7d9aab;
}

.ui-slider .ui-slider-handle span {
    height: 16px !important;
    width: 16px !important;
    float: none !important;
    margin: 0 auto !important;
}

.ui-slider .ui-slider-handle {
    background: #85b2cb;
    background: #85b2cb linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #85b2cb -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #85b2cb -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*   filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)"; */
}

.ui-tabview .ui-tabview-nav li {
    border: 1px solid #a8a8a8;
}

.ui-tabview .ui-tabview-nav li.ui-tabview-selected {
    border: 1px solid #a8a8a8;
}

.ui-tabview.ui-tabview-top .ui-tabview-nav li.ui-tabview-selected {
    border-bottom: 0;
}

.ui-tabview .ui-tabview-panel {
    border-width: 0;
    padding: 1em 1.4em;
    background: none;
}

.ui-tabview .ui-tabview-nav {
    background: #e3e3e3;
    background: #e3e3e3 linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #e3e3e3 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #e3e3e3 -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)"; */
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.ui-tabview .ui-tabview-nav .ui-tabview-selected {
    background: #fff;
    border-bottom: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.ui-datepicker .ui-datepicker-prev, .ui-datepicker .ui-datepicker-next {
    cursor: pointer;
}

.ui-datepicker table {
    table-layout: fixed;
}

.ui-datepicker .ui-datepicker-calendar .ui-state-default {
    background: none;
    border: none;
    color: #5F83B9;
}

.ui-datepicker .ui-datepicker-calendar .ui-state-hover {
    color: #1C4257;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.ui-datepicker .ui-datepicker-current-day .ui-state-highlight, .ui-datepicker .ui-datepicker-current-day .ui-state-default {
    background: #5F83B9;
    color: #FFFFFF !important;
    font-weight: bold;
    text-shadow: 0 1px 1px #234386;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.ui-datepicker .ui-datepicker-header {
    background: #e3e3e3;
    background: #e3e3e3 linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #e3e3e3 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #e3e3e3 -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0)); /*   filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);     -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)"; */
    border-right: none;
    border-left: none;
    border-top: none;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.ui-datepicker .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-prev-hover {
    background: none;
    border: solid 1px transparent;
    -moz-box-shadow: none;
}

.ui-progressbar {
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

.ui-progressbar .ui-widget-header {
    background: #85b2cb;
    background: #85b2cb linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #85b2cb -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #85b2cb -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF);
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#DDFFFFFF, endColorstr=#00FFFFFF)";
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

/* PrimeFaces Extensions */
.ui-inputtext, .ui-widget-content .ui-inputtext, .ui-widget-header .ui-inputtext {
    background: #ffffff;
    -moz-box-shadow: inset 0 2px 2px #8f8f8f;
    -webkit-box-shadow: inset 0 2px 2px #8f8f8f;
    box-shadow: inset 0 2px 2px #8f8f8f;
    color: #313131;
}

.ui-inputtext.ui-state-focus, .ui-widget-content .ui-inputtext.ui-state-focus, .ui-widget-header .ui-inputtext.ui-state-focus {
    -moz-box-shadow: 0px 0px 5px #85b2cb, inset 0 2px 2px #8f8f8f;
    -webkit-box-shadow: 0px 0px 5px #85b2cb, inset 0 2px 2px #8f8f8f;
    box-shadow: 0px 0px 5px #85b2cb, inset 0 2px 2px #8f8f8f;
}

.ui-menu, .ui-menu .ui-menu-child {
    background: #c4c4c4 url(ui-bg_highlight-hard_80_c4c4c4_1x100.png) top repeat-x;
    background: #c4c4c4 linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    background: #c4c4c4 -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.8)), to(rgba(255,255,255,0)));
    background: #c4c4c4 -moz-linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
}

*html .ui-menu .ui-menu-list {
    background: none;
}

*html .ui-menubar .ui-menu-child {
    background: #ffffff;
}

*html .ui-menu .ui-menuitem-link {
    border-color: #c4c4c4;
    filter: chroma(color=#c4c4c4);
}

/* Validation */
.ui-inputtext.ng-dirty.ng-invalid,
p-dropdown.ng-dirty.ng-invalid > .ui-dropdown,
p-autocomplete.ng-dirty.ng-invalid > .ui-autocomplete > .ui-inputtext,
p-calendar.ng-dirty.ng-invalid > .ui-inputtext,
p-checkbox.ng-dirty.ng-invalid .ui-chkbox-box,
p-radiobutton.ng-dirty.ng-invalid .ui-radiobutton-box,
p-inputswitch.ng-dirty.ng-invalid .ui-inputswitch,
p-listbox.ng-dirty.ng-invalid .ui-inputtext,
p-multiselect.ng-dirty.ng-invalid > .ui-multiselect,
p-spinner.ng-dirty.ng-invalid > .ui-inputtext,
p-selectbutton.ng-dirty.ng-invalid .ui-button,
p-togglebutton.ng-dirty.ng-invalid .ui-button {
    border-bottom-color: #cd0a0a;
}
