/*
 * Afternoon jQueryUI theme
 * Copyright 2012 <PERSON><PERSON><PERSON> <PERSON><PERSON>
 * Licensed under Creative Commons Attribution-Share Alike 3.0
 * Build 2012-03-04 09:12:06
 */
.ui-widget {
  font-family:"Segoe UI", Verdana, Arial, sans-serif;
  font-size:13px;
}
.ui-widget .ui-widget { font-size:1em; }
.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button {
  font-family:"Segoe UI", Verdana, Arial, sans-serif;
  font-size:1em;
}
.ui-widget-content {
  border:1px solid #9d9d9d;
  color:#000000;
  background-color:#ffffff;
  background:-moz-radial-gradient(50% 75%,circle,#ffffff,#e6e6e6);
  background:-webkit-radial-gradient(50% 75%,circle,#ffffff,#e6e6e6);
  background:-o-radial-gradient(50% 75%,circle,#ffffff,#e6e6e6);
  background:-ms-radial-gradient(50% 75%,circle,#ffffff,#e6e6e6);
  background:radial-gradient(50% 75%,circle,#ffffff,#e6e6e6);
}
.ui-widget-content a, .ui-widget-header a { color:#000000; }
.ui-widget-header {
  background-color:#cceeff;
  background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#ffffff),color-stop(100%,#cceeff));
  background:-webkit-linear-gradient(#ffffff,#cceeff);
  background:-moz-linear-gradient(#ffffff,#cceeff);
  background:-ms-linear-gradient(#ffffff,#cceeff);
  background:-o-linear-gradient(#ffffff,#cceeff);
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#cceeff');
  -ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#cceeff');
  background:linear-gradient(#ffffff,#cceeff);
  outline:0;
  border:1px solid #4dc4ff;
  color:#333399;
  text-shadow:0 -1px 2px rgba(255,255,255,0.5);
  font-weight:bold;
}
.ui-state-default, .ui-widget-content
.ui-state-default, .ui-widget-header
.ui-state-default {
  background-color:#dddddd;
  background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#fdfdfd),color-stop(100%,#dddddd));
  background:-webkit-linear-gradient(#fdfdfd,#dddddd);
  background:-moz-linear-gradient(#fdfdfd,#dddddd);
  background:-ms-linear-gradient(#fdfdfd,#dddddd);
  background:-o-linear-gradient(#fdfdfd,#dddddd);
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdfdfd', endColorstr='#dddddd');
  -ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdfdfd', endColorstr='#dddddd');
  background:linear-gradient(#fdfdfd,#dddddd);
  outline:0;
  border:1px solid #9d9d9d;
  color:#000000;
  text-shadow:0 -1px 2px rgba(255,255,255,0.5);
  font-weight:bold;
}
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {
  color:#000000;
  text-decoration:none;
}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover {
  background-color:#cceeff;
  background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#ffffff),color-stop(100%,#cceeff));
  background:-webkit-linear-gradient(#ffffff,#cceeff);
  background:-moz-linear-gradient(#ffffff,#cceeff);
  background:-ms-linear-gradient(#ffffff,#cceeff);
  background:-o-linear-gradient(#ffffff,#cceeff);
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#cceeff');
  -ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#cceeff');
  background:linear-gradient(#ffffff,#cceeff);
  outline:0;
  border:1px solid #4dc4ff;
  color:#333399;
  text-shadow:0 -1px 2px rgba(255,255,255,0.5);
  font-weight:bold;
}
.ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus {
  -moz-box-shadow:0 0 8px #3399ff ;
  -webkit-box-shadow:0 0 8px #3399ff ;
  box-shadow:0 0 8px #3399ff ;
}
.ui-state-hover a, .ui-state-hover a:hover {
  color:#333399;
  text-decoration:none;
}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  background-color:#3399ff;
  background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#73b9ff),color-stop(100%,#3399ff));
  background:-webkit-linear-gradient(#73b9ff,#3399ff);
  background:-moz-linear-gradient(#73b9ff,#3399ff);
  background:-ms-linear-gradient(#73b9ff,#3399ff);
  background:-o-linear-gradient(#73b9ff,#3399ff);
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#73b9ff', endColorstr='#3399ff');
  -ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#73b9ff', endColorstr='#3399ff');
  background:linear-gradient(#73b9ff,#3399ff);
  outline:0;
  border:1px solid #0059b3;
  color:#ffffff;
  text-shadow:0 1px 1px rgba(0,0,0,0.5);
  font-weight:bold;
  -moz-box-shadow:0 0 8px #3399ff ;
  -webkit-box-shadow:0 0 8px #3399ff ;
  box-shadow:0 0 8px #3399ff ;
}
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
  color:#ffffff;
  text-decoration:none;
}
.ui-widget :active { outline:none; }
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  background-color:#cceeff;
  background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#ffffff),color-stop(100%,#cceeff));
  background:-webkit-linear-gradient(#ffffff,#cceeff);
  background:-moz-linear-gradient(#ffffff,#cceeff);
  background:-ms-linear-gradient(#ffffff,#cceeff);
  background:-o-linear-gradient(#ffffff,#cceeff);
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#cceeff');
  -ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#cceeff');
  background:linear-gradient(#ffffff,#cceeff);
  outline:0;
  border:1px solid #4dc4ff;
  color:#333399;
  text-shadow:0 -1px 2px rgba(255,255,255,0.5);
  -moz-box-shadow:0 0 8px #3399ff ;
  -webkit-box-shadow:0 0 8px #3399ff ;
  box-shadow:0 0 8px #3399ff ;
}
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a { color:#0066cc; }
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
  background-color:#ff9999;
  background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#ffd9d9),color-stop(100%,#ff9999));
  background:-webkit-linear-gradient(#ffd9d9,#ff9999);
  background:-moz-linear-gradient(#ffd9d9,#ff9999);
  background:-ms-linear-gradient(#ffd9d9,#ff9999);
  background:-o-linear-gradient(#ffd9d9,#ff9999);
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9d9', endColorstr='#ff9999');
  -ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9d9', endColorstr='#ff9999');
  background:linear-gradient(#ffd9d9,#ff9999);
  outline:0;
  border:1px solid #ff1a1a;
  color:#660000;
  text-shadow:0 -1px 2px rgba(255,255,255,0.5);
  font-weight:bold;
  border-width:2px;
  -moz-box-shadow:0 0 8px #ff1a1a ;
  -webkit-box-shadow:0 0 8px #ff1a1a ;
  box-shadow:0 0 8px #ff1a1a ;
}
.ui-state-error a, .ui-widget-content .ui-state-error a, .ui-widget-header .ui-state-error a { color:#0066cc; }
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary { font-weight:bold; }
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary {
  opacity:.7;
  filter:Alpha(Opacity=70);
  font-weight:normal;
}
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  opacity:.35;
  filter:Alpha(Opacity=35);
  cursor:default !important;
}
.ui-icon {
  display:block;
  text-indent:-99999px;
  overflow:hidden;
  background-repeat:no-repeat;
  width:16px;
  height:16px;
  background-image:url(images/ui-icons-dark.png);
  -webkit-transition:-webkit-transform 200ms ease-in-out;
  -moz-transition:-moz-transform 200ms ease-in-out;
  -o-transition:-o-transform 200ms ease-in-out;
  -ms-transition:-ms-transform 200ms ease-in-out;
}
.ui-state-active .ui-icon { background-image:url(images/ui-icons-light.png); }

.ui-icon-carat-1-n { background-position:0 0; }
.ui-icon-carat-1-ne { background-position:-16px 0; }
.ui-icon-carat-1-e { background-position:-32px 0; }
.ui-icon-carat-1-se { background-position:-48px 0; }
.ui-icon-carat-1-s { background-position:-64px 0; }
.ui-icon-carat-1-sw { background-position:-80px 0; }
.ui-icon-carat-1-w { background-position:-96px 0; }
.ui-icon-carat-1-nw { background-position:-112px 0; }
.ui-icon-carat-2-n-s { background-position:-128px 0; }
.ui-icon-carat-2-e-w { background-position:-144px 0; }
.ui-icon-triangle-1-n { background-position:0 -16px; }
.ui-icon-triangle-1-ne { background-position:-16px -16px; }
.ui-icon-triangle-1-e { background-position:-32px -16px; }
.ui-icon-triangle-1-se { background-position:-48px -16px; }
.ui-icon-triangle-1-s { background-position:-64px -16px; }
.ui-icon-triangle-1-sw { background-position:-80px -16px; }
.ui-icon-triangle-1-w { background-position:-96px -16px; }
.ui-icon-triangle-1-nw { background-position:-112px -16px; }
.ui-icon-triangle-2-n-s { background-position:-128px -16px; }
.ui-icon-triangle-2-e-w { background-position:-144px -16px; }
.ui-icon-arrow-1-n { background-position:0 -32px; }
.ui-icon-arrow-1-ne { background-position:-16px -32px; }
.ui-icon-arrow-1-e { background-position:-32px -32px; }
.ui-icon-arrow-1-se { background-position:-48px -32px; }
.ui-icon-arrow-1-s { background-position:-64px -32px; }
.ui-icon-arrow-1-sw { background-position:-80px -32px; }
.ui-icon-arrow-1-w { background-position:-96px -32px; }
.ui-icon-arrow-1-nw { background-position:-112px -32px; }
.ui-icon-arrow-2-n-s { background-position:-128px -32px; }
.ui-icon-arrow-2-ne-sw { background-position:-144px -32px; }
.ui-icon-arrow-2-e-w { background-position:-160px -32px; }
.ui-icon-arrow-2-se-nw { background-position:-176px -32px; }
.ui-icon-arrowstop-1-n { background-position:-192px -32px; }
.ui-icon-arrowstop-1-e { background-position:-208px -32px; }
.ui-icon-arrowstop-1-s { background-position:-224px -32px; }
.ui-icon-arrowstop-1-w { background-position:-240px -32px; }
.ui-icon-arrowthick-1-n { background-position:0 -48px; }
.ui-icon-arrowthick-1-ne { background-position:-16px -48px; }
.ui-icon-arrowthick-1-e { background-position:-32px -48px; }
.ui-icon-arrowthick-1-se { background-position:-48px -48px; }
.ui-icon-arrowthick-1-s { background-position:-64px -48px; }
.ui-icon-arrowthick-1-sw { background-position:-80px -48px; }
.ui-icon-arrowthick-1-w { background-position:-96px -48px; }
.ui-icon-arrowthick-1-nw { background-position:-112px -48px; }
.ui-icon-arrowthick-2-n-s { background-position:-128px -48px; }
.ui-icon-arrowthick-2-ne-sw { background-position:-144px -48px; }
.ui-icon-arrowthick-2-e-w { background-position:-160px -48px; }
.ui-icon-arrowthick-2-se-nw { background-position:-176px -48px; }
.ui-icon-arrowthickstop-1-n { background-position:-192px -48px; }
.ui-icon-arrowthickstop-1-e { background-position:-208px -48px; }
.ui-icon-arrowthickstop-1-s { background-position:-224px -48px; }
.ui-icon-arrowthickstop-1-w { background-position:-240px -48px; }
.ui-icon-arrowreturnthick-1-w { background-position:0 -64px; }
.ui-icon-arrowreturnthick-1-n { background-position:-16px -64px; }
.ui-icon-arrowreturnthick-1-e { background-position:-32px -64px; }
.ui-icon-arrowreturnthick-1-s { background-position:-48px -64px; }
.ui-icon-arrowreturn-1-w { background-position:-64px -64px; }
.ui-icon-arrowreturn-1-n { background-position:-80px -64px; }
.ui-icon-arrowreturn-1-e { background-position:-96px -64px; }
.ui-icon-arrowreturn-1-s { background-position:-112px -64px; }
.ui-icon-arrowrefresh-1-w { background-position:-128px -64px; }
.ui-icon-arrowrefresh-1-n { background-position:-144px -64px; }
.ui-icon-arrowrefresh-1-e { background-position:-160px -64px; }
.ui-icon-arrowrefresh-1-s { background-position:-176px -64px; }
.ui-icon-arrow-4 { background-position:0 -80px; }
.ui-icon-arrow-4-diag { background-position:-16px -80px; }
.ui-icon-extlink { background-position:-32px -80px; }
.ui-icon-newwin { background-position:-48px -80px; }
.ui-icon-refresh { background-position:-64px -80px; }
.ui-icon-shuffle { background-position:-80px -80px; }
.ui-icon-transfer-e-w { background-position:-96px -80px; }
.ui-icon-transferthick-e-w { background-position:-112px -80px; }
.ui-icon-folder-collapsed { background-position:0 -96px; }
.ui-icon-folder-open { background-position:-16px -96px; }
.ui-icon-document { background-position:-32px -96px; }
.ui-icon-document-b { background-position:-48px -96px; }
.ui-icon-note { background-position:-64px -96px; }
.ui-icon-mail-closed { background-position:-80px -96px; }
.ui-icon-mail-open { background-position:-96px -96px; }
.ui-icon-suitcase { background-position:-112px -96px; }
.ui-icon-comment { background-position:-128px -96px; }
.ui-icon-person { background-position:-144px -96px; }
.ui-icon-print { background-position:-160px -96px; }
.ui-icon-trash { background-position:-176px -96px; }
.ui-icon-locked { background-position:-192px -96px; }
.ui-icon-unlocked { background-position:-208px -96px; }
.ui-icon-bookmark { background-position:-224px -96px; }
.ui-icon-tag { background-position:-240px -96px; }
.ui-icon-home { background-position:0 -112px; }
.ui-icon-flag { background-position:-16px -112px; }
.ui-icon-calendar { background-position:-32px -112px; }
.ui-icon-cart { background-position:-48px -112px; }
.ui-icon-pencil { background-position:-64px -112px; }
.ui-icon-clock { background-position:-80px -112px; }
.ui-icon-disk { background-position:-96px -112px; }
.ui-icon-calculator { background-position:-112px -112px; }
.ui-icon-zoomin { background-position:-128px -112px; }
.ui-icon-zoomout { background-position:-144px -112px; }
.ui-icon-search { background-position:-160px -112px; }
.ui-icon-wrench { background-position:-176px -112px; }
.ui-icon-gear { background-position:-192px -112px; }
.ui-icon-heart { background-position:-208px -112px; }
.ui-icon-star { background-position:-224px -112px; }
.ui-icon-link { background-position:-240px -112px; }
.ui-icon-cancel { background-position:0 -128px; }
.ui-icon-plus { background-position:-16px -128px; }
.ui-icon-plusthick { background-position:-32px -128px; }
.ui-icon-minus { background-position:-48px -128px; }
.ui-icon-minusthick { background-position:-64px -128px; }
.ui-icon-close { background-position:-80px -128px; }
.ui-icon-closethick { background-position:-96px -128px; }
.ui-icon-key { background-position:-112px -128px; }
.ui-icon-lightbulb { background-position:-128px -128px; }
.ui-icon-scissors { background-position:-144px -128px; }
.ui-icon-clipboard { background-position:-160px -128px; }
.ui-icon-copy { background-position:-176px -128px; }
.ui-icon-contact { background-position:-192px -128px; }
.ui-icon-image { background-position:-208px -128px; }
.ui-icon-video { background-position:-224px -128px; }
.ui-icon-script { background-position:-240px -128px; }
.ui-icon-alert { background-position:0 -144px; }
.ui-icon-info { background-position:-16px -144px; }
.ui-icon-notice { background-position:-32px -144px; }
.ui-icon-help { background-position:-48px -144px; }
.ui-icon-check { background-position:-64px -144px; }
.ui-icon-bullet { background-position:-80px -144px; }
.ui-icon-radio-off { background-position:-96px -144px; }
.ui-icon-radio-on { background-position:-112px -144px; }
.ui-icon-pin-w { background-position:-128px -144px; }
.ui-icon-pin-s { background-position:-144px -144px; }
.ui-icon-play { background-position:0 -160px; }
.ui-icon-pause { background-position:-16px -160px; }
.ui-icon-seek-next { background-position:-32px -160px; }
.ui-icon-seek-prev { background-position:-48px -160px; }
.ui-icon-seek-end { background-position:-64px -160px; }
.ui-icon-seek-start { background-position:-80px -160px; }
.ui-icon-seek-first { background-position:-80px -160px; }
.ui-icon-stop { background-position:-96px -160px; }
.ui-icon-eject { background-position:-112px -160px; }
.ui-icon-volume-off { background-position:-128px -160px; }
.ui-icon-volume-on { background-position:-144px -160px; }
.ui-icon-power { background-position:0 -176px; }
.ui-icon-signal-diag { background-position:-16px -176px; }
.ui-icon-signal { background-position:-32px -176px; }
.ui-icon-battery-0 { background-position:-48px -176px; }
.ui-icon-battery-1 { background-position:-64px -176px; }
.ui-icon-battery-2 { background-position:-80px -176px; }
.ui-icon-battery-3 { background-position:-96px -176px; }
.ui-icon-circle-plus { background-position:0 -192px; }
.ui-icon-circle-minus { background-position:-16px -192px; }
.ui-icon-circle-close { background-position:-32px -192px; }
.ui-icon-circle-triangle-e { background-position:-48px -192px; }
.ui-icon-circle-triangle-s { background-position:-64px -192px; }
.ui-icon-circle-triangle-w { background-position:-80px -192px; }
.ui-icon-circle-triangle-n { background-position:-96px -192px; }
.ui-icon-circle-arrow-e { background-position:-112px -192px; }
.ui-icon-circle-arrow-s { background-position:-128px -192px; }
.ui-icon-circle-arrow-w { background-position:-144px -192px; }
.ui-icon-circle-arrow-n { background-position:-160px -192px; }
.ui-icon-circle-zoomin { background-position:-176px -192px; }
.ui-icon-circle-zoomout { background-position:-192px -192px; }
.ui-icon-circle-check { background-position:-208px -192px; }
.ui-icon-circlesmall-plus { background-position:0 -208px; }
.ui-icon-circlesmall-minus { background-position:-16px -208px; }
.ui-icon-circlesmall-close { background-position:-32px -208px; }
.ui-icon-squaresmall-plus { background-position:-48px -208px; }
.ui-icon-squaresmall-minus { background-position:-64px -208px; }
.ui-icon-squaresmall-close { background-position:-80px -208px; }
.ui-icon-grip-dotted-vertical { background-position:0 -224px; }
.ui-icon-grip-dotted-horizontal { background-position:-16px -224px; }
.ui-icon-grip-solid-vertical { background-position:-32px -224px; }
.ui-icon-grip-solid-horizontal { background-position:-48px -224px; }
.ui-icon-gripsmall-diagonal-se { background-position:-64px -224px; }
.ui-icon-grip-diagonal-se { background-position:-80px -224px; }

/* Corner radius */
.ui-corner-tl {
    -moz-border-radius-topleft: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
}
.ui-corner-tr {
    -moz-border-radius-topright: 3px;
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
}
.ui-corner-bl {
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.ui-corner-br {
    -moz-border-radius-bottomright: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-corner-top {
    -moz-border-radius-topleft: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
    -moz-border-radius-topright: 3px;
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
}
.ui-corner-bottom {
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-corner-right {
    -moz-border-radius-topright: 3px;
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-corner-left {
    -moz-border-radius-topleft: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.ui-corner-all {
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}

.ui-widget-overlay {
  opacity:.75;
  filter:Alpha(Opacity=75);
  background-color:#282e28;
}
.ui-widget-shadow {
  -moz-box-shadow:0 4px 8px rgba(0,0,0,0.25) ;
  -webkit-box-shadow:0 4px 8px rgba(0,0,0,0.25) ;
  box-shadow:0 4px 8px rgba(0,0,0,0.25) ;
}

/* PrimeFaces Extensions */
.ui-inputtext, .ui-widget-content .ui-inputtext, .ui-widget-header .ui-inputtext {
    background: #ffffff;
    -moz-box-shadow: inset 0 2px 2px #8f8f8f;
    -webkit-box-shadow: inset 0 2px 2px #8f8f8f;
    box-shadow: inset 0 2px 2px #8f8f8f;
    color: #222222;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}

.ui-inputtext.ui-state-focus, .ui-widget-content .ui-inputtext.ui-state-focus, .ui-widget-header .ui-inputtext.ui-state-focus {
    -moz-box-shadow: 0px 0px 5px #4dc4ff, inset 0 2px 2px #8f8f8f;
    -webkit-box-shadow: 0px 0px 5px #4dc4ff, inset 0 2px 2px #8f8f8f;
    border-color:#4dc4ff;
}

.ui-tree .ui-tree-nodes .ui-tree-node {
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}

/* Validation */
.ui-inputtext.ng-dirty.ng-invalid,
p-dropdown.ng-dirty.ng-invalid > .ui-dropdown,
p-autocomplete.ng-dirty.ng-invalid > .ui-autocomplete > .ui-inputtext,
p-calendar.ng-dirty.ng-invalid > .ui-inputtext,
p-checkbox.ng-dirty.ng-invalid .ui-chkbox-box,
p-radiobutton.ng-dirty.ng-invalid .ui-radiobutton-box,
p-inputswitch.ng-dirty.ng-invalid .ui-inputswitch,
p-listbox.ng-dirty.ng-invalid .ui-inputtext,
p-multiselect.ng-dirty.ng-invalid > .ui-multiselect,
p-spinner.ng-dirty.ng-invalid > .ui-inputtext,
p-selectbutton.ng-dirty.ng-invalid .ui-button,
p-togglebutton.ng-dirty.ng-invalid .ui-button {
    border-bottom-color: #ff9999;
}