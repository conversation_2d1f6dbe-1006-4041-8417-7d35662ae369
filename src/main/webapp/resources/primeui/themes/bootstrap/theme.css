

/*
 * jQuery UI CSS Framework 1.8.9
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 *
 * To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Lucida%20Grande,%20Lucida%20Sans,%20Arial,%20sans-serif&fwDefault=bold&fsDefault=1.1em&cornerRadius=6px&bgColorHeader=deedf7&bgTextureHeader=03_highlight_soft.png&bgImgOpacityHeader=100&borderColorHeader=aed0ea&fcHeader=222222&iconColorHeader=72a7cf&bgColorContent=f2f5f7&bgTextureContent=04_highlight_hard.png&bgImgOpacityContent=100&borderColorContent=dddddd&fcContent=362b36&iconColorContent=72a7cf&bgColorDefault=d7ebf9&bgTextureDefault=02_glass.png&bgImgOpacityDefault=80&borderColorDefault=aed0ea&fcDefault=2779aa&iconColorDefault=3d80b3&bgColorHover=e4f1fb&bgTextureHover=02_glass.png&bgImgOpacityHover=100&borderColorHover=74b2e2&fcHover=0070a3&iconColorHover=2694e8&bgColorActive=3baae3&bgTextureActive=02_glass.png&bgImgOpacityActive=50&borderColorActive=2694e8&fcActive=ffffff&iconColorActive=ffffff&bgColorHighlight=ffef8f&bgTextureHighlight=03_highlight_soft.png&bgImgOpacityHighlight=25&borderColorHighlight=f9dd34&fcHighlight=363636&iconColorHighlight=2e83ff&bgColorError=cd0a0a&bgTextureError=01_flat.png&bgImgOpacityError=15&borderColorError=cd0a0a&fcError=ffffff&iconColorError=ffffff&bgColorOverlay=eeeeee&bgTextureOverlay=08_diagonals_thick.png&bgImgOpacityOverlay=90&opacityOverlay=80&bgColorShadow=000000&bgTextureShadow=04_highlight_hard.png&bgImgOpacityShadow=70&opacityShadow=30&thicknessShadow=7px&offsetTopShadow=-7px&offsetLeftShadow=-7px&cornerRadiusShadow=8px
 */


/* Component containers
----------------------------------*/
.ui-widget { font-family: "Helvetica Neue",Helvetica,Arial,sans-serif; font-size: 1em; }
.ui-widget .ui-widget { font-size: 1em; }
.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button { font-family: "Helvetica Neue",Helvetica,Arial,sans-serif; font-size: 1em; }
.ui-widget-content { border: 1px solid #dddddd; background: #ffffff; color: #333333; }
.ui-widget-content a { color: #333333; }
.ui-widget-header { border: 1px solid #eeeeee; background: #f5f5f5; color: #333333; font-weight: bold; }
.ui-widget-header a { color: #333333; }

/* Interaction states
----------------------------------*/
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  color: #333333;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  background-color: #f5f5f5;
  *background-color: #e6e6e6;
  background-image: -ms-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
  background-image: -webkit-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: -o-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: -moz-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-repeat: repeat-x;
  border: 1px solid #cccccc;
  *border: 0;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border-color: #e6e6e6 #e6e6e6 #bfbfbf;
  border-bottom-color: #b3b3b3;
  -webkit-border-radius: 4px;
     -moz-border-radius: 4px;
          border-radius: 4px;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffffff', endColorstr='#e6e6e6', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  *zoom: 1;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
     -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover,
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active,
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  background-color: #e6e6e6;
  *background-color: #d9d9d9;
}

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  background-color: #cccccc \9;
}

.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover {
  color: #333333;
  text-decoration: none;
  background-color: #e6e6e6;
  *background-color: #d9d9d9;
  /* Buttons in IE7 don't get borders, so darken on hover */

  background-position: 0 -15px;
  -webkit-transition: background-position 0.1s linear;
     -moz-transition: background-position 0.1s linear;
      -ms-transition: background-position 0.1s linear;
       -o-transition: background-position 0.1s linear;
          transition: background-position 0.1s linear;
}

.ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  background-color: #e6e6e6;
  background-color: #d9d9d9 \9;
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
     -moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
          box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  cursor: default;
  background-color: #e6e6e6;
  background-image: none;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
     -moz-box-shadow: none;
          box-shadow: none;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border-color: #ccc;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
}

/* Interaction Cues
----------------------------------*/
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight  {
    color: #ffffff;
    text-decoration: none;
    background-color: #0088cc;
    background-color: #0081c2;
    background-position: 0px;
    background-image: -moz-linear-gradient(to bottom, #0088cc, #0077b3);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
    background-image: -webkit-linear-gradient(to bottom, #0088cc, #0077b3);
    background-image: -o-linear-gradient(to bottom, #0088cc, #0077b3);
    background-image: linear-gradient(to bottom, #0088cc, #0077b3);
    background-repeat: repeat-x;
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a,.ui-widget-header .ui-state-highlight a {
    color: #ffffff;
}

.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {border: 1px solid #eed3d7; background: #f2dede 50% 50% repeat-x; color: #b94a48; }
.ui-state-error a, .ui-widget-content .ui-state-error a, .ui-widget-header .ui-state-error a { color: #b94a48; }
.ui-state-error-text, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error-text { color: #b94a48; }
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary { font-weight: bold; }
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary,  .ui-widget-header .ui-priority-secondary { opacity: .7; filter:Alpha(Opacity=70); font-weight: normal; }
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled { opacity: .35; filter:Alpha(Opacity=35); background-image: none; }

/* Icons
----------------------------------*/

/* states and images */
.ui-icon { width: 16px; height: 16px; background-image: url(images/ui-icons_333333_256x240.png); }
.ui-widget-content .ui-icon {background-image: url(images/ui-icons_333333_256x240.png); }
.ui-widget-header .ui-icon {background-image: url(images/ui-icons_333333_256x240.png); }
.ui-state-default .ui-icon { background-image: url(images/ui-icons_333333_256x240.png); }
.ui-state-hover .ui-icon, .ui-state-focus .ui-icon {background-image: url(images/ui-icons_333333_256x240.png); }
.ui-state-active .ui-icon {background-image: url(images/ui-icons_333333_256x240.png); }
.ui-state-highlight .ui-icon {background-image: url(images/ui-icons_ffffff_256x240.png); }
.ui-state-error .ui-icon, .ui-state-error-text .ui-icon {background-image: url(images/ui-icons_333333_256x240.png); }

/* positioning */
.ui-icon-carat-1-n { background-position: 0 0; }
.ui-icon-carat-1-ne { background-position: -16px 0; }
.ui-icon-carat-1-e { background-position: -32px 0; }
.ui-icon-carat-1-se { background-position: -48px 0; }
.ui-icon-carat-1-s { background-position: -64px 0; }
.ui-icon-carat-1-sw { background-position: -80px 0; }
.ui-icon-carat-1-w { background-position: -96px 0; }
.ui-icon-carat-1-nw { background-position: -112px 0; }
.ui-icon-carat-2-n-s { background-position: -128px 0; }
.ui-icon-carat-2-e-w { background-position: -144px 0; }
.ui-icon-triangle-1-n { background-position: 0 -16px; }
.ui-icon-triangle-1-ne { background-position: -16px -16px; }
.ui-icon-triangle-1-e { background-position: -32px -16px; }
.ui-icon-triangle-1-se { background-position: -48px -16px; }
.ui-icon-triangle-1-s { background-position: -64px -16px; }
.ui-icon-triangle-1-sw { background-position: -80px -16px; }
.ui-icon-triangle-1-w { background-position: -96px -16px; }
.ui-icon-triangle-1-nw { background-position: -112px -16px; }
.ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.ui-icon-arrow-1-n { background-position: 0 -32px; }
.ui-icon-arrow-1-ne { background-position: -16px -32px; }
.ui-icon-arrow-1-e { background-position: -32px -32px; }
.ui-icon-arrow-1-se { background-position: -48px -32px; }
.ui-icon-arrow-1-s { background-position: -64px -32px; }
.ui-icon-arrow-1-sw { background-position: -80px -32px; }
.ui-icon-arrow-1-w { background-position: -96px -32px; }
.ui-icon-arrow-1-nw { background-position: -112px -32px; }
.ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.ui-icon-arrowthick-1-n { background-position: 0 -48px; }
.ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.ui-icon-arrow-4 { background-position: 0 -80px; }
.ui-icon-arrow-4-diag { background-position: -16px -80px; }
.ui-icon-extlink { background-position: -32px -80px; }
.ui-icon-newwin { background-position: -48px -80px; }
.ui-icon-refresh { background-position: -64px -80px; }
.ui-icon-shuffle { background-position: -80px -80px; }
.ui-icon-transfer-e-w { background-position: -96px -80px; }
.ui-icon-transferthick-e-w { background-position: -112px -80px; }
.ui-icon-folder-collapsed { background-position: 0 -96px; }
.ui-icon-folder-open { background-position: -16px -96px; }
.ui-icon-document { background-position: -32px -96px; }
.ui-icon-document-b { background-position: -48px -96px; }
.ui-icon-note { background-position: -64px -96px; }
.ui-icon-mail-closed { background-position: -80px -96px; }
.ui-icon-mail-open { background-position: -96px -96px; }
.ui-icon-suitcase { background-position: -112px -96px; }
.ui-icon-comment { background-position: -128px -96px; }
.ui-icon-person { background-position: -144px -96px; }
.ui-icon-print { background-position: -160px -96px; }
.ui-icon-trash { background-position: -176px -96px; }
.ui-icon-locked { background-position: -192px -96px; }
.ui-icon-unlocked { background-position: -208px -96px; }
.ui-icon-bookmark { background-position: -224px -96px; }
.ui-icon-tag { background-position: -240px -96px; }
.ui-icon-home { background-position: 0 -112px; }
.ui-icon-flag { background-position: -16px -112px; }
.ui-icon-calendar { background-position: -32px -112px; }
.ui-icon-cart { background-position: -48px -112px; }
.ui-icon-pencil { background-position: -64px -112px; }
.ui-icon-clock { background-position: -80px -112px; }
.ui-icon-disk { background-position: -96px -112px; }
.ui-icon-calculator { background-position: -112px -112px; }
.ui-icon-zoomin { background-position: -128px -112px; }
.ui-icon-zoomout { background-position: -144px -112px; }
.ui-icon-search { background-position: -160px -112px; }
.ui-icon-wrench { background-position: -176px -112px; }
.ui-icon-gear { background-position: -192px -112px; }
.ui-icon-heart { background-position: -208px -112px; }
.ui-icon-star { background-position: -224px -112px; }
.ui-icon-link { background-position: -240px -112px; }
.ui-icon-cancel { background-position: 0 -128px; }
.ui-icon-plus { background-position: -16px -128px; }
.ui-icon-plusthick { background-position: -32px -128px; }
.ui-icon-minus { background-position: -48px -128px; }
.ui-icon-minusthick { background-position: -64px -128px; }
.ui-icon-close { background-position: -80px -128px; }
.ui-icon-closethick { background-position: -96px -128px; }
.ui-icon-key { background-position: -112px -128px; }
.ui-icon-lightbulb { background-position: -128px -128px; }
.ui-icon-scissors { background-position: -144px -128px; }
.ui-icon-clipboard { background-position: -160px -128px; }
.ui-icon-copy { background-position: -176px -128px; }
.ui-icon-contact { background-position: -192px -128px; }
.ui-icon-image { background-position: -208px -128px; }
.ui-icon-video { background-position: -224px -128px; }
.ui-icon-script { background-position: -240px -128px; }
.ui-icon-alert { background-position: 0 -144px; }
.ui-icon-info { background-position: -16px -144px; }
.ui-icon-notice { background-position: -32px -144px; }
.ui-icon-help { background-position: -48px -144px; }
.ui-icon-check { background-position: -64px -144px; }
.ui-icon-bullet { background-position: -80px -144px; }
.ui-icon-radio-off { background-position: -96px -144px; }
.ui-icon-radio-on { background-position: -112px -144px; }
.ui-icon-pin-w { background-position: -128px -144px; }
.ui-icon-pin-s { background-position: -144px -144px; }
.ui-icon-play { background-position: 0 -160px; }
.ui-icon-pause { background-position: -16px -160px; }
.ui-icon-seek-next { background-position: -32px -160px; }
.ui-icon-seek-prev { background-position: -48px -160px; }
.ui-icon-seek-end { background-position: -64px -160px; }
.ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.ui-icon-seek-first { background-position: -80px -160px; }
.ui-icon-stop { background-position: -96px -160px; }
.ui-icon-eject { background-position: -112px -160px; }
.ui-icon-volume-off { background-position: -128px -160px; }
.ui-icon-volume-on { background-position: -144px -160px; }
.ui-icon-power { background-position: 0 -176px; }
.ui-icon-signal-diag { background-position: -16px -176px; }
.ui-icon-signal { background-position: -32px -176px; }
.ui-icon-battery-0 { background-position: -48px -176px; }
.ui-icon-battery-1 { background-position: -64px -176px; }
.ui-icon-battery-2 { background-position: -80px -176px; }
.ui-icon-battery-3 { background-position: -96px -176px; }
.ui-icon-circle-plus { background-position: 0 -192px; }
.ui-icon-circle-minus { background-position: -16px -192px; }
.ui-icon-circle-close { background-position: -32px -192px; }
.ui-icon-circle-triangle-e { background-position: -48px -192px; }
.ui-icon-circle-triangle-s { background-position: -64px -192px; }
.ui-icon-circle-triangle-w { background-position: -80px -192px; }
.ui-icon-circle-triangle-n { background-position: -96px -192px; }
.ui-icon-circle-arrow-e { background-position: -112px -192px; }
.ui-icon-circle-arrow-s { background-position: -128px -192px; }
.ui-icon-circle-arrow-w { background-position: -144px -192px; }
.ui-icon-circle-arrow-n { background-position: -160px -192px; }
.ui-icon-circle-zoomin { background-position: -176px -192px; }
.ui-icon-circle-zoomout { background-position: -192px -192px; }
.ui-icon-circle-check { background-position: -208px -192px; }
.ui-icon-circlesmall-plus { background-position: 0 -208px; }
.ui-icon-circlesmall-minus { background-position: -16px -208px; }
.ui-icon-circlesmall-close { background-position: -32px -208px; }
.ui-icon-squaresmall-plus { background-position: -48px -208px; }
.ui-icon-squaresmall-minus { background-position: -64px -208px; }
.ui-icon-squaresmall-close { background-position: -80px -208px; }
.ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.ui-icon-grip-diagonal-se { background-position: -80px -224px; }


/* Misc visuals
----------------------------------*/

/* Corner radius */
.ui-corner-tl { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; border-top-left-radius: 4px; }
.ui-corner-tr { -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; border-top-right-radius: 4px; }
.ui-corner-bl { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; border-bottom-left-radius: 4px; }
.ui-corner-br { -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; border-bottom-right-radius: 4px; }
.ui-corner-top { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; border-top-left-radius: 4px; -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; border-top-right-radius: 4px; }
.ui-corner-bottom { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; border-bottom-left-radius: 4px; -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; border-bottom-right-radius: 4px; }
.ui-corner-right {  -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; border-top-right-radius: 4px; -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; border-bottom-right-radius: 4px; }
.ui-corner-left { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; border-top-left-radius: 4px; -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; border-bottom-left-radius: 4px; }
.ui-corner-all { -moz-border-radius: 4px; -webkit-border-radius: 4px; border-radius: 4px; }

/* Overlays */
.ui-widget-overlay { background: #000000; opacity: .80;filter:Alpha(Opacity=80); }
.ui-widget-shadow { margin: -7px 0 0 -7px; padding: 7px; background: #000000; opacity: .30;filter:Alpha(Opacity=30); -moz-border-radius: 8px; -webkit-border-radius: 8px; border-radius: 8px; }

/* PrimeFaces Extensions */
.ui-inputtext, .ui-widget-content .ui-inputtext, .ui-widget-header .ui-inputtext {
    cursor: text;
    background: none #ffffff;
    border: 1px solid #cccccc;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
          border-radius: 3px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
       -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border linear 0.2s, box-shadow linear 0.2s;
       -moz-transition: border linear 0.2s, box-shadow linear 0.2s;
        -ms-transition: border linear 0.2s, box-shadow linear 0.2s;
         -o-transition: border linear 0.2s, box-shadow linear 0.2s;
            transition: border linear 0.2s, box-shadow linear 0.2s;
}

.ui-inputtext.ui-state-focus, .ui-widget-content .ui-inputtext.ui-state-focus, .ui-widget-header .ui-inputtext.ui-state-focus {
    border-color: rgba(82, 168, 236, 0.8);
    outline: 0;
    outline: thin dotted \9;
    /* IE6-9 */

    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
       -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}

.ui-inputtext.ui-state-error, .ui-widget-header .ui-inputtext.ui-state-error, .ui-widget-content .ui-inputtext.ui-state-error {
    color: #333333;
    border: 1px solid #eed3d7;
}

/** TabView **/
.ui-tabview.ui-widget-content {
    background: transparent none;
    border: 0;
    border-radius: 0;
}

.ui-tabview .ui-tabview-nav.ui-widget-header {
    background: transparent none;
    border-radius: 0;
}

.ui-tabview .ui-tabview-nav.ui-widget-header li {
    line-height: 18px;
    background: transparent none;
    border: 1px solid #DDDDDD;
    box-shadow: none;
    border-radius: 0;
}

.ui-tabview .ui-tabview-nav.ui-widget-header li a {
    line-height: 18px;
    padding-bottom: 8px;
    padding-top: 8px;
    color: #0088CC;
    font-weight: normal;
    border: 0px none;
}

.ui-tabview .ui-tabview-nav.ui-widget-header li.ui-state-hover {
    text-decoration: none;
    background-color: #eeeeee;
    border-color: #eeeeee #eeeeee #dddddd;
    color: #005580;
}

.ui-tabview .ui-tabview-nav.ui-widget-header li.ui-state-active {
    background-color: #FFFFFF;
    border-style: solid;
    border-width: 1px;
    color: #555555;
    cursor: default;
}

.ui-tabview .ui-tabview-nav li .ui-icon {
    margin: 0.6em 0.2em 0 0 !important;
}

.ui-tabview.ui-tabview-top .ui-tabview-nav.ui-widget-header {
    border-bottom: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-top {
    border-bottom: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-top .ui-tabview-nav.ui-widget-header li.ui-state-active {
    border-color: #DDDDDD #DDDDDD transparent #DDDDDD;
}

.ui-tabview.ui-tabview-top .ui-tabview-nav.ui-widget-header li {
    border-radius: 4px 4px 0 0;
}

.ui-tabview.ui-tabview-top .ui-tabview-nav.ui-widget-header li.ui-state-active {
    margin-bottom: -1px;
}

.ui-tabview.ui-tabview-bottom .ui-tabview-nav.ui-widget-header {
    border-top: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-bottom {
    border-top: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-bottom .ui-tabview-nav.ui-widget-header li.ui-state-active {
    border-color: transparent #DDDDDD #DDDDDD #DDDDDD;
}

.ui-tabview.ui-tabview-bottom .ui-tabview-nav.ui-widget-header li {
    border-radius: 0 0 4px 4px;
}

.ui-tabview.ui-tabview-bottom .ui-tabview-nav.ui-widget-header li.ui-state-active {
    margin-top: -1px;
}

.ui-tabview.ui-tabview-left .ui-tabview-nav.ui-widget-header {
    border-right: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-left {
    border-right: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-left .ui-tabview-nav.ui-widget-header li.ui-state-active {
    border-color: #DDDDDD transparent #DDDDDD #DDDDDD;
}

.ui-tabview.ui-tabview-left .ui-tabview-nav.ui-widget-header li {
    border-radius: 4px 0 0 4px;
}

.ui-tabview.ui-tabview-left .ui-tabview-nav.ui-widget-header li.ui-state-active {
    margin-right: -1px;
}

.ui-tabview.ui-tabview-right .ui-tabview-nav.ui-widget-header {
    border-left: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-right {
    border-left: 1px solid #DDDDDD;
}

.ui-tabview.ui-tabview-right .ui-tabview-nav.ui-widget-header li.ui-state-active {
    border-color: #DDDDDD #DDDDDD #DDDDDD transparent;
}

.ui-tabview.ui-tabview-right .ui-tabview-nav.ui-widget-header li {
    border-radius: 0 4px 4px 0;
}

.ui-tabview.ui-tabview-right .ui-tabview-nav.ui-widget-header li.ui-state-active {
    margin-left: -1px;
}

/** AccordionPanel **/
.ui-accordion .ui-accordion-header {
    background:transparent;
    box-shadow: none;
}

.ui-accordion .ui-accordion-header.ui-state-default {
    margin-bottom: 2px;
    border: 1px solid #E5E5E5;
}

.ui-accordion .ui-accordion-header.ui-state-active {
    margin-bottom: 1px;
}

.ui-accordion .ui-accordion-header .ui-icon {
    display: none;
}

.ui-accordion .ui-accordion-header.ui-state-default a {
    padding: 8px 30px;
    color: #0088CC;
}

.ui-accordion .ui-accordion-header a:hover {
    color: #005580;
}

.ui-accordion .ui-accordion-content.ui-widget-content {
    padding: 9px 15px;
}

/* Breadcrumb */
.ui-breadcrumb {
  padding: 7px 14px;
  margin: 0 0 18px;
  list-style: none;
  background-color: #fbfbfb;
  background-image: -moz-linear-gradient(to bottom, #ffffff, #f5f5f5);
  background-image: -ms-linear-gradient(to bottom, #ffffff, #f5f5f5);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f5f5f5));
  background-image: -webkit-linear-gradient(to bottom, #ffffff, #f5f5f5);
  background-image: -o-linear-gradient(to bottom, #ffffff, #f5f5f5);
  background-image: linear-gradient(to bottom, #ffffff, #f5f5f5);
  background-repeat: repeat-x;
  border: 1px solid #ddd;
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffffff', endColorstr='#f5f5f5', GradientType=0);
  -webkit-box-shadow: inset 0 1px 0 #ffffff;
     -moz-box-shadow: inset 0 1px 0 #ffffff;
          box-shadow: inset 0 1px 0 #ffffff;
}

.ui-breadcrumb li {
  display: inline-block;
  *display: inline;
  text-shadow: 0 1px 0 #ffffff;
  *zoom: 1;
}

.ui-breadcrumb .divider {
  padding: 0 5px;
  color: #999999;
}

.ui-breadcrumb a {
  color: #0088CC;
  font-weight: normal;
}

/* ProgressBar */
.ui-progressbar.ui-widget-content {
  height: 18px;
  margin-bottom: 18px;
  overflow: hidden;
  background-color: #f7f7f7;
  background-image: -moz-linear-gradient(to bottom, #f5f5f5, #f9f9f9);
  background-image: -ms-linear-gradient(to bottom, #f5f5f5, #f9f9f9);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f5f5f5), to(#f9f9f9));
  background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #f9f9f9);
  background-image: -o-linear-gradient(to bottom, #f5f5f5, #f9f9f9);
  background-image: linear-gradient(to bottom, #f5f5f5, #f9f9f9);
  background-repeat: repeat-x;
  -webkit-border-radius: 4px;
     -moz-border-radius: 4px;
          border-radius: 4px;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#f5f5f5', endColorstr='#f9f9f9', GradientType=0);
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
     -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ui-progressbar .ui-progressbar-value.ui-widget-header {
  height: 18px;
  font-size: 12px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  border: 0 none;
  margin: 0;
  background-color: #0e90d2;
  background-image: -moz-linear-gradient(to bottom, #149bdf, #0480be);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#149bdf), to(#0480be));
  background-image: -webkit-linear-gradient(to bottom, #149bdf, #0480be);
  background-image: -o-linear-gradient(to bottom, #149bdf, #0480be);
  background-image: linear-gradient(to bottom, #149bdf, #0480be);
  background-image: -ms-linear-gradient(to bottom, #149bdf, #0480be);
  background-repeat: repeat-x;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#149bdf', endColorstr='#0480be', GradientType=0);
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
     -moz-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
          box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
      -ms-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-transition: width 0.6s ease;
     -moz-transition: width 0.6s ease;
      -ms-transition: width 0.6s ease;
       -o-transition: width 0.6s ease;
          transition: width 0.6s ease;
}

/** Dialog **/
.ui-dialog.ui-widget-content {
    background-color: #ffffff;
    border: 1px solid #999;
    border: 1px solid rgba(0, 0, 0, 0.3);
    *border: 1px solid #999;
    -webkit-border-radius: 6px;
       -moz-border-radius: 6px;
            border-radius: 6px;
    -webkit-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
       -moz-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
            box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
    -webkit-background-clip: padding-box;
       -moz-background-clip: padding-box;
            background-clip: padding-box;
}

.ui-dialog.ui-widget-content .ui-dialog-titlebar {
    padding: 9px 15px;
    border-bottom: 1px solid #eee;
    background-color: #ffffff;
}

.ui-dialog.ui-widget-content .ui-dialog-title {
    font-size: 24px;
}

.ui-dialog.ui-widget-content .ui-dialog-content {
    padding: 15px;
    font-size: 14px;
}

.ui-dialog.ui-widget-content .ui-dialog-titlebar-close:hover,
.ui-dialog.ui-widget-content .ui-dialog-titlebar-close:focus,
.ui-dialog.ui-widget-content .ui-dialog-titlebar-minimize:hover,
.ui-dialog.ui-widget-content .ui-dialog-titlebar-minimize:focus,
.ui-dialog.ui-widget-content .ui-dialog-titlebar-maximize:hover,
.ui-dialog.ui-widget-content .ui-dialog-titlebar-maximize:focus   { padding: 1px; }

/** DataTable **/
.ui-datatable-odd {
    background-color: #F9F9F9
}
/** TabMenu **/
.ui-tabmenu {
  *position: relative;
  *z-index: 2;
  margin-bottom: 20px;
  overflow: visible;
  color: #555555;
}

.ui-tabmenu .ui-tabmenu-nav.ui-widget-header {
  min-height: 38px;
  padding: 0 20px;
  background-color: #f5f5f5;
  *background-color: #e6e6e6;
  background-image: -ms-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
  background-image: -webkit-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: -o-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-image: -moz-linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-repeat: repeat-x;
}

.ui-tabmenu .ui-tabmenu-nav .ui-tabmenuitem.ui-state-default  {
  padding: 5px 10px;
  color: #555555;
  text-decoration: none;
  text-shadow: 0 1px 0 #ffffff;
  border: 0 none;
  top: 0;
  margin: 0;
  font-weight: normal;
  -webkit-border-radius: 0px;
       -moz-border-radius: 0px;
            border-radius: 0px;
}

/** Toolbar **/
.ui-toolbar.ui-widget-header {
  background-color: #fafafa;
  background-image: -moz-linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f2f2f2));
  background-image: -webkit-linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-image: -o-linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-repeat: repeat-x;
  border: 1px solid #d4d4d4;
}

/** **/
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #006dcc;
  *background-color: #0044cc;
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(to bottom, #0088cc, #0044cc);
  background-image: -o-linear-gradient(to bottom, #0088cc, #0044cc);
  background-image: linear-gradient(to bottom, #0088cc, #0044cc);
  background-image: -moz-linear-gradient(to bottom, #0088cc, #0044cc);
  background-repeat: repeat-x;
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0044cc', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
}

.ui-priority-primary:hover,
.ui-priority-primary:active,
.ui-priority-primary.active,
.ui-priority-primary.disabled,
.ui-priority-primary[disabled] {
  color: #ffffff;
  background-color: #0044cc;
  *background-color: #003bb3;
}

.ui-priority-primary:active,
.ui-priority-primary.active {
  background-color: #003399 \9;
}

/** Menu **/
.ui-menu.ui-widget {
  min-width: 160px;
  padding: 5px 0;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  *border-right-width: 2px;
  *border-bottom-width: 2px;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
  -webkit-background-clip: padding-box;
     -moz-background-clip: padding;
          background-clip: padding-box;
}

.ui-menu.ui-widget a {
  display: block;
  clear: both;
  font-weight: normal;
  color: #333333;
  white-space: nowrap;
}

.ui-menu.ui-widget li.ui-corner-all,
.ui-menu.ui-widget a.ui-corner-all {
    -webkit-border-radius: 0;
     -moz-border-radius: 0;
          border-radius: 0;
}

.ui-menu.ui-widget .ui-menu-list {

}

.ui-menu.ui-widget li > .ui-menuitem-link {
    padding: 0.3em 0;
    width: 100%;
    border-width: 1px 0px;
}

.ui-menu.ui-widget .ui-menu-list .ui-widget-header {
    border-width: 1px 0px;
    width: 100%;
}

.ui-menu.ui-widget li > .ui-menuitem-link.ui-state-hover {
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
  background-color: #0081c2;
  background-position: 0px;
  background-image: -moz-linear-gradient(to bottom, #0088cc, #0077b3);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
  background-image: -webkit-linear-gradient(to bottom, #0088cc, #0077b3);
  background-image: -o-linear-gradient(to bottom, #0088cc, #0077b3);
  background-image: linear-gradient(to bottom, #0088cc, #0077b3);
  background-repeat: repeat-x;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}

.ui-menu.ui-widget li > .ui-menuitem-link.ui-state-hover .ui-icon {
    background-image: url(images/ui-icons_ffffff_256x240.png);
}

.ui-menu.ui-widget .ui-separator {
  height: 1px;
  overflow: hidden;
  background-color: #e5e5e5;
  border:0 none;
  border-bottom: 1px solid #ffffff;
-webkit-box-shadow: none;
-moz-box-shadow: none;
     box-shadow: none;
-webkit-border-radius: 0;
 -moz-border-radius: 0;
      border-radius: 0;
}

.ui-menu.ui-widget .ui-menuitem-link .ui-menuitem-icon {
    margin-left: 0.3em;
}
.ui-menu.ui-widget .ui-menuitem-link .ui-menuitem-text {
    margin-left: 0.4em;
}

.ui-shadow {
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2) !important;
     -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2) !important;
          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2) !important;
}

.ui-menu.ui-slidemenu .ui-shadow {
    -webkit-box-shadow: none !important;
     -moz-box-shadow: none !important;
          box-shadow: none !important;
}

.ui-buttonset .ui-state-default {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
      border-radius: 0;
}

.ui-buttonset .ui-corner-left.ui-state-default {
    -moz-border-radius-topleft: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.ui-buttonset .ui-corner-right.ui-state-default {
    -moz-border-radius-topright: 4px;
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

/** MenuBar **/
.ui-menu.ui-menubar {
  padding-right: 20px;
  padding-left: 10px;
  background-color: #fafafa;
  background-image: -moz-linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f2f2f2));
  background-image: -webkit-linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-image: -o-linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-repeat: repeat-x;
  border: 1px solid #d4d4d4;
  -webkit-border-radius: 4px;
     -moz-border-radius: 4px;
          border-radius: 4px;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff2f2f2', GradientType=0);
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
     -moz-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
}

.ui-menu.ui-menubar .ui-menu-parent .ui-menu-child {
      padding: 5px 0;
}

/** Panel **/
.ui-panel.ui-widget {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
    box-shadow: 0 1px 1px rgba(0,0,0,.05);
    padding: 0;
    border-radius: 3px;
    border-color: #ddd;
}

.ui-panel .ui-panel-titlebar.ui-widget-header {
    padding: 10px 15px;
    border: 0px none;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    color: #333;
    background-color: #f5f5f5;
    border-color: #ddd;
    border-radius: 0px;
}

.ui-panel .ui-panel-content.ui-widget-content {
    padding: 15px;
}

/* Validation */
.ui-inputtext.ng-dirty.ng-invalid,
p-dropdown.ng-dirty.ng-invalid > .ui-dropdown,
p-autocomplete.ng-dirty.ng-invalid > .ui-autocomplete > .ui-inputtext,
p-calendar.ng-dirty.ng-invalid > .ui-inputtext,
p-checkbox.ng-dirty.ng-invalid .ui-chkbox-box,
p-radiobutton.ng-dirty.ng-invalid .ui-radiobutton-box,
p-inputswitch.ng-dirty.ng-invalid .ui-inputswitch,
p-listbox.ng-dirty.ng-invalid .ui-inputtext,
p-multiselect.ng-dirty.ng-invalid > .ui-multiselect,
p-spinner.ng-dirty.ng-invalid > .ui-inputtext,
p-selectbutton.ng-dirty.ng-invalid .ui-button,
p-togglebutton.ng-dirty.ng-invalid .ui-button {
    border-bottom-color: #eed3d7;
}
