.ui-autocomplete {
    width: auto;
    zoom: 1;
    cursor: pointer;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    position: relative;
    display: inline-block;
}

.ui-autocomplete .ui-autocomplete-dropdown {
    position: absolute;
    height: 100%;
    width: 2.4em;
}

.ui-autocomplete-query {
    font-weight: bold;
}

.ui-autocomplete-panel {
    position: absolute;
    overflow: auto;
}

.ui-autocomplete-panel .ui-autocomplete-list {
    padding: 0.4em;
    border: 0 none;
}

.ui-autocomplete-panel .ui-autocomplete-list-item {
    border:0px none;
    cursor:pointer;
    font-weight:normal;
    margin:1px 0;
    padding:3px 5px;
    text-align:left;
}

.ui-autocomplete-panel .ui-autocomplete-table {
    border-collapse:collapse;
    width: 100%;
}

.ui-autocomplete-panel .ui-autocomplete-table td {
    border-width: 1px;
    border-style: solid;
    border-color: inherit;
    padding:4px 10px 4px 10px;
}

.ui-autocomplete .ui-button-icon-only .ui-button-text {
    padding: 0.29em;
}

/* Multiple Selection */
.ui-autocomplete-multiple {
    clear: left;
    cursor: text;
    list-style-type: none;
    margin: 0;
    min-height: 1px;
    overflow: hidden;
}

.ui-autocomplete-multiple-container.ui-inputfield {
    padding:0;
}

.ui-autocomplete-token {
    cursor: default;
    float: left;
    overflow: hidden;
    padding: 1px 3px;
    white-space: nowrap;
    position: relative;
    margin:2px;
}

.ui-autocomplete-token-label {
    display: block;
    padding: 0em 2.1em 0em 0.5em;
}

.ui-autocomplete-token-icon {
    margin-top: -9px;
    position: absolute;
    right: 0.2em;
    top: 50%;
    cursor: pointer;
}

.ui-autocomplete-input-token {
    float: left;
    list-style-type: none;
    margin: 2px;
    padding: 1px;
}

.ui-autocomplete-input-token .ui-inputtext {
    border: 0 none;
    width: 140px;
    outline: medium none;
    background-color: transparent;
    margin: 0;
    padding: 0;
    box-shadow: none;
}

.ui-autocomplete-itemtip-content {
    display:none;
}

.ui-autocomplete-itemtip {
    position: absolute;
    display: none;
    padding: 0.4em;
}

.ui-autocomplete-emptyMessage {
    padding:3px 5px;
}

.ui-autocomplete-panel .ui-autocomplete-group {
    font-weight: bold;
    cursor: default;
}

/* ng */
.ui-autocomplete-dd input.ui-corner-all {
    -moz-border-radius-topright: 0px; 
    -webkit-border-top-right-radius: 0px;
     border-top-right-radius: 0px;
     -moz-border-radius-bottomright: 0px;
     -webkit-border-bottom-right-radius: 0px;
     border-bottom-right-radius: 0px;
 }
 
.ui-autocomplete-dd .ui-autocomplete-dropdown.ui-corner-all {
     -moz-border-radius-topleft: 0px; 
     -webkit-border-top-left-radius: 0px;
      border-top-left-radius: 0px;
      -moz-border-radius-bottomleft: 0px;
      -webkit-border-bottom-left-radius: 0px;
      border-bottom-left-radius: 0px;
}

/** AutoComplete **/
.ui-fluid .ui-autocomplete,
.ui-fluid .ui-autocomplete-input {
    width: 100%;
}

.ui-fluid .ui-autocomplete .ui-autocomplete-dropdown.ui-button {
    width: 2.4em;
}