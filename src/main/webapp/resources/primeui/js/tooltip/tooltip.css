.ui-tooltip {
    position:absolute;
    display:none;
    padding:3px 5px;
}

.ui-tooltip.ui-tooltip-right,
.ui-tooltip.ui-tooltip-left {
    padding: 0 5px;
}

.ui-tooltip.ui-tooltip-top,
.ui-tooltip.ui-tooltip-bottom {
    padding: 5px 0px;
}

.ui-tooltip .ui-tooltip-text {
   padding: 3px 10px;
   background-color: rgb(76, 76, 76);
   color: #ffffff;
}

.ui-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.ui-tooltip-right .ui-tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: rgb(76, 76, 76);
}

.ui-tooltip-left .ui-tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: rgb(76, 76, 76);
}

.ui-tooltip.ui-tooltip-top {
    padding: 5px 0;
}

.ui-tooltip-top .ui-tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: rgb(76, 76, 76);
}

.ui-tooltip-bottom .ui-tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: rgb(76, 76, 76);
}