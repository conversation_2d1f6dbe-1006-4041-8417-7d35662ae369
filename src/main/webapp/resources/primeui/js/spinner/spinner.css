.ui-spinner {
    display: inline-block;
    overflow: visible;
    padding: 0;
    position: relative;
    vertical-align: middle;
}
.ui-spinner-input {
    vertical-align: middle;
    text-align: right;
    padding-right: 20px;
}
.ui-spinner-button {
    cursor: default;
    display: block;
    font-size: 0.5em;
    height: 50%;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    right: 0;
    text-align: center;
    vertical-align: middle;
    width: 16px;
    z-index: 100;
}
.ui-spinner .fa {
    left: -1px;
    margin-top: -8px;
    position: absolute;
    top: 50%;
}
.ui-spinner-up {
    top: 0;
}
.ui-spinner-down {
    bottom: 0;
}

/* Fluid */
.ui-fluid .ui-spinner {
    width: 100%;
}
.ui-fluid .ui-spinner .ui-spinner-input {
    padding-right: 36px;
    width: 100%;
}
.ui-fluid .ui-spinner .ui-spinner-button {
    width: auto;
}
.ui-fluid .ui-spinner .ui-spinner-button .fa {
    left: 6px;
}