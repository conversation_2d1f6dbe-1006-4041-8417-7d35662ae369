.ui-datatable table {
	border-collapse:collapse;
    width: 100%;
    table-layout: fixed;
}

.ui-datatable .ui-datatable-header,
.ui-datatable .ui-datatable-caption,
.ui-datatable .ui-datatable-footer {
    text-align: center;
    padding: 4px 10px;
    box-sizing: border-box;
}

.ui-datatable .ui-datatable-caption,
.ui-datatable .ui-datatable-header {
    border-bottom: 0px none;
}

.ui-datatable .ui-datatable-footer {
    border-top: 0px none;
}

.ui-datatable thead th, .ui-datatable tfoot td {
    text-align: center;
}

.ui-datatable thead th,
.ui-datatable tbody td,
.ui-datatable tfoot td,
.ui-datatable tfoot th{
    padding: 4px 10px;
    overflow: hidden;
    border-width: 1px;
    border-style: solid;
}

.ui-datatable thead tr {
    border-width: 0px;
}

.ui-datatable thead th {
    border-color: inherit;
    box-sizing: border-box;
}

.ui-datatable tbody {
    outline: 0;
}

.ui-datatable tbody td {
    border-color: inherit;
}

.ui-datatable .ui-sortable-column {
    cursor: pointer;
}

.ui-datatable .ui-sortable-column-icon {
    display: inline-block;
    margin: -3px 0px -3px 2px;
}

.ui-datatable tr.ui-state-highlight {
    cursor: pointer;
}

/* Scrollable */
.ui-datatable-scrollable-body {
    overflow:auto;
}
.ui-datatable-scrollable-header {
    overflow: hidden;
    border: 0px none;
}

.ui-datatable-scrollable .ui-datatable-scrollable-header {
    position: relative;
}

.ui-datatable-scrollable .ui-datatable-scrollable-header td {
    font-weight: normal;
}

.ui-datatable .ui-datatable-scrollable-body  {
    min-height: 0%;
}

.ui-datatable .ui-datatable-data tr.ui-state-hover {
    border-color: inherit;
    font-weight: inherit;
    cursor: pointer;
}

.ui-datatable-scrollable-theadclone {
    height:0px;
}

.ui-datatable-scrollable-theadclone tr {
    height:0px;
}

.ui-datatable-scrollable-theadclone th.ui-state-default {
    height:0px;
    border-bottom-width: 0px;
    border-top-width: 0px;
    padding-top: 0px;
    padding-bottom: 0px;
    outline: 0 none;
}

.ui-datatable-scrollable-theadclone th span.ui-column-title {
  display: block;
  height: 0px;
}

.ui-datatable .ui-paginator {
    padding: 2px;
    border-top: 0 none;
}

.ui-datatable-rtl {
    direction: rtl;
}

.ui-datatable-rtl.ui-datatable thead th,
.ui-datatable-rtl.ui-datatable tfoot td {
    text-align: right;
}

/* Row Toggler */
.ui-row-toggler {
    cursor: pointer;
}

/* Resizable */
.ui-datatable .ui-column-resizer {
    display: block;
    position: absolute !important;
    top: 0px;
    right: 0px;
    margin: 0;
    width:8px;
    height:100%;
    padding:0px;
    cursor:col-resize;
    border: 1px solid transparent;
}

.ui-datatable .ui-column-resizer-helper {
    width: 1px;
    position: absolute;
    z-index: 10;
    display: none;
}

.ui-datatable-resizable {
    padding-bottom:1px;     /*fix for webkit overlow*/
    overflow:auto;
}

.ui-datatable-resizable thead th,
.ui-datatable-resizable tbody td,
.ui-datatable-resizable tfoot td {
    white-space: nowrap;
}

.ui-datatable-resizable th.ui-resizable-column {
    background-clip: padding-box;
    position: relative;
}

/** Reflow **/
.ui-datatable-reflow .ui-datatable-data td .ui-column-title {
    display: none;
}

/* Filter */
.ui-datatable .ui-column-filter {
    display: block;
    width: 100%;
    box-sizing: border-box;
    margin-top: 4px;
}

/* Editing */
.ui-datatable td.ui-cell-editing {
    padding: 0px;
}

.ui-datatable td.ui-cell-editing input {
    box-sizing: border-box;
    width: 100%;
    border: 0px none;
    outline: 0;
}

.ui-datatable .ui-editable-column .ui-cell-editor {
    display: none;
}

.ui-datatable .ui-editable-column.ui-cell-editing .ui-cell-editor {
    display: inline;
}

.ui-datatable .ui-editable-column.ui-cell-editing .ui-cell-data {
    display: none;
}

@media ( max-width: 35em ) {
    .ui-datatable-reflow thead th,
    .ui-datatable-reflow tfoot td {
        display: none !important;
    }

    .ui-datatable-reflow .ui-datatable-data td {
        text-align: left;
        display: block;
        border: 0px none;
        width: 100%;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		float: left;
		clear: left;
    }

    .ui-datatable-reflow .ui-datatable-data.ui-widget-content {
        border: 0px none;
    }

    .ui-datatable-reflow .ui-datatable-data tr.ui-widget-content {
        border-left: 0px none;
        border-right: 0px none;
    }

    .ui-datatable-reflow .ui-datatable-data td .ui-column-title {
        padding: .4em;
        min-width: 30%;
        display: inline-block;
        margin: -.4em 1em -.4em -.4em;
        font-weight: bold;
    }
}

.ui-datatable-stacked thead th,
.ui-datatable-stacked tfoot td {
    display: none !important;
}

.ui-datatable-stacked .ui-datatable-data td {
    text-align: left;
    display: block;
    border: 0px none;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    float: left;
    clear: left;
}

.ui-datatable-stacked .ui-datatable-data.ui-widget-content {
    border: 0px none;
}

.ui-datatable-stacked .ui-datatable-data tr.ui-widget-content {
    border-left: 0px none;
    border-right: 0px none;
}

.ui-datatable-stacked .ui-datatable-data td .ui-column-title {
    padding: .4em;
    min-width: 30%;
    display: inline-block;
    margin: -.4em 1em -.4em -.4em;
    font-weight: bold;
}

.ui-datatable .ui-selection-column .ui-chkbox,
.ui-datatable .ui-selection-column .ui-radiobutton {
     margin: 0;
     display: block;
}

.ui-datatable .ui-selection-column .ui-chkbox-box,
.ui-datatable .ui-selection-column .ui-radiobutton-box {
    display: block;
    box-sizing: border-box;
    height: 17px;
    width: 17px;
}
