.ui-galleria { 
    overflow: hidden; 
    visibility: hidden; 
    position: relative;
}

.ui-galleria-panel-wrapper {
    position: relative;
    padding: 0px;
    margin: 0px;
}

.ui-galleria-panel {
    filter: inherit;
    position: absolute;
    top: 0px;
    left: 0px;
    list-style-type: none;
}

.ui-galleria-filmstrip-wrapper {
    overflow: hidden;
    margin: 5px auto;
    position: relative;
}

.ui-galleria-filmstrip { 
    list-style: none outside none;
    margin: 0;
    padding: 0;
    width: 2340px;
    z-index: 900;
    position: absolute;
    top: 0px;
    left: 0px;
}

.ui-galleria-frame {
    float:left;
    margin-right: 5px;
    opacity: 0.3;
    cursor: pointer;
}

.ui-galleria-frame-active {
    opacity: 1;
}

.ui-galleria-frame-content {
    overflow: hidden;
}

.ui-galleria-nav-next, .ui-galleria-nav-prev {
	cursor: pointer;
    position: absolute;
}

.ui-galleria-nav-prev {	
    left: 5px;
}

.ui-galleria-nav-next {
    right: 5px;
}

.ui-galleria-caption {
    position: absolute;
    left:1px;
    background-color: rgba(0,0,0,0.5);
    display: none;
    color: #ededed;
    padding: 0.2em 1em;
}

.ui-galleria-caption h4 {
    color: #ededed;
}

.ui-galleria-panel-content {
    padding: 1em 1.4em;
}