.ui-picklist > div {
    float: left;
}

.ui-picklist .ui-picklist-buttons {
    height: 200px;
    padding: 0px 5px;
}

.ui-picklist .ui-picklist-list {
    list-style-type: none;
    margin: 0;
    padding: 0;
    overflow:auto;
    height:200px;
    width:200px;
}

.ui-picklist .ui-picklist-list li {
    margin: 1px;
    padding: 2px;
}

.ui-picklist .ui-button {
    display:block;
    margin-bottom:0.3em;
}

.ui-picklist .ui-button-text-icon-left {
    width: 100%;
}

.ui-picklist .ui-picklist-item {
    cursor:pointer;
    border:0px none;
    font-weight: inherit;
}

.ui-picklist .ui-picklist-caption {
    text-align: center;
    padding: 4px 0px;
    border-bottom:0px none;
}

.ui-picklist table {
    width:100%;
    border-collapse:collapse;
}

.ui-picklist .ui-picklist-filter {
    padding-right: 15px;
    width: 100%;
    box-sizing: border-box;
}

.ui-picklist .ui-picklist-filter-container {
    position: relative;
    margin: 0;
    padding: 0;
}

.ui-picklist .ui-picklist-filter-container .fa {
    position: absolute;
    top: 5px;
    right: 2px;
}

.ui-picklist {
    display: table;
}

.ui-picklist > div {
    float: none;
    display: table-cell;
    vertical-align: top;
}

.ui-picklist .ui-picklist-buttons {
    vertical-align: middle;
}

/* Vertical */
.ui-picklist.ui-picklist-vertical {
    display: table;
}

.ui-picklist.ui-picklist-vertical > div {
    float: none;
    display: table-row;
    vertical-align: top;
}

.ui-picklist.ui-picklist-vertical .ui-picklist-buttons {
    text-align:center;
    height: auto;
}

.ui-picklist.ui-picklist-vertical .ui-picklist-buttons .ui-button {
    display: inline-block;
}

.ui-picklist.ui-picklist-vertical .ui-button {
    margin-top: 0.3em;
}

.ui-picklist-outline {
    outline: 1px dotted black;
    z-index: 1;
}

.ui-picklist-list.ui-picklist-source,
.ui-picklist-list.ui-picklist-target {
    outline: none;
}

/* Responsive */
.ui-picklist.ui-picklist-responsive * {
    box-sizing: border-box;
}

.ui-picklist.ui-picklist-responsive {
    width: 100%;
}

.ui-picklist.ui-picklist-responsive .ui-picklist-listwrapper {
    width: 35%;
}

.ui-picklist.ui-picklist-responsive .ui-picklist-buttons {
    width: 10%;
}

.ui-picklist.ui-picklist-responsive .ui-picklist-buttons button {
    width: 100%;
}

.ui-picklist.ui-picklist-responsive .ui-picklist-list {
    width: auto;
}

.ui-picklist.ui-picklist-responsive .ui-chkbox-box {
    width: 18px;
    height: 18px;
}
        
/* Responsive */
@media (max-width: 640px) {
    .ui-picklist.ui-picklist-responsive {
        display: block;
    }
    
    .ui-picklist.ui-picklist-responsive > div {
        display: block;
        width: 100% !important;
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-buttons {
        text-align: center;
        height: auto;
        padding: 5px 0px;
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-buttons button {
        display: inline;
        width: 20%;
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-source-controls.ui-picklist-buttons {
        padding-bottom: 5px;
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-target-controls.ui-picklist-buttons {
        padding-top: 5px;
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-buttons .fa-angle-right:before {
        content: "\f107";
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-buttons .fa-angle-double-right:before {
        content: "\f103";
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-buttons .fa-angle-left:before {
        content: "\f106";
    }
    
    .ui-picklist.ui-picklist-responsive .ui-picklist-buttons .fa-angle-double-left:before {
        content: "\f102";
    }
}
