.ui-carousel {
    position: relative;
    padding: 1px;
}

.ui-carousel .ui-carousel-viewport .ui-carousel-items {
    list-style: none outside none;
    margin: 0;
    padding:0;
    position: relative;
    width: 32000px;
    left: 0;
}

.ui-carousel .ui-carousel-viewport .ui-carousel-items .ui-carousel-item {
    margin: 1px;
    padding: 0;
    float: left;
    box-sizing: border-box;
}

.ui-carousel .ui-carousel-viewport {
    overflow: hidden;
    position: relative;
    border: 0;
}

.ui-carousel .ui-carousel-footer {
    margin: 1px 1px 0px 1px;
    padding-top: 9px;
    padding-bottom: 6px;
    padding-right: 10px;
    padding-left: 10px;
    overflow: hidden;
}

.ui-carousel .ui-carousel-header {
    margin: 0 1px;
    overflow: hidden;
    padding-top: 7px;
    padding-bottom: 8px;
    padding-right: 10px;
    padding-left: 10px;
}

.ui-carousel .ui-carousel-header .ui-carousel-header-title {
    display: inline-block;
    padding-top: 2px;
    overflow: hidden;
}

.ui-carousel .ui-carousel-dropdown,
.ui-carousel .ui-carousel-mobiledropdown {
    float: right;
    margin: 0px 10px;
    background-image: none;
}

.ui-carousel .ui-carousel-dropdown option,
.ui-carousel .ui-carousel-mobiledropdown option{
    background-image: none;
    border:0 none;
    box-shadow:none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
}

.ui-carousel .ui-carousel-button {
    float: right;
    margin: 2px;
}

.ui-carousel .ui-carousel-page-link {
    float: left;
    margin: 0 2px;
}

.ui-carousel .ui-carousel-page-link, 
.ui-carousel .ui-carousel-button {
    cursor: pointer;
}

.ui-carousel .ui-carousel-page-links {
    margin: 0px 9px;
    margin-top: 2px;
    display: inline-table;
    float: right;
}

.ui-carousel .ui-carousel-mobiledropdown {
    display: none;
}