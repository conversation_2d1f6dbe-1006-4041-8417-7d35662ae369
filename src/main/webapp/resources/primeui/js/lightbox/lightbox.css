.ui-lightbox {
    position: fixed;
}

.ui-lightbox-content-wrapper {
    position: relative;
}

.ui-lightbox-content {
    position: relative;
    margin:0;
    padding: 0;
    background-color: #000000;
}

.ui-lightbox-nav-right, .ui-lightbox-nav-left {
   position: absolute;
   top:50%;
   cursor: pointer;
}

.ui-lightbox-nav-left {
   left:0;
}

.ui-lightbox-nav-right {
   right:0;
}

.ui-lightbox-loading {
    background: url("./images/loading.gif") #000000 center center no-repeat;
}

.ui-lightbox-caption {
    padding: 0.2em 0.4em;
    display: none;
}

.ui-lightbox-caption-text {
    margin: 0.3em 0 0.1em 0;
    float:left;
}

.ui-lightbox-close {
    float:right;
    margin:0;
    padding:1px;
}

.ui-lightbox-close.ui-state-hover {
    padding: 0;
}

.ui-lightbox-nav-left, .ui-lightbox-nav-right {
    opacity: .5;
}

.ui-lightbox-nav-left:hover, .ui-lightbox-nav-right:hover{
    opacity: 1;
}