@charset "UTF-8";

@font-face {
    font-family: 'exosemibold';
    src: url("../fonts/exo-semibold-webfont.eot");
    src: url("../fonts/exo-semibold-webfont.eot?#iefix") format('embedded-opentype'),
        url("../fonts/exo-semibold-webfont.woff") format('woff'),
        url("../fonts/exo-semibold-webfont.ttf") format('truetype'),
        url("../fonts/exo-semibold-webfont.svg#exosemibold") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'exobolditalic';
    src: url("../fonts/exo-bolditalic-webfont.eot");
    src: url("../fonts/exo-bolditalic-webfont.eot?#iefix") format('embedded-opentype'),
        url("../fonts/exo-bolditalic-webfont.woff") format('woff'),
        url("../fonts/exo-bolditalic-webfont.ttf") format('truetype'),
        url("../fonts/exo-bolditalic-webfont.svg#exobolditalic") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'exomedium';
    src: url("../fonts/exo-medium-webfont.eot");
    src: url("../fonts/exo-medium-webfont.eot?#iefix") format('embedded-opentype'),
        url("../fonts/exo-medium-webfont.woff") format('woff'),
        url("../fonts/exo-medium-webfont.ttf") format('truetype'),
        url("../fonts/exo-medium-webfont.svg#exomedium") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'exoregular';
    src: url("../fonts/exo-regular-webfont.eot");
    src: url("../fonts/exo-regular-webfont.eot?#iefix") format('embedded-opentype'),
        url("../fonts/exo-regular-webfont.woff") format('woff'),
        url("../fonts/exo-regular-webfont.ttf") format('truetype'),
        url("../fonts/exo-regular-webfont.svg#exoregular") format('svg');
    font-weight: normal;
    font-style: normal;

}

/*  ----------------------------------------------------------------------------------------------------- */

body{margin:0px; height:100%; overflow-x:hidden; overflow-y:auto; background-color:#20272a; font-family: 'exoregular', "Trebuchet MS", Arial, Helvetica, sans-serif;}

#LOGOTEXTSIDE{display:block;width:290px; float:left; height:74px; z-index:999; background-image:url(../images/logoCircuitLines.svg); background-repeat:no-repeat; background-position:center right;
              margin:-20px 0px 0px 0px; padding-top:25px; line-height:30px;}
.logoDarkText{color:#5c666a;}
.logoBlueText{color:#25aae1;}

/* menu side style*/
#MENUSIDE{width:85px; float:left; background-color:#313b3f; border-right:solid 1px #151d21; overflow:hidden; width: 299px; position: fixed;}
#MENUSIDEindent{width:100%; height:auto;}
#LOGO{height:64px; display:block; padding:50px 0 5px 0px; z-index:997; background-color:#313b3f;margin-left:45px}
#LOGO img{width:180px; height:auto; position:absolute;}
.MenuSideMainLink{width:100%; height:45px; display:block; padding:15px 0px 0px 27px; cursor:pointer;}
.MenuSideMainLink input{ padding:6px; background-color:#354044; outline:none; border:0px; border:none; color:#9ba5ac; border-radius:20px;
                         -webkit-box-shadow: inset 0px 0px 3px 0px rgba(0,0,0,0.5); -moz-box-shadow: inset 0px 0px 3px 0px rgba(0,0,0,0.5); box-shadow: inset 0px 0px 3px 0px rgba(0,0,0,0.5);}
.MenuSideMainLinkDark{background-color:#20282b !important; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.MenuSideMainLink:hover{background-color:#20282b; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.MenuSideMainLink img{width:30px; height:auto; float:left;}
.MainLinkText{color:#9ba5ac; font-size:20px; margin:2px 0px 0px 60px; display:block;}

.SubMenuLinkContainer{width:auto; padding:20px; overflow:hidden; background-color:#20282b; display: none;}
.SubMenuLink{width:50%; display:inline-block; float:left; padding:6px 0px; cursor:pointer; border-radius:4px; -webkit-border-radius:4px; color:#659eb3; font-size:14px;
}
.SubMenuLink:hover{background-color:#101517; color:#8dc5d9;}

.SubMenuLinkSamples{width:50%; display:inline-block; float:left; padding:6px 0px; cursor:pointer; border-radius:4px; -webkit-border-radius:4px; color:#659eb3; font-size:14px;
}
.SubMenuLinkSamples:hover{background-color:#101517; color:#8dc5d9;}

.selectmenu{width:auto; max-width:120px; float:right; padding:5px; outline:none; font-size:16px; display:block; overflow:hidden;}
.LEVEL1{color:#25aae1; padding-left:10px;}
.LEVEL2{font-size:12px; color:#25aae1; padding-left:10px;}
.selectmenu option{ font-size:12px;}



/* content side style*/
#CONTENTSIDE{width:auto; overflow:hidden; z-index:1; margin-left:300px; background-color:#eff3f6;}
#CONTENTSIDEindent{width:100%; height:auto;}
.ContentSideSections{display:block; border-bottom:solid 1px #bcc7cf; padding:30px;}
.MOBILE .ContentSideSections{padding:14px 24px;}
#PFTopLinksCover{background-color:#eff3f6;}
#PFMobileTopLinksCover{ position:inherit;}
.PFTopLinks{width:80px; display:inline-block; text-align:center; padding:5px; border-radius:5px;  font-size:12px; -webkit-border-radius:5px; margin-top:-10px; -webkit-transition: all 0.5s ease; 
            -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; overflow:hidden;}
.PFTopLinks:hover, .PFTopLinksMobile:hover{background-color:#e0e5e9;
                                           -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.PFTopLinksMobile{width:20%; display:inline-block; text-align:center; padding:1%; border-radius:5px;  font-size:11px; -webkit-border-radius:5px;
                  -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; overflow:hidden;}
.PFTopLinks img, .PFTopLinksMobile img{width:80%; height:55px;}
.PFGrayText{color:#aab7c1;}
.PFDarkText{color:#313b3f;}

.PropertyBox{width:27%; height:280px; float:left; padding:3%; overflow:hidden; text-align:center; border-radius:5px; -webkit-border-radius:5px; display:block;
             -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.PropertyBox:hover, .MOBILE .PropertyBox:hover{background-color:#e0e5e9;
                                               -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.PropertyBox img{width:80%;}
.PropertyBox .PropertyTopic{display:block; color:#25aae1; font-size:18px;}
.PropertyBox .PropertyText{display:block; color:#8c9aa5; font-size:14px;}

.MOBILE .PropertyBox{width:27%; height:250px; float:left; padding:3%; overflow:hidden; text-align:center; border-radius:5px; -webkit-border-radius:5px; display:block;
                     -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.MOBILE .PropertyBox img{width:80%;}
.MOBILE .PropertyBox .PropertyTopic{display:block; color:#25aae1; font-size:12px;}
.MOBILE .PropertyBox .PropertyText{display:block; color:#8c9aa5; font-size:11px;}

.widgetsLink{width:90%; height:auto; background-image:url(resources/images/widgetIconCover.svg); background-position:top; background-repeat:no-repeat; background-size:contain; display:table;
             text-align:center; padding:5%;}
.widgetsLink img{width:40%; height:auto; margin-top:5%;}
.widgetsLink span{ width:100%; margin-top:10px; overflow:hidden; height:50px; display:table; font-size:14px;}
.widgetsLink:hover span{color:#fdd106; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}

.MOBILE .widgetsLink img{width:40%; height:auto; margin-top:1%;}
.MOBILE .widgetsLink span{ width:100%; margin-top:0px; overflow:hidden; height:50px; display:table; font-size:11px;}

.BoxesIndent{width:85%; float:right; padding-top:30px;}
.brandLogo{ width:70px; height:70px; display:block;
            margin:10px 10px 10px 0px; float:left;}

.MOBILE .BoxesIndent{width:85%; float:none; margin:0px auto; padding-top:30px;}

/** ThemeSwitcher **/
.navOverlay{width:200px; max-height:400px; overflow-y:auto; background-color:#eff3f6; border:solid 1px #bcc7cf; z-index:99999999999; position: absolute; 
            border-radius:10px; -moz-border-radius:10px; -webkit-border-radius:10px; -webkit-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.2); -moz-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.2); 
            box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.2); margin-left:-60px; display: none;}
.navOverlay a{display:block; padding:10px; border-bottom:solid 1px #bcc7cf; text-align:left; color:#87939B; overflow:hidden;}
.navOverlay a:hover{ color:#fdd106;}
.navOverlay a span.ui-theme{width:30px; height:27px; float:left; border:solid 1px #D7DCE0; border-radius:5px; -moz-border-radius:5px; -webkit-border-radius:5px;}
.navOverlay a span.ui-text{ display:inline-block; margin-top:7px; margin-left:10px; font-size: 14px;}
/* cursor */
.cursorPointer{cursor:pointer;}

/* OTHERS */
a{ text-decoration:none; outline:none;color: #25AAE1}
.dispBlock{display:block !important;}
.TextShadow{text-shadow: 0 1px 0 #ffffff}
.TextShadowNone{text-shadow:none;}
.underline{ text-decoration:underline;}
.fontSize34{font-size:34px;}
.fontSize30{font-size:30px;}
.fontSize24{font-size:24px;}
.fontSize21{font-size:21px;}
.fontSize18{font-size:18px;}
.fontSize14{font-size:14px;}
.ContentX{width:95% !important; margin-bottom:30px; text-align:center; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; 
          transition: all 0.5s ease;}
.ContentX .defaultText{font-size:16px;}
.ContentX .defaultTopic{font-size:34px; line-height:36px; text-align:center;}
.ContentX .PropertyBox{height:220px;}
.ContentX .PropertyBox .PropertyTopic{font-size:14px;}
.ContentX .PropertyBox .PropertyText{font-size:12px;}
.PFTopLinksCover{padding-top:130px !important; height:auto !important; min-height:60px !important; text-align:left !important;  -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; 
                 -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.PFTopLinksCover .PFTopLinks{float:left !important; margin-top:0px;  -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease;
                             transition: all 0.5s ease;}

.wid50{width:50px;}

.texAliCenter{text-align:center;}

/* margins */
.marginRight10{margin-right:10px;}
.marginRight20{margin-right:20px;}
.marginBottom20{margin-bottom:20px;}
.marLefRigAuto{ margin-left:auto !important; margin-right:auto !important;}

/*fonts*/
.boldFont{font-family: 'exosemibold', "Trebuchet MS", Arial, Helvetica, sans-serif;}
.boldItalicFont{font-family: 'exobolditalic', "Trebuchet MS", Arial, Helvetica, sans-serif;}
.mediumFont{font-family: 'exomedium', "Trebuchet MS", Arial, Helvetica, sans-serif;}
.regularFont{font-family: 'exoregular', "Trebuchet MS", Arial, Helvetica, sans-serif;}

.bordersOfMenuSide{border-top:solid 1px #404a4e;}
.hiddenIcons{opacity:0; filter:alpha(opacity=0);margin-left:-30px}
.hiddenLogo{opacity:0; filter:alpha(opacity=0);}
.emptyBox{display:block; height:121px;}

/* display */
.dispBlock{ display:block;}
.dispTable{ display:table;}
.overHidden{ overflow:hidden;}

/* floating */
.floatRight{float:right;}
.floatLeft{float:left;}

/* content boxes*/
.Content33{width:33%; }
.Content66{width:65%; background-position:left top; background-repeat:no-repeat;}
.Content100{width:100%;}

/* general text styles */
.defaultText{font-size:18px; color:#5C666A;}
.defaultTopic{font-size:46px; color:#e26e60; line-height:46px; padding:10px 0px 10px 0px;}

/* buttons */
.BigButton{width:90%; box-sizing:border-box; -moz-box-sizing:border-box; -webkit-box-sizing:border-box; display:table; font-size:18px; font-family: 'exobolditalic', "Trebuchet MS", Arial, Helvetica, sans-serif; color:#404c51; margin:10px 0px 10px 0px; cursor:pointer;
           padding:20px; border-radius:5px; border-right:solid 60px #404c51;}
.BigButton img{ margin-right:-60px; height:23px;}	

.YellowBtn{background-color:#fdd106; -webkit-box-shadow: 0px 2px 0px 0px rgba(231,191,9,1); -moz-box-shadow: 0px 2px 0px 0px rgba(231,191,9,1); box-shadow: 0px 2px 0px 0px rgba(231,191,9,1);}
.YellowBtn:hover{ background-color:#fcdb42;}
.OrangeBtn{background-color:#fba751; -webkit-box-shadow: 0px 2px 0px 0px rgba(212,135,56,1); -moz-box-shadow: 0px 2px 0px 0px rgba(212,135,56,1); box-shadow: 0px 2px 0px 0px rgba(212,135,56,1);}
.OrangeBtn:hover{ background-color:#feb66d;}

/* text colors */
.orange{color:#fca752;}
.gray{color:#84939f;}
.pink{color:#e26e61;}
.yellow{color:#fdd106;}
.green{color:#39b54a;}
.blue{color:#25aae1;}
.subitem{color:#84939f;}

.fixedTop{position:fixed !important; margin-top:0 !important;}
.animated{-webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}

input.searchInput {
    padding-left: 30px;
}

/** ClearFix **/
.clearfix:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; }
.clearfix { display: inline-block; }
/* required comment for clearfix to work in Opera \*/
* html .clearfix { height:1%; }
.clearfix { display:block; }

/* override */
.ui-widget {
    font-size: 90%;
}

.Implementation {
    background-color:#dfe4e6;
}

.Implementation h3 {
    margin-top: 30px;
    color: #5C666A;
}

.Implementation h3.first {
    margin-top: 0px !important;
}

.Implementation h4 {
    color: #5C666A;
}

.SubSubMenu {
    padding: 15px 30px;
}
.SubSubMenu ul {
    margin: 0;
    padding: 0;
    width: 100%;
}
.SubSubMenu ul li {
    list-style: none;
    width: 20%;
    float: left;
}

.SubSubMenu ul li a:hover {
    color: #fdd106
}

.Source h3 {
    margin-top: 0px;
}

/* Docs Table */
.doc-table {
    border-collapse: collapse;
    font-size: 16px;
    width: 100%;
    text-shadow: none;
    font-family: 'exoregular', "Trebuchet MS", Arial, Helvetica, sans-serif;
}

.doc-table th {
    background-color: #dae8ef;
    color: #72828B;
    border: solid 1px #C1D5DF;
    padding: 5px;
}

.doc-table tbody td {
    color: #72828B;
    padding: 4px 10px;
    border: 1px solid #E5EBF0;
}

.doc-table tbody tr:nth-child(even) {
    background-color: #FBFCFD;
}

.doc-table tbody tr:nth-child(odd){
    background-color: #F3F6F9;
}

.Source h3 {
    margin-top: 20px;
}

/* Tabs Source */
.Source .ui-tabview {
    background: none;
    border: 0 none;
    color: #5C666A;
    font-family: 'exoregular', "Trebuchet MS", Arial, Helvetica, sans-serif;
}

.Source .ui-tabview .ui-tabview-nav {
    background: none;
    margin-bottom: -1px;
    padding: 0 !important;
}

.Source .ui-tabview .ui-tabview-nav li {
    background: #DFE4E6;
    border-color: #BCC7CF;
    box-shadow: none !important;
}

.Source .ui-tabview .ui-tabview-nav li.tab-doc {
    margin-right: 0;
}

.Source .ui-tabview .ui-tabview-nav li.ui-state-hover.ui-state-default a {
    color: #5C666A;
}

.Source .ui-tabview .ui-tabview-nav li.ui-state-default a {
    color: #84939F;
    text-shadow: none;
}

.Source .ui-tabview .ui-tabview-nav li.ui-state-active a {
    color: #5C666A;
}

.Source .ui-tabview .ui-tabview-nav li.ui-state-hover {
    box-shadow: none; 
}

.Source .ui-tabview .ui-tabview-nav li.ui-tabview-selected {
    background: #EFF3F6;
}

.Source .ui-tabview .ui-tabview-panels {
    border-top: 1px solid #BCC7CF;
    color: #5C666A !important;
}

.Source .ui-tabview-panels h3 {
    font-size: 24px;
    font-weight: normal;
}

/** Responsive **/
@media (min-width: 961px) {
    .PFTopLinks {
        display: block
    }

    .selectmenu {
        display: none;
    }

    .mobileLogoCover {
        display: none;
    }

    .PFNextGen iframe {
        margin-left: 100px;
    }
}

@media (max-width: 1140px) {
    #LOGOTEXTSIDE {
        width: auto;
        background: none;
    }

    #LOGOTEXTSIDE span {
        font-size: 24px;
    }

    #PFTopLinksCover .PFTopLinks {
        padding-left:0;
        padding-right:0;
    }
}

@media (max-width: 960px) {
    .PFTopLinks {
        display: none
    }

    #PFTopLinksCover {
        padding: 30px 15px 15px 15px;
    }

    .selectmenu {
        display: block;
    }

    .mobileLogoCover {
        display: block
    }

    #MENUSIDE {
        display: none
    }

    #CONTENTSIDE {
        margin-left:0
    }

    #LOGOTEXTSIDE {
        display: none
    }

    .ContentSideSections{
        padding:14px 24px;
    }

    .Content33 {
        width: 100%;
    }

    .Content66 {
        width: 100%;
    }

    .ui-tabview.ui-tabview-top > .ui-tabview-nav li {
        width: 100%;
    }

    .BigButton {
        width: 100%;
    }

    .PFNextGen iframe {
        margin-left: 0;
    }

    .SubSubMenu {
        display: none;
    }
}

/* Themes */
.ui-theme {background: url("../images/themes.png") no-repeat top left;display: block;}
.ui-theme.ui-theme-afterdark{ background-position: 0 0; width: 30px; height: 25px; } 
.ui-theme.ui-theme-afternoon{ background-position: 0 -75px; width: 30px; height: 25px; } 
.ui-theme.ui-theme-afterwork{ background-position: 0 -150px; width: 30px; height: 25px; } 
.ui-theme.ui-theme-aristo{ background-position: 0 -225px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-black-tie{ background-position: 0 -302px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-blitzer{ background-position: 0 -379px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-bluesky{ background-position: 0 -456px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-bootstrap{ background-position: 0 -533px; width: 30px; height: 26px; } 
.ui-theme.ui-theme-casablanca{ background-position: 0 -609px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-cruze{ background-position: 0 -686px; width: 30px; height: 25px; } 
.ui-theme.ui-theme-cupertino{ background-position: 0 -761px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-dark-hive{ background-position: 0 -838px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-delta{ background-position: 0 -915px; width: 30px; height: 30px; } 
.ui-theme.ui-theme-dot-luv{ background-position: 0 -995px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-eggplant{ background-position: 0 -1072px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-excite-bike{ background-position: 0 -1149px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-flick{ background-position: 0 -1226px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-glass-x{ background-position: 0 -1303px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-home{ background-position: 0 -1380px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-hot-sneaks{ background-position: 0 -1457px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-humanity{ background-position: 0 -1534px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-le-frog{ background-position: 0 -1611px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-midnight{ background-position: 0 -1688px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-mint-choc{ background-position: 0 -1765px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-overcast{ background-position: 0 -1842px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-pepper-grinder{ background-position: 0 -1919px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-redmond{ background-position: -80px 0; width: 30px; height: 27px; } 
.ui-theme.ui-theme-rocket{ background-position: -80px -77px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-sam{ background-position: -80px -154px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-smoothness{ background-position: -80px -231px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-south-street{ background-position: -80px -308px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-start{ background-position: -80px -385px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-sunny{ background-position: -80px -462px; width: 30px; height: 25px; } 
.ui-theme.ui-theme-swanky-purse{ background-position: -80px -537px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-trontastic{ background-position: -80px -614px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-ui-darkness{ background-position: -80px -691px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-ui-lightness{ background-position: -80px -768px; width: 30px; height: 27px; } 
.ui-theme.ui-theme-vader{ background-position: -80px -845px; width: 30px; height: 27px; }

.ui-tabview .tab-primeelement {
    float: right !important;
}

.PFLayouts .Content33 {
    box-sizing: border-box;
    padding-right: 15px;
}