/*
  一个分页展示按钮控件,kkpager V1.2
  https://github.com/pgkk/kkpager

  Copyright (c) 2013 <EMAIL>
  Licensed under the GNU GENERAL PUBLIC LICENSE
*/
var kkpager={pagerid:"kkpager",mode:"link",pno:1,total:1,totalRecords:0,isShowFirstPageBtn:true,isShowLastPageBtn:true,isShowPrePageBtn:true,isShowNextPageBtn:true,isShowTotalPage:true,isShowTotalRecords:true,isGoPage:true,hrefFormer:"",hrefLatter:"",gopageWrapId:"kkpager_gopage_wrap",gopageButtonId:"kkpager_btn_go",gopageTextboxId:"kkpager_btn_go_input",lang:{firstPageText:"\u9996\u9875",firstPageTipText:"\u9996\u9875",lastPageText:"\u5C3E\u9875",lastPageTipText:"\u5C3E\u9875",prePageText:"\u4E0A\u4E00\u9875",prePageTipText:"\u4E0A\u4E00\u9875",nextPageText:"\u4E0B\u4E00\u9875",nextPageTipText:"\u4E0B\u4E00\u9875",totalPageBeforeText:"\u5171",totalPageAfterText:"\u9875",totalRecordsAfterText:"\u6761\u6570\u636E",gopageBeforeText:"\u8F6C\u5230",gopageButtonOkText:"\u786E\u5B9A",gopageAfterText:"\u9875",buttonTipBeforeText:"\u7B2C",buttonTipAfterText:"\u9875"},getLink:function(a){if(a==1){return this.hrefFormer+this.hrefLatter}else{return this.hrefFormer+"_"+a+this.hrefLatter}},click:function(a){return false},getHref:function(a){return"#"},focus_gopage:function(){var a=$("#"+this.gopageButtonId);$("#"+this.gopageTextboxId).attr("hideFocus",true);a.show();a.css("left","0px");$("#"+this.gopageWrapId).css("border-color","#6694E3");a.animate({left:"+=44"},50,function(){})},blur_gopage:function(){var a=this;setTimeout(function(){var b=$("#"+a.gopageButtonId);b.animate({left:"-=44"},100,function(){b.css("left","0px");b.hide();$("#"+a.gopageWrapId).css("border-color","#DFDFDF")})},400)},gopage:function(){var a=$("#"+this.gopageTextboxId).val();if(isNaN(a)){$("#"+this.gopageTextboxId).val(this.next);return}var b=parseInt(a);if(b<1){b=1}if(b>this.total){b=this.total}if(this.mode=="click"){this._clickHandler(b)}else{window.location=this.getLink(b)}},selectPage:function(a){this._config.pno=a;this.generPageHtml(this._config,true)},generPageHtml:function(c,l){if(l||!this.inited){this.init(c)}var d="",n="",f="",m="";if(this.isShowFirstPageBtn){if(this.hasPrv){d="<a "+this._getHandlerStr(1)+' title="'+(this.lang.firstPageTipText||this.lang.firstPageText)+'">'+this.lang.firstPageText+"</a>"}else{d='<span class="disabled">'+this.lang.firstPageText+"</span>"}}if(this.isShowPrePageBtn){if(this.hasPrv){n="<a "+this._getHandlerStr(this.prv)+' title="'+(this.lang.prePageTipText||this.lang.prePageText)+'">'+this.lang.prePageText+"</a>"}else{n='<span class="disabled">'+this.lang.prePageText+"</span>"}}if(this.isShowNextPageBtn){if(this.hasNext){f="<a "+this._getHandlerStr(this.next)+' title="'+(this.lang.nextPageTipText||this.lang.nextPageText)+'">'+this.lang.nextPageText+"</a>"}else{f='<span class="disabled">'+this.lang.nextPageText+"</span>"}}if(this.isShowLastPageBtn){if(this.hasNext){m="<a "+this._getHandlerStr(this.total)+' title="'+(this.lang.lastPageTipText||this.lang.lastPageText)+'">'+this.lang.lastPageText+"</a>"}else{m='<span class="disabled">'+this.lang.lastPageText+"</span>"}}var k="";var a="<span>...</span>";var h="";if(this.isShowTotalPage||this.isShowTotalRecords){h='&nbsp;<span class="normalsize">'+this.lang.totalPageBeforeText;if(this.isShowTotalPage){h+=this.total+this.lang.totalPageAfterText;if(this.isShowTotalRecords){h+="/"}}if(this.isShowTotalRecords){h+=this.totalRecords+this.lang.totalRecordsAfterText}h+="</span>"}var j="";if(this.isGoPage){j="&nbsp;"+this.lang.gopageBeforeText+'<span id="'+this.gopageWrapId+'"><input type="button" id="'+this.gopageButtonId+'" onclick="kkpager.gopage()" value="'+this.lang.gopageButtonOkText+'" /><input type="text" id="'+this.gopageTextboxId+'" onfocus="kkpager.focus_gopage()"  onkeypress="if(event.keyCode<48 || event.keyCode>57)return false;"   onblur="kkpager.blur_gopage()" value="'+this.next+'" /></span>'+this.lang.gopageAfterText}if(this.total<=8){for(var g=1;g<=this.total;g++){if(this.pno==g){k+='<span class="curr">'+g+"</span>"}else{k+="<a "+this._getHandlerStr(g)+' title="'+this.lang.buttonTipBeforeText+g+this.lang.buttonTipAfterText+'">'+g+"</a>"}}}else{if(this.pno<=5){for(var g=1;g<=7;g++){if(this.pno==g){k+='<span class="curr">'+g+"</span>"}else{k+="<a "+this._getHandlerStr(g)+' title="'+this.lang.buttonTipBeforeText+g+this.lang.buttonTipAfterText+'">'+g+"</a>"}}k+=a}else{k+="<a "+this._getHandlerStr(1)+' title="'+this.lang.buttonTipBeforeText+"1"+this.lang.buttonTipAfterText+'">1</a>';k+="<a "+this._getHandlerStr(2)+' title="'+this.lang.buttonTipBeforeText+"2"+this.lang.buttonTipAfterText+'">2</a>';k+=a;var b=this.pno-2;var e=this.pno+2;if(e>this.total){e=this.total;b=e-4;if(this.pno-b<2){b=b-1}}else{if(e+1==this.total){e=this.total}}for(var g=b;g<=e;g++){if(this.pno==g){k+='<span class="curr">'+g+"</span>"}else{k+="<a "+this._getHandlerStr(g)+' title="'+this.lang.buttonTipBeforeText+g+this.lang.buttonTipAfterText+'">'+g+"</a>"}}if(e!=this.total){k+=a}}}k="&nbsp;"+d+n+k+f+m+h+j;$("#"+this.pagerid).html(k)},init:function(a){this.pno=isNaN(a.pno)?1:parseInt(a.pno);this.total=isNaN(a.total)?1:parseInt(a.total);this.totalRecords=isNaN(a.totalRecords)?0:parseInt(a.totalRecords);if(a.pagerid){this.pagerid=a.pagerid}if(a.mode){this.mode=a.mode}if(a.gopageWrapId){this.gopageWrapId=a.gopageWrapId}if(a.gopageButtonId){this.gopageButtonId=a.gopageButtonId}if(a.gopageTextboxId){this.gopageTextboxId=a.gopageTextboxId}if(a.isShowFirstPageBtn!=undefined){this.isShowFirstPageBtn=a.isShowFirstPageBtn}if(a.isShowLastPageBtn!=undefined){this.isShowLastPageBtn=a.isShowLastPageBtn}if(a.isShowPrePageBtn!=undefined){this.isShowPrePageBtn=a.isShowPrePageBtn}if(a.isShowNextPageBtn!=undefined){this.isShowNextPageBtn=a.isShowNextPageBtn}if(a.isShowTotalPage!=undefined){this.isShowTotalPage=a.isShowTotalPage}if(a.isShowTotalRecords!=undefined){this.isShowTotalRecords=a.isShowTotalRecords}if(a.isGoPage!=undefined){this.isGoPage=a.isGoPage}if(a.lang){for(var b in a.lang){this.lang[b]=a.lang[b]}}this.hrefFormer=a.hrefFormer||"";this.hrefLatter=a.hrefLatter||"";if(a.getLink&&typeof(a.getLink)=="function"){this.getLink=a.getLink}if(a.click&&typeof(a.click)=="function"){this.click=a.click}if(a.getHref&&typeof(a.getHref)=="function"){this.getHref=a.getHref}if(!this._config){this._config=a}if(this.pno<1){this.pno=1}this.total=(this.total<=1)?1:this.total;if(this.pno>this.total){this.pno=this.total}this.prv=(this.pno<=2)?1:(this.pno-1);this.next=(this.pno>=this.total-1)?this.total:(this.pno+1);this.hasPrv=(this.pno>1);this.hasNext=(this.pno<this.total);this.inited=true},_getHandlerStr:function(a){if(this.mode=="click"){return'href="'+this.getHref(a)+'" onclick="return kkpager._clickHandler('+a+')"'}return'href="'+this.getLink(a)+'"'},_clickHandler:function(b){var a=false;if(this.click&&typeof this.click=="function"){a=this.click.call(this,b)||false}return a}};
