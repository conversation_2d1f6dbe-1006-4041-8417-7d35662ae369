@CHARSET "UTF-8";
#kkpager{
	clear:both;
	height:30px;
	line-height:30px;
	margin-top:20px;
	color:#999999;
	font-size:14px;
}
#kkpager a{
	padding:4px 8px;
	margin:10px 3px;
	font-size:12px;
	border:1px solid #DFDFDF;
	background-color:#FFF;
	color:#9d9d9d;
	text-decoration:none;
}
#kkpager span{
	font-size:14px;
}
#kkpager span.disabled{
	padding:4px 8px;
	margin:10px 3px;
	font-size:12px;
	border:1px solid #DFDFDF;
	background-color:#FFF;
	color:#DFDFDF;
}
#kkpager span.curr{
	padding:4px 8px;
	margin:10px 3px;
	font-size:12px;
	border:1px solid #FF6600;
	background-color:#FF6600;
	color:#FFF;
}
#kkpager a:hover{
	background-color:#FFEEE5;
	border:1px solid #FF6600;
}
#kkpager span.normalsize{
	font-size:12px;
}
#kkpager_gopage_wrap{
	display:inline-block;
	width:44px;
	height:18px;
	border:1px solid #DFDFDF;
	margin:0px 1px;
	padding:0px;
	position:relative;
	left:0px;
	top:5px;
}
#kkpager_btn_go {
	width:44px;
	height:20px;
	line-height:20px;
	padding:0px;
	font-family:arial,'宋体',sans-serif;
	text-align:center;
	border:0px;
	background-color:#0063DC;
	color:#FFF;
	position:absolute;
	left:0px;
	top:-1px;
	display:none;
}
#kkpager_btn_go_input{
	width:42px;
	height:16px;
	text-align:center;
	border:0px;
	position:absolute;
	left:0px;
	top:0px;
	outline:none;
}