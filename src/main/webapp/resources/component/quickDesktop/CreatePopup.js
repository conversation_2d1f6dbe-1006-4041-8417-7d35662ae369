/*
Function:	创建Pop窗口对象,替代window.createPopup方法
*/

var CreatePopupExt = function() {
    var SetElementStyles = function(element, styleDict) {
        var style = element.style;
        for (var styleName in styleDict) style[styleName] = styleDict[styleName];
    }
    var eDiv = document.createElement('div');
    eDiv.name = "_WinPopupMenu";
    SetElementStyles(eDiv, { 'position': 'absolute', 'top': 0 + 'px', 'left': 0 + 'px', 'width': 0 + 'px', 'height': 0 + 'px', 'zIndex': 9999, 'display': 'none', 'overflow': 'hidden' });
    eDiv.body = eDiv;
    var opened = false;
    var setOpened = function(b) {
        opened = b;
    }
    var getOpened = function() {
        return opened;
    }
    var getCoordinates = function(oElement) {
        var coordinates = { x: 0, y: 0 };
        while (oElement) {
            coordinates.x += oElement.offsetLeft;
            coordinates.y += oElement.offsetTop;
            oElement = oElement.offsetParent;
        }
        return coordinates;
    }
    return {
        htmlTxt: '',
        document: eDiv,
        isOpen: getOpened(),
        isShow: false,
        isActive: false,
        hide: function() {
            SetElementStyles(eDiv, { 'top': 0 + 'px', 'left': 0 + 'px', 'width': 0 + 'px', 'height': 0 + 'px', 'display': 'none' });
            eDiv.innerHTML = '';
            this.isShow = false;
        },
        show: function(iX, iY, iWidth, iHeight, oElement) {
            if (!getOpened()) {
                document.body.appendChild(eDiv);
                setOpened(true);
            };
            this.htmlTxt = eDiv.innerHTML;
            if (this.isShow) {
                //this.hide();
                //return;
            };
            eDiv.innerHTML = this.htmlTxt + "<IFRAME style='WIDTH: " + (iWidth + 2) + "px; HEIGHT: " + (iHeight + 2) + "px; TOP: -1px; LEFT: -1px' class='ui-datepicker-cover' frameBorder=0></IFRAME>";
            var coordinates = getCoordinates(oElement);
            eDiv.style.left = (iX + coordinates.x) + 'px';
            eDiv.style.top = (iY + coordinates.y) + 'px';
            eDiv.style.width = iWidth + 'px';
            eDiv.style.height = iHeight + 'px';
            eDiv.style.display = 'block';
            this.isShow = true;
        },
        hideanimate: function() {
            var curobj = this;
            if (this.isActive == true) return;
            this.isActive = true;
            zwxJQ(eDiv).animate({
                width: 0 + 'px',
                height: 0 + 'px'
            }, 300, '', function() {
                SetElementStyles(eDiv, { 'top': 0 + 'px', 'left': 0 + 'px', 'display': 'none' });
                eDiv.innerHTML = '';
                curobj.isActive = false;
            });
            this.isShow = false;
        },
        showanimate: function(iX, iY, iWidth, iHeight, oElement) {
            var curobj = this;
            if (this.isActive == true) return;
            this.isActive = true;
            if (!getOpened()) {
                document.body.appendChild(eDiv);
                setOpened(true);
            };
            this.htmlTxt = eDiv.innerHTML;
            if (this.isShow) {
                //this.hideanimate();
                //return;
            };
            var coordinates = getCoordinates(oElement);
            eDiv.innerHTML = this.htmlTxt + "<IFRAME style='WIDTH: " + (iWidth + 2) + "px; HEIGHT: " + (iHeight + 2) + "px; TOP: -1px; LEFT: -1px' class='ui-datepicker-cover' frameBorder=0></IFRAME>";

            eDiv.style.left = (iX + coordinates.x) + 'px';
            eDiv.style.top = (iY + coordinates.y) + 'px';
            eDiv.style.display = 'block';
            zwxJQ(eDiv).animate({
                width: iWidth + 'px',
                height: iHeight + 'px'
            }, 300, '', function() {
                curobj.isActive = false;
            });
            this.isShow = true;
        }
    }
};