/*!
 * zwxJQ UI Widget 1.8.15
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Widget
 */
(function( zwxJQ, undefined ) {

// zwxJQ 1.4+
if ( zwxJQ.cleanData ) {
	var _cleanData = zwxJQ.cleanData;
	zwxJQ.cleanData = function( elems ) {
		for ( var i = 0, elem; (elem = elems[i]) != null; i++ ) {
			zwxJQ( elem ).triggerHandler( "remove" );
		}
		_cleanData( elems );
	};
} else {
	var _remove = zwxJQ.fn.remove;
	zwxJQ.fn.remove = function( selector, keepData ) {
		return this.each(function() {
			if ( !keepData ) {
				if ( !selector || zwxJQ.filter( selector, [ this ] ).length ) {
					zwxJQ( "*", this ).add( [ this ] ).each(function() {
						zwxJQ( this ).triggerHandler( "remove" );
					});
				}
			}
			return _remove.call( zwxJQ(this), selector, keepData );
		});
	};
}

zwxJQ.widget = function( name, base, prototype ) {
	var namespace = name.split( "." )[ 0 ],
		fullName;
	name = name.split( "." )[ 1 ];
	fullName = namespace + "-" + name;

	if ( !prototype ) {
		prototype = base;
		base = zwxJQ.Widget;
	}

	// create selector for plugin
	zwxJQ.expr[ ":" ][ fullName ] = function( elem ) {
		return !!zwxJQ.data( elem, name );
	};

	zwxJQ[ namespace ] = zwxJQ[ namespace ] || {};
	zwxJQ[ namespace ][ name ] = function( options, element ) {
		// allow instantiation without initializing for simple inheritance
		if ( arguments.length ) {
			this._createWidget( options, element );
		}
	};

	var basePrototype = new base();
	// we need to make the options hash a property directly on the new instance
	// otherwise we'll modify the options hash on the prototype that we're
	// inheriting from
//	zwxJQ.each( basePrototype, function( key, val ) {
//		if ( zwxJQ.isPlainObject(val) ) {
//			basePrototype[ key ] = zwxJQ.extend( {}, val );
//		}
//	});
	basePrototype.options = zwxJQ.extend( true, {}, basePrototype.options );
	zwxJQ[ namespace ][ name ].prototype = zwxJQ.extend( true, basePrototype, {
		namespace: namespace,
		widgetName: name,
		widgetEventPrefix: zwxJQ[ namespace ][ name ].prototype.widgetEventPrefix || name,
		widgetBaseClass: fullName
	}, prototype );

	zwxJQ.widget.bridge( name, zwxJQ[ namespace ][ name ] );
};

zwxJQ.widget.bridge = function( name, object ) {
	zwxJQ.fn[ name ] = function( options ) {
		var isMethodCall = typeof options === "string",
			args = Array.prototype.slice.call( arguments, 1 ),
			returnValue = this;

		// allow multiple hashes to be passed on init
		options = !isMethodCall && args.length ?
			zwxJQ.extend.apply( null, [ true, options ].concat(args) ) :
			options;

		// prevent calls to internal methods
		if ( isMethodCall && options.charAt( 0 ) === "_" ) {
			return returnValue;
		}

		if ( isMethodCall ) {
			this.each(function() {
				var instance = zwxJQ.data( this, name ),
					methodValue = instance && zwxJQ.isFunction( instance[options] ) ?
						instance[ options ].apply( instance, args ) :
						instance;
				// TODO: add this back in 1.9 and use zwxJQ.error() (see #5972)
//				if ( !instance ) {
//					throw "cannot call methods on " + name + " prior to initialization; " +
//						"attempted to call method '" + options + "'";
//				}
//				if ( !zwxJQ.isFunction( instance[options] ) ) {
//					throw "no such method '" + options + "' for " + name + " widget instance";
//				}
//				var methodValue = instance[ options ].apply( instance, args );
				if ( methodValue !== instance && methodValue !== undefined ) {
					returnValue = methodValue;
					return false;
				}
			});
		} else {
			this.each(function() {
				var instance = zwxJQ.data( this, name );
				if ( instance ) {
					instance.option( options || {} )._init();
				} else {
					zwxJQ.data( this, name, new object( options, this ) );
				}
			});
		}

		return returnValue;
	};
};

zwxJQ.Widget = function( options, element ) {
	// allow instantiation without initializing for simple inheritance
	if ( arguments.length ) {
		this._createWidget( options, element );
	}
};

zwxJQ.Widget.prototype = {
	widgetName: "widget",
	widgetEventPrefix: "",
	options: {
		disabled: false
	},
	_createWidget: function( options, element ) {
		// zwxJQ.widget.bridge stores the plugin instance, but we do it anyway
		// so that it's stored even before the _create function runs
		zwxJQ.data( element, this.widgetName, this );
		this.element = zwxJQ( element );
		this.options = zwxJQ.extend( true, {},
			this.options,
			this._getCreateOptions(),
			options );

		var self = this;
		this.element.bind( "remove." + this.widgetName, function() {
			self.destroy();
		});

		this._create();
		this._trigger( "create" );
		this._init();
	},
	_getCreateOptions: function() {
		return zwxJQ.metadata && zwxJQ.metadata.get( this.element[0] )[ this.widgetName ];
	},
	_create: function() {},
	_init: function() {},

	destroy: function() {
		this.element
			.unbind( "." + this.widgetName )
			.removeData( this.widgetName );
		this.widget()
			.unbind( "." + this.widgetName )
			.removeAttr( "aria-disabled" )
			.removeClass(
				this.widgetBaseClass + "-disabled " +
				"ui-state-disabled" );
	},

	widget: function() {
		return this.element;
	},

	option: function( key, value ) {
		var options = key;

		if ( arguments.length === 0 ) {
			// don't return a reference to the internal hash
			return zwxJQ.extend( {}, this.options );
		}

		if  (typeof key === "string" ) {
			if ( value === undefined ) {
				return this.options[ key ];
			}
			options = {};
			options[ key ] = value;
		}

		this._setOptions( options );

		return this;
	},
	_setOptions: function( options ) {
		var self = this;
		zwxJQ.each( options, function( key, value ) {
			self._setOption( key, value );
		});

		return this;
	},
	_setOption: function( key, value ) {
		this.options[ key ] = value;

		if ( key === "disabled" ) {
			this.widget()
				[ value ? "addClass" : "removeClass"](
					this.widgetBaseClass + "-disabled" + " " +
					"ui-state-disabled" )
				.attr( "aria-disabled", value );
		}

		return this;
	},

	enable: function() {
		return this._setOption( "disabled", false );
	},
	disable: function() {
		return this._setOption( "disabled", true );
	},

	_trigger: function( type, event, data ) {
		var callback = this.options[ type ];

		event = zwxJQ.Event( event );
		event.type = ( type === this.widgetEventPrefix ?
			type :
			this.widgetEventPrefix + type ).toLowerCase();
		data = data || {};

		// copy original event properties over to the new event
		// this would happen if we could call zwxJQ.event.fix instead of zwxJQ.Event
		// but we don't have a way to force an event to be fixed multiple times
		if ( event.originalEvent ) {
			for ( var i = zwxJQ.event.props.length, prop; i; ) {
				prop = zwxJQ.event.props[ --i ];
				event[ prop ] = event.originalEvent[ prop ];
			}
		}

		this.element.trigger( event, data );

		return !( zwxJQ.isFunction(callback) &&
			callback.call( this.element[0], event, data ) === false ||
			event.isDefaultPrevented() );
	}
};

})( zwxJQ );
