/*
* zwxJQ UI Sortable 1.8.15
*
* Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
* Dual licensed under the MIT or GPL Version 2 licenses.
* http://jquery.org/license
*
* http://docs.jquery.com/UI/Sortables
*
* Depends:
*	jquery.ui.core.js
*	jquery.ui.mouse.js
*	jquery.ui.widget.js
*/
(function(zwxJQ, undefined) {

    zwxJQ.widget("ui.sortable", zwxJQ.ui.mouse, {
        widgetEventPrefix: "sort",
        options: {
            appendTo: "parent",
            axis: false,
            connectWith: false,
            containment: false,
            cursor: 'auto',
            cursorAt: false,
            dropOnEmpty: true,
            forcePlaceholderSize: false,
            forceHelperSize: false,
            grid: false,
            handle: false,
            helper: "original",
            items: '> *',
            opacity: false,
            placeholder: false,
            revert: false,
            scroll: true,
            scrollSensitivity: 20,
            scrollSpeed: 20,
            scope: "default",
            tolerance: "intersect",
            zIndex: 1000
        },
        _create: function() {

            var o = this.options;
            this.containerCache = {};
            this.element.addClass("ui-sortable");

            //Get the items
            this.refresh();

            //Let's determine if the items are being displayed horizontally
            this.floating = this.items.length ? o.axis === 'x' || (/left|right/).test(this.items[0].item.css('float')) || (/inline|table-cell/).test(this.items[0].item.css('display')) : false;

            //Let's determine the parent's offset
            this.offset = this.element.offset();

            //Initialize mouse events for interaction
            this._mouseInit();

        },

        destroy: function() {
            this.element
			.removeClass("ui-sortable ui-sortable-disabled")
			.removeData("sortable")
			.unbind(".sortable");
            this._mouseDestroy();

            for (var i = this.items.length - 1; i >= 0; i--)
                this.items[i].item.removeData("sortable-item");

            return this;
        },

        _setOption: function(key, value) {
            if (key === "disabled") {
                this.options[key] = value;

                this.widget()
				[value ? "addClass" : "removeClass"]("ui-sortable-disabled");
            } else {
                // Don't call widget base _setOption for disable as it adds ui-state-disabled class
                zwxJQ.Widget.prototype._setOption.apply(this, arguments);
            }
        },

        _mouseCapture: function(event, overrideHandle) {

            if (this.reverting) {
                return false;
            }

            if (this.options.disabled || this.options.type == 'static') return false;

            //We have to refresh the items data once first
            this._refreshItems(event);

            //Find out if the clicked node (or one of its parents) is a actual item in this.items
            var currentItem = null, self = this, nodes = zwxJQ(event.target).parents().each(function() {
                if (zwxJQ.data(this, 'sortable-item') == self) {
                    currentItem = zwxJQ(this);
                    return false;
                }
            });
            if (zwxJQ.data(event.target, 'sortable-item') == self) currentItem = zwxJQ(event.target);

            if (!currentItem) return false;
            if (this.options.handle && !overrideHandle) {
                var validHandle = false;

                zwxJQ(this.options.handle, currentItem).find("*").andSelf().each(function() { if (this == event.target) validHandle = true; });
                if (!validHandle) return false;
            }

            this.currentItem = currentItem;
            this._removeCurrentsFromItems();
            return true;

        },

        _mouseStart: function(event, overrideHandle, noActivation) {

            var o = this.options, self = this;
            this.currentContainer = this;

            //We only need to call refreshPositions, because the refreshItems call has been moved to mouseCapture
            this.refreshPositions();

            //Create and append the visible helper
            this.helper = this._createHelper(event);

            //Cache the helper size
            this._cacheHelperProportions();

            /*
            * - Position generation -
            * This block generates everything position related - it's the core of draggables.
            */

            //Cache the margins of the original element
            this._cacheMargins();

            //Get the next scrolling parent
            this.scrollParent = this.helper.scrollParent();

            //The element's absolute position on the page minus margins
            this.offset = this.currentItem.offset();
            this.offset = {
                top: this.offset.top - this.margins.top,
                left: this.offset.left - this.margins.left
            };

            // Only after we got the offset, we can change the helper's position to absolute
            // TODO: Still need to figure out a way to make relative sorting possible
            this.helper.css("position", "absolute");
            this.cssPosition = this.helper.css("position");

            zwxJQ.extend(this.offset, {
                click: { //Where the click happened, relative to the element
                    left: event.pageX - this.offset.left,
                    top: event.pageY - this.offset.top
                },
                parent: this._getParentOffset(),
                relative: this._getRelativeOffset() //This is a relative to absolute position minus the actual position calculation - only used for relative positioned helper
            });

            //Generate the original position
            this.originalPosition = this._generatePosition(event);
            this.originalPageX = event.pageX;
            this.originalPageY = event.pageY;

            //Adjust the mouse offset relative to the helper if 'cursorAt' is supplied
            (o.cursorAt && this._adjustOffsetFromHelper(o.cursorAt));

            //Cache the former DOM position
            this.domPosition = { prev: this.currentItem.prev()[0], parent: this.currentItem.parent()[0] };

            //If the helper is not the original, hide the original so it's not playing any role during the drag, won't cause anything bad this way
            if (this.helper[0] != this.currentItem[0]) {
                this.currentItem.hide();
            }

            //Create the placeholder
            this._createPlaceholder();

            //Set a containment if given in the options
            if (o.containment)
                this._setContainment();

            if (o.cursor) { // cursor option
                if (zwxJQ('body').css("cursor")) this._storedCursor = zwxJQ('body').css("cursor");
                zwxJQ('body').css("cursor", o.cursor);
            }

            if (o.opacity) { // opacity option
                if (this.helper.css("opacity")) this._storedOpacity = this.helper.css("opacity");
                this.helper.css("opacity", o.opacity);
            }

            if (o.zIndex) { // zIndex option
                if (this.helper.css("zIndex")) this._storedZIndex = this.helper.css("zIndex");
                this.helper.css("zIndex", o.zIndex);
            }

            //Prepare scrolling
            if (this.scrollParent[0] != document && this.scrollParent[0].tagName != 'HTML')
                this.overflowOffset = this.scrollParent.offset();

            //Call callbacks
            this._trigger("start", event, this._uiHash());

            //Recache the helper size
            if (!this._preserveHelperProportions)
                this._cacheHelperProportions();


            //Post 'activate' events to possible containers
            if (!noActivation) {
                for (var i = this.containers.length - 1; i >= 0; i--) { this.containers[i]._trigger("activate", event, self._uiHash(this)); }
            }

            //Prepare possible droppables
            if (zwxJQ.ui.ddmanager)
                zwxJQ.ui.ddmanager.current = this;

            if (zwxJQ.ui.ddmanager && !o.dropBehaviour)
                zwxJQ.ui.ddmanager.prepareOffsets(this, event);

            this.dragging = true;

            this.helper.addClass("ui-sortable-helper");
            this._mouseDrag(event); //Execute the drag once - this causes the helper not to be visible before getting its correct position
            return true;

        },

        _mouseDrag: function(event) {

            //Compute the helpers position
            this.position = this._generatePosition(event);
            this.positionAbs = this._convertPositionTo("absolute");

            if (!this.lastPositionAbs) {
                this.lastPositionAbs = this.positionAbs;
            }

            //Do scrolling
            if (this.options.scroll) {
                var o = this.options, scrolled = false;
                if (this.scrollParent[0] != document && this.scrollParent[0].tagName != 'HTML') {

                    if ((this.overflowOffset.top + this.scrollParent[0].offsetHeight) - event.pageY < o.scrollSensitivity)
                        this.scrollParent[0].scrollTop = scrolled = this.scrollParent[0].scrollTop + o.scrollSpeed;
                    else if (event.pageY - this.overflowOffset.top < o.scrollSensitivity)
                        this.scrollParent[0].scrollTop = scrolled = this.scrollParent[0].scrollTop - o.scrollSpeed;

                    if ((this.overflowOffset.left + this.scrollParent[0].offsetWidth) - event.pageX < o.scrollSensitivity)
                        this.scrollParent[0].scrollLeft = scrolled = this.scrollParent[0].scrollLeft + o.scrollSpeed;
                    else if (event.pageX - this.overflowOffset.left < o.scrollSensitivity)
                        this.scrollParent[0].scrollLeft = scrolled = this.scrollParent[0].scrollLeft - o.scrollSpeed;

                } else {

                    if (event.pageY - zwxJQ(document).scrollTop() < o.scrollSensitivity)
                        scrolled = zwxJQ(document).scrollTop(zwxJQ(document).scrollTop() - o.scrollSpeed);
                    else if (zwxJQ(window).height() - (event.pageY - zwxJQ(document).scrollTop()) < o.scrollSensitivity)
                        scrolled = zwxJQ(document).scrollTop(zwxJQ(document).scrollTop() + o.scrollSpeed);

                    if (event.pageX - zwxJQ(document).scrollLeft() < o.scrollSensitivity)
                        scrolled = zwxJQ(document).scrollLeft(zwxJQ(document).scrollLeft() - o.scrollSpeed);
                    else if (zwxJQ(window).width() - (event.pageX - zwxJQ(document).scrollLeft()) < o.scrollSensitivity)
                        scrolled = zwxJQ(document).scrollLeft(zwxJQ(document).scrollLeft() + o.scrollSpeed);

                }

                if (scrolled !== false && zwxJQ.ui.ddmanager && !o.dropBehaviour)
                    zwxJQ.ui.ddmanager.prepareOffsets(this, event);
            }

            //Regenerate the absolute position used for position checks
            this.positionAbs = this._convertPositionTo("absolute");

            //Set the helper position
            if (!this.options.axis || this.options.axis != "y") this.helper[0].style.left = this.position.left + 'px';
            if (!this.options.axis || this.options.axis != "x") this.helper[0].style.top = this.position.top + 'px';

            //Rearrange
            for (var i = this.items.length - 1; i >= 0; i--) {

                //Cache variables and intersection, continue if no intersection
                var item = this.items[i], itemElement = item.item[0], intersection = this._intersectsWithPointer(item);
                if (!intersection) continue;

                if (itemElement != this.currentItem[0] //cannot intersect with itself
				&& this.placeholder[intersection == 1 ? "next" : "prev"]()[0] != itemElement //no useless actions that have been done before
				&& !zwxJQ.ui.contains(this.placeholder[0], itemElement) //no action if the item moved is the parent of the item checked
				&& (this.options.type == 'semi-dynamic' ? !zwxJQ.ui.contains(this.element[0], itemElement) : true)
                //&& itemElement.parentNode == this.placeholder[0].parentNode // only rearrange items within the same container
			) {

                    this.direction = intersection == 1 ? "down" : "up";

                    if (this.options.tolerance == "pointer" || this._intersectsWithSides(item)) {
                        this._rearrange(event, item);
                    } else {
                        break;
                    }

                    this._trigger("change", event, this._uiHash());
                    break;
                }
            }

            //Post events to containers
            this._contactContainers(event);

            //Interconnect with droppables
            if (zwxJQ.ui.ddmanager) zwxJQ.ui.ddmanager.drag(this, event);

            //Call callbacks
            this._trigger('sort', event, this._uiHash());

            this.lastPositionAbs = this.positionAbs;
            return false;

        },

        _mouseStop: function(event, noPropagation) {
            if (!event) return;
            //If we are using droppables, inform the manager about the drop
            if (zwxJQ.ui.ddmanager && !this.options.dropBehaviour)
                zwxJQ.ui.ddmanager.drop(this, event);

            if (this.options.revert) {
                var self = this;
                var cur = self.placeholder.offset();

                self.reverting = true;

                zwxJQ(this.helper).animate({
                    left: cur.left - this.offset.parent.left - self.margins.left + (this.offsetParent[0] == document.body ? 0 : this.offsetParent[0].scrollLeft),
                    top: cur.top - this.offset.parent.top - self.margins.top + (this.offsetParent[0] == document.body ? 0 : this.offsetParent[0].scrollTop)
                }, parseInt(this.options.revert, 10) || 500, function() {
                    self._clear(event);
                });
            } else {

                this._clear(event, noPropagation);
               // SendHttp("../JHSoft.Web.WorkFlat/persionZonesettingsave.aspx", window.getXml());
            }

            return false;

        },

        cancel: function() {

            var self = this;

            if (this.dragging) {

                this._mouseUp({ target: null });

                if (this.options.helper == "original")
                    this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper");
                else
                    this.currentItem.show();

                //Post deactivating events to containers
                for (var i = this.containers.length - 1; i >= 0; i--) {
                    this.containers[i]._trigger("deactivate", null, self._uiHash(this));
                    if (this.containers[i].containerCache.over) {
                        this.containers[i]._trigger("out", null, self._uiHash(this));
                        this.containers[i].containerCache.over = 0;
                    }
                }

            }

            if (this.placeholder) {
                //zwxJQ(this.placeholder[0]).remove(); would have been the zwxJQ way - unfortunately, it unbinds ALL events from the original node!
                if (this.placeholder[0].parentNode) this.placeholder[0].parentNode.removeChild(this.placeholder[0]);
                if (this.options.helper != "original" && this.helper && this.helper[0].parentNode) this.helper.remove();

                zwxJQ.extend(this, {
                    helper: null,
                    dragging: false,
                    reverting: false,
                    _noFinalSort: null
                });

                if (this.domPosition.prev) {
                    zwxJQ(this.domPosition.prev).after(this.currentItem);
                } else {
                    zwxJQ(this.domPosition.parent).prepend(this.currentItem);
                }
            }

            return this;

        },

        serialize: function(o) {

            var items = this._getItemsAszwxJQ(o && o.connected);
            var str = []; o = o || {};

            zwxJQ(items).each(function() {
                var res = (zwxJQ(o.item || this).attr(o.attribute || 'id') || '').match(o.expression || (/(.+)[-=_](.+)/));
                if (res) str.push((o.key || res[1] + '[]') + '=' + (o.key && o.expression ? res[1] : res[2]));
            });

            if (!str.length && o.key) {
                str.push(o.key + '=');
            }

            return str.join('&');

        },

        toArray: function(o) {

            var items = this._getItemsAszwxJQ(o && o.connected);
            var ret = []; o = o || {};

            items.each(function() { ret.push(zwxJQ(o.item || this).attr(o.attribute || 'id') || ''); });
            return ret;

        },

        /* Be careful with the following core functions */
        _intersectsWith: function(item) {

            var x1 = this.positionAbs.left,
			x2 = x1 + this.helperProportions.width,
			y1 = this.positionAbs.top,
			y2 = y1 + this.helperProportions.height;

            var l = item.left,
			r = l + item.width,
			t = item.top,
			b = t + item.height;

            var dyClick = this.offset.click.top,
			dxClick = this.offset.click.left;

            var isOverElement = (y1 + dyClick) > t && (y1 + dyClick) < b && (x1 + dxClick) > l && (x1 + dxClick) < r;

            if (this.options.tolerance == "pointer"
			|| this.options.forcePointerForContainers
			|| (this.options.tolerance != "pointer" && this.helperProportions[this.floating ? 'width' : 'height'] > item[this.floating ? 'width' : 'height'])
		) {
                return isOverElement;
            } else {

                return (l < x1 + (this.helperProportions.width / 2) // Right Half
				&& x2 - (this.helperProportions.width / 2) < r // Left Half
				&& t < y1 + (this.helperProportions.height / 2) // Bottom Half
				&& y2 - (this.helperProportions.height / 2) < b); // Top Half

            }
        },

        _intersectsWithPointer: function(item) {

            var isOverElementHeight = zwxJQ.ui.isOverAxis(this.positionAbs.top + this.offset.click.top, item.top, item.height),
			isOverElementWidth = zwxJQ.ui.isOverAxis(this.positionAbs.left + this.offset.click.left, item.left, item.width),
			isOverElement = isOverElementHeight && isOverElementWidth,
			verticalDirection = this._getDragVerticalDirection(),
			horizontalDirection = this._getDragHorizontalDirection();

            if (!isOverElement)
                return false;

            return this.floating ?
			(((horizontalDirection && horizontalDirection == "right") || verticalDirection == "down") ? 2 : 1)
			: (verticalDirection && (verticalDirection == "down" ? 2 : 1));

        },

        _intersectsWithSides: function(item) {

            var isOverBottomHalf = zwxJQ.ui.isOverAxis(this.positionAbs.top + this.offset.click.top, item.top + (item.height / 2), item.height),
			isOverRightHalf = zwxJQ.ui.isOverAxis(this.positionAbs.left + this.offset.click.left, item.left + (item.width / 2), item.width),
			verticalDirection = this._getDragVerticalDirection(),
			horizontalDirection = this._getDragHorizontalDirection();

            if (this.floating && horizontalDirection) {
                return ((horizontalDirection == "right" && isOverRightHalf) || (horizontalDirection == "left" && !isOverRightHalf));
            } else {
                return verticalDirection && ((verticalDirection == "down" && isOverBottomHalf) || (verticalDirection == "up" && !isOverBottomHalf));
            }

        },

        _getDragVerticalDirection: function() {
            var delta = this.positionAbs.top - this.lastPositionAbs.top;
            return delta != 0 && (delta > 0 ? "down" : "up");
        },

        _getDragHorizontalDirection: function() {
            var delta = this.positionAbs.left - this.lastPositionAbs.left;
            return delta != 0 && (delta > 0 ? "right" : "left");
        },

        refresh: function(event) {
            this._refreshItems(event);
            this.refreshPositions();
            return this;
        },

        _connectWith: function() {
            var options = this.options;
            return options.connectWith.constructor == String
			? [options.connectWith]
			: options.connectWith;
        },

        _getItemsAszwxJQ: function(connected) {

            var self = this;
            var items = [];
            var queries = [];
            var connectWith = this._connectWith();

            if (connectWith && connected) {
                for (var i = connectWith.length - 1; i >= 0; i--) {
                    var cur = zwxJQ(connectWith[i]);
                    for (var j = cur.length - 1; j >= 0; j--) {
                        var inst = zwxJQ.data(cur[j], 'sortable');
                        if (inst && inst != this && !inst.options.disabled) {
                            queries.push([zwxJQ.isFunction(inst.options.items) ? inst.options.items.call(inst.element) : zwxJQ(inst.options.items, inst.element).not(".ui-sortable-helper").not('.ui-sortable-placeholder'), inst]);
                        }
                    };
                };
            }

            queries.push([zwxJQ.isFunction(this.options.items) ? this.options.items.call(this.element, null, { options: this.options, item: this.currentItem }) : zwxJQ(this.options.items, this.element).not(".ui-sortable-helper").not('.ui-sortable-placeholder'), this]);

            for (var i = queries.length - 1; i >= 0; i--) {
                queries[i][0].each(function() {
                    items.push(this);
                });
            };

            return zwxJQ(items);

        },

        _removeCurrentsFromItems: function() {

            var list = this.currentItem.find(":data(sortable-item)");

            for (var i = 0; i < this.items.length; i++) {

                for (var j = 0; j < list.length; j++) {
                    if (list[j] == this.items[i].item[0])
                        this.items.splice(i, 1);
                };

            };

        },

        _refreshItems: function(event) {

            this.items = [];
            this.containers = [this];
            var items = this.items;
            var self = this;
            var queries = [[zwxJQ.isFunction(this.options.items) ? this.options.items.call(this.element[0], event, { item: this.currentItem }) : zwxJQ(this.options.items, this.element), this]];
            var connectWith = this._connectWith();

            if (connectWith) {
                for (var i = connectWith.length - 1; i >= 0; i--) {
                    var cur = zwxJQ(connectWith[i]);
                    for (var j = cur.length - 1; j >= 0; j--) {
                        var inst = zwxJQ.data(cur[j], 'sortable');
                        if (inst && inst != this && !inst.options.disabled) {
                            queries.push([zwxJQ.isFunction(inst.options.items) ? inst.options.items.call(inst.element[0], event, { item: this.currentItem }) : zwxJQ(inst.options.items, inst.element), inst]);
                            this.containers.push(inst);
                        }
                    };
                };
            }

            for (var i = queries.length - 1; i >= 0; i--) {
                var targetData = queries[i][1];
                var _queries = queries[i][0];

                for (var j = 0, queriesLength = _queries.length; j < queriesLength; j++) {
                    var item = zwxJQ(_queries[j]);

                    item.data('sortable-item', targetData); // Data for target checking (mouse manager)

                    items.push({
                        item: item,
                        instance: targetData,
                        width: 0, height: 0,
                        left: 0, top: 0
                    });
                };
            };

        },

        refreshPositions: function(fast) {

            //This has to be redone because due to the item being moved out/into the offsetParent, the offsetParent's position will change
            if (this.offsetParent && this.helper) {
                this.offset.parent = this._getParentOffset();
            }

            for (var i = this.items.length - 1; i >= 0; i--) {
                var item = this.items[i];

                //We ignore calculating positions of all connected containers when we're not over them
                if (item.instance != this.currentContainer && this.currentContainer && item.item[0] != this.currentItem[0])
                    continue;

                var t = this.options.toleranceElement ? zwxJQ(this.options.toleranceElement, item.item) : item.item;

                if (!fast) {
                    item.width = t.outerWidth();
                    item.height = t.outerHeight();
                }

                var p = t.offset();
                item.left = p.left;
                item.top = p.top;
            };

            if (this.options.custom && this.options.custom.refreshContainers) {
                this.options.custom.refreshContainers.call(this);
            } else {
                for (var i = this.containers.length - 1; i >= 0; i--) {
                    var p = this.containers[i].element.offset();
                    this.containers[i].containerCache.left = p.left;
                    this.containers[i].containerCache.top = p.top;
                    this.containers[i].containerCache.width = this.containers[i].element.outerWidth();
                    this.containers[i].containerCache.height = this.containers[i].element.outerHeight();
                };
            }

            return this;
        },

        _createPlaceholder: function(that) {

            var self = that || this, o = self.options;

            if (!o.placeholder || o.placeholder.constructor == String) {
                var className = o.placeholder;
                o.placeholder = {
                    element: function() {

                        var el = zwxJQ(document.createElement(self.currentItem[0].nodeName))
						.addClass(className || self.currentItem[0].className + " ui-sortable-placeholder")
						.removeClass("ui-sortable-helper")[0];

                        if (!className)
                            el.style.visibility = "hidden";

                        return el;
                    },
                    update: function(container, p) {

                        // 1. If a className is set as 'placeholder option, we don't force sizes - the class is responsible for that
                        // 2. The option 'forcePlaceholderSize can be enabled to force it even if a class name is specified
                        if (className && !o.forcePlaceholderSize) return;

                        //If the element doesn't have a actual height by itself (without styles coming from a stylesheet), it receives the inline height from the dragged item
                        if (!p.height()) { p.height(self.currentItem.innerHeight() - parseInt(self.currentItem.css('paddingTop') || 0, 10) - parseInt(self.currentItem.css('paddingBottom') || 0, 10)); };
                        if (!p.width()) { p.width(self.currentItem.innerWidth() - parseInt(self.currentItem.css('paddingLeft') || 0, 10) - parseInt(self.currentItem.css('paddingRight') || 0, 10)); };
                    }
                };
            }

            //Create the placeholder
            self.placeholder = zwxJQ(o.placeholder.element.call(self.element, self.currentItem));

            //Append it after the actual current item
            self.currentItem.after(self.placeholder);

            //Update the size of the placeholder (TODO: Logic to fuzzy, see line 316/317)
            o.placeholder.update(self, self.placeholder);

        },

        _contactContainers: function(event) {

            // get innermost container that intersects with item 
            var innermostContainer = null, innermostIndex = null;


            for (var i = this.containers.length - 1; i >= 0; i--) {

                // never consider a container that's located within the item itself 
                if (zwxJQ.ui.contains(this.currentItem[0], this.containers[i].element[0]))
                    continue;

                if (this._intersectsWith(this.containers[i].containerCache)) {

                    // if we've already found a container and it's more "inner" than this, then continue 
                    if (innermostContainer && zwxJQ.ui.contains(this.containers[i].element[0], innermostContainer.element[0]))
                        continue;

                    innermostContainer = this.containers[i];
                    innermostIndex = i;

                } else {
                    // container doesn't intersect. trigger "out" event if necessary 
                    if (this.containers[i].containerCache.over) {
                        this.containers[i]._trigger("out", event, this._uiHash(this));
                        this.containers[i].containerCache.over = 0;
                    }
                }

            }

            // if no intersecting containers found, return 
            if (!innermostContainer) return;

            // move the item into the container if it's not there already
            if (this.containers.length === 1) {
                this.containers[innermostIndex]._trigger("over", event, this._uiHash(this));
                this.containers[innermostIndex].containerCache.over = 1;
            } else if (this.currentContainer != this.containers[innermostIndex]) {

                //When entering a new container, we will find the item with the least distance and append our item near it 
                var dist = 10000; var itemWithLeastDistance = null; var base = this.positionAbs[this.containers[innermostIndex].floating ? 'left' : 'top'];
                for (var j = this.items.length - 1; j >= 0; j--) {
                    if (!zwxJQ.ui.contains(this.containers[innermostIndex].element[0], this.items[j].item[0])) continue;
                    var cur = this.items[j][this.containers[innermostIndex].floating ? 'left' : 'top'];
                    if (Math.abs(cur - base) < dist) {
                        dist = Math.abs(cur - base); itemWithLeastDistance = this.items[j];
                    }
                }

                if (!itemWithLeastDistance && !this.options.dropOnEmpty) //Check if dropOnEmpty is enabled 
                    return;

                this.currentContainer = this.containers[innermostIndex];
                itemWithLeastDistance ? this._rearrange(event, itemWithLeastDistance, null, true) : this._rearrange(event, null, this.containers[innermostIndex].element, true);
                this._trigger("change", event, this._uiHash());
                this.containers[innermostIndex]._trigger("change", event, this._uiHash(this));

                //Update the placeholder 
                this.options.placeholder.update(this.currentContainer, this.placeholder);

                this.containers[innermostIndex]._trigger("over", event, this._uiHash(this));
                this.containers[innermostIndex].containerCache.over = 1;
            }


        },

        _createHelper: function(event) {

            var o = this.options;
            var helper = zwxJQ.isFunction(o.helper) ? zwxJQ(o.helper.apply(this.element[0], [event, this.currentItem])) : (o.helper == 'clone' ? this.currentItem.clone() : this.currentItem);

            if (!helper.parents('body').length) //Add the helper to the DOM if that didn't happen already
                zwxJQ(o.appendTo != 'parent' ? o.appendTo : this.currentItem[0].parentNode)[0].appendChild(helper[0]);

            if (helper[0] == this.currentItem[0])
                this._storedCSS = { width: this.currentItem[0].style.width, height: this.currentItem[0].style.height, position: this.currentItem.css("position"), top: this.currentItem.css("top"), left: this.currentItem.css("left") };

            if (helper[0].style.width == '' || o.forceHelperSize) helper.width(this.currentItem.width());
            if (helper[0].style.height == '' || o.forceHelperSize) helper.height(this.currentItem.height());

            return helper;

        },

        _adjustOffsetFromHelper: function(obj) {
            if (typeof obj == 'string') {
                obj = obj.split(' ');
            }
            if (zwxJQ.isArray(obj)) {
                obj = { left: +obj[0], top: +obj[1] || 0 };
            }
            if ('left' in obj) {
                this.offset.click.left = obj.left + this.margins.left;
            }
            if ('right' in obj) {
                this.offset.click.left = this.helperProportions.width - obj.right + this.margins.left;
            }
            if ('top' in obj) {
                this.offset.click.top = obj.top + this.margins.top;
            }
            if ('bottom' in obj) {
                this.offset.click.top = this.helperProportions.height - obj.bottom + this.margins.top;
            }
        },

        _getParentOffset: function() {


            //Get the offsetParent and cache its position
            this.offsetParent = this.helper.offsetParent();
            var po = this.offsetParent.offset();

            // This is a special case where we need to modify a offset calculated on start, since the following happened:
            // 1. The position of the helper is absolute, so it's position is calculated based on the next positioned parent
            // 2. The actual offset parent is a child of the scroll parent, and the scroll parent isn't the document, which means that
            //    the scroll is included in the initial calculation of the offset of the parent, and never recalculated upon drag
            if (this.cssPosition == 'absolute' && this.scrollParent[0] != document && zwxJQ.ui.contains(this.scrollParent[0], this.offsetParent[0])) {
                po.left += this.scrollParent.scrollLeft();
                po.top += this.scrollParent.scrollTop();
            }

            if ((this.offsetParent[0] == document.body) //This needs to be actually done for all browsers, since pageX/pageY includes this information
		|| (this.offsetParent[0].tagName && this.offsetParent[0].tagName.toLowerCase() == 'html' && zwxJQ.browser.msie)) //Ugly IE fix
                po = { top: 0, left: 0 };

            return {
                top: po.top + (parseInt(this.offsetParent.css("borderTopWidth"), 10) || 0),
                left: po.left + (parseInt(this.offsetParent.css("borderLeftWidth"), 10) || 0)
            };

        },

        _getRelativeOffset: function() {

            if (this.cssPosition == "relative") {
                var p = this.currentItem.position();
                return {
                    top: p.top - (parseInt(this.helper.css("top"), 10) || 0) + this.scrollParent.scrollTop(),
                    left: p.left - (parseInt(this.helper.css("left"), 10) || 0) + this.scrollParent.scrollLeft()
                };
            } else {
                return { top: 0, left: 0 };
            }

        },

        _cacheMargins: function() {
            this.margins = {
                left: (parseInt(this.currentItem.css("marginLeft"), 10) || 0),
                top: (parseInt(this.currentItem.css("marginTop"), 10) || 0)
            };
        },

        _cacheHelperProportions: function() {
            this.helperProportions = {
                width: this.helper.outerWidth(),
                height: this.helper.outerHeight()
            };
        },

        _setContainment: function() {

            var o = this.options;
            if (o.containment == 'parent') o.containment = this.helper[0].parentNode;
            if (o.containment == 'document' || o.containment == 'window') this.containment = [
			0 - this.offset.relative.left - this.offset.parent.left,
			0 - this.offset.relative.top - this.offset.parent.top,
			zwxJQ(o.containment == 'document' ? document : window).width() - this.helperProportions.width - this.margins.left,
			(zwxJQ(o.containment == 'document' ? document : window).height() || document.body.parentNode.scrollHeight) - this.helperProportions.height - this.margins.top
		];

            if (!(/^(document|window|parent)zwxJQ/).test(o.containment)) {
                var ce = zwxJQ(o.containment)[0];
                var co = zwxJQ(o.containment).offset();
                var over = (zwxJQ(ce).css("overflow") != 'hidden');

                this.containment = [
				co.left + (parseInt(zwxJQ(ce).css("borderLeftWidth"), 10) || 0) + (parseInt(zwxJQ(ce).css("paddingLeft"), 10) || 0) - this.margins.left,
				co.top + (parseInt(zwxJQ(ce).css("borderTopWidth"), 10) || 0) + (parseInt(zwxJQ(ce).css("paddingTop"), 10) || 0) - this.margins.top,
				co.left + (over ? Math.max(ce.scrollWidth, ce.offsetWidth) : ce.offsetWidth) - (parseInt(zwxJQ(ce).css("borderLeftWidth"), 10) || 0) - (parseInt(zwxJQ(ce).css("paddingRight"), 10) || 0) - this.helperProportions.width - this.margins.left,
				co.top + (over ? Math.max(ce.scrollHeight, ce.offsetHeight) : ce.offsetHeight) - (parseInt(zwxJQ(ce).css("borderTopWidth"), 10) || 0) - (parseInt(zwxJQ(ce).css("paddingBottom"), 10) || 0) - this.helperProportions.height - this.margins.top
			];
            }

        },

        _convertPositionTo: function(d, pos) {

            if (!pos) pos = this.position;
            var mod = d == "absolute" ? 1 : -1;
            var o = this.options, scroll = this.cssPosition == 'absolute' && !(this.scrollParent[0] != document && zwxJQ.ui.contains(this.scrollParent[0], this.offsetParent[0])) ? this.offsetParent : this.scrollParent, scrollIsRootNode = (/(html|body)/i).test(scroll[0].tagName);

            return {
                top: (
				pos.top																	// The absolute mouse position
				+ this.offset.relative.top * mod										// Only for relative positioned nodes: Relative offset from element to offset parent
				+ this.offset.parent.top * mod											// The offsetParent's offset without borders (offset + border)
				- (zwxJQ.browser.safari && this.cssPosition == 'fixed' ? 0 : (this.cssPosition == 'fixed' ? -this.scrollParent.scrollTop() : (scrollIsRootNode ? 0 : scroll.scrollTop())) * mod)
			),
                left: (
				pos.left																// The absolute mouse position
				+ this.offset.relative.left * mod										// Only for relative positioned nodes: Relative offset from element to offset parent
				+ this.offset.parent.left * mod											// The offsetParent's offset without borders (offset + border)
				- (zwxJQ.browser.safari && this.cssPosition == 'fixed' ? 0 : (this.cssPosition == 'fixed' ? -this.scrollParent.scrollLeft() : scrollIsRootNode ? 0 : scroll.scrollLeft()) * mod)
			)
            };

        },

        _generatePosition: function(event) {

            var o = this.options, scroll = this.cssPosition == 'absolute' && !(this.scrollParent[0] != document && zwxJQ.ui.contains(this.scrollParent[0], this.offsetParent[0])) ? this.offsetParent : this.scrollParent, scrollIsRootNode = (/(html|body)/i).test(scroll[0].tagName);

            // This is another very weird special case that only happens for relative elements:
            // 1. If the css position is relative
            // 2. and the scroll parent is the document or similar to the offset parent
            // we have to refresh the relative offset during the scroll so there are no jumps
            if (this.cssPosition == 'relative' && !(this.scrollParent[0] != document && this.scrollParent[0] != this.offsetParent[0])) {
                this.offset.relative = this._getRelativeOffset();
            }

            var pageX = event.pageX;
            var pageY = event.pageY;

            /*
            * - Position constraining -
            * Constrain the position to a mix of grid, containment.
            */

            if (this.originalPosition) { //If we are not dragging yet, we won't check for options

                if (this.containment) {
                    if (event.pageX - this.offset.click.left < this.containment[0]) pageX = this.containment[0] + this.offset.click.left;
                    if (event.pageY - this.offset.click.top < this.containment[1]) pageY = this.containment[1] + this.offset.click.top;
                    if (event.pageX - this.offset.click.left > this.containment[2]) pageX = this.containment[2] + this.offset.click.left;
                    if (event.pageY - this.offset.click.top > this.containment[3]) pageY = this.containment[3] + this.offset.click.top;
                }

                if (o.grid) {
                    var top = this.originalPageY + Math.round((pageY - this.originalPageY) / o.grid[1]) * o.grid[1];
                    pageY = this.containment ? (!(top - this.offset.click.top < this.containment[1] || top - this.offset.click.top > this.containment[3]) ? top : (!(top - this.offset.click.top < this.containment[1]) ? top - o.grid[1] : top + o.grid[1])) : top;

                    var left = this.originalPageX + Math.round((pageX - this.originalPageX) / o.grid[0]) * o.grid[0];
                    pageX = this.containment ? (!(left - this.offset.click.left < this.containment[0] || left - this.offset.click.left > this.containment[2]) ? left : (!(left - this.offset.click.left < this.containment[0]) ? left - o.grid[0] : left + o.grid[0])) : left;
                }

            }

            return {
                top: (
				pageY																// The absolute mouse position
				- this.offset.click.top													// Click offset (relative to the element)
				- this.offset.relative.top												// Only for relative positioned nodes: Relative offset from element to offset parent
				- this.offset.parent.top												// The offsetParent's offset without borders (offset + border)
				+ (zwxJQ.browser.safari && this.cssPosition == 'fixed' ? 0 : (this.cssPosition == 'fixed' ? -this.scrollParent.scrollTop() : (scrollIsRootNode ? 0 : scroll.scrollTop())))
			),
                left: (
				pageX																// The absolute mouse position
				- this.offset.click.left												// Click offset (relative to the element)
				- this.offset.relative.left												// Only for relative positioned nodes: Relative offset from element to offset parent
				- this.offset.parent.left												// The offsetParent's offset without borders (offset + border)
				+ (zwxJQ.browser.safari && this.cssPosition == 'fixed' ? 0 : (this.cssPosition == 'fixed' ? -this.scrollParent.scrollLeft() : scrollIsRootNode ? 0 : scroll.scrollLeft()))
			)
            };

        },

        _rearrange: function(event, i, a, hardRefresh) {

            a ? a[0].appendChild(this.placeholder[0]) : i.item[0].parentNode.insertBefore(this.placeholder[0], (this.direction == 'down' ? i.item[0] : i.item[0].nextSibling));

            //Various things done here to improve the performance:
            // 1. we create a setTimeout, that calls refreshPositions
            // 2. on the instance, we have a counter variable, that get's higher after every append
            // 3. on the local scope, we copy the counter variable, and check in the timeout, if it's still the same
            // 4. this lets only the last addition to the timeout stack through
            this.counter = this.counter ? ++this.counter : 1;
            var self = this, counter = this.counter;

            window.setTimeout(function() {
                if (counter == self.counter) self.refreshPositions(!hardRefresh); //Precompute after each DOM insertion, NOT on mousemove
            }, 0);

        },

        _clear: function(event, noPropagation) {

            this.reverting = false;
            // We delay all events that have to be triggered to after the point where the placeholder has been removed and
            // everything else normalized again
            var delayedTriggers = [], self = this;

            // We first have to update the dom position of the actual currentItem
            // Note: don't do it if the current item is already removed (by a user), or it gets reappended (see #4088)
            if (!this._noFinalSort && this.currentItem.parent().length) this.placeholder.before(this.currentItem);
            this._noFinalSort = null;

            if (this.helper[0] == this.currentItem[0]) {
                for (var i in this._storedCSS) {
                    if (this._storedCSS[i] == 'auto' || this._storedCSS[i] == 'static') this._storedCSS[i] = '';
                }
                this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper");
            } else {
                this.currentItem.show();
            }

            if (this.fromOutside && !noPropagation) delayedTriggers.push(function(event) { this._trigger("receive", event, this._uiHash(this.fromOutside)); });
            if ((this.fromOutside || this.domPosition.prev != this.currentItem.prev().not(".ui-sortable-helper")[0] || this.domPosition.parent != this.currentItem.parent()[0]) && !noPropagation) delayedTriggers.push(function(event) { this._trigger("update", event, this._uiHash()); }); //Trigger update callback if the DOM position has changed
            if (!zwxJQ.ui.contains(this.element[0], this.currentItem[0])) { //Node was moved out of the current element
                if (!noPropagation) delayedTriggers.push(function(event) { this._trigger("remove", event, this._uiHash()); });
                for (var i = this.containers.length - 1; i >= 0; i--) {
                    if (zwxJQ.ui.contains(this.containers[i].element[0], this.currentItem[0]) && !noPropagation) {
                        delayedTriggers.push((function(c) { return function(event) { c._trigger("receive", event, this._uiHash(this)); }; }).call(this, this.containers[i]));
                        delayedTriggers.push((function(c) { return function(event) { c._trigger("update", event, this._uiHash(this)); }; }).call(this, this.containers[i]));
                    }
                };
            };

            //Post events to containers
            for (var i = this.containers.length - 1; i >= 0; i--) {
                if (!noPropagation) delayedTriggers.push((function(c) { return function(event) { c._trigger("deactivate", event, this._uiHash(this)); }; }).call(this, this.containers[i]));
                if (this.containers[i].containerCache.over) {
                    delayedTriggers.push((function(c) { return function(event) { c._trigger("out", event, this._uiHash(this)); }; }).call(this, this.containers[i]));
                    this.containers[i].containerCache.over = 0;
                }
            }

            //Do what was originally in plugins
            if (this._storedCursor) zwxJQ('body').css("cursor", this._storedCursor); //Reset cursor
            if (this._storedOpacity) this.helper.css("opacity", this._storedOpacity); //Reset opacity
            if (this._storedZIndex) this.helper.css("zIndex", this._storedZIndex == 'auto' ? '' : this._storedZIndex); //Reset z-index

            this.dragging = false;
            if (this.cancelHelperRemoval) {
                if (!noPropagation) {
                    this._trigger("beforeStop", event, this._uiHash());
                    for (var i = 0; i < delayedTriggers.length; i++) { delayedTriggers[i].call(this, event); }; //Trigger all delayed events
                    this._trigger("stop", event, this._uiHash());
                }
                return false;
            }

            if (!noPropagation) this._trigger("beforeStop", event, this._uiHash());

            //zwxJQ(this.placeholder[0]).remove(); would have been the zwxJQ way - unfortunately, it unbinds ALL events from the original node!
            this.placeholder[0].parentNode.removeChild(this.placeholder[0]);

            if (this.helper[0] != this.currentItem[0]) this.helper.remove(); this.helper = null;

            if (!noPropagation) {
                for (var i = 0; i < delayedTriggers.length; i++) { delayedTriggers[i].call(this, event); }; //Trigger all delayed events
                this._trigger("stop", event, this._uiHash());
            }

            this.fromOutside = false;
            return true;

        },

        _trigger: function() {
            if (zwxJQ.Widget.prototype._trigger.apply(this, arguments) === false) {
                this.cancel();
            }
        },

        _uiHash: function(inst) {
            var self = inst || this;
            return {
                helper: self.helper,
                placeholder: self.placeholder || zwxJQ([]),
                position: self.position,
                originalPosition: self.originalPosition,
                offset: self.positionAbs,
                item: self.currentItem,
                sender: inst ? inst.element : null
            };
        }

    });

    zwxJQ.extend(zwxJQ.ui.sortable, {
        version: "1.8.15"
    });

})(zwxJQ);
