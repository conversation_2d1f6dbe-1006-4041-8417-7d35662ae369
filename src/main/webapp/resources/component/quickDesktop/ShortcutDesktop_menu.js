/**
 * 菜单桌面
 */
var elemWid = 1024; //桌面宽度
var desktopNum = 1; //桌面数
var maxDesktopNum = 10; //最大桌面数
var maxMenuNum =24; //每个桌面最大菜单数

var tdmifa = "目标桌面菜单达到上限"; //目标桌面菜单达到上限。
var mkad = "必须保留一个桌面"; //必须保留一个桌面。
var isPc = !("orientation" in window && "onorientationchange" in window); //是否PC客户端
var isFirefox = navigator.userAgent.toLowerCase().match(/firefox/) != null; //是否Chrome浏览器
var isChrome = navigator.userAgent.toLowerCase().match(/chrome/) != null; //是否Chrome浏览器
var isSafari = zwxJQ.browser.safari; //是否Safari浏览器
var region = 'html';
if (isChrome || isSafari) {
    region = 'body';
} else {
    region = 'html';
}
//页面加载
zwxJQ(document).ready(function() {
    elemWid = zwxJQ(window).width();
    InitSD();
    //inntMenu2();
    InitEvent();
});

var Menu1 = null;    //暂存菜单1
var Menu2 = null;    //暂存菜单1
var Menu3 = null;    //暂存菜单1
var currNo=1;//当前桌号
var currAdd=null;//当前位置对应的上级位置说明
//初始化快捷桌面
function InitSD() {
	//加载一级菜单	
	 currAdd="";no=1;
	 var keyhidd = "";
     if (Menu1 == null) {
         keyhidd = this.GetMenu1();
     }
     else {
         keyhidd = Menu1;
     }
    if(keyhidd.indexOf("SESSIONOUT")!=-1){
        forwardLoutBtn();
        return;
    }
     var classname = "";
     this.Llen = 0;
     //取一级菜单
     var desktopList = [];
     var keyhidds= keyhidd.split("@#@");   	
     if(undefined!=keyhidds&&keyhidds.length>0){
    	 desktopList.push('<ul id="1UL" class="desktopList">');
     for(var i=0;i<keyhidds.length;i++){     	
     	 var defimg="/resources/component/quickDesktop/image/Folder.png";         
         var bigimg = keyhidds[i].split(",_,")[3];
         if(undefined==bigimg||null==bigimg||""==bigimg||""==newTrim(bigimg)||"null"==newTrim(bigimg)){
        	 bigimg=defimg;
         }else{
        	 bigimg="/resources/component/quickDesktop/image/64px/"+bigimg;
         }
         //图片地图，对于设置有地图的进行处理
         var fullname = name;
         var title = "";
         var keyvs= keyhidds[i].split(",_,")[0];
         var namevs= keyhidds[i].split(",_,")[1];
         desktopList.push('<li id="'+keyvs+'" code="'+keyvs+'" optype="1" openType="'+namevs+'" url="#">');
	     desktopList.push('<a href="javascript:void(0)" title="' + namevs + '">');
	     desktopList.push('<img src="'+bigimg+'"  with ="64px" height="64px"/>');
	     desktopList.push('<span>'+namevs+'</span>');
	     desktopList.push('</a>');
	     desktopList.push('</li>');
	     
     }
     	desktopList.push('</ul>'); 
     	 desktopList.push('<ul id="2UL" class="desktopList"></ul><ul id="3UL" class="desktopList"></ul>');
     }
   
 	    zwxJQ('.ulList').html(desktopList.join(""));
 	    AdjustULWidth();
 	    

}

//取1级菜单的文档
 function GetMenu1() {
    var xmldom;
      var req = new XMLHttpRequest();
      req.onreadystatechange = function(){
    	  if (req.readyState == 4&&req.status == 200) { 
    	  xmldom =  req.responseText; 
    	  Menu1=xmldom;
    	  }
      };
      try {
      	req.open("POST", "/newShortcutDesktopAjax?flag=ShowFirstMenu", false); //was get
      } catch (e) {
        alert("Problem Communicating with Server\n"+e);
      }
      req.send(null);
    
    return xmldom;
}
//取2级菜单的文档
 function GetMenu2() {
    var xmldom;
    var req = new XMLHttpRequest();
      req.onreadystatechange = function(){
    	  if (req.readyState == 4&&req.status == 200) { 
    	  xmldom =  req.responseText; 
    	  Menu2=xmldom;
    	  }
      };
      try {
      	req.open("POST", "newShortcutDesktopAjax?flag=ShowSecondMenu", false); //was get
      } catch (e) {
        alert("Problem Communicating with Server\n"+e);
      }
      req.send(null);
    return xmldom;
}

//取3级菜单的文档
 function GetMenu3() {
    var xmldom;
    var req = new XMLHttpRequest();
      req.onreadystatechange = function(){
    	  if (req.readyState == 4&&req.status == 200) { 
    	  xmldom =  req.responseText; 
    	  Menu3=xmldom;}
      };
      try {
      	req.open("POST", "newShortcutDesktopAjax?flag=ShowThirdMenu", false); //was get
      } catch (e) {
        alert("Problem Communicating with Server\n"+e);
      }
      req.send(null);
    return xmldom;
}
 
 //返回上一级菜单
 function upMenuFun(){
	 AdjustULWidth();
	 if(no==2){
		 //从第二个桌面进行返回
		   //去除返回按钮
		    no=1;
	    	currAdd="";
	    	zwxJQ('.backLink').html('');
		    zwxJQ('.desktopNav').html('');
			 zwxJQ('.ulList').width(elemWid*3);
	 }else{
		 //从第三个桌面进行返回
		 no=2;
		 if(currAdd!=""&&currAdd!=null){
			 currAdd=currAdd.split("<font")[0]; 
		     document.getElementById("pathSpan").innerHTML=currAdd;
		 }
		 zwxJQ('.ulList').width(elemWid*3);
		 
	 }
	 zwxJQ('.ulList').animate({
	        'margin-left': '-' + elemWid * (no-1) + 'px'
	    }, 500);
	
	    
	 
 } 
 function newTrim(text)
 {
     return text.replace(/^\s*|\s*$/g,"");
 }
//菜单单击事件：打开页签
function ClickMenuToOpen(menu) {
    var id = menu.attr("id");
    
    var name = menu.find("a").attr("title");
    var url = menu.attr("url");
    var optype = menu.attr("optype");//层级标记
	   var openType = menu.attr("openType");//菜单名称
	   var ifPop = menu.attr("ifPop");
	   
	   if(optype==1){
		   //添加返回按钮
		    zwxJQ('.backLink').html('<a href="javascript:;"  title="返回上级"  onclick="upMenuFun()"></a>');
		    zwxJQ('.desktopNav').html('<span id="pathSpan" style="color:#ffffff;font-size: 22px;font-family: 微软雅黑;"></span>');
	   }
  
    	//滑层显示二级或下级菜单
    	if(currAdd==""||currAdd==null){
    		currAdd=""+openType;
    	}else{
    		currAdd+="　<font color='#fffff'>》</font>　"+openType;
    	}
    	var code =menu.attr("code");
     if(optype==1||url=="#"){
    	  //滑动并加载
     	document.getElementById("pathSpan").innerHTML=openType;
    	InitSDs(optype,code);
    }else{
    	//打开菜单对应页签
         if(url.indexOf("Dialog")!=-1){
             document.getElementById("dialogDeskForm:dialogName").value=url;
             showDialog();
         }else{
             top.ShortcutMenuClickNew(openType, name, url, id,ifPop);
         }
    }
}
//二级或三级子菜单
function InitSDs(leavl,id) {
	 zwxJQ('.ulList').animate({
	        'margin-left': '-' + elemWid * parseInt(leavl,10) + 'px'
	    }, 500);
	 var keyhidd = "";
	 var optype=parseInt(leavl,10)+1;
	 no=optype;
	 var desktopList = [];
	 if(leavl==1){
		 //加载二级
		 if (Menu2 == null) {
	         keyhidd = this.GetMenu2();
	     }
	     else {
	         keyhidd = Menu2;
	     }
	 }else{
		 //加载三级
		 if (Menu3 == null) {
	         keyhidd = this.GetMenu3();
	     }
	     else {
	         keyhidd = Menu3;
	     }
	 }
    if(keyhidd.indexOf("SESSIONOUT")!=-1){
        forwardLoutBtn();
        return;
    }
     var classname = "";
     this.Llen = 0;     
    
  	var valhidds= keyhidd.split("@#@");  
    if(undefined!=valhidds&&valhidds.length>0){
    	
    	
//    	desktopList.push('<ul  class="desktopList">');
    	for(var i=0;i<valhidds.length;i++){
    		var valobj=valhidds[i];
    		var idobj=valobj.split(",_,")[0];
    		
     	   if(idobj!=""&&idobj==id){
    	//包含记录子菜单
    		   var urlval=valobj.split(",_,")[2];  
    			 var defimg=urlval=="#"?"/resources/component/quickDesktop/image/Folder.png":"/resources/component/quickDesktop/image/Run.png";   
    			 var imgIndex=optype==2?5:4;
    	         var bigimg = valobj.split(",_,")[imgIndex];
    	         if(undefined==bigimg||null==bigimg||""==bigimg||""==newTrim(bigimg)||"null"==newTrim(bigimg)){
    	        	 bigimg=defimg;
    	         }else{
    	        	 bigimg="/resources/component/quickDesktop/image/64px/"+bigimg;
    	         }
    	         //图片地图，对于设置有地图的进行处理
    	         var title = "";

    	         var code=optype==2?(valobj.split(",_,")[4]):(i+"N");
                 var rid=optype==2?6:5;
                  var keyvs=valobj.split(",_,")[rid];
        		 
    	         var namevs= valobj.split(",_,")[1];
    	         var ifPop = optype==2? valobj.split(",_,")[7] : valobj.split(",_,")[6];
    	         desktopList.push('<li id="'+keyvs+'" code="'+code+'" optype="'+optype+'" openType="'+namevs+'" url="'+urlval+'" ifPop="'+ifPop+'">');
    		     desktopList.push('<a href="javascript:void(0)" title="' + namevs + '">');
    		     desktopList.push('<img src="'+bigimg+'" with ="64px" height="64px"/>');
    		     desktopList.push('<span>'+namevs+'</span>');
    		     desktopList.push('</a>');
    		     desktopList.push('</li>'); 
    	   }}
  
//    	desktopList.push('</ul>'); 
       }
    document.getElementById(optype+"UL").innerHTML=desktopList.join("");
//    zwxJQ(optype+'UL').html(desktopList.join(""));
    AdjustULWidth();
    zwxJQ('.ulList').width(elemWid * 3);
   
}

//初始化事件
function InitEvent() {
    //禁止选中
	zwxJQ(document).bind("selectstart", function() { return false; });
	//菜单单击事件
	zwxJQ('.desktopList li[id]').live('click', function() {
        var menu = zwxJQ(this);
        if (!menu.hasClass("hoverMid")) {
            ClickMenuToOpen(menu);
        }
    });
	 //页面尺寸改变事件
    zwxJQ(window).resize(function() {
        elemWid = zwxJQ(window).width();
        AdjustULWidth();
    });
} 

//调整桌面宽度
function AdjustULWidth() {
    if (elemWid >= 1024) {
        zwxJQ('ul.desktopList').width(elemWid * 0.86).css('padding-left', elemWid * 0.07).css('padding-right', elemWid * 0.07);
    } else {
        zwxJQ('ul.desktopList').width(elemWid * 0.9).css('padding-left', elemWid * 0.05).css('padding-right', elemWid * 0.05);
    }
    zwxJQ('.mainList').width(elemWid);
    zwxJQ('.desktopNavL').css('margin-left', 72);
    zwxJQ('.ulList').width(elemWid*3);
}

