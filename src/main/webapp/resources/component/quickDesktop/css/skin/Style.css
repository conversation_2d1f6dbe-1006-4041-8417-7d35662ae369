body
{
}
.white
{
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF', endColorStr='#DEDFE7', gradientType='0');
}
.blue
{
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF', endColorStr='#6495ed', gradientType='0');
}
.red
{
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF', endColorStr='red', gradientType='0');
}
.selectedtag
{
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='LightGrey', endColorStr='#fff5d1', gradientType='0');
	}
.un_white
{
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#DEDFE7', endColorStr='#dbffff', gradientType='0');
}
.navigationbar
{
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF', endColorStr='LightBlue', gradientType='0');
	}
