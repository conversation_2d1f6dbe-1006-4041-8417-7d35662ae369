@charset "utf-8";
/*
 *Layout
 */
html, body {
	/*	overflow-x:hidden;
	overflow-y:hidden;
*/	min-width:100px;
	min-height:100px;
}
a.level-1 {
	color: #003299!important;
	text-decoration:none;
}
a.level-1:hover {
	color:#0000CC!important;
	text-decoration:underline;
}
.c6ui-f12 {
	font-size:12px!important;
}
.c6ui-f14 {
	font-size:14px!important;
}
.c6ui-f16 {
	font-size:16px!important;
}
#c6ui-header {
	background:#ccc; /*height:100px;*/
	position:relative;
}
#c6ui-header-extend {
	height:58px;
}
#c6ui-header-extend .c6ui-header-mark {
	height:13px;
	background-color: transparent;
	background-color: rgba(255, 255, 255, 0.3); /* FF3+, Saf3+, Opera 10.10+, Chrome, IE9 */
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#33FFFFFF, endColorstr=#33FFFFFF); /* IE6–IE9 */
	zoom: 1;
}
#c6ui-header-extend .c6ui-header-system {
	height:26px;
	text-align:right;
	position:relative;
	top:3px;
	_top:-16px;/*hack IE6*/
}
#c6ui-header-extend .c6ui-header-system a {
	height:20px;
	padding-left:3px;
	display:inline-block;
}
#c6ui-header-extend .c6ui-header-system span.c6ui-widget-btn-single {
	height:26px;
}
#c6ui-header-extend .c6ui-header-system span.c6ui-widget-btn-single img {
margin-top:5px;
}
#c6ui-header-extend .c6ui-header-system a:hover, #c6ui-header-min-menu a:hover, #c6ui-header-min-widget a:hover {
	background:url(images/base-a-hover.png) no-repeat 0 0;
}
#c6ui-header-extend .c6ui-header-system a:active, #c6ui-header-min-menu a:active, #c6ui-header-min-widget a:active {
	background:url(images/base-a-hover.png) no-repeat 0 -52px;
}
#c6ui-header-extend .c6ui-header-system a span {
	height:20px;
	display:inline-block;
	padding:0 3px 0 0;
}
#c6ui-header-extend .c6ui-header-system a:hover span, #c6ui-header-min-menu a:hover span, #c6ui-header-min-widget a:hover span {
	background:url(images/base-a-hover.png) no-repeat right -26px;
}
#c6ui-header-extend .c6ui-header-system a:active span, #c6ui-header-min-menu a:active span, #c6ui-header-min-widget a:active span {
	background:url(images/base-a-hover.png) no-repeat right -78px;
}
#c6ui-header-extend .c6ui-header-system a span img, #c6ui-header-min-menu a span img, #c6ui-header-min-widget a span img {
	margin:4px 0 -3px 0;
}
#c6ui-header-extend .c6ui-header-system span {
*vertical-align:middle;
}
/*system close*/
#c6ui-header-extend .c6ui-header-system a.system-close {
	height:20px;
	width:30px;
	padding-left:3px;
	display:inline-block;
	background:url(images/c6ui-close.png);
}
#c6ui-header-extend .c6ui-header-system a.system-close:hover {
	height:20px;
	width:30px;
	padding-left:3px;
	display:inline-block;
	background:url(images/c6ui-close.png);
	background-position:0 -21px;
}
#c6ui-header-extend .c6ui-header-system a.system-close:active  {
	height:20px;
	width:30px;
	padding-left:3px;
	display:inline-block;
	background:url(images/c6ui-close.png);
	background-position:0 -63px;
}
#c6ui-header-extend .c6ui-header-system a.system-close span {
	height:20px;
	width:27px;
	display:inline-block;
	padding:0 3px 0 0;
	text-align:center;
	}
#c6ui-header-extend .c6ui-header-system a.system-close:hover span {
	height:20px;
	width:27px;
	display:inline-block;
	padding:0 3px 0 0;
	text-align:center;
	background:url(images/c6ui-close.png);
	background-position:0 -42px;
	}
#c6ui-header-extend .c6ui-header-system a.system-close:active span {
	height:20px;
	width:27px;
	display:inline-block;
	padding:0 3px 0 0;
	text-align:center;
	background:url(images/c6ui-close.png);
	background-position:0 -84px;
	}
#c6ui-header-min {
	height:32px;/*background-color:#FFFFCC;*/
	background:url(images/top_border.png) center bottom repeat-x;
}
#c6ui-header-min-menu, #c6ui-header-min-tab, #c6ui-header-min-widget {
	float:left;
	height:26px;
	line-height:26px;
	padding:3px 0;
	_height:26px;
	_line-height:26px;
}
#c6ui-header-min-menu { /*background-color:#006699;*/
	padding-left:6px;
}
#c6ui-header-min-menu a, #c6ui-header-min-widget a {
	height:26px;
	padding-left:3px;
	display:inline-block;
	color:#FFFFFF;
}
#c6ui-header-min-menu a span, #c6ui-header-min-widget a span {
	padding: 0 2px 0 3px;
	display:inline-block;
	white-space:nowrap;
}
#c6ui-header-min-tab {
	background-color:transparent;
	margin:0 6px;
}
#c6ui-header-subpage {
	border-bottom:#bbb solid 1px;
	height:32px;
	line-height:32px;
	overflow:hidden;
	position:relative;
	background:#fff url(images/base-searchbar-bg.png) bottom repeat-x;
	-moz-box-shadow: 0px 2px 3px #d5d5d5; /* FF3.5+ */
	-webkit-box-shadow: 0px 2px 3px #d5d5d5; /* Saf3.0+, Chrome */
	box-shadow: 0px 2px 3px #d5d5d5; /* Opera 10.5, IE9, Chrome 10+ */
}
#c6ui-header-subpage .subpage_list {
	position:absolute;
	left:100px;
}
#c6ui-header-subpage .search_bar {
	position:absolute;
	right:0;
	padding:3px 10px 0 10px;
	background:#fff url(images/base-searchbar-bg.png) bottom repeat-x;
}
#c6ui-tabs {
	position:relative;
	padding:0 28px;
	margin-bottom:-3px;
}
#c6ui-tabs .left_btn {
	position:absolute;
	left:0px;
	top:2px;
	width:24px;
	height:29px;
}
#c6ui-tabs .right_btn {
	position:absolute;
	right:0;
	top:2px;
	width:24px;
	height:29px;
}
#c6ui-tabs ul {
	display:block;
	float:none;
	width:4000px;
}
#c6ui-tabs ul li {
	float:left;
	height:29px;
	margin-right:15px;
	cursor:pointer;
}
#c6ui-tabs ul li span.tab_title, #c6ui-tabs ul li span.tab_close {
	line-height:29px;
	height:29px;
	display:block;
	float:left;
	padding-left:9px;
	color:#fff;
}
#c6ui-tabs ul li span.tab_close {
	display:none;
}
#c6ui-tabs ul li.tab_hover span.tab_title, #c6ui-tabs ul li.tab_hover span.tab_close {
	background:url(images/tabs_bg.png) 0px 0px;
	line-height:29px;
	height:29px;
	display:block;
	float:left;
	padding-left:9px;
	color:#333;
	font-weight:bold;
}
#c6ui-tabs ul li.tab_hover span.tab_close {
	background-position:right bottom;
	width:25px;
}
#c6ui-tabs ul li.tab_hover a, #c6ui-tabs ul li.tab_hover a:hover {
	margin-top:4px;
	width:17px;
	height:17px;
	float:left;
	display:block
}
#c6ui-tabs ul li.tab_hover a:hover {
	background:url(../../../image/tab_close_bg.png) no-repeat;
}
#c6ui-tabs .left_btn a, #c6ui-tabs .right_btn a {
	padding:7px;
}
#c6ui-tabs .left_btn a:hover, #c6ui-tabs .right_btn a:hover {
	padding:7px;
	margin-top:2px;
	background:url(../../../image/tab_arrow_bg.png);
}
#c6ui-header-min-widget {
	float:right !important;
}
/*by eddy add plus*/
span.c6ui-widget-btn span.widget-btn-left-h,span.c6ui-widget-btn span.widget-btn-plus-h,span.c6ui-widget-btn span.widget-btn-left-c,span.c6ui-widget-btn span.widget-btn-plus-c,span.c6ui-widget-btn-single,span.c6ui-widget-btn-single span{
background-image:url(../../../image/widget_plus.png);
background-repeat:no-repeat;
}
span.c6ui-widget-btn{
	height:26px;
	display:inline-block;
	padding: 0 2px 0 3px;
	white-space: nowrap;
	padding-left: 3px;
}
span.c6ui-widget-btn span.widget-btn-left{
	height:26px;
	display:inline-block;
	margin:0px -3px 0px 0px;
	padding-left:6px;
	color:#FFFFFF;
}
span.c6ui-widget-btn span.widget-btn-left-h{
background-position:0 0;
}
span.c6ui-widget-btn span.widget-btn-left-c{
background-position:0 -52px;
}
span.c6ui-widget-btn span.widget-btn-plus{
	width:16px;
	height:26px;
	display:inline-block;
}
span.c6ui-widget-btn span.widget-btn-plus-h{
background-position:right -26px;
}
span.c6ui-widget-btn span.widget-btn-plus-c{
background-position:right -78px;
}
span.c6ui-widget-btn span.widget-btn-left img{
margin-left:1px;
margin-bottom:-2px;
 }
span.c6ui-widget-btn span.widget-btn-plus img{
margin-left:1px;
margin-bottom:-2px;
 }
span.c6ui-widget-btn-single{
	height:26px;
	display:inline-block;
	white-space: nowrap;
	background-position:right 100px;
	cursor:default;
}
span.c6ui-widget-btn-single-h{
	background-position:right -130px;
}
span.c6ui-widget-btn-single-c{
	background-position:right -182px;
}
span.c6ui-widget-btn-single span{
	height:26px;
	margin-right:3px;
	display:inline-block;
	background-position:0 100px;
	padding-left:3px;
	padding-right:0;
	color:#FFFFFF;
}
span.c6ui-widget-btn-single span img{
	margin-left:1px;
	margin-bottom:-2px;
	margin-right:0;
}
span.c6ui-widget-btn-single span label{
	margin:0 3px -2px 2px;
	height:26px;
	display:inline-block;
	line-height:24px;
}
span.c6ui-widget-btn-single-h span{
	background-position:0 -104px;
}
span.c6ui-widget-btn-single-c span{
	background-position:0 -156px;
}
.c6ui-header-handle,.c6ui-header-handle-h,.c6ui-header-handle-c{
background-image:url(../../../image/bace_min_btn.png);
background-repeat:no-repeat;
}
.c6ui-header-handle{
height:22px;
width:22px;
display:block;
float:right;
background-position:0 100px;
}
.c6ui-header-handle img{
margin:3px;
}
.c6ui-header-handle-h{
background-position:0 0;
}
.c6ui-header-handle-c{
background-position:0 -23px;
}
/*by eddy add plus end*/
#c6ui-main {
	overflow:auto;
}
#c6ui-bottomSide {
	/*width:100%;*/
	border:0;
	position:absolute;
	bottom:0;
	margin:0 auto;
	/*modify by duwy*/
	width:1002px;
	left:50%;
	margin-left:-501px;
}
/*filter*/
.c6ui-filter .c6ui-widget-minSearch {
 float:none !imortant;
	position:absolute;
	top:6px;
	left:400px;
}
.group1 {
	position:absolute;
	top:6px;
	left:10px;
}
.group2 {
	position:absolute;
	top:6px;
	left:80px;
}
.c6ui-filter {
	border:1px solid #c3daf9;
	position:relative;
}
.c6ui-radius {
	-moz-border-radius: 1px;/*firefox*/
	-webkit-border-radius: 1px;/*webkit*/
	border-radius: 1px;/*css 3*/
}
.c6ui-filter-top {
	height:29px;
	padding:0px 10px;
	background:url(./images/filter/filter_tp_bg.png) 0 0 repeat-x scroll;
}
.c6ui-filter-bottom {
	border-top:1px solid #c3daf9;
	padding:4px 10px 0px 10px;
	background:url(./images/filter/filter_bt_bg.png) 0 0 repeat-x scroll;
	padding-bottom:2px;
}
.c6ui-filter-list {
	position:relative;
	border-top-width:1px;
	border-bottom:1px dashed #cccccc;
	margin-bottom:2px;
	padding-right:4px;
	_zoom:1;
}
.c6ui-filter-active {
	position:relative;
	background-color:#d9e8fb;
	border-top-width:1px;
	border-bottom:1px solid #d9e8fb;
	padding-right:4px;
	margin-bottom:2px;
	_zoom:1;
}
.c6ui-filter-close {
	width:9px;
	height:9px;
	float:right;
}
.c6ui-filter-list .c6ui-filter-close {
	background:url(./images/filter/filter_close.png) center center no-repeat scroll;
}
.c6ui-filter-active .c6ui-filter-close {
	background:url(./images/filter/filter_close_active.png) center center no-repeat scroll;
}
/*check*/
.c6ui-checkbox {
	background:url(./images/base_check/base_checkbox.png) 0 0 scroll no-repeat;
	width:16px;
	height:16px;
	display:inline-block;
	zoom:1;
 *display:inline;
 margin-bottom:-3px;
}
.c6ui-checkbox-normal {
	background-position:0 0;
}
.c6ui-checkbox-normal-checked {
	background-position:0 -16px;
}
.c6ui-checkbox-normal-half {
	background-position:0 -32px;
}
.c6ui-checkbox-disabled {
	background-position:0 -48px;
}
.c6ui-checkbox-disabled-checked {
	background-position:0 -64px;
}
.c6ui-checkbox-disabled-half {
	background-position:0 -80px;
}
.c6ui-checkbox-active {
	background-position:0 -96px;
}
.c6ui-checkbox-active-checked {
	background-position:0 -112px;
}
.c6ui-checkbox-active-half {
	background-position:0 -128px;
}
.c6ui-radio {
	background:url(./images/base_check/base_radio.png) 0 0 scroll no-repeat;
	width:16px;
	height:16px;
	display:inline-block;
	zoom:1;
 *display:inline;
	margin-bottom:-3px;
}
.c6ui-radio-normal {
	background-position:0 0;
}
.c6ui-radio-normal-checked {
	background-position:0 -16px;
}
.c6ui-radio-disabled {
	background-position:0 -32px;
}
.c6ui-radio-disabled-checked {
	background-position:0 -48px;
}
.c6ui-radio-active {
	background-position:0 -64px;
}
.c6ui-radio-active-checked {
	background-position:0 -80px;
}
/*menu*/
#c6ui-menu .c6ui-menu-top {
	background:url(images/menu_png/menu_top.png) repeat-x scroll 0 0;
	height:7px;
	width:100%;
}
#c6ui-menu .c6ui-menu-bottom {
	background:url(images/menu_png/menu_bottom.png) repeat-x scroll 0 0;
	height:31px;
}
#c6ui-menu .c6ui-menu-top-left {
	background:url(images/menu_png/menu_top_left.png) no-repeat scroll 0 0;
	width:7px;
}
#c6ui-menu .c6ui-menu-top-right {
	background:url(images/menu_png/menu_top_right.png) no-repeat scroll 0 0;
	width:7px;
}
#c6ui-menu .c6ui-menu-bottom-left {
	background:url(images/menu_png/menu_bottom_left.png) no-repeat scroll 0 0;
}
#c6ui-menu .c6ui-menu-bottom-right {
	background:url(images/menu_png/menu_bottom_right.png) no-repeat scroll 0 0;
}
#c6ui-menu .c6ui-menu-left {
	background:url(images/menu_png/menu_left.png) repeat-y scroll 0 0;
}
#c6ui-menu .c6ui-menu-right {
	background:url(images/menu_png/menu_right.png) repeat-y scroll 0 0;
}
#c6ui-menu .c6ui-menu-content {
	width:398px;
	height:451px;
	overflow:hidden
}
#c6ui-menu .c6ui-menu-content ul {
	position:relative
}
#c6ui-menu .c6ui-menu-content-left {
	float:left;
	width:163px;
	position:relative;
	border:1px solid #9aaec1;
	height:450px;
	overflow:hidden
}
#c6ui-menu .c6ui-menu-content-right-list {
	float:right;
	width:231px;
	position:relative;
	border:1px solid #9aaec1;
	border-left-color:#FFFFFF;
	height:450px;
	background:url(images/menu_png/menu_list_bg.png) repeat-y 0 0 scroll;
	overflow:hidden
}
#c6ui-menu .c6ui-menu-content-right-history {
	float:right;
	width:231px;
	position:relative;
	border:1px solid #9aaec1;
	background-color:#f4f4f4;
	height:450px;
}
#c6ui-menu .c6ui-menu-content li {
	position:relative;
	line-height:0;
	overflow:hidden
}
#c6ui-menu .c6ui-menu-content li a {
	padding-left:28px;
	display:block;
	line-height:28px;
	height:28px;
	cursor:default;
}
#c6ui-menu .c6ui-menu-content-right-list li a {
	padding-left:34px;
}
#c6ui-menu .c6ui-menu-content-right-history li a {
	padding-left:6px;
	background:none;
	margin-right:0px;
}
#c6ui-menu .c6ui-menu-content-right-list li .ui-list-driver {
	background-color:#daeafb;
	padding-left:6px;
	margin-right:0px;
	height:28px;
	margin-left:0px;
	font-weight:bold;
	display:block;
	line-height:28px;
}
#c6ui-menu .c6ui-menu-content-right-history li .ui-list-driver {
	padding-left:6px;
	margin-right:0px;
	height:28px;
	margin-left:0px;
	background:none;
	font-weight:bold;
	display:block;
	line-height:28px;
}
#c6ui-menu .c6ui-menu-content-left li a:hover, #c6ui-menu .c6ui-menu-content .c6ui-menu-content-right-list li a:hover,#c6ui-menu .c6ui-menu-content-left li a.currentList,#c6ui-menu .c6ui-menu-content .c6ui-menu-content-right-list li a.currentList {
	background:url(images/menu_png/menu_list_active_bg.png) repeat-x 0 0 scroll;
	position:relative;
}
#c6ui-menu .c6ui-menu-content-right-history li a:hover {
	background:url(images/menu_png/menu_list_active_bg.png) repeat-x 0 0 scroll;
	position:relative;
}
#c6ui-menu .c6ui-menu-content img {
	position:absolute;
	top:6px;
	left:6px;
	z-index:1000; /*margin-bottom:-3px;*/
}
#c6ui-menu .c6ui-menu-content-right-list ul li.ui-list-line {
	border-bottom:1px #cccccc solid;
	height:0px;
	line-height:0;
	font-size:0px;
}
#c6ui-menu .c6ui-menu-content-right-history ul li.ui-list-line {
	border-top:1px #d8d8d8 solid;
	border-bottom:1px #ffffff solid;
	height:0px;
	line-height:0;
	font-size:0px;
}
.ui-menu-icon-arrow {
	display:block;
	position:absolute;
	top:0px;
	right:6px;
	width:16px;
	height:16px;
	padding:6px 6px;
	background:url(images/menu_png/menu_icon_arrow.png) center center scroll no-repeat;
}
.ui-menu-pin-on:hover {
}
.ui-menu-pin-off:hover {
}
.ui-menu-pin-on {
	display:block;
	position:absolute;
	top:0px;
	right:10px;
	width:16px;
	height:16px;
	padding:6px 6px;
	background:url(images/menu_png/menu_pin_on.png) center center scroll no-repeat;
}
.ui-menu-pin-off {
	display:block;
	position:absolute;
	top:0px;
	right:10px;
	width:16px;
	height:16px;
	padding:6px 6px;
	background:url(images/menu_png/menu_pin_off.png) center center scroll no-repeat;
}
#c6ui-menu .c6ui-menu-control {
	float:right;
}
#c6ui-menu .c6ui-menu-control button span {
	display:block;
}
#c6ui-menu .c6ui-menu-control .c6ui-menu-btn-text {
	padding-left:20px;
	line-height:22px;
}
#c6ui-menu .c6ui-menu-control button {
	height:22px;
	width:61px;
	background:url(images/menu_png/menu_button.png) 0 0 scroll no-repeat;
	border:none;
	position:relative;
}
#c6ui-menu .c6ui-menu-btn {
	top:2px;
	left:8px;
	width:16px;
	height:16px;
	position:absolute;
}
#c6ui-menu .c6ui-menu-btn-setting {
	background:url(images/menu_png/menu_icon_setting.png) no-repeat 0 0 scroll;
}
#c6ui-menu .c6ui-menu-btn-loginout {
	background:url(images/menu_png/menu_icon_loginout.png) no-repeat 0 0 scroll;
}
#c6ui-menu .c6ui-menu-roll-top {
	width:100%;
	height:13px;
	position:absolute;
	background:url(images/menu_png/menu_roll.png) no-repeat 0 0 scroll;
	z-index:1001;
	top:0px;
	border:none;
	font-size:0px;
}
#c6ui-menu .c6ui-menu-roll-bottom {
	width:100%;
	height:13px;
	position:absolute;
	background:url(images/menu_png/menu_roll.png) no-repeat 0 0 scroll;
	z-index:1001;
	bottom:0px;
	border:none;
	font-size:0px;
}
#c6ui-menu .c6ui-roll-top {
	position:relative;
	background:url(images/menu_png/menu_icon_arrow_t.png) no-repeat center center scroll;
	width:100%;
	display:block;
	height:12px
}
#c6ui-menu .c6ui-roll-bottom {
	position:relative;
	background:url(images/menu_png/menu_icon_arrow_b.png) no-repeat center center scroll;
	width:100%;
	display:block;
	height:12px
}
/*
 *base
 */
 .c6ui-base-poplist {
	width:231px;
	border:1px solid #9aaec1;
	border-left-color:#FFFFFF;
	height:100%;
	background:url(images/menu_png/menu_list_bg.png) repeat-y 0 0 scroll;
	/*add by duwy 修改ie6，ie7不跟随滚动条滚动的问题*/
	position:relative;
}
.c6ui-base-poplist ul li {
	position:relative;
	line-height:0;
}
.c6ui-base-poplist ul li a {
	padding-left:34px;
    display:block;
	line-height:28px;
	height:28px;
	cursor:default;
}
.c6ui-base-poplist ul li:hover {
	background:url(images/menu_png/menu_list_active_bg.png) repeat-x 0 0 scroll;
	position:relative;
}
.c6ui-base-poplist img {
    position:absolute;
    top:6px;
    left:6px;
    z-index:1000; /*margin-bottom:-3px;*/
}
.c6ui-iframe-main {
	width:100%;
	border-style: none;
	border:0px;
}
/*快捷菜单*/
#c6ui-bottomSide #c6ui-quick-menu, #c6ui-bottomSide #c6ui-quick-menu-short, #c6ui-bottomSide #c6ui-quick-menu.change_style1, #c6ui-bottomSide #c6ui-quick-menu-short.change_style1 {
	height:89px;
	width:782px;
	padding:0 110px;
	/*margin:0 auto;*/
	margin:0 auto 11px;
	background:url(../../images/quick_menu_bg.png) center bottom no-repeat;
	cursor:pointer;
}
/*add by duwy 鼠标移上hover效果*/
#c6ui-bottomSide #c6ui-quick-menu.change_style1, #c6ui-bottomSide #c6ui-quick-menu-short.change_style1 {
	background-image:url(../../images/hover_quick_menu_bg.png);
}
#c6ui-bottomSide #close-footer {
	height:12px;
	text-align:center;
	bottom:0;
	position:absolute;
	width:100%;
}
/*add by duwy*/
#c6ui-bottomSide #close-footer a#turn_off, #c6ui-bottomSide #close-footer a#turn_on, #c6ui-bottomSide #close-footer a#turn_off:hover, #c6ui-bottomSide #close-footer a#turn_on:hover {
	width:200px;
	height:12px;
	display:block;
	margin:0 auto;
	background:url(../../images/footer_close_bg.png) center center no-repeat;
}
#c6ui-bottomSide #close-footer a#turn_off {
	background:url(../../images/footer_close_bg2.png) center bottom no-repeat;
}
#c6ui-bottomSide #close-footer a#turn_off:hover {
	background:url(../../images/footerHover_close_bg2.png) center bottom no-repeat;
}
#c6ui-bottomSide #close-footer a#turn_on:hover {
	background-image:url(../../images/footerHover_close_bg.png);
}
#c6ui-bottomSide #close-footer a img {
	margin-top:2px;
}
#c6ui-quick-menu, #c6ui-quick-menu-short {
	position:relative;
	padding:0 33px;
	padding-top:5px;
}
#c6ui-quick-menu .left_btn, #c6ui-quick-menu-short .left_btn {
	position:absolute;
	left:105px;
	top:20px;
	width:33px;
	height:29px;
}
#c6ui-quick-menu .right_btn, #c6ui-quick-menu-short .right_btn {
	position:absolute;
	right:105px;
	top:20px;
	width:33px;
	height:29px;
}
#c6ui-quick-menu .quick_menu_list, #c6ui-quick-menu-short .quick_menu_list {
	margin:0 50px;
	width:681px;
	height:88px;
	overflow:hidden;
}
#c6ui-quick-menu .quick_menu_list ul, #c6ui-quick-menu-short .quick_menu_list ul {
	display:block;
	float:none;
	width:9000px;
	margin:0 auto;
	height:64px;
}
#c6ui-quick-menu .quick_menu_list ul li, #c6ui-quick-menu-short .quick_menu_list ul li {
	float:left;
	height:88px;
	width:116px;
	cursor:pointer;
	text-align:center;
	font-size:14px;
	line-height:20px;
	color:#000;
}
#c6ui-quick-menu .quick_menu_list ul li img, #c6ui-quick-menu-short .quick_menu_list ul li img {
	display:block;
	margin:3px auto 0;
}
/*add by duwy*/
.quick_menu_list ul li a, .quick_menu_list ul li a:hover {
	width:116px;
	height:88px;
	display:block;
	float:left;
	color:#000000
}
.quick_menu_list ul li a:hover {
	background:url(../../images/ico_shadow.png) bottom center no-repeat;
}
/*针对小于1024*768的时候做出的样式处理*/
#c6ui-bottomSide #c6ui-quick-menu-short, #c6ui-bottomSide #c6ui-quick-menu-short.change_style1 {
	width:518px;
	padding:0 20px;
	-moz-background-size:100% 56px;
	background-size:100% 56px;
}
#c6ui-quick-menu-short .left_btn {
	left:10px;
}
#c6ui-quick-menu-short .right_btn {
	right:10px;
}
#c6ui-quick-menu-short .quick_menu_list {
	margin:0 20px;
	width:480px;
}
#c6ui-quick-menu-short .quick_menu_list ul li {
	width:80px;
}
/*
 *form布局
 */
.widget-formtable {
	border-collapse:separate;
	border-spacing:6px;
}
/*
 *按钮
 */
/*全局*/
.e_btn, .e_con {
	background-image:url(images/base_btn/button_bg.png);
	background-repeat:no-repeat;
	border:0;
	margin:0;
	padding:0;
}
/*默认*/
.e_btn {
	background-position:0 0;
}
.e_con {
	background-position:right -22px;
}
.e_btn {
	height:22px;
	padding-left:3px;
	display:inline-block;
	font-weight:normal;
	cursor:default;
	margin:2px;
	margin-bottom:0;
	overflow:hidden;
}
.e_btn .e_con {
	height:22px;
	padding:0 4px 0 1px;
	display:block;
	font-weight:normal;
	white-space:nowrap;
	text-align:center;
	vertical-align:middle;
	float:left;
}
.e_btn .e_con .btn {
	margin:0;
	padding:0;
	border:0;
	background:none;
	font-size:12px;
	height:20px;
	overflow:hidden;
	line-height:20px;
	vertical-align:middle;
	margin-top:3px;
}
.e_btn .e_con button.btn, .e_btn .e_con input.btn {
	margin-top:2px;
	margin-bottom:-3px;
}
.e_btn .e_con div.ico {
	background-position:center center;
	background-repeat:no-repeat;
	float:left;
	height:16px;
	width:16px;
	vertical-align:middle;
	margin:3px;
	margin-bottom:0;
	overflow:hidden;
	font-size:1px;
}
.e_btn .e_con div.s {
	background-position:center center;
	background-repeat:no-repeat;
	float:left;
	height:16px;
	width:1px;
	vertical-align:middle;
	margin-top:3px;
	overflow:hidden;
	font-size:1px;
}
.e_btn .e_con span.triangle {
	display:inline-block;
	background-image:url(images/base_btn/btn_icon_menu.png);
	background-position:center center;
	background-repeat:no-repeat;
	height:16px;
	width:5px;
	vertical-align:middle;
	margin:3px;
	_margin-right:6px;
	margin-bottom:0;
	overflow:hidden;
	font-size:1px;
}
/*禁用*/
.d {
	background-position:0 -132px;
}
.d .e_con {
	background-position:right -154px;
}
.d .e_con .btn {
	color:#989898;
}
.d .e_con div.ico {
	vertical-align:middle;
	margin-top:3px;
 filter : progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=25, finishOpacity=0);
	opacity: 0.25;
}
/*click*/
.a {
	background-position:0 -176px;
}
.a .e_con {
	background-position:right -198px;
}
/*hover*/
.h {
	background-position:0 -44px;
}
.h .e_con {
	background-position:right -66px;
}
/*click*/
.c {
	background-position:0 -88px;
}
.c .e_con {
	background-position:right -110px;
}
/*light*/
.l {
	background:none;
}
.l .e_con {
	background:none;
}
/*light hover*/
.lh {
	background-position:0 -220px;
}
.lh .e_con {
	background-position:right -242px;
}
/*corner left*/
.cl {
	background-position:0 -264px;
}
/*corner right*/
.cr {
	background-position:right -286px;
}
.h .cr {
	background-position:right -330px;
}
.c .cr {
	background-position:right -374px;
}
.btnl {
	margin-left:0;
}
.btnr {
	margin-right:0;
}
/*min_tab*/
.c6ui-base-minTab {
	overflow:hidden;
	height:29px;
}
.c6ui-base-minTab ul {
	_overflow:hidden;
	width:100%;
	padding:0 6px;
	height:27px;
	line-height:27px;
	position:relative;
	border:solid #cfcfcf 1px;
	border-left:none;
	border-right:none;
	white-space:nowrap;
	background-color: #fbfbfb;
	background-image: -moz-linear-gradient(top, #fbfbfb, #eff0f1); /* FF3.6 */
	background-image: -ms-linear-gradient(top, #fbfbfb, #eff0f1); /* IE10 */
	background-image: -o-linear-gradient(top, #fbfbfb, #eff0f1); /* Opera 11.10+ */
	background-image: -webkit-gradient(linear, left top, left bottom, from(#fbfbfb), to(#eff0f1)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient(top, #fbfbfb, #eff0f1); /* Chrome 10+, Saf5.1+ */
	background-image: linear-gradient(top, #fbfbfb, #eff0f1);
 filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#fbfbfb', EndColorStr='#eff0f1'); /* IE6–IE9 */
}
.c6ui-base-minTab ul li {
	float:left;
	padding:0 16px;
	position:relative;
	height:28px;
	line-height:27px;
	cursor:pointer;
}
.c6ui-base-minTab ul li.active {
	border-right:solid #cfcfcf 1px;
	border-left:solid #cfcfcf 1px;
	background-color:#fff;
	padding:0 12px;
	height:28px;
	font-weight:bold;
	_margin-bottom:-1px;
}
/*min_tab_portrait*/
.c6ui-base-minTab-portrait {
	width:149px;
}
.c6ui-base-minTab-portrait ul {
	width:100%;
	padding:1px;
	line-height:26px;
	position:relative;
	border:solid #cfcfcf 1px;
	overflow:hidden;
}
.c6ui-base-minTab-portrait ul li {
	padding:0 12px;
	position:relative;
	height:26px;
	line-height:26px;
	cursor:pointer;
	white-space:nowrap;
	text-overflow:ellipsis;
	overflow:hidden;
}
.c6ui-base-minTab-portrait ul li.active {
	color:#FFFFFF;
	height:26px;
	background-color: #60b2e2;
	background-image: -moz-linear-gradient(top, #60b2e2, #3283bd); /* FF3.6 */
	background-image: -ms-linear-gradient(top, #60b2e2, #3283bd); /* IE10 */
	background-image: -o-linear-gradient(top, #60b2e2, #3283bd); /* Opera 11.10+ */
	background-image: -webkit-gradient(linear, left top, left bottom, from(#60b2e2), to(#3283bd)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient(top, #60b2e2, #3283bd); /* Chrome 10+, Saf5.1+ */
	background-image: linear-gradient(top, #60b2e2, #3283bd);
 filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#60b2e2', EndColorStr='#3283bd'); /* IE6–IE9 */
}
/*Tab-left&Tab-right*/


.c6ui-base-Tab-portrait-left ul, .c6ui-base-Tab-portrait-left ul li, .c6ui-base-Tab-portrait-left ul li.active, .c6ui-base-Tab-portrait-left ul li span {
	background-image:url(images/base_tab/base_tab_portrait_left.png);
}
.c6ui-base-Tab-portrait-right ul, .c6ui-base-Tab-portrait-right ul li, .c6ui-base-Tab-portrait-right ul li.active, .c6ui-base-Tab-portrait-right ul li span {
	background-image:url(images/base_tab/base_tab_portrait_right.png);
}
.c6ui-base-Tab-portrait-left, .c6ui-base-Tab-portrait-right {
	width:29px;
	font-size:14px;
	line-height:1;
}
.c6ui-base-Tab-portrait-left ul {
	padding:6px 0;
	width:29px;
	background-position: right;
}
.c6ui-base-Tab-portrait-right ul {
	padding:6px 0;
	width:29px;
	background-position:-120px 0;
}
.c6ui-base-Tab-portrait-left ul li, .c6ui-base-Tab-portrait-right ul li {
	cursor:pointer;
	padding-top:11px;
	margin:2px 0;
 *margin:0;
	width:29px;
	background-position:-58px 0;
}
.c6ui-base-Tab-portrait-left ul li.active, .c6ui-base-Tab-portrait-right ul li.active {
	background-position:0 0;
}
.c6ui-base-Tab-portrait-left ul li span, .c6ui-base-Tab-portrait-right ul li span {
	display:block;
	padding:0 0 11px 2px;
	text-align:center;
	letter-spacing: 2px;
	background-position: -87px bottom;
}
.c6ui-base-Tab-portrait-left ul li.active span, .c6ui-base-Tab-portrait-right ul li.active span {
	background-position:-29px bottom;
	font-weight:bold;
}
/*Tab-top&Tab-bottom*/


.c6ui-base-Tab-top ul, .c6ui-base-Tab-top ul li, .c6ui-base-Tab-top ul li.active, .c6ui-base-Tab-top ul li span {
	background-image:url(images/base_tab/base_tab_top.png);
}
.c6ui-base-Tab-bottom ul, .c6ui-base-Tab-bottom ul li, .c6ui-base-Tab-bottom ul li.active, .c6ui-base-Tab-bottom ul li span {
	background-image:url(images/base_tab/base_tab_bottom.png);
}
.c6ui-base-Tab-top, .c6ui-base-Tab-bottom {
	height:30px;
	font-size:14px;
	line-height:1;
}
.c6ui-base-Tab-top ul {
	padding:0 6px;
	height:30px;
	background-position:bottom;
}
.c6ui-base-Tab-bottom ul {
	padding:0 6px;
	height:30px;
	background-position:0 -124px;
}
.c6ui-base-Tab-top ul li, .c6ui-base-Tab-bottom ul li {
	float:left;
	cursor:pointer;
	padding-left:11px;
	margin:0 2px 0 1px;
	height:30px;
	background-position:0 -60px;
}
.c6ui-base-Tab-top ul li.active, .c6ui-base-Tab-bottom ul li.active {
	background-position:0 0;
}
.c6ui-base-Tab-top ul li span, .c6ui-base-Tab-bottom ul li span {
	line-height:30px;
	height:30px;
	display:inline-block;
	padding:0 11px 0 5px;
	white-space:nowrap;
	background-position: right -90px;
}
.c6ui-base-Tab-top ul li.active span, .c6ui-base-Tab-bottom ul li.active span {
	padding:0 7px 0 3px;
	background-position:right -30px;
	font-weight:bold;
}
.c6ui-base-Tab-portrait-left ul li span, .c6ui-base-Tab-portrait-right ul li span,.c6ui-base-Tab-top ul li span, .c6ui-base-Tab-bottom ul li span{
	font-size:14px;
}
/*widget ProgressBar*/
.c6ui-widget-progressBar, .c6ui-widget-progressBar span, .c6ui-widget-progressBar span b {
	height:15px;
	background-image: url(images/widget/widget_progressBar.png);
	background-repeat: repeat-x;
}
.c6ui-widget-progressBar {
	background-position:0 0;
	width:100%;
	font-size:0;
	display:inline-block;
}
.c6ui-widget-progressBar span {
	background-position:0 -15px;
	display:block;
	margin:0 1px;
}
.c6ui-widget-progressBar span b {
	width:0;
	background-position:0 -30px;
	display:block;
}
/*poplist
 *using in :poplist
 */
#menu-pop-second {
	display:none;
	position:absolute;
}
.c6ui-menu-pop, .c6ui-menu-pop-second {
	width:160px;
	border:1px solid #cccccc;
	-moz-border-radius:2px;
	-webkit-border-radius:2px;
	border-radius:2px;
	background-color:#FFFFFF;
}
.c6ui-menu-pop-bottom {
	background-color:#edf2f6;
	height:45px;
	margin-top:14px;
	padding-top:20px;
	padding-left:12px;
	position:relative;
}
.c6ui-menu-pop-bottom span {
	padding:0px 12px;
}
.c6ui-menu-pop-bottom .c6ui-btn {
	position:absolute;
	top:18px;
	right:12px;
}
.c6ui-menu-pop-list, .c6ui-menu-pop-second-list {
	padding:3px 0px;
}
.c6ui-menu-pop-list li, .c6ui-menu-pop-second-list li {
	padding:0px 12px;
	height:24px;
	line-height:24px;
	position:relative;
}
.c6ui-menu-pop-list .c6ui-menu-pop-active, .c6ui-menu-pop-second-list .c6ui-menu-pop-active {
	background-color:#d9e8fb
}
.c6ui-menu-pop-list input, .c6ui-menu-pop-second-list input {
	float:left;
	margin-top:6px;
	_margin-top:0px;
	_padding-top:6px;
}
.c6ui-menu-pop-list div {
	margin-left:24px;
	background:center right url(../JHsoft.UI.Lib/skin/default/images/filter_menu_pop/menu_icon_arrow.png) scroll no-repeat;
}
.c6ui-menu-pop-second-list div {
	margin-left:24px;
}
  /*textinput
 *using in :text and textarea
 */
/*默认状态*/
.c6ui-textinput-normal{
	border:1px solid #c5c5c5;
}

/*禁用状态*/
.c6ui-textinput-disabled {
	border:1px solid #dddddd;
	background-color:#f5f5f5;
}
/*激活状态*/
.c6ui-textinput-active {
	border:1px solid #39f;
}

/*widget slider*/
.c6ui-widget-slider {
	height:16px;
	position:relative;
	width:400px;
}
.c6ui-widget-slider div, .c6ui-widget-slider div span, .c6ui-widget-slider .handle {
	background-image:url(images/widget/widget_slider.png);
}
.c6ui-widget-slider div {
	margin:0 8px;
	font-size:1px;
	height:7px;
	margin-top:5px;
	border-left:solid #999 1px;
	border-right:solid #999 1px;
	background-position:0 0;
}
.c6ui-widget-slider div span {
	width:1px;
	font-size:1px;
	display:block;
	height:7px;
	background-position:0 -18px;
}
.c6ui-widget-slider table {
	margin-left:8px;
	width:100%;
	height:3px;
}
.c6ui-widget-slider table tr td {
	border-left:solid #b6b6b6 1px;
	border-right:solid #b6b6b6 1px;
}
.c6ui-widget-slider b.handle {
	margin-top:-12px;
	position:absolute;
	width:16px;
	height:16px;
	background-position:-1px -43px;
}
.c6ui-widget-slider b.handleHover {
	margin-top:-12px;
	position:absolute;
	width:16px;
	height:16px;
	background-position:-21px -43px;
	cursor:pointer;
}
/*select*/
.c6ui_select_active {
	position:relative;
	display:inline-block;
	z-index:1008;
}
.c6ui_select .border1, .c6ui_select_unable .border1, .c6ui_select .border1 .border2, .c6ui_select_unable .border1 .border2 {
	border:0;
}
.c6ui_select .border1, .c6ui_select_unable .border1 {
	margin:2px 2px;
}
.c6ui_select .c6ui_select_main {
	border:1px solid #c5c5c5;
	background-color:#fff;
	height:21px;
	display:inline-block;
}
.c6ui_select {
	display:inline-block;
}
.c6ui_select_active .border1 {
	border:1px solid #cadcf4;
}
.c6ui_select_active .border1 .border2 {
	border:1px solid #7ca9e6;
}
.c6ui_select_active .border12 {
	position:absolute;
	border:1px solid #cadcf4;
	top:24px;
	border-top:none;
}
.select_hover .border1 {
	border:1px solid #cadcf4;
	margin:0;
}
.select_hover .border1 .border2 {
	border:1px solid #7ca9e6;
	margin:0;
}
.select_hover .c6ui_select_main {
	border:1px solid #508cdd;
	line-height:21px;
	height:21px;
	cursor:pointer;
	background-color:#fff
}
.select_hover .border12, .c6ui_select .border12, .c6ui_select_unable .border12 {
	display:none;
}
.c6ui_select_active .border12 .border22 {
	border:1px solid #7ca9e6;
	position:absolute;
	border-top:0;
}
.c6ui_select_unable .c6ui_select_main {
	border:1px solid #c5c5c5;
	background-color:#f5f5f5;
	height:21px;
}
.c6ui_select_active .c6ui_select_main {
	border:1px solid #508cdd;
	line-height:21px;
	height:21px;
	cursor:pointer;
	background-color:#fff
}
.c6ui_select_active .border1 .border2 .c6ui_select_main .c6ui_selected, .c6ui_select .border1 .border2 .c6ui_select_main .c6ui_selected, .c6ui_select_unable .border1 .border2 .c6ui_select_main .c6ui_selected {
	float:left;
	width:100%;
	padding-left:3px;
	line-height:21px;
	height:21px;
	margin-right:-22px;
}
.c6ui_select .c6ui_select_main a, .c6ui_select_active .c6ui_select_main a, .c6ui_select_unable .c6ui_select_main a {
	color:#000;
}
.c6ui_select_active .border1 .border2 .c6ui_select_main span, .c6ui_select .border1 .border2 .c6ui_select_main span, .c6ui_select_unable .border1 .border2 .c6ui_select_main span {
	float:right;
	display:block;
	width:19px;
	height:21px;
	background:#fff url(images/widget/c6ui-select-arrow.gif) center 0 no-repeat
}
.c6ui_select_active .border1 .border2 .c6ui_select_main span, .select_hover .border1 .border2 .c6ui_select_main span {
	background-position:center -30px
}
.c6ui_select_unable .border1 .border2 .c6ui_select_main span {
	background-color:#f5f5f5;
}
.c6ui_select .border1 .border2 .c6ui_select_main ul {
	display:block;
	clear:both;
	height:0px;
	line-height:0px;
	font-size:0px;
	overflow:hidden;
}
.c6ui_select_unable .border1 .border2 .c6ui_select_main ul {
	display:block;
	clear:both;
	height:0px;
	line-height:0px;
	font-size:0px;
	overflow:hidden;
}
.c6ui_select_active .border12 .border22 ul {
	position:absolute;
	width:100%;
	top:0;
	background-color:#fff;
	border:1px solid #508cdd;
	border-top:0;
}
.c6ui_select_active .border12 .border22 ul li a {
	padding-left:3px;
	display:block;
	height:22px;
	line-height:22px;
	color:#000;
	font-size:12px;
	line-height:22px;
	height:22px;
}
.c6ui_select_active .border12 .border22 ul li a:hover, .c6ui_select_active .border12 .border22 ul li.li_hover {
	padding-left:3px;
	display:block;
	height:22px;
	line-height:22px;
	font-size:12px;
	color:#fff;
	background-color: #61b3e3;
	background-image: -moz-linear-gradient(top, #61b3e3, #398ac2);
	background-image: -ms-linear-gradient(top, #61b3e3, #398ac2);
	background-image: -o-linear-gradient(top, #61b3e3, #398ac2);
	background-image: -webkit-gradient(linear, left top, left bottom, from(#61b3e3), to(#398ac2));
	background-image: -webkit-linear-gradient(top, #61b3e3, #398ac2);
	background-image: linear-gradient(top, #61b3e3, #398ac2);
filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#61b3e3', EndColorStr='#398ac2');
}
/*dialog*/
body {
	_margin:0;
	_height:100%;
}
.aui_dialog_wrap {
	visibility:hidden
}
.aui_dialog {
	text-align:left;
	position:absolute;
	top:0;
	left:0
}
.aui_table {
	border:0;
	margin:0;
	border-collapse:collapse
}
.aui_table td {
	padding:0
}
.aui_title_icon, .aui_content, .aui_dialog_icon {
	display:inline-block;
*zoom:1;
*display:inline
}
.aui_title_icon, .aui_dialog_icon {
	vertical-align:middle;
	_font-size:0
}
.aui_title {
	overflow:hidden;
	cursor:move
}
.art_no_drag .aui_title {
	cursor:default
}
.aui_close {
	display:block;
	position:absolute;
	outline:none
}
.aui_td_content {
	text-align:center
}
.aui_content {
	margin:10px;
	text-align:left;
	border:none 0
}
.art_full {
	display:block;
	width:100%;
	margin:0;
	height:100%
}
.aui_noContent {
	width:5em;
	height:1.2em
}
.aui_content_wrap {
	margin:0;
*padding:0;
	display:block;
	height:100%;
	position:relative
}
.aui_td_icon {
	vertical-align: top
}
.aui_buttons_wrap {
	text-align:right;
	white-space:nowrap;
	height:30px;
}
.art_loading .aui_content, .art_loading {
	visibility:hidden;
*visibility:visible
}
.art_loading .aui_td_icon {
	display:none
}
.aui_loading_tip {
	visibility:hidden;
	width:5em;
	height:1.2em;
	text-align:center;
	line-height:1.2em;
	position:absolute;
	top:50%;
	left:50%;
	margin:-0.6em 0 0 -2.5em
}
.art_loading .aui_loading_tip {
	visibility:visible
}
#aui_iframe_mask {
	visibility:hidden;
	display:none\9;
	width:100%;
	height:100%;
	background:url(images/dialog/transparent.gif)\9;
	opacity:0
}
.art_page_lock #aui_iframe_mask, .art_page_move #aui_iframe_mask {
	visibility:visible;
	display:block\9
}
#aui_overlay {
	visibility:hidden;
	display:none\9;
	width:100%;
	height:100%
}
.art_page_lock #aui_overlay {
	visibility:visible;
	display:block\9
}
#aui_overlay div {
	height:100%;
	background:#000;
	opacity:0.7;
	background:url(images/dialog/shadow.png)\9;
	_filter:alpha(opacity=70);
	_background:#000
}
#aui_overlay iframe {
	width:100%;
	height:100%;
	position:absolute;
	top:0;
	left:0;
	z-index:-1;
	filter:alpha(opacity=0)
}
#aui_temp_wrap {
	_display:none
}
#aui_temp {
	display:none;
	position:absolute;
	background-color:#214FA3;
	border:1px solid #000;
	cursor:move;
	_filter:alpha(opacity=40);
	opacity:0.4
}
html>body #aui_temp {
	background:url(images/dialog/temp.png)\9
}
.art_page_move #aui_temp_wrap {
	_display:block
}
#aui_temp iframe {
	width:100%;
	height:100%;
	position:absolute;
	top:0;
	left:0;
	z-index:-1;
	filter:alpha(opacity=0)
}
.aui_content_mask {
	visibility:hidden;
	display:none\9;
	width:100%;
	height:100%;
	position:absolute;
	top:0;
	left:0;
	background:url(images/dialog/transparent.gif)\9;
	opacity:0
}
.art_loading .aui_content_mask, .art_page_move .aui_content_mask {
	visibility:visible;
	display:block\9
}
* html .aui_ie6_select_mask {
	width:100%;
	height:100%;
	position:absolute;
	top:0;
	left:0;
	z-index:-1;
	filter:alpha(opacity=0)
}
html>body #aui_iframe_mask, html>body #aui_overlay, html>body .art_fixed #aui_temp, html>body .art_fixed .aui_dialog {
	position:fixed;
	top:0;
	left:0
}
* .art_page_ie6_fixed {
	background:url(about:blank) fixed
}
* html #aui_iframe_mask, * html #aui_overlay, * html .art_fixed {
	width:100%;
	height:100%;
	position:absolute;
	overflow:hidden;
left:expression(documentElement.scrollLeft+documentElement.clientWidth-this.offsetWidth);
top:expression(documentElement.scrollTop+documentElement.clientHeight-this.offsetHeight)
}
.art_page_full {
*overflow:hidden;
_overflow:
}
.art_page_full>body {
	overflow:hidden;
	padding-right:17px
}
.art_opacity {
	opacity:0
}
.aui_aero .aui_td_title, .aui_aero .aui_td_buttons button {
	font:12px 'Microsoft Yahei', Arial;
}
.aui_aero .aui_content_table {
	min-width:9em;
	background:#FFF;
}
.aui_aero .aui_title_wrap {
	width:100%;
	height:30px;
	position:absolute;
	top:3px;
	left:0;
	_left:auto;
}
.aui_button_wrap {
	background-color:#edf2f6;
}
.aui_aero .aui_title {
	height:29px;
	line-height:29px;
	padding:0 30px 0 15px;
	_padding:0 0 0 8px;
	color:#000;
	font-weight:bold;
}
.aui_aero .aui_loading_tip {
	width:16px;
	height:16px;
	margin:-8px 0 0 -8px;
	text-indent:-9999em;
	background:url(aero/loading.gif) no-repeat center center;
}
.aui_aero .aui_left_top, .aui_aero .aui_right_top, .aui_aero .aui_left_bottom, .aui_aero .aui_right_bottom, .aui_aero .aui_top, .aui_aero .aui_bottom, .aui_aero .aui_title_icon, .aui_aero .aui_close {
	background-image:url(images/dialog/aero_s.png);
	background-repeat:no-repeat;
}
.aui_aero .aui_left_top {
	width:14px;
	height:34px;
	background-position: 0 0;
_ie6png:images/dialog/ie6/aui_left_top.png;
}
.aui_aero .aui_right_top {
	width:14px;
	height:34px;
	background-position: -14px 0;
_ie6png:images/dialog/ie6/aui_right_top.png;
}
.aui_aero .aui_left_bottom {
	width:14px;
	height:14px;
	background-position: 0 -34px;
_ie6png:images/dialog/ie6/aui_left_bottom.png;
}
.aui_aero .aui_right_bottom {
	width:14px;
	height:14px;
	background-position: -14px -34px;
_ie6png:images/dialog/ie6/aui_right_bottom.png;
}
.aui_aero .aui_title_icon {
	width:11px;
	height:11px;
	position:absolute;
	left:10px;
	_left:0px;
	top:9px;
	background-position:0 -109px;
_ie6png:aero/ie6/aui_title_icon.png;
}
.aui_aero .aui_close {
	top:7px;
	right:12px;
	_right:0;
	_z-index:1;
	width:13px;
	height:13px;
	background-position:left -96px;
	_font-size:0;
	_line-height:0;
	text-indent:-9999em;
_ie6png:images/dialog/ie6/aui_close.png;
}
.aui_aero .aui_close:hover {
	background-position:right -96px;
_ie6png:images/dialog/ie6/aui_close.hover.png;
}
.aui_aero .aui_top, .aui_aero .aui_bottom {
	background-repeat:repeat-x;
}
.aui_aero .aui_top {
	background-position: 0 -48px;
_ie6png:images/dialog/ie6/aui_top.png;
}
.aui_aero .aui_bottom {
	background-position: 0 -82px;
_ie6png:images/dialog/ie6/aui_bottom.png;
}
.aui_aero .aui_left, .aui_aero .aui_right {
	background-image:url(images/dialog/aero_s2.png);
	background-repeat:repeat-y;
}
.aui_aero .aui_left {
	background-position:left top;
_ie6png:images/dialog/ie6/aui_left.png;
}
.aui_aero .aui_right {
	background-position: right bottom;
_ie6png:images/dialog/ie6/aui_right.png;
}
/*minDialog*/
.aui_aero table.c6ui-alertBox, .aui_aero table.c6ui-errBox, .aui_aero table.c6ui-helpBox, .aui_aero table.c6ui-okBox, .aui_aero table.c6ui-infoBox {
	width:320px;
	height:50px;
}
.aui_aero table.c6ui-alertBox td, .aui_aero table.c6ui-errBox td, .aui_aero table.c6ui-helpBox td, .aui_aero table.c6ui-okBox td, .aui_aero table.c6ui-infoBox td {
	padding:6px;
}
.aui_aero table.c6ui-alertBox td.icon {
	width:40px;
	background:url(images/dialog/icon_alert.png) no-repeat center center;
}
.aui_aero table.c6ui-errBox td.icon {
	width:40px;
	background:url(images/dialog/icon_err.png) no-repeat center center;
}
.aui_aero table.c6ui-helpBox td.icon {
	width:40px;
	background:url(images/dialog/icon_help.png) no-repeat center center;
}
.aui_aero table.c6ui-okBox td.icon {
	width:40px;
	background:url(images/dialog/icon_ok.png) no-repeat center center;
}
.aui_aero table.c6ui-infoBox td.icon {
	width:40px;
	background:url(images/dialog/icon_info.png) no-repeat center center;
}
/*loading*/
.c6ui-loadDialog {
	width:300px;
	height:90px;
	background-image:url(images/dialog/minDialog.png);
	overflow:hidden;
}
.c6ui-loadDialog .progressBarLayout {
	margin:30px 10px 10px 35px;
}
.c6ui-loadDialog .progressBartxt {
	display:block;
	text-align:left;
	width:208px;
}
.c6ui-loadDialog .progressBarLoading {
	background-image:url(images/widget/widget_progressBarLoading.gif);
	width:208px;
	height:15px;
}
/*bubble*/
.c6ui-bubble {
	display:inline;
	position:absolute;
	display:none;
	cursor:default;
}
.c6ui-bubble-layout {
	margin:7px 0;
}
.c6ui-bubble-layout .t, .c6ui-bubble-layout .lt, .c6ui-bubble-layout .rt, .c6ui-bubble-layout .lb, .c6ui-bubble-layout .b, .c6ui-bubble-layout .rb, .c6ui-bubble .corner {
	background:url(images/widget_bubble/bubble_bg.png) repeat-x;
}
.c6ui-bubble-layout .r, .c6ui-bubble-layout .l {
	background:url(images/widget_bubble/bubble_bg_v.png) repeat-y;
}
.c6ui-bubble-layout .con {
	padding:6px;
	color:#9b6912;
	background-color: #fffbe6;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#fffbe6), to(#f9eda8)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient(top, #fffbe6, #f9eda8); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient(top, #fffbe6, #f9eda8); /* FF3.6 */
	background-image:     -ms-linear-gradient(top, #fffbe6, #f9eda8); /* IE10 */
	background-image:      -o-linear-gradient(top, #fffbe6, #f9eda8); /* Opera 11.10+ */
	background-image:         linear-gradient(top, #fffbe6, #f9eda8);
 filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#fffbe6', EndColorStr='#f9eda8'); /* IE6–IE9 */
}
.c6ui-bubble-layout .t {
	background-position:0 -4px;
}
.c6ui-bubble-layout .b {
	background-position:0 -6px;
}
.c6ui-bubble-layout .l {
	background-position:0 0;
}
.c6ui-bubble-layout .r {
	background-position:-2px 0;
}
.c6ui-bubble-layout .lt {
	width:2px;
	height:2px;
	background-position:0 0;
}
.c6ui-bubble-layout .rt {
	background-position:-2px 0;
}
.c6ui-bubble-layout .rb {
	width:2px;
	height:2px;
	background-position:-2px -2px;
}
.c6ui-bubble-layout .lb {
	background-position:0 -2px;
}
.c6ui-bubble .corner {
	width:11px;
	height:8px;
}
.c6ui-bubble .leftTop {
	position:absolute;
	left:10px;
	top:1px;
	background-position:0 -10px;
}
.c6ui-bubble .rightTop {
	position:absolute;
	right:10px;
	top:1px;
	background-position:0 -10px;
}
.c6ui-bubble .leftBottom {
	position:absolute;
	left:10px;
	bottom:1px;
	_bottom:-6px;
	background-position:-15px -10px;
}
.c6ui-bubble .rightBottom {
	position:absolute;
	right:10px;
	bottom:1px;
	_bottom:-6px;
	background-position:-15px -10px;
}
.c6ui-bubble .none {
	display:none;
}
/*minSearch*/
.c6ui-widget-minSearch, .c6ui-widget-minSearch .minSearchInput, .c6ui-widget-minSearch a.minSearchBtn {
	background-image: url(images/widget_minSearch/widget_minSearch.png);
}
.c6ui-widget-minSearch {
	background-position:0 0;
	background-repeat:no-repeat;
	float:left;
	display:inline-block;
	height:22px;
	position:relative;
	clear:both;
}
.c6ui-widget-minSearch .minSearchInput {
	background-position:0 -22px;
	height:22px;
	margin-right:24px;
	margin-left:3px;
}
.c6ui-widget-minSearch .minSearchInput input {
	background:none;
	width:98%;
	_width:70%;
	height:22px;
	line-height:20px;
	_height:18px;
	border:none;
	padding:0 3px;
}
.c6ui-widget-minSearch a.minSearchBtn {
	background-position:0 -44px;
	height:22px;
	width:24px;
	display:block;
	position:absolute;
	top:0;
	right:0;
}
.c6ui-widget-minSearch a.minSearchBtn:hover {
	background-position:0 -66px;
}
/*accordion*/
.c6ui-widget-accordion {
	border:#c3daf9 solid 1px;
	background-color:#FFFFFF;
	max-width:400px;
	width:200px;
	height:600px;
}
.c6ui-widget-accordion .accordionCon {
	overflow-y:auto;
	overflow-x:hidden;
	padding-top:4px;
}
.c6ui-widget-accordion h1.title, .c6ui-widget-accordion h2.item, .c6ui-widget-accordion h2.item span {
	background-image:url(images/widget_accordion/widget_accordion.png);
}
.c6ui-widget-accordion h1.title {
	font-size:14px;
	height:29px;
	border-bottom:#c3daf9 solid 1px;
	line-height:30px;
	font-size:14px;
	font-weight:bold;
	padding-left:10px;
}
.c6ui-widget-accordion h2.item {
	background-position:0 -29px;
	margin:4px;
	height:26px;
	cursor:pointer;
}
.c6ui-widget-accordion h2.item span {
	background-position:right -55px;
	background-repeat:no-repeat;
	margin-left:3px;
	height:26px;
	line-height:26px;
	cursor:pointer;
	display:block;
	padding-left:10px;
}
.c6ui-widget-accordion h2.open {
	background-position:0 -81px;
}
.c6ui-widget-accordion h2.open span {
	background-position:right -107px;
}
.c6ui-widget-accordion div.con {
	margin:0 4px;
	padding:3px 10px;
	overflow-x:hidden;
}
/*portalSet*/
.c6ui-portalSet-title {
	position:absolute;
	margin:6px;
	font-weight:bold;
}
.c6ui-portalSet-title img {
	margin-bottom:-3px;
	margin-right:3px;
}
.c6ui-portalSet-tabCon {
	background-color:#eaf1f9;
}
.c6ui-portalSet-tabCon .con {
	height:128px;
	width:100%;
	display:none;
	vertical-align:top;
}
.c6ui-portalSet-tabCon .con .tab {
	height:128px;
}
.c6ui-portalSet-ctrlhand {
	height:10px;
	background-color:#e6edf5;
	cursor:pointer;
	border-bottom:solid 1px #509ae1;
	background-image:url(images/portalSet/ctrlHand.png);
	background-position:center 0;
	background-repeat:no-repeat;
	overflow:hidden;
}
.c6ui-portalSet-tabCon .con .tab .c6ui-list {
	border-right:solid 1px #bfcedf;
	height:128px;
	width:110px;
}
.c6ui-portalSet-tabCon .con .tab .c6ui-list li {
	height:28px;
	line-height:28px;
	padding-left:15px;
	cursor:pointer;
}
.c6ui-portalSet-tabCon .con .tab .c6ui-list li.active {
	background-color:#f7fafd;
	border-bottom:solid 1px #bfcedf;
	border-top:solid 1px #bfcedf;
	position:relative;
	width:96px;
}
.c6ui-applist {
	display:none;
	height:128px;
	overflow:hidden;
	overflow-y:auto;
	background-color:#f7fafd;
}
.c6ui-applist a {
	color:#666666;
	display:block;
	height:39px;
	width:122px;
	background-image:url(images/portalSet/app.png);
	background-position:0 -117px;
	background-repeat:no-repeat;
	float:left;
	margin:5px;
	line-height:34px;
	padding-left:8px;
	cursor:pointer;
	text-decoration: none;
}
.c6ui-applist a:hover {
	background-position:0 -78px;
}
.c6ui-applist a.active {
	background-position:0 -39px;
}
.c6ui-applist a.active:hover {
	background-position:0 0;
}
.c6ui-applist a img {
	margin-right:6px;
	margin-bottom:-3px;
}
.radiobtnBox {
	width:120px;
	height:30px;
	margin:3px 12px;
}
.radiobtnBox span {
	float:left;
	display:block;
	width:60px;
	height:30px;
	line-height:30px;
	text-align:center;
	background-image:url(images/portalSet/radiobtn.png);
	background-repeat:no-repeat;
	cursor:pointer;
}
.radiobtnBox span.r {
	background-position:-60px 0;
}
.radiobtnBox span.r.active {
	background-position:-60px -30px;
	font-weight:bold;
}
.radiobtnBox span.m {
	background-position:-4px 0px;
	width:56px;
}
.radiobtnBox span.m.active {
	background-position:-4px -30px;
	font-weight:bold;
}
.radiobtnBox span.active {
	background-position:0 -30px;
	font-weight:bold;
}
.c6ui-applist .layoutlist {
	margin:10px 0 0 10px;
	display:none;
}
.c6ui-applist .layoutlist a {
	background-image:url(images/portalSet/laylist.png);
	background-position:0 0;
	padding:0;
	width:57px;
	height:55px;
}
.c6ui-applist .layoutlist a.active {
	_border:solid 1px #FF0000;
	_width:55px;
	_height:53px;
}
.c6ui-applist .layoutlist a.m1-3-2:hover, .c6ui-applist .layoutlist a.m1-3-2.active {
	background-position:0 -55px;
}
.c6ui-applist .layoutlist a.m2-3-1:hover, .c6ui-applist .layoutlist a.m2-3-1.active {
	background-position:-57px -55px;
}
.c6ui-applist .layoutlist a.m1-2-1:hover, .c6ui-applist .layoutlist a.m1-2-1.active {
	background-position:-116px -55px;
}
.c6ui-applist .layoutlist a.m1-3-1:hover, .c6ui-applist .layoutlist a.m1-3-1.active {
	background-position:-174px -55px;
}
.c6ui-applist .layoutlist a.m2-2-1:hover, .c6ui-applist .layoutlist a.m2-2-1.active {
	background-position:-232px -55px;
}
.c6ui-applist .layoutlist a.m1-2-2:hover, .c6ui-applist .layoutlist a.m1-2-2.active {
	background-position:-290px -55px;
}
/*-*/
.c6ui-applist .layoutlist a.m1-3-2 {
	background-position:0 0;
}
.c6ui-applist .layoutlist a.m2-3-1 {
	background-position:-57px 0;
}
.c6ui-applist .layoutlist a.m1-2-1 {
	background-position:-116px 0;
}
.c6ui-applist .layoutlist a.m1-3-1 {
	background-position:-174px 0;
}
.c6ui-applist .layoutlist a.m2-2-1 {
	background-position:-232px 0;
}
.c6ui-applist .layoutlist a.m1-2-2 {
	background-position:-290px 0;
}
/*2*/
.c6ui-applist .layoutlist a.m1-3:hover, .c6ui-applist .layoutlist a.m1-3.active {
	background-position:0 -165px;
}
.c6ui-applist .layoutlist a.m3-1:hover, .c6ui-applist .layoutlist a.m3-1.active {
	background-position:-57px -165px;
}
.c6ui-applist .layoutlist a.m2-2:hover, .c6ui-applist .layoutlist a.m2-2.active {
	background-position:-116px -165px;
}
.c6ui-applist .layoutlist a.m2-3:hover, .c6ui-applist .layoutlist a.m2-3.active {
	background-position:-174px -165px;
}
.c6ui-applist .layoutlist a.m3-2:hover, .c6ui-applist .layoutlist a.m3-2.active {
	background-position:-232px -165px;
}
/*-*/
.c6ui-applist .layoutlist a.m1-3 {
	background-position:0 -110px;
}
.c6ui-applist .layoutlist a.m3-1 {
	background-position:-57px -110px;
}
.c6ui-applist .layoutlist a.m2-2 {
	background-position:-116px -110px;
}
.c6ui-applist .layoutlist a.m2-3 {
	background-position:-174px -110px;
}
.c6ui-applist .layoutlist a.m3-2 {
	background-position:-232px -110px;
}
/*modType*/
.c6ui-modType li {
	padding:8px;
	margin:6px;
	float:left;
	width:165px;
	height:174px;
	_width:181px;
	_height:190px;
	border:solid 1px #eaeaea;
	/*radius*/
	-moz-border-radius: 2px; /* FF1+ */
	-webkit-border-radius: 2px; /* Saf3-4, iOS 1+, Android 1.5+ */
	border-radius: 2px; /* Opera 10.5, IE9, Saf5, Chrome, FF4 */
	-moz-background-clip: padding;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	/*gradient*/
	background-color: #ffffff;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#444444), to(#f6f6f6)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient(top, #ffffff, #f6f6f6); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient(top, #ffffff, #f6f6f6); /* FF3.6 */
	background-image:     -ms-linear-gradient(top, #ffffff, #f6f6f6); /* IE10 */
	background-image:      -o-linear-gradient(top, #ffffff, #f6f6f6); /* Opera 11.10+ */
	background-image:         linear-gradient(top, #ffffff, #f6f6f6);
 filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#ffffff', EndColorStr='#f6f6f6'); /* IE6–IE9 */
	cursor:pointer;
}
.c6ui-modType li.active {
	padding:8px;
	margin:6px;
	float:left;
	width:165px;
	height:174px;
	_width:181px;
	_height:190px;
	border:solid 1px #ff9900;
	/*radius*/
	-moz-border-radius: 2px; /* FF1+ */
	-webkit-border-radius: 2px; /* Saf3-4, iOS 1+, Android 1.5+ */
	border-radius: 2px; /* Opera 10.5, IE9, Saf5, Chrome, FF4 */
	-moz-background-clip: padding;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	/*gradient*/
	background-color: #ffffff;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#444444), to(#feefce)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient(top, #ffffff, #feefce); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient(top, #ffffff, #feefce); /* FF3.6 */
	background-image:     -ms-linear-gradient(top, #ffffff, #feefce); /* IE10 */
	background-image:      -o-linear-gradient(top, #ffffff, #feefce); /* Opera 11.10+ */
	background-image:         linear-gradient(top, #ffffff, #feefce);
 filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#ffffff', EndColorStr='#feefce'); /* IE6–IE9 */
}
.c6ui-modType li img {
	width:165px;
	height:121px;
	background-color:#CCCCCC;
}
/*分组*/
.c6ui-group {
	border:1px solid #cccccc;
	-moz-border-radius:3px;/*firefox*/
	-webkit-border-radius: 3px;/*webkit*/
	border-radius: 3px;/*css 3*/
	position:relative;
	padding:0px 10px;
}
.c6ui-group-legend {
	margin-top:-8px;
	display:block;
}
.c6ui-group-legend-inner {
	background-color:#FFFFFF;
}
.c6ui-group-inner {
	padding-top:10px;
	padding-bottom:12px;
}
/*分隔符*/
.c6ui-split {
	border-top:1px solid #cccccc;
	border-bottom:1px solid #cccccc;
	-moz-border-radius:3px;/*firefox*/
	-webkit-border-radius: 3px;/*webkit*/
	border-radius: 3px;/*css 3*/
	position:relative;
}
.c6ui-split-legend {
	margin-top:-8px;
	display:block;
}
.c6ui-split-legend-inner {
	background-color:#FFFFFF;
}
.c6ui-split-inner {
	padding:10px 10px 12px 10px;
}
/*userInfo*/
.c6ui-userInfo {
	position:absolute;
	height:141px;
	width:283px;
	background-color:#fff;
	background-image:url(images/userinfo/userinfoBg.png);
	background-repeat:repeat-x;
	border:solid 2px #45adf0;
	-moz-border-radius: 2px; /* FF1-3.6 */
	-webkit-border-radius: 2px; /* Saf3-4, iOS 1-3.2, Android <1.6 */
	border-radius: 2px; /* Opera 10.5, IE9, Saf5, Chrome, FF4, iOS 4, Android 2.1+ */
}
.c6ui-userInfo .photoBox {
	float:left;
	margin:8px;
	padding:2px;
	display:inline-block;
	_display:inline;
	border:solid #E6E6E6 1px;
	-moz-box-shadow: 0px 0px 4px #e9e9e9; /* FF3.5+ */
	-webkit-box-shadow: 0px 0px 4px #e9e9e9; /* Saf3.0+, Chrome */
	box-shadow: 0px 0px 4px #e9e9e9; /* Opera 10.5, IE9, Chrome 10+ */
	-moz-border-radius: 2px; /* FF1-3.6 */
	-webkit-border-radius: 2px; /* Saf3-4, iOS 1-3.2, Android <1.6 */
	border-radius: 2px; /* Opera 10.5, IE9, Saf5, Chrome, FF4, iOS 4, Android 2.1+ */
	position:relative;
}
.c6ui-userInfo .photoBox .status {
	position:absolute;
	bottom:0;
	right:0;
}
.c6ui-userInfo .InfoTxt {
	margin:8px;
	color:#999999;
	line-height:16px;
}
.c6ui-userInfo .InfoTxt .name {
	font-size:14px;
	font-weight:bold;
	margin:3px 0;
	color:#333333;
	line-height:1.5;
}
.c6ui-userInfo .bottom {
	height:29px;
	_height:32px;
	background-color:#edf2f6;
	bottom:0;
	text-align:right;
	padding-right:10px;
	padding-top:8px;
}
.c6ui-userInfo .tria {
	background-image:url(images/userinfo/tria.gif);
	height:10px;
	width:10px;
	position:absolute;
	font-size:0;
	overflow:hidden;
	line-height:0;
}
.c6ui-userInfo .lb {
	height:5px;
	background-position: left bottom;
	bottom:-7px;
	left:10px;
}
.c6ui-userInfo .rb {
	height:5px;
	background-position: left bottom;
	bottom:-7px;
	right:10px;
}
.c6ui-userInfo .lt {
	height:5px;
	background-position: left top;
	top:-7px;
	left:10px;
}
.c6ui-userInfo .rt {
	height:5px;
	background-position: left top;
	top:-7px;
	right:10px;
}
.c6ui-userInfo .l {
	width:5px;
	background-position: left top;
	top:10px;
	left:-7px;
}
.c6ui-userInfo .r {
	width:5px;
	background-position: right top;
	top:10px;
	right:-7px;
}
/*
 *Style
 */
#c6ui-header {
	background:#1869B7 url(images/c6ui_header_bannerBg_edit.png) no-repeat bottom left;
}
#c6ui-header-extend .c6ui-header-system span {
	color:#FFFFFF;
}
/*public search bar*/
.c6ui_public_search {
	float:left;
	white-space:nowrap
}
.c6ui_public_search a.leftSelect {
	float:left;
	height:24px;
	width:30px;
	background:url(images/widget_minSearch/c6ui-search.gif) 0 0 no-repeat;
}
.c6ui_public_search a.rightSearch {
	border:0;
	border-left:0;
	display:inline;
	float:left;
	height:24px;
	line-height:24px;
	width:39px;
	text-align:center;
	font-size:12px;
	background:url(images/widget_minSearch/c6ui-search.gif) right -26px no-repeat;
	color:#000;
}
.c6ui_public_search .search_input {
	border:1px solid #ccc;
	border-left:0;
	border-right:0;
	float:left;
	height:22px;
	line-height:22px;
	background:#fff url(../../images/c6ui-public-search.png) top center repeat-x;
	width:300px
}
/*tab 自动添加左右箭头*/
.c6ui-base-Tab-top ul li.left_arrow, .c6ui-base-Tab-top ul li.right_arrow {
	display:none;
}
.c6ui-base-Tab-top.add_arrow {
	position:relative;
	_width:100%;
	height:30px;
	overflow:hidden;
}
.c6ui-base-Tab-top.add_arrow ul {
	padding:0 20px;
	height:30px;
	width:10000px;
	overflow:hidden
}
.c6ui-base-Tab-top.add_arrow ul li.left_arrow, .c6ui-base-Tab-top.add_arrow ul li.right_arrow {
	background:#fff url(images/base_tab/base_tab_arrow_bg.gif) no-repeat;
	width:18px;
	height:27px;
	position:absolute;
	padding:0;
	display:inline;
}
.c6ui-base-Tab-top.add_arrow ul li.left_arrow {
	left:-1px;
}
.c6ui-base-Tab-top.add_arrow ul li.right_arrow {
	background-position:right center;
	right:-2px;
}
.left_arrow img.left_able, .left_arrow img.left_unable, .right_arrow img.right_able, .right_arrow img.right_unable {
	width:4px;
	height:8px;
	margin:9px 0 0 7px;
	background:url(images/base_tab/base_tab_arrow.gif) 0 0 no-repeat
}
.left_arrow img.left_unable {
	background-position:0 -20px;
}
.right_arrow img.right_able {
	background-position:0 -10px;
}
.right_arrow img.right_unable {
	background-position:0 -30px;
}
/*改写skin.css*/
.c6ui-base-Tab-top ul li span {
	display:block;
	float:left;
	text-align:center;
	width:auto!important;
	width:60px;
	min-width:60px;
}
/*日期控件*/
.ui-widget-header {
	background-color:#e8f1f9;
	border: 0;
	color: #1f376d;
	font-weight:700;
}
.ui-widget-header a:link, .ui-widget-header a:visited, .ui-widget-header a:hover, .ui-widget-header a:active {
	color:#1f376d;
	font-weight:500;
}
.ui-widget-content {
	border: 1px solid #c5ddf1;
	background: #ffffff
}
.ui-widget-content a:link, .ui-widget-content a:visited, .ui-widget-content a:hover, .ui-widget-content a:active {
	color:#1f376d;
	font-weight:500;
}
/*移植原C6非规范定义*/
.font-red {
	color:#FF0000;
}
/*大名片*/
#personCard {
	position:absolute;
	top:50%;
	left :50%;
	font-size:12px;
	display:none;
}
#personCard .cardBody {
	width:406px;
	padding:4px 4px 0 4px;
	position:relative;
	background:url(images/c6ui_card/card_bg.png) top center;
}
#personCard .cardFoot {
	width:406px;
	padding:0 4px 4px 4px;
	height:4px;
	background:url(images/c6ui_card/card_bottom.png) no-repeat;
	font-size:1px;
}
.cardBody .cardCon {
	width:404px;
	border:1px solid #99ccff;
	height:auto
}
table.structTable tr td {
	border-bottom:1px solid #d1e4f6;
	vertical-align:top;
}
table.innerTable tr td.odd {
	background:#edf2f6;
	text-align:center;
	width:110px;
}
table.innerTable tr td.topPadding {
	padding-top:12px;
}
table.innerTable tr td.botPadding {
	padding-bottom:12px;
}
table.innerTable tr td.allPadding {
	padding-top:12px;
	padding-bottom:12px;
}
table.innerTable tr td {
	border-bottom:0;
	padding:5px;
	line-height:14px;
	white-space:normal;
	word-break:break-all;
}
.innerTable img.personImg {
	margin:10px 0;
	-moz-border-radius: 3px;
	-khtml-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	box-shadow:0 0 5px #ccc;
	-moz-box-shadow:0 0 5px #ccc;
	-webkit-box-shadow:0 0 5px #ccc;
}
.personDetail {
	line-height:25px !important;
}
.personDetail b {
	font-weight:bold;
	font-size:14px;
	margin-right:5px;
}
.personDetail img {
	margin:0 5px;
}
img#closeIt {
	position:absolute;
	right:-18px;
	top:-15px;
	width:38px;
	height:38px;
	background:url(images/c6ui_card/close_card.png) no-repeat;
	cursor:pointer
}
/*workflow setting*/
#left_td, #right_td {
	margin:15px;
}
.work_flow {
	position:relative;
	line-height:24px;
}
.work_flow img {
	margin:5px 10px 0 10px;
	margin-top:0px\9;
	width:32px;
	height:32px;
	position:absolute;
	left:0;
	top:0;
}
.work_flow h3 {
	font-size:14px;
	font-weight:bold;
	padding-left:52px;
}
.work_flow ul {
	width:100%;
	border-bottom:1px solid #ccc;
	padding-bottom:10px;
	margin-bottom:10px;
	margin-left:52px;
	list-style-type:none;
}
.work_flow ul li {
	list-style-type:none;
}
.work_flow ul li a {
	color:#003399;
	text-decoration:none;
}
.work_flow ul li.normal {
	float:left;
	padding-right:10px;
	background:url(../../images/depart_li.png) right center no-repeat;
	white-space:nowrap;
}
.work_flow ul li.last {
	float:left;
	margin-right:10px;
	white-space:nowrap;
}
.work_flow ul li.clear_fix {
	clear:both;
	font-size:1px;
	height:1px;
	line-height:1px;
}
/*快捷桌面样式*/
.deskbody{
	background:url(images/shortcutdesktop_bg.png) repeat-x #8BBD1B;
}
.desktopMain{
	background:url(images/shortcutdesktop.png) center no-repeat;
}
/*左侧菜单*/
.leftMenuH3{
    background: url(images/leftMenuH3.png) no-repeat;  
    color: #0d6294;  
}
/*add by duwy 表格title样式*/
.table_title{height:32px;background:url(images/base_table/table_title.png) center top repeat-x;}
.subpage_list a.normalMenu,.subpage_list a.currentMenu,.subpage_list a.normalLast{display:block; float:left; height:26px; line-height:26px;margin:3px 2px 0 -1px;color:#333; background:url(images/base_tab/inline_depart.png) right center no-repeat; border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px;padding:0 16px;cursor:pointer;}
.subpage_list a.currentMenu,.subpage_list a:hover{color:#fff; background:url(images/base_tab/inline_tab.png) center center repeat-x;margin:3px 4px 0 -3px;}
  /*add by duwy 竖形菜单*/
table.vertical_menu_table tr td.vertical_td{background:#f5f8fd;border-right:1px solid #bccce1;}
table.vertical_menu_table tr td.vertical_td .vertical_menu{width:136px;padding:7px;}
.vertical_menu ul li{margin-bottom:4px;}
.vertical_menu ul li a,.vertical_menu ul li a:hover,.vertical_menu ul li a.vertical_td_on{width:120px;padding:0 8px; display:block;height:21px; line-height:21px;color:#003399; cursor:pointer}
.vertical_menu ul li a:hover,.vertical_menu ul li a.vertical_td_on{ background-color:#5483c5; color:#fff;}