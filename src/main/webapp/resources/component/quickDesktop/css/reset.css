@charset "utf-8";
/* CSS Document */
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, button, textarea, p, blockquote, th, td {
	margin:0;
	padding:0;
	font-family:Arial,"宋体";
}
:focus {
	outline:0
}
fieldset, img {
	border:0
}
:focus {
	outline:0
}
address, caption, cite, code, dfn, em, strong, th, var, optgroup {
	font-style:normal;
	font-weight:normal
}
h1, h2, h3, h4, h5, h6 {
	font-size:100%;
	font-weight:normal
}
abbr, acronym {
	border:0;
	font-variant:normal
}
input, button, textarea, select, optgroup, option {
	font-family:inherit;
	font-size:inherit;
	font-style:inherit;
	font-weight:inherit
}
code, kbd, samp, tt {
	font-size:100%
}
input, button, textarea, select {
*font-size:100%;
resize: none;
}
textarea:focus { outline: none; }
body {
	font-size:12px;
	line-height:1.5;
}
ol, ul {
	list-style:none
}
table {
	border-collapse:collapse;
	border-spacing:0
}
caption, th {
	text-align:left
}
sup, sub {
	font-size:100%;
	vertical-align:baseline
}
:link, :visited, ins {
	text-decoration:none
}
blockquote, q {
	quotes:none
}
blockquote:before, blockquote:after, q:before, q:after {
	content:'';
	content:none
}
html,body{ width:100%; height:100%;}

/* 原C6排版需要 */
.td{ padding-left:8px;  width:85px; word-break:break-all; height:30px;}
.td2{ width:175px; word-break:break-all;}
.dateTimePicker,.dateTimePicker1,.dateTimePicker2,.MessagedateTimePicker3{width:130px}
body,td,th,p,div,span,select,form,option,textarea,li,a{font-size:12px;}