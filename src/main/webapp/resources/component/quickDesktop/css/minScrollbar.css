/***滚动条样式**/
.ui-minScrollbar
{
    float: left;
    position: relative;
    left: 0px;
    top: 0px;
}
div.ui-minScrollbar-viewable
{
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 200px;
    -moz-user-select: none; /**ff*/
    -webkit-user-select: none; /**chrome、safari、360极速*/
    gn: expression(this.onselectstart=function(){return false;}); /**IE7*/
    hutia: expression(this.onselectstart=function(){return(false)}); /**IE7*/
}
div.ui-minScrollbar-horizontal, div.ui-minScrollbar-vertical
{
    position: absolute;
    z-index: 999;
    overflow: hidden;
    bottom: 1px;
    cursor: default;
}
div.ui-minScrollbar-horizontal
{
    height: 7px;
    left: 1px;
    bottom: 1px;
}
div.ui-minScrollbar-vertical
{
    width: 7px;
    top: 1px;
    right: 1px;
}
div.ui-vScroll, div.ui-hScroll
{
    position: relative;
    z-index: 999;
    top: 0px;
    left: 0px;
    border: 1px solid #ccc;
    -webkit-background-clip: padding-box;
    box-sizing: border-box;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    opacity: 0.6; /*非ie8及其以下低版本浏览器下*/
    filter: alpha(opacity=60); /*ie8及其以下低版本浏览器下*/
    background-color: #404040;
}
div.ui-vScroll:hover, div.ui-hScroll:hover
{
    opacity: 1;
    cursor: default;
}
.ui-minScrollbar-horizontal > div.ui-hScroll
{
    height: 7px;
}
.ui-minScrollbar-vertical > div.ui-vScroll
{
    width: 7px;
}
