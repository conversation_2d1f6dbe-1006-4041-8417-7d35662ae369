@CHARSET "UTF-8";
.desktopMain {
	margin: 0 auto;
	overflow: hidden;
	padding-top: 20px;
	width: 100%;
	/*height: 100%;*/
}

.desktopMain a {
	color: #fff;
}

.desktopNavL {
	background: url(../image/navL.png) no-repeat left;
	display: inline-block;
	float: left;
	height: 30px;
	width: 3px;
}

ul.desktopNav {
	list-style: none;
	background: url(../image/navC.png) repeat-x;
	float: left;
	height: 30px;
	padding-right: 16px;
	line-height: 30px;
}

.desktopNavR {
	background: url(../image/navR.png) no-repeat right;
	float: left;
	height: 30px;
	width: 41px;
}

.desktopNav li {
	list-style: none;
	float: left;
	margin-left: 4px;
	text-align: center;
	width: 42px;
}

.desktopNav li a {
	background: url(../image/navLi.png) no-repeat;
	display: block;
	margin: 0 auto;
	width: 21px;
}

.desktopNav li a:hover {
	background: url(../image/navLiHover.png) no-repeat;
}

.desktopNav li.pointLi a {
	cursor: default;
}

.desktopNav li.pointLi a:hover {
	background: url(../image/navLi.png) no-repeat;
}

.desktopNav li.navCurrent a {
	background: url(../image/navLiCurrent.png) no-repeat;
}

.desktopNav li.iconHover a {
	background: url(../image/num.png) no-repeat;
	height: 31px;
	width: 31px;
}

.desktopNavR a {
	background: url(../image/navAdd.png) no-repeat;
	display: block;
	height: 30px;
	width: 38px;
}

.desktopNavR a:hover {
	background: url(../image/navAddHover.png) no-repeat;
}

.mainList {
	clear: both;
	overflow: hidden;
	width: 100%;
}

.ulList {
	height: 600px;
	margin-left: 0px;
	overflow: hidden;
}

ul.desktopList {
	float: left;
	height: 420px;
	overflow: hidden;
	margin: 30px 0px;
	list-style: none;
}

ul.desktopList li {
	list-style: none;
	float: left;
	height: 111px;
	margin-left: 16px;
	margin-top: 20px;
	width: 108px;
}

ul.desktopList li a {
	background: url(../image/iconbg.png) no-repeat;
	display: block;
	height: 111px;
	text-align: center;
	width: 108px;
}

ul.desktopList li a:hover {
	background: url(../image/iconbgHover.png) no-repeat;
}

ul.desktopList li a img {
	margin-top: 13px;
}

ul.desktopList li a span {
	display: block;
	margin-top: 7px;
}

ul.desktopList li a p.numCount {
	background: url(../image/num.png) no-repeat;
	height: 31px;
	line-height: 31px;
	position: relative;
	left: 70px; *
	left: 32px;
	top: -98px; *
	top: -98px;
	width: 31px;
}

ul.desktopList li.hoverMid a {
	background: none;
	display: inline-block;
	height: 50px;
	width: 50px;
}

ul.desktopList li.hoverMid a img {
	height: 50px;
}

ul.desktopList li.hoverMid a span {
	display: none;
}

ul.desktopList li.hoverMid a p.numCount {
	display: none;
}

.trashbin {
	visibility: hidden;
	bottom: 10px;
	height: 123px;
	padding-top: 40px;
	position: absolute;
	text-align: center;
	width: 82px;
	z-index: 1001px;
}

.trashbin a {
	background: url(../image/trashbin.png) no-repeat;
	display: block;
	height: 123px;
}

.trashbin a.trashhover {
	background: url(../image/trashbinHover.png) no-repeat;
}

.addIcon {
	background: url(../image/addiconBg.png) repeat-y left top #F5F5F5;
	height: 500px;
	width: 756px;
}

.iconNav {
	float: left;
	overflow: hidden;
	padding: 20px 0px 10px 0px;
}

.iconNav ul {
	list-style: none;
	padding: 0px 14px 0px 12px;
}

.iconNav li {
	list-style: none;
	margin-bottom: 8px;
}

.iconNav li a {
	background: url(../image/menuBtn.png) no-repeat;
	color: #666;
	display: block;
	height: 32px;
	line-height: 32px;
	text-align: center;
	width: 126px;
}

.iconNav li.iconCurrent a {
	background: url(../image/menuBtnCur.png) no-repeat;
	color: #fff;
}

.iconMain {
	float: left;
	margin-left: 2px;
	padding: 12px 0px 0px 0px;
}

.listMain {
	width: 740px;
}

.iconList h1 {
	background: url(../image/titleBg.png) no-repeat left;
	color: #fff;
	display: block;
	font-size: 14px;
	height: 29px;
	line-height: 29px;
	padding-left: 16px;
	width: 153px;
}

.iconElem {
	overflow: hidden;
	padding-left: 25px;
	padding-top: 20px;
	width: 476px;
}

.iconElem li {
	list-style: none;
	float: left;
	margin-right: 126px;
	margin-bottom: 28px;
	width: 173px;
}

.iconImg {
	background: url(../image/addIcoBg.png) no-repeat;
	float: left;
	height: 66px;
	text-align: center;
	width: 66px;
}

.iconImg img {
	height: 46px;
	padding-top: 10px;
	width: 46px;
}

.iconMsg {
	float: left;
	margin-left: 7px;
}

.iconMsg h2 {
	font-weight: bold;
	margin-top: 17px;
}

.iconMsg a.addBtn {
	background: url(../image/addBtn.png) no-repeat;
	color: #fff;
	display: block;
	height: 24px;
	line-height: 24px;
	margin-top: 6px;
	padding-left: 28px;
	width: 36px;
}

.iconMsg a.addBtn:hover {
	color: #ff0;
}

.iconMsg a.delBtn {
	background: url(../image/delBtn.png) no-repeat;
	color: #717171;
	display: block;
	height: 24px;
	line-height: 24px;
	margin-top: 6px;
	text-align: center;
	width: 76px;
}

.iconMsg a.delBtn:hover {
	color: #000;
}