// JScript source code
/* 替代模式窗。showmodaldialog
var sURL = arguments[0]; //URL地址；
var vArguments = arguments[1]; //参数。原来模式窗传递过来的参数；
var vOptions = arguments[2]; //属性。是Joson对象。{width:500,height:300}，可以为空
var fCallback = arguments[3]; //回调方法名。第一参数是returnvalue,可以为空
var aCallbackArguments = arguments[4]; //回调方法参数
var btnOk = arguments[5]; //页面提交按钮ID；
var btnCancel = arguments[6]; //页面关闭按钮ID；

*/
Dialog = {
    Args: [],
    Arguments: null,
    maskdiv: null,
    dialogdiv: null,
    dlialogiframe: null,
    zindex: 0,
    Installize: function(sURL, vArguments, vOptions, fCallback, aCallbackArguments, btnOk, btnCancel, flag) {
        // open打开的窗口的dialog高度校正
        var bodyHeight = zwxJQ(window).height(); //document.body.clientHeight;
        var bodyScrollHeight = document.body.scrollHeight;
        var dialogWidth = vOptions.width || 500;
        var dialogHeight = vOptions.height || 300;
        var dialogTop = Math.floor((bodyHeight - dialogHeight) / 2);
        var dialogLeft = Math.floor((document.body.clientWidth - dialogWidth) / 2);
        if (dialogTop < 5) dialogTop = 5;

        var zindex = 1001;
        if (Dialog.zindex > 0) {
            zindex = Dialog.zindex + 2;
        }

        var markdivid = "modaldialogmark" + (zindex - 1);
        var markiframeid = "modaldialogmarkiframe" + (zindex - 1);
        var dialogdivid = "modaldialogdiv" + zindex;
        var dialogheaderid = "modaldialogheader" + zindex;
        var dialogtitleid = "modaldialogtitle" + zindex;
        var dialogcloseid = "modaldialogclose" + zindex;
        var dialogiframeid = "modaldialogiframe" + zindex;

        var strdialogdiv = "<div class='aui_dialog_wrap aui_aero' id='artDialog6' style='z-index: 1994; visibility: visible;'>";
        strdialogdiv += "<div class='aui_aero art_opacity art_focus' id='artDialog6dialog' style='z-index: 1994; position: absolute; opacity: 1;'>";
        strdialogdiv += "<table class='aui_table'>";
        strdialogdiv += "<tbody>";
        strdialogdiv += "<tr>";
        strdialogdiv += "<td class='aui_border aui_left_top'></td>";
        strdialogdiv += "<td class='aui_border aui_top'></td>";
        strdialogdiv += "<td class='aui_border aui_right_top'></td>";
        strdialogdiv += "</tr>";
        strdialogdiv += "<tr>";
        strdialogdiv += "<td class='aui_border aui_left'></td>";
        strdialogdiv += "<td class='aui_center'><table class='aui_table aui_content_table'>";
        strdialogdiv += "<tbody>";
        strdialogdiv += "<tr>";
        strdialogdiv += "<td class='aui_td_title'><div class='aui_title_wrap'>";
        strdialogdiv += "<div class='aui_title'> <span class='aui_title_icon'></span> <span id='" + dialogtitleid + "'></span></div>";
        strdialogdiv += "<a href='javascript:;' class='aui_close' id='" + dialogcloseid + "'>×</a> </div></td>";
        strdialogdiv += "</tr>";
        strdialogdiv += "<tr>";
        strdialogdiv += "<td style='width:auto;height:auto' class='aui_td_content' id='artDialog6td_content'><div class='aui_content_wrap'>";
        if (flag == "Modal") {     //模态窗口
            strdialogdiv += "<div class='aui_content' id='artDialog6content' style='width:" + (dialogWidth - 50) + "px; height:" + (dialogHeight - 30) + "px;'>";
            strdialogdiv += "<iframe id='" + dialogiframeid + "' src='" + sURL + "' frameBorder='0' scrolling='no'  style='width:100%; height:" + (dialogHeight - 25) + "px;'></iframe>";
            strdialogdiv += "</div>";
        }
        else {     //其它类型窗口
            var tempid = "";
            if (flag == "Alert") {
                tempid = "alertDialogContent";
            }
            else if (flag == "Confirm") {
                tempid = "confirmDialogContent";
            }
            else if (flag == "Prompt") {
                tempid = "promptDialogContent";
            }
            strdialogdiv += "<div class='aui_content' id='" + tempid + "' style='overflow-y:auto;overflow-x:hidden;width:" + (dialogWidth - 50) + "px; height:" + (dialogHeight - 30) + "px;'>";
            strdialogdiv += "</div>";
        }

        strdialogdiv += "</div></td>";
        strdialogdiv += "</tr>";
        if (flag != "Modal") {    //提示窗口
            var tempid = "";
            if (flag == "Alert") {
                tempid = "alertDialogbtn";
            }
            else if (flag == "Confirm") {
                tempid = "confirmDialogbtn";
            }
            else if (flag == "Prompt") {
                tempid = "promptDialogbtn";
            }
            strdialogdiv += "<tr>";
            strdialogdiv += "<td class='aui_td_buttons'><div class='aui_buttons_wrap' id='" + tempid + "'>";
            strdialogdiv += " </div></td>";
            strdialogdiv += "</tr>";
        }
        strdialogdiv += "</tbody>";
        strdialogdiv += "</table></td>";
        strdialogdiv += "<td class='aui_border aui_right'></td>";
        strdialogdiv += "</tr>";
        strdialogdiv += "<tr>";
        strdialogdiv += "<td class='aui_border aui_left_bottom'></td>";
        strdialogdiv += "<td class='aui_border aui_bottom'></td>";
        strdialogdiv += "<td class='aui_border aui_right_bottom'></td>";
        strdialogdiv += "</tr>";
        strdialogdiv += "</tbody>";
        strdialogdiv += "</table>";
        strdialogdiv += "</div>";
        strdialogdiv += "</div>";


        var oDiv = document.createElement("DIV");
        oDiv.id = dialogdivid;
        oDiv.style.position = "absolute";
        oDiv.style.top = dialogTop + "px";
        oDiv.style.left = dialogLeft + "px";
        oDiv.style.zIndex = zindex;
        oDiv.style.width = dialogWidth + "px";
        oDiv.style.height = dialogHeight + "px";
        document.body.appendChild(oDiv);
        document.getElementById(dialogdivid).innerHTML = strdialogdiv;
        if (IsAndroid()) {
            var frameid = zwxJQ(top.document).find("#c6ui-main").find("div:visible").find(".c6ui-iframe-main")[0].id;

            zwxJQ(top.document).find("#" + frameid).css("display", "none");
        }
        var mDiv = document.createElement("DIV");
        mDiv.id = markdivid;
        mDiv.style.position = "absolute";
        mDiv.style.backgroundColor = "#000";
        mDiv.style.top = 0;
        mDiv.style.left = 0;
        mDiv.style.zIndex = zindex - 1;
        mDiv.style.width = "100%";
        // open打开的窗口resize时dialog高度校正
        // 处理ipad与pc差异
        if (isipad())
            mDiv.style.height = (bodyHeight > bodyScrollHeight ? bodyHeight : bodyScrollHeight) + "px";
        else
            mDiv.style.height = "100%"; //(bodyHeight > bodyScrollHeight ? bodyHeight : bodyScrollHeight) + "px";
        mDiv.style.filter = "alpha(opacity=30)"; //IE
        mDiv.style.opacity = "0.3"; //非IE
        document.body.appendChild(mDiv);
        Dialog.zindex = zindex;
        Dialog.dlialogiframe = dialogiframeid;
        Dialog.maskdiv = document.getElementById(markdivid);
        Dialog.dialogdiv = document.getElementById(dialogdivid);
        Dialog.Args.push(vArguments);
        Dialog.Arguments = vArguments;
        var element = document.getElementById(dialogcloseid);
        if (element.attachEvent) {
            element.attachEvent("onclick", function() {
                Dialog.Close();
            });
        }
        else {
            element.addEventListener("click", function() {
                Dialog.Close();
            }, false);
        }

        // 屏蔽IE6自身bug，页面弹出时IE6下隐藏父页面的select
        try {
            zwxJQ("#" + markdivid).bgiframe();
        }
        catch (err) { }
        // 解决点击输入框（text、textarea）弹出模式窗时焦点光标还留在输入框的问题
        Dialog.dialogdiv.focus();
    },
    Call: function(fCallback, aCallbackArguments) {

    },
    Close: function() {
        //debugger;
        var dDiv = document.getElementById("modaldialogdiv" + Dialog.zindex);
        var mDiv = document.getElementById("modaldialogmark" + (Dialog.zindex - 1));
        var iDiv = document.getElementById("modaldialogmarkiframe" + (Dialog.zindex - 1));
        var frame = top.zwxJQ(dDiv).find("iframe");
        if (dDiv) {
            if (frame.length > 0) frame[0].src = "";
            document.body.removeChild(dDiv);
            document.body.removeChild(mDiv);
           // document.body.removeChild(iDiv);
            Dialog.Args.pop();
            if (Dialog.zindex > 1001) {
                var zindex = Dialog.zindex - 2;
                Dialog.zindex = zindex;
                Dialog.dlialogiframe = "modaldialogiframe" + zindex;
            }
            else {
                Dialog.zindex = 0;
                Dialog.dlialogiframe = null;
            }
        }
        //android下div的z-index属性无效
        if (IsAndroid()) {
            var frameid = zwxJQ(top.document).find("#c6ui-main").find("div:visible").find(".c6ui-iframe-main")[0].id;

            zwxJQ(top.document).find("#" + frameid).css("display", "block");
        }
        return false;
    }
}
function openModalDialog(sURL, vArguments, vOptions, fCallback, aCallbackArguments, btnOk, btnCancel) {
    if (sURL.toLowerCase().indexOf(location.host.toLowerCase()) == -1) { //绝对路径不做处理
        var href = location.pathname;
        href = href.substring(href.indexOf("/", 1) + 1, href.length);
        href = href.substring(0, href.lastIndexOf("/") + 1);
        sURL = href.substring(0, href.lastIndexOf("/") + 1) + sURL;
        //修改锁定路径，个别情况pathname有出入。
        var pathname = top.location.href.substring(top.location.href.indexOf("//") + 2);
        var num = pathname.split("/").length - 3;
        for (i = 0; i < num; i++) {
            sURL = "../" + sURL;
        }
    }

    Dialog.Installize(sURL, vArguments, vOptions, fCallback, aCallbackArguments, btnOk, btnCancel, 'Modal');
    var frame = document.getElementById(Dialog.dlialogiframe);
    //alert(frame);
    if (frame.attachEvent) {
        frame.attachEvent("onload", function() {
            load();
        });
    }
    else {
        frame.addEventListener("load", function() {
            load();
        }, false);
    }
    var load = function() {
        if (!document.getElementById("modaldialogtitle" + Dialog.zindex)) return false;
        // 增加frame.contentWindow判空，IE9下报错。 
        if (frame.contentWindow != null) {
            var title = frame.contentWindow.document.title;
            if (title != "") {
                document.getElementById("modaldialogtitle" + Dialog.zindex).innerHTML = title;
            }
            if (btnOk != null) {
                var elementOK = frame.contentWindow.document.getElementById(btnOk);
                if (elementOK) {
                    if (elementOK.attachEvent) {
                        elementOK.attachEvent("onclick", function() {
                            doCallback();
                        });
                    }
                    else {
                        elementOK.addEventListener("click", function() {
                            doCallback();
                        }, false);
                    }
                }
            }

            if (btnCancel != null) {
                var btnNo = frame.contentWindow.document.getElementById(btnCancel);
                if (btnNo) {
                    if (btnNo.attachEvent) {
                        btnNo.attachEvent("onclick", function() {
                            Dialog.Close();
                        });
                    }
                    else {
                        btnNo.addEventListener("click", function() {
                            top.Dialog.Close();
                        }, false);
                    }
                }
            }
        }

    }
    var doCallback = function() {
        var returnvalue = frame.contentWindow.returnValue;
        if (fCallback != null) {
            if (typeof (returnvalue) == "undefined") {
                return false;
            }
            var arg = new Array();
            arg[0] = returnvalue;
            if (aCallbackArguments != null) {
                for (j = 0; j < aCallbackArguments.length; j++) {
                    arg[j + 1] = aCallbackArguments[j];
                }
            }
            if (fCallback.apply(openModalDialog, arg) != false) {
                //Dialog.Close();
            }
        }
        else {
            if (typeof (returnvalue) != "undefined") {
                //top.Dialog.Close();
            }
        }
    }
}

//打开confirm窗口
function openConfirmDialog(paraDict) {
    var buttons;
    var contents = "";
    var icon;
    var size = {};
    var tsize = "";
    for (var paraName in paraDict) {
        if (paraName == "bottons") {
            buttons = paraDict[paraName];
        }
        else if (paraName == "contents") {
            contents = paraDict[paraName] || "";
        }
        else if (paraName == "icon") {
            icon = paraDict[paraName] || "";
        }
        else if (paraName == "size") {
            tsize = paraDict[paraName] || "";
        }
    }
    var width = tsize.width || 370;
    var height = tsize.height || 100;
    size = { width: width, height: height };

    Dialog.Installize("", "", size, "", "", "", "", 'Confirm');
    //添加提示内容
    var contentTB = "<table border='0' cellspacing='0' cellpadding='0' class='" + _getIconClass(icon) + "'>";
    contentTB += "<tr>";
    contentTB += "<td class='icon'>&nbsp;</td>";
    contentTB += "<td>" + contents + "</td>";
    contentTB += "</tr>";
    contentTB += "</table>";
    top.document.getElementById("confirmDialogContent").innerHTML = contentTB;

    //添加按钮
    top.document.getElementById("confirmDialogbtn").innerHTML = "";
    var icount = 0;
    for (var btnName in buttons) {
        if (btnName == "detachEvent") continue;
        var btn = top.document.createElement("input");
        btn.setAttribute("type", "button");
        if (icount == 0) {
            btn.setAttribute("data-type", "active");
        }
        icount++;
        btn.setAttribute("class", "c6ui-button");
        btn.value = "  " + btnName + "  ";

        if (btn.attachEvent) {
            if (buttons[btnName] != null) {
                btn.attachEvent("onclick", buttons[btnName]);
            }
            btn.attachEvent("onclick", function() { Dialog.Close(); });
        }
        else {
            if (buttons[btnName] != null) {
                btn.addEventListener("click", buttons[btnName], false);
            }
            btn.addEventListener("click", function() { Dialog.Close(); }, false);
        }
        top.document.getElementById("confirmDialogbtn").appendChild(btn);
        top.zwxJQ(btn).buttonround();
    }
}

//打开alert窗口
function openAlertDialog(content, icon, func, size) {
    if (size == null) {
        size = { width: 370, height: 100 };
    }
    top.Dialog.Installize("", "", size, "", "", "", "", 'Alert');
    //添加提示内容
    var contentTB = "<table border='0' cellspacing='0' cellpadding='0' class='" + _getIconClass(icon) + "'>";
    contentTB += "<tr>";
    contentTB += "<td class='icon'>&nbsp;</td>";
    contentTB += "<td>" + content + "</td>";
    contentTB += "</tr>";
    contentTB += "</table>";
    top.document.getElementById("alertDialogContent").innerHTML = contentTB;

    //添加按钮
    top.document.getElementById("alertDialogbtn").innerHTML = "";
    var btn = top.document.createElement("input");
    btn.setAttribute("type", "button");
    btn.setAttribute("data-type", "active");
    btn.setAttribute("class", "c6ui-button");
    btn.value = "  确定  ";

    if (btn.attachEvent) {
        if (func != null) {
            btn.attachEvent("onclick", func);
        }
        btn.attachEvent("onclick", function() { top.Dialog.Close(); });
    }
    else {
        if (func != null) {
            btn.addEventListener("click", func, false);
        }
        btn.addEventListener("click", function() { top.Dialog.Close(); }, false);
    }
    top.document.getElementById("alertDialogbtn").appendChild(btn);
    top.zwxJQ(btn).buttonround();
    zwxJQ(btn).focus();
}

//打开Prompt窗口
function openPromptDialog(title, func) {
    var size = { width: 400, height: 100 };
    top.Dialog.Installize("", "", size, "", "", "", "", 'Prompt');
    //添加提示内容
    var contentTB = "<table border='0' cellspacing='0' cellpadding='0' class='c6ui-alertBox'>";
    contentTB += "<tr>";
    contentTB += "<td>" + title + ":</td>";
    contentTB += "<td><input style='width:100%' id='_PromptText' type='text' /></td>";
    contentTB += "</tr>";
    contentTB += "</table>";
    top.document.getElementById("promptDialogContent").innerHTML = contentTB;

    //添加按钮
    top.document.getElementById("promptDialogbtn").innerHTML = "";
    var btn = top.document.createElement("input");
    btn.setAttribute("type", "button");
    btn.setAttribute("data-type", "active");
    btn.setAttribute("class", "c6ui-button");
    btn.value = "  确定  ";

    if (btn.attachEvent) {
        btn.attachEvent("onclick", function() {
            var arg = new Array();
            arg[0] = top.document.getElementById("_PromptText").value;
            if (func.apply(openPromptDialog, arg) != false) {
                top.Dialog.Close();
            }
        });
    }
    else {
        btn.addEventListener("click", function() {
            var arg = new Array();
            arg[0] = top.document.getElementById("_PromptText").value;
            if (func.apply(openPromptDialog, arg) != false) {
                top.Dialog.Close();
            }
        }, false);
    }
    top.document.getElementById("promptDialogbtn").appendChild(btn);
    top.zwxJQ(btn).buttonround();
}

function _getIconClass(icon) {
    if (icon == "alert") {
        return "c6ui-alertBox";
    } else if (icon == "error") {
        return "c6ui-errBox";
    } else if (icon == "help") {
        return "c6ui-helpBox";
    } else if (icon == "ok") {
        return "c6ui-okBox";
    } else if (icon == "info") {
        return "c6ui-infoBox";
    }
    else {
        return "c6ui-alertBox";
    }
}

