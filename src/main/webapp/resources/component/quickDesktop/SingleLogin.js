/**
 * 个人桌面内的弹出桌面内容加载
 */
var isConfirm = true;
//页面加载
zwxJQ(document).ready(function() {
	//InitMenuMethod();
    InitMenuList();
    //InitEvent();
});
//初始化菜单列表
function InitMenuList() {
	var req = new XMLHttpRequest();
    req.onreadystatechange = function(){
  	  if (req.readyState == 4&&req.status == 200) { 
  	 var dStrs =  req.responseText.split("TAGIDXS:"); 
  	document.getElementById("addTagIdxs").value=dStrs[1].split("#")[0];
	document.getElementById("delTagIdxs").value=dStrs[1].split("#")[1];
	document.getElementById("addTagIdxs_souc").value=dStrs[1].split("#")[0];
	document.getElementById("delTagIdxs_souc").value=dStrs[1].split("#")[1];
                    var iconMainList = [];
                    var iconMinorList = [];
                  //格式：[主菜单1,:,菜单rid_子菜单1,_,状态,_,图片名称,#,菜单rid,_,子菜单2,_,状态,_,图片名称,#,...]
                    var datas= dStrs[0].split("@#@");
                    if(null!=datas&&datas.length>0){
                    iconMainList.push('<div id="_fa1" class="iconList">');
                    iconMainList.push('<h1>');
                    iconMainList.push('已维护的系统');
                    iconMainList.push('</h1>');
                    iconMainList.push('<ul class="iconElem">');
                    
                    	
                        var dataObj=datas[0].substring(0,datas[0].length);
                        var dataObjs=dataObj.split(",_,");
                        for(var k=0;k<dataObjs.length-1;k++){
                        	
                                var dataObjs2=dataObjs[k];
                                var subDataObjs=dataObjs2.split(",");
                                if(null!=subDataObjs&&subDataObjs.length>0){
        	                            var id = subDataObjs[0];
        	                            var name =subDataObjs[1];
        	                            var image = subDataObjs[2];
        	                            var defaultImg = "/resources/component/quickDesktop/image/shortcut-menu.png";
        	                            if (image == "" || image == null) {
        	                                image = defaultImg;
        	                            }
        	                            else {
        	                                image = "/webFile"+image;
        	                            }
        	                            var isAdd = "0";
        	                            var btnClass = isAdd == "0" ? "addBtn" : "delBtn";
        	                            var btnName = isAdd == "0" ? addBtnName : delBtnNaem;
        	                            iconMainList.push('<li style="margin-right:0px" id="' + id + '" >');
        	                            
        	                            iconMainList.push('<div class="iconImg">');
        	                            iconMainList.push('<img src="' + image + '" onerror="this.src=\'' + image + '\'"/>');
        	                            iconMainList.push('</div>');
        	                            iconMainList.push('<div class="iconMsg">');
        	                            iconMainList.push('<h2 title="' + name + '">');
        	                            iconMainList.push(CheckLength(name, 12));
        	                            iconMainList.push('</h2>');
//        	                            iconMainList.push('<a href="javascript:void(0)" class="' + btnClass + '">');
//        	                            iconMainList.push(btnName);
//        	                            iconMainList.push('</a>');
        	                            iconMainList.push('<a href="javascript:void(1)" onclick="openDialog('+id+',1)" class="mdfBtn">');
        	                            iconMainList.push(mdfBtn);
        	                            iconMainList.push('</a>');
        	                            iconMainList.push('</div>');
        	                            iconMainList.push('</li>');
        	                        
        	                    }  
                        }
                        
	                        iconMainList.push('</ul>');
	                        iconMainList.push('</div>');
                    
		                    iconMainList.push('<div id="_fa2" class="iconList">');
		                    iconMainList.push('<h1>');
		                    iconMainList.push('未维护的系统');
		                    iconMainList.push('</h1>');
		                    iconMainList.push('<ul class="iconElem">');
		                    
		                    var dataObj=datas[1].substring(0,datas[1].length);
	                        var dataObjs=dataObj.split(",_,");
	                        for(var k=0;k<dataObjs.length-1;k++){
	                        	
	                                var dataObjs2=dataObjs[k];
	                                var subDataObjs=dataObjs2.split(",");
	                                if(null!=subDataObjs&&subDataObjs.length>0){
	        	                            var id = subDataObjs[0];
	        	                            var name =subDataObjs[1];
	        	                            var image = subDataObjs[2];
	        	                            var defaultImg = "/resources/component/quickDesktop/image/shortcut-menu.png";
	        	                            if (image == "" || image == null) {
	        	                                image = defaultImg;
	        	                            }
	        	                            else {
	        	                                image = "/webFile"+image;
	        	                            }
	        	                            iconMainList.push('<li style="margin-right:0px" id="' + id + '" >');
	        	                            
	        	                            iconMainList.push('<div class="iconImg">');
	        	                            iconMainList.push('<img src="' + image + '" onerror="this.src=\'' + image + '\'"/>');
	        	                            iconMainList.push('</div>');
	        	                            iconMainList.push('<div class="iconMsg">');
	        	                            iconMainList.push('<h2 title="' + name + '">');
	        	                            iconMainList.push(CheckLength(name, 12));
	        	                            iconMainList.push('</h2>');
	        	                            iconMainList.push('<a href="javascript:void(1)" onclick="openDialog('+id+',2)" class="mdfBtn">');
	        	                            iconMainList.push(whBtn);
	        	                            iconMainList.push('</a>');
	        	                            iconMainList.push('</div>');
	        	                            iconMainList.push('</li>');
	        	                        
	        	                    }  
	                        }
	                        
		                        iconMainList.push('</ul>');
		                        iconMainList.push('</div>');
                    	
                    }
                    
                    zwxJQ("#iconMain").html(iconMainList.join(""));
         AddScrollBar();
  	 }
    };
    try {
    	req.open("POST", "newSingleLoginDeskAjax?flag=InitMenuList"); //was get
    } catch (e) {
      alert("Problem Communicating with Server\n"+e);
    }
    req.send(null);
}
//添加自定义滚动条
function AddScrollBar() {

    zwxJQ("#iconMain").minScrollbar({
        hScroll: false,
        vScroll: true,
        width: '7px',
        viewWidth: '800px',
        viewHeight: '440px',
        bgColor: '#333'
    });
}
//初始化事件
function InitEvent() {
	 
    //菜单添加按钮单击事件
    zwxJQ(".addBtn").live("click", function() {
        var menuId = zwxJQ(this).parents("li").attr("id");
       // AddMenuCheck(menuId);
        var addTags=document.getElementById("addTagIdxs").value;
        var delTags=document.getElementById("delTagIdxs").value;
        if(addTags.indexOf(","+menuId+",")==-1){
            document.getElementById("addTagIdxs").value=addTags+","+menuId+",";
        }
        document.getElementById("delTagIdxs").value=delTags.replace(","+menuId+",","");
        
        
        zwxJQ("li#" + menuId).find(".addBtn").removeClass("addBtn").addClass("delBtn").text(delBtnNaem);
    });
    //菜单删除按钮单击事件
    zwxJQ(".delBtn").live("click", function() {
        var menuId = zwxJQ(this).parents("li").attr("id");
        //DelMenu(menuId);
        var addTags=document.getElementById("addTagIdxs").value;
        var delTags=document.getElementById("delTagIdxs").value;

        document.getElementById("addTagIdxs").value=addTags.replace(","+menuId+",","");
        if(delTags.indexOf(","+menuId+",")==-1){
        document.getElementById("delTagIdxs").value=delTags+","+menuId+",";
        }
        zwxJQ("li#" + menuId).find(".delBtn").removeClass("delBtn").addClass("addBtn").text(addBtnName);
    });
    
}
//添加菜单前判断
function AddMenuCheck(menuId) {
	var isAddDesktop = "0";
    if (toDesktopNo != 1 && objParam.desktopMenuCountMap[toDesktopNo] == 0) {
        isAddDesktop = 1;
    }
    if (objParam.desktopMenuCountMap[toDesktopNo] == maxMenuNum) {
        for (var key in objParam.desktopMenuCountMap) {
            if (key > toDesktopNo && objParam.desktopMenuCountMap[key] < maxMenuNum) {
                toDesktopNo = key;
                flag = true;
                break;
            }
        }
    } else {
        flag = true;
    }
    if (flag) {
        var isAddDesktop = "0";
        if (toDesktopNo != 1 && objParam.desktopMenuCountMap[toDesktopNo] == 0) {
            isAddDesktop = 1;
        }
        if (isConfirm && objParam.desktopMenuCountMap[objParam.curDeskTopNo] == maxMenuNum) {
        	AddMenu(menuId, isAddDesktop);
        }
        else {
            AddMenu(menuId, isAddDesktop);
        }
    } else {
        alert(dmaf);
    }
}
//添加菜单
function AddMenu(menuId, isAddDesktop) {
    var curDeskTopNo = isAddDesktop == "1" ? objParam.curDeskTopNo : toDesktopNo;
    zwxJQ.post("singleLoginIn.faces"
        , { flag: "AddMenu", isAddDesktop: isAddDesktop, curDeskTopNo: curDeskTopNo, menuId: menuId }
        , function(opRet) {
            if (opRet) {
                if (isAddDesktop == "1") {
                    objParam.desktopMenuCountMap[toDesktopNo] = 1;
                } else {
                    objParam.desktopMenuCountMap[toDesktopNo]++;
                }
                zwxJQ("li#" + menuId).find(".addBtn").removeClass("addBtn").addClass("delBtn").text(delBtnNaem);
            }
        });
}
//删除菜单
function DelMenu(menuId) {
    zwxJQ.post("singleLoginIn.faces"
        , { flag: "DelMenu", menuId: menuId }
        , function(opRet) {
            if (opRet) {
                opRet = parseInt(opRet);
                if (opRet) {
                    objParam.desktopMenuCountMap[opRet]--;
                    zwxJQ("li#" + menuId).find(".delBtn").removeClass("delBtn").addClass("addBtn").text(addBtnName);
                }
            }
        });
}


