/**
 * 悬浮菜单
 * */
if (language == null || language == "undefined") {
    var language = "zh-cn";
}
var PersonalSetting;
var Setting;
var RecentlyVisit;
var AddRecentlyVisit;
var RemoveRecentlyVisit;
var Loading;
var isShow = false;
var unitName = null;
var userName = null;

switch (language.toLowerCase()) {
    case "zh-cn":
        PersonalSetting = "个人设置";
        Setting = "设置";
        RecentlyVisit = "最近访问链接";
        AddRecentlyVisit = "将此链接固定到最近使用功能";
        RemoveRecentlyVisit = "在最近使用功能中将此链接取消固定";
        Loading = "正在加载...";
        break;
}
document.write("<script type='text/javascript' src='/resources/js/watermark.js'></script>");
zwxJQ(document).ready(function() {
    var req = new XMLHttpRequest();
    req.onreadystatechange = function(){
        if(req.readyState != 4 || req.status != 200){
            return;
        }
        var text = req.responseText;
        if (undefined == text || text.split(",_,").length <=1) {
            return;
        }
        unitName = text.split(",_,")[0];
        userName = text.split(",_,")[1];
        if(userName != null){
            watermark({watermark_userName:unitName,
                watermark_system:userName,
                watermark_date:getDate(),
            });
        }
    };
    try {
        req.open("POST", encryptUrl("/newShortcutDesktopAjax?flag=SystemName"), false); //was get
        //req.open("POST", encryptUrl(location.origin+"/newShortcutDesktopAjax?flag=SystemName"), false); //was get
    } catch (e) {
        alert("Problem Communicating with Server\n"+e);
    }
    req.send(null);
});
var MenuXML = null;    //暂存菜单XML
var Menu1 = null;    //暂存菜单1
var Menu2 = null;    //暂存菜单1
var Menu3 = null;    //暂存菜单1
var win_PopMenu = {
    Llen: 0,          //一级菜单项数
    Rlen: 0,         //二级菜单项数
    Lheight: 0,    //一级菜单最大高度
    Rheight: 0,    //二级菜单最大高度
    //显示菜单
    ShowMenu: function(obj) {
        //菜单显示时隐藏
        if (win_MainMenu.isShow) {
            win_MainMenu.hideanimate();
            return false;
        }
        zwxJQ(win_MainMenu.document.body).css({ "overflow": "hidden", "background-color": "#FFFFFF", "border": "0px" });
        var oDiv = win_MainMenu.document.getElementsByTagName("DIV");
        var sHTML =  "<div class='c6ui-menu' id='c6ui-menu' onclick='StopBubble(getEvent());'>";

        sHTML += "<table  cellpadding='0' cellspacing='0' border='0' width='100%'>";

        sHTML += "<tbody>";
        sHTML += "<tr class='c6ui-menu-top'><td class='c6ui-menu-top-left'></td><td class='c6ui-menu-top-center'></td><td class='c6ui-menu-top-right'></td></tr>";
        sHTML += "<tr><td class='c6ui-menu-left'></td><td class='c6ui-menu-content'>";
        sHTML += "<div id='c6ui-menu-content-left' class='c6ui-menu-content-left' onmousewheel='win_PopMenu.DoMouseWheel(this)'>";
        //取一级菜单
        var keyhidd=this.ShowFirstMenu();
        sHTML += keyhidd;
        sHTML += "</div>";

        sHTML += "<div id='c6ui-menu-content-right' class='c6ui-menu-content-right-history'  onmousewheel='win_PopMenu.DoMouseWheel(this)'>";
        //取访问记录
        sHTML += this.GetVisted();
        sHTML += "</div>";
        sHTML += "</td><td class='c6ui-menu-right'></td></tr>";
        sHTML += " <tr class='c6ui-menu-bottom'><td class='c6ui-menu-bottom-left'></td><td style='text-align:right'>";
        /*if (isShow) {
            sHTML += "<span class=\"e_btn\" id=\"menusetting_btn\">";
            sHTML += "<span class=\"e_con\">";
            sHTML += "<input class=\"c6ui-button btn\" id=\"menusetting\" style=\"margin-bottom: -2px;\" " +
                "onclick=\"top.CreateNewTabWin('/webapp/oa/empManageList.faces', '我的档案','m03');win_MainMenu.hideanimate();\"" +
                " type=\"button\" data-img=\"/images/16px/setting.png\" value=\"我的档案\"/>";
            sHTML += "</span></span>";
        }
        sHTML += "<span class=\"e_btn\" id=\"menusetting_btn\">";
        sHTML += "<span class=\"e_con\">";
        sHTML += "<input class=\"c6ui-button btn\" id=\"menusetting\" style=\"margin-bottom: -2px;\" " +
            "onclick=\"top.CreateNewTabWin('/webapp/portal/portalMgr4PersonEdit.faces', '个人门户布局','m03');win_MainMenu.hideanimate();\"" +
            " type=\"button\" data-img=\"/images/16px/setting.png\" value=\"个人门户布局\"/>";
        sHTML += "</span></span>";
*/

        sHTML += "</td><td class='c6ui-menu-bottom-right'></td></tr>";
        sHTML += "</tbody>";
        sHTML += "</table>";
        sHTML += "</div>";

        if(keyhidd.indexOf("SESSIONOUT")!=-1){
            forwardLoutBtn();
        }else {
            win_MainMenu.document.body.innerHTML = sHTML;
            var event = window.event || getEvent();
            var ev = obj || event.srcElement || event.target;
            win_MainMenu.showanimate(0, 21, 412, 490, ev);
            //隐藏滑动按钮
            if (this.Llen * 28 < 448) {
                zwxJQ('#c6ui-menu-roll-left-top').hide();
                zwxJQ('#c6ui-menu-roll-left-bottom').hide();
            }
            else {
                zwxJQ('#c6ui-menu-roll-left-top').hide();
                zwxJQ('#c6ui-menu-roll-left-bottom').show();
            }
        }
    },
    //显示一级菜单
    ShowFirstMenu: function() {
        var sHTML = "<div id='c6ui-menu-roll-left-top' class='c6ui-menu-roll-top' onclick='win_PopMenu.SlipClickFun(this);StopBubble(event);'><span class='c6ui-roll-top'></span></div>";
        sHTML += "<ul>";
        var keyhidd = "";
        if (Menu1 == null) {
            keyhidd = this.GetMenu1();
        }
        else {
            keyhidd = Menu1;
        }
        var classname = "";
        this.Llen = 0;
        //取一级菜单
        var keyhidds= keyhidd.split("@#@");
        if(undefined!=keyhidds&&keyhidds.length>0){
            for(var i=0;i<keyhidds.length;i++){

                var defimg="/resources/component/quickDesktop/image/default.png";
                var img = keyhidds[i].split(",_,")[2];
                if(undefined==img||null==img||""==img.trim()){
                    img=defimg;
                }else{
                    img="/resources/component/quickDesktop/image/16px/"+img;
                }
                //图片地图，对于设置有地图的进行处理
                var fullname = name;
                var title = "";
                var keyvs= keyhidds[i].split(",_,")[0];
                var namevs= keyhidds[i].split(",_,")[1];
                sHTML += "<li onclick=\"win_PopMenu.ShowSecondMenu('" + keyvs + "','" + namevs + "',this);StopBubble(getEvent());\">";
                sHTML += "<img src='" + img + "' />";
                sHTML += "<a>" + namevs + "</a><span class='ui-menu-icon-arrow'></span></li>";
                win_PopMenu.Llen = win_PopMenu.Llen + 1;
            }

        }

        sHTML += "</ul>";
        sHTML += "<div id='c6ui-menu-roll-left-bottom' class='c6ui-menu-roll-bottom' onclick='win_PopMenu.SlipClickFun(this);StopBubble(event);'><span class='c6ui-roll-bottom'></span></div>";

        this.Lheight = this.Llen * 28;
        return sHTML;

    },
    //显示二级菜单
    ShowSecondMenu: function(id, name, evt) {
        //选中菜单
        zwxJQ("#c6ui-menu-content-left a").attr("class", "");
        zwxJQ(evt).find("a").attr("class", "currentList");

        if (win_SubMenu.isShow) win_SubMenu.hide();
        var tree = "<div id='c6ui-menu-roll-right-top' class='c6ui-menu-roll-top' onclick='win_PopMenu.SlipClickFun(this);StopBubble(event);'><span class='c6ui-roll-top'></span></div>";
        tree += "<ul>";
        tree += "<li><span class='ui-list-driver'>" + name + "</span></li>";
        tree += "<li class='ui-list-line'></li>";
        this.Rlen = 0;
        //获取二级菜单
        var keyhidd = "";
        if (Menu2 == null) {
            keyhidd = this.GetMenu2();
        }
        else {
            keyhidd = Menu2;
        }
        if(keyhidd.indexOf("SESSIONOUT")!=-1){
            forwardLoutBtn();
            return;
        }else {
            var valhidds = keyhidd.split("@#@");
            if (undefined != valhidds && valhidds.length > 0) {
                for (var v = 0; v < valhidds.length; v++) {
                    var valobj = valhidds[v];
                    var idobj=valobj.split(",_,")[0];

                    if(idobj!=""&&idobj==id){
                        //包含记录子菜单
                        var subval = valobj.split(",_,")[1];
                        var urlval = valobj.split(",_,")[2];
                        var defimg = "/resources/component/quickDesktop/image/default.png";//默认图片
                        var img = valobj.split(",_,")[3];//获取数据库图片
                        var subid = valobj.split(",_,")[4];
                        var ifPop = valobj.split(",_,")[7];
                        if (undefined == img || null == img || "" == img.trim()) {
                            img = defimg;
                        } else {
                            img = "/resources/component/quickDesktop/image/16px/" + img;
                        }
                        //图片地图，对于设置有地图的进行处理
                        if (urlval == '#') {
                            //包含三级菜单
                            tree += "<li onclick=\"win_PopMenu.ShowThirdMenu('" + subid + "',this);StopBubble(getEvent());\">";
                            tree += "<img src='" + img + "' />";
                            tree += "<a>" + subval + "</a><span class='ui-menu-icon-arrow'></span></li>";
                        } else {
                            if(ifPop != '1') {
                                tree += "<li onclick=\"menuclick('" + subval + "','" + subval + "','" + urlval + "','" + valobj.split(",_,")[6] + "');";
                            } else {
                                tree += "<li onclick=\"openMenuBlank('" + urlval + "');";
                            }
                            tree += "StopBubble(getEvent());";
                            tree += "win_PopMenu.SetVistedNew('" + subval + "','" + subval + "','" + urlval + "','" + valobj.split(",_,")[6] +"','"+ifPop+"');\">";
                            tree += "<img src='" + img + "' />";
                            tree += "<a>" + subval + "</a></li>";
                        }
                        win_PopMenu.Rlen = win_PopMenu.Rlen + 1;
                    }
                }
            }
            this.Rheight = this.Rlen * 28;
            tree += "</ul>";
            tree += "<div id='c6ui-menu-roll-right-bottom' class='c6ui-menu-roll-bottom' onclick='win_PopMenu.SlipClickFun(this);StopBubble(event);'><span class='c6ui-roll-bottom'></span></div>";

            zwxJQ("#c6ui-menu-content-right").html(clearBr(tree));
            zwxJQ("#c6ui-menu-content-right").attr("class", "c6ui-menu-content-right-list");
            //隐藏滑动按钮
            if (this.Rlen * 28 < 420) {
                zwxJQ('#c6ui-menu-roll-right-top').hide();
                zwxJQ('#c6ui-menu-roll-right-bottom').hide();
            }
            else {
                zwxJQ('#c6ui-menu-roll-right-top').hide();
                zwxJQ('#c6ui-menu-roll-right-bottom').show();
            }
        }

    },
    //显示三级菜单
    ShowThirdMenu: function(id, evt) {
        //获取三级菜单
        var keyhidd = "";
        if (Menu3 == null) {
            keyhidd = this.GetMenu3();
        }
        else {
            keyhidd = Menu3;
        }
        if(keyhidd.indexOf("SESSIONOUT")!=-1){
            forwardLoutBtn();
            return;
        }else {
            var valhidds = keyhidd.split("@#@");
            //选中菜单
            zwxJQ("#c6ui-menu-content-right a").attr("class", "");
            zwxJQ(evt).find("a").attr("class", "currentList");

            //添加内容
            zwxJQ(win_SubMenu.document.body).css({ "overflow": "hidden", "background-color": "#FFFFFF", "border": "1px solid #9aaec1" });
            var oDiv = win_SubMenu.document.getElementsByTagName("DIV");
            var sHTML = "";
            var strxml = "";
            var classname = "";
            var rowcount = 0; //记录行数
            //取三级菜单
            if (null != valhidds && valhidds.length > 0) {
                for (var v = 0; v < valhidds.length; v++) {
                    var valobj = valhidds[v];
                    var idobj=valobj.split(",_,")[0];
                    if(idobj!=""&&idobj==id){
                        //包含记录子菜单
                        var subval = valobj.split(",_,")[1];
                        var urlval = valobj.split(",_,")[2];

                        var defimg = "/resources/component/quickDesktop/image/default.png";
                        var img = valobj.split(",_,")[3];
                        if (undefined == img || null == img || "" == img.trim()) {
                            img = defimg;
                        } else {
                            img = "/resources/component/quickDesktop/image/16px/" + img;
                        }
                        var ifPop = valobj.split(",_,")[6];
                        if(ifPop != '1') {
                            sHTML += "<li  id='" + subval + "' onclick=\"menuclick('" + subval + "','" + subval + "','" + urlval + "','" + valobj.split(",_,")[5]+"');"
                        } else {
                            sHTML += "<li id='"+subval+"' onclick=\"openMenuBlank('" + urlval + "');";
                        }
                        sHTML += "win_PopMenu.SetVisted('" + subval + "','" + subval + "','" + urlval + "','" + valobj.split(",_,")[5] + "');\">" +
                            "<img src='" + img + "' /><a>" + subval + "</a></li>";
                        rowcount++;
                    }
                }
            }
            sHTML += "</ul>";
            if (rowcount > 0) {
                var theight = rowcount * 28;
                //判断弹出菜单是否会超出屏幕范围
                var coordinates = { x: 0, y: 0 };
                oElement = evt;
                while (oElement) {
                    coordinates.x += oElement.offsetLeft;
                    coordinates.y += oElement.offsetTop;
                    oElement = oElement.offsetParent;
                }
                var winheight = zwxJQ(window).height();
                var temp = winheight - coordinates.y;

                if (theight < temp) {
                    sHTML = "<div class='c6ui-base-poplist' style='width:200px;' onclick='StopBubble(event);'><ul>" + sHTML + "</ul></div>";
                }
                else {
                    theight = temp - 10;
                    sHTML = "<div class='c6ui-base-poplist' style='width:200px;overflow:auto;' onclick='StopBubble(event);'><ul>" + sHTML + "</ul></div>";
                }

                win_SubMenu.document.body.innerHTML = clearBr(sHTML);
                win_SubMenu.show(235, 0, 200, theight, evt);
            }
        }
    },
    SlipClickFun: function(evt) {
        if (evt.id == "c6ui-menu-roll-left-top") {//一级菜单向上滚动
            this.SlipContent(evt, 28);
        }
        if (evt.id == "c6ui-menu-roll-left-bottom") {//一级菜单向下滚动
            this.SlipContent(evt, -28);
        }
        if (evt.id == "c6ui-menu-roll-right-top") { //二级菜单向上滚动
            this.SlipContent(evt, 28);
        }
        if (evt.id == "c6ui-menu-roll-right-bottom") {//二级菜单向下滚动
            this.SlipContent(evt, -28);
        }
    },
    //列表滑动
    SlipContent: function(evt, direction) {
        var ul = zwxJQ(evt.parentNode).find("ul");
        var itop = ul[0].style.top;
        if (itop == null || itop == "") itop = 0;
        itop = parseInt(itop) + direction;
        if (evt.id == "c6ui-menu-roll-left-top" || evt.id == "c6ui-menu-roll-left-bottom") {
            this.Lheight = this.Lheight + direction;
        }
        if (evt.id == "c6ui-menu-roll-right-top" || evt.id == "c6ui-menu-roll-right-bottom") {
            this.Rheight = this.Rheight + direction;
        }
        //ul.animate({ top: itop + 'px' }, "fast");
        ul.css({ "top": itop + "px" });
        this.DoSlipCallBack(evt);
    },
    DoSlipCallBack: function(evt) {
        if (evt.id == "c6ui-menu-roll-right-top" || evt.id == "c6ui-menu-roll-right-bottom") {
            if (this.Rheight == 392) {
                zwxJQ('#c6ui-menu-roll-right-top').show();
                zwxJQ('#c6ui-menu-roll-right-bottom').hide();
            }
            else if (this.Rheight == this.Rlen * 28) {
                zwxJQ('#c6ui-menu-roll-right-top').hide();
                zwxJQ('#c6ui-menu-roll-right-bottom').show();
            }
            else {
                zwxJQ('#c6ui-menu-roll-right-top').show();
                zwxJQ('#c6ui-menu-roll-right-bottom').show();
            }
        }
        if (evt.id == "c6ui-menu-roll-left-top" || evt.id == "c6ui-menu-roll-left-bottom") {
            if (this.Lheight == 420) {
                zwxJQ('#c6ui-menu-roll-left-top').show();
                zwxJQ('#c6ui-menu-roll-left-bottom').hide();
            }
            else if (this.Lheight == this.Llen * 28) {
                zwxJQ('#c6ui-menu-roll-left-top').hide();
                zwxJQ('#c6ui-menu-roll-left-bottom').show();
            }
            else {
                zwxJQ('#c6ui-menu-roll-left-top').show();
                zwxJQ('#c6ui-menu-roll-left-bottom').show();
            }
        }
    },
    DoMouseWheel: function(evt) {
        //  var event = getEvent();
        var event = window.event || getEvent();
        var direction = event.wheelDelta || event.detail; //滚动方向，向上为正，向下为负
        if (evt.id == "c6ui-menu-content-left") {
            if (this.Llen * 28 <= 420) return;
            if (direction > 0) {
                if (zwxJQ(evt).find("#c6ui-menu-roll-left-top")[0].style.display == "none") return;
                this.SlipClickFun(zwxJQ(evt).find("#c6ui-menu-roll-left-top")[0]);
            }
            else {
                if (zwxJQ(evt).find("#c6ui-menu-roll-left-bottom")[0].style.display == "none") return;
                this.SlipClickFun(zwxJQ(evt).find("#c6ui-menu-roll-left-bottom")[0]);
            }
        }
        if (evt.id == "c6ui-menu-content-right") {
            if (this.Rlen * 28 <= 392) return;
            if (direction > 0) {
                if (zwxJQ(evt).find("#c6ui-menu-roll-right-top")[0].style.display == "none") return;
                this.SlipClickFun(zwxJQ(evt).find("#c6ui-menu-roll-right-top")[0]);
            }
            else {
                if (zwxJQ(evt).find("#c6ui-menu-roll-right-bottom")[0].style.display == "none") return;
                this.SlipClickFun(zwxJQ(evt).find("#c6ui-menu-roll-right-bottom")[0]);
            }
        }
    },
    //取菜单的XML文档
    GetMenuXML: function() {
        var xmldom;
        MenuXML = xmldom;
        return xmldom;
    },
    //取1级菜单的文档
    GetMenu1: function() {
        var xmldom;
        var req = new XMLHttpRequest();
        req.onreadystatechange = function(){
            if (req.readyState == 4&&req.status == 200) {
                xmldom =  req.responseText;
                Menu1=xmldom;
            }
        };
        try {
            req.open("POST", encryptUrl("newShortcutDesktopAjax?flag=ShowFirstMenu"), false); //was get
        } catch (e) {
            alert("Problem Communicating with Server\n"+e);
        }
        req.send(null);

        return xmldom;
    },
    //取2级菜单的文档
    GetMenu2: function() {
        var xmldom;
        var req = new XMLHttpRequest();
        req.onreadystatechange = function(){
            if (req.readyState == 4&&req.status == 200) {
                xmldom =  req.responseText;
                Menu2=xmldom;
            }
        };
        try {
            req.open("POST", encryptUrl("newShortcutDesktopAjax?flag=ShowSecondMenu"), false); //was get
        } catch (e) {
            alert("Problem Communicating with Server\n"+e);
        }
        req.send(null);
        return xmldom;
    },
    //取3级菜单的文档
    GetMenu3: function() {
        var xmldom;
        var req = new XMLHttpRequest();
        req.onreadystatechange = function(){
            if (req.readyState == 4&&req.status == 200) {
                xmldom =  req.responseText;
                Menu3=xmldom;
            }
        };
        try {
            req.open("POST", encryptUrl("newShortcutDesktopAjax?flag=ShowThirdMenu"), false); //was get
        } catch (e) {
            alert("Problem Communicating with Server\n"+e);
        }
        req.send(null);
        return xmldom;
    },
    //记录访问痕迹
    SetVistedNew: function(type, name, url, id,ifPop) {
        try {
            var strXML = GetLocalUrlList("JHC6Visited");
            if (strXML == null || strXML == "" || strXML == "null") {
                strXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?><root></root>";
            }
            strXML = strXML.replace("__", "");
            var xmlObj = stringtoxml(strXML);
            var root;
            var temproot = zwxJQ(xmlObj).find("root>item[userid='" + location.hostname + userid + "']");

            if (temproot.length > 0)
                root = temproot[0];
            if (root == null || xmltostring(root) == "") {
                root = xmlObj.createElement("item");
                var a = xmlObj.createAttribute("userid");
                a.value = location.hostname + userid;
                root.setAttributeNode(a);
                xmlObj.documentElement.appendChild(root);
            }

            var Num = 10; //最近访问记录总数
            var oElement = xmlObj.createElement("navigation");
            var atName = xmlObj.createAttribute("name");
            atName.value = name;
            oElement.setAttributeNode(atName);
            var atURL = xmlObj.createAttribute("URL");
            atURL.value = url;
            oElement.setAttributeNode(atURL);
            var atLock = xmlObj.createAttribute("Lock");
            atLock.value = "0";
            oElement.setAttributeNode(atLock);

            var atType = xmlObj.createAttribute("Type");
            atType.value = type;
            oElement.setAttributeNode(atType);

            var atID = xmlObj.createAttribute("Id");
            atID.value = id;
            oElement.setAttributeNode(atID);

            if (root.childNodes.length == 0)
                root.appendChild(oElement);
            else
                root.insertBefore(oElement, root.firstChild);

            var ifPopVal = xmlObj.createAttribute("ifPopVal");
            ifPopVal.value = ifPop;
            oElement.setAttributeNode(ifPopVal);
            var lock = 0; //新加的访问地址以前是否被钉住
            for (i = 1; i < root.childNodes.length; i++) {
                var oElement = root.childNodes[i];
                if (url.toLowerCase() == oElement.attributes[1].value.toLowerCase()) {
                    if (oElement.attributes[2].value == "1") {
                        lock = "1";
                    }
                    root.removeChild(oElement);
                }
                if (i == Num) {
                    if (oElement.attributes[2].value == "1") {
                        for (j = i - 1; j >= 0; j--) {
                            if (root.childNodes[j].attributes[2].value == "0") {
                                root.removeChild(root.childNodes[j]);
                                break;
                            }
                        }
                    }
                    else {
                        try {
                            root.removeChild(oElement);
                        } catch (e) { }
                    }
                }
                if (i > Num) {
                    root.removeChild(oElement);
                }
            }
            if (lock == "1") {
                root.childNodes[0].setAttribute("Lock", "1");
            }
            var strEscape = escape(xmltostring(xmlObj));
            SetLocalUrlList("JHC6Visited", strEscape);
        } catch (e1) { }
    },
    //记录访问痕迹
    SetVisted: function(type, name, url, id) {
        try {
            var strXML = GetLocalUrlList("JHC6Visited");
            if (strXML == null || strXML == "" || strXML == "null") {
                strXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?><root></root>";
            }
            strXML = strXML.replace("__", "");
            var xmlObj = stringtoxml(strXML);
            var root;
            var temproot = zwxJQ(xmlObj).find("root>item[userid='" + location.hostname + userid + "']");

            if (temproot.length > 0)
                root = temproot[0];
            if (root == null || xmltostring(root) == "") {
                root = xmlObj.createElement("item");
                var a = xmlObj.createAttribute("userid");
                a.value = location.hostname + userid;
                root.setAttributeNode(a);
                xmlObj.documentElement.appendChild(root);
            }

            var Num = 10; //最近访问记录总数
            var oElement = xmlObj.createElement("navigation");
            var atName = xmlObj.createAttribute("name");
            atName.value = name;
            oElement.setAttributeNode(atName);
            var atURL = xmlObj.createAttribute("URL");
            atURL.value = url;
            oElement.setAttributeNode(atURL);
            var atLock = xmlObj.createAttribute("Lock");
            atLock.value = "0";
            oElement.setAttributeNode(atLock);

            var atType = xmlObj.createAttribute("Type");
            atType.value = type;
            oElement.setAttributeNode(atType);

            var atID = xmlObj.createAttribute("Id");
            atID.value = id;
            oElement.setAttributeNode(atID);

            if (root.childNodes.length == 0)
                root.appendChild(oElement);
            else
                root.insertBefore(oElement, root.firstChild);

            var lock = 0; //新加的访问地址以前是否被钉住
            for (i = 1; i < root.childNodes.length; i++) {
                var oElement = root.childNodes[i];
                if (url.toLowerCase() == oElement.attributes[1].value.toLowerCase()) {
                    if (oElement.attributes[2].value == "1") {
                        lock = "1";
                    }
                    root.removeChild(oElement);
                }
                if (i == Num) {
                    if (oElement.attributes[2].value == "1") {
                        for (j = i - 1; j >= 0; j--) {
                            if (root.childNodes[j].attributes[2].value == "0") {
                                root.removeChild(root.childNodes[j]);
                                break;
                            }
                        }
                    }
                    else {
                        try {
                            root.removeChild(oElement);
                        } catch (e) { }
                    }
                }
                if (i > Num) {
                    root.removeChild(oElement);
                }
            }
            if (lock == "1") {
                root.childNodes[0].setAttribute("Lock", "1");
            }
            var strEscape = escape(xmltostring(xmlObj));
            SetLocalUrlList("JHC6Visited", strEscape);
        } catch (e1) { }
    },
    //读取访问痕迹
    GetVisted: function() {
        var sHTML = "<ul>";
        sHTML += "<li><span class='ui-list-driver'>" + RecentlyVisit + "</span></li>";
        sHTML += "<li class='ui-list-line'></li>";
        try {
            var strEscape = GetLocalUrlList("JHC6Visited");
            var strXML = unescape(strEscape);

            if (strXML == null || strXML == "" || strXML == "null") {
                sHTML += "</ul>";
                return sHTML;
            }
            strXML = strXML.replace("__", "");
            var xml = zwxJQ(stringtoxml(strXML));
            xml.find("root").children().each(function() {
                var fieldRt = zwxJQ(this);
                if(fieldRt.attr("userid")==(location.hostname + userid)){
                    fieldRt.children().each(function() {
                        var field = zwxJQ(this);
                        var url = field.attr("URL");
                        var name = field.attr("name");
                        var lock = field.attr("Lock");
                        var type = field.attr("Type");
                        var id = field.attr("Id");
                        var ifPop = field.attr("ifPopVal");
                        if (lock == 0) {
                            if (null!=ifPop && ifPop==1) {
                                sHTML += "<li><a onclick=\"openMenuBlank('"+ url+"');win_PopMenu.SetVistedNew('" + type + "','" + name + "','" + url + "','" + id + "','"+ifPop+"');\">" + name + "</a><span title='" + AddRecentlyVisit + "' class='ui-menu-pin-off'  onclick=\"win_PopMenu.SetSticked('" + id + "');StopBubble(event);\"></span></li>";
                            }else {
                                sHTML += "<li><a onclick=\"menuclick('" + type + "','" + name + "','" + url + "','" + id + "');win_PopMenu.SetVisted('" + type + "','" + name + "','" + url + "','" + id + "');\">" + name + "</a><span title='" + AddRecentlyVisit + "' class='ui-menu-pin-off'  onclick=\"win_PopMenu.SetSticked('" + id + "');StopBubble(event);\"></span></li>";
                            }
                        }
                        else {
                            if (null!=ifPop && ifPop==1) {
                                sHTML += "<li><a onclick=\"openMenuBlank('" + url +"');win_PopMenu.SetVistedNew('" + type + "','" + name + "','" + url + "','" + id + "','"+ifPop+"');\">" + name + "</a><span title='" + RemoveRecentlyVisit + "'  class='ui-menu-pin-on' onclick=\"win_PopMenu.SetSticked('" + id + "');StopBubble(event);\"></span></li>";
                            }else {
                                sHTML += "<li><a onclick=\"menuclick('" + type + "','" + name + "','" + url + "','" + id + "');win_PopMenu.SetVisted('" + type + "','" + name + "','" + url + "','" + id + "');\">" + name + "</a><span title='" + RemoveRecentlyVisit + "'  class='ui-menu-pin-on' onclick=\"win_PopMenu.SetSticked('" + id + "');StopBubble(event);\"></span></li>";
                            }
                        }
                    });
                }
            });
        } catch (e1) { }
        sHTML += "</ul>";
        return sHTML;
    },
    //钉住已访问记录
    SetSticked: function(id) {
        try {
            var strEscape = GetLocalUrlList("JHC6Visited");
            var strXML = unescape(strEscape);
            if (strXML == null || strXML == "" || strXML == "") return false;
            strXML = strXML.replace("__", "");
            var xmlObj = stringtoxml(strXML);
            var root = zwxJQ(xmlObj).find("root>item[userid='" + location.hostname + userid + "']");
            if (root.length == 0) return false;
            var oElement = root.find("navigation[Id='" + id + "']");
            if (oElement.length == 0) return false;
            atvalue = (oElement[0].getAttribute("Lock") == "1" ? "0" : "1");
            oElement[0].setAttribute("Lock", atvalue);
            strEscape = escape(xmltostring(xmlObj));
            SetLocalUrlList("JHC6Visited", strEscape);
            //重新加载访问记录
            var sHTML = this.GetVisted();
            zwxJQ("#c6ui-menu-content-right").html(sHTML);
        } catch (e1) { }
    }
}

//---------------------------选择卡----------------------------------------------------------
var win_TabMenu = {
    iTabIndex: 0,       //页签索引号
    xmlTabBuffer: null,   //暂存当前打开的所有页签信息
    CurTab: {},      //当前显示的页签
    TabRelation: null,  //记录打开页面与父页面的关系
    loadFrameArr: [],//用于缓存加载完成的frameid
    //新建一个页签
    OpenIndexPortalTab: function(type, name, url, id) {
        if (this.xmlTabBuffer == null)
            this.xmlTabBuffer = stringtoxml("<?xml version=\"1.0\" encoding=\"utf-8\"?><root></root>");
        this.iTabIndex = this.iTabIndex + 1;
        var tabid = "tab-button-" + this.iTabIndex;
        var tabname = name;
        var menuid = "";
        var menudiv = "menu-div-" + this.iTabIndex;
        var framediv = "frame-div-" + this.iTabIndex;
        var frameid = "imain_" + this.iTabIndex;
        var tabRid=id;

        //判断是否已经打开了此菜单选项卡:指定页签名称时以页签名称作判断，未指定时以URL来判断，有相同的则先关闭再打开
        if (tabname != null && tabname != "") {
            var node = zwxJQ(this.xmlTabBuffer).find("root>tabitem[tabname='" + tabname + "']");
            if (node.length > 0) {
                //win_TabMenu.SwitchTabWin(node.attr("tabid"));
                win_TabMenu.CloseTabWin(node.attr("tabid"));
                //win_TabMenu.AsignFrameUrl(node.attr("frameid"), url, node.attr("menudiv"), name, node.attr("tabid"), node.attr("framediv"));
                //return;
            }
        }
        else {
            var node = zwxJQ(this.xmlTabBuffer).find("root>tabitem[url='" + url + "']");
            if (node.length > 0) {
                win_TabMenu.CloseTabWin(node.attr("tabid"));
            }
        }
        //var temname = tabname;
        //if (tabname == "") temname = Loading; //临时标题
        //添加页签按钮
        zwxJQ("#c6ui-tabs-list li").attr("class", "");
        var oli = document.createElement("li");
        zwxJQ(oli).attr("id", tabid);
        zwxJQ(oli).attr("class", "tab_hover");
        zwxJQ(oli).attr("tabRid", tabRid);
        zwxJQ(oli).click(function() {
            var tabRidtag=tabRid;
            if(tabRidtag.indexOf("01")!=-1) {
                tabRidtag="";
            }
            var req = new XMLHttpRequest();
            try {
                req.open("POST", encryptUrl("newShortcutDesktopAjax?flag=MenuIDSET&menuId="+tabRidtag), false); //was get
            } catch (e) {
                alert("Problem Communicating with Server\n"+e);
            }
            req.send(null);

            win_TabMenu.SwitchTabWin(this.id) });
        oli.oncontextmenu = function() { return false; };
        zwxJQ(oli).mousedown(function() { if (getEvent().button == 2) ShowContextMenu(oli); }); //右键菜单
        var titleHtml = "" +
            "<span class='tab_title' style='padding-right: 0px;'>" +
            "   <img style='margin-bottom:-3px;margin-right:2px' class='tabstatus' alt='' src='/resources/component/quickDesktop/image/loadding_indicator_arrows.gif' />" +
            "   <img alt='' src='/resources/images/system/home.png' style=\"height: 12px;width: 14px;margin-right: 5px;margin-bottom: -2px;\"/>" +
            "   <label class='tabname'>" + Loading + "</label>" +
            "</span>" +
            "<span class='tab_close' style='width: 15px !important;'></span>";
        zwxJQ(oli).html(titleHtml);
        zwxJQ("#c6ui-tabs-list").append(oli);

        //添加IFRAME
        zwxJQ("#c6ui-main div").hide();
        var odivs = document.createElement("div");
        zwxJQ(odivs).attr("id", framediv);
        //var heig=zwxJQ("#c6ui-main").css("height");
        zwxJQ(odivs).css({ "overflow": "hidden", "height": "100%"});
        zwxJQ(odivs).html("<iframe id='" + frameid + "' name='" + frameid + "'  class=\"c6ui-iframe-main\" src=" + url + " allowTransparency=\"true\" frameBorder=0></iframe>");
        zwxJQ("#c6ui-main").append(odivs);
        //功能页面显示预加载
        if(url.indexOf("/webapp/") != -1){
            this.InitLoading(frameid);
        }

        //记录打开页面与父页面的关系
        if (this.TabRelation == null) {
            this.TabRelation = stringtoxml("<?xml version=\"1.0\" encoding=\"utf-8\"?><root></root>");
        }
        if (id == "" || id == null) {
            if ((this.CurTab.tabid != null) && (this.CurTab.tabid != "")) {
                var oElement = this.TabRelation.createElement("tabitem");
                oElement.setAttribute("tabid", tabid);                                    //当前页签ID
                oElement.setAttribute("parenttab", this.CurTab.tabid);           //父页签ID
                this.TabRelation.documentElement.appendChild(oElement);
            }
        }

        //暂存当前打开的页签信息
        var oElement = this.xmlTabBuffer.createElement("tabitem");
        oElement.setAttribute("tabid", tabid);                //页签按钮ID
        oElement.setAttribute("tabname", tabname);    //页签名称
        oElement.setAttribute("menudiv", menudiv);    //页签对应菜单DIV的ID
        oElement.setAttribute("framediv", framediv);       //页签对应页面DIV的ID
        oElement.setAttribute("menuid", menuid);         //打开页面的父级菜单ID
        oElement.setAttribute("frameid", frameid);
        oElement.setAttribute("url", url.toLocaleLowerCase());
        oElement.setAttribute("tabRid",tabRid);
        this.xmlTabBuffer.documentElement.appendChild(oElement);
        this.CurTab = { tabid: tabid, menudiv: menudiv, framediv: framediv, frameid: frameid };



        //页签定位到最右侧
        this.SlipToRight();
        //给IFRAM添加点击事件，点击时关闭菜单等弹出窗口
        var frm = zwxJQ("#" + frameid);
        frm.load(function() {
            //重新设置IFRAME高度
            /* if (isipad()) {
                 //zwxJQ("#" + frameid).height(frm[0].contentWindow.document.body.offsetHeight);
             }
             else {*/
            zwxJQ("#" + frameid).height("100%");
            //}
            //清除页面加载状态标志
            zwxJQ("#" + tabid).find(".tabstatus").hide();
            //显示页签的标题：当页签未指定标题时取页面的TITLE作为标题
            if ((name == null || name == "") && (id == null || id == "")) {
                tabname = frm[0].contentWindow.document.title;
            }
            if (GetLength(tabname) > 10) {
                zwxJQ("#" + tabid).find(".tab_title .tabname").attr("title", tabname);
                zwxJQ("#" + tabid).find(".tab_title .tabname").text(CheckLength(tabname, 10) + "...");
            }
            else {
                zwxJQ("#" + tabid).find(".tab_title .tabname").text(tabname);
            }


            //添加页面事件，点击时隐藏菜单等弹出层
            zwxJQ(frm[0].contentWindow.document).click(function() {
                HidePopupWin();
            });
            frm.focus(function() {
                HidePopupWin();
            });
            /*   if (isipad()) {
                   frm[0].contentWindow.document.addEventListener('touchstart', function(e) {
                       HidePopupWin();
                   });
               }*/
        });
        this.ChangeTabBackImage();
    },
    OpenTabWin: function(type, name, url, id) {
        url = encryptUrl(url);
        if (this.xmlTabBuffer == null)
            this.xmlTabBuffer = stringtoxml("<?xml version=\"1.0\" encoding=\"utf-8\"?><root></root>");
        this.iTabIndex = this.iTabIndex + 1;
        var tabid = "tab-button-" + this.iTabIndex;
        var tabname = name;
        var menuid = "";
        var menudiv = "menu-div-" + this.iTabIndex;
        var framediv = "frame-div-" + this.iTabIndex;
        var frameid = "imain_" + this.iTabIndex;
        var tabRid=id;

        //判断是否已经打开了此菜单选项卡:指定页签名称时以页签名称作判断，未指定时以URL来判断，有相同的则先关闭再打开
        if (tabname != null && tabname != "") {
            var node = zwxJQ(this.xmlTabBuffer).find("root>tabitem[tabname='" + tabname + "']");
            if (node.length > 0) {
                //win_TabMenu.SwitchTabWin(node.attr("tabid"));
                win_TabMenu.CloseTabWin(node.attr("tabid"));
                //win_TabMenu.AsignFrameUrl(node.attr("frameid"), url, node.attr("menudiv"), name, node.attr("tabid"), node.attr("framediv"));
                //return;
            }
        }
        else {
            var node = zwxJQ(this.xmlTabBuffer).find("root>tabitem[url='" + url + "']");
            if (node.length > 0) {
                win_TabMenu.CloseTabWin(node.attr("tabid"));
            }
        }
        //var temname = tabname;
        //if (tabname == "") temname = Loading; //临时标题
        //添加页签按钮
        zwxJQ("#c6ui-tabs-list li").attr("class", "");
        var oli = document.createElement("li");
        zwxJQ(oli).attr("id", tabid);
        zwxJQ(oli).attr("class", "tab_hover");
        zwxJQ(oli).attr("tabRid", tabRid);
        zwxJQ(oli).click(function() {
            var tabRidtag=tabRid;
            if(tabRidtag.indexOf("01")!=-1) {
                tabRidtag="";
            }
            var req = new XMLHttpRequest();
            try {
                req.open("POST", encryptUrl("newShortcutDesktopAjax?flag=MenuIDSET&menuId="+tabRidtag), false); //was get
            } catch (e) {
                alert("Problem Communicating with Server\n"+e);
            }
            req.send(null);

            win_TabMenu.SwitchTabWin(this.id) });
        oli.oncontextmenu = function() { return false; };
        zwxJQ(oli).mousedown(function() { if (getEvent().button == 2) ShowContextMenu(oli); }); //右键菜单
        zwxJQ(oli).html("<span class='tab_title'><img style='margin-bottom:-3px;margin-right:2px' class='tabstatus' alt='' src='/resources/component/quickDesktop/image/loadding_indicator_arrows.gif' /><label class='tabname'>" + Loading + "</label></span><span class='tab_close' onclick=\"win_TabMenu.CloseTabWin('" + tabid + "')\"><a href='###' ><img  src='/resources/component/quickDesktop/image/tab_close.png' align='top'/></a></span>");
        zwxJQ("#c6ui-tabs-list").append(oli);

        //添加IFRAME
        zwxJQ("#c6ui-main div").hide();
        var odivs = document.createElement("div");
        zwxJQ(odivs).attr("id", framediv);
        //var heig=zwxJQ("#c6ui-main").css("height");
        zwxJQ(odivs).css({ "overflow": "hidden", "height": "100%"});
        zwxJQ(odivs).html("<iframe id='" + frameid + "' name='" + frameid + "'  class=\"c6ui-iframe-main\" src=" + url + " allowTransparency=\"true\" frameBorder=0></iframe>");
        zwxJQ("#c6ui-main").append(odivs);
        //功能页面显示预加载
        if(url.indexOf("/webapp/") != -1){
            this.InitLoading(frameid);
        }

        //记录打开页面与父页面的关系
        if (this.TabRelation == null) {
            this.TabRelation = stringtoxml("<?xml version=\"1.0\" encoding=\"utf-8\"?><root></root>");
        }
        if (id == "" || id == null) {
            if ((this.CurTab.tabid != null) && (this.CurTab.tabid != "")) {
                var oElement = this.TabRelation.createElement("tabitem");
                oElement.setAttribute("tabid", tabid);                                    //当前页签ID
                oElement.setAttribute("parenttab", this.CurTab.tabid);           //父页签ID
                this.TabRelation.documentElement.appendChild(oElement);
            }
        }

        //暂存当前打开的页签信息
        var oElement = this.xmlTabBuffer.createElement("tabitem");
        oElement.setAttribute("tabid", tabid);                //页签按钮ID
        oElement.setAttribute("tabname", tabname);    //页签名称
        oElement.setAttribute("menudiv", menudiv);    //页签对应菜单DIV的ID
        oElement.setAttribute("framediv", framediv);       //页签对应页面DIV的ID
        oElement.setAttribute("menuid", menuid);         //打开页面的父级菜单ID
        oElement.setAttribute("frameid", frameid);
        oElement.setAttribute("url", url.toLocaleLowerCase());
        oElement.setAttribute("tabRid",tabRid);
        this.xmlTabBuffer.documentElement.appendChild(oElement);
        this.CurTab = { tabid: tabid, menudiv: menudiv, framediv: framediv, frameid: frameid };



        //页签定位到最右侧
        this.SlipToRight();
        //给IFRAM添加点击事件，点击时关闭菜单等弹出窗口
        var frm = zwxJQ("#" + frameid);
        frm.load(function() {
            //重新设置IFRAME高度
            /* if (isipad()) {
                 //zwxJQ("#" + frameid).height(frm[0].contentWindow.document.body.offsetHeight);
             }
             else {*/
            zwxJQ("#" + frameid).height("100%");
            //}
            //清除页面加载状态标志
            zwxJQ("#" + tabid).find(".tabstatus").hide();
            //显示页签的标题：当页签未指定标题时取页面的TITLE作为标题
            if ((name == null || name == "") && (id == null || id == "")) {
                tabname = frm[0].contentWindow.document.title;
            }
            if (GetLength(tabname) > 10) {
                zwxJQ("#" + tabid).find(".tab_title .tabname").attr("title", tabname);
                zwxJQ("#" + tabid).find(".tab_title .tabname").text(CheckLength(tabname, 10) + "...");
            }
            else {
                zwxJQ("#" + tabid).find(".tab_title .tabname").text(tabname);
            }


            //添加页面事件，点击时隐藏菜单等弹出层
            zwxJQ(frm[0].contentWindow.document).click(function() {
                HidePopupWin();
            });
            frm.focus(function() {
                HidePopupWin();
            });
            /*   if (isipad()) {
                   frm[0].contentWindow.document.addEventListener('touchstart', function(e) {
                       HidePopupWin();
                   });
               }*/
        });
        this.ChangeTabBackImage();
    },
    //预加载效果
    InitLoading:function(frameid){
        var sHTML = "";
        if (null==document.getElementById("loadDiv")) {
            //阴影不要覆盖头部系统名称区域
            var top = $("#c6ui-header").css("height");
            if(undefined == top){
                top = 0;
            }
            sHTML +=  "<div class='loadDiv' id='loadDiv'><div class='loading' id='loading'><img src='/resources/images/main/loading2.gif?pfdrid_c=true'/></div></div>";
            sHTML += "<div class='overlay' id='overlay'></div>";
            //css
            sHTML += "<style>";
            sHTML += ".loadDiv{position: fixed;padding: 0;overflow: hidden;box-shadow: 0px 5px 10px rgba(0,0,0,0.8); border-radius: 2px;";
            sHTML += " border: 1px solid #dddddd;background: #ffffff url(/javax.faces.resource/images/ui-bg_flat_75_ffffff_40x100.png.faces?ln=primefaces-flick) 50% 50% repeat-x;";
            sHTML += " width: auto;height: auto;left: 50%; top: 50%;z-index: 1002;} ";
            sHTML += ".loading{ height: auto;position: relative;border: 0;padding: .5em 1em;background: none;overflow: auto; zoom: 1;}";
            sHTML += ".loading{ height: auto;position: relative;border: 0;padding: .5em 1em;background: none;overflow: auto; zoom: 1;}";
            sHTML += ".overlay{position: absolute;top: "+top+";left: 0;width: 100%;height: 100%;z-index: 1001;";
            sHTML += " background: #eeeeee url(/javax.faces.resource/images/ui-bg_flat_0_eeeeee_40x100.png.faces?ln=primefaces-flick) 50% 50% repeat-x;";
            sHTML += " opacity: .80;} </style>";
        }
        //js
        sHTML += "<script type='text/javascript'> ";
        sHTML += " var loadDiv = document.getElementById('loadDiv');";
        sHTML += " var overlay = document.getElementById('overlay');";
        sHTML += " var loading = document.getElementById('loading');";
        sHTML += " loadDiv.style.display = 'block';";
        sHTML += " loading.style.display = 'block';";
        sHTML += " overlay.style.display = 'block';";

        sHTML += " var iframe = document.getElementById('"+frameid+"');";
        sHTML += " if (iframe.attachEvent){  ";
        sHTML += " iframe.attachEvent('onload', function(){";
        sHTML += " loadDiv.style.display = 'none';";
        sHTML += " overlay.style.display = 'none';";
        //加载完 加入数组
        sHTML += " win_TabMenu.loadFrameArr.push('"+frameid+"'); ";
        sHTML += "});} else {";
        sHTML += " iframe.onload = function(){";
        sHTML += " loadDiv.style.display = 'none';";
        sHTML += " overlay.style.display = 'none';";
        sHTML += " win_TabMenu.loadFrameArr.push('"+frameid+"'); ";
        sHTML += " };}";
        if(this.loadFrameArr.filter(v => v == frameid).length > 0){
            sHTML += " loadDiv.style.display = 'none';";
            sHTML += " loading.style.display = 'none';";
            sHTML += " overlay.style.display = 'none';";
        }
        sHTML += "</script>";
        zwxJQ('#c6ui-main').append(sHTML);

    },
    //切换页签到显示状态
    SwitchTabWin: function(tabid) {
        if (tabid == null || tabid == "") {
            return;
        }
        if (zwxJQ("#" + tabid).attr("class") == "tab_hover") {
            //return false;
        }

        var root = zwxJQ(this.xmlTabBuffer).find("root>tabitem[tabid='" + tabid + "']");
        if (root.length == 0) {
            return;
        }
        menudiv = root[0].getAttribute("menudiv");
        framediv = root[0].getAttribute("framediv");
        frameid = root[0].getAttribute("frameid");

        //页签按钮切换
        zwxJQ("#c6ui-tabs-list li").attr("class", "");
        zwxJQ("#" + tabid).attr("class", "tab_hover");
        //子菜单切换
        zwxJQ("#c6ui-header-subpage .subpage_list").hide();
        zwxJQ("#c6ui-header-subpage #" + menudiv).show();
        //IFRAME切换
        zwxJQ("#c6ui-main div").hide();
        zwxJQ("#c6ui-main #" + framediv).show();
        var url = root[0].getAttribute("url");
        //查看是否加载完成
        if(undefined != url && url.indexOf("/webapp/") != -1 && this.loadFrameArr.filter(v => v == frameid).length == 0){
            this.InitLoading(frameid);
        }

        this.CurTab = { tabid: tabid, menudiv: menudiv, framediv: framediv, frameid: frameid };
    },
    //关闭一个页签
    CloseTabWin: function(tabid) {
        var root = zwxJQ(this.xmlTabBuffer).find("root>tabitem[tabid='" + tabid + "']");
        if (root.length == 0) {
            return false;
        }
        menudiv = root[0].getAttribute("menudiv");
        framediv = root[0].getAttribute("framediv");
        frameid = root[0].getAttribute("frameid");

        //移除页签按钮
        zwxJQ("#c6ui-tabs-list li").remove("#" + tabid);
        //移除子菜单
        zwxJQ("#c6ui-header-subpage div").remove("#" + menudiv);
        //移除IFRAME
        var frame = zwxJQ("#" + frameid);
        //frame[0].contentWindow.document.write('');
        frame.attr("src", ""); //清空iframe的内容
        //frame[0].contentWindow.close(); //避免iframe内存泄漏
        //frame.remove(); //删除iframe
        //从加载完成的数组中移除
        this.loadFrameArr = this.loadFrameArr.filter(v => v != frameid);

        zwxJQ("#c6ui-main div").remove("#" + framediv);
        //移除页签父子关系
        var tbr = zwxJQ(this.TabRelation).find("root>tabitem[tabid='" + tabid + "']");
        if (tbr.length > 0) {
            this.TabRelation.documentElement.removeChild(tbr[0]);
        }
        zwxJQ(this.TabRelation).find("root>tabitem[parenttab='" + tabid + "']").each(function() {
            var node = zwxJQ(this);
            win_TabMenu.TabRelation.documentElement.removeChild(node[0]);
        });

        //显示最后一个页签
        this.xmlTabBuffer.documentElement.removeChild(root[0]);
        var lastnode = this.xmlTabBuffer.documentElement.lastChild;
        if (lastnode != null) {
            var tmid = lastnode.getAttribute("tabid");
            var tabrid=lastnode.getAttribute("tabRid");
            if(tabrid.indexOf("01")!=-1){
                tabrid="";
            }
            var req = new XMLHttpRequest();
            try {
                var  url =encryptUrl("newShortcutDesktopAjax?flag=MenuIDSET&menuId="+tabrid);
                req.open("POST",url , false); //was get
            } catch (e) {
                alert("Problem Communicating with Server\n"+e);
            }
            req.send(null);
            this.SwitchTabWin(tmid);
        }
        else {
        }
        this.SlipToRight();
        this.ChangeTabBackImage();
    },
    //页签全部关闭或显示时切换主窗口的背景
    ChangeTabBackImage: function() {
        /*var oPageStyle = document.getElementById("PageStyle");
        var href = oPageStyle.href;*/
        var imgurl = "url(../css/skin/default/images/shortcutdesktop.png)";
        if (this.xmlTabBuffer.documentElement.childNodes.length == 0) {
            zwxJQ("#c6ui-main").css({ "margin":"6px","background-image": imgurl, "background-repeat": "repeat"});

        }
        else {
            zwxJQ("#c6ui-main").css({ "background-image": "", "background-repeat": "", "background-position": "","margin":"" });
        }
        //zwxJQ("#c6ui-main").css({ "min-height": "600px" });
    },
    //切换IFRAME中的内容
    AsignFrameUrl: function(url, obj, nodecode) {
        var isRight = false;
        var imain = this.CurTab.frameid;
        var menudiv = this.CurTab.menudiv;
        var tabid = this.CurTab.tabid;
        var framediv = this.CurTab.framediv;
        var title = "";
        if (typeof obj == "string") {
            title = obj;
        }
        if (typeof obj == "object") {
            title = zwxJQ(obj).text();
        }
        zwxJQ("#" + tabid).find(".tabstatus").show(); //显示加载状态图标
        zwxJQ("#" + imain).attr("src", url);
        zwxJQ("#" + menudiv).find("a").attr("class", "normalMenu");
        zwxJQ("#" + menudiv).find("a").each(function() {
            var node = zwxJQ(this);
            if (node.text() == title) {
                node.attr("class", "currentMenu");
            }
        });
        zwxJQ("#" + imain).load(function() {
            //清除页面加载状态标志
            zwxJQ("#" + tabid).find(".tabstatus").hide();
        });

        var root = zwxJQ(this.xmlTabBuffer).find("root>tabitem[tabid='" + tabid + "']");
        root.attr("url", url);
        this.CurTab = { tabid: tabid, menudiv: menudiv, framediv: framediv, frameid: imain };
    },
    //页签左边滑动按钮事件
    SlipTabLeft: function() {
        var currentLeft = zwxJQ("#c6ui-tabs-list").css("margin-left");
        if (parseInt(currentLeft) < 0) {
            var dis = 200;
            if (parseInt(currentLeft) + dis > 0)
                dis = -(parseInt(currentLeft));
            zwxJQ("#c6ui-tabs-list").animate({ marginLeft: "+=" + dis + "px" }, 300, '', this.SlipBtnStatus);
        }
        this.SlipBtnStatus();
    },
    //页签右边滑动按钮事件
    SlipTabRight: function() {
        var len = 0
        zwxJQ("#c6ui-tabs-list li").each(function() {
            var li = zwxJQ(this);
            len += li.outerWidth() + 15;
        });
        len = len + 25;
        var temlen = zwxJQ("#center_div").outerWidth();
        var currentLeft = zwxJQ("#c6ui-tabs-list").css("margin-left");
        var otherWidth = (len - temlen) + parseInt(currentLeft);
        if (otherWidth > 0) {
            zwxJQ("#c6ui-tabs-list").animate({ marginLeft: "-=200px" }, 300, '', this.SlipBtnStatus);
        } else {
        }
    },
    //页签超出显示区时，添加、关闭或切换操作都滑动到最右侧
    SlipToRight: function() {
        var len = 0;
        zwxJQ("#c6ui-tabs-list li").each(function() {
            var li = zwxJQ(this);
            len += li.outerWidth() + 15;
        });
        len = len + 25;
        var temlen = zwxJQ("#center_div").outerWidth();
        if (len > temlen) {
            var otherWidth = -(len - temlen);
            zwxJQ("#c6ui-tabs-list").animate({ marginLeft: otherWidth + "px" }, 300, '', this.SlipBtnStatus);
        }
        else {
            zwxJQ("#c6ui-tabs-list").animate({ marginLeft: "0px" }, 300, '', this.SlipBtnStatus);
        }
    },
    //页签滑动后设置左右移动按钮的状态
    SlipBtnStatus: function() {
        var len = 0
        zwxJQ("#c6ui-tabs-list li").each(function() {
            var li = zwxJQ(this);
            len += li.outerWidth() + 15;
        });
        len = len + 25;
        var temlen = zwxJQ("#center_div").outerWidth();
        var currentLeft = zwxJQ("#c6ui-tabs-list").css("margin-left");
        var status;
        var otherWidth = (len - temlen) + parseInt(currentLeft);

        if (parseInt(currentLeft) == 0 && otherWidth > 0) {
            status = "leftUnable";
        } else if (parseInt(currentLeft) == 0 && otherWidth <= 0) {
            status = "allNo";
        } else if (parseInt(currentLeft) != 0 && otherWidth <= 0) {
            status = "rightUnable";
        } else {
            status = "allCan";
        };
        switch (status) {
            case "leftUnable":
                zwxJQ("#c6ui-tabs .left_btn a").hide();
                zwxJQ("#c6ui-tabs .right_btn a").show();
                break;
            case "allNo":
                zwxJQ("#c6ui-tabs .left_btn a").hide();
                zwxJQ("#c6ui-tabs .right_btn a").hide();
                break;
            case "rightUnable":
                zwxJQ("#c6ui-tabs .left_btn a").show();
                zwxJQ("#c6ui-tabs .right_btn a").hide();
                break;
            case "allCan":
                zwxJQ("#c6ui-tabs .left_btn a").show();
                zwxJQ("#c6ui-tabs .right_btn a").show();
                break;
        }
    }
}
function ShortcutMenuClickNew(type, name, url, id,ifPop) {
    if (url == "") {
        //取主菜单XML
        var xml = "";
        if (MenuXML == null) {
            xml = win_PopMenu.GetMenuXML();
        }
        else {
            xml = MenuXML;
        }
        var node = zwxJQ(xml).find("item[id='" + id + "']").children();
        var menuid = node.attr("id");
        var menuname = node.attr("name");
        var menuurl = node.attr("href");
        var menutype = node.attr("type");
        menuclick(menutype, menuname, menuurl, menuid);
    }
    else {
        if (null!=ifPop && ifPop == 1) {
            openMenuBlank(url);
        }else {
            menuclick(type, name, url, id);
        }
    }
}
function ShortcutMenuClick(type, name, url, id) {
    if (url == "") {
        //取主菜单XML
        var xml = "";
        if (MenuXML == null) {
            xml = win_PopMenu.GetMenuXML();
        }
        else {
            xml = MenuXML;
        }
        var node = zwxJQ(xml).find("item[id='" + id + "']").children();
        var menuid = node.attr("id");
        var menuname = node.attr("name");
        var menuurl = node.attr("href");
        var menutype = node.attr("type");
        menuclick(menutype, menuname, menuurl, menuid);
    }
    else {
        menuclick(type, name, url, id);
    }
}

//菜单门户点击事件
function menuclick(type, name, url, id) {
    if(url.indexOf("Dialog")!=-1){
        showDialogHead();
    }else {
        var req = new XMLHttpRequest();
        try {
            req.open("POST", encryptUrl("newShortcutDesktopAjax?flag=MenuIDSET&menuId="+id), false); //was get
        } catch (e) {
            alert("Problem Communicating with Server\n"+e);
        }
        req.send(null);
        win_TabMenu.OpenTabWin(type, name, url, id);
    }
    HidePopupWin();
}

//阻止事件冒泡
function StopBubble(event) {
    if (event.stopPropagation)
        event.stopPropagation();
    else
        event.cancelBubble = true;
}

//设置本地存储的菜单
function SetLocalUrlList(name, val) {
    if (window.localStorage) {
        var storage = window.localStorage;
        storage.setItem(name, val);
    }
    else {
        SetCookie("name", val);
    }
}

//获取本地存储的菜单
function GetLocalUrlList(name) {
    if (window.localStorage) {
        var storage = window.localStorage;
        return unescape(storage.getItem(name));
    }
    else {
        return indexGetCookie(name);
    }
}
//去除换行
function clearBr(key)
{
    key = key.replace(/[\r\n]/g, "");
    return key;
}
function openMenuBlank(url) {
    window.open(url);
}
//sm2加密
function encryptUrl(url){
    if (url.indexOf("?") == -1) {
        return url;
    }
    //如果地址中带多个”?“，此处截取会有问题，导致第二个问号的内容取不到，建议使用其他特殊字符拼接参数
    var postData =  url.split('?')[1];
    if (postData == "" || postData.startsWith("encryptdata")) {
        return url;
    }
    postData = new SM2Utils().baseSm2Encrypt123(postData);
    postData = "encryptdata=" + encodeURIComponent(postData);
    url = url.split('?')[0]+ "?" +postData;
    return url;
}
