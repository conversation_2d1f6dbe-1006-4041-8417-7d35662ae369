/**
 * 个性桌面
 */
var elemWid = 1024; //桌面宽度
var desktopNum = 1; //桌面数

var maxDesktopNum = 10; //最大桌面数
var maxMenuNum =24; //每个桌面最大菜单数

var tdmifa = "目标桌面菜单达到上限"; //目标桌面菜单达到上限。
var mkad = "必须保留一个桌面"; //必须保留一个桌面。
var isPc = !("orientation" in window && "onorientationchange" in window); //是否PC客户端
var isFirefox = navigator.userAgent.toLowerCase().match(/firefox/) != null; //是否Chrome浏览器
var isChrome = navigator.userAgent.toLowerCase().match(/chrome/) != null; //是否Chrome浏览器
var isSafari = zwxJQ.browser.safari; //是否Safari浏览器
var region = 'html';
if (isChrome || isSafari) {
    region = 'body';
} else {
    region = 'html';
}
//页面加载
zwxJQ(document).ready(function() {
    elemWid = zwxJQ(window).width();//获取页面窗口的宽度
    InitSD();//初始化桌面信息
    InitEvent();//桌面初始化事件
});


//初始化快捷桌面
function InitSD() {

    var req = new XMLHttpRequest();
      req.onreadystatechange = function(){
    	  if (req.readyState == 4&&req.status == 200) {
    		   var no = 1;//现有桌面数
    	 	    var noTag = 1;//现有桌面数
    	 	    var desktopList = [];
    	 	    var navList = [];
    		  if(req.responseText!=""&&req.responseText.indexOf(",#,")!=-1){
    	 var menuObj =  req.responseText.split("@#@"); 

 		//格式：RID,:,所在桌面号,:,排序号,#,菜单名称,_,图标,_,连接地址
 		//var menuObj= data.split("@#@");
 	 
 	    var hasAFDesk=0;//是否已经存在桌面
 	    if(menuObj.length>0){
 	    	
 	    	var deskNo="";

 	 	    var tag = 0;
 		    for(var i=0;i<menuObj.length;i++){
 		       var menuStr=menuObj[i].split(",#,");
 		       var aftStr=menuStr[0];
 		       var befStr=menuStr[1];
 		       var aftStrs=aftStr.split(",:,");
 		    	   hasAFDesk=1;
 		    	   //数据库中存在排序功能
 			       var desNo=parseInt(aftStrs[1],10);
 			       var rid=aftStrs[0];
 			      if(no!=desNo&&no<desNo){
 			    	 
 			    	  no=desNo;
 			    	tag = 1;
 			      }
 			       if(no==desNo){
 			    	  if(i!=0){
 			    		  desktopList.push('</ul>'); 
 			    	  }
 			    	   desktopList.push('<ul class="desktopList">');
 			    	   if(tag==1){
 			    		  deskNo+=","+noTag+":"+desNo;
 			    	   }
 			    	   no++;noTag++;
 			       }
 			         var menVs=befStr.split(",_,");
 				     var image = menVs[3];
 						var defaultImg = "/resources/component/quickDesktop/image/shortcut-menu.png";
 				     if (image == "" || image == null) {
 				         image = defaultImg;
 				     }
 				     else {
 				         image = "/resources/component/quickDesktop/image/64px/"+image;
 				     }
 					 desktopList.push('<li id="'+rid+'" url="'+menVs[2]+'" openType="' + menVs[0] + '">');
 				     desktopList.push('<a href="javascript:void(0)" title="' + menVs[0] + '">');
 				     desktopList.push('<img src="'+image+'"  with ="64px" height="64px"/>');
 				     desktopList.push('<span>'+CheckLength(menVs[0], 12) +'</span>');
 				     desktopList.push('</a>');
 				     desktopList.push('</li>');
 			    
 			       if(i==(menuObj.length-1)){
 			    	   desktopList.push('</ul>');
 				   }
 		       
 		    }
 		    if(deskNo!=""){
	 		   var updReq = new XMLHttpRequest();
		    	try {
		    		updReq.open("POST", "newShortcutDesktopAjax?flag=InitSD_upd&deskNo="+(deskNo), true); //was get
		         } catch (e) {
		           alert("Problem Communicating with Server\n"+e);
		         }
		        updReq.send(null);
 		    }
 	    }  }
 	   noTag--;
 	    navList.push('<li no="1" class="navCurrent"><a href="javascript:void(0)">1</a></li>');
 	    for(var li=2;li<=noTag;li++){
 	      navList.push('<li no="'+li+'"><a href="javascript:void(0)">'+li+'</a></li>');
 	     }
 	        desktopNum = noTag;
 	    for (var j = desktopNum + 1; j <= maxDesktopNum; j++) {
 	    	navList.push('<li no="' + j + '" style="display:none"><a href="javascript:void(0)">' + j + '</a></li>');
 	        desktopList.push('<ul class="desktopList" style="display:none"></ul>');
 	    }
 	    
 	    var style = desktopNum < maxDesktopNum ? "display:block" : "display:none";
 	    navList.push('<li class="pointLi" style="' + style + '"><a href="javascript:void(0)">...</a></li>');
 	    zwxJQ('.desktopNav').html(navList.join(""));
 	    zwxJQ('.ulList').html(desktopList.join(""));

 	    AdjustULWidth();//调整桌面自适应宽度
 	    MoveMenuEvent();//鼠标移动菜单事件
 	    MoveDesktopEvent();//鼠标滑动桌面事件
 	    MoveMenuBetweenDesktopEvent();//菜单跨桌面移动事件
    	}
      };
      
      try {
      	req.open("POST", "newShortcutDesktopAjax?flag=InitSD", true); //was get
      } catch (e) {
        alert("Problem Communicating with Server\n"+e);
      }
      req.send(null);
}

//菜单单击事件：打开页签
function ClickMenuToOpen(menu) {
    var id = menu.attr("id");
    var openType = menu.attr("openType");
    var name = menu.find("a").attr("title");
    var url = menu.attr("url");
    top.ShortcutMenuClick(openType, name, url, id);
        
    
}
//初始化事件
function InitEvent() {
    //禁止选中
	zwxJQ(document).bind("selectstart", function() { return false; });
	 //导航桌面右侧“+”单击事件：打开菜单添加页面
	zwxJQ(".desktopNavR").live("click", function() {
    	var desktopMenuCountMap = {}; //桌面序号和桌面菜单数对应关系map
    	zwxJQ(".desktopNav li[no]").each(function() {
            var desktop = zwxJQ(this);
            var desktopNo = parseInt(desktop.text());
            if (desktop.is(":visible")) {
                desktopMenuCountMap[desktopNo] = zwxJQ(".ulList > ul").eq(desktopNo - 1).children("li[id]").length;
            } else {
                desktopMenuCountMap[desktopNo] = 0;
            }
        });
    	var curDeskTopNo = parseInt(zwxJQ(".desktopNav li.navCurrent").text());

        var url = "shortcutDesktopMenuAdd.faces";
        var size = { width: 800, height: 500 };
        var objParam = { curDeskTopNo: curDeskTopNo, desktopMenuCountMap: desktopMenuCountMap };
        openModalDialog(url, objParam, size, AddMenuCallBack, [], "btnClose", null);

   });
	//菜单单击事件
	zwxJQ('.desktopList li[id]').live('click', function() {
        var menu = zwxJQ(this);
        if (!menu.hasClass("hoverMid")) {
            ClickMenuToOpen(menu);
        }
    });
	
	
	//导航桌面单击切屏事件
	zwxJQ('.desktopNav li[no]').live('click', function() {
        var index = parseInt(zwxJQ(this).text()) - 1;
        zwxJQ(this).addClass('navCurrent').siblings('li').removeClass('navCurrent');
        zwxJQ('.ulList').animate({
            'margin-left': '-' + elemWid * index + 'px'
        }, 0);
    });
	
    //鼠标滑动屏幕切屏开始事件
    zwxJQ('.deskbody').live('mousedown', ChangeScreeStartEvent);
    //鼠标滚轮滚动切屏事件(无论滚动幅度大小只相当于滚动一次)
    var mwTimeout;
    var mwFlag = true;
    zwxJQ('.deskbody').live(isFirefox ? "DOMMouseScroll" : "mousewheel", function(e) {
    	if (mwFlag) {
            var index = parseInt(zwxJQ("li.navCurrent").text()) - 1;
            if (isFirefox) {
                if (e.detail >= 3) {
                    index -= 1;
                } else if (e.detail <= -3) {
                    index += 1;
                }
            } else {
                if (e.wheelDelta >= 120) {
                    index -= 1;
                } else if (e.wheelDelta <= -120) {
                    index += 1;
                }
            }
            ChangeScreen(index);
            mwFlag = false;
            clearInterval(mwTimeout);
            mwTimeout = setTimeout(function() {
                mwFlag = true;
            }, 350);
    	}
    });
	 
	 
	 //页面尺寸改变事件
    zwxJQ(window).resize(function() {
        elemWid = zwxJQ(window).width();
        AdjustULWidth();
    });
} 


//初始化菜单对应业务信息
    function InitModuleInfo() {
        //参数xml，用于描述需要获取什么样的业务信息，放在此处便于后续扩展
    }
	//打开菜单添加页面回调函数
	function AddMenuCallBack(ret) {
		var rets=ret.split("#");
		if(rets.length<=5){
			var add=rets[0];
			var del=null!=rets[1]?rets[1]:"";
			var addSou=null!=rets[2]?rets[2]:"";
			var delSou=null!=rets[3]?rets[3]:"";
			var _uRid=null!=rets[4]?rets[4]:"";
			 var req = new XMLHttpRequest();
		      req.onreadystatechange = function(){
		    	  if (req.readyState == 4&&req.status == 200) { 
		    		  window.location.href ="content.faces";
		    	  }
		      };
		      try {
		      	req.open("POST", "newShortcutDesktopAjax?flag=AddMenuCallBack&add="+add+"&del="+del
		      			+"&addSou="+addSou+"&delSou="+delSou+"&_uRid="+_uRid, false); //was get
		      } catch (e) {
		        alert(e);
		      }
		      req.send(null);
		}
	}
//调整桌面宽度
function AdjustULWidth() {
    if (elemWid >= 1024) {
        zwxJQ('ul.desktopList').width(elemWid * 0.86).css('padding-left', elemWid * 0.07).css('padding-right', elemWid * 0.07);
    } else {
        zwxJQ('ul.desktopList').width(elemWid * 0.9).css('padding-left', elemWid * 0.05).css('padding-right', elemWid * 0.05);
    }
    zwxJQ('.mainList').width(elemWid);
    zwxJQ('.ulList').width(elemWid * desktopNum);
    zwxJQ('.desktopNavL').css('margin-left', elemWid * 0.4);
}
//切换屏幕
function ChangeScreen(index) {
    if ((index >= 0) && (index < desktopNum)) {
    	zwxJQ('.desktopNav li[no]').eq(index).addClass('navCurrent').siblings('li').removeClass('navCurrent');
    	zwxJQ('.ulList').animate({
            'margin-left': '-' + elemWid * index + 'px'
        }, 500);
    }
}
//鼠标滑动切换屏幕开始事件
function ChangeScreeStartEvent(e) {
    var inTargets = false;
    var target = zwxJQ(e.target);
    var isTarget = ['deskbody', 'desktopMain', 'mainList', 'desktopList'];
    for (var i = 0; i < isTarget.length; i++) {
        if (target.hasClass(isTarget[i])) {
            inTargets = true;
            break;
        }
    }
    if (inTargets) {
        csFlag = true; //是否切换屏幕
        csBeginX = isPc ? e.clientX : e.targetTouches[0].pageX; //开始移动时x轴原始坐标
        e.preventDefault();
        //鼠标滑动屏幕切屏移动事件
        target.mousemove(function(e) { ChangeScreenMoveEvent(e); });
        //鼠标滑动屏幕切屏终止事件
        target.mouseup(function(e) { ChangeScreenEndEvent(e); });
    }
}
//鼠标滑动切换屏幕Move事件
function ChangeScreenMoveEvent(e) {
    if (csFlag) {
        var index = parseInt(zwxJQ("li.navCurrent").text()) - 1;
        var diffX = (isPc ? e.clientX : e.targetTouches[0].pageX) - csBeginX; //移动后的x轴坐标原始坐标差值。大于零向右滑，小于零向左滑
        var maxMl = (desktopNum - 1) * elemWid;
        diffX = elemWid * index - diffX;
        diffX = diffX < 0 ? 0 : diffX;
        diffX = diffX > maxMl ? maxMl : diffX;
        zwxJQ('.ulList').stop().animate({
            'margin-left': '-' + diffX + 'px'
        }, 0);
    }
}
//鼠标滑动切换屏幕End事件
function ChangeScreenEndEvent(e) {
    if (csFlag) {
        csFlag = false;
        var index = parseInt(zwxJQ("li.navCurrent").text()) - 1;
        var diffX = (isPc ? e.clientX : e.changedTouches[0].pageX) - csBeginX; //移动后的x轴坐标原始坐标差值。大于零向右滑，小于零向左滑
        if (diffX > 300) {
            index -= 1;
        } else if (diffX < -300) {
            index += 1;
        }
        ChangeScreen(index);
    }
   
}

//菜单拖拽排序
var menuDragEnd = false; //菜单拖拽是否结束
var menuInRegion = true; //被拖拽菜单是否在拖拽区域内
function MoveMenuEvent() {
    zwxJQ('.desktopList').sortable({
        containment: region,
        scroll: false,
        cursorAt: { top: 25, left: 25 },
        distance: 5,
        revert: true,
        over: function(event, ui) {
            ui.item.addClass('hoverMid');
            zwxJQ('.trashbin').css('visibility', 'visible');
        },
        stop: function(event, ui) {
            ui.item.removeClass('hoverMid');
            zwxJQ('.trashbin').css('visibility', 'hidden');
            if (menuDragEnd && menuInRegion) {
                MoveMenu(ui.item, ui.item.prev("li[id]"));
            }
            menuDragEnd = false;
            menuInRegion = true;
        },
        update: function(event, ui) {
            menuDragEnd = true;
        },
        remove: function(event, ui) {
            menuInRegion = false;
        }
    });
}

//桌面拖拽
var desktopDragEnd = false; //桌面拖拽是否结束
var desktopInRegion = true; //被拖拽桌面是否在拖拽区域内
function MoveDesktopEvent() {
    zwxJQ('.desktopNav').sortable({
        items: "> li[no]",
        containment: region,
        scroll: false,
        distance: 5,
        over: function(event, ui) {
            zwxJQ('.trashbin').css('visibility', 'visible');
        },
        stop: function(event, ui) {
            zwxJQ('.trashbin').css('visibility', 'hidden');
            if (desktopDragEnd && desktopInRegion) {
                MoveDesktop(ui.item, ui.item.prev("li[no]"));
            }
            desktopDragEnd = false;
            desktopInRegion = true;
        },
        update: function(event, ui) {
            desktopDragEnd = true;
        },
        remove: function(event, ui) {
            desktopInRegion = false;
        }
    });
}
//菜单跨桌面移动
function MoveMenuBetweenDesktopEvent() {
    zwxJQ('.desktopNav > li').droppable({
        over: function(event, ui) {
            var isMenu = ui.draggable.parent().hasClass("desktopList");
            if (isMenu) {//保证拖到桌面上的是菜单
                zwxJQ(this).addClass('iconHover');
            }
        },
        out: function(event, ui) {
            zwxJQ(this).removeClass('iconHover');
        },
        drop: function(event, ui) {
            var isMenu = ui.draggable.parent().hasClass("desktopList");
            if (isMenu) {//保证拖到桌面上的是菜单
                var newMenu = ui.draggable.clone().removeClass().removeAttr("style");
                var desktop = zwxJQ(this);
                desktop.removeClass('iconHover');
                MoveMenuBetweenDesktop(ui.draggable, desktop, newMenu);
            }
        }
    });
}
//删除菜单或桌面
function DelMenuOrDesktopEvent() {
    zwxJQ('.trashbin').droppable({
        over: function(event, ui) {
            zwxJQ(this).find('a').addClass('trashhover');
        },
        out: function(event, ui) {
            zwxJQ(this).find('a').removeClass('trashhover');
        },
        drop: function(event, ui) {
            var dragIcon = ui.draggable.parents('ul').hasClass('desktopList');
            var dragDesk = ui.draggable.parents('ul').hasClass('desktopNav');
            if (dragIcon) { //删除图标
                DelMenu(ui.draggable);
            } else if (dragDesk) { //删除桌面
                DelDesktop(ui.draggable);
            }
            zwxJQ('.trashbin a').removeClass('trashhover');
            zwxJQ('.trashbin').css('visibility', 'hidden');
        }
    });
}
//删除菜单(异步后台处理)
function DelMenu(menu) {
    var menuId = menu.attr("id");
		        var desktopList = menu.parent();
		        menu.remove();
		        if (desktopList.children("li[id]").length == 0) {//当前桌面已无菜单，则删除桌面
		            if (desktopNum != 1) {//至少保留一个桌面
		                DomDelDesktop(desktopList, 1, true);
		            }
		        }
}
//菜单拖拽排序(异步后台处理)
function MoveMenu(movedMenu, preMenu) {
    var curDesktopNo = movedMenu.parent().index() + 1; //当前桌面序号
    var uls=zwxJQ('.ulList').find("ul:eq("+movedMenu.parent().index()+")").children();
    //同一个桌面的移动排序调用
    var idstr="";
    for(var k=0;k<uls.length;k++){
    	idstr+=","+uls.eq(k).attr("id");
    }
    var req = new XMLHttpRequest();
    try {
    	req.open("POST", "newShortcutDesktopAjax?flag=mMoveFun&ordRids="+idstr+"&deskNo="+curDesktopNo, true); //was get
    } catch (e) {
      alert(e);
    }
    req.send(null);
}
//菜单跨桌面移动(异步后台处理)
function MoveMenuBetweenDesktop(menu, toDesktop, newMenu) {
    var menuId = menu.attr("id");
    var isAddDesktop = false; //是否添加桌面
    var toDesktopNo = parseInt(toDesktop.text()); //“移动后桌面”序号
    if (!toDesktopNo) {
        isAddDesktop = true;
        toDesktopNo = desktopNum + 1;
    }
    var curDesktopNo = menu.parent().index() + 1; //当前桌面序号
    var curDesktopList = zwxJQ(".ulList > ul").eq(curDesktopNo - 1); //当前桌面对象
    var curMenuNum = curDesktopList.children("li[id]").length; //当前桌面菜单数
    var toDesktopList = curDesktopList.parent().children("ul").eq(toDesktopNo - 1); //“移动后桌面”列表对象
    if (curDesktopNo == toDesktopNo) {//被移动菜单被移动到其所在桌面
        return;
    }
    else if (desktopNum == 1 && curMenuNum == 1) {//只有1个桌面和1个菜单
        return;
    }
    else if (curMenuNum == 1 && isAddDesktop) {//只有一个菜单的桌面，菜单移到新桌面(移动后和原来一样，无意义)
        return;
    }
    else if (!isAddDesktop && toDesktopList.children("li[id]").length == maxMenuNum) {//目标桌面菜单已满
        alert(tdmifa);
        return;
    }
    else {
    	 var req = new XMLHttpRequest();
       try {
       	req.open("POST", "newShortcutDesktopAjax?flag=mDeskMoveFun&menuId="+menuId+"&toDesktopNo="+toDesktopNo, true); //was get
       } catch (e) {
         alert(e);
       }	
       req.send(null);
		              menu.remove();
				            toDesktopList.append(newMenu);
				            if (curMenuNum == 1) {
				                DomDelDesktop(curDesktopList, 1, true);
				            }
				            if (isAddDesktop) {
				                desktopNum++;
				                toDesktopList.show();
				                AdjustULWidth();
				                RebuildDesktopNav(curDesktopNo, false);
				            }
		            
    }
}
//删除桌面(异步后台处理)
function DelDesktop(desktop) {
    var desktopNo = parseInt(desktop.text());
    if (desktopNum >= 2) {
		            var desktopList = zwxJQ(".ulList").children("ul").eq(desktopNo - 1);
		            if (desktop.hasClass("navCurrent")) {
		                DomDelDesktop(desktopList, 1, true);
		            } else {
		                var curDesktopNo = parseInt(desktop.siblings(".navCurrent").text());
		                if (curDesktopNo > desktopNo) {
		                    DomDelDesktop(desktopList, curDesktopNo - 1, true);
		                } else {
		                    DomDelDesktop(desktopList, curDesktopNo, false);
		                }
		            }
    }
    else {
    	alert(mkad);
    }
}
//桌面拖拽排序(异步后台处理)
function MoveDesktop(movedDesktop, preDesktop) {
    var movedDesktopNo = parseInt(movedDesktop.text());
    var preDesktopNo = 0;
    if (preDesktop.length > 0) {
        preDesktopNo = parseInt(preDesktop.text());
    }
		        //桌面移动
		        var movedDesktopList = zwxJQ(".ulList").children("ul").eq(movedDesktopNo - 1);
		        if (preDesktopNo > 0) {
		            var preDesktopList = movedDesktopList.parent().children("ul").eq(preDesktopNo - 1);
		            movedDesktopList.insertAfter(preDesktopList);
		        } else {
		            movedDesktopList.insertBefore(movedDesktopList.parent().children("ul:eq(0)"));
		        }
		        var curDesktopNo = movedDesktop.parent().children("li.navCurrent[no]:visible").index() + 1;
		        RebuildDesktopNav(curDesktopNo, true);
}
//“重新生成”桌面导航并定位当前桌面
function RebuildDesktopNav(curDesktopNo, isChangeScreen) {
    var desktopNavList = zwxJQ(".desktopNav").children("li[no]");
    desktopNavList.each(function(i) {
        var desktop = zwxJQ(this);
        if (i < desktopNum) {
            desktop.show();
        } else {
            desktop.hide()
        }
        desktop.children("a").text(i + 1);
    });
    if (desktopNum < maxDesktopNum) {
        zwxJQ(".pointLi").show();
    } else {
        zwxJQ(".pointLi").hide();
    }
    if (isChangeScreen) {
        if (!curDesktopNo) {
            curDesktopNo = 1;
        }
        desktopNavList.eq(curDesktopNo - 1).click();
    }
}//删除桌面的相关dom操作
function DomDelDesktop(desktopList, curDesktopNo, isChangeScreen) {
    desktopNum--;
    desktopList.hide().children().remove();
    zwxJQ(".ulList").append(desktopList);
    AdjustULWidth();
    RebuildDesktopNav(curDesktopNo, isChangeScreen);
}