//登陆后最大化窗口
//var myie = indexGetCookie("myie");
//if (myie != "true") {
//    self.moveTo(0, 0);
//    self.resizeTo(screen.availWidth, screen.availHeight);
//}

var ParentTab = {
    //获取父级页签ID
    ParentID: function(tabid) {
        if (typeof (tabid) == "undefined")
            tabid = win_TabMenu.CurTab.tabid;
        var node = zwxJQ(win_TabMenu.TabRelation).find("root>tabitem[tabid='" + tabid + "']");
        var parenttab = null;
        if (node.length > 0) {
            parenttab = node.attr("parenttab");
        }
        return parenttab;
    },
    ParentWindow: function(tabid) {
        if (typeof (tabid) == "undefined")
            tabid = win_TabMenu.CurTab.tabid;
        var node = zwxJQ(win_TabMenu.TabRelation).find("root>tabitem[tabid='" + tabid + "']");
        var pwindow = null;
        if (node.length > 0) {
            parentid = node.attr("parenttab");
            var root = zwxJQ(win_TabMenu.xmlTabBuffer).find("root>tabitem[tabid='" + parentid + "']");
            if (root.length > 0) {
                var frameid = root[0].getAttribute("frameid");
                pwindow = zwxJQ("#" + frameid)[0].contentWindow;
            }
        }
        return pwindow;
    }
};

//2012-03-07 沈繁荣 将焦点切换到主页上,解决IE下偶尔出现文本框无法输入的问题
function SetTextFocus() {
    if (zwxJQ.browser.msie) {
        zwxJQ("#minSearch").focus();
    }
}
//为控件添加气泡提示
function AddBubble() {
    AttachBubbleEvent("toolbarmenu", page_strMenu);  //菜单
    AttachBubbleEvent("locksystem", page_myLock, -25, 0, "rightTop");  //锁定
    AttachBubbleEvent("hidehead", page_strHide, -25, 0, "rightTop");  //收起
    AttachBubbleEvent("expandhead", page_strExpand, -25, 0, "rightTop");  //展开
    AttachBubbleEvent("turn_off", page_hideshortcut, 60, -60, "leftBottom");  //点击隐藏快捷菜单
    AttachBubbleEvent("turn_on", page_showshortcut, 60, -60, "leftBottom");  //点击显示快捷菜单
    AttachBubbleEvent("shortcutDesktop", "个人桌面");  
    AttachBubbleEvent("shortcutDesktop2", "菜单桌面");
    AttachBubbleEvent("allSeachDesktop", "全文检索");
}
//为有气泡提示的控件添加鼠标事件
function AttachBubbleEvent(ctrlid, content, left, top, corner) {
    var bubbleid = ctrlid + "_bubble";
    var con = content;
    zwxJQ("#" + ctrlid).mouseover(function() {
        if (ctrlid == "onlineusers")
            content = con + "(" + onlineUsers + ")";
        var oElement = zwxJQ("#" + ctrlid)[0];
        var oheight = oElement.offsetHeight;
        var coordinate = { x: 0, y: 0 };
        while (oElement) {
            coordinate.x += oElement.offsetLeft;
            coordinate.y += oElement.offsetTop;
            oElement = oElement.offsetParent;
        }

        if (typeof left == "undefined") left = 0;
        if (typeof top == "undefined") top = 0;
        if (typeof corner == "undefined") corner = "leftTop";
        var oleft = coordinate.x + left;
        var otop = coordinate.y + oheight + top;
        zwxJQ(document).bubble(bubbleid, true, { x: oleft, y: otop, con: content, corner: 'none' });
    });
    zwxJQ("#" + ctrlid).mouseout(function() {
        zwxJQ(document).bubble(bubbleid, false);
    });
}


//系统锁定
function doLock() {
    SetCookie("islock", "lock");
    SetCookie("islock2", "lock");
    var objLockFrame = document.getElementById("lockframe");
    objLockFrame.style.display = "inline";
    objLockFrame.src = "ScreenLock.aspx";
}
//检查系统是否锁定
function CheckLock() {
    var strlock = indexGetCookie("islock");
    if (strlock == "lock") {
        var objLockFrame = document.getElementById("lockframe");
        objLockFrame.style.display = "inline";
        if (objLockFrame.src == "") {
            objLockFrame.src = "ScreenLock.aspx";
        }
    }
}
//系统解锁
function UnLock() {
    SetCookie("islock", "unlock");
    var objLockFrame = document.getElementById("lockframe");
    objLockFrame.src = "";
    objLockFrame.style.display = "none";
    //window.location.reload();
}
//设置Cookie
function SetCookie(name, value) {
    var exp = new Date();
    exp.setTime(exp.getTime() + 3600000000);
    document.cookie = name + "=" + value + "; expires=" + exp.toGMTString() + "; path=/";
}
//删除Cookie 
function indexDeletecookie(name) {
    var exp = new Date();
    exp.setTime(exp.getTime() - 1);
    var cval = indexGetCookie(name);
    document.cookie = name + "=" + cval + "; expires=" + exp.toGMTString();
}
//取得名称为name的cookie值 
function indexGetCookie(name) {
    var arg = name + "=";
    var alen = arg.length;
    var clen = document.cookie.length;
    var i = 0;
    while (i < clen) {
        var j = i + alen;
        if (document.cookie.substring(i, j) == arg)

            return indexGetCookieVal(j);
        i = document.cookie.indexOf(" ", i) + 1;
        if (i == 0) break;
    }
    return null;
}
//取得项名称为offset的cookie值 
function indexGetCookieVal(offset) {
    var endstr = document.cookie.indexOf(";", offset);
    if (endstr == -1)
        endstr = document.cookie.length;
    return unescape(document.cookie.substring(offset, endstr));
}
//创建页签右键菜单
function ShowContextMenu(evt) {
    if (win_ContextMenu.isShow) {
        return false;
    }
    var cl = zwxJQ(evt).attr("class");
    zwxJQ(win_ContextMenu.document.body).css({ "overflow": "hidden", "background-color": "#FFFFFF", "border": "1px solid #9aaec1" });
    var iheight = 56;
    var sHTML = "";
    sHTML += "<div class='c6ui-base-poplist'>";
    sHTML += "<ul>";
    sHTML += "<li onclick=\"top.CloseTabWin('" + evt.id + "')\"><img style='position:absolute; top:6px; left:6px; z-index:1000' src='/resources/component/quickDesktop/image/16px/cancel.png' /><a>" + page_TabClose + "</a></li>";
    sHTML += "<li onclick=\"top.CloseAllTabWin()\"><img style='position:absolute; top:6px; left:6px; z-index:1000' src='/resources/component/quickDesktop/image/16px/document-copy.png' /><a>" + page_TabCloseAll + "</a></li>";
    if (cl == "tab_hover") {
        sHTML += "<li onclick=\"top.RefreshTabWin('" + evt.id + "')\"><img style='position:absolute; top:6px; left:6px; z-index:1000' src='/resources/component/quickDesktop/image/16px/refresh.png' /><a>" + page_titleReload + "</a></li>";
        sHTML += "<li onclick=\"top.CloseAllTabWin('" + evt.id + "')\"><img style='position:absolute; top:6px; left:6px; z-index:1000' src='/resources/component/quickDesktop/image/16px/file.png' /><a>" + page_TabCloseOther + "</a></li>";
        iheight += 56;
    }
    sHTML += "</ul></div>";

    win_ContextMenu.document.body.innerHTML = sHTML;
    var owidth = 100;
    var oheight = iheight;
    var otop = parseInt(zwxJQ(evt).height());
    var oleft = parseInt(zwxJQ(evt).width()) - owidth;
    win_ContextMenu.show(oleft, otop, owidth, oheight, evt);
}


//打开新的页签
function CreateNewTabWin(url, name, id) {
    if (typeof (name) == "undefined")
        name = "";
    win_TabMenu.OpenTabWin("01", name, url, id);
}
//关闭指定的页签
function CloseTabWin(tabid) {
    var parentWindow;
    var parentID;
    if (typeof (tabid) == "undefined") {
        //关闭当前显示的页签
        win_TabMenu.CloseTabWin(win_TabMenu.CurTab.tabid);
    }
    else {
        parentWindow = ParentTab.ParentWindow(tabid);
        parentID = ParentTab.ParentID(tabid);
        //关闭指定的页签
        win_TabMenu.CloseTabWin(tabid);
    }
    HidePopupWin();
    //单独处理从待办页面打开的流程,关闭流程审批页面时刷新父页面
  //  if (parentWindow != null && parentWindow.location.href.indexOf("JHSoft.Web.WorkFlat/PendingWorkSort") > 0) {
   //     parentWindow.zwxJQ("#btnSearch").click();
   // }
}
//关闭所有页签
function CloseAllTabWin(tabid) {
    zwxJQ("#c6ui-tabs-list li").each(function() {
        var node = zwxJQ(this);
        var id = node.attr("id");
        if (id != tabid) {
            win_TabMenu.CloseTabWin(id);
            HidePopupWin();
        }
    });
}
//刷新指定页签
function RefreshTabWin(tabid) {
    var node = zwxJQ(win_TabMenu.xmlTabBuffer).find("root>tabitem[tabid='" + tabid + "']");
    if (node.length > 0) {
        win_TabMenu.xmlTabBuffer.documentElement.removeChild(node[0]);
        win_TabMenu.xmlTabBuffer.documentElement.appendChild(node[0]);
        var frame = node.attr("frameid");
        zwxJQ("#" + frame)[0].contentWindow.location.href = zwxJQ("#" + frame).attr('src');
        zwxJQ("#" + frame).css("position","");
        zwxJQ("#" + frame).css("z-index","");
    }
    HidePopupWin();
}

//隐藏所有弹出层
function HidePopupWin() {
    if (win_MainMenu.isShow) win_MainMenu.hideanimate();
    if (win_SubMenu.isShow) win_SubMenu.hide();
    if (win_ContextMenu.isShow) win_ContextMenu.hide();
   
}
