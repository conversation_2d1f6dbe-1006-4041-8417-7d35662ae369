zwxJQ(function() {
    //隐藏帮助视图
    function helpHide() {
        zwxJQ("#helpCon").hide();
        zwxJQ("#help-content").attr("class", "help-1");
    }
    //显示帮助视图
    function helpShow() {
        zwxJQ("#helpCon").show(300);
    }
    //处理帮助序列
    zwxJQ("#help-content").click(function() {
        var zwxJQthis = zwxJQ(this);
        var zwxJQattr = zwxJQthis.attr("class");
        var i = parseInt(zwxJQattr.slice(5), 10);
        if (i < 5) {
            i++;
            if(i!=5){
            zwxJQthis.fadeOut(300, function() {
                zwxJQthis.attr("class", "help-" + i).fadeIn(300);
            });
            }else{
                helpHide();
            }
        } else {
            helpHide();
        }
    });
    //跳过
    zwxJQ("#help-skip").click(function() {
        helpHide();
    });
    //激活帮助视图
    zwxJQ("#showHelp").click(function() {
        helpShow();
        //checkPermission();
    });

});

//网页通知（only Chrome）
window.Notifications = window.Notifications || window.webkitNotifications;

function showNotifications(icon, title, message, timer) {
    //参数：图标路径(32*32),标题,详细信息,延时关闭(毫秒)
    var newNotification = Notifications.createNotification(icon, title, message);
    newNotification.ondisplay = function() {
        var temp = this;
        var fn = function() { temp.cancel(); };
        timer = timer || 3000;
        window.setTimeout(fn, timer);
    }
    newNotification.show();
}

function checkPermission() {
    //开启网页通知的权限
    var zwxJQinfo = zwxJQ("<div class='notificationInfo'>点击'允许'可以通过桌面通知获取更多的实时帮助！</div>");
    if (window.Notifications) {
        var permissionLevel = window.Notifications.checkPermission();
        if (permissionLevel == 0) {
            zwxJQ(".notificationInfo").remove();
        } else if (permissionLevel == 1) {
            window.Notifications.requestPermission(checkPermission);
            zwxJQ("body").append(zwxJQinfo);
        } else {
            //世界清静了！！
        }
    }
}