/*
*描    述: widget accordion
*/
(function(zwxJQ) {
    zwxJQ.fn.accordion = function(options) {
        var defaults = {}
        var options = zwxJQ.extend(defaults, options);
        this.each(function() {
            var jQuerythis = zwxJQ(this);
            jQuerythis.find(".con").hide().eq(0).show();
            jQuerythis.find(".item").eq(0).addClass("open");
            function getH() {
                var jQueryh = jQuerythis.height() - 33;
                jQuerythis.find(".accordionCon").height(jQueryh);
            }
            getH();
            zwxJQ(window).resize(getH);
            jQuerythis.click(function(event) {
                var jQuerytarget = zwxJQ(event.target);
                if (jQuerytarget.is("h2.item span") && jQuerytarget.is("h2.open span")) {
                    jQuerytarget.parent().removeClass("open").next().hide("blind", 100);
                } else if (jQuerytarget.is("h2.item span")) {
                    jQuerytarget.parent().addClass("open").next().show("blind", 100);
                }
            });

        });
    };
})(zwxJQ);
/*
*描    述: widget minSearch
*/
(function(zwxJQ) {
    zwxJQ.fn.minSearch = function(options) {
        //各种属性、参数 
        var defaults = {
            btnClick: function() { },
            onFocus: function() { },
            onBlur: function() { },
            width: "100%"
        }
        var options = zwxJQ.extend(defaults, options);
        this.each(function() {
            //插件实现代码 
            var jQuerythis = zwxJQ(this);
            var jQuerywidgetBox = zwxJQ("<div class='c6ui-widget-minSearch' style='width:" + options.width + ";'></div>");
            var jQuerybtn = zwxJQ("<a href='javascript:void(0)' class='minSearchBtn'></a>");
            var jQueryminSearchInput = zwxJQ("<div class='minSearchInput'></div>");
            var jQueryonClick = options.btnClick;
            var jQueryonFocus = options.onFocus;
            var jQueryonBlur = options.onBlur;
            jQuerythis.wrap(jQuerywidgetBox).after(jQuerybtn).wrap(jQueryminSearchInput).focus(jQueryonFocus).blur(jQueryonBlur).removeAttr("class");
            jQuerybtn.click(jQueryonClick);
        });
    };
})(zwxJQ);
/*
*描    述: widget public Search
*/
(function(zwxJQ) {
    zwxJQ.fn.publicSearch = function(options) {
        //各种属性、参数 
        var defaults = {
            width: "100%",
            btnClick: function() { },
            onFocus: function() { },
            onBlur: function() { },
            searchIt: function() { }
        }
        var options = zwxJQ.extend(defaults, options);
        this.each(function() {
            //插件实现代码 
            var jQuerythis = zwxJQ(this);
            var jQueryonClick = options.btnClick;
            var jQueryonFocus = options.onFocus;
            var jQueryonBlur = options.onBlur;
            var jQuerysearchIt = options.searchIt;
            jQuerythis.removeAttr("style");
            var jQueryleftSelect = zwxJQ('<a href="javascript:void(0)" class="leftSelect"></a>');
            var jQueryrightSearch = zwxJQ('<a href="javascript:void(0)" class="rightSearch" >搜索</a>');
            jQuerythis.wrap('<div class="c6ui_public_search"></div>')
			.after(jQueryrightSearch)
			.before(jQueryleftSelect);
            jQuerythis.focus(jQueryonFocus).blur(jQueryonBlur);
            jQueryleftSelect.click(jQueryonClick);
            jQueryrightSearch.click(jQuerysearchIt);
            lastWidth();
            zwxJQ(window).resize(function() { lastWidth() });
            function lastWidth() {
                var percent = "%";
                var px = "px";
                var ispercent = options.width.indexOf(percent);
                var ispx = options.width.indexOf(px);
                if (ispercent != -1) {
                    zwxJQ(".c6ui_public_search").css("width", options.width);
                    jQuerythis.width(zwxJQ(".c6ui_public_search").width() - 70);
                } else if (ispx != -1) {
                    zwxJQ(".c6ui_public_search").css("width", parseInt(options.width));
                    jQuerythis.width(parseInt(options.width) - 69);
                } else {
                    return false;
                }
            };
        })
    };
})(zwxJQ);


zwxJQ("head").append("<meta name='viewport' content='width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1.4, user-scalable=yes'>");


zwxJQ(function() {
    /*---------------------------Document Ready Begin-------------------------------------*/
    /*
    *描    述: set main height function
    */
    function setLayout() {
        var winHeight = zwxJQ(window).height();
        var headerHeight = zwxJQ("#c6ui-header").height();
//        var subMenuHeight = zwxJQ("#c6ui-header-subpage").height();
        var H = headerHeight;
        var mainHeight;
        if (winHeight > H) {
            var regexiPad = new RegExp(/iPad/); //创建iPad标识
            //判断是否是iPad
            if (regexiPad.exec(navigator.userAgent) || navigator.userAgent.match(/Android/i)) {
                mainHeight = null;
                zwxJQ("#c6ui-bottomSide").css("position", "relative");
                zwxJQ("#close-footer").hide();
            } else {
                mainHeight = winHeight - H;
            }
        } else {
            mainHeight = 300;
        }
        zwxJQ("#c6ui-main").height(mainHeight - 20);
    }

    //window set run
    setLayout();
    zwxJQ(window).resize(setLayout);


    /*
    *描    述: set header height function
    *关联文件: 
    */
    zwxJQ("#c6ui-header-handle").toggle(function() {
        zwxJQ("#c6ui-header-extend").slideUp(200, setLayout);
        zwxJQ(this).find("img").hide().eq(1).show();
    }, function() {
        zwxJQ("#c6ui-header-extend").slideDown(200, setLayout);
        zwxJQ(this).find("img").hide().eq(0).show();
    });
    /*
    *描    述: set tab width function
    */
    function setTab() {
        var headerMin = zwxJQ("#c6ui-header-min").width();
        zwxJQ("#c6ui-header-min-tab").width(headerMin - zwxJQ("#c6ui-header-min-widget").width() - 140);
    }

    zwxJQ("#c6ui-tabs-list li").live("click", function() {
        zwxJQ("#c6ui-tabs-list li.tab_hover").removeAttr("class");
        zwxJQ(this).addClass("tab_hover");
    });
    //set tab run
    setTab();
    zwxJQ(window).resize(setTab);





    /*
    *描    述: 快捷菜单
    */

    zwxJQ("#turn_off").live('click', function() {
        zwxJQ(".change_style").fadeOut("fast");
        zwxJQ(this).hide();
        zwxJQ("#turn_on").show();
    });
    zwxJQ("#turn_on").live('click', function() {
        zwxJQ(".change_style").fadeIn("fast");
        zwxJQ(this).hide();
        zwxJQ("#turn_off").show();
    });
    var imgPath = "../JHsoft.UI.Lib/images/";
    //滚动按钮的状态



    var scrollBtnStatus = function() {
        var status;
        var animated = zwxJQ(".quick_menu_list ul").is(":animated");
        var currentLeft = zwxJQ(".quick_menu_list ul").css("margin-left");
        var liNum = zwxJQ(".quick_menu_list ul li").length;
        var liWidth = zwxJQ(".quick_menu_list ul li:first").outerWidth();
        var otherWidth = liWidth * (liNum - 6) + parseInt(currentLeft);
        if (parseInt(currentLeft) == 0 && otherWidth > 0) {
            status = "leftUnable"; 	//左侧按钮不可用，右侧按钮可以点击
        } else if (parseInt(currentLeft) == 0 && otherWidth <= 0) {
            status = "allNo"; 		//左右两侧按钮都不可以使用
        } else if (parseInt(currentLeft) != 0 && otherWidth <= 0) {
            status = "rightUnable"; 	//左侧按钮可以点击，右侧按钮不可用
        } else {
            status = "allCan"; 		//左右两侧按钮都可以使用



        };
        switch (status) {
            case "leftUnable":
                zwxJQ(".change_style .left_btn a img").attr("src", "" + imgPath + "footer_arrow_left_no.png");
                zwxJQ(".change_style .right_btn a img").attr("src", "" + imgPath + "footer_arrow_right.png");
                break;
            case "allNo":
                zwxJQ(".change_style .left_btn a img").attr("src", "" + imgPath + "footer_arrow_left_no.png");
                zwxJQ(".change_style .right_btn a img").attr("src", "" + imgPath + "footer_arrow_right_no.png");
                break;
            case "rightUnable":
                zwxJQ(".change_style .left_btn a img").attr("src", "" + imgPath + "footer_arrow_left.png");
                zwxJQ(".change_style .right_btn a img").attr("src", "" + imgPath + "footer_arrow_right_no.png");
                break;
            case "allCan":
                zwxJQ(".change_style .left_btn a img").attr("src", "" + imgPath + "footer_arrow_left.png");
                zwxJQ(".change_style .right_btn a img").attr("src", "" + imgPath + "footer_arrow_right.png");
                break;
        }
    }
    /*快捷菜单的滚动操作*/
    zwxJQ(".change_style .left_btn a img").live('click', function() {
        var animated = zwxJQ(".quick_menu_list ul").is(":animated");
        var currentLeft = zwxJQ(".quick_menu_list ul").css("margin-left");
        var liNum = zwxJQ(".quick_menu_list ul li").length;
        var liWidth = zwxJQ(".quick_menu_list ul li:first").outerWidth();
        var otherWidth = liWidth * (liNum - 6) + parseInt(currentLeft);
        if (parseInt(currentLeft) != 0 && !animated) {
            zwxJQ(".quick_menu_list ul").animate({ marginLeft: "+=" + liWidth * 6 + "px" }, 1000, scrollBtnStatus);
        } else {
            zwxJQ(this).fadeOut("fast").fadeIn("fast");
        };
    });
    zwxJQ(".change_style .right_btn a img").live('click', function() {
        var animated = zwxJQ(".quick_menu_list ul").is(":animated");
        var currentLeft = zwxJQ(".quick_menu_list ul").css("margin-left");
        var liNum = zwxJQ(".quick_menu_list ul li").length;
        var liWidth = zwxJQ(".quick_menu_list ul li:first").outerWidth();
        var otherWidth = liWidth * (liNum - 6) + parseInt(currentLeft);
        if (otherWidth > 0 && !animated) {
            zwxJQ(".quick_menu_list ul").animate({ marginLeft: "-=" + liWidth * 6 + "px" }, 1000, scrollBtnStatus);
            scrollBtnStatus();
        } else {
            zwxJQ(this).fadeOut("fast").fadeIn("fast");
        };
    });
    //最外层背景色

    //阻止内层滑动层的背景变色
    zwxJQ(".quick_menu_list").bind({
        mouseenter: function() {
            zwxJQ("#c6ui-quick-menu").removeClass('change_style1');
            return false;
        },
        mouseleave: function(event) {
            zwxJQ("#c6ui-quick-menu").addClass('change_style1');
            return false;
        },
        click: function() {
            return false;
        }
    });
    /*当窗口改变时重新设置快捷方式的整体宽度*/
    function reloadWidth() {
        var currentWidth = document.body.clientWidth;
        if (currentWidth < 1024) {
            zwxJQ(".change_style").removeAttr("id").attr("id", "c6ui-quick-menu-short");
        } else {
            zwxJQ(".change_style").removeAttr("id").attr("id", "c6ui-quick-menu");
        }
    };
    reloadWidth();
    zwxJQ(".quick_menu_list ul").animate({ marginLeft: "-=0" + "px" }, 1000, scrollBtnStatus);
    zwxJQ(window).resize(function() {
        reloadWidth();
    });

    /*
    *描    述: Tab active
    */

    zwxJQ(".c6ui-base-minTab,.c6ui-base-minTab-portrait,.c6ui-base-Tab-portrait-left,.c6ui-base-Tab-portrait-right,.c6ui-base-Tab-top,.c6ui-base-Tab-bottom").live("click", function(event) {
        var jQuerytarget = zwxJQ(event.target);
        var jQuerythis = zwxJQ(this);
        if (jQuerytarget.is("li")) {
            jQuerythis.find("li").removeClass("active");
            jQuerytarget.addClass("active");
        } else if (jQuerytarget.is("li span")) {
            jQuerythis.find("li").removeClass("active");
            jQuerytarget.parent("li").addClass("active");
        }
    });



    /*------------------------------------------预览-----------------------------------------------------*/
    //plus btn
    zwxJQ(".c6ui-widget-btn").each(function() {

        zwxJQ(this).hover(function() {
            zwxJQ(this).find(".widget-btn-left").addClass("widget-btn-left-h");
            zwxJQ(this).find(".widget-btn-plus").addClass("widget-btn-plus-h");
        }, function() {
            zwxJQ(this).find(".widget-btn-left").removeClass("widget-btn-left-h").removeClass("widget-btn-left-c");
            zwxJQ(this).find(".widget-btn-plus").removeClass("widget-btn-plus-h").removeClass("widget-btn-plus-c");
        }).mousedown(function(e) {
            if (zwxJQ(e.target).is(".widget-btn-plus") || zwxJQ(e.target).is(".widget-btn-plus > img")) {
                zwxJQ(this).find(".widget-btn-plus").addClass("widget-btn-plus-c");
            } else {
                zwxJQ(this).find(".widget-btn-left").addClass("widget-btn-left-c");
            }
            //alert(e.target.);
        }).mouseup(function(e) {
            if (zwxJQ(e.target).is(".widget-btn-plus") || zwxJQ(e.target).is(".widget-btn-plus > img")) {
                zwxJQ(this).find(".widget-btn-plus").removeClass("widget-btn-plus-c");
            } else {
                zwxJQ(this).find(".widget-btn-left").removeClass("widget-btn-left-c");
            }
        });
    });
    //widget btn
    zwxJQ(".c6ui-widget-btn-single").each(function() {
        zwxJQ(this).hover(function() {
            zwxJQ(this).addClass("c6ui-widget-btn-single-h");
        }, function() {
            zwxJQ(this).removeClass("c6ui-widget-btn-single-h");
        }).mousedown(function() {
            zwxJQ(this).addClass("c6ui-widget-btn-single-c");
        }).mouseup(function() {
            zwxJQ(this).removeClass("c6ui-widget-btn-single-c");
        });
    });
    zwxJQ(".c6ui-header-handle").hover(function() {
        zwxJQ(this).addClass("c6ui-header-handle-h");
    }, function() {
        zwxJQ(this).removeClass("c6ui-header-handle-h");
    }).mousedown(function() {
        zwxJQ(this).removeClass("c6ui-header-handle-h").addClass("c6ui-header-handle-c");
    }).mouseup(function() {
        zwxJQ(this).removeClass("c6ui-header-handle-c").addClass("c6ui-header-handle-h");
    });


    /*---------------------------Document Ready End-------------------------------------*/
});


/*
*描    述: set iframe height
*/
function initalTab() {
    zwxJQ(".c6ui-base-Tab-top").hide().show();
    zwxJQ(".c6ui-base-Tab-top").each(function(i) {
        var outerDivWidth = parseInt(zwxJQ(this).width());     //最外层div的宽度
        var jQueryul = zwxJQ(this).children("ul");
        var allLiWidth = liWidth(jQueryul);
        (outerDivWidth - allLiWidth) > 11 ? removeArrow(zwxJQ(this)) : addArrow(zwxJQ(this));
        var jQueryleftArrow = jQueryul.children("li.left_arrow");
        var jQueryrightArrow = jQueryul.children("li.right_arrow");
        var marginLength = parseInt(jQueryul.css("marginLeft"));
        var distance = allLiWidth + marginLength;   //ul的margin-left的值
        arrowBtnStyle(jQueryleftArrow, jQueryrightArrow, marginLength, zwxJQ(this).width(), distance);
    });
};
/*temp code*/
zwxJQ("ul li.left_arrow").live("click", function() {
    var moveLength = 100;
    if (parseInt(zwxJQ(this).parent().css("marginLeft")) > -100) {
        moveLength = parseInt(zwxJQ(this).parent().css("marginLeft"));
    };
    if (isAnimate(zwxJQ(this).parent())) {
        if (zwxJQ(this).children("img").hasClass("left_able")) {
            zwxJQ(this).parent("ul").animate({ marginLeft: "+=" + moveLength + "px" }, { duration: 500 }, setTimeout(initalTab, 501));
        }
    };
});
zwxJQ("ul li.right_arrow").live("click", function() {
    if (isAnimate(zwxJQ(this).parent()) && zwxJQ(this).children("img").hasClass("right_able")) {
    	zwxJQ(this).parent("ul").animate({ marginLeft: "-=" + 100 + "px" }, { duration: 500 }, setTimeout(initalTab, 501));
    };
});
/*make sure is animate obj,'true' is not in animating*/
function isAnimate(obj) {
    return obj.is(":animated") ? false : true;
}
function arrowBtnStyle(leftArrow, rightArrow, marginLeftWidth, thisWidth, distance) {
    if (marginLeftWidth < 0) {
        leftArrow.children("img").removeAttr("class").addClass("left_able");
    } else {
        leftArrow.children("img").removeAttr("class").addClass("left_unable");
    };
    if (distance + 11 > thisWidth) {
        rightArrow.children("img").removeAttr("class").addClass("right_able");
    } else {
        rightArrow.children("img").removeAttr("class").addClass("right_unable");
    };
};
/*add left and right arrows code for current 'ul' */
function addArrow(obj) {
    if (obj.hasClass('add_arrow')) {
        return false;
    } else {
        obj.addClass('add_arrow').children('ul').prepend('<li class="left_arrow"><img src="../JHSoft.UI.Lib/images/blank.gif"/></li><li class="right_arrow"><img src="../JHSoft.UI.Lib/images/blank.gif"/></li>')
    }
};
function removeArrow(obj) {
    if (obj.hasClass('add_arrow')) {
        obj.removeClass('add_arrow');
        obj.children('ul').children("li[class*='_arrow']").remove();
    } else {
        return false;
    }
};
/*argument(obj) is the 'ul' which you want to calculate the content(li) width*/
function liWidth(obj) {
    var liVisible = obj.children("li:visible");
    var temp = 0;
    for (i = 0; i < liVisible.length; i++) {
        temp += parseInt(liVisible.eq(i).outerWidth(true));
    }
    return temp;
}
zwxJQ(window).resize(function() {
    /*添加判断，解决e人e本加载死机问题*/
    if (!IsAndroid()) {
        setTimeout(initalTab, 100);
    }
    zwxJQ(".c6ui-base-Tab-top").hide().show();
});
function checkListener() {
    function aa() {
        zwxJQ(":checkbox.c6ui-check,:radio.c6ui-check,span.c6ui-check :checkbox,span.c6ui-check :radio").check("destroy").check();
    }
    setInterval(aa, 1000);
}