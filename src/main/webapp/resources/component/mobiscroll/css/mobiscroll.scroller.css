.dwwb,
.dwwo,
.dwwol {
    -webkit-transform: translateZ(0);
}
/* Wheel container wrapper */

.dwc {
    max-width: 100%;
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
/* Wheel label */

.dwl {
    line-height: 30px;
    height: 30px;
    top: -30px;
    left: 0;
    text-align: center;
    white-space: nowrap;
    position: absolute;
    width: 100%;
}
/* Wheel container */

.dwwc {
    margin: 0 auto;
    position: relative;
    zoom: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}

.dwfl {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 auto;
    -ms-flex: 1 auto;
    flex: 1 auto;
    -ms-touch-action: none;
    touch-action: none;
}
/* Wheels */

.dwwl {
    position: relative;
    z-index: 5;
}

.dwww {
    position: relative;
    padding: 1px;
}

.dww {
    overflow: hidden;
    position: relative;
    /* Forces IE to respect overflow hidden while animating */
    
    border-radius: 1px;
    /* Fixes Firefox rendering issues */
    
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
}

.dw-bf {
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000px;
    backface-visibility: hidden;
    perspective: 1000px;
}

.dw-ul {
    position: relative;
    z-index: 3;
}

.dw-li {
    padding: 0 5px;
    position: relative;
    text-align: center;
    white-space: nowrap;
    vertical-align: bottom;
    opacity: .3;
    filter: Alpha(Opacity=30);
    cursor: pointer;
    -webkit-transition: opacity .2s ease-out;
    -moz-transition: opacity .2s ease-out;
    transition: opacity .2s ease-out;
}
/* Valid entry */

.dw-li.dw-v,
.dw-li.dw-fv {
    opacity: 1;
    filter: Alpha(Opacity=100);
}
/* Hidden entry */

.dw-li.dw-h {
    visibility: hidden;
}

.dw-i {
    position: relative;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* Clickpick mode */

.dwwb {
    position: absolute;
    z-index: 4;
    left: 0;
    cursor: pointer;
    width: 100%;
    text-decoration: none;
    text-align: center;
    opacity: 1;
    -webkit-transition: opacity .2s linear;
    transition: opacity .2s linear;
}

.dwa .dwwb {
    opacity: 0;
}

.dwpm .dwwbp {
    top: 0;
}

.dwpm .dwwbm {
    bottom: 0;
}

.dwpm .dwwol {
    display: none;
}
/* Wheel overlay */

.dwwo {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
/* Background line */

.dwwol {
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 0;
    width: 100%;
    pointer-events: none;
}
/* Liquid mode */

.dw-liq .dwc {
    display: block;
}

.dw-liq .dw-tbl {
    width: 100%;
    table-layout: fixed;
}
/* Hidden label */

.dwhl .dwl {
    display: none;
}
/* Hidden select element */

.dw-hsel {
    position: absolute;
    height: 1px !important;
    width: 1px !important;
    left: 0;
    overflow: hidden;
    clip: rect(1px, 1px, 1px, 1px);
}
/* Multiple lines */

.dw-ml .dw-li {
    overflow: hidden;
}

.dw-ml .dw-li .dw-i {
    width: 100%;
    height: auto;
    display: inline-block;
    vertical-align: middle;
    white-space: normal;
}
/* Multiple selection */

.dwwms .dw-li {
    padding: 0 40px;
}

.dwwms .dwwol {
    display: none;
}

.dw-msel:before {
    width: 40px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
}
/* Select groups */

.dww .dw-w-gr {
    padding: 0 5px;
    opacity: 1;
    font-weight: bold;
    text-align: left;
}
/* Default theme */

.mbsc-mobiscroll .dwc {
    padding: 2em .25em 0 .25em;
}

.mbsc-mobiscroll .dwl {
    color: #4eccc4;
    font-size: 20px;
    text-transform: uppercase;
}

.mbsc-mobiscroll .dwhl {
    padding-top: 0;
}

.mbsc-mobiscroll .dwfl {
    padding: .5em .25em;
}

.mbsc-mobiscroll .dw-li {
    font-size: 1.375em;
}

.mbsc-mobiscroll .dw-hl {
    background: rgba(78, 204, 196, .3);
}

.mbsc-mobiscroll .dwwol {
    border-top: 1px solid #4eccc4;
    border-bottom: 1px solid #4eccc4;
}
/* Clickpick mode */

.mbsc-mobiscroll .dwpm .dwwol {
    display: block;
}

.mbsc-mobiscroll .dwwb {
    color: #4eccc4;
    background: #f7f7f7;
}

.mbsc-mobiscroll .dwwbp {
    bottom: 0;
    top: auto;
}

.mbsc-mobiscroll .dwwbm {
    top: 0;
    bottom: auto;
}

.mbsc-mobiscroll .dwwb span {
    display: none;
}

.mbsc-mobiscroll .dwwb:before {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    font-size: 24px;
    text-align: center;
}

.mbsc-mobiscroll .dwwb.dwb-a:before {
    background: rgba(78, 204, 196, .3);
}
/* Group select */

.mbsc-mobiscroll .dw-w-gr {
    font-size: 1.125em;
}
/* Multiple select */

.mbsc-mobiscroll .dw-msel:before {
    font-size: 40px;
    color: #4eccc4;
}

.mbsc-mobiscroll .dwwms .dwwol {
    display: none;
}
