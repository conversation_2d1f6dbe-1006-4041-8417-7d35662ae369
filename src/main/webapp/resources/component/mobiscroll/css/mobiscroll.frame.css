.dw,
.dwo {
	-webkit-font-smoothing: antialiased;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.mbsc-fr-lock {
	-ms-touch-action: none;
	touch-action: none;
}

.dw {
	max-width: 98%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
	font-size: 12px;
	text-shadow: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-ms-touch-action: pan-y;
	touch-action: pan-y;
}

.dw:focus {
	outline: none;
}

.dw:focus {
	outline-offset: -2px;
}

.dw-rtl {
	direction: rtl;
}


/* Box sizing */

.dw,
.dwbc {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.dwwr {
	min-width: 200px;
	zoom: 1;
	overflow: hidden;
	text-align: center;
	font-family: arial, verdana, sans-serif;
}


/* Modal overlay */

.dw-persp,
.dwo {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.dw-persp {
	z-index: 99998;
	pointer-events: auto;
}

.dwo {
	z-index: 1;
	background: #000;
	background: rgba(0, 0, 0, .7);
	filter: Alpha(Opacity=70);
}


/* Liquid mode */

.dw-liq .dw {
	max-width: 100%;
}


/* Top/Bottom mode */

.dw-top .dw,
.dw-bottom .dw {
	width: 100%;
	max-width: 100%;
}


/* Inline mode */

.dw-inline .dw {
	position: static;
	display: inline-block;
	max-width: 100%;
}

.dw-inline.dw-liq .dw-persp .dw {
	display: block;
}

.dw-inline .dw-persp {
	position: static;
}


/* Bubble mode */

.dw-bubble .dw {
	margin: 20px 0;
}

.dw-bubble .dw-arrw {
	position: absolute;
	z-index: 1;
	left: 0;
	width: 100%;
}

.dw-bubble-top .dw-arrw {
	bottom: -36px;
}

.dw-bubble-bottom .dw-arrw {
	top: -36px;
}

.dw-bubble .dw-arrw-i {
	margin: 0 30px;
	position: relative;
	height: 36px;
}

.dw-bubble .dw-arr {
	display: block;
}

.dw-arr {
	display: none;
	position: absolute;
	left: 0;
	width: 0;
	height: 0;
	border-width: 18px 18px;
	border-style: solid;
	margin-left: -18px;
}

.dw-bubble-bottom .dw-arr {
	top: 0;
}

.dw-bubble-top .dw-arr {
	bottom: 0;
}

.dw-hidden {
	width: 0;
	height: 0;
	margin: 0;
	padding: 0;
	border: 0;
	overflow: hidden;
}


/* Header */

.dwv {
	overflow: hidden;
	text-overflow: ellipsis;
}


/* Buttons */

.dwb {
	overflow: hidden;
	display: block;
	text-decoration: none;
	white-space: nowrap;
	text-overflow: ellipsis;
	vertical-align: top;
}

.dwb-e {
	cursor: pointer;
}

.dwb-d {
	cursor: default;
}


/* Button container */

.dwbc {
	display: table;
	width: 100%;
	text-align: center;
}

.dwbc .dwb-d {
	opacity: .3;
}


/* Button wrapper */

.dwbw {
	vertical-align: top;
	display: table-cell;
	position: relative;
	z-index: 5;
}

.dwbw .dwb:before {
	padding: .375em;
}


/* Widget content styling */

.mbsc-wdg .mbsc-wdg-c {
	position: relative;
	z-index: 0;
	padding: 1em;
	font-size: 14px;
	text-align: left;
	white-space: normal;
}


/* Default theme */

.mbsc-mobiscroll .dwwr {
	min-width: 16em;
	background: #f7f7f7;
	color: #454545;
	font-size: 16px;
}

.mbsc-mobiscroll .dwv {
	padding: 0 .6666em;
	padding-top: .6666em;
	color: #4eccc4;
	font-size: .75em;
	text-transform: uppercase;
	min-height: 2em;
	line-height: 2em;
}

.mbsc-mobiscroll .dwbc {
	display: block;
	overflow: hidden;
	text-align: right;
	padding: 0 .5em .5em .5em;
}

.mbsc-mobiscroll .dwbw {
	display: block;
	float: right;
}

.mbsc-mobiscroll .dw-rtl .dwbw {
	float: left;
	width: 50%;
	position: relative;
}

.mbsc-mobiscroll .dwb {
	height: 2.5em;
	line-height: 2.5em;
	padding: 0 1em;
	text-align: center;
	color: #F7F7F7;
	background: #2695D0;
	text-transform: uppercase;
	box-sizing: border-box;
    font-size:  1.2em;
    margin: 5px;
	display: block;
	border-radius: 0.2em;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
}

.mbsc-mobiscroll .dwb-a {
	background: rgba(78, 204, 196, .3);
}

.mbsc-mobiscroll .dw-bubble-bottom .dw-arr {
	border-color: transparent transparent #f7f7f7 transparent;
}

.mbsc-mobiscroll .dw-bubble-top .dw-arr {
	border-color: #f7f7f7 transparent transparent transparent;
}