var httpUrlAddr = "http://10.88.99.202:8090";

function validator(frm) {
	var formElements = frm.elements;
	var fv = new FormValid(frm);

	for(var i = 0; i < formElements.length; i++) {
		var validType = formElements[i].getAttribute('zwx:valid');
		var errorMsg = formElements[i].getAttribute('zwx:errmsg');
		if(validType == null)
			continue;
		//滑动题不需要过滤隐藏域
		if($(formElements[i]).is(":hidden") && validType != 'requireSlider')
			continue;

		fv.addAllName(formElements[i].name);

		var vts = validType.split('|');
		var ems = errorMsg.split('|');
		for(var j = 0; j < vts.length; j++) {
			var curValidType = vts[j];
			var curErrorMsg = ems[j];

			switch(curValidType) {
				case 'isNumber':
				case 'isEmail':
				case 'isPhone':
				case 'isMobile':
				case 'isIdCard':
				case 'isMoney':
				case 'isZip':
				case 'isQQ':
				case 'isInt':
				case 'isEnglish':
				case 'isChinese':
				case 'isUrl':
				case 'isDate':
				case 'isTelphone':
				case 'isTime':
					fv.checkReg(formElements[i], RegExps[curValidType], curErrorMsg);
					break;
				case 'regexp':
					fv.checkReg(formElements[i], new RegExp(formElements[i].getAttribute('regexp'), "g"), curErrorMsg);
					break;
				case 'custom':
					if(!eval(formElements[i].getAttribute('custom') + '(formElements[i],formElements)')) {
						fv.addErrorMsg(formElements[i].name, curErrorMsg);
					}
					break;
				default:
					if(!eval('fv.' + curValidType + '(formElements[i],formElements)')) {
						fv.addErrorMsg(formElements[i].name, curErrorMsg);
					}
					break;
			}
		}
	}

	if(fv.passed() == true) {
		this.saveFormComm(1);
	}
	//跳转锚点
	var validateErrorId = $(".questionDiv_error:first").attr("id");
	scroller(validateErrorId, 100);
	return false;
}

function saveFormComm(state) {
	var ansQueId = $("#ansQueId").val();
	var dataJson = "{'state':'" + state + "','ansQueId':'" + ansQueId + "'}";

	$.ajax({
		cache: true,
		type: "POST",
		url: httpUrlAddr + "/submitPdiQAService?ph=1&&ifHtml=1&data=" + $("#urlParams").val() + "&dataJson=" + dataJson,
		data: $('#form').serialize(),
		async: false,
		error: function(request) {
			alert("网络出现异常，请重试！");
		},
		success: function(data) {
			var dataObj = $.parseJSON(data);
			//0-失败 1-成功 2-验证不通过
			if(dataObj.type == "1") {
				if(state == 1) {
					if(typeof dataObj.factorList != "undefined" && dataObj.factorList.length > 0 && typeof dataObj.sendInfo != "undefined" && "" != dataObj.sendInfo) {
						var sendInfo = dataObj.sendInfo;
						showBg(dataObj.factorList, sendInfo);
					} else {
						$("input").attr("disabled", "true");
						$("#textarea2").attr("disabled", "");
						$(".submitbutton").eq(0).hide();
						$(".submitbutton").eq(1).hide();
						$("#divMaxTime").hide();
						alert("提交成功！");
					}
				} else if(dataObj.type == "2") {
					var errors = dataObj.mess.split(";");
					var fv = new FormValid();
					for(var i = 0; i < errors.length; i++) {
						var err = errors[i].split("@");
						fv.addErrorMsg("q" + err[0], err[1]);
					}
					fv.passed();
				} else {
					console.log(dataObj.mess);
				}
			} else if(dataObj.type == "2") {
				console.log(dataObj.mess);
				var errors = dataObj.mess.split(";");
				var fv = new FormValid();
				for(var i = 0; i < errors.length; i++) {
					var err = errors[i].split("@");
					fv.addErrorMsg("q" + err[0], err[1]);
				}
				fv.passed();
				//跳转锚点
				var validateErrorId = $(".questionDiv_error:first").attr("id");
				scroller(validateErrorId, 100);
			} else {
				alert(dataObj.mess);
			}
		}
	});
}

function saveForm() {
	//移除错误提示样式
	$("fieldset > div[class*='div_question']").each(function() {
		$(this).children(".errorMessage").html("");
		$(this).removeClass("questionDiv_error");
	});
	this.saveFormComm(0);
}

/**
 * 问卷特殊的脚本，根据上次是否生病，给页面赋值
 */
$(function() {
	/**返回页面地址*/
	try {
		var fromUrl = resolveUrlParam("from");
		fromUrl = fromUrl.replace("@", "?");
		$("#fromUrl").val(fromUrl);
		//2015-9-21 xt 如果来源连接不为空，则增加返回按钮
		if('' != fromUrl) {
			$(".submitbutton").eq(1).show();
		}
	} catch(e) {}
	var noOper = false;
	//基础资料维护传参有值，则直接显示页面
	var simpleView = resolveUrlParam("simpleView");
	if(null == simpleView || '' == simpleView) {
		//详情
		var ifView = resolveUrlParam("ifView");
		if(ifView != null && ifView != '') {
			noOper = true;
		}
		var questId = resolveUrlParam("questId");
		var mainId = resolveUrlParam("mainId");
		var zoneId = resolveUrlParam("zoneId");
		var maxTime = resolveUrlParam("maxTime");
		$("#divMaxTime").attr("maxTime",maxTime);
		$("#queId").val(questId);
		$("#mainId").val(mainId);
		$("#zoneId").val(zoneId);
		if(!noOper) {
			$("#mainCss").hide();
			$("#infoDialog").show();
		} else {
			answerDetailInit(questId, ifView);
		}
	} else {
		$(".submitbutton").eq(0).hide();
		$(".submitbutton").eq(1).hide();
	}
	//加载滑动事件
	sliderInit();
	if(noOper) {
		sliderReadOnly();
	}

});

//开始答题
function begin() {
	if(beginFormValid()) {
		var queId = $("#queId").val();
		var mainId = $("#mainId").val();
		var zoneId = $("#zoneId").val();
		$.ajax({
			cache: true,
			type: "POST",
			url: httpUrlAddr + "/findPdiExamInfoService?queId=" + queId + "&mainId=" + mainId + "&zoneId=" + zoneId,
			data: $('#beginForm').serialize(),
			async: false,
			error: function(request) {
				$("#infoMess").val("网络出现异常，请重试！");
			},
			success: function(data) {
				var dataObj = $.parseJSON(data);
				$("#nowTime").val(dataObj.nowTime);
				$("#startTime").val(dataObj.startTime);
				$("#ansQueId").val(dataObj.ansQueId);
				var state = dataObj.state;
				if(state == "0") {
					answerDetailInit(dataObj.ansQueId, 0);
				} else if(state == "1") {
					$("#infoDialog").hide();
					$("#message").html("提示：您已提交过该份试卷，请勿重复作答！");
					answerDetailInit(dataObj.ansQueId, 1);
					$("#divMaxTime").hide();
					$("#mainCss").show();
					$("#msgDialog").show();
					return;
				}
				$("#mainCss").show();
				$("#infoDialog").hide();
				timerInit();
				bindSaveEvent();
			}
		});
	}
}

//题目答案变更的时候调用保存
function bindSaveEvent() {
	$("input[type=radio]").bind("click", function() {
		saveForm()
	})
	$("input[type=checkbox]").bind("click", function() {
		saveForm()
	})
	$("input[type=text]").bind("blur", function() {
		saveForm()
	})
}

function beginFormValid() {
	var valid = true;
	var psnName = $("#psnName").val();
	if(psnName == '' || psnName == undefined) {
		$("#psnNameError").html("姓名不能为空！");
		valid = false;
	}
	var psnIdc = $("#psnIdc").val();
	if(psnIdc == '' || psnIdc == undefined) {
		$("#psnIdcError").html("身份证号不能为空！");
		valid = false;
	}
	return valid;
}

function sliderReadOnly() {
	$("fieldset .slider").each(function() {
		var slider = $(this);
		var sliderId = slider.attr("id");
		//移除绑定时间
		var barId = sliderId + "_bar";
		$("#" + barId).unbind();
		//		$("#"+barId).unbind("mousedown");
		var _sliderId = sliderId + "_slider";
		$("#" + _sliderId).unbind();
		//		$("#"+_sliderId).unbind("click");
	});
}

var completeLoaded = false;

function sliderInit() {
	/**
	 * 滑动题加载Js
	 */
	$("fieldset .slider").each(function() {
		var slider = $(this);
		var minVal = slider.attr("minvalue");
		var maxVal = slider.attr("maxvalue");
		var sliderId = slider.attr("id");
		var hiddenId = sliderId.replace("divSlider", "");

		var sliderTipId = document.getElementById(slider.attr("rel"));
		var at = new neverModules.modules.slider({
			hiddenTxtId: hiddenId,
			targetId: sliderId,
			sliderCss: "imageSlider1",
			barCss: "imageBar1",
			min: parseInt(minVal),
			max: parseInt(maxVal),
			sliderValue: sliderTipId,
			hints: "拖动或点击滑动条"
		});
		at.create();

		var hidVal = $("#q" + hiddenId).val();
		if('' != hidVal) {
			at.setValue(hidVal, true);
		}
	});
	completeLoaded = true;
}

function answerDetailInit(questId, ifView) {
	$.ajax({
		cache: true,
		type: "POST",
		url: httpUrlAddr + "/findPdiQueAnsService?queAnsId=" + questId + "&ifHtml=1",
		data: null,
		async: false,
		error: function(request) {
			alert("网络出现异常，请重试！");
		},
		success: function(data) {
			var dataObj = $.parseJSON(data);
			//姓名
			$(".que_userinfo_name").html(dataObj.name);
			//性别
			$(".que_userinfo_sex").html(dataObj.sex);
			//身份证号
			$(".que_userinfo_code").html(dataObj.code);
			if(typeof dataObj.subAnsList != "undefined" && dataObj.subAnsList.length > 0) {
				for(var i = 0; i < dataObj.subAnsList.length; i++) {
					var ob = dataObj.subAnsList[i];
					//有其他填空值时，先赋值
					if(typeof ob.fillVal != "undefined") {
						var obDataType = ob.type;
						//如果为多项填空，则需要更新值
						if(obDataType == "4" || obDataType == "7" || obDataType == "9") {
							if(typeof ob.optionVal != "undefined") {
								$("#q" + ob.num + "_" + ob.optionVal).val(ob.fillVal);
							}
						} else {
							if(typeof ob.optionVal != "undefined") {
								if(typeof ob.multiNum != "undefined") {
									$("input[rel='q" + ob.num + "_" + ob.optionVal + "_" + ob.multiNum + "']").val(ob.fillVal);
								} else {
									$("input[rel='q" + ob.num + "_" + ob.optionVal + "']").val(ob.fillVal);
								}
							} else {
								$("input[rel='q" + ob.num + "']").val(ob.fillVal);
							}
						}
					} else {
						var obDataType = ob.type;
						if(obDataType == "0" || obDataType == "10") {
							$("input[name='q" + ob.num + "'][value='" + ob.value + "']").attr("checked", "checked").click();

						} else if(obDataType == "2" || obDataType == "3" || obDataType == "4" ||
							obDataType == "6" || obDataType == "7" || obDataType == "8" || obDataType == "9") {
							if(typeof ob.optionVal != "undefined") {
								$("#q" + ob.num + "_" + ob.optionVal).val(ob.value);
							} else {
								$("#q" + ob.num).val(ob.value);
							}
						} else if(obDataType == "1") {
							var val = ob.value;
							if(val != '') {
								var array = val.split(',');
								for(var j = 0; j < array.length; j++) {
									var sval = array[j];
									$("input[name='q" + ob.num + "'][value='" + sval + "']").attr("checked", "checked");
								}
							}
						}
					}
				}
			}
			if(ifView != null && ifView != '') {
				$("input").attr("disabled", "true");
				$(".submitbutton").eq(0).hide();
				$(".submitbutton").eq(1).hide();
			}
		}
	});
}

function timerInit() {
	var divMaxTime = document.getElementById("divMaxTime");
	if(divMaxTime != undefined) {
		divMaxTime.style.display="";
		var totalTime = $("#divMaxTime").attr("maxTime"); //总时间
		var spanTimeTip = document.getElementById("spanTimeTip");
		spanTimeTip.innerHTML = "剩余作答时间";
		var spanMaxTime = document.getElementById("spanMaxTime");
		var startTime = $("#startTime").val();
		var nowTime = $("#nowTime").val();
		var i = parseInt((nowTime - startTime) / 1000);
		var remain = totalTime - i;
		if(remain < 0) {
			remain = 0;
		}
		nowTime = parseInt(nowTime) + 1000;
		spanMaxTime.innerHTML = getMaxTimeStr(remain);
		maxTimer = setInterval(function() {
			var now = nowTime;
			var i = parseInt((now - startTime) / 1000);
			var remain = totalTime - i;
			nowTime = parseInt(nowTime) + 1000;
			if(remain <= 0) {
				clearInterval(maxTimer);
				divMaxTime.style.display = "none";
				$("#message").html("提示：您的作答时间已经超过最长时间限制，试卷已自动提交！");
				saveFormComm(1);
				$("#msgDialog").show();
			} else {
				spanMaxTime.innerHTML = getMaxTimeStr(remain);
			}
		}, 1000);
	}
}

function closeBg() {
	$("#fullbg,#msgDialog").hide();
}

function getMaxTimeStr(c) {
	var d = "";
	var b = c;
	var a = parseInt(b / 3600);
	if(a) {
		if(a < 10) {
			d += "0";
		}
		d += a + ":";
		b = b % 3600;
	} else {
		d = "00:";
	}
	var e = parseInt(b / 60);
	if(e) {
		if(e < 10) {
			d += "0";
		}
		d += e + ":";
		b = b % 60;
	} else {
		d += "00:";
	}
	if(b < 0) {
		b = 0;
	}
	if(b) {
		if(b < 10) {
			d += "0";
		}
		d += b;
	} else {
		d += "00";
	}
	return d;
}