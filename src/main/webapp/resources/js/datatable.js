PrimeFaces.widget.DataTable.prototype.onRowClick = function(e, d, a) {
   if ($(e.target).is("td,span:not(.ui-c)")) {
      var g = $(d)
          , c = g.hasClass("ui-state-highlight")
          , f = e.metaKey || e.ctrlKey
          , b = e.shift<PERSON>ey;
      if (c) {
         this.unselectRow(g, a)
      } else {
         if (this.isSingleSelection() || (this.isMultipleSelection() && e && !f && !b && this.cfg.rowSelectMode === "new")) {
            this.unselectAllRows()
         }
         if (this.isMultipleSelection() && e && e.shiftKey) {
            this.selectRowsInRange(g)
         } else {
            this.originRowIndex = g.index();
            this.cursorIndex = null;
            this.selectRow(g, a)
         }
      }
      PrimeFaces.clearSelection()
   }
}