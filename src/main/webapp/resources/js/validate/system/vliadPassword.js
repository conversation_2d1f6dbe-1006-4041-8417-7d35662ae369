
function baseIsVliadPassword(pws){
	if (typeof (pws) === "undefined" || pws === null || pws.length < 8 || pws.length > 16) {
		return false;
	}
	if (!(/([A-Z]+)/.test(pws) && /([a-z]+)/.test(pws) && /([0-9]+)/.test(pws) && /[\@\#\$\%\^\&\*\(\)\_\+\!\~\￥\^\(\)\=\{\}\:\;\'\"\,\<\>\|\.\【\】\—\、]/.test(pws))) {
		return false;
	}
	var arr = pws.split('');
	for (var i = 0; i < arr.length-2; i++) {
		var char1=this.isAlphanumericCheck(arr[i].charCodeAt());
		var char2=this.isAlphanumericCheck(arr[i+1].charCodeAt());
		var char3=this.isAlphanumericCheck(arr[i+2].charCodeAt());
		if (char1>0 && char2>0 && char3>0){
			//连续3位是字母或数字
			if ((char1+1==char2 && char2+1==char3) || (char1-1==char2 && char2-1==char3) || (char1==char2 && char2==char3)){
				return false;
			}
		}
		if (char1<0 && char2<0 && char3<0){
			//连续3位非字母数字
			if (char1==char2 && char2==char3){
				return false;
			}
		}
	}
	return true;
}
function isAlphanumericCheck(value) {
	if ((value>=65 && value <= 90)||(value>=97 && value <= 122)||(value>=48 && value <= 57)){
		return value;
	}
	return -value;
}

function removeSpace(pwdEle) {
	$(pwdEle).val($(pwdEle).val().replaceAll(/\s/g, ''));
}