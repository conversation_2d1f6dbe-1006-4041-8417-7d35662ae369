/**
 * @namespace 公共验证方法
 * @name com.chis.validate.system
 */
namespace.reg('com.chis.validate.system');
/**
 * 去字符串左右空格
 * 
 * @returns {string}
 * @example 调用： " aaa ".trim() 结果： "aaa"
 */
String.prototype.trim = function() {
	return this.replace(/(^\s*)|(\s*$)/g, "");
}
/**
 * 去字符串左空格
 * 
 * @returns {string}
 * @example 调用： " aaa ".trim() 结果： "aaa "
 */
String.prototype.ltrim = function() {
	return this.replace(/(^\s*)/g, "");
}
/**
 * 去字符串右空格
 * 
 * @returns {string}
 * @example 调用： " aaa ".trim() 结果： " aaa"
 */
String.prototype.rtrim = function() {
	return this.replace(/(\s*$)/g, "");
}

/**
 * 只能输入数字,用于焦点事件
 * 
 * @param obj
 *            输入框对象
 */
com.chis.validate.system.clearNoNum = function(obj) {
	if (obj.value.length == 1) {
		obj.value = obj.value.replace(/[^0-9]/g, '');
	} else {
		obj.value = obj.value.replace(/\D/g, '');
	}
};
/**
 * 只能输入正整数字,用于焦点事件
 *
 * @param obj
 *            输入框对象
 */
com.chis.validate.system.clearNoNumBig0 = function(obj) {
	if (obj.value.length == 1) {
		obj.value = obj.value.replace(/[^1-9]/g,'');
	} else {
		obj.value = obj.value.replace(/\D/g, '');
	}
};

/**
 * 非空验证
 * 
 * @param id
 *            要验证的对象id
 * @param alertText
 *            需提示的对象名称
 * @returns {boolean}
 * @example 调用 notNull('text1','测试对象')； 结果 id为'text1'的输入框，如果为空，则弹出“测试对象，不能为空！”
 */
com.chis.validate.system.notNull = function(id, alertText) {
	var obj = document.getElementById(id);
	if (obj !== null && obj.value.trim() == '') {
		alert(alertText + ",不能为空！");
		obj.focus();
		obj.select();
		return false;
	}
	return true;
};

/**
 * 日期前后的比较
 * 
 * @param startObj
 *            日期控件对象
 * @param endObj
 *            必须比startObj大的日期控件对象
 * @param clearObj
 *            验证失败后需要清空日期的控件对象
 * @param textMess
 *            用于弹出信息提示。例如：结束日期必须大于开始日期！
 * @rentun {boolean}
 * @example 一般用于日期发生改变时调用：
 *          onchange="DatecCmpare(this,document.getElementById('editFrm:define2InputDate'),this,'结束日期必须大于开始日期！')"
 *          结果：第二个参数时间值小于第一个参数时间值，则提示“结束日期必须大于开始日期！”并且第一个参数对应的控件清空
 */
com.chis.validate.system.DatecCmpare = function(startObj, endObj, clearObj,
		textMess) {
	var value1 = startObj.value;
	var value2 = endObj.value;
	if (value1 != "" && value2 != "") {
		value1 = new Date(value1.replace(/-/g, "/"));
		value2 = new Date(value2.replace(/-/g, "/"));
		if (value2.getTime() - value1.getTime() < 0) {
			alert(textMess);
			clearObj.value = "";
			clearObj.focus();
			return false;
		}
	}
	return true;
}

/**
 * 手机验证
 * 
 * @param id
 *            控件的id
 * @param error
 *            控件的名称用于弹出信息提示
 * @rentun {boolean}
 * @example 调用：checkMobile("phoneId",'手机号码')" 结果：验证不通过弹出提示“"手机号码"输入格式错误！”
 */
com.chis.validate.system.checkMobile = function(id, error) {
	var text = document.getElementById(id);
	var value = text.value;
	var temp = /^0?(13|15|18)\d{9}$/;
	if (value != "") {
		if (!temp.test(value)) {
			alert("“" + error + "”输入格式错误!");
			text.focus();
			text.select();
			return false;
		}
		return true;
	}
	return true;
}
/**
 * 邮编验证
 * 
 * @param id
 *            控件的id
 * @param error
 *            控件的名称用于弹出信息提示
 * @rentun {boolean}
 * @example 调用：checkPost("cardId",'邮编')" 结果：验证不通过弹出提示“"邮编"输入格式错误！”
 */
com.chis.validate.system.checkPost = function(id, error) {
	var text = document.getElementById(id);
	var value = text.value;
	var temp = /^[1-9]\d{5}$/;
	if (value != "") {
		if (!temp.test(value)) {
			alert("“" + error + "”输入格式错误!");
			text.focus();
			text.select();
			return false;
		}
		return true;
	}
	return true;
}

/**
 * 邮件验证
 * 
 * @param id
 *            控件的id
 * @param error
 *            控件的名称用于弹出信息提示
 * @rentun {boolean}
 * @example 调用：checkEmail("cardId",'邮件地址')" 结果：验证不通过弹出提示“"邮件地址"输入格式错误！”
 */
com.chis.validate.system.checkEmail = function(id, error) {
	var text = document.getElementById(id);
	var value = text.value;
	var temp = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
	if (value != "") {
		if (!temp.test(value)) {
			alert("“" + error + "”输入格式错误!");
			text.focus();
			text.select();
			return false;
		}
		return true;
	}
	return true;
}
/**
 * 固话验证
 * 
 * @param id
 *            控件的id
 * @param error
 *            控件的名称用于弹出信息提示
 * @rentun {boolean}
 * @example 调用：checkPhone("cardId",'固定电话')"
 *          结果：验证不通过弹出提示“"固定电话"输入格式错！格式如:051012345678或0510-12345678”
 */
com.chis.validate.system.checkPhone = function(id, error) {
	var text = document.getElementById(id);
	var value = text.value;
	var temp = /^0(([1,2]\d)|([3-9]\d{2}))[-]?\d{7,8}$/;
	if (value != "") {
		if (!temp.test(value)) {
			alert("“" + error + "”输入格式错误!格式如:051012345678或0510-12345678");
			text.focus();
			text.select();
			return false;
		}
		return true;
	}
	return true;
}

/**
 * 验证输入的整数的位数和小数的位数并且值不能为0<p>(onkeydown,onkeyup,onblur使用)
 *
 * @param obj 组件
 * @param intNum 整数位位数
 * @param decimalNum 小数位位数
 * @param isOnblur <p>onblur事件: true；<p>onkeydown,onkeyup事件: false
 */
com.chis.validate.system.verifyNum2 = function(obj, intNum, decimalNum, isOnblur) {
	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1 && decimalNum>0 ) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decimalNum);
		obj.value = values[0] + "." + values[1];
		if (isOnblur && values[1] !== ''){
			var decimal = values[1];
			decimal = decimal.replace(/^0*/,"");
			if (decimal === ''){
				obj.value = values[0]
			}
		}
	} else if (values.length === 1||decimalNum<=0) {
		values[0] = values[0].substring(0, intNum);
		if (values[0].length > 1 && values[0].substring(0,1) === '0'){
			values[0] = values[0].substring(1, intNum);
		}
		obj.value = values[0];
	}
	if (isOnblur && obj.value.endsWith('.')) {
		obj.value = values[0];
	}
	if (isOnblur && obj.value != '') {
		var v = obj.value;
		v = v.replace(/^0*/, "");
		if (v === '') {
			obj.value = '';
		} else if (!v.startsWith(".")) {
			obj.value = v;
		}
	}
}

/**
 * 验证联系方式
 * 
 * @param id
 *            控件的id
 * @param error
 *            控件的名称用于弹出信息提示
 * @rentun {boolean}
 * @example 调用：checkLinkTel("cardId",'联系方式')" 结果：验证不通过弹出提示
 *          “"联系方式"输入格式错误！格式如:051012345678或0510-12345678或者87654321或者手机号码”
 */
com.chis.validate.system.checkLinkTel = function(id, error) {
	var text = document.getElementById(id);
	var value = text.value.trim();
	var temp = /^0(([1,2]\d)|([3-9]\d{2}))[-]?\d{7,8}$/;
	var temp1 = /^0?(13|15|18)\d{9}$/;
	var temp2 = /^\d{8}$/;
	if (value != "") {
		if (temp.test(value) || temp1.test(value) || temp2.test(value)) {
			return true;
		} else {
			alert("“" + error
					+ "”输入格式错误!格式如:051012345678或0510-12345678或者87654321或者手机号码");
			text.focus();
			text.select();
			return false;
		}
		return true;
	}
	return true;
}
/**
 * 只能输入只能第一位输入数字，第二位是小数点，第三位是数字，第四位是小数点...... 比如：1.1.10.2
 */
com.chis.validate.system.clearNumDot = function(obj) {
	obj.value = obj.value.replace(/[^\d.]/g, "");
	var value = obj.value;
	var v="";
	var values = value.split(".");
	if (values.length >= 1) {
		for (var i = 0; i < values.length; i++) {
			if (SYSTEM.onlyNum(values[i])) {
				if (values[i]!="") {
					v= v + values[i]+".";
				}
			}else {
					v= v + values[i];
			}
		}
		if ( obj.value != v || v.lastIndexOf(".")) {
			v = v.substring(0,v.length-1);
			obj.value = v ;
		}
	} 
}
/**
 * 只能输入数字
 * 
 * @param obj
 *            输入框对象
 */
com.chis.validate.system.onlyNum = function(s) {
	if (s != null) {
		if (!/^[0-9]*$/.test(s) || s =="") {
			return false;
		}else {
			return true;
		}
	}
};

/**
 * 只能第一位输入数字，第二位是小数点，第三位是数字，第四位是小数点 比如：1.1.10
 */
com.chis.validate.system.clearNotNumDot = function(obj) {
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1) {
		values[0] = value.substring(0, value.indexOf("."));
		SYSTEM.clearNoNumDotByString(values[0] + ".");
		values[1] = value.substring(value.indexOf(".") + 1, value.length);
		values[1] = values[1].replace(/[^\d.]/g, "");
		values[1] = values[1].replace(/^\./g, "");
		values[1] = values[1].replace(/\.{2,}/g, ".");
		values[1] = values[1].replace(".", "$#$").replace(/\./g, "").replace(
				"$#$", ".");
		obj.value = values[0] + "." + values[1];
	} else if (values.length == 1) {
		obj.value = values[0];
		SYSTEM.clearNoNumDot(obj);
	}
}
/**
 * 只能输入数字和小数点
 * 
 * @param string
 *            输入框对象
 */
com.chis.validate.system.clearNoNumDotByString = function(s) {
	s = s.replace(/[^\d.]/g, "");
	s = s.replace(/^\./g, "");
	s = s.replace(/\.{2,}/g, ".");
	s = s.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
}
/**
 * 只能输入数字和小数点
 * 
 * @param obj
 *            输入框对象
 */
com.chis.validate.system.clearNoNumDot = function(obj) {
	obj.value = obj.value.replace(/[^\d.]/g, "");
	obj.value = obj.value.replace(/^\./g, "");
	obj.value = obj.value.replace(/\.{2,}/g, ".");
	obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$",
			".");
}
/**
 * 验证输入的整数的位数和小数的位数(onkeydown 或 onblur使用)
 * 
 * @param obj
 *            文本框对象
 * @param intNum
 *            整数的位数
 * @param decilNum
 *            小数的位数
 * @return
 * @example 调用：verifyNum(this,3,2) 结果：控制该输入框只能输入3位整数带2位小数
 */
com.chis.validate.system.verifyNum = function(obj, intNum, decilNum) {

	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decilNum);
		obj.value = values[0] + "." + values[1];
	} else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		obj.value = values[0];
	}
}

/**
 * verifyNum的升级版本
 * 验证输入的整数的位数和小数的位数(onkeydown 或 onblur使用)
 * 避免出现01、021、1.、22.的情况
 * 整数首位为0时后边只能输入小数点，失焦的时候如果以小数点结尾，那么只会获取整数部分
 * @param obj
 *            文本框对象
 * @param intNum
 *            整数的位数
 * @param decilNum
 *            小数的位数
 * @param isOnblur
 *            是否失焦事件
 * @return
 * @example 调用：verifyNum(this,3,2) 结果：控制该输入框只能输入3位整数带2位小数
 * verifyNum(this,3,0) 结果：控制输入框只能输入3位整数
 */
com.chis.validate.system.verifyNum3 = function(obj, intNum, decilNum, isOnblur) {
	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decilNum);
		obj.value = values[0] + "." + values[1];
		//无小数位 直接取整数
		if(decilNum <= 0){
			obj.value = values[0];
		}
		//去掉末尾的.
		if (isOnblur && obj.value.endsWith('.')) {
			obj.value = values[0];
		}
	} else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		obj.value = values[0];
		//避免出现类似01 021的情况，如果整数位以0开始，那么0后边只能是小数点
		if(value.startsWith("0")){
			obj.value = 0;
		}
	}
}

/**
 * 参照verifyNum3 只是verifyNum4允许整数以0开头
 * */
com.chis.validate.system.verifyNum4 = function(obj, intNum, decilNum, isOnblur) {
	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decilNum);
		obj.value = values[0] + "." + values[1];
		//无小数位 直接取整数
		if(decilNum <= 0){
			obj.value = values[0];
		}
		//去掉末尾的.
		if (isOnblur && obj.value.endsWith('.')) {
			obj.value = values[0];
		}
	}else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		obj.value = values[0];
	}
}

/**
 * 避免进入页面默认focus到文本框 光标在数字前的情况
 * */
com.chis.validate.system.numFocus = function(obj, intNum, decimalNum, isOnblur) {
	SYSTEM.verifyNum4(obj,intNum,decimalNum,isOnblur);
	if(undefined != obj){
		obj.selectionStart = obj.value.length;
		obj.selectionEnd = obj.value.length;
	}
}

/**
 * <p>方法描述：整数位最大为24</p>
 * @MethodAuthor： yzz
 * @Date：2022-03-28
 **/
com.chis.validate.system.verifyNumBy24 = function(obj, intNum, decilNum) {

	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1) {
		var result;
		values[0] = values[0].substring(0, intNum);
		if(parseInt(values[0])==24){
			result=24;
		}else{
			values[1] = values[1].substring(0, decilNum);
			result = values[0] + "." + values[1];
		}
		obj.value=result;
	} else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		if(values[0].length>1){
			if(parseInt(values[0])>24){
				values[0] = values[0].substring(0, 1);
			}
		}
		obj.value =values[0];
	}
}
/**
 * 验证输入的整数的位数和小数的位数和值的范围<p>(onkeydown,onkeyup,onblur使用)
 *
 * @param obj 组件
 * @param intNum 整数位位数
 * @param decimalNum 小数位位数
 * @param isOnblur <p>onblur事件: true；<p>onkeydown,onkeyup事件: false
 * @param minNum 最小值（包含）
 * @param maxNum 最大值 (包含)
 */
com.chis.validate.system.verifyNumRange = function(obj, intNum, decimalNum, isOnblur,minNum,maxNum) {
	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1 && decimalNum>0 ) {
		//可输入小数
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decimalNum);
		obj.value = values[0] + "." + values[1];
		if (values[1] !== ''){
			var decimal = values[1];
			decimal = decimal.replace(/^0*/,"");
			if (decimal === ''){
				obj.value = values[0]
			}
			if(parseFloat(obj.value) < parseFloat(minNum) || parseFloat(obj.value) > parseFloat(maxNum)){
				obj.value = obj.value.substring(0,obj.value.length-1);
			}
		}
	} else if (values.length === 1||decimalNum<=0) {
		//不可输入小数
		values[0] = values[0].substring(0, intNum);
		if (values[0].length > 1 && values[0].substring(0,1) === '0'){
			values[0] = values[0].substring(1, intNum);
		}
		obj.value = values[0];
		if(parseInt(obj.value) <parseInt(minNum) || parseInt(obj.value)>parseInt(maxNum)){
			obj.value = obj.value.substring(0,obj.value.length-1);
		}
	}
	if (isOnblur && obj.value.endsWith('.')) {
		obj.value = values[0];
	}
}

/**可输入负数*/
com.chis.validate.system.verifyNumMinus = function(obj, intNum, decilNum) {
	var value = obj.value;
	var minus="";
	if(value.substring(0,1)=="-"){
		minus="-";
	}
	SYSTEM.clearNoNumDot(obj);	
	var values = obj.value.split(".");
	if (values.length > 1) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decilNum);
		obj.value =values[0] + "." + values[1];
	} else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		obj.value = values[0];
	}
	obj.value=minus+obj.value;
}

/**可输入负数 onblur事件  如果只输入负号 清空*/
com.chis.validate.system.verifyNumMinus2 = function(obj, intNum, decilNum) {
	var value = obj.value;
	var minus="";
	if(value.substring(0,1)=="-"){
		minus="-";
	}
	SYSTEM.clearNoNumDot(obj);	
	var values = obj.value.split(".");
	if (values.length > 1) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, decilNum);
		if(values[1]==""){
			obj.value =values[0]
		}else{
			obj.value =values[0] + "." + values[1];
		}
	} else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		obj.value = values[0];
	}
	
	if(obj.value.length > 0){
		obj.value=minus+obj.value;
	}
}

/**可输入负数 onblur事件  如果只输入负号 清空*/
com.chis.validate.system.verifyNumMinus3 = function(obj, intNum) {
	var value = obj.value;
	var minus="";
	if(value.substring(0,1)=="-"){
		minus="-";
	}
	SYSTEM.clearNoNum(obj);

	// if(obj.value.length > 0){
		obj.value=minus+obj.value;
	// }

}

com.chis.validate.system.verifyNumMinus4 = function(obj, intNum, decilNum) {
    var value = obj.value;
    var minus="";
    if(value.substring(0,1)=="-"){
        minus="-";
    }
    SYSTEM.clearNoNumDot(obj);
    var values = obj.value.split(".");
    if (values.length > 1) {
        values[0] = values[0].substring(0, intNum);
        values[1] = values[1].substring(0, decilNum);
        obj.value =values[0] + "." + values[1];
    } else if (values.length == 1) {
        values[0] = values[0].substring(0, intNum);
        obj.value = values[0];
    }

	obj.value=minus+obj.value;

}

/**
 * 只能输入字母
 * 
 * @param obj
 *            文本框对象
 * @return
 */
com.chis.validate.system.onlyInLetter = function(obj) {
	obj.value = obj.value.replace(/[\u4e00-\u9fa5]/g, '');
}
/**
 * 只能输入字母和数字
 * 
 * @param obj
 *            文本框对象
 * @return
 */
com.chis.validate.system.onlyInLetterAndNum = function(obj) {
	obj.value = obj.value.replace(/[^A-Za-z0-9]*$@-/g, '');
}




/**
 * 只能输入字母和数字
 * 注册界面
 * @param obj
 *            文本框对象
 * @return
 */
com.chis.validate.system.onlyInLetterAndNum1 = function(obj) {
	obj.value = obj.value.replace(/[^A-Za-z0-9]*/g, '');
}
/**
 * 设置焦点
 * 
 * @param idStr
 *            控件id值
 * @return
 */
com.chis.validate.system.setfocus = function(idStr) {
	var obj = document.getElementById(idStr);
	if (null != obj) {
		obj.focus();
	}
}

/**
 * 去除汉字 方法需要用在 失焦与键盘弹起事件上，超过长度自动截取
 * 
 * @param txtObj
 *            需要控制长度的文本框
 * @createDate 2014-9-5
 * <AUTHOR>
 */
com.chis.validate.system.clearChinese = function(txtObj) {
	var val = txtObj.value;
	var returnVal = "";
	for (var ii = 0; ii < val.length; ii++) {
		var word = val.substr(ii, 1);
		if (!/[^\x00-\xff]/g.test(word)) {
			returnVal += word;
		}
	}
	if (val != returnVal) {
		txtObj.value = returnVal;
	}
}
/**
 * 去除0开头的数字
 * @param obj 输入框对象
 */
com.chis.validate.system.clearNoNumAndZeroStart=function(obj){
    if (obj.value.length == 1) {
        obj.value = obj.value.replace(/[^0-9]/g, '').replace(/^(0+)/, "");
    } else {
        obj.value = obj.value.replace(/\D/g, '').replace(/^(0+)/, "");
    }
};

/**
 * <p>方法描述：输入固定位数的正整数 去除前面多余的0</p>
 * @param obj
 * @param intNum  正整数的位数
 * @MethodAuthor： yzz
 * @Date：2021-08-13
 **/
com.chis.validate.system.verifyNumClear0=function(obj, intNum) {

	obj.value=parseInt(obj.value);
	if (obj.value.length == 1) {
		if(obj.value==0){
			obj.value = obj.value.replace(/[^0-9]/g, '');
		}else{
			obj.value = obj.value.replace(/[^0-9]/g, '').replace(/^(0+)/, "");
		}
	} else {
		obj.value = obj.value.replace(/\D/g, '').replace(/^(0+)/, "");
	}

}

/**
 * <p>方法描述：两个值比较，且只能为数字，小数的位数
 * clearNoNumAndCompareFalse(this,2,1)
 * </p>
 * @MethodAuthor qrr,2018年8月16日,init
 * */
com.chis.validate.system.clearNoNumAndCompareFalse=function(obj,compareVal,decilNum){
	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	if (parseFloat(value)>parseFloat(compareVal)) {
		obj.value = "";
	}else {
		var values = value.split(".");
		if (values.length > 1) {
			values[1] = values[1].substring(0, decilNum);
			obj.value = values[0] + "." + values[1];
		} else if (values.length == 1){
			if (value.length > 1 && /^(0+)/.test(value)) {
				obj.value = value.substring(1);
			}
		}
	}
};
/**
 * <p>方法描述：错误信息提醒
 * markErrorInfo
 * </p>
 * @MethodAuthor qrr,2020年4月28日,init
 * */
com.chis.validate.system.markErrorInfo = function(id) {
	if(id){
		var arr = id.split(",");
		$.each(arr,function(index,obj){
			var $id = $("#"+obj);
	    	if($id){console.log($id.hasClass("ui-selectmanycheckbox"));
	    		if($id.hasClass("ui-selectoneradio")){
	    			$id.find(".ui-radiobutton-box").addClass("ui-state-error");
	    		}else if($id.hasClass("ui-selectmanycheckbox")||$id.hasClass("ui-chkbox")||$id.hasClass("checkbox-padding")){
	    			$id.find(".ui-chkbox-box").addClass("ui-state-error");
	    		}else{
	    			$id.addClass("ui-state-error");
			    	if($id.hasClass("ui-selectonemenu")){
			    		$id.find(".ui-selectonemenu-trigger").addClass("ui-state-error");
			    	}
	    		}
	    	}
		});
	}
}

/**
 * 验证科学记数法 xEy 只保留0-9和E、点、负号、加号
 * @param obj
 */
com.chis.validate.system.scientificNotation = function(obj) {
	obj.value = obj.value.replace(/[^0123456789E+-\.]/g, '');
};
/**
 * @auther hsj
 * @date: 2023-04-29 11:54
 * @Description: 控制文本字节长度
 */
com.chis.validate.system.checkLength = function(obj,length) {
	var len = 0;
	var satr = 0;
	for (var i = 0; i < obj.value.length; i++) {
		var a = obj.value.charAt(i);
		if (a.match(/[^\x00-\xff]/ig) != null) {
			len += 2;
		}else {
			len += 1;
		}
		satr++;
		if(len == length){
			obj.value =obj.value.substring(0, satr);
			break;
		}
	}
	return obj.value;
};
/**
 * <p>方法描述：只能输入0以及0.5的倍数</p>
 * @param obj
 * @param intNum  正整数的位数
 * @param isOnblur  是否失焦事件
 * @MethodAuthor： hsj
 * @Date：2024-08-20
 **/
com.chis.validate.system.verifyNum5 = function(obj, intNum, isOnblur) {
	SYSTEM.clearNoNumDot(obj);
	var value = obj.value;
	var values = value.split(".");
	if (values.length > 1) {
		values[0] = values[0].substring(0, intNum);
		values[1] = values[1].substring(0, 1);
		obj.value = values[0] + "." + values[1];
		if(values[1] != '5'){
			obj.value = values[0] + ".";
		}
		//去掉末尾的.
		if (isOnblur && obj.value.endsWith('.')) {
			obj.value = values[0];
		}
	} else if (values.length == 1) {
		values[0] = values[0].substring(0, intNum);
		obj.value = values[0];
		//避免出现类似01 021的情况，如果整数位以0开始，那么0后边只能是小数点
		if(value.startsWith("0")){
			obj.value = 0;
		}
	}

}
var SYSTEM = com.chis.validate.system;
