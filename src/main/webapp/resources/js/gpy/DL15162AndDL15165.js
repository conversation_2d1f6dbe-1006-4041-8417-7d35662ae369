var _url = "wss://127.0.0.1:9443";
var websocket;
var noDrive = false;
var connected = false;
var devNumUse;
var takeMap = new Map();
var resolutionNumBig = 0;
var resolutionNumSmall = 0;

/**
 * 初始化webSocket连接
 * @param callback
 * @param value
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        websocket = new WebSocket(_url);
    } else if (window.WebSocket) {
        websocket = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        websocket = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    websocket.onopen = function (e) {
        connected = true;
        callback(value);
    }
    websocket.onclose = function (e) {
        connected = false;
    }
    websocket.onmessage = function (e) {
        onMessage(e);
    }
    websocket.onerror = function () {
        onError();
    };
}

/**
 * 接收服务器消息
 * @param e
 */
function onMessage(e) {
    //通用回调处理函数 遵循文档返回数据格式要求
    produceMessage(e.data);
}

/**
 * 向服务器发送信息的共享方法
 * @param jsonStr
 */
function sendMessage(jsonStr) {
    connected ? websocket.send(jsonStr) : ConnectServer(sendMessage, jsonStr)
}

/**
 * 处理连接错误
 */
function onError() {
    PF("InstallDialog").show();
}

/**
 * 处理接收到的信令消息
 * @param jsonObj
 */
function produceMessage(jsonObj) {
    var jv = JSON.parse(jsonObj.replace(/[\r\n]/g, ""));
    if (jv.result === 0) {
        switch (jv.func) {
            case "GetCameraInfo":
                if (jv.devInfo && jv.devInfo.length) {
                    noDrive = true;
                    devNumUse = jv.devInfo[0].id;
                    if (jv.devInfo[0].mediaTypes[0].resolutions) {
                        var resolutions = jv.devInfo[0].mediaTypes[0].resolutions;
                        for (let i = 0; i < resolutions.length; i++) {
                            if (resolutions[i] === '2592x1944') {
                                resolutionNumBig = i;
                            } else if (resolutions[i] === '1280x960') {
                                resolutionNumSmall = i;
                            }
                        }
                    }
                    CloseCamera(new Date().getTime(), devNumUse)
                    OpenCamera(new Date().getTime(), devNumUse)
                    SetCameraInfo(new Date().getTime(), devNumUse, 0, resolutionNumBig);
                    SetCameraImageInfo(new Date().getTime(), devNumUse, 6, 90);
                    //websocket方式传输视频
                    GetCameraVideoBuff(new Date().getTime(), devNumUse, "true");
                    //http方式传输视频
                    //DisplayVideoHttp();
                    console.log("连接设备成功");
                } else {
                    console.log("连接设备失败");
                    devNumUse = null;
                    alert("请检查是否插入高拍仪！");
                }
                break;
            case "CloseCamera":
                break;
            case "OpenCamera":
                break;
            case "GetCameraVideoBuff":
                DisplayVideo(jv);
                break;
            case "CameraCapture":
                savePicture(jv.reqId, jv.imgBase64[0]);
                break;
            case "CameraCaptureBase64":
                savePicture(jv.reqId, imgBase64Str);
                break;
            case "Notify":
                if (jv.event === "OnDeviceChanged") {
                    vedioInputInit();
                }
                break;
            default:
                break;
        }
    } else {
        // 统一显示错误信息
        let info = "";
        if (jv.reqId) {
            info += "回应命令ID: " + jv.reqId + ", 信令: " + jv.func;
        }
        if (jv.result) {
            if (jv.reqId) {
                info += "\n";
            }
            info += "错误代码: " + jv.result + ", 错误信息: " + jv.errorMsg;
        }
        if (jv.func === "CameraCapture" && jv.result === 11 && jv.errorMsg === "设备未开启") {
            console.log("尝试重新连接设备...");
            vedioInputInit();
            if (jv.reqId) {
                CameraCapture(jv.reqId, devNumUse);
            }
        }
        if (jv.func === "SetCameraImageInfo" && jv.errorMsg === "没有授权") {
            console.log("尝试切换设备分辨率...");
            SetCameraInfo(new Date().getTime(), devNumUse, 0, resolutionNumSmall);
        }
    }
}

/**
 * 获取设备信息
 */
function GetCameraInfo() {
    var data = JSON.stringify({func: 'GetCameraInfo', reqId: new Date().getTime()});
    sendMessage(data);
}

function SetCameraInfo(reqId, devNum, mediaNum, resolutionNum) {
    var data = JSON.stringify({
        'func': 'SetCameraInfo',
        'reqId': reqId,
        'devNum': devNum,
        'mediaNum': mediaNum,
        'resolutionNum': resolutionNum
    });
    sendMessage(data);
}

function GetCameraVideoBuff(reqId, devNum, enable) {
    var data = JSON.stringify({
        'func': 'GetCameraVideoBuff',
        'reqId': reqId,
        'devNum': devNum,
        'fps': 60,
        'mode': "base64",
        'enable': enable
    });
    sendMessage(data);
}

function OpenCamera(reqId, devNum) {
    var data = JSON.stringify({
        'func': 'OpenCamera',
        'reqId': reqId,
        'devNum': devNum,
        'fps': 60
    });
    sendMessage(data);
}

function CloseCamera(reqId, devNum) {
    var data = JSON.stringify({'func': 'CloseCamera', 'reqId': reqId, 'devNum': devNum});
    sendMessage(data);
}

function SetCameraImageInfo(reqId, devNum, cropType, rotate) {
    var data = JSON.stringify({
        'func': 'SetCameraImageInfo',
        'reqId': reqId,
        'devNum': devNum,
        'cropType': cropType,
        'rotate': rotate
    });
    sendMessage(data);
}

function CameraCapture(reqId, devNum) {
    if (devNum !== undefined && devNum !== null && devNum !== '') {
        var data = JSON.stringify({'func': 'CameraCapture', 'reqId': reqId, 'devNum': devNum, 'mode': 'base64'});
        sendMessage(data);
    } else {
        vedioInputInit();
    }
}

function CameraCaptureBase(reqId, devNum) {
    var data = JSON.stringify({'func': 'CameraCaptureBase64', 'reqId': reqId, 'devNum': devNum});
    sendMessage(data);
}

function CameraRecogBarCode(reqId, devNum) {
    var data = JSON.stringify({'func': 'RecogBarCode', 'reqId': reqId, 'devNum': devNum});
    sendMessage(data);
}

/**
 * 设备初始化
 */
function vedioInputInit() {
    GetCameraInfo();
}

/**
 * 显示视频
 * @param v
 */
function DisplayVideo(v) {
    if (v.mime && v.imgBase64Str) {
        $("#zwxVedioInput").css('width', '300px').css('height', '400px');
        document.getElementById("zwxVedioInput").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    }
}

/**
 * 显示视频
 * @param v
 */
function DisplayVideoHttp(v) {
    $("#zwxVedioInput").css('width', '300px').css('height', '400px');
    document.getElementById("zwxVedioInput").src = "https://127.0.0.1:9443/video=stream&camidx=" + devNumUse;
}

/**
 * 保存图片
 */
async function savePicture(reqId, imgBase64) {
    var aspectRatio = await getBase64ImageAspectRatio(imgBase64);
    if (aspectRatio !== undefined && aspectRatio !== null) {
        if (aspectRatio > 1) {
            imgBase64 = await rotateImgByBase64(imgBase64);
            imgBase64 = imgBase64.substring("data:image/png;base64, ".length);
        }
    }
    document.getElementById("mainForm:binaryStr").value = imgBase64;
    if (takeMap.get(reqId)) {
        savePictureAction(takeMap.get(reqId));
    } else {
        savePictureAction();
    }
}

/**
 * 拍照
 */
function vedioInputTakePhoto() {
    var reqId = new Date().getTime();
    CameraCapture(reqId, devNumUse);
}

/**
 * 插入
 */
function vedioInputInsert() {
    var selectLen = jQuery(".vedio-photo-choosen").length;
    if (selectLen === 0) {
        alert("请先选择要插入的页码！");
    } else {
        var find = jQuery("#mainForm\\:board").find("div.vedio-photo-choosen").find("img").eq(0);
        var index = jQuery("#mainForm\\:board").find("img").index(find);
        var reqId = new Date().getTime();
        takeMap.set(reqId, [{
            name: 'inx',
            value: index + 1
        }]);
        CameraCapture(reqId, devNumUse);
    }
}

/**
 * 提交
 */
function vedioInputSubmit() {
    var imgsJson = document.getElementById("mainForm:imgsJson").value;
    if (imgsJson === "" || imgsJson === "[]") {
        alert("请先拍照！");
        return;
    }
    submitPictureAction();
}

/**
 * 关闭摄像头,关闭连接
 */
function vedioInputClear() {
    if (connected) {
        websocket.close();
    }
}

/**
 * 弹出框关闭事件
 */
function diaAddCloseListener() {
    jQuery(window.parent.document).find("#dialogSelfTitle").parent().next().children("span")
        .bind("click", function () {
            vedioInputClear();
        });
}

window.onload = function () {
    //给弹出框绑定关闭事件
    setTimeout(diaAddCloseListener, 1000);
    vedioInputInit();
}
$(window).unload(function () {
    vedioInputClear();
});

function initVedioImgsEvents() {
    var closeBtn = jQuery(".img-close-icon");
    closeBtn.hide();
    jQuery(".vedio-photo").bind("click", function () {
        jQuery(".img-close-icon").hide();
        jQuery(".vedio-photo-choosen").each(function () {
            jQuery(this).removeClass("vedio-photo-choosen");
        });
        jQuery(this).addClass("vedio-photo-choosen");
        jQuery(this).parent().find(".img-close-icon").show();
    });
    closeBtn.bind("click", function () {
        var inx = jQuery(this).prev(".page").html();
        deletePictureAction([{
            name: 'inx',
            value: inx
        }]);
    });
}

/**
 * 显示隐藏的panel
 */
function showPanel() {
    jQuery("#mainForm\\:board").find("div.ui-panel").each(function () {
        if ($(this).hasClass("ui-helper-hidden")) {
            $(this).removeClass("ui-helper-hidden");
        }
    });
    var length = jQuery("#mainForm\\:board").find("img").length;
    $("#pageNum", window.parent.document).html("共" + length + "张");
    initVedioImgsEvents()
}


function getImageWidthAndHeight(base64) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = function () {
            resolve({width: this.width, height: this.height});
        };
        img.onerror = reject;
        img.src = base64;
    });
}

/**
 * 获取图片宽高比
 * @param base64Str
 * @returns {Promise<number>}
 */
async function getBase64ImageAspectRatio(base64Str) {
    const base64 = "data:image/png;base64," + base64Str;
    const {width, height} = await getImageWidthAndHeight(base64);
    return width / height;
}

function getImage(base64) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = function () {
            resolve(this);
        };
        img.onerror = reject;
        img.src = base64;
    });
}

/**
 * 图片旋转90度并压缩
 * @param base64Str
 * @returns {Promise<string>}
 */
async function rotateImgByBase64(base64Str) {
    const base64 = "data:image/png;base64," + base64Str;
    const img = await getImage(base64);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    // 旋转前，重置画布（因为旋转会改变画布的大小）
    canvas.width = img.height;
    canvas.height = img.width;
    // 将图像居中
    ctx.translate(img.height / 2, img.width / 2);
    // 旋转 90 度
    ctx.rotate(-Math.PI / 2);
    // 将图像移回原点
    ctx.translate(-img.width / 2, -img.height / 2);
    // 绘制图像
    ctx.drawImage(img, 0, 0);
    return canvas.toDataURL("image/jpeg", 0.5);
}
