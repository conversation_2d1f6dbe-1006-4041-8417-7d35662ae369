(function(a) {
	if (typeof define === "function" && define.amd) {
		define([ "jquery" ], a);
	} else {
		if (typeof exports === "object") {
			a(require("jquery"));
		} else {
			a(jQuery);
		}
	}
}(function(d) {
	function a() {
		var j = document.createElement("input");
		j.setAttribute("type", "range");
		return j.type !== "text";
	}
	var f = "rangeslider", g = [], b = a(), c = {
		polyfill : true,
		rangeClass : "rangeslider",
		disabledClass : "rangeslider--disabled",
		fillClass : "rangeslider__fill",
		handleClass : "rangeslider__handle",
		startEvent : [ "mousedown", "touchstart", "pointerdown" ],
		moveEvent : [ "mousemove", "touchmove", "pointermove" ],
		endEvent : [ "mouseup", "touchend", "pointerup" ]
	};
	function e(k, l) {
		var j = Array.prototype.slice.call(arguments, 2);
		return setTimeout(function() {
			return k.apply(null, j);
		}, l);
	}
	function i(k, j) {
		j = j || 100;
		return function() {
			if (!k.debouncing) {
				var l = Array.prototype.slice.apply(arguments);
				k.lastReturnVal = k.apply(window, l);
				k.debouncing = true;
			}
			clearTimeout(k.debounceTimeout);
			k.debounceTimeout = setTimeout(function() {
				k.debouncing = false;
			}, j);
			return k.lastReturnVal;
		};
	}
	function h(k, j) {
		this.$window = d(window);
		this.$document = d(document);
		this.$element = d(k);
		this.options = d.extend({}, c, j);
		this._defaults = c;
		this._name = f;
		this.startEvent = this.options.startEvent.join("." + f + " ") + "." + f;
		this.moveEvent = this.options.moveEvent.join("." + f + " ") + "." + f;
		this.endEvent = this.options.endEvent.join("." + f + " ") + "." + f;
		this.polyfill = this.options.polyfill;
		this.onInit = this.options.onInit;
		this.onSlide = this.options.onSlide;
		this.onSlideEnd = this.options.onSlideEnd;
		if (this.polyfill) {
			if (b) {
				return false;
			}
		}
		this.identifier = "js-" + f + "-" + (+new Date());
		this.min = parseFloat(this.$element[0].getAttribute("min") || 0);
		this.max = parseFloat(this.$element[0].getAttribute("max") || 100);
		this.value = parseFloat(this.$element[0].value || 0);
		this.step = parseFloat(this.$element[0].getAttribute("step") || 1);
		this.$fill = d('<div class="' + this.options.fillClass + '" />');
		this.$handle = d('<div class="' + this.options.handleClass + '" />');
		this.$range = d('<div class="' + this.options.rangeClass + '" id="' + this.identifier + '" />').insertAfter(this.$element).prepend(this.$fill, this.$handle);
		this.$element.css({
			position : "absolute",
			width : "40px",
			height : "20px",
			overflow : "hidden"
		});
		this.handleDown = d.proxy(this.handleDown, this);
		this.handleMove = d.proxy(this.handleMove, this);
		this.handleEnd = d.proxy(this.handleEnd, this);
		this.init();
		this.hasInit = true;
		var l = this;
		this.$document.on(this.startEvent, "#" + this.identifier + ":not(." + this.options.disabledClass + ")", this.handleDown);
		this.$element.on("change." + f, function(o, n) {
			if (n && n.origin === f) {
				return;
			}
			var m = o.target.value;
			if (parseInt(m) != m) {
				m = "";
			}
			if (m - this.max > 0) {
				m = this.max;
			}
			if (this.min - m > 0) {
				m = this.min;
			}
			var p = l.getPositionFromValue(m);
			l.setPosition(p, m);
		});
	}
	h.prototype.init = function() {
		if (this.onInit && typeof this.onInit === "function") {
			this.onInit();
		}
		this.update();
	};
	h.prototype.update = function() {
		this.handleWidth = this.$handle[0].offsetWidth;
		this.rangeWidth = this.$range[0].offsetWidth;
		this.maxHandleX = this.rangeWidth - this.handleWidth;
		this.grabX = this.handleWidth / 2;
		this.position = this.getPositionFromValue(this.value);
		if (this.$element[0].disabled) {
			this.$range.addClass(this.options.disabledClass);
		} else {
			this.$range.removeClass(this.options.disabledClass);
		}
		this.setPosition(this.position);
	};
	h.prototype.handleDown = function(j) {
		j.preventDefault();
		this.$document.on(this.moveEvent, this.handleMove);
		this.$document.on(this.endEvent, this.handleEnd);
		if ((" " + j.target.className + " ").replace(/[\n\t]/g, " ").indexOf(this.options.handleClass) > -1) {
			return;
		}
		var l = this.getRelativePosition(this.$range[0], j), k = this.getPositionFromNode(this.$handle[0]) - this.getPositionFromNode(this.$range[0]);
		this.setPosition(l - this.grabX);
		if (l >= k && l < k + this.handleWidth) {
			this.grabX = l - k;
		}
	};
	h.prototype.handleMove = function(j) {
		j.preventDefault();
		var k = this.getRelativePosition(this.$range[0], j);
		this.setPosition(k - this.grabX);
	};
	h.prototype.handleEnd = function(j) {
		j.preventDefault();
		this.$document.off(this.moveEvent, this.handleMove);
		this.$document.off(this.endEvent, this.handleEnd);
		if (this.onSlideEnd && typeof this.onSlideEnd === "function") {
			this.onSlideEnd(this.position, this.value);
		}
	};
	h.prototype.cap = function(l, k, j) {
		if (l < k) {
			return k;
		}
		if (l > j) {
			return j;
		}
		return l;
	};
	h.prototype.setPosition = function(l, j) {
		var k;
		if (j == undefined) {
			j = (this.getValueFromPosition(this.cap(l, 0, this.maxHandleX)) / this.step) * this.step;
		}
		k = this.getPositionFromValue(j);
		this.$fill[0].style.width = (k + this.grabX) + "px";
		this.$handle[0].style.left = k + "px";
		this.setValue(j);
		this.position = k;
		this.value = j;
		if (this.onSlide && typeof this.onSlide === "function") {
			this.onSlide(k, j);
		}
	};
	h.prototype.getPositionFromNode = function(k) {
		var j = 0;
		while (k !== null) {
			j += k.offsetLeft;
			k = k.offsetParent;
		}
		return j;
	};
	h.prototype.getRelativePosition = function(j, k) {
		return (k.pageX || k.originalEvent.clientX || k.originalEvent.touches[0].clientX || k.currentPoint.x) - this.getPositionFromNode(j);
	};
	h.prototype.getPositionFromValue = function(k) {
		var j, l;
		j = (k - this.min) / (this.max - this.min);
		l = j * this.maxHandleX;
		return l;
	};
	h.prototype.getValueFromPosition = function(l) {
		var j, k;
		j = ((l) / (this.maxHandleX || 1));
		k = this.step * Math.ceil((((j) * (this.max - this.min)) + this.min) / this.step);
		return Number((k).toFixed(2));
	};
	h.prototype.setValue = function(j) {
		if (j !== this.value || this.hasInit) {
			this.$element.val(j).trigger("change", {
				origin : f
			});
		}
	};
	h.prototype.destroy = function() {
		this.$document.off(this.startEvent, "#" + this.identifier, this.handleDown);
		this.$element.off("." + f).removeAttr("style").removeData("plugin_" + f);
		if (this.$range && this.$range.length) {
			this.$range[0].parentNode.removeChild(this.$range[0]);
		}
		g.splice(g.indexOf(this.$element[0]), 1);
		if (!g.length) {
			this.$window.off("." + f);
		}
	};
	d.fn[f] = function(j) {
		return this.each(function() {
			var l = d(this), k = l.data("plugin_" + f);
			if (!k) {
				l.data("plugin_" + f, (k = new h(this, j)));
				g.push(this);
			}
			if (typeof j === "string") {
				k[j]();
			}
		});
	};
}));