var interfaceUrl = "http://10.88.99.84:8083";// 接口地址
var ldmainId;// 答卷主表ID
var queId;
var state;// 答卷状态 0：保存 1：已提交 2：退回 3：已完成
var ldType;// 1：普通流调 2：临时流调
var initPageParam;

$(function() {
	ldmainId = resolveUrlParam("ldmainId");
	state = resolveUrlParam("state");
	queId = resolveUrlParam("queId");
	ldType = resolveUrlParam("ldType");
	initPageParam = {
		'rid' : ldmainId,
		'ldType' : ldType,
		'state' : state,
		'queId' : queId
	};
	if (ldmainId != null && state != null) {// 加载答案
		$.ajax({
			cache : true,
			type : "POST",
			url : interfaceUrl + "/crbObtainQueAns?ldmainId=" + ldmainId + "&ifHtml=1",
			data : null,
			async : false,
			error : function(request) {
				alert("网络出现异常，请重试！");
			},
			success : function(data) {
				var dataObj = $.parseJSON(data);
				answerDetailInit(dataObj.answers, state);
			}
		});
	}
	var simpleView = resolveUrlParam("simpleView");
	if (null == simpleView || '' == simpleView) {

	} else {
		$("#ctlNext").hide();
		$("#save").hide();
	}

});

function answerDetailInit(answers, state) {
	if (typeof answers != "undefined" && answers.length > 0) {
		for (var i = 0; i < answers.length; i++) {
			var ob = answers[i];
			// 有其他填空值时，先赋值
			if (ob.rid != undefined) {
				if (typeof ob.fillVal != "undefined") {
					var obDataType = ob.type;
					// 如果为多项填空，则需要更新值
					if (obDataType == "4" || obDataType == "7" || obDataType == "9") {
						if (typeof ob.optionVal != "undefined") {
							$("#q" + ob.num + "_" + ob.optionVal).val(ob.fillVal);
						}
					} else {
						if (typeof ob.optionVal != "undefined") {
							if (typeof ob.multiNum != "undefined") {
								$("input[rel='q" + ob.num + "_" + ob.optionVal + "_" + ob.multiNum + "']").val(ob.fillVal);
							} else {
								$("input[rel='q" + ob.num + "_" + ob.optionVal + "']").val(ob.fillVal);
							}
						} else {
							$("input[rel='q" + ob.num + "']").val(ob.fillVal);
						}
					}
				} else {
					var obDataType = ob.type;
					if (obDataType == "0" || obDataType == "10") {
						$("input[name='q" + ob.num + "'][value='" + ob.value + "']").attr("checked", "checked");
						$("input[name='q" + ob.num + "'][value='" + ob.value + "']").closest("div.ui-radio").click();
					} else if (obDataType == "2" || obDataType == "3" || obDataType == "4" || obDataType == "6" || obDataType == "7" || obDataType == "8" || obDataType == "9") {
						if (typeof ob.optionVal != "undefined") {
							$("#q" + ob.num + "_" + ob.optionVal).val(ob.value);
						} else {
							$("#q" + ob.num).val(ob.value);
						}
					} else if (obDataType == "1") {

						var val = ob.value;
						if (val != '') {
							var array = val.split(',');
							for (var j = 0; j < array.length; j++) {
								var sval = array[j];
								// $("input[name='q" + ob.num + "'][value='" +
								// sval + "']").attr("checked", "checked");
								$("input[name='q" + ob.num + "'][value='" + sval + "']").closest("div.ui-checkbox").click();
							}
						}
					}
				}
			} else if (ob.tabanswers != undefined && ob.tabanswers.length > 0) {
				for (var j = 0; j < ob.tabanswers.length; j++) {
					if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).size() > 0) {
						if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).is("select")) {
							$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).find("option").each(function() {
								if (ob.tabanswers[j].colValue == $(this).attr("value")) {
									$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).parents(".ui-select").find("span").html($(this).html());
								}
							})
						}
						$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).val(ob.tabanswers[j].colValue);
					} else {
						var id = "table" + ob.tabanswers[j].fkByColId.colName.split("_")[0].replace("q", "");
						addRow(id);
						$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).val(ob.tabanswers[j].colValue);
						if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).is("select")) {
							$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).find("option").each(function() {
								if (ob.tabanswers[j].colValue == $(this).attr("value")) {
									$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).parents(".ui-select").find("span").html($(this).html());
								}
							})
						}
					}
				}
			}
		}
	}
	if (state != undefined && (state == 1 || state == 5)) {
		$("input").attr("disabled", "true");
		$("select").attr("disabled", "true");
		$(".rowRemove").hide();
		$(".addrow").hide();
		$("#save").hide();
		$("#ctlNext").hide();
	}
}

function saveForm(state) {
	initPageParam.state = state;
	$.ajax({
		cache : true,
		type : "POST",
		url : interfaceUrl + "/crbSubmitAns?ifHtml=1&dataJson=" + JSON.stringify(initPageParam),
		data : $('#form').serialize(),
		async : false,
		error : function(request) {
			alert("网络出现异常，请重试！");
			$("#ctlNext").show();
			$("#save").show();
			$(".ValError").html("");
		},
		success : function(data) {
			var dataObj = $.parseJSON(data);
			if (dataObj.type == 1) {
				if (state == 1) {
					$("#ctlNext").hide();
					$("#save").hide();
				}
				try {
					chiscdc.onResult(data);
				} catch (e) {
					console.log(e);
				}
			} else {
				chiscdc.onResult(data);
			}
		}
	});
}