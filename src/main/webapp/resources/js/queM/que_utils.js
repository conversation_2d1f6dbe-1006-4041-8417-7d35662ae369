/**
 * 清除checkbox,radio选中状态
 * 
 * @param name
 */
function clearSelectGroup(name) {
	$("input[name=" + name + "]").each(function() {
		$(this).removeAttr("checked");
	});
}

/**
 * 获取页面的参数
 * 
 * @param name
 * @returns
 */
function resolveUrlParam(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); // 构造一个含有目标参数的正则表达式对象
	var r = window.location.search.substr(1).match(reg); // 匹配目标参数
	if (r != null)
		return decodeURI(r[2]);
	return null; // 返回参数值
}

/**
 * url转json
 */
function getUrlVars() {
	var url = window.location.search.substr(1);
	var hash;
	var myJson = {};
	var hashes = url.split('&');
	for (var i = 0; i < hashes.length; i++) {
		hash = hashes[i].split('=');
		if(hash[0] != null && hash[0] != ""){
			if(hash.length > 2){
				var reg = new RegExp("(^|&)" + hash[0] + "=([^&]*)(&|$)");
				var v =hashes[i].match(reg); // 匹配目标参数
				var h = ""
				if (v != null){
					h = decodeURI(v[2]);
				}
				myJson[hash[0]] = h ;
			}else{
				myJson[hash[0]] = hash[1] ;
			}

		}
	}
	return myJson;
}
/**
 * 页面初始化的时候，数据异常，要关闭当前页面的方法
 */
function wx_init_closewindow() {
	document.addEventListener('WeixinJSBridgeReady', function onBridgeReady() {
		WeixinJSBridge.call('closeWindow');
	});
}

function wx_closewindow() {
	WeixinJSBridge.call('closeWindow');
}

/** 重构表格列id name* */
function refact(tableid) {
	var rowIndex = 1;
	$('#' + tableid + ' tbody tr').each(function() {
		var colIndex = 1;
		$(this).find("td :input").each(function() {
			$(this).attr('id', 'q' + tableid.replace('table', '') + '_' + colIndex + '_' + rowIndex);
			$(this).attr('name', 'q' + tableid.replace('table', '') + '_' + colIndex + '_' + rowIndex);
			colIndex++;
		});
		rowIndex++;
	});
};
/** 添行* */
function addRow(id) {
	$('#' + id + ' tbody').append('<tr>' + $('#' + id + ' tbody tr:last').html() + '</tr>');
	var rowNum = $('#' + id + ' tbody tr').length;
	var tdNum = 1;
	$('#' + id + ' tbody tr:last td :input').each(function() {
		$(this).attr('id', 'q' + id.replace('table', '') + '_' + (tdNum) + '_' + rowNum);
		$(this).attr('name', 'q' + id.replace('table', '') + '_' + (tdNum) + '_' + rowNum);
		tdNum++;
	});
	$('#' + id + ' tbody tr:last td input[data-role="datebox"]').mobiscroll().date({
		mode : 'scroller',
		display : 'modal',
		showLabel : true,
		lang : 'zh',
		dateOrder : 'ddmmyy',
		dateFormat : 'yy-mm-dd',
		rtl : true
	});
	$('#' + id + ' tbody tr:last .rowRemove').on("click", function() {
		rowRemove(this);
	});
}

function rowRemove(item) {
	var rowCount = $(item).parents("table").find('tbody tr').length;
	if (rowCount == 1) {
		alert("至少有一条记录！");
		return;
	}
	var tableid = $(item).parents("table").attr("id");
	$(item.parentNode.parentNode).remove();
	refact(tableid);

}
$(function() {
	$('tr .rowRemove').each(function() {
		$(this).on("click", function() {
			rowRemove(this);
		});
	});
});
