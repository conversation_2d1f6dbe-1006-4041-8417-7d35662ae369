var httpUrlAddr = "http://10.88.99.202:8808";

function validator(frm) {
	var formElements = frm.elements;
	var fv = new FormValid(frm);

	for (var i = 0; i < formElements.length; i++) {
		var validType = formElements[i].getAttribute('zwx:valid');
		var errorMsg = formElements[i].getAttribute('zwx:errmsg');
		if (validType == null)
			continue;
		//滑动题不需要过滤隐藏域
		if($(formElements[i]).is(":hidden") && validType != 'requireSlider')
			continue;
		
		fv.addAllName(formElements[i].name);

		var vts = validType.split('|');
		var ems = errorMsg.split('|');
		for (var j = 0; j < vts.length; j++) {
			var curValidType = vts[j];
			var curErrorMsg = ems[j];

			switch (curValidType) {
			case 'isNumber':
			case 'isEmail':
			case 'isPhone':
			case 'isMobile':
			case 'isIdCard':
			case 'isMoney':
			case 'isZip':
			case 'isQQ':
			case 'isInt':
			case 'isEnglish':
			case 'isChinese':
			case 'isUrl':
			case 'isDate':
			case 'isTelphone':
			case 'isTime':
				fv.checkReg(formElements[i], RegExps[curValidType], curErrorMsg);
				break;
			case 'regexp':
				fv.checkReg(formElements[i], new RegExp(formElements[i].getAttribute('regexp'), "g"), curErrorMsg);
				break;
			case 'custom':
				if (!eval(formElements[i].getAttribute('custom') + '(formElements[i],formElements)')) {
					fv.addErrorMsg(formElements[i].name, curErrorMsg);
				}
				break;
			default:
				if (!eval('fv.' + curValidType + '(formElements[i],formElements)')) {
					fv.addErrorMsg(formElements[i].name, curErrorMsg);
				}
				break;
			}
		}
	}

	if (fv.passed() == true) {
		this.saveFormComm(1);
	}
	//跳转锚点
	var validateErrorId=$(".questionDiv_error:first").attr("id");
	scroller(validateErrorId, 100);
	return false;
}

//计算完成率
function countWcl() {
	var wcl = 0;
	var frm = document.getElementById("form");
	var fv = new FormValid(frm);
	var formElements = frm.elements;
	var countAll = 0;
	var hasComp = 0;
	for (var i = 0; i < formElements.length; i++) {

		var validType = formElements[i].getAttribute('zwx:valid');
		if (validType == null)
			continue;
		if ($(formElements[i]).is(":hidden") && validType != 'requireSlider' )
			continue;

		var vts = validType.split('|');
		for (var j = 0; j < vts.length; j++) {
			var curValidType = vts[j];
			// 验证比填，则需要验证
			if (curValidType == "requireChecked" || curValidType == "required" || curValidType == "requireSlider" ) {
				countAll++;
				if (eval('fv.' + curValidType
						+ '(formElements[i],formElements)')) {
					hasComp++;
				}
				break;
			}
		}
	}
	if( countAll == 0 )	{
		wcl = 0 ;
	}else{
		wcl = parseInt(hasComp*100/countAll);
	}
	return wcl;
}

function saveFormComm(state){
	var ansQueId = $("#ansQueId").val();
	var wcl = countWcl();
	var dataJson = "{'state':'"+state+"','ansQueId':'"+ansQueId+"','queComp':'"+wcl+"'}";
	
	$.ajax({
		cache : true,
		type : "POST",
		url : httpUrlAddr+"/submitAnsService?ph=1&&ifHtml=1&data="+$("#urlParams").val()+"&dataJson="+dataJson,
		data : $('#form').serialize(),
		async : false,
		error : function(request) {
			alert("网络出现异常，请重试！");
		},
		success : function(data) {
			var dataObj = $.parseJSON(data);
			//0-失败 1-成功 2-验证不通过
			if(dataObj.type=="1") {
				if(state==1)	{
					if(typeof dataObj.factorList != "undefined" && dataObj.factorList.length > 0 && typeof dataObj.sendInfo != "undefined" && "" != dataObj.sendInfo)  {
						var sendInfo = dataObj.sendInfo ;
						showBg(dataObj.factorList,sendInfo);
					}else{
						$("input").attr("disabled", "true");
						//将处方弹出框的内容可修改
						$(".cfCheck").attr("disabled", "");
						$(".dialogbutton").attr("disabled", "");
						$("#textarea2").attr("disabled", "");
						$(".submitbutton").eq(0).hide();
						$(".submitbutton").eq(1).hide();
						alert("提交成功！");
					}
				}else if(dataObj.type=="2") {
					var errors = dataObj.mess.split(";");
					var fv = new FormValid();
					for(var i = 0;i<errors.length;i++){
						var err = errors[i].split("@");
						fv.addErrorMsg("q"+err[0], err[1]);
					}
					fv.passed();
				}else {
					alert(dataObj.mess);
				}
			}else if(dataObj.type=="2") {
				alert(dataObj.mess);
				var errors = dataObj.mess.split(";");
				var fv = new FormValid();
				for(var i = 0;i<errors.length;i++){
					var err = errors[i].split("@");
					fv.addErrorMsg("q"+err[0], err[1]);
				}
				fv.passed();
				//跳转锚点
				var validateErrorId=$(".questionDiv_error:first").attr("id");
				scroller(validateErrorId, 100);
			}else {
				alert(dataObj.mess);
			}
		}
	});	
}

function saveForm(){
	//移除错误提示样式
	$("fieldset > div[class*='div_question']").each(function() {
		$(this).children(".errorMessage").html("");
		$(this).removeClass("questionDiv_error");
	});
	this.saveFormComm(0);
}

/**
 * 问卷特殊的脚本，根据上次是否生病，给页面赋值
 */
$(function() {
	//	var currentLoc = window.location.href;
	/**返回页面地址*/
	try {
		var fromUrl=resolveUrlParam("from");
		fromUrl=fromUrl.replace("@","?");
		$("#fromUrl").val(fromUrl);
		//2015-9-21 xt 如果来源连接不为空，则增加返回按钮
		if( '' != fromUrl)	{
			$(".submitbutton").eq(1).show();
		}
	}catch(e){}
	var noOper = false;
	//基础资料维护传参有值，则直接显示页面
	var simpleView = resolveUrlParam("simpleView");
	if( null == simpleView ||  '' == simpleView  ){
		//详情
		var ifView=resolveUrlParam("ifView");
		if( ifView != null && ifView != '' )	{
			noOper = true;
		}
		var questId=resolveUrlParam("questId");
		var userId=resolveUrlParam("userId");
		$("#ansQueId").val(questId);
		$("#userId").val(userId);
		answerDetailInit(questId,ifView);
	}else{
		$(".submitbutton").eq(0).hide();
		$(".submitbutton").eq(1).hide();
	}
	//加载滑动事件
	sliderInit();
	if(noOper)	{
		sliderReadOnly();
	}
	
});

function sliderReadOnly()	{
	$("fieldset .slider").each(function() {
		var slider = $(this);
		var sliderId = slider.attr("id");
		//移除绑定时间
		var barId = sliderId + "_bar"; 
		$("#"+barId).unbind();
//		$("#"+barId).unbind("mousedown");
		var _sliderId = sliderId + "_slider";
		$("#"+_sliderId).unbind();
//		$("#"+_sliderId).unbind("click");
	});
}

var completeLoaded = false;
function sliderInit()	{
	/**
	 * 滑动题加载Js
	 */
	$("fieldset .slider").each(function() {
		var slider = $(this);
		var minVal = slider.attr("minvalue");
		var maxVal = slider.attr("maxvalue");
		var sliderId = slider.attr("id");
		var hiddenId = sliderId.replace("divSlider","");

		var sliderTipId = document.getElementById(slider.attr("rel"));
		var at = new neverModules.modules.slider({
			hiddenTxtId : hiddenId,
			targetId : sliderId,
			sliderCss : "imageSlider1",
			barCss : "imageBar1",
			min : parseInt(minVal),
			max : parseInt(maxVal),
			sliderValue : sliderTipId,
			hints : "拖动或点击滑动条"
		});
		at.create();
		
		var hidVal = $("#q"+hiddenId).val();
		if( '' != hidVal )	{
			at.setValue(hidVal, true);
		}
	});
	completeLoaded = true;
}

function answerDetailInit(questId,ifView) {
	$.ajax({
		cache : true,
		type : "POST",
		url : httpUrlAddr+"/findQueAnsService?queAnsId="+questId+"&ifHtml=1",
		data : null,
		async : false,
		error : function(request) {
			alert("网络出现异常，请重试！");
		},
		success : function(data) {
			var dataObj = $.parseJSON(data);
			//姓名
			$(".que_userinfo_name").html(dataObj.name);
			//性别
			$(".que_userinfo_sex").html(dataObj.sex);
			//身份证号
			$(".que_userinfo_code").html(dataObj.code);
			if(typeof dataObj.subAnsList != "undefined" && dataObj.subAnsList.length > 0) {
				for(var i=0; i<dataObj.subAnsList.length; i++) {
					var ob=dataObj.subAnsList[i];
					//有其他填空值时，先赋值
					if(typeof ob.fillVal != "undefined") {
						var obDataType=ob.type;
						//如果为多项填空，则需要更新值
						if(obDataType=="4" || obDataType=="7" || obDataType=="9")	{
							if(typeof ob.optionVal != "undefined") {
								$("#q"+ob.num+"_"+ob.optionVal).val(ob.fillVal);
							}
						}if(obDataType=="12"){
							if(typeof ob.optionVal != "undefined") {
								$("#q"+ob.num).html(ob.fillVal);
							}
						}else{
							if(typeof ob.optionVal != "undefined") {
								if(typeof ob.multiNum != "undefined"){
									$("input[rel='q"+ob.num+"_"+ob.optionVal+"_"+ob.multiNum+"']").val(ob.fillVal);
								}else{
									$("input[rel='q"+ob.num+"_"+ob.optionVal+"']").val(ob.fillVal);
								}
							}else {
								$("input[rel='q"+ob.num+"']").val(ob.fillVal);
							}
						}
					}else{
						var obDataType=ob.type;
						if(obDataType=="0"||obDataType=="10") {
							$("input[name='q"+ob.num+"'][value='"+ob.value+"']").attr("checked","checked").click();
							
						}else if(obDataType=="2" || obDataType=="3" || obDataType=="4"
							|| obDataType=="6" || obDataType=="7" || obDataType=="8"|| obDataType=="9") {
							if(typeof ob.optionVal != "undefined") {
								$("#q"+ob.num+"_"+ob.optionVal).val(ob.value);
							}else {
								$("#q"+ob.num).val(ob.value);
							}
						}else if(obDataType=="1"){
							var val = ob.value;
							if( val != '')	{
								var array =val.split(',');
								for(var j = 0; j< array.length; j++)	{
									var sval = array[j];
									$("input[name='q"+ob.num+"'][value='"+sval+"']").attr("checked","checked");
								}
							}
						}else if(obDataType=="12"){
							$("#q"+ob.num).html(ob.value);
						}
					}
				}
			}
			if(ifView != null && ifView != '')	{
				$("input").attr("disabled", "true");
				$(".submitbutton").eq(0).hide();
				$(".submitbutton").eq(1).hide();
			}
		}
	});
}


var sendInfoTemp;
var orgFactorList;
// 显示灰色 jQuery 遮罩层
function showBg(factorList, sendInfo) {

	if (null != factorList && null != sendInfo) {
		orgFactorList = factorList;
		sendInfoTemp = sendInfo;
		
		var cfHtml = "<tr ><th style=\"width:40px;text-align:center;\" >选择</th><th style=\"width:220px;text-align:center;\" >处方名称</th></tr>";
	
		for( var i = 0 ; i < factorList.length ; i++ )	{
			var factorName = factorList[i].factorName;		    
			cfHtml += "<tr><td colspan=\"2\" style=\"text-align:left;padding-left:20px;font-weight:bold;\"  >"
					+ factorName  +"</td></tr>";
            var cfList = factorList[i].cfList;
			
			for( var j = 0; j < cfList.length; j++ )	{
				var cfName = cfList[j].cfName;
				var cfDesc = cfList[j].cfDesc;
				cfHtml += "<tr>";
				cfHtml += "<td style=\"width:40px;text-align:center;\"  ><input type=\"checkbox\" onclick=\"replaceCheckTemp()\" class=\"checkCf\" name=\"cfCheck\" value=\""
						+ cfDesc
						+ "\" /> </td><td  style=\"text-align: left;\" >"
						+ (j+1) + "." + cfName
						+"</td>";
				cfHtml += "</tr>";
			}
		}

		$("#textarea2").val("");
		$(".gridtable").html(cfHtml);
		replaceCheckTemp();
	}

	var bh = $("body").height();
	var bw = $("body").width();
	$("#fullbg").css({
		height : bh,
		width : bw,
		display : "block"
	});
	$("#dialog").show();
}
// 关闭灰色 jQuery 遮罩
function closeBg() {
	$("#fullbg,#dialog").hide();
}

function replaceCheckTemp() {
	
	var descs = "";
	var tempn = 1;
	$("input[name='cfCheck']:checkbox").each(function() {
		if ($(this).attr("checked")) {
			var val = $(this).val();
			descs += "\n\t" + tempn +"." + val;
			tempn++;
		}
	});
	var txtArea =$("#textarea2").val();
	if( '' != txtArea)	{
		descs += "\n\t" +$("#textarea2").val();
	}
	sendInfo = sendInfoTemp.replace("【处方通知—处方名称】", "").replace(
			"【处方通知—处方定义】", descs);
	
	sendInfo = sendInfo.replace(new RegExp("\n","gm"),"<br>");
	sendInfo = sendInfo.replace(new RegExp("\t","gm"),"&nbsp;&nbsp;");


	$("#contentDx").html(sendInfo);

}

function sendMsg() {
	var flag = false;
	$("input[name='cfCheck']:checkbox").each(function() {
		if ($(this).attr("checked")) {
			flag = true;
		}
	});
	//是否发送处方
	var ifSend = $("#ifSend").attr("checked");
	
	if (!flag && ifSend) {
		alert('请先选择处方！');
	} else {
		var ansQueId = $("#ansQueId").val();
		var userId = $("#userId").val();
		var content = $("#contentDx").html();
		content = content.replace(new RegExp("<br>","gm"),"\n");
		content = content.replace(new RegExp("&nbsp;&nbsp;","gm"),"\t");

		$.ajax({
			cache : true,
			type : "POST",
			url : httpUrlAddr+"/sendCfInfoService?ansQueId="
					+ ansQueId + "&ifHtml=1&userId="+userId+"&cfContent="
					+ encodeURI(content, "UTF-8") + (ifSend?"&ifSend=1":""),
			data : null,
			async : false,
			error : function(request) {
				alert("网络出现异常，请重试！");
			},
			success : function(data) {
				var dataObj = $.parseJSON(data);
				if (dataObj.type == 1) {


					$("input").attr("disabled", "true");
					//将处方弹出框的内容可修改
					$(".cfCheck").attr("disabled", "");
					$(".dialogbutton").attr("disabled", "");
					$("#textarea2").attr("disabled", "");
					
					
					$(".submitbutton").eq(0).hide();
					$(".submitbutton").eq(1).hide();

					closeBg();
					alert('提交成功！');
				} else {
					alert(dataObj.mess);
				}
			}
		});
	}
}




