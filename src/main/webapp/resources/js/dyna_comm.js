/**
 * 字符串转日期
 * @param string
 * @returns
 */ 
function stringToTime(string) {
	var f = string.split(' ', 2);
	var d = (f[0] ? f[0] : '').split('-', 3);
	var t = (f[1] ? f[1] : '').split(':', 3);
	return (new Date(parseInt(d[0], 10) || null, (parseInt(d[1], 10) || 1) - 1, parseInt(d[2], 10) || null, parseInt(t[0], 10) || null, parseInt(t[1], 10) || null, parseInt(t[2],
			10)
			|| null)).getTime();
}
/**
 * 计算两个日期的间隔天数  
 */
function dateDiff(date1, date2) {
	var type1 = typeof date1, type2 = typeof date2;
	if (type1 == 'string')
		date1 = stringToTime(date1);
	else if (date1.getTime)
		date1 = date1.getTime();
	if (type2 == 'string')
		date2 = stringToTime(date2);
	else if (date2.getTime)
		date2 = date2.getTime();
	return (date2 - date1) / (1000 * 60 * 60 * 24); //结果是秒
}

function toDecimal(x) {    
    var f = parseFloat(x);    
    if (isNaN(f)) {    
        return;    
    }    
    f = Math.round(x*100)/100;    
    return f;    
}    


//制保留2位小数，如：2，会在2后面补上00.即2.00    
function toDecimal2(x) {    
    var f = parseFloat(x);    
    if (isNaN(f)) {    
        return false;    
    }    
    var f = Math.round(x*100)/100;    
    var s = f.toString();    
    var rs = s.indexOf('.');    
    if (rs < 0) {    
        rs = s.length;    
        s += '.';    
    }    
    while (s.length <= rs + 2) {    
        s += '0';    
    }    
    return s;    
}    
    
function fomatFloat(src,pos){       
     return Math.round(src*Math.pow(10, pos))/Math.pow(10, pos);       
} 