function DirectoryNav($h, config) {
	this.opts = $.extend(true, {
		scrollThreshold : 0.5, // 滚动检测阀值 0.5在浏览器窗口中间部位
		scrollSpeed : 700, // 滚动到指定位置的动画时间
		scrollTopBorder : 500, // 滚动条距离顶部多少的时候显示导航，如果为0，则一直显示
		easing : 'swing', // 不解释
		delayDetection : 10, // 延时检测，避免滚动的时候检测过于频繁
		scrollChange : function() {
		}
	}, config);
	this.$win = $(window);
	this.$h = $h;
	this.$pageNavList = "";
	this.$pageNavListLis = "";
	this.$curTag = "";
	this.$pageNavListLiH = "";
	this.offArr = [];
	this.curIndex = 0;
	this.scrollIng = false;
	this.init();
	this.ifPos(0);
}

DirectoryNav.prototype = {
	init : function() {
		this.make();
		this.setArr();
		this.bindEvent();
		this.$pageNavList.fadeIn();
	},
	make : function() {
		var $hs = this.$h, $directoryNav = $("#directoryNav"), temp = [], index1 = 0, index2 = 0;
		$hs.each(function(index) {
			var $this = $(this), text = $this.text();
			if (this.className == 'report_title') {
				index1++;
				if (index1 % 2 == 0)
					index2 = 0;
				temp.push('<li class="l1"><span class="c-dot"></span><a class="l1-text">' + text + '</a></li>');
			} else {
				index2++;
				temp.push('<li class="l2"> <a class="l2-text">' + text + '</a></li>');

			}
		});
		$directoryNav.find("ul").html(temp.join(""));
		// 设置变量
		this.$pageNavList = $directoryNav;
		this.$pageNavListLis = this.$pageNavList.find("li");
		this.$curTag = this.$pageNavList.find(".cur-tag");
		this.$pageNavListLiH = this.$pageNavListLis.eq(0).height();
		
		if (!this.opts.scrollTopBorder) {
			this.$pageNavList.show();
		}
	},
	setArr : function() {
		var This = this;
		this.$h.each(function() {
			var $this = $(this), offT = Math.round($this.offset().top);
			This.offArr.push(offT);
		});
	},
	posTag : function(top) {
		this.$curTag.css({
			top : top + 'px'
		});
	},
	ifPos : function(st) {
		var offArr = this.offArr;
		// console.log("st"+st);
		var windowHeight = Math.round((this.$win.height()) * this.opts.scrollThreshold);
		// console.log("windowheight"+windowHeight);
		var flag = false;
		for (var i = 0; i < offArr.length; i++) {
			//console.log(offArr[i] - st);
			if ((offArr[i] - st) <= 10) {
				var $curLi = this.$pageNavListLis.eq(i);
				var tagTop = $curLi.position().top;
				$curLi.addClass("cur").siblings("li").removeClass("cur");
				this.curIndex = i;
				//console.log("i====" + i);
				this.posTag(tagTop + this.$pageNavListLiH * 0.5);
				// this.curIndex = this.$pageNavListLis.filter(".cur").index();
				this.opts.scrollChange.call(this);
				flag = true;
			}
		}
		if (!flag) {
			var $curLi = this.$pageNavListLis.eq(0);
			var tagTop = $curLi.position().top;
			$curLi.addClass("cur").siblings("li").removeClass("cur");
			this.curIndex = 0;
			this.posTag(tagTop + this.$pageNavListLiH * 0.5);
			this.opts.scrollChange.call(this);
			flag = true;
		}
	},
	bindEvent : function() {
		var This = this, show = false, timer = 0;
		this.$win.on("scroll", function() {
			var $this = $(this);
			clearTimeout(timer);
			timer = setTimeout(function() {
				This.scrollIng = true;
				if ($this.scrollTop() > This.opts.scrollTopBorder) {
					if (!This.$pageNavListLiH)
						This.$pageNavListLiH = This.$pageNavListLis.eq(0).height();
					if (!show) {
						This.$pageNavList.fadeIn();
						show = true;
					}
					This.ifPos($(this).scrollTop());
				} else {
					if (show) {
						This.$pageNavList.fadeOut();
						show = false;
					}
				}
			}, This.opts.delayDetection);
		});

		this.$pageNavList.on("click", "li", function() {
			var $this = $(this), index = $this.index();
			This.scrollTo(This.offArr[index]);
		});
	},
	scrollTo : function(offset, callback) {
		var This = this;
		$('html,body').animate({
			scrollTop : offset
		}, this.opts.scrollSpeed, this.opts.easing, function() {
			This.scrollIng = false;
			callback && this.tagName.toLowerCase() == 'body' && callback();
		});
	}
};

$(window).scroll(function() {
	var s = $(window).scrollTop();
	var dh = $(document).height();
	var wh = $(window).height();
//	 console.log("scrollTop:"+s);
//	 console.log("wh:"+wh);
//	 console.log("dh:"+dh);
	if (s > 300) {
//		$(".directory-nav").css('visibility', 'visible');
	}else {
		$(".directory-nav").css('visibility', 'hidden');
	}
	if (s > (dh - wh - 100)) {
		$(".directory-nav").css('bottom', s - (dh - wh - 200));
	} else {
		$(".directory-nav").css('bottom', '100px');
	}
});

function ChangeTop() {
	var s = $(window).scrollTop();
	$('html,body').animate({
		scrollTop : s - 50
	}, 10);
	$('html,body').animate({
		scrollTop : s
	}, 10);
}