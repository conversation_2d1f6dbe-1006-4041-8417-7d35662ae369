// ajax 请求
function Ajax(type, url, data, success) {
    var xhr = null; // 初始化xhr
    if (window.XMLHttpRequest) { //兼容IE
        xhr = new XMLHttpRequest();
    } else {
        xhr = new ActiveXObject('Microsoft.XMLHTTP')
    }
    var type = type.toUpperCase();
    var random = Math.random(); //创建随机数

    if (type == 'GET') {
        if (data) {
            xhr.open('GET', url + '?' + data + "&" + random, true); //如果有数据就拼接
        } else {
            xhr.open('GET', url + '?t=' + random, true); //如果没有数据就传入一个随机数
        }

        xhr.send();
    } else if (type == 'POST') {
        if (IE_data <= 8) {
            xhr.open('POST', url, true);
            xhr.setRequestHeader('Content-Type', 'application/json')
            xhr.responseType = 'blob'
            xhr.send(data);
        } else {
            xhr.open('POST', url, true);
            xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
            xhr.send(data);
        }

    }
    xhr.onreadystatechange = function () { // 创建监听函数
        if (xhr.readyState === 4 ) {
            if(xhr.status === 200){
                if (xhr.responseText) {
                    var res = xhr.responseText
                    if (isJSON(res)) {
                        success(JSON.parse(res));
                    }
                }
            }else{
                success(JSON.parse(99));
            }
        }
    }
}


function isJSON(str) {
    if (typeof str == 'string') {
        try {
            var obj = JSON.parse(str);
            if (typeof obj == 'object' && obj) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            console.log('error：' + str + '!!!' + e);
            return false;
        }
    }
}