/*!
 * mustache.js - Logic-less {{mustache}} templates with JavaScript
 * http://github.com/janl/mustache.js
 */
(function(a,b){if(typeof exports==="object"&&exports){b(exports)}else{var c={};b(c);if(typeof define==="function"&&define.amd){define(c)}else{a.Mustache=c}}}(this,function(a){var f=/\s*/;var m=/\s+/;var k=/\S/;var i=/\s*=/;var o=/\s*\}/;var u=/#|\^|\/|>|\{|&|=|!/;var g=RegExp.prototype.test;function t(A,z){return g.call(A,z)}function h(z){return !t(k,z)}var w=Object.prototype.toString;var l=Array.isArray||function(z){return w.call(z)==="[object Array]"};function b(z){return typeof z==="function"}function e(z){return z.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}var d={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function n(z){return String(z).replace(/[&<>"'\/]/g,function(A){return d[A]})}function v(z){this.string=z;this.tail=z;this.pos=0}v.prototype.eos=function(){return this.tail===""};v.prototype.scan=function(B){var A=this.tail.match(B);if(A&&A.index===0){var z=A[0];this.tail=this.tail.substring(z.length);this.pos+=z.length;return z}return""};v.prototype.scanUntil=function(B){var A=this.tail.search(B),z;switch(A){case -1:z=this.tail;this.tail="";break;case 0:z="";break;default:z=this.tail.substring(0,A);this.tail=this.tail.substring(A)}this.pos+=z.length;return z};function s(z,A){this.view=z==null?{}:z;this.parent=A;this._cache={".":this.view}}s.make=function(z){return(z instanceof s)?z:new s(z)};s.prototype.push=function(z){return new s(z,this)};s.prototype.lookup=function(z){var C;if(z in this._cache){C=this._cache[z]}else{var B=this;while(B){if(z.indexOf(".")>0){C=B.view;var D=z.split("."),A=0;while(C!=null&&A<D.length){C=C[D[A++]]}}else{C=B.view[z]}if(C!=null){break}B=B.parent}this._cache[z]=C}if(b(C)){C=C.call(this.view)}return C};function q(){this.clearCache()}q.prototype.clearCache=function(){this._cache={};this._partialCache={}};q.prototype.compile=function(B,z){var A=this._cache[B];if(!A){var C=a.parse(B,z);A=this._cache[B]=this.compileTokens(C,B)}return A};q.prototype.compilePartial=function(A,C,z){var B=this.compile(C,z);
this._partialCache[A]=B;return B};q.prototype.getPartial=function(z){if(!(z in this._partialCache)&&this._loadPartial){this.compilePartial(z,this._loadPartial(z))}return this._partialCache[z]};q.prototype.compileTokens=function(B,A){var z=this;return function(C,E){if(E){if(b(E)){z._loadPartial=E}else{for(var D in E){z.compilePartial(D,E[D])}}}return p(B,z,s.make(C),A)}};q.prototype.render=function(B,z,A){return this.compile(B)(z,A)};function p(H,B,z,K){var E="";function A(N){return B.render(N,z)}var C,I,J;for(var F=0,G=H.length;F<G;++F){C=H[F];I=C[1];switch(C[0]){case"#":J=z.lookup(I);if(typeof J==="object"||typeof J==="string"){if(l(J)){for(var D=0,M=J.length;D<M;++D){E+=p(C[4],B,z.push(J[D]),K)}}else{if(J){E+=p(C[4],B,z.push(J),K)}}}else{if(b(J)){var L=K==null?null:K.slice(C[3],C[5]);J=J.call(z.view,L,A);if(J!=null){E+=J}}else{if(J){E+=p(C[4],B,z,K)}}}break;case"^":J=z.lookup(I);if(!J||(l(J)&&J.length===0)){E+=p(C[4],B,z,K)}break;case">":J=B.getPartial(I);if(b(J)){E+=J(z)}break;case"&":J=z.lookup(I);if(J!=null){E+=J}break;case"name":J=z.lookup(I);if(J!=null){E+=a.escape(J)}break;case"text":E+=I;break}}return E}function y(F){var A=[];var E=A;var G=[];var C;for(var B=0,z=F.length;B<z;++B){C=F[B];switch(C[0]){case"#":case"^":G.push(C);E.push(C);E=C[4]=[];break;case"/":var D=G.pop();D[5]=C[2];E=G.length>0?G[G.length-1][4]:A;break;default:E.push(C)}}return A}function c(E){var B=[];var D,A;for(var C=0,z=E.length;C<z;++C){D=E[C];if(D){if(D[0]==="text"&&A&&A[0]==="text"){A[1]+=D[1];A[3]=D[3]}else{A=D;B.push(D)}}}return B}function r(z){return[new RegExp(e(z[0])+"\\s*"),new RegExp("\\s*"+e(z[1]))]}function x(P,F){P=P||"";F=F||a.tags;if(typeof F==="string"){F=F.split(m)}if(F.length!==2){throw new Error("Invalid tags: "+F.join(", "))}var J=r(F);var B=new v(P);var H=[];var G=[];var E=[];var Q=false;var O=false;function N(){if(Q&&!O){while(E.length){delete G[E.pop()]}}else{E=[]}Q=false;O=false}var C,A,I,K,D,z;while(!B.eos()){C=B.pos;I=B.scanUntil(J[0]);if(I){for(var L=0,M=I.length;L<M;++L){K=I.charAt(L);if(h(K)){E.push(G.length)
}else{O=true}G.push(["text",K,C,C+1]);C+=1;if(K=="\n"){N()}}}if(!B.scan(J[0])){break}Q=true;A=B.scan(u)||"name";B.scan(f);if(A==="="){I=B.scanUntil(i);B.scan(i);B.scanUntil(J[1])}else{if(A==="{"){I=B.scanUntil(new RegExp("\\s*"+e("}"+F[1])));B.scan(o);B.scanUntil(J[1]);A="&"}else{I=B.scanUntil(J[1])}}if(!B.scan(J[1])){throw new Error("Unclosed tag at "+B.pos)}D=[A,I,C,B.pos];G.push(D);if(A==="#"||A==="^"){H.push(D)}else{if(A==="/"){z=H.pop();if(!z){throw new Error('Unopened section "'+I+'" at '+C)}if(z[1]!==I){throw new Error('Unclosed section "'+z[1]+'" at '+C)}}else{if(A==="name"||A==="{"||A==="&"){O=true}else{if(A==="="){F=I.split(m);if(F.length!==2){throw new Error("Invalid tags at "+C+": "+F.join(", "))}J=r(F)}}}}}z=H.pop();if(z){throw new Error('Unclosed section "'+z[1]+'" at '+B.pos)}return y(c(G))}a.name="mustache.js";a.version="0.7.3";a.tags=["{{","}}"];a.Scanner=v;a.Context=s;a.Writer=q;a.parse=x;a.escape=n;var j=new q();a.clearCache=function(){return j.clearCache()};a.compile=function(A,z){return j.compile(A,z)};a.compilePartial=function(A,B,z){return j.compilePartial(A,B,z)};a.compileTokens=function(A,z){return j.compileTokens(A,z)};a.render=function(B,z,A){return j.render(B,z,A)};a.to_html=function(C,A,B,D){var z=a.render(C,A,B);if(b(D)){D(z)}else{return z}}}));