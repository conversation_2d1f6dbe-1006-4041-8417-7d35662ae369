var httpUrlAddr;//请求地址
var ifView;//是否详情页 详情页
var dcState;//调查表状态
var ifAudit;//是否需要二级审核
var middleCompareVal;//用于向问卷传递页面值，比如当前机构所在地区，用于比较表格结果是否外区
var initPageParam;
var fromUrl;
//详情页面是否显示退回按钮
var viewShowReturn;
//退回审核页面是否显示审核按钮
var returnShowAudit;
//操作类型 1:初审;2:终审
var opType;
//初始化body的高度
var body_height =0;//遮屏显示
var height =0;
$(function () {
    body_height = $(document.body).height();
    height = $(document.body).height();
    showHeight();
    window.onresize = function(){
        showHeight();
    }
    $(".surveycontent").resize(function(){
        changeHeight();
    });
    /**
     * @ifView {返回参数值}
     * 0:页面只读，页面有返回，审核通过，退回按钮
     * 2：页面可编辑，页面有返回，审核通过，保存，退回按钮
     * 1:页面只读，页面只有返回按钮
     */
    initPageParam = getUrlVars();
    //取值（页面需要引用时获取）
    ifView = initPageParam.ifView;
    dcState = initPageParam.dcState;
    ifAudit = initPageParam.ifAudit;
    httpUrlAddr = initPageParam.deleteUrl;
    httpUrlAddr = httpUrlAddr+"/que";
    middleCompareVal = initPageParam.middleCompareVal;
    viewShowReturn = initPageParam.viewShowReturn;
    returnShowAudit = initPageParam.returnShowAudit;
    opType = initPageParam.opType;
    if(null == middleCompareVal || undefined == middleCompareVal){
        middleCompareVal = "";
    }
    //赋值（接口取值与平台赋值不一致时需重新赋值）
    initPageParam.ifHtml = 1;
    initPageParam.busType = initPageParam.dcType;
    initPageParam.rid = initPageParam.dcRid;
    initPageParam.state = dcState;
    initPageParam.backRsn = '';
    //中文需要转码
    $("#middleCompareVal").val(decodeURI(middleCompareVal));
    /** 返回页面地址 */
    try {
        fromUrl = initPageParam.from;
        var reg = new RegExp("@","g");
        fromUrl = fromUrl.replace(reg, "&");
        fromUrl = encryptUrl(fromUrl)
        $("#fromUrl").val(fromUrl);
    } catch (e) {
    }
    //按钮初始化
    intButton();
    var noOper = false;
    var simpleView = initPageParam.simpleView;
    if (null == simpleView || '' == simpleView) {
        // 详情
        if(null != ifView && 1 == ifView){
            noOper = true;
        }
        if(null != ifView){
            answerDetailInit(ifView);
        }
        if (!fromUrl) {
            $(".submitbutton").eq(0).hide();
        }
    } else {// 预览
        if (!fromUrl) {
            $(".submitbutton").eq(0).hide();
        }
        $(".submitbutton").eq(1).hide();
        $(".submitbutton").eq(2).hide();
        $(".submitbutton").eq(3).hide();
        $(".submitbutton").eq(4).hide();
    }
    // 加载滑动事件
    sliderInit();
    if (noOper) {
        sliderReadOnly();
    }
});
//页面高度
function  showHeight() {
    //当前显示屏的高度
    var window_height = $(window).height() - 70;
    var page_height =  $(".surveycontent").height() + 300;
    body_height = page_height <= window_height ? window_height :page_height;
    $("#box").css("height",body_height+"px");
}
function changeHeight(){

}
function sliderReadOnly() {
    $("fieldset .slider").each(function () {
        var slider = $(this);
        var sliderId = slider.attr("id");
        // 移除绑定时间
        var barId = sliderId + "_bar";
        $("#" + barId).unbind();
        // $("#"+barId).unbind("mousedown");
        var _sliderId = sliderId + "_slider";
        $("#" + _sliderId).unbind();
        // $("#"+_sliderId).unbind("click");
    });
}

var completeLoaded = false;

function sliderInit() {
    /**
     * 滑动题加载Js
     */
    $("fieldset .slider").each(function () {
        var slider = $(this);
        var minVal = slider.attr("minvalue");
        var maxVal = slider.attr("maxvalue");
        var sliderId = slider.attr("id");
        var hiddenId = sliderId.replace("divSlider", "");

        var sliderTipId = document.getElementById(slider.attr("rel"));
        var at = new neverModules.modules.slider({
            hiddenTxtId: hiddenId,
            targetId: sliderId,
            sliderCss: "imageSlider1",
            barCss: "imageBar1",
            min: parseInt(minVal),
            max: parseInt(maxVal),
            sliderValue: sliderTipId,
            hints: "拖动或点击滑动条"
        });
        at.create();

        var hidVal = $("#q" + hiddenId).val();
        if ('' != hidVal) {
            at.setValue(hidVal, true);
        }
    });
    completeLoaded = true;
}

function answerDetailInit(ifView) {
    $.ajax({
        cache: true,
        async:true,//异步
        type: "POST",
        url: httpUrlAddr + "/obtainQueAns",
        data: encrypt(JSON.stringify(initPageParam)),
        dataType:"json",
        headers:{
            "Content-Type":"application/json"
        },
        error: function (request) {
            var da =request.responseText;
            if('' !=da && null != da && undefined != da){
                var data =JSON.parse(decrypt(da));
                if(data.type == '00'){
                    initAnswerByStates(data.answers, ifView);
                }else{
                    alert('错误信息' + data.mess);
                }
            }else {
                alert("网络出现异常，请重试！");
            }
        },
        success: function (data) {
            if('' !=data && null != data && undefined != data){
                if(data.type == '00'){
                    initAnswerByStates(data.answers, ifView);
                }else{
                    alert('错误信息' + data.mess);
                }
            }
        
        }
    });
}
function intButton() {
    if(null != ifView){
        $("#submit_div").show();
        if(ifView == 1){
            //详情
            hideButton();
        }else if(ifView != 1 && null != dcState ){
            showButton();
        }
    }
}
function hideButton() {
    var viewShowReturnBoolean = viewShowReturn && viewShowReturn == '1';
    $("input").attr("disabled", true);
    //下拉组件禁用
    $("select").attr("disabled", true);
    $(".slider").css("pointer-events", "none");
    $(".submitbutton").eq(0).attr("disabled", false);
    $(".submitbutton").eq(1).attr("disabled", true);
    $(".submitbutton").eq(3).attr("disabled", !viewShowReturnBoolean);
    $(".submitbutton").eq(2).attr("disabled", true);
    $(".submitbutton").eq(4).attr("disabled", true);
    $(".dialogbutton").attr("disabled", false);
    $(".closeButton").attr("disabled", false);
    $(".submitbutton").eq(0).show();
    $(".submitbutton").eq(1).hide();
    $(".submitbutton").eq(2).hide();
    if (viewShowReturnBoolean) {
        $(".submitbutton").eq(3).show();
    } else {
        $(".submitbutton").eq(3).hide();
    }
    $(".submitbutton").eq(4).hide();
    $(".rowRemove").each(function () {
        $(this).hide();
    });
    $(".addrow").each(function () {
        $(this).hide();
    });
}
/**
 * 根据类型-按钮显示
 */
function showButton(){
    //终审退回，初审审核页面
    var returnShowAuditBoolean = returnShowAudit && returnShowAudit == '1' && ifView == '0';
    //待提交，初审退回，终审退回
    if(0 == dcState || 3 == dcState || (5 == dcState && !returnShowAuditBoolean)){
        $("input").attr("disabled", false);
        $("select").attr("disabled", false);
        $(".slider").css("pointer-events", "");
        //保存
        $(".submitbutton").eq(1).show();
        $(".submitbutton").eq(1).attr("disabled", false);
        //提交
        $(".submitbutton").eq(4).show();
        $(".submitbutton").eq(4).attr("disabled", false);
        //审核通过按钮
        $(".submitbutton").eq(2).hide();
        $(".submitbutton").eq(2).attr("disabled", true);
        //退回按钮
        $(".submitbutton").eq(3).hide();
        $(".submitbutton").eq(3).attr("disabled", true);
        //弹出框确认按钮
        $(".dialogbutton").attr("disabled", true);
        //弹出框关闭按钮
        $(".closeButton").attr("disabled", true);
        //表格删除按钮显示
        $(".rowRemove").each(function () {
            $(this).show();
        });
        //表格添加按钮显示
        $(".addrow").each(function () {
            $(this).show();
        });
    }else if(2 == dcState || 1 == dcState || (5 == dcState && returnShowAuditBoolean)){
        if(0 == ifView){
            //页面只读
            $("input").attr("disabled", true);
            $("select").attr("disabled", true);
            $(".slider").css("pointer-events", "none");
            //保存按钮隐藏
            $(".submitbutton").eq(1).hide();
            //表格删除按钮隐藏
            $(".rowRemove").each(function () {
                $(this).hide();
            });
            //表格添加按钮隐藏
            $(".addrow").each(function () {
                $(this).hide();
            });
        }else if(2 == ifView){
            //页面可修改
            $("input").attr("disabled", false);
            $("select").attr("disabled", false);
            $(".slider").css("pointer-events", "");
            //保存按钮显示
            $(".submitbutton").eq(1).show();
            $(".submitbutton").eq(1).attr("disabled", false);
            //表格删除按钮显示
            $(".rowRemove").each(function () {
                $(this).show();
            });
            //表格添加按钮显示
            $(".addrow").each(function () {
                $(this).show();
            });
        }
        //审核通过按钮
        $(".submitbutton").eq(2).show();
        $(".submitbutton").eq(2).attr("disabled", false);
        //退回按钮
        $(".submitbutton").eq(3).show();
        $(".submitbutton").eq(3).attr("disabled", false);
        //弹出框确认按钮
        $(".dialogbutton").attr("disabled", false);
        //弹出框关闭按钮
        $(".closeButton").attr("disabled", false);
        //提交按钮
        $(".submitbutton").eq(4).hide();
    }
    //返回按钮
    $(".submitbutton").eq(0).show();
    $(".submitbutton").eq(0).attr("disabled", false);
}
/**
 * 根据状态加载答案，是否可以编辑
 *
 * @param answers
 * @param state
 * @returns
 */
function initAnswerByStates(answers, state) {
    if (typeof answers != "undefined" && null != answers && answers.length > 0) {
        var ifSlider = false;
        for (var i = 0; i < answers.length; i++) {
            var ob = answers[i];
            // 有其他填空值时，先赋值
            var obDataType = ob.type;
            if(obDataType == "2"){
                ifSlider = true;
            }
            if (typeof ob.fillVal != "undefined" && ob.fillVal != null) {
                // 如果为多项填空，则需要更新值
                if (obDataType == "4" || obDataType == "7" || obDataType == "9" || obDataType == "12") {
                    if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
                        $("#q" + ob.num + "_" + ob.optionVal).val(ob.fillVal);
                    }
                } else {
                    if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
                        if (typeof ob.multiNum != "undefined" && ob.multiNum != null) {
                            $("input[rel='q" + ob.num + "_" + ob.optionVal + "_" + ob.multiNum + "']").val(ob.fillVal);
                        } else {
                            $("input[rel='q" + ob.num + "_" + ob.optionVal + "']").val(ob.fillVal);
                        }
                    } else {
                        $("input[rel='q" + ob.num + "']").val(ob.fillVal);
                    }
                }
            } else {
                if (obDataType == "0" || obDataType == "10") {
                    if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
                        $("input[name='q" + ob.num + "_" + ob.optionVal + "_1']").val(ob.value);
                    }else{
                        $("input[name='q" + ob.num + "'][value='" + ob.value + "']").attr("checked", "checked").click();//有依赖题或跳转题时必须执行点击事件，会根据点击事件判断是否显示题目
                    }

                } else if (obDataType == "2" || obDataType == "3" || obDataType == "4" || obDataType == "6" || obDataType == "7" || obDataType == "8" || obDataType == "9"|| obDataType == "12") {
                    if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
                        $("#q" + ob.num + "_" + ob.optionVal).val(ob.value);
                    } else {
                        $("#q" + ob.num).val(ob.value);
                    }
                } else if (obDataType == "1") {
                    var val = ob.value;
                    if (val != '') {
                        var array = val.split(',');
                        for (var j = 0; j < array.length; j++) {
                            var sval = array[j];
                            $("input[name='q" + ob.num + "'][value='" + sval + "']").attr("checked", "checked");
                        }
                    }
                }
            }
            if (ob.tabanswers != undefined && ob.tabanswers.length > 0) {
                for (var j = 0; j < ob.tabanswers.length; j++) {
                	if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).size() > 0) {
                        //下拉组件需要按下拉组件的id赋值
                		if(ob.tabanswers[j].fkByColId.dsType=="2" && ob.tabanswers[j].fkByColId.colType!="6"){
                			$("input[name='" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num + "'][value='" + ob.tabanswers[j].colValue + "']").attr("checked","checked");
                		}else{
                			if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).is("select")) {
                				$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).find("option").each(function () {
                					if (ob.tabanswers[j].colValue == $(this).attr("value")) {
                						$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).parents(".ui-select").find("span").html($(this).html());
                					}
                				});
                			}
                			$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).val(ob.tabanswers[j].colValue);
                		}
            			
            		} else {
            			var id = "table" + ob.tabanswers[j].fkByColId.colName.split("_")[0].replace("q", "");
            			addRow(id);
            			if(ob.tabanswers[j].fkByColId.dsType=="2" && ob.tabanswers[j].fkByColId.colType!="6"){
            				$("input[name='" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num + "'][value='" + ob.tabanswers[j].colValue + "']").attr("checked","checked");
            			}else{
            				$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).val(ob.tabanswers[j].colValue);
            				if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).is("select")) {
            					$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).find("option").each(function () {
            						if (ob.tabanswers[j].colValue == $(this).attr("value")) {
            							$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).parents(".ui-select").find("span").html($(this).html());
            						}
            					})
            				}
            			}
            		}
                }
            }
        }
        //存在滑动题时重新加载
        if(ifSlider){
            sliderInit();
        }

    }
}

// 显示灰色 jQuery 遮罩层
function showBg() {
    $("#fullbg").css({
        height: body_height+130+"px",
        width: "100%",
        display: "block"
    });
    $("#dialog").show();
}

// 关闭灰色 jQuery 遮罩
function closeBg() {
    document.getElementById("queTextArea").value = "";
    $("#fullbg,#dialog,#msgDialog").hide();
}

function showMsgDialog() {
    $("#fullbg").css({
        height: body_height+130+"px",
        width: "100%",
        display: "block"
    });

    $("#msgDialog").show();
}
var isFlag ;
//审核通过
function saveSubMitAction() {
    $('.dialogbutton').attr("disabled", "true");
    var state;
    var curState = initPageParam.state;
    //终审退回，初审审核页面
    var returnShowAuditBoolean = returnShowAudit && returnShowAudit == '1' && ifView == '0';
    if(1 == curState || (returnShowAuditBoolean && 5 == curState)){
        state = 2;
    }else if(2 == curState){
        state = 4;
    }
    //是否需要验证
    if(null != ifView && 2== ifView){
        var t = $('form')[0];
        $("#opType").val(state);
        isFlag = true;
        validator(t,state);
        if (isFlag == false) {
            closeBg();
        }
    }else{
        saveForm(state,false);
    }


}
//退回事件
function backAction() {
    $('.dialogbutton').attr("disabled", "true");
    var queTextArea = $("#queTextArea").val();
    if (queTextArea.trim() != null && queTextArea.trim() != '') {
        var state;
        var curState = initPageParam.state;
        //终审退回，初审审核页面
        var returnShowAuditBoolean = returnShowAudit && returnShowAudit == '1' && ifView == '0';
        if(1 == curState){
            state = 3;
        }else if(2 == curState){
            state = 5;
            if (ifView == 1 && opType && opType == '1' && viewShowReturn && viewShowReturn == '1') {
                state = 3;
            }
        } else if(4 == curState){
            state = 5;
        } else if(returnShowAuditBoolean && 5 == curState){
            state = 3;
        }
        initPageParam.backRsn = queTextArea;
        saveForm(state,false);
    } else {
        alert("请填写退回原因！");
        $(".dialogbutton").removeAttr("disabled");
    }
}

function back() {
    window.location.href = fromUrl;
}
function setOpType(type) {
    $("#opType").val(type);
    $(".submitbutton").attr("disabled", "true");
    $(".submitbutton").eq(0).removeAttr("disabled");
    $("#form").submit();
}

function validator(frm,s) {
    //去除错误提示若有错误生成新的提示
    $("div[class*='div_question']").each(function() {
        $(this).children(".errorMessage").html("");
        $(this).removeClass("questionDiv_error");
    });
    var flag = false;
    if ($("#opType").val() == 0) {
        flag = true;
    }
    var formElements = frm.elements;
    var fv = new FormValid(frm);
    for (var i = 0; i < formElements.length; i++) {
        var validType = formElements[i].getAttribute('zwx:valid');
        var errorMsg = formElements[i].getAttribute('zwx:errmsg');
        if (validType == null)
            continue;
        // 滑动题不需要过滤隐藏域
        if ($(formElements[i]).is(":hidden") && validType != 'requireSlider')
            continue;

        fv.addAllName(formElements[i].name);

        var vts = validType.split('|');
        var ems = errorMsg.split('|');
        for (var j = 0; j < vts.length; j++) {
            var curValidType = vts[j];
            var curErrorMsg = ems[j];
            //保存 不需要必填验证
            if(flag && (curValidType == "required" || curValidType == "requireChecked" || curValidType == "requireSlider")){
                continue;
            }
            switch (curValidType) {
                case 'isNumber':
                case 'isEmail':
                case 'isPhone':
                case 'isMobile':
                case 'isIdCard':
                case 'isMoney':
                case 'isZip':
                case 'isQQ':
                case 'isInt':
                case 'isEnglish':
                case 'isChinese':
                case 'isUrl':
                case 'isDate':
                case 'isTelphone':
                case 'isTime':
                    fv.checkReg(formElements[i], RegExps[curValidType], curErrorMsg);
                    break;
                case 'regexp':
                    fv.checkReg(formElements[i], new RegExp(formElements[i].getAttribute('regexp'), "g"), curErrorMsg);
                    break;
                case 'custom':
                    if (!eval(formElements[i].getAttribute('custom') + '(formElements[i],formElements)')) {
                        fv.addErrorMsg(formElements[i].name, curErrorMsg);
                    }
                    break;
                default:
                    if (!eval('fv.' + curValidType + '(formElements[i],formElements)')) {
                        fv.addErrorMsg(formElements[i].name, curErrorMsg);
                    }
                    break;
            }
        }
    }
    isFlag = fv.passed();
    if (fv.passed() == true) {
        if(null != s){
            this.saveForm(s,false);
        }else {
            this.saveForm($("#opType").val(),true);
        }
    }else{
        $(".submitbutton").removeAttr("disabled");
        // 跳转锚点
        var validateErrorId = $(".questionDiv_error:first").attr("id");
        scroller(validateErrorId, 100);
        return false;
    }
}

function saveForm(state,isSave) {
	$("div[class*='div_question']").each(function() {
		$(this).children(".errorMessage").html("");
		$(this).removeClass("questionDiv_error");
	});
    if(null !=ifView && 2 == ifView && isSave){
        //保存状态为当前调查表的状态，不更新状态为0
        initPageParam.state = dcState;
    }else {
        //提交，是否需要二级审核（0：需要，1：不需要），状态为2（待终审/初审通过）;保存不用变
        if(ifAudit != null && ifAudit != "" && ifAudit == 1 && state == 1){
            initPageParam.state = 2;
        }else {
            initPageParam.state = state;
        }
    }
    $.ajax({
        cache: false,
        type: "POST",
        url: httpUrlAddr + "/submitQueAns?" +encodeURIComponent( encrypt("dataJson=" + JSON.stringify(initPageParam))),
        data: encodeURIComponent(encrypt($('#form').serialize())),
        async: true,//只能设置为true，否则连续点击设置disabled不生效
        error: function (request) {
            var da =request.responseText;
            if('' !=da && null != da && undefined != da){
                var data =JSON.parse(decrypt(da));
                dealData(data,state);
            }else {
                alert("网络出现异常，请重试！");
                $(".submitbutton").removeAttr("disabled");
                $(".dialogbutton").removeAttr("disabled");
            }

        },
        success: function (data) {
            dealData(data,state);
        }
    });
}
function  dealData(data,state){
    if (data.type == '00') {
        if (state == 1) {
            alert("提交成功！");
        }
        if (state == 0) {
            alert("保存成功！");
        }
        if(state == 2 || state == 4){
            alert("审核成功！");
        }
        if(state == 3 || state == 5){
            alert("退回成功！");
        }
        //提交 审核 退回后 直接返回
        if(state == 1 || state == 2 || state == 4 || state == 3 || state == 5){
            back();
        }
    } else if (data.type == '05'){
        var errors = data.mess.split(";");
        var fv = new FormValid();
        for(var i = 0;i<errors.length;i++){
            var err = errors[i].split("@");
            fv.addErrorMsg("q"+err[0], err[1]);
        }
        fv.passed();
        //跳转锚点
        var validateErrorId=$(".questionDiv_error:first").attr("id");
        scroller(validateErrorId, 100);
        $(".dialogbutton").removeAttr("disabled");
    }else{
        alert('错误信息' + data.mess);
        $(".dialogbutton").removeAttr("disabled");
    }
    $(".submitbutton").removeAttr("disabled");
}

function encryptUrl(url){
    if (url.indexOf("?") == -1) {
        return url;
    }
    var postData =  url.split('?')[1];
    if (postData == "" || postData.startsWith("encryptdata")) {
        return url;
    }
    postData = new window.parent.SM2Utils(0).baseSm2Encrypt123(postData);
    postData = "encryptdata=" + encodeURIComponent(postData);
    url = url.split('?')[0]+ "?" +postData;
    return url;
}