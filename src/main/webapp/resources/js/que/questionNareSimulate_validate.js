var httpUrlAddr="";
$(function () {
    httpUrlAddr = zwxJQ("#mainForm\\:deleteUrl").val()+"/checkQue";
})
function validator(frm) {
	//去除错误提示若有错误生成新的提示
	$("div[class*='div_question']").each(function() {
		$(this).children(".errorMessage").html("");
		$(this).removeClass("questionDiv_error");
	});
	var formElements = frm.elements;
	var fv = new FormValid(frm);
	var flag = false;
	if ($("#opType").val() == 1) {
		flag = true;
	}
	for (var i = 0; i < formElements.length; i++) {
		var validType = formElements[i].getAttribute('zwx:valid');
		var errorMsg = formElements[i].getAttribute('zwx:errmsg');
		if (validType == null)
			continue;
		// 滑动题不需要过滤隐藏域
		if (zwxJQ(formElements[i]).is(":hidden")
			&& validType != 'requireSlider')
			continue;

		fv.addAllName(formElements[i].name);

		var vts = validType.split('|');
		var ems = errorMsg.split('|');
		for (var j = 0; j < vts.length; j++) {
			var curValidType = vts[j];
			var curErrorMsg = ems[j];
			//暂存 不需要必填验证
			if(flag && (curValidType == "required" || curValidType == "requireChecked" || curValidType == "requireSlider")){
				continue;
			}
			switch (curValidType) {
				case 'isNumber':
				case 'isEmail':
				case 'isPhone':
				case 'isMobile':
				case 'isIdCard':
				case 'isMoney':
				case 'isZip':
				case 'isQQ':
				case 'isInt':
				case 'isEnglish':
				case 'isChinese':
				case 'isUrl':
				case 'isDate':
				case 'isTelphone':
				case 'isTime':
					fv
						.checkReg(formElements[i], RegExps[curValidType],
							curErrorMsg);
					break;
				case 'regexp':
					fv.checkReg(formElements[i], new RegExp(formElements[i]
						.getAttribute('regexp'), "g"), curErrorMsg);
					break;
				case 'custom':
					if (!eval(formElements[i].getAttribute('custom')
						+ '(formElements[i],formElements)')) {
						fv.addErrorMsg(formElements[i].name, curErrorMsg);
					}
					break;
				default:
					if (!eval('fv.' + curValidType
						+ '(formElements[i],formElements)')) {
						fv.addErrorMsg(formElements[i].name, curErrorMsg);
					}
					break;
			}
		}
	}

	if (fv.passed() == true) {
		this.saveFormComm();
	}else{
		console.log("暂存提交按钮禁用解除");
		zwxJQ(".submitbutton").removeAttr("disabled");
	}
	// 跳转锚点
	var validateErrorId = zwxJQ(".questionDiv_error:first").attr("id");
	scroller(validateErrorId, 100);
	return false;
}

function validator2(frm,subType) {
	//去除错误提示若有错误生成新的提示
	$("div[class*='div_question_score']").each(function () {
		$(this).children(".errorMessage").html("");
		$(this).removeClass("questionDiv_error");
	});
	var formElements = frm.elements;
	var fv = new FormValid(frm);
	this.errMsg = new Array();
	this.errName = new Array();

	for (var i = 0; i < formElements.length; i++) {
		var zwxMax = formElements[i].getAttribute('zwx:max');
		if (!zwxMax) {
			continue;
		}
		var max = parseFloat(zwxMax);
		var eName = formElements[i].name;
		var eValue = formElements[i].value;
		fv.addAllName(eName);
		var err;
		if (subType === 2 && !eval('fv.required(formElements[i],formElements)')) {
			this.errMsg.push('得分不能为空！');
			this.errName.push(eName);
		} else if (eValue && !/^\d+(\.[0-9]{1,3})*$/.test(eValue)) {
			err = '得分应大于等于0且最多只能为三位小数！';
			this.errMsg.push('得分应大于等于0且最多只能有三位小数！');
			this.errName.push(eName);
		}
		if (parseFloat(eValue) > max) {
			this.errMsg.push((err ? err : '') + '得分不能大于分值' + max + '！');
			this.errName.push(eName);
		}
	}
	if (this.errMsg.length === 0) {
		return true;
	}
	for (var j = 0; j < this.errMsg.length; j++) {
		var errDivId = this.errName[j].substring(8);
		$("#div_score_" + errDivId + " > div[class*='errorMessageScore']").html(this.errMsg[j]);
		$("#div_score_" + errDivId).addClass("questionDiv_error");
	}

	// 跳转锚点
	var validateErrorId = zwxJQ(".questionDiv_error:first").attr("id");
	scroller(validateErrorId, 100);
	return false;
}


//倒计时 isShow：true显示倒计时  false：隐藏倒计时
var maxTime;var lefttime; var MysetInterval;var div;
function countdown(isShow,lefttime1) {
	if(isShow){
		$("#divMaxTime").show();
		div = document.getElementById("spanMaxTime");
		lefttime = lefttime1;
		div.innerHTML =IntShowtime();
		MysetInterval=setInterval (function () {
			lefttime=lefttime-1000;
			div.innerHTML = showtime();
		}, 1000);
	}else{
		document.getElementById("spanMaxTime").innerHTML="00:00:00";
		$("#divMaxTime").hide();
		clearInterval(MysetInterval);
	}

}
function  showtime() {
	var lefth = Math.floor(lefttime/(1000*60*60)%24),  //计算小时数
		leftm = Math.floor(lefttime/(1000*60)%60),  //计算分钟数
		lefts = Math.floor(lefttime/1000%60);  //计算秒数
	if(parseInt(lefth.toString())==0&&parseInt(leftm.toString())==0&&parseInt(lefts.toString())==0){
		clearInterval(MysetInterval);
		forcingSaveForm();
	}
	return  (lefth.toString().length>1?lefth:"0"+lefth) + ":" + (leftm.toString().length>1?leftm:("0"+leftm)) + ":" + (lefts.toString().length>1?lefts:"0"+lefts);  //返回倒计时的字符串
}
function forcingSaveForm(){
	console.log("自动提交，暂存提交按钮禁用");
	zwxJQ(".submitbutton").eq(0).attr("disabled", "true");
	zwxJQ(".submitbutton").eq(1).attr("disabled", "true");
	//提交保存 - 判断是否需要评分 需要 -3 不需要-2
	var ifNeedScore = $("#ifNeedScore").val();
	if( ifNeedScore != null && ifNeedScore == 1){
		$("#opType").val(3);
	}else {
		$("#opType").val(2);
	}
	saveFormComm();
}

function  IntShowtime() {
	var lefth = Math.floor(lefttime/(1000*60*60)%24),  //计算小时数
		leftm = Math.floor(lefttime/(1000*60)%60),  //计算分钟数
		lefts = Math.floor(lefttime/1000%60);  //计算秒数

	return  (lefth.toString().length>1?lefth:"0"+lefth) + ":" + (leftm.toString().length>1?leftm:("0"+leftm)) + ":" + (lefts.toString().length>1?lefts:"0"+lefts);  //返回倒计时的字符串
}

//暂存、提交调用
function saveFormComm() {
	executeSaveForm(1);
}

function  dealData(data,state){
	zwxJQ(".submitbutton").removeAttr("disabled");
	console.log("暂存提交按钮禁用解除");
	if (data.type == '00') {
		successShow(state);
	}else if (data.type == '05'){
		var errors = data.mess.split(";");
		var fv = new FormValid();
		for(var i = 0;i<errors.length;i++){
			var err = errors[i].split("@");
			fv.addErrorMsg("q"+err[0], err[1]);
		}
		fv.passed();
		//跳转锚点
		var validateErrorId=$(".questionDiv_error:first").attr("id");
		scroller(validateErrorId, 100);
	}else{
		alert('错误信息' + data.mess);
	}
}
function  successShow(state){
	if(state == 2 ||  state == 3){
	//倒计时隐藏
		countdown(false);
		alert("提交成功");
		closePage();
		zwxJQ("#submit_div").hide();
		zwxJQ("#khts").hide();
		zwxJQ("input").attr("disabled", "true");
		//大文本
		zwxJQ("textarea").attr("disabled", "true");
		//暂存，提交按钮隐藏
		zwxJQ(".submitbutton").eq(0).hide();
		zwxJQ(".submitbutton").eq(1).hide();
		zwxJQ(".submitbutton").removeAttr("disabled");
		zwxJQ("fieldset > div[class*='div_question']").unbind('click');
	}else{
		alert("暂存成功");
	}
}

function closePage() {
	var closeType1 = $('.tab_hover', parent.document).length && $('.tab_hover', parent.document).length > 0;
	var closeType2 = $('.J_menuTabs', parent.document).length && $('.J_menuTabs', parent.document).length > 0;
	if (closeType1) {
		top.CloseTabWin($('.tab_hover').attr("id"));
	} else if (closeType2) {
		top.Menu03CloseNowTabs();
	} else {
		closePageWin();
	}
}

function closePageWin() {
	var userAgent = navigator.userAgent;
	if (userAgent.indexOf("Firefox") !== -1 || userAgent.indexOf("Chrome") !== -1) {
		location.href = "about:blank";
	}
	window.opener = null;
	window.open('', '_self');
	window.close();
}

function setOpType(type) {
	$("#opType").val(type);
	console.log("暂存提交按钮禁用");
	zwxJQ(".submitbutton").eq(0).attr("disabled", "true");
	zwxJQ(".submitbutton").eq(1).attr("disabled", "true");
	zwxJQ("#form").submit();
}

/**
 * 暂存
 */
function saveForm(state) {
	// 移除错误提示样式
	zwxJQ("fieldset > div[class*='div_question']").each(function() {
		zwxJQ(this).children(".errorMessage").html("");
		zwxJQ(this).removeClass("questionDiv_error");
	});
	this.saveFormComm(state);
}


function addFlag(dataObj) {
	if (typeof dataObj != "undefined" && dataObj.length > 0) {

		for (var i = 0; i < dataObj.length; i++) {
			var ob = dataObj[i];
			if (ob.flag == "1") {
				zwxJQ("#div" + ob.qesCode + " > div[class='errorMessage']")
					.html("回答正确");
				zwxJQ("#div" + ob.qesCode + " > div[class='errorMessage']")
					.css("color", "green");
			} else {
				zwxJQ("#div" + ob.qesCode + " > div[class='errorMessage']")
					.html("回答错误");
				if(typeof ob.ansStr != "undefined" &&ob.ansStr!=""){
					var ans = ob.ansStr.split(",");
					for (var j = 0; j < ans.length; j++) {
						$("#q"+ob.qesCode+"_"+ans[j]).parent("li").append("<img src=\"\\resources\\images\\pdi\\yes.png\" style=\"border:none;\"/>");
						console.log("label [for='q"+ob.qesCode+"_"+ans[j]+"']");
						$("#q"+ob.qesCode+"_"+ans[j]).parent("li").find("label").css("float","left");
					}
				}
			}
		}
	}
}

var completeLoaded = false;

/**
 * 详情页面答案获取
 * @param ifView
 */
function answerDetailInit(ifView) {
	if(ifView == 1){
		zwxJQ("input").not(".score_input").attr("disabled", "true");
		zwxJQ("#mainForm\\:allScore").removeAttr("disabled");
		//大文本
		zwxJQ("textarea").attr("disabled", "true");
		zwxJQ(".submitbutton").removeAttr("disabled");
	}
	var initPageParam ={
		'rid':zwxJQ("#mainForm\\:mainId").val(),
	}
	$.ajax({
		cache: true,
		async:false,//异步
		type: "POST",
		url: httpUrlAddr + "/obtainQueAns",
		data: encrypt(JSON.stringify(initPageParam)),
		dataType:"json",
		headers:{
			"Content-Type":"application/json"
		},
		error: function (request) {
			var da =request.responseText;
			if('' !=da && null != da && undefined != da){
				var data =JSON.parse(decrypt(da));
				if(data.type == '00'){
					initAnswerByStates(data.answers, ifView);
				}else{
					alert('错误信息' + data.mess);
				}
			}else {
				alert("网络出现异常，请重试！");
			}
		},
		success: function (data) {
			if('' !=data && null != data && undefined != data){
				if(data.type == '00'){
					initAnswerByStates(data.answers, ifView);
				}else{
					alert('错误信息' + data.mess);
				}
			}

		}
	});
}
/**
 * 根据状态加载答案，是否可以编辑
 *
 * @param answers
 * @param state
 * @returns
 */
function initAnswerByStates(answers, state) {
	if (typeof answers != "undefined" && null != answers && answers.length > 0) {
		for (var i = 0; i < answers.length; i++) {
			var ob = answers[i];
			// 有其他填空值时，先赋值
			if (typeof ob.fillVal != "undefined" && ob.fillVal != null) {
				var obDataType = ob.type;
				// 如果为多项填空，则需要更新值
				if (obDataType == "4" || obDataType == "7" || obDataType == "9" || obDataType == "12") {
					if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
						$("#q" + ob.num + "_" + ob.optionVal).val(ob.fillVal);
					}
				} else {
					if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
						if (typeof ob.multiNum != "undefined" && ob.multiNum != null) {
							$("input[rel='q" + ob.num + "_" + ob.optionVal + "_" + ob.multiNum + "']").val(ob.fillVal);
						} else {
							$("input[rel='q" + ob.num + "_" + ob.optionVal + "']").val(ob.fillVal);
						}
					} else {
						$("input[rel='q" + ob.num + "']").val(ob.fillVal);
					}
				}
			} else {
				var obDataType = ob.type;
				if (obDataType == "0" || obDataType == "10") {
					if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
						$("input[name='q" + ob.num + "_" + ob.optionVal + "']").val(ob.value);
					}else{
						$("input[name='q" + ob.num + "'][value='" + ob.value + "']").attr("checked", "checked").click();//有依赖题或跳转题时必须执行点击事件，会根据点击事件判断是否显示题目
					}

				} else if (obDataType == "2" || obDataType == "3" || obDataType == "4" || obDataType == "6" || obDataType == "7" || obDataType == "8" || obDataType == "9"|| obDataType == "12") {
					if (typeof ob.optionVal != "undefined" && ob.optionVal != null) {
						$("#q" + ob.num + "_" + ob.optionVal).val(ob.value);
					} else {
						$("#q" + ob.num).val(ob.value);
					}
				} else if (obDataType == "1") {
					var val = ob.value;
					if (val != '') {
						var array = val.split(',');
						for (var j = 0; j < array.length; j++) {
							var sval = array[j];
							$("input[name='q" + ob.num + "'][value='" + sval + "']").attr("checked", "checked");
						}
					}
				}
			}
			if (ob.tabanswers != undefined && ob.tabanswers.length > 0) {
				for (var j = 0; j < ob.tabanswers.length; j++) {
					if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).size() > 0) {
						if(ob.tabanswers[j].fkByColId.dsType=="2"){
							$("input[name='" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num + "'][value='" + ob.tabanswers[j].colValue + "']").attr("checked","checked");
						}else{
							if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).is("select")) {
								$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).find("option").each(function () {
									if (ob.tabanswers[j].colValue == $(this).attr("value")) {
										$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).parents(".ui-select").find("span").html($(this).html());
									}
								});
							}
							$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).val(ob.tabanswers[j].colValue);
						}

					} else {
						var id = "table" + ob.tabanswers[j].fkByColId.colName.split("_")[0].replace("q", "");
						addRow(id);
						if(ob.tabanswers[j].fkByColId.dsType=="2"){
							$("input[name='" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num + "'][value='" + ob.tabanswers[j].colValue + "']").attr("checked","checked");
						}else{
							$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).val(ob.tabanswers[j].colValue);
							if ($("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).is("select")) {
								$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).find("option").each(function () {
									if (ob.tabanswers[j].colValue == $(this).attr("value")) {
										$("#" + ob.tabanswers[j].fkByColId.colName + "_" + ob.tabanswers[j].num).parents(".ui-select").find("span").html($(this).html());
									}
								})
							}
						}
					}
				}
			}
		}

	}
}

function closeBg() {
	zwxJQ("#fullbg,#msgDialog").hide();
}

function getMaxTimeStr(c) {
	var d = "";
	var b = c;
	var a = parseInt(b / 3600);
	if (a) {
		if (a < 10) {
			d += "0";
		}
		d += a + ":";
		b = b % 3600;
	} else {
		d = "00:";
	}
	var e = parseInt(b / 60);
	if (e) {
		if (e < 10) {
			d += "0";
		}
		d += e + ":";
		b = b % 60;
	} else {
		d += "00:";
	}
	if (b < 0) {
		b = 0;
	}
	if (b) {
		if (b < 10) {
			d += "0";
		}
		d += b;
	} else {
		d += "00";
	}
	return d;
}

/**
 * 问卷调查的公用JS <br/> 依赖页面的布局和Jquery,Jquery的别名:zwxJQ <br/>
 *
 * 一、题型布局 1. 题目: <div class="div_question" id="div3"> <div
 * class="div_title_question_all"> <div class="div_topic_question"> <b>3.1</b>
 * </div> <div id="divTitle3" class="div_title_question"> 是否咳嗽？<span
 * style="color:red;">&#160;*</span> </div> <div style="clear:both;"></div>
 * </div> <div class="div_table_radio_question" id="divquestion3"> <div
 * class="div_table_clear_top"></div>
 * <ul class="ulradiocheck">
 * <li style="width: 99%;"><input type="radio" name="q3" id="q3_1" value="1" /><label
 * for="q3_1">否</label></li>
 * <li style="width: 99%;"><input type="radio" name="q3" id="q3_2" value="2" /><label
 * for="q3_2">是</label></li>
 * <div style="clear:both;"></div>
 * </ul>
 * <div style="clear:both;"></div> <div class="div_table_clear_bottom"></div>
 * </div> <div class="errorMessage"></div> </div>
 *
 * 2. 标题： <div class="div_title_page_question"> <span
 * style="font-size:16px;">3.了解一下您的症状</span><br>
 * </br> </div>
 *
 *
 * 二、跳转题说明 1.静态依赖题（下依赖上） 描述：后面的题目依赖上面的题目所选择的选项
 * 展示效果：后面的题目不显示，当选择所依赖题目的选项的时候，才显示，选择其他选项的时候隐藏掉 标签：<div class="div_question"
 * id="div4" style="display:none;" relation="3,1@2">
 * 标签属性说明：relation表示依赖于第三题的值为1、2的选项 注意：不能依赖多个题目，可以依赖一个题目的多个选项
 *
 * 2.静态跳转题（上跳下） 描述：上面的题目，选择某些选项的时候，跳至下面的某题
 * 展示效果：后面的题目正常显示，当选择某题目的选项的时候，跳至下面的某题，中间的题目隐藏掉 标签：<div class="div_question"
 * id="div4" jumpto="15,1@2;19,3;">
 * 标签属性说明：jump表示当选择该题值为的1或者2的选项的时候，跳至15题，当选择值为3的选项的时候，跳至19题
 *
 * 3.
 *
 *
 *
 * <AUTHOR>
 */

/**
 * 页面初始化加载
 */
function pageInit() {
	/**
	 * 多选题选项互斥
	 */
	zwxJQ("fieldset > div[class*='div_question'][mutex]")
		.each(
			function() {
				var r1 = zwxJQ(this);
				var r1Id = r1.attr("id");
				var r1num = r1Id.replace("div", "");
				var r2 = r1.attr("mutex").split(",");
				if (r1 != null && r1 != undefined) {
					zwxJQ("input[name=q" + r1num + "]")
						.bind(
							"click",
							function() {
								if (!zwxJQ(this)
									.attr("checked")) {
									return;
								}
								var val = zwxJQ(this).attr(
									"value");
								if (r1.attr("mutex").indexOf(
									val) != -1) {
									zwxJQ(
										"input[name=q"
										+ r1num
										+ "]")
										.each(
											function() {
												if (zwxJQ(
													this)
													.attr(
														"value") != val) {
													zwxJQ(
														this)
														.removeAttr(
															"checked");
												}
											});
								} else {
									zwxJQ(
										"input[name=q"
										+ r1num
										+ "]")
										.each(
											function() {
												if (r1
													.attr(
														"mutex")
													.indexOf(
														zwxJQ(
															this)
															.attr(
																"value")) != -1) {
													zwxJQ(
														this)
														.removeAttr(
															"checked");
												}
											});
								}
							});
				}
			});
	/**
	 * 静态依赖题
	 */
	zwxJQ("fieldset > div[class*='div_question'][relation]").each(
		function() {
			var r1 = zwxJQ(this);
			var r1Id = r1.attr("id");
			var r1num = r1Id.replace("div", "");
			if (r1 != null && r1 != undefined) {
				var r2 = r1.attr("relation").split(",");
				// 0-题号 1-触发的选项，可能有多个
				var r3 = "@" + r2[1] + "@";

				zwxJQ("input[name=q" + r2[0] + "]").bind(
					"click",
					function() {
						if (zwxJQ(this).attr("checked")
							&& r3.indexOf("@" + zwxJQ(this).val()
								+ "@") >= 0) {
							r1.css('display', 'block');
						} else {
							relationHide(r1num);
						}
					});
			}
		});

	/**
	 * 静态跳转题 跳过的时候正常隐藏之间的题目； <div class="div_question" id="div4"
	 * jumpto="15,1@2;19,3;">
	 */
	zwxJQ("fieldset > div[class*='div_question'][jumpto]")
		.each(
			function() {
				var r1 = zwxJQ(this);
				var r1Id = r1.attr("id");
				var r1num = r1Id.replace("div", "");
				if (r1 != null && r1 != undefined) {
					var rr = r1.attr("jumpto").split(";");
					// 定义跳转Map，封装值为 当前选项值-->跳转题号
					var map = {};
					for (var i = 0; i < rr.length; i++) {
						// 0-要跳转的题号 1-触发的选项，可能有多个
						var r2 = rr[i].split(",");
						var selValArr = r2[1].split("@");
						for (var j = 0; j < selValArr.length; j++) {
							var key = selValArr[j];
							map[key] = r2[0];
						}
					}

					// 遍历单选框
					zwxJQ("input[name=q" + r1num + "]")
						.bind(
							"click",
							function() {
								if (zwxJQ(this).attr("checked")) {// 如果选中
									var cVal = zwxJQ(this)
										.val();
									// 判断选中是否需要跳转，如果需要跳转，则进行执行反向
									var ifExist = map[cVal];
									for ( var allkey in map) {
										// 当期跳转序号
										var tempVal = map[allkey];
										if (tempVal != ifExist) {
											var r2Id = "div"
												+ map[allkey];
											r1
												.nextAll(
													"div[class*='div_question']")
												.each(
													function() {
														var nextR1Id = zwxJQ(
															this)
															.attr(
																"id");
														if (nextR1Id == r2Id) {
															return false;
														}
														var nextR1Num = nextR1Id
															.replace(
																"div",
																"");

														/**
														 * 先清空里面的值
														 * radio
														 * checkbox
														 * text
														 * textarea
														 */
														zwxJQ(
															"input[name='q"
															+ nextR1Num
															+ "'],textarea[name='q"
															+ nextR1Num
															+ "']")
															.each(
																function() {
																	var nextType = zwxJQ(
																		this)
																		.attr(
																			"type");
																	if (nextType == "radio"
																		|| nextType == "checkbox") {
																		clearSelectGroup(zwxJQ(
																			this)
																			.attr(
																				"name"));
																	} else if (nextType == "text"
																		|| nextType == "textarea") {
																		zwxJQ(
																			this)
																			.val(
																				"");
																	}
																});

														if (typeof (zwxJQ(this)
															.attr("relation")) == "undefined") {
															zwxJQ(
																this)
																.css(
																	'display',
																	'block');
														} else {
															zwxJQ(
																this)
																.css(
																	'display',
																	'none');
														}
													});
										}
									}

									// 如果单选存在，则需要执行跳转逻辑
									if (ifExist != undefined) {
										var r2Id = "div"
											+ map[cVal];
										r1
											.nextAll(
												"div[class*='div_question']")
											.each(
												function() {
													if (zwxJQ(
														this)
														.attr(
															"id") == r2Id) {
														return false;
													}
													// 设置该控件的值为NULL
													var aNum = zwxJQ(
														this)
														.attr(
															"id")
														.replace(
															"div",
															"");
													zwxJQ(
														"input[name='q"
														+ aNum
														+ "'],textarea[name='q"
														+ aNum
														+ "']")
														.each(
															function() {
																var nextType = zwxJQ(
																	this)
																	.attr(
																		"type");
																if (nextType == "radio"
																	|| nextType == "checkbox") {
																	clearSelectGroup(zwxJQ(
																		this)
																		.attr(
																			"name"));
																} else if (nextType == "text"
																	|| nextType == "textarea") {
																	zwxJQ(
																		this)
																		.val(
																			"");
																}
															});
													// 隐藏中间的控件
													zwxJQ(
														this)
														.css(
															'display',
															'none');
												});
									}
								}
							});
				}
			});

	/**
	 * ul li的mouse over, mouse out
	 */
	zwxJQ("ul[class*='ulradiocheck'] > li").mouseover(function() {
		zwxJQ(this).addClass("questionDiv_mouseover");
	}).mouseout(function() {
		zwxJQ(this).removeClass("questionDiv_mouseover");
	});

	bindClickRemoveError();

}

/**
 * 问题div添加选中样式
 */
function bindClickRemoveError() {
	zwxJQ("fieldset > div[class*='div_question']").bind(
		"click",
		function() {
			// 移除其他所有的含有选中的样式
			zwxJQ(".questionDiv_select").removeClass("questionDiv_select");
			zwxJQ(this).children(".errorMessage").html("");
			zwxJQ(this).removeClass("questionDiv_error").addClass(
				"questionDiv_select");
		});
}

/**
 * a依赖b,当b选中不显示a的选项的时候，需要将a的值清空，并且将a隐藏， 同时对依赖于a的控件做同样的操作
 *
 * @param aNum
 * @param bNum
 */
function relationHide(aNum) {
	zwxJQ("input[name='q" + aNum + "'],textarea[name='q" + aNum + "']").each(
		function() {
			var nextType = zwxJQ(this).attr("type");
			if (nextType == "radio" || nextType == "checkbox") {
				clearSelectGroup(zwxJQ(this).attr("name"));
			} else if (nextType == "text" || nextType == "textarea") {
				zwxJQ(this).val("");
			}
		});
	zwxJQ("#div" + aNum).css('display', 'none');

	zwxJQ("fieldset > div[class*='div_question'][relation^='" + aNum + ",']")
		.each(function() {
			relationHide(zwxJQ(this).attr("id").replace("div", ""));
		});
}

var pageAutoSaveInterval;
// 设置定时保存答案
function pageExeAutoSaveResult(autoTi){
	console.log("自动保存答案频率");
	var t = parseInt(autoTi) * 1000;
	console.log(autoTi);
	console.log("自动保存答案频率");
	if (undefined != pageAutoSaveInterval) {
		clearInterval(pageAutoSaveInterval);
	}
	if (t > 0) {
		pageAutoSaveInterval = setInterval (function(){
			pageInvokeAutoSaveFunction();
		}, t);
	}
}
// 通过状态判断 如果已经提交清除定时器
function pageInvokeAutoSaveFunction() {
	var a = $("#opType").val();
	if (undefined == a || (a != 2 && a != 3)) {
		repeatSaveForm();
	} else if (a == 2 || a == 3) {
		// 提交后 清除状态
		clearInterval(pageAutoSaveInterval);
	}
}

// 定时存储题目结果调用
function repeatSaveForm() {
	executeSaveForm(0);
}

var nareSaveState = 0;
var nareRepeatState = 0;
// type为1时暂存、提交调用 type为0时定时存储题目结果调用
function executeSaveForm(type) {
	if (1 == type) {
		nareSaveState = 1;
		if (1 == nareRepeatState) {
			console.log("等待调用定时存储答案接口完成");
			setTimeout(function(){
				executeSaveForm(1);
			}, 1000);
		} else {
			executeSaveOrSubmit();
		}
	}
	if (0 == type && 0 == nareSaveState) {
		nareRepeatState = 1;
		// 设置500毫秒的时间差 避免定时存储时正好页面时间走完定时提交或者点击了暂存或提交
		// 注意 虽然定时存储答案仅存储答案 但会存在删除已有答案后新增答案的情况 定时存储接口调用的同一时间调用暂存提交接口 有可能存在存储双份答案的情况
		setTimeout(function(){
			executeRepeatSave();
		}, 500);
	}
}

// 暂存或者提交
function executeSaveOrSubmit() {
	var state = $("#opType").val();
	console.log("调用暂存、提交接口，状态："+state);
	var initPageParam ={
		'rid':zwxJQ("#mainForm\\:mainId").val(),
		'state':state,
	}
	var tranData = encodeURIComponent(encrypt($('#form').serialize()));
	if(state == 2 ||  state == 3) {
		// 调用失败 重试10次
		exeOnlySubmit(state,tranData,10);
	} else {
		exeOnlySave(state, tranData);
	}
}

// 暂存调用的方法
function exeOnlySave(state, tranData) {
	var initPageParam ={
		'rid':zwxJQ("#mainForm\\:mainId").val(),
		'state':state,
	}
	zwxJQ.ajax({
		cache : false,
		type : "POST",
		url: httpUrlAddr + "/submitQueAns?" +encodeURIComponent( encrypt("dataJson=" + JSON.stringify(initPageParam))),
		data: tranData,
		async : true,
		error : function(request) {
			var da =request.responseText;
			if('' !=da && null != da && undefined != da){
				var data =JSON.parse(decrypt(da));
				dealData(data,state);
				nareSaveState = 0;
			}else {
				alert("网络出现异常，请重试！");
				zwxJQ(".submitbutton").removeAttr("disabled");
				nareSaveState = 0;
				console.log("暂存提交按钮禁用解除");
			}
		},
		success : function(data) {
			dealData(data,state);
			nareSaveState = 0;
		}
	});
}

// 提交调用的方法 提交失败 会重试
function exeOnlySubmit(state,tranData, tranTime) {
	var initPageParam ={
		'rid':zwxJQ("#mainForm\\:mainId").val(),
		'state':state,
	}
	var reTime = parseInt(tranTime);
	zwxJQ.ajax({
		cache : false,
		type : "POST",
		url: httpUrlAddr + "/submitQueAns?" +encodeURIComponent( encrypt("dataJson=" + JSON.stringify(initPageParam))),
		data: tranData,
		async : true,
		error : function(request) {
			var da =request.responseText;
			if('' !=da && null != da && undefined != da){
				var data =JSON.parse(decrypt(da));
				dealData(data,state);
				nareSaveState = 0;
			}else {
				if (reTime == 1) {
					alert("网络出现异常，请重试！");
					zwxJQ(".submitbutton").removeAttr("disabled");
					nareSaveState = 0;
					console.log("暂存提交按钮禁用解除");
				}
				if (reTime > 1) {
					// 间隔一秒重试
					setTimeout(function(){
						console.log("提交重试中。。。");
						reTime = reTime - 1;
						exeOnlySubmit(state, tranData, reTime);
					}, 1000);
				}
			}
		},
		success : function(data) {
			dealData(data,state);
			nareSaveState = 0;
		}
	});
}

var nareRepeatTime;
var nareRepeatReady = 0;
// 判断、执行定时保存答案
function executeRepeatSave() {
	if (1 == nareSaveState) {
		console.log("刚刚执行过暂存、提交，无需重复存储");
		nareRepeatState = 0;
	}
	var curTime = new Date().getTime();
	if (0 == nareSaveState && undefined != nareRepeatTime && (curTime - nareRepeatTime) <= 5000) {
		console.log("禁止高频调用定时存储答案接口");
		nareRepeatState = 0;
	}else if (1 == nareRepeatReady) {
		console.log("已经调用定时存储答案接口未返回，当前调用不执行");
		nareRepeatState = 0;
	}
	if (0 == nareSaveState && 0 == nareRepeatReady && (undefined == nareRepeatTime || (curTime - nareRepeatTime) > 5000)) {
		nareRepeatReady = 1;
		nareRepeatTime = new Date().getTime();
		var initPageParam ={
			'rid':zwxJQ("#mainForm\\:mainId").val()
		}
		zwxJQ.ajax({
			cache : false,
			type : "POST",
			url: httpUrlAddr + "/repeatSaveQueAns?" +encodeURIComponent( encrypt("dataJson=" + JSON.stringify(initPageParam))),
			data: encodeURIComponent(encrypt($('#form').serialize())),
			async : true,
			error : function(request) {
				nareRepeatState = 0;
				nareRepeatReady = 0;
				console.log("调用定时存储答案接口完成");
			},
			success : function(data) {
				nareRepeatState = 0;
				nareRepeatReady = 0;
				console.log("调用定时存储答案接口完成");
			}
		});
	}
}