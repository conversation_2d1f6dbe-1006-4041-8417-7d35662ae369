/**
 * 问卷调查的公用JS <br/>
 * 依赖页面的布局和Jquery,Jquery的别名:$ <br/>
 * 
 * 一、题型布局
 * 1. 题目:
		<div class="div_question" id="div3">
			<div class="div_title_question_all">
				<div class="div_topic_question">
					<b>3.1</b>
				</div>
				<div id="divTitle3" class="div_title_question">
					是否咳嗽？<span style="color:red;">&#160;*</span>
				</div>
				<div style="clear:both;"></div>
			</div>
			<div class="div_table_radio_question" id="divquestion3">
				<div class="div_table_clear_top"></div>
				<ul class="ulradiocheck">
					<li style="width: 99%;"><input type="radio" name="q3" id="q3_1" value="1" /><label for="q3_1">否</label></li>
					<li style="width: 99%;"><input type="radio" name="q3" id="q3_2" value="2" /><label for="q3_2">是</label></li>
					<div style="clear:both;"></div>
				</ul>
				<div style="clear:both;"></div>
				<div class="div_table_clear_bottom"></div>
			</div>
			<div class="errorMessage"></div>
		</div> 
		
	2. 标题：
		<div class="div_title_page_question">
			<span style="font-size:16px;">3.了解一下您的症状</span><br></br>
		</div>			
 * 
 * 
 * 二、跳转题说明
 * 	1.静态依赖题（下依赖上）
		描述：后面的题目依赖上面的题目所选择的选项
		展示效果：后面的题目不显示，当选择所依赖题目的选项的时候，才显示，选择其他选项的时候隐藏掉
 * 		标签：<div class="div_question" id="div4" style="display:none;" relation="3,1@2">
 * 	    标签属性说明：relation表示依赖于第三题的值为1、2的选项
 * 		注意：不能依赖多个题目，可以依赖一个题目的多个选项
 * 
 *  2.静态跳转题（上跳下）
		描述：上面的题目，选择某些选项的时候，跳至下面的某题
		展示效果：后面的题目正常显示，当选择某题目的选项的时候，跳至下面的某题，中间的题目隐藏掉
 * 		标签：<div class="div_question" id="div4" jumpto="15,1@2;19,3;">
 * 	    标签属性说明：jump表示当选择该题值为的1或者2的选项的时候，跳至15题，当选择值为3的选项的时候，跳至19题
 *  
 *  3.
 * 		
 * 
 * 
 * <AUTHOR>
 */

/**
 * 页面初始化加载
 */
$(function() {
	/**
	 * 多选题选项互斥
	 */
	$("fieldset > div[class*='div_question'][mutex]").each(function() {
		var r1 = $(this);
		var r1Id = r1.attr("id");
		var r1num = r1Id.replace("div", "");
		var r2 = r1.attr("mutex").split(",");
		if (r1 != null && r1 != undefined) {
			$("input[name=q" + r1num + "]").bind("click", function() {
				if (!$(this).attr("checked")) {
					return;
				}
				var val = $(this).attr("value");
				if (r1.attr("mutex").indexOf(val) != -1) {
					$("input[name=q" + r1num + "]").each(function() {
						if ($(this).attr("value") != val) {
							$(this).removeAttr("checked");
						}
					});
				} else {
					$("input[name=q" + r1num + "]").each(function() {
						if (r1.attr("mutex").indexOf($(this).attr("value")) != -1) {
							$(this).removeAttr("checked");
						}
					});
				}
			});
		}
	});
	/**
	 * 静态依赖题
	 */
	$("fieldset > div[class*='div_question'][relation]").each(function() {
		var r1 = $(this);
		var r1Id = r1.attr("id");
		var r1num = r1Id.replace("div", "");
		if (r1 != null && r1 != undefined) {
			var r2 = r1.attr("relation").split(",");
			// 0-题号 1-触发的选项，可能有多个
			var r3 = "@" + r2[1] + "@";

			$("input[name=q" + r2[0] + "]").bind("click", function() {
				if ($(this).attr("checked") && r3.indexOf("@" + $(this).val() + "@") >= 0) {
					r1.css('display', 'block');
				} else {
					relationHide(r1num);
				}
			});
		}
	});

	/**
	 * 静态跳转题 跳过的时候正常隐藏之间的题目； <div class="div_question" id="div4"
	 * jumpto="15,1@2;19,3;">
	 */
	$("fieldset > div[class*='div_question'][jumpto]").each(function() {
		var r1 = $(this);
		var r1Id = r1.attr("id");
		var r1num = r1Id.replace("div", "");
		if (r1 != null && r1 != undefined) {
			var rr = r1.attr("jumpto").split(";");
			// 定义跳转Map，封装值为 当前选项值-->跳转题号
			var map = {};
			for (var i = 0; i < rr.length; i++) {
				// 0-要跳转的题号 1-触发的选项，可能有多个
				var r2 = rr[i].split(",");
				var selValArr = r2[1].split("@");
				for (var j = 0; j < selValArr.length; j++) {
					var key = selValArr[j];
					map[key] = r2[0];
				}
			}

			// 遍历单选框
			$("input[name=q" + r1num + "]").bind("click", function() {
				if ($(this).attr("checked")) {// 如果选中
					var cVal = $(this).val();
					// 判断选中是否需要跳转，如果需要跳转，则进行执行反向
					var ifExist = map[cVal];
					for ( var allkey in map) {
						// 当期跳转序号
						var tempVal = map[allkey];
						if (tempVal != ifExist) {
							var r2Id = "div" + map[allkey];
							r1.nextAll("div[class*='div_question']").each(function() {
								var nextR1Id = $(this).attr("id");
								if (nextR1Id == r2Id) {
									return false;
								}
								var nextR1Num = nextR1Id.replace("div", "");

								/**
								 * 先清空里面的值 radio checkbox text textarea
								 */
								// $("input[name='q" + nextR1Num +
								// "'],textarea[name='q" + nextR1Num +
								// "']").each(function() {
								// var nextType = $(this).attr("type");
								// if (nextType == "radio" || nextType ==
								// "checkbox") {
								// clearSelectGroup($(this).attr("name"));
								// } else if (nextType == "text" || nextType ==
								// "textarea") {
								// $(this).val("");
								// }
								// });
								if (typeof ($(this).attr("relation")) == "undefined") {
									$(this).css('display', 'block');
								} else {
									$(this).css('display', 'none');
								}
							});
						}
					}

					// 如果单选存在，则需要执行跳转逻辑
					if (ifExist != undefined) {
						var r2Id = "div" + map[cVal];
						r1.nextAll("div[class*='div_question']").each(function() {
							if ($(this).attr("id") == r2Id) {
								return false;
							}
							// 设置该控件的值为NULL
							var aNum = $(this).attr("id").replace("div", "");
							$("input[name='q" + aNum + "'],textarea[name='q" + aNum + "']").each(function() {
								var nextType = $(this).attr("type");
								if (nextType == "radio" || nextType == "checkbox") {
									clearSelectGroup($(this).attr("name"));
								} else if (nextType == "text" || nextType == "textarea") {
									$(this).val("");
								}
							});
							// 隐藏中间的控件
							$(this).css('display', 'none');
						});
					}
				}
			});
		}
	});

	/**
	 * ul li的mouse over, mouse out
	 */
	$("ul[class*='ulradiocheck'] > li").mouseover(function() {
		$(this).addClass("questionDiv_mouseover");
	}).mouseout(function() {
		$(this).removeClass("questionDiv_mouseover");
	});

	/**
	 * 问题div添加选中样式
	 */
	$("fieldset > div[class*='div_question']").bind("click", function() {
		// 移除其他所有的含有选中的样式
		$(".questionDiv_select").removeClass("questionDiv_select");
		$(this).children(".errorMessage").html("");
		$(this).removeClass("questionDiv_error").addClass("questionDiv_select");
	});

	$("input[type='text']").bind("keyup", function() {
		$('#' + $(this).attr("id").replace("q", "div")).click();
	});

	$("input[type='text']").bind("click", function() {
		$('#' + $(this).attr("id").replace("q", "div")).click();
	});

	// 选项填空题，填空进入焦点选中该选项，选项取消选中清空填空内容
	$("input[rel^='q']").each(function() {
	
		var option_input = $(this);
		$(this).focus(function() {
			if ($("#" + $(this).attr("rel")).attr("type") == "radio") {
				$("#" + $(this).attr("rel")).attr("checked", "checked").click();
			} else {
				$("#" + $(this).attr("rel")).attr("checked", "checked");
			}
		});
		// 多选题
		$($("#" + $(this).attr("rel"))).change(function() {
			if (!"true" == $(this).attr("checked")) {
				option_input.val("");
			}
		});

		var option_name = $("#" + $(this).attr("rel")).attr("name");
		// 单选题
		$(":radio[name='" + option_name + "']").change(function() {
			if ($(this).attr("id") != option_input.attr("name")) {
				option_input.val("");
			}
		});
	});
});

/**
 * a依赖b,当b选中不显示a的选项的时候，需要将a的值清空，并且将a隐藏， 同时对依赖于a的控件做同样的操作
 * 
 * @param aNum
 * @param bNum
 */
function relationHide(aNum) {
	$("input[name^='q" + aNum + "'],textarea[name^='q" + aNum + "']").each(function() {
		var nextType = $(this).attr("type");
		if (nextType == "radio" || nextType == "checkbox") {
			clearSelectGroup($(this).attr("name"));
		} else if (nextType == "text" || nextType == "textarea") {
			$(this).val("");
		}
	});
	$("#div" + aNum).css('display', 'none');

	$("fieldset > div[class*='div_question'][relation^='" + aNum + ",']").each(function() {
		relationHide($(this).attr("id").replace("div", ""));
	});
}
