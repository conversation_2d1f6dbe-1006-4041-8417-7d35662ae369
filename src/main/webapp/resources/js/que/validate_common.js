var FormValid = function(frm) {
	this.frm = frm;
	this.errMsg = new Array();
	this.errName = new Array();

	this.telphone = function(inputObj) {
		if (typeof (inputObj) == "undefined" || inputObj.value.trim() == "") {
			return false;
		}

		var len = inputObj.value.length;
		if (len) {
			var minv = inputObj.getAttribute('zwx:min');
			var maxv = inputObj.getAttribute('zwx:max');
			minv = minv || 0;
			maxv = maxv || Number.MAX_VALUE;
			return minv <= len && len <= maxv;
		}

		return true;
	};

	this.required = function(inputObj) {
		if (typeof (inputObj) == "undefined" || inputObj.value.trim() == "") {
			return false;
		}
		return true;
	};
	
	this.requireSlider = function(inputObj) {
		if (typeof (inputObj) == "undefined" || inputObj.value.trim() == "") {
			return false;
		}
		return true;
	};

	this.eqaul = function(inputObj, formElements) {
		var fstObj = inputObj;
		var sndObj = formElements[inputObj.getAttribute('eqaulName')];

		if (fstObj != null && sndObj != null) {
			if (fstObj.value != sndObj.value) {
				return false;
			}
		}
		return true;
	};

	this.gt = function(inputObj, formElements) {
		var fstObj = inputObj;
		var sndObj = formElements[inputObj.getAttribute('eqaulName')];

		if (fstObj != null && sndObj != null && fstObj.value.trim() != '' && sndObj.value.trim() != '') {
			if (parseInt(fstObj.value) <= parseInt(sndObj.value)) {
				return false;
			}
		}
		return true;
	};
	
	this.lt = function(inputObj, formElements) {
		var fstObj = inputObj;
		var sndObj = formElements[inputObj.getAttribute('eqaulName')];
		if (fstObj != null && sndObj != null && fstObj.value.trim() != '' && sndObj.value.trim() != '') {
			if (parseInt(fstObj.value) > parseInt(sndObj.value)) {
				return false;
			}
		}
		return true;
	};

	
	this.gtDate = function(inputObj, formElements) {
		var fstObj = inputObj;
		var sndObj = formElements[inputObj.getAttribute('eqaulName')];
		if (fstObj != null && sndObj != null && fstObj.value.trim() != '' && sndObj.value.trim() != '') {
			if (new Date(fstObj.value).getTime() < new Date(sndObj.value).getTime()) {
				return false;
			}
		}
		return true;
	};
	
	this.ltNow = function(inputObj, formElements) {
		if (inputObj != null && inputObj.value.trim() != '' ) {
			var input = new Date(inputObj.value);
			if (input.getTime() > new Date().getTime()) {
				return false;
			}
		}
		return true;
	};
	
	this.ltNowYear = function(inputObj, formElements) {
		if (inputObj != null && inputObj.value.trim() != '' ) {
			var input =  parseInt(inputObj.value);
			if (input > new Date().getFullYear()) {
				return false;
			}
		}
		return true;
	};

	this.compare = function(inputObj, formElements) {
		var fstObj = inputObj;
		var sndObj = formElements[inputObj.getAttribute('objectName')];
		if (fstObj != null && sndObj != null && fstObj.value.trim() != '' && sndObj.value.trim() != '') {
			if (!eval('fstObj.value' + inputObj.getAttribute('operate')
					+ 'sndObj.value')) {
				return false;
			}
		}
		return true;
	};

	this.limit = function(inputObj) {
		var len = inputObj.value.length;
		if (len) {
			var minv = inputObj.getAttribute('zwx:min');
			var maxv = inputObj.getAttribute('zwx:max');
			minv = minv || 0;
			maxv = maxv || Number.MAX_VALUE;
			return minv <= len && len <= maxv;
		}
		return true;
	};

	this.range = function(inputObj) {
		var val = parseInt(inputObj.value);
		if (inputObj.value) {
			var minv = inputObj.getAttribute('zwx:min');
			var maxv = inputObj.getAttribute('zwx:max');
			minv = minv || 0;
			maxv = maxv || Number.MAX_VALUE;

			return minv <= val && val <= maxv;
		}
		return true;
	};
	
	this.numberRange = function(inputObj) {
		var val = parseFloat(inputObj.value);
		if (inputObj.value) {
			var minv = inputObj.getAttribute('zwx:min');
			var maxv = inputObj.getAttribute('zwx:max');
			minv = minv || 0;
			maxv = maxv || Number.MAX_VALUE;

			return minv <= val && val <= maxv;
		}
		return true;
	};
	
	this.yearRange = function(inputObj) {
		if (inputObj.value) {
			var val =  parseInt(inputObj.value.substring(0,4));
			var year = new Date().getFullYear();
			var minv = parseInt(inputObj.getAttribute('zwx:min'));
			var maxv = parseInt(inputObj.getAttribute('zwx:max'));
			minv = (year+minv) || year;
			maxv = (year+maxv) || year;
			return minv <= val && val <= maxv;
		}
		return true;
	};
	
	this.requireChecked = function(inputObj) {
		var minv = inputObj.getAttribute('zwx:min');
		var maxv = inputObj.getAttribute('zwx:max');
		minv = minv || 1;
		maxv = maxv || Number.MAX_VALUE;

		var checked = 0;
		var groups = document.getElementsByName(inputObj.name);

		for (var i = 0; i < groups.length; i++) {
			if (groups[i].checked)
				checked++;

		}
		return minv <= checked && checked <= maxv;
	};

	this.filter = function(inputObj) {
		var value = inputObj.value;
		var allow = inputObj.getAttribute('allow');
		if (value.trim()) {
			return new RegExp("^.+\.(?=EXT)(EXT)$".replace(/EXT/g, allow.split(/\s*,\s*/).join("|")), "gi").test(value);
		}
		return true;
	};

	this.isNo = function(inputObj) {
		var value = inputObj.value;
		var noValue = inputObj.getAttribute('noValue');
		return value != noValue;
	};

	this.checkReg = function(inputObj, reg, msg) {
		inputObj.value = inputObj.value.trim();

		if (inputObj.value == '') {
			return;
		} else {
			if (!reg.test(inputObj.value)) {
				this.addErrorMsg(inputObj.name, msg);
			}
		}
	};

	this.passed = function() {
		if (this.errMsg.length > 0) {
			FormValid.showError(this.errMsg, this.errName);
			/*frt = document.getElementsByName(this.errName[0])[0];
			if (null != frt && undefined != frt && frt.type != 'radio' && frt.type != 'checkbox') {
				//不需要focus 否则会出现光标定位到第一个错误文本框 输入后 提交 错误信息不应显示但还在显示的问题
				frt.focus();
			}*/
			return false;
		} else {
			return true;
		}
	};

	this.addErrorMsg = function(name, str) {
		this.errMsg.push(str);
		this.errName.push(name);
	};

	this.addAllName = function(name) {
		FormValid.allName.push(name);
	};

};

FormValid.allName = new Array();
FormValid.showError = function(errMsg, errName) {
	var i = 0;
	for (i = 0; i < errMsg.length; i++) {
		var errDivId=errName[i].split("_")[0].replace("q","div");
		$("#"+errDivId + " > div[class='errorMessage']").html(errMsg[i]);
		$("#"+errDivId).addClass("questionDiv_error");
	}
};

var RegExps = function() {
};

RegExps.isTelphone = /^[-\+]?\d+(\.\d+)?$/;
RegExps.isNumber = /^[-\+]?\d+(\.\d+)?$/;
RegExps.isEmail = /([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)/;
RegExps.isPhone = /^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$/;
RegExps.isMobile = /^(0|86|17951)?(13[0-9]|15[0-9]|17[0-9]|18[0-9]|14[0-9])[0-9]{8}$/;
RegExps.isIdCard = /(^\d{15}$)|(^\d{17}[0-9Xx]$)/;
RegExps.isMoney = /^\d+(\.\d+)?$/;
RegExps.isZip = /^[1-9]\d{5}$/;
RegExps.isQQ = /^[1-9]\d{4,10}$/;
RegExps.isInt = /^[-\+]?\d+$/;
RegExps.isEnglish = /^[A-Za-z]+$/;
RegExps.isChinese = /^[\u0391-\uFFE5]+$/;
RegExps.isUrl = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/;
RegExps.isDate = /^\d{4}-\d{1,2}-\d{1,2}$/;
RegExps.isTime = /^\d{4}-\d{1,2}-\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}$/;
