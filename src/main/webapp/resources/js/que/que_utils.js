/**
 * 清除checkbox,radio选中状态
 *
 * @param name
 */
function clearSelectGroup(name) {
	$("input[name=" + name + "]").each(function() {
		$(this).removeAttr("checked");
	});
	$("input[name^='" + name + "_']").each(function() {
		$(this).val("");
	});
}

/**
 * 获取页面的参数
 *
 * @param name
 * @returns
 */
function resolveUrlParam(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); // 构造一个含有目标参数的正则表达式对象
	var r = window.location.search.substr(1).match(reg); // 匹配目标参数
	if (r != null)
		return decodeURI(r[2]);
	return null; // 返回参数值
}

/**
 * url转json
 */
function getUrlVars() {
	var urlParams = new URLSearchParams(window.location.search);
	var encryptdata = urlParams.get('encryptdata');
	var url = new window.parent.SM2Utils(0).baseSm2Decrypt123(encryptdata);
	var hash;
	var myJson = {};
	var hashes = url.split('&');
	for (var i = 0; i < hashes.length; i++) {
		hash = hashes[i].split('=');
		if(hash[0] != null && hash[0] != ""){
			if(hash.length > 2){
				var reg = new RegExp("(^|&)" + hash[0] + "=([^&]*)(&|$)");
				var v =hashes[i].match(reg); // 匹配目标参数
				var h = ""
				if (v != null){
					h = decodeURI(v[2]);
				}
				myJson[hash[0]] = h ;
			}else{
				myJson[hash[0]] = hash[1] ;
			}

		}
	}
	return myJson;
}
/** 重构表格列id name* */
function refact(tableid) {
	var rowIndex = 1;
	$('#' + tableid + ' tbody tr').each(function() {
		var colIndex = 1;
		$(this).find("td:gt(0)").each(function() {
			/** :input 选择所有 input, textarea, select 和 button元素 */
			$(this).find(':input').each(function() {
				$(this).attr('id', 'q' + tableid.replace('table', '') + '_' + colIndex + '_' + rowIndex);
				$(this).attr('name', 'q' + tableid.replace('table', '') + '_' + colIndex + '_' + rowIndex);
			});
			colIndex++;
		});
		rowIndex++;
	});
};
/** 添行* */
function addRow(id) {
	var selectedRadio = {};
	$('#' + id + ' tbody tr:last td:gt(0)').each(function() {
		$(this).find('input').each(function() {
			var type = $(this).attr('type');
			if(type=='radio'){
				var key = "input[name='"+$(this).attr('name')+"'][value='"+$(this).val()+"']";
				selectedRadio[key] = $(this).is(":checked");
			}
		});
	});
	//将最后一行复制到新的一行 新的一行作为最后一行
	$('#' + id + ' tbody').append('<tr>' + $('#' + id + ' tbody tr:last').html() + '</tr>');
	var rowNum = $('#' + id + ' tbody tr').length;
	var tdNum = 1;
	$('#' + id + ' tbody tr:last td:gt(0)').each(function() {
		//修改下拉组件的id
		$(this).find('select').each(function(){
			$(this).attr('id', 'q' + id.replace('table', '') + '_' + (tdNum) + '_' + rowNum);
			$(this).attr('name', 'q' + id.replace('table', '') + '_' + (tdNum) + '_' + rowNum);
		});
		$(this).find('input').each(function() {
			$(this).attr('id', 'q' + id.replace('table', '') + '_' + (tdNum) + '_' + rowNum);
			$(this).attr('name', 'q' + id.replace('table', '') + '_' + (tdNum) + '_' + rowNum);
			var type = $(this).attr('type');
			if(type=='radio'){
				var val = $(this).attr('value');
				var up = "input[name='q" + id.replace('table', '') + "_" + (tdNum) + '_' + (rowNum-1) + "'][value='" + val + "']";
				var isSelected = selectedRadio[up];
				if(isSelected){//处理上一行选中的值
					$(up).attr("checked",true);
				}
				if($(this).is(":checked")){
					$(this).attr('checked',false);
				}
			}
		});
		tdNum++;
	});
	$('#' + id + ' tbody tr:last .rowRemove').bind("click", function() {
		rowRemove(this);
	});
}

function rowRemove(item) {
	var rowCount = $(item).parents("table").find('tbody tr').length;
	if (rowCount == 1) {
		alert("至少有一条记录！");
		return;
	}
	var tableid = $(item).parents("table").attr("id");
	$(item.parentNode.parentNode).remove();
	refact(tableid);

}
$(function() {
	$('tr .rowRemove').each(function() {
		$(this).bind("click", function() {
			rowRemove(this);
		});
	});
});
