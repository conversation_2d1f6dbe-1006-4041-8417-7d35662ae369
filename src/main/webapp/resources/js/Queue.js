/*
 * @brief: 定义队列类
 * @remark:实现队列基本功能
 */
function QueueZwx(){

    var queue = new Object;
    //存储元素数组
    queue.aElement = new Array();

    /*
    * @brief: 元素入队
    * @param: vElement元素列表
    * @return: 返回当前队列元素个数
    * @remark: 1.EnQueue方法参数可以多个
    *    2.参数为空时返回-1
    */
    queue.EnQueue = function(vElement){
        if (arguments.length == 0)
            return - 1;
        //元素入队
        for (var i = 0; i < arguments.length; i++){
            queue.aElement.push(arguments[i]);
        }
        return queue.aElement.length;
    };
    /*
    * @brief: 元素出队
    * @return: vElement
    * @remark: 当队列元素为空时,返回null
    */
    queue.DeQueue = function(){
        if (queue.aElement.length == 0)
            return null;
        else
            return queue.aElement.shift();
 
    };

    /*
     * 所有元素出列
     */
    queue.DeQueueAll = function(){
        if (queue.aElement.length == 0)
            return null;
        else{
            var tempArr = queue.aElement;
            queue.aElement = new Array();
            return tempArr;
            // return queue.aElement.shift();
        }
    };

    /*
     * 获取指定长度的元素出列
     */
    queue.DeQueueSize = function(leng){
        if('' == leng || leng <= 0 || queue.aElement.length == 0)    {
            return null;
        }else{
            var tempArr = new Array();
            var fetchSize = leng;
            if( queue.aElement.length < leng)    {
                fetchSize = queue.aElement.length;
            }
            for(var i = 0; i< fetchSize ; i++)   {
                tempArr.push(queue.aElement.shift());
            }
            return tempArr;
        }
    };

    /*
    * @brief: 获取队列元素个数
    * @return: 元素个数
    */
    queue.GetSize = function(){
        return queue.aElement.length;
    }
    /*
    * @brief: 返回队头素值
    * @return: vElement
    * @remark: 若队列为空则返回null
    */
    queue.GetHead = function(){
        if (queue.aElement.length == 0)
            return null;
        else
            return queue.aElement[0];
    };
    /*
    * @brief: 返回队尾素值
    * @return: vElement
    * @remark: 若队列为空则返回null
    */
    queue.GetEnd = function(){
        if (queue.aElement.length == 0)
            return null;
        else
            return queue.aElement[queue.aElement.length - 1];
    };
    /*
    * @brief: 将队列置空
    */
    queue.MakeEmpty = function(){
        queue.aElement.length = 0;
    };
    /*
    * @brief: 判断队列是否为空
    * @return: 队列为空返回true,否则返回false
    */
    queue.IsEmpty = function(){
        if (queue.aElement.length == 0)
            return true;
        else
            return false;
    };
    /*
    * @brief: 将队列元素转化为字符串
    * @return: 队列元素字符串
    */
    queue.toString = function(){
        var sResult = (queue.aElement.reverse()).toString();
        queue.aElement.reverse()
        return sResult;
    };
    return queue;
}