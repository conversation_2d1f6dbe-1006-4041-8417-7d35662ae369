/**
 * 将enter键改为tab键
 */
function checkobjvisual(e) {
	var objvis = false;
	if (e) {
		if (e.offsetHeight > 0) {
			objvis = true;
		}
		if (e.offsetTop > 0) {
			objvis = true;
		}
		if (e.readOnly == true) {
			objvis = false;
		}
	}
	return objvis;
}
function focunext(e) {
	var code;
	if (!e) {
		var e = window.event;
	}
	if (e.keyCode) {
		code = e.keyCode;
	} else if (e.which) {
		code = e.which;
	}
	if (code == 13) {
		var inputList = document.getElementsByTagName("input");
		// 循坏这个集合，包括了所有的input。
		var nextindex;
		nextindex = -1;
		for (var i = 0; i < inputList.length; i++) {
			if (inputList[i] == document.activeElement) {
				if (document.activeElement.type != "submit"
						&& document.activeElement.type != "button") {
					var acobj = document.activeElement;
					nextindex = i + 1;

					while ((!checkobjvisual(inputList[nextindex]))
							&& (nextindex <= inputList.length)) {
						nextindex = nextindex + 1;
					}
					if (inputList[nextindex])
						inputList[nextindex].focus();
					break;
				}
			}
		}
	}
}
//将回车键转换为TAB
document.onkeydown = function(e) {
	var code;
	if (!e) {
		var e = window.event;
	}
	if (e.keyCode) {
		code = e.keyCode;
	} else if (e.which) {
		code = e.which;
	}
	if (code == 8) {
		if (document.activeElement.readOnly == true
				|| document.activeElement.disabled == true) {
			return false;
		}
	}
	if (code == 13) {
		if (document.activeElement.type == "textarea") {
			return;
		}
		if (document.activeElement.type != "submit"
				&& document.activeElement.type != "button") {
			focunext(e);
			return false;
		}
	}
}