
/*
 * Superfish v1.4.8 - jQuery menu widget
 * Copyright (c) 2008 <PERSON>
 *
 * Dual licensed under the MIT and GPL licenses:
 * 	http://www.opensource.org/licenses/mit-license.php
 * 	http://www.gnu.org/licenses/gpl.html
 *
 * CHANGELOG: http://users.tpg.com.au/j_birch/plugins/superfish/changelog.txt
 */

;(function(zwxMiniJQ){
	zwxMiniJQ.fn.superfish = function(op){

		var sf = zwxMiniJQ.fn.superfish,
			c = sf.c,
			zwxMiniJQarrow = zwxMiniJQ(['<span class="',c.arrowClass,'"> &#187;</span>'].join('')),
			over = function(){
				var zwxMiniJQzwxMiniJQ = zwxMiniJQ(this), menu = getMenu(zwxMiniJQzwxMiniJQ);
				clearTimeout(menu.sfTimer);
				zwxMiniJQzwxMiniJQ.showSuperfishUl().siblings().hideSuperfishUl();
			},
			out = function(){
				var zwxMiniJQzwxMiniJQ = zwxMiniJQ(this), menu = getMenu(zwxMiniJQzwxMiniJQ), o = sf.op;
				clearTimeout(menu.sfTimer);
				menu.sfTimer=setTimeout(function(){
					o.retainPath=(zwxMiniJQ.inArray(zwxMiniJQzwxMiniJQ[0],o.zwxMiniJQpath)>-1);
					zwxMiniJQzwxMiniJQ.hideSuperfishUl();
					if (o.zwxMiniJQpath.length && zwxMiniJQzwxMiniJQ.parents(['li.',o.hoverClass].join('')).length<1){over.call(o.zwxMiniJQpath);}
				},o.delay);	
			},
			getMenu = function(zwxMiniJQmenu){
				var menu = zwxMiniJQmenu.parents(['ul.',c.menuClass,':first'].join(''))[0];
				sf.op = sf.o[menu.serial];
				return menu;
			},
			addArrow = function(zwxMiniJQa){ zwxMiniJQa.addClass(c.anchorClass).append(zwxMiniJQarrow.clone()); };
			
		return this.each(function() {
			var s = this.serial = sf.o.length;
			var o = zwxMiniJQ.extend({},sf.defaults,op);
			o.zwxMiniJQpath = zwxMiniJQ('li.'+o.pathClass,this).slice(0,o.pathLevels).each(function(){
				zwxMiniJQ(this).addClass([o.hoverClass,c.bcClass].join(' '))
					.filter('li:has(ul)').removeClass(o.pathClass);
			});
			sf.o[s] = sf.op = o;
			
			zwxMiniJQ('li:has(ul)',this)[(zwxMiniJQ.fn.hoverIntent && !o.disableHI) ? 'hoverIntent' : 'hover'](over,out).each(function() {
				if (o.autoArrows) addArrow( zwxMiniJQ('>a:first-child',this) );
			})
			.not('.'+c.bcClass)
				.hideSuperfishUl();
			
			var zwxMiniJQa = zwxMiniJQ('a',this);
			zwxMiniJQa.each(function(i){
				var zwxMiniJQli = zwxMiniJQa.eq(i).parents('li');
				zwxMiniJQa.eq(i).focus(function(){over.call(zwxMiniJQli);}).blur(function(){out.call(zwxMiniJQli);});
			});
			o.onInit.call(this);
			
		}).each(function() {
			var menuClasses = [c.menuClass];
			if (sf.op.dropShadows  && !(zwxMiniJQ.browser.msie && zwxMiniJQ.browser.version < 7)) menuClasses.push(c.shadowClass);
			zwxMiniJQ(this).addClass(menuClasses.join(' '));
		});
	};

	var sf = zwxMiniJQ.fn.superfish;
	sf.o = [];
	sf.op = {};
	sf.IE7fix = function(){
		var o = sf.op;
		if (zwxMiniJQ.browser.msie && zwxMiniJQ.browser.version > 6 && o.dropShadows && o.animation.opacity!=undefined)
			this.toggleClass(sf.c.shadowClass+'-off');
		};
	sf.c = {
		bcClass     : 'sf-breadcrumb',
		menuClass   : 'sf-js-enabled',
		anchorClass : 'sf-with-ul',
		arrowClass  : 'sf-sub-indicator',
		shadowClass : 'sf-shadow'
	};
	sf.defaults = {
		hoverClass	: 'sfHover',
		pathClass	: 'overideThisToUse',
		pathLevels	: 1,
		delay		: 800,
		animation	: {opacity:'show'},
		speed		: 'normal',
		autoArrows	: true,
		dropShadows : true,
		disableHI	: false,		// true disables hoverIntent detection
		onInit		: function(){}, // callback functions
		onBeforeShow: function(){},
		onShow		: function(){},
		onHide		: function(){}
	};
	zwxMiniJQ.fn.extend({
		hideSuperfishUl : function(){
			var o = sf.op,
				not = (o.retainPath===true) ? o.zwxMiniJQpath : '';
			o.retainPath = false;
			var zwxMiniJQul = zwxMiniJQ(['li.',o.hoverClass].join(''),this).add(this).not(not).removeClass(o.hoverClass)
					.find('>ul').hide().css('visibility','hidden');
			o.onHide.call(zwxMiniJQul);
			return this;
		},
		showSuperfishUl : function(){
			var o = sf.op,
				sh = sf.c.shadowClass+'-off',
				zwxMiniJQul = this.addClass(o.hoverClass)
					.find('>ul:hidden').css('visibility','visible');
			sf.IE7fix.call(zwxMiniJQul);
			o.onBeforeShow.call(zwxMiniJQul);
			zwxMiniJQul.animate(o.animation,o.speed,function(){ sf.IE7fix.call(zwxMiniJQul); o.onShow.call(zwxMiniJQul); });
			return this;
		}
	});

})(jQuery);
