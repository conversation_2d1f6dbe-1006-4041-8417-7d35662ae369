.el-scrollbar.ms > .el-scrollbar__wrap {
    overflow-x: hidden;
}

.ms-fragment-in {
    background: #f4f7fa;
}

.content-img img {
    width: 25px;
    height: 25px;
}

.card-table {
    text-align: center;
    float: left;
    padding: 14px;
    margin: 10px 30px 10px 0;
}

.card-table img {
    height: 46px;
}

.card-table:hover {
    background: #f4f8ff;
    border-radius: 8px;
    box-shadow: 0px 0px 7px 0px rgba(157, 218, 249, 0.40) inset;
    cursor: pointer;
}

.ms-occ-hd-add-content-scrollbar > .el-scrollbar__wrap {
    overflow-x: hidden;
    height: calc(100vh - 320px);
    margin-bottom: 0px !important;
}

.two_content {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    float: left;
    background: #ffffff
}


.ms-information-dialog-content img {
    max-width: 100%;
}


.ms-information-dialog-file .ms-information-dialog-file-item {
    font-size: 13px;
    color: #272727;
    font-weight: 400;
    line-height: 20px;
}

.ms-information-dialog-file .ms-information-dialog-file-item span {
    margin-left: 10px;
    color: #20A0FF;
    cursor: pointer;
}


.ms-declare-link-header {
    font-size: 12px;
    letter-spacing: 1px;
    color: #147DE9;
    float: right;
    margin-right: 15px;
    cursor: pointer;
}

.ms-declare-link-header > img {
    position: relative;
    line-height: 16px;
    margin-left: 20px;
    top: 3px;
    letter-spacing: 1px;
    height: 14px;
}

.el-divider {
    background-color: #DCDFE6;
    position: relative;
}

.el-divider--horizontal {
    display: block;
    height: 1px;
    width: 100%;
    margin: 1px;
}

#one {
    padding: 5px 20px 20px 20px;
}

#two {
    padding: 0 20px;
    height: calc(100% - 190px);
}
.myTooltip{
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    cursor:pointer;
    width: 95%;
    font-size: 13px !important;
    font-weight: 500;
}
#notice .ui-tabs {
    height: 100%;
    overflow-y: hidden;
    border: none !important;
}
#notice .ui-tabs-panels {
    height: calc(100% - 40px);
    overflow-y: auto;
}
#notice .ui-tabs-panels::-webkit-scrollbar {
    display: none;
}
.panelDiv{
    height: 38px;
    line-height: 38px;
    border: 1px solid;
    border-color: transparent;
    border-bottom-color: #dadada;
}
.panelTitle{
    margin-left: 20px;
    letter-spacing: 1px;
    font-size: 14px !important;
    font-weight: bold;
    color: #1f2d3d;
}
.panelGrid{
	width: 100%;
}
.panelGrid tr{
	border: transparent;
}
.panelGrid .dateCol{
	width: 80px !important;
    text-align: right;
}
#notice ul {
    border-bottom:1px solid #dadada;
    background-image: none !important;
    background: rgb(255, 255, 255);
}
#notice .textStyle {
    font-size: 12px !important;
    color: #707879;
    font-weight: 400;
    /*line-height: 14px;*/
}
#notice .ui-tabs .ui-tabs-nav .ui-tabs-selected,#notice .ui-tabs .ui-tabs-nav .ui-tabs-active {
    background-image: none !important;
}
#notice .ui-state-default,#notice .ui-widget-content .ui-state-default,#notice .ui-widget-header .ui-state-default {
    background: rgb(255, 255, 255) !important;
}
#notice .ui-tabs .ui-tabs-nav>li{
    font-size: 14px !important;
    height: 39px;
    line-height: 39px;
    border-right: none !important;
    padding: 0 20px !important;
}
.imgNoDataDiv{
	height: 80%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.card-table-tip {
    text-align: center;
    padding: 14px;
    margin: 10px 30px 10px 0;
}

.card-table-tip img {
    height: 46px;
}

.ui-tabs-nav {
     border-radius:6px 6px 0 0;
 }

.ui-tabs .ui-tabs-nav>li:FIRST-CHILD {
    border-radius: 6px 0 0 0;
}

.ui-tabs .ui-tabs-nav>li {
    top: 0 !important;
    outline: none !important;
    margin: 0 !important;
    height: 30px;
    line-height: 29px;
    border: none !important;
    border-right: 1px solid #cdcdcd !important;
    padding: 0 10px !important;
    font-weight: 500 !important;
}

.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
    color: #e17009;
}

.ui-tabs .ui-tabs-nav .ui-tabs-selected a {
    color: #2b2b2b !important;
    font-weight: bold !important;
    border-bottom: solid 3px #577dff;
}

.ui-tabs .ui-tabs-nav>li a {
    outline: none !important;
    padding: 0 !important;
    color: #595959 !important;
    font-weight: 500 !important;
}

.ui-tabs {
    padding: 0 !important;
}