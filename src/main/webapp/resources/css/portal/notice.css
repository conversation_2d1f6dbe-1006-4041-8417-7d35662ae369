.notification-detail-card {
    width: 60%;
    overflow-y: auto;
    margin: 0 auto;
    padding: 0 30px;
    border-radius: unset;
    background: #fafcff;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    border-radius: 8px;
}
.notification-detail-header {
    padding: 50px 30px 0px 30px;/*
            border-bottom: 1px solid #E6E6E6;*/
    margin-bottom: 20px;
}

.notification-detail-title {
    font-size: 22px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: bold;
    text-align: center;
    color: #252525;
    line-height: 30px;
    padding-bottom: 20px;

}
.notification-subtitle {
    font-size: 14px !important;
    font-weight: 400;
    text-align: center;
    color: #575757;
    line-height: 25px;
    padding-left: 1px;
}
.notification-detail-body {
    padding: 0 30px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #000000;
    margin-bottom: 50px;
}

.notification-detail-footer-text {
    display: flex;
    padding-top: 20px;
    padding-bottom: 20px;
    font-size: 14px !important;
    font-weight: 400;
    text-align: left;
    color: #000000;
    line-height: 14px;
}