.panelGrid-right-first tr td:first-child{
	 text-align: right;
	 width: 320px;
 }
.panelGrid-right-first tr:first-child td:first-child{
	text-align: left;
}
.panelGrid-right-first tr:first-child td:first-child span{
	color:#334B9A;padding-left: 15px;
}
.panelGrid-none{
	line-height: 30px;
	padding-top: 4px;
	padding-bottom: 4px;
	width: 100%;
}
.panelGrid-none tr{
	border: transparent !important;
}
.panelGrid-none tr td{
	background: #F7FAFD;
	border: transparent !important;
}
.noNotLeftBorder{
    border-right-color: transparent !important;
    border-top-color: transparent !important;
    border-bottom-color: transparent !important;
}
.noBorder{
    border-color: transparent !important;
}
.diagRightPanel{
	padding:0px;
	background-color: #f6f8f9;
	border: 1px solid #CDCDCD;
	border-radius: 6px;
	background: #FFFFFF;
}
.businessInfo{
	width:1050px;
	margin: auto;
	box-shadow: 0 0 8px 0;
	background: #FFFFFF;
	border: 1px solid #D9DEE4;
	border-radius: 4px;
	margin-top: 35px;
	margin-bottom: 45px;
}
.writeSortPanel{
	width:945px;
	height: 80px;
	margin: auto;
	background: #F7FAFD;
	border: 1px solid #D9DEE4;
	border-radius: 6px;
	border-collapse: separate;
	margin-top: 30px;
	margin-bottom: 5px;
}
.writeSortInfo{
	width: 945px;
	margin: auto;
}
.writeSortInfo td{
	height: 35px;
}
.lineHeight td{
	height: 17px;
}

.column_title {
	text-align: right;
	padding-right: 0px !important;
	font-size: 12px;
	line-height: 15px;
	color: #444444;
	border-right: 0px solid transparent !important;
	width: 135px;
	padding-left: 8px;
}
   
