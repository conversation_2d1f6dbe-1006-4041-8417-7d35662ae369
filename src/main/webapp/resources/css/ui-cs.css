/*布局*/
.cs-none {
    display: none;
}

.cs-flex {
    display: flex;
}

/*Flexbox*/
.cs-flex-row {
    flex-direction: row;
}

.cs-flex-col {
    flex-direction: column;
}

.cs-flex-ac-center {
    align-content: center;
}

.cs-flex-jc-center {
    justify-content: center;
}

.cs-flex-ai-center {
    align-items: center;
}

/*间隔*/
.cs-p-0 {
    padding: 0;
}

.cs-px-0 {
    padding-left: 0;
    padding-right: 0;
}

.cs-py-0 {
    padding-top: 0;
    padding-bottom: 0;
}

.cs-pt-0 {
    padding-top: 0;
}

.cs-pr-0 {
    padding-right: 0;
}

.cs-pb-0 {
    padding-bottom: 0;
}

.cs-pl-0 {
    padding-left: 0;
}

.cs-p-1 {
    padding: 1px;
}

.cs-px-1 {
    padding-left: 1px;
    padding-right: 1px;
}

.cs-py-1 {
    padding-top: 1px;
    padding-bottom: 1px;
}

.cs-pt-1 {
    padding-top: 1px;
}

.cs-pr-1 {
    padding-right: 1px;
}

.cs-pb-1 {
    padding-bottom: 1px;
}

.cs-pl-1 {
    padding-left: 1px;
}

.cs-p-2 {
    padding: 2px;
}

.cs-px-2 {
    padding-left: 2px;
    padding-right: 2px;
}

.cs-py-2 {
    padding-top: 2px;
    padding-bottom: 2px;
}

.cs-pt-2 {
    padding-top: 2px;
}

.cs-pr-2 {
    padding-right: 2px;
}

.cs-pb-2 {
    padding-bottom: 2px;
}

.cs-pl-2 {
    padding-left: 2px;
}

.cs-p-3 {
    padding: 3px;
}

.cs-px-3 {
    padding-left: 3px;
    padding-right: 3px;
}

.cs-py-3 {
    padding-top: 3px;
    padding-bottom: 3px;
}

.cs-pt-3 {
    padding-top: 3px;
}

.cs-pr-3 {
    padding-right: 3px;
}

.cs-pb-3 {
    padding-bottom: 3px;
}

.cs-pl-3 {
    padding-left: 3px;
}

.cs-p-4 {
    padding: 4px;
}

.cs-px-4 {
    padding-left: 4px;
    padding-right: 4px;
}

.cs-py-4 {
    padding-top: 4px;
    padding-bottom: 4px;
}

.cs-pt-4 {
    padding-top: 4px;
}

.cs-pr-4 {
    padding-right: 4px;
}

.cs-pb-4 {
    padding-bottom: 4px;
}

.cs-pl-4 {
    padding-left: 4px;
}

.cs-p-5 {
    padding: 5px;
}

.cs-px-5 {
    padding-left: 5px;
    padding-right: 5px;
}

.cs-py-5 {
    padding-top: 5px;
    padding-bottom: 5px;
}

.cs-pt-5 {
    padding-top: 5px;
}

.cs-pr-5 {
    padding-right: 5px;
}

.cs-pb-5 {
    padding-bottom: 5px;
}

.cs-pl-5 {
    padding-left: 5px;
}

.cs-p-6 {
    padding: 6px;
}

.cs-px-6 {
    padding-left: 6px;
    padding-right: 6px;
}

.cs-py-6 {
    padding-top: 6px;
    padding-bottom: 6px;
}

.cs-pt-6 {
    padding-top: 6px;
}

.cs-pr-6 {
    padding-right: 6px;
}

.cs-pb-6 {
    padding-bottom: 6px;
}

.cs-pl-6 {
    padding-left: 6px;
}

.cs-p-7 {
    padding: 7px;
}

.cs-px-7 {
    padding-left: 7px;
    padding-right: 7px;
}

.cs-py-7 {
    padding-top: 7px;
    padding-bottom: 7px;
}

.cs-pt-7 {
    padding-top: 7px;
}

.cs-pr-7 {
    padding-right: 7px;
}

.cs-pb-7 {
    padding-bottom: 7px;
}

.cs-pl-7 {
    padding-left: 7px;
}

.cs-p-8 {
    padding: 8px;
}

.cs-px-8 {
    padding-left: 8px;
    padding-right: 8px;
}

.cs-py-8 {
    padding-top: 8px;
    padding-bottom: 8px;
}

.cs-pt-8 {
    padding-top: 8px;
}

.cs-pr-8 {
    padding-right: 8px;
}

.cs-pb-8 {
    padding-bottom: 8px;
}

.cs-pl-8 {
    padding-left: 8px;
}

.cs-p-9 {
    padding: 9px;
}

.cs-px-9 {
    padding-left: 9px;
    padding-right: 9px;
}

.cs-py-9 {
    padding-top: 9px;
    padding-bottom: 9px;
}

.cs-pt-9 {
    padding-top: 9px;
}

.cs-pr-9 {
    padding-right: 9px;
}

.cs-pb-9 {
    padding-bottom: 9px;
}

.cs-pl-9 {
    padding-left: 9px;
}

.cs-p-10 {
    padding: 10px;
}

.cs-px-10 {
    padding-left: 10px;
    padding-right: 10px;
}

.cs-py-10 {
    padding-top: 10px;
    padding-bottom: 10px;
}

.cs-pt-10 {
    padding-top: 10px;
}

.cs-pr-10 {
    padding-right: 10px;
}

.cs-pb-10 {
    padding-bottom: 10px;
}

.cs-pl-10 {
    padding-left: 10px;
}

.cs-p-11 {
    padding: 11px;
}

.cs-px-11 {
    padding-left: 11px;
    padding-right: 11px;
}

.cs-py-11 {
    padding-top: 11px;
    padding-bottom: 11px;
}

.cs-pt-11 {
    padding-top: 11px;
}

.cs-pr-11 {
    padding-right: 11px;
}

.cs-pb-11 {
    padding-bottom: 11px;
}

.cs-pl-11 {
    padding-left: 11px;
}

.cs-p-12 {
    padding: 12px;
}

.cs-px-12 {
    padding-left: 12px;
    padding-right: 12px;
}

.cs-py-12 {
    padding-top: 12px;
    padding-bottom: 12px;
}

.cs-pt-12 {
    padding-top: 12px;
}

.cs-pr-12 {
    padding-right: 12px;
}

.cs-pb-12 {
    padding-bottom: 12px;
}

.cs-pl-12 {
    padding-left: 12px;
}

.cs-m-0 {
    margin: 0;
}

.cs-mx-0 {
    margin-left: 0;
    margin-right: 0;
}

.cs-my-0 {
    margin-top: 0;
    margin-bottom: 0;
}

.cs-mt-0 {
    margin-top: 0;
}

.cs-mr-0 {
    margin-right: 0;
}

.cs-mb-0 {
    margin-bottom: 0;
}

.cs-ml-0 {
    margin-left: 0;
}

.cs-m-1 {
    margin: 1px;
}

.cs-mx-1 {
    margin-left: 1px;
    margin-right: 1px;
}

.cs-my-1 {
    margin-top: 1px;
    margin-bottom: 1px;
}

.cs-mt-1 {
    margin-top: 1px;
}

.cs-mr-1 {
    margin-right: 1px;
}

.cs-mb-1 {
    margin-bottom: 1px;
}

.cs-ml-1 {
    margin-left: 1px;
}

.cs-m-2 {
    margin: 2px;
}

.cs-mx-2 {
    margin-left: 2px;
    margin-right: 2px;
}

.cs-my-2 {
    margin-top: 2px;
    margin-bottom: 2px;
}

.cs-mt-2 {
    margin-top: 2px;
}

.cs-mr-2 {
    margin-right: 2px;
}

.cs-mb-2 {
    margin-bottom: 2px;
}

.cs-ml-2 {
    margin-left: 2px;
}

.cs-m-3 {
    margin: 3px;
}

.cs-mx-3 {
    margin-left: 3px;
    margin-right: 3px;
}

.cs-my-3 {
    margin-top: 3px;
    margin-bottom: 3px;
}

.cs-mt-3 {
    margin-top: 3px;
}

.cs-mr-3 {
    margin-right: 3px;
}

.cs-mb-3 {
    margin-bottom: 3px;
}

.cs-ml-3 {
    margin-left: 3px;
}

.cs-m-4 {
    margin: 4px;
}

.cs-mx-4 {
    margin-left: 4px;
    margin-right: 4px;
}

.cs-my-4 {
    margin-top: 4px;
    margin-bottom: 4px;
}

.cs-mt-4 {
    margin-top: 4px;
}

.cs-mr-4 {
    margin-right: 4px;
}

.cs-mb-4 {
    margin-bottom: 4px;
}

.cs-ml-4 {
    margin-left: 4px;
}

.cs-m-5 {
    margin: 5px;
}

.cs-mx-5 {
    margin-left: 5px;
    margin-right: 5px;
}

.cs-my-5 {
    margin-top: 5px;
    margin-bottom: 5px;
}

.cs-mt-5 {
    margin-top: 5px;
}

.cs-mr-5 {
    margin-right: 5px;
}

.cs-mb-5 {
    margin-bottom: 5px;
}

.cs-ml-5 {
    margin-left: 5px;
}

.cs-m-6 {
    margin: 6px;
}

.cs-mx-6 {
    margin-left: 6px;
    margin-right: 6px;
}

.cs-my-6 {
    margin-top: 6px;
    margin-bottom: 6px;
}

.cs-mt-6 {
    margin-top: 6px;
}

.cs-mr-6 {
    margin-right: 6px;
}

.cs-mb-6 {
    margin-bottom: 6px;
}

.cs-ml-6 {
    margin-left: 6px;
}

.cs-m-7 {
    margin: 7px;
}

.cs-mx-7 {
    margin-left: 7px;
    margin-right: 7px;
}

.cs-my-7 {
    margin-top: 7px;
    margin-bottom: 7px;
}

.cs-mt-7 {
    margin-top: 7px;
}

.cs-mr-7 {
    margin-right: 7px;
}

.cs-mb-7 {
    margin-bottom: 7px;
}

.cs-ml-7 {
    margin-left: 7px;
}

.cs-m-8 {
    margin: 8px;
}

.cs-mx-8 {
    margin-left: 8px;
    margin-right: 8px;
}

.cs-my-8 {
    margin-top: 8px;
    margin-bottom: 8px;
}

.cs-mt-8 {
    margin-top: 8px;
}

.cs-mr-8 {
    margin-right: 8px;
}

.cs-mb-8 {
    margin-bottom: 8px;
}

.cs-ml-8 {
    margin-left: 8px;
}

.cs-m-9 {
    margin: 9px;
}

.cs-mx-9 {
    margin-left: 9px;
    margin-right: 9px;
}

.cs-my-9 {
    margin-top: 9px;
    margin-bottom: 9px;
}

.cs-mt-9 {
    margin-top: 9px;
}

.cs-mr-9 {
    margin-right: 9px;
}

.cs-mb-9 {
    margin-bottom: 9px;
}

.cs-ml-9 {
    margin-left: 9px;
}

.cs-m-10 {
    margin: 10px;
}

.cs-mx-10 {
    margin-left: 10px;
    margin-right: 10px;
}

.cs-my-10 {
    margin-top: 10px;
    margin-bottom: 10px;
}

.cs-mt-10 {
    margin-top: 10px;
}

.cs-mr-10 {
    margin-right: 10px;
}

.cs-mb-10 {
    margin-bottom: 10px;
}

.cs-ml-10 {
    margin-left: 10px;
}

.cs-m-11 {
    margin: 11px;
}

.cs-mx-11 {
    margin-left: 11px;
    margin-right: 11px;
}

.cs-my-11 {
    margin-top: 11px;
    margin-bottom: 11px;
}

.cs-mt-11 {
    margin-top: 11px;
}

.cs-mr-11 {
    margin-right: 11px;
}

.cs-mb-11 {
    margin-bottom: 11px;
}

.cs-ml-11 {
    margin-left: 11px;
}

.cs-m-12 {
    margin: 12px;
}

.cs-mx-12 {
    margin-left: 12px;
    margin-right: 12px;
}

.cs-my-12 {
    margin-top: 12px;
    margin-bottom: 12px;
}

.cs-mt-12 {
    margin-top: 12px;
}

.cs-mr-12 {
    margin-right: 12px;
}

.cs-mb-12 {
    margin-bottom: 12px;
}

.cs-ml-12 {
    margin-left: 12px;
}


/*尺寸*/

.cs-w-0 {
    width: 0;
}

.cs-w-10 {
    width: 10px;
}

.cs-w-20 {
    width: 20px;
}

.cs-w-20 {
    width: 20px;
}

.cs-w-30 {
    width: 30px;
}

.cs-w-40 {
    width: 40px;
}

.cs-w-50 {
    width: 50px;
}

.cs-w-60 {
    width: 60px;
}

.cs-w-70 {
    width: 70px;
}

.cs-w-80 {
    width: 80px;
}

.cs-w-90 {
    width: 90px;
}

.cs-w-100 {
    width: 100px;
}

.cs-w-120 {
    width: 120px;
}

.cs-w-140 {
    width: 140px;
}

.cs-w-160 {
    width: 160px;
}

.cs-w-180 {
    width: 180px;
}

.cs-w-200 {
    width: 200px;
}

.cs-w-220 {
    width: 220px;
}

.cs-w-240 {
    width: 240px;
}

.cs-w-260 {
    width: 260px;
}

.cs-w-280 {
    width: 280px;
}

.cs-w-300 {
    width: 300px;
}

.cs-w-auto {
    width: auto;
}

.cs-w-full {
    width: 100%;
}

.cs-w-screen {
    width: 100vw;
}

.cs-h-0 {
    height: 0;
}

.cs-h-20 {
    height: 20px;
}

.cs-h-29 {
    height: 29px;
}

.cs-h-38 {
    height: 38px;
}

.cs-h-auto {
    height: auto;
}

.cs-h-full {
    height: 100%;
}

.cs-h-screen {
    height: 100vw;
}

/*排版*/
.cs-tal {
    text-align: left;
}

.cs-tac {
    text-align: center;
}

.cs-tar {
    text-align: right;
}

.cs-fs-12 {
    font-size: 12px;
}

.cs-fs-13 {
    font-size: 13px;
}

.cs-fs-14 {
    font-size: 14px;
}

/*背景*/
/*边框*/

/*自定义*/
/*标题*/
.cs-title {
    text-align: left;
    padding-left: 5px;
    height: 20px;
}

/*查询条件label*/
.cs-scl {
    text-align: right;
    padding-right: 3px;
}

.cs-scl-first {
    text-align: right;
    padding-right: 3px;
    width: 160px;
    height: 38px;
}

.cs-scl-w {
    text-align: right;
    padding-right: 3px;
    width: 160px;
}

.cs-scl-h {
    text-align: right;
    padding-right: 3px;
    height: 38px;
}

/*查询条件value*/
.cs-scv {
    text-align: left;
    padding-left: 8px !important;
}

.cs-scv-w {
    text-align: left;
    padding-left: 8px !important;
    width: 260px;
}

/*不可选择文字*/
.cs-none-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/*文字单词自动直接换行*/
.cs-break-word {
    word-wrap: break-word;
    word-break: break-all;
}

/*必填标记*/
.cs-required:before {
    content: '*';
    color: red;
}

/*不显示但占位*/
.cs-hidden {
    visibility: hidden;
    height: 0;
    margin: 0;
    padding: 0;
    border: none;
    width: 1px;
}