.annex_info_table {
    width: 100%;
    background: #2D2D2D;
    box-shadow: 0 -2 px 0 0 rgba(5, 5, 5, 0 .50), 0 -1 px 0 0 rgba(255, 255, 255, 0 .41);
    color: #FFFFFF;
    font-size: 12px;
    font-family: microsoft yahei;
}

.annex_info_table tr {
    height: 25px;
}

.annex_info_table td {
    text-align: left;
    padding-left: 20px;
}

.annex_view_dialog  .ui-dialog-content {
    background-color: #1F1F1F !important;
    border: none;
    box-shadow: 0 11px 7px 0 rgba(0, 0, 0, 0.25);
    padding: 0;
    position: relative;
}

.annex_content_table {
    width: 100%;
    height: 475px;
}

.annex_link {
    width: 50px;
}

.annex_link a {
    width: 28px;
    height: 48px;
    display: block;
}

.annex_pre {
    background-image: url("/resources/images/heth/annex_pre.png");
    margin-left: 20px;
}

.annex_next {
    background-image: url("/resources/images/heth/annex_next.png");
    margin-right: 20px;
}
.fullscreen {
    height:32px;
    width:786px;
    background:#000000;
    position: absolute;
    top:0px;
    z-index:999;
    opacity:0.8;
    display: none;
}

.fullscreen span{
    width:16px;
    height:16px;
    display: block;
    background: url("/resources/images/heth/fullscreen.png") no-repeat;
    margin:8px auto;
    cursor: pointer;
}
.annex_view_dialog .ui-dialog-titlebar {
    background-image: linear-gradient(to bottom, #f5f5f5 1%, #eeeeee 98%) !important;
    border-bottom: solid 1px #cdcdcd !important;
}
.annex_view_dialog .ui-dialog-title {
    color: #444444;
    font-weight: bold;
}
.annex_view_dialog .ui-icon {
    background-image: url("/resources/primeui/themes/redmond/images/ui-icons_469bdd_256x240.png") !important;
}
.annex_view_dialog .ui-dialog-content {
    overflow: hidden;
}
.icon-alert{
    background-image: url(/resources/images/alert-tip.png) !important;
    background-size: 12px 12px;
    margin-left: 3px;
    margin-top: -6px !important;
}