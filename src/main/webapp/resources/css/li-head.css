@CHARSET "UTF-8";
/**
 * 用于ul li 菜单样式，卫生应急事件处置界面
 */
.header .menu {z-index: 500; width: 100%;height: 52px;background: url(/resources/images/menu-list-bg-index.gif) 0 0 repeat-x;}

.header .menu ul.menu-list {/*width: 520px;_width: 540px;*/width: 100%; margin-top: -16px;padding-left: 10px;}

.header .menu ul.menu-list li { position: relative;z-index: 500;float: left;margin: 0 -1px;width: 67px; *width:64px; height: 50px;line-height: 50px;text-align: center; font-size: 13px;list-style-type: none;}

.header .menu ul.menu-list li.menu-list-mx2 {width: 72px;}

.header .menu ul.menu-list li.menu-list-mx3 { width: 69px;}

.header .menu ul.menu-list li.menu-list-mm { width: 82px;}

.header .menu ul.menu-list li.menu-list-passport {width: 88px;}

.header .menu ul.menu-list li.menu-list-lab { width: 101px;}

.header .menu ul.menu-list li a {display: block; _display: inline-block;padding: 0 0 0 2px; _width: 100%;height: 50px; color: #b8cede;
}

.header .menu ul.menu-list li a span { display: block;_display: inline-block;padding: 1px 2px 0 0;_width: 100%;cursor: pointer;}

.header .menu ul.menu-list li.current {background: url(/resources/images/menu-list-current-li-bg.gif) center top no-repeat;}

.header .menu ul.menu-list li.current a { color: #fff;font-weight: bold;text-decoration: none; background: url(/resources/images/menu-list-current-bg.gif) left top no-repeat;}

.header .menu ul.menu-list li.current a span {background: url(/resources/images/menu-list-current-bg.gif) right top no-repeat;}

.header .menu ul.menu-list li a:hover {color: #fff;text-decoration: none;white-space: nowrap;background: url(/resources/images/menu-list-hover-bg.gif) left 2px no-repeat;}

.header .menu ul.menu-list li a:hover span {background: url(/resources/images/menu-list-hover-bg.gif) right 2px no-repeat;}

.header .menu ul.menu-list li ul.menu-sublist {display: none; position: absolute;top: 50px;left: 2px; z-index: 60; padding: 5px 1px 10px 1px; width: 153px;background: #003b81;}

.header .menu ul.menu-list li.menu-list-bbs ul.menu-sublist { width: 150px;}

.header .menu ul.menu-list li.menu-list-company ul.menu-sublist {
    left: -93px;
    _left: -90px;
}

.header .menu ul.menu-list li ul.menu-sublist li {
    float: none;
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: left;
    border-top: 1px solid #184a86;
    background: none;
}

.header .menu ul.menu-list li.menu-list-company ul.menu-sublist li {
    text-align: right;
}

.header .menu ul.menu-list li ul.menu-sublist li.menu-sublist-li1 {
    border-top: 0;
}

.header .menu ul.menu-list li ul.menu-sublist li a {
    padding: 0 0 0 15px;
    height: 30px;
    color: #fff;
    font-size: 12px;
    font-weight: normal;
    background: none;
}

.header .menu ul.menu-list li.menu-list-company.hover ul.menu-sublist li a, 

.header .menu ul.menu-list li.menu-list-company ul.menu-sublist li a {
    padding: 0 15px 0 0;
    width: 139px;
}

.header .menu ul.menu-list li ul.menu-sublist li a:hover {
    font-weight: normal;
    background: #01326b;
}