* {
	margin: 0px;
	padding: 0px;
}

body {
	font-size: 16px;
	-webkit-user-select: none;
	-webkit-text-size-adjust: none;
	font-family: "Microsoft Yahei", helvetica;
	background: #f7f7f7;
	color: #333;
	position: relative;
	word-break: break-all;
	word-wrap: break-word;
}

*,*:after,*:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

ul,li {
	list-style: none;
}

a,button,label,textarea,div,select,option {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

input {
	outline: none;
}

input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button
	{
	-webkit-appearance: none;
	appearance: none;
	margin: 0;
	-moz-appearance: textfield;
}

a,a:visited {
	text-decoration: none;
	color: #45AFE3;
}

.clear:after {
	clear: both;
	content: ".";
	display: block;
	height: 0;
	visibility: hidden;
}

.i_header {
	display: inline-block;
	width: 100%;
	text-align: center;
	padding: 0 20px 0 20px;
}

.header_title {
	padding: 15px;
	color: #fff;
	text-align: left;
	background: #1C668B;
}

.header_t {
	width: 100%;
	height: 15px;
	text-align: right;
}

.header_c {
	width: 100%;
	margin: 0 auto;
}

.logo {
	width: 183px;
	display: block;
	height: 76px;
	margin: 30px auto 20px;
	background-image: url(/images/weixin/mobileLogo.png);
	background-repeat: no-repeat;
	background-position: 0 0;
	background-size: cover;
}

/*---------------------*/
.content {
	padding: 10px 7%;
}

.button {
	width: 100%;
	display: block;
	margin: 10px 0px;
	outline: none;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	font-size: 1.2em;
	padding: 9px 0px 9px;
	border-radius: .2em;
}

.button:hover {
	text-decoration: none;
}

.button:active {
	position: relative;
	top: 1px;
}

.orange,.orange:visited {
	color: #ffffff;
	background: #e87814;
}

.orange:hover {
	background: #f47c20;
}

.blue,blue:visited {
	color: #F7F7F7;
	background: #1ea0fa;
}

.blue:hover {
	background: #0078c8;
}

.white {
	background: White;
	color: #333;
	color: #000;
	box-shadow: none;
	border-radius: 16px;
	border: 1px solid #ccc;
}

.logofooter {
	background-color: #4b4b4b;
	float: left;
	width: 100%;
}

.fixedbottom {
	bottom: 0;
	left: 0;
	margin: 0;
	position: fixed;
	width: 100%;
}

.wjfooter {
	height: 30px;
	line-height: 30px;
	margin: 5px 0;
	text-align: center;
	color: #fff;
	font-size: 14px;
}

.logofooter a {
	color: #fff;
	font-size: 14px;
	text-decoration: none;
}

fieldset {
	border: none;
}

.ui-controlgroup {
	margin: 5px 0;
	padding: 0;
	border: 1px solid #d5d5d5;
	border-radius: 4px;
}

.ui-controlgroup .ui-radio,.ui-controlgroup .ui-checkbox,.ui-controlgroup .ui-li-static
	{
	background: none repeat scroll 0 0 #fff;
	border-bottom: 1px solid #ebebeb;
	font-size: 16px;
	min-height: 43px;
	position: relative;
}

.ui-li-static {
	padding: 11px 0;
}

.ui-controlgroup .ui-radio:first-child,.ui-controlgroup .ui-checkbox:first-child,.ui-controlgroup .ui-li-static:first-child
	{
	border-radius: 4px 4px 0 0;
}

.ui-controlgroup .ui-radio:last-child,.ui-controlgroup .ui-checkbox:last-child,.ui-controlgroup .ui-li-static:last-child
	{
	border-radius: 0 0 4px 4px;
	border-bottom: 0 solid #d5d5d5;
}

.ui-controlgroup .ui-radio .label,.ui-controlgroup .ui-checkbox .label,.ui-controlgroup .ui-radio label,.ui-controlgroup .ui-checkbox label
	{
	display: block;
	margin-left: 40px;
	padding: 12px 10px 10px 0;
	cursor: pointer;
}

.ui-controlgroup .ui-radio .hasImagelabel,.ui-controlgroup .ui-checkbox .hasImagelabel
	{
	margin-left: 10px;
}

.ui-text {
	border: 1px solid #d5d5d5;
	margin: 0 10px 0 40px;
	border-radius: 2px;
	padding: 0;
}

.ui-text input {
	border: none !important;
	border-radius: 2px;
	font-size: 16px;
	margin: 0;
	padding: 8px 5px;
	width: 100%;
	background: #fff;
	display: inline-block;
	-webkit-appearance: none;
}

.ui-input-text {
	border: 1px solid #d5d5d5;
	margin: 5px 0;
	background-color: #fff;
	padding: 0;
	border-radius: 3px;
}

.ui-input-text input,.ui-input-text textarea {
	width: 100%;
	background-color: #fff;
	border: none !important;
	border-radius: 5px;
	padding: 8px 10px;
	font-size: 16px;
	display: inline-block;
	margin: 0;
	-webkit-appearance: none;
}

.ui-text input:focus,.ui-input-text input:focus,.ui-input-text textarea:focus
	{
	box-shadow: 0 0 12px #3388cc;
	outline: none;
}

.nofocus input:focus {
	box-shadow: none;
}

.ui-select {
	background-color: #ffffff;
	border: 1px solid #d5d5d5;
	border-radius: 5px;
	padding: 7px 10px;
	position: relative;
}

.divlabel {
	text-align: left !important;
	padding: 5px !important;
	background: #f9f9f9;
	font-size: 20px;
	color: #444;
	border-radius: 4px 4px 0 0;
}

input.ui-slider-input {
	border: 1px solid #d5d5d5;
	border-radius: 2px;
	overflow: auto;
	display: block;
	float: left;
	font-size: 14px;
	font-weight: 700;
	margin: 0;
	padding: 2px 4px;
	width: 40px;
	height: 20px;
	line-height: 20px;
	border-width: 1px;
	border-style: solid;
	outline: 0;
	text-align: center;
	vertical-align: text-bottom;
	appearance: none;
	box-sizing: content-box;
}

.ui-select select {
	height: 40px;
	left: 0;
	opacity: 0;
	outline: medium none;
	position: absolute;
	top: -3px;
	width: 100%;
	font-size: 16px;
	line-height: 40px;
}

.ui-select .arrowT {
	border-bottom: medium none !important;
	font-size: 30px !important;
	margin: 0 0 0 10px;
	padding: 0;
	position: absolute;
	right: 20px;
	top: 14px;
	vertical-align: middle;
}

.ui-select .arrowt {
	border-color: #9a9a9a transparent transparent;
	border-style: solid dashed dashed;
	border-width: 10px 10px 0;
	color: inherit;
	font: 0px/9999px serif;
	height: 0;
	overflow: hidden;
	padding: 0 !important;
	width: 0;
	z-index: 1;
}

iframe[video='1'] {
	width: 100% !important;
	height: 298px !important;
}

.jqradio {
	-webkit-appearance: none;
	background-color: #fafafa;
	border: 1px solid #cacece;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px
		rgba(0, 0, 0, 0.05);
	padding: 9px;
	border-radius: 50px;
	display: inline-block;
	position: relative;
}

.jqradiowrapper a.jqchecked {
	background-color: #53a4f4;
	color: #99a1a7;
	border: 1px solid #53a4f4;
}

.jqradiowrapper a.jqchecked:after {
	content: ' ';
	width: 10px;
	height: 10px;
	border-radius: 50px;
	position: absolute;
	top: 4px;
	background: #fff;
	text-shadow: 0px;
	left: 4px;
	font-size: 32px;
}

a.jqcheck {
	background-color: #fafafa;
	border: 1px solid #cacece;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px
		rgba(0, 0, 0, 0.05);
	padding: 9px;
	border-radius: 3px;
	display: inline-block;
	position: relative;
}

.jqcheckwrapper a.jqchecked {
	background-color: #53a4f4 !important;
	background: url("/resources/images/que/checkedbox.png") no-repeat scroll center top
		transparent;;
	background-size: cover;
	display: block;
	height: 21px;
	vertical-align: middle;
	width: 21px;
	border: none;
}

.jqradiowrapper,.jqcheckwrapper {
	display: block;
	float: left;
	margin: 11px 4px 10px 11px;
}

.ui-select span {
	font-size: 18px;
	margin: 5px 0;
}

.ui-block-a,.ui-block-b {
	margin: 0;
	padding: 0;
	border: 0;
	float: left;
	width: 48%;
	margin: 0 1%;
}

.ui-block-b {
	
}

#divQuestion {
	padding-top: 10px;
}

div.field {
	clear: both;
	margin: 4px 5px;
	padding: 0 7px;
	border: 2px solid #f7f7f7;
}

div.formfield {
	display: block;
	padding: 5px 5px 10px;
	margin: 15px 5px 10px;
	clear: both;
	border-bottom: 1px dotted #ccc;
}

span.description {
	font-size: 90%;
}

span.req {
	color: red !important;
	margin: 2px 0 0 2px;
}

span.error {
	display: none;
}

div.field-label {
	font-size: 16px !important;
	font-weight: bold !important;
	margin: 10px 0 10px 0 !important;
	display: block;
	word-wrap: break-word;
	overflow: hidden;
}

div.ValError {
	color: Red;
	display: block;
	font-size: 20px;
	text-align: center;
}

#ctlPrev {
	float: left;
}

h1 {
	text-shadow: none;
	font-weight: 700;
	font-size: 20px;
}

table.matrix-rating {
	width: 100%;
	background: #fff;
	padding: 10px;
}

table.scale-rating th,table.matrix-rating th {
	text-align: center;
	font-size: 14px;
	font-weight: normal;
	padding: 5px;
	word-break: break-all;
}

table.scale-rating {
	width: 100%;
}

.scale-div {
	width: 100%;
	background: #fff;
	padding: 0 15px;
}

table.scale-rating th {
	padding: 8px 0;
}

table.scale-rating td {
	text-align: center;
	padding: 10px 0px;
	border-top: 1px solid #dbdbdb;
	width: 32px;
}

table.matrix-rating td.title {
	text-align: left;
	padding: 3px 0;
	font-size: 16px;
}

table.matrix-rating td {
	text-align: center;
	padding: 6px 0 20px 0;
}

table.scale-rating a.rate-off,table.matrix-rating a.rate-off {
	width: 21px;
	height: 21px;
	text-decoration: none;
	border-radius: 30px;
	color: #333;
	/*box-shadow: 0 1px 2px #606060*/
	border: 1px solid #cacece;
	cursor: default;
	display: inline-block;
	background: #fafafa;
	line-height: 19px;
}

table.scale-rating a.rate-offlarge,table.matrix-rating a.rate-offlarge {
	width: 26px;
	height: 26px;
	line-height: 23px;
}

table.scale-rating a.rate-offlarge:after,table.matrix-rating a.rate-offlarge:after
	{
	top: 7px !important;
	left: 8px !important;
}

table.scale-rating a.rate-on,table.matrix-rating a.rate-on {
	background-color: #53a4f4 !important;
	border: 1px solid #53a4f4;
	position: relative;
	color: #fff;
}

table.scale-rating a.rate-ontxt:after,table.matrix-rating a.rate-ontxt:after
	{
	content: ' ';
	width: 9px;
	height: 9px;
	border-radius: 50px;
	position: absolute;
	top: 5px;
	background: #fff;
	text-shadow: 0px;
	left: 5px;
	font-size: 32px;
}

table.matrix-rating a.rate-onchk {
	background: url("/resources/images/que/checkedbox.png") no-repeat scroll center top
		transparent;
	background-size: cover;
	display: inline-block;
	height: 21px;
	vertical-align: middle;
	width: 21px;
	border: none;
}

table.matrix-rating a.rate-off2,table.scale-rating a.rate-off2 {
	background: url(/images/wjx/JoinQuestionnaire/Rate/wjxstar.png);
	border-radius: 0;
	box-shadow: none;
	border: 0;
	padding: 12px;
}

table.matrix-rating a.rate-on2,table.scale-rating a.rate-on2 {
	background: url(/images/wjx/JoinQuestionnaire/Rate/wjxstar.png)
		!important;
	background-position: 0 -28px !important;
}

table.matrix-rating a.rate-off3,table.scale-rating a.rate-off3 {
	background: url(/images/wjx/JoinQuestionnaire/Rate/wjxlike.png);
	border-radius: 0;
	box-shadow: none;
	border: 0;
	padding: 12px;
}

table.matrix-rating a.rate-on3,table.scale-rating a.rate-on3 {
	background: url(/images/wjx/JoinQuestionnaire/Rate/wjxlike.png)
		!important;
	background-position: 0 -28px !important;
}

table.matrix-rating a.rate-off4,table.scale-rating a.rate-off4 {
	background: url(/images/wjx/JoinQuestionnaire/Rate/wjxheart.png);
	border-radius: 0;
	box-shadow: none;
	border: 0;
	padding: 12px;
}

table.matrix-rating a.rate-on4,table.scale-rating a.rate-on4 {
	background: url(/images/wjx/JoinQuestionnaire/Rate/wjxheart.png)
		!important;
	background-position: 0 -27px !important;
}

.onscore {
	background: url(/images/wjx/JoinQuestionnaire/Rate/piont_lv.png);
	background-color: #FFFFFF;
	overflow: hidden;
	width: 355px;
	height: 20px;
}

table.matrix-rating a.rate-off6,table.scale-rating a.rate-off6 {
	background: #fff;
	text-align: center;
	cursor: pointer;
	border-radius: 0;
	box-shadow: none;
	padding: 6px 0;
	color: #444;
	width: 100%;
	border: solid 1px #dbdbdb;
	border-left: 0;
	border-right: 0;
	height: 31px;
}

table.matrix-rating a.rate-on6,table.scale-rating a.rate-on6 {
	background: none !important;
	text-align: center;
	cursor: pointer;
}

.ui-body-b {
	background: #EEEEEE !important;
}

div.mobosurvey {
	margin: 20px 10px 10px 20px;
	text-align: center;
	font-weight: 700;
}

.powby {
	font-size: 14px;
	font-weight: normal;
	text-align: center;
	padding: 30px 0 !important;
}

div.left {
	display: inline;
	float: left;
	width: 48%;
	text-align: left;
	margin-left: 5px;
	height: 30px;
}

div.right {
	display: inline;
	float: left;
	width: 48%;
	text-align: right;
	margin-right: 5px;
}

.other-checkbox-off {
	background-position: -684px 50% !important;
	border-radius: 3px !important;
	-webkit-border-radius: 3px !important;
}

.other-checkbox-on {
	background-position: -648px 50% !important;
	background-color: #9DC149 !important;
	border-radius: 3px !important;
	-webkit-border-radius: 3px !important;
}

.other-radio-off {
	background-position: -756px 50% !important;
}

.other-radio-on {
	background-position: -720px 50% !important;
	background-color: #9DC149 !important;
}

h1.mobologo {
	padding: 0 0 0 10px;
	margin: 0 -10px -10px 0;
	min-height: 0;
	background-color: #DEDEDE;
	text-indent: -9000px;
	text-decoration: none;
}

h1.mobologo a {
	display: block;
	background: no-repeat left top;
	overflow: hidden;
}

a.ui-slider-handle {
	position: absolute;
	z-index: 10;
	top: 50%;
	width: 28px;
	height: 28px;
	margin-top: -15px;
	margin-left: -15px;
	border: 1px solid #8DB529;
	background: #9DC149;
	font-weight: bold;
	color: #444;
	text-shadow: 0 1px 1px #f6f6f6;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#B8E055),
		to(#9DC149));
	background-image: -webkit-linear-gradient(top, #B8E055, #9DC149);
	background-image: -moz-linear-gradient(top, #B8E055, #9DC149);
	background-image: -ms-linear-gradient(top, #B8E055, #9DC149);
	background-image: -o-linear-gradient(top, #B8E055, #9DC149);
	background-image: linear-gradient(top, #B8E055, #9DC149);
}

div.section {
	padding: 4px 10px !important;
	background: #fff !important;
}

div.qinsert {
	clear: both;
	color: #666666;
	line-height: 18px;
	padding-left: 10px;
	margin-top: 8px;
}

div.section .stitle {
	margin: 0;
}

.errorMessage {
	color: Red;
	min-height: 20px;
	line-height: 20px;
	font-size: 12px;
}

.award {
	border-spacing: 4px;
	border-collapse: separate;
}

.award td {
	height: 75px;
	text-align: center;
	width: 75px;
	font-size: 12px;
	box-shadow: 0 0 8px 2px rgba(255, 198, 102, 0.85);
	border-radius: 4px;
	padding: 0px 3px;
	background: #fff;
}

img {
	max-width: 100%;
	height: auto;
}

.sortnum {
	width: 22px;
	height: 22px;
	display: inline-block;
	vertical-align: middle;
	margin: 0 10px;
	background: #e2e2e2;
	border: 1px solid white;
}

.sortnum-sel {
	width: 22px;
	height: 22px;
	border-radius: 12px;
	border: 1px solid #ff6600;
	color: #ff6600;
	text-align: center;
	background: none;
}

.div_item_desc {
	color: #999999;
	font-weight: normal;
}

a.sumitbutton,a.sumitbutton:visited {
	background: #f17819;
	color: #ffffff !important;
	display: inline-block;
	height: 30px;
	line-height: 30px;
	padding-right: 5px;
	text-decoration: none;
	vertical-align: middle;
	padding: 0 10px 0 15px;
	text-align: center;
	font-weight: bold;
	font-size: 15px;
	cursor: pointer;
	border-radius: 5px;
}

a.sumitbutton:hover {
	background: #fda420;
	color: #fff !important;
}

a.cancle,a.cancle:visited {
	background: #fff;
	color: #333 !important;
	border: 1px solid #dbdbdb;
	font-weight: normal;
}

a.cancle:hover {
	background: #fff;
	color: #333 !important;
	border: 1px solid #2392f0;
}

#toplogo {
	background-color: #2695D0;
	min-height: 20px;
	padding: 0 10px;
	width: 100%;
}

#toplogo img {
	padding: 4px 0;
	height: 46px;
}

#toptitle {
	background-color: #1ea0fa;
	padding: 12px 10px;
	width: 100%;
	color: #ffffff;
}

#toptitle .htitle {
	font-size: 20px;
	font-weight: bold;
	margin: 0;
	padding: 0;
	text-align: center;
}

.ui-li-static {
	white-space: normal !important;
}

.qtypetip {
	color: #0066FF;
}

.trlabel {
	background: #e8f3ff;
	font-size: 20px;
	color: #444;
	height: 30px;
}

.shop-item {
	float: left;
	width: 100%;
	padding: 0 5px;
	background-color: #FFF;
	border: 1px solid #D5D5D5;
	position: relative;
	height: 92px;
	margin-bottom: 5px;
}

.haspic {
	height: 168px;
}

.shop-item .img_place {
	display: inline-block;
	width: 45%;
	height: 110px;
	margin: 2px 2px 6px 2px;
	overflow: hidden;
}

.shop-item .img_place img {
	max-height: 100px;
}

.shop-item .item_name {
	height: 2.4em;
	line-height: 1.2em;
	margin-top: 8px;
	margin-left: 8px;
	color: #333;
	overflow: hidden;
	/* white-space: nowrap; */
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	font-size: .8em;
}

.haspic .item_name {
	position: absolute;
	left: 46%;
	top: 0;
	width: 50%;
	height: 6em;
}

.shop-item .item_select {
	height: 25px;
	margin-left: 8px;
	margin-right: 8px;
	margin-bottom: 10px;
	z-index: 20;
}

.shop-item .item_price {
	display: inline-block;
	float: right;
	color: #0077B3;
}

.item_select .operation {
	cursor: pointer;
	float: left;
	display: inline-block;
	width: 34px;
	height: 34px;
	line-height: 33px;
	text-align: center;
	border: 1px solid #ABABAB;
	background: #eee;
}

.item_select .remove {
	border-radius: 2px 0 0 2px;
	border-right: 0;
}

.item_select .add {
	border-left: 0;
	border-radius: 0 2px 2px 0;
}

.item_select .itemnum {
	outline: 0 none;
	font-size: 12px;
	cursor: default;
	background: #fff;
	border-radius: 0;
}

.shopcart {
	padding: 15px;
	margin: 20px;
	border: 1px solid #ACACAC;
	background: #FFF;
}

.productslist {
	border-bottom: 1px dashed #CCC;
	padding-bottom: 8px;
	margin-bottom: 4px;
}

.productitem {
	padding: 4px;
	position: relative;
	font-size: 12px;
	height: 33px;
	border-bottom: 1px solid #EFEFEF;
}

.productitem:last-child {
	border-bottom: 0;
}

.productitem .fpname {
	position: absolute;
	left: 0;
	top: 8px;
	width: 60%;
	height: 32px;
	font-weight: bold;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.productitem .fpnum {
	position: absolute;
	left: 60%;
	top: 8px;
	width: 10%;
	height: 32px;
}

.productitem .fpprice {
	position: absolute;
	left: 70%;
	top: 8px;
	width: 30%;
	height: 32px;
	text-align: right;
	color: #0077B3;
}

.ftotalprice {
	text-align: right;
	font-size: 14px;
	color: #0077B3;
	font-weight: bold;
}

.scrolltop {
	display: block;
	position: fixed;
	right: 8px;
	height: 38px;
	width: 38px;
	z-index: 99;
	background: rgba(64, 64, 64, .9);
	border-radius: 1px;
	box-shadow: 0 0 2px rgba(0, 0, 0, .3);
	opacity: 0.618;
	bottom: 50%;
}

.scrolltop:before {
	content: "";
	position: absolute;
	left: 10px;
	display: inline-block;
	border-left: 9px solid transparent;
	border-right: 9px solid transparent;
	border-top: 9px solid white;
	border-bottom: 0;
	top: 12px;
	-webkit-transform: scale(1, 1.2222);
	-webkit-transform: translate(0, 1px);
	transform: scale(1, 1.2222);
	transform: translate(0, 1px);
}

.scrolltop:after {
	content: "";
	position: absolute;
	left: 10px;
	top: 23px;
	width: 18px;
	height: 3px;
	border-radius: 1px;
	background: white;
}

.header_left {
	position: absolute;
	left: 0px;
	top: 0px;
	width: 60px;
	height: 50px;
}

.header_left a {
	display: block;
	width: 100%;
	height: 100%;
	text-align: center;
}

.back {
	display: inline-block;
	width: 21px;
	height: 21px;
	margin: 15px auto 0 auto;
	background-position: 0 0;
	background-repeat: no-repeat;
	background-image: url('/images/weixin/left.png');
	background-size: contain;
}