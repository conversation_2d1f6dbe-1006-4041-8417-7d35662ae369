@CHARSET "UTF-8";
/**
* 用于两个页面跳转的样式
* 目前两个页面跳转采用1个页面，用tabview的方式
*/

.ui-tabs.ui-tabs-top>.ui-tabs-nav {
	padding: 0px;
}

.ui-tabs .ui-tabs-panel {
	border-width: 0;
	padding: 0;
	background: none;
}	
.ui-tabs-top.ui-tabs > .ui-tabs-nav {
	padding: 0px;
}
.ui-radiobutton{
	margin: 0px;
	width: 12px;
	line-height:10px;
}
.ui-radiobutton.ui-widget{
	height: 10px;
	width: 10px;
}
.ui-selectoneradio.ui-widget{
	height: 10px;
	width: 10px;
}
.ui-panelgrid td{
	border-width: 1px;
	border-style: solid;
	border-color: inherit;
	padding: 3px;
}


.ui-datatable-odd {
    background: #ECF3FE;
}
.ui-datatable-even {
    background: #FCFFFE;
}

 .fileUploadColor {
 	color:white !important;
 }   
 
 .ui-state-deault-defferent{
 	border: 1px solid #dddddd;
    background: #f6f6f6 url(/javax.faces.resource/images/ui-bg_highlight-soft_100_f6f6f6_1x100.png.faces?ln=primefaces-flick) 50% 50% repeat-x;
    font-weight: bold;
    color: #0073ea;
 }

.zwx_toobar_42 .icon-alert{
	background-image: url(/resources/images/alert-tip.png) !important;
	background-size: 12px 12px;
	margin-left: 3px;
	margin-top: -6px !important;
}