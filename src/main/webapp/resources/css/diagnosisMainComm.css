.diag-content *{
	line-height: 30px;
	font-size: 15px;
}
.underline{
	padding-bottom: 3px;
	border-bottom: 1px solid #000000;
}
.time-tip div{
	height:18px;background: #A53DFF;border-radius: 4px;padding-top: 10px;
}
.time-tip div span{
	padding-left:5px;color: #FFFFFF;padding-right:5px;
}
.check-box-bottom tr td:first-child div:last-child{
	margin-bottom: -6px;
}
.font-table{
	font-size: 14px;
}
.word-all *{
	font-size: 15px;
	line-height: 30px;
}
.word-all input{
	line-height: 20px;
}

.word-all button span{
	font-size: 14px;
}
.file-title{
	color: #505A97;
}
.fileUpload table{
	border: transparent;
}
.div-layout{
	background: #F7FAFD;border: 1px solid #D9DEE4;border-radius: 4px;
}
.matriFileUpload tbody{
	border: transparent;
}
.panelGrid-right-first tr td:first-child{
	 text-align: right;
	 width: 320px;
 }
.panelGrid-right-first tr:first-child td:first-child{
	text-align: left;
}
.panelGrid-right-first tr:first-child td:first-child span{
	color:#334B9A;padding-left: 15px;
}
.panelGrid-1 tr td:first-child{
	text-align: right;width: 110px;vertical-align: top;
}
.panelGrid-1 tr td:last-child textarea{
	width: 600px;
	height: 80px;
	resize: none;
}
.radio-text-top div{
vertical-align: text-top;
}
.radio-text-top{
	width: 120px !important;
}
.panelGrid-none{
	line-height: 30px;
	padding-top: 4px;
	padding-bottom: 4px;
	width: 100%;
}
.zyzdUploadDialog .ui-dialog-content{
	max-height: 500px;
	overflow-x: hidden;
}
.panelGrid-none tr{
	border: transparent !important;
}
.panelGrid-none tr td{
	background: #F7FAFD;
	border: transparent !important;
}
.panelGrid-bottom tr{
	border-bottom: 1px solid #D9DEE4 !important;
}
.panelGrid-bottom tr:last-child{
	border-bottom: 0px solid #D9DEE4 !important;
}
.div-sub{
	padding: 10px 50px 10px 50px;
	border-bottom: 1px solid #D9DEE4;
}
.panelGrid-sub{
	width: 100%;
}
.panelGrid-sub tr{
	border: transparent;
}
.panelGrid-sub tr td:last-child{
	text-align: right;
}




.diagnosis_left_toobar{
	width:179px;
	background: url(/javax.faces.resource/images/ui-bg_gloss-wave_55_5c9ccc_500x100.png.faces?ln=primefaces-redmond) 50% 50% repeat-x rgb(92, 156, 204);
	color: rgb(255, 255, 255);
	border-radius: 6px;
	text-align: center;
	line-height: 2.5;
}
.circle_light{
	height:28px;
	width:28px;
	background-color: #02B777;
	border-radius:50px;
	border: 1px solid #00BC79;
	text-align:center;
	margin-left:28px;
}
.circle_noLight{
	height:28px;
	width:28px;
	background-color: #E8E8E8;
	border-radius:50px;
	border: 1px solid #E8E8E8;
	text-align:center;
	margin-left:28px;
}
.processLeft{
	padding-left: 44px !important;
}
.mainProcess{
	position: relative;
	top: -4px;
	margin-bottom: -5px;
	min-height: 60px;
}
.process_light{
	padding-left: 18px !important;
	border-left-color: #00BC79 !important;
	border-left-width: 3px !important;
}
.process_noLight{
	padding-left: 18px !important;
	border-left-color: #E8E8E8 !important;
	border-left-width: 3px !important;
}

.node-title{
	font-size: 16px;
}
.number{
	font-weight: bold;
	font-size: 16px;
	color: #9BA6A2;
	line-height: 28px;
}
.noNotLeftBorder{
    border-right-color: transparent !important;
    border-top-color: transparent !important;
    border-bottom-color: transparent !important;
}
.noBorder{
    border-color: transparent !important;
}
.diagPanel{
    background: #f6f8f9;
    vertical-align: top;
}
.overdue{
	width: 75px;
	height:20px;
	background: #FF2929;
	border-radius: 4px;
	padding-top: 5px;
	padding-left: 3px;
}
.reachdue{
	width: 75px;
	height:20px;
	background: #fcbe04;
	border-radius: 4px;
	padding-top: 5px;
	padding-left: 3px;
}
.remaindue{
	width: 75px;
	height:20px;
	background: #A53DFF;
	border-radius: 4px;
	padding-top: 5px;
	padding-left: 3px;
}
.diagLeftPanel{
	width:179px;
	height: 1160px;
	margin: auto;
	background-color: #FFFFFF;
	border: 1px solid #CDCDCD;
	border-radius: 6px;
}
.diagRightPanel{
	padding:0px;
	background-color: #f6f8f9;
	border: 1px solid #CDCDCD;
	border-radius: 6px;
	background: #FFFFFF;
}
.businessInfo{
	width:950px;
	margin: auto;
	box-shadow: 0 0 8px 0;
	background: #FFFFFF;
	border: 1px solid #D9DEE4;
	border-radius: 4px;
	margin-top: 35px;
	margin-bottom: 45px;
}
.writeSortPanel{
	width:845px;
	height: 80px;
	margin: auto;
	background: #F7FAFD;
	border: 1px solid #D9DEE4;
	border-radius: 6px;
	border-collapse: separate;
	margin-top: 30px;
	margin-bottom: 5px;
}
.writeSortInfo{
	width: 845px;
	margin: auto;
}
.column_title {
	text-align: right;
	padding-right: 0px !important;
	font-size: 12px;
	line-height: 15px;
	color: black;
	border-right: 0px solid transparent !important;
	width: 135px;
	padding-left: 8px;
}
   
