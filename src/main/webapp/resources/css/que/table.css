.addrow {
	display: inline-block;
	outline: none;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	font: 14px/100%;
	padding: .4em 1.2em .4em 1.2em;
	text-shadow: 0 1px 1px rgba(0, 0, 0, .3);
	-webkit-border-radius: .5em;
	-moz-border-radius: .5em;
	border-radius: .5em;
	-webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
	-moz-box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
	box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
}

.addrow:hover {
	text-decoration: none;
}

.addrow:active {
	position: relative;
	top: 1px;
}

table input {
	height: 30px;
}

table.matrix-rating {
	width: 100%;
	background: #fff;
	padding: 10px;
}

table.scale-rating th,table.matrix-rating th {
	text-align: center;
	font-weight: normal;
	padding: 5px;
	word-break: break-all;
}

table.scale-rating {
	width: 100%;
}

.scale-div {
	width: 100%;
	background: #fff;
	padding: 0 15px;
}

table.scale-rating th {
	padding: 8px 0;
}

table.scale-rating td {
	text-align: center;
	padding: 10px 0px;
	border-top: 1px solid #A0A0A0;
	width: 32px;
}

table.matrix-rating td.title {
	text-align: left;
	padding: 3px 0;
}

table.matrix-rating td {
	text-align: center;
	padding: 3px 0 8px 0;
}

table.matrix-rating a.rate-off {
	padding: 13px;
	text-decoration: none;
	border-radius: 18px;
	-moz-box-shadow: 0 1px 2px #606060;
	-webkit-box-shadow: 0 1px 2px #606060;
	box-shadow: 0 1px 2px #606060;
	cursor: default;
	display: inline-block;
	background: #D6D6D6;
}

table.matrix-rating a.rate-on {
	background-color: #99e40f !important;
}

table.matrix-rating {
	border-width: 1px;
	border-color: #666666;
	border-collapse: collapse;
}

table.matrix-rating th {
	border-width: 1px;
	padding: 8px;
	border-style: solid;
	border-color: #666666;
	background-color: #dedede;
}

table.matrix-rating td {
	border-width: 1px;
	padding: 8px;
	border-style: solid;
	border-color: #666666;
	background-color: #ffffff;
}

table.matrix-rating input {
	width: 100%;
	height: 30px;
}
