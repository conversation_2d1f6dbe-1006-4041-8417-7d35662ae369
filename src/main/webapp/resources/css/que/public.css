body {
	font-family: "微软雅黑", "Lucida Grande", Helvetica, Arial, Verdana, sans-serif
}

.slider {
	width: 100%;
}

.imageSlider1 {
	margin: 0;
	padding: 0;
	height: 20px;
	width: 100%;
	background-image: url(/resources/images/que/scrollbg.gif);
}

.imageBar1 {
	margin: 0;
	padding: 0;
	height: 17px;
	width: 10px;
	background-image: url(/resources/images/que/scrfloat.gif);
}

.tableoption {
	background: #CDD7CC;
	padding: 3px; /*solid 1px*/
}

.tableoption td {
	border-bottom: 1px solid #eeeeee;
}

.div_table_radio_question ul {
	clear: both;
}

.div_table_radio_question li {
	margin-bottom: 2px;
}

.div_table_radio_question li label,.div_table_radio_question li input,.div_table_radio_question td input {
	cursor: pointer;
}

.div_table_radio_question li img {
	border: solid 2px #eeeeee;
	padding: 1px;
	display: block;
}

.div_item_desc {
	margin-left: 19px;
	font-weight: normal;
	color: #999999;
}

.likertImageTypeList {
	margin-left: 10px;
	margin-top: 5px;
}

.likertImageTypeList li {
	margin-right: 10px;
	list-style-type: none;
	float: left;
}

.likertImageTypeList li img {
	vertical-align: middle;
}

.on3 {
	background: url(/resources/images/que/hand_24.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.off3 {
	background: url(/resources/images/que/hand_24_off.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.on2 {
	background: url(/resources/images/que/star_24.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.off2 {
	background: url(/resources/images/que/star_24_off.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.onscore {
	background: url(/resources/images/que/piont_lv.png);
	background-color: #FFFFFF;
	overflow: hidden;
	border: solid 1px #dbdbdb;
	width: 355px;
	height: 20px;
}

.on6 {
	background: none !important;
	text-align: center;
	border: solid 1px white;
	cursor: pointer;
}

.off6 {
	background: #fff;
	border: solid 1px white;
	text-align: center;
	cursor: pointer;
}

.on4 {
	background: url(/resources/images/que/heart_24.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.off4 {
	background: url(/resources/images/que/heart_24_off.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.on5 {
	background: url(/resources/images/que/right_24.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.off5 {
	background: url(/resources/images/que/right_24_off.gif) no-repeat;
	width: 24px;
	height: 24px;
	text-decoration: none;
	cursor: pointer;
}

.goTop {
	BACKGROUND-IMAGE: url(/resources/images/que/GoTop1.gif)
}

.goBottom {
	BACKGROUND-IMAGE: url(/resources/images/que/GoBottom1.gif)
}

.upMove {
	BACKGROUND-IMAGE: url(/resources/images/que/UpMove.gif)
}

.downMove {
	BACKGROUND-IMAGE: url(/resources/images/que/DownMove.gif)
}

.qButton {
	font-size: 12px;
	width: 82px;
	height: 106px;
	font-weight: normal;
}

.qButton UL {
	margin: 0;
	width: 82px;
	height: 20px;
}

.qButton LI {
	background-color: #99ccff;
	PADDING: 0;
	width: 82px;
}

.qButton LI a {
	border: #fff 1px solid;
	padding-right: 6px;
	background-position: 5px 3px;
	width: 48px;
	height: 20px;
	display: inline-block;
	padding-left: 26px;
	padding-bottom: 0;
	margin: 0;
	cursor: pointer;
	color: #666;
	line-height: 20px;
	padding-top: 2px;
	background-repeat: no-repeat;
	text-decoration: none;
}

.qButton LI a:hover {
	border: #999 1px solid;
	color: #336699;
	width: 48px;
	background-color: #ccc
}

.matrixtitle {
	float: left;
}

.survey {
	width: 780px;
	line-height: 20px;
	word-wrap: break-word;
	padding: 0;
	margin: 0 auto;
}

.surveyhead {
	line-height: 20px;
	text-align: center;
	width: 96%;
	padding: 40px 2% 30px;
}

.surveyhead h1 {
	font-size: 24px !important;
	font-weight: bold;
	color: #f53d05;
	vertical-align: middle;
	margin: 0;
	padding: 15px 0;
	line-height: 24px;
}

.surveydescription {
	color: #555555;
	line-height: 24px;
	text-align: left;
	font-size: 16px;
	padding-bottom: 15px;
	border-bottom: 1px dashed #ccc;
	margin-left: 0;
	margin-top: 20px;
}

.surveycontent {
	margin: 0;
	width: 100%;
}

.errorMessage {
	color: Red;
	margin-left: 20px;
	height: 20px;
}

.div_question {
	border: 2px solid white;
	padding: 4px;
	clear: both;
	margin: 2px auto 10px;
	width: 96%;
	height: auto;
}

.surveydescription img,.div_title_page_question img,.div_question img {
	max-width: 760px;
	_width: expression(this.width > 760 ? "760px" : "auto");
	overflow: hidden;
}

.div_question_mouseover {
	border: 2px solid #fdb553;
	cursor: pointer;
	padding: 4px;
}

.div_question_mouseout {
	cursor: pointer;
}

.div_question_onclick {
	border: 2px solid #66CAFF;
	padding: 4px;
}

.div_question_error {
	border: 2px solid red;
	padding: 4px;
}

.div_topic_question {
	font-weight: bold;
	float: left;
	padding-right: 10px;
}

.div_topic_page_question {
	padding-top: 1px;
	font-weight: bold;
}

.div_title_question_all {
	padding-top: 2px;
	font-size: 15px;
	color: #444444;
	font-weight: bold;
	height: auto;
	line-height: 20px;
}

.div_title_question {
	overflow: hidden;
	zoom: 1;
}

.div_title_page_question {
	font-weight: bold;
	font-size: 14px;
}

.div_title_cut_question {
	font-size: 14px;
}

.div_table_question {
	float: right;
	display: block;
	margin-bottom: 4px;
	height: 25px;
	clear: both;
}

.div_table_question_hide {
	display: none;
}

.div_table_radio_question {
	clear: both;
	padding-top: 5px;
	padding-left: 24px;
	padding-bottom: 2px;
	border-bottom: 1px solid #EFEFEF;
	font-size: 14px;
	color: #333333;
	_padding-left: 27px;
}

.div_table_radio_question select {
	border: 1px solid #7F9DB9;
	padding: 3px;
	vertical-align: middle;
}

.div_table_radio_question .inputtext {
	line-height: 22px;
	padding: 2px;
}

.div_table_clear_top {
	clear: both;
	margin-top: 4px;
}

.div_table_clear_bottom {
	clear: both;
	margin-bottom: 10px;
}

.div_table_radio_question li {
	float: left;
	list-style-type: none;
}

.ulradiocheck li {
	line-height: 30px;
	position: relative;
}

.ulradiocheck li input {
	position: absolute;
	top: 6px;
}

.ulradiocheck li label {
	display: block;
	padding-left: 20px;
}

tr.labelname {
	background: #eff6fb;
}

tr.labelname b {
	color: #666;
	font-size: 16px;
}

.div_ins_question {
	padding-top: 8px;
	color: #666666;
	padding-left: 20px;
	line-height: 18px;
	clear: both;
}

.option {
	font-size: 12px;
	background-color: #cdd7cc;
}

.div_title_attr_question {
	border: 1px solid #cccccc;
	margin: 5px 10px 10px;
	padding: 5px;
	font-size: 12px;
	background: #cfe7ff;
}

.div_btn_confirm_attr_question {
	margin: 5px 10px 10px;
	padding: 5px;
	font-size: 12px;
	float: right;
}

.div_type_attr_question {
	border: 1px solid #6290d2;
	margin: 5px 10px 10px;
	padding: 5px;
	font-size: 12px;
}

.div_jump_attr_question {
	border: 1px solid #6290d2;
	margin: 5px 10px 10px;
	padding: 5px;
	font-size: 12px;
}

.div_default_attr_question {
	border: 1px solid #cccccc;
	margin: 5px 10px 10px;
	padding: 5px;
	background: #cfe7ff;
	font-size: 12px;
}

.qtypetip {
	color: #0066FF;
	font-weight: normal;
	font-size: 14px;
}

legend {
	display: none;
}

fieldset {
	border: 0;
}

.shop-item {
	position: relative;
	float: left;
	width: 200px;
	border: 1px solid #D5D5D5;
	box-shadow: 0 2px 2px #EBEBEB;
	margin-bottom: 8px !important;
	margin-right: 15px;
}

.shop-item:hover {
	border: 1px solid #95CDF3;
	box-shadow: 0 2px 3px #B9DFF0;
}

.shop-item .img_place {
	width: 190px;
	height: 126px;
	margin: 2px auto 4px;
	overflow: hidden;
}

.shop-item .img_place img {
	max-width: 160px;
	_width: expression(this.width > 160 ? "160px" : "auto");
	max-height: 100px;
	border: none;
}

.shop-item .text_place {
	height: 95px;
	position: relative;
	margin: 0 8px;
}

.haspic .item_name {
	position: absolute;
	left: 46%;
	top: 0;
	width: 50%;
	height: 6em;
}

.shop-item .item_name {
	width: 189px;
	display: block;
	margin-left: 8px;
	color: #333;
	font-size: 14px;
	position: absolute;
	top: 10px;
	left: 0;
	max-height: 32px;
	height: 32px;
	line-height: 16px;
	overflow: hidden;
	text-overflow: ellipsis;
}

.shop-item .item_select {
	margin-left: 8px;
	position: absolute;
	top: 48px;
	left: 0;
	z-index: 20;
}

.shop-item .item_price {
	font-size: 12px;
	font-weight: 600;
	color: #0077B3;
	margin-right: 5px;
	position: absolute;
	text-align: right;
	top: 49px;
	right: 0;
}

.item_select .operation {
	display: inline-block;
	height: 34px;
	border: 1px solid #ABABAB;
	width: 34px;
	line-height: 33px;
	text-align: center;
	background: #EEE;
	vertical-align: top;
	padding: 0;
	cursor: pointer;
	float: left;
}

.item_select .remove {
	border-right: 0;
	border-radius: 2px 0 0 2px;
}

.item_select .add {
	border-left: 0;
	border-radius: 0 2px 2px 0;
}

.item_select .remove:hover,.item_select .add:hover {
	color: #0077B3;
	background: #fff;
}

.item_select .itemnum {
	outline: 0 none;
	font-size: 12px;
	cursor: default;
	margin: 0;
	background: #fff;
}

.shopcart {
	padding: 15px;
	margin: 20px;
	border: 1px solid #ACACAC;
	background: #FFF;
}

.productslist {
	border-bottom: 1px dashed #CCC;
	padding-bottom: 8px;
	margin-bottom: 4px;
}

.productitem {
	padding: 4px;
	position: relative;
	font-size: 12px;
	height: 33px;
	border-bottom: 1px solid #EFEFEF;
}

.productitem:last-child {
	border-bottom: 0;
}

.productitem .fpname {
	position: absolute;
	left: 0;
	top: 8px;
	width: 60%;
	height: 32px;
	font-weight: bold;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.productitem .fpnum {
	position: absolute;
	left: 60%;
	top: 8px;
	width: 10%;
	height: 32px;
}

.productitem .fpprice {
	position: absolute;
	left: 70%;
	top: 8px;
	width: 30%;
	height: 32px;
	text-align: right;
	color: #0077B3;
}

.ftotalprice {
	text-align: right;
	font-size: 14px;
	color: #0077B3;
	font-weight: bold;
}

/**
 * 题目选中样式
 */
.questionDiv_select {
	background-color: rgb(237, 250, 254); 
	background-position: initial initial; 
	background-repeat: initial initial;
}

/**
 * 提交时验证失败样式
 */
.questionDiv_error {
	border: 2px solid rgb(255, 153, 0);
}

/**
 * ul li的mouserover样式
 */
.questionDiv_mouseover {
	background-color: rgb(239,239,239); 
	background-position: initial initial; 
	background-repeat: initial initial;
}