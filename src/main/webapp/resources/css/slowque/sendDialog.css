#main {
	height: 1800px;
	padding-top: 90px;
	text-align: center;
}

#fullbg {
	background-color: gray;
	left: 0;
	opacity: 0.5;
	position: absolute;
	top: 0;
	z-index: 3;
	filter: alpha(opacity = 50);
	-moz-opacity: 0.5;
	-khtml-opacity: 0.5;
}

#dialog {
	background-color: #fff;
	border: 3px solid rgba(0, 0, 0, 0.4);
	height: 400px;
	left: 50%;
	margin: -200px 0 0 -400px;
	padding: 1px;
	position: fixed !important; /* �����Ի��� */
	position: absolute;
	top: 50%;
	width: 800px;
	z-index: 5;
	border-radius: 5px;
	display: none;
}

#dialog p {
	margin: 0 0 3px;
	height: 24px;
	line-height: 24px;
	background: #6699CC;
}

#dialog p.close {
	text-align: left;
	padding-left: 10px;
	color: #fff;
}

#dialog p.close a {
	color: #fff;
	text-decoration: none;
}

table.gridtable {
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	color: #333333;
	border-width: 1px;
	border-color: #666666;
	border-collapse: collapse;
}

table.gridtable th {
	border-width: 1px;
	padding: 8px;
	border-style: solid;
	border-color: #666666;
	background-color: #dedede;
}

table.gridtable td {
	border-width: 1px;
	padding: 8px;
	border-style: solid;
	border-color: #666666;
	background-color: #ffffff;
}

.dialogbutton {
	display: inline-block;
	height: 25px;
	line-height: 25px;
	border: 0;
	cursor: pointer;
	background: #6699CC;
	font-size: 14px;
	color: #fff;
	border-radius: 5px;
	padding: 0 15px;
	overflow: visible;
}

.closeRight {
	float: right;
	padding-right: 10px;
}

.area {
	border: 1px solid;
	overflow: auto;
	background: #fff right bottom no-repeat;
	width: 99%;
	resize: none;
}


#msgDialog {
	background-color: #fff;
	border: 3px solid rgba(0, 0, 0, 0.4);
	height: 215px;
	left: 50%;
	margin: -150px 0 0 -175px;
	padding: 1px;
	position: fixed !important;  /* �����Ի��� */
	position: absolute;
	top: 50%;
	width: 350px;
	z-index: 5;
	border-radius: 5px;
	display: none;
}

#msgDialog p {
	margin: 0 0 3px;
	height: 24px;
	line-height: 24px;
	background: #6699CC;
}

#msgDialog p.close {
	text-align: left;
	padding-left: 10px;
	color: #fff;
}

#msgDialog p.close a {
	color: #fff;
	text-decoration: none;
}
#infoDialog {
	background-color: #fff;
	border: 3px solid rgba(0, 0, 0, 0.4);
	height:420px;
	left: 50%;
	margin: -210px 0 0 -250px;
	padding: 1px;
	position: fixed !important; /* �����Ի��� */
	position: absolute;
	top: 50%;
	width: 500px;
	z-index: 5;
	border-radius: 5px;
	display: none;
}

#infoDialog  p {
	margin: 0 0 3px;
	height: 24px;
	line-height: 24px;
	background: #6699CC;
}

#infoDialog p.close {
	text-align: left;
	padding-left: 10px;
	color: #fff;
}

#infoDialog p.close a {
	color: #fff;
	text-decoration: none;
}