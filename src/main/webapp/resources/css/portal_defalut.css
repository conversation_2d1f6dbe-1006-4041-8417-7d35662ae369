body {
    margin: 0px;
    padding: 0;
    color: #616161;
}

h1, h2, h3 {
    margin-top: 0;
}

h1 {
    font-size: 1.6em;
    font-weight: normal;
}

h2 {
    font-size: 1.6em;
}

h3 {
    font-size: 1em;
    margin-top:20px;
}

ul {
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

a img {
    border: none;
}

img.left {
    float: left;
    margin: 0 20px 0 0;
}

img.right {
    float: right;
    margin: 0 0 0 20px;
}

/* Header */
#header {
    padding: 4px 0px;
    height: 75px;
    position: relative;
}

#logo {
    text-align: left;
}

/* Menu */
#menu {
    position: absolute;
    top:25px;
    right:15px;
    padding: 0;
    height: 59px;
    overflow: hidden;
}

#menu ul {
    margin: 0;
    list-style: none;
}

#menu li {
    display: inline;
}

#menu li a {
    display: block;
    float: left;
    height: 42px;
    margin: 0;
    padding: 18px 40px 0 40px;
    text-decoration: none;
    font-size: 20px;
}

#menu li a:hover {
    text-decoration: underline;
}

#globalthemeswitcher {
    position:absolute;
    top:5px;
    right:50px;
}

/* Page */
#page {
    padding: 10px 0px;
    background: #FFFFFF;
}

/* Content */
#content {
    width: 70%;
    float:left;
}

.post {
    padding-bottom: 15px;
}

.post h1 {
    font-weight: normal;
}

.title {
    height: 30px;
    margin-left: 10px;
    padding: 8px 0 0 20px;
}

.title a {
    border-bottom: none;
    color: #FFFFFF;
}

.title a:hover {
    border-bottom: 1px dotted #000000;
}

.submenu-title {
    padding: 4px 10px;
}

.submenu-content {
    text-align:center;
}

.byline {
    margin: -60px 20px 20px 20px;
}

.byline a {
    color: #DC8700;
}

.tag {
    padding: 0 15px;
}

.entry {
    padding: 0 20px;
}

.entry p {
    line-height: 200%;
}

.links {
    padding: 4px 0px;
    text-align: right;
    font-weight: bold;
}

.links a {
    border: none;
}

.links a:hover {
}

#sidebar {
    float: left;
    width: 25%;
    padding: 0 10px;
    margin-left: -1px;
    overflow: hidden;
}

#sidebar a.ui-state-hover,
#sidebar a.ui-state-highlight {
    border:0 none;
    text-decoration: none;
    font-weight: normal;
}

#sidebar table {
    width: 100%;
}

#sidebar table td {
    vertical-align: top;
}

#sidebar ul {
    padding: 0;
    margin: 0;
}

#sidebar ul li {
    list-style-type: none;
    padding: 1px;
}

/* Footer */
#footer {
    height: 70px;
    margin: 0 auto;
    padding: 0 20px;
    font-size: 14px;
}

#footer p {
    margin: 0;
    padding: 25px 0 0 0;
    text-align: center;
}

#footer a {
}

#footer .link {
    float: right;
}

#footer .copyright {
    float: left;
}

.homeText {
    line-height: 175%;
}

.homeMenu {
    width:200px;
    float:left;
    margin-right:10px
}

.themeMenu {
    overflow: auto;
    height:300px;
    width:200px;
}

.dp-xml {
    width:1000px;
}

.ui-themeswitcher a.ui-selectonemenu-label-container {
    text-decoration: none !important;
}

.ui-widget {
    font-size: 13px !important;
}

.ui-growl {
    position: fixed;
    top:50px;
    right: 20px;
    width: 301px;
}

/**
* ����������ʽ
*/
.zwx_toobar_42 {
	background-color: #fafafa;
	background-image: -moz-linear-gradient(top, #ffffff, #f2f2f2);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f2f2f2));
	background-image: -webkit-linear-gradient(top, #ffffff, #f2f2f2);
	background-image: -o-linear-gradient(top, #ffffff, #f2f2f2);
	background-image: linear-gradient(to bottom, #ffffff, #f2f2f2);
	background-repeat: repeat-x;
	border: 1px solid #d4d4d4;
	border-radius: 6px;
	height: 38px;
	vertical-align: middle;
}

