.ui-dashboard .ui-dashboard-column {
    padding-bottom: 10px !important;
}

.clear {
    clear: both;
}

#mainForm\:contentLay .ui-corner-all {
    border-radius: 0 !important;
}

#mainForm\:leftLay, #mainForm\:centerLay, #mainForm\:rightLay .ui-widget-content {
    border: 1px solid #666666;
}

#mainForm\:leftLay {
    left: 20px !important;
    width: 410px !important;
}

#mainForm\:centerLay {
    left: 472px !important;
    width: 250px !important;
    text-align: center;
}

#mainForm\:centerLay .ui-helper-hidden {
    display: block;
}

#mainForm\:rightLay {
    border: 1px solid #666666 !important;
    width: 280px !important;
}

#mainForm\:rightLay .ui-layout-unit-content {
    border: none !important;
}

.vedio-photo-choosen {
    border: 3px solid red;
}

.line-cutoff {
    height: 20px;
    border-bottom: 1px solid #A6C9E2;
    clear: both;
    margin-bottom: 15px;
}

#mainForm\:leftLay .ui-layout-unit-content {
    overflow-y: hidden !important;
}

#mainForm\:centerLay .ui-layout-unit-content {
    overflow-x: hidden !important;
}

.center-container .img-info {
    margin-top: 3px;
    font-family: Microsoft YaHei;
    font-size: 11px;
    color: #333333;
}

.center-container ul {
    width: 205px;
    height: 330px;
    margin-top: 3px;
    margin-left: 20px;
    padding-left: 0px;
    padding-top: 10px;
    overflow-y: auto;
    border: 1px solid #666666;
    background: #F5F5F5;
}

.center-container ul li {
    list-style: none;
    height: 145px;
    margin-left: 0px;
    border: 1px solid red;
}

.center-container ul li div.vedio-photo {
    width: 90px;
    height: 120px;
    -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px rgba(0, 0, 0, .5);
    -moz-box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px rgba(0, 0, 0, .5);
    -o-box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px rgba(0, 0, 0, .5);
    box-shadow: 0 2px 10px rgba(0, 0, 0, .5), 0 2px 3px rgba(0, 0, 0, .5);
    margin-left: 50px;
    margin-bottom: 5px;
}

.ui-layout-unit .ui-layout-unit-content {
    padding: 0.2em 0em;
    border: 0px none;
    overflow: auto;
}

.center-container ul li div.photo:before {
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(rgba(255, 255, 255, .15)),
    to(rgba(0, 0, 0, .25))),
    -webkit-gradient(linear, left top, right bottom, color-stop(0, rgba(255,
            255, 255, 0)), color-stop(0.5, rgba(255, 255, 255, .1)),
            color-stop(0.501, rgba(255, 255, 255, 0)),
            color-stop(1, rgba(255, 255, 255, 0)));
    background: -moz-linear-gradient(top, rgba(255, 255, 255, .15),
    rgba(0, 0, 0, .25)),
    -moz-linear-gradient(left top, rgba(255, 255, 255, 0),
            rgba(255, 255, 255, .1) 50%, rgba(255, 255, 255, 0) 50%,
            rgba(255, 255, 255, 0));
    background: linear-gradient(top, rgba(255, 255, 255, .15),
    rgba(0, 0, 0, .25)), linear-gradient(left top, rgba(255, 255, 255, 0),
    rgba(255, 255, 255, .1) 50%, rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0));
}

.center-container ul li img.vedio-image {
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.center-container ul li img.img-close-icon {
    width: 20px;
    height: 20px;
    position: relative;
    top: -155px;
    left: 53px;
    cursor: pointer;
}

#mainForm\:dataList_content {
    border: none !important;
}

.ui-datalist-item {
    height: 30px;
    border-bottom: dotted 1px #b2b2b2;
    margin-bottom: 10px;
}

.frpt_diag .ui-dialog-content {
    background-color: #FFFFFF;
}

.frpt_diag .ui-dialog-content {
    padding: 0 0;
}

.frpt_panel {
    display: flex;
    width: 100%;
    height: 100%;
}

.frpt_img {
    width: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.frpt_tip {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.frpt2_img {
    width: 150px;
    text-align: center;
    vertical-align: top;
    padding-top: 27px;
}

.frpt_tip_head {
    font-size: 14px;
    padding-bottom: 20px;
}

.frpt_tip_body {
    font-size: 13px;
    padding-bottom: 10px;
}

.frpt_tip_body a {
    color: #25AAE1;
    cursor: pointer;
}

.frpt_bottom {
    text-align: right;
    padding-right: 24px;
    padding-bottom: 20px;
    vertical-align: bottom;
}

.video_panel {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 415px;
}