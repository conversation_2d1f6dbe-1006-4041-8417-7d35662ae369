<?xml version="1.0" encoding="UTF-8"?>
<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <rect id="path-1" x="0" y="0" width="12" height="12"></rect>
        <polygon id="path-3" points="0 12 12 12 12 0 0 0"></polygon>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页1" transform="translate(-340, -748)">
            <g id="编组-7" transform="translate(262, 740)">
                <g id="-mockplus-" transform="translate(68, 0)">
                    <g id="-mockplus-" transform="translate(10, 7)">
                        <g id="上传" transform="translate(6, 7) rotate(90) translate(-6, -7)translate(0, 1)">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#path-1"></use>
                            </mask>
                            <g id="路径"></g>
                            <path d="M0,12 L12,12 L12,0 L0,0 L0,12 Z" id="路径" fill-opacity="0.01" fill="#FFFFFF" mask="url(#mask-2)"></path>
                            <g id="分组-4" mask="url(#mask-2)">
                                <mask id="mask-4" fill="white">
                                    <use xlink:href="#path-3"></use>
                                </mask>
                                <g id="路径"></g>
                                <g id="分组-1" mask="url(#mask-4)" stroke="#0037C7" stroke-dasharray="0,0" stroke-linejoin="round">
                                    <g transform="translate(1.5, 1.5)" id="路径">
                                        <path d="M0,4.5 L0,9 L9,9 L9,4.5"></path>
                                        <path d="M6.75,2.25 L4.5,0 L2.25,2.25" stroke-linecap="round"></path>
                                    </g>
                                    <line x1="6" y1="2" x2="6" y2="8"/>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>