@import url(http://fonts.googleapis.com/css?family=Raleway:700);

/****************************
Author: Bora DAN
Version: 2.0.2
21 November 2014
http://dribbble.com/bqra
http://themeforest.com/bqra
http://twitter.com/bqra
http://pikselmatik.com
****************************/
.raxus-slider {
	position: relative;
	text-align: center;
	outline: none;
	width: 800px;
	height: 600px;
	overflow: hidden;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.raxus-slider .slider-area,
.raxus-slider .slider-area:active {
	cursor: col-resize;
}
.raxus-slider[data-direction=vertical] .slider-area,
.raxus-slider[data-direction=vertical] .slider-area:active {
	cursor: row-resize;
}

.raxus-slider[data-arrows=auto-hide] .arrow-l, 
.raxus-slider[data-arrows=auto-hide] .arrow-r {
	opacity: 0;
	visibility: hidden;
}
.raxus-slider[data-arrows=auto-hide]:hover .arrow-l, 
.raxus-slider[data-arrows=auto-hide]:hover .arrow-r {
	opacity: 1;
	visibility: visible;
}
.raxus-slider .arrow-l, 
.raxus-slider .arrow-r {
	display: inline-block;
	width: 30px;
	height: 30px;
	position: absolute;
	top: 50%;
	margin-top: -15px;
	cursor: pointer;
	z-index: 2;
	background: url(../img/sprite.png) rgba(0,0,0,0.8) no-repeat;
	transition: all .1s;
	-moz-transition: all .1s;
	-webkit-transition: all .1s;
}

.raxus-slider .arrow-r.outer {
	right: -30px;
	border-radius: 0 2px 2px 0;
	border-left: none;
	border-right: 1px solid #000;
}
.raxus-slider .arrow-r {
	right: 0;
	border-radius: 2px 0 0 2px;
	border-right: none;
	background-position: 10px -26px;
}
.raxus-slider[data-direction=vertical] .arrow-r {
	bottom: 0;
	top: auto;
	right: auto;
	left: 50%;
	margin-left: -15px;
	border-radius: 2px 2px 0 0;
	border-bottom: none;
	background-position: -71px -26px;
}
.raxus-slider .arrow-r:active {
	background-position: 13px -26px;
}
.raxus-slider[data-direction=vertical] .arrow-r:active {
	background-position: -71px -23px;
}
.raxus-slider .arrow-l.outer {
	left: -30px;
	border-radius: 3px 0 0 3px;
	border-right: none;
	border-left: 1px solid #000;
}
.raxus-slider .arrow-l {
	left: 0;
	border-radius: 0 3px 3px 0;
	border-left: none;
	background-position: -17px -26px;
}
.raxus-slider[data-direction=vertical] .arrow-l {
	top: 0;
	margin-top: auto;
	right: auto;
	left: 50%;
	margin-left: -15px;
	border-radius: 0 0 2px 2px;
	border-top: none;
	background-position: -42px -27px;
}
.raxus-slider .arrow-l:active {
	background-position: -20px -26px;
}
.raxus-slider[data-direction=vertical] .arrow-l:active {
	background-position: -42px -30px;
}
.raxus-slider .slider-area {
	overflow: hidden;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	border-radius: 4px 4px 0 0;
	background-color: #444;
	/*box-shadow: inset 0 0 20px 10px rgba(44,178,190,0.7);*/
}
.raxus-slider[data-thumbnail=bottom] .slider-area,
.raxus-slider[data-thumbnail=top] .slider-area {
	width: 100%;
}
.raxus-slider[data-thumbnail=bottom] .slider-area {
	border-radius: 4px 4px 0 0;
}
.raxus-slider[data-thumbnail=top] .slider-area {
	border-radius: 0 0 4px 4px;
}
.raxus-slider[data-thumbnail=left] .slider-area,
.raxus-slider[data-thumbnail=right] .slider-area {
	height: 100%;
}
.raxus-slider[data-thumbnail=right] .slider-area {
	border-radius: 4px 0 0 4px;
}
.raxus-slider[data-thumbnail=left] .slider-area {
	border-radius: 0 4px 4px 0;
}

.raxus-slider .slider-relative {
	width: 100%;
	position: absolute;
	font-size: 0;
	left: 0;
	margin: 0;
	padding: 0;
	height: 100%;
	text-align: left;
	white-space: nowrap;
	transition: all .5s;
	-webkit-transition: all .5s;
	-moz-transition: all .5s;
	-o-transition: all .5s;
}
.raxus-slider .slider-relative .slide {
	padding: 0;
	height: 100%;
	width: 100%;
	display: inline-block;
	font-size: 0;
	text-align: center;
	position: relative;
	overflow: hidden;
	vertical-align: middle;
	background-color: #111;
}
.raxus-slider .slider-relative .slide img {
	opacity: 0;
	transition: all .5s;
	-webkit-transition: all .5s;
	-moz-transition: all .5s;
	-o-transition: all .5s;
}
.raxus-slider .slider-relative .slide.selected img {
	opacity: 1;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
    -webkit-perspective: 1000;
}
.raxus-slider[data-direction=vertical] .slider-relative .slide {
	display: block !important;
}

.raxus-slider .slider-relative .slide .image {
	text-align: center;
	height: 100%;
	display: inline;
	box-sizing: initial;
	-webkit-box-sizing: initial;
	-moz-box-sizing: initial;
	width: 100%;
}
.raxus-slider .slider-relative .slide .image * {
	vertical-align: middle;
}
.raxus-slider.fix-width .slider-relative .slide .image {
	position: absolute;
	left: 0;
	top: 50%;
	margin: -2500px 0 0 0;
	height: 5000px;
	width: 100%;
}
.raxus-slider .slider-relative .slide.fix-width .image {
	position: absolute;
	left: 0 !important;
	top: 50% !important;
	margin: -2500px 0 0 0 !important;
	height: 5000px !important;
	width: 100% !important;
}
.raxus-slider.fix-height .slider-relative .slide .image {
	position: absolute;
	left: 50%;
	top: 0;
	margin: 0 0 0 -2500px;
	width: 5000px;
	height: 100%;
}
.raxus-slider .slider-relative .slide.fix-height .image {
	position: absolute;
	left: 50% !important;
	top: 0 !important;
	margin: 0 0 0 -2500px !important;
	width: 5000px !important;
	height: 100% !important;
}

.raxus-slider.fix-height .slider-relative .slide .image img {
	max-width: auto;
	height: 100%;
	width: auto;
}
.raxus-slider .slider-relative .slide.fix-height .image img {
	max-width: auto;
	height: 100% !important;
	width: auto !important;
}
.raxus-slider.fix-width .slider-relative .slide .image img {
	max-height: none !important;
	width: 100%;
	height: auto;
}
.raxus-slider .slider-relative .slide.fix-width .image img {
	max-height: none !important;
	width: 100% !important;
	height: auto !important;
}
.raxus-slider.fit .slider-relative .slide .image img,
.raxus-slider .slider-relative .slide.fit .image img {
	width: 100% !important;
	height: 100% !important;
}
.raxus-slider .slider-relative .slide .image img {
	max-width: 100%;
	max-height: 100%;
}
.raxus-slider .slider-relative .slide .image .play-button {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 60px;
	height: 50px;
	border-radius: 5px;
	background: url(../img/sprite.png) -58px 14px rgba(0,0,0,0.6) no-repeat;
	margin: -25px 0 0 -30px;
	cursor: pointer;
}
.raxus-slider .slider-relative .slide .image .play-button:hover {
	background-color: #000;
}
.raxus-slider .slider-relative .slide .text {
	position: absolute;
	text-align: left !important;
}
.raxus-slider .slider-relative .slide a {
	cursor: pointer;
}

/* text animation */
.raxus-slider .slider-relative .slide .text {
	bottom: 20px;
	left: 45px;
	right: 45px;
}

/* text left animation */
.raxus-slider .slider-relative .slide .text strong,
.raxus-slider .slider-relative .slide .text small,
.raxus-slider .slider-relative .slide .text.ani-left strong,
.raxus-slider .slider-relative .slide .text.ani-left small {
	-webkit-transform: translate3d(-2000px, 0, 0);
    -moz-transform: translate3d(-2000px, 0, 0);
    -o-transform: translate3d(-2000px, 0, 0);
    -ms-transform: translate3d(-2000px, 0, 0);
    transform: translate(-2000px, 0);
}

/* text top animation */
.raxus-slider .slider-relative .slide .text.ani-top strong,
.raxus-slider .slider-relative .slide .text.ani-top small {
	-webkit-transform: translate3d(0, -2000px, 0);
    -moz-transform: translate3d(0, -2000px, 0);
    -o-transform: translate3d(0, -2000px, 0);
    -ms-transform: translate3d(0, -2000px, 0);
    transform: translate(0, -2000px);
}
/* text right animation */
.raxus-slider .slider-relative .slide .text.ani-right strong,
.raxus-slider .slider-relative .slide .text.ani-right small {
	-webkit-transform: translate3d(3000px, 0, 0);
    -moz-transform: translate3d(3000px, 0, 0);
    -o-transform: translate3d(3000px, 0, 0);
    -ms-transform: translate3d(3000px, 0, 0);
    transform: translate(3000px, 0);
}
/* text bottom animation */
.raxus-slider .slider-relative .slide .text.ani-bottom strong,
.raxus-slider .slider-relative .slide .text.ani-bottom small {
	-webkit-transform: translate3d(0, 3000px, 0);
    -moz-transform: translate3d(0, 3000px, 0);
    -o-transform: translate3d(0, 3000px, 0);
    -ms-transform: translate3d(0, 3000px, 0);
    transform: translate(0, 3000px);
}

.raxus-slider .slider-relative .slide .text strong,
.raxus-slider .slider-relative .slide .text small { 
	transition: all .5s; -moz-transition: all .5s; -webkit-transition: all .5s; 
}

.raxus-slider .slider-relative .slide .text.left-animated strong,
.raxus-slider .slider-relative .slide .text.left-animated small {
	-webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate(0, 0);
}
.raxus-slider .slider-relative .slide .text.left-animated small {
	transition-delay: .5s; -moz-transition-delay: .5s; -webkit-transition-delay: .5s; 
}
/* text animation #end */

.raxus-slider[data-dots=show] .slider-relative .slide .text {
	bottom: 40px !important;
}
.raxus-slider .slider-relative .slide .text strong, 
.raxus-slider .slider-relative .slide .text small {
	margin: 0 !important;
	padding: 5px 10px !important;
	text-align: left !important;
	color: #fff !important;
	font: 26px 'Raleway', 'Helvetica Neue', Arial !important;
	text-shadow: 0 1px 10px rgba(0,0,0,0.8) !important;
	background: rgba(0,0,0,0.6);
}
.raxus-slider .slider-relative .slide .text strong {
	display: inline-block;
}
.raxus-slider .slider-relative .slide .text small {
	font: 14px 'Helvetica Neue', Arial !important;
	white-space: normal;
	display: inline-block;
	line-height: 19px !important;
}

/* raxus slider dots */ 
.raxus-slider[data-dots=show] ul.dots {
	display: inline-block;
}
.raxus-slider ul.dots {
	position: absolute;
	width: 100%;
	text-align: center;
	bottom: 15px;
	left: 0;
	display: none;
	margin: 0;
	padding: 0;
	list-style: none;
	z-index: 999;
	font-size: 0;
}
.raxus-slider[data-direction=vertical] ul.dots {
	width: 10px !important;
	vertical-align: middle;
	right: 10px;
	top: 50%;
	left: auto;
	bottom: auto;
}
.raxus-slider ul.dots li {
	display: inline-block;
	text-align: center;
	margin: 0 2px;
	width: 10px;
	height: 10px;
	border-radius: 20px;
	background: rgba(0,0,0,0.4);
	box-shadow: inset 0 1px 2px rgba(0,0,0,0.7), inset 0 1px 1px rgba(255,255,255,0.3), 0 1px 1px rgba(255,255,255,0.3);
	cursor: pointer;
	position: relative;
}
.raxus-slider[data-direction=vertical] ul.dots li {
	margin: 6px 0;
	display: block;
}

.raxus-slider ul.dots li span {
	display: inline-block;
	width: 4px;
	height: 4px;
	border-radius: 10px;
	position: absolute;
	top: 3px;
	left: 3px;
}
.raxus-slider ul.dots li.selected span, .raxus-slider ul.dots li:hover span {
	background: rgba(255,255,255,1);
}

/* mini images */
.raxus-slider .mini-images-relative {
	position: absolute;
	overflow: hidden;
	background-color: #222;
	z-index: 99;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
}
.raxus-slider .mini-images {
	margin: 0;
	padding: 0;
	text-align: left;
	position: relative;
	display: inline-block;
	font-size: 0;
	transition: all .2s; -moz-transition: all .2s; -webkit-transition: all .2s; 
}
.raxus-slider[data-thumbnail=bottom] .mini-images,
.raxus-slider[data-thumbnail=top] .mini-images {
	/*width: 10000px;*/
}
.raxus-slider[data-thumbnail=bottom] .mini-images-relative,
.raxus-slider[data-thumbnail=top] .mini-images-relative {
	padding: 10px 0 10px 10px;
	left: 0;
	width: 100%;
	white-space: nowrap;
}
.raxus-slider[data-thumbnail=bottom] .mini-images-relative {
	box-shadow: inset 0 3px 5px rgba(0,0,0,0.2);
	border-radius: 0 0 4px 4px;
	bottom: 0;
}
.raxus-slider[data-thumbnail=top] .mini-images-relative {
	box-shadow: inset 0 -3px 5px rgba(0,0,0,0.2);
	border-radius: 4px 4px 0 0;
	top: 0;
}
.raxus-slider[data-thumbnail=right] .mini-images-relative,
.raxus-slider[data-thumbnail=left] .mini-images-relative {
	top: 0;
	height: 100%;
	bottom: 0;
	padding: 0 10px;
}
.raxus-slider[data-thumbnail=right] .mini-images-relative {
	box-shadow: inset 3px 0 5px rgba(0,0,0,0.2);
	border-radius: 0 4px 4px 0;
	right: 0;
}
.raxus-slider[data-thumbnail=left] .mini-images-relative {
	box-shadow: inset -3px 0 5px rgba(0,0,0,0.2);
	border-radius: 4px 0 0 4px;
	left: 0;
}
.raxus-slider .mini-images.no-slide {
	text-align: center;
}
.raxus-slider .mini-images li {
	display: inline-block;
	position: relative;
	margin: 0 10px 0 0;
	width: 50px;
	height: 50px;
	overflow: hidden;
	text-align: center;
	border-radius: 2px;
	cursor: pointer;
}
.raxus-slider[data-thumbnail=bottom] .mini-images li:active,
.raxus-slider[data-thumbnail=top] .mini-images li:active {
	cursor: col-resize;
}
.raxus-slider[data-thumbnail=right] .mini-images li,
.raxus-slider[data-thumbnail=left] .mini-images li {
	display: block;
	margin: 10px 0 0 0;
}
.raxus-slider[data-thumbnail=right] .mini-images li:active,
.raxus-slider[data-thumbnail=left] .mini-images li:active {
	cursor: row-resize;
}
.raxus-slider .mini-images li.selected .img-selected, 
.raxus-slider .mini-images li:hover .img-selected {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: inline-block;
	box-shadow: inset 0 0 0 1px #66d6a0, inset 0 0 2px 2px rgba(0,0,0,0.6);
	border-radius: 3px;
	z-index: 2;
}

.raxus-slider .mini-images li .image {
	width: 1000px;
	height: 100%;
	position: relative;
	left: 50%;
	margin-left: -500px;
	text-align: center;
	z-index: 1;
}
.raxus-slider .mini-images li img {
	height: 100%;
}
/* mini images #end */

.vertical-helper {
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}
.raxus-slider .fullscreen, 
.raxus-slider .close-video {
	width: 30px;
	height: 30px;
	background: url(../img/sprite.png) 8px -51px rgba(0,0,0,0.8);
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 0 0 2px 0;
	z-index: 9999;
	cursor: pointer;
	display: inline-block;
	
	transition: all .3s;
	-moz-transition: all .3s;
	-webkit-transition: all .3s;
}
.raxus-slider[data-fullscreen=show]:hover .fullscreen,
.raxus-slider:hover .close-video {
	opacity: 1;
}
.raxus-slider .close-video {
	background: url(../img/sprite.png) -16px -51px rgba(0,0,0,0.8);
}

.raxus-slider[data-loop=false] .arrow-l {
	opacity: 0.5;
}

.no-transition {
	transition: none !important;
	-webkit-transition: none !important;
	-moz-transition: none !important;
	-o-transition: none !important;
}

/* when fullscreen active */
:-webkit-full-screen {
	position: absolute !important;
	top: 0 !important;
	width: 100% !important;
	height: 100% !important;
}


/* for responsive */
@media screen and (max-width: 640px) {
	
	/* small captions */
	
	.raxus-slider .slider-relative .slide .text strong {
		font-size: 13px !important;
		line-height: 14px !important;
	}
	.raxus-slider .slider-relative .slide .text small {
		font-size: 10px !important;
		line-height: 13px !important;
	}
	
	/* small dots */
	.raxus-slider ul.dots li {
		width: 5px;
		height: 5px;
	}
	.raxus-slider ul.dots li span {
		width: 3px;
		height: 3px;
		top: 1px;
		left: 1px;
	}
}





