body {
	margin: 0;
	padding: 0;
	background: url(../img/body-bg.png);
	font-family: 'Helvetica Neue', Arial;
	-moz-osx-font-smoothing: grayscale; 
	-webkit-font-smoothing: antialiased;
}
.bg {
	background: url(../img/body-top-bg.png) center -150px no-repeat;
	position: relative;
	z-index: 0;
	height: 500px;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
}
.header {
	z-index: 9;
	position: fixed;
	top: 0;
	width: 100%;
}
.header.small {
	background-color: rgba(255,255,255,0.6);
}
.header .container {
	margin-top: 0;
	margin-bottom: 0;
}
.container {
	z-index: 2;
	position: relative;
}
a {
	text-decoration: none !important;
}
h1 {
	text-transform: uppercase;
	font: 700 30px 'Raleway', 'Helvetica Neue', Arial !important;
	color: #fff !important;
	text-shadow: 0 1px 2px rgba(0,0,0,0.2);
	letter-spacing: -1px;
	margin: 25px 0 !important;
	padding: 0 !important;
	position: relative;
	display: inline-block;
}
h1 a,
h1 a:hover {
	color: #fff;
}
.small h1 {
	font: 700 16px 'Raleway', 'Helvetica Neue', Arial !important;
	margin: 20px 0 !important;
	text-shadow: 0 0 10px #fff;
	color: #333 !important;
}
.small h1 a,
.small h1 a:hover {
	color: #333;
}
.small h1 small  {
	font-size: 10px;
}
h1 small {
	position: absolute;
	right: -28px;
	top: -8px;
	margin: 0 !important;
	border-radius: 50px;
	font: bold 12px 'Helvetica Neue', Arial;
	color: #fff !important;
	background-color: #f89883;
	padding: 2px 7px;
	letter-spacing: 1px;
}
h2 {
	font: 700 20px 'Raleway', 'Helvetica Neue', Arial !important;
	color: #000 !important;
	text-align: center;
	text-transform: uppercase;
	opacity: 0.3;
	margin: 20px 0 40px 0 !important;
	padding: 0 !important;
}
h3 {
	text-align: center;
	font: 700 15px 'Raleway', 'Helvetica Neue', Arial !important;
	color: #425957 !important;
	text-transform: uppercase;
	margin: 20px 0 !important;
	padding: 0 !important;
}
.mt20 {
	margin-top: 20px;
}
p {
	font: 15px 'Helvetica Neue', Arial;
	padding: 0;
	line-height: 24px;
	margin: 0 0 60px 0 !important;
}

strong {
	display: block;
	color: #425957 !important;
	font: bold 16px 'Helvetica Neue', Arial;
	margin: 20px 0 10px 0;
}
small {
	color: #000 !important;
	font: bold 13px 'Helvetica Neue', Arial;
	margin: 20px 0 10px 0;
	padding: 5px 10px;
	display: inline-block;
	border-radius: 2px;
	background-color: rgba(0,0,0,0.1);
}

section {
	padding: 50px 0;
	margin: 0;
	color: #fff;
	font: 14px 'Helvetica Neue', Arial !important;
	color: #333;
}
section.welcome {
	margin-top: 60px;
}
section.whatis {
	background-color: #f89883;
}
section.usage {
	background-color: #f2e5a2;
}
section.features {
	background-color: #5EB898;
}
section.changelog {
	background-color: #A8BEC3;
}
.opacity {
	opacity: 0.4;
}

.ratio-sample {
	padding: 20px;
	border-radius: 4px;
	display: inline-block;
	width: 100%;
	margin: 20px 0 0 0;
	background-color: rgba(0,0,0,0.1);
}

.title {
	font: 13px 'Helvetica Neue', Arial !important;
	color: #000 !important;
	margin: 0 0 30px 0 !important;
	line-height: 25px !important;
	padding: 0 !important;
}
.row p {
	text-align: center;
}
.row .text-left {
	text-align: left;
}
.row p b {
	text-decoration: underline;
}

.left {
	float: left;
}
.right {
	float: right;
}
.other-links-bar {
	display: inline-block;
	width: 100%;
	margin: 0 0 30px 0;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	padding: 20px 0;
	text-align: center;
}
.other-links-bar .clearfix {
	margin: 20px 0;
}
a.button {
	border-radius: 3px;
	padding: 10px 20px;
	border: 1px solid #2DB870;
	font: 12px 'Raleway', 'Helvetica Neue', Arial;
	text-decoration: none;
	color: #2DB870;
	display: inline-block;
	transition: all .2s;
	-webkit-transition: all .2s;
	-moz-transition: all .2s;
}
a:hover.button {
	color: #fff;
	background-color: #2DB870;
}

pre {
	background: url('../img/code-bg.png') #fff !important;
	padding: 10px 30px 0 30px !important;
	box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 1px 4px rgba(0,0,0,0.2) !important;
	border-radius: 2px !important;
	display: block;
	color: #555 !important;
	margin: 20px 0 !important;
	border: none !important;
}
pre code {
	background: transparent !important;
	padding: 0 !important;
	margin: 0 !important;
	overflow-y: scroll;
	overflow-x: hidden;
	max-height: 700px;
}

.try-title {
	font: bold 16px 'Raleway', 'Helvetica Neue', Arial;
	color: #999;
	padding-bottom: 15px;
	border-bottom: 1px solid #ddd;
	text-transform: uppercase;
}
.tl {
	font: bold 12px 'Helvetica Neue', Arial;
	padding: 20px 0 10px 0;
}
ul.templates {
	font-size: 0;
	margin: 0 !important;
	padding: 0 !important;
}
ul.templates li, 
.dimension input {
	padding: 10px 20px;
	border: 1px solid #2DB870;
	font: 12px 'Helvetica Neue', Arial;
	text-decoration: none;
	color: #81ac8b;
	margin: 0 !important;
	display: inline-block;
	transition: all .2s;
	-webkit-transition: all .2s;
	-moz-transition: all .2s;	
	border-left: none;
	cursor: pointer;
}
ul.templates li:hover, 
ul.templates li.selected {
	color: #fff;
	background-color: #2DB870;
}
ul.templates li:first-child,
.dimension input:first-child {
	border-radius: 3px 0 0 3px;
	border-left: 1px solid #2DB870;
}
ul.templates li:last-child {
	border-radius: 0 3px 3px 0;
}

.dimension {
	font-size: 0;
}
.dimension input {
	width: 80px;
	outline: none;
	box-shadow: inset 0 0 2px rgba(0,0,0,0.2);
	cursor: text;
}
.dimension a.button {
	border-radius: 0 3px 3px 0;
	border-left: none;
}

.previews .view {
	display: none;
}
.previews .view:first-child,
.previews .view.selected {
	display: block;
}

.changelog ul {
	padding: 0 0 20px 20px;
	margin: 10px 0 0 0;
	border-bottom: 1px solid rgba(0,0,0,0.1);
	font: 14px 'Helvetica Neue', Arial;
	color: #333; 
}
.changelog ul li {
	margin: 5px 0;
}

.menu {
	position: absolute;
	top: 50%;
	right: 15px;
	margin-top: -10px;
}
.menu a {
	color: #000;
	height: 20px;
	margin: 0 0 0 20px;
	display: inline-block;
	font: bold 13px 'Helvetica Neue', Arial;
	transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.menu a:hover {
	color: #F2594B;
}

/* for mobile */
@media screen and (max-width: 640px) {
	.header {
		position: absolute !important;
	}
	.header .menu {
		top: 95%;
		right: inherit;
		left: 0;
	}
	.header .menu a {
		font-size: 12px;
		margin: 0 -5px 0 15px;
	}
	.b {
		margin: 20px 0 0 0;
	}
}
































