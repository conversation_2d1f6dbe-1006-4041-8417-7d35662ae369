<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">
<!--日期动态限制-->
<composite:interface>
    <composite:attribute name="pattern"/>
    <composite:attribute name="maxlength"/>
    <composite:attribute name="readonlyInput"/>
    <composite:attribute name="showOtherMonths"/>
    <composite:attribute name="size"/>
    <composite:attribute name="navigator"/>
    <composite:attribute name="yearRange"/>
    <composite:attribute name="converterMessageB"/>
    <composite:attribute name="converterMessageE"/>
    <composite:attribute name="showButtonPanel"/>
    <composite:attribute name="startDate" required="true"/>
    <composite:attribute name="endDate" required="true"/>
    <composite:attribute name="defaultMaxDate" />
    <composite:attribute name="defaultMinDate" />
    <composite:attribute name="styleClass"/>
    <composite:attribute name="ifNotMaxDate" />
    <composite:attribute name="disabled" />
</composite:interface>

<composite:implementation>
    <p:calendar pattern="#{cc.attrs.pattern==null?'yyyy-MM-dd':cc.attrs.pattern}" maxlength="#{cc.attrs.maxlength==null?10:cc.attrs.maxlength}" readonlyInput="#{cc.attrs.readonlyInput==null?true:cc.attrs.readonlyInput}"
                showOtherMonths="#{cc.attrs.showOtherMonths==null?true:cc.attrs.showOtherMonths}" id="bDate" size="#{cc.attrs.size==null?11:cc.attrs.size}" navigator="#{cc.attrs.navigator==null?true:cc.attrs.navigator}"
                yearRange="c-#{cc.attrs.yearRange==null?10:cc.attrs.yearRange}:c+#{cc.attrs.yearRange==null?10:cc.attrs.yearRange}" converterMessage="#{cc.attrs.converterMessageB==null?'开始日期，格式输入不正确！':cc.attrs.converterMessageB}"
                showButtonPanel="#{cc.attrs.showButtonPanel==null?true:cc.attrs.showButtonPanel}" styleClass="#{cc.attrs.styleClass==null?'myCalendar1':cc.attrs.styleClass}"
                mindate="#{cc.attrs.defaultMinDate}" maxdate="#{cc.attrs.endDate==null?(cc.attrs.ifNotMaxDate?null:(cc.attrs.defaultMaxDate==null?cc.defaultMaxDate:cc.attrs.defaultMaxDate)):cc.attrs.endDate}"
                value="#{cc.attrs.startDate}" binding="#{cc.beginCalendar}" disabled="#{cc.attrs.disabled==null?false:cc.attrs.disabled}">
        <p:ajax event="dateSelect"  process="@this,bDate,eDate"  update="eDate" />
    </p:calendar>
    ~
    <p:calendar pattern="#{cc.attrs.pattern==null?'yyyy-MM-dd':cc.attrs.pattern}" maxlength="#{cc.attrs.maxlength==null?10:cc.attrs.maxlength}" readonlyInput="#{cc.attrs.readonlyInput==null?true:cc.attrs.readonlyInput}"
                showOtherMonths="#{cc.attrs.showOtherMonths==null?true:cc.attrs.showOtherMonths}" id="eDate" size="#{cc.attrs.size==null?11:cc.attrs.size}" navigator="#{cc.attrs.navigator==null?true:cc.attrs.navigator}"
                yearRange="c-#{cc.attrs.yearRange==null?10:cc.attrs.yearRange}:c+#{cc.attrs.yearRange==null?10:cc.attrs.yearRange}" converterMessage="#{cc.attrs.converterMessageE==null?'结束日期，格式输入不正确！':cc.attrs.converterMessageE}"
                showButtonPanel="#{cc.attrs.showButtonPanel==null?true:cc.attrs.showButtonPanel}" styleClass="#{cc.attrs.styleClass==null?'myCalendar1':cc.attrs.styleClass}"
                mindate="#{cc.attrs.startDate==null?cc.attrs.defaultMinDate:cc.attrs.startDate}" maxdate="#{cc.attrs.ifNotMaxDate?null:(cc.attrs.defaultMaxDate==null?cc.defaultMaxDate:cc.attrs.defaultMaxDate)}"
                value="#{cc.attrs.endDate}" binding="#{cc.endCalendar}" disabled="#{cc.attrs.disabled==null?false:cc.attrs.disabled}">
        <p:ajax event="dateSelect"  process="@this,bDate,eDate"  update="bDate" />
    </p:calendar>
</composite:implementation>
</html>