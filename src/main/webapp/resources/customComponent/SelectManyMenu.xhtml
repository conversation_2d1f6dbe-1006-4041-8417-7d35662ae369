<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
	xmlns:composite="http://java.sun.com/jsf/composite">

<composite:interface>
	<composite:attribute name="dataLabel" />
	<composite:attribute name="dataValue" required="true"/>
	<composite:attribute name="onchange" />
	<composite:attribute name="dataMap" required="true" />
</composite:interface>

<composite:implementation>
	<p:outputPanel>
		<h:panelGrid columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
			<p:inputText id="dataLabel" value="#{cc.attrs.dataLabel}" binding="#{cc.labelInput}" style="width: 180px;" onclick="PF('#{cc.clientId}:initBtn').getJQ().click();" readonly="true" />
			<p:commandLink styleClass="ui-icon ui-icon-search" style="position: relative;left: -30px;" type="button" onclick="PF('#{cc.clientId}:initBtn').getJQ().click();"/> 
		</h:panelGrid>
		<h:inputHidden id="dataValue" value="#{cc.attrs.dataValue}" binding="#{cc.valueInput}" />
		
		<p:commandButton style="display:none" action="#{cc.init}" process="@this,@parent" oncomplete="PF('#{cc.clientId}:DataOverPanel').show();" widgetVar="#{cc.clientId}:initBtn"/>
		<p:commandButton style="display:none" action="#{cc.onSelect}" process="@this,@parent" oncomplete="#{cc.attrs.onchange}" widgetVar="#{cc.clientId}:closeBtn"/>
		
		<p:overlayPanel id="dataOverPanel" for="dataLabel" style="width:280px;" widgetVar="#{cc.clientId}:DataOverPanel" 
			showEvent="dblclick" showCloseIcon="true" onHide="PF('#{cc.clientId}:closeBtn').getJQ().click();">
			<p:selectManyCheckbox binding="#{cc.selectManyCheckBox}" layout="grid" columns="1" value="#{cc.selectedList}" id="manyCheckbox">
				<f:selectItems value="#{cc.attrs.dataMap}" binding="#{cc.selectItems}"/>
			</p:selectManyCheckbox>
		</p:overlayPanel>
	</p:outputPanel>
</composite:implementation>
</html>