<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
	xmlns:composite="http://java.sun.com/jsf/composite">

<head></head>

<body>
<composite:interface>
	<composite:attribute name="eventId" required="true"/>
	<composite:attribute name="eventLevel" required="true"/>
	<composite:attribute name="eventTime" required="true"/>
	<composite:attribute name="eventTitle" required="true"/>
	<composite:attribute name="eventState" required="true"/>
	<composite:attribute name="eventFbrs" required="true"/>
	<composite:attribute name="eventSjrs" required="true"/>
	<composite:attribute name="eventSwrs" required="true"/>
	<composite:attribute name="zoneName" required="true"/>
</composite:interface>

<composite:implementation>
	<!-- 红#FFAEC9橙黄蓝白 -->
	<div class="zwx_comp_blockmenu_block" id="#{cc.clientId}_blockdiv" onclick="zwx_comp_block_click(this.id)"
		style="background:#{cc.attrs.eventLevel=='1001'?'#FFAEC9':(cc.attrs.eventLevel=='1002'?'#f59454':(cc.attrs.eventLevel=='1003'?'#EFE4B0':(cc.attrs.eventLevel=='1004'?'#99D9EA':'#FFFFFF')))}">
		<table style="width:100%;">
			<tr style="vertical-align: top;">
				<td style="padding-top: 10px;padding-left:5px;text-align: left;" class="zwx_dialog_font">事件时间：#{cc.attrs.eventTime}</td>
			</tr>			
			<tr style="vertical-align: top;text-align: center;">
				<td style="padding-top: 10px;font-size: 22px;font-weight: bold;" class="zwx_dialog_font_22">#{cc.attrs.eventTitle}</td>
			</tr>			
			<tr style="vertical-align: top;text-align: center;">
				<td style="padding-top: 8px;font-size: 16px;font-weight: bold;" class="zwx_dialog_font_16">（#{cc.attrs.eventState}）</td>
			</tr>
			<tr style="vertical-align: top;">
				<td style="padding-top: 8px;padding-left:5px;text-align: left;" class="zwx_dialog_font">地区：#{cc.attrs.zoneName}<p:spacer width="10"/>发病人数:#{cc.attrs.eventFbrs}人</td>
			</tr>						
			<tr style="vertical-align: top;">
				<td style="padding-left:5px;text-align: left;" class="zwx_dialog_font">涉及人数：#{cc.attrs.eventSjrs}人<p:spacer width="10"/>死亡人数:#{cc.attrs.eventSwrs}人</td>
			</tr>						
		</table>
	</div>
   	<div class="zwx_comp_blockmenu_menuitem" id="#{cc.clientId}_menudiv">
   		<span onclick="zwx_comp_blockmenu_click('#{cc.attrs.eventId}')" style="margin-left: 5px;cursor: pointer;" onmouseover="this.style.background='orange'" onmouseout="this.style.background=''">流调分析</span>
   		<span onclick="" style="margin-left: 5px;cursor: pointer;" onmouseover="this.style.background='orange'" onmouseout="this.style.background=''">送检情况</span>
   	</div>	
	
	<script type="text/javascript">
	//<![CDATA[
	function zwx_comp_block_click(id) {
		jQuery(document.getElementById(id.replace('blockdiv','menudiv'))).toggle("slow");
		return false;
	}
	function zwx_comp_blockmenu_click(eventId) {
    	var url = "#{request.scheme}" + "://" + "#{request.serverName}"+":"+"#{request.serverPort}"+"#{request.contextPath}"+"/webapp/emerg/tdEmEpiActList.faces";
    	url = url + "?eventId=" + eventId;
    	this.location.href=url;		
	}
	
		//]]>		           
	</script>
	
   	<style type="text/css">
   		.zwx_comp_blockmenu_block {
   			width:250px;
   			height: 150px;
   			border-radius: 6px;
   			border: 1px solid #72AAD6;
   			cursor: pointer;
   		}
   	
   		.zwx_comp_blockmenu_menuitem {
   			width: 250px;
   			height: 30px;
   			display: none;
   			border: 1px solid #72AAD6;
   			border-radius: 6px;
   			position: absolute;
   			
   			background:"#FFCC33";
            filter:alpha(opacity=50);
            -moz-opacity:0.50;
            opacity:0.50;
            
            text-align: center;
            line-height: 30px;
   		}
   	</style>		
</composite:implementation>
</body>
</html>