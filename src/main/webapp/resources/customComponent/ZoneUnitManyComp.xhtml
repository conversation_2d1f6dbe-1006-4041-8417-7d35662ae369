<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">

<composite:interface>
    <!--已选中的名称-->
    <composite:attribute name="unitName"/>
    <!--已选中的-->
    <composite:attribute name="unitRids"/>
    <!--数据集合-->
    <composite:attribute name="zoneUnitList"/>
    <!--是否显示清除-->
    <composite:attribute name="ifShowTrash"/>
    <composite:attribute name="zoneUnitHeight"/>
    <composite:attribute name="zoneUnitWidth"/>
    <composite:attribute name="zoneUnitPaddingLeft"/>
</composite:interface>

<composite:implementation>
    <style type="text/css">
        .ui-tree .ui-tree-container{
            overflow: visible;
        }
    </style>
    <p:outputPanel>
        <h:panelGrid columns="4" style="border-color: #ffffff;margin: 0px;padding-left:#{cc.attrs.zonePaddingLeft==null?9:cc.attrs.zonePaddingLeft}px;" id="searchGrid">
            <p:inputText  id="unitName" value="#{cc.attrs.unitName}" binding="#{cc.nameInput}"
                          style="width: #{cc.attrs.zoneUnitWidth==null?'180':cc.attrs.zoneUnitWidth}px;" readonly="true"
                          onclick="#{cc.clientId}_initZoneUnitTree();" />

            <p:commandLink styleClass="ui-icon ui-icon-search" type="button" style="position: relative;left: -30px;"
                           process="@this"  action="#{cc.initZoneUnitTree}"  oncomplete="PF('#{cc.clientId}:ZoneUnitPanel').show()" />
            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                           style="position: relative;left: -33px;display:#{cc.attrs.ifShowTrash=='true'?'block':'none'}; "
                           action="#{cc.clearCodeName}" process="@this"
                           update="searchGrid,zoneUnitPanel,unitRids" />
        </h:panelGrid>
        <h:inputHidden  id="unitRids" value="#{cc.attrs.unitRids}" binding="#{cc.unitRidsInput}"/>
        <p:remoteCommand name="#{cc.clientId}_initZoneUnitTree" action="#{cc.initZoneUnitTree}" process="@this"
                         oncomplete="PF('#{cc.clientId}:ZoneUnitPanel').show()"/>
        <p:remoteCommand name="#{cc.clientId}_hideMining" process="@this,zoneUnitTree" action="#{cc.hideAction}" />
        <p:overlayPanel id="zoneUnitPanel" for="unitName" style="width:318px;" widgetVar="#{cc.clientId}:ZoneUnitPanel"
                        showCloseIcon="true" onHide="#{cc.clientId}_hideMining();">
            <p:tree binding="#{cc.uiTree}" var="node" selectionMode="checkbox" id="zoneUnitTree" dynamic="true"
                    style="width: 290px;height: #{cc.attrs.zoneUnitHeight==null?400:cc.attrs.zoneUnitHeight}px;overflow-y: auto;"
                    selection="#{cc.selectedExportNodes}">
                <p:treeNode>
                    <h:outputText value="#{node.name}"/>
                </p:treeNode>
            </p:tree>
        </p:overlayPanel>
    </p:outputPanel>
</composite:implementation>
</html>