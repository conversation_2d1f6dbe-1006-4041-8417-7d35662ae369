<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">
<composite:interface>
    <!-- 输入框内容 -->
    <composite:attribute name="dataLabel"/>
    <!-- 传入的Map -->
    <composite:attribute name="dataMap" required="true"/>
    <!-- 选择的value，逗号隔开 -->
    <composite:attribute name="dataValue" required="true"/>
    <composite:attribute name="onchange" />
    <!-- 输入框宽度，默认180px，传数字即可 -->
    <composite:attribute name="inputWidth" />
    <!-- 下拉panel宽度，默认280px，传数字即可 -->
    <composite:attribute name="panelWidth" />
    <!-- 下拉panel高度，默认400px，传数字即可 -->
    <composite:attribute name="height" />
    <!-- 是否可操作 -->
    <composite:attribute name="disabled" />
    <!-- 左偏移量 -->
    <composite:attribute name="zonePaddingLeft" />
</composite:interface>

<composite:implementation>
    <style type="text/css">
        .ui-tree .ui-tree-container{
            overflow: visible;
        }
    </style>
    <p:outputPanel>
        <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;padding-left:#{cc.attrs.zonePaddingLeft==null?0:cc.attrs.zonePaddingLeft}px;" id="searchCitySizeGrid">
            <p:inputText id="codeName" value="#{cc.attrs.dataLabel}" binding="#{cc.nameInput}" style="width: #{cc.attrs.inputWidth==null?'180':cc.attrs.inputWidth}px;" readonly="true"
                         onclick="PF('#{cc.clientId}:initBtn').getJQ().click();"
                         disabled="#{cc.attrs.disabled=='true'}"/>
            <p:commandLink styleClass="ui-icon ui-icon-search" type="button" style="position: relative;left: -30px;"
                           process="@this" onclick="PF('#{cc.clientId}:initBtn').getJQ().click();"
                           disabled="#{cc.attrs.disabled=='true'}"/>
            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                           style="position: relative;left: -33px;"
                           action="#{cc.clearCodeName}" process="@this"
                           update="codeName,selectedIds,dataOverPanel"
                           disabled="#{cc.attrs.disabled=='true'}"/>
        </h:panelGrid>

        <h:inputHidden id="selectedIds" value="#{cc.attrs.dataValue}" binding="#{cc.idsValue}"/>
        <p:commandButton style="display:none" action="#{cc.init}" process="@this,@parent"
                         oncomplete="PF('#{cc.clientId}:DataOverPanel').show();" widgetVar="#{cc.clientId}:initBtn"/>
        <p:commandButton style="display:none" action="#{cc.hideAction}" process="@this,@parent"
                         oncomplete="#{cc.attrs.onchange}" widgetVar="#{cc.clientId}:closeBtn"/>
        <p:overlayPanel id="dataOverPanel" for="codeName" style="width: #{cc.attrs.panelWidth==null?'280':cc.attrs.panelWidth}px;" widgetVar="#{cc.clientId}:DataOverPanel"
                        showEvent="dblclick" showCloseIcon="true" onHide="PF('#{cc.clientId}:closeBtn').getJQ().click();">
            <p:tree id="simpleCodeTree" binding="#{cc.uiTree}" var="node" selectionMode="checkbox"
                    style="width: #{cc.attrs.panelWidth==null?'250':cc.attrs.panelWidth-30}px;height: #{cc.attrs.height==null?'400':cc.attrs.height}px;overflow-y: auto;"
            >
                <p:ajax event="select"  process="@this" listener="#{cc.onNodeSelect}"/>
                <p:ajax event="unselect"  process="@this" listener="#{cc.onNodeNoSelect}"/>
                <p:treeNode>
                    <h:outputText value="#{node.key}"/>
                </p:treeNode>
            </p:tree>
        </p:overlayPanel>
    </p:outputPanel>
</composite:implementation>
</html>