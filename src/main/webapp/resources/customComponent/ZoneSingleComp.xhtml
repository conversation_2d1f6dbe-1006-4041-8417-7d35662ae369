<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">

<composite:interface>
    <composite:attribute name="zoneName"/>
    <composite:attribute name="zoneId"/>
    <composite:attribute name="zoneCode"/>
    <composite:attribute name="zoneType"/>
    <composite:attribute name="onchange"/>
    <composite:attribute name="zoneList" required="true"/>
    <!-- 下拉panel高度，默认420px，传数字即可 -->
    <composite:attribute name="panelHeight" />
    <!-- 下拉panel高度，默认400px，传数字即可 -->
    <composite:attribute name="height" />
    <!-- 文本框宽度 -->
    <composite:attribute name="width" />
</composite:interface>

<composite:implementation>
    <h:panelGrid columns="2" style="border-color: #ffffff;margin: 0px;padding: 0px;">
        <p:inputText id="zoneName" value="#{cc.attrs.zoneName}" binding="#{cc.nameInput}"  style="width: #{cc.attrs.width==null?'180':cc.attrs.width}px;" onclick="#{cc.clientId}_initZoneTree();" readonly="true" />
        <p:commandLink styleClass="ui-icon ui-icon-search"  id="initTreeLink" partialSubmit="true"
                       action="#{cc.initZoneTree}" process="@this" style="position: relative;left: -30px;"
                       oncomplete="PF('#{cc.clientId}:ZonePanel').show()">
        </p:commandLink>
    </h:panelGrid>
     <h:inputHidden id="zoneId" value="#{cc.attrs.zoneId}" binding="#{cc.idInput}" />
     <h:inputHidden id="zoneCode" value="#{cc.attrs.zoneCode}" binding="#{cc.codeInput}"/>
     <h:inputHidden id="zoneType" value="#{cc.attrs.zoneType}" binding="#{cc.levelInput}"/>
     <p:remoteCommand name="#{cc.clientId}_initZoneTree" action="#{cc.initZoneTree}" process="@this"  oncomplete="PF('#{cc.clientId}:ZonePanel').show()"/>
        <p:overlayPanel id="zonePanel" for="zoneName" style="width:280px;height: #{cc.attrs.panelHeight==null?'420':cc.attrs.panelHeight}px;" widgetVar="#{cc.clientId}:ZonePanel" showEvent="dblclick" showCloseIcon="true">
            <p:tree binding="#{cc.uiTree}" var="node" selectionMode="single" id="zoneTree" dynamic="true" 
                    style="width: 250px;overflow-y: auto;height: #{cc.attrs.height==null?'400':cc.attrs.height}px;" selection="#{cc.selectedNode}" >
                <p:ajax event="select" update=":#{cc.clientId}:zoneId,:#{cc.clientId}:zoneName,:#{cc.clientId}:zoneCode,:#{cc.clientId}:zoneType" listener="#{cc.onNodeSelect}"
                        process="@this,@parent" oncomplete="PF('#{cc.clientId}:ZonePanel').hide();#{cc.attrs.onchange}"/>
                <p:ajax event="expand"/>
                <p:treeNode>
                    <h:outputText value="#{node.zoneName}"/>
                </p:treeNode>
            </p:tree>
        </p:overlayPanel>     
</composite:implementation>
</html>