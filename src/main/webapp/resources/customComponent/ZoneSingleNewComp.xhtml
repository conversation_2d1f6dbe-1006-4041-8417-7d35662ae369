<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">

<composite:interface>
    <composite:attribute name="zoneName"/>
    <composite:attribute name="zoneId"/>
    <composite:attribute name="zoneCode"/>
    <composite:attribute name="zoneCodeNew"/>
    <composite:attribute name="zoneType"/>
    <composite:attribute name="realZoneType"/>
    <composite:attribute name="onchange"/>
    <composite:attribute name="zoneList" required="true"/>
    <composite:attribute name="resetList"/>
    <composite:attribute name="resetTimes"/>
    <composite:attribute name="setedTimes"/>
    <composite:attribute name="ifShowTrash"/>
    <composite:attribute name="zonePaddingLeft"/>
    <composite:attribute name="zoneHeight"/>
    <composite:attribute name="zoneWidth"/>
    <!--最高可选择的地区级别-->
    <composite:attribute name="choseZoneTypeMin"/>
    <!--最低可选择的地区级别-->
    <composite:attribute name="choseZoneTypeMax"/>
</composite:interface>

<composite:implementation>
    <div style="border-color: #ffffff;margin: 0px;padding: 0px;display: table-cell;padding-left:#{cc.attrs.zonePaddingLeft==null?9:cc.attrs.zonePaddingLeft}px;" >
        <p:inputText id="zoneName" value="#{cc.attrs.zoneName}" binding="#{cc.nameInput}"  style="width: #{cc.attrs.zoneWidth==null?180:cc.attrs.zoneWidth}px;" onclick="#{cc.clientId}_initZoneTree();" readonly="true" />
    </div>
   	<div style="display: table-cell;vertical-align: middle;">
       <p:commandLink styleClass="ui-icon ui-icon-search"  id="initTreeLink" partialSubmit="true"
                      action="#{cc.initZoneTree}" process="@this" style="position: relative;left: -20px;"
                      oncomplete="PF('#{cc.clientId}:ZonePanel').show()">
       </p:commandLink>
   	</div>
    <div style="display: table-cell;vertical-align: middle;"   >
        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" process="@this" update="zoneName,zoneId,zoneCode,zoneCodeNew,zoneType,realZoneType,setedTimes" action="#{cc.clearCodeName}"  style="position: relative;left: -13px;display:#{cc.attrs.ifShowTrash=='true'?'block':'none'}; " ></p:commandLink>
    </div>
     <h:inputHidden id="zoneId" value="#{cc.attrs.zoneId}" binding="#{cc.idInput}" />
     <h:inputHidden id="zoneCode" value="#{cc.attrs.zoneCode}" binding="#{cc.codeInput}"/>
     <h:inputHidden id="zoneCodeNew" value="#{cc.attrs.zoneCodeNew}" binding="#{cc.newCodeInput}"/>
     <h:inputHidden id="zoneType" value="#{cc.attrs.zoneType}" binding="#{cc.levelInput}"/>
     <h:inputHidden id="realZoneType" value="#{cc.attrs.realZoneType}" binding="#{cc.realLevelInput}"/>
     <h:inputHidden id="setedTimes" value="#{cc.attrs.setedTimes}" binding="#{cc.setedTimesInput}"/>
     <p:remoteCommand name="#{cc.clientId}_initZoneTree" action="#{cc.initZoneTree}" process="@this"  oncomplete="PF('#{cc.clientId}:ZonePanel').show()"/>
        <p:overlayPanel id="zonePanel" for="zoneName" style="width:280px;" widgetVar="#{cc.clientId}:ZonePanel" showCloseIcon="true">
            <p:tree binding="#{cc.uiTree}" var="node" selectionMode="single" id="zoneTree" dynamic="true"
                    style="width: 250px;height: #{cc.attrs.zoneHeight==null?400:cc.attrs.zoneHeight}px;overflow-y: auto;" selection="#{cc.selectedNode}">
                <p:ajax event="select" update=":#{cc.clientId}:zoneId,:#{cc.clientId}:zoneName,:#{cc.clientId}:zoneCode,:#{cc.clientId}:zoneCodeNew,:#{cc.clientId}:zoneType" listener="#{cc.onNodeSelect}"
                        process="@this,@parent" />
                <p:ajax event="expand"/>
                <p:treeNode>
                    <h:outputText value="#{node.zoneName}"/>
                </p:treeNode>
            </p:tree>
        </p:overlayPanel>     
</composite:implementation>
</html>