<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite="http://java.sun.com/jsf/composite">

<composite:interface>
    <!-- 是否树结构展示 -->
    <composite:attribute name="ifTree"/>
    <!-- 输入框内容 -->
    <composite:attribute name="codeName"/>
    <!-- 查询码表集合 -->
    <composite:attribute name="simpleCodeList" required="true"/>
    <!-- 选择的码表主键Rid，逗号隔开 -->
    <composite:attribute name="selectedIds" required="true"/>
    <composite:attribute name="onchange" />
    <!-- 输入框宽度，默认180px，传数字即可 -->
    <composite:attribute name="inputWidth" />
    <!-- 下拉panel宽度，默认280px，传数字即可 -->
    <composite:attribute name="panelWidth" />
    <!-- 下拉panel高度，默认400px，传数字即可 -->
    <composite:attribute name="height" />
    <!-- 树形结构，父级是否可选择 -->
    <composite:attribute name="ifSelectParent" />
    <!-- 树形结构，是否返回选择的rid（包含父级rid），文本选了父级就不显示子类的 -->
    <composite:attribute name="ifContantsParent" />
    <!-- 是否可操作 -->
    <composite:attribute name="disabled" />
    <!--是否有上下标-->
    <composite:attribute name="ifEscape" />
    <!--是否重置数据源-->
    <composite:attribute name="reset"/>
    <!--搜索框 距离-->
    <composite:attribute name="clientWidth"/>
    <!--清空 距离-->
    <composite:attribute name="clearWidth"/>
</composite:interface>

<composite:implementation>
	<style type="text/css">
		 .ui-tree .ui-tree-container{
		 	overflow: visible;
		 }
	</style>
    <p:outputPanel>
        <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;" id="searchCitySizeGrid">
            <p:outputLabel  rendered="#{cc.attrs.ifEscape=='true'}" id="codeNameLabel" escape="false" value="#{cc.attrs.codeName}" binding="#{cc.nameLabel}"
                          style="width: #{cc.attrs.inputWidth==null?'180':cc.attrs.inputWidth}px;display: block;height:16px; word-break: break-all;cursor: pointer;overflow: hidden;"
                           onclick="PF('#{cc.clientId}:initBtn').getJQ().click();" styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all" />
            <p:inputText rendered="#{cc.attrs.ifEscape!='true'}" id="codeName" value="#{cc.attrs.codeName}" binding="#{cc.nameInput}" style="width: #{cc.attrs.inputWidth==null?'180':cc.attrs.inputWidth}px;" readonly="true"
                         onclick="PF('#{cc.clientId}:initBtn').getJQ().click();"
                         disabled="#{cc.attrs.disabled=='true'}"/>
            <p:commandLink styleClass="ui-icon ui-icon-search" type="button" style="position: relative;left: -#{cc.attrs.clientWidth==null?'30':cc.attrs.clientWidth}px;;"
                           process="@this" onclick="PF('#{cc.clientId}:initBtn').getJQ().click();"
                           disabled="#{cc.attrs.disabled=='true'}"/>
            <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                           style="position: relative;left:-#{cc.attrs.clearWidth==null?'33':cc.attrs.clearWidth}px;"
                           action="#{cc.clearCodeName}" process="@this"
                           update="codeName,dataOverPanel,codeNameLabel,selectedIds"
                           oncomplete="#{cc.attrs.onchange}"
                           disabled="#{cc.attrs.disabled=='true'}"/>
        </h:panelGrid>

        <h:inputHidden id="selectedIds" value="#{cc.attrs.selectedIds}" binding="#{cc.idsValue}"/>
        <h:inputHidden id="reset" value="#{cc.attrs.reset}" binding="#{cc.resetInput}"/>
        <p:commandButton style="display:none" action="#{cc.init}" process="@this,@parent"
                         oncomplete="PF('#{cc.clientId}:DataOverPanel').show();" widgetVar="#{cc.clientId}:initBtn"/>
        <p:commandButton style="display:none" action="#{cc.hideAction}" process="@this,@parent"
                         oncomplete="#{cc.attrs.onchange}" widgetVar="#{cc.clientId}:closeBtn"/>
        <p:overlayPanel id="dataOverPanel" for="codeNameLabel" style="width: #{cc.attrs.panelWidth==null?'280':cc.attrs.panelWidth}px;" widgetVar="#{cc.clientId}:DataOverPanel"
                         showCloseIcon="true" onHide="PF('#{cc.clientId}:closeBtn').getJQ().click();">
            <p:tree id="simpleCodeTree" binding="#{cc.uiTree}" var="node" selectionMode="checkbox"
                    style="width: #{cc.attrs.panelWidth==null?'250':cc.attrs.panelWidth-30}px;height: #{cc.attrs.height==null?'400':cc.attrs.height}px;overflow-y: auto;"
                    >
                <p:ajax event="select"  process="@this" listener="#{cc.onNodeSelect}"/>
				<p:ajax event="unselect"  process="@this" listener="#{cc.onNodeNoSelect}"/>
                <p:treeNode>
                    <h:outputText value="#{node.codeName}" escape="false"/>
                </p:treeNode>
            </p:tree>
        </p:overlayPanel>
    </p:outputPanel>
</composite:implementation>
</html>