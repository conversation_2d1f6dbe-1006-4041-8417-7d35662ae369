(function($j) {
	$j.positionFixed = function(el) {
		$j(el).each(function() {
			new fixed(this)
		});
		return el;
	};
	$j.fn.positionFixed = function() {
		return $j.positionFixed(this)
	}
	var fixed = $j.positionFixed.impl = function(el) {
		var o = this;
		o.sts = {
			target : $j(el).css('position', 'fixed'),
			container : $j(window)
		}
		o.sts.currentCss = {
			top : o.sts.target.css('top'),
			right : o.sts.target.css('right'),
			bottom : o.sts.target.css('bottom'),
			left : o.sts.target.css('left')
		}
		if (!o.ie6)
			return;
		o.bindEvent();
	}
	$j.extend(fixed.prototype, {
		ie6 : $.browser.msie && $.browser.version < 7.0,
		bindEvent : function() {
			var o = this;
			o.sts.target.css('position', 'absolute')
			o.overRelative().initBasePos();
			o.sts.target.css(o.sts.basePos)
			o.sts.container.scroll(o.scrollEvent()).resize(o.resizeEvent());
			o.setPos();
		},
		overRelative : function() {
			var o = this;
			var relative = o.sts.target.parents().filter(function() {
				if ($j(this).css('position') == 'relative')
					return this;
			})
			if (relative.size() > 0)
				relative.after(o.sts.target)
			return o;
		},
		initBasePos : function() {
			var o = this;
			o.sts.basePos = {
				top : o.sts.target.offset().top
						- (o.sts.currentCss.top == 'auto' ? o.sts.container
								.scrollTop() : 0),
				left : o.sts.target.offset().left
						- (o.sts.currentCss.left == 'auto' ? o.sts.container
								.scrollLeft() : 0)
			}
			return o;
		},
		setPos : function() {
			var o = this;
			o.sts.target.css( {
				top : o.sts.container.scrollTop() + o.sts.basePos.top,
				left : o.sts.container.scrollLeft() + o.sts.basePos.left
			})
		},
		scrollEvent : function() {
			var o = this;
			return function() {
				o.setPos();
			}
		},
		resizeEvent : function() {
			var o = this;
			return function() {
				setTimeout(function() {
					o.sts.target.css(o.sts.currentCss)
					o.initBasePos();
					o.setPos()
				}, 1)
			}
		}
	})
})(jQuery);
(function($) {
	$.fn.popWin = function(options) {
		var def = {
			PopSelector : "",
			CloseSelector : "",
			slideTime : 600
		};
		var options = $.extend(def, options);
		var noie6 = !($.browser.msie && ($.browser.version == "6.0") && !$.support.style);
		var PopSelector = options.PopSelector, CloseSelector = options.CloseSelector, slideTime = options.slideTime;
		if (noie6) {
			$(PopSelector).slideDown(slideTime)
		} else {
			$(PopSelector).fadeIn(slideTime);
			var thisPop = $(PopSelector);
			jQuery(function($j) {
				$j(thisPop).positionFixed()
			})
		}
		if (noie6) {
			$(CloseSelector).click(function() {
				$(PopSelector).slideUp(slideTime);
				//updRidState();
			});
		} else {
			$(CloseSelector).click(function() {
				$(PopSelector).fadeOut(slideTime);
				//updRidState();
			});
		}
	}
})(jQuery);