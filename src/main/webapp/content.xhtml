<html xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:f="http://java.sun.com/jsf/core" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">
	<h:head>
		<title></title>
	<link rel="stylesheet" type="text/css" href="#{request.contextPath}/resources/css/default.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/fastDesktop.css" />
        <link rel="stylesheet" href="/resources/component/quickDesktop/css/help.css" />
        <ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
        <script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.widget.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.core.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.mouse.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.sortable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.draggable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.droppable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/desk.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery.dialog.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/ShortcutDesktop.js"></script>
        <script type="text/javascript" src="/resources/component/quickDesktop/help.js"></script>
		<script>
		//<![CDATA[
         function showMess(data){
		      if(data=="0"){
		       window.location.href = "content.faces";
		      }     
		           }
	
		//]]>
		</script>
	</h:head><f:view>
	<body class="deskbody">
	<div class="desktopMain"> 
				<div class="desktopNavL">
				</div>
				<ul class="desktopNav" style="margin-top: 0px;">
				</ul>
				<div class="desktopNavR"  >
					<a href="javascript:;" ></a>
				</div>
				<div class="mainList">
					<div class="ulList">
					</div>
				</div>
			</div>
			<a id="showHelp" href="javascript:void(0)" title="帮助">?</a><font color="yellow">
				    </font>
		    <div id="helpCon">
		        <div class="help-overlay">
		        </div>
		        <div id="help-content" class="help-1">
		        </div>
		        <div id="help-skip" class="help-skip">
		        </div>
		    </div>
	</body></f:view>
</html>