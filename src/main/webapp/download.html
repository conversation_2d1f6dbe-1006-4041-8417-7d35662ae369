<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:f="http://java.sun.com/jsf/core" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">

<head>
<meta http-equiv="Pragma" content="no-cache" />
<meta http-equiv="Cache-Control" content="no-cache" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta http-equiv="Access-Control-Allow-Origin" content="*" />
	<title>系统插件安装</title>
	<style>
img {
	border: 0px;
}

html,body {
	height: 100%;
}
</style>
	<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />

	<script type="text/javascript">
		//<![CDATA[
		/**
		 * 检查基础插件是否安装
		 */
		function isAcrobatPluginInstall() {
			//  alert(navigator.plugins);
			if (navigator.plugins && navigator.plugins.length) {
				for (x = 0; x < navigator.plugins.length; x++) {

					//    alert(navigator.plugins[x].name);
					if (navigator.plugins[x].name == 'ActiveX hosting plugin for NPAPI')
						return true;
				}
			}
		}

		/**
		 * 检查office是否安装
		 */
		function isOfficePluginInstall() {
			try {
				if(WebOffice.Version =="undefined" || WebOffice.Version==null){
					return true;
				}else{
					return false;
				}
			} catch (e) {
				return false;
			}
		}
		/**
		 * 检查身份证读卡器插件是否安装
		 */
		function isIdcPluginInstall() {
			try {
				idc.test();
				return true;
			} catch (e) {
				return false;
			}
		}

		function isFrptPluginInstall() {
			try {
				frpt.test();
				return true;
			} catch (e) {
				return false;
			}
		}
		/**已安装*/
		function install(v) {
			$(v).css({background : "url(/resources/images/download/download_04.png) top left no-repeat"});
		}

		function uninstall(v) {
			$(v).css({background : "url(/resources/images/download/download_02.png) top left no-repeat"});
			$(v).mouseover(function() {$(v).css({background : "url(/resources/images/download/download_03.png) top left no-repeat"});});
			$(v).mouseout(function() {$(v).css({background : "url(/resources/images/download/download_02.png) top left no-repeat"});});
		}

		 window.onload =function() {
			/**基础插件是否安装*/
			if (isAcrobatPluginInstall()) {
				install("#basePlugin");
			} else {
				uninstall("#basePlugin");
				$("#basePlugin").click(function() {
					window.open('/resources/files/extension.crx', '_self');
				});
			}
			/**报表控件是否安装*/
			if (isFrptPluginInstall()) {
				install("#frptPlugin");
			} else {
				uninstall("#frptPlugin");
				$("#frptPlugin").click(function() {
					window.location.href = '/resources/files/ChisReport.exe';
				});
			}
			/**身份证读卡器控件是否安装*/
			if (isIdcPluginInstall()) {
				install("#idcPlugin");
			} else {
				uninstall("#idcPlugin");
				$("#idcPlugin").click(function() {
					window.location.href = '/resources/files/IDReader.exe';
				});
			}
			/**Office是否安装*/
			if (isOfficePluginInstall()) {
				install("#officePlugin");
			} else {
				uninstall("#officePlugin");
				$("#officePlugin").click(function() {
					window.location.href = '/resources/files/chiscdcWebOfficePlus.crx';
				});
			}
		}
		//]]>
	</script>
</head>

<body style="margin:0px">
	<table width="100%" height="100%" border="0" cellspacing="0"
		cellpadding="0"
		style="background: url(/resources/images/browser/back.png) repeat-x; leftmargin:0;topmargin:0;marginwidth：0;marginheight:0;">
		<tr valign="top">
			<td style="height: 30px;"></td>
		</tr>
		<tr valign="top">
			<td>
				<table
					style="background:url(/resources/images/download/download_01.png) center top no-repeat;width: 1024px;text-align: center;vertical-align: top;"
					border="0" cellpadding="0" cellspacing="0" valign="top"
					align="center">
					<tr align="top">
						<td colspan="8" style="height:187px;width: 1024px;"></td>
					</tr>
					<tr align="top" style="height:95px;">
						<td style="width: 420px;"></td>
						<td id="basePlugin" style="width:260px;cursor: pointer;"></td>
						<td style="width: 424px;"></td>
					</tr>
					<tr align="top">
						<td style="height: 24px;"></td>
					</tr>
					<tr align="top" style="height:95px;">
						<td style="width: 420px;"></td>
						<td id="frptPlugin" style="width:260px;cursor: pointer;"></td>
						<td style="width: 424px;"></td>
					</tr>
					<tr align="top">
						<td style="height: 23px;"></td>
					</tr>
					<tr align="top" style="height:95px;">
						<td style="width: 420px;"></td>
						<td id="idcPlugin" style="width:260px;cursor: pointer;"></td>
						<td style="width: 424px;"></td>
					</tr>
					<tr align="top">
						<td style="height: 24px;"></td>
					</tr>
					<tr align="top" style="height:95px;">
						<td style="width: 420px;"></td>
						<td id="officePlugin" style="width:260px;cursor:pointer;"></td>
						<td style="width: 424px;"></td>
					</tr>
					<tr align="top">
						<td style="height: 80px;"></td>
					</tr>
				</table>
				<object id="frpt" width="0" height="0" hspace="0" vspace="0"
						classid="CLSID:D4DEDE1E-578F-489D-A5C7-3AE2796F67B0"></object>
				<object id="idc" classid="clsid:3166844F-A299-45FE-ACE0-AA81A6CD9D50" hspace="0" vspace="0"
						width="0" height="0"> </object>
				<object id="WebOffice" width="0" height="0" hspace="0" vspace="0"
					classid="clsid:8B23EA28-2009-402F-92C4-59BE0E063499" > </object>
			</td>
		</tr>
	</table>
</body>
</html>