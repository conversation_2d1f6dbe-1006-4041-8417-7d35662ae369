<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<h:head>
    <link rel="stylesheet" type="text/css"
          href="#{request.contextPath}/resources/css/default.css"/>

    <title><h:outputText value="#{applicationBean.appTitle}"/></title>
    <h:outputScript library="js" name="namespace.js"/>
    <h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
    <script type="text/javascript"
            src="/resources/js/validate/system/validate.js"></script>
    <script type="text/javascript"
            src="/resources/js/aes.js"></script>
    <script type="text/javascript">
        var zwxMiniJQ = $.noConflict(true);
    </script>
    <script type="text/javascript">
        //<![CDATA[
        jQuery(document).ready(
            function () {
                //alert(navigator.userAgent.toLowerCase());
                var isFirefox = navigator.userAgent.toLowerCase().match(
                    /firefox/) != null; //是否Chrome浏览器
                var isChrome = navigator.userAgent.toLowerCase().match(
                    /chrome/) != null; //是否Chrome浏览器
                var isWebkit = navigator.userAgent.toLowerCase().match(
                    /applewebkit/) != null; //是否极速模式浏览器
                var isSafari = jQuery.browser.safari; //是否Safari浏览器
                if (!isSafari && !isWebkit && !isChrome && !isFirefox) {
                    //跳转到下载页面
                    forwardBtn();
                }
            });
        document.onkeydown = function(event) {
            var target, code, tag;
            if (!event) {
                event = window.event; //针对ie浏览器
                target = event.srcElement;
                code = event.keyCode;
                if (code == 13) {
                    tag = target.tagName;
                    if (tag == "TEXTAREA") { return true; }
                    else { return false; }
                }
            }
            else {
                target = event.target; //针对遵循w3c标准的浏览器，如Firefox
                code = event.keyCode;
                if (code == 13) {
                    tag = target.tagName;
                    if (tag == "INPUT") { return false; }
                    else { return true; }
                }
            }
        };
        //图形验证码
        function setVerImg(){
            $("#form\\:verifyCode").val("");
            var url = Math.random();
            document.getElementById("abc").src = "#{request.contextPath}/authImage2?id=" + url;
        }
        var countdown=60;
        //发送验证码倒计时
        function setCountDownTime() {
            var obj = $("#personForm\\:smsVerifyCodeLink");
            if (countdown == 0) {
                obj.attr('disabled',false);
                obj.css('margin-left','-75px');
                obj.css('pointer-events','auto');
                obj.css('color','#00a0e9');
                obj.text("获取验证码");
                countdown = 60;
                return;
            } else {
                obj.attr('disabled',true);
                obj.css('margin-left','-90px');
                obj.css('pointer-events','none');
                obj.css('color','#616161');
                obj.text("重新发送(" + countdown + "s)");
                countdown--;
            }
            setTimeout(function() {
                setCountDownTime(obj);
            },1000);
        }

        function jm(type){
            var idc = document.getElementById("personForm:idc").value;
            var mobileNum = document.getElementById("personForm:mobileNum").value;
            if (idc) {
                var hash = encrypt(idc);
                $("#personForm\\:encryIdc").val(hash);
            }else {
                $("#personForm\\:encryIdc").val('');
            }
            if (mobileNum) {
                var hash1 = encrypt(mobileNum);
                $("#personForm\\:encryMobileNum").val(hash1);
            }else {
                $("#personForm\\:encryMobileNum").val('');
            }
            if(type ==1){
                codeAction();
            }else {
                nextAction();
            }
        }
        function encrypt(data) {
            var key  =  CryptoJS.enc.Utf8.parse('#{retrievePasswordBean.encryptKey}');
            return CryptoJS.AES.encrypt(data, key, {mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.Pkcs7}).toString();
        }
        //]]>
    </script>
    <style type="text/css">
        /*.panel {*/
        /*    width: 100%;*/
        /*}*/

        .panel2 {
            background-color: #fcfdfd;;
            box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
            height: 93px;
            width: 100%;
            border-style: hidden;

        }
        .panel3 {
            box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
            width: 1240px;
            /*height: 974px;*/
            background-color: #fcfdfd;
            border: 1px solid #adabab59;
            position: absolute;
            z-index: 1;
            top: 130px;
            left: -webkit-calc(50% - 620px);
        }
        #abc {
            height:22px;
            margin-left: -79px;
            cursor: pointer;
            position: absolute;
            margin-top: 7px;
        }
        .panel3 tr{
            border-style: hidden;
        }

    </style>
</h:head>

<h:body leftmargin="0" topmargin="0" onload="setVerImg();"

        style="margin-right: 0px;margin-top:0px; overflow: visible;background-color: #F0F0F0; ">
    <h:form id="personForm">
        <h:inputHidden id="encryIdc" value="#{retrievePasswordBean.encryIdc}"></h:inputHidden>
        <h:inputHidden id="encryMobileNum" value="#{retrievePasswordBean.encryMobileNum}"></h:inputHidden>
        <p:panelGrid styleClass="panel2">
            <p:row>
                <p:column colspan="3"
                          style="width: 1280px; height: 78px;padding-left: 30px; background-color: #2a6fcd; box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.02); ">
                    <p:graphicImage style="vertical-align: middle; height: 63%;"
                                    value="/resources/images/login/registerWhHead.png"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid styleClass="panel3" >
            <p:row >
                <p:column style="text-align: center;  font-weight: 700; width: 1238px;  height: 60px;border-bottom-color: transparent;background: linear-gradient(180deg,#fcfcfd, #f7f8fa); box-shadow: 0px -1px 0px 0px #dcdee3 inset;">
                    <font color="#333333"
                          style="font-size:23px;text-align: center;line-height: 22px;height: 20px;letter-spacing: 0px;">找回账号密码</font>
                </p:column>
            </p:row>
            <p:row >
                <p:column style="text-align: center;   width: 1238px;  height: 315px;border-bottom-color: transparent;padding-bottom:200px">
                    <p:panelGrid style="margin-top: 3px;background-color: #fcfdfd;margin-left: 20%;">
                        <!--身份证号-->
                        <p:row>
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;">
                                <font color="red">*</font>
                                <p:outputLabel value="身份证号："
                                               redisplay="true"/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                <p:inputText id="idc"
                                        style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                        value="#{retrievePasswordBean.idc}"
                                        placeholder="请输入"
                                        styleClass="text"  maxlength="25" />
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" >
                                <p:outputPanel  id="idcVerify">
                                    <div style="display:#{retrievePasswordBean.ifIdcEmtpy?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="身份证号不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                    <div style="display:#{retrievePasswordBean.ifIdcError?'flex':'none'};;width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="身份证号格式不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                    <div style="display:#{retrievePasswordBean.ifIdcExsit?'flex':'none'};width: 180px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="不存在该身份证号的账户" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                    <div style="display:#{retrievePasswordBean.ifIdcStop?'flex':'none'};width: 270px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="该身份证账户已停用，请联系辖区管理员" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                            </p:column>
                        </p:row>
                        <!--手机号-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                <font color="red">*</font>
                                <p:outputLabel value="手机号码："
                                               redisplay="true"/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                <p:inputText id="mobileNum"
                                        style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                        value="#{retrievePasswordBean.mobileNum}" placeholder="请输入"
                                        styleClass="text"  maxlength="25" />
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                <p:outputPanel  id="mobileNumVerify">
                                <div style="display:#{retrievePasswordBean.ifMobileEmtpy?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="手机号码不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                <div style="display:#{retrievePasswordBean.ifMobileError?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="手机号码格式不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                <div style="display:#{retrievePasswordBean.ifMobileMatch?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="手机号码不正确" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                        <!--图形验证码-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                <font color="red">*</font>
                                <p:outputLabel value="验证码："
                                               redisplay="true"/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" id="chk-td">
                                <p:inputText id ="verifyCode"
                                        style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                        value="#{retrievePasswordBean.verifyCode}" placeholder="请输入验证码" tabindex="3"
                                        styleClass="text"  maxlength="4" />
                                <img alt="" id="abc"  src="" onclick="changeVerImg()"/>
                                <p:remoteCommand name="changeVerImg" action="#{retrievePasswordBean.changeVerImg}" process="@this"  />
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                <p:outputPanel  id="verifyCodeVerify">
                                <div style="display:#{retrievePasswordBean.ifVerifyError?'flex':'none'};width: 130px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="验证码不能为空" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                <div style="display:#{retrievePasswordBean.ifVerifyMatch?'flex':'none'};width: 155px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="请输入正确的验证码" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                        <!--短信验证码-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                <font color="red">*</font>
                                <p:outputLabel value="短信验证："
                                               redisplay="true"/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" >
                                <p:inputText  style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;"
                                             placeholder="请输入短信验证码"
                                             id ="smsVerifyCode" maxlength="6"
                                             autocomplete="off"
                                             onkeyup="SYSTEM.verifyNum(this,6,0);"
                                             onkeydown="SYSTEM.verifyNum(this,6,0);"
                                             onblur="SYSTEM.verifyNum(this,6,0);"
                                             value="#{retrievePasswordBean.smsVerifyCode}" />
                                <p:commandLink id="smsVerifyCodeLink" value="获取验证码" process="@this"
                                               oncomplete="zwx_loading_start();jm(1);"
                                style=" position: absolute;margin-top: 7px; margin-left: -75px;text-decoration: none; white-space: nowrap;"/>
                                <p:remoteCommand name="codeAction" action="#{retrievePasswordBean.codeAction}"
                                                 process="@this,encryIdc,encryMobileNum,verifyCode"
                                                 update="idc,mobileNum,verifyCode,smsVerifyCode,idcVerify,mobileNumVerify,verifyCodeVerify,smsVerifyCodeVerify" />
                                <div style="background:url('/resources/images/login/login_ic_duanxi.png') no-repeat;" class="icSvg" />

                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                                <p:outputPanel  id="smsVerifyCodeVerify">
                                <div style="display:#{retrievePasswordBean.ifSmsOnline?'flex':'none'};width: 230px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="发送次数达到上限，不可再次发送" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                <div style="display:#{retrievePasswordBean.ifSmsErrror?'flex':'none'};width: 180px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                    <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                    value="/resources/images/login/mustIcon.png"/>
                                    <p:outputLabel value="请输入正确的短信验证码" style="color: red;margin-top: 3px;"></p:outputLabel>
                                </div>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                        <!--下一步-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" >
                                <p:commandButton id="nextBtn"
                                                 process="@this"
                                                 style="width:312px;height:40px;color:white;font-size: 16px !important;background: #2a6fcd;border-radius: 3px;"
                                                 oncomplete="jm(2);zwx_loading_start();" value="下一步 "/>
                                <p:remoteCommand name="nextAction" action="#{retrievePasswordBean.nextAction}"
                                                 process="@this,encryIdc,encryMobileNum,verifyCode,smsVerifyCode"
                                                 update="personForm" oncomplete="zwx_loading_stop()"/>
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </p:column>
            </p:row>


        </p:panelGrid>

        <p:focus id="focus"/>
    </h:form>
    <p:growl id="errorMsg" autoUpdate="true"/>
    <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
    <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
</h:body>
</html>