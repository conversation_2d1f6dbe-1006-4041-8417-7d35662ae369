<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
    xmlns:p="http://primefaces.org/ui">
	<h:head>
		<title>密码修改</title>
	</h:head>
	<f:view>
	<body>
    <p:dialog  header="密码修改" dynamic="true" position="500,200"  widgetVar="PwdDialog" resizable="false" width="500" height="160"
               modal="true" onHide="hidePwdDialog();">
        <h:form id="dialogDeskForm2">
            <script type="text/javascript" src="#{request.contextPath}/resources/js/md5.js">
            </script>
            <script type="text/javascript" src="/resources/js/validate/system/vliadPassword.js"></script>
            <script type="text/javascript">
                //<![CDATA[
                function hidePwdDialog() {
                    $("#dialogDeskForm\\:dialogDeskForm2\\:password0").val("");
                    $("#dialogDeskForm\\:dialogDeskForm2\\:password").val("");
                    $("#dialogDeskForm\\:dialogDeskForm2\\:password2").val("");
                    $("#dialogDeskForm\\:dialogDeskForm2\\:encryptPassword0").val("");
                    $("#dialogDeskForm\\:dialogDeskForm2\\:encryptPassword1").val("");
                    $("#dialogDeskForm\\:dialogDeskForm2\\:encryptPassword2").val("");
                }
                function changePsd() {
                    var password0 = $("#dialogDeskForm\\:dialogDeskForm2\\:password0").val();
                    var password1 = $("#dialogDeskForm\\:dialogDeskForm2\\:password").val();
                    var password2 = $("#dialogDeskForm\\:dialogDeskForm2\\:password2").val();
                    var encryptPassword0 = $("#dialogDeskForm\\:dialogDeskForm2\\:encryptPassword0");
                    var encryptPassword1 = $("#dialogDeskForm\\:dialogDeskForm2\\:encryptPassword1");
                    var encryptPassword2 = $("#dialogDeskForm\\:dialogDeskForm2\\:encryptPassword2");
                    encryptPassword0.val("");
                    encryptPassword1.val("");
                    encryptPassword2.val("");
                    if (password0) {
                        encryptPassword0.val(hex_md5(password0));
                    }
                    if (password1) {
                        encryptPassword1.val(hex_md5(password1));
                    }
                    if (password2) {
                        encryptPassword2.val(hex_md5(password2));
                    }
                    if(password0!=undefined && password1!=undefined && password0!='' && password1!='' && password0==password1){
                        changePsdAction([{name: 'check', value: "3"}]);
                        return;
                    }
                    if (!baseIsVliadPassword(password1)) {
                        changePsdAction([{name: 'check', value: "1"}]);
                    } else if (password1 != password2) {
                        changePsdAction([{name: 'check', value: "0"}]);
                    } else {
                        changePsdAction([{name: 'check', value: "2"}]);
                    }
                }
                //]]>
            </script>
            <p:panelGrid style="width:100%;" id="PwdDialogGrid">
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                        <font color="red">*</font>
                        <h:outputLabel for="password0" value="旧的密码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:password style="width: 200px" id="password0" maxlength="16"  tabindex="1"  value="#{pwdBean.password0}" redisplay="true" />
                        <h:inputHidden id="encryptPassword0" value="#{pwdBean.encryptPassword0}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                        <font color="red">*</font>
                        <h:outputLabel for="password" value="新的密码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:password style="width: 200px" id="password" maxlength="16" tabindex="1" value="#{pwdBean.password}"
                                    redisplay="true" onkeyup="removeSpace(this);" onchange="removeSpace(this);"/>
                        <h:inputHidden id="encryptPassword1" value="#{pwdBean.encryptPassword1}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column style="text-align:right;padding-right:3px;width:30%;height: 28px;">
                        <font color="red">*</font>
                        <h:outputLabel for="password2" value="确认密码："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:password style="width: 200px" id="password2" maxlength="16" tabindex="1" value="#{pwdBean.password2}"
                                    redisplay="true" onkeyup="removeSpace(this);" onchange="removeSpace(this);"/>
                        <h:inputHidden id="encryptPassword2" value="#{pwdBean.encryptPassword2}"/>
                    </p:column>
                </p:row>


                <p:row>
                    <p:column style="text-align:center;" colspan="2">
                        <p:commandButton value="保存" icon="ui-icon-check" id="saveBtn" onclick="changePsd()" type="button"/>
                        <p:remoteCommand name="changePsdAction" id="saveHidBtn"
                                         process="@this,encryptPassword0,encryptPassword1,encryptPassword2"
                                         action="#{pwdBean.changePwdAction}"/>
                        &#160;&#160;
                        <p:commandButton value="关闭" icon="ui-icon-close"  style="cursor: hand;" onclick="PF('PwdDialog').hide();"
                                         immediate="true"  resetValues="true"/>&#160;&#160;
                    </p:column>
                </p:row>
            </p:panelGrid>
        </h:form>
    </p:dialog>
	</body>
	</f:view>
</html>