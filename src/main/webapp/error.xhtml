<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui">
<h:head>
	<title>错误信息</title>
	<link rel="stylesheet" type="text/css"
		href="#{request.contextPath}/resources/css/default.css" />
	<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
	<style type="text/css">
	.bodyClass {
		background: url(/resources/images/bg.jpg);
		overflow: hidden;
	}
	</style>
	<script type="text/javascript">
	//<![CDATA[
		jQuery(document).ready(function() {
			widthDivSet();
		});
		jQuery(window).resize(function() {
			widthDivSet();
	    });
	    function widthDivSet(){
	         var  elemWid = jQuery(window).width();
		     jQuery('.bodydiv').width(elemWid);
		     var  elemHei = jQuery(window).height();
		     jQuery('.bodydiv').height(elemHei);
	    }
	//]]>	
	</script>
</h:head>

<h:body leftmargin="0" topmargin="0"
	style="margin-right: 0px;">
	<div class="bodydiv" style="overflow:hidden;">
	<table width="100%" style="height: 100%" class="bodyClass">
		<tr>
			<td>
							<table id="Table_01" width="1121" style="vertical-align: top;overflow: hidden;"
								border="0" cellpadding="0" cellspacing="0" align="center">
								<tr>
									<td colspan="3"
										style="background:url(/resources/images/error_01.gif);height: 78px;"></td>
									<td
										style="background:url(/resources/images/spacer.gif);height: 78px;width: 1px;">
									</td>
								</tr>
								<tr>
									<td colspan="3"
										style="background:url(/resources/images/error_02.gif);height: 65px;"></td>
									<td
										style="background:url(/resources/images/spacer.gif);height: 65px;width: 1px;">
									</td>
								</tr>
								<tr>
									<td colspan="3"
										style="background:url(/resources/images/error_03.gif);height: 71px;">
									</td>
									<td
										style="background:url(/resources/images/spacer.gif);height: 71px;width: 1px;">
									</td>
								</tr>
								<tr>
									<td rowspan="2"
										style="background:url(/resources/images/error_04.gif);width:464px;height:436px">
									</td>
									<td width="472" 
										style="background:url(/resources/images/error_05.gif);height: 404px;">
										<div
											style="height: 320px;width:460px;margin-top:58px;margin-left:6px; overflow-x:hidden;overflow-y:auto;
				word-wrap: break-word; word-break: break-all;border:1px solid #ffffff;SCROLLBAR-FACE-COLOR: #97cdfa;SCROLLBAR-HIGHLIGHT-COLOR: #97cdfa;SCROLLBAR-SHADOW-COLOR: #609ddc; SCROLLBAR-3DLIGHT-COLOR: #e8f6ff; SCROLLBAR-DARKSHADOW-COLOR: #e8f6ff;SCROLLBAR-ARROW-COLOR: #fff; SCROLLBAR-TRACK-COLOR: #e8f6ff;z-index:900px">
											<font color="#ffffff">
                                                <h:outputText value="#{facesContext.externalContext.requestMap['msg']}" />
                                                <h:outputText value="#{facesContext.externalContext.requestParameterMap['msg']}" />
											</font>
										</div>
									 </td>
									<td rowspan="3"
										style="background:url(/resources/images/error_06.gif);width:184px;height:570px">
									</td>
									<td style="background:url(/resources/images/spacer.gif)"
										width="1px" height="404px"></td>
								</tr>
								<tr>
									<td rowspan="2"
										style="vertical-align: top;background:url(/resources/images/error_07.gif);width:472px;height:166px">
									</td>
									<td
										style="background:url(/resources/images/spacer.gif); width:1px;height:32px">
									</td>
								</tr>
								<tr>
									<td style="background:url(/resources/images/error_08.gif)"
										width="464px" height="134px"></td>
									<td style="background:url(/resources/images/spacer.gif)"
										width="1px" height="134px"></td>
								</tr>
							</table>
					</td>
		</tr>
	</table>
	</div>
</h:body>
</html>