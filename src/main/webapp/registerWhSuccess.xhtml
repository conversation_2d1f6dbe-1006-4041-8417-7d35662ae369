<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
<h:head >
	<link rel="stylesheet" type="text/css" href="#{request.contextPath}/resources/css/default.css" /> 

	<title><h:outputText value="#{applicationBean.appTitle}"/></title>
	<script type="text/javascript">
	//<![CDATA[	        
function setVerImg(){
	var url=Math.random();
	document.getElementById("abc").src="/authImage?id="+url;
}
 jQuery(document).ready(function() {
 //alert(navigator.userAgent.toLowerCase());
	var isFirefox = navigator.userAgent.toLowerCase().match(/firefox/) != null; //是否Chrome浏览器
	var isChrome = navigator.userAgent.toLowerCase().match(/chrome/) != null; //是否Chrome浏览器
	var isWebkit = navigator.userAgent.toLowerCase().match(/applewebkit/) != null; //是否极速模式浏览器
	var isSafari = jQuery.browser.safari; //是否Safari浏览器
	if(!isSafari&&!isWebkit&&!isChrome&&!isFirefox){
	   //跳转到下载页面
	 forwardBtn();
	}
});

//]]>	
	</script>
<style type="text/css">

.panel2 {
	background: #FFFFFF;
	box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
	height: 93px;
	width: 100%;
	border-style: hidden;

}
.panel3 {
	box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
	width: 1240px;
	height: 600px;
	background: #ffffff;
	border: 1px solid #dcdee3;
	position: absolute;
	z-index: 1;
	top: 130px;
	left: -webkit-calc(50% - 620px);
}


.panel3 tr{
	border-color: #adabab59;
}
</style>
</h:head>

<h:body leftmargin="0" topmargin="0"
	style="margin-right: 0px;margin-top:0px; overflow: visible;">
	<h:form id="personForm">

		<p:panelGrid styleClass="panel2">
			<p:row>
				<p:column colspan="3"
						  style="width: 1280px; height: 78px; background: #2a6fcd; padding-left: 30px;box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.02); ">
					<p:graphicImage style="vertical-align: middle; height: 63%;"
									value="/resources/images/login/registerWhHead.png"/>
				</p:column>
			</p:row>
		</p:panelGrid>
		<p:panelGrid styleClass="panel3" id="registPage">
			<p:row>
				<p:column colspan="2"
						  style="text-align: center;font-weight: 700;padding-left: 218px; height: 60px;border-bottom-color: transparent;border-right-color: transparent; background: linear-gradient(180deg,#fcfcfd, #f7f8fa); box-shadow: 0px -1px 0px 0px #dcdee3 inset;">
					<font color="#333333"
						  style="font-size:23px;text-align: center;line-height: 22px;height: 20px;letter-spacing: 0px;">注册账号</font>
				</p:column>
				<p:column
						  style="text-align: right;padding-right: 28px;  font-weight: 700; width: 170px;  height: 60px;border-bottom-color: transparent;border-left-color: transparent; background: linear-gradient(180deg,#fcfcfd, #f7f8fa); box-shadow: 0px -1px 0px 0px #dcdee3 inset;">
						<p:outputLabel style="font-size: 14px;font-weight: 400;text-align: right;color: #000000;line-height: 14px;" value="已有账号，" ></p:outputLabel>
						<p:commandLink style="font-size: 14px;font-weight: 400;text-align: right;line-height: 14px;"  value="立即登录"  action="#{registerWhBean.backToLogin}" ></p:commandLink>
				</p:column>
			</p:row>
			<p:row>
				<p:column colspan="3" style="height: 200px;border-top-color: transparent; padding-top: 80px;text-align:center;vertical-align:bottom;background:white;border-bottom-color:transparent;">
					<p:graphicImage style=" width: 300px;"
									value="/resources/images/login/registerWhSuccess.png" />
				</p:column>
			</p:row>
			<p:row>
				<p:column colspan="3" style="text-align:center;background:white;height: 80px;border-top-color:transparent;border-bottom-color:transparent;">
					<p:outputLabel style="width: 286px;height: 22px;font-size: 21px !important;;font-weight: 600;color: #333333;line-height:  22px " value="账号申请已提交，请等待审核" ></p:outputLabel>
					<br/>
					<p:outputLabel style="width: 286px;height: 22px;font-size: 17px !important;;font-weight: 500;color: #333333;line-height: 40px " value="可以在登录界面查询账号审核进度" ></p:outputLabel>
				</p:column>

			</p:row>
			<p:row>
				<p:column colspan="3"
						  style="text-align:center;padding-top:20px;border-top-color:transparent;border-bottom-color:transparent;background:white;">
					<p:commandButton id="loginBut"
									 style="width: 300px; height: 40px;color:white;font-size: 16px !important;background: #2a6fcd;border-radius: 3px;"
									 action="#{registerWhBean.backToLogin}" value="返回登录 " />
				</p:column>
			</p:row>
			<p:row>
			<p:column colspan="3"
					  style="height: 100px;">
			</p:column>
			</p:row>
		</p:panelGrid>
		<p:focus id="focus" />
	</h:form>
	<p:growl id="errorMsg" autoUpdate="true" />
	<ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
	<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
	<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
	<ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
</h:body>
</html>