<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui">
<h:head>
    <link rel="stylesheet" type="text/css"
          href="#{request.contextPath}/resources/css/default.css"/>

    <title><h:outputText value="#{applicationBean.appTitle}"/></title>
    <h:outputScript library="js" name="namespace.js"/>
    <h:outputStylesheet name="css/default.css"/>
    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
    <script type="text/javascript"
            src="/resources/js/validate/system/validate.js"></script>
    <script type="text/javascript" src="#{request.contextPath}/resources/js/md5.js" />
    <script type="text/javascript" src="#{request.contextPath}/resources/js/validate/system/vliadPassword.js"></script>
    <script type="text/javascript">
        //<![CDATA[
        function changePsd() {
            var password1 = $("#pwdSetForm\\:password").val();
            var password2 = $("#pwdSetForm\\:password2").val();
            var encryptPassword1 = $("#pwdSetForm\\:encryptPassword1");
            var encryptPassword2 = $("#pwdSetForm\\:encryptPassword2");
            encryptPassword1.val("");
            encryptPassword2.val("");
            if (password1) {
                encryptPassword1.val(hex_md5(password1));
            }
            if (password2) {
                encryptPassword2.val(hex_md5(password2));
            }
            var flag1 =!baseIsVliadPassword(password1);
            var flag2 = !baseIsVliadPassword(password2);
            if(flag1 && flag2){
                changePwdAction([{name: 'check', value: "3"}]);
            }else if(flag1){
                changePwdAction([{name: 'check', value: "1"}]);
            }else if(flag2){
                changePwdAction([{name: 'check', value: "2"}]);
            }else if (password1 != password2) {
                changePwdAction([{name: 'check', value: "0"}]);
            }else {
                changePwdAction([{name: 'check', value: "4"}]);
            }
        }

        function hideShowPsw(obj) {
            var pwdImg = obj == 0 ? $("#showImg") : $("#showImg2");
            var pwdInput = obj == 0 ? $("#pwdSetForm\\:password") : $("#pwdSetForm\\:password2");

            if (pwdInput.attr("type") == "password") {
                pwdInput.attr("type", "text");
                pwdImg.attr("src","/resources/images/login/xsmm.png");
            } else {
                pwdInput.attr("type", "password");
                pwdImg.attr("src","/resources/images/login/ycmm.png");
            }
        }

        function showPwdSetSuccess(){
            $("#pwdSetForm\\:resetPassWordPanel").hide();
            $("#pwdSetSuccessInfo").show();
        }

        function jump2LoginPage(){
            window.location.href = "/login.faces";
        }

        jQuery(document).ready(
            function () {
                $("#pwdSetForm\\:password").focus();
            });
        //去除空格
        function removeSpace(pwdEle) {
            $(pwdEle).val($(pwdEle).val().replaceAll(/\s/g, ''));
        }
        //]]>
    </script>
    <style type="text/css">
        /*.panel {*/
        /*    width: 100%;*/
        /*}*/

        .panel2 {
            background-color: #fcfdfd;;
            box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
            height: 93px;
            width: 100%;
            border-style: hidden;

        }
        .panel3 {
            box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.10);
            width: 1240px;
            /*height: 974px;*/
            background-color: #fcfdfd;
            border: 1px solid #adabab59;
            position: absolute;
            z-index: 1;
            top: 130px;
            left: -webkit-calc(50% - 620px);
        }
        .panel3 tr{
            border-style: hidden;
        }
        .icSvgPwd {
            height: 20px;
            margin-left: -38px;
            cursor: pointer;
            position: absolute;
            margin-top: 7px;
        }

    </style>
</h:head>

<h:body leftmargin="0" topmargin="0"
        style="margin-right: 0px;margin-top:0px; overflow: visible;background-color: #F0F0F0; ">
    <h:form id="pwdSetForm">
        <p:panelGrid styleClass="panel2">
            <p:row>
                <p:column colspan="3"
                          style="width: 1280px; height: 78px;padding-left: 30px; background-color: #2a6fcd; box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.02); ">
                    <p:graphicImage style="vertical-align: middle; height: 63%;"
                                    value="/resources/images/login/registerWhHead.png"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid styleClass="panel3" id="resetPassWordPanel">
            <p:row >
                <p:column style="text-align: center;  font-weight: 700; width: 1238px;  height: 60px;border-bottom-color: transparent;background: linear-gradient(180deg,#fcfcfd, #f7f8fa); box-shadow: 0px -1px 0px 0px #dcdee3 inset;">
                    <font color="#333333"
                          style="font-size:23px;text-align: center;line-height: 22px;height: 20px;letter-spacing: 0px;">设置新密码</font>
                </p:column>
            </p:row>
            <p:row >
                <p:column style="text-align: center;   width: 1238px;  height: 315px;border-bottom-color: transparent;padding-bottom:200px">
                    <p:panelGrid style="margin-top: 3px;background-color: #fcfdfd;margin-left: 20%;">
                        <!--登录账号-->
                        <p:row>
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;">
                                <p:outputLabel value="您的登录账号："
                                               redisplay="true"/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                <div style="width: 310px;height:36px;background-color: #AFAFAF;"><font color="#CE40B2" style="font-weight: bold; font-size: 30px;" >#{retrievePasswordBean.userNo}</font></div>
                                <!--<p:inputText readonly="true" value="#{retrievePasswordBean.userNo}" style="width: 300px;height:28px; font-weight: bold; border-style: hidden; pointer-events: none; background-color: #AFAFAF; color: #CE40B2;" />-->
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                            <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                        </p:row>
                        <!-- 提示 -->
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;border-style:hidden;background-color:#fcfdfd;" />
                            <p:column style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;" colspan="3">
                                <div style="color: red;font-size: 12px;line-height: 12px;">提示：系统登录的用户名，请牢记！</div>
                            </p:column>
                        </p:row>
                        <!--设置密码-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                                <font color="red">*</font>
                                <h:outputLabel for="password" value="设置密码："/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;">
                                <p:password style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;" id="password"
                                            maxlength="16"  tabindex="1"  value="#{retrievePasswordBean.password}"
                                            placeholder="请输入" onchange="removeSpace(this)" onkeyup="removeSpace(this);"
                                            redisplay="true" />
                                <h:inputHidden id="encryptPassword1" value="#{retrievePasswordBean.encryptPassword1}"/>
                                <img id="showImg" alt="" src="/resources/images/login/ycmm.png" class="icSvgPwd" onclick="hideShowPsw(0)" />
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;  padding-top: 18px;">
                                <p:outputPanel  id="passwordVerify">
                                    <div style="display:#{null ne retrievePasswordBean.validatePwd and 0 == retrievePasswordBean.validatePwd ?'flex':'none'};width: 128px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="密码不能为空！" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                    <div style="display:#{null ne retrievePasswordBean.validatePwd and 1 == retrievePasswordBean.validatePwd ?'flex':'none'};width: 140px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="密码不符合规则！" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                        <!--规则-->
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;border-style:hidden;background-color:#fcfdfd;" />
                            <p:column style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;" colspan="3">
                                <div style="color: #7b7b7b;font-size: 12px;line-height: 14px;">规则：8~16位字符，必须包含大小写字母、数字和特殊字
                                    <br/>
                                    符；不能包含连续3位顺序或逆序或重复的数字、字母、特 <br/>殊字符</div>
                            </p:column>
                        </p:row>
                        <!--确认密码-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;">
                                <font color="red">*</font>
                                <h:outputLabel for="password2" value="确认密码："/>
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" id="chk-td">
                                <p:password style="width: 300px;height:23px;border: 1px solid #c4c6cf; border-radius: 2px;" id="password2" maxlength="16"  tabindex="1"  value="#{retrievePasswordBean.password2}"
                                            placeholder="请输入" onchange="removeSpace(this)" onkeyup="removeSpace(this);"
                                            redisplay="true" />
                                <h:inputHidden id="encryptPassword2" value="#{retrievePasswordBean.encryptPassword2}"/>
                                <img id="showImg2" alt="" src="/resources/images/login/ycmm.png" class="icSvgPwd" onclick="hideShowPsw(1)" />
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent; padding-top: 18px;">
                                <p:outputPanel  id="password2Verify">
                                    <div style="display:#{null ne retrievePasswordBean.validatePwd2 and 0 == retrievePasswordBean.validatePwd2 ?'flex':'none'};width: 128px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="密码不能为空！" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                    <div style="display:#{null ne retrievePasswordBean.validatePwd2 and 1 == retrievePasswordBean.validatePwd2 ?'flex':'none'};width: 140px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="密码不符合规则！" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                    <div style="display:#{null ne retrievePasswordBean.validatePwd2 and 2 == retrievePasswordBean.validatePwd2 ?'flex':'none'};width: 178px; height: 28px; background-color: #fff6f2; border: 1px solid #f9ddd2; border-radius: 2px;vertical-align: center;">
                                        <p:graphicImage style="vertical-align: middle;height: 63%;  margin-top: 5px; margin-left: 10px;  margin-right: 5px;"
                                                        value="/resources/images/login/mustIcon.png"/>
                                        <p:outputLabel value="确认密码与密码不一致！" style="color: red;margin-top: 3px;"></p:outputLabel>
                                    </div>
                                </p:outputPanel>
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                        <!--确定-->
                        <p:row >
                            <p:column
                                    style="text-align:right;padding-right:3px;border-style:hidden;background-color:#fcfdfd; padding-top: 18px;">
                            </p:column>
                            <p:column
                                    style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" >
                                <p:commandButton value="确定" style="width:312px;height:40px;color:white;font-size: 16px !important;background: #2a6fcd;border-radius: 3px;"
                                                 id="saveBtn" onclick="changePsd()" type="button"/>
                                <p:remoteCommand name="changePwdAction" id="saveHidBtn"
                                                 process="@this,encryptPassword1,encryptPassword2" update=":pwdSetForm:passwordVerify,:pwdSetForm:password2Verify"
                                                 action="#{retrievePasswordBean.changePwdAction}"/>
                            </p:column>
                            <p:column style="background-color:#fcfdfd;border-color: transparent;    padding-top: 18px;">
                            </p:column>
                            <p:column style="border-color: transparent;  background-color:#fcfdfd;  padding-top: 18px;">
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </p:column>
            </p:row>
        </p:panelGrid>
    </h:form>
    <h:form  id="successForm">
        <div id="pwdSetSuccessInfo" style="width:100%;background-color:white;position:absolute;z-index:1008;display: none ">
            <p:panelGrid styleClass="panel3" style="top: 36px;" >
                <p:row >
                    <p:column style="text-align: center;  font-weight: 700; width: 1238px;  height: 60px;border-bottom-color: transparent;background: linear-gradient(180deg,#fcfcfd, #f7f8fa); box-shadow: 0px -1px 0px 0px #dcdee3 inset;">
                        <font color="#333333"
                              style="font-size:23px;text-align: center;line-height: 22px;height: 20px;letter-spacing: 0px;">找回账号密码</font>
                    </p:column>
                </p:row>
                <p:row >
                    <p:column style="text-align: center;height: 378px;border-bottom-color: transparent;padding-bottom: 30px;">
                        <p:panelGrid style="margin-top: 3px;background-color: #fcfdfd;margin-left: 20%;">
                            <!--图片-->
                            <p:row>
                                <p:column
                                        style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;" />
                                <p:column
                                        style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" >
                                    <p:graphicImage style=" width: 240px;"
                                                    value="/resources/images/login/registerWhSuccess.png" />
                                </p:column>
                                <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                                <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                            </p:row>
                            <!--登录账号-->
                            <p:row>
                                <p:column
                                        style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;" />
                                <p:column
                                        style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" >
                                    <img alt="" src="/resources/images/icon3.png" style="vertical-align: middle;"/>
                                    <span style="font-weight: bold;font-size: 18px;" >账号密码设置成功，请登录</span>
                                </p:column>
                                <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                                <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                            </p:row>
                            <p:row>
                                <p:column
                                        style="text-align:right;padding-right:3px;width:220px;border-style:hidden;background-color:#fcfdfd;padding-top: 18px;" />
                                <p:column
                                        style="background-color:#fcfdfd;text-align:left;padding-left:12px;border-style:hidden;padding-top: 18px;" >
                                    <p:commandButton value="立即登录" style="width:242px;height:40px;color:white;font-size: 16px !important;background: #2a6fcd;border-radius: 3px;"
                                                     onclick="jump2LoginPage()" type="button"/>
                                </p:column>
                                <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                                <p:column style="background-color:#fcfdfd;border-color: transparent;padding-top: 18px;" />
                            </p:row>
                        </p:panelGrid>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </div>
    </h:form>
</h:body>
</html>