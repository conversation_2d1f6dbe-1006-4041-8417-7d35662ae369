<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"  xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<h:head >
	<link rel="stylesheet" type="text/css" href="#{request.contextPath}/resources/css/default.css" /> 

	<title><h:outputText value="#{applicationBean.appTitle}"/></title>
	<script type="text/javascript">
	//<![CDATA[	        
function setVerImg(){
	var url=Math.random();
	document.getElementById("abc").src="/authImage?id="+url;
}
 jQuery(document).ready(function() {
 //alert(navigator.userAgent.toLowerCase());
	var isFirefox = navigator.userAgent.toLowerCase().match(/firefox/) != null; //是否Chrome浏览器
	var isChrome = navigator.userAgent.toLowerCase().match(/chrome/) != null; //是否Chrome浏览器
	var isWebkit = navigator.userAgent.toLowerCase().match(/applewebkit/) != null; //是否极速模式浏览器
	var isSafari = jQuery.browser.safari; //是否Safari浏览器
	if(!isSafari&&!isWebkit&&!isChrome&&!isFirefox){
	   //跳转到下载页面
	 forwardBtn();
	}
});

//]]>	
	</script>
<style type="text/css">
.loginBtn{
background-image: url("/resources/images/login/login_a.png");
}

</style>
</h:head>

<h:body leftmargin="0" topmargin="0" style="margin-right: 0px;margin-top:0px; overflow: visible;" onload="document.getElementById('form:userno').focus();setVerImg()">
<h:form id="forwardFRM">
<p:remoteCommand  name="forwardBtn" action="#{loginBean.browserGo}"/>
</h:form>
	<h:form id="form">
		<table width="100%" style="height: 760px;" border="0" background="/resources/images/login/login_bg.jpg">
			<tr>
				<td width="100%">
					<table cellpadding="0" background="/resources/images/login/ERP/login_ag.jpg" cellspacing="0" border="0" style="width: 950px; height: 760px;" align="center">
						<tr style="height: 374px;">
							<td width="950" colspan="3"></td>
						</tr>
						<tr>
							<td width="580" rowspan="2"></td>
							<td height="30" width="230" style="vertical-align: top;">
								<p:inputText style="width: 200px" value="#{loginBean.userno}" tabindex="1" styleClass="text" id="userno" />
                            </td>
							<td rowspan="2" height="30" style="vertical-align: top;" width="238px;">
								<div id="loginDiv">
									<p:commandButton id="loginBut"  update="userno,password,verifyCode,focus"
										style="width:67px;height:57px;" styleClass="loginBtn" icon=""
										action="#{loginBean.loginAction}" />
								</div>
							</td>
						</tr>
						<tr>
							<td height="30" style="vertical-align: top;">
								<p:password style="width: 200px" id="password" tabindex="2" styleClass="text" value="#{loginBean.password}" redisplay="true" />
                            </td>
						</tr>
						<tr>
							<td width="522" rowspan="2" style="height: 320px;"></td>
							<td style="text-align: left; vertical-align: top; height: 10px; " colspan="2">
								<table cellpadding="0" cellspacing="0" width="100%" border="0" style="height: 100%;margin-left: -2px;">
									<tr>
										<td width="90px;" style="padding-left: 3px;"  valign="top">
											<p:inputText styleClass="text" id="verifyCode" tabindex="3" size="10" value="#{loginBean.verifyCode}" />
										</td>
										<td valign="top" style="padding-left: 3px;" >
											<img alt="" id="abc" style="vertical-align: middle;" src="" /> <a href="javascript:setVerImg()">换一张</a>
										</td>
									</tr>
								</table>
							</td>
						</tr>

						<tr>
							<td style="vertical-align: top;" colspan="2">
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
        <p:focus id="focus"/>
	</h:form>
    <p:growl id ="errorMsg" autoUpdate="true" />

</h:body>
</html>