<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
    xmlns:p="http://primefaces.org/ui"
    xmlns:c="http://java.sun.com/jsp/jstl/core">
	<f:view>
	<h:head>
		<title></title>
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/minScrollbar.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/fastDesktop.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/jquery-ui-1.8.4.custom.css" />
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
        <script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.widget.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.mouse.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/minScrollbar.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/SingleLogin.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery.dialog.js"></script>
		<script  type="text/javascript">
		//<![CDATA[
		var maxDesktopNum =6; //最大桌面数
        var maxMenuNum =24; //每个桌面最大菜单数

        var addBtnName = "添加"; //添加
        var delBtnNaem = "从桌面删除"; //从桌面删除
        var mdfBtn="修改";
        var whBtn="信息维护";
        var cdmtul = "当前桌面菜单达到上限，是否按序添加到其他桌面？"; //当前桌面菜单达到上限，是否按序添加到其他桌面？
        var dmaf = "桌面菜单已满,无法继续添加。"; //桌面菜单已满,无法继续添加。
        //弹出自定义确认窗口(放在此处是为了兼容多语言)
        function ConfirmDialog(menuId, isAddDesktop) {
            openConfirmDialog({ contents: cdmtul, icon: "help",
                bottons: {
                    "确定": function() {
                        isConfirm = false;
                        AddMenu(menuId, isAddDesktop);
                    }, "关闭": null
                }
            });
        }
 		function retunVJS(){
 	     window.returnValue =document.getElementById("addTagIdxs").value+ "#"+document.getElementById("delTagIdxs").value+"#"+
 	     document.getElementById("addTagIdxs_souc").value+ "#"+document.getElementById("delTagIdxs_souc").value
 	     + "#"+'#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.rid}';
	       zwxJQ("#btnClose").click();
	       // _updState();
	    }
 		
 		function openDialog(id,type){
 			document.getElementById('mainForm:dialogid').value=id;
 			document.getElementById('mainForm:opentype').value=type;
 			dialogprocess();
 		}
 		
 		function ReInitMenuList(){
 			zwxJQ(document).ready(function() {
 			    InitMenuList();
 			});
 		}
		//]]>
		</script>
		<style type="text/css">
			.addIcon2 {
					height: 500px;
					width: 756px;
					}
		</style>
	</h:head>
	    <body style="overflow: hidden" onunload="retunVJS();">
		    <h:form id="mainForm">
		    	<p:remoteCommand process="@this,dialogid,opentype" name="dialogprocess" action="#{tdSingleSoBean.onClick}" oncomplete="PF('SystemDialog').show();" update="mainForm:systemDg"/>
		    	<h:inputHidden id="dialogid" value="#{tdSingleSoBean.dialogid}"/>
		    	<h:inputHidden id="opentype" value="#{tdSingleSoBean.opentype}"/>
			    <input type="hidden" id="addTagIdxs"/>
			    <input type="hidden"  id="delTagIdxs"/>
			    <input type="hidden"  id="addTagIdxs_souc" />
			    <input type="hidden"  id="delTagIdxs_souc" />
		    <input type="button" id="btnClose"  style="display: none" />
		    <div class="addIcon2">
		        <div class="iconMain">
		            <div id="iconMain" class="listMain ui-minScrollbar">
		            </div>
		        </div> 
		    </div>
		<p:dialog id="systemDg" header="系统维护" widgetVar="SystemDialog" resizable="false" width="450" height="300">
		<p:panelGrid style="width:100%;" id="systemGrid">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="系统名称：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 160px;">
           			<p:outputLabel id="systemMenu" value="#{tdSingleSoBean.tsDsfSys.sysName}" style="width:160px;" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:250px;">
                    <h:outputLabel value="系统类型：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 250px;">
           			<p:outputLabel id="xtType" value="#{tdSingleSoBean.tsDsfSys.xtType == '1'?'BS':'CS'}" style="width:160px;" />
				</p:column>
			</p:row>
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:250px;">
                    <h:outputLabel value="程序地址：" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 300px;">
           			<p:inputText id="app_path" value="#{tdSingleSoBean.sysUrl}" style="width:220px;" readonly="true"/>
					<p:commandLink  value="选择程序" rendered="#{tdSingleSoBean.tsDsfSys.xtType == '2'}" onclick="action_xzcx();" >
					</p:commandLink>
				</p:column>
			</p:row>
			<c:forEach items="#{tdSingleSoBean.syslist}" var="sysitem">
			<p:row>
				<p:column style="text-align:right;padding-right:3px;width:30%;">
                    <h:outputLabel value="#{sysitem.paramCn}" />
				</p:column>
				<p:column style="text-align:left;padding-left:3px;width: 160px;">
					<p:inputText value="#{sysitem.enValue}" style="width:160px" maxlength="40"/>
				</p:column>
			</p:row>
			</c:forEach>
		</p:panelGrid>
		<f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" id="saveBtn" action="#{tdSingleSoBean.saveAction}" oncomplete="ReInitMenuList()" process="@this,systemGrid">
                	</p:commandButton>
                	<p:spacer width="5" />
                	<p:commandButton value="删除" id="delBtn" action="#{tdSingleSoBean.delAction}" oncomplete="ReInitMenuList()" process="@this" rendered="#{tdSingleSoBean.opentype == '1'}">
                		<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                	</p:commandButton>
                	<p:spacer width="5" />
                	<p:commandButton value="取消" id="backBtn" type="button" onclick="PF('SystemDialog').hide();" process="@this"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
        
	</p:dialog>	
	<p:confirmDialog global="true" showEffect="fade" >
        <p:commandButton value="是" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
        <p:commandButton value="否" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
    </p:confirmDialog>
    <ui:include src="/WEB-INF/templates/system/cslogin.xhtml">
        </ui:include>
    <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
    </h:form>
	</body>
	</f:view>
</ui:composition>