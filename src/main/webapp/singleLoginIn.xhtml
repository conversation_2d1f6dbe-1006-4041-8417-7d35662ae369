<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
    xmlns:p="http://primefaces.org/ui"
    xmlns:c="http://java.sun.com/jsp/jstl/core">
	<h:head>
		<title></title>
	<link rel="stylesheet" type="text/css" href="#{request.contextPath}/resources/css/default.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/fastDesktop.css" />
        <link rel="stylesheet" href="/resources/component/quickDesktop/css/help.css" />
        <ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
        <script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.widget.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.core.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.mouse.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.sortable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.draggable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.droppable.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/desk.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery.dialog.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/SingleLoginDesk.js"></script>
        <script type="text/javascript" src="/resources/component/quickDesktop/help.js"></script>
		<script type="text/javascript">
		//<![CDATA[
         function showMess(data){
		      if(data=="0"){
		       window.location.href = "singleLoginIn.faces";
		      }
		 }
         
         function openWindow(newUrl) {
        	 sty="scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes";  
        	 window.open(newUrl,"_blank",sty);
         }
         
         function zwx_sso_cslogin(url,user,pwd){
        	 embed1.UserName=user;
        	 embed1.UserPwd=pwd;
        	 embed1.ExeFileName=url;
        	 action_login();
         }
		//]]>
		</script>
		<style type="text/css">
			.deskbodynew{
				background:url(/resources/images/dsfLogin/dsf_login_repartbackground.png) repeat-x #d2ecf8;
			}
			.desktopMainnew{
				background:url(/resources/images/dsfLogin/dsf_login_background.png) center no-repeat;
			}
		</style>
	</h:head>
	<f:view>
	<body class="deskbodynew">
	<h:form id="mainForm">
	<div class="desktopMainnew"> 
				<div class="desktopNavL">
				</div>
				<ul class="desktopNav" style="margin-top: 0px;">
				</ul>
				<div class="desktopNavR" style="text-align:center;">
					<a href="javascript:;"></a>
				</div>
				<div class="mainList">
					<div class="ulList">
					</div>
				</div>
			</div>
		<ui:include src="/WEB-INF/templates/system/cslogin.xhtml"/>
    </h:form>
	</body>
	</f:view>
</ui:composition>