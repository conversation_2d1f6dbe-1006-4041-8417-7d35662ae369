<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:f="http://java.sun.com/jsf/core" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">
	<f:view>
	<h:head>
		<title></title>
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/minScrollbar.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/fastDesktop.css" />
		<link rel="stylesheet" href="/resources/component/quickDesktop/css/jquery-ui-1.8.4.custom.css" />
		<ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
        <script type="text/javascript">
            var zwxJQ = $.noConflict(true);
        </script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.widget.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery/jquery.ui.mouse.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/minScrollbar.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/ShortcutDesktopMenuAdd.js"></script>
		<script type="text/javascript" src="/resources/component/quickDesktop/jquery.dialog.js"></script>
		<script  type="text/javascript">
		//<![CDATA[
		           var maxDesktopNum =6; //最大桌面数
        var maxMenuNum =24; //每个桌面最大菜单数

        var addBtnName = "添加"; //添加
        var delBtnNaem = "从桌面删除"; //从桌面删除
        var cdmtul = "当前桌面菜单达到上限，是否按序添加到其他桌面？"; //当前桌面菜单达到上限，是否按序添加到其他桌面？
        var dmaf = "桌面菜单已满,无法继续添加。"; //桌面菜单已满,无法继续添加。
        //弹出自定义确认窗口(放在此处是为了兼容多语言)
        function ConfirmDialog(menuId, isAddDesktop) {
            openConfirmDialog({ contents: cdmtul, icon: "help",
                bottons: {
                    "确定": function() {
                        isConfirm = false;
                        AddMenu(menuId, isAddDesktop);
                    }, "关闭": null
                }
            });
        }
 	function retunVJS(){
 	     window.returnValue =document.getElementById("addTagIdxs").value+ "#"+document.getElementById("delTagIdxs").value+"#"+
 	     document.getElementById("addTagIdxs_souc").value+ "#"+document.getElementById("delTagIdxs_souc").value
 	     + "#"+'#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.rid}';
	       zwxJQ("#btnClose").click();
	       // _updState();
	    }
		//]]>
		</script>
	</h:head>
	    <body style="overflow: hidden" onunload="retunVJS();">
		    <h:form>
			    <input type="hidden" id="addTagIdxs"/>
			    <input type="hidden"  id="delTagIdxs"/>
			    <input type="hidden"  id="addTagIdxs_souc" />
			    <input type="hidden"  id="delTagIdxs_souc" />
		    <input type="button" id="btnClose"  style="display: none" />
		    <div class="addIcon">
		        <div class="iconNav">
		            <ul id="iconNav" class="ui-minScrollbar">
		            </ul>
		        </div>
		        <div class="iconMain">
		            <div id="iconMain" class="listMain ui-minScrollbar">
		            </div>
		        </div>
		    </div>
		    </h:form>
		</body>
	</f:view>
</html>