<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
xmlns:ui="http://java.sun.com/jsf/facelets" 
xmlns:f="http://java.sun.com/jsf/core" 
xmlns:h="http://java.sun.com/jsf/html" 
xmlns:c="http://java.sun.com/jsp/jstl/core"
xmlns:p="http://primefaces.org/ui">
<h:head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><h:outputText value="#{applicationBean.appTitle}"/></title>
	<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/default/skin.css" />
	<link rel="stylesheet" href="/resources/component/quickDesktop/css/minScrollbar.css" />
	<link rel="stylesheet" href="/resources/component/quickDesktop/css/reset.css" />
	<link rel="stylesheet" href="/resources/component/quickDesktop/css/skin/Style.css" />
    <link rel="stylesheet" href="/resources/component/quickDesktop/ERP/imgSkin.css" />
    <ui:include src="/WEB-INF/templates/system/jquery.xhtml" />
    <script type="text/javascript">
        var zwxJQ = $.noConflict(true);
    </script>
	<script type="text/javascript" src="/resources/js/namespace.js"></script>
			<script type="text/javascript" src="/resources/js/validate/system/validate.js"></script>
			<script type="text/javascript" src="/resources/component/quickDesktop/Common.js"></script>
			<script type="text/javascript" src="/resources/component/quickDesktop/NewPopMenu.js"></script>
			<script type="text/javascript" src="/resources/component/quickDesktop/CreatePopup.js"></script>
			<script type="text/javascript" src="/resources/component/quickDesktop/index.js"></script>
			<script type="text/javascript" src="/resources/component/quickDesktop/zwx.system.js"></script>
		
		<script type="text/javascript">
		//<![CDATA[
		          // document.body.scroll="no";
					var win_MainMenu; //主菜单窗口
			        var win_SubMenu;   //子菜单窗口
			        var win_MessagePop;  //寻呼提醒窗口
			        var win_ColMenu;   //门户弹出窗口
			        var win_NoReadCall; //未阅寻呼窗口
			        var win_HelpPop;   //帮助弹出窗口
			        var win_ContextMenu; //页签右键菜单
			        var win_SkinMenu; //皮肤切换菜单
			        var win_MyStatus; //状态切换菜单
			        var win_MyApplication; //个人应用菜单
			        var RedirectUrl = "";
			        var page_TabClose = "关闭";   //关闭
			        var page_TabCloseAll ="全部关闭"; //全部关闭
			        var page_titleReload = "刷新"; //刷新
			        var page_TabCloseOther = "关闭其他"; //关系其他
					var userid ;
			zwxJQ(document).ready(function() {
                window.moveTo(0, 0);
                window.resizeTo(screen.availWidth, screen.availHeight);
			var isFirefox = navigator.userAgent.toLowerCase().match(/firefox/) != null; //是否Chrome浏览器
			var isChrome = navigator.userAgent.toLowerCase().match(/chrome/) != null; //是否Chrome浏览器
			var isWebkit = navigator.userAgent.toLowerCase().match(/applewebkit/) != null; //是否极速模式浏览器
			var isSafari = zwxJQ.browser.safari; //是否Safari浏览器
			if(!isSafari&&!isWebkit&&!isChrome&&!isFirefox){
			   //跳转到下载页面
			 forwardBtn();
			}
			

		
			
					userid='#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.userNo}';
				    win_MainMenu = new CreatePopupExt();
			        win_SubMenu = new CreatePopupExt();
			        win_ContextMenu = new CreatePopupExt();
			        win_NoReadCall = new CreatePopupExt();
			        win_ColMenu = new CreatePopupExt();
                        //悬浮菜单事件调用
					    zwxJQ("#toolbarmenu").click(function(e) {
					    	var value="#{headBean.systemModules}";
							if (value.indexOf("18") == -1) {
					    		isShow = false;
					    	} else {
								isShow = true;
							}
                            win_PopMenu.ShowMenu(this); StopBubble(e);
					     });
					     
					      if (RedirectUrl != "") {
					        //从iyou打开C6页面
					        CreateNewTabWin(RedirectUrl);
					    }
					    else {
                            var openName_ = '#{headBean.tsPersonalSetting.displayName}';
                            var operUrl = '#{headBean.tsPersonalSetting.defaultUrl}';
					        //C6登陆后打开默认页面
//					        var tempdefaultpage = "content_menu.faces";
					            CreateNewTabWin(operUrl,openName_,"m03");
					    } 
					      
					  	var showPortals = "#{headBean.ifShowPortals}";
						if( '1' == showPortals ){
							ifMorePorts = true;
						}else{
							ifMorePorts = false;
						}
						if (ifMorePorts) {
							zwxJQ("#shortcutDesktopPortal").show();
							zwxJQ("#shortcutDesktop8").hide();
						} else {
							zwxJQ("#shortcutDesktop8").show();
							zwxJQ("#shortcutDesktopPortal").hide();
						}
						
						var portals = "#{headBean.portals}";
						if (null != portals && portals.length > 0) {
							var arrs = portals.split(";");
							var portal = arrs[0].split(",");
							zwxJQ("#shortcutDesktop8").click(function() {
								var tempdefaultpage = "/webapp/portal/portalCenter.faces?pid="+ portal[1];
								CreateNewTabWin(
										tempdefaultpage,
										portal[0], "protal"
										+ portal[1]);
								
							});
						}
						
					   zwxJQ("#shortcutDesktop2").click(function() { var tempdefaultpage = "content_menu.faces";
					            CreateNewTabWin(tempdefaultpage,"菜单桌面","m01");});
					   zwxJQ("#shortcutDesktop").click(function() { var tempdefaultpage = "content.faces";
					            CreateNewTabWin(tempdefaultpage,"个人桌面","01");});
                       zwxJQ("#allSeachDesktop").click(function() { var tempdefaultpage = "/webapp/system/allFileSel.faces";
                                CreateNewTabWin(tempdefaultpage,"全文检索","m02");});
                       zwxJQ("#KleSeachDesktop").click(function() { var tempdefaultpage = "/webapp/system/tsKleFileSel.faces";
                       			CreateNewTabWin(tempdefaultpage,"知识库检索","m02");});
                       zwxJQ("#shortcutDesktop3").click(function() { var tempdefaultpage = "/webapp/system/tdMsgMainSearchList.faces";
	            				CreateNewTabWin(tempdefaultpage,"信息查看","m03");});
                       zwxJQ("#portalListDesktop").click(function() { var tempdefaultpage = "/webapp/portal/portalCenter.faces";
       							CreateNewTabWin(tempdefaultpage,"门户","m03");});        
                       zwxJQ("#ColMenuList").click(function() { CreatePortalList(this);  });
                       zwxJQ("#shortcutDesktop4").click(function() { var tempdefaultpage = "/webapp/system/tdInfoMainList.faces?type=head";
       					CreateNewTabWin(tempdefaultpage,"寻呼","m03");});
                       zwxJQ("#shortcutDesktop5").click(function() { var tempdefaultpage = "/webapp/portal/personalPortalCenter.faces";
       					CreateNewTabWin(tempdefaultpage,"工作台","m03");});
                       zwxJQ("#personalSet").click(function() {
                           showPersonalDiag();});
                       //zwxJQ("#aboutUs").click(function() {PF('AboutUsDialog').show();});
					   //发送自己未读的消息
					   window.setTimeout(findMyNewMsg,5000);
                        //更换主题
                       var skinName = '#{facesContext.externalContext.sessionMap['SESSION_DATA'].skinName}';
                       PrimeFaces.changeTheme(skinName);
					});
				       //打开新的页签
			function CreateNewTabWin(url, name, id) {
			    if (typeof (name) == "undefined")
			        name = "";
			    win_TabMenu.OpenTabWin("m01", name, url, id);
			}
				 
			function CreateQickTabWin(url, name){
				win_TabMenu.OpenTabWin("m01", name, url, "");
			}
				       
			function forwordPage(name,adr,rid){
                if("" != name && "" != adr){
                    top.ShortcutMenuClick("01",name,adr,"");
                }
            }

			//创建门户下拉列表
		function CreatePortalList(evt) {
			    //门户列表显示时隐藏
			    if (win_ColMenu.isShow) {
			        win_ColMenu.hideanimate();
			        return false;
			    }
			    var portals = "#{headBean.portals}";
			    if(null != portals && portals.length > 0) {
			    
				    zwxJQ(win_ColMenu.document.body).css({ "overflow": "hidden", "background-color": "#FFFFFF", "border": "1px solid #9aaec1" });
				    
				    var strMenu = "";
				    var rowcount = 0;
			    
			    	var arrs = portals.split(";");
			    	rowcount = arrs.length;
			    	
			    	for(var i=0; i<rowcount; i++) {
			    		var portal=arrs[i].split(",");
			    		strMenu += "<li onclick=\"top.CreateNewTabWin('/webapp/portal/portalCenter.faces?pid="+portal[1]+"','"+portal[0]+"','protal"+portal[1]+"'); win_ColMenu.hideanimate();\"><a>"+portal[0]+"</a></li>";
			    	}
			    	
		            var sHTML = "";
		            sHTML += "<div class='c6ui-base-poplist'>";
		            sHTML += "<ul>";
		            sHTML += strMenu;
		            sHTML += "</ul></div>";

		            win_ColMenu.document.body.innerHTML = sHTML;
		            var theight = rowcount * 28;
		            var owidth = 230;
		            var oheight = theight;
		            var otop = parseInt(zwxJQ(evt).height());
		            var oleft = parseInt(zwxJQ(evt).width()) - owidth;
		            win_ColMenu.showanimate(oleft, otop, owidth, oheight, evt);			    	
			    	
			    }
			}
//]]>
	</script>
	
	<style type="text/css">
		.ui-dock-bottom{
			width:50%;
		}
	</style>
</h:head>
<h:body style="padding-top: 0; margin-right:0; margin-top: 0; margin-left: 0; width: 100%;overflow: hidden;">
    <h:form id="forwardFRM">
	<p:remoteCommand  name="forwardBtn" action="#{loginBean.browserGo}"/>
    <p:remoteCommand  name="forwardLoutBtn" action="#{loginBean.logOutGo}"/>
</h:form>
	<h:form id="dialogDeskForm">
        <p:remoteCommand  name="findMyNewMsg" action="#{headBean.sendMsgAction}" process="@this"/>
                    <p:remoteCommand name="updateMsg" action="#{headBean.showMsgAction}" update="dialogDeskForm:newImg" />
					<ui:include src="/WEB-INF/templates/system/pop.xhtml"/>
				
					 <div id="c6ui-header">
					  <div id="c6ui-header-extend">
				            
				            
				            <!-- 系统功能按钮 -->
				            <div class="c6ui-header-system">
				                	<table align="right">
				            		<tr>
				            			<td style="text-align: left;">	
				            				<span style="font-weight: bold;text-align: left;">
				            					 <h:outputText value="单位：#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.unitname}" rendered="#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.unitname!=null}" style="font-size:10pt"/>
				            					 <h:outputText value="单位：无 " style="font-size:10pt" rendered="#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.tsUnit.unitname==null}"/>
				            				</span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;
				            			</td>
				            			<td style="text-align: left;">			
						                     <p:commandLink style="font-size:9pt" action="#{pwdBean.showDialogHeadAction}" resetValues="true" update="dialogDeskForm:dialogDeskForm2:PwdDialogGrid">
						                        <span class="widget-btn-plus">修改密码 </span>
						                     </p:commandLink>
						                    	&#160;&#160;&#160;&#160;
						                     <p:commandLink style="font-size:9pt" action="#{applicationBean.logOut}" 
						                       >  
						                        <span class="widget-btn-plus">重新登录 </span>
						                     </p:commandLink>
				            			</td>
				            		</tr>	
				            		<tr>
				            			<td style="text-align: left;">
				            				<span style="font-weight: bold;text-align: left;"> 
				            				<h:outputText value="人员：#{facesContext.externalContext.sessionMap['SESSION_DATA'].user.username}"  style="font-size:10pt"/>
				            				</span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;
				            			</td>
				            			<td style="text-align: left;"></td>
				            		</tr>
				            	</table>
				            </div>
				        </div>
						 <div id="c6ui-header-min">
				            <div id="c6ui-header-min-menu">

				                <a href="javascript:void(0)" id="toolbarmenu"><span  class="c6ui-widget-btn-single">
				                    <img alt="" src="/resources/component/quickDesktop/image/ui-scroll-pane-list.png" />
				                  		   菜单
				                    <img id="imgMenuStyle" alt="" src="/resources/component/quickDesktop/image/arrowDown.png" />
				                </span></a>

				            </div>
				             <!-- Tab区 -->
				            <div id="c6ui-header-min-tab">
				                <div id="c6ui-tabs">
				                    <div class="left_btn">
				                        <a href="javascript:void(0)" onclick="win_TabMenu.SlipTabLeft()">
				                            <img alt="左边" src="/resources/component/quickDesktop/image/tab_arrow_left.png" /></a></div>
				                    <div class="right_btn">
				                        <a href="javascript:void(0)" onclick="win_TabMenu.SlipTabRight()">
				                            <img  alt="右边" src="/resources/component/quickDesktop/image/tab_arrow_right.png" /></a></div>
				                    <div id="center_div" style="width: 100%; overflow: hidden;">
				                        <ul id="c6ui-tabs-list">
				                        </ul>
				                    </div>
				                </div>
				            </div>
				             <!-- 扩展功能区 -->
					            <div id="c6ui-header-min-widget">
					            
					            <span class="c6ui-widget-btn"  id="shortcutDesktopPortal" >
					            	<span class="widget-btn-left" >
                    					<img id="backhomepage" src="/resources/component/quickDesktop/image/16px/home.png" width="16" height="16" align="bottom" />&nbsp;
                    				</span>
                    				<span class="widget-btn-plus" >
                            			<img id="ColMenuList" src="/resources/component/quickDesktop/image/base-triangle-down.png" width="16"  height="16" align="bottom"   onclick="PF('PortalBtns').show()"/>&nbsp;
                            		</span> 
                            	</span>
                            	
                            	<span id="shortcutDesktop8"  style="display:none;"  class="c6ui-widget-btn-single"
									title="门户"> <span> <img alt="门户" width="16"
										height="16" align="bottom"
										src="/resources/component/quickDesktop/image/16px/home.png" />&#160;
								</span>
								</span>
                            	
					            <span id="shortcutDesktop5" class="c6ui-widget-btn-single" title="工作台"> 
					            	 <span>
										<img alt="工作台" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/workbench.png" />&#160;
									 </span>
								</span>
					            <span id="shortcutDesktop4" class="c6ui-widget-btn-single" title="寻呼"> 
					            	 <span>
										<img alt="寻呼" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/bbs.png" />&#160;
									 </span>
								</span>
					            <span id="shortcutDesktop3" class="c6ui-widget-btn-single" title="信息查看"> 
									<h:panelGroup id="newImg">
										<h:panelGroup rendered="#{headBean.messNum=='0'}">
											<img alt="我的消息" width="16" height="16" align="bottom" src="/resources/images/login/balloon.png" />&#160;
										</h:panelGroup>
										<h:panelGroup rendered="#{headBean.messNum!='0'}">
											<img alt="未阅消息" src="/resources/images/login/new.gif" />
												<h:outputLabel style="color: #ffffff" value="#{headBean.messNum}"/>
										</h:panelGroup>
									</h:panelGroup>
								 </span>
								     &#160;
                                      <span id="allSeachDesktop" class="c6ui-widget-btn-single" title="全文检索"> <span>
					                    <img alt="全文检索" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/all-seach.png" /> &#160;
					                </span> </span>
                                    &#160;
                                    <span id="KleSeachDesktop" class="c6ui-widget-btn-single" title="知识库检索"> <span>
					                    <img alt="知识库检索" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/all-seach.png" /> &#160;
					                </span> </span>
                                    &#160;
					            <span id="shortcutDesktop2" class="c6ui-widget-btn-single" title="菜单桌面"> <span>
					                    <img alt="菜单桌面" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/select.png" /> &#160;
					                </span> </span>
					            &#160;
					            
					                <span id="shortcutDesktop" class="c6ui-widget-btn-single" title="个人桌面"> <span>
					                    <img alt="个人桌面" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/shortcutdesktop.png" /> &#160;
					                </span> </span>
					                
					                &#160;
                                    <span id="personalSet" class="c6ui-widget-btn-single" title="个人设置"> <span>
					                    <img alt="个人设置" width="16" height="16" align="bottom" src="/resources/component/quickDesktop/image/16px/t-shirt.png" /> &#160;
					                </span> </span>
  								 &#160;
                                   <span class="c6ui-widget-btn-single" id="c6ui-header-handle"> <span>
                                       <img   alt="收缩" id="hidehead" src="/resources/component/quickDesktop/image/16px/header-goCollapse.png" width="16"
                                           height="16" align="bottom" />
                                       <img alt="展开" id="expandhead" align="bottom" width="16" height="16" style="display: none"
                                           src="/resources/component/quickDesktop/image/16px/header-goExpanded.png" /> &#160; </span> 
					                </span>
					            </div>
			            </div>
		             
			              </div>
			            <!-- 主显示区 -->
					    <div id="c6ui-main" style="padding-bottom: 15px; padding-left: 6px; padding-right: 6px;
					        padding-top: 6px;height: 700px ;  ">
					    </div>
		
		<!-- 			    
		<p:dialog widgetVar="AboutUsDialog" id="aboutUsDialog" modal="true" header="关于我们" draggable="false" resizable="false" style="padding:0px;">
			<img src="/resources/images/About.png" style="cursor: pointer;margin: 0px;"/>
		</p:dialog>
		 -->		    
        <p:remoteCommand name="showDialogHead" action="#{pwdBean.showDialogHeadAction}"/>
        <p:remoteCommand name="showPersonalDiag" action="#{personalSetBean.showPersonalDiag}"/>
        <ui:include src="pwdEdit.xhtml" />
        <ui:include src="/webapp/portal/personalSetList.xhtml" />

		<p:poll interval="60" process="@this" widgetVar="HeadPoll" autoStart="true"/>	
		<p:poll interval="900" process="@this" widgetVar="TodoPoll" autoStart="true"
			listener="#{headBean.timeUpdateMsg}"/>	
		<p:dock position="bottom" rendered="#{headBean.ifUserQickDesk==1}" itemWidth="40" 
			maxWidth="40" id="dockPanel" >
			<c:forEach items="#{headBean.qickDeskList}" var="itm">
				<p:menuitem value="#{itm[0]}" icon="#{itm[2]}" onclick="CreateQickTabWin('#{itm[1]}','#{itm[0]}');"/>
			</c:forEach>
		</p:dock>
    </h:form>
    <p:growl id="growl" life="3000" showDetail="false" autoUpdate="true"/>
</h:body>
</html>
