<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>zyws.xjzw</groupId>
    <artifactId>web-heth-comm</artifactId>
    <version>2.2-SNAPSHOT</version>
    <packaging>war</packaging>
    <name>web-heth-comm</name>
    <description />
    <properties>
        <!-- 依赖项目版本 -->
        <zwx.base.version>1.19-SNAPSHOT</zwx.base.version>
        <zwx.web-system.version>1.17-SNAPSHOT</zwx.web-system.version>
        <zwx.web-heth-base.version>1.1-SNAPSHOT</zwx.web-heth-base.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <tagBase>https://************:8443/svn/dev/NEWSVN/DevelopmentNo1/JavaProjects/tags/web-heth-comm</tagBase>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <!--<version>3.0</version>-->
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <attachClasses>true</attachClasses>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.3</version>
                <configuration>
                    <excludePackageNames>*.*</excludePackageNames>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- base -->
        <dependency>
            <groupId>cyber</groupId>
            <artifactId>base</artifactId>
            <version>${zwx.base.version}</version>
        </dependency>
        <dependency>
            <groupId>cyber</groupId>
            <artifactId>web-system</artifactId>
            <version>${zwx.web-system.version}</version>
            <type>war</type>
        </dependency>
        <dependency>
            <groupId>cyber</groupId>
            <artifactId>web-system</artifactId>
            <version>${zwx.web-system.version}</version>
            <type>jar</type>
            <classifier>classes</classifier>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cyber</groupId>
            <artifactId>web-heth-base</artifactId>
            <version>${zwx.web-heth-base.version}</version>
            <type>war</type>
        </dependency>
        <dependency>
            <groupId>cyber</groupId>
            <artifactId>web-heth-base</artifactId>
            <version>${zwx.web-heth-base.version}</version>
            <type>jar</type>
            <classifier>classes</classifier>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>JxcDomain</id>
            <name>JxcDomainName</name>
            <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>JxcDomain</id>
            <name>JxcDomainName</name>
            <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>JxcDomain</id>
            <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
