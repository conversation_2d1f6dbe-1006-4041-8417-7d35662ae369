<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <modelVersion>4.0.0</modelVersion>  
  <groupId>cyber</groupId>  
  <artifactId>base</artifactId>  
  <version>1.19-SNAPSHOT</version>
  <properties> 
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>  
    <spring.version>4.1.8</spring.version>  
    <mybatis.version>3.5.8</mybatis.version>  
    <mybatis-spring.version>2.0.5</mybatis-spring.version>
    <mybatis-mapper.version>2.3.4</mybatis-mapper.version>  
    <jsf.version>2.2.1</jsf.version>  
    <jsf-impl.version>2.2.2</jsf-impl.version>
    <poi.version>3.8</poi.version>
    <echarts.version>2.2.7</echarts.version>  
    <gson.version>2.6.1</gson.version>
    <!-- 由于jwt中包含2.9.0版本的jackson（当前被排除） 当前jackson升级到2.15.4 jackson-databind存在漏洞CVE-2019-14439 jackson-dataformat因外网仓库无对应的jackson-dataformat保留2.7.3-->
    <jackson.version>2.15.4</jackson.version>
    <log4j.version>2.18.0</log4j.version>
  </properties>
  <build> 
    <plugins> 
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-javadoc-plugin</artifactId>  
        <version>2.10.3</version>  
        <configuration> 
          <excludePackageNames>*.*</excludePackageNames> 
        </configuration> 
      </plugin>  
      <plugin> 
        <artifactId>maven-compiler-plugin</artifactId>  
        <version>2.3.2</version>  
        <configuration> 
          <source>1.7</source>  
          <target>1.7</target> 
        </configuration> 
      </plugin>  
      <plugin> 
        <groupId>org.jacoco</groupId>  
        <artifactId>jacoco-maven-plugin</artifactId>  
        <executions> 
          <execution> 
            <id>pre-test</id>  
            <goals> 
              <goal>prepare-agent</goal> 
            </goals> 
          </execution>  
          <execution> 
            <id>post-test</id>  
            <phase>test</phase>  
            <goals> 
              <goal>report</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin> 
    </plugins> 
  </build>  
  <dependencies> 
    <!-- apache -->  
    <dependency> 
      <groupId>commons-codec</groupId>  
      <artifactId>commons-codec</artifactId>  
      <version>1.15</version>
    </dependency>  
    <dependency> 
      <groupId>commons-lang</groupId>  
      <artifactId>commons-lang</artifactId>  
      <version>2.6</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-lang</groupId>  
      <artifactId>commons-lang3</artifactId>  
      <version>3.3.2</version> 
    </dependency>
    <dependency> 
      <groupId>org.apache.logging.log4j</groupId>  
      <artifactId>log4j-1.2-api</artifactId>  
      <version>${log4j.version}</version>
    </dependency> 
    <dependency> 
      <groupId>org.apache.logging.log4j</groupId>  
      <artifactId>log4j-api</artifactId>  
      <version>${log4j.version}</version>
    </dependency>
    <dependency> 
      <groupId>org.apache.logging.log4j</groupId>  
      <artifactId>log4j-core</artifactId>  
      <version>${log4j.version}</version>
    </dependency> 
    <dependency> 
      <groupId>apache-ant-zip</groupId>  
      <artifactId>apache-ant-zip</artifactId>  
      <version>2.3</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-io</groupId>  
      <artifactId>commons-io</artifactId>  
      <version>2.4</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-collections</groupId>  
      <artifactId>commons-collections</artifactId>  
      <version>3.2.2</version>
    </dependency>  
    <dependency> 
      <groupId>commons-fileupload</groupId>  
      <artifactId>commons-fileupload</artifactId>  
      <version>1.3</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-logging</groupId>  
      <artifactId>commons-logging</artifactId>  
      <version>1.0.4</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-httpclient</groupId>  
      <artifactId>commons-httpclient</artifactId>  
      <version>3.0</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-beanutils</groupId>  
      <artifactId>commons-beanutils</artifactId>  
      <version>1.8.1</version> 
    </dependency>  
    <dependency> 
      <groupId>org.apache.xmlbeans</groupId>  
      <artifactId>xmlbeans</artifactId>  
      <version>2.6.0</version> 
    </dependency>  
    <!-- hibernate -->  
    <dependency> 
      <groupId>dom4j</groupId>  
      <artifactId>dom4j</artifactId>  
      <version>1.6.1</version> 
    </dependency>  
    <dependency> 
      <groupId>hibernate3</groupId>  
      <artifactId>hibernate3</artifactId>  
      <version>3.6</version> 
    </dependency>  
    <dependency> 
      <groupId>antlr</groupId>  
      <artifactId>antlr</artifactId>  
      <version>2.7.7</version> 
    </dependency>  
    <!-- spring -->  
    <dependency> 
      <groupId>aopalliance</groupId>  
      <artifactId>aopalliance</artifactId>  
      <version>1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>cglib</groupId>  
      <artifactId>cglib</artifactId>  
      <version>3.1</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-aop</groupId>  
      <artifactId>spring-aop</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-aspects</groupId>  
      <artifactId>spring-aspects</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-beans</groupId>  
      <artifactId>spring-beans</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-context</groupId>  
      <artifactId>spring-context</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-context-support</groupId>  
      <artifactId>spring-context-support</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-core</groupId>  
      <artifactId>spring-core</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-expression</groupId>  
      <artifactId>spring-expression</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-instrument</groupId>  
      <artifactId>spring-instrument</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-instrument-tomcat</groupId>  
      <artifactId>spring-instrument-tomcat</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-jdbc</groupId>  
      <artifactId>spring-jdbc</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-jms</groupId>  
      <artifactId>spring-jms</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-orm</groupId>  
      <artifactId>spring-orm</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-oxm</groupId>  
      <artifactId>spring-oxm</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-test</groupId>  
      <artifactId>spring-test</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-tx</groupId>  
      <artifactId>spring-tx</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-web</groupId>  
      <artifactId>spring-web</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-webmvc</groupId>  
      <artifactId>spring-webmvc</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>spring-webmvc-portlet</groupId>  
      <artifactId>spring-webmvc-portlet</artifactId>  
      <version>${spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>aspectjweaver</groupId>  
      <artifactId>aspectjweaver</artifactId>  
      <version>1.8.4</version> 
    </dependency>  
    <!-- mybatis -->  
    <dependency> 
      <groupId>mybatis</groupId>  
      <artifactId>mybatis</artifactId>  
      <version>${mybatis.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>mybatis</groupId>
      <artifactId>mybatis-spring</artifactId>  
      <version>${mybatis-spring.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>mybatis-mapper</groupId>  
      <artifactId>mybatis-mapper</artifactId>  
      <version>${mybatis-mapper.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>joda-time</groupId>  
      <artifactId>joda-time</artifactId>  
      <version>2.1</version> 
    </dependency>  
    <!-- javaee6 -->  
    <dependency> 
      <groupId>javax.annotation</groupId>  
      <artifactId>javax.annotation</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.ejb</groupId>  
      <artifactId>javax.ejb</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.enterprise.deploy</groupId>  
      <artifactId>javax.enterprise.deploy</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.jms</groupId>  
      <artifactId>javax.jms</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.management.j2ee</groupId>  
      <artifactId>javax.management.j2ee</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.persistence</groupId>  
      <artifactId>javax.persistence</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.resource</groupId>  
      <artifactId>javax.resource</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.security.auth.message</groupId>  
      <artifactId>javax.security.auth.message</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.security.jacc</groupId>  
      <artifactId>javax.security.jacc</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.transaction</groupId>  
      <artifactId>javax.transaction</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.servlet-3.0</groupId>  
      <artifactId>javax.servlet-3.0</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.servlet.jsp.jstl</groupId>  
      <artifactId>javax.servlet.jsp.jstl</artifactId>  
      <version>6.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.servlet.jsp</groupId>  
      <artifactId>javax.servlet.jsp</artifactId>  
      <version>6.0-1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>javax.servlet</groupId>  
      <artifactId>javax.servlet</artifactId>  
      <version>6.0-1.0</version> 
    </dependency>
    <dependency> 
      <groupId>jsf-api</groupId>  
      <artifactId>jsf-api</artifactId>  
      <version>${jsf.version}</version> 
    </dependency>
    <dependency>
      <groupId>jsf-impl</groupId>
      <artifactId>jsf-impl</artifactId>
      <version>${jsf-impl.version}</version>
    </dependency>
    <!-- excel -->  
    <dependency> 
      <groupId>poi</groupId>  
      <artifactId>poi</artifactId>  
      <version>${poi.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>poi-ooxml</groupId>  
      <artifactId>poi-ooxml</artifactId>  
      <version>${poi.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>poi-ooxml-schemas</groupId>  
      <artifactId>poi-ooxml-schemas</artifactId>  
      <version>${poi.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>poi-scratchpad</groupId>  
      <artifactId>poi-scratchpad</artifactId>  
      <version>${poi.version}</version> 
    </dependency>
    <dependency> 
      <groupId>javassist</groupId>  
      <artifactId>javassist</artifactId>  
      <version>1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>ehcache-core</groupId>  
      <artifactId>ehcache-core</artifactId>  
      <version>2.6.9</version> 
    </dependency>  
    <dependency> 
      <groupId>ehcache-web</groupId>  
      <artifactId>ehcache-web</artifactId>  
      <version>2.0.4</version> 
    </dependency>  
    <dependency> 
      <groupId>guava</groupId>  
      <artifactId>guava</artifactId>  
      <version>17.0</version> 
    </dependency>  
    <dependency> 
      <groupId>org.quartz-scheduler</groupId>  
      <artifactId>quartz</artifactId>  
      <version>2.2.1</version> 
    </dependency>  
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
    <dependency> 
      <groupId>freemarker</groupId>  
      <artifactId>freemarker</artifactId>  
      <version>2.3.20</version> 
    </dependency>  
    <dependency> 
      <groupId>json-lib-jdk15</groupId>  
      <artifactId>json-lib-jdk15</artifactId>  
      <version>1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>ezmorph</groupId>  
      <artifactId>ezmorph</artifactId>  
      <version>1.0.6</version> 
    </dependency>  
    <dependency> 
      <groupId>junit</groupId>  
      <artifactId>junit</artifactId>  
      <version>4.12</version> 
    </dependency>  
    <dependency> 
      <groupId>org.hamcrest.core</groupId>  
      <artifactId>org.hamcrest.core</artifactId>  
      <version>1.3.0</version> 
    </dependency>  
    <!--jodconvert -->  
    <dependency> 
      <groupId>jodconverter</groupId>  
      <artifactId>jodconverter</artifactId>  
      <version>2.2.2</version> 
    </dependency>  
    <dependency> 
      <groupId>jodconverter-cli</groupId>  
      <artifactId>jodconverter-cli</artifactId>  
      <version>2.2.2</version> 
    </dependency>  
    <dependency> 
      <groupId>commons-cli</groupId>  
      <artifactId>commons-cli</artifactId>  
      <version>1.2</version> 
    </dependency>  
    <dependency> 
      <groupId>juh</groupId>  
      <artifactId>juh</artifactId>  
      <version>3.0.1</version> 
    </dependency>  
    <dependency> 
      <groupId>jurt</groupId>  
      <artifactId>jurt</artifactId>  
      <version>3.0.1</version> 
    </dependency>  
    <dependency> 
      <groupId>ridl</groupId>  
      <artifactId>ridl</artifactId>  
      <version>3.0.1</version> 
    </dependency>  
    <dependency> 
      <groupId>unoil</groupId>  
      <artifactId>unoil</artifactId>  
      <version>3.0.1</version> 
    </dependency>
    <dependency>
      <groupId>com.thoughtworks.xstream</groupId>
      <artifactId>xstream</artifactId>
      <version>1.4.20</version>
    </dependency>
    <dependency> 
      <groupId>fakepath</groupId>  
      <artifactId>xmlpull</artifactId>  
      <version>1.1.3.1</version> 
    </dependency>  
    <dependency> 
      <groupId>echarts</groupId>  
      <artifactId>echarts</artifactId>  
      <version>${echarts.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>gson</groupId>  
      <artifactId>gson</artifactId>  
      <version>${gson.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>nl.bitwalker</groupId>  
      <artifactId>UserAgentUtils</artifactId>  
      <version>1.13</version> 
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>  
      <artifactId>jackson-core</artifactId>  
      <version>${jackson.version}</version> 
    </dependency>  
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>  
      <artifactId>jackson-databind</artifactId>  
      <version>${jackson.version}</version>
    </dependency>  
    <dependency> 
      <groupId>com.fasterxml.jackson.core</groupId>  
      <artifactId>jackson-annotations</artifactId>  
      <version>${jackson.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>com.fasterxml.jackson.module</groupId>  
      <artifactId>jackson-module-jaxb-annotations</artifactId>  
      <version>${jackson.version}</version> 
    </dependency>  
    <dependency> 
      <groupId>jackson-dataformat</groupId>  
      <artifactId>jackson-dataformat</artifactId>
      <version>2.7.3</version>
    </dependency>
    <!-- 极光推送JAR包 -->  
    <dependency> 
      <groupId>jpush-client</groupId>  
      <artifactId>jpush-client</artifactId>  
      <version>3.2.9</version> 
    </dependency>  
    <dependency> 
      <groupId>activation</groupId>  
      <artifactId>activation</artifactId>  
      <version>1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>fakepath</groupId>  
      <artifactId>commons-email</artifactId>  
      <version>1.3.3</version> 
    </dependency>  
    <dependency> 
      <groupId>mail</groupId>  
      <artifactId>mail</artifactId>  
      <version>1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>jaxen</groupId>  
      <artifactId>jaxen</artifactId>  
      <version>1.1.6</version> 
    </dependency>  
    <dependency> 
      <groupId>IKExpression</groupId>  
      <artifactId>IKExpression</artifactId>  
      <version>2.1.2</version> 
    </dependency>  
    <!-- 阿里云 -->  
    <dependency> 
      <groupId>aliyun</groupId>  
      <artifactId>aliyun-sdk-oss</artifactId>  
      <version>2.5.0</version> 
    </dependency>  
    <dependency> 
      <groupId>aliyun</groupId>  
      <artifactId>aliyun-java-sdk-core</artifactId>  
      <version>3.1.1</version> 
    </dependency>  
    <dependency> 
      <groupId>aliyun</groupId>  
      <artifactId>aliyun-java-sdk-mts</artifactId>  
      <version>2.1.9</version> 
    </dependency>  
    <dependency> 
      <groupId>aliyun</groupId>  
      <artifactId>aliyun-java-sdk-sts</artifactId>  
      <version>2.1.6</version> 
    </dependency>  
    <dependency> 
      <groupId>httpclient</groupId>  
      <artifactId>httpclient</artifactId>  
      <version>4.4.1</version> 
    </dependency>  
    <dependency> 
      <groupId>httpcore</groupId>  
      <artifactId>httpcore</artifactId>  
      <version>4.4.1</version> 
    </dependency>  
    <dependency> 
      <groupId>jdom</groupId>  
      <artifactId>jdom</artifactId>  
      <version>1.1</version> 
    </dependency>  
    <dependency> 
      <groupId>com.sun.mail</groupId>  
      <artifactId>javax.mail</artifactId>  
      <version>1.5.0</version> 
    </dependency>  
    <!-- Qrcode二维码 -->  
    <dependency> 
      <groupId>com.google.zxing</groupId>  
      <artifactId>core</artifactId>  
      <version>3.1.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.google.zxing</groupId>  
      <artifactId>javase</artifactId>  
      <version>3.1.0</version> 
    </dependency>  
    <!--腾讯云对象存储-->  
    <dependency> 
      <groupId>com.qcloud</groupId>  
      <artifactId>cos_api</artifactId>  
      <version>5.2.4</version> 
    </dependency>
    <!-- sm加密 -->  
    <!--<dependency>
      <groupId>org.bouncycastle</groupId>  
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.55</version>
    </dependency>
    <dependency>
      <groupId>bouncycastle</groupId>  
      <artifactId>bcprov-jdk14</artifactId>  
      <version>140</version>
      <scope>compile</scope> 
    </dependency>-->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <version>1.70</version>
      <scope>compile</scope>
    </dependency>
    <!-- 百度ai -->  
    <dependency> 
      <groupId>com.baidu.aip</groupId>  
      <artifactId>aip-java-sdk</artifactId>  
      <version>4.2.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.baidu.aip</groupId>  
      <artifactId>aip-java-json</artifactId>  
      <version>20160810</version> 
    </dependency> 
    <dependency>
		<groupId>org.apache.camel</groupId>
		<artifactId>camel-ftp</artifactId>
		<version>2.13.2</version>
		</dependency>
	<dependency>
	    <groupId>commons-net</groupId>
	    <artifactId>commons-net</artifactId>
	    <version>3.6</version>
	</dependency>
  <dependency>
      <groupId>org.jasig.cas.client</groupId>
      <artifactId>cas-client-core</artifactId>
      <version>3.2.1</version>
    </dependency>
    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>3.4.0</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--二维码生成 -->
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
      <version>3.3.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>javase</artifactId>
      <version>3.3.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.14.8</version>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.16</version>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>2.0.31</version>
    </dependency>
    <!--繁体字校验-->
    <dependency>
      <groupId>com.hankcs</groupId>
      <artifactId>hanlp</artifactId>
      <version>portable-1.8.1</version>
    </dependency>
  </dependencies>  
  <repositories>
    <repository>
      <id>JxcDomain</id>
      <name>JxcDomainName</name>
      <url>http://10.88.99.21:8081/nexus/content/repository/JxcDomain/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>central</id>
      <name>Central</name>
      <url>http://10.88.99.21:8081/nexus/content/repository/central/</url>
      <releases>
        <enabled>false</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
    </repository>
  </repositories> 
</project>
