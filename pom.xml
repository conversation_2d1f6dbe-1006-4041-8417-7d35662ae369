<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cyber</groupId>
  <artifactId>web-system</artifactId>
  <version>1.17-SNAPSHOT</version>
  <packaging>war</packaging>
  <name>web-system</name>
  <description />
  <properties>
    <!-- base jf-->
    <zwx.base.version>1.19-SNAPSHOT</zwx.base.version>
    <zwx.web-files.version>1.3-SNAPSHOT</zwx.web-files.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <primefaces.version>4.1.5</primefaces.version>
    <atmosphere-runtime.version>2.0.10</atmosphere-runtime.version>
    <lucene.version>4.8.1</lucene.version>
    <activiti.version>5.14</activiti.version>
  </properties>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.3</version>
        <configuration>
          <tagBase>https://************:8443/svn/dev/NEWSVN/DevelopmentNo1/JavaProjects/tags/web-system</tagBase>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.3</version>
        <configuration>
          <excludePackageNames>*.*</excludePackageNames>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <source>1.7</source>
          <target>1.7</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>2.2</version>
        <configuration>
          <!--<version>3.0</version>  -->
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <attachClasses>true</attachClasses>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>pre-test</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>post-test</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <excludes>
          <exclude>**/*.pdf</exclude>
        </excludes>
      </resource>
    </resources>
  </build>
  <dependencies>
    <!-- zwx-base -->
    <dependency>
      <groupId>cyber</groupId>
      <artifactId>base</artifactId>
      <version>${zwx.base.version}</version>
    </dependency>
    <dependency>
      <groupId>cyber</groupId>
      <artifactId>web-files</artifactId>
      <version>${zwx.web-files.version}</version>
      <type>war</type>
    </dependency>
    <dependency>
      <groupId>cyber</groupId>
      <artifactId>web-files</artifactId>
      <version>${zwx.web-files.version}</version>
      <type>jar</type>
      <classifier>classes</classifier>
      <scope>provided</scope>
    </dependency>
    <!-- db -->
    <dependency>
      <groupId>ojdbc6</groupId>
      <artifactId>ojdbc6</artifactId>
      <version>11.2.0.4</version>
    </dependency>
    <dependency>
      <groupId>druid</groupId>
      <artifactId>druid</artifactId>
      <version>1.0.14</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>5.0.1.Final</version>
    </dependency>
    <!-- primefaces -->
    <dependency>
      <groupId>primefaces</groupId>
      <artifactId>primefaces</artifactId>
      <version>${primefaces.version}</version>
    </dependency>
    <dependency>
      <groupId>all-themes</groupId>
      <artifactId>all-themes</artifactId>
      <version>1.0.10</version>
    </dependency>
    <dependency>
      <groupId>atmosphere-runtime</groupId>
      <artifactId>atmosphere-runtime</artifactId>
      <version>${atmosphere-runtime.version}</version>
    </dependency>
    <!-- lucene -->
    <dependency>
      <groupId>lucene-analyzers-common</groupId>
      <artifactId>lucene-analyzers-common</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <dependency>
      <groupId>lucene-core</groupId>
      <artifactId>lucene-core</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <dependency>
      <groupId>lucene-highlighter</groupId>
      <artifactId>lucene-highlighter</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <dependency>
      <groupId>lucene-memory</groupId>
      <artifactId>lucene-memory</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <dependency>
      <groupId>lucene-queries</groupId>
      <artifactId>lucene-queries</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <dependency>
      <groupId>lucene-queryparser</groupId>
      <artifactId>lucene-queryparser</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <dependency>
      <groupId>lucene-sandbox</groupId>
      <artifactId>lucene-sandbox</artifactId>
      <version>${lucene.version}</version>
    </dependency>
    <!-- activiti -->
    <dependency>
      <groupId>activiti-engine</groupId>
      <artifactId>activiti-engine</artifactId>
      <version>${activiti.version}.5</version>
    </dependency>
    <dependency>
      <groupId>activiti-bmpn-model</groupId>
      <artifactId>activiti-bmpn-model</artifactId>
      <version>${activiti.version}</version>
    </dependency>
    <dependency>
      <groupId>activiti-spring</groupId>
      <artifactId>activiti-spring</artifactId>
      <version>${activiti.version}</version>
    </dependency>
    <dependency>
      <groupId>activiti-bpmn-converter</groupId>
      <artifactId>activiti-bpmn-converter</artifactId>
      <version>${activiti.version}</version>
    </dependency>
    <!-- webservice -->
    <dependency>
      <groupId>axis</groupId>
      <artifactId>axis</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>commons-discovery</groupId>
      <artifactId>commons-discovery</artifactId>
      <version>0.2</version>
    </dependency>
    <dependency>
      <groupId>jaxrpc</groupId>
      <artifactId>jaxrpc</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>saaj</groupId>
      <artifactId>saaj</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>wsdl4j</groupId>
      <artifactId>wsdl4j</artifactId>
      <version>1.5.1</version>
    </dependency>
    <!-- others -->
    <dependency>
      <groupId>groovy</groupId>
      <artifactId>groovy</artifactId>
      <version>2.0.1</version>
    </dependency>
    <dependency>
      <groupId>core-renderer</groupId>
      <artifactId>core-renderer</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>iText</groupId>
      <artifactId>iText</artifactId>
      <version>2.1.7</version>
    </dependency>
    <dependency>
      <groupId>com.lowagie</groupId>
      <artifactId>itext-rtf</artifactId>
      <version>2.1.7</version>
      <exclusions>
        <exclusion>
          <groupId>bouncycastle</groupId>
          <artifactId>bcmail-jdk14</artifactId>
        </exclusion>
        <exclusion>
          <groupId>bouncycastle</groupId>
          <artifactId>bcprov-jdk14</artifactId>
        </exclusion>
        <exclusion>
          <groupId>bouncycastle</groupId>
          <artifactId>bctsp-jdk14</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- mongodb java driver -->
    <dependency>
      <groupId>org.mongodb</groupId>
      <artifactId>mongo-java-driver</artifactId>
      <version>3.4.3</version>
    </dependency>
    <!-- spring data mongodb -->
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-commons</artifactId>
      <version>1.11.1.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-mongodb</artifactId>
      <version>1.8.0.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>0.7.3</version>
      <exclusions>
        <exclusion>
          <groupId>bouncycastle</groupId>
          <artifactId>bcmail-jdk14</artifactId>
        </exclusion>
        <exclusion>
          <groupId>bouncycastle</groupId>
          <artifactId>bcprov-jdk14</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- 金格Office -->
    <dependency>
      <groupId>ioffice</groupId>
      <artifactId>ioffice</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>sqljdbc</groupId>
      <artifactId>sqljdbc</artifactId>
      <version>1.0</version>
    </dependency>
    <!-- freemarker -->
    <dependency>
      <groupId>freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>2.3.20</version>
    </dependency>
    <!-- guava -->
    <dependency>
      <groupId>guava</groupId>
      <artifactId>guava</artifactId>
      <version>17.0</version>
    </dependency>
    <!-- activemq -->
    <dependency>
      <groupId>apache</groupId>
      <artifactId>activemq-all</artifactId>
      <version>5.11.1</version>
    </dependency>
    <dependency>
      <groupId>apache</groupId>
      <artifactId>activemq-pool</artifactId>
      <version>5.9.1</version>
    </dependency>
    <dependency>
      <groupId>commons-pool</groupId>
      <artifactId>commons-pool</artifactId>
      <version>1.6</version>
    </dependency>
    <!-- commons -->
    <dependency>
      <groupId>commons-dbcp</groupId>
      <artifactId>commons-dbcp</artifactId>
      <version>1.4</version>
    </dependency>
    <dependency>
      <groupId>commons-math</groupId>
      <artifactId>commons-math</artifactId>
      <version>1.2</version>
    </dependency>
    <dependency>
      <groupId>commons-vfs</groupId>
      <artifactId>commons-vfs</artifactId>
      <version>1.0</version>
    </dependency>
    <!-- eigenbase -->
    <dependency>
      <groupId>eigenbase-properties</groupId>
      <artifactId>eigenbase-properties</artifactId>
      <version>1.1.0.10924</version>
    </dependency>
    <dependency>
      <groupId>eigenbase-resgen</groupId>
      <artifactId>eigenbase-resgen</artifactId>
      <version>1.3.0.11873</version>
    </dependency>
    <dependency>
      <groupId>eigenbase-xom</groupId>
      <artifactId>eigenbase-xom</artifactId>
      <version>1.3.0.11999</version>
    </dependency>
    <!-- mondrian -->
    <dependency>
      <groupId>mondrian</groupId>
      <artifactId>mondrian</artifactId>
      <version>4.3.0.1</version>
      <classifier>SPARK</classifier>
    </dependency>
    <!-- olap4j -->
    <dependency>
      <groupId>olap4j</groupId>
      <artifactId>olap4j</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>olap4j-xmla</groupId>
      <artifactId>olap4j-xmla</artifactId>
      <version>1.2.0</version>
    </dependency>
    <dependency>
      <groupId>olap4j-xmlaserver</groupId>
      <artifactId>olap4j-xmlaserver</artifactId>
      <version>1.2.0</version>
    </dependency>
    <!-- saiku -->
    <dependency>
      <groupId>saiku-olap-util</groupId>
      <artifactId>saiku-olap-util</artifactId>
      <version>3.8</version>
      <classifier>RC5</classifier>
    </dependency>
    <dependency>
      <groupId>saiku-query</groupId>
      <artifactId>saiku-query</artifactId>
      <version>0.1</version>
    </dependency>
    <dependency>
      <groupId>saiku-service</groupId>
      <artifactId>saiku-service</artifactId>
      <version>3.8</version>
      <classifier>RC5</classifier>
    </dependency>
    <dependency>
      <groupId>saiku-web</groupId>
      <artifactId>saiku-web</artifactId>
      <version>3.8</version>
      <classifier>RC5</classifier>
    </dependency>
    <dependency>
      <groupId>activation</groupId>
      <artifactId>activation</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>fakepath</groupId>
      <artifactId>commons-email</artifactId>
      <version>1.3.3</version>
    </dependency>
    <dependency>
      <groupId>mail</groupId>
      <artifactId>mail</artifactId>
      <version>1.0</version>
    </dependency>
    <!-- fckeditor -->
    <dependency>
      <groupId>java-core</groupId>
      <artifactId>java-core</artifactId>
      <version>2.4.1</version>
    </dependency>
    <dependency>
      <groupId>mssg-pdf-client</groupId>
      <artifactId>mssg-pdf-client</artifactId>
      <version>1.0.6</version>
      <classifier>client</classifier>
    </dependency>
    <dependency>
      <groupId>net.sourceforge.jexcelapi</groupId>
      <artifactId>jxl</artifactId>
      <version>2.6.12</version>
      <exclusions>
      	<exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
      	</exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>JxcDomain</id>
      <name>JxcDomainName</name>
      <url>http://10.88.99.21:8081/nexus/content/repository/JxcDomain/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>central</id>
      <name>Central</name>
      <url>http://10.88.99.21:8081/nexus/content/repository/central/</url>
      <releases>
        <enabled>false</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
    </repository>
  </repositories>
</project>
